﻿# -*- coding: utf-8 -*-
"""
收敛性指标计算模块
用于RQ3: 自动化迭代流程有效性评估
"""

import numpy as np
import json
from typing import Dict, List, Tuple, Any
from pathlib import Path

def calculate_convergence_metrics(iteration_data: List[Dict]) -> Dict[str, Any]:
    """计算收敛性指标"""
    if not iteration_data:
        return {"error": "No iteration data provided"}
    
    # 提取各项指标的时间序列
    iterations = []
    entity_distribution_scores = []
    diversity_scores = []
    quality_scores = []
    
    for data in iteration_data:
        iterations.append(data.get("iteration", 0))
        
        # 提取实体分布得分
        balance_eval = data.get("balance_evaluation", {})
        entity_distribution_scores.append(balance_eval.get("overall_score", 0))
        
        # 提取多样性得分
        diversity_eval = data.get("diversity_evaluation", {})
        diversity_scores.append(diversity_eval.get("vocabulary_diversity", 0))
        
        # 提取质量得分（自然度）
        naturalness_eval = data.get("naturalness_evaluation", {})
        quality_scores.append(naturalness_eval.get("avg_score", 0))
    
    # 计算收敛性指标
    convergence_metrics = {
        "total_iterations": len(iterations),
        "entity_distribution_convergence": analyze_convergence(entity_distribution_scores),
        "diversity_convergence": analyze_convergence(diversity_scores),
        "quality_convergence": analyze_convergence(quality_scores),
        "overall_convergence": {},
        "efficiency_metrics": calculate_efficiency_metrics(iteration_data)
    }
    
    # 计算整体收敛性
    all_scores = [entity_distribution_scores, diversity_scores, quality_scores]
    overall_improvements = []
    
    for scores in all_scores:
        if len(scores) > 1:
            improvement = scores[-1] - scores[0]
            overall_improvements.append(improvement)
    
    convergence_metrics["overall_convergence"] = {
        "average_improvement": np.mean(overall_improvements) if overall_improvements else 0,
        "total_improvement": sum(overall_improvements) if overall_improvements else 0,
        "converged": detect_convergence(all_scores)
    }
    
    return convergence_metrics

def analyze_convergence(scores: List[float], window_size: int = 3, threshold: float = 0.01) -> Dict[str, Any]:
    """分析单个指标的收敛性"""
    if len(scores) < 2:
        return {"converged": False, "convergence_iteration": None}
    
    # 计算变化率
    changes = [abs(scores[i] - scores[i-1]) for i in range(1, len(scores))]
    
    # 检测收敛点
    convergence_iteration = None
    converged = False
    
    if len(changes) >= window_size:
        for i in range(window_size - 1, len(changes)):
            window_changes = changes[i - window_size + 1:i + 1]
            if all(change < threshold for change in window_changes):
                convergence_iteration = i + 1  # +1因为changes比scores少一个元素
                converged = True
                break
    
    # 计算趋势
    if len(scores) > 1:
        trend = "increasing" if scores[-1] > scores[0] else "decreasing" if scores[-1] < scores[0] else "stable"
        trend_strength = abs(scores[-1] - scores[0]) / len(scores)
    else:
        trend = "unknown"
        trend_strength = 0
    
    return {
        "converged": converged,
        "convergence_iteration": convergence_iteration,
        "final_score": scores[-1] if scores else 0,
        "initial_score": scores[0] if scores else 0,
        "total_improvement": scores[-1] - scores[0] if len(scores) > 1 else 0,
        "average_change": np.mean(changes) if changes else 0,
        "trend": trend,
        "trend_strength": trend_strength,
        "stability": np.std(scores[-window_size:]) if len(scores) >= window_size else np.std(scores)
    }

def detect_convergence(score_sequences: List[List[float]], threshold: float = 0.01) -> bool:
    """检测多个指标序列是否都收敛"""
    for scores in score_sequences:
        convergence_info = analyze_convergence(scores, threshold=threshold)
        if not convergence_info["converged"]:
            return False
    return True

def calculate_efficiency_metrics(iteration_data: List[Dict]) -> Dict[str, Any]:
    """计算效率指标"""
    if not iteration_data:
        return {}
    
    # 提取时间和资源消耗数据
    time_costs = []
    api_calls = []
    quality_improvements = []
    
    for i, data in enumerate(iteration_data):
        # 时间成本
        time_cost = data.get("generation_time", 0)
        time_costs.append(time_cost)
        
        # API调用次数
        api_call_count = data.get("api_calls", 0)
        api_calls.append(api_call_count)
        
        # 质量改进
        if i > 0:
            current_quality = data.get("naturalness_evaluation", {}).get("avg_score", 0)
            previous_quality = iteration_data[i-1].get("naturalness_evaluation", {}).get("avg_score", 0)
            improvement = current_quality - previous_quality
            quality_improvements.append(improvement)
    
    efficiency_metrics = {
        "time_efficiency": {
            "total_time": sum(time_costs),
            "average_time_per_iteration": np.mean(time_costs) if time_costs else 0,
            "time_trend": "increasing" if len(time_costs) > 1 and time_costs[-1] > time_costs[0] else "decreasing"
        },
        "resource_efficiency": {
            "total_api_calls": sum(api_calls),
            "average_api_calls_per_iteration": np.mean(api_calls) if api_calls else 0
        },
        "quality_efficiency": {
            "average_quality_improvement": np.mean(quality_improvements) if quality_improvements else 0,
            "quality_improvement_per_time": np.mean(quality_improvements) / np.mean(time_costs) if quality_improvements and time_costs and np.mean(time_costs) > 0 else 0
        }
    }
    
    return efficiency_metrics

def analyze_iteration_trends(iteration_data: List[Dict]) -> Dict[str, Any]:
    """分析迭代趋势"""
    if not iteration_data:
        return {}
    
    # 提取关键指标
    iterations = list(range(len(iteration_data)))
    entity_scores = []
    diversity_scores = []
    quality_scores = []
    
    for data in iteration_data:
        balance_eval = data.get("balance_evaluation", {})
        entity_scores.append(balance_eval.get("overall_score", 0))
        
        diversity_eval = data.get("diversity_evaluation", {})
        diversity_scores.append(diversity_eval.get("vocabulary_diversity", 0))
        
        naturalness_eval = data.get("naturalness_evaluation", {})
        quality_scores.append(naturalness_eval.get("avg_score", 0))
    
    # 趋势分析
    trends = {
        "entity_distribution_trend": calculate_trend(iterations, entity_scores),
        "diversity_trend": calculate_trend(iterations, diversity_scores),
        "quality_trend": calculate_trend(iterations, quality_scores),
        "optimal_stopping_point": detect_optimal_stopping_point(iteration_data)
    }
    
    return trends

def calculate_trend(x: List[int], y: List[float]) -> Dict[str, Any]:
    """计算趋势线"""
    if len(x) != len(y) or len(x) < 2:
        return {"slope": 0, "intercept": 0, "r_squared": 0}
    
    # 简单线性回归
    n = len(x)
    x_mean = np.mean(x)
    y_mean = np.mean(y)
    
    slope = sum((x[i] - x_mean) * (y[i] - y_mean) for i in range(n)) / sum((x[i] - x_mean) ** 2 for i in range(n))
    intercept = y_mean - slope * x_mean
    
    # 计算R
    y_pred = [slope * x[i] + intercept for i in range(n)]
    ss_res = sum((y[i] - y_pred[i]) ** 2 for i in range(n))
    ss_tot = sum((y[i] - y_mean) ** 2 for i in range(n))
    r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
    
    return {
        "slope": slope,
        "intercept": intercept,
        "r_squared": r_squared,
        "trend_direction": "increasing" if slope > 0 else "decreasing" if slope < 0 else "stable"
    }

def detect_optimal_stopping_point(iteration_data: List[Dict]) -> Dict[str, Any]:
    """检测最优停止点"""
    if len(iteration_data) < 2:
        return {"optimal_iteration": 0, "reason": "insufficient_data"}
    
    # 计算综合得分
    composite_scores = []
    for data in iteration_data:
        balance_score = data.get("balance_evaluation", {}).get("overall_score", 0)
        diversity_score = data.get("diversity_evaluation", {}).get("vocabulary_diversity", 0)
        quality_score = data.get("naturalness_evaluation", {}).get("avg_score", 0) / 10  # 归一化到0-1
        
        # 综合得分（可以调整权重）
        composite_score = 0.4 * balance_score + 0.3 * diversity_score + 0.3 * quality_score
        composite_scores.append(composite_score)
    
    # 找到最高得分的迭代
    max_score_iteration = np.argmax(composite_scores)
    max_score = composite_scores[max_score_iteration]
    
    # 检查后续迭代是否有显著改进
    significant_improvement_threshold = 0.05
    has_later_improvement = False
    
    for i in range(max_score_iteration + 1, len(composite_scores)):
        if composite_scores[i] - max_score > significant_improvement_threshold:
            has_later_improvement = True
            break
    
    # 确定最优停止点
    if has_later_improvement:
        optimal_iteration = len(composite_scores) - 1  # 最后一次迭代
        reason = "continued_improvement"
    else:
        optimal_iteration = max_score_iteration
        reason = "peak_performance"
    
    return {
        "optimal_iteration": optimal_iteration + 1,  # 转换为1-based indexing
        "optimal_score": composite_scores[optimal_iteration],
        "reason": reason,
        "all_scores": composite_scores
    }

def load_iteration_data(run_dir: str) -> List[Dict]:
    """从运行目录加载迭代数据"""
    run_path = Path(run_dir)
    iteration_data = []
    
    # 查找所有迭代目录
    iteration_dirs = [d for d in run_path.glob("iterations/iteration_*") if d.is_dir()]
    iteration_dirs.sort()
    
    for iter_dir in iteration_dirs:
        # 加载迭代信息文件
        info_files = list(iter_dir.glob("*_info.json"))
        if info_files:
            try:
                with open(info_files[0], 'r', encoding='utf-8') as f:
                    iter_data = json.load(f)
                iteration_data.append(iter_data)
            except Exception as e:
                print(f"Warning: Failed to load iteration data from {info_files[0]}: {e}")
    
    return iteration_data
