﻿# evaluation/framework/metrics/__init__.py
"""
评估指标模块
提供各种评估指标的计算功能
"""

from .statistical_metrics import *
from .quality_metrics import *
from .convergence_metrics import *
from .ablation_metrics import *

__all__ = [
    # 统计指标
    'calculate_dataset_statistics',
    'compare_distributions',
    'statistical_significance_test',
    
    # 质量指标
    'evaluate_naturalness',
    'evaluate_annotation_accuracy',
    'evaluate_semantic_consistency',
    
    # 收敛性指标
    'calculate_convergence_metrics',
    'detect_convergence',
    'analyze_iteration_trends',
    
    # 消融实验指标
    'calculate_component_contribution',
    'compare_ablation_results',
    'analyze_interaction_effects'
]
