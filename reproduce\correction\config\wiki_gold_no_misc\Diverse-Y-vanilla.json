{"meta": {"dataset_name": "wiki-gold-no-misc", "source_dataset_dir_name": "24-02-11_NER-Dataset_{fmt=n-p2,#l=3,de=T}_add-super-idx", "diversity_variant": "Diverse-Y-vanilla"}, "entity_type2correction_info": {"person": {"defn": "A named person entity must be the name of a person. Only first names, last names, and full names are considered named person entities. Names of people in history such as \"<PERSON><PERSON><PERSON><PERSON>\" are also named person entities. General reference to a person or persons such as \"philanthropists\" are not named entities. Film titles and works of art such as \"The Dark Knight\" and \"Mona Lisa\" are also not relevant named entities. A named person entity should not have any starting titles such as \"Dr.\".", "name": "person", "name_full": "named person entity", "demos": [{"sentence": "The novel To Kill a Mockingbird, written by <PERSON>, is a classic of modern American literature.", "entity_span": "<PERSON>", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "Dr. <PERSON> was instrumental in the development and success of the Apollo lunar landing program.", "entity_span": "Dr. <PERSON>", "entity_type": null, "label": "WRONG_SPAN", "correct_span": "<PERSON>", "correct_type": null, "reason": null}, {"sentence": "The <PERSON> Lisa, also known as La Gioconda, is a half-length portrait painting by <PERSON>.", "entity_span": "<PERSON>", "entity_type": null, "label": "WRONG_TYPE", "correct_span": null, "correct_type": "other", "reason": null}, {"sentence": "She is a well-known environmental activist and has spoken at numerous international conferences on climate change.", "entity_span": "environmental activist", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "location": {"defn": "A named location entity must be the name of a location. General reference to a location such as \"city\" and \"business park\" is not a named entity. Demonyms such as \"French\", \"American\", \"African\" and \"Western\" are also not relevant named entities.", "name": "location", "name_full": "named location entity", "demos": [{"sentence": "The Mississippi River runs through several states in the United States, including Minnesota, Wisconsin, and Louisiana.", "entity_span": "United States", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "Dr. <PERSON>, an American virologist, developed the first successful polio vaccine in 1955.", "entity_span": "American", "entity_type": null, "label": "WRONG_TYPE", "correct_span": null, "correct_type": "other", "reason": null}]}, "organization": {"defn": "A named organization entity must be the name of an organization. Demonyms such as \"American\", \"French\", \"European\" and \"Indian\" are not relevant named entities. Prestigious Awards such as \"Academy Award\" and \"Nobel Prize in Physics\" are also not relevant named entities. Events such as \"Olympic Games\" and \"Cannes Film Festival\" are not relevant named entities. Historical events such as \"World War II\" and \"American Civil War\" are also not relevant named entities. Historical periods such as \"Elizabethan era\" and \"Romantic movement\" are not relevant named entities. Literary works and works of art such as \"The Two Towers\" and \"Mona Lisa\" are also not relevant named entities. Film titles and entertainment series such as \"The Dark Knight\" and \"Harry Potter\" are not relevant named entities. General references to an organization or organizations such as \"environmental organization\" and \"political organization\" are not named entities.", "name": "organization", "name_full": "named organization entity", "demos": [{"sentence": "The World Health Organization is a specialized agency of the United Nations responsible for international public health.", "entity_span": "United Nations", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "She won the Nobel Prize in Literature in 2013 for her novels and short stories.", "entity_span": "Nobel Prize in Literature", "entity_type": null, "label": "WRONG_TYPE", "correct_span": null, "correct_type": "other", "reason": null}, {"sentence": "<PERSON><PERSON>, also known as the \"Queen of Soul\", was an American singer, songwriter, and pianist.", "entity_span": "American", "entity_type": null, "label": "WRONG_TYPE", "correct_span": null, "correct_type": "other", "reason": null}, {"sentence": "The Fashion industry plays a significant role in shaping global trends and consumer preferences.", "entity_span": "Fashion industry", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "The actress <PERSON> gained international fame from her role as <PERSON><PERSON><PERSON> in the Harry Potter film series.", "entity_span": "<PERSON>", "entity_type": null, "label": "WRONG_TYPE", "correct_span": null, "correct_type": "other", "reason": null}, {"sentence": "The Declaration of Independence, written by <PERSON>, was adopted by the Continental Congress on July 4, 1776.", "entity_span": "Declaration of Independence", "entity_type": null, "label": "WRONG_TYPE", "correct_span": null, "correct_type": "other", "reason": null}]}}}