{"prompts": ["Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type MPAA Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type mpaa rating\n- (B). The span contains a named mpaa rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not mpaa rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, Plot, Actor, Trailer, Song, Review, Character, other].\n\nOnly actual MPAA rating labels such as \"G\", \"PG\", \"PG-13\", \"R\", and \"NC-17\" are named MPAA rating entities.\nYou should always drop starting and trailing \"rated\" or \"MPAA rating of\" words from named MPAA rating entity spans.\nRating interpretations such as \"Parental Guidance Suggested\" and \"suitable for children\" are not relevant named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"I want to see a trailer for The Wolf of Wall Street, {{rated R}}\"\nText Span: \"rated R\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"R\".\n\n2. Query: \"Can you recommend a family-friendly movie rated {{PG}} with the song Sound of Silence?\"\nText Span: \"PG\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"What are some unforgettable {{G-rated}} movies from the 90s\"\nText Span: \"G-rated\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"G\".\n\n4. Query: \"who directed the movie Amadeus and is it appropriate for {{mature audiences}}\"\nText Span: \"mature audiences\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What {{D rated}} movie did Steven Spielberg direct in the 1980s?\"\nText Span: \"D rated\"\n\n2. Query: \"what are some {{U}}-rated animated movies\"\nText Span: \"U\"\n\n3. Query: \"Can you recommend a family-friendly movie directed by Gus Van Sant, rated {{TV-PG}}?\"\nText Span: \"TV-PG\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type MPAA Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type mpaa rating\n- (B). The span contains a named mpaa rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not mpaa rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, Plot, Actor, Trailer, Song, Review, Character, other].\n\nOnly actual MPAA rating labels such as \"G\", \"PG\", \"PG-13\", \"R\", and \"NC-17\" are named MPAA rating entities.\nYou should always drop starting and trailing \"rated\" or \"MPAA rating of\" words from named MPAA rating entity spans.\nRating interpretations such as \"Parental Guidance Suggested\" and \"suitable for children\" are not relevant named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"who directed the movie Amadeus and is it appropriate for {{mature audiences}}\"\nText Span: \"mature audiences\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n2. Query: \"Can you recommend a family-friendly movie rated {{PG}} with the song Sound of Silence?\"\nText Span: \"PG\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"What are some unforgettable {{G-rated}} movies from the 90s\"\nText Span: \"G-rated\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"G\".\n\n4. Query: \"I want to see a trailer for The Wolf of Wall Street, {{rated R}}\"\nText Span: \"rated R\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"R\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Is there a Spell-binding movie suitable for {{All Ages}} featuring a character like Mystique?\"\nText Span: \"All Ages\"\n\n2. Query: \"Is John McClane in any {{parental guidance}} films that are worth watching?\"\nText Span: \"parental guidance\"\n\n3. Query: \"Tell me about the wonderful {{PG}}-rated movie directed by Lars von Trier\"\nText Span: \"PG\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type MPAA Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type mpaa rating\n- (B). The span contains a named mpaa rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not mpaa rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, Plot, Actor, Trailer, Song, Review, Character, other].\n\nOnly actual MPAA rating labels such as \"G\", \"PG\", \"PG-13\", \"R\", and \"NC-17\" are named MPAA rating entities.\nYou should always drop starting and trailing \"rated\" or \"MPAA rating of\" words from named MPAA rating entity spans.\nRating interpretations such as \"Parental Guidance Suggested\" and \"suitable for children\" are not relevant named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you recommend a family-friendly movie rated {{PG}} with the song Sound of Silence?\"\nText Span: \"PG\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"I want to see a trailer for The Wolf of Wall Street, {{rated R}}\"\nText Span: \"rated R\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"R\".\n\n3. Query: \"who directed the movie Amadeus and is it appropriate for {{mature audiences}}\"\nText Span: \"mature audiences\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n4. Query: \"What are some unforgettable {{G-rated}} movies from the 90s\"\nText Span: \"G-rated\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"G\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Show me a movie directed by Christopher Nolan that is {{Recommended for Adults}} and has a viewers' rating of R.\"\nText Span: \"Recommended for Adults\"\n\n2. Query: \"Can you recommend a mind-blowing movie suitable for {{All Ages}} that features waterfalls?\"\nText Span: \"All Ages\"\n\n3. Query: \"Is there a movie {{suitable for children and adults}} that came out in the 1964s?\"\nText Span: \"suitable for children and adults\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type MPAA Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type mpaa rating\n- (B). The span contains a named mpaa rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not mpaa rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, Plot, Actor, Trailer, Song, Review, Character, other].\n\nOnly actual MPAA rating labels such as \"G\", \"PG\", \"PG-13\", \"R\", and \"NC-17\" are named MPAA rating entities.\nYou should always drop starting and trailing \"rated\" or \"MPAA rating of\" words from named MPAA rating entity spans.\nRating interpretations such as \"Parental Guidance Suggested\" and \"suitable for children\" are not relevant named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What are some unforgettable {{G-rated}} movies from the 90s\"\nText Span: \"G-rated\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"G\".\n\n2. Query: \"I want to see a trailer for The Wolf of Wall Street, {{rated R}}\"\nText Span: \"rated R\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"R\".\n\n3. Query: \"Can you recommend a family-friendly movie rated {{PG}} with the song Sound of Silence?\"\nText Span: \"PG\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"who directed the movie Amadeus and is it appropriate for {{mature audiences}}\"\nText Span: \"mature audiences\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend any movies with world-building that are {{suitable for adults}}?\"\nText Span: \"suitable for adults\"\n\n2. Query: \"Show me a trailer for a movie starring Ryan Reynolds that has been {{approved for all audiences}}.\"\nText Span: \"approved for all audiences\"\n\n3. Query: \"Is Alicia Vikander featured in any {{Mature Audiences}} films that are a must see?\"\nText Span: \"Mature Audiences\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type MPAA Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type mpaa rating\n- (B). The span contains a named mpaa rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not mpaa rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, Plot, Actor, Trailer, Song, Review, Character, other].\n\nOnly actual MPAA rating labels such as \"G\", \"PG\", \"PG-13\", \"R\", and \"NC-17\" are named MPAA rating entities.\nYou should always drop starting and trailing \"rated\" or \"MPAA rating of\" words from named MPAA rating entity spans.\nRating interpretations such as \"Parental Guidance Suggested\" and \"suitable for children\" are not relevant named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What are some unforgettable {{G-rated}} movies from the 90s\"\nText Span: \"G-rated\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"G\".\n\n2. Query: \"who directed the movie Amadeus and is it appropriate for {{mature audiences}}\"\nText Span: \"mature audiences\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n3. Query: \"Can you recommend a family-friendly movie rated {{PG}} with the song Sound of Silence?\"\nText Span: \"PG\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"I want to see a trailer for The Wolf of Wall Street, {{rated R}}\"\nText Span: \"rated R\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"R\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Who directed the movie set to be released in 2027, and what is the {{MPAA}} rating?\"\nText Span: \"MPAA\"\n\n2. Query: \"Can you recommend any {{G}}-rated movies suitable for kids to watch?\"\nText Span: \"G\"\n\n3. Query: \"Who directed the film with Terrence Malick and a family-friendly {{M/PG}} rating\"\nText Span: \"M/PG\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type MPAA Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type mpaa rating\n- (B). The span contains a named mpaa rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not mpaa rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, Plot, Actor, Trailer, Song, Review, Character, other].\n\nOnly actual MPAA rating labels such as \"G\", \"PG\", \"PG-13\", \"R\", and \"NC-17\" are named MPAA rating entities.\nYou should always drop starting and trailing \"rated\" or \"MPAA rating of\" words from named MPAA rating entity spans.\nRating interpretations such as \"Parental Guidance Suggested\" and \"suitable for children\" are not relevant named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"who directed the movie Amadeus and is it appropriate for {{mature audiences}}\"\nText Span: \"mature audiences\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n2. Query: \"What are some unforgettable {{G-rated}} movies from the 90s\"\nText Span: \"G-rated\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"G\".\n\n3. Query: \"Can you recommend a family-friendly movie rated {{PG}} with the song Sound of Silence?\"\nText Span: \"PG\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"I want to see a trailer for The Wolf of Wall Street, {{rated R}}\"\nText Span: \"rated R\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"R\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the top film directed by Steven Spielberg that is {{suitable for all ages}}, such as TV-G?\"\nText Span: \"suitable for all ages\"\n\n2. Query: \"What are some good {{TV-Y7}} movies that came out in the last decade\"\nText Span: \"TV-Y7\"\n\n3. Query: \"Show me a movie with Dwayne Johnson that involves sports and is {{suitable for children}}.\"\nText Span: \"suitable for children\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type MPAA Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type mpaa rating\n- (B). The span contains a named mpaa rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not mpaa rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, Plot, Actor, Trailer, Song, Review, Character, other].\n\nOnly actual MPAA rating labels such as \"G\", \"PG\", \"PG-13\", \"R\", and \"NC-17\" are named MPAA rating entities.\nYou should always drop starting and trailing \"rated\" or \"MPAA rating of\" words from named MPAA rating entity spans.\nRating interpretations such as \"Parental Guidance Suggested\" and \"suitable for children\" are not relevant named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What are some unforgettable {{G-rated}} movies from the 90s\"\nText Span: \"G-rated\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"G\".\n\n2. Query: \"Can you recommend a family-friendly movie rated {{PG}} with the song Sound of Silence?\"\nText Span: \"PG\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"I want to see a trailer for The Wolf of Wall Street, {{rated R}}\"\nText Span: \"rated R\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"R\".\n\n4. Query: \"who directed the movie Amadeus and is it appropriate for {{mature audiences}}\"\nText Span: \"mature audiences\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Which movies released in 2015 are suitable for {{Mature Audiences}}?\"\nText Span: \"Mature Audiences\"\n\n2. Query: \"I'm looking for a {{G}}-rated movie about corporate corruption.\"\nText Span: \"G\"\n\n3. Query: \"What are some good movies from the year 2000 that are {{Not Rated}}?\"\nText Span: \"Not Rated\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type MPAA Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type mpaa rating\n- (B). The span contains a named mpaa rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not mpaa rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, Plot, Actor, Trailer, Song, Review, Character, other].\n\nOnly actual MPAA rating labels such as \"G\", \"PG\", \"PG-13\", \"R\", and \"NC-17\" are named MPAA rating entities.\nYou should always drop starting and trailing \"rated\" or \"MPAA rating of\" words from named MPAA rating entity spans.\nRating interpretations such as \"Parental Guidance Suggested\" and \"suitable for children\" are not relevant named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"I want to see a trailer for The Wolf of Wall Street, {{rated R}}\"\nText Span: \"rated R\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"R\".\n\n2. Query: \"who directed the movie Amadeus and is it appropriate for {{mature audiences}}\"\nText Span: \"mature audiences\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n3. Query: \"Can you recommend a family-friendly movie rated {{PG}} with the song Sound of Silence?\"\nText Span: \"PG\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"What are some unforgettable {{G-rated}} movies from the 90s\"\nText Span: \"G-rated\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"G\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Is Gerard Butler in any {{G}}-rated movies?\"\nText Span: \"G\"\n\n2. Query: \"I'd like to watch a movie {{Approved}} for all ages. How about something from The Godfather series?\"\nText Span: \"Approved\"\n\n3. Query: \"Can you recommend any {{Unrated}} movies with a small town mystery plot?\"\nText Span: \"Unrated\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type MPAA Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type mpaa rating\n- (B). The span contains a named mpaa rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not mpaa rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, Plot, Actor, Trailer, Song, Review, Character, other].\n\nOnly actual MPAA rating labels such as \"G\", \"PG\", \"PG-13\", \"R\", and \"NC-17\" are named MPAA rating entities.\nYou should always drop starting and trailing \"rated\" or \"MPAA rating of\" words from named MPAA rating entity spans.\nRating interpretations such as \"Parental Guidance Suggested\" and \"suitable for children\" are not relevant named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"who directed the movie Amadeus and is it appropriate for {{mature audiences}}\"\nText Span: \"mature audiences\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n2. Query: \"Can you recommend a family-friendly movie rated {{PG}} with the song Sound of Silence?\"\nText Span: \"PG\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"I want to see a trailer for The Wolf of Wall Street, {{rated R}}\"\nText Span: \"rated R\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"R\".\n\n4. Query: \"What are some unforgettable {{G-rated}} movies from the 90s\"\nText Span: \"G-rated\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"G\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"who directed the movie Amadeus and is it appropriate for {{mature audiences}}\"\nText Span: \"mature audiences\"\n\n2. Query: \"Is Mufasa in any films rated {{AO}}?\"\nText Span: \"AO\"\n\n3. Query: \"Could you recommend a movie similar to Alien that is {{rated R-13}}?\"\nText Span: \"rated R-13\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type MPAA Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type mpaa rating\n- (B). The span contains a named mpaa rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not mpaa rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, Plot, Actor, Trailer, Song, Review, Character, other].\n\nOnly actual MPAA rating labels such as \"G\", \"PG\", \"PG-13\", \"R\", and \"NC-17\" are named MPAA rating entities.\nYou should always drop starting and trailing \"rated\" or \"MPAA rating of\" words from named MPAA rating entity spans.\nRating interpretations such as \"Parental Guidance Suggested\" and \"suitable for children\" are not relevant named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What are some unforgettable {{G-rated}} movies from the 90s\"\nText Span: \"G-rated\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"G\".\n\n2. Query: \"who directed the movie Amadeus and is it appropriate for {{mature audiences}}\"\nText Span: \"mature audiences\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n3. Query: \"Can you recommend a family-friendly movie rated {{PG}} with the song Sound of Silence?\"\nText Span: \"PG\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"I want to see a trailer for The Wolf of Wall Street, {{rated R}}\"\nText Span: \"rated R\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"R\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Who directed the movie that is {{Recommended for Adults}} and stars Niki Caro?\"\nText Span: \"Recommended for Adults\"\n\n2. Query: \"I want to see a trailer for The Wolf of Wall Street, {{rated R}}\"\nText Span: \"rated R\"\n\n3. Query: \"Who is the director of the movie with the {{highest MPAA rating}}?\"\nText Span: \"highest MPAA rating\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type MPAA Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type mpaa rating\n- (B). The span contains a named mpaa rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not mpaa rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, Plot, Actor, Trailer, Song, Review, Character, other].\n\nOnly actual MPAA rating labels such as \"G\", \"PG\", \"PG-13\", \"R\", and \"NC-17\" are named MPAA rating entities.\nYou should always drop starting and trailing \"rated\" or \"MPAA rating of\" words from named MPAA rating entity spans.\nRating interpretations such as \"Parental Guidance Suggested\" and \"suitable for children\" are not relevant named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"who directed the movie Amadeus and is it appropriate for {{mature audiences}}\"\nText Span: \"mature audiences\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n2. Query: \"I want to see a trailer for The Wolf of Wall Street, {{rated R}}\"\nText Span: \"rated R\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"R\".\n\n3. Query: \"What are some unforgettable {{G-rated}} movies from the 90s\"\nText Span: \"G-rated\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"G\".\n\n4. Query: \"Can you recommend a family-friendly movie rated {{PG}} with the song Sound of Silence?\"\nText Span: \"PG\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Show me a TV spot for the {{Unrated}} movie that is epic in scale\"\nText Span: \"Unrated\"\n\n2. Query: \"do you have any promos for {{NC-17}} movies\"\nText Span: \"NC-17\"\n\n3. Query: \"Is Viggo Mortensen in any {{U-rated}} movies?\"\nText Span: \"U-rated\"", "Here are 2 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type MPAA Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type mpaa rating\n- (B). The span contains a named mpaa rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not mpaa rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, Plot, Actor, Trailer, Song, Review, Character, other].\n\nOnly actual MPAA rating labels such as \"G\", \"PG\", \"PG-13\", \"R\", and \"NC-17\" are named MPAA rating entities.\nYou should always drop starting and trailing \"rated\" or \"MPAA rating of\" words from named MPAA rating entity spans.\nRating interpretations such as \"Parental Guidance Suggested\" and \"suitable for children\" are not relevant named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you recommend a family-friendly movie rated {{PG}} with the song Sound of Silence?\"\nText Span: \"PG\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"What are some unforgettable {{G-rated}} movies from the 90s\"\nText Span: \"G-rated\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"G\".\n\n3. Query: \"I want to see a trailer for The Wolf of Wall Street, {{rated R}}\"\nText Span: \"rated R\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"R\".\n\n4. Query: \"who directed the movie Amadeus and is it appropriate for {{mature audiences}}\"\nText Span: \"mature audiences\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n\n---\n\n\nPlease classify the following 2 spans of text:\n\n1. Query: \"can you recommend a buddy film that is appropriate for {{all ages}}\"\nText Span: \"all ages\"\n\n2. Query: \"What are some {{S-rated}} movies starring Amanda Seyfried directed by Jonathan Demme?\"\nText Span: \"S-rated\""]}