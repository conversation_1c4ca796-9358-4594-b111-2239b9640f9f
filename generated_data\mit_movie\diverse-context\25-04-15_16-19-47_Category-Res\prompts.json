{"prompts": ["Suppose you are a user of a dialog system or conversational agent. List 10 diverse movie query categories for movie queries to the dialog system.", "Suppose you are a user of a dialog system or conversational agent. List 10 diverse movie query categories for movie queries to the dialog system.", "Suppose you are a user of a dialog system or conversational agent. List 10 diverse movie query categories for movie queries to the dialog system.", "Suppose you are a user of a dialog system or conversational agent. List 6 different user demographics, i.e. different age groups and genders, for movie queries to the dialog system. Some examples are [young, age-friendly, older, family-friendly].", "Suppose you are a user of a dialog system or conversational agent. List 6 different user demographics, i.e. different age groups and genders, for movie queries to the dialog system. Some examples are [young, age-friendly, older, family-friendly].", "Suppose you are a user of a dialog system or conversational agent. List 6 different user demographics, i.e. different age groups and genders, for movie queries to the dialog system. Some examples are [family-friendly, older, young, age-friendly].", "Suppose you are a user of a dialog system or conversational agent. List 10 different user moods for movie queries to the dialog system. Some examples are [curious, frustrated].", "Suppose you are a user of a dialog system or conversational agent. List 10 different user moods for movie queries to the dialog system. Some examples are [curious, frustrated].", "Suppose you are a user of a dialog system or conversational agent. List 10 different user moods for movie queries to the dialog system. Some examples are [frustrated, curious].", "Suppose you are a user of a dialog system or conversational agent. List 8 different language variations for movie queries to the dialog system. Some examples are [informal, formal, vague, colloquial, indirect, technical, casual, straightforward, slang].", "Suppose you are a user of a dialog system or conversational agent. List 8 different language variations for movie queries to the dialog system. Some examples are [vague, colloquial, straightforward, slang, formal, informal, technical, indirect, casual].", "Suppose you are a user of a dialog system or conversational agent. List 8 different language variations for movie queries to the dialog system. Some examples are [vague, slang, indirect, informal, straightforward, technical, colloquial, casual, formal]."]}