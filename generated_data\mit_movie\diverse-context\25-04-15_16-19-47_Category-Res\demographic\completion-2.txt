
Okay, here are 6 different user demographics for movie queries, focusing on combinations of age and gender:

1.  **Young Male:** (e.g., a teenage boy) Interested in action, sci-fi, superhero movies, or recent blockbusters.
2.  **Older Female:** (e.g., a woman in her 60s or 70s) Might prefer classic films, romantic comedies, dramas, or documentaries.
3.  **Young Female:** (e.g., a young adult woman) Could be looking for romantic movies, feel-good comedies, indie films, or popular dramas.
4.  **Older Male:** (e.g., a man in his 50s or 60s) Might ask about older, iconic films, specific genres like westerns or war movies, or award-winning pictures.
5.  **Middle-Aged Couple:** (e.g., both around 40-50) Looking for family-friendly options, recent popular movies suitable for a date night, or films they can both enjoy.
6.  **Young Family:** (e.g., parents with young children) Primarily interested in animated movies, children's films, or movies suitable for all ages.