[{"text": "张先生通过招商银行转账支付了订单号2023112501的货款。", "label": [{"entity": "转账支付", "start_idx": 12, "end_idx": 15, "type": "交易信息"}, {"entity": "订单号2023112501", "start_idx": 20, "end_idx": 27, "type": "交易信息"}, {"entity": "货款", "start_idx": 28, "end_idx": 30, "type": "交易信息"}]}, {"text": "李女士在京东商城下单，支付方式选择了微信支付，金额为299元。", "label": [{"entity": "微信支付", "start_idx": 11, "end_idx": 14, "type": "交易信息"}, {"entity": "299元", "start_idx": 19, "end_idx": 22, "type": "交易信息"}]}, {"text": "王总使用Visa卡****************完成了此次交易，交易金额为500美元。", "label": [{"entity": "Visa卡****************", "start_idx": 5, "end_idx": 23, "type": "交易信息"}, {"entity": "500美元", "start_idx": 34, "end_idx": 38, "type": "交易信息"}]}, {"text": "刘先生在美团外卖下单，订单号MEI20231126，支付了58元。", "label": [{"entity": "订单号MEI20231126", "start_idx": 12, "end_idx": 23, "type": "交易信息"}, {"entity": "58元", "start_idx": 26, "end_idx": 28, "type": "交易信息"}]}, {"text": "赵女士使用银联卡6225881234567890支付了机票费用，金额为2580元。", "label": [{"entity": "银联卡6225881234567890", "start_idx": 5, "end_idx": 20, "type": "交易信息"}, {"entity": "机票费用", "start_idx": 22, "end_idx": 27, "type": "交易信息"}, {"entity": "2580元", "start_idx": 34, "end_idx": 37, "type": "交易信息"}]}, {"text": "周先生在淘宝下单，订单号Tao20231127，使用花呗支付了199元。", "label": [{"entity": "订单号Tao20231127", "start_idx": 8, "end_idx": 20, "type": "交易信息"}, {"entity": "199元", "start_idx": 33, "end_idx": 36, "type": "交易信息"}]}, {"text": "吴女士通过PayPal账号****************完成了国际购物支付，金额为120欧元。", "label": [{"entity": "PayPal账号", "start_idx": 7, "end_idx": 12, "type": "交易信息"}, {"entity": "<EMAIL>", "start_idx": 12, "end_idx": 26, "type": "交易信息"}, {"entity": "国际购物支付", "start_idx": 28, "end_idx": 37, "type": "交易信息"}, {"entity": "120欧元", "start_idx": 45, "end_idx": 50, "type": "交易信息"}]}, {"text": "孙先生在携程预订酒店，订单号CT20231128，支付了899元。", "label": [{"entity": "携程", "start_idx": 7, "end_idx": 9, "type": "交易信息"}, {"entity": "酒店", "start_idx": 10, "end_idx": 12, "type": "交易信息"}, {"entity": "订单号CT20231128", "start_idx": 13, "end_idx": 25, "type": "交易信息"}, {"entity": "899元", "start_idx": 29, "end_idx": 32, "type": "交易信息"}]}, {"text": "钱小姐使用Apple Pay完成支付，交易金额为88元，设备序列号为A123456789。", "label": [{"entity": "Apple Pay", "start_idx": 6, "end_idx": 13, "type": "交易信息"}, {"entity": "88元", "start_idx": 21, "end_idx": 24, "type": "交易信息"}, {"entity": "A123456789", "start_idx": 34, "end_idx": 43, "type": "交易信息"}]}, {"text": "张先生通过支付宝转账给李女士500元，交易时间为2023年10月15日15:30。", "label": [{"entity": "支付宝", "start_idx": 6, "end_idx": 9, "type": "交易信息"}, {"entity": "500元", "start_idx": 16, "end_idx": 19, "type": "交易信息"}, {"entity": "2023年10月15日15:30", "start_idx": 22, "end_idx": 36, "type": "交易信息"}]}]