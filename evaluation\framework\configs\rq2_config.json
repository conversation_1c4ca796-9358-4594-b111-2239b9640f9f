{"rq2_quality_assessment": {"description": "生成数据质量评估配置", "metrics": {"naturalness": {"enabled": true, "model": "zhipu-api", "scoring_scale": [1, 10], "sample_size": 100, "threshold": 6.0}, "annotation_accuracy": {"enabled": true, "check_entity_boundaries": true, "check_entity_types": true, "manual_validation_sample": 50}, "semantic_consistency": {"enabled": true, "context_window": 5, "similarity_threshold": 0.7}, "diversity_metrics": {"vocabulary_diversity": 0.6, "syntactic_diversity": 0.5, "semantic_diversity": 0.4, "context_diversity": 0.5, "entity_diversity": 0.7}, "balance_metrics": {"distribution_tolerance": 0.15, "min_coverage_ratio": 0.8}}, "evaluation_methods": {"automatic_scoring": true, "manual_evaluation": true, "cross_validation": true}}}