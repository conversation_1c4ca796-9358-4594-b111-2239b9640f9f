{"timestamp": "20250720_145555", "start_time": "2025-07-20T14:55:55.815353", "status": "completed", "config": {"dataset_path": "format-dataset/privacy_bench_small_10.json", "target_count": 10, "max_iterations": 10, "batch_size": 10, "distribution_threshold": 0.05, "generation_features": {"use_sentence_diversity": false, "use_entity_diversity": false, "use_example_sentences": false}}, "directories": {"root": "synth_dataset\\runs\\20250720_145555", "config": "synth_dataset\\runs\\20250720_145555\\config", "source": "synth_dataset\\runs\\20250720_145555\\source", "intermediate": "synth_dataset\\runs\\20250720_145555\\intermediate", "intermediate_raw": "synth_dataset\\runs\\20250720_145555\\intermediate\\raw", "intermediate_processed": "synth_dataset\\runs\\20250720_145555\\intermediate\\processed", "intermediate_by_entity": "synth_dataset\\runs\\20250720_145555\\intermediate\\by_entity", "iterations": "synth_dataset\\runs\\20250720_145555\\iterations", "strategies": "synth_dataset\\runs\\20250720_145555\\strategies", "output": "synth_dataset\\runs\\20250720_145555\\output", "evaluation": "synth_dataset\\runs\\20250720_145555\\evaluation", "logs": "synth_dataset\\runs\\20250720_145555\\logs"}, "last_updated": "2025-07-20T15:12:48.670981", "stage": "iteration", "current_iteration": 2, "max_iterations": 10, "final_dataset_path": "synth_dataset\\runs\\20250720_145555\\output\\20250720_145555_final_synthetic_dataset.json", "final_report_path": "synth_dataset\\runs\\20250720_145555\\output\\20250720_145555_synthesis_report.json", "total_iterations": 2, "final_metrics": {"original_dataset_size": 32, "final_dataset_size": 229, "total_iterations": 2, "all_entity_types": ["姓名", "年龄", "性别", "国籍", "职业", "民族", "教育背景", "婚姻状况", "政治倾向", "家庭成员", "工资数额", "投资产品", "税务记录", "信用记录", "实体资产", "交易信息", "疾病", "药物", "临床表现", "医疗程序", "过敏信息", "生育信息", "地理位置", "行程信息"], "original_distribution": {"姓名": 1, "地理位置": 3, "职业": 8, "医疗程序": 1, "疾病": 7, "药物": 8, "临床表现": 3, "国籍": 4, "民族": 1, "教育背景": 3, "性别": 2, "年龄": 2}, "target_distribution": {"姓名": 10, "年龄": 10, "性别": 10, "国籍": 10, "职业": 10, "民族": 10, "教育背景": 10, "婚姻状况": 10, "政治倾向": 10, "家庭成员": 10, "工资数额": 10, "投资产品": 10, "税务记录": 10, "信用记录": 10, "实体资产": 10, "交易信息": 10, "疾病": 10, "药物": 10, "临床表现": 10, "医疗程序": 10, "过敏信息": 10, "生育信息": 10, "地理位置": 10, "行程信息": 10}, "final_distribution": {"姓名": 15, "地理位置": 19, "职业": 10, "医疗程序": 10, "疾病": 10, "药物": 10, "临床表现": 24, "国籍": 12, "民族": 17, "教育背景": 13, "性别": 14, "年龄": 11, "婚姻状况": 10, "政治倾向": 15, "家庭成员": 37, "工资数额": 12, "投资产品": 11, "税务记录": 17, "信用记录": 16, "实体资产": 19, "交易信息": 34, "过敏信息": 18, "生育信息": 27, "行程信息": 33}, "final_gap": {"姓名": 0, "年龄": 0, "性别": 0, "国籍": 0, "职业": 0, "民族": 0, "教育背景": 0, "婚姻状况": 0, "政治倾向": 0, "家庭成员": 0, "工资数额": 0, "投资产品": 0, "税务记录": 0, "信用记录": 0, "实体资产": 0, "交易信息": 0, "疾病": 0, "药物": 0, "临床表现": 0, "医疗程序": 0, "过敏信息": 0, "生育信息": 0, "地理位置": 0, "行程信息": 0}, "diversity_metrics": {"vocabulary_diversity": 0.9696969696969697, "syntactic_diversity": 0.8, "semantic_diversity": 0.7, "context_diversity": 0.6, "entity_diversity": 0.75}, "distribution_passed": true, "diversity_passed": false}}