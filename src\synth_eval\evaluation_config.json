{"evaluation_thresholds": {"balance": {"distribution_tolerance": 0.15, "min_coverage_ratio": 0.8, "max_missing_entities": 2, "max_excess_entities": 3}, "diversity": {"vocabulary_diversity": 0.6, "syntactic_diversity": 0.5, "semantic_diversity": 0.4, "context_diversity": 0.5, "entity_diversity": 0.7, "min_unique_entities": 0.3, "min_sentence_length": 10, "max_sentence_length": 100}}, "syntactic_patterns": ["basic_sentence", "with_punctuation", "with_parentheses", "with_quotes", "with_colon", "with_enumeration", "with_condition", "with_comparison"], "context_patterns": ["multi_entity", "with_punctuation", "with_parentheses", "with_quotes", "with_colon", "with_numbers", "with_time", "with_location"], "evaluation_weights": {"balance": 0.5, "diversity": 0.5, "vocabulary_diversity": 0.2, "syntactic_diversity": 0.2, "semantic_diversity": 0.2, "context_diversity": 0.2, "entity_diversity": 0.2}, "report_settings": {"include_detailed_analysis": true, "include_visualization": false, "save_intermediate_results": true, "output_format": "json"}, "naturalness": {"score_threshold": 6.0, "low_rate_threshold": 0.1, "api_url": "https://open.bigmodel.cn/api/paas/v4/chat/completions", "api_key_env": "ZHIPUAI_API_KEY", "max_retry": 3, "sleep_time": 1.5}, "dataset_path": "synth_dataset\\runs\\20250720_173011\\output\\20250720_173011_final_synthetic_dataset.json"}