[{"text": "二十周岁至三十四岁半的公民，务必参与此次选举投票。", "label": [{"entity": "二十周岁", "start_idx": 0, "end_idx": 3, "type": "年龄"}, {"entity": "三十四岁半", "start_idx": 5, "end_idx": 9, "type": "年龄"}]}, {"text": "岁及二十周岁群体，须在规定时间内完成兵役登记。", "label": [{"entity": "二十周岁", "start_idx": 2, "end_idx": 5, "type": "年龄"}]}, {"text": "有关5岁3个月至二十四周岁儿童，家长应严格遵循疫苗接种指南。", "label": [{"entity": "5岁3个月", "start_idx": 2, "end_idx": 6, "type": "年龄"}, {"entity": "二十四周岁", "start_idx": 8, "end_idx": 12, "type": "年龄"}]}, {"text": "通知：35岁以上及二十周岁以下人员，立即进行健康体检。", "label": [{"entity": "35岁以上", "start_idx": 3, "end_idx": 7, "type": "年龄"}, {"entity": "二十周岁以下", "start_idx": 9, "end_idx": 14, "type": "年龄"}]}, {"text": "岁3个月至22岁青年，请密切关注教育部门发布的学业指导信息。", "label": [{"entity": "3个月至22岁", "start_idx": 1, "end_idx": 7, "type": "年龄"}]}, {"text": "请务必详细对比三十四岁半与二十四周岁的病例资料。", "label": [{"entity": "三十四岁半", "start_idx": 7, "end_idx": 11, "type": "年龄"}, {"entity": "二十四周岁", "start_idx": 13, "end_idx": 17, "type": "年龄"}]}, {"text": "立即整理二十周岁和二十四周岁的患者档案。", "label": [{"entity": "二十周岁", "start_idx": 4, "end_idx": 7, "type": "年龄"}, {"entity": "二十四周岁", "start_idx": 9, "end_idx": 13, "type": "年龄"}]}, {"text": "将22岁和二十周岁的病患资料进行分类归档。", "label": [{"entity": "22岁", "start_idx": 1, "end_idx": 3, "type": "年龄"}, {"entity": "二十周岁", "start_idx": 5, "end_idx": 8, "type": "年龄"}]}, {"text": "分析35岁与22岁患者的治疗反应差异。", "label": [{"entity": "35岁", "start_idx": 2, "end_idx": 4, "type": "年龄"}, {"entity": "22岁", "start_idx": 6, "end_idx": 8, "type": "年龄"}]}, {"text": "对二十四周岁和二十周岁患者的治疗方案进行评估。", "label": [{"entity": "二十四周岁", "start_idx": 1, "end_idx": 5, "type": "年龄"}, {"entity": "二十周岁", "start_idx": 7, "end_idx": 10, "type": "年龄"}]}, {"text": "小王，你是女性还是双性啊？我有点儿记不清了。", "label": [{"entity": "女性", "start_idx": 5, "end_idx": 6, "type": "性别"}, {"entity": "双性", "start_idx": 9, "end_idx": 10, "type": "性别"}]}, {"text": "银行工作人员问：“性别选项有双性和雌雄同体，请问您要怎么填？”", "label": [{"entity": "双性", "start_idx": 14, "end_idx": 15, "type": "性别"}, {"entity": "雌雄同体", "start_idx": 17, "end_idx": 20, "type": "性别"}]}, {"text": "小李说他是非二元性别的，但我觉得他更像男的。", "label": [{"entity": "非二元", "start_idx": 5, "end_idx": 7, "type": "性别"}, {"entity": "男", "start_idx": 19, "end_idx": 19, "type": "性别"}]}, {"text": "那张表格上写着性别一栏可以选非二元或雌雄同体，真够细的。", "label": [{"entity": "非二元", "start_idx": 14, "end_idx": 16, "type": "性别"}, {"entity": "雌雄同体", "start_idx": 18, "end_idx": 21, "type": "性别"}]}, {"text": "他填写申请表时，中性旁边还勾了个男性，真是搞不懂。", "label": [{"entity": "中性", "start_idx": 8, "end_idx": 9, "type": "性别"}, {"entity": "男性", "start_idx": 16, "end_idx": 17, "type": "性别"}]}, {"text": "女性及双性群体的合法权益在本次法律修订中得到了明确的保障。", "label": [{"entity": "女性", "start_idx": 0, "end_idx": 1, "type": "性别"}, {"entity": "双性", "start_idx": 3, "end_idx": 4, "type": "性别"}]}, {"text": "双性人士在就业平等权法案中与女性享有同等的保护措施。", "label": [{"entity": "双性人士", "start_idx": 0, "end_idx": 3, "type": "性别"}, {"entity": "女性", "start_idx": 14, "end_idx": 15, "type": "性别"}]}, {"text": "跨性别者权益保障法明确了男性跨性别者应当享有的法定权利。", "label": [{"entity": "男性跨性别者", "start_idx": 12, "end_idx": 17, "type": "性别"}, {"entity": "男", "start_idx": 12, "end_idx": 12, "type": "性别"}, {"entity": "性别", "start_idx": 1, "end_idx": 2, "type": "性别"}]}, {"text": "性别中性观念在法律中对女性权益的维护提出了新的视角。", "label": [{"entity": "女性", "start_idx": 11, "end_idx": 12, "type": "性别"}]}, {"text": "跨性别和中性性别者在劳动法保护下，享有平等的职业发展机会。", "label": [{"entity": "跨性别", "start_idx": 0, "end_idx": 2, "type": "性别"}, {"entity": "中性性别者", "start_idx": 4, "end_idx": 8, "type": "性别"}]}, {"text": "丧偶或单身客户，请核对交易明细。", "label": [{"entity": "丧偶", "start_idx": 0, "end_idx": 1, "type": "婚姻状况"}, {"entity": "单身", "start_idx": 3, "end_idx": 4, "type": "婚姻状况"}]}, {"text": "离异或婚姻存续中客户，立即审查账户异常活动。", "label": [{"entity": "离异", "start_idx": 0, "end_idx": 1, "type": "婚姻状况"}, {"entity": "婚姻存续中", "start_idx": 3, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "丧偶且未登记状态客户，注意监控交易频率。", "label": [{"entity": "丧偶", "start_idx": 0, "end_idx": 1, "type": "婚姻状况"}]}, {"text": "已婚并婚姻解除者，请提交最新的交易记录。", "label": [{"entity": "已婚", "start_idx": 0, "end_idx": 1, "type": "婚姻状况"}, {"entity": "婚姻解除者", "start_idx": 3, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "订婚但婚姻解除客户，务必评估交易敏感度。", "label": [{"entity": "订婚", "start_idx": 0, "end_idx": 1, "type": "婚姻状况"}, {"entity": "婚姻解除", "start_idx": 3, "end_idx": 6, "type": "婚姻状况"}]}, {"text": "这份合同里写着你是未婚，也没登记过哦？", "label": [{"entity": "未婚", "start_idx": 9, "end_idx": 10, "type": "婚姻状况"}]}, {"text": "我看你资料里婚姻状况是未说明，现在是不是分居呢？", "label": [{"entity": "未说明", "start_idx": 11, "end_idx": 13, "type": "婚姻状况"}, {"entity": "分居", "start_idx": 20, "end_idx": 21, "type": "婚姻状况"}]}, {"text": "你这边是未说明再婚的情况，具体是怎么个情况？", "label": [{"entity": "再婚", "start_idx": 7, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "既然你已经订婚了，那合同里写的再婚又是怎么回事呢？", "label": [{"entity": "订婚", "start_idx": 5, "end_idx": 6, "type": "婚姻状况"}, {"entity": "再婚", "start_idx": 15, "end_idx": 16, "type": "婚姻状况"}]}, {"text": "合同里提到你有过再婚经历，现在又面临婚姻解除，挺复杂的啊。", "label": [{"entity": "再婚", "start_idx": 8, "end_idx": 9, "type": "婚姻状况"}, {"entity": "婚姻解除", "start_idx": 18, "end_idx": 21, "type": "婚姻状况"}]}, {"text": "无党派与中间派在银行交易监管政策上达成共识，共同呼吁加强金融风险防范。", "label": [{"entity": "无党派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "中间派", "start_idx": 4, "end_idx": 6, "type": "政治倾向"}]}, {"text": "自由派与女权主义者对银行交易中的性别歧视问题提出严正批评，倡导平等金融服务。", "label": [{"entity": "自由派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "女权主义者", "start_idx": 4, "end_idx": 8, "type": "政治倾向"}]}, {"text": "极左势力与中间派在银行交易税问题上存在分歧，引发社会各界广泛关注。", "label": [{"entity": "极左势力", "start_idx": 0, "end_idx": 3, "type": "政治倾向"}, {"entity": "中间派", "start_idx": 5, "end_idx": 7, "type": "政治倾向"}]}, {"text": "中立派与激进派就银行交易透明度改革展开激烈辩论，双方观点针锋相对。", "label": [{"entity": "中立派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "激进派", "start_idx": 4, "end_idx": 6, "type": "政治倾向"}]}, {"text": "无党派与极左阵营在银行交易监管力度上各有主张，争论不休。", "label": [{"entity": "无党派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "极左阵营", "start_idx": 4, "end_idx": 7, "type": "政治倾向"}]}, {"text": "进步派和保守派在这次金融改革上的意见分歧挺大的。", "label": [{"entity": "进步派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "保守派", "start_idx": 4, "end_idx": 6, "type": "政治倾向"}]}, {"text": "技术官僚们认为民族主义情绪会影响金融市场稳定。", "label": [{"entity": "民族主义", "start_idx": 7, "end_idx": 10, "type": "政治倾向"}]}, {"text": "改革派提出要重视女权主义在金融合同中的体现。", "label": [{"entity": "改革派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}]}, {"text": "保守派这次居然跟民族主义者在某项金融政策上达成了共识。", "label": [{"entity": "保守派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "民族主义者", "start_idx": 8, "end_idx": 12, "type": "政治倾向"}]}, {"text": "这场辩论中，民粹主义和自由主义的观点在金融合同问题上针锋相对。", "label": [{"entity": "民粹主义", "start_idx": 6, "end_idx": 9, "type": "政治倾向"}, {"entity": "自由主义", "start_idx": 11, "end_idx": 14, "type": "政治倾向"}]}, {"text": "表弟今天跟我爸去看球赛了，好期待晚上能听到他们的精彩解说。", "label": [{"entity": "表弟", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "我爸", "start_idx": 5, "end_idx": 6, "type": "家庭成员"}]}, {"text": "女儿和堂姐一起去逛街了，不知道会不会给我带礼物回来呢？", "label": [{"entity": "女儿", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "堂姐", "start_idx": 3, "end_idx": 4, "type": "家庭成员"}]}, {"text": "姨和堂姐打算开个直播，分享她们今天的化妆心得。", "label": [{"entity": "姨", "start_idx": 0, "end_idx": 0, "type": "家庭成员"}, {"entity": "堂姐", "start_idx": 2, "end_idx": 3, "type": "家庭成员"}]}, {"text": "我养女和她配偶刚从国外旅行回来，给我们带了好多纪念品。", "label": [{"entity": "养女", "start_idx": 1, "end_idx": 2, "type": "家庭成员"}, {"entity": "她配偶", "start_idx": 4, "end_idx": 6, "type": "家庭成员"}]}, {"text": "舅妈和外祖母正在商量下周一起去哪里旅游的事儿。", "label": [{"entity": "舅妈", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "外祖母", "start_idx": 3, "end_idx": 5, "type": "家庭成员"}]}, {"text": "舅舅，我姐妹最近身体不太舒服，你能不能帮忙看看？", "label": [{"entity": "舅舅", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "我姐妹", "start_idx": 3, "end_idx": 5, "type": "家庭成员"}]}, {"text": "表妹，听说舅舅是这方面的专家，我们去咨询一下他吧。", "label": [{"entity": "表妹", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "舅舅", "start_idx": 5, "end_idx": 6, "type": "家庭成员"}]}, {"text": "堂姐，你父亲的身体恢复得怎么样了？我们都很关心。", "label": [{"entity": "堂姐", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "你父亲", "start_idx": 3, "end_idx": 5, "type": "家庭成员"}]}, {"text": "女儿，这个病你养女也有，一定要注意预防哦。", "label": [{"entity": "女儿", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "养女", "start_idx": 7, "end_idx": 8, "type": "家庭成员"}]}, {"text": "表哥，继女最近总说身体不舒服，你能不能帮忙诊断一下？", "label": [{"entity": "表哥", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "继女", "start_idx": 3, "end_idx": 4, "type": "家庭成员"}]}, {"text": "据报告，该工人日结工资240元，年收入达到18.5万元。", "label": [{"entity": "240元", "start_idx": 11, "end_idx": 14, "type": "工资数额"}, {"entity": "18.5万元", "start_idx": 21, "end_idx": 26, "type": "工资数额"}]}, {"text": "调查数据显示，该岗位员工净收入16万，薪资总额按36个月计算为120,000元。", "label": [{"entity": "16万", "start_idx": 15, "end_idx": 17, "type": "工资数额"}, {"entity": "120,000元", "start_idx": 31, "end_idx": 38, "type": "工资数额"}]}, {"text": "在某公司，销售岗位每月固定薪酬9,200元，月收入含提成可达23,500元。", "label": [{"entity": "9,200元", "start_idx": 15, "end_idx": 20, "type": "工资数额"}, {"entity": "23,500元", "start_idx": 30, "end_idx": 36, "type": "工资数额"}]}, {"text": "该企业高级管理人员年薪总额为25万，年底还将获得一次性奖励45,000元。", "label": [{"entity": "25万", "start_idx": 14, "end_idx": 16, "type": "工资数额"}, {"entity": "45,000元", "start_idx": 29, "end_idx": 35, "type": "工资数额"}]}, {"text": "该科技公司员工年薪270,000元税后，每月净收入为10,300元。", "label": [{"entity": "270,000元", "start_idx": 9, "end_idx": 16, "type": "工资数额"}, {"entity": "10,300元", "start_idx": 26, "end_idx": 32, "type": "工资数额"}]}, {"text": "我每月的基本工资是9,200元，年底还能拿到80,000元的年终奖呢。", "label": [{"entity": "9,200元", "start_idx": 9, "end_idx": 14, "type": "工资数额"}, {"entity": "80,000元", "start_idx": 22, "end_idx": 28, "type": "工资数额"}]}, {"text": "我一年下来净收入有16万，年底还能额外得到45,000元的奖励。", "label": [{"entity": "16万", "start_idx": 9, "end_idx": 11, "type": "工资数额"}, {"entity": "45,000元", "start_idx": 21, "end_idx": 27, "type": "工资数额"}]}, {"text": "我税前的年收入是11万，算上三个月的薪资总额就是120,000元。", "label": [{"entity": "11万", "start_idx": 8, "end_idx": 10, "type": "工资数额"}, {"entity": "120,000元", "start_idx": 24, "end_idx": 31, "type": "工资数额"}]}, {"text": "我的年终奖是40,000元，税前的年薪总共能有230,000元。", "label": [{"entity": "40,000元", "start_idx": 6, "end_idx": 12, "type": "工资数额"}, {"entity": "230,000元", "start_idx": 23, "end_idx": 30, "type": "工资数额"}]}, {"text": "我每个季度的净收入是57,000元，税后的年薪大概有210,000元。", "label": [{"entity": "57,000元", "start_idx": 10, "end_idx": 16, "type": "工资数额"}, {"entity": "210,000元", "start_idx": 26, "end_idx": 33, "type": "工资数额"}]}, {"text": "股权众筹项目与保健品研发基金的交易在银行监管下顺利进行。", "label": [{"entity": "股权众筹项目", "start_idx": 0, "end_idx": 5, "type": "投资产品"}, {"entity": "保健品研发基金", "start_idx": 7, "end_idx": 13, "type": "投资产品"}]}, {"text": "高尔夫球场股权投资通过知识产权运营基金完成了资金配置。", "label": [{"entity": "知识产权运营基金", "start_idx": 11, "end_idx": 18, "type": "投资产品"}]}, {"text": "银行记录显示，矿产资源私募股权与保健品研发基金的交易额有所上升。", "label": [{"entity": "矿产资源私募股权", "start_idx": 7, "end_idx": 14, "type": "投资产品"}, {"entity": "保健品研发基金", "start_idx": 16, "end_idx": 22, "type": "投资产品"}]}, {"text": "跨境电商物流仓储项目吸引了长租公寓房地产投资的广泛关注。", "label": [{"entity": "跨境电商物流仓储项目", "start_idx": 0, "end_idx": 9, "type": "投资产品"}, {"entity": "长租公寓房地产", "start_idx": 13, "end_idx": 19, "type": "投资产品"}]}, {"text": "珍稀邮票投资组合成功吸引了文化遗产基金的目光，交易正在有序进行。", "label": [{"entity": "珍稀邮票投资组合", "start_idx": 0, "end_idx": 7, "type": "投资产品"}]}, {"text": "客户A：我最近想了解一下绿色能源债券和个性化教育投资计划，你们有什么推荐吗？", "label": [{"entity": "绿色能源债券", "start_idx": 12, "end_idx": 17, "type": "投资产品"}, {"entity": "个性化教育投资计划", "start_idx": 19, "end_idx": 27, "type": "投资产品"}]}, {"text": "客户B：私募股权份额投资我听说挺火，那个生态农业种植项目怎么样？", "label": [{"entity": "私募股权份额", "start_idx": 4, "end_idx": 9, "type": "投资产品"}]}, {"text": "客户C：农业科技股权投资和艺术品收藏品，你觉得哪个更适合我呢？", "label": [{"entity": "农业科技股权投资", "start_idx": 4, "end_idx": 11, "type": "投资产品"}, {"entity": "艺术品收藏品", "start_idx": 13, "end_idx": 18, "type": "投资产品"}]}, {"text": "客户D：我想问问，矿产资源私募股权和旅游产业的，哪个前景更好？", "label": [{"entity": "矿产资源私募股权", "start_idx": 9, "end_idx": 16, "type": "投资产品"}, {"entity": "旅游产业", "start_idx": 18, "end_idx": 21, "type": "投资产品"}]}, {"text": "客户E：人工智能研发基金感觉很有潜力，生态旅游度假村项目也不错，能给点建议吗？", "label": [{"entity": "人工智能研发基金", "start_idx": 4, "end_idx": 11, "type": "投资产品"}, {"entity": "生态旅游度假村项目", "start_idx": 19, "end_idx": 27, "type": "投资产品"}]}, {"text": "根据电子商务进口关税缴纳记录显示，多数企业已通过保险公司代扣代缴车险税费。", "label": [{"entity": "电子商务进口关税缴纳记录", "start_idx": 2, "end_idx": 13, "type": "税务记录"}, {"entity": "车险税费", "start_idx": 32, "end_idx": 35, "type": "税务记录"}]}, {"text": "税务局最新公告，增值税进项税额抵扣清单及印花税购销合同备案流程已完善。", "label": [{"entity": "增值税进项税额抵扣清单", "start_idx": 8, "end_idx": 18, "type": "税务记录"}, {"entity": "印花税购销合同备案流程", "start_idx": 20, "end_idx": 30, "type": "税务记录"}]}, {"text": "海关部门提醒，进口货物消费税缴纳凭证及委托加工业务增值税结算记录应齐全。", "label": [{"entity": "进口货物消费税缴纳凭证", "start_idx": 7, "end_idx": 17, "type": "税务记录"}, {"entity": "委托加工业务增值税结算记录", "start_idx": 19, "end_idx": 31, "type": "税务记录"}]}, {"text": "针对税收违法行为，税务局已作出处罚决定，相关进口货物消费税缴纳凭证须备查。", "label": [{"entity": "税收违法行为", "start_idx": 2, "end_idx": 7, "type": "税务记录"}, {"entity": "税务局", "start_idx": 9, "end_idx": 11, "type": "税务记录"}, {"entity": "处罚决定", "start_idx": 15, "end_idx": 18, "type": "税务记录"}, {"entity": "进口货物消费税缴纳凭证", "start_idx": 22, "end_idx": 32, "type": "税务记录"}]}, {"text": "个人所得税综合所得汇缴证明已开始受理，非居民企业源泉扣税记录亦同步更新。", "label": [{"entity": "个人所得税综合所得汇缴证明", "start_idx": 0, "end_idx": 12, "type": "税务记录"}, {"entity": "非居民企业源泉扣税记录", "start_idx": 19, "end_idx": 29, "type": "税务记录"}]}, {"text": "请提供以下记录：城市维护建设税应税项目申报及基础设施投资增值税抵扣记录。", "label": [{"entity": "城市维护建设税应税项目申报", "start_idx": 8, "end_idx": 20, "type": "税务记录"}, {"entity": "基础设施投资增值税抵扣记录", "start_idx": 22, "end_idx": 34, "type": "税务记录"}]}, {"text": "年度企业所得税年度汇缴记录和税务合规报告需要立即上传。", "label": [{"entity": "年度企业所得税年度汇缴记录", "start_idx": 0, "end_idx": 12, "type": "税务记录"}, {"entity": "税务合规报告", "start_idx": 14, "end_idx": 19, "type": "税务记录"}]}, {"text": "请查实税控加油机发票领用情况，并提交企业研发费用加计扣除申请。", "label": [{"entity": "税控加油机发票领用情况", "start_idx": 3, "end_idx": 13, "type": "税务记录"}, {"entity": "企业研发费用加计扣除申请", "start_idx": 18, "end_idx": 29, "type": "税务记录"}]}, {"text": "出口退税申报单据和个体工商户营业税申报单必须尽快处理。", "label": [{"entity": "出口退税申报单据", "start_idx": 0, "end_idx": 7, "type": "税务记录"}, {"entity": "个体工商户营业税申报单", "start_idx": 9, "end_idx": 19, "type": "税务记录"}]}, {"text": "请核对进口货物消费税缴纳凭证及电子商务进口关税缴纳记录。", "label": [{"entity": "进口货物消费税缴纳凭证", "start_idx": 3, "end_idx": 13, "type": "税务记录"}, {"entity": "电子商务进口关税缴纳记录", "start_idx": 15, "end_idx": 26, "type": "税务记录"}]}, {"text": "病例1：患者年车贷按时还款，无逾期，贷款账户信用良好，无拖欠记录。", "label": [{"entity": "患者年车贷按时还款", "start_idx": 4, "end_idx": 12, "type": "信用记录"}, {"entity": "无逾期", "start_idx": 14, "end_idx": 16, "type": "信用记录"}, {"entity": "贷款账户信用良好", "start_idx": 18, "end_idx": 25, "type": "信用记录"}, {"entity": "无拖欠记录", "start_idx": 27, "end_idx": 31, "type": "信用记录"}]}, {"text": "病例2：自年以来，患者无信用卡透支记录，无商业贷款拖欠，信用记录保持良好。", "label": [{"entity": "无信用卡透支记录", "start_idx": 11, "end_idx": 18, "type": "信用记录"}, {"entity": "无商业贷款拖欠", "start_idx": 20, "end_idx": 26, "type": "信用记录"}, {"entity": "信用记录保持良好", "start_idx": 28, "end_idx": 35, "type": "信用记录"}]}, {"text": "病例3：患者的信用账户未曾出现透支消费，年度信用卡账单均按时还款。", "label": [{"entity": "信用账户未曾出现透支消费", "start_idx": 7, "end_idx": 18, "type": "信用记录"}, {"entity": "年度信用卡账单均按时还款", "start_idx": 20, "end_idx": 31, "type": "信用记录"}]}, {"text": "病例4：患者个人资信状况良好，近三年内无贷款拖欠现象。", "label": [{"entity": "个人资信状况良好", "start_idx": 6, "end_idx": 13, "type": "信用记录"}, {"entity": "近三年内无贷款拖欠现象", "start_idx": 15, "end_idx": 25, "type": "信用记录"}]}, {"text": "病例5：患者信用行为优良，历史无任何信用污点。", "label": [{"entity": "信用行为优良", "start_idx": 6, "end_idx": 11, "type": "信用记录"}, {"entity": "历史无任何信用污点", "start_idx": 13, "end_idx": 21, "type": "信用记录"}]}, {"text": "根据银行交易记录，信用行为表现优良，年房贷按时还款，无逾期记录。", "label": [{"entity": "信用行为表现优良", "start_idx": 9, "end_idx": 16, "type": "信用记录"}, {"entity": "年房贷按时还款", "start_idx": 18, "end_idx": 24, "type": "信用记录"}, {"entity": "无逾期记录", "start_idx": 26, "end_idx": 30, "type": "信用记录"}]}, {"text": "银行征信报告显示正常，无银行贷款拖欠现象，信用评分保持稳定。", "label": [{"entity": "银行征信报告显示正常", "start_idx": 0, "end_idx": 9, "type": "信用记录"}, {"entity": "无银行贷款拖欠现象", "start_idx": 11, "end_idx": 19, "type": "信用记录"}, {"entity": "信用评分保持稳定", "start_idx": 21, "end_idx": 28, "type": "信用记录"}]}, {"text": "在信用历史中，自年初至今，无个人贷款拖欠的记录。", "label": [{"entity": "无个人贷款拖欠的记录", "start_idx": 13, "end_idx": 22, "type": "信用记录"}]}, {"text": "信用报告详细显示，无拖欠水电费等记录，亦无任何信用卡账户被追讨的情况。", "label": [{"entity": "无拖欠水电费等记录", "start_idx": 9, "end_idx": 17, "type": "信用记录"}, {"entity": "无任何信用卡账户被追讨的情况", "start_idx": 20, "end_idx": 33, "type": "信用记录"}]}, {"text": "综合信用报告分析，负债率维持在30%以下，且无虚拟信用卡逾期记录。", "label": [{"entity": "负债率维持在30%以下", "start_idx": 9, "end_idx": 19, "type": "信用记录"}, {"entity": "无虚拟信用卡逾期记录", "start_idx": 22, "end_idx": 31, "type": "信用记录"}]}, {"text": "青海省的那家西宁市城西区的藏式家具店，跟陕西碑林区的那个历史建筑，合同的事儿谈得怎么样了？", "label": [{"entity": "陕西碑林区的那个历史建筑", "start_idx": 20, "end_idx": 31, "type": "实体资产"}]}, {"text": "成都宽窄巷子那家餐馆真有特色，跟长春南关区的幼儿园比起来，俩地方的合同敏感度都挺高。", "label": [{"entity": "成都宽窄巷子那家餐馆", "start_idx": 0, "end_idx": 9, "type": "实体资产"}, {"entity": "长春南关区的幼儿园", "start_idx": 16, "end_idx": 24, "type": "实体资产"}]}, {"text": "听说大连湾渔港那艘30吨的渔船，跟南昌东湖区的老字号药店，最近都在谈合同？", "label": [{"entity": "大连湾渔港那艘30吨的渔船", "start_idx": 2, "end_idx": 14, "type": "实体资产"}, {"entity": "南昌东湖区的老字号药店", "start_idx": 17, "end_idx": 27, "type": "实体资产"}]}, {"text": "无锡宜兴那家紫砂壶工作室要跟实体商铺签合同了，这事儿你知道吗？", "label": [{"entity": "无锡宜兴那家紫砂壶工作室", "start_idx": 0, "end_idx": 11, "type": "实体资产"}, {"entity": "实体商铺", "start_idx": 14, "end_idx": 17, "type": "实体资产"}]}, {"text": "那个实体仓库跟昆明五华区的茶叶庄园，合同快到期了吧，得抓紧准备了。", "label": [{"entity": "那个实体仓库", "start_idx": 0, "end_idx": 5, "type": "实体资产"}, {"entity": "昆明五华区的茶叶庄园", "start_idx": 7, "end_idx": 16, "type": "实体资产"}]}, {"text": "吉林省长春市南关区的一所幼儿园发生火灾，同时，江西省上饶市婺源县的一座徽派建筑群正在接受文物部门评估。", "label": [{"entity": "吉林省长春市南关区的一所幼儿园", "start_idx": 0, "end_idx": 14, "type": "实体资产"}, {"entity": "江西省上饶市婺源县的一座徽派建筑群", "start_idx": 23, "end_idx": 39, "type": "实体资产"}]}, {"text": "四川省成都市武侯区一餐馆经营权易主，门面装修即将开始。", "label": [{"entity": "四川省成都市武侯区一餐馆经营权", "start_idx": 0, "end_idx": 14, "type": "实体资产"}]}, {"text": "海南省三亚市海棠湾一套度假别墅成功交易，同时深交所记录了1000股腾讯控股的转让。", "label": [{"entity": "海南省三亚市海棠湾一套度假别墅", "start_idx": 0, "end_idx": 14, "type": "实体资产"}, {"entity": "1000股腾讯控股", "start_idx": 28, "end_idx": 36, "type": "实体资产"}]}, {"text": "位于西安古城墙内的一间百年老字号店铺进行了翻新，附近的实体厂房也完成了扩建。", "label": [{"entity": "一间百年老字号店铺", "start_idx": 9, "end_idx": 17, "type": "实体资产"}, {"entity": "附近的实体厂房", "start_idx": 24, "end_idx": 30, "type": "实体资产"}]}, {"text": "一套新引进的实体设备在厂房内投入使用，提升了生产效率。", "label": [{"entity": "一套新引进的实体设备", "start_idx": 0, "end_idx": 9, "type": "实体资产"}]}, {"text": "本次虚拟货币交易不得少于0.5BTC，数字货币交易同样需达到0.5BTC。", "label": [{"entity": "0.5BTC", "start_idx": 12, "end_idx": 17, "type": "交易信息"}, {"entity": "0.5BTC", "start_idx": 12, "end_idx": 17, "type": "交易信息"}]}, {"text": "请在交易时输入验证码990065，并核对交易流水日期是否为2024年6月。", "label": [{"entity": "验证码990065", "start_idx": 7, "end_idx": 15, "type": "交易信息"}, {"entity": "2024年6月", "start_idx": 29, "end_idx": 35, "type": "交易信息"}]}, {"text": "信用卡消费记录须达到800美元，现金支付务必保留5元找零。", "label": [{"entity": "800美元", "start_idx": 10, "end_idx": 14, "type": "交易信息"}, {"entity": "5元", "start_idx": 24, "end_idx": 25, "type": "交易信息"}]}, {"text": "网银转账时请注意手续费为10元，退款申请请参考编号RK20240618。", "label": [{"entity": "手续费为10元", "start_idx": 8, "end_idx": 14, "type": "交易信息"}, {"entity": "编号RK20240618", "start_idx": 23, "end_idx": 34, "type": "交易信息"}]}, {"text": "账户冻结金额须确保为10000元，同时进行购物积分1000分的兑换。", "label": [{"entity": "购物积分1000分", "start_idx": 21, "end_idx": 29, "type": "交易信息"}]}, {"text": "微信支付5元交易与账户提现4000元操作，已严格按照法律规定进行。", "label": [{"entity": "微信支付5元", "start_idx": 0, "end_idx": 5, "type": "交易信息"}, {"entity": "账户提现4000元", "start_idx": 9, "end_idx": 17, "type": "交易信息"}]}, {"text": "交易附言中注明“生日快乐”，同时使用支付宝红包成功抵扣5元。", "label": [{"entity": "5元", "start_idx": 27, "end_idx": 28, "type": "交易信息"}]}, {"text": "本次审计发现，信用卡消费记录800美元与投资项目分红1000元均符合规定范围。", "label": [{"entity": "800美元", "start_idx": 14, "end_idx": 18, "type": "交易信息"}, {"entity": "1000元", "start_idx": 26, "end_idx": 30, "type": "交易信息"}]}, {"text": "在最新跨境支付业务中，手续费50元与微信转账200元已被明确记录在案。", "label": [{"entity": "手续费50元", "start_idx": 11, "end_idx": 16, "type": "交易信息"}, {"entity": "微信转账200元", "start_idx": 18, "end_idx": 25, "type": "交易信息"}]}, {"text": "法律文档显示，投资项目分红1000元已发放至交易对手方账户，户主为张三。", "label": [{"entity": "1000元", "start_idx": 13, "end_idx": 17, "type": "交易信息"}, {"entity": "交易对手方账户", "start_idx": 22, "end_idx": 28, "type": "交易信息"}]}, {"text": "本案涉及原告对羊毛纤维的皮肤刺激及对梧桐树花粉引起的眼睛痒症状。", "label": [{"entity": "对羊毛纤维的皮肤刺激", "start_idx": 6, "end_idx": 15, "type": "过敏信息"}, {"entity": "对梧桐树花粉引起的眼睛痒症状", "start_idx": 17, "end_idx": 30, "type": "过敏信息"}]}, {"text": "患者表现出对乳制品不耐受，并伴有对热带水果的过敏反应。", "label": [{"entity": "对乳制品不耐受", "start_idx": 5, "end_idx": 11, "type": "过敏信息"}, {"entity": "对热带水果的过敏反应", "start_idx": 16, "end_idx": 25, "type": "过敏信息"}]}, {"text": "测试结果显示，受试者对金属镍存在过敏反应，并对特定防腐剂有不良反应。", "label": [{"entity": "对金属镍存在过敏反应", "start_idx": 10, "end_idx": 19, "type": "过敏信息"}, {"entity": "对特定防腐剂有不良反应", "start_idx": 22, "end_idx": 32, "type": "过敏信息"}]}, {"text": "病例中，患者对腰果存在过敏史，同时猫毛也引起了其呼吸道不适。", "label": [{"entity": "腰果", "start_idx": 7, "end_idx": 8, "type": "过敏信息"}, {"entity": "猫毛", "start_idx": 17, "end_idx": 18, "type": "过敏信息"}]}, {"text": "在本次法律诉讼中，原告声称对特定食物排斥，并在秋天出现枯草热症状。", "label": [{"entity": "对特定食物排斥", "start_idx": 13, "end_idx": 19, "type": "过敏信息"}, {"entity": "枯草热症状", "start_idx": 27, "end_idx": 31, "type": "过敏信息"}]}, {"text": "对羊肉过敏者出现的皮疹及对热带水果常见的过敏反应，需引起注意。", "label": [{"entity": "对羊肉过敏者出现的皮疹", "start_idx": 0, "end_idx": 10, "type": "过敏信息"}, {"entity": "对热带水果常见的过敏反应", "start_idx": 12, "end_idx": 23, "type": "过敏信息"}]}, {"text": "部分人群对鸡蛋白摄入后易出现消化不良，对狗毛接触可能导致眼睛发炎。", "label": [{"entity": "对鸡蛋白摄入后易出现消化不良", "start_idx": 4, "end_idx": 17, "type": "过敏信息"}, {"entity": "对狗毛接触可能导致眼睛发炎", "start_idx": 19, "end_idx": 31, "type": "过敏信息"}]}, {"text": "大豆蛋白质敏感性问题及对特定药物的过敏反应，应予以足够重视。", "label": [{"entity": "大豆蛋白质敏感性", "start_idx": 0, "end_idx": 7, "type": "过敏信息"}, {"entity": "对特定药物的过敏反应", "start_idx": 11, "end_idx": 20, "type": "过敏信息"}]}, {"text": "某些杀虫剂可引发呼吸道不适，与羊肉过敏所致皮疹同属关注范畴。", "label": [{"entity": "羊肉过敏", "start_idx": 15, "end_idx": 18, "type": "过敏信息"}]}, {"text": "芒果接触性皮肤不适及化妆品中化学成分引发的过敏反应，亦需加强防范。", "label": [{"entity": "芒果接触性皮肤不适", "start_idx": 0, "end_idx": 8, "type": "过敏信息"}, {"entity": "化妆品中化学成分引发的过敏反应", "start_idx": 10, "end_idx": 24, "type": "过敏信息"}]}, {"text": "三孩父亲在合同中记录了首次怀孕的相关信息。", "label": [{"entity": "三孩父亲", "start_idx": 0, "end_idx": 3, "type": "生育信息"}, {"entity": "首次怀孕", "start_idx": 11, "end_idx": 14, "type": "生育信息"}]}, {"text": "根据合同显示，该客户已成家有女，同时记录了首次怀孕的情况。", "label": [{"entity": "已成家有女", "start_idx": 10, "end_idx": 14, "type": "生育信息"}, {"entity": "首次怀孕", "start_idx": 21, "end_idx": 24, "type": "生育信息"}]}, {"text": "生育实况表明，该家庭已育有一儿一女，符合金融产品的申请条件。", "label": [{"entity": "已育有一儿一女", "start_idx": 10, "end_idx": 16, "type": "生育信息"}]}, {"text": "生殖资讯卡片中，详细记录了三孩父亲的生育状况。", "label": [{"entity": "三孩父亲", "start_idx": 13, "end_idx": 16, "type": "生育信息"}]}, {"text": "有子一族，请立即提交你们的生育历史数据以供分析。", "label": [{"entity": "有子一族", "start_idx": 0, "end_idx": 3, "type": "生育信息"}]}, {"text": "针对育有一儿一女的家庭，速将孕产详情报告相关部门。", "label": [{"entity": "育有一儿一女", "start_idx": 2, "end_idx": 7, "type": "生育信息"}]}, {"text": "家庭人口动态变化及孕育新生命的详情，必须按时上报。", "label": [{"entity": "孕育新生命", "start_idx": 9, "end_idx": 13, "type": "生育信息"}]}, {"text": "有子一族，迅速整理并上报你们的孕产历程。", "label": [{"entity": "孕产历程", "start_idx": 15, "end_idx": 18, "type": "生育信息"}]}, {"text": "生育相关说明应详细记录，特别是育有两女的家庭。", "label": [{"entity": "育有两女", "start_idx": 15, "end_idx": 18, "type": "生育信息"}]}, {"text": "家庭生育次数及生育状况概述必须详细记录于法律文档中。", "label": [{"entity": "家庭生育次数及生育状况概述", "start_idx": 0, "end_idx": 12, "type": "生育信息"}]}, {"text": "年11月深圳至北京的故宫之旅，行程涉及历史文化遗产法律保护范围。", "label": [{"entity": "年11月深圳至北京的故宫之旅", "start_idx": 0, "end_idx": 13, "type": "行程信息"}]}, {"text": "月22日从北京到三亚的亲子度假，需关注亲子旅游相关的法律规定。", "label": [{"entity": "月22日从北京到三亚的亲子度假", "start_idx": 0, "end_idx": 14, "type": "行程信息"}]}, {"text": "明年儿童节北京到哈尔滨亲子游，涉及节假日旅游合同法律条款。", "label": [{"entity": "明年儿童节北京到哈尔滨亲子游", "start_idx": 0, "end_idx": 13, "type": "行程信息"}]}, {"text": "月11日广州飞往乌鲁木齐的航线，法律明文规定了航班运行的相关责任。", "label": [{"entity": "月11日广州飞往乌鲁木齐的航线", "start_idx": 0, "end_idx": 14, "type": "行程信息"}]}, {"text": "明年9月上海至成都的熊猫守护之旅，活动需遵守野生动物保护法相关规定。", "label": [{"entity": "明年9月上海至成都的熊猫守护之旅", "start_idx": 0, "end_idx": 15, "type": "行程信息"}]}, {"text": "明年清明节我想和朋友一起去杭州然后春游到黄山，想想都挺期待的，今年春节我还去了青岛度假呢。", "label": [{"entity": "明年清明节", "start_idx": 0, "end_idx": 4, "type": "行程信息"}, {"entity": "杭州", "start_idx": 13, "end_idx": 14, "type": "行程信息"}, {"entity": "春游到黄山", "start_idx": 17, "end_idx": 21, "type": "行程信息"}, {"entity": "今年春节", "start_idx": 31, "end_idx": 34, "type": "行程信息"}, {"entity": "青岛度假", "start_idx": 39, "end_idx": 42, "type": "行程信息"}]}, {"text": "春节的时候我和几个朋友打算从武汉一路吃到广州去，月底还计划带小朋友从北京飞到三亚玩儿。", "label": [{"entity": "春节的时候我和几个朋友打算从武汉一路吃到广州去", "start_idx": 0, "end_idx": 22, "type": "行程信息"}, {"entity": "月底还计划带小朋友从北京飞到三亚玩儿", "start_idx": 24, "end_idx": 41, "type": "行程信息"}]}, {"text": "九号的时候，我和几个朋友从成都去青岛看海了，国庆节我还打算去四川的九寨沟玩儿。", "label": [{"entity": "从成都去青岛看海", "start_idx": 12, "end_idx": 19, "type": "行程信息"}, {"entity": "国庆节我还打算去四川的九寨沟玩儿", "start_idx": 22, "end_idx": 37, "type": "行程信息"}]}, {"text": "记得九号的时候我去青岛玩了，二十号又打算和对象从上海到西安来个浪漫之旅。", "label": [{"entity": "九号的时候我去青岛玩了", "start_idx": 2, "end_idx": 12, "type": "行程信息"}, {"entity": "二十号又打算和对象从上海到西安来个浪漫之旅", "start_idx": 14, "end_idx": 34, "type": "行程信息"}]}, {"text": "成都到青岛的海滨度假我还挺怀念的，一月份的时候我又和爱人从南京出发，去厦门度了个蜜月。", "label": [{"entity": "成都到青岛的海滨度假", "start_idx": 0, "end_idx": 9, "type": "行程信息"}, {"entity": "一月份的时候我又和爱人从南京出发", "start_idx": 17, "end_idx": 32, "type": "行程信息"}, {"entity": "去厦门度了个蜜月", "start_idx": 34, "end_idx": 41, "type": "行程信息"}]}]