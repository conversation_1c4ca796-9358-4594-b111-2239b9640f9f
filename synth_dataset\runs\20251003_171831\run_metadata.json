{"timestamp": "20251003_171831", "start_time": "2025-10-03T17:18:31.777251", "status": "running", "config": {"dataset_path": "format-dataset\\privacy_bench_small_10.json", "target_count": 20, "max_iterations": 10, "batch_size": 10, "distribution_threshold": 0.05, "generation_features": {"use_sentence_diversity": true, "use_entity_diversity": true, "use_example_sentences": true}}, "directories": {"root": "synth_dataset\\runs\\20251003_171831", "config": "synth_dataset\\runs\\20251003_171831\\config", "source": "synth_dataset\\runs\\20251003_171831\\source", "intermediate": "synth_dataset\\runs\\20251003_171831\\intermediate", "intermediate_raw": "synth_dataset\\runs\\20251003_171831\\intermediate\\raw", "intermediate_processed": "synth_dataset\\runs\\20251003_171831\\intermediate\\processed", "intermediate_by_entity": "synth_dataset\\runs\\20251003_171831\\intermediate\\by_entity", "iterations": "synth_dataset\\runs\\20251003_171831\\iterations", "strategies": "synth_dataset\\runs\\20251003_171831\\strategies", "output": "synth_dataset\\runs\\20251003_171831\\output", "evaluation": "synth_dataset\\runs\\20251003_171831\\evaluation", "logs": "synth_dataset\\runs\\20251003_171831\\logs"}, "last_updated": "2025-10-03T17:18:31.836902", "stage": "iteration", "current_iteration": 1, "max_iterations": 10}