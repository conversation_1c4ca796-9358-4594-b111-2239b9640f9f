{"examples": [{"sentence": "a four star restaurant with a bar", "entity_names": ["four star", "with a", "bar"], "entity_types": ["Rating", "Location", "Amenity"]}, {"sentence": "any asian cuisine around", "entity_names": ["asian", "around"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "any bbq places open before 5 nearby", "entity_names": ["bbq", "open before 5", "nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Hours", "Location"]}, {"sentence": "any dancing establishments with reasonable pricing", "entity_names": ["dancing establishments", "reasonable"], "entity_types": ["Location", "Price"]}, {"sentence": "any good cheap german restaurants nearby", "entity_names": ["cheap", "german", "nearby"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "any good ice cream parlors around", "entity_names": ["good", "ice cream parlors", "around"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "any good place to get a pie at an affordable price", "entity_names": ["good", "pie", "affordable"], "entity_types": ["Rating", "Dish", "Price"]}, {"sentence": "any good vegan spots nearby", "entity_names": ["vegan", "nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "any mexican places have a tameles special today", "entity_names": ["mexican", "tameles", "special today"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Dish", "Amenity"]}, {"sentence": "any place along the road has a good beer selection that also serves ribs", "entity_names": ["along the road", "good", "beer", "ribs"], "entity_types": ["Location", "Rating", "Dish", "Dish"]}, {"sentence": "any places around here that has a nice view", "entity_names": ["around here", "nice view"], "entity_types": ["Location", "Amenity"]}, {"sentence": "any reasonably priced indian restaurants in the theater district", "entity_names": ["reasonably", "indian", "in the theater district"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "any restaurants open right now", "entity_names": ["open right now"], "entity_types": ["Hours"]}, {"sentence": "any restaurants that still allow smoking", "entity_names": ["smoking"], "entity_types": ["Amenity"]}, {"sentence": "any stores around where i could buy a pasta dish where the prices are not too high", "entity_names": ["around", "pasta", "prices are not too high"], "entity_types": ["Location", "Dish", "Price"]}, {"sentence": "anything on the avenue", "entity_names": ["avenue"], "entity_types": ["Location"]}, {"sentence": "anything open after midnight with reasonable prices", "entity_names": ["open after midnight", "reasonable"], "entity_types": ["Hours", "Price"]}, {"sentence": "are children allowed in this particular sitting area", "entity_names": ["children", "sitting area"], "entity_types": ["Amenity", "Amenity"]}, {"sentence": "are reservations available for four people for 8 pm tonight at 112 eatery", "entity_names": ["112 eatery"], "entity_types": ["Restaurant Name"]}, {"sentence": "are the portion at le bec fin large or very small", "entity_names": ["le bec fin"], "entity_types": ["Restaurant Name"]}, {"sentence": "are there any 24 hour breakfast places nearby", "entity_names": ["24 hour", "breakfast", "nearby"], "entity_types": ["Hours", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "are there any 50s style diners in glendale", "entity_names": ["50s style", "diners", "glendale"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "are there any authentic mexican restaurants in the area", "entity_names": ["authentic mexican", "in the area"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "are there any bars nearby that serve food like italian or french", "entity_names": ["bars", "nearby", "italian", "french"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "are there any brewpubs downtown", "entity_names": ["brewpubs", "downtown"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "are there any cafeterias near", "entity_names": ["cafeterias", "near"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "are there any charlestown restaurants open very early for lunch", "entity_names": ["charlestown", "open very early", "lunch"], "entity_types": ["Location", "Hours", "Hours"]}, {"sentence": "are there any chick fil as in the city open on sunday", "entity_names": ["chick fil as", "in the city", "sunday"], "entity_types": ["Restaurant Name", "Location", "Hours"]}, {"sentence": "are there any chicken wing places nearby", "entity_names": ["chicken wing", "nearby"], "entity_types": ["Dish", "Location"]}, {"sentence": "are there any child friendly restaurants within ten miles", "entity_names": ["child friendly", "within ten miles"], "entity_types": ["Amenity", "Location"]}, {"sentence": "are there any chinese restaurants near cheyenne", "entity_names": ["chinese", "near cheyenne"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "are there any crab restaurants near here that are open late until 2 a m", "entity_names": ["crab", "open late until 2 a m"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Hours"]}, {"sentence": "are there any dining specials at le bec fin", "entity_names": ["specials", "le bec fin"], "entity_types": ["Amenity", "Restaurant Name"]}, {"sentence": "are there any donut and donuts within 5 minutes drive that has an extensive beer menu", "entity_names": ["donut and donuts", "within 5 minutes drive", "extensive beer menu"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "are there any eatery at the hotel downtown", "entity_names": ["hotel downtown"], "entity_types": ["Location"]}, {"sentence": "are there any exciting joints along the way thats reasonably priced", "entity_names": ["exciting", "along the way", "reasonably"], "entity_types": ["Amenity", "Location", "Price"]}, {"sentence": "are there any fancy cambodian places on seaver street", "entity_names": ["fancy", "cambodian", "seaver street"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "are there any fast food joints east of here", "entity_names": ["fast food", "east of here"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "are there any fast food restaurants that are kid friendly", "entity_names": ["fast food", "kid friendly"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "are there any fine dining options within 5 miles of my location", "entity_names": ["fine dining", "within 5 miles"], "entity_types": ["Amenity", "Location"]}, {"sentence": "are there any five star restaurants around here", "entity_names": ["five star", "around here"], "entity_types": ["Rating", "Location"]}, {"sentence": "are there any four star restaurants in this town", "entity_names": ["four star", "town"], "entity_types": ["Rating", "Location"]}, {"sentence": "are there any fun restaurants serving brisket in town", "entity_names": ["fun", "brisket", "in town"], "entity_types": ["Amenity", "Dish", "Location"]}, {"sentence": "are there any good family style restaurants around boston", "entity_names": ["family style", "around boston"], "entity_types": ["Amenity", "Location"]}, {"sentence": "are there any good soul food restaurants near by", "entity_names": ["good", "soul", "near by"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "are there any greek restaurants in the area", "entity_names": ["greek", "in the area"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "are there any greek restaurants in the theater district of the back bay", "entity_names": ["greek", "theater district", "back bay"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Location"]}, {"sentence": "are there any hamburger restaurants close by", "entity_names": ["hamburger", "close by"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "are there any hotels nearby that have private rooms available at 1 am", "entity_names": ["1"], "entity_types": ["Hours"]}, {"sentence": "are there any ice cream shops in my neighborhood that are open right now", "entity_names": ["ice cream", "in my neighborhood", "open right now"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Hours"]}, {"sentence": "are there any indian restaurants on long island", "entity_names": ["indian", "long island"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "are there any italian eateries nearby", "entity_names": ["italian", "nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "are there any japanese restaurants in town that do discounts for bulk orders of sushi", "entity_names": ["japanese", "in town", "discounts for bulk orders", "sushi"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Amenity", "Amenity"]}, {"sentence": "are there any jazz clubs that serve food", "entity_names": ["jazz clubs"], "entity_types": ["Amenity"]}, {"sentence": "are there any kid friendly restaurants close by", "entity_names": ["kid friendly", "close by"], "entity_types": ["Amenity", "Location"]}, {"sentence": "are there any kid friendly restaurants with valet parking", "entity_names": ["kid friendly", "valet parking"], "entity_types": ["Amenity", "Amenity"]}, {"sentence": "are there any locally owned franchises that give money to charity", "entity_names": ["locally owned", "give money to charity"], "entity_types": ["Amenity", "Amenity"]}, {"sentence": "are there any maid cafe in town", "entity_names": ["maid cafe", "in town"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "are there any mcdonalds that i can drive too in 3 minutes", "entity_names": ["mc<PERSON><PERSON><PERSON>", "in 3 minutes"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "are there any mid priced restaurants within 5 miles that offer curb side pickup", "entity_names": ["mid", "within 5 miles", "offer curb side pickup"], "entity_types": ["Price", "Location", "Amenity"]}, {"sentence": "are there any nationally known chefs with restaurants in this city", "entity_names": ["nationally known", "in this city"], "entity_types": ["Rating", "Location"]}, {"sentence": "are there any new restaurants nearby that i can try", "entity_names": ["new", "nearby"], "entity_types": ["Amenity", "Location"]}, {"sentence": "are there any nice taco places nearby open for breakfast", "entity_names": ["nice", "taco", "nearby", "open for breakfast"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location", "Hours"]}, {"sentence": "are there any place close by that is open 24 hours", "entity_names": ["close", "open 24 hours"], "entity_types": ["Location", "Hours"]}, {"sentence": "are there any places around here that has tomato sauce based dishes", "entity_names": ["around here", "tomato sauce based dishes"], "entity_types": ["Location", "Dish"]}, {"sentence": "are there any places left that allow smoking in a restaraunt", "entity_names": ["allow smoking"], "entity_types": ["Amenity"]}, {"sentence": "are there any places near by that sell hamburgers and pizza", "entity_names": ["near by", "hamburgers", "pizza"], "entity_types": ["Location", "Dish", "Dish"]}, {"sentence": "are there any places near by that serve lunch all day", "entity_names": ["near by", "lunch all day"], "entity_types": ["Location", "Hours"]}, {"sentence": "are there any places to eat in the area that offer a two for one special", "entity_names": ["in the area", "two for one special"], "entity_types": ["Location", "Amenity"]}, {"sentence": "are there any places with a notable beer list on white st", "entity_names": ["notable", "beer list", "white st"], "entity_types": ["Rating", "Amenity", "Location"]}, {"sentence": "are there any reasonably priced restaurants on east haverhill street that serve cheese", "entity_names": ["reasonably", "east haverhill street", "cheese"], "entity_types": ["Price", "Location", "Dish"]}, {"sentence": "are there any restaurant nearby that serve thai food", "entity_names": ["nearby", "thai"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "are there any restaurants around with a smoking area", "entity_names": ["smoking area"], "entity_types": ["Amenity"]}, {"sentence": "are there any restaurants for diabetics that serve sugar free desserts", "entity_names": ["sugar free desserts"], "entity_types": ["Dish"]}, {"sentence": "are there any restaurants nearby that have great reviews and plenty of parking", "entity_names": ["nearby", "great reviews", "plenty of parking"], "entity_types": ["Location", "Rating", "Amenity"]}, {"sentence": "are there any restaurants nearby that have outdoor dining", "entity_names": ["nearby", "outdoor dining"], "entity_types": ["Location", "Amenity"]}, {"sentence": "are there any restaurants on kilmarnock street that feature large portions and a brewpub", "entity_names": ["kilmarnock street", "large portions", "brewpub"], "entity_types": ["Location", "Amenity", "Amenity"]}, {"sentence": "are there any restaurants on the way that serve hamburgers and are open after 1 am", "entity_names": ["way", "hamburgers", "open after 1 am"], "entity_types": ["Location", "Dish", "Hours"]}, {"sentence": "are there any restaurants on the way to my destination that have a fireplace inside", "entity_names": ["way to my destination", "fireplace inside"], "entity_types": ["Location", "Amenity"]}, {"sentence": "are there any restaurants on this side of the river", "entity_names": ["this side of the river"], "entity_types": ["Location"]}, {"sentence": "are there any restaurants open after 2 am", "entity_names": ["open after 2 am"], "entity_types": ["Hours"]}, {"sentence": "are there any restaurants that are open 24 hours", "entity_names": ["24 hours"], "entity_types": ["Hours"]}, {"sentence": "are there any restaurants that serve raw and organic food nearby", "entity_names": ["raw and organic", "nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "are there any restaurants that will let me take my dog in with me", "entity_names": ["take my dog in with me"], "entity_types": ["Amenity"]}, {"sentence": "are there any restaurants with happy hour in the area", "entity_names": ["happy hour", "in the area"], "entity_types": ["Amenity", "Location"]}, {"sentence": "are there any restaurants with valet parking and a multilingual staff near here", "entity_names": ["valet parking", "multilingual staff", "near here"], "entity_types": ["Amenity", "Amenity", "Location"]}, {"sentence": "are there any restaurants within 5 miles that accept travelers checks", "entity_names": ["within 5 miles", "accept travelers checks"], "entity_types": ["Location", "Amenity"]}, {"sentence": "are there any rib joints nearby", "entity_names": ["rib joints", "nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "are there any seafood restaurants near government center where i can make online reservations", "entity_names": ["seafood", "near government center", "online reservations"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Location"]}, {"sentence": "are there any spanish restaurants that are cheap", "entity_names": ["spanish", "cheap"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Price"]}, {"sentence": "are there any steak houses within 3 miles of me", "entity_names": ["steak", "within 3 miles"], "entity_types": ["Dish", "Location"]}, {"sentence": "are there any sub sandwich shops that also serve beer", "entity_names": ["sub sandwich", "beer"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Dish"]}, {"sentence": "are there any sushi restaurants near colonel bell drive", "entity_names": ["sushi", "near colonel bell drive"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "are there any tapas restaurants with good reviews nearby", "entity_names": ["tapas", "good reviews", "nearby"], "entity_types": ["Dish", "Rating", "Location"]}, {"sentence": "are there any turkish restaurants in florida", "entity_names": ["turkish", "florida"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "are there any vegan spots that are open after 11 at night", "entity_names": ["vegan spots", "open after 11 at night"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Hours"]}, {"sentence": "are there any vegetarian restaurants in this town", "entity_names": ["vegetarian", "in this town"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "are there any vegetarian restaurants that allow you to order online ahead of time", "entity_names": ["vegetarian", "order online ahead of time"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "are there any vietnamese restaurants nearby", "entity_names": ["vietnamese", "nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "are there are any cracker barrells on long island", "entity_names": ["cracker barrells", "long island"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "are there reservations still available for bar la grassa for 2 tomorrow at 7 pm", "entity_names": ["bar la grassa", "tomorrow at 7 pm"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "areas that allow smoking", "entity_names": ["allow smoking"], "entity_types": ["Amenity"]}, {"sentence": "asian cuisine in my zip code", "entity_names": ["asian", "in my zip code"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "at which french restaurant can i dine outdoors", "entity_names": ["french", "dine outdoors"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "beer and hot wings in town", "entity_names": ["beer", "hot wings", "in town"], "entity_types": ["Dish", "Dish", "Location"]}, {"sentence": "best chinese food in the area", "entity_names": ["best", "chinese", "in the area"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "borscht", "entity_names": ["borscht"], "entity_types": ["Dish"]}, {"sentence": "bradford lantern cafe directions", "entity_names": ["bradford lantern cafe"], "entity_types": ["Restaurant Name"]}, {"sentence": "brasil cuisine near me", "entity_names": ["brasil", "near me"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "burgers", "entity_names": ["burgers"], "entity_types": ["Dish"]}, {"sentence": "cafes on ashlannd street", "entity_names": ["cafes", "ashlannd street"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "call cheeseboard in berkeley for me", "entity_names": ["cheeseboard", "berkeley"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "call chinese", "entity_names": ["chinese"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "call dominos", "entity_names": ["dominos"], "entity_types": ["Restaurant Name"]}, {"sentence": "call the closest korean restaurant", "entity_names": ["closest", "korean"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "can i bring my kid to any of the restaurants down town that have bars attached", "entity_names": ["my kid", "down town", "bars attached"], "entity_types": ["Amenity", "Location", "Amenity"]}, {"sentence": "can i bring my pet iguana to the japanese restaurant", "entity_names": ["pet", "japanese"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "can i dine at the barat a nossa casa", "entity_names": ["barat a nossa casa"], "entity_types": ["Restaurant Name"]}, {"sentence": "can i find a bar and grill within short walking distance of the shopping district", "entity_names": ["bar and grill", "short walking distance of the shopping district"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "can i find a good chinese buffet within 3 miles from me", "entity_names": ["good", "chinese", "buffet", "within 3 miles"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Amenity", "Location"]}, {"sentence": "can i find any restaurants close by with a meal under 8", "entity_names": ["close by", "meal under 8"], "entity_types": ["Location", "Price"]}, {"sentence": "can i find good portugues", "entity_names": ["good", "portugues"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "can i get a chefands table on north bedford street very late at night", "entity_names": ["chefands table", "north bedford street", "very late at night"], "entity_types": ["Amenity", "Location", "Hours"]}, {"sentence": "can i get a list of close fast food places", "entity_names": ["close", "fast food"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "can i get gluten free pizza within 10 miles of here", "entity_names": ["gluten free pizza", "within 10 miles of here"], "entity_types": ["Dish", "Location"]}, {"sentence": "can i get hambers at lone star", "entity_names": ["hambers", "lone star"], "entity_types": ["Dish", "Restaurant Name"]}, {"sentence": "can i get raw vegan food in honolulu", "entity_names": ["raw vegan", "honolulu"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "can i get sushi on a prix fixe menu with reasonable prices", "entity_names": ["sushi", "prix fixe menu", "reasonable"], "entity_types": ["Dish", "Amenity", "Price"]}, {"sentence": "can i have the phone number for kfc in los angeles ca", "entity_names": ["kfc", "los angeles ca"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "can i see hamburger restaurants nearby", "entity_names": ["hamburger", "nearby"], "entity_types": ["Dish", "Location"]}, {"sentence": "can i valet park at the blue coyote grill", "entity_names": ["valet park", "blue coyote grill"], "entity_types": ["Amenity", "Restaurant Name"]}, {"sentence": "can i wear shorts", "entity_names": ["wear shorts"], "entity_types": ["Amenity"]}, {"sentence": "can you find a bar that serves tapas and takes reservations for happy hour", "entity_names": ["bar", "tapas", "reservations", "happy hour"], "entity_types": ["Amenity", "Dish", "Amenity", "Amenity"]}, {"sentence": "can you find a burger joint with a smoking section", "entity_names": ["burger joint", "smoking section"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "can you find a cheap vietnamese restaurant", "entity_names": ["cheap", "vietnamese"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "can you find a fast food restaurant", "entity_names": ["fast food"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "can you find a highly rated long john silvers open this late", "entity_names": ["highly rated", "long john silvers", "open this late"], "entity_types": ["Rating", "Restaurant Name", "Hours"]}, {"sentence": "can you find a middle eastern place with great prices", "entity_names": ["middle eastern", "great"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Price"]}, {"sentence": "can you find a pizza place with a buffet within 15 miles", "entity_names": ["pizza", "buffet", "within 15 miles"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity", "Location"]}, {"sentence": "can you find a pub downtown", "entity_names": ["pub", "downtown"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "can you find a restaurant that serves bean soup in the morning and isnt too expensive", "entity_names": ["bean soup", "morning", "isnt too expensive"], "entity_types": ["Dish", "Hours", "Price"]}, {"sentence": "can you find a restaurant that serves duck that not cheap near here", "entity_names": ["duck", "not cheap", "near"], "entity_types": ["Dish", "Price", "Location"]}, {"sentence": "can you find a restaurant under 20 per dish", "entity_names": ["under 20 per dish"], "entity_types": ["Price"]}, {"sentence": "can you find a seafood restaurant", "entity_names": ["seafood"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "can you find a site where i can see reviews on restaurant downtown", "entity_names": ["downtown"], "entity_types": ["Location"]}, {"sentence": "can you find a steak house that serves wine", "entity_names": ["steak house", "wine"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Dish"]}, {"sentence": "can you find a thai japanese fusion restaurant in town", "entity_names": ["thai japanese fusion", "in town"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "can you find an italian restaurant nearby", "entity_names": ["italian", "nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "can you find an italian restaurant that serves brunch on sunday", "entity_names": ["italian", "brunch", "sunday"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Hours", "Hours"]}, {"sentence": "can you find an italian restaurant where i can wear casual atire", "entity_names": ["italian", "casual atire"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "can you find east dedham pizzeria that have a dine at bar location", "entity_names": ["east", "dedham", "pizzeria"], "entity_types": ["Restaurant Name", "Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "can you find latin american cuisine within a hotel along the way", "entity_names": ["latin american", "within a hotel along the way"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "can you find me a burmese restaurant with a parking spot", "entity_names": ["burmese", "parking spot"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "can you find me a fredas thats not too busy", "entity_names": ["fredas", "not too busy"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "can you find me a good place to eat chowder", "entity_names": ["good", "chowder"], "entity_types": ["Rating", "Dish"]}, {"sentence": "can you find me a kid friendly seafood restaurant", "entity_names": ["kid friendly", "seafood"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "can you find me a kid friendly sushi restaurant", "entity_names": ["kid friendly", "sushi"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "can you find me a moderately priced italian restaurant", "entity_names": ["moderately", "italian"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "can you find me a nice italian restaurant that takes reservations", "entity_names": ["nice", "italian", "reservations"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "can you find me a not cheap lunch spot that serves pasteur", "entity_names": ["not cheap", "lunch", "pasteur"], "entity_types": ["Price", "Hours", "Dish"]}, {"sentence": "can you find me a pizza place", "entity_names": ["pizza"], "entity_types": ["Dish"]}, {"sentence": "can you find me a pizzeria that delivers after midnight", "entity_names": ["pizzeria", "delivers", "after midnight"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity", "Hours"]}, {"sentence": "can you find me a place nearby thats open after 12 pm with bean dishes", "entity_names": ["nearby", "after 12 pm", "bean dishes"], "entity_types": ["Location", "Hours", "Dish"]}, {"sentence": "can you find me a place that serves french fries", "entity_names": ["french fries"], "entity_types": ["Dish"]}, {"sentence": "can you find me a restaurant that has a bar in it", "entity_names": ["bar in it"], "entity_types": ["Amenity"]}, {"sentence": "can you find me a restaurant that has entrees priced between 15 and 20 dollars", "entity_names": ["between 15 and 20 dollars"], "entity_types": ["Price"]}, {"sentence": "can you find me a thai restaurant that is caual", "entity_names": ["thai", "caual"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "can you find me chinese restaurant with a smoking section", "entity_names": ["chinese", "smoking section"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "can you find me hotel dining with comfort food", "entity_names": ["hotel dining", "comfort food"], "entity_types": ["Restaurant Name", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "can you find me some malaysian food late at night", "entity_names": ["malaysian", "late at night"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Hours"]}, {"sentence": "can you find me some take out ribs", "entity_names": ["take out", "ribs"], "entity_types": ["Amenity", "Dish"]}, {"sentence": "can you find out if the best little restaurant has dancing and a good looking atmosphere", "entity_names": ["best little restaurant", "dancing", "good looking atmosphere"], "entity_types": ["Restaurant Name", "Amenity", "Amenity"]}, {"sentence": "can you find starting gate restaurant thats hard to find with a huge price", "entity_names": ["starting gate restaurant", "huge"], "entity_types": ["Restaurant Name", "Price"]}, {"sentence": "can you find the closest ihop", "entity_names": ["closest", "ihop"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "can you find the nearest health food store and bar", "entity_names": ["nearest", "health food store", "bar"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "can you find the waterfront restaurant albertos deli of course thats open until 11 pm", "entity_names": ["waterfront", "al<PERSON><PERSON> deli", "open until 11 pm"], "entity_types": ["Location", "Restaurant Name", "Hours"]}, {"sentence": "can you find us an ice cream shop near haight street", "entity_names": ["ice cream", "near haight street"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "can you get me a list of chinese food places in great neck ny", "entity_names": ["chinese", "great neck ny"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "can you get pork in chinatown", "entity_names": ["pork", "chinatown"], "entity_types": ["Dish", "Location"]}, {"sentence": "can you give me the name of the restaurant on green st", "entity_names": ["name", "green st"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "can you give me the phone number for the nearest mexican restaurant that is both affordable and has good quality food", "entity_names": ["nearest", "mexican", "affordable", "good quality"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "Price", "Rating"]}, {"sentence": "can you help me find a fancy restaurant with 5 star ratings", "entity_names": ["fancy", "5 star ratings"], "entity_types": ["Amenity", "Rating"]}, {"sentence": "can you help me find a high end restaurant where i can have lunch", "entity_names": ["high end", "lunch"], "entity_types": ["Price", "Amenity"]}, {"sentence": "can you help me find a korean restaurant that is close by", "entity_names": ["korean", "close by"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "can you help me find a reasonably priced harvard chinese restaurant that lets you byob", "entity_names": ["reasonably", "harvard", "chinese", "byob"], "entity_types": ["Price", "Location", "<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "can you help me find a restaurant that has fast service and is open before 11 am", "entity_names": ["fast service", "open before 11 am"], "entity_types": ["Amenity", "Hours"]}, {"sentence": "can you help me find a tong villa that serves small portions", "entity_names": ["tong villa", "small portions"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "can you help me find inexpensive dining within a five mile radius of my current location", "entity_names": ["inexpensive", "within a five mile radius"], "entity_types": ["Price", "Location"]}, {"sentence": "can you help me get to a restaurant where i can get lunch for under 10", "entity_names": ["lunch", "under 10"], "entity_types": ["Hours", "Price"]}, {"sentence": "can you locate a 4 star or higher restaurant that serves italian food", "entity_names": ["4 star or higher", "italian"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "can you locate a chinese buffet for under 12 within a five minute drive", "entity_names": ["chinese", "buffet", "under 12", "within a five minute drive"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity", "Price", "Location"]}, {"sentence": "can you locate a diner that has a smoking section in this area", "entity_names": ["diner", "smoking section", "this area"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity", "Location"]}, {"sentence": "can you locate a restaurant that sell burgers after 12 00 am", "entity_names": ["burgers", "12 00 am"], "entity_types": ["Dish", "Hours"]}, {"sentence": "can you locate the business hours for a restaurant that serves brunch", "entity_names": ["brunch"], "entity_types": ["Hours"]}, {"sentence": "can you make a reservation at pf changs for tonight", "entity_names": ["pf changs", "tonight"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "can you make me a reservation at the cheapest restaurant with valet parking", "entity_names": ["reservation", "cheapest", "valet parking"], "entity_types": ["Amenity", "Price", "Amenity"]}, {"sentence": "can you make me a reservation at the most expensive restaurant within 15 miles", "entity_names": ["most expensive", "within 15 miles"], "entity_types": ["Price", "Location"]}, {"sentence": "can you make me a reservation for 4 at the nearest upscale steakhouse", "entity_names": ["nearest", "upscale", "steakhouse"], "entity_types": ["Location", "Price", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "can you make me a reservation for to<PERSON> for thursday for two people", "entity_names": ["reservation", "todai", "thursday"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Restaurant Name"]}, {"sentence": "can you make reservations for two at heartland restaurant for tonight at 7 30", "entity_names": ["heartland restaurant", "tonight at 7 30"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "can you order me a pizza", "entity_names": ["pizza"], "entity_types": ["Dish"]}, {"sentence": "can you please direct me to the nearest chinese restaurant", "entity_names": ["nearest", "chinese"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "can you please find the olive garden restaurant in manhattan", "entity_names": ["olive garden", "in manhattan"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "can you search for the most expensive restaurant", "entity_names": ["most expensive"], "entity_types": ["Price"]}, {"sentence": "can you take me to a pizza place", "entity_names": ["pizza"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "can you take me to roosevelts restaurant and sin in framingham", "entity_names": ["roosevelts restaurant and sin", "framingham"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "can you tell me what italian restaurants north of heat rd in fairfax", "entity_names": ["italian", "north of heat rd in fairfax"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "can you tell me where an affordable burmese place is", "entity_names": ["affordable", "burmese"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "can you tell me where the closest taco bell is", "entity_names": ["closest", "taco bell"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "can you tell me where the nearest sushi restaurant is", "entity_names": ["nearest", "sushi"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "can you tell me where the nearest wendys is", "entity_names": ["nearest", "wendys"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "can you tell me where to get some cheap vegetarian food", "entity_names": ["cheap", "vegetarian"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "car look for upscale restaurants downtown and make reservations at the best reviewed", "entity_names": ["upscale", "downtown", "best reviewed"], "entity_types": ["Price", "Location", "Rating"]}, {"sentence": "cheap place close to home", "entity_names": ["cheap", "close to home"], "entity_types": ["Price", "Location"]}, {"sentence": "chinese food sounds good help me out with a good place", "entity_names": ["chinese", "good"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Rating"]}, {"sentence": "closest sushi bar in town", "entity_names": ["closest", "sushi", "bar"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "cocktails and dancing", "entity_names": ["cocktails", "dancing"], "entity_types": ["Dish", "Amenity"]}, {"sentence": "could you find a restaurant which plays live music", "entity_names": ["plays live music"], "entity_types": ["Amenity"]}, {"sentence": "could you find me a place thats open every day", "entity_names": ["open every day"], "entity_types": ["Hours"]}, {"sentence": "could you find me some restaurants located along the way to the airport that are open late", "entity_names": ["along the way to the airport", "open late"], "entity_types": ["Location", "Hours"]}, {"sentence": "could you help me locate a place for lunch", "entity_names": ["lunch"], "entity_types": ["Hours"]}, {"sentence": "could you locate an italian restaurant around four miles away with pesto pasta", "entity_names": ["italian", "four miles away", "pesto pasta"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Dish"]}, {"sentence": "could you locate the closest seafood restaurant", "entity_names": ["closest", "seafood"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "could you make a reservation for me at a nice italian restaurant", "entity_names": ["nice", "italian"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "create directions to closest chinese restaurtant", "entity_names": ["closest", "chinese"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "danny cooks home made dinners", "entity_names": ["danny cooks", "home made", "dinners"], "entity_types": ["Restaurant Name", "Amenity", "Hours"]}, {"sentence": "diner", "entity_names": ["diner"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "diner locations", "entity_names": ["diner"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "dining along the waterfront restaurant is located on sutton street", "entity_names": ["dining", "along the", "waterfront", "sutton street"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity", "Restaurant Name", "Location"]}, {"sentence": "direct me to a donut shop please", "entity_names": ["donut"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "direct me to the nearest indian restaurant", "entity_names": ["nearest", "indian"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "direct me to the nearest mcdonalds", "entity_names": ["direct me", "nearest", "mc<PERSON><PERSON><PERSON>"], "entity_types": ["Location", "Location", "Restaurant Name"]}, {"sentence": "do any famous people frequent the jimmys pizza too that is close by", "entity_names": ["famous people", "jimmys pizza", "close by"], "entity_types": ["Amenity", "Restaurant Name", "Location"]}, {"sentence": "do any fast food restaurants accept out of town checks", "entity_names": ["fast food", "out of town checks"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "do any of the asian food restaurants use only organic meats and vegetables", "entity_names": ["asian", "organic meats and vegetables"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "do any of the authentic italian restaurants sell bottles of imported extra virgin olive oil", "entity_names": ["authentic italian", "sell bottles of imported extra virgin olive oil"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "do any of the chinese restaurants in town do take out after midnight", "entity_names": ["chinese", "in town", "take out after midnight"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Amenity"]}, {"sentence": "do any of the family restaurants down town serve strawberry milk", "entity_names": ["family", "down town", "strawberry milk"], "entity_types": ["Amenity", "Location", "Dish"]}, {"sentence": "do any of the italian restaurants around here offer an all you can eat buffet", "entity_names": ["italian", "around here", "all you can eat buffet"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Amenity"]}, {"sentence": "do any of the middle eastern restaurants close by 7 pm", "entity_names": ["middle eastern", "close by 7 pm"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Hours"]}, {"sentence": "do any of the restaurants in this town also have a dance floor and live music", "entity_names": ["dance floor", "live music"], "entity_types": ["Amenity", "Amenity"]}, {"sentence": "do any restaurants nearby offer a few gluten free options", "entity_names": ["nearby", "gluten free options"], "entity_types": ["Location", "Amenity"]}, {"sentence": "do any restaurants serve loaves of patitza around christmas time", "entity_names": ["loaves of patitza"], "entity_types": ["Dish"]}, {"sentence": "do fast food restaurants accept checks in baltimore", "entity_names": ["fast food", "accept checks", "baltimore"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity", "Location"]}, {"sentence": "do i need a reservation for kings", "entity_names": ["reservation", "kings"], "entity_types": ["Amenity", "Restaurant Name"]}, {"sentence": "do know any place around here to take clients", "entity_names": ["place around here", "take clients"], "entity_types": ["Location", "Amenity"]}, {"sentence": "do they have a smoking area", "entity_names": ["smoking area"], "entity_types": ["Amenity"]}, {"sentence": "do they have any restaurants in the mall", "entity_names": ["in the mall"], "entity_types": ["Location"]}, {"sentence": "do you have any good food recommendations", "entity_names": ["good"], "entity_types": ["Rating"]}, {"sentence": "do you have any suggestions for some great tacos", "entity_names": ["great", "tacos"], "entity_types": ["Rating", "Dish"]}, {"sentence": "do you have information about any deals they may have", "entity_names": [], "entity_types": []}, {"sentence": "do you have to make a reservation to go to peachtree cafe", "entity_names": ["reservation", "peachtree cafe"], "entity_types": ["Amenity", "Restaurant Name"]}, {"sentence": "do you know any good pizza places open until midnight", "entity_names": ["good", "pizza", "open until midnight"], "entity_types": ["Rating", "Dish", "Hours"]}, {"sentence": "do you know if elmos have a dress code", "entity_names": ["el<PERSON>", "dress code"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "do you know if i can find fine dining in the theater district", "entity_names": ["fine dining", "in the theater district"], "entity_types": ["Amenity", "Location"]}, {"sentence": "do you know if reggianos serve breakfast", "entity_names": ["reggiano<PERSON>", "breakfast"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "do you know if the burrito place is open this late", "entity_names": ["burrito", "open this late"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Hours"]}, {"sentence": "do you know if the purple cactus burrito and wrap bar ongreentree lane have byob", "entity_names": ["purple cactus burrito and wrap bar", "ongreentree lane", "byob"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "do you know if there are any excellent wine bar close by", "entity_names": ["excellent", "wine bar", "close by"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "do you know if there are any fine dining cajun restaurant with a fireplace that is within one mile", "entity_names": ["fine dining", "cajun", "fireplace", "within one mile"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>", "Amenity", "Location"]}, {"sentence": "do you know if there are any irish restaurants that is not too far away", "entity_names": ["irish", "not too far away"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "do you know if there are any reasonably priced cajun restaurants that open at 11 am", "entity_names": ["reasonably", "cajun", "open at 11 am"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Hours"]}, {"sentence": "do you know if there are any resataurants in the mall", "entity_names": ["in the mall"], "entity_types": ["Location"]}, {"sentence": "do you know if there are any reviews on monacos", "entity_names": ["monacos"], "entity_types": ["Restaurant Name"]}, {"sentence": "do you know of a bakery that is still open", "entity_names": ["bakery", "still open"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Hours"]}, {"sentence": "do you know the name of that restaurant on morgan street that is getting all the raves", "entity_names": ["morgan street", "getting all the raves"], "entity_types": ["Location", "Rating"]}, {"sentence": "do you know what restaurants have catering", "entity_names": ["catering"], "entity_types": ["Amenity"]}, {"sentence": "do you know where i can find a place to eat that has gotten good reviews", "entity_names": ["good reviews"], "entity_types": ["Rating"]}, {"sentence": "do you know where i can get some fresh sushi", "entity_names": ["fresh sushi"], "entity_types": ["Dish"]}, {"sentence": "do you know where there is a wine bar", "entity_names": ["wine bar"], "entity_types": ["Amenity"]}, {"sentence": "do you know where they sell hummus", "entity_names": ["<PERSON>mus"], "entity_types": ["Dish"]}, {"sentence": "do you think the noodle bar is open", "entity_names": ["noodle bar", "open"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "do you think tin whistle has fabulous service", "entity_names": ["tin whistle", "fabulous service"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does angels have a dress code", "entity_names": ["angels", "dress code"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does any place sells corned beef and cabbage for st patricks day", "entity_names": ["corned beef and cabbage", "st patricks day"], "entity_types": ["Dish", "Hours"]}, {"sentence": "does anyone in town deliver tasty vegan pizza", "entity_names": ["in town", "deliver", "tasty", "vegan pizza"], "entity_types": ["Location", "Amenity", "Rating", "Dish"]}, {"sentence": "does bellinis have any outdoor parking", "entity_names": ["bellinis", "outdoor parking"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does buffalo wild wings do takeout orders", "entity_names": ["buffalo wild wings", "takeout"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does burger king accept credit cards", "entity_names": ["burger king", "accept credit cards"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does caribe have a smoking area", "entity_names": ["caribe", "smoking area"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does chuck e cheeses have drive thru", "entity_names": ["chuck e cheeses", "drive thru"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does dennys have a kids menu", "entity_names": ["dennys", "kids menu"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does firstwatch breakfast restuarant have outdoor seating", "entity_names": ["firstwatch breakfast restuarant", "outdoor seating"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does freds have take out", "entity_names": ["freds", "take out"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does granit grill at 703 fx have cheap breakfast", "entity_names": ["granit grill", "703 fx", "cheap", "breakfast"], "entity_types": ["Restaurant Name", "Location", "Price", "Hours"]}, {"sentence": "does gustovs have a bar", "entity_names": ["gustovs", "bar"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does jaimes bakery have a great decor", "entity_names": ["jaimes bakery", "great decor"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does johnsons steakhouse require formal attire", "entity_names": ["johnsons steakhouse", "formal attire"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does logans serve hamburgers", "entity_names": ["logans", "hamburgers"], "entity_types": ["Restaurant Name", "Dish"]}, {"sentence": "does mcdonalds serve ice cream during breakfast hours", "entity_names": ["mc<PERSON><PERSON><PERSON>", "ice cream during", "breakfast hours"], "entity_types": ["Restaurant Name", "Dish", "Hours"]}, {"sentence": "does midys have takeout", "entity_names": ["midys", "takeout"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does mikes cafe have a smoking section", "entity_names": ["mikes cafe", "smoking section"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does mikes country kitchen have a senior special", "entity_names": ["mikes country kitchen"], "entity_types": ["Restaurant Name"]}, {"sentence": "does mister foos take reservations", "entity_names": ["mister foos", "reservations"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does mortons have a dress code", "entity_names": ["mortons", "dress code"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does olive garden serve wine", "entity_names": ["olive garden", "wine"], "entity_types": ["Restaurant Name", "Dish"]}, {"sentence": "does osaka sushi express have great portions", "entity_names": ["osaka sushi express", "great portions"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does paymon serves white wine", "entity_names": ["paymon", "white wine"], "entity_types": ["Restaurant Name", "Dish"]}, {"sentence": "does pedros mexican restaurant accept credit cards", "entity_names": ["pedros", "mexican", "accept credit cards"], "entity_types": ["Restaurant Name", "<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "does pizza hut accept credit", "entity_names": ["pizza hut"], "entity_types": ["Restaurant Name"]}, {"sentence": "does quang take credit cards", "entity_names": ["quang", "credit cards"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does ricatonis offer a lunch portion option", "entity_names": ["rica<PERSON><PERSON>", "lunch portion option"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does ruby tuesday on drake avenue have a salad bar", "entity_names": ["ruby tuesday", "drake avenue", "salad bar"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "does sherrys have an all you can eat buffet", "entity_names": ["sherrys"], "entity_types": ["Restaurant Name"]}, {"sentence": "does stephanies on newbury have a brunch menu", "entity_names": ["<PERSON><PERSON><PERSON>", "newbury", "brunch menu"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "does target have their own parking spot is it good for bringing my date there", "entity_names": ["parking spot", "good", "bringing", "date"], "entity_types": ["Amenity", "Rating", "Location", "Amenity"]}, {"sentence": "does texas roadhouse open for lunch every day", "entity_names": ["texas roadhouse", "open for lunch every day"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "does tgi fridays have senior discounts", "entity_names": ["tgi fridays", "senior discounts"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does the alicias diner on danton drive offer fixed price menus", "entity_names": ["alicias diner", "danton drive", "fixed price menus"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "does the asian restaurant downtown has parking", "entity_names": ["asian", "downtown", "parking"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Amenity"]}, {"sentence": "does the caranova restaurant at kendall square offer a fixed price menu", "entity_names": ["<PERSON><PERSON><PERSON>", "kendall square", "fixed price menu"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "does the chinese buffet on 6 th avenue have a smoking section", "entity_names": ["chinese buffet", "6 th avenue", "smoking section"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Amenity"]}, {"sentence": "does the eastside grill have a dress code", "entity_names": ["eastside grill", "dress code"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does the einstein bros bagels offer fine dining and a close location", "entity_names": ["einstein bros bagels", "fine dining", "close"], "entity_types": ["Restaurant Name", "Amenity", "Location"]}, {"sentence": "does the italian restaurant in the town center offer carry out", "entity_names": ["italian", "in the town center", "carry out"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Amenity"]}, {"sentence": "does the italian restaurant on 5 th street require a dress code", "entity_names": ["italian", "5 th street", "dress code"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Amenity"]}, {"sentence": "does the italos bakery along the road have waterfront dining", "entity_names": ["italos bakery", "along the road", "waterfront dining"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "does the kostas pizza and seafood restaurant have grat prices", "entity_names": ["kostas pizza and seafood restaurant", "grat"], "entity_types": ["Restaurant Name", "Price"]}, {"sentence": "does the kyotoyo japanese restaurant in the theater district deliver", "entity_names": ["kyotoyo japanese restaurant", "theater district", "deliver"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "does the mcdonalds on 7 th avenue offer free parking", "entity_names": ["mc<PERSON><PERSON><PERSON>", "7 th avenue", "free parking"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "does the nearest asian restaurant have a kids menu", "entity_names": ["nearest", "asian", "kids menu"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "does the nearest howard johnsons have room service", "entity_names": ["nearest", "howard johnsons", "room service"], "entity_types": ["Location", "Restaurant Name", "Amenity"]}, {"sentence": "does the pho 2000 on state park rd have online reservation options", "entity_names": ["pho 2000", "state park rd", "online reservation"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "does the pizza shop on florida have outdoor parking", "entity_names": ["pizza shop", "florida", "outdoor parking"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "does the trendy new place in holbrook have egg rolls", "entity_names": ["<PERSON><PERSON><PERSON>", "egg rolls"], "entity_types": ["Location", "Dish"]}, {"sentence": "does the wildhorse saloon restaurant have a museum or gift shop", "entity_names": ["wildhorse saloon restaurant", "museum or gift shop"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "does this chinese restaurant have private rooms", "entity_names": ["chinese", "private rooms"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "does this restaurant have a good family friendly atmosphere", "entity_names": ["family friendly atmosphere"], "entity_types": ["Amenity"]}, {"sentence": "dominos pizza joint near my location", "entity_names": ["dominos pizza", "near my location"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "farmer boys burgers in my town", "entity_names": ["farmer boys burgers", "town"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "fast food restaurant in the area", "entity_names": ["fast food", "in the area"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "feel like having a heart attack heart attack grill", "entity_names": ["heart attack grill"], "entity_types": ["Restaurant Name"]}, {"sentence": "find a brewpub with entrees under fifteen dollars", "entity_names": ["brewpub", "entrees under fifteen dollars"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Price"]}, {"sentence": "find a carry out chinese restaurant", "entity_names": ["carry out", "chinese"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find a cheap brewpub that serves beef", "entity_names": ["cheap", "brewpub", "beef"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Dish"]}, {"sentence": "find a chinese place that delivers", "entity_names": ["chinese", "delivers"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "find a chinese restaurant that will take american express", "entity_names": ["chinese", "will take american express"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "find a chinese restaurant with a large buffet", "entity_names": ["chinese", "large buffet"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "find a classy expensive restaurant with excellent reviews", "entity_names": ["classy", "expensive", "excellent reviews"], "entity_types": ["Amenity", "Price", "Rating"]}, {"sentence": "find a clean place to eat that has reasonable prices", "entity_names": ["clean", "reasonable"], "entity_types": ["Amenity", "Price"]}, {"sentence": "find a comella brothersand italian market for our anniversary that is within 10 miles from here", "entity_names": ["comella brothersand italian market", "within 10 miles", "here"], "entity_types": ["Restaurant Name", "Location", "Location"]}, {"sentence": "find a fine dining restaurant within 6 miles", "entity_names": ["fine dining", "within 6 miles"], "entity_types": ["Amenity", "Location"]}, {"sentence": "find a high end place that serves pancakes", "entity_names": ["high end", "pancakes"], "entity_types": ["Rating", "Dish"]}, {"sentence": "find a local fruit stand", "entity_names": ["local", "fruit stand"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find a mexican food restaurant that has a very good rating", "entity_names": ["mexican", "very good rating"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Rating"]}, {"sentence": "find a mike donuts for non smokers", "entity_names": ["mike donuts", "non smokers"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "find a place to eat that has a drive thru", "entity_names": ["drive thru"], "entity_types": ["Amenity"]}, {"sentence": "find a place with live music and happy hour specials", "entity_names": ["live music", "happy hour specials"], "entity_types": ["Amenity", "Amenity"]}, {"sentence": "find a reasonably priced restaurant on highwood drive called oriental kitchen", "entity_names": ["reasonably", "highwood drive", "oriental kitchen"], "entity_types": ["Price", "Location", "Restaurant Name"]}, {"sentence": "find a thai cuisine within 2 miles", "entity_names": ["thai", "within 2 miles"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "find a wright wright take out for a special occasion", "entity_names": ["wright wright", "take out", "special occasion"], "entity_types": ["Restaurant Name", "Amenity", "Amenity"]}, {"sentence": "find an in and out burger place", "entity_names": ["in and out burger"], "entity_types": ["Restaurant Name"]}, {"sentence": "find an inexpensive mexican restaurant in the area", "entity_names": ["inexpensive", "mexican", "in the area"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "find an italian resturant that serves family style", "entity_names": ["italian", "family style"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "find italian restaurants in atlanta ga", "entity_names": ["italian", "atlanta ga"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "find italian restaurants within 10 miles", "entity_names": ["italian", "within 10 miles"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "find me a 3 star rated burger restaurant within 10 miles", "entity_names": ["3 star rated", "burger restaurant", "within 10 miles"], "entity_types": ["Rating", "Dish", "Location"]}, {"sentence": "find me a causal bar and grill", "entity_names": ["causal", "bar and grill"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find me a cheap brazilian restaurant", "entity_names": ["cheap", "brazilian"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find me a cheap restaurant with a no smoking area", "entity_names": ["cheap", "no smoking area"], "entity_types": ["Price", "Amenity"]}, {"sentence": "find me a cheaply priced thai restaurant", "entity_names": ["cheaply", "thai"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find me a chinese restaurant nearby", "entity_names": ["chinese", "nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "find me a chinese take out restaurant", "entity_names": ["chinese", "take out"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "find me a close by meat market which sells produce", "entity_names": ["close by", "meat", "produce"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "Dish"]}, {"sentence": "find me a close by sub shop", "entity_names": ["close by", "sub"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find me a close travel center", "entity_names": ["close"], "entity_types": ["Location"]}, {"sentence": "find me a deli which has an eat in area", "entity_names": ["deli", "eat in area"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "find me a dim sum restaurant open at 8 am", "entity_names": ["dim sum restaurant", "open at 8 am"], "entity_types": ["Dish", "Hours"]}, {"sentence": "find me a fancy place to eat", "entity_names": ["fancy"], "entity_types": ["Amenity"]}, {"sentence": "find me a fast food restaurant", "entity_names": ["fast food"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find me a german restaurant within 10 miles", "entity_names": ["german", "within 10 miles"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "find me a good buffet near stockton ca", "entity_names": ["buffet", "near stockton ca"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "find me a good burrito truck in the mission", "entity_names": ["good", "burrito truck", "in the mission"], "entity_types": ["Rating", "Dish", "Location"]}, {"sentence": "find me a good deli in manhattan", "entity_names": ["good", "deli", "manhattan"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "find me a good pho restaurant in portland or", "entity_names": ["good", "pho", "portland or"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "find me a good pub that has a dance floor", "entity_names": ["good", "pub", "dance floor"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "find me a good vegetarian restaurant", "entity_names": ["good", "vegetarian"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find me a jack in the box thats open", "entity_names": ["jack in the box", "open"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "find me a kid friendly restaurant within three miles of here", "entity_names": ["kid friendly", "within three miles"], "entity_types": ["Amenity", "Location"]}, {"sentence": "find me a local outdoors shop that sells fire wood", "entity_names": ["local", "outdoors shop", "sells fire wood"], "entity_types": ["Location", "Amenity", "Amenity"]}, {"sentence": "find me a local restaurant serving turkish food", "entity_names": ["local", "turkish"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find me a nail salon that does pedicures", "entity_names": ["nail salon", "pedicures"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "find me a pizza parlour please", "entity_names": ["pizza parlour"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find me a pizza place that takes credit cards", "entity_names": ["pizza", "credit cards"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "find me a place that sells burgers closest to me", "entity_names": ["burgers", "closest to me"], "entity_types": ["Dish", "Location"]}, {"sentence": "find me a place that serves chinese takeout", "entity_names": ["chinese", "takeout"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "find me a place to eat near by", "entity_names": ["near by"], "entity_types": ["Location"]}, {"sentence": "find me a place to eat that has excellent sushi", "entity_names": ["excellent", "sushi"], "entity_types": ["Rating", "Dish"]}, {"sentence": "find me a place to get pizza nearby", "entity_names": ["pizza", "nearby"], "entity_types": ["Dish", "Location"]}, {"sentence": "find me a popular local coffee shop", "entity_names": ["popular", "local", "coffee"], "entity_types": ["Rating", "Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find me a ranch style barbecue that serves lunch at 3 pm about one mile from my current position", "entity_names": ["ranch style barbecue", "serves lunch at 3 pm", "about one mile"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Hours", "Location"]}, {"sentence": "find me a restaurant good for a date", "entity_names": ["good for a date"], "entity_types": ["Amenity"]}, {"sentence": "find me a restaurant near a movie theater thats open at 6 pm on saturday", "entity_names": ["near a movie theater", "open at 6 pm on saturday"], "entity_types": ["Location", "Hours"]}, {"sentence": "find me a restaurant near the stadium", "entity_names": ["near the stadium"], "entity_types": ["Location"]}, {"sentence": "find me a restaurant that has a smoking area and serves alcohol and is withing 20 miles of my current location", "entity_names": ["smoking area", "alcohol", "withing 20 miles"], "entity_types": ["Amenity", "Dish", "Location"]}, {"sentence": "find me a restaurant that is quick and cheap", "entity_names": ["quick", "cheap"], "entity_types": ["Amenity", "Price"]}, {"sentence": "find me a restaurant that serves hawaiian food", "entity_names": ["hawaiian"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find me a restaurant with burgers on the menu and large portions that offers business dining", "entity_names": ["burgers", "large portions", "business dining"], "entity_types": ["Dish", "Amenity", "Amenity"]}, {"sentence": "find me a restaurant with truck parking and good ratings", "entity_names": ["truck parking", "good ratings"], "entity_types": ["Amenity", "Rating"]}, {"sentence": "find me a restaurant with valet access", "entity_names": ["valet access"], "entity_types": ["Amenity"]}, {"sentence": "find me a restaurant within 5 miles of here that is open at 1 am", "entity_names": ["within 5 miles of here", "open at 1 am"], "entity_types": ["Location", "Hours"]}, {"sentence": "find me a review of grasons barbeque", "entity_names": ["grasons barbeque"], "entity_types": ["Restaurant Name"]}, {"sentence": "find me a romantic restaurant in 7 hills", "entity_names": ["romantic", "restaurant in 7 hills"], "entity_types": ["Amenity", "Location"]}, {"sentence": "find me a romantic restaurant that has an open table", "entity_names": ["romantic", "open table"], "entity_types": ["Amenity", "Amenity"]}, {"sentence": "find me a sitdown place with a buy one get one free offer", "entity_names": ["sitdown", "buy one get one free offer"], "entity_types": ["Amenity", "Amenity"]}, {"sentence": "find me a southwestern restaurant that serves breakfast and is located nearby", "entity_names": ["southwestern", "breakfast", "located nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Hours", "Location"]}, {"sentence": "find me a spanish restaurant where i can smoke", "entity_names": ["spanish", "can smoke"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "find me a sports bar that serves food", "entity_names": ["sports bar", "serves food"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "find me a store that sells apple products within fifty miles", "entity_names": ["apple", "within fifty miles"], "entity_types": ["Dish", "Location"]}, {"sentence": "find me a sudbury pizza place thats no more than 10 minutes from here", "entity_names": ["sudbury pizza", "no more than 10 minutes from here"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "find me a take out chinese restaurant", "entity_names": ["take out", "chinese"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find me a take out restaurant with fried chicken less than 2 miles from here", "entity_names": ["take out", "fried chicken", "less than 2 miles from here"], "entity_types": ["Amenity", "Dish", "Location"]}, {"sentence": "find me a tgi fridays near me", "entity_names": ["tgi fridays", "near me"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "find me a thai restaurant with a great rating", "entity_names": ["thai", "great rating"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Rating"]}, {"sentence": "find me a vegan restaurant not more than 5 miles away", "entity_names": ["vegan", "not more than 5 miles away"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "find me a very well priced cuban restaurant", "entity_names": ["well", "cuban"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find me all the local italian joints", "entity_names": ["local", "italian joints"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find me an ethiopian restaurant with good service", "entity_names": ["ethiopian", "good service"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "find me an ethiopian restaurant within 5 miles of here", "entity_names": ["ethiopian", "within 5 miles"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "find me an expensive american restaurant", "entity_names": ["expensive", "american"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find me an indian restaurant that serves lamb", "entity_names": ["indian", "lamb"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Dish"]}, {"sentence": "find me an italian restaurant which has received high ratings for its service", "entity_names": ["italian", "high ratings for its service"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Rating"]}, {"sentence": "find me brazilian food with on location parking", "entity_names": ["brazilian", "on location parking"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "find me chicken places that accept discover card", "entity_names": ["chicken", "accept discover card"], "entity_types": ["Dish", "Amenity"]}, {"sentence": "find me chinese food", "entity_names": ["chinese"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find me fast food that serves salad", "entity_names": ["fast food", "salad"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Dish"]}, {"sentence": "find me italian restaurants with cheesecake", "entity_names": ["italian", "cheesecake"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Dish"]}, {"sentence": "find me restaurant that isnt cheap with chocolate cake on the dessert menu and byob", "entity_names": ["isnt cheap", "chocolate cake", "byob"], "entity_types": ["Price", "Dish", "Amenity"]}, {"sentence": "find me route directions from lexington ky to cincinnatti ohio", "entity_names": ["lexington ky", "cincinnatti ohio"], "entity_types": ["Location", "Location"]}, {"sentence": "find me soul food in los angeles", "entity_names": ["soul", "los angeles"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "find me the best rated chinese restaurant in the twin cities", "entity_names": ["best rated", "chinese", "twin cities"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "find me the closest bakers", "entity_names": ["closest", "bakers"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find me the closest ihop", "entity_names": ["closest", "ihop"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "find me the closest sonic drive thru", "entity_names": ["closest", "sonic", "drive thru"], "entity_types": ["Location", "Restaurant Name", "Amenity"]}, {"sentence": "find me the closest walmart", "entity_names": ["closest", "walmart"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "find me the nearest chase bank", "entity_names": ["nearest", "chase bank"], "entity_types": ["Location", "Amenity"]}, {"sentence": "find me the nearest mcdonalds please", "entity_names": ["nearest", "mc<PERSON><PERSON><PERSON>"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "find me the nearest placed to eat", "entity_names": ["nearest"], "entity_types": ["Location"]}, {"sentence": "find me the phone number to dominos pizza", "entity_names": ["dominos pizza"], "entity_types": ["Restaurant Name"]}, {"sentence": "find me the phone number to the closest afghan restaurant", "entity_names": ["closest", "afghan"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find my someplace around here where i can rock out with a smoothie", "entity_names": ["someplace", "smoothie"], "entity_types": ["Location", "Dish"]}, {"sentence": "find nearby restaurants with coupons", "entity_names": ["nearby", "coupons"], "entity_types": ["Location", "Amenity"]}, {"sentence": "find out how to get to the chipotle mexican grill on 12 th and pine", "entity_names": ["chipotle mexican grill", "12 th and pine"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "find pizza places", "entity_names": ["pizza"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find ratings of all local italian restaurants", "entity_names": ["ratings", "local", "italian"], "entity_types": ["Rating", "Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find restaurant with italian food", "entity_names": ["italian"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find restaurants within 5 miles with entrees under 15", "entity_names": ["within 5 miles", "entrees under 15"], "entity_types": ["Location", "Price"]}, {"sentence": "find somewhere to eat that is open before 7 am with average pricing along the route", "entity_names": ["open before 7 am", "average", "along the route"], "entity_types": ["Hours", "Price", "Location"]}, {"sentence": "find thai food", "entity_names": ["thai"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find the closest brewpub", "entity_names": ["closest", "brewpub"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find the closest dunkin donuts", "entity_names": ["closest", "dunkin donuts"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "find the closest homeward bound that has a happy hour", "entity_names": ["closest homeward bound", "happy hour"], "entity_types": ["Location", "Amenity"]}, {"sentence": "find the closest sea food restaurant", "entity_names": ["closest", "sea food"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find the nearest chicken stand", "entity_names": ["nearest", "chicken stand"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find the nearest irish restaurant", "entity_names": ["nearest", "irish"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "find the number of the best barbecue joint in town", "entity_names": ["best", "barbecue", "in town"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "find us a deli near central park", "entity_names": ["deli", "near central park"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "find us a sushi bar near brooklyn heights", "entity_names": ["sushi bar", "near brooklyn heights"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "find us a sushi bar near jackson", "entity_names": ["sushi", "bar", "near jackson"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity", "Location"]}, {"sentence": "find us locations of china wok", "entity_names": ["china wok"], "entity_types": ["Restaurant Name"]}, {"sentence": "get me to a good pho restaurant", "entity_names": ["good", "pho"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "get me to a mexican place", "entity_names": ["mexican"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "get me to the best italian restaurant with the highest rating", "entity_names": ["best", "italian", "highest rating"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Rating"]}, {"sentence": "give me a list of restaurants that have seafood on the menu", "entity_names": ["seafood"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "give me a random restaurant that i havent been to yet", "entity_names": ["random"], "entity_types": ["Amenity"]}, {"sentence": "give me directions to a mcdonalds", "entity_names": ["mc<PERSON><PERSON><PERSON>"], "entity_types": ["Restaurant Name"]}, {"sentence": "give me directions to an arbys", "entity_names": ["arbys"], "entity_types": ["Restaurant Name"]}, {"sentence": "give me directions to saturn grill", "entity_names": ["saturn grill"], "entity_types": ["Restaurant Name"]}, {"sentence": "give me the closest place that does sushi", "entity_names": ["closest", "sushi"], "entity_types": ["Location", "Dish"]}, {"sentence": "give me the hours for the closest waffle house", "entity_names": ["hours", "closest", "waffle house"], "entity_types": ["Hours", "Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "give me the locations of the buffets in town", "entity_names": ["locations", "buffets", "in town"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "give me the names of the three closest pizza parlors", "entity_names": ["closest", "pizza parlors"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "going to shake shack wheres the best place to park", "entity_names": ["shake shack"], "entity_types": ["Restaurant Name"]}, {"sentence": "gryos nears restaurant serving them", "entity_names": ["gryos", "nears"], "entity_types": ["Dish", "Location"]}, {"sentence": "hard rock hotel restaurant near me", "entity_names": ["hard rock hotel", "near me"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "hello would you please take me to sushi stop", "entity_names": ["sushi stop"], "entity_types": ["Restaurant Name"]}, {"sentence": "hello yes i need a steakhouse reservations childrens menu and pricing along with reviews of at least 3 stars", "entity_names": ["steakhouse", "reservations childrens menu", "at least 3 stars"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity", "Rating"]}, {"sentence": "help me find a burger joint", "entity_names": ["burger joint"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "help me find a five star chinese buffet within ten minutes of my current location", "entity_names": ["five star", "chinese buffet", "within ten minutes"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "help me find a good place to eat", "entity_names": ["good"], "entity_types": ["Rating"]}, {"sentence": "help me find a high end restaurant that is open until 11 pm", "entity_names": ["open until 11 pm"], "entity_types": ["Hours"]}, {"sentence": "help me find a place for fast food", "entity_names": ["fast food"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "help me find a place my kids would like to eat", "entity_names": ["kids would like to eat"], "entity_types": ["Amenity"]}, {"sentence": "help me find a sports bar that is smoke friendly", "entity_names": ["sports bar", "smoke friendly"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "help me find jack in the box in beverly hills", "entity_names": ["jack in the box", "beverly hills"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "hey could you look up a restaurant with the best meatballs in town", "entity_names": ["best", "meatballs", "in town"], "entity_types": ["Rating", "Dish", "Location"]}, {"sentence": "hey im looking for a great date night idea any suggestions", "entity_names": ["great", "date night"], "entity_types": ["Rating", "Amenity"]}, {"sentence": "hey take me to variety food court", "entity_names": ["variety food court"], "entity_types": ["Restaurant Name"]}, {"sentence": "hey tell me where theres a taco bell nearby", "entity_names": ["taco bell", "nearby"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "hi <PERSON><PERSON> is there any place around here with something good to eat", "entity_names": ["around here", "good"], "entity_types": ["Location", "Rating"]}, {"sentence": "hi i would like some thai food is there any nearby", "entity_names": ["thai", "nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "hi please find me a sushi restaurant that has good reviews and that isnt too expensive", "entity_names": ["sushi", "good reviews", "isnt too expensive"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Rating", "Price"]}, {"sentence": "hi would you please find a restaurant with cheap vegan options", "entity_names": ["cheap", "vegan"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "how are the prices at donatellas", "entity_names": ["donatellas"], "entity_types": ["Restaurant Name"]}, {"sentence": "how big are the portions at cheddars", "entity_names": ["cheddars"], "entity_types": ["Restaurant Name"]}, {"sentence": "how can i get to anthonys cafe on the waterfront", "entity_names": ["anthonys cafe", "waterfront"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "how can i quickly get to the nearst long john silvers", "entity_names": ["nearst", "long john silvers"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "how close is the closest mexican restaurant", "entity_names": ["closest", "mexican"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "how close is the nearest olive garden", "entity_names": ["nearest", "olive garden"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "how do i get from the honolulu zoo to izakaya gazen", "entity_names": ["honolulu zoo", "izakaya gazen"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "how do i get to burger king on oak street", "entity_names": ["burger king", "oak street"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "how do i get to the cloest port of subs", "entity_names": ["cloest", "port of subs"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "how do i get to the nearest hooters", "entity_names": ["nearest", "hooters"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "how do i get to the nearest taco bell", "entity_names": ["nearest", "taco bell"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "how do you get to als diner from here", "entity_names": ["als diner", "from here"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "how expensive is olive garden", "entity_names": ["expensive", "olive garden"], "entity_types": ["Price", "Restaurant Name"]}, {"sentence": "how expensive is the food at chinese express", "entity_names": ["chinese express"], "entity_types": ["Restaurant Name"]}, {"sentence": "how far am i from the nearest bagel shop", "entity_names": ["nearest", "bagel"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "how far am i from true thai right now", "entity_names": ["true thai"], "entity_types": ["Restaurant Name"]}, {"sentence": "how far away is a chicago style sub joint", "entity_names": ["chicago style sub joint"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "how far away is the closest burger king", "entity_names": ["closest", "burger king"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "how far away is the nearest applebees", "entity_names": ["nearest", "applebees"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "how far away is the nearest steak house", "entity_names": ["nearest", "steak house"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "how far for a burger place", "entity_names": ["burger"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "how far is evergreen taiwanese restaurant from the himalayan", "entity_names": ["evergreen taiwanese", "himalayan"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "how far is the arbys", "entity_names": ["arbys"], "entity_types": ["Restaurant Name"]}, {"sentence": "how far is the english pub that serves a fry up", "entity_names": ["english pub", "fry up"], "entity_types": ["Amenity", "Dish"]}, {"sentence": "how far is the nearest applebeas", "entity_names": ["nearest", "applebeas"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "how far is the nearest olive garden", "entity_names": ["nearest", "olive garden"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "how far is the taco bell", "entity_names": ["taco bell"], "entity_types": ["Restaurant Name"]}, {"sentence": "how far to the nearest fast food", "entity_names": ["fast food"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "how far to the next subway", "entity_names": ["next", "subway"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "how late does mc<PERSON><PERSON>s serve breakfast", "entity_names": ["mc<PERSON><PERSON><PERSON>"], "entity_types": ["Restaurant Name"]}, {"sentence": "how late pfchangs in paradise road will be open", "entity_names": ["pfchangs", "paradise road", "will be open"], "entity_types": ["Restaurant Name", "Location", "Hours"]}, {"sentence": "how long will it take me to drive from glasgow to the three chimneys in skye", "entity_names": ["glasgow", "three chimneys", "skye"], "entity_types": ["Location", "Restaurant Name", "Location"]}, {"sentence": "how long will it take me to drive to the band box diner from here", "entity_names": ["long", "band box diner"], "entity_types": ["Hours", "Restaurant Name"]}, {"sentence": "how many burger kings are around", "entity_names": ["burger kings", "around"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "how many burger kings are nearby", "entity_names": ["burger kings", "nearby"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "how many chinese restaurants are there", "entity_names": ["chinese"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "how many miles will it take me to get to dominoes", "entity_names": ["dominoes"], "entity_types": ["Restaurant Name"]}, {"sentence": "how many pizza restaurants are nearby", "entity_names": ["pizza", "nearby"], "entity_types": ["Dish", "Location"]}, {"sentence": "how many places serve pizza", "entity_names": ["pizza"], "entity_types": ["Dish"]}, {"sentence": "how many restaurants near me have bathrooms", "entity_names": ["near me", "have bathrooms"], "entity_types": ["Location", "Amenity"]}, {"sentence": "how many restaurants that accept reservations are within 10 miles", "entity_names": ["accept reservations", "within 10 miles"], "entity_types": ["Amenity", "Location"]}, {"sentence": "how much is a plate at the olive garden", "entity_names": ["olive garden"], "entity_types": ["Restaurant Name"]}, {"sentence": "how much is an average plate at spencers diner", "entity_names": ["spencers diner"], "entity_types": ["Restaurant Name"]}, {"sentence": "how much is small box of fries at jack in the box", "entity_names": ["fries", "jack in the box"], "entity_types": ["Dish", "Restaurant Name"]}, {"sentence": "how much longer is subway open", "entity_names": ["subway", "open"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "how should i dress when going to francoiss", "entity_names": ["francoiss"], "entity_types": ["Restaurant Name"]}, {"sentence": "huges bar and brill thanks", "entity_names": ["huges bar and brill"], "entity_types": ["Restaurant Name"]}, {"sentence": "i am diabetic and need to know if there are any health stores in the area", "entity_names": ["health", "in the area"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "i am having trouble fully waking up could you find some reviews of local coffee shops", "entity_names": ["local", "coffee"], "entity_types": ["Location", "Dish"]}, {"sentence": "i am hungry please find nearby restaurants", "entity_names": ["nearby"], "entity_types": ["Location"]}, {"sentence": "i am in the mood for shrimp where is the closet place i can go", "entity_names": ["shrimp", "closet"], "entity_types": ["Dish", "Location"]}, {"sentence": "i am in the mood for some chinese food can you find me a place", "entity_names": ["chinese"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i am looking for a dennys that is 5 miles from here", "entity_names": ["dennys", "5 miles from here"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "i am looking for a good place to eat", "entity_names": ["good"], "entity_types": ["Rating"]}, {"sentence": "i am looking for a local pizza restaurant that delivers", "entity_names": ["local", "pizza", "delivers"], "entity_types": ["Location", "Dish", "Amenity"]}, {"sentence": "i am looking for a mediterranean restaurant that delivers", "entity_names": ["mediterranean", "delivers"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "i am looking for a mexican restuarant that has a mariachi band", "entity_names": ["mexican", "mariachi band"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "i am looking for a nice restaurant within 30 miles that accepts credit cards", "entity_names": ["nice", "within 30 miles", "accepts credit cards"], "entity_types": ["Rating", "Location", "Amenity"]}, {"sentence": "i am looking for a restaurant that allows smoking", "entity_names": ["allows smoking"], "entity_types": ["Amenity"]}, {"sentence": "i am looking for a restaurant that serves english food i want the price to be cheap to moderate", "entity_names": ["english", "cheap to moderate"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Price"]}, {"sentence": "i am looking for an olive garden are there any close by", "entity_names": ["olive garden", "close by"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "i am looking for lunch buffets within 15 minutes driving distance", "entity_names": ["lunch", "buffets", "within 15 minutes driving distance"], "entity_types": ["Hours", "Amenity", "Location"]}, {"sentence": "i am looking for sandwhiches", "entity_names": ["sandwhiches"], "entity_types": ["Dish"]}, {"sentence": "i am looking for some chinese food", "entity_names": ["chinese"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i am looking for the best large buffet within 15 miles", "entity_names": ["best", "large buffet", "within 15 miles"], "entity_types": ["Rating", "Amenity", "Location"]}, {"sentence": "i feel in the mood for spicy food what can you do for me", "entity_names": ["spicy"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i feel the need for some killer barbeque help me please", "entity_names": ["killer", "barbeque"], "entity_types": ["Rating", "Dish"]}, {"sentence": "i hate smoke so list off some eateries i can go relax at", "entity_names": ["hate smoke", "relax"], "entity_types": ["Amenity", "Amenity"]}, {"sentence": "i have a coupon for sweet tomatoes where is the nearest one", "entity_names": ["sweet tomatoes", "nearest"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "i have a craving for fish and chips but i am on a budget so i need them cheap", "entity_names": ["fish", "chips", "cheap"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Price"]}, {"sentence": "i have a dinner date at 5 where should i take her", "entity_names": ["dinner", "date", "at 5"], "entity_types": ["Hours", "Amenity", "Hours"]}, {"sentence": "i have alcohol where can i find a good appetizer", "entity_names": ["good", "appetizer"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i have an important business luncheon and need to find a place that caters to professionals", "entity_names": ["business", "luncheon", "caters to professionals"], "entity_types": ["Amenity", "Hours", "Amenity"]}, {"sentence": "i have to be back at the office before 3 pm which restaurant is located within 1 mile from here", "entity_names": ["within 1 mile from here"], "entity_types": ["Location"]}, {"sentence": "i just need to find the closest diner to pick up some quick cheap breakfast", "entity_names": ["closest", "diner", "cheap", "breakfast"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "Price", "Hours"]}, {"sentence": "i just want to eat at home could you direct me to a fast food place that has carry out", "entity_names": ["fast food", "carry out"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "i need a 4 star rated subway nearby", "entity_names": ["4 star rated", "subway", "nearby"], "entity_types": ["Rating", "Restaurant Name", "Location"]}, {"sentence": "i need a 5 star rated sushi bar close by", "entity_names": ["5 star", "sushi bar", "close by"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "i need a cheap restaurant for brunch", "entity_names": ["cheap", "brunch"], "entity_types": ["Price", "Hours"]}, {"sentence": "i need a close restaurant that is open currently", "entity_names": ["close", "open currently"], "entity_types": ["Location", "Hours"]}, {"sentence": "i need a deli that caters", "entity_names": ["deli", "caters"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "i need a family friendly place to eat tonight", "entity_names": ["family friendly", "tonight"], "entity_types": ["Amenity", "Hours"]}, {"sentence": "i need a kid friendly lunch place", "entity_names": ["kid friendly", "lunch"], "entity_types": ["Amenity", "Hours"]}, {"sentence": "i need a kid friendly place to get sushi", "entity_names": ["kid friendly", "sushi"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i need a korean barbecue restaurant", "entity_names": ["korean barbecue"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i need a late night spot with good service", "entity_names": ["late night", "good service"], "entity_types": ["Hours", "Rating"]}, {"sentence": "i need a list of restaurants that take the diners card in a 5 mile radius", "entity_names": ["diners card", "5 mile radius"], "entity_types": ["Amenity", "Location"]}, {"sentence": "i need a middle eastern restaurant with friendly service", "entity_names": ["middle eastern", "friendly service"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Rating"]}, {"sentence": "i need a place for kids to eat at 12 pm", "entity_names": ["kids", "12 pm"], "entity_types": ["Amenity", "Hours"]}, {"sentence": "i need a place for smoking", "entity_names": ["smoking"], "entity_types": ["Amenity"]}, {"sentence": "i need a place to get enchiladas thats open every day", "entity_names": ["enchiladas", "open every day"], "entity_types": ["Dish", "Hours"]}, {"sentence": "i need a quick bite to eat", "entity_names": ["quick"], "entity_types": ["Amenity"]}, {"sentence": "i need a reservation for 12 at 6 pm at the nearest asian restaurant", "entity_names": ["6 pm", "nearest", "asian"], "entity_types": ["Hours", "Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i need a reservation for two at the nearest steakhouse", "entity_names": ["nearest", "steakhouse"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i need a restaurant with lots of parking and large portions called nations restaurant news", "entity_names": ["lots of parking", "large portions", "nations restaurant news"], "entity_types": ["Amenity", "Amenity", "Restaurant Name"]}, {"sentence": "i need a thai place nearby can you find one", "entity_names": ["thai", "nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "i need a two pm reservation for hooters thanks kitt", "entity_names": ["two pm", "hooters"], "entity_types": ["Hours", "Restaurant Name"]}, {"sentence": "i need an expensive place within 5 miles of here", "entity_names": ["expensive", "within 5 miles"], "entity_types": ["Price", "Location"]}, {"sentence": "i need an inexpensive italian restaurant with big portions", "entity_names": ["inexpensive", "italian", "big portions"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "i need an italian restaurant with a kids menu", "entity_names": ["italian", "kids menu"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "i need an unbelievably priced place on kingsdale st that has a bar atmosphere", "entity_names": ["unbelievably", "kingsdale st", "bar atmosphere"], "entity_types": ["Price", "Location", "Amenity"]}, {"sentence": "i need directions from my location to luigis pizza", "entity_names": ["luigis pizza"], "entity_types": ["Restaurant Name"]}, {"sentence": "i need directions to the closest pancake place", "entity_names": ["closest", "pancake"], "entity_types": ["Location", "Dish"]}, {"sentence": "i need directions to the nearest ethiopian restaurant", "entity_names": ["nearest", "ethiopian"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i need food delivered from near downtown", "entity_names": ["delivered", "near downtown"], "entity_types": ["Amenity", "Location"]}, {"sentence": "i need gluten free options on the restauarant menu", "entity_names": ["gluten free options"], "entity_types": ["Amenity"]}, {"sentence": "i need help finding a soul food restaurant", "entity_names": ["soul"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i need info on wendys opening hours", "entity_names": ["wendys", "opening hours"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "i need phone numbers to carry out pizza places within 2 miles", "entity_names": ["carry out", "pizza", "within 2 miles"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "i need reservations for 4 at the nearest sushi bar", "entity_names": ["nearest", "sushi bar"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i need some potato fries on beacon hill with quick service", "entity_names": ["potato fries", "beacon hill", "quick service"], "entity_types": ["Dish", "Location", "Amenity"]}, {"sentence": "i need something hot to eat on the way to work", "entity_names": ["hot", "way to work"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "i need somewhere decently priced", "entity_names": ["decently"], "entity_types": ["Price"]}, {"sentence": "i need the closest chic fil a that is still serving the peach shake", "entity_names": ["closest", "chic fil a", "peach shake"], "entity_types": ["Location", "Restaurant Name", "Dish"]}, {"sentence": "i need the hours of operation for lulus restaurant in gulf shores", "entity_names": ["lulus", "gulf shores"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "i need to book a business breakfast at the right price", "entity_names": ["business", "right"], "entity_types": ["Amenity", "Price"]}, {"sentence": "i need to find a galvins harp and bard near here", "entity_names": ["galvins harp and bard", "near here"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "i need to find a place that is open every day", "entity_names": ["open every day"], "entity_types": ["Hours"]}, {"sentence": "i need to find a restaurant int he government center with good service an a decent price", "entity_names": ["government center", "good service", "decent"], "entity_types": ["Location", "Rating", "Price"]}, {"sentence": "i need to know of a place that serves breakfast beginning as early as 5 30 am", "entity_names": ["breakfast", "5 30 am"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Hours"]}, {"sentence": "i really feel like seafood right now whats close", "entity_names": ["seafood", "close"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "i smell bread take me there", "entity_names": ["bread"], "entity_types": ["Dish"]}, {"sentence": "i think i could go for some mexican food right now can you see if there is anything nearby", "entity_names": ["mexican", "nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "i wanna try something new find me a restaurant that carries sushi", "entity_names": ["sushi"], "entity_types": ["Dish"]}, {"sentence": "i want a 5 star restaurant that does carry out", "entity_names": ["5 star", "carry out"], "entity_types": ["Rating", "Amenity"]}, {"sentence": "i want a beer from the cambridge brewing company", "entity_names": ["beer", "cambridge brewing company"], "entity_types": ["Dish", "Restaurant Name"]}, {"sentence": "i want a buffet", "entity_names": ["buffet"], "entity_types": ["Amenity"]}, {"sentence": "i want a good milkshake where can i find it nearby", "entity_names": ["good", "milkshake", "nearby"], "entity_types": ["Rating", "Dish", "Location"]}, {"sentence": "i want a great milkshake", "entity_names": ["milkshake"], "entity_types": ["Dish"]}, {"sentence": "i want a greek sandwich with goat cheese", "entity_names": ["greek", "sandwich with goat cheese"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Dish"]}, {"sentence": "i want a list of restaurants that are chinese buffets within 5 miles of here", "entity_names": ["chinese", "buffets", "within 5 miles of here"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity", "Location"]}, {"sentence": "i want a pad thai place around here thats open all day", "entity_names": ["pad thai", "around here", "open all day"], "entity_types": ["Dish", "Location", "Hours"]}, {"sentence": "i want a place that allows smoking and serves health food", "entity_names": ["allows smoking", "serves", "health"], "entity_types": ["Amenity", "Amenity", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i want a restaurant near the campground that allows smoking", "entity_names": ["near the campground", "allows smoking"], "entity_types": ["Location", "Amenity"]}, {"sentence": "i want a restaurant on smith st that serves toast", "entity_names": ["smith st", "toast"], "entity_types": ["Location", "Dish"]}, {"sentence": "i want a restaurant that is open after 9 pm that is quite and has nice service", "entity_names": ["open after 9 pm", "quite", "nice service"], "entity_types": ["Hours", "Amenity", "Amenity"]}, {"sentence": "i want a restaurant where i can order some carry out potato soup", "entity_names": ["carry out", "potato soup"], "entity_types": ["Amenity", "Dish"]}, {"sentence": "i want a taco from a taco truck", "entity_names": ["taco", "taco truck"], "entity_types": ["Dish", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i want an upscale steakhouse that has valet parking", "entity_names": ["upscale", "steakhouse", "valet parking"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "i want mc<PERSON><PERSON>s", "entity_names": ["mc<PERSON><PERSON><PERSON>"], "entity_types": ["Restaurant Name"]}, {"sentence": "i want mexican food thats cheap which restaurant is closest", "entity_names": ["mexican", "cheap", "closest"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Price", "Location"]}, {"sentence": "i want pizza", "entity_names": ["pizza"], "entity_types": ["Dish"]}, {"sentence": "i want pizza im looking for the best pizza restaurant that is kid friendly and has carry out", "entity_names": ["pizza", "best", "pizza", "kid friendly", "carry out"], "entity_types": ["Dish", "Rating", "Dish", "Amenity", "Amenity"]}, {"sentence": "i want restaurants that are pet friendly", "entity_names": ["pet friendly"], "entity_types": ["Amenity"]}, {"sentence": "i want some chips and salsa", "entity_names": ["chips and salsa"], "entity_types": ["Dish"]}, {"sentence": "i want some taco bell", "entity_names": ["taco bell"], "entity_types": ["Restaurant Name"]}, {"sentence": "i want something full of grease", "entity_names": ["grease"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i want something to eat close by", "entity_names": ["close by"], "entity_types": ["Location"]}, {"sentence": "i want something to eat that is not fast food", "entity_names": ["not fast food"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i want tacos", "entity_names": ["tacos"], "entity_types": ["Dish"]}, {"sentence": "i want take out mexican food right now", "entity_names": ["take out", "mexican"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i want the spiciest buffalo wings in town where should i go", "entity_names": ["spiciest buffalo wings", "in town"], "entity_types": ["Dish", "Location"]}, {"sentence": "i want to eat at a very classy restaurant", "entity_names": ["very classy"], "entity_types": ["Amenity"]}, {"sentence": "i want to eat fast food italian", "entity_names": ["fast food", "italian"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i want to eat hamburgers", "entity_names": ["hamburgers"], "entity_types": ["Dish"]}, {"sentence": "i want to eat in the best rated restaurant in the area", "entity_names": ["best rated", "in the area"], "entity_types": ["Rating", "Location"]}, {"sentence": "i want to eat mexican food", "entity_names": ["mexican"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i want to eat some pasta", "entity_names": ["pasta"], "entity_types": ["Dish"]}, {"sentence": "i want to eat sushi please find me a place", "entity_names": ["sushi"], "entity_types": ["Dish"]}, {"sentence": "i want to find a burger that isnt fast food", "entity_names": ["burger", "isnt fast food"], "entity_types": ["Dish", "Rating"]}, {"sentence": "i want to find a german restaurant on the lower east side that serves brunch on saturday and or sunday", "entity_names": ["german", "lower east side", "brunch", "saturday and or sunday"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Hours", "Hours"]}, {"sentence": "i want to find a kosher deli that serves tongue and brisket sandwiches", "entity_names": ["kosher deli", "tongue", "brisket sandwiches"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Dish", "Dish"]}, {"sentence": "i want to find a nearby coffee shop with the highest customer ratings", "entity_names": ["nearby", "coffee", "highest customer ratings"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "Rating"]}, {"sentence": "i want to find a new restaurant in my area that has just opened", "entity_names": ["new", "in my area"], "entity_types": ["Amenity", "Location"]}, {"sentence": "i want to find a place that serves beef patties", "entity_names": ["beef patties"], "entity_types": ["Dish"]}, {"sentence": "i want to find a place that serves pizza by the slice", "entity_names": ["pizza by the slice"], "entity_types": ["Dish"]}, {"sentence": "i want to find a place to eat that is very clean and has good service", "entity_names": ["very clean", "good service"], "entity_types": ["Amenity", "Amenity"]}, {"sentence": "i want to find a place with spaghetti and meatballs", "entity_names": ["spaghetti and meatballs"], "entity_types": ["Dish"]}, {"sentence": "i want to find a restaurant that has a diet friendly menu", "entity_names": ["diet friendly"], "entity_types": ["Amenity"]}, {"sentence": "i want to find a restaurant with an outdoor dining section that permits smoking", "entity_names": ["outdoor dining section", "permits smoking"], "entity_types": ["Amenity", "Amenity"]}, {"sentence": "i want to find an italian restaurant", "entity_names": ["italian"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i want to get a list of pancake restaurants that are nonsmoking", "entity_names": ["pancake", "nonsmoking"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "i want to get a reservation at the best michelin rated french restaurant", "entity_names": ["best michelin rated", "french"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i want to get price info on chez yogis restaurant in tow city", "entity_names": ["chez yogis", "in tow city"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "i want to get some chinese food", "entity_names": ["chinese"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i want to get to a coffee shop that serves breakfast after 11 am", "entity_names": ["coffee", "breakfast after 11 am"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Hours"]}, {"sentence": "i want to get to a restauarant as fast as possible", "entity_names": ["as fast as possible"], "entity_types": ["Location"]}, {"sentence": "i want to go dancing at a nearby place and i want scallops while im at it", "entity_names": ["dancing", "nearby", "scallops"], "entity_types": ["Amenity", "Location", "Dish"]}, {"sentence": "i want to go to a nicely priced place within 10 minutes that has a good tomato sauce", "entity_names": ["nicely", "within 10 minutes", "good", "tomato sauce"], "entity_types": ["Price", "Location", "Rating", "Dish"]}, {"sentence": "i want to go to a restaurant with a high zagats rating and average plate cost of less than 20 nearby", "entity_names": ["high zagats rating", "average plate cost", "less than 20", "nearby"], "entity_types": ["Rating", "Price", "Price", "Location"]}, {"sentence": "i want to go to an indian restaurant downtown", "entity_names": ["indian", "downtown"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "i want to go to taco bell or taco jhons what ever is closer", "entity_names": ["taco bell", "taco jhons"], "entity_types": ["Restaurant Name", "Restaurant Name"]}, {"sentence": "i want to have lunch in downtown la", "entity_names": ["lunch", "downtown la"], "entity_types": ["Hours", "Location"]}, {"sentence": "i want to have tacos", "entity_names": ["tacos"], "entity_types": ["Dish"]}, {"sentence": "i want to make a reservation at an indian restaurant but i dont know the location of one around here", "entity_names": ["reservation", "indian"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i want to make dinner plans for the convention can you take me somewhere that does catering", "entity_names": ["catering"], "entity_types": ["Amenity"]}, {"sentence": "i want to reserve the back room at ben<PERSON> grill for my daughters birthday party next friday night is it available", "entity_names": ["bensons grill", "friday night"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "i want to try a blow fish could you find a good place to try it", "entity_names": ["blow fish", "good"], "entity_types": ["Dish", "Rating"]}, {"sentence": "i want to try something exotic", "entity_names": ["exotic"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i want to try something new for dinner tonight", "entity_names": ["new", "dinner", "tonight"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Hours"]}, {"sentence": "i was some expensive dumplings", "entity_names": ["expensive", "dumplings"], "entity_types": ["Price", "Dish"]}, {"sentence": "i wnat to go to a steakhouse with outdoor dining within 10 miles", "entity_names": ["steakhouse", "outdoor dining", "within 10 miles"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity", "Location"]}, {"sentence": "i would like a list of restaurants that are smoke free near my house", "entity_names": ["smoke free", "near my house"], "entity_types": ["Amenity", "Location"]}, {"sentence": "i would like find where all the nearby food trucks are", "entity_names": ["nearby", "food trucks"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i would like some dim sum today that is less than 5 miles away", "entity_names": ["dim sum", "less than 5 miles away"], "entity_types": ["Dish", "Location"]}, {"sentence": "i would like some food what restaurants arent closed in the area", "entity_names": ["closed", "in the area"], "entity_types": ["Hours", "Location"]}, {"sentence": "i would like to eat fish today any recommendations", "entity_names": ["fish", "today"], "entity_types": ["Dish", "Hours"]}, {"sentence": "i would like to eat pizza at a place with outdoor seating", "entity_names": ["pizza", "outdoor seating"], "entity_types": ["Dish", "Amenity"]}, {"sentence": "i would like to eat tofu at 12 pm for a reasonable price", "entity_names": ["tofu", "at 12 pm", "reasonable"], "entity_types": ["Dish", "Hours", "Price"]}, {"sentence": "i would like to find a french restaurant that is rated at least 4 stars", "entity_names": ["french", "rated at least 4 stars"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Rating"]}, {"sentence": "i would like to find a mexican restaurant nearby", "entity_names": ["mexican", "nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "i would like to find a soul food restaurant", "entity_names": ["soul"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i would like to find a vegan restaurant", "entity_names": ["vegan"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i would like to find a vegeterian restaurant", "entity_names": ["vegeterian"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i would like to go to a sushi restaurant", "entity_names": ["sushi"], "entity_types": ["Amenity"]}, {"sentence": "i would like to know the location of a chinese restaurant", "entity_names": ["chinese"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "i would like to know where the best restaurants are", "entity_names": ["best"], "entity_types": ["Rating"]}, {"sentence": "i would like wendys", "entity_names": ["wendys"], "entity_types": ["Restaurant Name"]}, {"sentence": "i would llike breakfast foods", "entity_names": ["breakfast"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "ice cream shop with 20 flavors or more near here", "entity_names": ["ice cream", "20 flavors or more", "near here"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity", "Location"]}, {"sentence": "id like a coffee shop that serves pie", "entity_names": ["coffee", "pie"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Dish"]}, {"sentence": "id like some comfort food farm vegetables", "entity_names": ["comfort food", "farm vegetables"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Dish"]}, {"sentence": "id like to eat at a good lunch spot for the right price", "entity_names": ["lunch", "for the right"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Price"]}, {"sentence": "id like to eat in a reasonably priced restaurant that is not part of chain and that serves american food", "entity_names": ["reasonably", "not part of chain", "american"], "entity_types": ["Price", "Amenity", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "id like to eat some halal at a bar", "entity_names": ["halal", "bar"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "id like to find a breakfast place", "entity_names": ["breakfast"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "id like to find a cheap pub with internet access", "entity_names": ["cheap", "pub", "internet access"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "id like to find a chinese restaurant nearby", "entity_names": ["chinese", "nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "id like to find a diner that has grilled cheese and soup close to here could you suggest one", "entity_names": ["diner", "grilled cheese and soup", "close to here"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Dish", "Location"]}, {"sentence": "id like to go somewhere off the beaten path to get some middle eastern food", "entity_names": ["off the beaten path", "middle eastern"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "id like to know the closest starbucks open past 9 pm", "entity_names": ["closest", "starbucks", "past 9 pm"], "entity_types": ["Location", "Restaurant Name", "Hours"]}, {"sentence": "id really like a thai restaurant that has carryout do you know of one", "entity_names": ["thai", "carryout"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "id there a mina bakery in chinatown", "entity_names": ["mina bakery", "in chinatown"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "if want only organic vegetables and fruits in my dishes which restaurant is best", "entity_names": ["organic vegetables and fruits", "best"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Rating"]}, {"sentence": "im craving twice baked potatoes where do they serve them in a ten mile radius", "entity_names": ["twice baked potatoes", "ten mile radius"], "entity_types": ["Dish", "Location"]}, {"sentence": "im feeling a little down so id like go somewhere thats really bright and fun for breakfast", "entity_names": ["bright and fun", "breakfast"], "entity_types": ["Amenity", "Hours"]}, {"sentence": "im hungry and i feel like eating chinese", "entity_names": ["chinese"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "im hungry and want a tasty burger", "entity_names": ["tasty", "burger"], "entity_types": ["Rating", "Dish"]}, {"sentence": "im hungry find me a restaurant with large portions", "entity_names": ["large portions"], "entity_types": ["Amenity"]}, {"sentence": "im hungry for a steak any good restaurants around", "entity_names": ["steak", "good", "around"], "entity_types": ["Dish", "Rating", "Location"]}, {"sentence": "im hungry for thai", "entity_names": ["thai"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "im hungry lets get some tacos", "entity_names": ["tacos"], "entity_types": ["Dish"]}, {"sentence": "im in a hurry wheres a place i can get a quick meal", "entity_names": ["quick meal"], "entity_types": ["Amenity"]}, {"sentence": "im in the mood for chinese food", "entity_names": ["chinese"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "im in the mood for chinese which restaurants nearby have the best ratings", "entity_names": ["chinese", "nearby", "best ratings"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Rating"]}, {"sentence": "im in the mood for mexican food", "entity_names": ["mexican"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "im in the mood for some texas chili", "entity_names": ["texas chili"], "entity_types": ["Dish"]}, {"sentence": "im in the mood for something light", "entity_names": ["light"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "im in the mood to eat something ive never tried before can you find me something like that", "entity_names": ["never tried before"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "im looking for a 5 star restaurant whats the closest one", "entity_names": ["5 star", "closest"], "entity_types": ["Rating", "Location"]}, {"sentence": "im looking for a chinese restureant thats moderate", "entity_names": ["chinese", "moderate"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Price"]}, {"sentence": "im looking for a diner along my route", "entity_names": ["diner", "along my route"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "im looking for a family style restaurant so i can eat at the bar anything within two miles", "entity_names": ["family style", "eat at the bar", "within two miles"], "entity_types": ["Amenity", "Amenity", "Location"]}, {"sentence": "im looking for a nice place to eat for me and my girlfriends one year anniversary where i can schedule romantic candles and flowers for her", "entity_names": ["nice", "romantic candles", "flowers"], "entity_types": ["Rating", "Amenity", "Amenity"]}, {"sentence": "im looking for a pizza restaurant that has buffalo chicken where i can eat in i only have a credit card", "entity_names": ["pizza", "buffalo chicken", "credit card"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Dish", "Amenity"]}, {"sentence": "im looking for a place that serves apple pie", "entity_names": ["apple pie"], "entity_types": ["Dish"]}, {"sentence": "im looking for a romantic restaurant where are some near me", "entity_names": ["romantic", "near me"], "entity_types": ["Amenity", "Location"]}, {"sentence": "im looking for a top of the line steak place with valet parking", "entity_names": ["top of the line", "steak", "valet parking"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "im looking for an all you can eat mexican buffet", "entity_names": ["all you can eat", "mexican buffet"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "im looking for an asian buffet", "entity_names": ["asian", "buffet"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "im looking for cheap spanish cuisine", "entity_names": ["cheap", "spanish"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "im looking for some nearby brazilian food for a special occasion", "entity_names": ["nearby", "brazilian", "special occasion"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "im looking for somewhere i can get a lot of food for not too much money", "entity_names": ["lot of food", "not too much money"], "entity_types": ["Amenity", "Price"]}, {"sentence": "im looking for somewhere to get affordable sushi", "entity_names": ["affordable", "sushi"], "entity_types": ["Price", "Dish"]}, {"sentence": "im looking for somewhere with a good wine list within a mile of here that is open before ten in the morning", "entity_names": ["good", "wine list", "within a mile", "open before ten in the morning"], "entity_types": ["Rating", "Amenity", "Location", "Hours"]}, {"sentence": "im low on cash where is the nearest atm", "entity_names": ["nearest"], "entity_types": ["Location"]}, {"sentence": "im on a budget but i want to eat can you suggest something", "entity_names": ["budget"], "entity_types": ["Price"]}, {"sentence": "im on a really tight budget but im hungry help me out", "entity_names": ["tight budget"], "entity_types": ["Price"]}, {"sentence": "im really hungry take me to the closest restaurant", "entity_names": ["closest"], "entity_types": ["Location"]}, {"sentence": "im starving help me find a fast food restaurant", "entity_names": ["fast food"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "im starving is there a restaurant that sells shawarma here", "entity_names": ["shaw<PERSON>", "here"], "entity_types": ["Dish", "Location"]}, {"sentence": "im starving so fast food will do", "entity_names": ["fast food"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "im starving tell me where the closest mcdonalds is", "entity_names": ["closest", "mc<PERSON><PERSON><PERSON>"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "im thinking spanish tapas is there one in port", "entity_names": ["spanish tapas", "in port"], "entity_types": ["Dish", "Location"]}, {"sentence": "im trying to find a family friendly restaurant with a gift shop within 10 miles of sunswept hotel in orange beach", "entity_names": ["family friendly", "gift shop", "within 10 miles of sunswept hotel in orange beach"], "entity_types": ["Amenity", "Amenity", "Location"]}, {"sentence": "in which restaurants can one smoke", "entity_names": ["smoke"], "entity_types": ["Amenity"]}, {"sentence": "indian restraunt", "entity_names": ["indian"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "is a dairy queen nearby", "entity_names": ["dairy queen", "nearby"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is albertos deli open until 11 pm", "entity_names": ["al<PERSON><PERSON> deli", "open until 11 pm"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "is azita restaurant a date spot", "entity_names": ["azita restaurant", "date spot"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "is bambinos restaurant close by", "entity_names": ["bambinos", "close by"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is chepes restaurant on the way", "entity_names": ["chepes restaurant", "way"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is chickfila open today", "entity_names": ["chickfila", "open today"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "is dave and busters a good lunch spot", "entity_names": ["dave and busters", "good", "lunch"], "entity_types": ["Restaurant Name", "Rating", "Hours"]}, {"sentence": "is deluna pizza open after midnight", "entity_names": ["deluna pizza", "after midnight"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "is fitzys pub in san jose family friendly", "entity_names": ["fitzys pub", "in san jose", "family friendly"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "is golden house restaurant kids friendly", "entity_names": ["golden house restaurant", "kids friendly"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "is izzys ice cream shop open right now", "entity_names": ["izzys ice cream shop", "open right now"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "is jade garden within a mile of my current location", "entity_names": ["jade garden", "within a mile"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is outback having any specials today", "entity_names": ["outback"], "entity_types": ["Restaurant Name"]}, {"sentence": "is panera bread open for breakfast", "entity_names": ["panera bread", "breakfast"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "is papa johns on cradle way still open", "entity_names": ["papa johns", "cradle way", "still open"], "entity_types": ["Restaurant Name", "Location", "Hours"]}, {"sentence": "is pasquales still located on dayton street", "entity_names": ["pas<PERSON><PERSON>", "dayton street"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is quiznos open for breakfast", "entity_names": ["quiznos", "breakfast"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "is ruby tuesdays in chattanooga tn romantic", "entity_names": ["ruby tuesdays", "chattanooga", "tn"], "entity_types": ["Restaurant Name", "Location", "Restaurant Name"]}, {"sentence": "is santa ramen in san mateo busy on thursday nights", "entity_names": ["santa ramen", "san mateo", "thursday nights"], "entity_types": ["Restaurant Name", "Location", "Hours"]}, {"sentence": "is sidney and hampton an expensive hotel restaurant", "entity_names": ["sidney and hampton", "expensive"], "entity_types": ["Restaurant Name", "Price"]}, {"sentence": "is the a chau restaurant within a mile from here a local favorite", "entity_names": ["chau", "within a mile"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is the amaral manuel near here open after 12 pm", "entity_names": ["amaral manuel", "near here", "open after 12 pm"], "entity_types": ["Restaurant Name", "Location", "Hours"]}, {"sentence": "is the chateau restaurant affordable", "entity_names": ["chateau restaurant", "affordable"], "entity_types": ["Restaurant Name", "Price"]}, {"sentence": "is the hoseas restaurant nearby and does it offer live music", "entity_names": ["hoseas restaurant", "nearby", "live music"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "is the mcdonalds near my house open after midnight", "entity_names": ["mc<PERSON><PERSON><PERSON>", "near my house", "open after midnight"], "entity_types": ["Restaurant Name", "Location", "Hours"]}, {"sentence": "is the pasteur restaurant a good place for lunch are they expensive", "entity_names": ["pasteur restaurant", "good", "lunch", "expensive"], "entity_types": ["Restaurant Name", "Rating", "Hours", "Price"]}, {"sentence": "is the patio at the butcher and the boar dog friendly", "entity_names": ["patio", "butcher and the boar", "dog friendly"], "entity_types": ["Amenity", "Restaurant Name", "Amenity"]}, {"sentence": "is the pizza place closer than the chinese restaurant", "entity_names": ["pizza", "closer", "chinese"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "is the pricing fair", "entity_names": ["pricing fair"], "entity_types": ["Price"]}, {"sentence": "is the restaurant pushcart open until 11 am", "entity_names": ["pushcart", "open", "11 am"], "entity_types": ["Restaurant Name", "Hours", "Hours"]}, {"sentence": "is the rubios fresh mex grill within 10 miles from my house a hidden find", "entity_names": ["rubios fresh mex grill", "within 10 miles", "my house", "hidden"], "entity_types": ["Restaurant Name", "Location", "Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "is the seven seas diner expensive", "entity_names": ["seven seas diner", "expensive"], "entity_types": ["Restaurant Name", "Price"]}, {"sentence": "is the silverbull steakhouse open seven days a week", "entity_names": ["silverbull steakhouse"], "entity_types": ["Restaurant Name"]}, {"sentence": "is the tea garden restaurant in medford good for a date night", "entity_names": ["tea garden restaurant", "medford", "date night"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "is the twirl pasta in north end a good place for a business lunch", "entity_names": ["twirl pasta", "north end", "business", "lunch"], "entity_types": ["Restaurant Name", "Location", "Amenity", "Hours"]}, {"sentence": "is the tylers restaurant in baltimore a little pricey", "entity_names": ["tylers restaurant", "baltimore", "little"], "entity_types": ["Restaurant Name", "Location", "Price"]}, {"sentence": "is there a an indian restaurant in this town", "entity_names": ["indian", "in this town"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there a bakery near here", "entity_names": ["bakery", "near here"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there a bar that stays open after 2 am that is within a 5 minute distance", "entity_names": ["bar", "open after 2 am", "within a 5 minute distance"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Hours", "Location"]}, {"sentence": "is there a benihana in this city", "entity_names": ["<PERSON><PERSON><PERSON>", "this city"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a bojangles in laurel", "entity_names": ["bojangles", "laurel"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a breakfast place that has valet parking", "entity_names": ["breakfast", "valet parking"], "entity_types": ["Hours", "Amenity"]}, {"sentence": "is there a business dining restaurant where i can get cakes until 1 am", "entity_names": ["business", "cakes", "until 1 am"], "entity_types": ["Amenity", "Dish", "Hours"]}, {"sentence": "is there a cafe that serves frog legs in fort worth", "entity_names": ["cafe", "frog legs", "fort worth"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Dish", "Location"]}, {"sentence": "is there a cheap fast food restaurant nearby", "entity_names": ["cheap", "fast food", "nearby"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there a cheap vegetarian restaurant nearby", "entity_names": ["cheap", "vegetarian", "nearby"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there a chick fil a within 5 miles of here", "entity_names": ["chick fil a", "within 5 miles"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a chinese buffet that serves family style", "entity_names": ["chinese", "buffet", "family style"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity", "Amenity"]}, {"sentence": "is there a chinese restaurant in the midvale mall", "entity_names": ["chinese", "midvale mall"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there a chipotle located with in a mile of where i am now", "entity_names": ["chipotle", "with in a mile"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a ciao bella on holyoke st", "entity_names": ["ciao bella", "holyoke st"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a club diner in watertown with a bar", "entity_names": ["club", "diner", "watertown", "bar"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>", "Location", "Amenity"]}, {"sentence": "is there a deli nearby that takes credit cards and is open right now", "entity_names": ["deli", "nearby", "takes credit cards", "open right now"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Amenity", "Hours"]}, {"sentence": "is there a diner with a patio in boxford", "entity_names": ["diner", "patio", "boxford"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity", "Location"]}, {"sentence": "is there a donut and donuts restaurant within 5 miles with a beer list", "entity_names": ["donut and donuts restaurant", "within 5 miles", "beer list"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "is there a dress code and yuris dine in restaurant", "entity_names": ["dress code", "yuris dine in restaurant"], "entity_types": ["Amenity", "Restaurant Name"]}, {"sentence": "is there a five star restaurant or bar open late", "entity_names": ["five star", "open late"], "entity_types": ["Rating", "Hours"]}, {"sentence": "is there a good diner in oakland", "entity_names": ["diner", "oakland"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there a good place to eat close by", "entity_names": ["good", "close by"], "entity_types": ["Rating", "Location"]}, {"sentence": "is there a good vegan restaurant for kids", "entity_names": ["good", "vegan", "kids"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "is there a halal place downtown where i could eat outside", "entity_names": ["halal", "downtown", "eat outside"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Amenity"]}, {"sentence": "is there a hidden tudors biscut world in this town", "entity_names": ["hidden tudors biscut world", "this town"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a hooters in this area", "entity_names": ["hooters", "in this area"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a jacks restaurant around here", "entity_names": ["jacks", "around here"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a japanese restraunt near by", "entity_names": ["japanese", "near by"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there a korean restaurant that is smoker friendly", "entity_names": ["korean", "smoker friendly"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "is there a korean restaurant with nice decor and open late", "entity_names": ["korean", "nice decor", "open late"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity", "Hours"]}, {"sentence": "is there a late night place in west newbury where i can smoke", "entity_names": ["late night", "west newbury", "smoke"], "entity_types": ["Hours", "Location", "Amenity"]}, {"sentence": "is there a location of field corner restaurant near by that has a happy hour", "entity_names": ["field corner", "near by", "happy hour"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "is there a malaysian restaurant near roxbury crossing", "entity_names": ["malaysian", "roxbury crossing"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there a mall with a food court around here", "entity_names": ["food court", "around here"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there a mcdonalds between here and my destination", "entity_names": ["mc<PERSON><PERSON><PERSON>", "between here and my destination"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a mcdonalds nearby", "entity_names": ["mc<PERSON><PERSON><PERSON>", "nearby"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a mcdonalds within two miles of here", "entity_names": ["mc<PERSON><PERSON><PERSON>", "within two miles"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a mexican restaurant near here with large portion sizes", "entity_names": ["mexican", "near here", "large portion sizes"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Amenity"]}, {"sentence": "is there a moderately priced french restaurant in this area", "entity_names": ["moderately", "french", "in this area"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there a nice place in the theater district that wont drain my wallet", "entity_names": ["nice", "theater district", "wont drain my wallet"], "entity_types": ["Rating", "Location", "Price"]}, {"sentence": "is there a nicely decorated restaurant in sacramento with a fireplace", "entity_names": ["nicely decorated", "sacramento", "fireplace"], "entity_types": ["Amenity", "Location", "Amenity"]}, {"sentence": "is there a olive garden nearby", "entity_names": ["olive garden", "nearby"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a pancake house nearby", "entity_names": ["pancake house", "nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there a patio at w a frost", "entity_names": ["patio", "w a frost"], "entity_types": ["Amenity", "Restaurant Name"]}, {"sentence": "is there a place near by that serves tapas", "entity_names": ["near by", "tapas"], "entity_types": ["Location", "Dish"]}, {"sentence": "is there a place that serves breakfast this late", "entity_names": ["breakfast", "late"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Hours"]}, {"sentence": "is there a place that will let me bring my own alcohol", "entity_names": ["bring", "alcohol"], "entity_types": ["Amenity", "Amenity"]}, {"sentence": "is there a place to eat in the abington theatre district open after midnight", "entity_names": ["abington theatre district", "after midnight"], "entity_types": ["Location", "Hours"]}, {"sentence": "is there a place to eat that has a friendly and cheerful atmosphere and offers steak on the menu", "entity_names": ["friendly and cheerful atmosphere", "steak"], "entity_types": ["Amenity", "Dish"]}, {"sentence": "is there a place to eat that is still open at 10 pm close by", "entity_names": ["open at 10 pm", "close by"], "entity_types": ["Hours", "Location"]}, {"sentence": "is there a place to get high tea nearby", "entity_names": ["place", "high tea", "nearby"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there a place with walking distance of the cinema that serves pizza by the slice", "entity_names": ["walking distance of the cinema", "pizza by the slice"], "entity_types": ["Location", "Dish"]}, {"sentence": "is there a place within 10 minutes that has great atmosphere for a special meal", "entity_names": ["within 10 minutes", "great atmosphere"], "entity_types": ["Location", "Amenity"]}, {"sentence": "is there a ponderosa near here", "entity_names": ["ponderosa", "near here"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a pool side night club nearby", "entity_names": ["pool side night club", "nearby"], "entity_types": ["Amenity", "Location"]}, {"sentence": "is there a portuguese restaurant nearby", "entity_names": ["portuguese", "nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there a rancho veo restaurant in north memphis", "entity_names": ["rancho veo", "north memphis"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a real cheap place to eat in kendall square at 8 pm", "entity_names": ["real cheap", "kendall square", "at 8 pm"], "entity_types": ["Price", "Location", "Hours"]}, {"sentence": "is there a reasonably priced restaurant that has perfect portion sizes", "entity_names": ["reasonably", "perfect portion sizes"], "entity_types": ["Price", "Amenity"]}, {"sentence": "is there a red lobster in the area", "entity_names": ["red lobster", "area"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a restaurant around here that serves chicken wings", "entity_names": ["around here", "chicken wings"], "entity_types": ["Location", "Dish"]}, {"sentence": "is there a restaurant around where i could watch a football game it needs to be kid friendly", "entity_names": ["around", "watch a football game", "kid friendly"], "entity_types": ["Location", "Amenity", "Amenity"]}, {"sentence": "is there a restaurant close by that has dancing and serves scallops", "entity_names": ["close by", "dancing", "scallops"], "entity_types": ["Location", "Amenity", "Dish"]}, {"sentence": "is there a restaurant close by with valet parking", "entity_names": ["close by", "valet parking"], "entity_types": ["Location", "Amenity"]}, {"sentence": "is there a restaurant in inman square that is after 9 pm 7 days a week", "entity_names": ["after 9 pm 7 days a week"], "entity_types": ["Hours"]}, {"sentence": "is there a restaurant in the south end that has a fireplace and serves good portions", "entity_names": ["south end", "fireplace", "serves good portions"], "entity_types": ["Location", "Amenity", "Amenity"]}, {"sentence": "is there a restaurant on the way", "entity_names": ["way"], "entity_types": ["Location"]}, {"sentence": "is there a restaurant on waverly street that is priced competitively", "entity_names": ["waverly street", "priced competitively"], "entity_types": ["Location", "Price"]}, {"sentence": "is there a restaurant that has a 5 star rating for it that people like", "entity_names": ["5 star rating"], "entity_types": ["Rating"]}, {"sentence": "is there a restaurant that serves tacos before noon in charlestown", "entity_names": ["tacos", "before noon", "in charlestown"], "entity_types": ["Dish", "Hours", "Location"]}, {"sentence": "is there a restaurant with a bar scene nearby that serves small portioned meals and snacks", "entity_names": ["bar scene", "nearby", "small portioned meals and snacks"], "entity_types": ["Amenity", "Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "is there a sbarro in the galleria mall", "entity_names": ["sbarro", "galleria mall"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a sclafani italian bakery nearby with a view", "entity_names": ["sclafani italian bakery", "nearby", "view"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "is there a seafood restaurant around here", "entity_names": ["seafood"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "is there a small place in franklin that has beans", "entity_names": ["small", "in franklin", "beans"], "entity_types": ["Amenity", "Location", "Dish"]}, {"sentence": "is there a smoking section at olive garden", "entity_names": ["smoking section", "olive garden"], "entity_types": ["Amenity", "Restaurant Name"]}, {"sentence": "is there a sports bar within a mile of the minneapolis convention center", "entity_names": ["sports bar", "within a mile of the minneapolis convention center"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there a starbucks on the west side of town", "entity_names": ["starbucks", "west side of town"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a sudbury pizza place thats within 10 miles", "entity_names": ["sudbury pizza place", "within 10 miles"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a swensens restaurant in the north end", "entity_names": ["swensens restaurant", "in the north end"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a taco joint near the college", "entity_names": ["taco joint", "near the college"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there a taqueria el rancho grandes around here for taking a date", "entity_names": ["taqueria el rancho grandes", "around here", "taking a date"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "is there a thai restaurant with a great wine list", "entity_names": ["thai", "great", "wine list"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Rating", "Amenity"]}, {"sentence": "is there a vegan restaurant within 5 miles", "entity_names": ["vegan", "within 5 miles"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there a very high end pastry place close to me", "entity_names": ["very high end", "pastry", "close to me"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there a wah sang restaurant with a great beer list", "entity_names": ["wah sang", "beer list"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "is there a wangs fast food near providence highway", "entity_names": ["wangs fast food", "providence highway"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there a white castle on berkeley avenue", "entity_names": ["white castle", "berkeley avenue"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there an all asia cafe nearby", "entity_names": ["all asia cafe", "nearby"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there an apple bees near by or similar bar and grill", "entity_names": ["apple bees", "near by"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there an applebees in hicksville ny with a special 10 menu", "entity_names": ["applebees", "hicksville ny", "special 10 menu"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "is there an english style restaurant open until midnight in north weymouth", "entity_names": ["english", "open until midnight", "in north weymouth"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Hours", "Location"]}, {"sentence": "is there an environmentally friendly restaurant nearby", "entity_names": ["environmentally friendly", "nearby"], "entity_types": ["Amenity", "Location"]}, {"sentence": "is there an expensive hotel named sidney and hampton with dining services", "entity_names": ["expensive", "sidney and hampton", "dining services"], "entity_types": ["Price", "Restaurant Name", "Amenity"]}, {"sentence": "is there an expensive restaurant near here", "entity_names": ["expensive", "near here"], "entity_types": ["Price", "Location"]}, {"sentence": "is there an ihop near by", "entity_names": ["ihop", "near by"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there an indian restaurant within 5 miles that has a dinner buffet", "entity_names": ["indian", "within 5 miles", "dinner", "buffet"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Hours", "Amenity"]}, {"sentence": "is there an italian bistro nearby", "entity_names": ["italian bistro", "nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there an italian place nearby", "entity_names": ["italian", "nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there an olive garden in kendall square with private dining rooms", "entity_names": ["olive garden", "kendall square", "private dining rooms"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "is there an open taco bell nearby", "entity_names": ["open", "taco bell", "nearby"], "entity_types": ["Hours", "Restaurant Name", "Location"]}, {"sentence": "is there an restaurant in this part of town that serves thai food", "entity_names": ["this part of town", "thai"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "is there an s and i to go nearby that has small portions", "entity_names": ["s and i to go", "nearby"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there any 24 hour deli on the west side of town", "entity_names": ["24 hour", "deli", "west side of town"], "entity_types": ["Hours", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there any chinese food nearby", "entity_names": ["chinese", "nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there any cuban food nearby with authentic dining", "entity_names": ["cuban", "nearby", "authentic dining"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Amenity"]}, {"sentence": "is there any good pizza in the area", "entity_names": ["good", "pizza", "in the area"], "entity_types": ["Rating", "Dish", "Location"]}, {"sentence": "is there any place around that has bring your own liquor", "entity_names": ["bring your own liquor"], "entity_types": ["Amenity"]}, {"sentence": "is there any place in this area that has outdoor seating", "entity_names": ["in this area", "outdoor seating"], "entity_types": ["Location", "Amenity"]}, {"sentence": "is there any place nearby that serves seafood", "entity_names": ["nearby", "seafood"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "is there any place on the waterfront that does not allow smoking on the outside decks", "entity_names": ["waterfront", "not allow smoking", "outside decks"], "entity_types": ["Location", "Amenity", "Amenity"]}, {"sentence": "is there any place open after 9 pm", "entity_names": ["open after 9 pm"], "entity_types": ["Hours"]}, {"sentence": "is there any place that i can get a quick meal that isnt fast food", "entity_names": ["meal that", "fast food"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "is there any place where i can take my pet", "entity_names": ["pet"], "entity_types": ["Amenity"]}, {"sentence": "is there any red lobster by the mall", "entity_names": ["red lobster", "by the mall"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there any restaurant close by has live music", "entity_names": ["close by", "live music"], "entity_types": ["Location", "Amenity"]}, {"sentence": "is there any restaurant in the city that serves congee", "entity_names": ["in the city", "congee"], "entity_types": ["Location", "Dish"]}, {"sentence": "is there any restaurant near kendall square that delivers tea", "entity_names": ["near kendall square", "delivers", "tea"], "entity_types": ["Location", "Amenity", "Dish"]}, {"sentence": "is there any restaurant that serves fried chicken late night", "entity_names": ["fried chicken", "late night"], "entity_types": ["Dish", "Hours"]}, {"sentence": "is there any sandwich shop by the high school", "entity_names": ["sandwich", "by the high school"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there anywhere in kendall square that has tea and delivers", "entity_names": ["kendall square", "tea", "delivers"], "entity_types": ["Location", "Dish", "Amenity"]}, {"sentence": "is there anywhere near here that is open 24 hours and serves breakfast", "entity_names": ["near here", "24 hours", "breakfast"], "entity_types": ["Location", "Hours", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "is there anywhere near me that i can find a great sandwich", "entity_names": ["near me", "great", "sandwich"], "entity_types": ["Location", "Rating", "Dish"]}, {"sentence": "is there anywhere thats open past 1 am with exceptional prices", "entity_names": ["open past 1 am", "exceptional"], "entity_types": ["Hours", "Price"]}, {"sentence": "is there anywhere to eat after 10 p m", "entity_names": ["after 10 p m"], "entity_types": ["Hours"]}, {"sentence": "is there caribbean food around here", "entity_names": ["caribbean", "around here"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "is there food near by", "entity_names": ["near by"], "entity_types": ["Location"]}, {"sentence": "is there moderately priced food near", "entity_names": ["moderately", "near"], "entity_types": ["Price", "Location"]}, {"sentence": "is there more than one fatty fish restaurant on long island", "entity_names": ["fatty fish", "long island"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "is there parking at pierres", "entity_names": ["parking", "pierres"], "entity_types": ["Amenity", "Restaurant Name"]}, {"sentence": "is there somewhere to eat that is kid friendly", "entity_names": ["kid friendly"], "entity_types": ["Amenity"]}, {"sentence": "is this restaurant a hidden find", "entity_names": ["hidden find"], "entity_types": ["Amenity"]}, {"sentence": "is this restaurant a local favorite", "entity_names": ["local", "favorite"], "entity_types": ["Location", "Rating"]}, {"sentence": "is this restuarant open 7 days a week", "entity_names": ["open 7 days"], "entity_types": ["Hours"]}, {"sentence": "is togos smoke free", "entity_names": ["togos", "smoke free"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "is ye olde cottage restaurant on the way i need a place to take some clients", "entity_names": ["ye olde cottage restaurant", "way", "take some clients"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "its 10 pm is anything open", "entity_names": ["10 pm", "open"], "entity_types": ["Hours", "Hours"]}, {"sentence": "jeez the traffic is horrible today it looks like we need something quick off to donalds", "entity_names": ["donalds"], "entity_types": ["Restaurant Name"]}, {"sentence": "kid friendly restaurant never design center place with uper nice service", "entity_names": ["kid friendly", "design center place", "uper", "nice service"], "entity_types": ["Amenity", "Location", "Rating", "Amenity"]}, {"sentence": "king fung garden two with parking", "entity_names": ["king fung garden two", "parking"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "lets find the fanciest french cuisine in the city", "entity_names": ["fanciest", "french", "in the city"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "lets get some fries", "entity_names": ["fries"], "entity_types": ["Dish"]}, {"sentence": "lets go get a taco", "entity_names": ["taco"], "entity_types": ["Dish"]}, {"sentence": "list all restaurants that cater to families within a ten mile location of my current location", "entity_names": ["cater to families", "within a ten mile", "location", "current location"], "entity_types": ["Amenity", "Location", "Amenity", "Location"]}, {"sentence": "list close restaurants", "entity_names": ["close"], "entity_types": ["Location"]}, {"sentence": "list the nearest mcdonalds to 87 th and ashland", "entity_names": ["nearest", "mc<PERSON><PERSON><PERSON>", "87 th and ashland"], "entity_types": ["Location", "Restaurant Name", "Location"]}, {"sentence": "lobster roll", "entity_names": ["lobster roll"], "entity_types": ["Dish"]}, {"sentence": "local fish tacos", "entity_names": ["local", "fish tacos"], "entity_types": ["Location", "Dish"]}, {"sentence": "local hooters", "entity_names": ["local", "hooters"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "local mcdonalds please", "entity_names": ["local", "mc<PERSON><PERSON><PERSON>"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "local restaurant joints with smoking areas", "entity_names": ["local", "smoking areas"], "entity_types": ["Location", "Amenity"]}, {"sentence": "local subway resturant", "entity_names": ["local", "subway"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "locate a five star restaurant that is open after 10 pm", "entity_names": ["five star", "open after 10 pm"], "entity_types": ["Rating", "Hours"]}, {"sentence": "locate a place to eat that serves milk shakes", "entity_names": ["milk shakes"], "entity_types": ["Dish"]}, {"sentence": "locate a sushi restaurant and give me their phone number", "entity_names": ["sushi"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "locate all mcdonalds within 5 miles", "entity_names": ["mc<PERSON><PERSON><PERSON>", "within 5 miles"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "locate an all you can eat buffet", "entity_names": ["all you can eat buffet"], "entity_types": ["Amenity"]}, {"sentence": "locate bar with alcohol", "entity_names": ["alcohol"], "entity_types": ["Dish"]}, {"sentence": "locate the nearest fast food restaurant", "entity_names": ["nearest", "fast food"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "locate the nearest restraunt", "entity_names": ["nearest"], "entity_types": ["Location"]}, {"sentence": "look for inexpensive authentic mexican restaurants in chattanooga tennessee", "entity_names": ["inexpensive", "authentic mexican", "chattanooga tennessee"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "look up the reviews for this new asain restaurant", "entity_names": ["asain"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "looking for a 5 star restaurant that serves a great steack", "entity_names": ["5 star restaurant", "great", "steack"], "entity_types": ["Rating", "Rating", "Dish"]}, {"sentence": "looking for a diner with comfortable atmosphere and a rustic setting", "entity_names": ["diner", "comfortable atmosphere", "rustic setting"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity", "Amenity"]}, {"sentence": "looking for a drive through restaurant close by opened 24 7", "entity_names": ["drive", "close by", "opened 24 7"], "entity_types": ["Location", "Location", "Hours"]}, {"sentence": "looking for a good place to get cheese fries", "entity_names": ["good", "cheese fries"], "entity_types": ["Rating", "Dish"]}, {"sentence": "looking for a high end italian restaurant within a 5 miles", "entity_names": ["high", "end", "italian", "within", "5 miles"], "entity_types": ["Price", "Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location", "Location"]}, {"sentence": "looking for a kfc", "entity_names": ["kfc"], "entity_types": ["Restaurant Name"]}, {"sentence": "looking for a kid friendly restaurant in mid price range", "entity_names": ["kid friendly", "mid"], "entity_types": ["Amenity", "Price"]}, {"sentence": "looking for a restaurant with the highest approval ratings", "entity_names": ["highest approval ratings"], "entity_types": ["Rating"]}, {"sentence": "looking for a strong fair priced restaurant that is near", "entity_names": ["fair", "near"], "entity_types": ["Price", "Location"]}, {"sentence": "looking for a tai restaurant that is nerby that takes online reservations", "entity_names": ["tai", "<PERSON><PERSON>", "online reservations"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Amenity"]}, {"sentence": "looking for a three star restaurant along my current route within the next hundred miles and not more then a mile off route any suggestions", "entity_names": ["three star", "along my current route within the next hundred miles", "not more then a mile off route"], "entity_types": ["Rating", "Location", "Location"]}, {"sentence": "looking for an expensive seafood place make a reservation for 6 people at 6 00", "entity_names": ["expensive", "seafood"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "looking for anything open before 7 a m nearby offering hip music", "entity_names": ["open before 7 a m", "nearby", "hip music"], "entity_types": ["Hours", "Location", "Amenity"]}, {"sentence": "looking for breakfast restaurants with byob", "entity_names": ["breakfast", "byob"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "looking for californian cuisine with great prices", "entity_names": ["californian", "great"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Price"]}, {"sentence": "looking for crowd pleasing fatz along the way", "entity_names": ["crowd pleasing fatz", "along the way"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "looking for d k bakery near here where the stars hang out", "entity_names": ["d k bakery", "near", "stars hang out"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "looking for duck dish with great pricing and good rating", "entity_names": ["duck dish", "great", "good rating"], "entity_types": ["Dish", "Price", "Rating"]}, {"sentence": "looking for freebirds world burrito that is open late", "entity_names": ["freebirds world burrito", "open late"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "looking for hotel dining on central square with great pricing", "entity_names": ["hotel dining", "central square", "great"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Price"]}, {"sentence": "looking for local pizzahut", "entity_names": ["local", "pizzahut"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "looking for olympic house of pizza for lunch any close by", "entity_names": ["olympic house of pizza", "lunch", "close by"], "entity_types": ["Restaurant Name", "Hours", "Location"]}, {"sentence": "looking for reasonably priced diners", "entity_names": ["reasonably", "diners"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "looking for sebastians restaurant offering generous portions", "entity_names": ["sebastians restaurant", "generous portions"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "looking for star hangout close by called marios italian restaurant", "entity_names": ["star hangout", "close by", "marios italian restaurant"], "entity_types": ["Amenity", "Location", "Restaurant Name"]}, {"sentence": "looking for sushi with prix fixe menu at a reasonable price", "entity_names": ["sushi", "prix fixe menu", "reasonable"], "entity_types": ["Dish", "Amenity", "Price"]}, {"sentence": "looking for tapas cuisine with great prices", "entity_names": ["tapas", "great"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Price"]}, {"sentence": "looking for the cheapest place to eat im on a budget", "entity_names": ["cheapest"], "entity_types": ["Price"]}, {"sentence": "looking for tom yum cafe around here with excellent prices", "entity_names": ["tom yum cafe", "around here", "excellent"], "entity_types": ["Restaurant Name", "Location", "Price"]}, {"sentence": "looking for tuna dishes that offer parking", "entity_names": ["tuna dishes", "offer parking"], "entity_types": ["Dish", "Amenity"]}, {"sentence": "looking for urban gourmet on bay road with great wine lists", "entity_names": ["urban gourmet", "bay road", "great"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Rating"]}, {"sentence": "looking for very cheap sake restaurant that allows smoking", "entity_names": ["very cheap", "sake", "allows smoking"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "make a 5 00 p m reservation for black angus", "entity_names": ["black angus"], "entity_types": ["Restaurant Name"]}, {"sentence": "make a reservation for 3 people at 8 pm tonight at the melting pot in towson", "entity_names": ["reservation for 3 people", "8 pm tonight", "melting pot", "towson"], "entity_types": ["Amenity", "Hours", "Restaurant Name", "Location"]}, {"sentence": "make a reservation for six pm at a french restaurant with at least a 4 star rating", "entity_names": ["reservation", "six pm", "french", "4 star rating"], "entity_types": ["Amenity", "Hours", "<PERSON><PERSON><PERSON><PERSON>", "Rating"]}, {"sentence": "make a reservation for us at the french restaurant for tonight at 8 pm also give us the dress code for the restaurant", "entity_names": ["french", "dress code"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "make a reservation tonight for four at billies steakhouse", "entity_names": ["reservation", "tonight", "for four", "billies steakhouse"], "entity_types": ["Amenity", "Hours", "Amenity", "Restaurant Name"]}, {"sentence": "make me a reservation for a restaurant with a nonsmoking patio and that isnt fancy for 30 minutes from now", "entity_names": ["reservation", "nonsmoking patio", "isnt fancy", "30 minutes from now"], "entity_types": ["Amenity", "Amenity", "Amenity", "Hours"]}, {"sentence": "make me reservations for three people at devitos italian", "entity_names": ["devitos italian"], "entity_types": ["Restaurant Name"]}, {"sentence": "may i have the business hours for the nearest red lobster", "entity_names": ["business", "nearest", "red lobster"], "entity_types": ["Hours", "Location", "Restaurant Name"]}, {"sentence": "mexican food", "entity_names": ["mexican"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "mexican food to go", "entity_names": ["mexican", "to go"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "mexican restaurant nearby", "entity_names": ["mexican", "nearby"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "moderately priced seafood restaurant", "entity_names": ["moderately", "seafood"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "my date and i would like some espresso thats within 10 min", "entity_names": ["espresso", "within 10 min"], "entity_types": ["Dish", "Location"]}, {"sentence": "my kids need lunch can you help", "entity_names": ["kids", "lunch"], "entity_types": ["Amenity", "Amenity"]}, {"sentence": "my kids want a happy meal", "entity_names": ["happy"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "my kids want to sit outside and eat cheeseburgers", "entity_names": ["sit outside", "cheeseburgers"], "entity_types": ["Amenity", "Dish"]}, {"sentence": "my nephew loves burgers where is the nearest five guys", "entity_names": ["burgers", "nearest", "five guys"], "entity_types": ["Dish", "Location", "Restaurant Name"]}, {"sentence": "name the local buffets", "entity_names": ["local buffets"], "entity_types": ["Location"]}, {"sentence": "navigate me to a thai restaurant thats 4 stars or higher", "entity_names": ["thai", "4 stars or higher"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Rating"]}, {"sentence": "nearest fast food restaurant", "entity_names": ["nearest", "fast food"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "need a four star restaurant in a hotel", "entity_names": ["four star", "in a hotel"], "entity_types": ["Rating", "Location"]}, {"sentence": "need a reservation for six pm at the steak shack in detroit for four people and possibly a baby for tonight", "entity_names": ["six pm", "the steak shack", "detroit", "tonight"], "entity_types": ["Hours", "Restaurant Name", "Location", "Hours"]}, {"sentence": "newbury street bakery and deli directions", "entity_names": ["newbury street bakery and deli"], "entity_types": ["Restaurant Name"]}, {"sentence": "nyc 5 star pizza parlors", "entity_names": ["nyc", "5 star", "pizza", "parlors"], "entity_types": ["Location", "Rating", "Dish", "Restaurant Name"]}, {"sentence": "of the restaurants that require suit jackets for men which have the best service and food", "entity_names": ["require suit jackets for men", "best service and food"], "entity_types": ["Amenity", "Rating"]}, {"sentence": "please find a high end restaurant that i can take my clients to", "entity_names": ["high end", "clients"], "entity_types": ["Rating", "Amenity"]}, {"sentence": "please find a japanese place that near an indian restaurant", "entity_names": ["japanese", "near", "indian"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "please find a mexican restaurant with a smoking section and more then 1 star review", "entity_names": ["mexican", "smoking section", "more then 1 star review"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity", "Rating"]}, {"sentence": "please find a restaurant where i can order cocktails with lunch", "entity_names": ["cocktails", "lunch"], "entity_types": ["Amenity", "Amenity"]}, {"sentence": "please find a taco place near my house", "entity_names": ["taco", "near my house"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "please find italian restaurants that are child friendly", "entity_names": ["italian", "child friendly"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "please find me a cheap chinese restaurant nearby", "entity_names": ["cheap", "chinese", "nearby"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "please find me a child friendly diner with a tv", "entity_names": ["child friendly", "diner", "tv"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "please find me a non smoking restaurant that serves seafood i also want on site parking", "entity_names": ["non smoking", "seafood", "on site parking"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "please find me the nearest thai place", "entity_names": ["nearest", "thai"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "please find the address of international house of pancakes", "entity_names": ["international house of pancakes"], "entity_types": ["Restaurant Name"]}, {"sentence": "please find the closest midpriced sit down restaurant with chicken tenders or macaroni on the kids meal", "entity_names": ["closest", "midpriced", "sit down", "chicken tenders", "macaroni", "kids meal"], "entity_types": ["Location", "Price", "Amenity", "Dish", "Dish", "Amenity"]}, {"sentence": "please get me the street address of the dennys in this area", "entity_names": ["street address", "dennys", "area"], "entity_types": ["Location", "Restaurant Name", "Location"]}, {"sentence": "please give me directions to the nearest made to order place where i can get a cheeseburger", "entity_names": ["nearest", "made to order", "cheeseburger"], "entity_types": ["Location", "Dish", "Dish"]}, {"sentence": "please give me the hours of operation for the burger king nearest me", "entity_names": ["burger king", "nearest me"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "please help me locate a restaurant that allows smoking", "entity_names": ["allows smoking"], "entity_types": ["Amenity"]}, {"sentence": "please list restaurants that serve alcohol within 5 miles", "entity_names": ["alcohol", "within 5 miles"], "entity_types": ["Dish", "Location"]}, {"sentence": "please locate a bar off of main street down town that has valet parking is open late and has good reviews", "entity_names": ["bar", "off of main street down town", "valet parking", "open late", "good reviews"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Amenity", "Hours", "Rating"]}, {"sentence": "please locate a mcdonalds", "entity_names": ["mc<PERSON><PERSON><PERSON>"], "entity_types": ["Restaurant Name"]}, {"sentence": "please make me a reservation for tonight at jax cafe at 7 pm for six", "entity_names": ["reservation", "jax cafe", "7 pm"], "entity_types": ["Amenity", "Restaurant Name", "Hours"]}, {"sentence": "please name all restaurants that offer curb side pick up on highway 43 south", "entity_names": ["curb side pick up", "highway 43 south"], "entity_types": ["Amenity", "Location"]}, {"sentence": "please navigate to the closest coffee shop with free trade coffee", "entity_names": ["closest", "coffee", "free trade coffee"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "Dish"]}, {"sentence": "please show me some reviews about the sushi place", "entity_names": ["sushi", "place"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "please take me to a highly rated family diner", "entity_names": ["highly rated", "family diner"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "pub", "entity_names": ["pub"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "quick i need a hot dog", "entity_names": ["hot dog"], "entity_types": ["Dish"]}, {"sentence": "reasonably priced byob irish restaurant", "entity_names": ["reasonably", "byob", "irish"], "entity_types": ["Price", "Amenity", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "red lobster with no smoking", "entity_names": ["red lobster", "no smoking"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "red robins restaurant", "entity_names": ["red robins"], "entity_types": ["Restaurant Name"]}, {"sentence": "restaurant that caters to children and that is fun for parents and kids alike", "entity_names": ["caters to children", "fun for parents and kids alike"], "entity_types": ["Amenity", "Amenity"]}, {"sentence": "restaurants in the area", "entity_names": ["in the area"], "entity_types": ["Location"]}, {"sentence": "restaurants that take visa or master card", "entity_names": [], "entity_types": []}, {"sentence": "restuarnt that cathay owns", "entity_names": ["cathay"], "entity_types": ["Amenity"]}, {"sentence": "search for a buffet that is a smoke free environment within 5 miles of my location", "entity_names": ["buffet", "smoke free environment", "within 5 miles of my location"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity", "Location"]}, {"sentence": "search for a place that serves meatloaf", "entity_names": ["meatloaf"], "entity_types": ["Dish"]}, {"sentence": "search for dine in restaurant", "entity_names": ["dine in"], "entity_types": ["Amenity"]}, {"sentence": "search for fast food places that also have a kid play area", "entity_names": ["fast food", "kid play area"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "search for restaurants with seafood", "entity_names": ["seafood"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "show me a inexpensive restaurant within 10 miles", "entity_names": ["inexpensive", "within 10 miles"], "entity_types": ["Price", "Location"]}, {"sentence": "show me all of the local restaurants with a smoking area", "entity_names": ["local", "smoking area"], "entity_types": ["Location", "Amenity"]}, {"sentence": "show me all the restaurants that are not franchises in my area", "entity_names": ["not franchises", "in my area"], "entity_types": ["Amenity", "Location"]}, {"sentence": "show me restaurants that serve senior portions", "entity_names": ["senior portions"], "entity_types": ["Amenity"]}, {"sentence": "show me some cheap eats", "entity_names": ["cheap"], "entity_types": ["Price"]}, {"sentence": "show me some mexican restaurants that arent too far", "entity_names": ["mexican", "arent too far"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "show me some restaurants nearby that have 3 star or higher rating", "entity_names": ["nearby", "3 star or higher rating"], "entity_types": ["Location", "Rating"]}, {"sentence": "show me the moderately priced japanese restaurants in a ten mile radius", "entity_names": ["moderately", "japanese", "ten mile radius"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "show me the three closest wendys", "entity_names": ["closest", "wendys"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "show me the three nearest boston market restaurants", "entity_names": ["nearest", "boston market"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "show me where the nearest sports bar is", "entity_names": ["nearest", "sports bar"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "smoke friendly restaurants", "entity_names": ["smoke friendly"], "entity_types": ["Amenity"]}, {"sentence": "some jerky would make my day", "entity_names": ["jerky"], "entity_types": ["Dish"]}, {"sentence": "someplace where the prices for food arent bad and i can get something to eat before 8 am", "entity_names": ["arent bad", "before 8 am"], "entity_types": ["Price", "Hours"]}, {"sentence": "strega restaurant and lounge offering local cuisine", "entity_names": ["strega restaurant and lounge", "local"], "entity_types": ["Restaurant Name", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "suppose im looking for the best fish in sd", "entity_names": ["best", "fish", "sd"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "tacos stand near my location", "entity_names": ["tacos stand", "near my location"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "take me for the best dessert today", "entity_names": ["best", "dessert", "today"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Hours"]}, {"sentence": "take me to a hole in the wall that has high reviews is open late and serve drinks", "entity_names": ["hole in the wall", "high reviews", "open late", "drinks"], "entity_types": ["Amenity", "Rating", "Hours", "Dish"]}, {"sentence": "take me to a local restaurant", "entity_names": ["local"], "entity_types": ["Location"]}, {"sentence": "take me to a nice restaurant with generous portions", "entity_names": ["nice", "generous portions"], "entity_types": ["Rating", "Amenity"]}, {"sentence": "take me to a restaurant i would like", "entity_names": ["would like"], "entity_types": ["Rating"]}, {"sentence": "take me to the closest restaurant i can smoke in", "entity_names": ["closest", "smoke"], "entity_types": ["Location", "Amenity"]}, {"sentence": "take me to the nearest tex mex restaurant", "entity_names": ["nearest", "tex mex"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "tell me the hours of chipotle in midtown", "entity_names": ["chipotle", "midtown"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "tell me what restaurants are available within a 5 minute drive", "entity_names": ["5 minute drive"], "entity_types": ["Location"]}, {"sentence": "tell me where i can find a good meal in this town", "entity_names": ["good", "town"], "entity_types": ["Rating", "Location"]}, {"sentence": "thai", "entity_names": ["thai"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "the most expensive restaurant in town with a dress code and reservations", "entity_names": ["most expensive", "in town", "dress code", "reservations"], "entity_types": ["Price", "Location", "Amenity", "Amenity"]}, {"sentence": "the opening times of nearby pizza places", "entity_names": ["opening times", "nearby", "pizza"], "entity_types": ["Hours", "Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "today is a good day for some tacos off to taco bell", "entity_names": ["tacos", "taco bell"], "entity_types": ["Dish", "Restaurant Name"]}, {"sentence": "tummys grumbling give me the nearest fast food joint", "entity_names": ["nearest", "fast food"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "want mushrooms near mansfield avenue before 7 am", "entity_names": ["mushrooms", "near mansfield avenue", "before 7 am"], "entity_types": ["Dish", "Location", "Hours"]}, {"sentence": "want to find a restaurant that specilizes in spagetti", "entity_names": ["specilizes in", "<PERSON><PERSON><PERSON>"], "entity_types": ["Amenity", "Dish"]}, {"sentence": "we have a crying baby how far is a place to eat that is kid friendly", "entity_names": ["kid friendly"], "entity_types": ["Amenity"]}, {"sentence": "we want t go to dinner and then dance", "entity_names": ["dinner", "dance"], "entity_types": ["Hours", "Amenity"]}, {"sentence": "what are some fine dining restaurants in the area", "entity_names": ["fine dining", "in the area"], "entity_types": ["Amenity", "Location"]}, {"sentence": "what are some of the best restaurants in this city", "entity_names": ["best", "in this city"], "entity_types": ["Rating", "Location"]}, {"sentence": "what are some of the locally favourite restaurants", "entity_names": ["locally"], "entity_types": ["Location"]}, {"sentence": "what are some restaurants that serve american food that are with in one mile", "entity_names": ["american", "with in one mile"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "what are the average prices for lunch at mikanos", "entity_names": ["lunch", "mi<PERSON><PERSON>"], "entity_types": ["Hours", "Restaurant Name"]}, {"sentence": "what are the directions to siu kuen chinese restaurant", "entity_names": ["siu kuen chinese restaurant"], "entity_types": ["Restaurant Name"]}, {"sentence": "what are the directions to the speakeasy cafe", "entity_names": ["speakeasy cafe"], "entity_types": ["Restaurant Name"]}, {"sentence": "what are the fast food restraurants on the next 6 miles of this road", "entity_names": ["fast food", "next 6 miles of this road"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "what are the hours to verazzanos", "entity_names": ["verazzanos"], "entity_types": ["Restaurant Name"]}, {"sentence": "what are the portions like at red lobster", "entity_names": ["red lobster"], "entity_types": ["Restaurant Name"]}, {"sentence": "what are the prices like at gregorys restaurant", "entity_names": ["gregor<PERSON>"], "entity_types": ["Restaurant Name"]}, {"sentence": "what are the reviews on this italian restaurant", "entity_names": ["italian"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "what are the spiciest local favourites served at cappys pizza and subs", "entity_names": ["cappys pizza and subs"], "entity_types": ["Restaurant Name"]}, {"sentence": "what are the three highest rated restaurants in my area", "entity_names": ["three highest rated", "my area"], "entity_types": ["Rating", "Location"]}, {"sentence": "what asian restaurants offer carry out", "entity_names": ["asian", "carry out"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "what chicken restaurants are in the area", "entity_names": ["chicken", "in the area"], "entity_types": ["Dish", "Location"]}, {"sentence": "what do you know about restaurants that have impressive wine lists", "entity_names": ["impressive wine lists"], "entity_types": ["Amenity"]}, {"sentence": "what fast food is near by", "entity_names": ["fast food", "near by"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "what has the best dumplings in town", "entity_names": ["best", "dumplings", "in town"], "entity_types": ["Rating", "Dish", "Location"]}, {"sentence": "what high end restaurants are nearby", "entity_names": ["high end", "nearby"], "entity_types": ["Rating", "Location"]}, {"sentence": "what is a family friendly restaurant in dorche that serves vietnamese food", "entity_names": ["family friendly", "dorche", "vietnamese"], "entity_types": ["Amenity", "Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "what is an italian restaurant in the area with family style meals", "entity_names": ["italian", "in", "area", "family style"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Location", "Amenity"]}, {"sentence": "what is dominos numbers", "entity_names": ["dominos"], "entity_types": ["Restaurant Name"]}, {"sentence": "what is jack in the box phone number", "entity_names": ["jack in the box"], "entity_types": ["Restaurant Name"]}, {"sentence": "what is the address to burger king on route 63", "entity_names": ["burger king", "route 63"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "what is the average mealcost for townline pizza in my city", "entity_names": ["townline pizza", "in my city"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "what is the average price for lunch at olive garden", "entity_names": ["lunch", "olive garden"], "entity_types": ["Hours", "Restaurant Name"]}, {"sentence": "what is the average price range at ruby tuesday", "entity_names": ["average", "ruby tuesday"], "entity_types": ["Price", "Restaurant Name"]}, {"sentence": "what is the best coffee house near here", "entity_names": ["best", "coffee house", "near here"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "what is the best ice cream parlor", "entity_names": ["best", "ice cream parlor"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "what is the best mexican restaurant in town", "entity_names": ["best", "mexican", "in town"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "what is the best pizza place in the theater district", "entity_names": ["best", "pizza", "theater district"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "what is the best restaurant around", "entity_names": ["best", "around"], "entity_types": ["Rating", "Location"]}, {"sentence": "what is the best steak house in kansas city mo", "entity_names": ["best", "steak house", "kansas city mo"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "what is the closest bar near heidis restaurant", "entity_names": ["closest", "heidis restaurant"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "what is the closest chic fil a", "entity_names": ["closest", "chic fil a"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "what is the closest fast food to me", "entity_names": ["closest", "fast food"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "what is the closest wendys to me", "entity_names": ["closest", "wendys"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "what is the dress code at fazolis", "entity_names": ["fazolis"], "entity_types": ["Restaurant Name"]}, {"sentence": "what is the dress code at the precinct", "entity_names": ["the precinct"], "entity_types": ["Restaurant Name"]}, {"sentence": "what is the dress code for eating at planet hollywood", "entity_names": ["planet hollywood"], "entity_types": ["Restaurant Name"]}, {"sentence": "what is the dress code for ruth chris steakhouse", "entity_names": ["dress code", "ruth chris steakhouse"], "entity_types": ["Amenity", "Restaurant Name"]}, {"sentence": "what is the entree price at kiosque", "entity_names": ["entree", "kiosque"], "entity_types": ["Price", "Restaurant Name"]}, {"sentence": "what is the fastest route to the sonic", "entity_names": ["sonic"], "entity_types": ["Restaurant Name"]}, {"sentence": "what is the favorite type of food people eat out here and where can i get it", "entity_names": ["favorite", "out here"], "entity_types": ["Rating", "Location"]}, {"sentence": "what is the highest rated thai restaurant in austin", "entity_names": ["highest rated", "thai", "austin"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "what is the most closest eat in restaurant in the area", "entity_names": ["closest", "eat in", "in the area"], "entity_types": ["Location", "Amenity", "Location"]}, {"sentence": "what is the most expensive restaurant with a dress code and valet parking within 15 miles", "entity_names": ["most expensive", "dress code", "valet parking", "within 15 miles"], "entity_types": ["Price", "Amenity", "Amenity", "Location"]}, {"sentence": "what is the most popular italian restaurant near me", "entity_names": ["italian", "near me"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "what is the name of that expensive greek restaurant in alston", "entity_names": ["expensive", "greek", "<PERSON>ston"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "what is the name of the restaurant within 50 miles of chatswin that was built inside a cavern", "entity_names": ["within 50 miles of chatswin", "built inside a cavern"], "entity_types": ["Location", "Amenity"]}, {"sentence": "what is the name or phone number of the coffee shop on seminary street", "entity_names": ["coffee", "seminary street"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "what is the nearest fast food restaurant", "entity_names": ["nearest", "fast food"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "what is the phone number for the closest chinese restaurant", "entity_names": ["closest", "chinese"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "what is the phone number of a nearby restaurant that serves garlic shrimp", "entity_names": ["nearby", "garlic shrimp"], "entity_types": ["Location", "Dish"]}, {"sentence": "what is the phone number of bills pizza", "entity_names": ["bills pizza"], "entity_types": ["Restaurant Name"]}, {"sentence": "what is the phone number of the mcdonalds on the east side of town", "entity_names": ["mc<PERSON><PERSON><PERSON>", "east side of town"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "what is the phone number to alans on oak street", "entity_names": ["alans", "oak street"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "what is the price for a lobster entree at martys marina", "entity_names": ["lobster", "martys marina"], "entity_types": ["Dish", "Restaurant Name"]}, {"sentence": "what is the price range for dinner at clar<PERSON> family restaurant", "entity_names": ["clarkes family restaurant"], "entity_types": ["Restaurant Name"]}, {"sentence": "what is the rating for pho saigon in mesa arizona", "entity_names": ["pho saigon", "mesa arizona"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "what is the rating on arbys", "entity_names": ["arbys"], "entity_types": ["Restaurant Name"]}, {"sentence": "what is the rating on burger island", "entity_names": ["burger island"], "entity_types": ["Restaurant Name"]}, {"sentence": "what is this months special at mcdonalds", "entity_names": ["mc<PERSON><PERSON><PERSON>"], "entity_types": ["Restaurant Name"]}, {"sentence": "what italian restaurant is open 24 hours on dalton street", "entity_names": ["italian", "open 24 hours", "dalton street"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Hours", "Location"]}, {"sentence": "what kind of atmosphere does simpsons have", "entity_names": ["simpsons"], "entity_types": ["Restaurant Name"]}, {"sentence": "what kind of chicken dishes does theos serve", "entity_names": ["theos"], "entity_types": ["Restaurant Name"]}, {"sentence": "what kind of fondue does the magic kettle have", "entity_names": ["fondue", "magic kettle"], "entity_types": ["Dish", "Restaurant Name"]}, {"sentence": "what kind of food does abc cafe serve", "entity_names": ["abc cafe"], "entity_types": ["Restaurant Name"]}, {"sentence": "what kind of food does this place have", "entity_names": [], "entity_types": []}, {"sentence": "what kind of food does ubice sell", "entity_names": ["ubice"], "entity_types": ["Restaurant Name"]}, {"sentence": "what kosher restaurants still have smoking sections", "entity_names": ["kosher", "smoking sections"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "what local restaurants have an outdoor play area for young children", "entity_names": ["local", "outdoor play area"], "entity_types": ["Location", "Amenity"]}, {"sentence": "what nearby is open past midnight and has fabulous service", "entity_names": ["nearby", "open past midnight", "fabulous service"], "entity_types": ["Location", "Hours", "Rating"]}, {"sentence": "what nearby places are byob", "entity_names": ["nearby", "byob"], "entity_types": ["Location", "Amenity"]}, {"sentence": "what nearby restaurant has the best reviews", "entity_names": ["nearby", "best reviews"], "entity_types": ["Location", "Rating"]}, {"sentence": "what nearby restaurants serve grilled tilapia", "entity_names": ["nearby", "grilled tilapia"], "entity_types": ["Location", "Dish"]}, {"sentence": "what pancake restaurants are nearby", "entity_names": ["pancake", "nearby"], "entity_types": ["Dish", "Location"]}, {"sentence": "what pizza place has the best toppings and is no farther than 4 miles", "entity_names": ["pizza", "best toppings", "no farther than 4 miles"], "entity_types": ["Dish", "Rating", "Location"]}, {"sentence": "what place in saugus has the best decor", "entity_names": ["saugus", "best", "decor"], "entity_types": ["Location", "Rating", "Amenity"]}, {"sentence": "what places are cheap here", "entity_names": ["cheap", "here"], "entity_types": ["Price", "Location"]}, {"sentence": "what places have great selections of beer", "entity_names": ["beer"], "entity_types": ["Dish"]}, {"sentence": "what places have pretty good prices", "entity_names": ["pretty good"], "entity_types": ["Price"]}, {"sentence": "what places in roxbury are open before 8 am and have excellent service", "entity_names": ["roxbury", "open before 8 am", "excellent service"], "entity_types": ["Location", "Hours", "Rating"]}, {"sentence": "what places serve dinner late", "entity_names": ["dinner late"], "entity_types": ["Hours"]}, {"sentence": "what portugeese restaurant is on colonel bell drive", "entity_names": ["portugeese", "colonel bell drive"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "what restaurant are cheap and only vegan", "entity_names": ["cheap", "vegan"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "what restaurant are open till 10 pm", "entity_names": ["open till 10 pm"], "entity_types": ["Hours"]}, {"sentence": "what restaurant has middle eastern food and great service", "entity_names": ["middle eastern", "great service"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Rating"]}, {"sentence": "what restaurant is expensive and has entertainment", "entity_names": ["expensive", "entertainment"], "entity_types": ["Price", "Amenity"]}, {"sentence": "what restaurant is open till 9 pm", "entity_names": ["open till 9 pm"], "entity_types": ["Hours"]}, {"sentence": "what restaurant nearby are affordable", "entity_names": ["nearby", "affordable"], "entity_types": ["Location", "Price"]}, {"sentence": "what restaurant on the south side has the hottest sauce for their chicken wings", "entity_names": ["south side", "hottest", "sauce", "chicken wings"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "Dish", "Dish"]}, {"sentence": "what restaurant sells crab close by", "entity_names": ["crab", "close by"], "entity_types": ["Dish", "Location"]}, {"sentence": "what restaurant serves burritos 7 days per week", "entity_names": ["burritos", "7 days per week"], "entity_types": ["Dish", "Hours"]}, {"sentence": "what restaurant serves the largest portions of meat within 10 mile", "entity_names": ["largest portions", "meat", "within 10 mile"], "entity_types": ["Amenity", "Dish", "Location"]}, {"sentence": "what restaurant within 25 miles has the poorest quality food", "entity_names": ["within 25 miles", "poorest"], "entity_types": ["Location", "Rating"]}, {"sentence": "what restaurants are close to the museum", "entity_names": ["museum"], "entity_types": ["Location"]}, {"sentence": "what restaurants are nearby", "entity_names": ["nearby"], "entity_types": ["Location"]}, {"sentence": "what restaurants are open in the downtown area", "entity_names": ["open", "in the downtown area"], "entity_types": ["Hours", "Location"]}, {"sentence": "what restaurants are open past midnight", "entity_names": ["open past midnight"], "entity_types": ["Hours"]}, {"sentence": "what restaurants are still open this late", "entity_names": ["still open this late"], "entity_types": ["Hours"]}, {"sentence": "what restaurants are within 1 mile and have good prices", "entity_names": ["within 1 mile", "good"], "entity_types": ["Location", "Price"]}, {"sentence": "what restaurants around here have free wi fi", "entity_names": ["around here", "free wi fi"], "entity_types": ["Location", "Amenity"]}, {"sentence": "what restaurants downtown is open after 9 p", "entity_names": ["downtown", "open after 9 p"], "entity_types": ["Location", "Hours"]}, {"sentence": "what restaurants have a large beer menu and are close to a hotel", "entity_names": ["large beer menu", "close to a hotel"], "entity_types": ["Amenity", "Location"]}, {"sentence": "what restaurants have nice family friendly brunch on weekends", "entity_names": ["nice family friendly", "brunch", "weekends"], "entity_types": ["Amenity", "Hours", "Hours"]}, {"sentence": "what restaurants nearby serve ribs and ice cream sundaes", "entity_names": ["nearby", "ribs", "ice cream sundaes"], "entity_types": ["Location", "Dish", "Dish"]}, {"sentence": "what restaurants stay open after 2 am within 5 miles", "entity_names": ["open after 2 am", "within 5 miles"], "entity_types": ["Hours", "Location"]}, {"sentence": "what route should i take in order to get to the closest fast food place", "entity_names": ["closest", "fast food"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "what seafood place around here has the biggest portions", "entity_names": ["seafood", "around here"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "what time does is breakfast over at burger king", "entity_names": ["breakfast", "burger king"], "entity_types": ["Hours", "Restaurant Name"]}, {"sentence": "what time does jollibees open", "entity_names": ["joll<PERSON>es", "open"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "what time does kareems restaurant open", "entity_names": ["kareems"], "entity_types": ["Restaurant Name"]}, {"sentence": "what time does king palace serve dimsum until", "entity_names": ["king palace", "serve", "dimsum", "until"], "entity_types": ["Restaurant Name", "Amenity", "Dish", "Hours"]}, {"sentence": "what time does little johns open on sunday", "entity_names": ["little johns", "on sunday"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "what time does mc<PERSON>alds in main street open", "entity_names": ["mc<PERSON><PERSON><PERSON>", "main street"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "what time does on the rocks on seminary stop serving food", "entity_names": ["time", "on the rocks", "seminary", "stop serving food"], "entity_types": ["Hours", "Restaurant Name", "Location", "Hours"]}, {"sentence": "what time does papa johns close", "entity_names": ["papa johns", "close"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "what time does red robin close", "entity_names": ["red robin"], "entity_types": ["Restaurant Name"]}, {"sentence": "what time does sonic burger open", "entity_names": ["sonic burger"], "entity_types": ["Restaurant Name"]}, {"sentence": "what time does sonic open", "entity_names": ["sonic", "open"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "what time does subway open", "entity_names": ["does", "subway"], "entity_types": ["Hours", "Restaurant Name"]}, {"sentence": "what time does the burger king on main street closes", "entity_names": ["burger king", "main street"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "what time does the nearest chipotle close", "entity_names": ["nearest", "chipotle"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "what time does the pho place in reno close", "entity_names": ["pho place", "reno", "close"], "entity_types": ["Restaurant Name", "Location", "Hours"]}, {"sentence": "what time does the sushi place near me open", "entity_names": ["sushi", "near", "open"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Hours"]}, {"sentence": "what time is happy hour at the juice shop", "entity_names": ["happy hour", "juice shop"], "entity_types": ["Amenity", "Restaurant Name"]}, {"sentence": "what time of the day does mcdonals start serving lunch the one near here", "entity_names": ["mc<PERSON><PERSON>", "near here"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "what type of cusine does gates have", "entity_names": ["gates"], "entity_types": ["Restaurant Name"]}, {"sentence": "what vegetarian options does zaxbys offer", "entity_names": ["zaxbys"], "entity_types": ["Restaurant Name"]}, {"sentence": "what would be an impressive restaurant for me to take a future business partner to", "entity_names": ["impressive", "business"], "entity_types": ["Rating", "Amenity"]}, {"sentence": "whats a cheap place near melrose that serves pakistani styled kabobs", "entity_names": ["cheap", "near melrose", "paki<PERSON>i", "kabobs"], "entity_types": ["Price", "Location", "<PERSON><PERSON><PERSON><PERSON>", "Dish"]}, {"sentence": "whats a good restaurant for a casual date", "entity_names": ["good", "casual date"], "entity_types": ["Rating", "Amenity"]}, {"sentence": "whats a nicely priced southwestern place near here", "entity_names": ["nicely", "southwestern", "near here"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "whats a popular place to east", "entity_names": ["popular"], "entity_types": ["Rating"]}, {"sentence": "whats an expensive belgian restaurant in san diego", "entity_names": ["expensive", "belgian", "san diego"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "whats open after noon", "entity_names": ["open after noon"], "entity_types": ["Hours"]}, {"sentence": "whats the address of best locally owned sub sandwich shop", "entity_names": ["best", "locally", "owned", "sub sandwich"], "entity_types": ["Rating", "Location", "Amenity", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "whats the best barbeque restaurant in memphis", "entity_names": ["best", "barbeque", "memphis"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "whats the best french style cafe in sacramento", "entity_names": ["best", "french", "cafe", "sacramento"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "whats the best indian restaurant in the area", "entity_names": ["best", "indian", "in the area"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "whats the best place around here to get cheese", "entity_names": ["best", "cheese"], "entity_types": ["Rating", "Dish"]}, {"sentence": "whats the best restaurant within 10 blocks from us", "entity_names": ["best", "within 10 blocks from us"], "entity_types": ["Rating", "Location"]}, {"sentence": "whats the cheapest fast food restaurant that has a play area for children", "entity_names": ["cheapest", "fast food", "play area"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "whats the closest and cheapest bistro nearby", "entity_names": ["closest", "cheapest", "bistro", "nearby"], "entity_types": ["Location", "Price", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "whats the closest chinese food place", "entity_names": ["closest", "chinese"], "entity_types": ["Location", "Amenity"]}, {"sentence": "whats the closest pizza restaurant", "entity_names": ["closest", "pizza"], "entity_types": ["Location", "Dish"]}, {"sentence": "whats the closest restaurant with a full salad bar", "entity_names": ["closest", "full salad bar"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "whats the most expensive restaurant on the back bay thats open until midnight", "entity_names": ["most expensive", "back bay", "open until midnight"], "entity_types": ["Price", "Location", "Hours"]}, {"sentence": "whats the most popular steak house around here", "entity_names": ["popular", "steak house", "around here"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "whats the nearest pizza place that serves anchovies", "entity_names": ["nearest", "pizza", "anchovies"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "Dish"]}, {"sentence": "whats the phone number for that family owned thai restaurant on the north side", "entity_names": ["family owned", "thai", "north side"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "whats the phone number the restaurant i just pass", "entity_names": ["i just pass"], "entity_types": ["Location"]}, {"sentence": "whats the phone number to bou<PERSON>n in napa", "entity_names": ["bouchon", "napa"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "whats the shortest route to mcdonalds", "entity_names": ["mc<PERSON><PERSON><PERSON>"], "entity_types": ["Restaurant Name"]}, {"sentence": "when does better burger open today", "entity_names": ["better burger"], "entity_types": ["Restaurant Name"]}, {"sentence": "when does churchs open", "entity_names": ["churchs"], "entity_types": ["Restaurant Name"]}, {"sentence": "when does dominos open", "entity_names": ["dominos", "open"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "when does pizza patron open", "entity_names": ["pizza patron", "open"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "when does subway open", "entity_names": ["subway", "open"], "entity_types": ["Restaurant Name", "Hours"]}, {"sentence": "when does white castle close", "entity_names": ["white castle", "close"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "where are some fast food joints in driving distance to here", "entity_names": ["fast food joints", "driving distance"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "where are some mexican restaurants", "entity_names": ["mexican"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where are the restaurants with a formal dress code around here", "entity_names": ["formal dress code", "around here"], "entity_types": ["Amenity", "Location"]}, {"sentence": "where can a group be served omelets", "entity_names": ["omelets"], "entity_types": ["Dish"]}, {"sentence": "where can get some fried chicken", "entity_names": ["fried chicken"], "entity_types": ["Dish"]}, {"sentence": "where can i dine outside and eat pork chops", "entity_names": ["dine outside", "pork chops"], "entity_types": ["Amenity", "Dish"]}, {"sentence": "where can i eat a nice sit down dinner and have some pho", "entity_names": ["sit down dinner", "pho"], "entity_types": ["Amenity", "Dish"]}, {"sentence": "where can i eat african food for cheap", "entity_names": ["african", "cheap"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Price"]}, {"sentence": "where can i eat around here", "entity_names": ["around here"], "entity_types": ["Location"]}, {"sentence": "where can i eat by the water close by", "entity_names": ["by the water", "close by"], "entity_types": ["Amenity", "Location"]}, {"sentence": "where can i eat pita bread late at night", "entity_names": ["pita bread", "late at night"], "entity_types": ["Dish", "Hours"]}, {"sentence": "where can i eat that delivers food", "entity_names": ["delivers food"], "entity_types": ["Amenity"]}, {"sentence": "where can i eat that has valet parking", "entity_names": ["valet parking"], "entity_types": ["Amenity"]}, {"sentence": "where can i eat that isnt cheap", "entity_names": ["isnt cheap"], "entity_types": ["Price"]}, {"sentence": "where can i eat that wont need a reservation and that i can be at in 20 minutes", "entity_names": ["wont need a reservation", "in 20 minutes"], "entity_types": ["Amenity", "Location"]}, {"sentence": "where can i find a cambodian restaurant that also sells ingredients", "entity_names": ["cambodian", "sells ingredients"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "where can i find a cheap place to eat in peabody", "entity_names": ["cheap", "peabody"], "entity_types": ["Price", "Location"]}, {"sentence": "where can i find a coffee shop within 10 miles that is not part of a chain", "entity_names": ["coffee", "within 10 miles", "not part of a chain"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Amenity"]}, {"sentence": "where can i find a coffee zone that has dining at the bar and a good rating", "entity_names": ["coffee zone", "dining at", "bar", "good rating"], "entity_types": ["Restaurant Name", "Amenity", "Amenity", "Rating"]}, {"sentence": "where can i find a cracker barrel near miami", "entity_names": ["cracker barrel", "miami"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "where can i find a good brepub that is open after 11 pm", "entity_names": ["brepub", "open after 11 pm"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Hours"]}, {"sentence": "where can i find a good place to eat that is in within miles from me", "entity_names": ["good", "within miles"], "entity_types": ["Rating", "Location"]}, {"sentence": "where can i find a good pork chop", "entity_names": ["good", "pork chop"], "entity_types": ["Rating", "Dish"]}, {"sentence": "where can i find a good restaurant in the theatre district", "entity_names": ["good", "theatre district"], "entity_types": ["Rating", "Location"]}, {"sentence": "where can i find a high end restaurant on chestnut street open after 11 pm", "entity_names": ["high end", "chestnut street", "open after 11 pm"], "entity_types": ["Price", "Location", "Hours"]}, {"sentence": "where can i find a late night taco joint", "entity_names": ["late night", "taco joint"], "entity_types": ["Hours", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where can i find a nice gelato place that offers carryout", "entity_names": ["gelato", "carryout"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "where can i find a place for people watching nearby", "entity_names": ["people watching", "nearby"], "entity_types": ["Amenity", "Location"]}, {"sentence": "where can i find a place that serves gelato", "entity_names": ["gelato"], "entity_types": ["Dish"]}, {"sentence": "where can i find a place to eat that serves pasta", "entity_names": ["pasta"], "entity_types": ["Dish"]}, {"sentence": "where can i find a place with a great wine list", "entity_names": ["great wine list"], "entity_types": ["Amenity"]}, {"sentence": "where can i find a place within 5 minutes with great prices", "entity_names": ["within 5 minutes", "great"], "entity_types": ["Location", "Price"]}, {"sentence": "where can i find a rainforest cafe", "entity_names": ["rainforest cafe"], "entity_types": ["Restaurant Name"]}, {"sentence": "where can i find a restaurant near me", "entity_names": ["near me"], "entity_types": ["Location"]}, {"sentence": "where can i find a restaurant that has good portions and is open at 9 p", "entity_names": ["good portions", "open at 9 p"], "entity_types": ["Amenity", "Hours"]}, {"sentence": "where can i find a restaurant with a vegetarian senior discount menu", "entity_names": ["vegetarian", "senior discount"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "where can i find a seafood restaurant that also sells fresh fish", "entity_names": ["seafood", "sells fresh fish"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "where can i find a sports bar in nassau county new york", "entity_names": ["sports bar", "nassau county new york"], "entity_types": ["Amenity", "Location"]}, {"sentence": "where can i find a thai restaurant that is non smoking", "entity_names": ["thai", "non smoking"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "where can i find authentic mexican cuisine at a great price", "entity_names": ["authentic mexican", "great"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Price"]}, {"sentence": "where can i find bobby hacketts restaurant", "entity_names": ["bobby hacketts"], "entity_types": ["Restaurant Name"]}, {"sentence": "where can i find cheap vegan food", "entity_names": ["cheap", "vegan"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where can i find good irish food", "entity_names": ["irish"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where can i find halal with a price not so high", "entity_names": ["halal", "price not so high"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Price"]}, {"sentence": "where can i find naugles", "entity_names": ["naugles"], "entity_types": ["Restaurant Name"]}, {"sentence": "where can i find somewhere relaxed thats open in the morning around beacon hill", "entity_names": ["relaxed", "open in the morning", "around beacon hill"], "entity_types": ["Amenity", "Hours", "Location"]}, {"sentence": "where can i find the best sushi in town", "entity_names": ["best", "sushi", "in town"], "entity_types": ["Rating", "Dish", "Location"]}, {"sentence": "where can i find the cheapest burgers in town", "entity_names": ["cheapest", "burgers", "in town"], "entity_types": ["Price", "Dish", "Location"]}, {"sentence": "where can i find the cheapest pizza", "entity_names": ["cheapest", "pizza"], "entity_types": ["Price", "Dish"]}, {"sentence": "where can i find the cheapest take out chinese food", "entity_names": ["cheapest", "take out", "chinese"], "entity_types": ["Price", "Amenity", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where can i find the closest bakery", "entity_names": ["closest", "bakery"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where can i find the highest rated breakfast burrito", "entity_names": ["highest rated", "breakfast burrito"], "entity_types": ["Rating", "Dish"]}, {"sentence": "where can i find tofu in city hall plaza with parking", "entity_names": ["tofu", "city hall plaza", "parking"], "entity_types": ["Dish", "Location", "Amenity"]}, {"sentence": "where can i get a banana split", "entity_names": ["banana split"], "entity_types": ["Dish"]}, {"sentence": "where can i get a burger", "entity_names": ["burger"], "entity_types": ["Dish"]}, {"sentence": "where can i get a cheap dinner", "entity_names": ["cheap", "dinner"], "entity_types": ["Price", "Hours"]}, {"sentence": "where can i get a cheap slice of pizza in huntington ny", "entity_names": ["cheap", "slice of pizza", "huntington ny"], "entity_types": ["Price", "Dish", "Location"]}, {"sentence": "where can i get a good bagel from", "entity_names": ["good", "bagel"], "entity_types": ["Rating", "Dish"]}, {"sentence": "where can i get a good deal on two pizzas in manhattan", "entity_names": ["pizzas", "manhattan"], "entity_types": ["Dish", "Location"]}, {"sentence": "where can i get a good sandwich for a good price", "entity_names": ["good", "sandwich", "good"], "entity_types": ["Rating", "Dish", "Price"]}, {"sentence": "where can i get a great tofu omelette in wayland", "entity_names": ["tofu omelette", "wayland"], "entity_types": ["Dish", "Location"]}, {"sentence": "where can i get a hamburger", "entity_names": ["hamburger"], "entity_types": ["Dish"]}, {"sentence": "where can i get a hamburger between thompson falls and plains", "entity_names": ["hamburger", "between thompson falls and plains"], "entity_types": ["Dish", "Location"]}, {"sentence": "where can i get a healthy lunch", "entity_names": ["healthy", "lunch"], "entity_types": ["Amenity", "Hours"]}, {"sentence": "where can i get a milkshake within walking distance", "entity_names": ["milkshake", "within walking distance"], "entity_types": ["Dish", "Location"]}, {"sentence": "where can i get a pho king sized bowl", "entity_names": ["pho king sized bowl"], "entity_types": ["Dish"]}, {"sentence": "where can i get a pricey drink on kendall square", "entity_names": ["drink", "kendall square"], "entity_types": ["Dish", "Location"]}, {"sentence": "where can i get a taco for a dollar", "entity_names": ["taco", "for a dollar"], "entity_types": ["Dish", "Price"]}, {"sentence": "where can i get an appitizer in american legion highway", "entity_names": ["appitizer", "american legion highway"], "entity_types": ["Dish", "Location"]}, {"sentence": "where can i get bagels", "entity_names": ["bagels"], "entity_types": ["Dish"]}, {"sentence": "where can i get barbecue no farther than 5 miles", "entity_names": ["barbecue", "5 miles"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "where can i get caribbean food not far from here in a romantic setting", "entity_names": ["caribbean", "not far from here", "romantic setting"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Amenity"]}, {"sentence": "where can i get cheap food at 5 pm in alston", "entity_names": ["cheap", "5 pm", "in alston"], "entity_types": ["Price", "Hours", "Location"]}, {"sentence": "where can i get decent inexpensive sushi in glen cove ny", "entity_names": ["inexpensive", "sushi", "glen cove ny"], "entity_types": ["Price", "Dish", "Location"]}, {"sentence": "where can i get doughnuts right now", "entity_names": ["doughnuts", "now"], "entity_types": ["Dish", "Hours"]}, {"sentence": "where can i get eggrolls", "entity_names": ["eggrolls"], "entity_types": ["Dish"]}, {"sentence": "where can i get fish", "entity_names": ["fish"], "entity_types": ["Dish"]}, {"sentence": "where can i get large portions of fish with a group of interesting folk", "entity_names": ["large portions", "fish", "group"], "entity_types": ["Amenity", "Dish", "Amenity"]}, {"sentence": "where can i get nachos within 1 mile of me that is open late", "entity_names": ["nachos", "within 1 mile", "open late"], "entity_types": ["Dish", "Location", "Hours"]}, {"sentence": "where can i get reasonably priced sashimi at 8 pm", "entity_names": ["reasonably", "sashimi", "8 pm"], "entity_types": ["Price", "Dish", "Hours"]}, {"sentence": "where can i get some chicken nuggets", "entity_names": ["chicken nuggets"], "entity_types": ["Dish"]}, {"sentence": "where can i get some cookies", "entity_names": ["cookies"], "entity_types": ["Dish"]}, {"sentence": "where can i get some crab nearby that is open until 2 am", "entity_names": ["crab", "nearby", "open until 2 am"], "entity_types": ["Dish", "Location", "Hours"]}, {"sentence": "where can i get some fast food", "entity_names": ["fast food"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where can i get some good coffee", "entity_names": ["good", "coffee"], "entity_types": ["Rating", "Dish"]}, {"sentence": "where can i get some ice cream", "entity_names": ["ice cream"], "entity_types": ["Dish"]}, {"sentence": "where can i get some macaroni and cheese", "entity_names": ["macaroni and cheese"], "entity_types": ["Dish"]}, {"sentence": "where can i get some shrimp", "entity_names": ["shrimp"], "entity_types": ["Dish"]}, {"sentence": "where can i get some slushy", "entity_names": ["slushy"], "entity_types": ["Dish"]}, {"sentence": "where can i get some southern food", "entity_names": ["southern"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where can i get some spaghetti", "entity_names": ["spaghetti"], "entity_types": ["Dish"]}, {"sentence": "where can i get some sushi", "entity_names": ["sushi"], "entity_types": ["Dish"]}, {"sentence": "where can i get some waffles", "entity_names": ["waffles"], "entity_types": ["Dish"]}, {"sentence": "where can i get something inexpensive to eat near my current location", "entity_names": ["inexpensive", "near", "current location"], "entity_types": ["Price", "Location", "Location"]}, {"sentence": "where can i get starbucks around me", "entity_names": ["starbucks", "around me"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "where can i get the best pie", "entity_names": ["best", "pie"], "entity_types": ["Rating", "Dish"]}, {"sentence": "where can i get the top rated hamburger in baltimore", "entity_names": ["top rated", "hamburger", "baltimore"], "entity_types": ["Rating", "Dish", "Location"]}, {"sentence": "where can i get vegan food", "entity_names": ["vegan"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where can i go for expensive gelato for carry out", "entity_names": ["expensive", "gelato", "carry out"], "entity_types": ["Price", "Dish", "Amenity"]}, {"sentence": "where can i go to eat", "entity_names": [], "entity_types": []}, {"sentence": "where can i go to get a sandwich around here", "entity_names": ["sandwich", "around here"], "entity_types": ["Dish", "Location"]}, {"sentence": "where can i have a glass of wine in jamaica plain that also does not allow smoking", "entity_names": ["glass", "wine", "jamaica plain", "does not allow smoking"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location", "Amenity"]}, {"sentence": "where can i order fried chicken to eat", "entity_names": ["fried chicken"], "entity_types": ["Dish"]}, {"sentence": "where can i take a date nearby for enchiladas", "entity_names": ["date", "nearby", "enchiladas"], "entity_types": ["Amenity", "Location", "Dish"]}, {"sentence": "where can i take coworkers for excellent priced food", "entity_names": ["coworkers", "excellent"], "entity_types": ["Amenity", "Price"]}, {"sentence": "where can i take my 5 year old nephew to eat", "entity_names": ["take my 5 year old"], "entity_types": ["Amenity"]}, {"sentence": "where can i take my date for a cheap meal in charlestown", "entity_names": ["date", "cheap", "charlestown"], "entity_types": ["Amenity", "Price", "Location"]}, {"sentence": "where can i take my kids for good tacos", "entity_names": ["kids", "good", "tacos"], "entity_types": ["Amenity", "Rating", "Dish"]}, {"sentence": "where can i take my kids to eat", "entity_names": ["kids"], "entity_types": ["Amenity"]}, {"sentence": "where can i take my kids to get an omelet", "entity_names": ["kids", "omelet"], "entity_types": ["Amenity", "Dish"]}, {"sentence": "where can we find a french inspired restaurant", "entity_names": ["french inspired"], "entity_types": ["Amenity"]}, {"sentence": "where could i eat sushi on the waterfront", "entity_names": ["sushi", "waterfront"], "entity_types": ["Dish", "Location"]}, {"sentence": "where could i find some good tomato sauce thats 10 minutes away with excellent pricing", "entity_names": ["good", "tomato sauce", "10 minutes away", "excellent"], "entity_types": ["Rating", "Dish", "Location", "Price"]}, {"sentence": "where could i get some cakes in a business atmosphere thats open until 1 am", "entity_names": ["cakes", "business atmosphere", "open until 1 am"], "entity_types": ["Dish", "Amenity", "Hours"]}, {"sentence": "where could i go for a burger and a movie", "entity_names": ["burger", "movie"], "entity_types": ["Dish", "Amenity"]}, {"sentence": "where could i go for a roast beef sandwich right now", "entity_names": ["roast beef sandwich", "right now"], "entity_types": ["Dish", "Hours"]}, {"sentence": "where do i park to go to the butcher and the boar", "entity_names": ["butcher and the boar"], "entity_types": ["Restaurant Name"]}, {"sentence": "where i can get seafood and bring my own drinks", "entity_names": ["seafood", "bring my own drinks"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "where in beverly can i bring a group to an italys little kitchen", "entity_names": ["beverly", "italys little kitchen"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "where in the theater district is there a restaurant with portions that are a bit small", "entity_names": ["theater district"], "entity_types": ["Location"]}, {"sentence": "where is a bob evans on diauto drive with moderate portions", "entity_names": ["bob evans", "diauto drive", "moderate portions"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "where is a cheap restaurant in the north end that serves burritos", "entity_names": ["cheap", "north end", "burritos"], "entity_types": ["Price", "Location", "Dish"]}, {"sentence": "where is a good place to eat", "entity_names": ["good"], "entity_types": ["Rating"]}, {"sentence": "where is a good place to get seafood", "entity_names": ["good", "seafood"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where is a good restaurant on the water", "entity_names": ["good", "water"], "entity_types": ["Rating", "Location"]}, {"sentence": "where is a kid friendly pizza place with games", "entity_names": ["kid friendly", "pizza", "games"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "where is a kosher place for lunch", "entity_names": ["kosher", "for lunch"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Hours"]}, {"sentence": "where is a late night ethiopian place that we can go after 2 am", "entity_names": ["late night", "ethiopian", "after 2 am"], "entity_types": ["Hours", "<PERSON><PERSON><PERSON><PERSON>", "Hours"]}, {"sentence": "where is a reasonably price japanese restaurant downtown", "entity_names": ["reasonably", "japanese", "downtown"], "entity_types": ["Price", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "where is a restaurant open till midnight near west bridgewater with really friendly service", "entity_names": ["open till midnight", "near west bridgewater", "really friendly service"], "entity_types": ["Hours", "Location", "Amenity"]}, {"sentence": "where is a restaurant that opens 24 hours", "entity_names": ["opens 24 hours"], "entity_types": ["Hours"]}, {"sentence": "where is a romantic place to get a sub near here", "entity_names": ["romantic place", "sub", "near"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "where is an italian restaurant with outdoor seating", "entity_names": ["italian", "outdoor seating"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "where is an italos bakery along this route that has waterfront dining", "entity_names": ["italos bakery", "along this route", "waterfront dining"], "entity_types": ["Restaurant Name", "Location", "Amenity"]}, {"sentence": "where is charlotte nc", "entity_names": ["charlotte nc"], "entity_types": ["Location"]}, {"sentence": "where is dominos", "entity_names": ["dominos"], "entity_types": ["Restaurant Name"]}, {"sentence": "where is dominos pizza", "entity_names": ["dominos pizza"], "entity_types": ["Restaurant Name"]}, {"sentence": "where is good ethnic food", "entity_names": ["ethnic"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where is ii moro", "entity_names": ["ii moro"], "entity_types": ["Restaurant Name"]}, {"sentence": "where is jack in the box", "entity_names": ["jack in the box"], "entity_types": ["Restaurant Name"]}, {"sentence": "where is long john silvers", "entity_names": ["long john silvers"], "entity_types": ["Restaurant Name"]}, {"sentence": "where is p f changs", "entity_names": ["p f changs"], "entity_types": ["Restaurant Name"]}, {"sentence": "where is pizza express", "entity_names": ["pizza express"], "entity_types": ["Restaurant Name"]}, {"sentence": "where is roosevelts restaurant and sin in framingham", "entity_names": ["roosevelts restaurant and sin", "framingham"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "where is some good outdoor dining", "entity_names": ["good", "outdoor dining"], "entity_types": ["Rating", "Amenity"]}, {"sentence": "where is sportsway bar at melrose", "entity_names": ["sportsway bar", "melrose"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "where is station donuts", "entity_names": ["station donuts"], "entity_types": ["Restaurant Name"]}, {"sentence": "where is subway", "entity_names": ["subway"], "entity_types": ["Restaurant Name"]}, {"sentence": "where is that restaurant where you can order burgers and fries and watch a movie while you eat", "entity_names": ["burgers", "fries", "watch a movie"], "entity_types": ["Dish", "Dish", "Amenity"]}, {"sentence": "where is the apple store in the area", "entity_names": ["apple store", "in the area"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "where is the best italian food in the city", "entity_names": ["best", "italian", "in the city"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "where is the best korean bbq", "entity_names": ["best", "korean bbq"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where is the best mediterranean restaurant", "entity_names": ["best", "mediterranean"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where is the best rated fast food establishment", "entity_names": ["best rated", "fast food"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where is the best reviewed place to get a burger and fries near beacon hill", "entity_names": ["best reviewed", "burger and fries", "near beacon hill"], "entity_types": ["Rating", "Dish", "Location"]}, {"sentence": "where is the cheapest place to eat nearest to me", "entity_names": ["cheapest", "nearest"], "entity_types": ["Price", "Location"]}, {"sentence": "where is the closest 5 star restaurant", "entity_names": ["closest", "5 star"], "entity_types": ["Location", "Rating"]}, {"sentence": "where is the closest apple bees", "entity_names": ["closest", "apple bees"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "where is the closest chillis to my current location", "entity_names": ["closest", "chillis", "my current location"], "entity_types": ["Location", "Restaurant Name", "Location"]}, {"sentence": "where is the closest fine dining restaurant", "entity_names": ["closest", "fine dining"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where is the closest happy hour", "entity_names": ["closest", "happy hour"], "entity_types": ["Location", "Amenity"]}, {"sentence": "where is the closest international brownie", "entity_names": ["closest", "international brownie"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "where is the closest jimmie johns", "entity_names": ["closest", "jimmie johns"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "where is the closest non smoking restaurant", "entity_names": ["closest", "non smoking"], "entity_types": ["Location", "Amenity"]}, {"sentence": "where is the closest pizza hut", "entity_names": ["closest", "pizza hut"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "where is the closest place to get cheap potatoes", "entity_names": ["closest", "cheap", "potatoes"], "entity_types": ["Location", "Price", "Dish"]}, {"sentence": "where is the closest place to get pizza", "entity_names": ["closest", "pizza"], "entity_types": ["Location", "Dish"]}, {"sentence": "where is the closest red robin", "entity_names": ["closest", "red robin"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "where is the closest restaurant that serves chinese food", "entity_names": ["closest", "chinese"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where is the closest sub or sandwich shop that serves gyros", "entity_names": ["closest", "sub", "sandwich", "gyros"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Dish"]}, {"sentence": "where is the closest sushi bars to my zip code", "entity_names": ["closest", "sushi bars", "my zip code"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "where is the closest tgi fridays", "entity_names": ["closest", "tgi fridays"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "where is the closest tim hortons restaurant", "entity_names": ["closest", "tim hortons restaurant"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "where is the different locations of taco bell", "entity_names": ["taco bell"], "entity_types": ["Restaurant Name"]}, {"sentence": "where is the nearest 4 star restaurant", "entity_names": ["nearest", "4 star"], "entity_types": ["Location", "Rating"]}, {"sentence": "where is the nearest asia express restaurant", "entity_names": ["nearest", "asia express restaurant"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "where is the nearest bar with dinning located", "entity_names": ["nearest", "bar", "dinning"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "where is the nearest buffet", "entity_names": ["nearest", "buffet"], "entity_types": ["Location", "Amenity"]}, {"sentence": "where is the nearest burger place", "entity_names": ["nearest", "burger"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where is the nearest casual restaurant", "entity_names": ["nearest", "casual"], "entity_types": ["Location", "Amenity"]}, {"sentence": "where is the nearest chinese restaurant with more than 3 stars that is under 10 an entree", "entity_names": ["nearest", "chinese", "more than 3 stars", "under 10"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "Rating", "Price"]}, {"sentence": "where is the nearest family restaurant", "entity_names": ["nearest", "family"], "entity_types": ["Location", "Amenity"]}, {"sentence": "where is the nearest ice cream shop", "entity_names": ["nearest", "ice cream"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where is the nearest kosher restaurant", "entity_names": ["nearest", "kosher"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where is the nearest mexican restaurant open late for dinner", "entity_names": ["nearest", "mexican", "open late", "dinner"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "Hours", "Hours"]}, {"sentence": "where is the nearest napoli pizzeria", "entity_names": ["nearest", "napoli pizzeria"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "where is the nearest place i can get a smoothie", "entity_names": ["nearest", "smoothie"], "entity_types": ["Location", "Dish"]}, {"sentence": "where is the nearest place that serves nachos", "entity_names": ["nearest", "nachos"], "entity_types": ["Location", "Dish"]}, {"sentence": "where is the nearest place with a dining patio", "entity_names": ["nearest", "dining patio"], "entity_types": ["Location", "Amenity"]}, {"sentence": "where is the nearest place within 5 miles that has a game room for kids", "entity_names": ["nearest", "within 5 miles", "game room for kids"], "entity_types": ["Location", "Location", "Amenity"]}, {"sentence": "where is the nearest restaurant that allows indoor smoking", "entity_names": ["nearest", "indoor smoking"], "entity_types": ["Location", "Amenity"]}, {"sentence": "where is the nearest restaurant that serves hush puppies", "entity_names": ["nearest", "hush puppies"], "entity_types": ["Location", "Dish"]}, {"sentence": "where is the nearest restaurant that serves steak", "entity_names": ["nearest", "steak"], "entity_types": ["Location", "Dish"]}, {"sentence": "where is the nearest restaurant with a casual kid friendly atmosphere", "entity_names": ["nearest", "casual kid friendly"], "entity_types": ["Location", "Amenity"]}, {"sentence": "where is the nearest shell gas station", "entity_names": ["nearest"], "entity_types": ["Location"]}, {"sentence": "where is the nearest sushi bar", "entity_names": ["nearest", "sushi bar"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where is the nearest take out chinese restaurant", "entity_names": ["nearest", "take out", "chinese"], "entity_types": ["Location", "Amenity", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where is the nearest tapas restaurant", "entity_names": ["nearest", "tapas"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where is the nearest thai restaurant that serves breakfast in alston", "entity_names": ["nearest", "thai", "<PERSON>ston"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "where is the nearest tomacchios with patio seating located", "entity_names": ["nearest", "<PERSON><PERSON><PERSON><PERSON>", "patio seating"], "entity_types": ["Location", "Restaurant Name", "Amenity"]}, {"sentence": "where is the nearest uptown espresso", "entity_names": ["nearest uptown"], "entity_types": ["Location"]}, {"sentence": "where is the next mcdonald", "entity_names": ["next", "mc<PERSON><PERSON>"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "where is the place with byob 2 miles from here", "entity_names": ["byob", "2 miles from here"], "entity_types": ["Amenity", "Location"]}, {"sentence": "where is the reading station coffee depot on norfolk ave do they serve breakfast", "entity_names": ["reading station coffee depot", "norfolk ave", "breakfast"], "entity_types": ["Restaurant Name", "Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where is the sportsway bar in melrose", "entity_names": ["sportsway bar", "melrose"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "where is there a fatz with interesting people around", "entity_names": ["fatz", "interesting people"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "where is there a good mid priced restaurant near 4 th street that serves appetizers", "entity_names": ["good", "mid", "near 4 th street", "appetizers"], "entity_types": ["Rating", "Price", "Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "where is there a great steak near here that has a buffet", "entity_names": ["great", "steak", "near here", "buffet"], "entity_types": ["Rating", "Dish", "Location", "Amenity"]}, {"sentence": "where is wing stop", "entity_names": ["wing stop"], "entity_types": ["Restaurant Name"]}, {"sentence": "where should we go to get some great food", "entity_names": ["great"], "entity_types": ["Rating"]}, {"sentence": "where there a restaurant located within 1 mile from here", "entity_names": ["within 1 mile from here"], "entity_types": ["Location"]}, {"sentence": "wheres a good place to get tacos near central park", "entity_names": ["tacos", "near central park"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "wheres a kid friendly diner thats within an hour of here", "entity_names": ["kid friendly", "diner", "within", "hour of here"], "entity_types": ["Amenity", "<PERSON><PERSON><PERSON><PERSON>", "Location", "Location"]}, {"sentence": "wheres closest mcdonalds", "entity_names": ["closest", "mc<PERSON><PERSON><PERSON>"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "wheres papa johns", "entity_names": ["papa johns"], "entity_types": ["Restaurant Name"]}, {"sentence": "wheres the best korean restaurant in the area", "entity_names": ["best", "korean", "in the area"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "wheres the closes chicken place", "entity_names": ["closes", "chicken"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "wheres the closest 5 star restaurant located", "entity_names": ["closest", "5 star"], "entity_types": ["Location", "Rating"]}, {"sentence": "wheres the closest bar", "entity_names": ["closest", "bar"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "wheres the closest pizza place", "entity_names": ["closest", "pizza"], "entity_types": ["Location", "Dish"]}, {"sentence": "wheres the italian restaurant downtown thats open until midnight", "entity_names": ["italian", "downtown", "open until midnight"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location", "Hours"]}, {"sentence": "wheres the local el penllo arriba peru", "entity_names": ["local", "el penllo arriba peru"], "entity_types": ["Location", "Restaurant Name"]}, {"sentence": "wheres the local soup and sandwich joint", "entity_names": ["local", "soup and sandwich"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "wheres the nearest french place to eat", "entity_names": ["nearest", "french"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "wheres the nearest italian restaurant", "entity_names": ["nearest", "italian"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "wheres the nearest pizza restaurant to the amc white flint movie theater", "entity_names": ["nearest", "pizza", "amc white flint movie theater"], "entity_types": ["Location", "<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "wheres the nearest restaurant", "entity_names": ["nearest"], "entity_types": ["Location"]}, {"sentence": "wheres the nearest restaurant with a 3 star rating or better that serves a hamburger for under five dollars", "entity_names": ["nearest", "3 star rating or better", "hamburger", "under five dollars"], "entity_types": ["Location", "Rating", "Dish", "Price"]}, {"sentence": "wheres the nearest restaurant with a childrens menu", "entity_names": ["nearest", "childrens menu"], "entity_types": ["Location", "Amenity"]}, {"sentence": "wheres the serborn inn located", "entity_names": ["serborn inn"], "entity_types": ["Restaurant Name"]}, {"sentence": "which authentic mexican restaurants offer complementary chips and salsa", "entity_names": ["authentic mexican", "complementary chips and salsa"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Dish"]}, {"sentence": "which five star italian restaurants in manattan have the best reviews", "entity_names": ["five star", "italian", "<PERSON><PERSON>an", "best reviews"], "entity_types": ["Rating", "<PERSON><PERSON><PERSON><PERSON>", "Location", "Rating"]}, {"sentence": "which local restaurant serves only seafood", "entity_names": ["local", "only", "seafood"], "entity_types": ["Location", "Amenity", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "which nearby restaurant serves the best steak", "entity_names": ["nearby", "best", "steak"], "entity_types": ["Location", "Rating", "Dish"]}, {"sentence": "which parks are kid friendly", "entity_names": ["kid friendly"], "entity_types": ["Amenity"]}, {"sentence": "which pizza places will deliver to me", "entity_names": ["pizza", "deliver"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Amenity"]}, {"sentence": "which places serve large portions", "entity_names": ["large portions"], "entity_types": ["Amenity"]}, {"sentence": "which restaurant can i get to within 5 minutes which serves healthy portions", "entity_names": ["within 5 minutes", "healthy portions"], "entity_types": ["Location", "Amenity"]}, {"sentence": "which restaurant has a better rating on yelp sakura or fuji ya", "entity_names": ["better rating", "sakura", "fuji ya"], "entity_types": ["Rating", "Restaurant Name", "Restaurant Name"]}, {"sentence": "which restaurant has a smoking section", "entity_names": ["smoking section"], "entity_types": ["Amenity"]}, {"sentence": "which restaurant has fried pickles for appetizers", "entity_names": ["fried pickles", "appetizers"], "entity_types": ["Dish", "Amenity"]}, {"sentence": "which restaurant in my city has the highest reviews", "entity_names": ["my city", "highest reviews"], "entity_types": ["Location", "Rating"]}, {"sentence": "which restaurant is open late and has good prices on espressos", "entity_names": ["open late", "good", "espressos"], "entity_types": ["Hours", "Price", "Dish"]}, {"sentence": "which restaurants are considered vegetarian in my city", "entity_names": ["vegetarian", "in my city"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "which restaurants are open after midnight on a wednesday", "entity_names": ["open after midnight on a wednesday"], "entity_types": ["Hours"]}, {"sentence": "which restaurants around my city have the best deserts", "entity_names": ["around my city", "best", "deserts"], "entity_types": ["Location", "Rating", "<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "which restaurants have playgrounds in them", "entity_names": ["playgrounds"], "entity_types": ["Amenity"]}, {"sentence": "which restaurants near here are open till midnight", "entity_names": ["near here", "open till midnight"], "entity_types": ["Location", "Hours"]}, {"sentence": "which restaurants offer outdoor dining", "entity_names": ["outdoor dining"], "entity_types": ["Amenity"]}, {"sentence": "which restaurants serving grass fed beef steaks have the best prices", "entity_names": ["grass fed beef steaks", "best"], "entity_types": ["Dish", "Price"]}, {"sentence": "which tim hortons is closest to detroit", "entity_names": ["tim hortons", "closest to detroit"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "whixh restaurant has delivery in washington court for a reasonable price", "entity_names": ["delivery", "washington court", "reasonable"], "entity_types": ["Amenity", "Location", "Price"]}, {"sentence": "who has good burgers around here", "entity_names": ["good", "burgers", "around here"], "entity_types": ["Rating", "Dish", "Location"]}, {"sentence": "who has happy hour right now", "entity_names": ["happy hour", "right now"], "entity_types": ["Amenity", "Hours"]}, {"sentence": "who has the best lobster in town", "entity_names": ["best", "lobster", "in town"], "entity_types": ["Rating", "Dish", "Location"]}, {"sentence": "who has the best pizza that takes credit cards", "entity_names": ["best", "pizza", "credit cards"], "entity_types": ["Rating", "Dish", "Amenity"]}, {"sentence": "who has the best selection of micro brew and imported beers", "entity_names": ["best selection", "micro brew", "imported beers"], "entity_types": ["Rating", "Dish", "Dish"]}, {"sentence": "who makes the absolute best sausage", "entity_names": ["absolute best", "sausage"], "entity_types": ["Rating", "Dish"]}, {"sentence": "who makes the best burgers in town", "entity_names": ["best", "burgers", "in town"], "entity_types": ["Rating", "Dish", "Location"]}, {"sentence": "who makes the best chicken wings in town", "entity_names": ["best", "chicken wings", "in town"], "entity_types": ["Rating", "Dish", "Location"]}, {"sentence": "who serves cali food", "entity_names": ["cali"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"sentence": "who serves the best wings in this town", "entity_names": ["who serves", "best", "wings", "in this town"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Dish", "Location"]}, {"sentence": "who will give me the best priced party subs in town", "entity_names": ["best", "party subs", "town"], "entity_types": ["Rating", "Dish", "Location"]}, {"sentence": "will i be able to find a romantic restaurant for my date tonight", "entity_names": ["romantic", "tonight"], "entity_types": ["Amenity", "Hours"]}, {"sentence": "will waffle house accept a prepaid visa gift card", "entity_names": ["waffle house", "prepaid visa gift card"], "entity_types": ["Restaurant Name", "Amenity"]}, {"sentence": "yes please get me mc<PERSON><PERSON>s phone number in patchogue new york", "entity_names": ["mc<PERSON><PERSON><PERSON>", "patchogue new york"], "entity_types": ["Restaurant Name", "Location"]}, {"sentence": "yes the new diner on south street please", "entity_names": ["diner", "south street"], "entity_types": ["<PERSON><PERSON><PERSON><PERSON>", "Location"]}, {"sentence": "yes we need some chicken for our new diet so chik fa lay it is", "entity_names": ["chicken", "chik fa lay"], "entity_types": ["Dish", "Restaurant Name"]}, {"sentence": "you can help me with some onion rings", "entity_names": ["onion rings"], "entity_types": ["Dish"]}]}