{"prompts": ["Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Director.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type director\n- (B). The span contains a named director entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not director\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named director entity must be the name of a film director. Only first names, last names, and full names of directors are considered named director entities.\nAmbiguous identifiers such as \"director\", \"female filmmaker\" and \"female directors\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"who {{directed}} the movie Inception\"\nText Span: \"directed\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Are there any movies directed by {{<PERSON>}} that I should watch?\"\nText Span: \"Steven <PERSON>lberg\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What historical film did an indie director, like {{<PERSON><PERSON>}}, direct?\"\nText Span: \"Tarantino\"\n\n2. Query: \"Could you recommend a movie with unforgettable performances by {{<PERSON> Donen}}?\"\nText Span: \"<PERSON> Donen\"\n\n3. Query: \"Show me the trailer for the movie featuring a mercenary mission with Salma Hayek and directed by {{Darren Aronofsky}}.\"\nText Span: \"Darren Aronofsky\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Director.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type director\n- (B). The span contains a named director entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not director\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named director entity must be the name of a film director. Only first names, last names, and full names of directors are considered named director entities.\nAmbiguous identifiers such as \"director\", \"female filmmaker\" and \"female directors\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Are there any movies directed by {{<PERSON>}} that I should watch?\"\nText Span: \"<PERSON>\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"who {{directed}} the movie Inception\"\nText Span: \"directed\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the best movie directed by {{<PERSON> <PERSON>}}?\"\nText Span: \"<PERSON> <PERSON>\"\n\n2. Query: \"I'm looking for a movie where <PERSON> <PERSON> plays the lead role. Who is the {{director}} of that film?\"\nText Span: \"director\"\n\n3. Query: \"What is the plot of the movie about the Zombie apocalypse featuring {{<PERSON> <PERSON>}} and Margot <PERSON>?\"\nText Span: \"<PERSON> Kent\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Director.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type director\n- (B). The span contains a named director entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not director\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named director entity must be the name of a film director. Only first names, last names, and full names of directors are considered named director entities.\nAmbiguous identifiers such as \"director\", \"female filmmaker\" and \"female directors\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Are there any movies directed by {{<PERSON>}} that I should watch?\"\nText Span: \"<PERSON>\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"who {{directed}} the movie Inception\"\nText Span: \"directed\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the plot of The Wizard of Oz and who {{directed}} it?\"\nText Span: \"directed\"\n\n2. Query: \"Who {{directed}} the movie from 1987 with <PERSON> Pfeiffer and the song Don't Stop Believin'?\"\nText Span: \"directed\"\n\n3. Query: \"Can you show me the trailer for the latest film by {{<PERSON> Nolan}}?\"\nText Span: \"<PERSON> Nolan\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Director.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type director\n- (B). The span contains a named director entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not director\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named director entity must be the name of a film director. Only first names, last names, and full names of directors are considered named director entities.\nAmbiguous identifiers such as \"director\", \"female filmmaker\" and \"female directors\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"who {{directed}} the movie Inception\"\nText Span: \"directed\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Are there any movies directed by {{<PERSON>}} that I should watch?\"\nText Span: \"Steven <PERSON>lberg\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you show me the trailer for <PERSON> <PERSON>, directed by {{<PERSON> <PERSON><PERSON>}}?\"\nText <PERSON>n: \"<PERSON> <PERSON><PERSON>\"\n\n2. Query: \"Who are the main actors in the film Charade, directed by {{<PERSON> Donen}}?\"\nText Span: \"<PERSON> Donen\"\n\n3. Query: \"Who is the director of the cinematic Wonder Woman movie featuring {{<PERSON> <PERSON>}}\"\nText Span: \"<PERSON> <PERSON>\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Director.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type director\n- (B). The span contains a named director entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not director\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named director entity must be the name of a film director. Only first names, last names, and full names of directors are considered named director entities.\nAmbiguous identifiers such as \"director\", \"female filmmaker\" and \"female directors\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Are there any movies directed by {{<PERSON>}} that I should watch?\"\nText Span: \"<PERSON>\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"who {{directed}} the movie Inception\"\nText Span: \"directed\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the viewers' rating for the movie directed by {{<PERSON> <PERSON>}}?\"\nText Span: \"Chris Evans\"\n\n2. Query: \"Who is the main actor in the movie House of Flying Daggers by {{Zhang Yimou}}\"\nText Span: \"Zhang Yimou\"\n\n3. Query: \"Can you play the announcement trailer for the new Marvel movie directed by {{Chloé Zhao}}\"\nText Span: \"Chloé Zhao\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Director.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type director\n- (B). The span contains a named director entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not director\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named director entity must be the name of a film director. Only first names, last names, and full names of directors are considered named director entities.\nAmbiguous identifiers such as \"director\", \"female filmmaker\" and \"female directors\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"who {{directed}} the movie Inception\"\nText Span: \"directed\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Are there any movies directed by {{<PERSON>}} that I should watch?\"\nText Span: \"Steven <PERSON>lberg\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you provide me with the trailer for the 1994 movie directed by {{<PERSON> Be<PERSON>}}?\"\nText Span: \"Luc Be<PERSON>\"\n\n2. Query: \"What is the genre of the 1973 movie directed by {{<PERSON> <PERSON>}} called The <PERSON> <PERSON>r?\"\nText Span: \"<PERSON> <PERSON>\"\n\n3. Query: \"Show me the trailer for the new action movie directed by {{<PERSON> Bigelow}}\"\nText Span: \"<PERSON> <PERSON>elow\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Director.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type director\n- (B). The span contains a named director entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not director\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named director entity must be the name of a film director. Only first names, last names, and full names of directors are considered named director entities.\nAmbiguous identifiers such as \"director\", \"female filmmaker\" and \"female directors\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"who {{directed}} the movie Inception\"\nText Span: \"directed\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Are there any movies directed by {{<PERSON>}} that I should watch?\"\nText Span: \"Steven Spielberg\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a movie with {{<PERSON> Spielberg}} as the director and a strong female character?\"\nText Span: \"Steven Spielberg\"\n\n2. Query: \"what is the viewers' rating for the romantic comedy directed by {{nora ephron}} and released in 1993\"\nText Span: \"nora ephron\"\n\n3. Query: \"Who {{directed}} the film that features the character Jack Dawson?\"\nText Span: \"directed\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Director.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type director\n- (B). The span contains a named director entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not director\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named director entity must be the name of a film director. Only first names, last names, and full names of directors are considered named director entities.\nAmbiguous identifiers such as \"director\", \"female filmmaker\" and \"female directors\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"who {{directed}} the movie Inception\"\nText Span: \"directed\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Are there any movies directed by {{<PERSON>}} that I should watch?\"\nText Span: \"Steven S<PERSON>lberg\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Que<PERSON>: \"What is the top film directed by {{<PERSON> S<PERSON>lberg}} that is suitable for all ages, such as TV-G?\"\nText Span: \"<PERSON> Spielberg\"\n\n2. Query: \"What reflective movie directed by {{<PERSON> Nolan}} has the song Time by Hans Z<PERSON>?\"\nText <PERSON>n: \"<PERSON> <PERSON>\"\n\n3. Query: \"what are the most popular films directed by {{<PERSON> <PERSON><PERSON>}}\"\nText <PERSON>n: \"<PERSON> <PERSON><PERSON>\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Director.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type director\n- (B). The span contains a named director entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not director\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named director entity must be the name of a film director. Only first names, last names, and full names of directors are considered named director entities.\nAmbiguous identifiers such as \"director\", \"female filmmaker\" and \"female directors\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Are there any movies directed by {{<PERSON>}} that I should watch?\"\nText Span: \"<PERSON>\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"who {{directed}} the movie Inception\"\nText Span: \"directed\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What's the viewers' rating for the latest {{<PERSON> <PERSON><PERSON>}} film\"\nText Span: \"<PERSON> Tara<PERSON>\"\n\n2. Query: \"Who {{directed}} the adventure film with D<PERSON> <PERSON> and what year was it released?\"\nText Span: \"directed\"\n\n3. Query: \"What is the most recent film directed by {{<PERSON> <PERSON>}}\"\nText Span: \"<PERSON> <PERSON>\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Director.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type director\n- (B). The span contains a named director entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not director\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named director entity must be the name of a film director. Only first names, last names, and full names of directors are considered named director entities.\nAmbiguous identifiers such as \"director\", \"female filmmaker\" and \"female directors\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Are there any movies directed by {{<PERSON>}} that I should watch?\"\nText Span: \"<PERSON>\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"who {{directed}} the movie Inception\"\nText Span: \"directed\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Show me the trailer for the movie directed by {{Jane Campion}} that has a song by Nirvana.\"\nText Span: \"Jane Campion\"\n\n2. Query: \"What is the genre of the movie {{E}} directed?\"\nText Span: \"E\"\n\n3. Query: \"Can you recommend a family-friendly movie directed by {{Gus Van Sant}}, rated TV-PG?\"\nText Span: \"Gus Van Sant\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Director.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type director\n- (B). The span contains a named director entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not director\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named director entity must be the name of a film director. Only first names, last names, and full names of directors are considered named director entities.\nAmbiguous identifiers such as \"director\", \"female filmmaker\" and \"female directors\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"who {{directed}} the movie Inception\"\nText Span: \"directed\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Are there any movies directed by {{<PERSON>}} that I should watch?\"\nText Span: \"Steven <PERSON>lberg\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a family-friendly movie rated PG directed by {{<PERSON> Daldry}}\"\nText Span: \"Stephen Daldry\"\n\n2. Query: \"Show me a trailer for the latest {{Wong Kar-wai}} film.\"\nText Span: \"Wong Kar-wai\"\n\n3. Query: \"What is a good mockbuster movie directed by {{Clint Eastwood}}?\"\nText Span: \"Clint Eastwood\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Director.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type director\n- (B). The span contains a named director entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not director\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named director entity must be the name of a film director. Only first names, last names, and full names of directors are considered named director entities.\nAmbiguous identifiers such as \"director\", \"female filmmaker\" and \"female directors\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"who {{directed}} the movie Inception\"\nText Span: \"directed\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Are there any movies directed by {{<PERSON>}} that I should watch?\"\nText Span: \"Steven S<PERSON>lberg\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a popular title directed by {{Steven Spielberg}} in the 80s?\"\nText Span: \"Steven Spielberg\"\n\n2. Query: \"Show me a trailer for a 1950s film noir directed by {{Alfred Hitchcock}}.\"\nText Span: \"Alfred Hitchcock\"\n\n3. Query: \"What year did {{Bong Joon-ho}} direct a film with a strong female lead and a gripping plot?\"\nText Span: \"Bong Joon-ho\""]}