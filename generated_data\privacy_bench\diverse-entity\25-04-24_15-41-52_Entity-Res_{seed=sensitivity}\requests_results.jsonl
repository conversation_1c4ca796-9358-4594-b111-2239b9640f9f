[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为姓名的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的姓名类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 392}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n张伟  \n李娜  \n王芳  \n刘强  \n陈明  \n杨丽  \n赵洋  \n周静  \n吴刚  \n郑华  \n孙悦  \n马超  \n朱琳  \n胡明  \n郭静", "role": "assistant"}}], "created": 1745480513, "id": "202504241541529925d6b4a1c249da", "model": "glm-4-air-250414", "request_id": "202504241541529925d6b4a1c249da", "usage": {"completion_tokens": 46, "prompt_tokens": 52, "total_tokens": 98}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为国籍的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的国籍类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 63}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Chinese  \n2. American  \n3. British  \n4. French  \n5. German  \n6. Japanese  \n7. Indian  \n8. Brazilian  \n9. Russian  \n10. Canadian  \n11. Australian  \n12. Italian  \n13. Spanish  \n14. Mexican  \n15. South Korean", "role": "assistant"}}], "created": 1745480513, "id": "202504241541510650eb9582f84862", "model": "glm-4-air-250414", "request_id": "202504241541510650eb9582f84862", "usage": {"completion_tokens": 62, "prompt_tokens": 52, "total_tokens": 114}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成10个多样化的、可以归类为政治倾向的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的政治倾向类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 455}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 民主先锋  \n2. 自由之声  \n3. 绿色未来党  \n4. 和平联盟  \n5. 进步力量  \n6. 民主团结  \n7. 自由灯塔  \n8. 和平卫士  \n9. 绿色行动  \n10. 进步先锋", "role": "assistant"}}], "created": 1745480513, "id": "202504241541514d81d3aa27c846ef", "model": "glm-4-air-250414", "request_id": "202504241541514d81d3aa27c846ef", "usage": {"completion_tokens": 66, "prompt_tokens": 54, "total_tokens": 120}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为国籍的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的国籍类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 238}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. American  \n2. Chinese  \n3. British  \n4. French  \n5. German  \n6. Japanese  \n7. Indian  \n8. Brazilian  \n9. Canadian  \n10. Australian  \n11. Russian  \n12. Mexican  \n13. Italian  \n14. Spanish  \n15. South Korean", "role": "assistant"}}], "created": 1745480513, "id": "20250424154152d121aa353c61436f", "model": "glm-4-air-250414", "request_id": "20250424154152d121aa353c61436f", "usage": {"completion_tokens": 62, "prompt_tokens": 52, "total_tokens": 114}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成10个多样化的、可以归类为婚姻状况的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的婚姻状况类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 273}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Married with children  \n2. Divorced without dependents  \n3. Widowed and remarried  \n4. Single parent  \n5. Civil union partner  \n6. Separated pending divorce  \n7. Cohabiting without legal marriage  \n8. Legally separated  \n9. Never married adult  \n10. Domestic partnership registered", "role": "assistant"}}], "created": 1745480513, "id": "20250424154152fde1eca6b4f34bcc", "model": "glm-4-air-250414", "request_id": "20250424154152fde1eca6b4f34bcc", "usage": {"completion_tokens": 69, "prompt_tokens": 54, "total_tokens": 123}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成10个多样化的、可以归类为性别的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的性别类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 56}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n性别标识符（最高敏感级别）：\n\n1. 性别信息\n2. 性别认同\n3. 性别特征\n4. 性别偏好\n5. 性别角色\n6. 性别表达\n7. 性别历史\n8. 性别确认\n9. 性别分类\n10. 性别自我认同", "role": "assistant"}}], "created": 1745480513, "id": "202504241541516c40be21da674ac8", "model": "glm-4-air-250414", "request_id": "202504241541516c40be21da674ac8", "usage": {"completion_tokens": 71, "prompt_tokens": 52, "total_tokens": 123}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为国籍的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的国籍类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 413}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. American  \n2. Chinese  \n3. British  \n4. Canadian  \n5. Australian  \n6. Indian  \n7. German  \n8. French  \n9. Japanese  \n10. Brazilian  \n11. Mexican  \n12. Russian  \n13. Italian  \n14. Spanish  \n15. South African", "role": "assistant"}}], "created": 1745480513, "id": "20250424154152b3d1140f168a43d4", "model": "glm-4-air-250414", "request_id": "20250424154152b3d1140f168a43d4", "usage": {"completion_tokens": 62, "prompt_tokens": 52, "total_tokens": 114}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成10个多样化的、可以归类为政治倾向的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的政治倾向类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 280}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 民主党派联盟  \n2. 共和党核心小组  \n3. 绿色和平倡议  \n4. 工党全国委员会  \n5. 保守派论坛  \n6. 自由党俱乐部  \n7. 进步联盟组织  \n8. 社会主义研究会  \n9. 独立政治团体  \n10. 亲商联盟协会", "role": "assistant"}}], "created": 1745480513, "id": "20250424154151c7a790711c064252", "model": "glm-4-air-250414", "request_id": "20250424154151c7a790711c064252", "usage": {"completion_tokens": 80, "prompt_tokens": 54, "total_tokens": 134}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为种族的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的种族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 252}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. AfricanAmerican\n2. Caucasian\n3. Hispanic\n4. Asian\n5. NativeAmerican\n6. PacificIslander\n7. MiddleEastern\n8. SouthAsian\n9. EastAsian\n10. Multiracial\n11. Caribbean\n12. Latino\n13. Arab\n14. Indigenous\n15. SouthEastAsian", "role": "assistant"}}], "created": 1745480513, "id": "20250424154151232edfa8dcbb43ad", "model": "glm-4-air-250414", "request_id": "20250424154151232edfa8dcbb43ad", "usage": {"completion_tokens": 72, "prompt_tokens": 52, "total_tokens": 124}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成10个多样化的、可以归类为性别的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的性别类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 406}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Mr. Smith  \n2. Miss Johnson  \n3. Dr. Chen  \n4. Prof. Garcia  \n5. Ms. Lee  \n6. Mrs. Patel  \n7. Mr. Kim  \n8. Ms. Martinez  \n9. Dr. Singh  \n10. Prof. Nguyen", "role": "assistant"}}], "created": 1745480513, "id": "20250424154151c18c83d9e50942c9", "model": "glm-4-air-250414", "request_id": "20250424154151c18c83d9e50942c9", "usage": {"completion_tokens": 60, "prompt_tokens": 52, "total_tokens": 112}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为姓名的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的姓名类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 217}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 李明\n2. 张伟\n3. 王芳\n4. 刘强\n5. 陈静\n6. 赵娜\n7. 杨勇\n8. 周丽\n9. 吴刚\n10. 郑洁\n11. 孙涛\n12. 马琳\n13. 朱峰\n14. 胡敏\n15. 林波", "role": "assistant"}}], "created": 1745480513, "id": "20250424154151624820b184fc4f70", "model": "glm-4-air-250414", "request_id": "20250424154151624820b184fc4f70", "usage": {"completion_tokens": 88, "prompt_tokens": 52, "total_tokens": 140}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成10个多样化的、可以归类为婚姻状况的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的婚姻状况类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 98}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 已婚夫妇身份记录  \n2. 未婚登记信息  \n3. 离婚状态数据库  \n4. 丧偶者个人档案  \n5. 同居关系声明  \n6. 单身状态认证  \n7. 分居人员清单  \n8. 再婚历史详情  \n9. 无效婚姻证明  \n10. 民事结合记录", "role": "assistant"}}], "created": 1745480513, "id": "2025042415415150aa166a713147b6", "model": "glm-4-air-250414", "request_id": "2025042415415150aa166a713147b6", "usage": {"completion_tokens": 78, "prompt_tokens": 54, "total_tokens": 132}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成10个多样化的、可以归类为婚姻状况的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的婚姻状况类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 448}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 单身  \n2. 已婚  \n3. 离异  \n4. 丧偶  \n5. 分居  \n6. 恋爱中  \n7. 未婚同居  \n8. 注册伴侣  \n9. 分手状态  \n10. 暂未确定", "role": "assistant"}}], "created": 1745480513, "id": "2025042415415248bc586d070447cc", "model": "glm-4-air-250414", "request_id": "2025042415415248bc586d070447cc", "usage": {"completion_tokens": 62, "prompt_tokens": 54, "total_tokens": 116}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为家庭成员的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的家庭成员类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 462}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 张伟  \n2. 李娜  \n3. 王芳  \n4. 刘洋  \n5. 陈静  \n6. 赵明  \n7. 杨丽  \n8. 周强  \n9. 吴婷  \n10. 郑宇  \n11. 孙悦  \n12. 马超  \n13. 朱琳  \n14. 徐磊  \n15. 高菲", "role": "assistant"}}], "created": 1745480513, "id": "202504241541520637648280404bc5", "model": "glm-4-air-250414", "request_id": "202504241541520637648280404bc5", "usage": {"completion_tokens": 86, "prompt_tokens": 52, "total_tokens": 138}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为家庭成员的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的家庭成员类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 287}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n张明（父亲）  \n李华（母亲）  \n王芳（女儿）  \n刘强（儿子）  \n赵敏（妻子）  \n陈刚（丈夫）  \n杨丽（姐妹）  \n吴涛（兄弟）  \n周静（阿姨）  \n孙宇（叔叔）  \n郑洁（姑姑）  \n钱峰（舅舅）  \n冯丽（表姐）  \n朱强（堂弟）  \n徐燕（外甥女）", "role": "assistant"}}], "created": 1745480513, "id": "20250424154152ac100cc7fdfd4210", "model": "glm-4-air-250414", "request_id": "20250424154152ac100cc7fdfd4210", "usage": {"completion_tokens": 96, "prompt_tokens": 52, "total_tokens": 148}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为临床表现的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的临床表现类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 350}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 心悸  \n2. 胸痛  \n3. 呼吸困难  \n4. 头痛  \n5. 腹痛  \n6. 发热  \n7. 恶心  \n8. 呕吐  \n9. 疲劳  \n10. 关节疼痛  \n11. 皮肤瘙痒  \n12. 视力模糊  \n13. 肌肉无力  \n14. 头晕  \n15. 意识模糊", "role": "assistant"}}], "created": 1745480513, "id": "20250424154152a4acdcfda9ff4f19", "model": "glm-4-air-250414", "request_id": "20250424154152a4acdcfda9ff4f19", "usage": {"completion_tokens": 101, "prompt_tokens": 52, "total_tokens": 153}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为年龄的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的年龄类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 224}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 出生年份\n2. 出生月日\n3. 出生日期\n4. 年龄段\n5. 年龄段分类\n6. 出生年\n7. 出生纪年\n8. 出生时年\n9. 成年年龄\n10. 法定年龄\n11. 年龄范围\n12. 年龄组\n13. 年龄区间\n14. 年龄等级\n15. 出生时月\n16. 出生时日\n17. 出生时\n18. 出生时长\n19. 生命时长\n20. 年岁", "role": "assistant"}}], "created": 1745480513, "id": "20250424154151eaa15633676f41a6", "model": "glm-4-air-250414", "request_id": "20250424154151eaa15633676f41a6", "usage": {"completion_tokens": 109, "prompt_tokens": 52, "total_tokens": 161}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为临床表现的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的临床表现类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 175}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 胸痛  \n2. 高血压  \n3. 糖尿病  \n4. 呼吸困难  \n5. 癫痫发作  \n6. 心肌梗死  \n7. 肝功能衰竭  \n8. 肾功能不全  \n9. 脑卒中  \n10. 恶性肿瘤  \n11. 骨折  \n12. 败血症  \n13. 艾滋病  \n14. 肺炎  \n15. 中毒性休克综合征", "role": "assistant"}}], "created": 1745480513, "id": "2025042415415159563fa124484a5c", "model": "glm-4-air-250414", "request_id": "2025042415415159563fa124484a5c", "usage": {"completion_tokens": 109, "prompt_tokens": 52, "total_tokens": 161}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为疾病的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的疾病类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 161}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 癌症\n2. 糖尿病\n3. 艾滋病\n4. 心脏病\n5. 肺结核\n6. 精神分裂症\n7. 乙肝\n8. 肝硬化\n9. 系统性红斑狼疮\n10. 肾衰竭\n11. 脑卒中\n12. 红斑狼疮\n13. 肺炎\n14. 艾滋病毒感染\n15. 帕金森病", "role": "assistant"}}], "created": 1745480513, "id": "20250424154151b441f9759ee845ba", "model": "glm-4-air-250414", "request_id": "20250424154151b441f9759ee845ba", "usage": {"completion_tokens": 115, "prompt_tokens": 51, "total_tokens": 166}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为家庭成员的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的家庭成员类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 112}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 父亲  \n2. 母亲  \n3. 儿子  \n4. 女儿  \n5. 爷爷  \n6. 奶奶  \n7. 姥爷  \n8. 姥姥  \n9. 丈夫  \n10. 妻子  \n11. 兄弟  \n12. 姐妹  \n13. 儿媳  \n14. 女婿  \n15. 孙子", "role": "assistant"}}], "created": **********, "id": "20250424154152a3f9c9baf67a45dc", "model": "glm-4-air-250414", "request_id": "20250424154152a3f9c9baf67a45dc", "usage": {"completion_tokens": 101, "prompt_tokens": 52, "total_tokens": 153}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为医疗程序的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的医疗程序类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 532}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 牙齿拔除  \n2. 眼科检查  \n3. 皮肤活检  \n4. 常规体检  \n5. 血压测量  \n6. 胸部X光  \n7. 注射疫苗  \n8. 耳鼻喉科检查  \n9. 骨密度测试  \n10. 腹部超声波  \n11. 糖尿病筛查  \n12. 视力矫正  \n13. 心电图测试  \n14. 牙科清洁  \n15. 伤口缝合", "role": "assistant"}}], "created": **********, "id": "20250424154151b8ae9128356f4b3a", "model": "glm-4-air-250414", "request_id": "20250424154151b8ae9128356f4b3a", "usage": {"completion_tokens": 113, "prompt_tokens": 53, "total_tokens": 166}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为生育信息的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的生育信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 196}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 末次月经日期  \n2. 孕周数  \n3. 流产史  \n4. 人工流产记录  \n5. 剖宫产史  \n6. 辅助生殖技术使用情况  \n7. 子宫肌瘤诊断  \n8. 卵巢囊肿记录  \n9. 子宫内膜异位症确诊  \n10. 生育年龄  \n11. 计划生育手术史  \n12. 哺乳期状态  \n13. 女性激素水平检测值  \n14. 精液分析报告  \n15. 精子库捐赠记录", "role": "assistant"}}], "created": **********, "id": "20250424154151bab4430d77da4bf4", "model": "glm-4-air-250414", "request_id": "20250424154151bab4430d77da4bf4", "usage": {"completion_tokens": 127, "prompt_tokens": 53, "total_tokens": 180}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为种族的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的种族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 427}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Caucasian\n2. African American\n3. Asian\n4. Hispanic\n5. Native American\n6. Pacific Islander\n7. Middle Eastern\n8. South Asian\n9. East Asian\n10. Caribbean\n11. Latino\n12. Indigenous\n13. Multiracial\n14. White\n15. Black", "role": "assistant"}}], "created": **********, "id": "2025042415415259be9ae1a5dc4a0b", "model": "glm-4-air-250414", "request_id": "2025042415415259be9ae1a5dc4a0b", "usage": {"completion_tokens": 70, "prompt_tokens": 52, "total_tokens": 122}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为疾病的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的疾病类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 336}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 糖尿病  \n2. 高血压  \n3. 支气管炎  \n4. 胃溃疡  \n5. 偏头痛  \n6. 风湿性关节炎  \n7. 甲状腺功能减退  \n8. 慢性肾病  \n9. 肺炎  \n10. 心律失常  \n11. 贫血  \n12. 胆囊炎  \n13. 脂肪肝  \n14. 帕金森病  \n15. 脊柱侧弯", "role": "assistant"}}], "created": **********, "id": "20250424154152c39f96fc9e704050", "model": "glm-4-air-250414", "request_id": "20250424154152c39f96fc9e704050", "usage": {"completion_tokens": 113, "prompt_tokens": 51, "total_tokens": 164}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为药物的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的药物类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 343}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Atorvastatin  \n2. Metformin  \n3. Ibuprofen  \n4. Omeprazole  \n5. Loratadine  \n6. Amoxicillin  \n7. Simvastatin  \n8. Lisinopril  \n9. Citalopram  \n10. Acetaminophen  \n11. Ciprofloxacin  \n12. Sertraline  \n13. Hydrochlorothiazide  \n14. Albuterol  \n15. Fluoxetine", "role": "assistant"}}], "created": **********, "id": "2025042415415158db87bc9e144050", "model": "glm-4-air-250414", "request_id": "2025042415415158db87bc9e144050", "usage": {"completion_tokens": 105, "prompt_tokens": 51, "total_tokens": 156}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为地理位置的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的地理位置类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 553}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 纽约市\n2. 北京\n3. 东京\n4. 伦敦\n5. 巴黎\n6. 上海\n7. 悉尼\n8. 莫斯科\n9. 新德里\n10. 多伦多\n11. 曼谷\n12. 首尔\n13. 米兰\n14. 哥本哈根\n15. 柏林\n16. 马德里\n17. 罗马\n18. 布宜诺斯艾利斯\n19. 里约热内卢\n20. 开普敦", "role": "assistant"}}], "created": **********, "id": "202504241541515a9e3b59f7ae4a3d", "model": "glm-4-air-250414", "request_id": "202504241541515a9e3b59f7ae4a3d", "usage": {"completion_tokens": 127, "prompt_tokens": 52, "total_tokens": 179}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为生育信息的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的生育信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 371}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 孕周记录  \n2. 流产史详情  \n3. 产前检查结果  \n4. 人工授精记录  \n5. 辅助生殖方案  \n6. 不孕症诊断  \n7. 剖宫产手术记录  \n8. 子宫健康检查  \n9. 产后恢复计划  \n10. 多胎妊娠风险  \n11. 胎儿发育监测  \n12. 卵巢功能评估  \n13. 避孕方法选择  \n14. 生育能力测试  \n15. 婴儿喂养方式", "role": "assistant"}}], "created": **********, "id": "202504241541511b0288006ac64ac2", "model": "glm-4-air-250414", "request_id": "202504241541511b0288006ac64ac2", "usage": {"completion_tokens": 119, "prompt_tokens": 53, "total_tokens": 172}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为地理位置的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的地理位置类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 378}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. New York City\n2. Shanghai District\n3. Paris Region\n4. London Borough\n5. Tokyo Ward\n6. Berlin Suburb\n7. Mumbai Locality\n8. Sydney Suburb\n9. Rio de Janeiro Zone\n10. Cairo Governorate\n11. Mexico City Delegación\n12. Toronto Neighbourhood\n13. Bangkok District\n14. Moscow Raion\n15. Lagos Local Government\n16. Dubai Emirate\n17. Hamburg Borough\n18. Cape Town Ward\n19. Buenos Aires Barrio\n20. Seoul Gu", "role": "assistant"}}], "created": **********, "id": "20250424154152da0978c4063f43b6", "model": "glm-4-air-250414", "request_id": "20250424154152da0978c4063f43b6", "usage": {"completion_tokens": 119, "prompt_tokens": 52, "total_tokens": 171}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为疾病的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的疾病类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 511}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 高血压  \n2. 糖尿病  \n3. 风湿性关节炎  \n4. 哮喘  \n5. 胃炎  \n6. 贫血  \n7. 感冒  \n8. 湿疹  \n9. 青光眼  \n10. 普通感冒  \n11. 腰椎间盘突出  \n12. 慢性支气管炎  \n13. 骨质疏松  \n14. 脂肪肝  \n15. 过敏性鼻炎", "role": "assistant"}}], "created": **********, "id": "202504241541528bbd588d289e4abb", "model": "glm-4-air-250414", "request_id": "202504241541528bbd588d289e4abb", "usage": {"completion_tokens": 115, "prompt_tokens": 51, "total_tokens": 166}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为药物的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的药物类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 518}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 阿司匹林  \n2. 布洛芬  \n3. 对乙酰氨基酚  \n4. 头孢克洛  \n5. 氧氟沙星  \n6. 甲硝唑  \n7. 青霉素V钾  \n8. 氯雷他定  \n9. 地西泮  \n10. 氢氯噻嗪  \n11. 辛伐他汀  \n12. 格列美脲  \n13. 吲哚美辛  \n14. 维生素C  \n15. 链霉素", "role": "assistant"}}], "created": **********, "id": "20250424154151b466fb64792143a3", "model": "glm-4-air-250414", "request_id": "20250424154151b466fb64792143a3", "usage": {"completion_tokens": 123, "prompt_tokens": 51, "total_tokens": 174}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成10个多样化的、可以归类为性别的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的性别类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 231}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 女性用户群体  \n2. 男性身份信息  \n3. 非二元性别标签  \n4. 性别自述记录  \n5. 女性姓名列表  \n6. 男性联系方式  \n7. 性别认同声明  \n8. 女性年龄分布  \n9. 男性健康档案  \n10. 性别偏好选项", "role": "assistant"}}], "created": **********, "id": "2025042415415167db583a7f9a4838", "model": "glm-4-air-250414", "request_id": "2025042415415167db583a7f9a4838", "usage": {"completion_tokens": 79, "prompt_tokens": 52, "total_tokens": 131}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为民族的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的民族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 259}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n以下是为命名实体识别任务生成的15个中等敏感级别的民族命名实体，每行一个：\n\n1. 汉族  \n2. 藏族  \n3. 蒙古族  \n4. 壮族  \n5. 维吾尔族  \n6. 苗族  \n7. 彝族  \n8. 布依族  \n9. 朝鲜族  \n10. 满族  \n11. 瑶族  \n12. 白族  \n13. 土家族  \n14. 哈尼族  \n15. 傣族", "role": "assistant"}}], "created": **********, "id": "2025042415415249fd7fe409ec4893", "model": "glm-4-air-250414", "request_id": "2025042415415249fd7fe409ec4893", "usage": {"completion_tokens": 120, "prompt_tokens": 51, "total_tokens": 171}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为信用记录的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的信用记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 315}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 信用评分\n2. 信用报告\n3. 信用卡账单\n4. 贷款历史\n5. 支付记录\n6. 债务明细\n7. 逾期记录\n8. 信用查询记录\n9. 信用额度\n10. 征信报告\n11. 账户状态\n12. 信用额度使用率\n13. 公共记录\n14. 信用账户类型\n15. 信用期限\n16. 信用还款计划\n17. 信用违约记录\n18. 信用评分模型\n19. 信用风险评级\n20. 信用监控报告", "role": "assistant"}}], "created": **********, "id": "202504241541513703f08932da486e", "model": "glm-4-air-250414", "request_id": "202504241541513703f08932da486e", "usage": {"completion_tokens": 139, "prompt_tokens": 54, "total_tokens": 193}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为职业的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的职业类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 420}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 市场专员  \n2. 软件工程师  \n3. 人力资源助理  \n4. 会计师  \n5. 行政秘书  \n6. 销售代表  \n7. 内容编辑  \n8. 客户服务代表  \n9. 物流协调员  \n10. 项目助理  \n11. 数据分析师  \n12. 图形设计师  \n13. 实验室技术员  \n14. 设备操作员  \n15. 健身教练  \n16. 汽车修理工  \n17. 图书管理员  \n18. 房地产经纪人  \n19. 教育顾问  \n20. 餐厅服务员", "role": "assistant"}}], "created": **********, "id": "20250424154151b87b81e7ffa543f7", "model": "glm-4-air-250414", "request_id": "20250424154151b87b81e7ffa543f7", "usage": {"completion_tokens": 136, "prompt_tokens": 52, "total_tokens": 188}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为信用记录的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的信用记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 490}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 信用评分\n2. 信用报告\n3. 借款历史\n4. 还款记录\n5. 信用卡使用情况\n6. 账单支付历史\n7. 逾期记录\n8. 贷款申请信息\n9. 信用额度\n10. 债务余额\n11. 贷款类型\n12. 信用查询记录\n13. 公共记录\n14. 信用账户状态\n15. 信用历史长度\n16. 负债率\n17. 信用局报告\n18. 账户余额\n19. 偿付能力记录\n20. 信用风险评估", "role": "assistant"}}], "created": **********, "id": "20250424154151674b101ea89647f1", "model": "glm-4-air-250414", "request_id": "20250424154151674b101ea89647f1", "usage": {"completion_tokens": 145, "prompt_tokens": 54, "total_tokens": 199}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为过敏信息的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的过敏信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 189}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. PenicillinAllergyRecord  \n2. PeanutAllergyMedicalHistory  \n3. LatexAllergyProfile  \n4. ShellfishAllergyDocumentation  \n5. SulfaDrugAllergyReport  \n6. MilkAllergyDiagnosticNotes  \n7. WheatAllergySeverityAssessment  \n8. BeeStingAllergyEmergencyPlan  \n9. AspirinAllergyInteractionLog  \n10. EggAllergyTreatmentProtocol  \n11. GlutenAllergyPatientProfile  \n12. SulfonylureaAllergyReactionDetails  \n13. NickelAllergyContactLog  \n14. SulfonamideAllergyIntoleranceForm  \n15. TreeNutAllergyMedicalAlert", "role": "assistant"}}], "created": **********, "id": "20250424154152d389e681f29840c5", "model": "glm-4-air-250414", "request_id": "20250424154152d389e681f29840c5", "usage": {"completion_tokens": 137, "prompt_tokens": 53, "total_tokens": 190}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为投资产品的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的投资产品类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 126}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 高级私人股本基金\n2. 超级对冲基金组合\n3. 顶级私募股权投资\n4. 高净值财富管理计划\n5. 机构级债券信托\n6. 全球战略投资组合\n7. 首席风险投资基金\n8. 顶级不动产信托\n9. 私密家族办公室\n10. 超级衍生品交易\n11. 保密股权基金\n12. 高级货币市场基金\n13. 保密大宗商品基金\n14. 私人银行投资计划\n15. 顶级期货合约\n16. 高级结构性投资产品\n17. 保密股票期权交易\n18. 高级对冲交易基金\n19. 私密共同基金\n20. 超级指数基金投资", "role": "assistant"}}], "created": **********, "id": "202504241541519a7eda52e0ba4762", "model": "glm-4-air-250414", "request_id": "202504241541519a7eda52e0ba4762", "usage": {"completion_tokens": 169, "prompt_tokens": 53, "total_tokens": 222}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为职业的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的职业类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 245}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 医生\n2. 教师\n3. 工程师\n4. 律师\n5. 警察\n6. 护士\n7. 软件开发人员\n8. 会计师\n9. 记者\n10. 银行家\n11. 建筑师\n12. 心理咨询师\n13. 物理治疗师\n14. 航空航天工程师\n15. 税务顾问\n16. 审计师\n17. 人力资源经理\n18. 数据分析师\n19. 市场营销经理\n20. 研究员", "role": "assistant"}}], "created": **********, "id": "20250424154152b969525853564bbd", "model": "glm-4-air-250414", "request_id": "20250424154152b969525853564bbd", "usage": {"completion_tokens": 137, "prompt_tokens": 52, "total_tokens": 189}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为信用记录的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的信用记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 140}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 信用评分\n2. 信用报告\n3. 借贷历史\n4. 还款记录\n5. 逾期信息\n6. 公共记录\n7. 破产记录\n8. 信用卡账单\n9. 贷款申请记录\n10. 债务清算历史\n11. 信用查询记录\n12. 信用额度\n13. 负面信用事件\n14. 信用账户状态\n15. 信用违约记录\n16. 信用修复历史\n17. 信用授权声明\n18. 信用验证结果\n19. 信用监控报告\n20. 信用风险评估", "role": "assistant"}}], "created": **********, "id": "20250424154151562ca7063d1d4cfa", "model": "glm-4-air-250414", "request_id": "20250424154151562ca7063d1d4cfa", "usage": {"completion_tokens": 142, "prompt_tokens": 54, "total_tokens": 196}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为实体资产的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的实体资产类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 497}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 服务器机房\n2. 数据存储单元\n3. 办公设备\n4. 网络交换机\n5. 打印机\n6. 会议室投影仪\n7. 办公桌椅\n8. 备用电源设备\n9. 网络路由器\n10. 固定电话\n11. 硬盘驱动器\n12. 复印机\n13. 机柜\n14. 电脑显示器\n15. 鼠标键盘套装\n16. 网络防火墙\n17. 备份磁带机\n18. 服务器机架\n19. 工作站\n20. 传真机", "role": "assistant"}}], "created": **********, "id": "202504241541527cb4941b29c24266", "model": "glm-4-air-250414", "request_id": "202504241541527cb4941b29c24266", "usage": {"completion_tokens": 148, "prompt_tokens": 53, "total_tokens": 201}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为姓名的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的姓名类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 42}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 张伟\n2. 李娜\n3. 王芳\n4. 刘强\n5. 陈静\n6. 杨磊\n7. 赵敏\n8. 黄勇\n9. 周红\n10. 吴刚\n11. 徐丽\n12. 孙杰\n13. 郑华\n14. 朱明\n15. 马丽", "role": "assistant"}}], "created": **********, "id": "2025042415415188a611ec47cd4d4b", "model": "glm-4-air-250414", "request_id": "2025042415415188a611ec47cd4d4b", "usage": {"completion_tokens": 86, "prompt_tokens": 52, "total_tokens": 138}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为实体资产的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的实体资产类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 322}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 个人健康记录系统  \n2. 公司财务数据库  \n3. 客户联系方式列表  \n4. 员工绩效评估文件  \n5. 供应链管理平台  \n6. 研发项目保密文档  \n7. 银行客户账户信息  \n8. 医院病人就诊记录  \n9. 在线教育用户数据  \n10. 汽车制造商客户档案  \n11. 政府公务员薪资记录  \n12. 保险公司理赔数据库  \n13. 房地产交易客户信息  \n14. 电商平台购物记录  \n15. 社交媒体用户资料  \n16. 食品公司供应商清单  \n17. 电信运营商通话记录  \n18. 能源公司设备维护日志  \n19. 旅游网站预订信息  \n20. 法律事务所案件档案", "role": "assistant"}}], "created": **********, "id": "202504241541520a862a0b82834c94", "model": "glm-4-air-250414", "request_id": "202504241541520a862a0b82834c94", "usage": {"completion_tokens": 168, "prompt_tokens": 53, "total_tokens": 221}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成25个多样化的、可以归类为交易信息的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的交易信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 504}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 消费金额  \n2. 交易时间  \n3. 交易编号  \n4. 支付方式  \n5. 商品编号  \n6. 订单总额  \n7. 退款金额  \n8. 购买数量  \n9. 优惠券编号  \n10. 交易状态  \n11. 购物车ID  \n12. 商品单价  \n13. 运费金额  \n14. 交易地点  \n15. 结账时间  \n16. 商品类别  \n17. 支付平台  \n18. 交易手续费  \n19. 购买日期  \n20. 发票编号  \n21. 购物积分  \n22. 交易备注  \n23. 商品重量  \n24. 支付状态  \n25. 交易流水号", "role": "assistant"}}], "created": **********, "id": "202504241541518c3790863586428e", "model": "glm-4-air-250414", "request_id": "202504241541518c3790863586428e", "usage": {"completion_tokens": 162, "prompt_tokens": 53, "total_tokens": 215}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为教育背景的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的教育背景类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 266}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 学士学位证书\n2. 硕士研究生学历\n3. 博士学位授予记录\n4. 高中毕业证书\n5. 大专学历证明\n6. 职业资格证书\n7. 学术成绩单\n8. 在读学校信息\n9. 学位授予日期\n10. 专业方向记录\n11. 学术奖励证书\n12. 教育经历时间线\n13. 学分完成情况\n14. 毕业院校名称\n15. 培训课程参与记录\n16. 研究生入学资格\n17. 学术论文发表记录\n18. 继续教育学分\n19. 学历验证报告\n20. 教育背景摘要", "role": "assistant"}}], "created": **********, "id": "2025042415415279c74b63e7024af7", "model": "glm-4-air-250414", "request_id": "2025042415415279c74b63e7024af7", "usage": {"completion_tokens": 154, "prompt_tokens": 54, "total_tokens": 208}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为医疗程序的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的医疗程序类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 357}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 血液透析治疗  \n2. 核磁共振成像  \n3. 腹腔镜手术  \n4. 支气管镜检查  \n5. 乳房X光筛查  \n6. 经皮冠状动脉介入治疗  \n7. 骨密度检测  \n8. 甲状腺超声检查  \n9. 结肠镜检查  \n10. 前列腺活检  \n11. 胸腔穿刺术  \n12. 脊柱融合手术  \n13. 眼底荧光血管造影  \n14. 鼻内镜手术  \n15. 肾脏穿刺活检", "role": "assistant"}}], "created": 1745480515, "id": "20250424154152a0af3e96800d445c", "model": "glm-4-air-250414", "request_id": "20250424154152a0af3e96800d445c", "usage": {"completion_tokens": 125, "prompt_tokens": 53, "total_tokens": 178}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为税务记录的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的税务记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 483}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 年度个人所得税申报表\n2. 工资单\n3. 扣缴税款记录\n4. 零申报证明\n5. 税务登记证\n6. 增值税发票\n7. 税款缴纳凭证\n8. 税务减免申请表\n9. 税务罚款通知单\n10. 税务咨询记录\n11. 税务审计报告\n12. 税务代理委托书\n13. 税务检查通知\n14. 税务行政复议决定\n15. 税务行政复议申请\n16. 税务行政复议答辩\n17. 税务行政复议决定书\n18. 税务行政复议申请书\n19. 税务行政复议答辩状\n20. 税务行政复议决定书副本", "role": "assistant"}}], "created": 1745480515, "id": "2025042415415198f810136bdf4e85", "model": "glm-4-air-250414", "request_id": "2025042415415198f810136bdf4e85", "usage": {"completion_tokens": 183, "prompt_tokens": 54, "total_tokens": 237}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为过敏信息的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的过敏信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 364}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 花粉过敏史记录  \n2. 食物过敏源清单  \n3. 青霉素过敏反应文档  \n4. 药物过敏警示标签  \n5. 过敏原测试结果  \n6. 化学物质过敏档案  \n7. 昆虫叮咬过敏记录  \n8. 动物毛发过敏信息  \n9. 食品添加剂过敏表  \n10. 尘螨过敏检测报告  \n11. 鱼类过敏医疗证明  \n12. 乳制品过敏诊断书  \n13. 非处方药过敏提示  \n14. 芳香剂过敏注意事项  \n15. 食物不耐受测试数据", "role": "assistant"}}], "created": 1745480515, "id": "20250424154152a901cbd5457d4ab9", "model": "glm-4-air-250414", "request_id": "20250424154152a901cbd5457d4ab9", "usage": {"completion_tokens": 146, "prompt_tokens": 53, "total_tokens": 199}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为过敏信息的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的过敏信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 539}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 常见的季节性过敏症状  \n2. 某些食物不耐受反应  \n3. 花粉引发的呼吸道敏感  \n4. 青霉素类抗生素过敏史  \n5. 乳制品摄入后的轻微不适  \n6. 尘螨导致的轻微皮肤反应  \n7. 某种坚果的轻度过敏表现  \n8. 鱼类蛋白质引发的轻微不适  \n9. 鸡蛋过敏的轻微症状描述  \n10. 某种药物的轻度不良反应  \n11. 猫毛接触后的轻微打喷嚏  \n12. 虫咬后的局部轻微红肿  \n13. 某种花粉的轻微过敏迹象  \n14. 海鲜摄入后的轻微肠胃反应  \n15. 某种香料接触后的轻微不适", "role": "assistant"}}], "created": 1745480515, "id": "20250424154152f65070cec3394583", "model": "glm-4-air-250414", "request_id": "20250424154152f65070cec3394583", "usage": {"completion_tokens": 169, "prompt_tokens": 53, "total_tokens": 222}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为年龄的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的年龄类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 49}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 婴儿\n2. 幼儿\n3. 少年\n4. 青年\n5. 中年\n6. 老年\n7. 新生儿\n8. 学龄前儿童\n9. 青少年\n10. 中年人\n11. 老龄人口\n12. 青年时期\n13. 中年阶段\n14. 老年阶段\n15. 未成年人\n16. 成年人\n17. 老年人\n18. 青壮年\n19. 青少年时期\n20. 老年群体", "role": "assistant"}}], "created": 1745480515, "id": "20250424154152d9366600d79a407b", "model": "glm-4-air-250414", "request_id": "20250424154152d9366600d79a407b", "usage": {"completion_tokens": 138, "prompt_tokens": 52, "total_tokens": 190}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为地理位置的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的地理位置类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 203}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 北京市中心\n2. 香港中环\n3. 上海浦东新区\n4. 东京涩谷区\n5. 纽约曼哈顿\n6. 伦敦金融城\n7. 巴黎卢浮宫\n8. 莫斯科红场\n9. 开罗金字塔\n10. 新德里国会大厦\n11. 悉尼歌剧院\n12. 巴西利亚总统府\n13. 哥本哈根市政厅\n14. 首尔青瓦台\n15. 华盛顿白宫\n16. 罗马梵蒂冈\n17. 曼谷皇宫\n18. 马德里普拉多博物馆\n19. 墨西哥城宪法广场\n20. 菲尼克斯总统府", "role": "assistant"}}], "created": 1745480515, "id": "20250424154152c88aead4c03d4642", "model": "glm-4-air-250414", "request_id": "20250424154152c88aead4c03d4642", "usage": {"completion_tokens": 165, "prompt_tokens": 52, "total_tokens": 217}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成25个多样化的、可以归类为工资数额的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的工资数额类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 119}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 年薪850000元  \n2. 月薪35000人民币  \n3. 时薪50美元  \n4. 年薪1200000人民币  \n5. 月薪20000欧元  \n6. 时薪35人民币  \n7. 年薪600000元  \n8. 月薪45000美元  \n9. 时薪80欧元  \n10. 年薪900000人民币  \n11. 月薪30000元  \n12. 时薪40美元  \n13. 年薪1500000人民币  \n14. 月薪50000欧元  \n15. 时薪90人民币  \n16. 年薪750000元  \n17. 月薪25000美元  \n18. 时薪60欧元  \n19. 年薪1100000人民币  \n20. 月薪40000元  \n21. 时薪45美元  \n22. 年薪2000000人民币  \n23. 月薪60000欧元  \n24. 时薪100人民币  \n25. 年薪500000元", "role": "assistant"}}], "created": 1745480515, "id": "2025042415415104ad486d44af450e", "model": "glm-4-air-250414", "request_id": "2025042415415104ad486d44af450e", "usage": {"completion_tokens": 197, "prompt_tokens": 54, "total_tokens": 251}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为税务记录的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的税务记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 308}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 年度个人所得税申报表\n2. 雇主所得税扣缴记录\n3. 房产税缴纳证明\n4. 车辆购置税凭证\n5. 增值税专用发票\n6. 企业所得税汇算清缴表\n7. 继承遗产税申报单\n8. 土地使用税缴纳记录\n9. 消费税纳税申报表\n10. 个人所得纳税记录\n11. 营业税纳税申报表\n12. 印花税缴纳凭证\n13. 契税缴纳证明\n14. 企业年度纳税审计报告\n15. 自由职业者税务登记表\n16. 小规模纳税人税务登记\n17. 企业纳税信用等级证明\n18. 个人税务身份识别号\n19. 退税申请表\n20. 税务审计通知书", "role": "assistant"}}], "created": 1745480515, "id": "20250424154152512bf9b0e7b94772", "model": "glm-4-air-250414", "request_id": "20250424154152512bf9b0e7b94772", "usage": {"completion_tokens": 182, "prompt_tokens": 54, "total_tokens": 236}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成10个多样化的、可以归类为政治倾向的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的政治倾向类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 105}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 民主党派联盟  \n2. 共和党核心小组  \n3. 社会主义先锋队  \n4. 自由主义论坛  \n5. 保守主义研究会  \n6. 激进左翼阵线  \n7. 极右翼民族联盟  \n8. 绿色和平政治分支  \n9. 无政府主义行动小组  \n10. 祖国保卫委员会", "role": "assistant"}}], "created": 1745480515, "id": "202504241541531e48222a1b6d481d", "model": "glm-4-air-250414", "request_id": "202504241541531e48222a1b6d481d", "usage": {"completion_tokens": 87, "prompt_tokens": 54, "total_tokens": 141}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为教育背景的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的教育背景类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 441}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 高中毕业证书\n2. 学士学位\n3. 硕士学位\n4. 博士学位\n5. 职业培训证书\n6. 大专学历\n7. 本科毕业证\n8. 研究生学历\n9. 学士学位证书\n10. 技工学校毕业证\n11. 职业学院学历\n12. 硕士学位证书\n13. 博士后研究经历\n14. 专科毕业证书\n15. 成人高等教育学历\n16. 自考本科学历\n17. 网络教育毕业证\n18. 职业资格证书\n19. 学位证书\n20. 高等教育自学考试学历", "role": "assistant"}}], "created": 1745480515, "id": "202504241541514f016ed1de134e53", "model": "glm-4-air-250414", "request_id": "202504241541514f016ed1de134e53", "usage": {"completion_tokens": 154, "prompt_tokens": 54, "total_tokens": 208}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成25个多样化的、可以归类为行程信息的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的行程信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 560}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 出发日期  \n2. 抵达时间  \n3. 机票号码  \n4. 火车车次  \n5. 船票编号  \n6. 飞机座位号  \n7. 汽车租赁合同号  \n8. 旅馆预订码  \n9. 酒店入住日期  \n10. 酒店退房日期  \n11. 旅行保险单号  \n12. 行程单编号  \n13. 旅行社订单号  \n14. 景点门票序列号  \n15. 导游证号  \n16. 旅行团编号  \n17. 自驾路线名称  \n18. 公共交通卡号  \n19. 地铁站名称  \n20. 火车站名称  \n21. 机场代码  \n22. 停车场编号  \n23. 自行车租赁码  \n24. 长途巴士票号  \n25. 行李标签编号", "role": "assistant"}}], "created": 1745480515, "id": "202504241541511066e3acea014d50", "model": "glm-4-air-250414", "request_id": "202504241541511066e3acea014d50", "usage": {"completion_tokens": 197, "prompt_tokens": 53, "total_tokens": 250}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成25个多样化的、可以归类为行程信息的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的行程信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 385}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 航班号  \n2. 座位号  \n3. 登机口  \n4. 航班日期  \n5. 航班时间  \n6. 机场代码  \n7. 航班路线  \n8. 航空公司名称  \n9. 行李提取号  \n10. 机票预订号  \n11. 酒店入住时间  \n12. 酒店退房时间  \n13. 酒店房间号  \n14. 酒店预订号  \n15. 火车票号  \n16. 火车座位类型  \n17. 火车出发站  \n18. 火车到达站  \n19. 公交车线路号  \n20. 公交车班次号  \n21. 地铁线路号  \n22. 地铁站点名称  \n23. 出租车车牌号  \n24. 自行车租赁码  \n25. 旅游景点门票号", "role": "assistant"}}], "created": 1745480515, "id": "20250424154151188ecd3fdeb448d5", "model": "glm-4-air-250414", "request_id": "20250424154151188ecd3fdeb448d5", "usage": {"completion_tokens": 201, "prompt_tokens": 53, "total_tokens": 254}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为实体资产的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的实体资产类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 147}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 生物识别数据库  \n2. 核电站控制面板  \n3. 军事基地加密文件  \n4. 国家安全局服务器  \n5. 医院患者完整病历  \n6. 研发中心源代码库  \n7. 空军飞行路径密钥  \n8. 银行核心交易系统  \n9. 国家机密情报档案  \n10. 研究所DNA样本库  \n11. 电信网络核心密钥  \n12. 航空公司飞行计划  \n13. 核燃料管理记录  \n14. 金融机构客户全量数据  \n15. 国防部卫星图像库  \n16. 药厂临床试验完整数据  \n17. 电力公司电网控制终端  \n18. 智能城市交通调度系统  \n19. 航空管制雷达数据  \n20. 生物实验室病毒样本库", "role": "assistant"}}], "created": 1745480515, "id": "202504241541524334ec9f3d5c45dd", "model": "glm-4-air-250414", "request_id": "202504241541524334ec9f3d5c45dd", "usage": {"completion_tokens": 184, "prompt_tokens": 53, "total_tokens": 237}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为教育背景的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的教育背景类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 91}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Harvard University Degree Records\n2. MIT Graduate School Transcripts\n3. Stanford University Admissions Data\n4. Yale College Personal Academic Histories\n5. Oxford University Student Registration Details\n6. Cambridge University Examination Results\n7. Princeton University Financial Aid Information\n8. UC Berkeley Academic Performance Logs\n9. Columbia University Faculty Student Lists\n10. University of Chicago Educational Background Files\n11. Cornell University Personal Identification Records\n12. Imperial College London Course Enrollment Data\n13. University of Tokyo Student Biometrics\n14. Sorbonne University Personalized Learning Paths\n15. ETH Zurich Research Project Participant Data\n16. University of Melbourne Undergraduate Records\n17. National University of Singapore Graduate Profiles\n18. University of Toronto Continuing Education Logs\n19. University of Sydney Special Needs Accommodations\n20. University of Edinburgh Alumni Personal Details", "role": "assistant"}}], "created": 1745480515, "id": "20250424154151e34c948a9d7140ca", "model": "glm-4-air-250414", "request_id": "20250424154151e34c948a9d7140ca", "usage": {"completion_tokens": 174, "prompt_tokens": 54, "total_tokens": 228}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成25个多样化的、可以归类为交易信息的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的交易信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 154}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 信用卡号\n2. 银行账号\n3. 支付密码\n4. 网络支付令牌\n5. 金融交易记录\n6. 投资账户ID\n7. 电子钱包余额\n8. 汇款密码\n9. 税号\n10. 税务申报记录\n11. 证券交易流水\n12. 保险单号\n13. 医疗账单详情\n14. 借贷合同编号\n15. 贷款审批记录\n16. 税务缴款证明\n17. 股票交易密码\n18. 虚拟货币钱包地址\n19. 银行安全密钥\n20. 交易授权码\n21. 银行对账单\n22. 保险理赔记录\n23. 资产评估报告\n24. 税务审计文件\n25. 期货合约编号", "role": "assistant"}}], "created": 1745480515, "id": "20250424154152a8fc3c8b554748aa", "model": "glm-4-air-250414", "request_id": "20250424154152a8fc3c8b554748aa", "usage": {"completion_tokens": 205, "prompt_tokens": 53, "total_tokens": 258}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成25个多样化的、可以归类为交易信息的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的交易信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 329}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 信用卡号  \n2. 银行账户余额  \n3. 支付凭证号  \n4. 购物小票信息  \n5. 电子钱包交易记录  \n6. 移动支付订单号  \n7. 银行对账单明细  \n8. 线上购物交易ID  \n9. 现金交易记录  \n10. 税务交易编号  \n11. 股票交易流水  \n12. 保险理赔单号  \n13. 房产交易合同号  \n14. 车辆购置交易号  \n15. 贷款申请编号  \n16. 租赁合同交易码  \n17. 投资基金交易ID  \n18. 捐赠交易凭证  \n19. 慈善捐款记录号  \n20. 跨境交易流水号  \n21. 外汇交易单号  \n22. 会员消费积分记录  \n23. 电子发票号码  \n24. 会员卡交易记录  \n25. 线上预订确认号", "role": "assistant"}}], "created": 1745480515, "id": "20250424154151c495d9da04ed4d62", "model": "glm-4-air-250414", "request_id": "20250424154151c495d9da04ed4d62", "usage": {"completion_tokens": 220, "prompt_tokens": 53, "total_tokens": 273}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成25个多样化的、可以归类为行程信息的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的行程信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 210}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 机票预订记录  \n2. 酒店入住信息  \n3. 铁路行程详情  \n4. 航班登机牌信息  \n5. 租车合同记录  \n6. 旅游签证申请表  \n7. 住宿订单确认单  \n8. 地铁乘车记录  \n9. 出境检查日志  \n10. 船票购买凭证  \n11. 火车座位分配信息  \n12. 旅行保险单据  \n13. 行李托运标签  \n14. 酒店退房时间记录  \n15. 航班延误通知单  \n16. 自驾路线规划文件  \n17. 国际旅行健康声明  \n18. 酒店房间号记录  \n19. 旅行者护照信息  \n20. 酒店会员账户数据  \n21. 交通卡消费明细  \n22. 旅行行程单  \n23. 酒店入住时间日志  \n24. 航班行李限额信息  \n25. 旅行预订确认码", "role": "assistant"}}], "created": 1745480515, "id": "202504241541515274f0fee3774dcd", "model": "glm-4-air-250414", "request_id": "202504241541515274f0fee3774dcd", "usage": {"completion_tokens": 215, "prompt_tokens": 53, "total_tokens": 268}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成25个多样化的、可以归类为工资数额的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的工资数额类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 469}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 年薪50000元  \n2. 月薪8000人民币  \n3. 基本工资3000元  \n4. 绩效奖金2000元  \n5. 年终奖10000元  \n6. 时薪20元  \n7. 基本工资4000人民币  \n8. 税前工资60000元  \n9. 税后工资50000元  \n10. 津贴1500元  \n11. 奖金5000元  \n12. 月薪7000人民币  \n13. 年薪60000元  \n14. 加班费300元  \n15. 年终奖15000元  \n16. 基本工资5000元  \n17. 绩效奖金3000元  \n18. 时薪25元  \n19. 月薪9000人民币  \n20. 税前工资70000元  \n21. 税后工资60000元  \n22. 奖金8000元  \n23. 津贴2000元  \n24. 年薪80000元  \n25. 月薪10000人民币", "role": "assistant"}}], "created": 1745480515, "id": "20250424154152c5768388523749da", "model": "glm-4-air-250414", "request_id": "20250424154152c5768388523749da", "usage": {"completion_tokens": 234, "prompt_tokens": 54, "total_tokens": 288}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为种族的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的种族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 77}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n作为数据隐私专家，我必须强调，生成和识别基于种族的命名实体需要非常谨慎，因为这类信息属于高度敏感数据，可能涉及歧视、偏见和隐私侵犯。在实际应用中，处理这类数据必须严格遵守相关法律法规（如GDPR、CCPA等），并获得充分的用户同意。\n\n以下生成的命名实体仅为示例，用于命名实体识别任务的模拟训练，实际应用中应避免使用或确保合规性。这些实体涵盖了不同的种族背景，但请注意，这种分类本身可能带有历史或社会偏见，需谨慎使用。\n\n1. African_American\n2. Asian_Pacific_Islander\n3. Hispanic_Latino\n4. Native_American\n5. Caucasian_European\n6. Middle_Eastern\n7. South_Asian\n8. East_Asian\n9. Native_Hawaiian\n10. Multiracial\n11. Black_African\n12. White_European\n13. Arab\n14. Indigenous_Australian\n15. Pacific_Islander\n\n再次强调，这些实体属于最高敏感级别，任何使用均需在法律和伦理框架内进行。", "role": "assistant"}}], "created": 1745480515, "id": "20250424154152b0c93f8fa5634e91", "model": "glm-4-air-250414", "request_id": "20250424154152b0c93f8fa5634e91", "usage": {"completion_tokens": 230, "prompt_tokens": 52, "total_tokens": 282}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为投资产品的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的投资产品类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 476}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 增利债券基金\n2. 稳健增长组合\n3. 蓝筹股票投资\n4. 优选混合资产\n5. 普惠理财计划\n6. 阳光私募基金\n7. 资本增值信托\n8. 价值成长组合\n9. 平安保险金\n10. 量化对冲策略\n11. 创新科技基金\n12. 优选债券组合\n13. 全球资产配置\n14. 活力消费投资\n15. 基础设施信托\n16. 智能投顾计划\n17. 绿色能源基金\n18. 跨境资产组合\n19. 永续价值投资\n20. 稳健收益计划", "role": "assistant"}}], "created": **********, "id": "202504241541526cfd9cec493a4726", "model": "glm-4-air-250414", "request_id": "202504241541526cfd9cec493a4726", "usage": {"completion_tokens": 166, "prompt_tokens": 53, "total_tokens": 219}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为税务记录的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的税务记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 133}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 个人所得税申报表\n2. 企业所得税纳税证明\n3. 增值税专用发票\n4. 税务登记证\n5. 税款缴纳凭证\n6. 税务审计报告\n7. 税务减免申请表\n8. 税务违法记录\n9. 税务处罚决定书\n10. 税务行政复议文件\n11. 税务诉讼记录\n12. 税务代理委托书\n13. 税务筹划方案\n14. 税务风险评估报告\n15. 税务合规证明\n16. 税务检查通知书\n17. 税务稽查报告\n18. 税务信息泄露记录\n19. 税务数据备份文件\n20. 税务密码保护文档", "role": "assistant"}}], "created": **********, "id": "2025042415415117e5d32494e64137", "model": "glm-4-air-250414", "request_id": "2025042415415117e5d32494e64137", "usage": {"completion_tokens": 185, "prompt_tokens": 54, "total_tokens": 239}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为生育信息的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的生育信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 546}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 孕期周数  \n2. 分娩方式  \n3. 哺乳期  \n4. 避孕方法  \n5. 月经周期  \n6. 子宫位置  \n7. 卵巢健康  \n8. 输卵管状况  \n9. 卵子质量  \n10. 精子数量  \n11. 遗传病史  \n12. 流产次数  \n13. 剖腹产疤痕  \n14. 产后恢复  \n15. 生育保险", "role": "assistant"}}], "created": **********, "id": "20250424154154894d0d15cdcb4e66", "model": "glm-4-air-250414", "request_id": "20250424154154894d0d15cdcb4e66", "usage": {"completion_tokens": 108, "prompt_tokens": 53, "total_tokens": 161}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为药物的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的药物类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 168}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n请注意，以下列出的是虚构的药物名称，仅用于演示目的。在现实世界中，药物名称和敏感级别可能会不同。\n\n1. 诺华神盾 (Novartis Shield)\n2. 百健康灵 (Biogen Wellness)\n3. 辉瑞安泰 (Pfizer Secure)\n4. 强生护康 (Johnson & Johnson Guardian)\n5. 葛兰素安诺 (GSK Auno)\n6. 罗氏康宁 (Roche HealthGuard)\n7. 礼来健护 (Eli Lilly Guardian)\n8. 默沙东安健 (Merck SecureHealth)\n9. 艾伯维康宁 (AbbVie HealthGuard)\n10. 赛诺菲健安 (Sanofi HealthShield)\n11. 拜耳安健 (Bayer HealthGuard)\n12. 诺和诺德健护 (Novo Nordisk Guardian)\n13. 安进康安 (Amgen SecureWell)\n14. 艾尔建安诺 (Allergan Auno)\n15. 阿斯利康健护 (AstraZeneca Guardian)", "role": "assistant"}}], "created": **********, "id": "2025042415415210dad3df54764b88", "model": "glm-4-air-250414", "request_id": "2025042415415210dad3df54764b88", "usage": {"completion_tokens": 240, "prompt_tokens": 51, "total_tokens": 291}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为年龄的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的年龄类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 399}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 幼年  \n2. 少年  \n3. 青年  \n4. 中年  \n5. 老年  \n6. 儿童  \n7. 青少年  \n8. 中青年  \n9. 老龄  \n10. 少儿  \n11. 青壮年  \n12. 中老年  \n13. 青年人  \n14. 中年人  \n15. 老年人  \n16. 青少年期  \n17. 中年期  \n18. 老年期  \n19. 青年阶段  \n20. 中老年阶段", "role": "assistant"}}], "created": **********, "id": "20250424154154caaaeedf457e4d61", "model": "glm-4-air-250414", "request_id": "20250424154154caaaeedf457e4d61", "usage": {"completion_tokens": 130, "prompt_tokens": 52, "total_tokens": 182}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为民族的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的民族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 434}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 汉族  \n2. 藏族  \n3. 回族  \n4. 蒙古族  \n5. 维吾尔族  \n6. 苗族  \n7. 彝族  \n8. 壮族  \n9. 布依族  \n10. 朝鲜族  \n11. 满族  \n12. 黎族  \n13. 瑶族  \n14. 土家族  \n15. 哈尼族", "role": "assistant"}}], "created": **********, "id": "2025042415415502ac10423a084cdf", "model": "glm-4-air-250414", "request_id": "2025042415415502ac10423a084cdf", "usage": {"completion_tokens": 99, "prompt_tokens": 51, "total_tokens": 150}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为民族的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的民族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 84}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 犹太人  \n2. 爱尔兰人  \n3. 阿拉伯人  \n4. 美洲原住民  \n5. 黑人  \n6. 亚裔  \n7. 拉丁美洲人  \n8. 澳大利亚原住民  \n9. 印度人  \n10. 德国人  \n11. 波兰人  \n12. 俄罗斯人  \n13. 意大利人  \n14. 菲律宾人  \n15. 土著人", "role": "assistant"}}], "created": 1745480517, "id": "202504241541552a97e908cd2045b8", "model": "glm-4-air-250414", "request_id": "202504241541552a97e908cd2045b8", "usage": {"completion_tokens": 113, "prompt_tokens": 51, "total_tokens": 164}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为职业的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的职业类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 70}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 医生  \n2. 护士  \n3. 精神病医生  \n4. 牙医  \n5. 心理咨询师  \n6. 法医  \n7. 药剂师  \n8. 肿瘤专家  \n9. 儿科医生  \n10. 产科医生  \n11. 麻醉师  \n12. 脑外科医生  \n13. 眼科医生  \n14. 耳鼻喉科医生  \n15. 超声医生  \n16. 核医学医生  \n17. 遗传咨询师  \n18. 放射科医生  \n19. 传染病专家  \n20. 皮肤科医生", "role": "assistant"}}], "created": 1745480518, "id": "20250424154155904247cbe44a47c9", "model": "glm-4-air-250414", "request_id": "20250424154155904247cbe44a47c9", "usage": {"completion_tokens": 147, "prompt_tokens": 52, "total_tokens": 199}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为投资产品的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的投资产品类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 301}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 共同基金账户  \n2. 股票交易记录  \n3. 退休金计划方案  \n4. 债券投资组合  \n5. 期权交易合约  \n6. 房地产投资信托  \n7. 有限合伙企业份额  \n8. 股权众筹项目  \n9. 对冲基金投资  \n10. 证券借贷协议  \n11. 期货合约交易  \n12. 创业投资凭证  \n13. 保险年金产品  \n14. 艺术品投资基金  \n15. 数字货币钱包  \n16. 外汇交易账户  \n17. 生命保险保单  \n18. 分红保险计划  \n19. 养老金基金份额  \n20. 混合型投资工具", "role": "assistant"}}], "created": 1745480518, "id": "20250424154156db29730701d04183", "model": "glm-4-air-250414", "request_id": "20250424154156db29730701d04183", "usage": {"completion_tokens": 158, "prompt_tokens": 53, "total_tokens": 211}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为临床表现的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的临床表现类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 525}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 头痛  \n2. 发热  \n3. 咳嗽  \n4. 腹泻  \n5. 疲劳  \n6. 皮疹  \n7. 呼吸困难  \n8. 胸痛  \n9. 肌肉酸痛  \n10. 恶心  \n11. 呕吐  \n12. 关节疼痛  \n13. 失眠  \n14. 眩晕  \n15. 水肿", "role": "assistant"}}], "created": 1745480519, "id": "20250424154157674728f5e8a54d37", "model": "glm-4-air-250414", "request_id": "20250424154157674728f5e8a54d37", "usage": {"completion_tokens": 101, "prompt_tokens": 52, "total_tokens": 153}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为医疗程序的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的医疗程序类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 182}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 磁共振成像扫描  \n2. 心脏导管插入术  \n3. 脑部手术  \n4. 肾脏移植手术  \n5. 癌症化疗治疗  \n6. 骨髓穿刺活检  \n7. 器官移植手术  \n8. 人工关节置换术  \n9. 糖尿病胰岛素注射  \n10. 胰腺癌切除术  \n11. 肺癌放疗治疗  \n12. 眼部激光手术  \n13. 脊柱融合手术  \n14. 肝脏移植手术  \n15. 胆囊切除术", "role": "assistant"}}], "created": 1745480519, "id": "20250424154157d4ab4557628542c4", "model": "glm-4-air-250414", "request_id": "20250424154157d4ab4557628542c4", "usage": {"completion_tokens": 131, "prompt_tokens": 53, "total_tokens": 184}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成25个多样化的、可以归类为工资数额的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的工资数额类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 294}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 年薪范围\n2. 月薪总额\n3. 时薪标准\n4. 基本工资\n5. 绩效奖金\n6. 年终奖金\n7. 股票期权\n8. 福利补贴\n9. 加班工资\n10. 税前收入\n11. 税后收入\n12. 员工薪资\n13. 职位薪酬\n14. 调薪幅度\n15. 津贴金额\n16. 工资单数额\n17. 薪资等级\n18. 现金补偿\n19. 退休金计划\n20. 住房补贴\n21. 健康保险金\n22. 交通补贴\n23. 培训津贴\n24. 员工分红\n25. 薪资保密协议", "role": "assistant"}}], "created": 1745480519, "id": "202504241541565daeb5a68912424b", "model": "glm-4-air-250414", "request_id": "202504241541565daeb5a68912424b", "usage": {"completion_tokens": 186, "prompt_tokens": 54, "total_tokens": 240}}]
