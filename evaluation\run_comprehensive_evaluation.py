﻿#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合评估脚本
整合所有RQ评估，生成完整的评估报告

使用方法:
python evaluation/run_comprehensive_evaluation.py --run-dir synth_dataset/runs/20250705_143000 --original-dataset data.json --output results/comprehensive/
"""

import os
import sys
import json
import argparse
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_rq1_evaluation(original_dataset: str, generated_dataset: str, output_dir: Path) -> Dict[str, Any]:
    """运行RQ1评估"""
    print("运行RQ1: 数据集统计与分析...")
    
    rq1_output = output_dir / "rq1_statistics"
    rq1_output.mkdir(parents=True, exist_ok=True)
    
    cmd = [
        sys.executable,
        "evaluation/framework/rq1_dataset_statistics.py",
        "--original", original_dataset,
        "--generated", generated_dataset,
        "--output", str(rq1_output)
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        if result.returncode == 0:
            print(" RQ1评估完成")
            
            # 读取评估结果
            report_file = rq1_output / "rq1_evaluation_report.json"
            if report_file.exists():
                with open(report_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        else:
            print(f" RQ1评估失败: {result.stderr}")
            return {"error": result.stderr}
    except Exception as e:
        print(f" RQ1评估异常: {e}")
        return {"error": str(e)}

def run_rq2_evaluation(generated_dataset: str, output_dir: Path, original_dataset: str = None, strategy_dir: str = None) -> Dict[str, Any]:
    """运行RQ2评估"""
    print("运行RQ2: 生成数据质量评估...")
    
    rq2_output = output_dir / "rq2_quality"
    rq2_output.mkdir(parents=True, exist_ok=True)
    
    cmd = [
        sys.executable,
        "evaluation/framework/rq2_quality_assessment.py",
        "--dataset", generated_dataset,
        "--output", str(rq2_output)
    ]
    
    if original_dataset:
        cmd.extend(["--original", original_dataset])
    
    if strategy_dir:
        cmd.extend(["--strategy-dir", strategy_dir])
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        if result.returncode == 0:
            print(" RQ2评估完成")
            
            # 读取评估结果
            report_file = rq2_output / "rq2_evaluation_report.json"
            if report_file.exists():
                with open(report_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        else:
            print(f" RQ2评估失败: {result.stderr}")
            return {"error": result.stderr}
    except Exception as e:
        print(f" RQ2评估异常: {e}")
        return {"error": str(e)}

def run_rq3_evaluation(run_dir: str, output_dir: Path) -> Dict[str, Any]:
    """运行RQ3评估"""
    print("运行RQ3: 自动化迭代流程有效性评估...")
    
    rq3_output = output_dir / "rq3_iteration"
    rq3_output.mkdir(parents=True, exist_ok=True)
    
    cmd = [
        sys.executable,
        "evaluation/framework/rq3_iteration_analysis.py",
        "--run-dir", run_dir,
        "--output", str(rq3_output)
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        if result.returncode == 0:
            print(" RQ3评估完成")
            
            # 读取评估结果
            report_file = rq3_output / "rq3_evaluation_report.json"
            if report_file.exists():
                with open(report_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        else:
            print(f" RQ3评估失败: {result.stderr}")
            return {"error": result.stderr}
    except Exception as e:
        print(f" RQ3评估异常: {e}")
        return {"error": str(e)}

def run_rq4_evaluation(run_dir: str, output_dir: Path) -> Dict[str, Any]:
    """运行RQ4评估"""
    print("运行RQ4: 消融实验...")
    
    rq4_output = output_dir / "rq4_ablation"
    rq4_output.mkdir(parents=True, exist_ok=True)
    
    # 这里应该调用消融实验脚本
    # 由于RQ4脚本还未完全实现，我们创建一个占位符
    placeholder_report = {
        "evaluation_type": "RQ4: 消融实验",
        "evaluation_time": datetime.now().isoformat(),
        "status": "placeholder",
        "message": "RQ4评估脚本待实现"
    }
    
    with open(rq4_output / "rq4_evaluation_report.json", 'w', encoding='utf-8') as f:
        json.dump(placeholder_report, f, ensure_ascii=False, indent=2)
    
    print(" RQ4评估完成（占位符）")
    return placeholder_report

def generate_comprehensive_report(rq1_results: Dict, rq2_results: Dict, 
                                rq3_results: Dict, rq4_results: Dict,
                                output_dir: Path) -> Dict[str, Any]:
    """生成综合评估报告"""
    print("生成综合评估报告...")
    
    comprehensive_report = {
        "evaluation_type": "综合评估报告",
        "evaluation_time": datetime.now().isoformat(),
        "rq1_dataset_statistics": rq1_results,
        "rq2_quality_assessment": rq2_results,
        "rq3_iteration_analysis": rq3_results,
        "rq4_ablation_study": rq4_results,
        "executive_summary": generate_executive_summary(rq1_results, rq2_results, rq3_results, rq4_results)
    }
    
    # 保存综合报告
    report_dir = output_dir / "comprehensive_report"
    report_dir.mkdir(parents=True, exist_ok=True)
    
    with open(report_dir / "comprehensive_evaluation_report.json", 'w', encoding='utf-8') as f:
        json.dump(comprehensive_report, f, ensure_ascii=False, indent=2)
    
    # 生成执行摘要
    generate_executive_summary_text(comprehensive_report, report_dir)
    
    print(f" 综合评估报告已保存到: {report_dir}")
    return comprehensive_report

def generate_executive_summary(rq1_results: Dict, rq2_results: Dict, 
                             rq3_results: Dict, rq4_results: Dict) -> Dict[str, Any]:
    """生成执行摘要"""
    summary = {
        "overall_assessment": "",
        "key_findings": [],
        "recommendations": [],
        "performance_metrics": {}
    }
    
    # RQ1关键发现
    if "error" not in rq1_results:
        rq1_conclusions = rq1_results.get("conclusions", {})
        summary["key_findings"].append({
            "category": "数据集统计分析",
            "finding": rq1_conclusions.get("overall_assessment", "数据集统计分析完成")
        })
        
        # 添加性能指标
        rq1_summary = rq1_results.get("summary", {})
        if rq1_summary:
            summary["performance_metrics"]["dataset_size_ratio"] = rq1_results.get("comparison_analysis", {}).get("size_comparison", {}).get("size_ratio", 0)
    
    # RQ2关键发现
    if "error" not in rq2_results and rq2_results.get("status") != "placeholder":
        rq2_summary = rq2_results.get("summary", {})
        if rq2_summary:
            quality_level = rq2_summary.get("quality_level", "未知")
            overall_score = rq2_summary.get("overall_quality_score", 0)
            
            summary["key_findings"].append({
                "category": "数据质量评估",
                "finding": f"数据质量等级: {quality_level}，综合得分: {overall_score:.3f}"
            })
            
            # 添加性能指标
            summary["performance_metrics"]["quality_score"] = overall_score
            summary["performance_metrics"]["quality_level"] = quality_level
            
            # 各质量维度
            quality_dimensions = rq2_summary.get("quality_dimensions", {})
            for dimension, data in quality_dimensions.items():
                summary["performance_metrics"][f"{dimension}_score"] = data.get("score", 0)
    
    # RQ3关键发现
    if "error" not in rq3_results:
        rq3_conclusions = rq3_results.get("conclusions", {})
        summary["key_findings"].append({
            "category": "迭代流程有效性",
            "finding": rq3_conclusions.get("overall_assessment", "迭代流程分析完成")
        })
        
        # 添加性能指标
        rq3_summary = rq3_results.get("summary", {})
        if rq3_summary:
            summary["performance_metrics"]["total_iterations"] = rq3_summary.get("total_iterations", 0)
            summary["performance_metrics"]["convergence_achieved"] = rq3_summary.get("convergence_achieved", False)
    
    # 生成整体评估
    successful_evaluations = sum(1 for result in [rq1_results, rq2_results, rq3_results, rq4_results] 
                               if "error" not in result and result.get("status") != "placeholder")
    
    if successful_evaluations >= 3:
        summary["overall_assessment"] = "评估系统运行良好，大部分指标正常"
    elif successful_evaluations >= 2:
        summary["overall_assessment"] = "评估系统基本正常，部分指标需要关注"
    else:
        summary["overall_assessment"] = "评估系统存在问题，需要进一步检查"
    
    # 生成建议
    if "error" not in rq1_results:
        rq1_conclusions = rq1_results.get("conclusions", {})
        if "显著差异" in rq1_conclusions.get("entity_distribution", ""):
            summary["recommendations"].append("建议优化实体分布策略以减少与原始数据集的差异")
    
    if "error" not in rq2_results and rq2_results.get("status") != "placeholder":
        rq2_recommendations = rq2_results.get("recommendations", [])
        for rec in rq2_recommendations[:2]:  # 只取前两个建议
            summary["recommendations"].append(f"数据质量: {rec}")
    
    if "error" not in rq3_results:
        rq3_conclusions = rq3_results.get("conclusions", {})
        if "未完全收敛" in rq3_conclusions.get("convergence_effectiveness", ""):
            summary["recommendations"].append("建议增加迭代次数或优化收敛条件")
    
    if not summary["recommendations"]:
        summary["recommendations"].append("系统运行正常，建议继续监控性能指标")
    
    return summary

def generate_executive_summary_text(comprehensive_report: Dict, output_dir: Path):
    """生成执行摘要文本"""
    summary_lines = []
    summary_lines.append("=" * 80)
    summary_lines.append("NER数据生成系统 - 综合评估报告")
    summary_lines.append("=" * 80)
    summary_lines.append(f"评估时间: {comprehensive_report['evaluation_time']}")
    summary_lines.append("")
    
    # 执行摘要
    exec_summary = comprehensive_report["executive_summary"]
    summary_lines.append("执行摘要:")
    summary_lines.append(f"整体评估: {exec_summary['overall_assessment']}")
    summary_lines.append("")
    
    # 关键发现
    summary_lines.append("关键发现:")
    for finding in exec_summary["key_findings"]:
        summary_lines.append(f"   {finding['category']}: {finding['finding']}")
    summary_lines.append("")
    
    # 性能指标
    if exec_summary["performance_metrics"]:
        summary_lines.append("关键性能指标:")
        for metric, value in exec_summary["performance_metrics"].items():
            summary_lines.append(f"   {metric}: {value}")
        summary_lines.append("")
    
    # 建议
    summary_lines.append("建议:")
    for recommendation in exec_summary["recommendations"]:
        summary_lines.append(f"   {recommendation}")
    summary_lines.append("")
    
    # 各RQ评估状态
    summary_lines.append("各研究问题评估状态:")
    
    rq_status = [
        ("RQ1: 数据集统计分析", comprehensive_report["rq1_dataset_statistics"]),
        ("RQ2: 质量评估", comprehensive_report["rq2_quality_assessment"]),
        ("RQ3: 迭代流程分析", comprehensive_report["rq3_iteration_analysis"]),
        ("RQ4: 消融实验", comprehensive_report["rq4_ablation_study"])
    ]
    
    for rq_name, rq_result in rq_status:
        if "error" in rq_result:
            status = " 失败"
        elif rq_result.get("status") == "placeholder":
            status = " 待实现"
        else:
            status = " 完成"
        
        summary_lines.append(f"  {status} {rq_name}")
    
    summary_lines.append("")
    summary_lines.append("=" * 80)
    
    # 保存摘要
    with open(output_dir / "executive_summary.txt", 'w', encoding='utf-8') as f:
        f.write('\n'.join(summary_lines))

def find_generated_dataset(run_dir: Path) -> str:
    """查找生成的数据集文件"""
    # 查找最终数据集
    output_dir = run_dir / "output"
    if output_dir.exists():
        json_files = list(output_dir.glob("final_synthetic_dataset_*.json"))
        if json_files:
            return str(json_files[0])
    
    # 查找最后一次迭代的数据集
    iterations_dir = run_dir / "iterations"
    if iterations_dir.exists():
        iter_dirs = [d for d in iterations_dir.iterdir() if d.is_dir() and d.name.startswith("iteration_")]
        if iter_dirs:
            iter_dirs.sort(key=lambda x: int(x.name.split("_")[1]))
            last_iter = iter_dirs[-1]
            json_files = list(last_iter.glob("iteration_*.json"))
            if json_files:
                return str(json_files[0])
    
    raise FileNotFoundError("未找到生成的数据集文件")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="综合评估脚本")
    parser.add_argument("--run-dir", required=True, help="运行目录路径")
    parser.add_argument("--original-dataset", required=True, help="原始数据集路径")
    parser.add_argument("--output", required=True, help="输出目录")
    parser.add_argument("--skip-rq1", action="store_true", help="跳过RQ1评估")
    parser.add_argument("--skip-rq2", action="store_true", help="跳过RQ2评估")
    parser.add_argument("--skip-rq3", action="store_true", help="跳过RQ3评估")
    parser.add_argument("--skip-rq4", action="store_true", help="跳过RQ4评估")
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print("=" * 80)
    print("NER数据生成系统 - 综合评估")
    print("=" * 80)
    print(f"运行目录: {args.run_dir}")
    print(f"原始数据集: {args.original_dataset}")
    print(f"输出目录: {output_dir}")
    print()
    
    try:
        # 查找生成的数据集
        run_dir = Path(args.run_dir)
        generated_dataset = find_generated_dataset(run_dir)
        print(f"找到生成数据集: {generated_dataset}")
        print()
        
        # 运行各个RQ评估
        rq1_results = {}
        rq2_results = {}
        rq3_results = {}
        rq4_results = {}
        
        if not args.skip_rq1:
            rq1_results = run_rq1_evaluation(args.original_dataset, generated_dataset, output_dir)
        
        if not args.skip_rq2:
            rq2_results = run_rq2_evaluation(generated_dataset, output_dir, args.original_dataset)
        
        if not args.skip_rq3:
            rq3_results = run_rq3_evaluation(args.run_dir, output_dir)
        
        if not args.skip_rq4:
            rq4_results = run_rq4_evaluation(args.run_dir, output_dir)
        
        # 生成综合报告
        comprehensive_report = generate_comprehensive_report(
            rq1_results, rq2_results, rq3_results, rq4_results, output_dir
        )
        
        print()
        print("=" * 80)
        print(" 综合评估完成！")
        print(f"详细报告: {output_dir}/comprehensive_report/comprehensive_evaluation_report.json")
        print(f"执行摘要: {output_dir}/comprehensive_report/executive_summary.txt")
        print("=" * 80)
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
