{"start_time": "2025-07-05T18:06:13.856222", "output_directory": "synth_dataset\\runs\\20250705_180613\\strategies", "target_entity_types": ["姓名", "年龄", "性别", "国籍", "职业", "民族", "教育背景", "婚姻状况", "政治倾向", "家庭成员", "工资数额", "投资产品", "税务记录", "信用记录", "实体资产", "交易信息", "疾病", "药物", "临床表现", "医疗程序", "过敏信息", "生育信息", "地理位置", "行程信息"], "total_entity_types": 24, "generation_config": {"dataset_path": "format-dataset/privacy_bench_small.json", "target_count": 50, "max_iterations": 3, "batch_size": 10, "distribution_threshold": 0.05}, "strategy_files": {"target_distribution": "synth_dataset\\runs\\20250705_180613\\strategies\\entity_target\\privacy_bench_target.json", "entity_diversity": "synth_dataset\\runs\\20250705_180613\\strategies\\entity_diversity\\entity_diversity_20250705_180614"}, "generation_statistics": {"sentence_diversity_attributes": 0, "entity_diversity_pools": 0, "target_distribution_size": 0}, "strategy_statistics": {"strategy_file_sizes": {"target_distribution": 505, "entity_diversity": 0}, "sentence_diversity_stats": {}, "entity_diversity_stats": {}, "target_distribution_stats": {"total_entity_types": 24, "entity_types": ["姓名", "年龄", "性别", "国籍", "职业", "民族", "教育背景", "婚姻状况", "政治倾向", "家庭成员", "工资数额", "投资产品", "税务记录", "信用记录", "实体资产", "交易信息", "疾病", "药物", "临床表现", "医疗程序", "过敏信息", "生育信息", "地理位置", "行程信息"], "target_counts": {"姓名": 34, "年龄": 50, "性别": 50, "国籍": 28, "职业": 9, "民族": 24, "教育背景": 24, "婚姻状况": 50, "政治倾向": 50, "家庭成员": 50, "工资数额": 50, "投资产品": 50, "税务记录": 50, "信用记录": 50, "实体资产": 50, "交易信息": 50, "疾病": 16, "药物": 38, "临床表现": 29, "医疗程序": 23, "过敏信息": 50, "生育信息": 50, "地理位置": 5, "行程信息": 50}, "total_target_count": 930, "average_target_count": 38.75, "target_count_distribution": {"34": 1, "50": 14, "28": 1, "9": 1, "24": 2, "16": 1, "38": 1, "29": 1, "23": 1, "5": 1}}}}