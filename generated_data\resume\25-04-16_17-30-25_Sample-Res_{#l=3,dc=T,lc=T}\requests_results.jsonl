[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名人力资源专家。请生成 3 条中文简历句子。请标记所有属于以下实体类型的命名实体：nationality, education, location, name, ethnicity。\n请在下一行中按照出现的顺序列出这些命名实体及其对应的实体类型。\n如果在生成的句子中没有找到实体，请将方括号留空。\n以下是一些示例句子及其标注，请参考并按照这种格式生成。\n\n示例：\n\nSentence: \"王芳 ， 女 ， 汉族 ， 1990年生 ， 大专学历 ， 现居广州。\"\nNamed Entities: [王芳 (NAME), 汉族 (RACE), 大专 (EDU), 广州 (LOC)]\n\n\n---\n\n生成的简历内容应该符合以下要求：\n1. 生成一份学术简历：适用于学术研究、教育领域，重点展示教育背景、研究成果、发表论文、学术会议等。;\n2. 简历中的教育水平为高中."}], "temperature": 1, "max_tokens": 2560, "logprobs": true, "seed": 56}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\nSentence: \"李明，汉族，毕业于北京大学附属中学高中部，研究方向为人工智能，曾发表学术论文3篇。\"\nNamed Entities: [李明 (NAME), 汉族 (RACE), 北京大学附属中学 (EDU)]", "role": "assistant"}}], "created": 1744795828, "id": "202504161730267353bcc2bc7e4f98", "model": "glm-4-air-250414", "request_id": "202504161730267353bcc2bc7e4f98", "usage": {"completion_tokens": 53, "prompt_tokens": 198, "total_tokens": 251}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名人力资源专家。请生成 3 条中文简历句子。请标记所有属于以下实体类型的命名实体：nationality, education, location, name, ethnicity。\n请在下一行中按照出现的顺序列出这些命名实体及其对应的实体类型。\n如果在生成的句子中没有找到实体，请将方括号留空。\n以下是一些示例句子及其标注，请参考并按照这种格式生成。\n\n示例：\n\nSentence: \"王芳 ， 女 ， 汉族 ， 1990年生 ， 大专学历 ， 现居广州。\"\nNamed Entities: [王芳 (NAME), 汉族 (RACE), 大专 (EDU), 广州 (LOC)]\n\n\n---\n\n生成的简历内容应该符合以下要求：\n1. 生成一份经验简历：重点突出个人过往的工作经验、成就和技能，适合有多年工作经验的求职者。;\n2. 简历中的行业领域为制造业."}], "temperature": 1, "max_tokens": 2560, "logprobs": true, "seed": 42}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\nSentence: \"李明，中国籍，毕业于清华大学机械工程系，拥有15年制造业生产管理经验，曾在上海某大型制造企业担任生产总监。\"\nNamed Entities: [李明 (NAME), 中国 (NATIONALITY), 清华大学 (EDU), 上海 (LOC)]", "role": "assistant"}}], "created": 1744795828, "id": "20250416173026bc9a7ff5b85e4b54", "model": "glm-4-air-250414", "request_id": "20250416173026bc9a7ff5b85e4b54", "usage": {"completion_tokens": 62, "prompt_tokens": 197, "total_tokens": 259}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名人力资源专家。请生成 3 条中文简历句子。请标记所有属于以下实体类型的命名实体：nationality, education, location, name, ethnicity。\n请在下一行中按照出现的顺序列出这些命名实体及其对应的实体类型。\n如果在生成的句子中没有找到实体，请将方括号留空。\n以下是一些示例句子及其标注，请参考并按照这种格式生成。\n\n示例：\n\nSentence: \"王芳 ， 女 ， 汉族 ， 1990年生 ， 大专学历 ， 现居广州。\"\nNamed Entities: [王芳 (NAME), 汉族 (RACE), 大专 (EDU), 广州 (LOC)]\n\n\n---\n\n生成的简历内容应该符合以下要求：\n1. 生成一份创意简历：设计独特、具有创意的简历，适合设计、艺术、广告等创意行业，通过视觉效果吸引招聘者注意。;\n2. 简历中的教育水平为硕士."}], "temperature": 1, "max_tokens": 2560, "logprobs": true, "seed": 70}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\nSentence: \"李明 ， 汉族 ， 持有设计硕士学位 ， 曾在北京工作 ， 现居上海， 热爱创意设计。\"\nNamed Entities: [李明 (NAME), 汉族 (RACE), 硕士 (EDU), 北京 (LOC), 上海 (LOC)]", "role": "assistant"}}], "created": 1744795828, "id": "20250416173026eceba7ef376541d3", "model": "glm-4-air-250414", "request_id": "20250416173026eceba7ef376541d3", "usage": {"completion_tokens": 71, "prompt_tokens": 203, "total_tokens": 274}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名人力资源专家。请生成 3 条中文简历句子。请标记所有属于以下实体类型的命名实体：nationality, education, location, name, ethnicity。\n请在下一行中按照出现的顺序列出这些命名实体及其对应的实体类型。\n如果在生成的句子中没有找到实体，请将方括号留空。\n以下是一些示例句子及其标注，请参考并按照这种格式生成。\n\n示例：\n\nSentence: \"王芳 ， 女 ， 汉族 ， 1990年生 ， 大专学历 ， 现居广州。\"\nNamed Entities: [王芳 (NAME), 汉族 (RACE), 大专 (EDU), 广州 (LOC)]\n\n\n---\n\n生成的简历内容应该符合以下要求：\n1. 生成一份学生简历：面向在校学生，重点展示学业成绩、校园活动、社团经历等，可能较少工作经验。;\n2. 简历中的工作经验为应届生/实习生."}], "temperature": 1, "max_tokens": 2560, "logprobs": true, "seed": 49}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\nSentence: \"李明，汉族，本科生，主修计算机科学，曾担任学生会主席，实习于北京某科技公司。\"\nNamed Entities: [李明 (NAME), 汉族 (RACE), 本科 (EDU), 北京 (LOC)]\n\nSentence: \"张伟，少数民族，研究生，学术成绩优秀，参与多个科研项目，曾在上海某大学交换学习。\"\nNamed Entities: [张伟 (NAME), 少数民族 (RACE), 研究生 (EDU), 上海 (LOC)]\n\nSentence: \"陈静，汉族，大专生，积极参与校园志愿者活动，实习于深圳某互联网公司。\"\nNamed Entities: [陈静 (NAME), 汉族 (RACE), 大专 (EDU), 深圳 (LOC)]", "role": "assistant"}}], "created": 1744795830, "id": "20250416173026c4c8495022944ead", "model": "glm-4-air-250414", "request_id": "20250416173026c4c8495022944ead", "usage": {"completion_tokens": 163, "prompt_tokens": 199, "total_tokens": 362}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名人力资源专家。请生成 3 条中文简历句子。请标记所有属于以下实体类型的命名实体：nationality, education, location, name, ethnicity。\n请在下一行中按照出现的顺序列出这些命名实体及其对应的实体类型。\n如果在生成的句子中没有找到实体，请将方括号留空。\n以下是一些示例句子及其标注，请参考并按照这种格式生成。\n\n示例：\n\nSentence: \"王芳 ， 女 ， 汉族 ， 1990年生 ， 大专学历 ， 现居广州。\"\nNamed Entities: [王芳 (NAME), 汉族 (RACE), 大专 (EDU), 广州 (LOC)]\n\n\n---\n\n生成的简历内容应该符合以下要求：\n1. 生成一份技能导向简历：重点突出个人的专业技能和专长，适合技术类岗位或需要特定技能的职位。;\n2. 简历中的行业领域为零售."}], "temperature": 1, "max_tokens": 2560, "logprobs": true, "seed": 63}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\nSentence: \"李明，汉族，拥有5年零售行业数据分析经验，精通Python和SQL，毕业于北京大学计算机科学专业，现居上海。\"\nNamed Entities: [李明 (NAME), 汉族 (RACE), 北京大学 (EDU), 上海 (LOC)]\n\nSentence: \"张伟，汉族，熟练掌握零售系统架构设计，擅长Java和Spring Boot，曾在清华大学获得软件工程硕士学位，目前工作地点在北京。\"\nNamed Entities: [张伟 (NAME), 汉族 (RACE), 清华大学 (EDU), 北京 (LOC)]\n\nSentence: \"王芳，汉族，具备零售供应链优化能力，精通Excel和Power BI，毕业于南京大学信息管理专业，现居广州。\"\nNamed Entities: [王芳 (NAME), 汉族 (RACE), 南京大学 (EDU), 广州 (LOC)]", "role": "assistant"}}], "created": 1744795831, "id": "20250416173026a9fc9e69ab5744eb", "model": "glm-4-air-250414", "request_id": "20250416173026a9fc9e69ab5744eb", "usage": {"completion_tokens": 182, "prompt_tokens": 198, "total_tokens": 380}}]
