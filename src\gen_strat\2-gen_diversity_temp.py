# # -*- coding: utf-8 -*-
# import json
# import os
# import re
# import requests
# import time
# from datetime import datetime
# from tqdm import tqdm
# from typing import Dict, List, Set, Tuple, Optional
# from pathlib import Path

# # 日志相关全局变量
# log_main_file = None
# log_detail_file = None
# log_timestamp = None
# NUM_ENTITY_VARIANTS = 10  # 默认值，会在main函数中被覆盖

# def setup_logging(output_dir: Path, timestamp: str, target_count: int):
#     """设置日志文件"""
#     global log_main_file, log_detail_file, log_timestamp
#     log_timestamp = timestamp

#     # 清理同目录下的旧日志文件 - 匹配所有可能的日志文件模式
#     log_patterns = [
#         "entity_generation_log_*.txt",
#         "entity_generation_details_*.txt",
#         "entity_generation_*.txt"
#     ]

#     for pattern in log_patterns:
#         for log_file in output_dir.glob(pattern):
#             try:
#                 log_file.unlink()
#                 print(f"[信息] 清理旧日志文件: {log_file}")
#             except Exception as e:
#                 print(f"[警告] 清理日志文件失败 {log_file}: {e}")

#     # 创建日志文件路径 - 保存在ENTITY_OUTPUT_DIR目录下
#     log_main_file = output_dir / f"entity_generation_log_{timestamp}.txt"
#     log_detail_file = output_dir / f"entity_generation_details_{timestamp}.txt"

#     # 写入日志头
#     with open(log_main_file, 'w', encoding='utf-8') as f:
#         f.write(f"=== 实体生成日志 ===\n")
#         f.write(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
#         f.write(f"目标数量: {target_count}\n\n")

#     with open(log_detail_file, 'w', encoding='utf-8') as f:
#         f.write(f"=== 实体生成详细日志 ===\n")
#         f.write(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

# def log_main(message: str, print_to_console: bool = False):
#     """写入主日志"""
#     global log_main_file
#     if log_main_file:
#         with open(log_main_file, 'a', encoding='utf-8') as f:
#             f.write(f"{message}\n")
#     if print_to_console:
#         print(message)

# def log_detail(message: str, print_to_console: bool = False):
#     """写入详细日志"""
#     global log_detail_file
#     if log_detail_file:
#         with open(log_detail_file, 'a', encoding='utf-8') as f:
#             f.write(f"{message}\n")
#     if print_to_console:
#         print(message)

# def log_generation_step(entity_type: str, step_type: str, api_count: int, filtered_count: int, final_count: int, details: List[str] = None, filtered_entities: List[Tuple[str, str]] = None):
#     """记录生成步骤"""
#     step_name = "Vanilla" if step_type == "vanilla" else f"Latent[{step_type}]"
#     log_main(f"[{entity_type}] {step_name} 第1轮生成:")
#     log_main(f"- API返回: {api_count}个")
#     log_main(f"- 过滤后: {filtered_count}个")
#     log_main(f"- 去重后: {final_count}个")

#     if details:
#         log_detail(f"\n=== {entity_type} ===")
#         log_detail(f"{step_name} 第1轮生成:")
#         log_detail(f"生成内容: {details}")
#         log_detail(f"保留: {filtered_count}个")
#         log_detail(f"过滤: {api_count - filtered_count}个")

#         if filtered_entities:
#             log_detail("过滤详情:")
#             for entity, reason in filtered_entities:
#                 log_detail(f"  - \"{entity}\" ({reason})")

# def log_filter_reason(entity: str, reason: str):
#     """记录过滤原因"""
#     log_detail(f"过滤: [\"{entity}\"]({reason})")

# def log_final_result(entity_type: str, target: int, actual: int, success: bool):
#     """记录最终结果"""
#     status = "达标" if success else "未达标"
#     log_main(f"最终结果: {status} ({actual}/{target})")
#     log_main("")  # 空行分隔

# def main(output_dir="reproduce", use_global_cache=True):
#     """主函数：生成多样化策略

#     Args:
#         output_dir: 输出目录
#         use_global_cache: 是否使用全局缓存（默认True）
#     """
#     # =====================
#     # 配置和路径
#     # =====================

#     # 读取API配置
#     with open('src/gen_strat/diversity_config.json', 'r', encoding='utf-8') as f:
#         config = json.load(f)

#     API_KEY_PATH = config["api_key_path"]
#     with open(API_KEY_PATH, 'r', encoding='utf-8') as f:
#         api_key = json.load(f)['api_key']

#     # 读取实体-语境适配表
#     ENTITY_CONTEXT_MAPPING = config.get("entity_context_mapping", {})

#     # 配置参数
#     NUM_SENTENCE_VARIANTS = config.get("num_sentence_variants", 5)
#     NUM_ENTITY_VARIANTS = config.get("num_entity_variants", 10)
#     SKIP_ENTITY_TYPES = config.get("skip_entity_types", [])

#     # API参数
#     ZHIPU_API_URL = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
#     MODEL_NAME = "glm-4-air-250414"
#     headers = {
#         "Authorization": f"Bearer {api_key}",
#         "Content-Type": "application/json"
#     }

#     # 路径设置
#     output_dir = Path(output_dir)
#     timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

#     # 全局缓存目录（项目根目录下的reproduce）
#     global_cache_dir = Path("reproduce")

#     # 运行特定输出目录
#     run_output_dir = output_dir

#     # 句子多样化：优先使用全局缓存，同时复制到运行目录
#     if use_global_cache:
#         SEN_CACHE_DIR = global_cache_dir / "sen_diversity"
#         SEN_OUTPUT_DIR = run_output_dir / "sen_diversity"
#     else:
#         SEN_CACHE_DIR = run_output_dir / "sen_diversity"
#         SEN_OUTPUT_DIR = run_output_dir / "sen_diversity"

#     # 实体多样化：优先使用全局缓存，同时复制到运行目录
#     if use_global_cache:
#         ENTITY_CACHE_DIR = global_cache_dir / "entity_diversity"
#         ENTITY_OUTPUT_DIR = run_output_dir / "entity_diversity" / f"entity_diversity_{timestamp}"
#     else:
#         ENTITY_CACHE_DIR = run_output_dir / "entity_diversity"
#         ENTITY_OUTPUT_DIR = run_output_dir / "entity_diversity" / f"entity_diversity_{timestamp}"

#     # 创建目录
#     SEN_CACHE_DIR.mkdir(parents=True, exist_ok=True)
#     SEN_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
#     ENTITY_CACHE_DIR.mkdir(parents=True, exist_ok=True)
#     ENTITY_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

#     # 设置日志 - 在ENTITY_OUTPUT_DIR创建后设置
#     setup_logging(ENTITY_OUTPUT_DIR, timestamp, NUM_ENTITY_VARIANTS)

#     # 文件路径
#     FILE_SEN_ATTRIBUTE_CACHE = SEN_CACHE_DIR / 'sen_diversify_attribute.json'
#     FILE_SEN_VALUE_CACHE = SEN_CACHE_DIR / 'sen_diversify_value.json'
#     FILE_SEN_ATTRIBUTE_OUTPUT = SEN_OUTPUT_DIR / 'sen_diversify_attribute.json'
#     FILE_SEN_VALUE_OUTPUT = SEN_OUTPUT_DIR / 'sen_diversify_value.json'

#     ENTITY_SCHEMA_PATH = 'src/gen_strat/entity_schema.json'

#     print(f"[信息] 使用全局缓存: {use_global_cache}")
#     print(f"[信息] 全局缓存目录: {global_cache_dir}")
#     print(f"[信息] 运行输出目录: {run_output_dir}")

#     # 打印跳过配置
#     if SKIP_ENTITY_TYPES:
#         print(f"[信息] 配置跳过以下实体类型的生成：{SKIP_ENTITY_TYPES}")

#     # 定义latent场景类型
#     LATENT_SCENARIOS = {
#         "医学描述": "医学、医疗、健康相关的专业术语和表达",
#         "法律文本": "法律、合同、正式文档中的规范表达",
#         "口语表达": "日常对话、非正式场合的通俗表达",
#         "学术论文": "学术研究、论文中的专业表达",
#         "新闻报道": "新闻媒体、报道中的标准表达",
#         "社交场合": "社交、聊天中的常用表达",
#         "商务场合": "商务、职场中的正式表达",
#         "网络用语": "网络、社交媒体中的流行表达"
#     }

#     # 读取实体schema
#     with open(ENTITY_SCHEMA_PATH, 'r', encoding='utf-8') as f:
#         schema_json = json.load(f)
#         flat_list = schema_json.get("flat_list", [])
#         example_map = schema_json.get("example_map", {})

#     # 定义小值域实体的基础值（仅对schema中声明的类型）
#     SMALL_VALUE_DOMAIN_ENTITIES = {
#         "性别": ["男", "女", "男性", "女性", "非二元", "跨性别", "双性", "中性", "雌雄同体"],
#         "婚姻状况": ["已婚", "未婚", "离异", "丧偶", "单身", "再婚", "分居", "未说明", "同居", "未登记", "订婚", "未婚同居", "婚姻存续中", "婚姻解除"],
#         "国籍": ["中国", "美国", "法国", "日本", "韩国", "英国", "德国", "加拿大", "澳大利亚", "俄罗斯", "印度", "巴西", "意大利", "西班牙", "荷兰", "瑞士", "瑞典", "挪威", "丹麦", "芬兰", "比利时", "奥地利", "葡萄牙", "希腊", "波兰", "捷克", "匈牙利", "罗马尼亚", "保加利亚", "克罗地亚", "斯洛文尼亚", "斯洛伐克", "爱沙尼亚", "拉脱维亚", "立陶宛", "新加坡", "马来西亚", "泰国", "越南", "菲律宾", "印度尼西亚", "蒙古", "朝鲜", "伊朗", "以色列", "埃及", "南非", "墨西哥", "阿根廷", "智利", "新西兰", "土耳其", "沙特阿拉伯", "阿联酋", "卡塔尔", "科威特", "巴林", "也门", "约旦", "黎巴嫩", "叙利亚", "巴勒斯坦", "乌克兰", "白俄罗斯", "格鲁吉亚", "亚美尼亚", "哈萨克斯坦", "乌兹别克斯坦", "吉尔吉斯斯坦", "塔吉克斯坦", "土库曼斯坦", "阿塞拜疆", "摩洛哥", "突尼斯", "阿尔及利亚", "苏丹", "尼日利亚", "加纳", "肯尼亚", "坦桑尼亚", "乌干达", "卢旺达", "布隆迪", "塞内加尔", "安哥拉", "津巴布韦", "赞比亚", "莫桑比克", "马达加斯加", "毛里求斯", "塞舌尔", "古巴", "牙买加", "巴哈马", "多米尼加", "哥伦比亚", "委内瑞拉", "秘鲁", "厄瓜多尔", "玻利维亚", "巴拉圭", "乌拉圭"],
#         "民族": ["汉族", "满族", "蒙古族", "回族", "藏族", "维吾尔族", "苗族", "彝族", "壮族", "布依族", "侗族", "瑶族", "白族", "土家族", "哈尼族", "哈萨克族", "傣族", "黎族", "傈僳族", "佤族", "畲族", "高山族", "拉祜族", "水族", "东乡族", "纳西族", "景颇族", "柯尔克孜族", "土族", "达斡尔族", "仫佬族", "羌族", "布朗族", "撒拉族", "毛南族", "仡佬族", "锡伯族", "阿昌族", "普米族", "朝鲜族", "塔吉克族", "怒族", "乌孜别克族", "俄罗斯族", "鄂温克族", "德昂族", "保安族", "裕固族", "京族", "塔塔尔族", "独龙族", "鄂伦春族", "赫哲族", "门巴族", "珞巴族", "基诺族"],
#         "政治倾向": ["保守派", "自由派", "中立", "激进派", "温和派", "改革派", "保守主义", "自由主义", "社会主义", "民族主义", "无党派", "环保主义", "女权主义", "进步派", "传统派", "技术官僚", "民粹主义", "中间派", "极左", "极右"],
#         "教育背景": ["小学毕业", "初中毕业", "高中毕业", "中专", "大专", "本科", "本科毕业", "硕士", "硕士研究生", "博士", "博士后", "MBA", "EMBA", "技校", "自学", "成人教育", "网络教育", "海外留学"],
#         "家庭成员": ["父亲", "母亲", "配偶", "妻子", "丈夫", "儿子", "女儿", "兄弟", "姐妹", "祖父", "祖母", "外祖父", "外祖母", "孙子", "孙女", "外孙", "外孙女", "叔叔", "婶婶", "舅舅", "舅妈", "姑姑", "姑父", "姨", "姨夫", "侄子", "侄女", "表哥", "表姐", "表弟", "表妹", "堂兄", "堂姐", "堂弟", "堂妹", "继父", "继母", "继子", "继女", "养父", "养母", "养子", "养女"]
#         }

#     # =====================
#     # API调用函数
#     # =====================

#     def call_zhipu_api(prompt, max_retries=3, base_wait=5):
#         """调用智谱API"""
#         # print("\n[API PROMPT] 本次API调用的prompt如下:\n" + prompt + "\n")
#         data = {
#             "model": MODEL_NAME,
#             "messages": [{"role": "user", "content": prompt}],
#             "temperature": 0.7,
#             "max_tokens": 2000
#         }

#         for attempt in range(max_retries):
#             try:
#                 response = requests.post(ZHIPU_API_URL, headers=headers, json=data, timeout=30)
#                 response.raise_for_status()
#                 result = response.json()
#                 return result['choices'][0]['message']['content']
#             except Exception as e:
#                 if attempt < max_retries - 1:
#                     wait_time = base_wait * (2 ** attempt)
#                     print(f"API调用失败，{wait_time}秒后重试: {e}")
#                     time.sleep(wait_time)
#                 else:
#                     raise RuntimeError(f"API调用失败: {e}")

#     def extract_list_items(text):
#         """从文本中提取列表项"""
#         lines = [line.strip() for line in text.strip().split('\n') if line.strip()]
#         items = []
#         for line in lines:
#             # 只去除真正的编号格式（如 "1.", "2.", "- " 等），保留年份
