{"basic_statistics": {"total_samples": 307, "text_length_stats": {"mean": 33.00651465798045, "median": 31.0, "std": 15.796959565074522, "min": 4, "max": 152, "percentiles": {"25": 23.5, "75": 40.0, "90": 48.0, "95": 60.69999999999999}}, "entity_count_stats": {"mean": 1.729641693811075, "median": 2.0, "std": 0.9593704854195794, "min": 1, "max": 8}, "label_distribution": {"姓名": 16, "地理位置": 45, "职业": 41, "医疗程序": 27, "疾病": 34, "药物": 12, "临床表现": 21, "国籍": 22, "民族": 26, "教育背景": 26, "年龄": 18, "性别": 20, "婚姻状况": 17, "政治倾向": 18, "家庭成员": 20, "工资数额": 20, "投资产品": 17, "税务记录": 22, "信用记录": 24, "实体资产": 17, "交易信息": 18, "过敏信息": 19, "生育信息": 12, "行程信息": 19}}, "entity_analysis": {"entity_type_distribution": {"姓名": 16, "地理位置": 45, "职业": 41, "医疗程序": 27, "疾病": 34, "药物": 12, "临床表现": 21, "国籍": 22, "民族": 26, "教育背景": 26, "年龄": 18, "性别": 20, "婚姻状况": 17, "政治倾向": 18, "家庭成员": 20, "工资数额": 20, "投资产品": 17, "税务记录": 22, "信用记录": 24, "实体资产": 17, "交易信息": 18, "过敏信息": 19, "生育信息": 12, "行程信息": 19}, "entity_length_analysis": {"姓名": {"count": 16, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "地理位置": {"count": 45, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "职业": {"count": 41, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "医疗程序": {"count": 27, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "疾病": {"count": 34, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "药物": {"count": 12, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "临床表现": {"count": 21, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "国籍": {"count": 22, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "民族": {"count": 26, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "教育背景": {"count": 26, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "年龄": {"count": 18, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "性别": {"count": 20, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "婚姻状况": {"count": 17, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "政治倾向": {"count": 18, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "家庭成员": {"count": 20, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "工资数额": {"count": 20, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "投资产品": {"count": 17, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "税务记录": {"count": 22, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "信用记录": {"count": 24, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "实体资产": {"count": 17, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "交易信息": {"count": 18, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "过敏信息": {"count": 19, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "生育信息": {"count": 12, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "行程信息": {"count": 19, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}}, "entity_position_analysis": {"姓名": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "地理位置": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "职业": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "医疗程序": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "疾病": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "药物": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "临床表现": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "国籍": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "民族": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "教育背景": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "年龄": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "性别": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "婚姻状况": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "政治倾向": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "家庭成员": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "工资数额": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "投资产品": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "税务记录": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "信用记录": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "实体资产": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "交易信息": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "过敏信息": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "生育信息": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "行程信息": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}}, "entity_density_analysis": {"mean_density": 0.05947085603624622, "median_density": 0.05555555555555555, "std_density": 0.031099362658011546, "max_density": 0.25}, "entity_overlap_analysis": {"total_overlaps": 0, "overlap_rate": 0.0, "overlap_details": []}}, "linguistic_analysis": {"vocabulary_analysis": {"total_words": 5682, "unique_words": 2301, "vocabulary_diversity": 0.40496304118268217, "total_chars": 10133, "unique_chars": 1417, "most_common_words": [["，", 506], ["。", 238], ["的", 169], ["了", 50], ["、", 50], ["和", 49], ["在", 46], ["：", 41], ["年", 39], ["（", 36], ["）", 33], ["月", 31], ["是", 29], ["出生", 29], ["无", 27], ["我", 26], ["记录", 26], ["元", 25], ["中国", 24], ["汉族", 24]], "word_length_distribution": {"mean_word_length": 1.7833509327701513, "median_word_length": 2.0, "std_word_length": 0.9629081002598038, "word_length_distribution": {"single_char": 0.4188665962689194, "two_chars": 0.4498416050686378, "three_chars": 0.08289334741288279, "four_plus_chars": 0.048398451249560014}}}, "sentence_structure_analysis": {"mean_sentence_length": 18.50814332247557, "median_sentence_length": 17.0, "std_sentence_length": 8.490410310272859, "sentence_length_distribution": {"short": 0.09120521172638436, "medium": 0.5472312703583062, "long": 0.36156351791530944}}, "punctuation_analysis": {"total_punctuation": 950, "punctuation_diversity": 12, "punctuation_distribution": {"。": 238, "，": 506, "、": 50, "；": 6, "：": 41, "（": 36, "）": 33, "《": 8, "》": 8, "？": 20, "【": 2, "】": 2}}, "word_frequency_analysis": {}}, "diversity_analysis": {"lexical_diversity": {"type_token_ratio": 0.40496304118268217, "hapax_legomena_ratio": 0.7001303780964798, "vocabulary_richness": 897.342029642272}, "syntactic_diversity": {"unique_patterns": 17, "pattern_diversity": 0.05537459283387622, "most_common_patterns": [["LONG_COMMA_END_PUNCT", 120], ["MEDIUM_COMMA_END_PUNCT", 63], ["MEDIUM_END_PUNCT", 29], ["LONG_COMMA", 22], ["LONG_COMMA_END_PUNCT_MID_PUNCT", 20], ["LONG_END_PUNCT", 10], ["SHORT_COMMA", 10], ["MEDIUM_COMMA_MID_PUNCT", 7], ["MEDIUM_COMMA", 6], ["MEDIUM_COMMA_END_PUNCT_MID_PUNCT", 5]]}, "semantic_diversity": {}, "entity_context_diversity": {"姓名": {"total_contexts": 16, "unique_contexts": 11, "context_diversity": 0.6875}, "地理位置": {"total_contexts": 45, "unique_contexts": 31, "context_diversity": 0.6888888888888889}, "职业": {"total_contexts": 41, "unique_contexts": 36, "context_diversity": 0.8780487804878049}, "医疗程序": {"total_contexts": 27, "unique_contexts": 11, "context_diversity": 0.4074074074074074}, "疾病": {"total_contexts": 34, "unique_contexts": 15, "context_diversity": 0.4411764705882353}, "药物": {"total_contexts": 12, "unique_contexts": 6, "context_diversity": 0.5}, "临床表现": {"total_contexts": 21, "unique_contexts": 12, "context_diversity": 0.5714285714285714}, "国籍": {"total_contexts": 22, "unique_contexts": 22, "context_diversity": 1.0}, "民族": {"total_contexts": 26, "unique_contexts": 23, "context_diversity": 0.8846153846153846}, "教育背景": {"total_contexts": 26, "unique_contexts": 21, "context_diversity": 0.8076923076923077}, "年龄": {"total_contexts": 18, "unique_contexts": 10, "context_diversity": 0.5555555555555556}, "性别": {"total_contexts": 20, "unique_contexts": 10, "context_diversity": 0.5}, "婚姻状况": {"total_contexts": 17, "unique_contexts": 10, "context_diversity": 0.5882352941176471}, "政治倾向": {"total_contexts": 18, "unique_contexts": 10, "context_diversity": 0.5555555555555556}, "家庭成员": {"total_contexts": 20, "unique_contexts": 10, "context_diversity": 0.5}, "工资数额": {"total_contexts": 20, "unique_contexts": 10, "context_diversity": 0.5}, "投资产品": {"total_contexts": 17, "unique_contexts": 10, "context_diversity": 0.5882352941176471}, "税务记录": {"total_contexts": 22, "unique_contexts": 10, "context_diversity": 0.45454545454545453}, "信用记录": {"total_contexts": 24, "unique_contexts": 10, "context_diversity": 0.4166666666666667}, "实体资产": {"total_contexts": 17, "unique_contexts": 10, "context_diversity": 0.5882352941176471}, "交易信息": {"total_contexts": 18, "unique_contexts": 10, "context_diversity": 0.5555555555555556}, "过敏信息": {"total_contexts": 19, "unique_contexts": 10, "context_diversity": 0.5263157894736842}, "生育信息": {"total_contexts": 12, "unique_contexts": 9, "context_diversity": 0.75}, "行程信息": {"total_contexts": 19, "unique_contexts": 10, "context_diversity": 0.5263157894736842}}}, "quality_indicators": {"annotation_quality": {"total_annotations": 531, "valid_annotations": 0, "boundary_accuracy": 0.0, "boundary_errors": 531, "type_consistency_errors": 0, "type_consistency_rate": 1.0}, "text_quality": {"mean_quality_score": 0.9713355048859936, "median_quality_score": 1.0, "std_quality_score": 0.05729381508981918, "quality_distribution": {"high": 0.993485342019544, "medium": 0.006514657980456026, "low": 0.0}}, "consistency_indicators": {}}}