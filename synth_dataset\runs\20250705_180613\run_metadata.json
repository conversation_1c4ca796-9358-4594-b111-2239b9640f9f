{"timestamp": "20250705_180613", "start_time": "2025-07-05T18:06:13.730978", "status": "failed", "config": {"dataset_path": "format-dataset/privacy_bench_small.json", "target_count": 50, "max_iterations": 3, "batch_size": 10, "distribution_threshold": 0.05}, "directories": {"root": "synth_dataset\\runs\\20250705_180613", "config": "synth_dataset\\runs\\20250705_180613\\config", "source": "synth_dataset\\runs\\20250705_180613\\source", "intermediate": "synth_dataset\\runs\\20250705_180613\\intermediate", "intermediate_raw": "synth_dataset\\runs\\20250705_180613\\intermediate\\raw", "intermediate_processed": "synth_dataset\\runs\\20250705_180613\\intermediate\\processed", "intermediate_by_entity": "synth_dataset\\runs\\20250705_180613\\intermediate\\by_entity", "iterations": "synth_dataset\\runs\\20250705_180613\\iterations", "strategies": "synth_dataset\\runs\\20250705_180613\\strategies", "output": "synth_dataset\\runs\\20250705_180613\\output", "evaluation": "synth_dataset\\runs\\20250705_180613\\evaluation", "logs": "synth_dataset\\runs\\20250705_180613\\logs"}, "last_updated": "2025-07-05T18:06:14.100785", "stage": "iteration", "current_iteration": 1, "max_iterations": 3, "error": "[Errno 2] No such file or directory: 'synth_dataset\\\\runs\\\\20250705_180613\\\\strategies\\\\sen_diversity\\\\sen_diversify_value.json'"}