"""
Store long strings that serves as part of GPT completion prompt
"""

from stefutil import ca


__all__ = [
    # 'job_desc_schema',
    # 'conll2003_no_misc_defn', 'conll2003_no_misc_defn2', 'conll2003_no_misc_defn3',
    # 'conll2003_no_misc_schema', 'conll2003_no_misc_schema2', 'conll2003_no_misc_schema3', 'conll2003_no_misc_schema4',
    # 'conll2003_no_misc_schema5'
    'Dataset2Schema'
]


job_desc_schema = """- **Skills**
    - Examples: 'computer programming', 'French', 'data analysis', 'Microsoft Word', 'leadership', 'unloading cargo', 'problem solving', 'honesty', 'graduate recruitment strategy'
    - They are tasks that can be performed, or the attributes and abilities that enable people to perform tasks
    - Includes descriptions of tasks (e.g. 'unloading cargo')
    - Includes both domain-specific 'hard skills' and domain-general 'soft skills'
    - Includes specific knowledge (e.g. 'understanding of marketing strategies')
    - May be validated with a qualification or experience
- **Qualifications**
    - Examples: 'Bachelor's Degree', 'chartership', 'National Pool Lifeguard Qualification', 'three A-levels'
    - They are official certifications obtained through taking a course or passing an exam or an appraisal
    - Includes driving licenses and security clearance
- **Experience**
    - Examples: '2 years experience', 'minimum of 5 years experience'
    - They are quantified by length of time
    - Does not include what the experience is of or in - for example, in the sentence 'this job requires at least 10 years of experience as a CEO', only the words 'at least 10 years of experience' are the Experience
- **Occupations**
    - Examples: 'Teaching Assistant', 'CEO', 'Data Analyst', 'Chef de partie'
    - These are job titles
    - Includes abbreviations - for example, both 'Chief Executive Officer' and 'CEO' are Occupations
- **Domains**
    - Examples: 'aerospace', 'oil industry', 'education', 'human resources'
    - These are areas of industry in which someone might have knowledge or experience
"""

conll2003_no_misc_defn = """1. **Person (`person`)**:
    - **Definition**: Refers to the name of an individual. It can be the full name, first name, surname, or even a nickname as long as it clearly indicates a specific individual.
    - **Examples**: [Peter Blackburn, Werner Zwingmann, Nikolaus van der Pas, Fischler, Hendrix]

2. **Location (`location`)**:
    - **Definition**: Refers to the name of a geographical place. It can be a city, a country, a specific address, a landmark, or any other identifiable location.
    - **Examples**: [BRUSSELS, Germany, Britain, France]

3. **Organization (`organization`)**:
    - **Definition**: Refers to the name of any corporate or collective entity. It could be the name of companies, agencies, institutions, associations, government bodies, or other groups.
    - **Examples**: [EU, European Commission, European Union, Commission]"""


conll2003_no_misc_defn2 = """Person: This category encompasses names of individuals. Entities annotated as a person can include full names, last names when they appear alone, or even titles if they unambiguously refer to specific individuals. In a dataset, it's crucial that only references to actual people (not characters or roles) are annotated as such.
Location: This includes the names of geographical entities such as cities, countries, regions, bodies of water, mountains, and other identifiable areas of land or water. Annotations for locations are typically clear when the text refers to a place that can be located on a map.
Organization: This category includes entities that are groups or collections of people. These can be companies, governmental entities, non-profits, sports teams, or any other kind of organization. It may also include ad hoc groups, like a government coalition, that are recognized by name."""


# Generated by GPT4 zero-shot
conll2003_no_misc_schema = """### 1. Person

**Definition:** Refers to names of individual people. This includes full names, first names, last names, or even nicknames if they specifically refer to an individual.

**Examples:**

- "Peter Blackburn" is a person name.
- "Nikolaus van der Pas" is a person name.
- "Hendrix" can be considered a person name if it's clear in the context that it refers to a specific individual (e.g., Jimi Hendrix).

**Non-examples:**

- Names of organizations, even if they have personal names in them (e.g., "Johnson & Johnson" would not be labeled as a person).

### 2. Location

**Definition:** Refers to names of geographical places. This includes countries, cities, towns, villages, continents, regions, and other geographical landmarks. It does not include addresses or buildings unless the building's name is synonymous with a location (e.g., "The Pentagon").

**Examples:**

- "BRUSSELS" refers to a city, thus a location.
- "Germany" and "Britain" are names of countries, thus locations.

**Non-examples:**

- Names of organizations or events, even if they sound like locations (e.g., "European Union" is not a location).

### 3. Organization

**Definition:** Refers to names of formal groups, institutions, companies, unions, and other collective entities. This can be governmental, non-governmental, profit, non-profit, or any other form of organized group.

**Examples:**

- "EU" or "European Union" refers to a collective body of countries, thus an organization.
- "European Commission" is a branch of the EU, thus an organization.

**Non-examples:**

- Names of individual people (e.g., "Peter Blackburn" is not an organization).
- Names of locations (e.g., "France" is not an organization)."""

# Generated by GPT4 1-shot
conll2003_no_misc_schema2 = """- **Person**
    - **Examples**: 'Peter Blackburn', 'Nikolaus van der Pas', 'Hendrix', 'Fischler'
    - Persons are individual human beings, whether living or fictional.
    - This category captures both full names (e.g., 'Peter Blackburn') and surnames (e.g., 'Fischler') when the surname is used to refer to a known individual.
    - Includes titles if they are an inseparable part of the person's recognition, e.g., 'Sir Ian McKellen'.
    - Does not include generic roles or professions unless they are used as a title for a specific person (e.g., 'The Doctor' when referring to the character from Doctor Who).

- **Location**
    - **Examples**: 'BRUSSELS', 'Germany', 'Britain', 'France'
    - Locations can be countries, cities, towns, villages, and other named geographical places.
    - They can also be more generic places like 'the beach' or 'downtown' when they refer to a specific known location in context.
    - Landmarks, named buildings, or areas can also be tagged as locations.
    - It does not include addresses, but if a specific building or landmark is mentioned, it will be tagged.

- **Organization**
    - **Examples**: 'EU', 'European Commission', 'European Union', 'Commission'
    - Organizations include named corporate, governmental, non-governmental, and other institutional entities.
    - It can also include groups, teams, committees, or any other collective of individuals with a formal or widely recognized structure.
    - The name of an organization might be its full name, abbreviation, or any other form that refers to the organization unambiguously in the given context.
    - When a division or department of an organization is mentioned, it will also be tagged under this category."""

# Generated by GPT4 zero-shot + more detailed instruction
conll2003_no_misc_schema3 = """### 1. **Person**

    - **Definition**: Refers to a named individual or group of individuals.
    - **Examples**: 'Peter Blackburn', 'Werner Zwingmann', 'Nikolaus van der Pas', 'Hendrix', 'Fischler'.
    - **Characteristics**:
        - Typically refers to individual human beings, but can also refer to pseudonyms or pen names.
        - Might have titles attached to them, like 'Dr.', 'Mr.', 'Mrs.', but the core identifier is the name.
        - Could be first names, last names, or both.
        - Can occasionally refer to fictional or mythological characters if mentioned in the context.
    - **Exclusions**:
        - Names of companies, organizations, or places.
        - Generic terms that refer to a group but aren't specific names (e.g., 'people', 'consumers').

### 2. **Location**

    - **Definition**: Represents a geographic area or place, whether it be a country, city, town, village, or even specific landmarks.
    - **Examples**: 'BRUSSELS', 'Germany', 'Britain', 'France'.
    - **Characteristics**:
        - Can range from continents to neighborhoods or landmarks.
        - Could include political entities, states, provinces, and regions.
        - Names of planets, galaxies, or celestial bodies also fall into this category if mentioned.
    - **Exclusions**:
        - Non-specific terms that indicate direction or position but aren't places themselves (e.g., 'abroad', 'overseas').
        - Locations that are only referred to by a descriptor without their proper name (e.g., 'the city', 'that country').

### 3. **Organization**

    - **Definition**: Refers to a collection of people who come together to achieve a common purpose or goal. This includes companies, institutions, committees, or any formal and informal groups.
    - **Examples**: 'EU', 'European Commission', 'European Union', 'Commission'.
    - **Characteristics**:
        - Can be for-profit companies, non-profit organizations, governmental bodies, committees, or unions.
        - Might have acronyms or abbreviations that are commonly recognized (e.g., 'EU' for 'European Union').
        - Can be multinational, national, or local.
        - Organizations can be public or private, big or small.
    - **Exclusions**:
        - Non-specific, generic references to groups of people without a formal organizational context (e.g., 'crowd', 'team' without specifying which team)."""


# after 5-shot demo change
conll2003_no_misc_schema4 = """#### 1. Class: Person

**Definition:** This class represents names of individual people.

**Features:**
- May contain first name, middle name, last name, or any combination of these.
- Can include titles, initials, or suffixes when they are part of the person's commonly recognized name.
- May have non-standard characters or apostrophes, e.g., "O'Meara".

**Examples:**
- "Julius Nyrere"
- "Nsanze Terence"
- "Mark O'Meara"

---

#### 2. Class: Location

**Definition:** This class represents names of geographical places or areas.

**Features:**
- Can be countries, cities, regions, or any specific geographical location.
- May refer to commonly recognized regions or amalgamations of cities.
- Does not include organizations or establishments located in a place (e.g., "U.N. Security Council" is an organization, not a location).

**Examples:**
- "Saudi Arabia"
- "Burundi"
- "Amsterdam-Rotterdam-Antwerp"

---

#### 3. Class: Organization

**Definition:** This class represents names of companies, teams, institutions, government bodies, or any other organized group.

**Features:**
- Can be official names or commonly recognized abbreviations.
- Can be names of sports teams, government bodies, companies, or other organized entities.
- May sometimes look like standard words but should be annotated if they refer to a specific organization in context.

**Examples:**
- "Mouscron" (when referring to a sports team)
- "DODGERS"
- "U.N. Security Council"
- "Interacciones"
- "Gloucestershire" (when referring to a sports team)"""


conll2003_no_misc_schema5 = """#### Person:
A `Person` entity refers to any individual's name or a group of individuals commonly referred to by a single name. This includes full names, last names when used alone, and any titles that are typically used in direct conjunction with a person's name. It does not include occupational titles used in isolation.

Examples of `Person` entities:
- Full name: John Smith
- Last name (if famous or contextually clear): Obama
- Titled name: President Lincoln
- Common name for a group: The Beatles

#### Location:
A `Location` entity is any geographical place name, ranging from small scale (like a street name) to large scale (such as continents). This includes bodies of water, mountains, cities, countries, and even celestial bodies if mentioned in a geographical context. It does not include locations that are part of an organization's name unless the location is being referred to in a geographical sense.

Examples of `Location` entities:
- Countries: France
- Cities: Tokyo
- Natural formations: Mount Everest
- Streets: Fifth Avenue
- Celestial: Mars (when referred to as a planet)

#### Organization:
An `Organization` entity is any named corporate, governmental, or other institutional body. It includes companies, government entities, sports teams, non-profits, and other organized groups. Names of organizations are often followed by legal identifiers like Inc., Ltd., or Corp., but those are not necessary for a name to be classified as an organization.

Examples of `Organization` entities:
- Companies: Google Inc.
- Government bodies: United Nations
- Sports teams: Los Angeles Lakers
- Non-profits: Red Cross
- Bands: Coldplay"""


# after 1-shot demo update; from GPT4, generated once
conll2003_no_misc_defn3 = """1. **Person:** Refers to individual human beings. The annotation should identify names of people, which may include first names, last names, or full names. Titles or honorifics attached to a person's name (like "Dr.", "Mr.", "Mrs.", etc.) can also be included in this category if they appear with the name.

2. **Location:** This category encompasses geographical entities. It can include names of countries, cities, regions, specific addresses, buildings, rivers, mountains, and other identifiable physical locations. It does not include generic places without specific names (like "a mountain" without a proper name)

3. **Organization:** This class is for entities that represent groups, institutions, companies, agencies, teams, and other organized bodies. Organizations can range from small local entities (like a local restaurant) to large international corporations or government agencies. Sports teams, political parties, non-profits, and academic institutions also fall under this category."""


mit_movie_defn = """1. **Title**: Refers to the name of a movie. This entity should capture the complete title as used in the movie industry.

2. **Year**: This entity represents the year a movie was released. It should be a four-digit number indicating the release year.

3. **Genre**: This refers to the category or type of movie, such as action, comedy, drama, etc. The genre might be explicitly mentioned in the query or implied.

4. **Director**: This entity is the name of the person who directed the movie. The name can be first name, last name, or full name.

5. **MPAA Rating**: The Motion Picture Association of America (MPAA) rating of the movie. This includes ratings like G, PG, PG-13, R, and NC-17.

6. **Plot**: Keywords or phrases that describe the plot or a significant element of the story in the movie.

7. **Actor**: This entity is the name of an actor or actress appearing in the movie. Names can be first, last, or full.

8. **Trailer**: Refers to a part or segment of the movie typically used in promotional material. It can be a specific scene or a general reference to the trailer.

9. **Song**: This entity captures the title of a song featured in a movie.

10. **Review**: Keywords or phrases that indicate an opinion or review about the movie.

11. **Character**: This entity refers to a character's name in a movie.

12. **Viewers' Rating**: This includes phrases or terms that indicate how viewers have rated or perceived the movie. It can be formal ratings or informal descriptors."""


privacy_bench_defn = """1. **姓名**: 指个人的全名、姓氏或名字。这包括真实姓名、笔名、艺名等任何用于标识特定个人的名称。

2. **身份证号**: 指中国居民身份证号码，通常是18位数字，其中包含出生日期和地区编码信息。

3. **手机号**: 指个人的移动电话号码，在中国通常是11位数字，以1开头。

4. **电子邮箱**: 指个人的电子邮件地址，通常包含@符号和域名。

5. **地理位置**: 指特定的地理区域，如国家、省份、城市、区县、街道等。

6. **组织机构**: 指公司、政府机构、学校等组织实体的名称。

7. **职业**: 指个人的职业或职位名称，如医生、教师、工程师等。

8. **教育背景**: 指个人的学历、学位、毕业院校等教育相关信息。

9. **政治倾向**: 指个人的政治立场、党派归属或政治观点。

10. **宗教信仰**: 指个人的宗教信仰或宗教活动参与情况。

11. **种族民族**: 指个人的种族或民族身份。

12. **家庭成员**: 指与个人有亲属关系的人员信息，如父母、配偶、子女等。

13. **银行卡号**: 指个人的银行卡或信用卡号码。

14. **账号密码**: 指个人在各类系统或平台中使用的账号和密码信息。

15. **社保号码**: 指个人的社会保障号码或社会保险号码。

16. **车牌号码**: 指个人拥有或使用的机动车辆的牌照号码。

17. **网络标识**: 指个人在互联网上使用的唯一标识，如IP地址、MAC地址、设备ID等。

18. **投资产品**: 指个人持有的股票、基金、债券等金融投资产品信息。

19. **税务记录**: 指个人的纳税信息、税号或税务申报记录。

20. **信用记录**: 指个人的信用评分、信用历史或债务情况。

21. **物理资产**: 指个人拥有的房产、车辆等有形资产信息。

22. **交易信息**: 指个人的消费记录、转账记录等金融交易信息。

23. **健康状况**: 指个人的疾病、伤残、医疗记录等健康相关信息。

24. **过敏信息**: 指个人对药物、食物或环境因素的过敏反应信息。

25. **临床表现**: 指个人的症状、体征等临床医学表现。

26. **医疗程序**: 指个人接受的手术、治疗、检查等医疗过程。

27. **生育信息**: 指个人的生育计划、怀孕、分娩等生殖健康信息。

28. **旅行信息**: 指个人的出行记录、行程安排、目的地等旅行相关信息。

29. **生物识别**: 指个人的指纹、面部特征、虹膜等生物特征数据。"""


privacy_bench_schema = """#### 1. 类别: 姓名

**定义:** 此类别表示个人的姓名。

**特征:**
- 可能包含姓氏、名字或全名。
- 可能包含头衔、称谓或后缀。
- 可能包含非标准字符或特殊符号。

**示例:**
- "张三"
- "李明"
- "王小红"

---

#### 2. 类别: 身份证号

**定义:** 此类别表示中国居民身份证号码。

**特征:**
- 通常为18位数字。
- 包含出生日期信息。
- 包含地区编码信息。

**示例:**
- "110101199003077890"
- "320123199801012345"
- "440301200001015678"

---

#### 3. 类别: 手机号

**定义:** 此类别表示个人的移动电话号码。

**特征:**
- 在中国通常为11位数字。
- 通常以1开头。
- 可能包含分隔符如空格或连字符。

**示例:**
- "13812345678"
- "139 8765 4321"
- "158-1234-5678"

---

#### 4. 类别: 电子邮箱

**定义:** 此类别表示个人的电子邮件地址。

**特征:**
- 包含@符号。
- 包含域名部分。
- 可能包含点号、下划线等特殊字符。

**示例:**
- "<EMAIL>"
- "<EMAIL>"
- "<EMAIL>"

---

#### 5. 类别: 地理位置

**定义:** 此类别表示特定的地理区域或地点。

**特征:**
- 可以是国家、省份、城市、区县、街道等。
- 可以是具体的地址或地标。
- 不包括组织机构名称。

**示例:**
- "北京市海淀区"
- "上海市浦东新区"
- "广东省深圳市南山区科技园"

---

#### 6. 类别: 组织机构

**定义:** 此类别表示公司、政府机构、学校等组织实体的名称。

**特征:**
- 可以是官方名称或常用缩写。
- 可以是企业、政府部门、教育机构等。
- 可能包含地理位置信息。

**示例:**
- "中国移动"
- "北京大学"
- "国家税务总局"

---

#### 7. 类别: 职业

**定义:** 此类别表示个人的职业或职位名称。

**特征:**
- 描述个人从事的工作类型。
- 可以是具体职位或职业类别。
- 不包括组织机构名称。

**示例:**
- "软件工程师"
- "医生"
- "教师"

---

#### 8. 类别: 教育背景

**定义:** 此类别表示个人的学历、学位、毕业院校等教育相关信息。

**特征:**
- 可以包含学历层次。
- 可以包含专业名称。
- 可以包含教育机构名称。

**示例:**
- "本科学历"
- "计算机科学硕士"
- "北京大学法学院毕业"

---

#### 9. 类别: 银行卡号

**定义:** 此类别表示个人的银行卡或信用卡号码。

**特征:**
- 通常为16-19位数字。
- 可能包含分隔符如空格或连字符。
- 可能部分隐藏，如显示前四位和后四位。

**示例:**
- "6225 7654 3210 9876"
- "4000123456789010"
- "621700 **** **** 3456"

---

#### 10. 类别: 健康状况

**定义:** 此类别表示个人的疾病、伤残、医疗记录等健康相关信息。

**特征:**
- 描述个人的健康问题或状况。
- 可以包含疾病名称。
- 可以包含健康状态描述。

**示例:**
- "糖尿病"
- "高血压"
- "心脏病史"""


dataset2schema = {
    'conll2003': dict(defn=conll2003_no_misc_defn3, schema=conll2003_no_misc_schema5),
    'mit-movie': dict(defn=mit_movie_defn, schema=None),
    'job-stack': dict(defn=None, schema=job_desc_schema),
    'privacy_bench': dict(defn=privacy_bench_defn, schema=privacy_bench_schema)
}


class Dataset2Schema:
    def __init__(self, dataset_name: str = 'conll2003', schema_type: str = 'defn'):
        self.dataset_name = dataset_name

        ca.assert_options(display_name='Dataset Entity Schema Kind', val=schema_type, options=['defn', 'schema'])
        self.schema_type = schema_type

        if dataset_name not in dataset2schema:
            raise NotImplementedError(f'No schema for dataset {dataset_name}')
        self.d = dataset2schema[dataset_name]

    def __call__(self):
        ret = self.d[self.schema_type]
        if ret is None:
            raise NotImplementedError
        return ret
