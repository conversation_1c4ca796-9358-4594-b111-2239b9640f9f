﻿#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RQ1: 数据集统计与分析
评估生成的数据集在统计特征上与原始数据集的差异

使用方法:
python evaluation/framework/rq1_dataset_statistics.py --original original.json --generated generated.json --output results/rq1/
"""

import os
import sys
import json
import argparse
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
from collections import defaultdict # Added for entity value diversity plots

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入评估指标
from evaluation.framework.metrics.statistical_metrics import (
    calculate_dataset_statistics,
    compare_distributions,
    statistical_significance_test,
    calculate_entity_density_metrics
)

def load_config():
    """加载RQ1配置"""
    config_path = Path(__file__).parent / "configs" / "rq1_config.json"
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def load_dataset(dataset_path: str) -> List[Dict]:
    """加载数据集"""
    with open(dataset_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def numpy_to_python(obj):
    """将NumPy类型转换为Python原生类型"""
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, np.bool_):
        return bool(obj)
    elif isinstance(obj, dict):
        return {k: numpy_to_python(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [numpy_to_python(item) for item in obj]
    return obj

def convert_stats_to_serializable(stats: Dict) -> Dict:
    """将统计结果转换为可序列化的格式"""
    return numpy_to_python(stats)

def load_target_config():
    """加载目标配置"""
    config_path = os.path.join(project_root, "src", "gen_strat", "balance_config.json")
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def create_entity_type_distribution_plot(original_stats: Dict, generated_stats: Dict, target_config: Dict, output_dir: Path):
    """创建实体类型分布对比图"""
    plt.figure(figsize=(15, 8))
    
    # 获取数据
    orig_entity_dist = original_stats["entity_density"]["entity_type_distribution"]
    gen_entity_dist = generated_stats["entity_density"]["entity_type_distribution"]
    target_dist = target_config["entity_type_targets"]
    
    # 获取所有实体类型
    all_types = sorted(set(orig_entity_dist.keys()) | set(gen_entity_dist.keys()) | set(target_dist.keys()))
    
    # 准备数据
    x = np.arange(len(all_types))
    width = 0.35
    
    # 绘制柱状图
    plt.bar(x - width/2, [orig_entity_dist.get(t, 0) for t in all_types], width, 
            label='原始数据集', alpha=0.8, color='lightblue')
    plt.bar(x + width/2, [gen_entity_dist.get(t, 0) for t in all_types], width, 
            label='生成数据集', alpha=0.8, color='lightcoral')
    
    # 绘制目标值虚线
    plt.plot(x, [target_dist.get(t, 0) for t in all_types], 'k--', 
            label='目标分布', linewidth=2, marker='o', markersize=6)
    
    # 设置图表属性
    plt.xlabel('实体类型', fontsize=12, fontweight='bold')
    plt.ylabel('实体数量', fontsize=12, fontweight='bold')
    plt.title('实体类型分布对比', fontsize=14, fontweight='bold')
    plt.xticks(x, all_types, rotation=45, ha='right')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    plt.savefig(output_dir / "entity_type_distribution.png", dpi=300, bbox_inches='tight')
    plt.close()
    
def create_sentence_length_distribution_plot(original_stats: Dict, generated_stats: Dict, output_dir: Path):
    """创建句子长度分布对比图"""
    plt.figure(figsize=(12, 6))
    
    # 绘制直方图
    plt.hist(original_stats["sentence_lengths"], bins=30, alpha=0.7, 
            label='原始数据集', density=True, color='lightblue')
    plt.hist(generated_stats["sentence_lengths"], bins=30, alpha=0.7, 
            label='生成数据集', density=True, color='lightcoral')
    
    # 设置图表属性
    plt.xlabel('句子长度', fontsize=12, fontweight='bold')
    plt.ylabel('密度', fontsize=12, fontweight='bold')
    plt.title('句子长度分布对比', fontsize=14, fontweight='bold')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    plt.savefig(output_dir / "sentence_length_distribution.png", dpi=300, bbox_inches='tight')
    plt.close()

def create_entity_value_diversity_plots(original_dataset: List[Dict], generated_dataset: List[Dict], output_dir: Path):
    """创建实体值多样性分析图 - 比较同一实体类型下实体值的多样性程度"""
    if not original_dataset or not generated_dataset:
        print("[警告] 数据集为空，跳过实体值多样性分析图的生成")
        return
    
    print(f"[信息] 开始处理数据集，原始数据集 {len(original_dataset)} 条记录，生成数据集 {len(generated_dataset)} 条记录")
    
    def extract_entity_values(dataset: List[Dict]) -> Dict[str, set]:
        """从数据集中提取每种实体类型的唯一值集合"""
        entity_values = defaultdict(set)
        
        print(f"[调试] 开始提取实体值，数据集大小: {len(dataset)}")
        
        for idx, item in enumerate(dataset):
            if not isinstance(item, dict):
                print(f"[调试] 第 {idx} 条记录不是字典类型: {type(item)}")
                continue
            
            # 检查数据结构
            if idx < 3:  # 只打印前3条记录的结构
                print(f"[调试] 第 {idx} 条记录结构: {list(item.keys())}")
            
            # 尝试多种可能的字段名
            labels = None
            if "labels" in item:
                labels = item["labels"]
                if idx < 3:
                    print(f"[调试] 找到 'labels' 字段，内容: {labels}")
            elif "label" in item:
                labels = item["label"]
                if idx < 3:
                    print(f"[调试] 找到 'label' 字段，内容: {labels}")
            elif "entities" in item:
                labels = item["entities"]
                if idx < 3:
                    print(f"[调试] 找到 'entities' 字段，内容: {labels}")
            elif "ner_tags" in item:
                labels = item["ner_tags"]
                if idx < 3:
                    print(f"[调试] 找到 'ner_tags' 字段，内容: {labels}")
            
            if not labels:
                if idx < 3:
                    print(f"[调试] 第 {idx} 条记录没有找到标签字段")
                continue
            
            # 处理标签数据
            if isinstance(labels, list):
                for label in labels:
                    if not isinstance(label, dict):
                        if idx < 3:
                            print(f"[调试] 标签不是字典类型: {type(label)}, 内容: {label}")
                        continue
                    
                    # 尝试多种可能的字段名
                    entity_type = None
                    entity_value = None
                    
                    # 检查实体类型字段
                    if "type" in label:
                        entity_type = label["type"]
                    elif "entity_type" in label:
                        entity_type = label["entity_type"]
                    elif "category" in label:
                        entity_type = label["category"]
                    
                    # 检查实体值字段
                    if "text" in label:
                        entity_value = label["text"]
                    elif "value" in label:
                        entity_value = label["value"]
                    elif "entity" in label:
                        entity_value = label["entity"]
                    elif "word" in label:
                        entity_value = label["word"]
                    
                    # 处理数据
                    if entity_type and entity_value:
                        entity_type = str(entity_type).strip()
                        entity_value = str(entity_value).strip()
                        
                        if entity_type and entity_value:
                            entity_values[entity_type].add(entity_value)
                            if idx < 3:
                                print(f"[调试] 添加实体: 类型='{entity_type}', 值='{entity_value}'")
            
            elif isinstance(labels, dict):
                # 如果是字典格式，直接处理
                for entity_type, entity_value in labels.items():
                    if entity_type and entity_value:
                        entity_type = str(entity_type).strip()
                        entity_value = str(entity_value).strip()
                        
                        if entity_type and entity_value:
                            entity_values[entity_type].add(entity_value)
                            if idx < 3:
                                print(f"[调试] 添加实体: 类型='{entity_type}', 值='{entity_value}'")
        
        print(f"[调试] 提取完成，找到 {len(entity_values)} 种实体类型")
        for entity_type, values in entity_values.items():
            print(f"[调试] {entity_type}: {len(values)} 个唯一值")
        
        return dict(entity_values)
    
    # 提取两个数据集的实体值
    print("\n[调试] 处理原始数据集...")
    orig_entity_values = extract_entity_values(original_dataset)
    
    print("\n[调试] 处理生成数据集...")
    gen_entity_values = extract_entity_values(generated_dataset)
    
    # 获取所有实体类型
    all_entity_types = sorted(set(orig_entity_values.keys()) | set(gen_entity_values.keys()))
    
    print(f"\n[信息] 发现的实体类型数量: {len(all_entity_types)}")
    for entity_type in all_entity_types:
        orig_count = len(orig_entity_values.get(entity_type, set()))
        gen_count = len(gen_entity_values.get(entity_type, set()))
        print(f"  - {entity_type}: 原始数据集 {orig_count} 个唯一值，生成数据集 {gen_count} 个唯一值")
    
    if not all_entity_types:
        print("[警告] 未找到有效的实体数据，跳过实体值多样性分析图的生成")
        return
    
    try:
        # 准备数据
        orig_unique_counts = [len(orig_entity_values.get(et, set())) for et in all_entity_types]
        gen_unique_counts = [len(gen_entity_values.get(et, set())) for et in all_entity_types]
        
        # 计算多样性比率（唯一值数量/总实体数量）
        def calculate_diversity_ratio(entity_values: Dict[str, set], dataset: List[Dict]) -> List[float]:
            """计算每种实体类型的多样性比率"""
            ratios = []
            for entity_type in all_entity_types:
                unique_count = len(entity_values.get(entity_type, set()))
                total_count = 0
                
                # 统计该实体类型的总出现次数
                for item in dataset:
                    labels = item.get("labels", [])
                    if not labels and "label" in item:
                        labels = item["label"]
                    elif not labels and "entities" in item:
                        labels = item["entities"]
                    elif not labels and "ner_tags" in item:
                        labels = item["ner_tags"]
                    
                    if isinstance(labels, list):
                        for label in labels:
                            if isinstance(label, dict):
                                label_type = label.get("type") or label.get("entity_type") or label.get("category")
                                if str(label_type).strip() == entity_type:
                                    total_count += 1
                    elif isinstance(labels, dict):
                        if entity_type in labels:
                            total_count += 1
                
                ratio = unique_count / total_count if total_count > 0 else 0
                ratios.append(ratio)
            
            return ratios
        
        orig_diversity_ratios = calculate_diversity_ratio(orig_entity_values, original_dataset)
        gen_diversity_ratios = calculate_diversity_ratio(gen_entity_values, generated_dataset)
        
        # 创建图表
        fig = plt.figure(figsize=(15, 12))
        
        # 1. 上半部分：唯一值数量对比
        ax1 = plt.subplot(2, 1, 1)
        x = np.arange(len(all_entity_types))
        width = 0.35
        
        # 绘制条形图
        bars1 = ax1.bar(x - width/2, orig_unique_counts, width, label='原始数据集', color='#66B2FF', alpha=0.8)
        bars2 = ax1.bar(x + width/2, gen_unique_counts, width, label='生成数据集', color='#FF9999', alpha=0.8)
        
        # 添加数值标签
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height,
                        f'{int(height)}', ha='center', va='bottom')
        
        # 设置图表属性
        ax1.set_title('实体值唯一性对比 (每种实体类型的唯一值数量)', fontsize=14, fontweight='bold', pad=15)
        ax1.set_xlabel('实体类型', fontsize=12)
        ax1.set_ylabel('唯一值数量', fontsize=12)
        ax1.set_xticks(x)
        ax1.set_xticklabels(all_entity_types, rotation=45, ha='right')
        ax1.legend(loc='upper right')
        ax1.grid(True, alpha=0.3, axis='y')
        
        # 2. 下半部分：多样性比率对比
        ax2 = plt.subplot(2, 1, 2)
        bars3 = ax2.bar(x - width/2, orig_diversity_ratios, width, label='原始数据集', color='#66B2FF', alpha=0.8)
        bars4 = ax2.bar(x + width/2, gen_diversity_ratios, width, label='生成数据集', color='#FF9999', alpha=0.8)
        
        # 添加比率标签
        for bars in [bars3, bars4]:
            for bar in bars:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height,
                        f'{height:.1%}', ha='center', va='bottom')
        
        # 设置图表属性
        ax2.set_title('实体值多样性比率对比 (唯一值数量/总出现次数)', fontsize=14, fontweight='bold', pad=15)
        ax2.set_xlabel('实体类型', fontsize=12)
        ax2.set_ylabel('多样性比率', fontsize=12)
        ax2.set_xticks(x)
        ax2.set_xticklabels(all_entity_types, rotation=45, ha='right')
        ax2.legend(loc='upper right')
        ax2.grid(True, alpha=0.3, axis='y')
        ax2.set_ylim(0, 1.1)  # 比率最大为1
        
        # 添加统计信息
        stats_text = (
            f'统计信息:\n'
            f'实体类型总数: {len(all_entity_types)}\n'
            f'原始数据集平均多样性: {np.mean(orig_diversity_ratios):.1%}\n'
            f'生成数据集平均多样性: {np.mean(gen_diversity_ratios):.1%}\n'
            f'多样性提升: {np.mean(gen_diversity_ratios) - np.mean(orig_diversity_ratios):.1%}'
        )
        ax2.text(0.02, 0.98, stats_text,
                transform=ax2.transAxes, fontsize=10,
                verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        # 调整布局
        plt.tight_layout()
        plt.savefig(output_dir / "entity_value_diversity.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"[✓] 已生成实体值多样性分析图")
        print(f"    - 包含{len(all_entity_types)}种实体类型的对比")
        print(f"    - 原始数据集平均多样性: {np.mean(orig_diversity_ratios):.1%}")
        print(f"    - 生成数据集平均多样性: {np.mean(gen_diversity_ratios):.1%}")
            
    except Exception as e:
        print(f"[错误] 生成实体值多样性分析图时出错: {str(e)}")
        import traceback
        traceback.print_exc()

def create_comparison_table(comparison: Dict, target_config: Dict, output_dir: Path):
    """创建对比表格"""
    # 实体分布对比表
    entity_comparison = comparison["entity_distribution_comparison"]
    target_dist = target_config["entity_type_targets"]
    
    table_data = []
    for entity_type, data in entity_comparison.items():
        target_count = target_dist.get(entity_type, 0)
        final_count = int(data["generated_count"])
        target_diff = final_count - target_count
        
        # 计算目标值的允许偏差范围（±10%）
        tolerance = target_count * 0.1
        is_out_of_range = target_diff < 0 or abs(target_diff) > tolerance
        
        table_data.append({
            "实体类型": entity_type,
            "原始数量": int(data["original_count"]),
            "最终数量": final_count,
            "目标差值": target_diff,
            "需标红": is_out_of_range
        })
    
    # 保存为JSON格式
    with open(output_dir / "entity_comparison_table.json", 'w', encoding='utf-8') as f:
        json.dump(table_data, f, ensure_ascii=False, indent=2)
    
    # 创建可视化表格
    fig, ax = plt.subplots(figsize=(12, 8))
    ax.axis('tight')
    ax.axis('off')
    
    # 准备表格数据
    headers = ["实体类型", "原始数量", "最终数量", "目标差值"]
    cell_data = []
    cell_colors = []
    
    for row in table_data:
        cell_data.append([
            row["实体类型"],
            row["原始数量"],
            row["最终数量"],
            row["目标差值"]
        ])
        # 设置单元格颜色
        colors = ['white'] * 3  # 前三列使用白色
        # 目标差值列根据条件设置颜色
        colors.append('lightcoral' if row["需标红"] else 'white')
        cell_colors.append(colors)
    
    table = ax.table(cellText=cell_data, colLabels=headers, 
                    cellLoc='center', loc='center',
                    cellColours=cell_colors)
    
    # 设置表格样式
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 1.5)
    
    # 设置表头样式
    for i in range(len(headers)):
        table[(0, i)].set_facecolor('#40466e')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    plt.title('实体分布对比表', fontsize=14, fontweight='bold', pad=20)
    plt.savefig(output_dir / "entity_comparison_table.png", dpi=300, bbox_inches='tight')
    plt.close()

def create_distribution_plots(original_stats: Dict, generated_stats: Dict, comparison: Dict, output_dir: Path):
    """创建所有分布对比图"""
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    try:
        # 加载目标配置
        target_config = load_target_config()
        
        # 1. 实体类型分布对比图
        print("\n生成实体类型分布对比图...")
        create_entity_type_distribution_plot(original_stats, generated_stats, target_config, output_dir)
        
        # 2. 句子长度分布对比图
        print("\n生成句子长度分布对比图...")
        create_sentence_length_distribution_plot(original_stats, generated_stats, output_dir)
        
        # 3. 实体值多样性分析图
        print("\n生成实体值多样性分析图...")
        create_entity_value_diversity_plots(original_stats.get("raw_data", []), generated_stats.get("raw_data", []), output_dir)
        
        # 4. 实体分布对比表
        print("\n生成实体分布对比表...")
        create_comparison_table(comparison, target_config, output_dir)
        
    except Exception as e:
        print(f"\n[错误] 生成分布图时出错: {str(e)}")
        import traceback
        traceback.print_exc()

def run_statistical_tests(original_stats: Dict, generated_stats: Dict, config: Dict) -> Dict[str, Any]:
    """运行统计显著性检验"""
    test_results = {}
    
    # 句子长度分布检验
    if config["rq1_dataset_statistics"]["metrics"]["sentence_length"]["enabled"]:
        tests = config["rq1_dataset_statistics"]["metrics"]["sentence_length"]["statistical_tests"]
        
        original_lengths = original_stats["sentence_lengths"]
        generated_lengths = generated_stats["sentence_lengths"]
        
        test_results["sentence_length_tests"] = {}
        for test_type in tests:
            try:
                result = statistical_significance_test(original_lengths, generated_lengths, test_type)
                test_results["sentence_length_tests"][test_type] = result
            except Exception as e:
                test_results["sentence_length_tests"][test_type] = {"error": str(e)}
    
    # 实体分布检验
    if config["rq1_dataset_statistics"]["metrics"]["entity_distribution"]["enabled"]:
        tests = config["rq1_dataset_statistics"]["metrics"]["entity_distribution"]["statistical_tests"]
        
        # 准备实体类型数据
        orig_entity_dist = original_stats["entity_density"]["entity_type_distribution"]
        gen_entity_dist = generated_stats["entity_density"]["entity_type_distribution"]
        
        all_types = list(set(orig_entity_dist.keys()) | set(gen_entity_dist.keys()))
        orig_counts = [orig_entity_dist.get(t, 0) for t in all_types]
        gen_counts = [gen_entity_dist.get(t, 0) for t in all_types]
        
        test_results["entity_distribution_tests"] = {}
        for test_type in tests:
            try:
                if test_type == "chi_square":
                    # 对于卡方检验，需要使用分类数据
                    result = statistical_significance_test(all_types * len(orig_counts), all_types * len(gen_counts), test_type)
                else:
                    result = statistical_significance_test(orig_counts, gen_counts, test_type)
                test_results["entity_distribution_tests"][test_type] = result
            except Exception as e:
                test_results["entity_distribution_tests"][test_type] = {"error": str(e)}
    
    return test_results

def generate_rq1_report(original_stats: Dict, generated_stats: Dict, comparison: Dict, 
                       test_results: Dict, output_dir: Path):
    """生成RQ1评估报告"""
    # 转换统计数据为可序列化格式
    original_stats = convert_stats_to_serializable(original_stats)
    generated_stats = convert_stats_to_serializable(generated_stats)
    comparison = convert_stats_to_serializable(comparison)
    test_results = convert_stats_to_serializable(test_results)
    
    # 计算平均句子长度时确保使用Python原生类型
    avg_orig_len = float(np.mean(original_stats["sentence_lengths"]))
    avg_gen_len = float(np.mean(generated_stats["sentence_lengths"]))
    
    report = {
        "evaluation_type": "RQ1: 数据集统计与分析",
        "evaluation_time": datetime.now().isoformat(),
        "summary": {
            "original_dataset": {
                "total_records": original_stats["total_records"],
                "total_entities": original_stats["total_entities"],
                "entity_types": len(original_stats["entity_types"]),
                "avg_sentence_length": avg_orig_len,
                "vocabulary_richness": float(original_stats["vocabulary_stats"]["vocabulary_richness"])
            },
            "generated_dataset": {
                "total_records": generated_stats["total_records"],
                "total_entities": generated_stats["total_entities"],
                "entity_types": len(generated_stats["entity_types"]),
                "avg_sentence_length": avg_gen_len,
                "vocabulary_richness": float(generated_stats["vocabulary_stats"]["vocabulary_richness"])
            }
        },
        "detailed_statistics": {
            "original_stats": original_stats,
            "generated_stats": generated_stats
        },
        "comparison_analysis": comparison,
        "statistical_tests": test_results,
        "conclusions": generate_conclusions(comparison, test_results)
    }
    
    # 保存报告
    with open(output_dir / "rq1_evaluation_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    # 生成文本摘要
    generate_text_summary(report, output_dir)

def generate_conclusions(comparison: Dict, test_results: Dict) -> Dict[str, Any]:
    """生成结论"""
    conclusions = {
        "dataset_size": "",
        "entity_distribution": "",
        "sentence_length": "",
        "vocabulary": "",
        "overall_assessment": ""
    }
    
    # 数据集规模结论
    size_ratio = comparison["size_comparison"]["size_ratio"]
    if size_ratio > 1.5:
        conclusions["dataset_size"] = "生成数据集规模显著大于原始数据集"
    elif size_ratio < 0.5:
        conclusions["dataset_size"] = "生成数据集规模显著小于原始数据集"
    else:
        conclusions["dataset_size"] = "生成数据集规模与原始数据集相近"
    
    # 实体分布结论
    entity_tests = test_results.get("entity_distribution_tests", {})
    significant_tests = [test for test, result in entity_tests.items() 
                        if result.get("significant", False)]
    
    if significant_tests:
        conclusions["entity_distribution"] = f"实体分布存在显著差异 ({', '.join(significant_tests)})"
    else:
        conclusions["entity_distribution"] = "实体分布无显著差异"
    
    # 句子长度结论
    length_tests = test_results.get("sentence_length_tests", {})
    significant_length_tests = [test for test, result in length_tests.items() 
                               if result.get("significant", False)]
    
    if significant_length_tests:
        conclusions["sentence_length"] = f"句子长度分布存在显著差异 ({', '.join(significant_length_tests)})"
    else:
        conclusions["sentence_length"] = "句子长度分布无显著差异"
    
    # 词汇丰富度结论
    orig_richness = comparison["vocabulary_comparison"]["original_vocabulary_richness"]
    gen_richness = comparison["vocabulary_comparison"]["generated_vocabulary_richness"]
    richness_diff = abs(gen_richness - orig_richness)
    
    if richness_diff > 0.1:
        conclusions["vocabulary"] = "词汇丰富度存在显著差异"
    else:
        conclusions["vocabulary"] = "词汇丰富度相近"
    
    # 整体评估
    significant_differences = len(significant_tests) + len(significant_length_tests)
    if significant_differences == 0:
        conclusions["overall_assessment"] = "生成数据集与原始数据集在统计特征上高度相似"
    elif significant_differences <= 2:
        conclusions["overall_assessment"] = "生成数据集与原始数据集在统计特征上基本相似，存在少量差异"
    else:
        conclusions["overall_assessment"] = "生成数据集与原始数据集在统计特征上存在较多差异"
    
    return conclusions

def generate_text_summary(report: Dict, output_dir: Path):
    """生成文本摘要"""
    summary_lines = []
    summary_lines.append("=" * 60)
    summary_lines.append("RQ1: 数据集统计与分析评估报告")
    summary_lines.append("=" * 60)
    summary_lines.append(f"评估时间: {report['evaluation_time']}")
    summary_lines.append("")
    
    # 基本统计
    orig_summary = report["summary"]["original_dataset"]
    gen_summary = report["summary"]["generated_dataset"]
    
    summary_lines.append("数据集基本信息:")
    summary_lines.append(f"  原始数据集: {orig_summary['total_records']} 条记录, {orig_summary['total_entities']} 个实体")
    summary_lines.append(f"  生成数据集: {gen_summary['total_records']} 条记录, {gen_summary['total_entities']} 个实体")
    summary_lines.append(f"  规模比例: {report['comparison_analysis']['size_comparison']['size_ratio']:.2f}")
    summary_lines.append("")
    
    # 统计特征对比
    summary_lines.append("统计特征对比:")
    summary_lines.append(f"  平均句子长度: {orig_summary['avg_sentence_length']:.1f}  {gen_summary['avg_sentence_length']:.1f}")
    summary_lines.append(f"  词汇丰富度: {orig_summary['vocabulary_richness']:.3f}  {gen_summary['vocabulary_richness']:.3f}")
    summary_lines.append(f"  实体类型数: {orig_summary['entity_types']}  {gen_summary['entity_types']}")
    summary_lines.append("")
    
    # 结论
    conclusions = report["conclusions"]
    summary_lines.append("评估结论:")
    for key, conclusion in conclusions.items():
        summary_lines.append(f"  {key}: {conclusion}")
    summary_lines.append("")
    
    # 统计检验结果
    summary_lines.append("统计显著性检验:")
    for test_category, tests in report["statistical_tests"].items():
        summary_lines.append(f"  {test_category}:")
        for test_name, result in tests.items():
            if "error" not in result:
                significance = "显著" if result["significant"] else "不显著"
                summary_lines.append(f"    {test_name}: p={result['p_value']:.4f} ({significance})")
            else:
                summary_lines.append(f"    {test_name}: 错误 - {result['error']}")
    
    # 保存摘要
    with open(output_dir / "rq1_summary.txt", 'w', encoding='utf-8') as f:
        f.write('\n'.join(summary_lines))

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="RQ1: 数据集统计与分析")
    parser.add_argument("--original", required=True, help="原始数据集路径")
    parser.add_argument("--generated", required=True, help="生成数据集路径")
    parser.add_argument("--output", required=True, help="输出目录")
    parser.add_argument("--config", help="配置文件路径")
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print("=" * 60)
    print("RQ1: 数据集统计与分析评估")
    print("=" * 60)
    print(f"原始数据集: {args.original}")
    print(f"生成数据集: {args.generated}")
    print(f"输出目录: {output_dir}")
    print()
    
    try:
        # 加载配置
        config = load_config()
        
        # 加载数据集
        print("加载数据集...")
        original_dataset = load_dataset(args.original)
        generated_dataset = load_dataset(args.generated)
        print(f"原始数据集: {len(original_dataset)} 条记录")
        print(f"生成数据集: {len(generated_dataset)} 条记录")
        
        # 计算统计信息
        print("计算统计信息...")
        original_stats = calculate_dataset_statistics(original_dataset)
        generated_stats = calculate_dataset_statistics(generated_dataset)
        
        # 比较分布
        print("比较分布差异...")
        comparison = compare_distributions(original_stats, generated_stats)
        
        # 运行统计检验
        print("运行统计显著性检验...")
        test_results = run_statistical_tests(original_stats, generated_stats, config)
        
        # 生成可视化
        if config["rq1_dataset_statistics"]["visualization"]["distribution_plots"]:
            print("生成分布对比图...")
            create_distribution_plots(original_stats, generated_stats, comparison, output_dir)
        
        if config["rq1_dataset_statistics"]["visualization"]["comparison_tables"]:
            print("生成对比表格...")
            create_comparison_table(comparison, load_target_config(), output_dir)
        
        # 生成报告
        print("生成评估报告...")
        generate_rq1_report(original_stats, generated_stats, comparison, test_results, output_dir)
        
        print(f"\n RQ1评估完成！结果已保存到: {output_dir}")
        print(f"  - 详细报告: {output_dir}/rq1_evaluation_report.json")
        print(f"  - 文本摘要: {output_dir}/rq1_summary.txt")
        if config["rq1_dataset_statistics"]["visualization"]["distribution_plots"]:
            print(f"  - 实体类型分布图: {output_dir}/entity_type_distribution.png")
            print(f"  - 句子长度分布图: {output_dir}/sentence_length_distribution.png")
            print(f"  - 实体值多样性图: {output_dir}/entity_value_diversity.png")
        if config["rq1_dataset_statistics"]["visualization"]["comparison_tables"]:
            print(f"  - 对比表格: {output_dir}/entity_comparison_table.png")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
