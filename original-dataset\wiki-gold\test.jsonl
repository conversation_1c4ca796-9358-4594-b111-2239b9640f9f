{"sentence": "UK Edition came with the OSC-DIS video, and most of the tracks were re-engineered.", "tokens": ["UK", "Edition", "came", "with", "the", "OSC-DIS", "video", ",", "and", "most", "of", "the", "tracks", "were", "re-engineered", "."], "ner_tags": ["B-LOC", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "During this time <PERSON><PERSON>'s Division was detached from the Army and was occupied with guarding the fords on the Potomac.", "tokens": ["During", "this", "time", "Couch", "'s", "Division", "was", "detached", "from", "the", "Army", "and", "was", "occupied", "with", "guarding", "the", "fords", "on", "the", "Potomac", "."], "ner_tags": ["O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "At the Battle of Gettysburg in July, it helped defend the left flank of the Union army.", "tokens": ["At", "the", "Battle", "of", "Gettysburg", "in", "July", ",", "it", "helped", "defend", "the", "left", "flank", "of", "the", "Union", "army", "."], "ner_tags": ["O", "O", "B-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O"]}
{"sentence": "The 139th supported Sheridan in the Appomattox Campaign and fought in the Battle of Sayler's Creek.", "tokens": ["The", "139th", "supported", "Sheridan", "in", "the", "Appomattox", "Campaign", "and", "fought", "in", "the", "Battle", "of", "Sayler", "'s", "Creek", "."], "ner_tags": ["O", "B-ORG", "O", "B-PER", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "O"]}
{"sentence": "Two cars were demolished and the two following cars were telescoped.", "tokens": ["Two", "cars", "were", "demolished", "and", "the", "two", "following", "cars", "were", "telescoped", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The second car of the West Jersey train was also carried into the ditch, the third and fourth cars being telescoped.", "tokens": ["The", "second", "car", "of", "the", "West", "Jersey", "train", "was", "also", "carried", "into", "the", "ditch", ",", "the", "third", "and", "fourth", "cars", "being", "telescoped", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "As onlookers watched through the night, the mangled and burned bodies of the dead were carried from the wreckage which trapped them and laid side by side on the gravel bank near the track, with no other covering than the few newspapers gathered from the passengers.", "tokens": ["As", "onlookers", "watched", "through", "the", "night", ",", "the", "mangled", "and", "burned", "bodies", "of", "the", "dead", "were", "carried", "from", "the", "wreckage", "which", "trapped", "them", "and", "laid", "side", "by", "side", "on", "the", "gravel", "bank", "near", "the", "track", ",", "with", "no", "other", "covering", "than", "the", "few", "newspapers", "gathered", "from", "the", "passengers", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "James W. Hoyt, Secretary of the New Jersey Department of Public Safety, immediately upon learning of the extent of the accident, telegraphed for the Philadelphia Medical Emergency Corps, fifteen of whom responded, and hurried to Atlantic City on a special train which left Philadelphia at 10:45 pm.", "tokens": ["James", "W.", "Hoyt", ",", "Secretary", "of", "the", "New", "Jersey", "Department", "of", "Public", "Safety", ",", "immediately", "upon", "learning", "of", "the", "extent", "of", "the", "accident", ",", "telegraphed", "for", "the", "Philadelphia", "Medical", "Emergency", "Corps", ",", "fifteen", "of", "whom", "responded", ",", "and", "hurried", "to", "Atlantic", "City", "on", "a", "special", "train", "which", "left", "Philadelphia", "at", "10:45", "pm", "."], "ner_tags": ["B-PER", "I-PER", "I-PER", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O"]}
{"sentence": "He went directly into the block tower and questioned operator George F. Hauser. Houser told him that he thought the excursion train had time to cross the tracks of the Reading before the express got there and he set the \"clear\" signal for the West Jersey train.", "tokens": ["He", "went", "directly", "into", "the", "block", "tower", "and", "questioned", "operator", "George", "F.", "Hauser.", "Houser", "told", "him", "that", "he", "thought", "the", "excursion", "train", "had", "time", "to", "cross", "the", "tracks", "of", "the", "Reading", "before", "the", "express", "got", "there", "and", "he", "set", "the", "\"", "clear", "\"", "signal", "for", "the", "West", "Jersey", "train", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "I-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O"]}
{"sentence": "Before Hauser could make a further explanation to the coroner he received an order from the railroad officials to say nothing.", "tokens": ["Before", "Hauser", "could", "make", "a", "further", "explanation", "to", "the", "coroner", "he", "received", "an", "order", "from", "the", "railroad", "officials", "to", "say", "nothing", "."], "ner_tags": ["O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "His reputation was that of an experienced engineer and a man of exceptionally high moral character, and not of a reckless or careless disposition.", "tokens": ["His", "reputation", "was", "that", "of", "an", "experienced", "engineer", "and", "a", "man", "of", "exceptionally", "high", "moral", "character", ",", "and", "not", "of", "a", "reckless", "or", "careless", "disposition", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Three others found that \"the cause of the collision was the failure of Edward Farr, engineer of Train No. 23, to give heed in time to the semaphore signals and crossing under the rules... the tower man, George F. Hauser, may have used poor judgment in his estimate of the distance away of the Atlantic City Railroad train when he gave the white boards to the West Jersey and Seashore Excursion Train No. 700.\"", "tokens": ["Three", "others", "found", "that", "\"", "the", "cause", "of", "the", "collision", "was", "the", "failure", "of", "Edward", "Farr", ",", "engineer", "of", "Train", "No.", "23", ",", "to", "give", "heed", "in", "time", "to", "the", "semaphore", "signals", "and", "crossing", "under", "the", "rules.", "..", "the", "tower", "man", ",", "George", "F.", "Hauser", ",", "may", "have", "used", "poor", "judgment", "in", "his", "estimate", "of", "the", "distance", "away", "of", "the", "Atlantic", "City", "Railroad", "train", "when", "he", "gave", "the", "white", "boards", "to", "the", "West", "Jersey", "and", "Seashore", "Excursion", "Train", "No", ".", "700", ".", "\""], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "They participated in the GMAC Bowl, losing to Tulsa 63-7.", "tokens": ["They", "participated", "in", "the", "GMAC", "Bowl", ",", "losing", "to", "Tulsa", "63-7", "."], "ner_tags": ["O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "B-ORG", "O", "O"]}
{"sentence": "Overall, 53 lettermen are returning from the 2006 team (25 on offense, 28 on defense and 0 on special teams).", "tokens": ["Overall", ",", "53", "lettermen", "are", "returning", "from", "the", "2006", "team", "(", "25", "on", "offense", ",", "28", "on", "defense", "and", "0", "on", "special", "teams", ")", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "30 Seconds to Mars (or Thirty Seconds to Mars) is an alternative rock band from Los Angeles, California, featuring Jared Leto, Shannon Leto, Tomo Milicevic.", "tokens": ["30", "Seconds", "to", "Mars", "(", "or", "Thirty", "Seconds", "to", "Mars", ")", "is", "an", "alternative", "rock", "band", "from", "Los", "Angeles", ",", "California", ",", "featuring", "Jared", "Leto", ",", "Shannon", "Leto", ",", "Tomo", "Milicevic", "."], "ner_tags": ["B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "B-LOC", "O", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O"]}
{"sentence": "Their sophomore album, A Beautiful Lie, was released on August 30, 2005.", "tokens": ["Their", "sophomore", "album", ",", "A", "Beautiful", "Lie", ",", "was", "released", "on", "August", "30", ",", "2005", "."], "ner_tags": ["O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The second nomination was for Best Rock Video; however, they lost to AFI's \"Miss Murder\".", "tokens": ["The", "second", "nomination", "was", "for", "Best", "Rock", "Video", ";", "however", ",", "they", "lost", "to", "AFI", "'s", "\"", "Miss", "Murder", "\"", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "B-MISC", "I-MISC", "O", "O"]}
{"sentence": "\"Jared and Shannon put together this thing called Environmentor which is illustrating ways --- alternatives --- to kind of clean up some of the mess we leave behind.", "tokens": ["\"", "Jared", "and", "Shannon", "put", "together", "this", "thing", "called", "Environmentor", "which", "is", "illustrating", "ways", "---", "alternatives", "---", "to", "kind", "of", "clean", "up", "some", "of", "the", "mess", "we", "leave", "behind", "."], "ner_tags": ["O", "B-PER", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "They are also scheduled to play Roskilde, Rock am Ring, Pinkpop, Give It A Name Festival and Download.", "tokens": ["They", "are", "also", "scheduled", "to", "play", "Roskilde", ",", "Rock", "am", "Ring", ",", "Pinkpop", ",", "Give", "It", "A", "Name", "Festival", "and", "Download", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-MISC", "O", "B-MISC", "I-MISC", "I-MISC", "O", "B-MISC", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "B-MISC", "O"]}
{"sentence": "On August 8th, 2007, Kerrang! announced that 30 Seconds to Mars has been nominated for two categories of the Kerrang!", "tokens": ["On", "August", "8th", ",", "2007", ",", "Kerrang", "!", "announced", "that", "30", "Seconds", "to", "Mars", "has", "been", "nominated", "for", "two", "categories", "of", "the", "Kerrang", "!"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG"]}
{"sentence": "They beat out Korn and had amassed over 7 million votes.", "tokens": ["They", "beat", "out", "Korn", "and", "had", "amassed", "over", "7", "million", "votes", "."], "ner_tags": ["O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "It is also the name of one of their songs in the album 30 Seconds to Mars.", "tokens": ["It", "is", "also", "the", "name", "of", "one", "of", "their", "songs", "in", "the", "album", "30", "Seconds", "to", "Mars", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "O"]}
{"sentence": "Another track featured on certain imported 30 Seconds to Mars titles is \"Anarchy in Tokyo\", a song that was recorded during the process of their self-titled debut.", "tokens": ["Another", "track", "featured", "on", "certain", "imported", "30", "Seconds", "to", "Mars", "titles", "is", "\"", "Anarchy", "in", "Tokyo", "\"", ",", "a", "song", "that", "was", "recorded", "during", "the", "process", "of", "their", "self-titled", "debut", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Also made available are the demo versions of \"Buddha for Mary\" and \"93 Million Miles\"; the latter originally had lyrics referring to the band Deadsy, whose members Dr. Nner and P. Exeter Blue I provided extra instrumentation on several tracks, but the lyrics were changed after the two bands were involved in a small feud.", "tokens": ["Also", "made", "available", "are", "the", "demo", "versions", "of", "\"", "Buddha", "for", "Mary", "\"", "and", "\"", "93", "Million", "Miles", "\"", ";", "the", "latter", "originally", "had", "lyrics", "referring", "to", "the", "band", "Deadsy", ",", "whose", "members", "Dr.", "Nner", "and", "P.", "Exeter", "Blue", "I", "provided", "extra", "instrumentation", "on", "several", "tracks", ",", "but", "the", "lyrics", "were", "changed", "after", "the", "two", "bands", "were", "involved", "in", "a", "small", "feud", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "I-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The 38th NAACP Image Awards honored the best in film, television and music for 2006.", "tokens": ["The", "38th", "NAACP", "Image", "Awards", "honored", "the", "best", "in", "film", ",", "television", "and", "music", "for", "2006", "."], "ner_tags": ["O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The following recipients received distinguished awards by the NAACP for their contributions to arts, civil rights, news, and humanitarian efforts.", "tokens": ["The", "following", "recipients", "received", "distinguished", "awards", "by", "the", "NAACP", "for", "their", "contributions", "to", "arts", ",", "civil", "rights", ",", "news", ",", "and", "humanitarian", "efforts", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Its units supported the Allied invasion of Normandy (June 1944); the Allied ground troops during the Battle of the Bulge (December 1944 through January 1945); the Allied airborne attack on Holland (Operation Market Garden, September 1944); the defense of the Remagen bridgehead against German air attacks (March 1945); and the airborne attack across the Rhine (March 1945).", "tokens": ["Its", "units", "supported", "the", "Allied", "invasion", "of", "Normandy", "(", "June", "1944", ")", ";", "the", "Allied", "ground", "troops", "during", "the", "Battle", "of", "the", "Bulge", "(", "December", "1944", "through", "January", "1945", ")", ";", "the", "Allied", "airborne", "attack", "on", "Holland", "(", "Operation", "Market", "Garden", ",", "September", "1944", ")", ";", "the", "defense", "of", "the", "Remagen", "bridgehead", "against", "German", "air", "attacks", "(", "March", "1945", ")", ";", "and", "the", "airborne", "attack", "across", "the", "Rhine", "(", "March", "1945", ")", "."], "ner_tags": ["O", "O", "O", "O", "B-ORG", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "B-LOC", "O", "B-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O"]}
{"sentence": "Assigned or attached units of the division participated in numerous exercises with the Spanish Air Defense Command, and in some instances, with the U.S. Sixth Fleet.", "tokens": ["Assigned", "or", "attached", "units", "of", "the", "division", "participated", "in", "numerous", "exercises", "with", "the", "Spanish", "Air", "Defense", "Command", ",", "and", "in", "some", "instances", ",", "with", "the", "U.S.", "Sixth", "Fleet", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O"]}
{"sentence": "Redesignated 65 Air Division (Defense) on 17 April 1952.", "tokens": ["Redesignated", "65", "Air", "Division", "(", "Defense", ")", "on", "17", "April", "1952", "."], "ner_tags": ["O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O"]}
{"sentence": "Inactivated on 30 June 1991.", "tokens": ["Inactivated", "on", "30", "June", "1991", "."], "ner_tags": ["O", "O", "O", "O", "O", "O"]}
{"sentence": "Iceland Air Defense Force, 24 April 1952 -- 8 March 1954.", "tokens": ["Iceland", "Air", "Defense", "Force", ",", "24", "April", "1952", "--", "8", "March", "1954", "."], "ner_tags": ["B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Sembach Air Base, Germany, 1 June 1985; Lindsey Air Station, Germany, 1 October 1987 -- 30 June 1991.", "tokens": ["Sembach", "Air", "Base", ",", "Germany", ",", "1", "June", "1985", ";", "Lindsey", "Air", "Station", ",", "Germany", ",", "1", "October", "1987", "--", "30", "June", "1991", "."], "ner_tags": ["B-ORG", "I-ORG", "I-ORG", "O", "B-LOC", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Curry, 29 July 1945 -- unkn.", "tokens": ["Curry", ",", "29", "July", "1945", "--", "unkn", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "In 1963 the Victorian Broadcasting Network purchased Nicholson's and sold the electrical division of the organisation to Vox Adeon.", "tokens": ["In", "1963", "the", "Victorian", "Broadcasting", "Network", "purchased", "Nicholson", "'s", "and", "sold", "the", "electrical", "division", "of", "the", "organisation", "to", "Vox", "Adeon", "."], "ner_tags": ["O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O"]}
{"sentence": "The limited hours of broadcasting 69 years ago have gradually been increased to a 24 hour, seven day a week service.", "tokens": ["The", "limited", "hours", "of", "broadcasting", "69", "years", "ago", "have", "gradually", "been", "increased", "to", "a", "24", "hour", ",", "seven", "day", "a", "week", "service", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "In 1987 the TAB purchased 6PR to secure the future of race broadcasts in Western Australia and races were broadcast across the station during popular talkback programs.", "tokens": ["In", "1987", "the", "TAB", "purchased", "6PR", "to", "secure", "the", "future", "of", "race", "broadcasts", "in", "Western", "Australia", "and", "races", "were", "broadcast", "across", "the", "station", "during", "popular", "talkback", "programs", "."], "ner_tags": ["O", "O", "O", "B-ORG", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "During the Australian Rules Football Season 882 6PR is Perth's Football Leader.", "tokens": ["During", "the", "Australian", "Rules", "Football", "Season", "882", "6PR", "is", "Perth", "'s", "Football", "Leader", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "B-LOC", "O", "O", "O", "O"]}
{"sentence": "Peter Vlahos is based in the 6PR Studio while Karl Langdon & Harvey Deegan base themselves at different matches.", "tokens": ["Peter", "Vlahos", "is", "based", "in", "the", "6PR", "Studio", "while", "Karl", "Langdon", "&", "Harvey", "Deegan", "base", "themselves", "at", "different", "matches", "."], "ner_tags": ["B-PER", "I-PER", "O", "O", "O", "O", "B-ORG", "O", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O"]}
{"sentence": "As of the 2007/2008 A-League season 6PR will again broadcast every Perth Glory Home & Away fixture.", "tokens": ["As", "of", "the", "2007/2008", "A-League", "season", "6PR", "will", "again", "broadcast", "every", "Perth", "Glory", "Home", "&", "Away", "fixture", "."], "ner_tags": ["O", "O", "O", "B-ORG", "I-ORG", "O", "B-ORG", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O"]}
{"sentence": "883JIA FM, is a Chinese radio station of SAFRA Radio in Singapore.", "tokens": ["883JIA", "FM", ",", "is", "a", "Chinese", "radio", "station", "of", "SAFRA", "Radio", "in", "Singapore", "."], "ner_tags": ["B-ORG", "I-ORG", "O", "O", "O", "B-MISC", "O", "O", "O", "B-ORG", "I-ORG", "O", "B-LOC", "O"]}
{"sentence": "883Jia FM Official Website", "tokens": ["883Jia", "FM", "Official", "Website"], "ner_tags": ["B-ORG", "I-ORG", "O", "O"]}
{"sentence": "Both were transferred from Kreis Flensburg with the establishment of the current Danish-German border.", "tokens": ["Both", "were", "transferred", "from", "Kreis", "Flensburg", "with", "the", "establishment", "of", "the", "current", "Danish-German", "border", "."], "ner_tags": ["O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O"]}
{"sentence": "He is known to have composed three chansons de geste as well as the romance Cleomad\u00e9.", "tokens": ["He", "is", "known", "to", "have", "composed", "three", "chansons", "de", "geste", "as", "well", "as", "the", "romance", "Cleomad\u00e9", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O"]}
{"sentence": "Despite being born in the West African country of the Cape Verde islands he plays for the Swiss Under-19s side.", "tokens": ["Despite", "being", "born", "in", "the", "West", "African", "country", "of", "the", "Cape", "Verde", "islands", "he", "plays", "for", "the", "Swiss", "Under-19s", "side", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "B-LOC", "I-LOC", "I-LOC", "O", "O", "O", "O", "B-MISC", "B-ORG", "O", "O"]}
{"sentence": "Born on April 16, 1819 in Gostk\u00f3city near \u0141\u0119czyca in a Polish szlachta family of Lipscy to Jacob Lipski and Marjania Zaluska, she spent her life in the Prussian partition, including the Grand Duchy of Pozna\u0144.", "tokens": ["Born", "on", "April", "16", ",", "1819", "in", "Gostk\u00f3city", "near", "\u0141\u0119czyca", "in", "a", "Polish", "szlachta", "family", "of", "Lipscy", "to", "Jacob", "Lipski", "and", "Marjania", "Zaluska", ",", "she", "spent", "her", "life", "in", "the", "Prussian", "partition", ",", "including", "the", "Grand", "Duchy", "of", "Pozna\u0144", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "B-MISC", "O", "O", "O", "B-LOC", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "B-LOC", "I-LOC", "I-LOC", "I-LOC", "O"]}
{"sentence": "She took part in many activities designed to promote Polish culture, and sponsored and organized various festivities or organizations (such as the Society of Scientific Help for Girls (Towarzystwo Pomocy Naukowej dla dziewcz\u0105t)).", "tokens": ["She", "took", "part", "in", "many", "activities", "designed", "to", "promote", "Polish", "culture", ",", "and", "sponsored", "and", "organized", "various", "festivities", "or", "organizations", "(", "such", "as", "the", "Society", "of", "Scientific", "Help", "for", "Girls", "(", "Towarzystwo", "Pomocy", "Naukowej", "dla", "dziewcz\u0105t", ")", ")", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O"]}
{"sentence": "Excluded from all political and social rights, they were only allowed to enter a church by a special door, and during the service a rail separated them from the other worshippers.", "tokens": ["Excluded", "from", "all", "political", "and", "social", "rights", ",", "they", "were", "only", "allowed", "to", "enter", "a", "church", "by", "a", "special", "door", ",", "and", "during", "the", "service", "a", "rail", "separated", "them", "from", "the", "other", "worshippers", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "It has been suggested that they were descendants of the Visigoths, and somebody derives the name from caas (dog) and Goth.", "tokens": ["It", "has", "been", "suggested", "that", "they", "were", "descendants", "of", "the", "Visigoths", ",", "and", "somebody", "derives", "the", "name", "from", "caas", "(", "dog", ")", "and", "Goth", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O"]}
{"sentence": "It was not until the French Revolution that any steps were taken to ameliorate their lot, but today they no longer form a class and have been practically lost sight of in the general peasantry.", "tokens": ["It", "was", "not", "until", "the", "French", "Revolution", "that", "any", "steps", "were", "taken", "to", "ameliorate", "their", "lot", ",", "but", "today", "they", "no", "longer", "form", "a", "class", "and", "have", "been", "practically", "lost", "sight", "of", "in", "the", "general", "peasantry", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Robert Gibson succeeded Wren as the \"Liberal-Labour\" MPP for Kenora and served until the 1967 election.", "tokens": ["Robert", "Gibson", "succeeded", "Wren", "as", "the", "\"", "Liberal-Labour", "\"", "MPP", "for", "Kenora", "and", "served", "until", "the", "1967", "election", "."], "ner_tags": ["B-PER", "I-PER", "O", "B-PER", "O", "O", "O", "B-ORG", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "It was probably first performed by Gradsky on August 20, 1998 on Vasilievsky Spusk next to the Red Square in Moscow.", "tokens": ["It", "was", "probably", "first", "performed", "by", "Gradsky", "on", "August", "20", ",", "1998", "on", "Vasilievsky", "Spusk", "next", "to", "the", "Red", "Square", "in", "Moscow", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "B-LOC", "I-LOC", "O", "B-LOC", "O"]}
{"sentence": "The Amateur Hockey Association of Canada (AHAC) was an amateur men's ice hockey league founded in 1886, in existence until 1898.", "tokens": ["The", "Amateur", "Hockey", "Association", "of", "Canada", "(", "AHAC", ")", "was", "an", "amateur", "men", "'s", "ice", "hockey", "league", "founded", "in", "1886", ",", "in", "existence", "until", "1898", "."], "ner_tags": ["O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "They agreed that the season should run from the 1st of January until the 15th of March.", "tokens": ["They", "agreed", "that", "the", "season", "should", "run", "from", "the", "1st", "of", "January", "until", "the", "15th", "of", "March", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The rover would line up behind the centre, with the point and coverpoint following, in an 'I' formation towards the goaltender.", "tokens": ["The", "rover", "would", "line", "up", "behind", "the", "centre", ",", "with", "the", "point", "and", "coverpoint", "following", ",", "in", "an", "'", "I", "'", "formation", "towards", "the", "goaltender", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "There were no boards along the sides of the ice, and there were no standard dimensions for a rink, although dimensions were instituted for the positioning of the goal out from the ends of the rink.", "tokens": ["There", "were", "no", "boards", "along", "the", "sides", "of", "the", "ice", ",", "and", "there", "were", "no", "standard", "dimensions", "for", "a", "rink", ",", "although", "dimensions", "were", "instituted", "for", "the", "positioning", "of", "the", "goal", "out", "from", "the", "ends", "of", "the", "rink", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The first championship team of the AHAC was the Montreal Crystals, having unofficially being declared the champions before the AHAC.", "tokens": ["The", "first", "championship", "team", "of", "the", "AHAC", "was", "the", "Montreal", "Crystals", ",", "having", "unofficially", "being", "declared", "the", "champions", "before", "the", "AHAC", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O"]}
{"sentence": "In 1893, the first year since 1888 where the AHAC played under a series system, the Montreal Hockey Club lost their first game against the Ottawa Hockey Club, and proceeded to win their next seven en route to the championship.", "tokens": ["In", "1893", ",", "the", "first", "year", "since", "1888", "where", "the", "AHAC", "played", "under", "a", "series", "system", ",", "the", "Montreal", "Hockey", "Club", "lost", "their", "first", "game", "against", "the", "Ottawa", "Hockey", "Club", ",", "and", "proceeded", "to", "win", "their", "next", "seven", "en", "route", "to", "the", "championship", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "In the end, the MAAA investigated into why its hockey club wanted to refuse and return the trophy, even though such an action would damage the reputation of the MAAA.", "tokens": ["In", "the", "end", ",", "the", "MAAA", "investigated", "into", "why", "its", "hockey", "club", "wanted", "to", "refuse", "and", "return", "the", "trophy", ",", "even", "though", "such", "an", "action", "would", "damage", "the", "reputation", "of", "the", "MAAA", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O"]}
{"sentence": "\u2020 Stanley Cup winner", "tokens": ["\u2020", "Stanley", "Cup", "winner"], "ner_tags": ["O", "B-MISC", "I-MISC", "O"]}
{"sentence": "Andrew Sant (b. 1950) is an English born Australian poet.", "tokens": ["Andrew", "Sant", "(", "b.", "1950", ")", "is", "an", "English", "born", "Australian", "poet", "."], "ner_tags": ["B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "B-MISC", "O", "O"]}
{"sentence": "Individual poems have appeared in The Times Literary Supplement, Poetry (Chicago), Poetry London, The Australian, Antipodes and many other publications.", "tokens": ["Individual", "poems", "have", "appeared", "in", "The", "Times", "Literary", "Supplement", ",", "Poetry", "(", "Chicago", ")", ",", "Poetry", "London", ",", "The", "Australian", ",", "Antipodes", "and", "many", "other", "publications", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "B-ORG", "O", "B-LOC", "O", "O", "B-ORG", "I-ORG", "O", "B-ORG", "I-ORG", "O", "B-ORG", "O", "O", "O", "O", "O"]}
{"sentence": "Ten years after the initial diagnosis she developed another form of cancer, which she and her doctors fought with equal determination.", "tokens": ["Ten", "years", "after", "the", "initial", "diagnosis", "she", "developed", "another", "form", "of", "cancer", ",", "which", "she", "and", "her", "doctors", "fought", "with", "equal", "determination", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Fearing a lawsuit by pro-life activists, they convened a court hearing at the hospital and obtained separate counsels for Carder, her fetus, and the hospital.", "tokens": ["Fearing", "a", "lawsuit", "by", "pro-life", "activists", ",", "they", "convened", "a", "court", "hearing", "at", "the", "hospital", "and", "obtained", "separate", "counsels", "for", "Carder", ",", "her", "fetus", ",", "and", "the", "hospital", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Obstetricians at the hospital initially refused to carry out the procedure, but eventually one reluctantly agreed.", "tokens": ["Obstetricians", "at", "the", "hospital", "initially", "refused", "to", "carry", "out", "the", "procedure", ",", "but", "eventually", "one", "reluctantly", "agreed", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "In the wake of the surgery, Carder's family and the American Civil Liberties Union's Reproductive Freedom Project asked the D.C. Court of Appeals to vacate the order and its legal precedent, on grounds that the order had violated Carder's right to informed consent and her constitutional rights of privacy and bodily integrity.", "tokens": ["In", "the", "wake", "of", "the", "surgery", ",", "Carder", "'s", "family", "and", "the", "American", "Civil", "Liberties", "Union", "'s", "Reproductive", "Freedom", "Project", "asked", "the", "D.C.", "Court", "of", "Appeals", "to", "vacate", "the", "order", "and", "its", "legal", "precedent", ",", "on", "grounds", "that", "the", "order", "had", "violated", "Carder", "'s", "right", "to", "informed", "consent", "and", "her", "constitutional", "rights", "of", "privacy", "and", "bodily", "integrity", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "With a very diverse background in every type of industry imaginable Angelo began his growth in Miami as a young Actor / Model working many non-union & later union jobs.", "tokens": ["With", "a", "very", "diverse", "background", "in", "every", "type", "of", "industry", "imaginable", "Angelo", "began", "his", "growth", "in", "Miami", "as", "a", "young", "Actor", "/", "Model", "working", "many", "non-union", "&", "later", "union", "jobs", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "During this time he was also working in Corporate America for Fortune 500 Corporations leading their marketing departments in companies like AT&T, Wachovia, Carnival Cruise Lines, and many more.", "tokens": ["During", "this", "time", "he", "was", "also", "working", "in", "Corporate", "America", "for", "Fortune", "500", "Corporations", "leading", "their", "marketing", "departments", "in", "companies", "like", "AT&T", ",", "Wachovia", ",", "Carnival", "Cruise", "Lines", ",", "and", "many", "more", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "B-ORG", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O"]}
{"sentence": "In 2002, Angelo launched NOK Entertainment to create a company that could market, promote, produce, & create all types of special events at any venue from Miami, South Beach to West Palm Beach, including L.A., NYC, & Vegas.", "tokens": ["In", "2002", ",", "Angelo", "launched", "NOK", "Entertainment", "to", "create", "a", "company", "that", "could", "market", ",", "promote", ",", "produce", ",", "&", "create", "all", "types", "of", "special", "events", "at", "any", "venue", "from", "Miami", ",", "South", "Beach", "to", "West", "Palm", "Beach", ",", "including", "L.A.", ",", "NYC", ",", "&", "Vegas", "."], "ner_tags": ["O", "O", "O", "B-PER", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "I-LOC", "O", "B-LOC", "I-LOC", "I-LOC", "O", "O", "B-LOC", "O", "B-LOC", "O", "O", "B-LOC", "O"]}
{"sentence": "Angelo has reached out to corporate America, the young and successful, the trendy, the tourist, the wealthy, the professional, the rich & famous.", "tokens": ["Angelo", "has", "reached", "out", "to", "corporate", "America", ",", "the", "young", "and", "successful", ",", "the", "trendy", ",", "the", "tourist", ",", "the", "wealthy", ",", "the", "professional", ",", "the", "rich", "&", "famous", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Current News: Angelo is working on a book, the date for release is Dec. 2008. He is also launching several businesses as well as launching several concerts in the Miami Area.", "tokens": ["Current", "News", ":", "Angelo", "is", "working", "on", "a", "book", ",", "the", "date", "for", "release", "is", "Dec.", "2008", ".", "He", "is", "also", "launching", "several", "businesses", "as", "well", "as", "launching", "several", "concerts", "in", "the", "Miami", "Area", "."], "ner_tags": ["O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O"]}
{"sentence": "Influence rather than position. \"", "tokens": ["Influence", "rather", "than", "position", ".", "\""], "ner_tags": ["O", "O", "O", "O", "O", "O"]}
{"sentence": "It was written by Henrique Lopes de Mendon\u00e7a (lyrics) and Alfredo Keil (music) after the nationalist resurgence provoked by the British Ultimatum (for Portuguese troops to vacate the territory between Angola and Mozambique), was adopted as a Republican anthem and, finally, by the new Portuguese Republic in 1910 as the national anthem, replacing \"O Hymno da Carta\", the last anthem of the Constitutional Monarchy in Portugal.", "tokens": ["It", "was", "written", "by", "Henrique", "Lopes", "de", "Mendon\u00e7a", "(", "lyrics", ")", "and", "Alfredo", "Keil", "(", "music", ")", "after", "the", "nationalist", "resurgence", "provoked", "by", "the", "British", "Ultimatum", "(", "for", "Portuguese", "troops", "to", "vacate", "the", "territory", "between", "Angola", "and", "Mozambique", ")", ",", "was", "adopted", "as", "a", "Republican", "anthem", "and", ",", "finally", ",", "by", "the", "new", "Portuguese", "Republic", "in", "1910", "as", "the", "national", "anthem", ",", "replacing", "\"", "O", "Hymno", "da", "Carta", "\"", ",", "the", "last", "anthem", "of", "the", "Constitutional", "Monarchy", "in", "Portugal", "."], "ner_tags": ["O", "O", "O", "O", "B-PER", "I-PER", "I-PER", "I-PER", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "B-LOC", "O"]}
{"sentence": "On January 31, 1891, a republican revolution broke out in the northern city of Porto and \"A Portuguesa\" was adopted by the rebels as their anthem.", "tokens": ["On", "January", "31", ",", "1891", ",", "a", "republican", "revolution", "broke", "out", "in", "the", "northern", "city", "of", "Porto", "and", "\"", "A", "Portuguesa", "\"", "was", "adopted", "by", "the", "rebels", "as", "their", "anthem", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The fifth line of the chorus: \"Contra os canh\u00f5es marchar, marchar\" (Against the cannons march, march!) is an alteration of the original \"Contra os bret\u00f5es marchar, marchar\" (Against the Britos march, march!), a reference to the Pink Map.", "tokens": ["The", "fifth", "line", "of", "the", "chorus", ":", "\"", "Contra", "os", "canh\u00f5es", "marchar", ",", "marchar", "\"", "(", "Against", "the", "cannons", "march", ",", "march", "!", ")", "is", "an", "alteration", "of", "the", "original", "\"", "Contra", "os", "bret\u00f5es", "marchar", ",", "marchar", "\"", "(", "Against", "the", "Britos", "march", ",", "march", "!", ")", ",", "a", "reference", "to", "the", "Pink", "Map", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "After World War II, it was changed to Veterans Day in the United States and to Remembrance Day in the British Commonwealth of Nations.", "tokens": ["After", "World", "War", "II", ",", "it", "was", "changed", "to", "Veterans", "Day", "in", "the", "United", "States", "and", "to", "Remembrance", "Day", "in", "the", "British", "Commonwealth", "of", "Nations", "."], "ner_tags": ["O", "B-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "B-LOC", "I-LOC", "O", "O", "B-MISC", "I-MISC", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O"]}
{"sentence": "November 11th 2008 will be the 90th anniversary of Armistice Day.", "tokens": ["November", "11th", "2008", "will", "be", "the", "90th", "anniversary", "of", "Armistice", "Day", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O"]}
{"sentence": "The ISAP, ILPAP and ETHEL companies belong to the public company OASA (Organismos Astikon Syngoinonion Athinon / Athens Urban Transport Organisation / \u039f\u03c1\u03b3\u03b1\u03bd\u03b9\u03c3\u03bc\u03cc\u03c2 \u0391\u03c3\u03c4\u03b9\u03ba\u03ce\u03bd \u03a3\u03c5\u03b3\u03ba\u03bf\u03b9\u03bd\u03c9\u03bd\u03b9\u03ce\u03bd \u0391\u03b8\u03b7\u03bd\u03ce\u03bd. The AMEL and Tram companies belong to Attiko Metro (\u0391\u03c4\u03c4\u03b9\u03ba\u03cc \u039c\u03b5\u03c4\u03c1\u03cc \u0391.\u0395. in Greek), a company that is currently wholly owned by the Greek government.", "tokens": ["The", "ISAP", ",", "ILPAP", "and", "ETHEL", "companies", "belong", "to", "the", "public", "company", "OASA", "(", "Organismos", "Astikon", "Syngoinonion", "Athinon", "/", "Athens", "Urban", "Transport", "Organisation", "/", "\u039f\u03c1\u03b3\u03b1\u03bd\u03b9\u03c3\u03bc\u03cc\u03c2", "\u0391\u03c3\u03c4\u03b9\u03ba\u03ce\u03bd", "\u03a3\u03c5\u03b3\u03ba\u03bf\u03b9\u03bd\u03c9\u03bd\u03b9\u03ce\u03bd", "\u0391\u03b8\u03b7\u03bd\u03ce\u03bd", ".", "The", "AMEL", "and", "Tram", "companies", "belong", "to", "Attiko", "Metro", "(", "\u0391\u03c4\u03c4\u03b9\u03ba\u03cc", "\u039c\u03b5\u03c4\u03c1\u03cc", "\u0391.\u0395.", "in", "Greek", ")", ",", "a", "company", "that", "is", "currently", "wholly", "owned", "by", "the", "Greek", "government", "."], "ner_tags": ["O", "B-ORG", "O", "B-ORG", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "B-ORG", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O"]}
{"sentence": "According to the 2001 UK census, Atwick parish had a population of 318.", "tokens": ["According", "to", "the", "2001", "UK", "census", ",", "Atwick", "parish", "had", "a", "population", "of", "318", "."], "ner_tags": ["O", "O", "O", "O", "B-LOC", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "It is not a political party, but rather sees itself as a watchdog against government intervention.", "tokens": ["It", "is", "not", "a", "political", "party", ",", "but", "rather", "sees", "itself", "as", "a", "watchdog", "against", "government", "intervention", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The League is distinctive for asserting that The Protocols of the Elders of Zion is a genuine Jewish document (the Encyclop\u00e6di Britannica for example describes the Protocols as a \"fraudulent document that served as a pretext and rationale for anti-Semitism in the early 20th century\").", "tokens": ["The", "League", "is", "distinctive", "for", "asserting", "that", "The", "Protocols", "of", "the", "Elders", "of", "Zion", "is", "a", "genuine", "Jewish", "document", "(", "the", "Encyclop\u00e6di", "Britannica", "for", "example", "describes", "the", "Protocols", "as", "a", "\"", "fraudulent", "document", "that", "served", "as", "a", "pretext", "and", "rationale", "for", "anti-Semitism", "in", "the", "early", "20th", "century", "\"", ")", "."], "ner_tags": ["O", "B-ORG", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "B-MISC", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Former Western Australian Labor MP, founder of the Australia First Party and later One Nation member Graeme Campbell was associated with the League at the same time as he was a member of One Nation and Australia First.", "tokens": ["Former", "Western", "Australian", "Labor", "MP", ",", "founder", "of", "the", "Australia", "First", "Party", "and", "later", "One", "Nation", "member", "Graeme", "Campbell", "was", "associated", "with", "the", "League", "at", "the", "same", "time", "as", "he", "was", "a", "member", "of", "One", "Nation", "and", "Australia", "First", "."], "ner_tags": ["O", "B-MISC", "I-MISC", "B-ORG", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "B-ORG", "I-ORG", "O", "B-PER", "I-PER", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "B-ORG", "I-ORG", "O"]}
{"sentence": "In the 1970s and early 1980s, the League attempted to gain control of the National Party of Australia, encouraging members to join the party in sufficient numbers to take control, a tactic known as entryism.", "tokens": ["In", "the", "1970s", "and", "early", "1980s", ",", "the", "League", "attempted", "to", "gain", "control", "of", "the", "National", "Party", "of", "Australia", ",", "encouraging", "members", "to", "join", "the", "party", "in", "sufficient", "numbers", "to", "take", "control", ",", "a", "tactic", "known", "as", "entryism", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Critics have pointed to the past participation of the League in the former World Anti-Communist League alongside dictatorial regimes like that of Augusto Pinochet.", "tokens": ["Critics", "have", "pointed", "to", "the", "past", "participation", "of", "the", "League", "in", "the", "former", "World", "Anti-Communist", "League", "alongside", "dictatorial", "regimes", "like", "that", "of", "Augusto", "Pinochet", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O"]}
{"sentence": "The Ayalon Cave is a large underground limestone cave located near Ramla, Israel.", "tokens": ["The", "Ayalon", "Cave", "is", "a", "large", "underground", "limestone", "cave", "located", "near", "Ramla", ",", "Israel", "."], "ner_tags": ["O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "O"]}
{"sentence": "The cave will remain closed to the public to allow scientific investigation to continue undisturbed.", "tokens": ["The", "cave", "will", "remain", "closed", "to", "the", "public", "to", "allow", "scientific", "investigation", "to", "continue", "undisturbed", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Baatsagaan is a sum (district) of Bayankhongor Province in southern Mongolia.", "tokens": ["Baatsagaan", "is", "a", "sum", "(", "district", ")", "of", "Bayankhongor", "Province", "in", "southern", "Mongolia", "."], "ner_tags": ["B-LOC", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "B-LOC", "O"]}
{"sentence": "The Badminton World Federation (BWF) is the international governing body for the sport of badminton.", "tokens": ["The", "Badminton", "World", "Federation", "(", "BWF", ")", "is", "the", "international", "governing", "body", "for", "the", "sport", "of", "badminton", "."], "ner_tags": ["O", "B-ORG", "I-ORG", "I-ORG", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "About ten miles from Bandarlapalle the Deccan Plateau ends in a dense forest range, sloping down into the plains of Tamilnadu.", "tokens": ["About", "ten", "miles", "from", "Bandarlapalle", "the", "Deccan", "Plateau", "ends", "in", "a", "dense", "forest", "range", ",", "sloping", "down", "into", "the", "plains", "of", "Tamilnadu", "."], "ner_tags": ["O", "O", "O", "O", "B-LOC", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "However, since the last decade there has lot of developmental work taken place, with good roads, drinking water and schools being built.", "tokens": ["However", ",", "since", "the", "last", "decade", "there", "has", "lot", "of", "developmental", "work", "taken", "place", ",", "with", "good", "roads", ",", "drinking", "water", "and", "schools", "being", "built", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Following its independence, Bangladesh became an independent member of the World Organization of the Scout Movement in 1974.", "tokens": ["Following", "its", "independence", ",", "Bangladesh", "became", "an", "independent", "member", "of", "the", "World", "Organization", "of", "the", "Scout", "Movement", "in", "1974", "."], "ner_tags": ["O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O"]}
{"sentence": "Membership is open to youth between 6 and 25 years of age, regardless of caste, creed or color.", "tokens": ["Membership", "is", "open", "to", "youth", "between", "6", "and", "25", "years", "of", "age", ",", "regardless", "of", "caste", ",", "creed", "or", "color", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "5.", "tokens": ["5", "."], "ner_tags": ["O", "O"]}
{"sentence": "In addition, there are American Boy Scouts in Dhaka, linked to the Direct Service branch of the Boy Scouts of America, which supports units around the world.", "tokens": ["In", "addition", ",", "there", "are", "American", "Boy", "Scouts", "in", "Dhaka", ",", "linked", "to", "the", "Direct", "Service", "branch", "of", "the", "Boy", "Scouts", "of", "America", ",", "which", "supports", "units", "around", "the", "world", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Li Shimin led a siege on the city of Luoyang, head of the self-declared emperor Wang Shichong, who solicited help from Dou Jiande from the east.", "tokens": ["Li", "Shimin", "led", "a", "siege", "on", "the", "city", "of", "Luoyang", ",", "head", "of", "the", "self-declared", "emperor", "Wang", "Shichong", ",", "who", "solicited", "help", "from", "Dou", "Jiande", "from", "the", "east", "."], "ner_tags": ["B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O"]}
{"sentence": "The French Voulet-Chanoine Mission, led by the captains Paul Voulet and Julien Chanoine, had been dispatched in 1898 to Africa by the French government with the mission to conquer the territories between the Niger River and Lake Chad and join in uniting French territories in West Africa.", "tokens": ["The", "French", "Voulet-Chanoine", "Mission", ",", "led", "by", "the", "captains", "Paul", "Voulet", "and", "Julien", "Chanoine", ",", "had", "been", "dispatched", "in", "1898", "to", "Africa", "by", "the", "French", "government", "with", "the", "mission", "to", "conquer", "the", "territories", "between", "the", "Niger", "River", "and", "Lake", "Chad", "and", "join", "in", "uniting", "French", "territories", "in", "West", "Africa", "."], "ner_tags": ["O", "B-MISC", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "B-MISC", "O", "O", "B-LOC", "I-LOC", "O"]}
{"sentence": "The French found the enemy assembled on the field, while women and children had already retired themselves in a small thick and almost impenetrable bush where the Azna defended themselves when facing a superior enemy.", "tokens": ["The", "French", "found", "the", "enemy", "assembled", "on", "the", "field", ",", "while", "women", "and", "children", "had", "already", "retired", "themselves", "in", "a", "small", "thick", "and", "almost", "impenetrable", "bush", "where", "the", "Azna", "defended", "themselves", "when", "facing", "a", "superior", "enemy", "."], "ner_tags": ["O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Battle of Vilcapugio was fought on October 1, 1813 during the Campaign of Alto Peru in the Argentine War of Independence, where the pro-independence forces led by General Manuel Belgrano were defeated by the pro-Spanish Realists, led by Joaquin de la Pazuela.", "tokens": ["The", "Battle", "of", "Vilcapugio", "was", "fought", "on", "October", "1", ",", "1813", "during", "the", "Campaign", "of", "Alto", "Peru", "in", "the", "Argentine", "War", "of", "Independence", ",", "where", "the", "pro-independence", "forces", "led", "by", "General", "Manuel", "Belgrano", "were", "defeated", "by", "the", "pro-Spanish", "Realists", ",", "led", "by", "Joaquin", "de", "la", "Pazuela", "."], "ner_tags": ["O", "B-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "B-PER", "I-PER", "I-PER", "I-PER", "O"]}
{"sentence": "At the end of September 1813, most of Belgrano's army arrived in the prairie of Vilcapugio, which was a plateau surrounded by tall mountains to the north of Potos\u00ed.", "tokens": ["At", "the", "end", "of", "September", "1813", ",", "most", "of", "Belgrano", "'s", "army", "arrived", "in", "the", "prairie", "of", "Vilcapugio", ",", "which", "was", "a", "plateau", "surrounded", "by", "tall", "mountains", "to", "the", "north", "of", "Potos\u00ed", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "Belgrano and Eustaquio D\u00edaz V\u00e9lez had decided that Vel would be the one to go to Potos\u00ed to reunite with the dispersed troops.", "tokens": ["Belgrano", "and", "Eustaquio", "D\u00edaz", "V\u00e9lez", "had", "decided", "that", "Vel", "would", "be", "the", "one", "to", "go", "to", "Potos\u00ed", "to", "reunite", "with", "the", "dispersed", "troops", "."], "ner_tags": ["B-PER", "O", "B-PER", "I-PER", "I-PER", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "BayCon is a large convention topping two thousand attendees over the last several years.", "tokens": ["BayCon", "is", "a", "large", "convention", "topping", "two", "thousand", "attendees", "over", "the", "last", "several", "years", "."], "ner_tags": ["B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "BayCon '83 was also held over Thanksgiving weekend.", "tokens": ["BayCon", "'", "83", "was", "also", "held", "over", "Thanksgiving", "weekend", "."], "ner_tags": ["B-MISC", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O"]}
{"sentence": "The BEL20 is the benchmark stock market index of Euronext Brussels.", "tokens": ["The", "BEL20", "is", "the", "benchmark", "stock", "market", "index", "of", "Euronext", "Brussels", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O"]}
{"sentence": "In addition, a candidate for inclusion must possess a free float market capitalisation (in Euros) of at least 300000 times the price of the index on the last trading day of December.", "tokens": ["In", "addition", ",", "a", "candidate", "for", "inclusion", "must", "possess", "a", "free", "float", "market", "capitalisation", "(", "in", "Euros", ")", "of", "at", "least", "300000", "times", "the", "price", "of", "the", "index", "on", "the", "last", "trading", "day", "of", "December", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "He began his career as a short film comedy director and gradually moved into feature film directing and production later in his career.", "tokens": ["He", "began", "his", "career", "as", "a", "short", "film", "comedy", "director", "and", "gradually", "moved", "into", "feature", "film", "directing", "and", "production", "later", "in", "his", "career", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "He played professionally for the Rochester Jeffersons and Buffalo Bisons", "tokens": ["He", "played", "professionally", "for", "the", "Rochester", "Jeffersons", "and", "Buffalo", "Bisons"], "ner_tags": ["O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "B-ORG", "I-ORG"]}
{"sentence": "Ben now also helps run Movement Bodyboarding Magazine.", "tokens": ["Ben", "now", "also", "helps", "run", "Movement", "Bodyboarding", "Magazine", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O"]}
{"sentence": "At 28, he sensed his chances of ever winning a world title receding with each hand raised in opposition: he had fancied his chances at the infamous Island.", "tokens": ["At", "28", ",", "he", "sensed", "his", "chances", "of", "ever", "winning", "a", "world", "title", "receding", "with", "each", "hand", "raised", "in", "opposition", ":", "he", "had", "fancied", "his", "chances", "at", "the", "infamous", "Island", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "The irony of that frigid afternoon in Cronulla did n't dawn on Player as he paddled out in flawless, two-metre waves for the final of the Pipeline Pro; he was too preoccupied with matters at hand.", "tokens": ["The", "irony", "of", "that", "frigid", "afternoon", "in", "Cronulla", "did", "n't", "dawn", "on", "Player", "as", "he", "paddled", "out", "in", "flawless", ",", "two-metre", "waves", "for", "the", "final", "of", "the", "Pipeline", "Pro", ";", "he", "was", "too", "preoccupied", "with", "matters", "at", "hand", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Then, further controversy.", "tokens": ["Then", ",", "further", "controversy", "."], "ner_tags": ["O", "O", "O", "O", "O"]}
{"sentence": "No such contest debacles this year, which means Ben Player is more ambitious than ever to win it clean.", "tokens": ["No", "such", "contest", "debacles", "this", "year", ",", "which", "means", "Ben", "Player", "is", "more", "ambitious", "than", "ever", "to", "win", "it", "clean", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "That wiry frame has filled out during the 13 seasons he's spent hassling for waves amongst the hungry pack of board riders who converge on the famous stretch of sand every northern winter.", "tokens": ["That", "wiry", "frame", "has", "filled", "out", "during", "the", "13", "seasons", "he", "'s", "spent", "hassling", "for", "waves", "amongst", "the", "hungry", "pack", "of", "board", "riders", "who", "converge", "on", "the", "famous", "stretch", "of", "sand", "every", "northern", "winter", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Player won Australian titles as a cadet (under-16s) and as a pro in 2000, part of the new generation that reshaped bodyboarding in Australia.", "tokens": ["Player", "won", "Australian", "titles", "as", "a", "cadet", "(", "under-16s", ")", "and", "as", "a", "pro", "in", "2000", ",", "part", "of", "the", "new", "generation", "that", "reshaped", "bodyboarding", "in", "Australia", "."], "ner_tags": ["B-PER", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "Basically I lost $85,000 for that year and $35,000 that was owed to me, \"says Player.", "tokens": ["Basically", "I", "lost", "$85,000", "for", "that", "year", "and", "$35,000", "that", "was", "owed", "to", "me", ",", "\"", "says", "Player", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O"]}
{"sentence": "\"It's still growing, but with the money remaining in bodyboarding.\"", "tokens": ["\"", "It", "'s", "still", "growing", ",", "but", "with", "the", "money", "remaining", "in", "bodyboarding", ".", "\""], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "For the first two years Player sold advertising for the mag, which was named Movement.", "tokens": ["For", "the", "first", "two", "years", "Player", "sold", "advertising", "for", "the", "mag", ",", "which", "was", "named", "Movement", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O"]}
{"sentence": "Bodyboarders now seek out the sort of aquatic slabs breaking in shallow water that stand-up surfers leave alone.", "tokens": ["Bodyboarders", "now", "seek", "out", "the", "sort", "of", "aquatic", "slabs", "breaking", "in", "shallow", "water", "that", "stand-up", "surfers", "leave", "alone", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "\"I think surfing actually embraces bodyboarding much more now, because it's not really seen as competition -- we're something totally different,\" he says.", "tokens": ["\"", "I", "think", "surfing", "actually", "embraces", "bodyboarding", "much", "more", "now", ",", "because", "it", "'s", "not", "really", "seen", "as", "competition", "--", "we", "'re", "something", "totally", "different", ",", "\"", "he", "says", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Portugal is the exception on the tour, a playful beach break, but it retains its status on the tour due to the carnival atmosphere that the sport attracts in Europe, as well as in South America.", "tokens": ["Portugal", "is", "the", "exception", "on", "the", "tour", ",", "a", "playful", "beach", "break", ",", "but", "it", "retains", "its", "status", "on", "the", "tour", "due", "to", "the", "carnival", "atmosphere", "that", "the", "sport", "attracts", "in", "Europe", ",", "as", "well", "as", "in", "South", "America", "."], "ner_tags": ["B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O"]}
{"sentence": "Kopa\u010dki Rit, situated at the confluence of the Drava and the Danube, the unique preserved wetland area in this part of Europe is not far from Mece.", "tokens": ["Kopa\u010dki", "Rit", ",", "situated", "at", "the", "confluence", "of", "the", "Drava", "and", "the", "Danube", ",", "the", "unique", "preserved", "wetland", "area", "in", "this", "part", "of", "Europe", "is", "not", "far", "from", "Mece", "."], "ner_tags": ["B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "The name Bilka is believed by many to be a mix of the German Billiges Kaufhaus (low-price department store), which its founder, Herman Salling had encountered during his business trips to West Germany.", "tokens": ["The", "name", "Bilka", "is", "believed", "by", "many", "to", "be", "a", "mix", "of", "the", "German", "Billiges", "Kaufhaus", "(", "low-price", "department", "store", ")", ",", "which", "its", "founder", ",", "Herman", "Salling", "had", "encountered", "during", "his", "business", "trips", "to", "West", "Germany", "."], "ner_tags": ["O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O"]}
{"sentence": "Attended James High School, in Houston.", "tokens": ["Attended", "James", "High", "School", ",", "in", "Houston", "."], "ner_tags": ["O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "B-LOC", "O"]}
{"sentence": "Atessis was a member of teams which set school record 30-game winning streak that currently stands as the twelfth-longest in NCAA history and was a three-year letterman and three year starter at left defensive end.", "tokens": ["Atessis", "was", "a", "member", "of", "teams", "which", "set", "school", "record", "30-game", "winning", "streak", "that", "currently", "stands", "as", "the", "twelfth-longest", "in", "NCAA", "history", "and", "was", "a", "three-year", "letterman", "and", "three", "year", "starter", "at", "left", "defensive", "end", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Played in the Senior Bowl in Mobile, Alabama, in January 1971.", "tokens": ["Played", "in", "the", "Senior", "Bowl", "in", "Mobile", ",", "Alabama", ",", "in", "January", "1971", "."], "ner_tags": ["O", "O", "O", "B-MISC", "I-MISC", "O", "B-LOC", "O", "B-LOC", "O", "O", "O", "O", "O"]}
{"sentence": "In 2005 was named to the All-Time University of Texas team by the Austin American-Statesman and was named to the Red River Rivalry All-time team by the Fort Worth Star-Telegram, also in 2005.", "tokens": ["In", "2005", "was", "named", "to", "the", "All-Time", "University", "of", "Texas", "team", "by", "the", "Austin", "American-Statesman", "and", "was", "named", "to", "the", "Red", "River", "Rivalry", "All-time", "team", "by", "the", "Fort", "Worth", "Star-Telegram", ",", "also", "in", "2005", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O"]}
{"sentence": "Asked to drop weight and move to outside linebacker, a position he had never played.", "tokens": ["Asked", "to", "drop", "weight", "and", "move", "to", "outside", "linebacker", ",", "a", "position", "he", "had", "never", "played", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Frequently injured, he was on track to shatter the league record for scoring in 1944 when another injury ended his season two points short.", "tokens": ["Frequently", "injured", ",", "he", "was", "on", "track", "to", "shatter", "the", "league", "record", "for", "scoring", "in", "1944", "when", "another", "injury", "ended", "his", "season", "two", "points", "short", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "In 1998, he was ranked number 53 on The Hockey News list of the 100 Greatest Hockey Players.", "tokens": ["In", "1998", ",", "he", "was", "ranked", "number", "53", "on", "The", "Hockey", "News", "list", "of", "the", "100", "Greatest", "Hockey", "Players", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Brampton City Council is the governing body for the City of Brampton, Ontario.", "tokens": ["Brampton", "City", "Council", "is", "the", "governing", "body", "for", "the", "City", "of", "Brampton", ",", "Ontario", "."], "ner_tags": ["B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "I-LOC", "O", "B-LOC", "O"]}
{"sentence": "The municipality is a part of the administrative municipality of W\u00f6rlitze Winkel, which has a seat in the city of Oranienbau.", "tokens": ["The", "municipality", "is", "a", "part", "of", "the", "administrative", "municipality", "of", "W\u00f6rlitze", "Winkel", ",", "which", "has", "a", "seat", "in", "the", "city", "of", "Oranienbau", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "Hinkley Locomotive Works modified their 2-foot gauge Forney design to run boiler first with an extended frame similar to that installed on Sandy River Railroad # 1 following a wreck in early 1882.", "tokens": ["Hinkley", "Locomotive", "Works", "modified", "their", "2-foot", "gauge", "Forney", "design", "to", "run", "boiler", "first", "with", "an", "extended", "frame", "similar", "to", "that", "installed", "on", "Sandy", "River", "Railroad", "#", "1", "following", "a", "wreck", "in", "early", "1882", "."], "ner_tags": ["B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Original Hinkley locomotives # 1-2 were replaced by # 5-6 of an improved design with pilot wheels.", "tokens": ["Original", "Hinkley", "locomotives", "#", "1-2", "were", "replaced", "by", "#", "5-6", "of", "an", "improved", "design", "with", "pilot", "wheels", "."], "ner_tags": ["O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The first consisted of baggage # 10, RPO # 25, and one or two coaches.", "tokens": ["The", "first", "consisted", "of", "baggage", "#", "10", ",", "RPO", "#", "25", ",", "and", "one", "or", "two", "coaches", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Locomotive # 8 was the heaviest locomotive on any 2-foot gauge railway in Maine.", "tokens": ["Locomotive", "#", "8", "was", "the", "heaviest", "locomotive", "on", "any", "2-foot", "gauge", "railway", "in", "Maine", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "Milepost 0: Bridgton Junction - Interchange yard with the Portland and Ogdensburg (later Maine Central Railroad Mountain Division.)", "tokens": ["Milepost", "0", ":", "Bridgton", "Junction", "-", "Interchange", "yard", "with", "the", "Portland", "and", "Ogdensburg", "(", "later", "Maine", "Central", "Railroad", "Mountain", "Division", ".", ")"], "ner_tags": ["O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "I-LOC", "O", "O", "B-LOC", "I-LOC", "I-LOC", "I-LOC", "I-LOC", "O", "O"]}
{"sentence": "Milepost 1: granite masonry arch over Hancock Brook.", "tokens": ["Milepost", "1", ":", "granite", "masonry", "arch", "over", "Hancock", "Brook", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O"]}
{"sentence": "Milepost 4.4: Twin Lake - small flag stop passenger shelter.", "tokens": ["Milepost", "4.4", ":", "Twin", "Lake", "-", "small", "flag", "stop", "passenger", "shelter", "."], "ner_tags": ["O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Milepost 9.0: Perley's Mills - southbound spur with small flag stop passenger shelter.", "tokens": ["Milepost", "9.0", ":", "Perley", "'s", "Mills", "-", "southbound", "spur", "with", "small", "flag", "stop", "passenger", "shelter", "."], "ner_tags": ["O", "O", "O", "B-LOC", "I-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Milepost 15.8: Bridgton - had the largest population of any village served by the Maine 2-foot gauge railroads.", "tokens": ["Milepost", "15.8", ":", "Bridgton", "-", "had", "the", "largest", "population", "of", "any", "village", "served", "by", "the", "Maine", "2-foot", "gauge", "railroads", "."], "ner_tags": ["O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O"]}
{"sentence": "Milepost 20.7: Harrison - agent's station with a passing siding and several southbound spurs serving a freight house, a cannery, a grain store, a 2-track car shed, and a turntable with a single-stall enginehouse.", "tokens": ["Milepost", "20.7", ":", "Harrison", "-", "agent", "'s", "station", "with", "a", "passing", "siding", "and", "several", "southbound", "spurs", "serving", "a", "freight", "house", ",", "a", "cannery", ",", "a", "grain", "store", ",", "a", "2-track", "car", "shed", ",", "and", "a", "turntable", "with", "a", "single-stall", "enginehouse", "."], "ner_tags": ["O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The car was sold for use as a restaurant in 1935.", "tokens": ["The", "car", "was", "sold", "for", "use", "as", "a", "restaurant", "in", "1935", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Cars # 22 and # 21/14 were the only 2-foot gauge tank cars in Maine.", "tokens": ["Cars", "#", "22", "and", "#", "21/14", "were", "the", "only", "2-foot", "gauge", "tank", "cars", "in", "Maine", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "It is also a partner in the Masters of Digital Media program run by Great Northern Way Campus Ltd, and rents training space there with several other institutions.", "tokens": ["It", "is", "also", "a", "partner", "in", "the", "Masters", "of", "Digital", "Media", "program", "run", "by", "Great", "Northern", "Way", "Campus", "Ltd", ",", "and", "rents", "training", "space", "there", "with", "several", "other", "institutions", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "His manager was future Hall of Famer Bill McKechnie.", "tokens": ["His", "manager", "was", "future", "Hall", "of", "Famer", "Bill", "McKechnie", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O"]}
{"sentence": "Stan believes that this will destroy his chance for a promotion.", "tokens": ["Stan", "believes", "that", "this", "will", "destroy", "his", "chance", "for", "a", "promotion", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "At a diner, Bullock calls Hayley a slut, which offends Stan, and the two CIA men get into a lengthy fist-fight (with some audio-commentary from Klaus).", "tokens": ["At", "a", "diner", ",", "Bullock", "calls", "Hayley", "a", "slut", ",", "which", "offends", "Stan", ",", "and", "the", "two", "CIA", "men", "get", "into", "a", "lengthy", "fist-fight", "(", "with", "some", "audio-commentary", "from", "Klaus", ")", "."], "ner_tags": ["O", "O", "O", "O", "B-PER", "O", "B-PER", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O"]}
{"sentence": "A parcel just south of it was named Lincoln Park the same year.", "tokens": ["A", "parcel", "just", "south", "of", "it", "was", "named", "Lincoln", "Park", "the", "same", "year", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O"]}
{"sentence": "Rebuilding of the reservoir to convert it from open-air to covered started in April 2003 and lasted through summer 2005.", "tokens": ["Rebuilding", "of", "the", "reservoir", "to", "convert", "it", "from", "open-air", "to", "covered", "started", "in", "April", "2003", "and", "lasted", "through", "summer", "2005", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "In 1972, Caloi introduced the Caloi 10, which became a cultural landmark and established the name Caloi in Brazil.", "tokens": ["In", "1972", ",", "Caloi", "introduced", "the", "Caloi", "10", ",", "which", "became", "a", "cultural", "landmark", "and", "established", "the", "name", "Caloi", "in", "Brazil", "."], "ner_tags": ["O", "O", "O", "B-ORG", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "B-LOC", "O"]}
{"sentence": "Guido died in 1955, and the company was directed by his son Bruno Caloi until 1999.", "tokens": ["Guido", "died", "in", "1955", ",", "and", "the", "company", "was", "directed", "by", "his", "son", "Bruno", "Caloi", "until", "1999", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O"]}
{"sentence": "This tracks comes off their 1998 album, Electro-Shock Blues.", "tokens": ["This", "tracks", "comes", "off", "their", "1998", "album", ",", "Electro-Shock", "Blues", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O"]}
{"sentence": "Savage lives in Saskatchewan.", "tokens": ["Savage", "lives", "in", "Saskatchewan", "."], "ner_tags": ["B-PER", "O", "O", "B-LOC", "O"]}
{"sentence": "\"She insists on 'a clean sound', has banned backing vocals and says her pet hate is' over-singing '.\"", "tokens": ["\"", "She", "insists", "on", "'", "a", "clean", "sound", "'", ",", "has", "banned", "backing", "vocals", "and", "says", "her", "pet", "hate", "is", "'", "over-singing", "'", ".", "\""], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Because of the power of the drugs the handlers then have to move quickly to secure the animal for transport, monitor its vital signs, protect its eyes and ears, and then inject reversing drugs when needed.", "tokens": ["Because", "of", "the", "power", "of", "the", "drugs", "the", "handlers", "then", "have", "to", "move", "quickly", "to", "secure", "the", "animal", "for", "transport", ",", "monitor", "its", "vital", "signs", ",", "protect", "its", "eyes", "and", "ears", ",", "and", "then", "inject", "reversing", "drugs", "when", "needed", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "He began playing in 2001 and has won tournaments in Walsall, Melbourne, Luton and London (where he won the \u00a3500 no limit hold'em event of the 200 British Open, defeating Lucy Rokach in the heads-up confrontation.", "tokens": ["He", "began", "playing", "in", "2001", "and", "has", "won", "tournaments", "in", "Walsall", ",", "Melbourne", ",", "Luton", "and", "London", "(", "where", "he", "won", "the", "\u00a3500", "no", "limit", "hold'em", "event", "of", "the", "200", "British", "Open", ",", "defeating", "Lucy", "Rokach", "in", "the", "heads-up", "confrontation", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "O", "B-LOC", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "B-MISC", "I-MISC", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O"]}
{"sentence": "Citrone eventually served approximately twelve months for the offence and was ordered to pay some \u00a3370,000 as part of a Confiscation Order ordered in favour of the Crown.", "tokens": ["Citrone", "eventually", "served", "approximately", "twelve", "months", "for", "the", "offence", "and", "was", "ordered", "to", "pay", "some", "\u00a3370,000", "as", "part", "of", "a", "Confiscation", "Order", "ordered", "in", "favour", "of", "the", "Crown", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O"]}
{"sentence": "As of 2007 his total live tournament winnings exceed $670,000.", "tokens": ["As", "of", "2007", "his", "total", "live", "tournament", "winnings", "exceed", "$670,000", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Carmen Cavallaro (May 6, 1913 -- October 12, 1989) was an American pianist born in New York.", "tokens": ["Carmen", "Cavallaro", "(", "May", "6", ",", "1913", "--", "October", "12", ",", "1989", ")", "was", "an", "American", "pianist", "born", "in", "New", "York", "."], "ner_tags": ["B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "B-LOC", "I-LOC", "O"]}
{"sentence": "After four years he switched to a series of other big bands, including Rudy Vallee's in 1937.", "tokens": ["After", "four", "years", "he", "switched", "to", "a", "series", "of", "other", "big", "bands", ",", "including", "Rudy", "Vallee", "'s", "in", "1937", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O"]}
{"sentence": "Cavallaro developed a piano style of glittering and rippling arpeggios to augment his melody, which was often arranged in thick and lush triple and quadruple octave chords.", "tokens": ["Cavallaro", "developed", "a", "piano", "style", "of", "glittering", "and", "rippling", "arpeggios", "to", "augment", "his", "melody", ",", "which", "was", "often", "arranged", "in", "thick", "and", "lush", "triple", "and", "quadruple", "octave", "chords", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "His most celebrated film achievement was playing the piano music for actor Tyrone Power's hands to mime, in The Eddy Duchin Story (1956).", "tokens": ["His", "most", "celebrated", "film", "achievement", "was", "playing", "the", "piano", "music", "for", "actor", "Tyrone", "Power", "'s", "hands", "to", "mime", ",", "in", "The", "Eddy", "Duchin", "Story", "(", "1956", ")", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O"]}
{"sentence": "At the time, his father had bought tickets to a Buddy Rich concert and could not find someone to watch his son, so he took young Carter along to the show.", "tokens": ["At", "the", "time", ",", "his", "father", "had", "bought", "tickets", "to", "a", "Buddy", "Rich", "concert", "and", "could", "not", "find", "someone", "to", "watch", "his", "son", ",", "so", "he", "took", "young", "Carter", "along", "to", "the", "show", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O"]}
{"sentence": "Secrets would perform throughout Virginia, often at Miller's, the bar in Charlottesville where Dave Matthews worked as a bartender.", "tokens": ["Secrets", "would", "perform", "throughout", "Virginia", ",", "often", "at", "Miller's", ",", "the", "bar", "in", "Charlottesville", "where", "Dave", "Matthews", "worked", "as", "a", "bartender", "."], "ner_tags": ["O", "O", "O", "O", "B-LOC", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "B-LOC", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O"]}
{"sentence": "With his second wife, Karen, he has two children: a daughter, Nadja Angelique Beauford born October 5th 2001 and a son Marcus Carrington Beauford born in December 2004.", "tokens": ["With", "his", "second", "wife", ",", "Karen", ",", "he", "has", "two", "children", ":", "a", "daughter", ",", "Nadja", "Angelique", "Beauford", "born", "October", "5th", "2001", "and", "a", "son", "Marcus", "Carrington", "Beauford", "born", "in", "December", "2004", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "I-PER", "O", "B-PER", "I-PER", "I-PER", "O", "O", "O", "B-PER", "I-PER", "I-PER", "O", "O", "O", "O", "O"]}
{"sentence": "In 1991, the mall expanded, adding a food court and three new anchors: Thalhimer's, JC Penney, and Sears, and was renamed Cary Towne Center.", "tokens": ["In", "1991", ",", "the", "mall", "expanded", ",", "adding", "a", "food", "court", "and", "three", "new", "anchors", ":", "Thalhimer's", ",", "JC", "Penney", ",", "and", "Sears", ",", "and", "was", "renamed", "Cary", "Towne", "Center", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "B-ORG", "I-ORG", "O", "O", "B-ORG", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O"]}
{"sentence": "Cable systems in the early 1980s had far more limited channel capacity than they do today (usually only a few dozen channels in most cities).", "tokens": ["Cable", "systems", "in", "the", "early", "1980s", "had", "far", "more", "limited", "channel", "capacity", "than", "they", "do", "today", "(", "usually", "only", "a", "few", "dozen", "channels", "in", "most", "cities", ")", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Centennial Tower and PlazaCenter are next door.", "tokens": ["Centennial", "Tower", "and", "PlazaCenter", "are", "next", "door", "."], "ner_tags": ["B-ORG", "I-ORG", "O", "B-ORG", "O", "O", "O", "O"]}
{"sentence": "He played in Single A in 2002 and recorded a 2.26 ERA which ranked him 10th in all of minor league baseball.", "tokens": ["He", "played", "in", "Single", "A", "in", "2002", "and", "recorded", "a", "2.26", "ERA", "which", "ranked", "him", "10th", "in", "all", "of", "minor", "league", "baseball", "."], "ner_tags": ["O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "On December 12, 2004, Gaudin was traded to the Toronto Blue Jays for backup catcher Kevin Cash.", "tokens": ["On", "December", "12", ",", "2004", ",", "Gaudin", "was", "traded", "to", "the", "Toronto", "Blue", "Jays", "for", "backup", "catcher", "Kevin", "Cash", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "B-PER", "I-PER", "O"]}
{"sentence": "In his first season as an Athletic, he posted various career highs and his ERA (3.09) and opponents batting average were the lowest of his career.", "tokens": ["In", "his", "first", "season", "as", "an", "Athletic", ",", "he", "posted", "various", "career", "highs", "and", "his", "ERA", "(", "3.09", ")", "and", "opponents", "batting", "average", "were", "the", "lowest", "of", "his", "career", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The language is of the Chamicuro people who number between 10 and 20.", "tokens": ["The", "language", "is", "of", "the", "Chamicuro", "people", "who", "number", "between", "10", "and", "20", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Champdor is a commune in the French d\u00e9partement of Ai.", "tokens": ["Champdor", "is", "a", "commune", "in", "the", "French", "d\u00e9partement", "of", "Ai", "."], "ner_tags": ["B-LOC", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "B-LOC", "O"]}
{"sentence": "Charan Jeath Singh is a Fiji Indian who has been involved in local Government and national politics in Fiji representing various political organisations.", "tokens": ["Charan", "Jeath", "Singh", "is", "a", "Fiji", "Indian", "who", "has", "been", "involved", "in", "local", "Government", "and", "national", "politics", "in", "Fiji", "representing", "various", "political", "organisations", "."], "ner_tags": ["B-PER", "I-PER", "I-PER", "O", "O", "B-LOC", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O"]}
{"sentence": "In the 2006 general election, he contested the Vanua Levu West Indian Communal Constituency, Fiji) for the National Alliance Party (NAPF) and again lost with only 14% of the votes cast.", "tokens": ["In", "the", "2006", "general", "election", ",", "he", "contested", "the", "Vanua", "Levu", "West", "Indian", "Communal", "Constituency", ",", "Fiji", ")", "for", "the", "National", "Alliance", "Party", "(", "NAPF", ")", "and", "again", "lost", "with", "only", "14%", "of", "the", "votes", "cast", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "I-LOC", "I-LOC", "O", "O", "O", "B-LOC", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "After just a year at Coburg he returned to the league and played with North Melbourne, becoming a regular in their side during the 1930s.", "tokens": ["After", "just", "a", "year", "at", "Coburg", "he", "returned", "to", "the", "league", "and", "played", "with", "North", "Melbourne", ",", "becoming", "a", "regular", "in", "their", "side", "during", "the", "1930s", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "It has been used as a political prison by Germans during the occupation of France from 1940 to 1944.", "tokens": ["It", "has", "been", "used", "as", "a", "political", "prison", "by", "Germans", "during", "the", "occupation", "of", "France", "from", "1940", "to", "1944", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O"]}
{"sentence": "Christopher Bailey is a lecturer of English at the University of Brighton and is an occasional screenwriter for television.", "tokens": ["Christopher", "Bailey", "is", "a", "lecturer", "of", "English", "at", "the", "University", "of", "Brighton", "and", "is", "an", "occasional", "screenwriter", "for", "television", "."], "ner_tags": ["B-PER", "I-PER", "O", "O", "O", "O", "B-MISC", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "As a result, the two Mara scripts were Bailey's final broadcast work for both Doctor Who and television in general and he returned to a career in academia.", "tokens": ["As", "a", "result", ",", "the", "two", "Mara", "scripts", "were", "Bailey", "'s", "final", "broadcast", "work", "for", "both", "Doctor", "Who", "and", "television", "in", "general", "and", "he", "returned", "to", "a", "career", "in", "academia", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "After a twenty minute stand-off he aimed the pistol at Lieutenant Michael Weippert, who responded by opening fire.", "tokens": ["After", "a", "twenty", "minute", "stand-off", "he", "aimed", "the", "pistol", "at", "Lieutenant", "Michael", "Weippert", ",", "who", "responded", "by", "opening", "fire", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Orlando Sentinel reported that Ralph Penley was not told of events until after his son was shot.", "tokens": ["The", "Orlando", "Sentinel", "reported", "that", "Ralph", "Penley", "was", "not", "told", "of", "events", "until", "after", "his", "son", "was", "shot", "."], "ner_tags": ["O", "B-ORG", "I-ORG", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "It remained on the F1 calendar for 30 out of the next 34 years.", "tokens": ["It", "remained", "on", "the", "F1", "calendar", "for", "30", "out", "of", "the", "next", "34", "years", "."], "ner_tags": ["O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The most famous corner in the circuit is the Tarzanbocht (Tarzan corner) which provides excellent overtaking opportunities.", "tokens": ["The", "most", "famous", "corner", "in", "the", "circuit", "is", "the", "Tarzanbocht", "(", "Tarzan", "corner", ")", "which", "provides", "excellent", "overtaking", "opportunities", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "This was later converted to NASCAR 4, NASCAR 2002, 2003 and other simulations using a converter from website 'The Pits'.", "tokens": ["This", "was", "later", "converted", "to", "NASCAR", "4", ",", "NASCAR", "2002", ",", "2003", "and", "other", "simulations", "using", "a", "converter", "from", "website", "'", "The", "Pits", "'", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O"]}
{"sentence": "Clergoux is a village and commune in the Corr\u00e8z d\u00e9partement of central Franc.", "tokens": ["Clergoux", "is", "a", "village", "and", "commune", "in", "the", "Corr\u00e8z", "d\u00e9partement", "of", "central", "Franc", "."], "ner_tags": ["B-LOC", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "Clinical Hospital Mostar (Croatian Klini\u010dka bolnica Mostar) is the largest hospital in Mostar, Bosnia and Herzegovina.", "tokens": ["Clinical", "Hospital", "Mostar", "(", "Croatian", "Klini\u010dka", "bolnica", "Mostar", ")", "is", "the", "largest", "hospital", "in", "Mostar", ",", "Bosnia", "and", "Herzegovina", "."], "ner_tags": ["B-ORG", "I-ORG", "I-ORG", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "I-LOC", "I-LOC", "O"]}
{"sentence": "The Cold Spring Harbor Laboratory (CSHL) is a private, non-profit institution with research programs focusing on cancer, neurobiology, plant genetics, genomics and bioinformatics.", "tokens": ["The", "Cold", "Spring", "Harbor", "Laboratory", "(", "CSHL", ")", "is", "a", "private", ",", "non-profit", "institution", "with", "research", "programs", "focusing", "on", "cancer", ",", "neurobiology", ",", "plant", "genetics", ",", "genomics", "and", "bioinformatics", "."], "ner_tags": ["O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Carnegie Institution Department of Genetics scientists at Cold Spring Harbor made innumerable contributions to the sciences of genetics, medicine, and the then-infant science of molecular biology, and in 1962 its facilities merged with those of The Brooklyn Institute's Biological Laboratory to create what is known today as Cold Spring Harbor Laboratory.", "tokens": ["The", "Carnegie", "Institution", "Department", "of", "Genetics", "scientists", "at", "Cold", "Spring", "Harbor", "made", "innumerable", "contributions", "to", "the", "sciences", "of", "genetics", ",", "medicine", ",", "and", "the", "then-infant", "science", "of", "molecular", "biology", ",", "and", "in", "1962", "its", "facilities", "merged", "with", "those", "of", "The", "Brooklyn", "Institute", "'s", "Biological", "Laboratory", "to", "create", "what", "is", "known", "today", "as", "Cold", "Spring", "Harbor", "Laboratory", "."], "ner_tags": ["O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O"]}
{"sentence": "Currently, cancer biologist Bruce Stillman serves as laboratory President.", "tokens": ["Currently", ",", "cancer", "biologist", "Bruce", "Stillman", "serves", "as", "laboratory", "President", "."], "ner_tags": ["O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O"]}
{"sentence": "Aside from its scientific mission, the laboratory is host to world-class scientific conferences on a variety of topics.", "tokens": ["Aside", "from", "its", "scientific", "mission", ",", "the", "laboratory", "is", "host", "to", "world-class", "scientific", "conferences", "on", "a", "variety", "of", "topics", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Salvador Luria and Max Delbr\u00fcc founded the Phage Course in 1948, a course that trained many of the leaders of the new field of molecular genetics.", "tokens": ["Salvador", "Luria", "and", "Max", "Delbr\u00fcc", "founded", "the", "Phage", "Course", "in", "1948", ",", "a", "course", "that", "trained", "many", "of", "the", "leaders", "of", "the", "new", "field", "of", "molecular", "genetics", "."], "ner_tags": ["B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "It is part of the Coleman Federal Correctional Complex.", "tokens": ["It", "is", "part", "of", "the", "Coleman", "Federal", "Correctional", "Complex", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-LOC", "I-LOC", "I-LOC", "I-LOC", "O"]}
{"sentence": "Held since 1990, the tournament is played on outdoor hardcourts.", "tokens": ["Held", "since", "1990", ",", "the", "tournament", "is", "played", "on", "outdoor", "hardcourts", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Later it became a 6/40 game.", "tokens": ["Later", "it", "became", "a", "6/40", "game", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Top-prize winners choose lump sum or annuity within 60 days of claiming the jackpot.", "tokens": ["Top-prize", "winners", "choose", "lump", "sum", "or", "annuity", "within", "60", "days", "of", "claiming", "the", "jackpot", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "A Classic Lotto jackpot of $3.5 million (annuity) on a ticket bought in February 2006 went unclaimed.", "tokens": ["A", "Classic", "Lotto", "jackpot", "of", "$3.5", "million", "(", "annuity", ")", "on", "a", "ticket", "bought", "in", "February", "2006", "went", "unclaimed", "."], "ner_tags": ["O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "It is currently served by the South West Trains, Southern, and First Great Western train operating companies.", "tokens": ["It", "is", "currently", "served", "by", "the", "South", "West", "Trains", ",", "Southern", ",", "and", "First", "Great", "Western", "train", "operating", "companies", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "B-ORG", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O"]}
{"sentence": "Crazyhead are an English punk rock / garage rock band from Leicester.", "tokens": ["Crazyhead", "are", "an", "English", "punk", "rock", "/", "garage", "rock", "band", "from", "Leicester", "."], "ner_tags": ["B-ORG", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "Their second single \"Baby Turpentine\" reached number 4 in the Indie Chart.", "tokens": ["Their", "second", "single", "\"", "Baby", "Turpentine", "\"", "reached", "number", "4", "in", "the", "Indie", "Chart", "."], "ner_tags": ["O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O"]}
{"sentence": "Later that year they played at the Namibian Independence Day concert to an audience of 50,000.", "tokens": ["Later", "that", "year", "they", "played", "at", "the", "Namibian", "Independence", "Day", "concert", "to", "an", "audience", "of", "50,000", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O"]}
{"sentence": "They started recording material for a new album, but the departure of Anderson to work abroad meant the end of the band.", "tokens": ["They", "started", "recording", "material", "for", "a", "new", "album", ",", "but", "the", "departure", "of", "Anderson", "to", "work", "abroad", "meant", "the", "end", "of", "the", "band", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The partnership went public in 1994, trading under the stock symbol CRO on the New York Stock Exchange.", "tokens": ["The", "partnership", "went", "public", "in", "1994", ",", "trading", "under", "the", "stock", "symbol", "CRO", "on", "the", "New", "York", "Stock", "Exchange", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O"]}
{"sentence": "He was born in Humberstone Township in 1825, the son of John Near, and grew up there.", "tokens": ["He", "was", "born", "in", "Humberstone", "Township", "in", "1825", ",", "the", "son", "of", "John", "Near", ",", "and", "grew", "up", "there", "."], "ner_tags": ["O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Prodan won five consecutive league titles with Steaua before moving to play in Spain.", "tokens": ["Prodan", "won", "five", "consecutive", "league", "titles", "with", "Steaua", "before", "moving", "to", "play", "in", "Spain", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "He specialises in algebra and group theory.", "tokens": ["He", "specialises", "in", "algebra", "and", "group", "theory", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "It was released in 2003 on Aftermath Records.", "tokens": ["It", "was", "released", "in", "2003", "on", "Aftermath", "Records", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O"]}
{"sentence": "The band's music has been described as electro / synthpop with a slightly sombre overtone and introverted lyrical sensibility.", "tokens": ["The", "band", "'s", "music", "has", "been", "described", "as", "electro", "/", "synthpop", "with", "a", "slightly", "sombre", "overtone", "and", "introverted", "lyrical", "sensibility", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The band were recently selected to play on the 6th series of the acclaimed RT\u00c9 music show Other Voices.", "tokens": ["The", "band", "were", "recently", "selected", "to", "play", "on", "the", "6th", "series", "of", "the", "acclaimed", "RT\u00c9", "music", "show", "Other", "Voices", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "B-MISC", "I-MISC", "O"]}
{"sentence": "The start was given in 1990 with the first Cyrillic fonts in the PostScript format, the first Laser printer with in - built Cyrillic fonts in 1991 and the first professional program for complete Cyrillic support under MS Windows - the Flex Type - in 1992.", "tokens": ["The", "start", "was", "given", "in", "1990", "with", "the", "first", "Cyrillic", "fonts", "in", "the", "PostScript", "format", ",", "the", "first", "Laser", "printer", "with", "in", "-", "built", "Cyrillic", "fonts", "in", "1991", "and", "the", "first", "professional", "program", "for", "complete", "Cyrillic", "support", "under", "MS", "Windows", "-", "the", "Flex", "Type", "-", "in", "1992", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "B-ORG", "B-MISC", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O"]}
{"sentence": "In only a few years - and 100 000 devices sold in 11 countries around the World - Datecs became the best known brand of electronic cash registers with fiscal memory in Central and Eastern Europe.", "tokens": ["In", "only", "a", "few", "years", "-", "and", "100", "000", "devices", "sold", "in", "11", "countries", "around", "the", "World", "-", "Datecs", "became", "the", "best", "known", "brand", "of", "electronic", "cash", "registers", "with", "fiscal", "memory", "in", "Central", "and", "Eastern", "Europe", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "I-LOC", "I-LOC", "O"]}
{"sentence": "Snoke received his PhD in physics from the University of Illinois at Urbana-Champaign.", "tokens": ["Snoke", "received", "his", "PhD", "in", "physics", "from", "the", "University", "of", "Illinois", "at", "Urbana-Champaign", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "B-LOC", "O"]}
{"sentence": "In 2004 co-authored an article, with Michael Behe, a senior fellow of the Discovery Institute's Center for Science and Culture, in the scientific journal Protein Science, which received widespread criticism.", "tokens": ["In", "2004", "co-authored", "an", "article", ",", "with", "Michael", "Behe", ",", "a", "senior", "fellow", "of", "the", "Discovery", "Institute", "'s", "Center", "for", "Science", "and", "Culture", ",", "in", "the", "scientific", "journal", "Protein", "Science", ",", "which", "received", "widespread", "criticism", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O"]}
{"sentence": "In his ruling, Judge Jones noted that \"A review of the article indicates that it does not mention either irreducible complexity or ID.", "tokens": ["In", "his", "ruling", ",", "Judge", "Jones", "noted", "that", "\"", "A", "review", "of", "the", "article", "indicates", "that", "it", "does", "not", "mention", "either", "irreducible", "complexity", "or", "ID", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Davis Gym could hold around 1,100 people.", "tokens": ["Davis", "Gym", "could", "hold", "around", "1,100", "people", "."], "ner_tags": ["B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O"]}
{"sentence": "In return Magedson filed a lawsuit under RICO.", "tokens": ["In", "return", "Magedson", "filed", "a", "lawsuit", "under", "RICO", "."], "ner_tags": ["O", "O", "B-PER", "O", "O", "O", "O", "B-MISC", "O"]}
{"sentence": "A descendant of farmer immigrants from Bentheim, Germany, Langejans was born and raised in Holland, Michigan, where his luthier shop keeps him busy building guitars for such notable musicians as Thom Bresh (Merle Travis's son) and Jars of Clay.", "tokens": ["A", "descendant", "of", "farmer", "immigrants", "from", "Bentheim", ",", "Germany", ",", "Langejans", "was", "born", "and", "raised", "in", "Holland", ",", "Michigan", ",", "where", "his", "luthier", "shop", "keeps", "him", "busy", "building", "guitars", "for", "such", "notable", "musicians", "as", "Thom", "Bresh", "(", "Merle", "Travis", "'s", "son", ")", "and", "Jars", "of", "Clay", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "O", "B-PER", "O", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O"]}
{"sentence": "He has made over 1200 guitars as of 2006, many of them custom-made-to-order, putting Langejans guitars in the hands of numerous stars from all genre of music.", "tokens": ["He", "has", "made", "over", "1200", "guitars", "as", "of", "2006", ",", "many", "of", "them", "custom-made-to-order", ",", "putting", "Langejans", "guitars", "in", "the", "hands", "of", "numerous", "stars", "from", "all", "genre", "of", "music", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The standard production time is generally one year.", "tokens": ["The", "standard", "production", "time", "is", "generally", "one", "year", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "I Dewa Gede Budjana or Dewa Budjana (born August 30, 1963 in Waikabubak) is an Indonesian guitarist, songwriter and composer.", "tokens": ["I", "Dewa", "Gede", "Budjana", "or", "Dewa", "Budjana", "(", "born", "August", "30", ",", "1963", "in", "Waikabubak", ")", "is", "an", "Indonesian", "guitarist", ",", "songwriter", "and", "composer", "."], "ner_tags": ["B-PER", "I-PER", "I-PER", "I-PER", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Budjana's passion and talent in music, especially guitar, has been very dominant since he was in an elementary school in Klungkung, Bali.", "tokens": ["Budjana", "'s", "passion", "and", "talent", "in", "music", ",", "especially", "guitar", ",", "has", "been", "very", "dominant", "since", "he", "was", "in", "an", "elementary", "school", "in", "Klungkung", ",", "Bali", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "O"]}
{"sentence": "In 1976, when he was thirteen, Budjana's name started to be seen in the music world in Surabaya.", "tokens": ["In", "1976", ",", "when", "he", "was", "thirteen", ",", "Budjana", "'s", "name", "started", "to", "be", "seen", "in", "the", "music", "world", "in", "Surabaya", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "One year after that, Budjana decided to fly to Jakarta in order to expand his music career.", "tokens": ["One", "year", "after", "that", ",", "Budjana", "decided", "to", "fly", "to", "Jakarta", "in", "order", "to", "expand", "his", "music", "career", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "His current band, Gigi, was formed in 1994 with Baron (guitar), Thomas (bass), Armand (vocal) and Ronald (drum).", "tokens": ["His", "current", "band", ",", "Gigi", ",", "was", "formed", "in", "1994", "with", "Baron", "(", "guitar", ")", ",", "Thomas", "(", "bass", ")", ",", "Armand", "(", "vocal", ")", "and", "Ronald", "(", "drum", ")", "."], "ner_tags": ["O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O"]}
{"sentence": "Spain allied with France during the Seven Years' War against Great Britain.", "tokens": ["Spain", "allied", "with", "France", "during", "the", "Seven", "Years", "'", "War", "against", "Great", "Britain", "."], "ner_tags": ["B-LOC", "O", "O", "B-LOC", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "B-LOC", "I-LOC", "O"]}
{"sentence": "The British force never materialized.", "tokens": ["The", "British", "force", "never", "materialized", "."], "ner_tags": ["O", "B-MISC", "O", "O", "O", "O"]}
{"sentence": "She fled again to Abra, where she was captured.", "tokens": ["She", "fled", "again", "to", "Abra", ",", "where", "she", "was", "captured", "."], "ner_tags": ["O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Digilogue is a music album of recordings which became the twentieth commercial release by the British avant-garde music group: zoviet*france:.", "tokens": ["Digilogue", "is", "a", "music", "album", "of", "recordings", "which", "became", "the", "twentieth", "commercial", "release", "by", "the", "British", "avant-garde", "music", "group", ":", "zoviet*france", ":", "."], "ner_tags": ["B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "B-ORG", "O", "O"]}
{"sentence": "digilogue was recorded using a mixture of failing analogue equipment and high-end digital equipment, and monitored in our studio exclusively on damaged hi-fi speakers.", "tokens": ["digilogue", "was", "recorded", "using", "a", "mixture", "of", "failing", "analogue", "equipment", "and", "high-end", "digital", "equipment", ",", "and", "monitored", "in", "our", "studio", "exclusively", "on", "damaged", "hi-fi", "speakers", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "1996 edition", "tokens": ["1996", "edition"], "ner_tags": ["O", "O"]}
{"sentence": "The island of Divar (Divar-derived from the word Dipavati or 'small Island' in Konkani) lies in the state of Goa, India.", "tokens": ["The", "island", "of", "Divar", "(", "Divar-derived", "from", "the", "word", "Dipavati", "or", "'", "small", "Island", "'", "in", "Konkani", ")", "lies", "in", "the", "state", "of", "Goa", ",", "India", "."], "ner_tags": ["O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "O"]}
{"sentence": "Though in a manner equally typical of modern Goa, of late the paddy fields lie fallow and overrun with weeds.", "tokens": ["Though", "in", "a", "manner", "equally", "typical", "of", "modern", "Goa", ",", "of", "late", "the", "paddy", "fields", "lie", "fallow", "and", "overrun", "with", "weeds", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "It was destroyed by the sultan of the Deccan in the middle of the 14th century and reconstructed at the same locality by Madhav Mantri of Vijaynagar at the close of the same century.", "tokens": ["It", "was", "destroyed", "by", "the", "sultan", "of", "the", "Deccan", "in", "the", "middle", "of", "the", "14th", "century", "and", "reconstructed", "at", "the", "same", "locality", "by", "Madhav", "Mantri", "of", "Vijaynagar", "at", "the", "close", "of", "the", "same", "century", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "And on the foundations of the same temple a prayer and catechism house was constructed in 1563, which was later transformed into the chapel of Our Lady of Cande laria. \"", "tokens": ["And", "on", "the", "foundations", "of", "the", "same", "temple", "a", "prayer", "and", "catechism", "house", "was", "constructed", "in", "1563", ",", "which", "was", "later", "transformed", "into", "the", "chapel", "of", "Our", "Lady", "of", "Cande", "laria", ".", "\""], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "I-PER", "I-PER", "I-PER", "O", "O"]}
{"sentence": "Bonderam Festival: The festival is a memoir to the dispute and the fury of the Villagers over the Portuguese system of resolving the disputes.", "tokens": ["Bonderam", "Festival", ":", "The", "festival", "is", "a", "memoir", "to", "the", "dispute", "and", "the", "fury", "of", "the", "Villagers", "over", "the", "Portuguese", "system", "of", "resolving", "the", "disputes", "."], "ner_tags": ["B-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Each section of the village has a float at the parade.", "tokens": ["Each", "section", "of", "the", "village", "has", "a", "float", "at", "the", "parade", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Unfortunately though, every time the bell was struck, it shattered the windows of the church and the houses in the vicinity, so a deal was struck and the bell was exchanged with that of the Se Cathedral.", "tokens": ["Unfortunately", "though", ",", "every", "time", "the", "bell", "was", "struck", ",", "it", "shattered", "the", "windows", "of", "the", "church", "and", "the", "houses", "in", "the", "vicinity", ",", "so", "a", "deal", "was", "struck", "and", "the", "bell", "was", "exchanged", "with", "that", "of", "the", "Se", "Cathedral", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O"]}
{"sentence": "Their son, Philipp Veit, would later become part of a circle of German Christian painters called \"the Nazarenes,\" who influenced the later English painters in the Pre-Raphaelite Brotherhood.", "tokens": ["Their", "son", ",", "Philipp", "Veit", ",", "would", "later", "become", "part", "of", "a", "circle", "of", "German", "Christian", "painters", "called", "\"", "the", "Nazarenes", ",", "\"", "who", "influenced", "the", "later", "English", "painters", "in", "the", "Pre-Raphaelite", "Brotherhood", "."], "ner_tags": ["O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "B-ORG", "I-ORG", "O"]}
{"sentence": "They lived in Paris from 1802 until 1804, and after her divorce they married as Protestants.", "tokens": ["They", "lived", "in", "Paris", "from", "1802", "until", "1804", ",", "and", "after", "her", "divorce", "they", "married", "as", "Protestants", "."], "ner_tags": ["O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O"]}
{"sentence": "There, she lived with her son Philipp (also a convert to a medieval style of Catholicism) until her death in 1839.", "tokens": ["There", ",", "she", "lived", "with", "her", "son", "Philipp", "(", "also", "a", "convert", "to", "a", "medieval", "style", "of", "Catholicism", ")", "until", "her", "death", "in", "1839", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Her nephew was Felix Mendelssohn, the composer, who with his sister, Fanny, were considered nearly the equals of the Mozarts as child prodigies.", "tokens": ["Her", "nephew", "was", "Felix", "Mendelssohn", ",", "the", "composer", ",", "who", "with", "his", "sister", ",", "Fanny", ",", "were", "considered", "nearly", "the", "equals", "of", "the", "Mozarts", "as", "child", "prodigies", "."], "ner_tags": ["O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O"]}
{"sentence": "For some Jews, she may be a less than admirable figure as well, having left her Jewish husband, violated her divorce settlement, and converted first to Protestantism (which was favorable towards Judaism), and finally to Catholicism (which was not).", "tokens": ["For", "some", "Jews", ",", "she", "may", "be", "a", "less", "than", "admirable", "figure", "as", "well", ",", "having", "left", "her", "Jewish", "husband", ",", "violated", "her", "divorce", "settlement", ",", "and", "converted", "first", "to", "Protestantism", "(", "which", "was", "favorable", "towards", "Judaism", ")", ",", "and", "finally", "to", "Catholicism", "(", "which", "was", "not", ")", "."], "ner_tags": ["O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O"]}
{"sentence": "[1] In older literature and on her gravestone one finds the date 1763, but this is the birthyear of her older sister Sara (May 23rd 1763-April 15 1764) whose untimely death was one of the reasons Moses Mendelssohn wrote the Phaedon.", "tokens": ["[", "1", "]", "In", "older", "literature", "and", "on", "her", "gravestone", "one", "finds", "the", "date", "1763", ",", "but", "this", "is", "the", "birthyear", "of", "her", "older", "sister", "Sara", "(", "May", "23rd", "1763-April", "15", "1764", ")", "whose", "untimely", "death", "was", "one", "of", "the", "reasons", "Moses", "Mendelssohn", "wrote", "the", "Phaedon", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "B-MISC", "O"]}
{"sentence": "Stylistically, the tracks range from industrial metal to goth and electronica.", "tokens": ["Stylistically", ",", "the", "tracks", "range", "from", "industrial", "metal", "to", "goth", "and", "electronica", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Six foot, three inches tall and 215 pounds in his prime, Bobick was part of a boxing family and grew up with the sport in the 1960s.", "tokens": ["Six", "foot", ",", "three", "inches", "tall", "and", "215", "pounds", "in", "his", "prime", ",", "Bobick", "was", "part", "of", "a", "boxing", "family", "and", "grew", "up", "with", "the", "sport", "in", "the", "1960s", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Bobick trained with and was managed by heavyweight legend Joe Frazier.", "tokens": ["Bobick", "trained", "with", "and", "was", "managed", "by", "heavyweight", "legend", "Joe", "Frazier", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O"]}
{"sentence": "Knockout wins that year included Ted Gullick and future champion Mike Weaver.", "tokens": ["Knockout", "wins", "that", "year", "included", "Ted", "Gullick", "and", "future", "champion", "Mike", "Weaver", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "B-PER", "I-PER", "O"]}
{"sentence": "He was now being dodged by some, but a win over Randy Neumann proved he could not be ignored.", "tokens": ["He", "was", "now", "being", "dodged", "by", "some", ",", "but", "a", "win", "over", "Randy", "Neumann", "proved", "he", "could", "not", "be", "ignored", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Norton connected first and very hard.", "tokens": ["Norton", "connected", "first", "and", "very", "hard", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O"]}
{"sentence": "He finished the year 1977 40-1 with 36 KO's.", "tokens": ["He", "finished", "the", "year", "1977", "40-1", "with", "36", "KO's", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "All four of his pro losses were by KO.", "tokens": ["All", "four", "of", "his", "pro", "losses", "were", "by", "KO", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "No. 18 later moved to northern Australia, No. 120 to Western Australia and later transferring overseas..", "tokens": ["No.", "18", "later", "moved", "to", "northern", "Australia", ",", "No.", "120", "to", "Western", "Australia", "and", "later", "transferring", "overseas", ".."], "ner_tags": ["B-ORG", "I-ORG", "O", "O", "O", "O", "B-LOC", "O", "B-ORG", "I-ORG", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O"]}
{"sentence": "Henry Eberhard Faber is the namesake for both the college in Animal House and the character Faber in Fahrenheit 451.", "tokens": ["Henry", "Eberhard", "Faber", "is", "the", "namesake", "for", "both", "the", "college", "in", "Animal", "House", "and", "the", "character", "Faber", "in", "Fahrenheit", "451", "."], "ner_tags": ["B-PER", "I-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "B-PER", "O", "B-MISC", "I-MISC", "O"]}
{"sentence": "In 1885 Hitzig became a professor at the University of Halle, where he remained until his retirement in 1903.", "tokens": ["In", "1885", "Hitzig", "became", "a", "professor", "at", "the", "University", "of", "Halle", ",", "where", "he", "remained", "until", "his", "retirement", "in", "1903", "."], "ner_tags": ["O", "O", "B-PER", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "In 1870 Hitzig published his findings in an essay called \"On the Electrical Excitability of the Cerebrum\". This experimentation was considered the first time anyone had done any \"localized study\" regarding the brain and electrical current.", "tokens": ["In", "1870", "Hitzig", "published", "his", "findings", "in", "an", "essay", "called", "\"", "On", "the", "Electrical", "Excitability", "of", "the", "Cerebrum", "\"", ".", "This", "experimentation", "was", "considered", "the", "first", "time", "anyone", "had", "done", "any", "\"", "localized", "study", "\"", "regarding", "the", "brain", "and", "electrical", "current", "."], "ner_tags": ["O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "In the late 1930s in Budapest the restaurant owner L\u00e1szl\u00f3 hires the pianist Andr\u00e1s to play in his restaurant.", "tokens": ["In", "the", "late", "1930s", "in", "Budapest", "the", "restaurant", "owner", "L\u00e1szl\u00f3", "hires", "the", "pianist", "Andr\u00e1s", "to", "play", "in", "his", "restaurant", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "B-PER", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O"]}
{"sentence": "After a few years Nazi Germany captures Budapest and Hans returns.", "tokens": ["After", "a", "few", "years", "Nazi", "Germany", "captures", "Budapest", "and", "Hans", "returns", "."], "ner_tags": ["O", "O", "O", "O", "B-LOC", "I-LOC", "O", "B-LOC", "O", "B-PER", "O", "O"]}
{"sentence": "The street was built in the 1880s and now hosts Brixton Market, selling a mix of African, Caribbean, English, Portuguese and Chinese products.", "tokens": ["The", "street", "was", "built", "in", "the", "1880s", "and", "now", "hosts", "Brixton", "Market", ",", "selling", "a", "mix", "of", "African", ",", "Caribbean", ",", "English", ",", "Portuguese", "and", "Chinese", "products", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "B-MISC", "O", "B-MISC", "O", "B-MISC", "O", "B-MISC", "O", "B-MISC", "O", "O"]}
{"sentence": "Production at Elm Coulee has more than doubled the oil output of the state of Montana.", "tokens": ["Production", "at", "Elm", "Coulee", "has", "more", "than", "doubled", "the", "oil", "output", "of", "the", "state", "of", "Montana", "."], "ner_tags": ["O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "In 1942, along with Dr. Haidar Abdel-Shafi, and the late Mukhlis Amer, Emil Habibi and Mufid Nashashibi, Touma was a founder of the Palestinian National Liberation League.", "tokens": ["In", "1942", ",", "along", "with", "Dr.", "Haidar", "Abdel-Shafi", ",", "and", "the", "late", "Mukhlis", "Amer", ",", "Emil", "Habibi", "and", "Mufid", "Nashashibi", ",", "Touma", "was", "a", "founder", "of", "the", "Palestinian", "National", "Liberation", "League", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O"]}
{"sentence": "Eugenio Calabi (born 1923) is an Italian-American mathematician and professor emeritus at the University of Pennsylvania, specializing in differential geometry, partial differential equations and their applications.", "tokens": ["Eugenio", "Calabi", "(", "born", "1923", ")", "is", "an", "Italian-American", "mathematician", "and", "professor", "emeritus", "at", "the", "University", "of", "Pennsylvania", ",", "specializing", "in", "differential", "geometry", ",", "partial", "differential", "equations", "and", "their", "applications", "."], "ner_tags": ["B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Scott Chair of Mathematics at the University of Pennsylvania in 1967.", "tokens": ["Scott", "Chair", "of", "Mathematics", "at", "the", "University", "of", "Pennsylvania", "in", "1967", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O"]}
{"sentence": "The trains designated as Express Nakhonphink are Train No. 1 for Bangkok - Chiang Mai and Train No. 2 for Chiang Mai - Bangkok.", "tokens": ["The", "trains", "designated", "as", "Express", "Nakhonphink", "are", "Train", "No.", "1", "for", "Bangkok", "-", "Chiang", "Mai", "and", "Train", "No.", "2", "for", "Chiang", "Mai", "-", "Bangkok", "."], "ner_tags": ["O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "B-LOC", "O"]}
{"sentence": "EZ2DJ is a series of music video games created by the South Korean company Amuseworld.", "tokens": ["EZ2DJ", "is", "a", "series", "of", "music", "video", "games", "created", "by", "the", "South", "Korean", "company", "Amuseworld", "."], "ner_tags": ["B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "B-ORG", "O"]}
{"sentence": "Ez2DJ 2nd TraX - It Rules Once Again - (October, 2000)", "tokens": ["Ez2DJ", "2nd", "TraX", "-", "It", "Rules", "Once", "Again", "-", "(", "October", ",", "2000", ")"], "ner_tags": ["B-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Ez2DJ 7th TraX - Resistance - Version 1.50 (December, 2007)", "tokens": ["Ez2DJ", "7th", "TraX", "-", "Resistance", "-", "Version", "1.50", "(", "December", ",", "2007", ")"], "ner_tags": ["B-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O"]}
{"sentence": "However, unlike the Beatmania series (including III and IIDX), depending on the gamemode, effector button are treated as a gameplay button, especially in the gamemode \"Radio Mix\", where each player need to control 2 effector buttons.", "tokens": ["However", ",", "unlike", "the", "Beatmania", "series", "(", "including", "III", "and", "IIDX", ")", ",", "depending", "on", "the", "gamemode", ",", "effector", "button", "are", "treated", "as", "a", "gameplay", "button", ",", "especially", "in", "the", "gamemode", "\"", "Radio", "Mix", "\"", ",", "where", "each", "player", "need", "to", "control", "2", "effector", "buttons", "."], "ner_tags": ["O", "O", "O", "O", "B-MISC", "O", "O", "O", "B-MISC", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Written by Erin (10/14/07), Edited by AHRISA (07/01/08)", "tokens": ["Written", "by", "Erin", "(", "10/14/07", ")", ",", "Edited", "by", "AHRISA", "(", "07/01/08", ")"], "ner_tags": ["O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O"]}
{"sentence": "3.", "tokens": ["3", "."], "ner_tags": ["O", "O"]}
{"sentence": "5.", "tokens": ["5", "."], "ner_tags": ["O", "O"]}
{"sentence": "(1) 1st Tracks / 1st SE", "tokens": ["(", "1", ")", "1st", "Tracks", "/", "1st", "SE"], "ner_tags": ["O", "O", "O", "B-MISC", "I-MISC", "O", "B-MISC", "I-MISC"]}
{"sentence": "A Korean Judge's ruling has ordered Amuse World to pay Konami damages and stop production of the product completely.", "tokens": ["A", "Korean", "Judge", "'s", "ruling", "has", "ordered", "Amuse", "World", "to", "pay", "Konami", "damages", "and", "stop", "production", "of", "the", "product", "completely", "."], "ner_tags": ["O", "B-MISC", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "This area continues to be the core of the system, and is noted for the number of residents in the Richmond Highway area who use the service at all times of the day.", "tokens": ["This", "area", "continues", "to", "be", "the", "core", "of", "the", "system", ",", "and", "is", "noted", "for", "the", "number", "of", "residents", "in", "the", "Richmond", "Highway", "area", "who", "use", "the", "service", "at", "all", "times", "of", "the", "day", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Base fare is usually $1, but can be as much as $3 for express routes.", "tokens": ["Base", "fare", "is", "usually", "$1", ",", "but", "can", "be", "as", "much", "as", "$3", "for", "express", "routes", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Fairmont State University is a public university located in Fairmont, West Virginia (population 19,097).", "tokens": ["Fairmont", "State", "University", "is", "a", "public", "university", "located", "in", "Fairmont", ",", "West", "Virginia", "(", "population", "19,097", ")", "."], "ner_tags": ["B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O"]}
{"sentence": "On April 7, 2004, Governor Bob Wise signed legislation allowing Fairmont State College to change its name to Fairmont State University.", "tokens": ["On", "April", "7", ",", "2004", ",", "Governor", "Bob", "Wise", "signed", "legislation", "allowing", "Fairmont", "State", "College", "to", "change", "its", "name", "to", "Fairmont", "State", "University", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O"]}
{"sentence": "Fairmont State's athletic teams, known as the Falcons, compete in the West Virginia Intercollegiate Athletic Conference in NCAA Division II.", "tokens": ["Fairmont", "State", "'s", "athletic", "teams", ",", "known", "as", "the", "Falcons", ",", "compete", "in", "the", "West", "Virginia", "Intercollegiate", "Athletic", "Conference", "in", "NCAA", "Division", "II", "."], "ner_tags": ["B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "B-ORG", "I-ORG", "I-ORG", "O"]}
