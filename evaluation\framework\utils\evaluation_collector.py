﻿# -*- coding: utf-8 -*-
"""
评估数据收集器
在现有流水线基础上增加评估所需的详细数据收集
"""

import json
import os
import time
import jieba
import numpy as np
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
from collections import Counter, defaultdict
import re

# 添加自定义JSON编码器
class NumpyEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return super().default(obj)

def convert_numpy_types(data):
    """递归转换数据结构中的NumPy类型为Python原生类型"""
    if isinstance(data, dict):
        return {k: convert_numpy_types(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [convert_numpy_types(item) for item in data]
    elif isinstance(data, np.integer):
        return int(data)
    elif isinstance(data, np.floating):
        return float(data)
    elif isinstance(data, np.ndarray):
        return data.tolist()
    return data

def collect_strategy_generation_metadata(output_dir: str, entity_types: List[str], 
                                       generation_config: Dict) -> Dict[str, Any]:
    """收集策略生成的元数据"""
    metadata = {
        "start_time": datetime.now().isoformat(),
        "output_directory": output_dir,
        "target_entity_types": entity_types,
        "total_entity_types": len(entity_types),
        "generation_config": generation_config,
        "strategy_files": {},
        "generation_statistics": {
            "sentence_diversity_attributes": 0,
            "entity_diversity_pools": 0,
            "target_distribution_size": 0
        }
    }
    
    return metadata

def collect_strategy_statistics(strategy_files: Dict[str, str]) -> Dict[str, Any]:
    """收集策略文件的统计信息"""
    statistics = {
        "strategy_file_sizes": {},
        "sentence_diversity_stats": {},
        "entity_diversity_stats": {},
        "target_distribution_stats": {}
    }
    
    for strategy_type, file_path in strategy_files.items():
        if Path(file_path).exists():
            file_size = Path(file_path).stat().st_size
            statistics["strategy_file_sizes"][strategy_type] = file_size
                
            # 分析具体策略内容
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                if "sentence" in strategy_type.lower():
                    statistics["sentence_diversity_stats"] = analyze_sentence_diversity_data(data)
                elif "entity" in strategy_type.lower():
                    statistics["entity_diversity_stats"] = analyze_entity_diversity_data(data)
                elif "target" in strategy_type.lower():
                    statistics["target_distribution_stats"] = analyze_target_distribution_data(data)
                    
            except Exception as e:
                print(f"警告: 分析策略文件 {file_path} 失败: {e}")
    
    return statistics

def analyze_sentence_diversity_data(data: Dict) -> Dict[str, Any]:
    """分析句子多样化数据"""
    stats = {
        "total_attributes": len(data),
        "attributes": list(data.keys()),
        "total_values": sum(len(values) if isinstance(values, list) else 1 for values in data.values()),
        "attribute_value_counts": {}
    }
    
    for attr, values in data.items():
        if isinstance(values, list):
            stats["attribute_value_counts"][attr] = len(values)
        else:
            stats["attribute_value_counts"][attr] = 1
    
    return stats

def analyze_entity_diversity_data(data: Dict) -> Dict[str, Any]:
    """分析实体多样化数据"""
    stats = {
        "total_entity_types": 0,
        "entity_type_counts": {},
        "vanilla_entity_counts": {},
        "latent_scenario_counts": {},
        "total_entities": 0
    }
    
    if not isinstance(data, dict):
        return stats
    
    for entity_type, entity_data in data.items():
        stats["total_entity_types"] += 1
        
        if isinstance(entity_data, dict):
            # 新格式：包含vanilla和latent
            vanilla_count = len(entity_data.get("vanilla", []))
            latent_data = entity_data.get("latent", {})
            latent_count = sum(len(scenarios) for scenarios in latent_data.values())
            
            stats["vanilla_entity_counts"][entity_type] = vanilla_count
            stats["latent_scenario_counts"][entity_type] = len(latent_data)
            stats["entity_type_counts"][entity_type] = vanilla_count + latent_count
            stats["total_entities"] += vanilla_count + latent_count
        else:
            # 旧格式：简单列表
            entity_count = len(entity_data) if isinstance(entity_data, list) else 1
            stats["entity_type_counts"][entity_type] = entity_count
            stats["total_entities"] += entity_count
    
    return stats

def analyze_target_distribution_data(data: Dict) -> Dict[str, Any]:
    """分析目标分布数据"""
    stats = {
        "total_entity_types": len(data),
        "entity_types": list(data.keys()),
        "target_counts": dict(data),
        "total_target_count": sum(data.values()),
        "average_target_count": sum(data.values()) / len(data) if data else 0,
        "target_count_distribution": {}
    }
    
    # 统计目标数量分布
    count_freq = Counter(data.values())
    stats["target_count_distribution"] = dict(count_freq)
    
    return stats

def collect_data_generation_metadata(dataset_size: int, generation_config: Dict) -> Dict[str, Any]:
    """收集数据生成的元数据"""
    metadata = {
        "start_time": datetime.now().isoformat(),
        "target_dataset_size": dataset_size,
        "generation_config": generation_config,
        "api_calls": 0,  # 将在生成过程中更新
        "generation_time": 0  # 将在生成完成后更新
    }
    
    return metadata

def collect_detailed_evaluation_metrics(dataset: List[Dict], 
                                       original_dataset: Optional[List[Dict]] = None) -> Dict[str, Any]:
    """收集详细的评估指标"""
    metrics = {
        "basic_statistics": collect_basic_statistics(dataset),
        "entity_analysis": collect_entity_analysis(dataset),
        "linguistic_analysis": collect_linguistic_analysis(dataset),
        "diversity_analysis": collect_diversity_analysis(dataset),
        "quality_indicators": collect_quality_indicators(dataset)
    }
    
    if original_dataset:
        metrics["comparison_analysis"] = collect_comparison_analysis(dataset, original_dataset)
    
    return metrics

def collect_basic_statistics(dataset: List[Dict]) -> Dict[str, Any]:
    """收集基本统计信息"""
    stats = {
        "total_samples": len(dataset),
        "text_length_stats": {},
        "entity_count_stats": {},
        "label_distribution": {}
    }
    
    text_lengths = []
    entity_counts = []
    label_counter = Counter()
    
    for item in dataset:
        text = item.get("text", "")
        labels = item.get("label", [])
        
        text_lengths.append(len(text))
        entity_counts.append(len(labels))
        
        for label in labels:
            entity_type = label.get("type", "")
            label_counter[entity_type] += 1
    
    if text_lengths:
        stats["text_length_stats"] = {
            "mean": np.mean(text_lengths),
            "median": np.median(text_lengths),
            "std": np.std(text_lengths),
            "min": np.min(text_lengths),
            "max": np.max(text_lengths),
            "percentiles": {
                "25": np.percentile(text_lengths, 25),
                "75": np.percentile(text_lengths, 75),
                "90": np.percentile(text_lengths, 90),
                "95": np.percentile(text_lengths, 95)
            }
        }
    
    if entity_counts:
        stats["entity_count_stats"] = {
            "mean": np.mean(entity_counts),
            "median": np.median(entity_counts),
            "std": np.std(entity_counts),
            "min": np.min(entity_counts),
            "max": np.max(entity_counts)
        }
    
    stats["label_distribution"] = dict(label_counter)
    
    return stats

def collect_entity_analysis(dataset: List[Dict]) -> Dict[str, Any]:
    """收集实体分析数据"""
    analysis = {
        "entity_type_distribution": {},
        "entity_length_analysis": {},
        "entity_position_analysis": {},
        "entity_density_analysis": {},
        "entity_overlap_analysis": {}
    }
    
    entity_data = defaultdict(list)
    entity_positions = defaultdict(list)
    entity_densities = []
    overlaps = []
    
    for item in dataset:
        text = item.get("text", "")
        labels = item.get("label", [])
        
        # 计算实体密度
        if len(text) > 0:
            entity_density = len(labels) / len(text)
            entity_densities.append(entity_density)
        
        # 分析实体重叠
        sorted_labels = sorted(labels, key=lambda x: x.get("start", 0))
        for i in range(len(sorted_labels) - 1):
            current_end = sorted_labels[i].get("end", 0)
            next_start = sorted_labels[i + 1].get("start", 0)
            if current_end > next_start:
                overlaps.append({
                    "current": sorted_labels[i],
                    "next": sorted_labels[i + 1],
                    "overlap_length": current_end - next_start
                })
        
        for label in labels:
            entity_type = label.get("type", "")
            entity_text = label.get("text", "")
            start = label.get("start", 0)
            end = label.get("end", 0)
            
            entity_data[entity_type].append({
                "text": entity_text,
                "length": len(entity_text),
                "start": start,
                "end": end
            })
                
            # 计算相对位置
            if len(text) > 0:
                relative_position = start / len(text)
                entity_positions[entity_type].append(relative_position)
    
    # 分析各实体类型
    for entity_type, entities in entity_data.items():
        lengths = [e["length"] for e in entities]
        
        analysis["entity_type_distribution"][entity_type] = len(entities)
        
        if lengths:
            analysis["entity_length_analysis"][entity_type] = {
                "count": len(lengths),
                "mean_length": np.mean(lengths),
                "median_length": np.median(lengths),
                "std_length": np.std(lengths),
                "min_length": np.min(lengths),
                "max_length": np.max(lengths)
        }
    
        positions = entity_positions[entity_type]
        if positions:
            analysis["entity_position_analysis"][entity_type] = {
                "mean_position": np.mean(positions),
                "std_position": np.std(positions),
                "position_distribution": {
                    "beginning": sum(1 for p in positions if p < 0.33) / len(positions),
                    "middle": sum(1 for p in positions if 0.33 <= p < 0.67) / len(positions),
                    "end": sum(1 for p in positions if p >= 0.67) / len(positions)
                }
            }
    
    # 实体密度分析
    if entity_densities:
        analysis["entity_density_analysis"] = {
            "mean_density": np.mean(entity_densities),
            "median_density": np.median(entity_densities),
            "std_density": np.std(entity_densities),
            "max_density": np.max(entity_densities)
        }
    
    # 实体重叠分析
    analysis["entity_overlap_analysis"] = {
        "total_overlaps": len(overlaps),
        "overlap_rate": len(overlaps) / len(dataset) if dataset else 0,
        "overlap_details": overlaps[:10]  # 只保存前10个重叠案例
        }
    
    return analysis

def collect_linguistic_analysis(dataset: List[Dict]) -> Dict[str, Any]:
    """收集语言学分析数据"""
    analysis = {
        "vocabulary_analysis": {},
        "sentence_structure_analysis": {},
        "punctuation_analysis": {},
        "word_frequency_analysis": {}
    }
    
    all_words = []
    all_chars = []
    sentence_lengths = []
    punctuation_counts = defaultdict(int)
    
    for item in dataset:
        text = item.get("text", "")
        
        # 分词
        words = list(jieba.cut(text))
        all_words.extend(words)
        
        # 字符分析
        chars = list(text)
        all_chars.extend(chars)
        
        # 句子长度（按词计算）
        sentence_lengths.append(len(words))
        
        # 标点符号分析
        punctuation_pattern = r'[，。！？；：""''（）【】《》、]'
        punctuations = re.findall(punctuation_pattern, text)
        for punct in punctuations:
            punctuation_counts[punct] += 1
    
    # 词汇分析
    word_counter = Counter(all_words)
    char_counter = Counter(all_chars)
    
    analysis["vocabulary_analysis"] = {
            "total_words": len(all_words),
        "unique_words": len(word_counter),
        "vocabulary_diversity": len(word_counter) / len(all_words) if all_words else 0,
        "total_chars": len(all_chars),
        "unique_chars": len(char_counter),
        "most_common_words": word_counter.most_common(20),
        "word_length_distribution": analyze_word_lengths(all_words)
    }
    
    # 句子结构分析
    if sentence_lengths:
        analysis["sentence_structure_analysis"] = {
            "mean_sentence_length": np.mean(sentence_lengths),
            "median_sentence_length": np.median(sentence_lengths),
            "std_sentence_length": np.std(sentence_lengths),
            "sentence_length_distribution": {
                "short": sum(1 for l in sentence_lengths if l < 10) / len(sentence_lengths),
                "medium": sum(1 for l in sentence_lengths if 10 <= l < 20) / len(sentence_lengths),
                "long": sum(1 for l in sentence_lengths if l >= 20) / len(sentence_lengths)
            }
        }
    
    # 标点符号分析
    analysis["punctuation_analysis"] = {
        "total_punctuation": sum(punctuation_counts.values()),
        "punctuation_diversity": len(punctuation_counts),
        "punctuation_distribution": dict(punctuation_counts)
    }
    
    return analysis

def analyze_word_lengths(words: List[str]) -> Dict[str, Any]:
    """分析词长分布"""
    word_lengths = [len(word) for word in words if word.strip()]
    
    if not word_lengths:
        return {}
    
    return {
        "mean_word_length": np.mean(word_lengths),
        "median_word_length": np.median(word_lengths),
        "std_word_length": np.std(word_lengths),
        "word_length_distribution": {
            "single_char": sum(1 for l in word_lengths if l == 1) / len(word_lengths),
            "two_chars": sum(1 for l in word_lengths if l == 2) / len(word_lengths),
            "three_chars": sum(1 for l in word_lengths if l == 3) / len(word_lengths),
            "four_plus_chars": sum(1 for l in word_lengths if l >= 4) / len(word_lengths)
        }
    }

def collect_diversity_analysis(dataset: List[Dict]) -> Dict[str, Any]:
    """收集多样性分析数据"""
    analysis = {
        "lexical_diversity": {},
        "syntactic_diversity": {},
        "semantic_diversity": {},
        "entity_context_diversity": {}
    }
    
    # 词汇多样性
    all_texts = [item.get("text", "") for item in dataset]
    all_words = []
    for text in all_texts:
        words = list(jieba.cut(text))
        all_words.extend(words)
    
    word_counter = Counter(all_words)
    analysis["lexical_diversity"] = {
        "type_token_ratio": len(word_counter) / len(all_words) if all_words else 0,
        "hapax_legomena_ratio": sum(1 for count in word_counter.values() if count == 1) / len(word_counter) if word_counter else 0,
        "vocabulary_richness": calculate_vocabulary_richness(word_counter)
    }
    
    # 句法多样性
    sentence_patterns = []
    for text in all_texts:
        pattern = analyze_sentence_pattern(text)
        sentence_patterns.append(pattern)
    
    pattern_counter = Counter(sentence_patterns)
    analysis["syntactic_diversity"] = {
        "unique_patterns": len(pattern_counter),
        "pattern_diversity": len(pattern_counter) / len(sentence_patterns) if sentence_patterns else 0,
        "most_common_patterns": pattern_counter.most_common(10)
    }
    
    # 实体上下文多样性
    entity_contexts = defaultdict(list)
    for item in dataset:
        text = item.get("text", "")
        labels = item.get("label", [])
        
        for label in labels:
            entity_type = label.get("type", "")
            start = label.get("start", 0)
            end = label.get("end", 0)
            
            # 提取上下文
            context_start = max(0, start - 5)
            context_end = min(len(text), end + 5)
            context = text[context_start:start] + "[ENTITY]" + text[end:context_end]
            
            entity_contexts[entity_type].append(context)
    
    for entity_type, contexts in entity_contexts.items():
        unique_contexts = len(set(contexts))
        analysis["entity_context_diversity"][entity_type] = {
            "total_contexts": len(contexts),
            "unique_contexts": unique_contexts,
            "context_diversity": unique_contexts / len(contexts) if contexts else 0
    }
    
    return analysis

def calculate_vocabulary_richness(word_counter: Counter) -> float:
    """计算词汇丰富度 (Yule's K)"""
    if not word_counter:
        return 0.0
    
    N = sum(word_counter.values())
    freq_counter = Counter(word_counter.values())
    
    sum_freq_squared = sum(freq * count**2 for freq, count in freq_counter.items())
    
    if N <= 1:
        return 0.0
    
    return 10000 * (sum_freq_squared - N) / (N * (N - 1))

def analyze_sentence_pattern(text: str) -> str:
    """分析句子模式"""
    # 简化的句子模式分析
    pattern = ""
    
    # 检查句子长度
    if len(text) < 10:
        pattern += "SHORT_"
    elif len(text) < 30:
        pattern += "MEDIUM_"
    else:
        pattern += "LONG_"
    
    # 检查标点符号
    if "，" in text:
        pattern += "COMMA_"
    if any(punct in text for punct in "。！？"):
        pattern += "END_PUNCT_"
    if any(punct in text for punct in "：；"):
        pattern += "MID_PUNCT_"
    
    return pattern.rstrip("_")

def collect_quality_indicators(dataset: List[Dict]) -> Dict[str, Any]:
    """收集质量指标"""
    indicators = {
        "annotation_quality": {},
        "text_quality": {},
        "consistency_indicators": {}
    }
    
    # 标注质量指标
    valid_annotations = 0
    total_annotations = 0
    boundary_errors = 0
    type_consistency_errors = 0
    
    for item in dataset:
        text = item.get("text", "")
        labels = item.get("label", [])
        
        for label in labels:
            total_annotations += 1
            
            start = label.get("start", 0)
            end = label.get("end", 0)
            entity_text = label.get("text", "")
            entity_type = label.get("type", "")
            
            # 检查边界正确性
            if start >= 0 and end <= len(text) and start < end:
                if text[start:end] == entity_text:
                    valid_annotations += 1
                else:
                    boundary_errors += 1
            else:
                boundary_errors += 1
            
            # 检查类型一致性（简化版）
            if not is_entity_type_consistent(entity_text, entity_type):
                type_consistency_errors += 1
    
    indicators["annotation_quality"] = {
        "total_annotations": total_annotations,
        "valid_annotations": valid_annotations,
        "boundary_accuracy": valid_annotations / total_annotations if total_annotations > 0 else 0,
        "boundary_errors": boundary_errors,
        "type_consistency_errors": type_consistency_errors,
        "type_consistency_rate": (total_annotations - type_consistency_errors) / total_annotations if total_annotations > 0 else 0
    }
    
    # 文本质量指标
    text_quality_scores = []
    for item in dataset:
        text = item.get("text", "")
        score = assess_text_quality(text)
        text_quality_scores.append(score)
    
    if text_quality_scores:
        indicators["text_quality"] = {
            "mean_quality_score": np.mean(text_quality_scores),
            "median_quality_score": np.median(text_quality_scores),
            "std_quality_score": np.std(text_quality_scores),
            "quality_distribution": {
                "high": sum(1 for s in text_quality_scores if s >= 0.8) / len(text_quality_scores),
                "medium": sum(1 for s in text_quality_scores if 0.6 <= s < 0.8) / len(text_quality_scores),
                "low": sum(1 for s in text_quality_scores if s < 0.6) / len(text_quality_scores)
            }
        }
    
    return indicators

def is_entity_type_consistent(entity_text: str, entity_type: str) -> bool:
    """检查实体类型一致性（简化版）"""
    # 基本的一致性检查规则
    type_patterns = {
        "人名": {"min_length": 2, "max_length": 6, "pattern": r'[\u4e00-\u9fff]+'},
        "地名": {"min_length": 2, "max_length": 10, "pattern": r'[\u4e00-\u9fff]+'},
        "时间": {"min_length": 2, "max_length": 20, "pattern": r'[\d年月日时分秒]+'},
        "组织名": {"min_length": 2, "max_length": 15, "pattern": r'[\u4e00-\u9fff]+'}
    }
    
    if entity_type not in type_patterns:
        return True  # 未知类型默认一致
    
    pattern_info = type_patterns[entity_type]
    
    # 检查长度
    if len(entity_text) < pattern_info["min_length"] or len(entity_text) > pattern_info["max_length"]:
        return False
    
    # 检查模式
    if not re.search(pattern_info["pattern"], entity_text):
        return False
    
    return True

def assess_text_quality(text: str) -> float:
    """评估文本质量（简化版）"""
    score = 1.0
    
    # 基本检查
    if len(text.strip()) == 0:
        return 0.0
    
    # 长度合理性
    if len(text) < 5:
        score -= 0.3
    elif len(text) > 200:
        score -= 0.1
    
    # 标点符号使用
    if not text.endswith(('。', '！', '？', '；', '，')):
        score -= 0.1
    
    # 重复字符检查
    if re.search(r'(.)\1{3,}', text):
        score -= 0.2
    
    # 特殊字符检查
    if re.search(r'[^\u4e00-\u9fff\w\s，。！？；：""''（）【】\-]', text):
        score -= 0.1
    
    return max(0.0, score)

def collect_comparison_analysis(generated_dataset: List[Dict], 
                              original_dataset: List[Dict]) -> Dict[str, Any]:
    """收集对比分析数据"""
    analysis = {
        "size_comparison": {},
        "distribution_comparison": {},
        "quality_comparison": {},
        "diversity_comparison": {}
    }
    
    # 大小对比
    analysis["size_comparison"] = {
        "original_size": len(original_dataset),
        "generated_size": len(generated_dataset),
        "size_ratio": len(generated_dataset) / len(original_dataset) if original_dataset else 0
    }
    
    # 分布对比
    original_dist = Counter()
    generated_dist = Counter()
    
    for item in original_dataset:
        for label in item.get("label", []):
            original_dist[label.get("type", "")] += 1
    
    for item in generated_dataset:
        for label in item.get("label", []):
            generated_dist[label.get("type", "")] += 1
    
    analysis["distribution_comparison"] = {
        "original_distribution": dict(original_dist),
        "generated_distribution": dict(generated_dist),
        "distribution_similarity": calculate_distribution_similarity(original_dist, generated_dist)
    }
    
    return analysis

def calculate_distribution_similarity(dist1: Counter, dist2: Counter) -> float:
    """计算分布相似度（余弦相似度）"""
    all_types = set(dist1.keys()) | set(dist2.keys())
    
    if not all_types:
        return 1.0
    
    vec1 = [dist1.get(t, 0) for t in all_types]
    vec2 = [dist2.get(t, 0) for t in all_types]
    
    dot_product = sum(a * b for a, b in zip(vec1, vec2))
    norm1 = sum(a ** 2 for a in vec1) ** 0.5
    norm2 = sum(b ** 2 for b in vec2) ** 0.5
    
    if norm1 == 0 or norm2 == 0:
        return 0.0
    
    return dot_product / (norm1 * norm2)

def save_enhanced_iteration_data(dataset: List[Dict], iteration: int, 
                               gap: Dict[str, int], diversity_metrics: Dict[str, float],
                               dataset_manager, strategy_metadata: Optional[Dict] = None) -> Dict[str, Any]:
    """保存增强的迭代数据"""
    # 创建迭代目录
    iter_dir = dataset_manager.create_iteration_dir(iteration)
    
    # 收集详细评估指标
    detailed_metrics = collect_detailed_evaluation_metrics(dataset)
    
    # 创建增强的迭代信息
    enhanced_iteration_info = {
        "iteration": iteration,
        "timestamp": datetime.now().isoformat(),
        "dataset_size": len(dataset),
        "entity_gap": gap,
        "diversity_metrics": diversity_metrics,
        "total_gap": sum(gap.values()),
        "detailed_metrics": detailed_metrics,
        "strategy_metadata": strategy_metadata or {},
        "convergence_indicators": {
            "gap_reduction_rate": calculate_gap_reduction_rate(gap, iteration),
            "diversity_improvement": calculate_diversity_improvement(diversity_metrics, iteration),
            "stability_score": calculate_stability_score(detailed_metrics)
        }
    }
    
    # 转换所有NumPy类型为Python原生类型
    enhanced_iteration_info = convert_numpy_types(enhanced_iteration_info)
    dataset = convert_numpy_types(dataset)
    detailed_metrics = convert_numpy_types(detailed_metrics)
    
    # 保存数据集
    dataset_file = iter_dir / f"iteration_{iteration:03d}.json"
    with open(dataset_file, 'w', encoding='utf-8') as f:
        json.dump(dataset, f, ensure_ascii=False, indent=2, cls=NumpyEncoder)
    
    # 保存增强的迭代信息
    info_file = iter_dir / f"iteration_{iteration:03d}_enhanced_info.json"
    with open(info_file, 'w', encoding='utf-8') as f:
        json.dump(enhanced_iteration_info, f, ensure_ascii=False, indent=2, cls=NumpyEncoder)
    
    # 保存详细指标（单独文件）
    metrics_file = iter_dir / f"iteration_{iteration:03d}_detailed_metrics.json"
    with open(metrics_file, 'w', encoding='utf-8') as f:
        json.dump(detailed_metrics, f, ensure_ascii=False, indent=2, cls=NumpyEncoder)
    
    print(f"[✓] 增强迭代数据已保存到：{iter_dir}")
    return enhanced_iteration_info

def calculate_gap_reduction_rate(current_gap: Dict[str, int], iteration: int) -> float:
    """计算差距缩减率"""
    if iteration <= 1:
        return 0.0
    
    total_gap = sum(current_gap.values())
    # 这里应该与前一次迭代的差距比较，简化处理
    return max(0.0, 1.0 - total_gap / (iteration * 10))  # 简化计算

def calculate_diversity_improvement(diversity_metrics: Dict[str, float], iteration: int) -> float:
    """计算多样性改进"""
    if not diversity_metrics:
        return 0.0
    
    avg_diversity = sum(diversity_metrics.values()) / len(diversity_metrics)
    return min(1.0, avg_diversity * iteration / 10)  # 简化计算

def calculate_stability_score(detailed_metrics: Dict[str, Any]) -> float:
    """计算稳定性得分"""
    # 基于多个指标的稳定性评估
    stability_factors = []
    
    # 文本质量稳定性
    text_quality = detailed_metrics.get("quality_indicators", {}).get("text_quality", {})
    if text_quality:
        std_score = text_quality.get("std_quality_score", 0)
        stability_factors.append(max(0, 1 - std_score))
    
    # 实体分布稳定性
    entity_analysis = detailed_metrics.get("entity_analysis", {})
    if entity_analysis:
        entity_counts = entity_analysis.get("entity_type_distribution", {})
        if entity_counts:
            values = list(entity_counts.values())
            if len(values) > 1:
                cv = np.std(values) / np.mean(values)  # 变异系数
                stability_factors.append(max(0, 1 - cv))
    
    return np.mean(stability_factors) if stability_factors else 0.5

def collect_ablation_experiment_data(components: List[str], 
                                   baseline_performance: Dict[str, float],
                                   component_performances: Dict[str, Dict[str, float]]) -> Dict[str, Any]:
    """收集消融实验数据"""
    ablation_data = {
        "experiment_timestamp": datetime.now().isoformat(),
        "components": components,
        "baseline_performance": baseline_performance,
        "component_performances": component_performances,
        "component_contributions": {},
        "interaction_effects": {},
        "ablation_summary": {}
    }
    
    # 计算组件贡献度
    for component in components:
        if component in component_performances:
            component_perf = component_performances[component]
            contribution = {}
            
            for metric, baseline_value in baseline_performance.items():
                component_value = component_perf.get(metric, baseline_value)
                contribution[metric] = baseline_value - component_value
            
            ablation_data["component_contributions"][component] = contribution
    
    # 计算交互效应（简化版）
    ablation_data["interaction_effects"] = analyze_interaction_effects(
        components, component_performances, baseline_performance
    )
    
    # 生成消融摘要
    ablation_data["ablation_summary"] = generate_ablation_summary(
        ablation_data["component_contributions"]
    )
    
    # 确保返回的数据都是Python原生类型
    return convert_numpy_types(ablation_data)

def analyze_interaction_effects(components: List[str], 
                              component_performances: Dict[str, Dict[str, float]],
                              baseline_performance: Dict[str, float]) -> Dict[str, Any]:
    """分析交互效应"""
    # 简化的交互效应分析
    interactions = {}
    
    for i, comp1 in enumerate(components):
        for j, comp2 in enumerate(components[i+1:], i+1):
            interaction_key = f"{comp1}_{comp2}"
            
            # 计算预期的联合效应
            comp1_effect = component_performances.get(comp1, {})
            comp2_effect = component_performances.get(comp2, {})
            
            interactions[interaction_key] = {
                "components": [comp1, comp2],
                "expected_joint_effect": "calculated",  # 实际计算会更复杂
                "observed_joint_effect": "measured",    # 需要实际测量数据
                "interaction_strength": "moderate"      # 简化评估
            }
    
    return interactions

def generate_ablation_summary(component_contributions: Dict[str, Dict[str, float]]) -> Dict[str, Any]:
    """生成消融实验摘要"""
    summary = {
        "most_important_components": [],
        "least_important_components": [],
        "performance_impact_ranking": {},
        "critical_components": []
    }
    
    # 计算每个组件的平均贡献度
    component_avg_contributions = {}
    
    for component, contributions in component_contributions.items():
        avg_contribution = sum(contributions.values()) / len(contributions) if contributions else 0
        component_avg_contributions[component] = avg_contribution
    
    # 排序组件
    sorted_components = sorted(component_avg_contributions.items(), 
                             key=lambda x: x[1], reverse=True)
    
    summary["performance_impact_ranking"] = dict(sorted_components)
    
    if sorted_components:
        summary["most_important_components"] = [sorted_components[0][0]]
        summary["least_important_components"] = [sorted_components[-1][0]]
        
        # 识别关键组件（贡献度超过阈值）
        threshold = 0.1  # 10%的性能影响
        summary["critical_components"] = [
            comp for comp, contrib in sorted_components if contrib > threshold
        ]
    
    return summary
