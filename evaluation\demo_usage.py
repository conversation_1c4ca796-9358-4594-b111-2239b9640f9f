﻿#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
评估框架使用示例
演示如何使用评估框架进行数据质量评估
"""

import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def create_sample_data():
    """创建示例数据"""
    # 原始数据集
    original_dataset = [
        {
            "text": "张三是一名软件工程师",
            "label": [
                {"text": "张三", "type": "人名", "start": 0, "end": 2},
                {"text": "软件工程师", "type": "职业", "start": 5, "end": 10}
            ]
        },
        {
            "text": "李四在北京工作",
            "label": [
                {"text": "李四", "type": "人名", "start": 0, "end": 2},
                {"text": "北京", "type": "地名", "start": 3, "end": 5}
            ]
        },
        {
            "text": "王五是医生",
            "label": [
                {"text": "王五", "type": "人名", "start": 0, "end": 2},
                {"text": "医生", "type": "职业", "start": 3, "end": 5}
            ]
        }
    ]
    
    # 生成数据集（模拟）
    generated_dataset = [
        {
            "text": "赵六是一名教师",
            "label": [
                {"text": "赵六", "type": "人名", "start": 0, "end": 2},
                {"text": "教师", "type": "职业", "start": 5, "end": 7}
            ]
        },
        {
            "text": "钱七在上海居住",
            "label": [
                {"text": "钱七", "type": "人名", "start": 0, "end": 2},
                {"text": "上海", "type": "地名", "start": 3, "end": 5}
            ]
        },
        {
            "text": "孙八是护士",
            "label": [
                {"text": "孙八", "type": "人名", "start": 0, "end": 2},
                {"text": "护士", "type": "职业", "start": 3, "end": 5}
            ]
        },
        {
            "text": "周九在广州工作",
            "label": [
                {"text": "周九", "type": "人名", "start": 0, "end": 2},
                {"text": "广州", "type": "地名", "start": 3, "end": 5}
            ]
        }
    ]
    
    return original_dataset, generated_dataset

def demo_rq1_evaluation():
    """演示RQ1评估"""
    print("=" * 60)
    print("RQ1: 数据集统计与分析 演示")
    print("=" * 60)
    
    # 创建示例数据
    original_dataset, generated_dataset = create_sample_data()
    
    # 保存到临时文件
    temp_dir = Path("evaluation/temp")
    temp_dir.mkdir(exist_ok=True)
    
    original_file = temp_dir / "original_sample.json"
    generated_file = temp_dir / "generated_sample.json"
    
    with open(original_file, 'w', encoding='utf-8') as f:
        json.dump(original_dataset, f, ensure_ascii=False, indent=2)
    
    with open(generated_file, 'w', encoding='utf-8') as f:
        json.dump(generated_dataset, f, ensure_ascii=False, indent=2)
    
    print(f"原始数据集: {len(original_dataset)} 条记录")
    print(f"生成数据集: {len(generated_dataset)} 条记录")
    
    # 导入并运行统计分析
    try:
        from evaluation.framework.metrics.statistical_metrics import (
            calculate_dataset_statistics, 
            compare_distributions
        )
        
        print("\n计算统计信息...")
        original_stats = calculate_dataset_statistics(original_dataset)
        generated_stats = calculate_dataset_statistics(generated_dataset)
        
        print(f"原始数据集统计:")
        print(f"  - 总记录数: {original_stats['total_records']}")
        print(f"  - 总实体数: {original_stats['total_entities']}")
        print(f"  - 实体类型: {original_stats['entity_types']}")
        print(f"  - 平均句子长度: {original_stats.get('avg_sentence_length', 0):.1f}")
        
        print(f"\n生成数据集统计:")
        print(f"  - 总记录数: {generated_stats['total_records']}")
        print(f"  - 总实体数: {generated_stats['total_entities']}")
        print(f"  - 实体类型: {generated_stats['entity_types']}")
        print(f"  - 平均句子长度: {generated_stats.get('avg_sentence_length', 0):.1f}")
        
        print("\n比较分布...")
        comparison = compare_distributions(original_stats, generated_stats)
        
        print(f"数据集规模比较:")
        size_comp = comparison["size_comparison"]
        print(f"  - 原始数据集大小: {size_comp['original_size']}")
        print(f"  - 生成数据集大小: {size_comp['generated_size']}")
        print(f"  - 规模比例: {size_comp['size_ratio']:.2f}")
        
        print(f"\n实体分布比较:")
        entity_comp = comparison["entity_distribution_comparison"]
        for entity_type, data in entity_comp.items():
            print(f"  - {entity_type}: {data['original_count']}  {data['generated_count']} (比例: {data['ratio']:.2f})")
        
        print("\n RQ1评估演示完成")
        
    except Exception as e:
        print(f" RQ1评估演示失败: {e}")
        import traceback
        traceback.print_exc()

def demo_quality_evaluation():
    """演示质量评估"""
    print("\n" + "=" * 60)
    print("质量评估 演示")
    print("=" * 60)
    
    try:
        from evaluation.framework.metrics.quality_metrics import (
            evaluate_naturalness,
            evaluate_annotation_accuracy,
            calculate_diversity_metrics
        )
        
        # 创建示例数据
        _, generated_dataset = create_sample_data()
        
        print("评估自然度...")
        naturalness_results = evaluate_naturalness(generated_dataset)
        print(f"  - 平均自然度得分: {naturalness_results['avg_score']:.2f}")
        print(f"  - 得分范围: {naturalness_results['min_score']:.2f} - {naturalness_results['max_score']:.2f}")
        
        print("\n评估标注准确性...")
        accuracy_results = evaluate_annotation_accuracy(generated_dataset)
        print(f"  - 边界准确率: {accuracy_results['boundary_accuracy']:.2%}")
        print(f"  - 类型准确率: {accuracy_results['type_accuracy']:.2%}")
        print(f"  - 有效实体数: {accuracy_results['valid_entities']}/{accuracy_results['total_entities']}")
        
        print("\n评估多样性...")
        diversity_results = calculate_diversity_metrics(generated_dataset)
        print(f"  - 词汇多样性: {diversity_results['vocabulary_diversity']:.3f}")
        print(f"  - 句子多样性: {diversity_results['sentence_diversity']:.3f}")
        print(f"  - 实体多样性: {diversity_results['entity_diversity']:.3f}")
        
        print("\n 质量评估演示完成")
        
    except Exception as e:
        print(f" 质量评估演示失败: {e}")
        import traceback
        traceback.print_exc()

def demo_convergence_analysis():
    """演示收敛性分析"""
    print("\n" + "=" * 60)
    print("收敛性分析 演示")
    print("=" * 60)
    
    try:
        from evaluation.framework.metrics.convergence_metrics import (
            analyze_convergence,
            calculate_trend
        )
        
        # 模拟迭代数据
        iteration_scores = [0.5, 0.6, 0.7, 0.75, 0.78, 0.79, 0.80, 0.80, 0.80]
        iterations = list(range(1, len(iteration_scores) + 1))
        
        print("分析收敛性...")
        convergence_result = analyze_convergence(iteration_scores)
        
        print(f"  - 是否收敛: {'是' if convergence_result['converged'] else '否'}")
        if convergence_result['converged']:
            print(f"  - 收敛于第 {convergence_result['convergence_iteration']} 次迭代")
        
        print(f"  - 初始得分: {convergence_result['initial_score']:.3f}")
        print(f"  - 最终得分: {convergence_result['final_score']:.3f}")
        print(f"  - 总改进: {convergence_result['total_improvement']:.3f}")
        print(f"  - 趋势: {convergence_result['trend']}")
        
        print("\n分析趋势...")
        trend_result = calculate_trend(iterations, iteration_scores)
        print(f"  - 斜率: {trend_result['slope']:.4f}")
        print(f"  - R: {trend_result['r_squared']:.4f}")
        print(f"  - 趋势方向: {trend_result['trend_direction']}")
        
        print("\n 收敛性分析演示完成")
        
    except Exception as e:
        print(f" 收敛性分析演示失败: {e}")
        import traceback
        traceback.print_exc()

def demo_ablation_analysis():
    """演示消融分析"""
    print("\n" + "=" * 60)
    print("消融分析 演示")
    print("=" * 60)
    
    try:
        from evaluation.framework.metrics.ablation_metrics import (
            calculate_component_contribution,
            simulate_performance_score
        )
        
        # 模拟实验结果
        full_system_config = {
            "use_sentence_diversity": True,
            "use_entity_diversity": True,
            "use_global_cache": True,
            "use_latent_scenarios": True,
            "max_iterations": 10
        }
        
        ablation_configs = {
            "no_sentence_diversity": {**full_system_config, "use_sentence_diversity": False},
            "no_entity_diversity": {**full_system_config, "use_entity_diversity": False},
            "no_global_cache": {**full_system_config, "use_global_cache": False},
            "no_latent_scenarios": {**full_system_config, "use_latent_scenarios": False},
            "no_iterations": {**full_system_config, "max_iterations": 1}
        }
        
        print("模拟消融实验...")
        full_score = simulate_performance_score(full_system_config)
        print(f"完整系统得分: {full_score:.3f}")
        
        print("\n各组件消融结果:")
        ablation_results = {}
        for name, config in ablation_configs.items():
            score = simulate_performance_score(config)
            ablation_results[name] = {"overall_score": score}
            contribution = full_score - score
            print(f"  - {name}: {score:.3f} (贡献度: {contribution:.3f})")
        
        print("\n计算组件贡献度...")
        full_results = {"overall_score": full_score}
        contributions = calculate_component_contribution(full_results, ablation_results)
        
        print("组件重要性排序:")
        sorted_contributions = sorted(contributions.items(), 
                                    key=lambda x: x[1]["relative_contribution"], 
                                    reverse=True)
        
        for component, data in sorted_contributions:
            print(f"  - {component}: {data['relative_contribution']:.2%}")
        
        print("\n 消融分析演示完成")
        
    except Exception as e:
        print(f" 消融分析演示失败: {e}")
        import traceback
        traceback.print_exc()

def cleanup():
    """清理临时文件"""
    temp_dir = Path("evaluation/temp")
    if temp_dir.exists():
        import shutil
        shutil.rmtree(temp_dir)
        print("\n 临时文件已清理")

def main():
    """主函数"""
    print("NER数据生成系统 - 评估框架使用示例")
    print("=" * 80)
    
    try:
        # 运行各个演示
        demo_rq1_evaluation()
        demo_quality_evaluation()
        demo_convergence_analysis()
        demo_ablation_analysis()
        
        print("\n" + "=" * 80)
        print(" 所有演示完成！")
        print("\n使用说明:")
        print("1. 运行RQ1评估: python evaluation/framework/rq1_dataset_statistics.py --help")
        print("2. 运行RQ3评估: python evaluation/framework/rq3_iteration_analysis.py --help")
        print("3. 运行综合评估: python evaluation/run_comprehensive_evaluation.py --help")
        print("4. 查看详细文档: evaluation/README.md")
        print("=" * 80)
        
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"\n演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        cleanup()

if __name__ == "__main__":
    main()
