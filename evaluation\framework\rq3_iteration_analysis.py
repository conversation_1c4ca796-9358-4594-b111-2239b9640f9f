﻿#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RQ3: 自动化迭代流程有效性评估
分析迭代优化流程是否有效提升数据质量

使用方法:
python evaluation/framework/rq3_iteration_analysis.py --run-dir synth_dataset/runs/20250705_143000 --output results/rq3/
"""

import os
import sys
import json
import argparse
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入评估指标
from evaluation.framework.metrics.convergence_metrics import (
    calculate_convergence_metrics,
    analyze_iteration_trends,
    load_iteration_data
)

def load_config():
    """加载RQ3配置"""
    config_path = Path(__file__).parent / "configs" / "rq3_config.json"
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def collect_iteration_data(run_dir: Path) -> List[Dict]:
    """收集迭代数据"""
    iteration_data = []
    
    # 查找迭代目录
    iterations_dir = run_dir / "iterations"
    if not iterations_dir.exists():
        print(f"警告: 未找到迭代目录 {iterations_dir}")
        return []
    
    # 获取所有迭代目录
    iter_dirs = [d for d in iterations_dir.iterdir() if d.is_dir() and d.name.startswith("iteration_")]
    iter_dirs.sort(key=lambda x: int(x.name.split("_")[1]))
    
    for iter_dir in iter_dirs:
        iteration_num = int(iter_dir.name.split("_")[1])
        
        # 查找迭代信息文件
        info_files = list(iter_dir.glob("*_info.json"))
        if info_files:
            try:
                with open(info_files[0], 'r', encoding='utf-8') as f:
                    iter_info = json.load(f)
                
                # 查找对应的评估结果
                eval_dir = run_dir / "evaluation" / f"iteration_{iteration_num:03d}"
                eval_file = eval_dir / "evaluation_report.json"
                
                if eval_file.exists():
                    with open(eval_file, 'r', encoding='utf-8') as f:
                        eval_result = json.load(f)
                    
                    # 合并迭代信息和评估结果
                    combined_data = {
                        **iter_info,
                        **eval_result,
                        "iteration": iteration_num
                    }
                    iteration_data.append(combined_data)
                else:
                    print(f"警告: 未找到迭代 {iteration_num} 的评估结果")
                    
            except Exception as e:
                print(f"警告: 读取迭代 {iteration_num} 数据失败: {e}")
    
    return iteration_data

def create_convergence_plots(iteration_data: List[Dict], output_dir: Path):
    """创建收敛性图表"""
    if not iteration_data:
        print("警告: 没有迭代数据，跳过收敛性图表生成")
        return
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 提取数据
    iterations = [data["iteration"] for data in iteration_data]
    entity_scores = []
    diversity_scores = []
    quality_scores = []
    
    for data in iteration_data:
        # 实体分布得分
        balance_eval = data.get("balance_evaluation", {})
        entity_scores.append(balance_eval.get("overall_score", 0))
        
        # 多样性得分
        diversity_eval = data.get("diversity_evaluation", {})
        diversity_scores.append(diversity_eval.get("vocabulary_diversity", 0))
        
        # 质量得分
        naturalness_eval = data.get("naturalness_evaluation", {})
        quality_scores.append(naturalness_eval.get("avg_score", 0) / 10)  # 归一化到0-1
    
    # 创建收敛曲线图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 实体分布收敛曲线
    ax1.plot(iterations, entity_scores, 'o-', linewidth=2, markersize=6, label='实体分布得分')
    ax1.set_xlabel('迭代次数')
    ax1.set_ylabel('得分')
    ax1.set_title('实体分布得分收敛曲线')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 2. 多样性收敛曲线
    ax2.plot(iterations, diversity_scores, 'o-', linewidth=2, markersize=6, label='多样性得分', color='orange')
    ax2.set_xlabel('迭代次数')
    ax2.set_ylabel('得分')
    ax2.set_title('多样性得分收敛曲线')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    # 3. 质量收敛曲线
    ax3.plot(iterations, quality_scores, 'o-', linewidth=2, markersize=6, label='质量得分', color='green')
    ax3.set_xlabel('迭代次数')
    ax3.set_ylabel('得分')
    ax3.set_title('质量得分收敛曲线')
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    
    # 4. 综合收敛曲线
    composite_scores = [0.4 * e + 0.3 * d + 0.3 * q for e, d, q in zip(entity_scores, diversity_scores, quality_scores)]
    ax4.plot(iterations, composite_scores, 'o-', linewidth=2, markersize=6, label='综合得分', color='red')
    ax4.set_xlabel('迭代次数')
    ax4.set_ylabel('得分')
    ax4.set_title('综合得分收敛曲线')
    ax4.grid(True, alpha=0.3)
    ax4.legend()
    
    plt.tight_layout()
    plt.savefig(output_dir / "convergence_curves.png", dpi=300, bbox_inches='tight')
    plt.close()
    
    # 创建改进率图
    fig, ax = plt.subplots(1, 1, figsize=(10, 6))
    
    # 计算改进率
    if len(iterations) > 1:
        entity_improvements = [entity_scores[i] - entity_scores[i-1] for i in range(1, len(entity_scores))]
        diversity_improvements = [diversity_scores[i] - diversity_scores[i-1] for i in range(1, len(diversity_scores))]
        quality_improvements = [quality_scores[i] - quality_scores[i-1] for i in range(1, len(quality_scores))]
        
        iter_range = iterations[1:]
        
        ax.plot(iter_range, entity_improvements, 'o-', label='实体分布改进', alpha=0.8)
        ax.plot(iter_range, diversity_improvements, 's-', label='多样性改进', alpha=0.8)
        ax.plot(iter_range, quality_improvements, '^-', label='质量改进', alpha=0.8)
        
        ax.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax.set_xlabel('迭代次数')
        ax.set_ylabel('改进幅度')
        ax.set_title('各指标迭代改进率')
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        plt.tight_layout()
        plt.savefig(output_dir / "improvement_rates.png", dpi=300, bbox_inches='tight')
        plt.close()

def create_efficiency_plots(iteration_data: List[Dict], output_dir: Path):
    """创建效率分析图表"""
    if not iteration_data:
        return
    
    # 提取效率数据
    iterations = [data["iteration"] for data in iteration_data]
    time_costs = []
    api_calls = []
    data_sizes = []
    
    for data in iteration_data:
        # 时间成本（如果有的话）
        time_cost = data.get("generation_time", 0)
        time_costs.append(time_cost)
        
        # API调用次数（如果有的话）
        api_call_count = data.get("api_calls", 0)
        api_calls.append(api_call_count)
        
        # 数据集大小
        dataset_size = data.get("dataset_size", 0)
        data_sizes.append(dataset_size)
    
    # 创建效率图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 时间成本趋势
    if any(t > 0 for t in time_costs):
        ax1.plot(iterations, time_costs, 'o-', linewidth=2, markersize=6, color='blue')
        ax1.set_xlabel('迭代次数')
        ax1.set_ylabel('时间成本 (秒)')
        ax1.set_title('时间成本趋势')
        ax1.grid(True, alpha=0.3)
    else:
        ax1.text(0.5, 0.5, '无时间成本数据', ha='center', va='center', transform=ax1.transAxes)
        ax1.set_title('时间成本趋势 (无数据)')
    
    # 2. API调用趋势
    if any(a > 0 for a in api_calls):
        ax2.plot(iterations, api_calls, 'o-', linewidth=2, markersize=6, color='orange')
        ax2.set_xlabel('迭代次数')
        ax2.set_ylabel('API调用次数')
        ax2.set_title('API调用次数趋势')
        ax2.grid(True, alpha=0.3)
    else:
        ax2.text(0.5, 0.5, '无API调用数据', ha='center', va='center', transform=ax2.transAxes)
        ax2.set_title('API调用次数趋势 (无数据)')
    
    # 3. 数据集大小增长
    ax3.plot(iterations, data_sizes, 'o-', linewidth=2, markersize=6, color='green')
    ax3.set_xlabel('迭代次数')
    ax3.set_ylabel('数据集大小')
    ax3.set_title('数据集大小增长趋势')
    ax3.grid(True, alpha=0.3)
    
    # 4. 效率比率（质量提升/时间成本）
    if any(t > 0 for t in time_costs) and len(iterations) > 1:
        # 计算质量改进
        quality_scores = []
        for data in iteration_data:
            naturalness_eval = data.get("naturalness_evaluation", {})
            quality_scores.append(naturalness_eval.get("avg_score", 0))
        
        quality_improvements = [quality_scores[i] - quality_scores[i-1] for i in range(1, len(quality_scores))]
        time_intervals = [time_costs[i] for i in range(1, len(time_costs))]
        
        efficiency_ratios = [q/t if t > 0 else 0 for q, t in zip(quality_improvements, time_intervals)]
        
        ax4.plot(iterations[1:], efficiency_ratios, 'o-', linewidth=2, markersize=6, color='red')
        ax4.set_xlabel('迭代次数')
        ax4.set_ylabel('效率比率 (质量提升/时间)')
        ax4.set_title('迭代效率比率')
        ax4.grid(True, alpha=0.3)
    else:
        ax4.text(0.5, 0.5, '无效率数据', ha='center', va='center', transform=ax4.transAxes)
        ax4.set_title('迭代效率比率 (无数据)')
    
    plt.tight_layout()
    plt.savefig(output_dir / "efficiency_analysis.png", dpi=300, bbox_inches='tight')
    plt.close()

def analyze_target_achievement(iteration_data: List[Dict]) -> Dict[str, Any]:
    """分析目标达成情况"""
    if not iteration_data:
        return {"error": "No iteration data"}
    
    target_analysis = {
        "entity_distribution_progress": [],
        "quality_progress": [],
        "final_achievement": {},
        "convergence_analysis": {}
    }
    
    # 分析实体分布目标达成
    for data in iteration_data:
        balance_eval = data.get("balance_evaluation", {})
        entity_gap = data.get("entity_gap", {})
        
        total_gap = sum(entity_gap.values()) if entity_gap else 0
        distribution_score = balance_eval.get("overall_score", 0)
        
        target_analysis["entity_distribution_progress"].append({
            "iteration": data["iteration"],
            "distribution_score": distribution_score,
            "total_gap": total_gap,
            "gap_reduction": 0  # 将在后面计算
        })
    
    # 计算gap减少量
    for i in range(1, len(target_analysis["entity_distribution_progress"])):
        current_gap = target_analysis["entity_distribution_progress"][i]["total_gap"]
        previous_gap = target_analysis["entity_distribution_progress"][i-1]["total_gap"]
        gap_reduction = previous_gap - current_gap
        target_analysis["entity_distribution_progress"][i]["gap_reduction"] = gap_reduction
    
    # 分析质量目标达成
    for data in iteration_data:
        naturalness_eval = data.get("naturalness_evaluation", {})
        quality_score = naturalness_eval.get("avg_score", 0)
        
        target_analysis["quality_progress"].append({
            "iteration": data["iteration"],
            "quality_score": quality_score,
            "meets_threshold": quality_score >= 6.0  # 假设6.0为质量阈值
        })
    
    # 最终达成情况
    if iteration_data:
        final_data = iteration_data[-1]
        final_balance = final_data.get("balance_evaluation", {})
        final_diversity = final_data.get("diversity_evaluation", {})
        final_naturalness = final_data.get("naturalness_evaluation", {})
        
        target_analysis["final_achievement"] = {
            "balance_passed": final_balance.get("passed", False),
            "diversity_passed": final_diversity.get("passed", False),
            "quality_threshold_met": final_naturalness.get("avg_score", 0) >= 6.0,
            "overall_passed": final_data.get("overall_passed", False)
        }
    
    return target_analysis

def generate_rq3_report(iteration_data: List[Dict], convergence_metrics: Dict, 
                       trends_analysis: Dict, target_analysis: Dict, output_dir: Path):
    """生成RQ3评估报告"""
    report = {
        "evaluation_type": "RQ3: 自动化迭代流程有效性评估",
        "evaluation_time": datetime.now().isoformat(),
        "summary": {
            "total_iterations": len(iteration_data),
            "convergence_achieved": convergence_metrics.get("overall_convergence", {}).get("converged", False),
            "final_targets_achieved": target_analysis.get("final_achievement", {}).get("overall_passed", False)
        },
        "iteration_data": iteration_data,
        "convergence_analysis": convergence_metrics,
        "trends_analysis": trends_analysis,
        "target_achievement": target_analysis,
        "conclusions": generate_rq3_conclusions(convergence_metrics, trends_analysis, target_analysis)
    }
    
    # 保存报告
    with open(output_dir / "rq3_evaluation_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    # 生成文本摘要
    generate_rq3_text_summary(report, output_dir)

def generate_rq3_conclusions(convergence_metrics: Dict, trends_analysis: Dict, 
                            target_analysis: Dict) -> Dict[str, Any]:
    """生成RQ3结论"""
    conclusions = {
        "convergence_effectiveness": "",
        "iteration_efficiency": "",
        "target_achievement_effectiveness": "",
        "optimal_iteration_count": "",
        "overall_assessment": ""
    }
    
    # 收敛有效性
    overall_convergence = convergence_metrics.get("overall_convergence", {})
    if overall_convergence.get("converged", False):
        conclusions["convergence_effectiveness"] = "迭代流程成功实现收敛"
    else:
        conclusions["convergence_effectiveness"] = "迭代流程未完全收敛，可能需要更多迭代"
    
    # 迭代效率
    efficiency_metrics = convergence_metrics.get("efficiency_metrics", {})
    time_efficiency = efficiency_metrics.get("time_efficiency", {})
    
    if time_efficiency.get("time_trend") == "decreasing":
        conclusions["iteration_efficiency"] = "迭代效率逐步提升"
    elif time_efficiency.get("time_trend") == "increasing":
        conclusions["iteration_efficiency"] = "迭代效率逐步下降"
    else:
        conclusions["iteration_efficiency"] = "迭代效率保持稳定"
    
    # 目标达成有效性
    final_achievement = target_analysis.get("final_achievement", {})
    achieved_targets = sum(final_achievement.values())
    total_targets = len(final_achievement)
    
    if achieved_targets == total_targets:
        conclusions["target_achievement_effectiveness"] = "所有目标均已达成"
    elif achieved_targets >= total_targets * 0.7:
        conclusions["target_achievement_effectiveness"] = "大部分目标已达成"
    else:
        conclusions["target_achievement_effectiveness"] = "目标达成情况不理想"
    
    # 最优迭代次数
    optimal_point = trends_analysis.get("optimal_stopping_point", {})
    optimal_iter = optimal_point.get("optimal_iteration", 0)
    total_iters = convergence_metrics.get("total_iterations", 0)
    
    if optimal_iter < total_iters:
        conclusions["optimal_iteration_count"] = f"建议在第{optimal_iter}次迭代停止"
    else:
        conclusions["optimal_iteration_count"] = "当前迭代次数合理"
    
    # 整体评估
    if (overall_convergence.get("converged", False) and 
        achieved_targets >= total_targets * 0.7):
        conclusions["overall_assessment"] = "迭代流程有效，显著提升了数据质量"
    elif achieved_targets >= total_targets * 0.5:
        conclusions["overall_assessment"] = "迭代流程部分有效，有一定的质量提升"
    else:
        conclusions["overall_assessment"] = "迭代流程效果有限，需要优化策略"
    
    return conclusions

def generate_rq3_text_summary(report: Dict, output_dir: Path):
    """生成RQ3文本摘要"""
    summary_lines = []
    summary_lines.append("=" * 60)
    summary_lines.append("RQ3: 自动化迭代流程有效性评估报告")
    summary_lines.append("=" * 60)
    summary_lines.append(f"评估时间: {report['evaluation_time']}")
    summary_lines.append("")
    
    # 基本信息
    summary = report["summary"]
    summary_lines.append("迭代流程基本信息:")
    summary_lines.append(f"  总迭代次数: {summary['total_iterations']}")
    summary_lines.append(f"  是否收敛: {'是' if summary['convergence_achieved'] else '否'}")
    summary_lines.append(f"  目标达成: {'是' if summary['final_targets_achieved'] else '否'}")
    summary_lines.append("")
    
    # 收敛分析
    convergence = report["convergence_analysis"]
    summary_lines.append("收敛性分析:")
    
    entity_conv = convergence.get("entity_distribution_convergence", {})
    diversity_conv = convergence.get("diversity_convergence", {})
    quality_conv = convergence.get("quality_convergence", {})
    
    summary_lines.append(f"  实体分布收敛: {'是' if entity_conv.get('converged', False) else '否'}")
    if entity_conv.get('converged', False):
        summary_lines.append(f"    收敛于第{entity_conv.get('convergence_iteration', 0)}次迭代")
    
    summary_lines.append(f"  多样性收敛: {'是' if diversity_conv.get('converged', False) else '否'}")
    if diversity_conv.get('converged', False):
        summary_lines.append(f"    收敛于第{diversity_conv.get('convergence_iteration', 0)}次迭代")
    
    summary_lines.append(f"  质量收敛: {'是' if quality_conv.get('converged', False) else '否'}")
    if quality_conv.get('converged', False):
        summary_lines.append(f"    收敛于第{quality_conv.get('convergence_iteration', 0)}次迭代")
    
    summary_lines.append("")
    
    # 效率分析
    efficiency = convergence.get("efficiency_metrics", {})
    if efficiency:
        summary_lines.append("效率分析:")
        time_eff = efficiency.get("time_efficiency", {})
        if time_eff:
            summary_lines.append(f"  平均每次迭代时间: {time_eff.get('average_time_per_iteration', 0):.2f} 秒")
            summary_lines.append(f"  时间趋势: {time_eff.get('time_trend', '未知')}")
        
        resource_eff = efficiency.get("resource_efficiency", {})
        if resource_eff:
            summary_lines.append(f"  平均每次API调用: {resource_eff.get('average_api_calls_per_iteration', 0):.0f}")
        summary_lines.append("")
    
    # 目标达成分析
    target_achievement = report["target_achievement"]
    final_achievement = target_achievement.get("final_achievement", {})
    
    summary_lines.append("目标达成情况:")
    summary_lines.append(f"  均衡性目标: {'达成' if final_achievement.get('balance_passed', False) else '未达成'}")
    summary_lines.append(f"  多样性目标: {'达成' if final_achievement.get('diversity_passed', False) else '未达成'}")
    summary_lines.append(f"  质量目标: {'达成' if final_achievement.get('quality_threshold_met', False) else '未达成'}")
    summary_lines.append("")
    
    # 结论
    conclusions = report["conclusions"]
    summary_lines.append("评估结论:")
    for key, conclusion in conclusions.items():
        summary_lines.append(f"  {key}: {conclusion}")
    
    # 保存摘要
    with open(output_dir / "rq3_summary.txt", 'w', encoding='utf-8') as f:
        f.write('\n'.join(summary_lines))

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="RQ3: 自动化迭代流程有效性评估")
    parser.add_argument("--run-dir", required=True, help="运行目录路径")
    parser.add_argument("--output", required=True, help="输出目录")
    parser.add_argument("--config", help="配置文件路径")
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print("=" * 60)
    print("RQ3: 自动化迭代流程有效性评估")
    print("=" * 60)
    print(f"运行目录: {args.run_dir}")
    print(f"输出目录: {output_dir}")
    print()
    
    try:
        # 加载配置
        config = load_config()
        
        # 收集迭代数据
        print("收集迭代数据...")
        run_dir = Path(args.run_dir)
        iteration_data = collect_iteration_data(run_dir)
        
        if not iteration_data:
            print("错误: 未找到有效的迭代数据")
            sys.exit(1)
        
        print(f"找到 {len(iteration_data)} 次迭代的数据")
        
        # 计算收敛性指标
        print("计算收敛性指标...")
        convergence_metrics = calculate_convergence_metrics(iteration_data)
        
        # 分析迭代趋势
        print("分析迭代趋势...")
        trends_analysis = analyze_iteration_trends(iteration_data)
        
        # 分析目标达成情况
        print("分析目标达成情况...")
        target_analysis = analyze_target_achievement(iteration_data)
        
        # 生成可视化
        if config["rq3_iteration_analysis"]["metrics"]["convergence_analysis"]["enabled"]:
            print("生成收敛性图表...")
            create_convergence_plots(iteration_data, output_dir)
        
        if config["rq3_iteration_analysis"]["metrics"]["efficiency_analysis"]["enabled"]:
            print("生成效率分析图表...")
            create_efficiency_plots(iteration_data, output_dir)
        
        # 生成报告
        print("生成评估报告...")
        generate_rq3_report(iteration_data, convergence_metrics, trends_analysis, 
                           target_analysis, output_dir)
        
        print(f"\n RQ3评估完成！结果已保存到: {output_dir}")
        print(f"  - 详细报告: {output_dir}/rq3_evaluation_report.json")
        print(f"  - 文本摘要: {output_dir}/rq3_summary.txt")
        if config["rq3_iteration_analysis"]["metrics"]["convergence_analysis"]["enabled"]:
            print(f"  - 收敛曲线图: {output_dir}/convergence_curves.png")
            print(f"  - 改进率图: {output_dir}/improvement_rates.png")
        if config["rq3_iteration_analysis"]["metrics"]["efficiency_analysis"]["enabled"]:
            print(f"  - 效率分析图: {output_dir}/efficiency_analysis.png")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
