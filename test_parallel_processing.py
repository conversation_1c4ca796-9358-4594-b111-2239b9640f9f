#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试并行处理和重试功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.synth_data.ner_data_generation import (
    PARALLEL_CONFIG,
    RETRY_CONFIG,
    LOG_CONFIG,
    task_manager,
    should_retry_error,
    calculate_retry_delay,
    call_zhipu_api_with_retry,
    generate_ner_data_parallel,
    generate_ner_data_sequential
)

def test_config_loading():
    """测试配置加载"""
    print("=" * 60)
    print("测试配置加载")
    print("=" * 60)
    
    print("并行处理配置:")
    for key, value in PARALLEL_CONFIG.items():
        print(f"  {key}: {value}")
    
    print("\n重试配置:")
    for key, value in RETRY_CONFIG.items():
        print(f"  {key}: {value}")
    
    print("\n日志配置:")
    for key, value in LOG_CONFIG.items():
        print(f"  {key}: {value}")

def test_retry_logic():
    """测试重试逻辑"""
    print("\n" + "=" * 60)
    print("测试重试逻辑")
    print("=" * 60)
    
    # 测试重试延迟计算
    print("重试延迟计算:")
    for retry_count in range(5):
        delay = calculate_retry_delay(retry_count)
        print(f"  重试次数 {retry_count}: {delay:.2f}秒")
    
    # 测试错误重试判断
    import requests
    
    # 模拟不同类型的错误
    test_errors = [
        requests.exceptions.RequestException("网络错误"),
        requests.exceptions.Timeout("超时错误"),
        Exception("普通错误")
    ]
    
    print("\n错误重试判断:")
    for error in test_errors:
        should_retry = should_retry_error(error)
        print(f"  {type(error).__name__}: {'应该重试' if should_retry else '不应该重试'}")

def test_task_manager():
    """测试任务管理器"""
    print("\n" + "=" * 60)
    print("测试任务管理器")
    print("=" * 60)
    
    # 创建测试任务
    task_id1 = task_manager.create_task("姓名", "generation", {"target_count": 10})
    task_id2 = task_manager.create_task("职业", "annotation", {"batch_size": 5})
    
    print(f"创建任务: {task_id1}, {task_id2}")
    
    # 更新任务状态
    task_manager.update_task_status(task_id1, task_manager.tasks[task_id1].status.SUCCESS)
    task_manager.update_task_status(task_id2, task_manager.tasks[task_id2].status.FAILED, "测试错误")
    
    # 记录错误
    try:
        raise Exception("测试异常")
    except Exception as e:
        task_manager.log_task_error(task_id2, e, {"test": True})
    
    # 获取统计信息
    stats = task_manager.get_task_stats()
    print(f"任务统计: {stats}")

def test_parallel_vs_sequential():
    """测试并行vs串行处理"""
    print("\n" + "=" * 60)
    print("测试并行vs串行处理")
    print("=" * 60)
    
    # 模拟目标分布
    target_distribution = {
        "姓名": 5,
        "职业": 5,
        "地理位置": 5
    }
    
    # 模拟其他参数
    sentence_diversity = {}
    vanilla_entities = {}
    latent_entities = {}
    examples = []
    generation_features = {
        "use_entity_diversity": False,
        "use_sentence_diversity": False,
        "use_example_sentences": False
    }
    
    print("目标分布:", target_distribution)
    print("并行处理配置:", PARALLEL_CONFIG.get("enabled", True))
    
    # 注意：这个测试需要API调用，可能会失败
    try:
        print("\n尝试并行处理（可能会失败，因为需要API）...")
        # 这里只是测试函数调用，不实际执行
        print("并行处理函数可用")
    except Exception as e:
        print(f"并行处理测试失败（预期）: {e}")
    
    try:
        print("\n尝试串行处理（可能会失败，因为需要API）...")
        # 这里只是测试函数调用，不实际执行
        print("串行处理函数可用")
    except Exception as e:
        print(f"串行处理测试失败（预期）: {e}")

def test_api_retry():
    """测试API重试功能"""
    print("\n" + "=" * 60)
    print("测试API重试功能")
    print("=" * 60)
    
    # 注意：这个测试需要API调用，可能会失败
    test_prompt = "请生成一个包含姓名的句子。"
    
    try:
        print("尝试带重试的API调用（可能会失败，因为需要API）...")
        # 这里只是测试函数调用，不实际执行
        print("API重试函数可用")
    except Exception as e:
        print(f"API重试测试失败（预期）: {e}")

def test_logging():
    """测试日志功能"""
    print("\n" + "=" * 60)
    print("测试日志功能")
    print("=" * 60)
    
    if LOG_CONFIG.get("enabled", True):
        print("日志功能已启用")
        print(f"日志级别: {LOG_CONFIG.get('log_level', 'INFO')}")
        print(f"日志目录: {LOG_CONFIG.get('log_dir', 'logs')}")
        
        # 测试日志记录
        task_manager.logger.info("测试信息日志")
        task_manager.logger.warning("测试警告日志")
        task_manager.logger.error("测试错误日志")
        
        print("日志记录测试完成")
    else:
        print("日志功能已禁用")

if __name__ == "__main__":
    print("开始测试并行处理和重试功能...")
    
    # 测试1：配置加载
    test_config_loading()
    
    # 测试2：重试逻辑
    test_retry_logic()
    
    # 测试3：任务管理器
    test_task_manager()
    
    # 测试4：并行vs串行处理
    test_parallel_vs_sequential()
    
    # 测试5：API重试
    test_api_retry()
    
    # 测试6：日志功能
    test_logging()
    
    print("\n测试完成！") 