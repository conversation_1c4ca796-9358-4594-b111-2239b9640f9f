﻿# -*- coding: utf-8 -*-
"""
高级质量评估模块
扩展质量指标，支持更深入的质量分析
"""

import json
import numpy as np
from typing import Dict, List, Any, Tuple
from collections import Counter, defaultdict
import jieba
import re

# 导入基础质量指标
from .quality_metrics import (
    evaluate_naturalness,
    evaluate_annotation_accuracy,
    calculate_diversity_metrics
)

def evaluate_linguistic_quality(dataset: List[Dict]) -> Dict[str, Any]:
    """评估语言学质量"""
    linguistic_metrics = {
        "grammar_score": 0.0,
        "fluency_score": 0.0,
        "coherence_score": 0.0,
        "readability_score": 0.0,
        "detailed_analysis": {}
    }
    
    grammar_scores = []
    fluency_scores = []
    coherence_scores = []
    readability_scores = []
    
    for item in dataset:
        text = item.get("text", "")
        
        # 语法评估
        grammar_score = evaluate_grammar(text)
        grammar_scores.append(grammar_score)
        
        # 流畅性评估
        fluency_score = evaluate_fluency(text)
        fluency_scores.append(fluency_score)
        
        # 连贯性评估
        coherence_score = evaluate_coherence(text)
        coherence_scores.append(coherence_score)
        
        # 可读性评估
        readability_score = evaluate_readability(text)
        readability_scores.append(readability_score)
    
    linguistic_metrics.update({
        "grammar_score": np.mean(grammar_scores),
        "fluency_score": np.mean(fluency_scores),
        "coherence_score": np.mean(coherence_scores),
        "readability_score": np.mean(readability_scores),
        "detailed_analysis": {
            "grammar_distribution": analyze_score_distribution(grammar_scores),
            "fluency_distribution": analyze_score_distribution(fluency_scores),
            "coherence_distribution": analyze_score_distribution(coherence_scores),
            "readability_distribution": analyze_score_distribution(readability_scores)
        }
    })
    
    return linguistic_metrics

def evaluate_grammar(text: str) -> float:
    """评估语法正确性"""
    score = 1.0
    
    # 基本语法规则检查
    # 1. 标点符号使用
    if not text.strip():
        return 0.0
    
    # 检查句子结构
    if not text.endswith(('。', '！', '？', '；', '，')):
        score -= 0.1
    
    # 检查重复标点
    if re.search(r'[。！？]{2,}', text):
        score -= 0.1
    
    # 检查括号匹配
    if text.count('（') != text.count('）') or text.count('(') != text.count(')'):
        score -= 0.1
    
    # 检查引号匹配
    if text.count('"') % 2 != 0 or text.count('"') % 2 != 0:
        score -= 0.1
    
    # 检查词汇合理性
    words = list(jieba.cut(text))
    if len(words) < 2:
        score -= 0.2
    
    # 检查异常字符
    if re.search(r'[^\u4e00-\u9fff\w\s，。！？；：""''（）【】\-]', text):
        score -= 0.1
    
    return max(0.0, score)

def evaluate_fluency(text: str) -> float:
    """评估流畅性"""
    score = 1.0
    
    words = list(jieba.cut(text))
    
    # 词汇重复检查
    word_counts = Counter(words)
    total_words = len(words)
    
    if total_words > 0:
        # 检查过度重复
        max_repetition = max(word_counts.values()) if word_counts else 0
        if max_repetition > total_words * 0.3:  # 超过30%重复
            score -= 0.2
        
        # 检查单字词过多
        single_chars = sum(1 for word in words if len(word) == 1 and word not in '，。！？；：')
        if single_chars > total_words * 0.5:
            score -= 0.1
    
    # 检查句子长度合理性
    if len(text) < 5:
        score -= 0.3
    elif len(text) > 200:
        score -= 0.1
    
    return max(0.0, score)

def evaluate_coherence(text: str) -> float:
    """评估连贯性"""
    score = 1.0
    
    # 检查逻辑连接词
    connectors = ['因为', '所以', '但是', '然而', '而且', '另外', '首先', '其次', '最后', '总之']
    has_connectors = any(connector in text for connector in connectors)
    
    # 检查代词使用
    pronouns = ['他', '她', '它', '这', '那', '其', '此']
    has_pronouns = any(pronoun in text for pronoun in pronouns)
    
    # 简单的连贯性评分
    if len(text) > 20:  # 较长文本需要连贯性
        if not has_connectors and not has_pronouns:
            score -= 0.1
    
    return max(0.0, score)

def evaluate_readability(text: str) -> float:
    """评估可读性"""
    score = 1.0
    
    words = list(jieba.cut(text))
    
    # 平均词长
    if words:
        avg_word_length = sum(len(word) for word in words) / len(words)
        if avg_word_length > 4:  # 词汇过于复杂
            score -= 0.1
        elif avg_word_length < 1.5:  # 词汇过于简单
            score -= 0.05
    
    # 句子复杂度
    comma_count = text.count('，')
    if comma_count > 5:  # 句子过于复杂
        score -= 0.1
    
    return max(0.0, score)

def analyze_score_distribution(scores: List[float]) -> Dict[str, Any]:
    """分析得分分布"""
    if not scores:
        return {"mean": 0, "std": 0, "min": 0, "max": 0, "percentiles": {}}
    
    return {
        "mean": np.mean(scores),
        "std": np.std(scores),
        "min": np.min(scores),
        "max": np.max(scores),
        "percentiles": {
            "25th": np.percentile(scores, 25),
            "50th": np.percentile(scores, 50),
            "75th": np.percentile(scores, 75),
            "95th": np.percentile(scores, 95)
        }
    }

def evaluate_entity_coverage(dataset: List[Dict], target_distribution: Dict[str, int] = None) -> Dict[str, Any]:
    """评估实体覆盖率"""
    actual_distribution = Counter()
    
    for item in dataset:
        labels = item.get("label", [])
        for label in labels:
            entity_type = label.get("type", "")
            if entity_type:
                actual_distribution[entity_type] += 1
    
    coverage_metrics = {
        "type_coverage": 0.0,
        "quantity_coverage": 0.0,
        "distribution_similarity": 0.0,
        "missing_types": [],
        "excess_types": [],
        "detailed_coverage": {}
    }
    
    if target_distribution:
        target_types = set(target_distribution.keys())
        actual_types = set(actual_distribution.keys())
        
        # 类型覆盖率
        covered_types = target_types & actual_types
        coverage_metrics["type_coverage"] = len(covered_types) / len(target_types) if target_types else 0
        
        # 缺失和多余的类型
        coverage_metrics["missing_types"] = list(target_types - actual_types)
        coverage_metrics["excess_types"] = list(actual_types - target_types)
        
        # 数量覆盖率
        total_target = sum(target_distribution.values())
        total_actual = sum(actual_distribution.values())
        coverage_metrics["quantity_coverage"] = min(1.0, total_actual / total_target) if total_target > 0 else 0
        
        # 分布相似性
        coverage_metrics["distribution_similarity"] = calculate_distribution_similarity(
            target_distribution, dict(actual_distribution)
        )
        
        # 详细覆盖情况
        for entity_type in target_types:
            target_count = target_distribution[entity_type]
            actual_count = actual_distribution.get(entity_type, 0)
            
            coverage_metrics["detailed_coverage"][entity_type] = {
                "target_count": target_count,
                "actual_count": actual_count,
                "coverage_ratio": min(1.0, actual_count / target_count) if target_count > 0 else 0,
                "excess_ratio": max(0.0, (actual_count - target_count) / target_count) if target_count > 0 else 0
            }
    
    return coverage_metrics

def calculate_distribution_similarity(target_dist: Dict[str, int], actual_dist: Dict[str, int]) -> float:
    """计算分布相似性（使用余弦相似度）"""
    all_types = set(target_dist.keys()) | set(actual_dist.keys())
    
    if not all_types:
        return 1.0
    
    target_vector = [target_dist.get(t, 0) for t in all_types]
    actual_vector = [actual_dist.get(t, 0) for t in all_types]
    
    # 余弦相似度
    dot_product = sum(t * a for t, a in zip(target_vector, actual_vector))
    target_norm = sum(t ** 2 for t in target_vector) ** 0.5
    actual_norm = sum(a ** 2 for a in actual_vector) ** 0.5
    
    if target_norm == 0 or actual_norm == 0:
        return 0.0
    
    return dot_product / (target_norm * actual_norm)

def evaluate_entity_quality(dataset: List[Dict]) -> Dict[str, Any]:
    """评估实体质量"""
    entity_metrics = {
        "entity_length_analysis": {},
        "entity_position_analysis": {},
        "entity_context_analysis": {},
        "entity_type_distribution": {},
        "problematic_entities": []
    }
    
    entity_data = defaultdict(list)
    entity_positions = defaultdict(list)
    entity_contexts = defaultdict(list)
    
    for item_idx, item in enumerate(dataset):
        text = item.get("text", "")
        labels = item.get("label", [])
        
        for label in labels:
            entity_type = label.get("type", "")
            entity_text = label.get("text", "")
            start = label.get("start", 0)
            end = label.get("end", 0)
            
            # 收集实体数据
            entity_data[entity_type].append({
                "text": entity_text,
                "length": len(entity_text),
                "start": start,
                "end": end,
                "item_index": item_idx
            })
            
            # 位置分析
            relative_position = start / len(text) if len(text) > 0 else 0
            entity_positions[entity_type].append(relative_position)
            
            # 上下文分析
            context_start = max(0, start - 5)
            context_end = min(len(text), end + 5)
            context = text[context_start:context_end]
            entity_contexts[entity_type].append(context)
            
            # 检查问题实体
            if is_problematic_entity(entity_text, entity_type, text, start, end):
                entity_metrics["problematic_entities"].append({
                    "entity_text": entity_text,
                    "entity_type": entity_type,
                    "context": text,
                    "item_index": item_idx,
                    "issue": identify_entity_issue(entity_text, entity_type, text, start, end)
                })
    
    # 分析各实体类型
    for entity_type, entities in entity_data.items():
        lengths = [e["length"] for e in entities]
        positions = entity_positions[entity_type]
        
        entity_metrics["entity_length_analysis"][entity_type] = {
            "avg_length": np.mean(lengths) if lengths else 0,
            "min_length": np.min(lengths) if lengths else 0,
            "max_length": np.max(lengths) if lengths else 0,
            "length_variance": np.var(lengths) if lengths else 0
        }
        
        entity_metrics["entity_position_analysis"][entity_type] = {
            "avg_position": np.mean(positions) if positions else 0,
            "position_variance": np.var(positions) if positions else 0,
            "position_distribution": analyze_position_distribution(positions)
        }
        
        # 上下文多样性
        contexts = entity_contexts[entity_type]
        unique_contexts = len(set(contexts))
        entity_metrics["entity_context_analysis"][entity_type] = {
            "total_contexts": len(contexts),
            "unique_contexts": unique_contexts,
            "context_diversity": unique_contexts / len(contexts) if contexts else 0,
            "most_common_contexts": Counter(contexts).most_common(3)
        }
    
    # 实体类型分布
    entity_type_counts = {et: len(entities) for et, entities in entity_data.items()}
    entity_metrics["entity_type_distribution"] = entity_type_counts
    
    return entity_metrics

def is_problematic_entity(entity_text: str, entity_type: str, context: str, start: int, end: int) -> bool:
    """检查是否为问题实体"""
    # 边界检查
    if start < 0 or end > len(context) or start >= end:
        return True
    
    # 文本一致性检查
    if context[start:end] != entity_text:
        return True
    
    # 长度合理性检查
    if len(entity_text) == 0 or len(entity_text) > 20:
        return True
    
    # 特殊字符检查
    if re.search(r'[^\u4e00-\u9fff\w\s\-]', entity_text):
        return True
    
    # 类型合理性检查
    if entity_type == "人名" and len(entity_text) > 6:
        return True
    elif entity_type == "时间" and not re.search(r'[\d年月日时分秒]', entity_text):
        return True
    
    return False

def identify_entity_issue(entity_text: str, entity_type: str, context: str, start: int, end: int) -> str:
    """识别实体问题类型"""
    if start < 0 or end > len(context) or start >= end:
        return "边界错误"
    
    if context[start:end] != entity_text:
        return "文本不匹配"
    
    if len(entity_text) == 0:
        return "空实体"
    elif len(entity_text) > 20:
        return "实体过长"
    
    if re.search(r'[^\u4e00-\u9fff\w\s\-]', entity_text):
        return "包含特殊字符"
    
    if entity_type == "人名" and len(entity_text) > 6:
        return "人名过长"
    elif entity_type == "时间" and not re.search(r'[\d年月日时分秒]', entity_text):
        return "时间格式错误"
    
    return "其他问题"

def analyze_position_distribution(positions: List[float]) -> Dict[str, float]:
    """分析位置分布"""
    if not positions:
        return {"beginning": 0, "middle": 0, "end": 0}
    
    beginning = sum(1 for p in positions if p < 0.33) / len(positions)
    middle = sum(1 for p in positions if 0.33 <= p < 0.67) / len(positions)
    end = sum(1 for p in positions if p >= 0.67) / len(positions)
    
    return {"beginning": beginning, "middle": middle, "end": end}

def evaluate_cross_validation_quality(dataset: List[Dict], fold_count: int = 5) -> Dict[str, Any]:
    """交叉验证质量评估"""
    from random import shuffle
    
    # 随机打乱数据集
    shuffled_dataset = dataset.copy()
    shuffle(shuffled_dataset)
    
    fold_size = len(shuffled_dataset) // fold_count
    cv_results = {
        "fold_results": [],
        "average_metrics": {},
        "consistency_analysis": {}
    }
    
    for fold_idx in range(fold_count):
        start_idx = fold_idx * fold_size
        end_idx = start_idx + fold_size if fold_idx < fold_count - 1 else len(shuffled_dataset)
        
        fold_data = shuffled_dataset[start_idx:end_idx]
        
        # 评估该折的质量
        fold_naturalness = evaluate_naturalness(fold_data, sample_size=min(20, len(fold_data)))
        fold_accuracy = evaluate_annotation_accuracy(fold_data)
        fold_diversity = calculate_diversity_metrics(fold_data)
        
        fold_result = {
            "fold_index": fold_idx,
            "data_size": len(fold_data),
            "naturalness_score": fold_naturalness["avg_score"],
            "boundary_accuracy": fold_accuracy["boundary_accuracy"],
            "type_accuracy": fold_accuracy["type_accuracy"],
            "vocabulary_diversity": fold_diversity.get("vocabulary_diversity", 0),
            "entity_diversity": fold_diversity.get("entity_diversity", 0)
        }
        
        cv_results["fold_results"].append(fold_result)
    
    # 计算平均指标
    if cv_results["fold_results"]:
        metrics = ["naturalness_score", "boundary_accuracy", "type_accuracy", 
                  "vocabulary_diversity", "entity_diversity"]
        
        for metric in metrics:
            values = [fold[metric] for fold in cv_results["fold_results"]]
            cv_results["average_metrics"][metric] = {
                "mean": np.mean(values),
                "std": np.std(values),
                "min": np.min(values),
                "max": np.max(values)
            }
    
    # 一致性分析
    cv_results["consistency_analysis"] = analyze_cv_consistency(cv_results["fold_results"])
    
    return cv_results

def analyze_cv_consistency(fold_results: List[Dict]) -> Dict[str, Any]:
    """分析交叉验证一致性"""
    consistency = {
        "overall_consistency": 0.0,
        "metric_consistency": {},
        "stability_score": 0.0
    }
    
    if len(fold_results) < 2:
        return consistency
    
    metrics = ["naturalness_score", "boundary_accuracy", "type_accuracy", 
              "vocabulary_diversity", "entity_diversity"]
    
    consistency_scores = []
    
    for metric in metrics:
        values = [fold[metric] for fold in fold_results]
        
        # 计算变异系数（CV）
        mean_val = np.mean(values)
        std_val = np.std(values)
        cv = std_val / mean_val if mean_val > 0 else 0
        
        # 一致性得分（变异系数越小，一致性越高）
        metric_consistency = max(0, 1 - cv)
        consistency_scores.append(metric_consistency)
        
        consistency["metric_consistency"][metric] = {
            "consistency_score": metric_consistency,
            "coefficient_of_variation": cv,
            "range": np.max(values) - np.min(values)
        }
    
    consistency["overall_consistency"] = np.mean(consistency_scores)
    consistency["stability_score"] = 1 - np.std(consistency_scores)
    
    return consistency
