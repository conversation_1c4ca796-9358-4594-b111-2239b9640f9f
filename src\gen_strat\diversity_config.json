{"api_key_path": "auth/zhipu-api-key.json", "entity_schema_path": "src/gen_strat/entity_schema.json", "num_sentence_variants": 5, "num_entity_variants": 50, "skip_entity_types": [], "entity_context_mapping": {"姓名": ["新闻报道", "法律文本", "社交场合", "口语表达", "网络用语"], "年龄": ["新闻报道", "医学描述", "社交场合", "法律文本", "口语表达"], "性别": ["医学描述", "新闻报道", "法律文本", "社交场合", "网络用语"], "国籍": ["新闻报道", "法律文本", "学术论文"], "职业": ["新闻报道", "法律文本", "社交场合", "商务场合"], "民族": ["新闻报道", "法律文本", "学术论文"], "教育背景": ["新闻报道", "法律文本", "学术论文", "社交场合"], "婚姻状况": ["法律文本", "新闻报道", "社交场合", "口语表达"], "政治倾向": ["新闻报道", "法律文本", "学术论文"], "家庭成员": ["新闻报道", "社交场合", "口语表达"], "工资数额": ["法律文本", "商务场合"], "投资产品": ["法律文本", "商务场合"], "税务记录": ["法律文本", "学术论文"], "信用记录": ["法律文本", "商务场合"], "实体资产": ["法律文本", "新闻报道"], "交易信息": ["法律文本", "商务场合"], "疾病": ["医学描述", "新闻报道", "法律文本"], "药物": ["医学描述", "学术论文", "法律文本"], "临床表现": ["医学描述", "学术论文"], "医疗程序": ["医学描述", "学术论文"], "过敏信息": ["医学描述", "法律文本"], "生育信息": ["医学描述", "法律文本", "新闻报道"], "地理位置": ["新闻报道", "社交场合", "法律文本"], "行程信息": ["新闻报道", "法律文本", "口语表达"]}}