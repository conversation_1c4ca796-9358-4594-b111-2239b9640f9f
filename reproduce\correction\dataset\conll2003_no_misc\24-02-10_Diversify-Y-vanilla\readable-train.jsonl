{"sentence": "Protesters gather in downtown Portland to support the Black Lives Matter movement.", "entity_names": ["Portland", "Black Lives Matter"], "entity_types": ["location", "organization"]}
{"sentence": "New York City mayor announces police reform plan in response to Black Lives Matter demonstrations.", "entity_names": ["New York City", "Black Lives Matter"], "entity_types": ["location", "organization"]}
{"sentence": "Chicago police chief meets with leaders of Black Lives Matter to discuss community policing initiatives.", "entity_names": ["Chicago", "Black Lives Matter"], "entity_types": ["location", "organization"]}
{"sentence": "<PERSON> to direct new film about the United Nations Development Programme.", "entity_names": ["<PERSON>", "United Nations Development Programme"], "entity_types": ["person", "organization"]}
{"sentence": "United Nations Development Programme launches new initiative to combat climate change in developing countries.", "entity_names": ["United Nations Development Programme"], "entity_types": ["organization"]}
{"sentence": "<PERSON>'s latest movie receives funding from the United Nations Development Programme.", "entity_names": ["<PERSON>", "United Nations Development Programme"], "entity_types": ["person", "organization"]}
{"sentence": "Elon Musk's SpaceX successfully launches 60 Starlink satellites into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "Paris Climate Agreement participants pledge to reduce carbon emissions by 20% by 2030.", "entity_names": ["Paris"], "entity_types": ["location"]}
{"sentence": "Apple Inc. announces record-breaking quarterly earnings, surpassing analyst expectations.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "The Organization of the Petroleum Exporting Countries announces a decrease in oil production.", "entity_names": ["Organization of the Petroleum Exporting Countries"], "entity_types": ["organization"]}
{"sentence": "Russia and Saudi Arabia agree to cooperate on oil production cuts to stabilize the market.", "entity_names": ["Russia", "Saudi Arabia"], "entity_types": ["location", "location"]}
{"sentence": "Oil prices surge as tensions rise between the United States and Iran.", "entity_names": ["United States", "Iran"], "entity_types": ["location", "location"]}
{"sentence": "Pop star Britney Spears announces new Las Vegas residency.", "entity_names": ["Britney Spears", "Las Vegas"], "entity_types": ["person", "location"]}
{"sentence": "Britney Spears' new album tops charts in over 20 countries.", "entity_names": ["Britney Spears"], "entity_types": ["person"]}
{"sentence": "Britney Spears' former manager files lawsuit against her estate.", "entity_names": ["Britney Spears"], "entity_types": ["person"]}
{"sentence": "Sydney Opera House to host annual music festival.", "entity_names": ["Sydney", "Opera House"], "entity_types": ["location", "location"]}
{"sentence": "Australian Prime Minister to visit Sydney for climate change summit.", "entity_names": ["Australian", "Sydney"], "entity_types": ["location", "location"]}
{"sentence": "New South Wales government announces major infrastructure plan for Sydney.", "entity_names": ["New South Wales government", "Sydney"], "entity_types": ["organization", "location"]}
{"sentence": "Supreme Court Justice Ruth Bader Ginsburg hospitalized for treatment of possible infection.", "entity_names": ["Supreme Court", "Ruth Bader Ginsburg"], "entity_types": ["organization", "person"]}
{"sentence": "Ruth Bader Ginsburg, United States Supreme Court Justice, to undergo non-surgical procedure for benign gallbladder condition.", "entity_names": ["Ruth Bader Ginsburg", "United States Supreme Court"], "entity_types": ["person", "organization"]}
{"sentence": "Ruth Bader Ginsburg to return to work after treatment for cancer.", "entity_names": ["Ruth Bader Ginsburg"], "entity_types": ["person"]}
{"sentence": "Billie Eilish's new album tops the charts.", "entity_names": ["Billie Eilish"], "entity_types": ["person"]}
{"sentence": "Billie Eilish announces world tour dates for next year.", "entity_names": ["Billie Eilish"], "entity_types": ["person"]}
{"sentence": "Angelina Jolie donates $1 million to refugee charity.", "entity_names": ["Angelina Jolie"], "entity_types": ["person"]}
{"sentence": "Critics praise Angelina Jolie's performance in new film.", "entity_names": ["Angelina Jolie"], "entity_types": ["person"]}
{"sentence": "Angelina Jolie partners with UNICEF for new global initiative.", "entity_names": ["Angelina Jolie", "UNICEF"], "entity_types": ["person", "organization"]}
{"sentence": "Miami Heat signs top free agent from Oslo.", "entity_names": ["Miami", "Heat", "Oslo"], "entity_types": ["location", "organization", "location"]}
{"sentence": "Oslo to host international peace summit.", "entity_names": ["Oslo"], "entity_types": ["location"]}
{"sentence": "Miami police department announces new chief of police.", "entity_names": ["Miami"], "entity_types": ["location"]}
{"sentence": "Oxfam International reports a rise in global hunger due to the COVID-19 pandemic.", "entity_names": ["Oxfam International"], "entity_types": ["organization"]}
{"sentence": "The United Nations calls for urgent action to address the climate crisis.", "entity_names": ["United Nations"], "entity_types": ["organization"]}
{"sentence": "Professor Johnson's research reveals promising results in the fight against cancer.", "entity_names": ["Professor Johnson"], "entity_types": ["person"]}
{"sentence": "Microsoft acquires cybersecurity firm for $10 billion.", "entity_names": ["Microsoft", "cybersecurity firm"], "entity_types": ["organization", "organization"]}
{"sentence": "President Biden meets with German Chancellor Angela Merkel in Washington.", "entity_names": ["President Biden", "Angela Merkel", "Washington"], "entity_types": ["person", "person", "location"]}
{"sentence": "Tropical storm damages thousands of homes in Philippines.", "entity_names": ["Philippines"], "entity_types": ["location"]}
{"sentence": "International Red Cross deploys relief teams to flood-affected areas in Southeast Asia.", "entity_names": ["International Red Cross"], "entity_types": ["organization"]}
{"sentence": "Renowned scientist from Zurich wins prestigious award for contribution to environmental sustainability.", "entity_names": ["Zurich"], "entity_types": ["location"]}
{"sentence": "Tesla stocks soar after successful launch of new electric vehicle.", "entity_names": ["Tesla"], "entity_types": ["organization"]}
{"sentence": "Australian Prime Minister meets with Chinese President to discuss trade agreements.", "entity_names": ["Australian Prime Minister", "Chinese President"], "entity_types": ["person", "person"]}
{"sentence": "Flooding in Midwest causes thousands to evacuate their homes.", "entity_names": ["Midwest"], "entity_types": ["location"]}
{"sentence": "The new Starbucks Corporation branch in Jakarta is expected to attract a large number of coffee lovers.", "entity_names": ["Starbucks Corporation", "Jakarta"], "entity_types": ["organization", "location"]}
{"sentence": "Jakarta experiences heavy flooding after a week of relentless rain.", "entity_names": ["Jakarta"], "entity_types": ["location"]}
{"sentence": "APPLE ANNOUNCES LAUNCH OF NEW IPHONE MODEL.", "entity_names": ["APPLE", "IPHONE"], "entity_types": ["organization", "organization"]}
{"sentence": "TOM CRUISE TO STAR IN UPCOMING ACTION THRILLER FILM.", "entity_names": ["TOM CRUISE"], "entity_types": ["person"]}
{"sentence": "CALIFORNIA GOVERNOR SIGNS BILL TO COMBAT CLIMATE CHANGE.", "entity_names": ["CALIFORNIA"], "entity_types": ["location"]}
{"sentence": "Dwayne \"The Rock\" Johnson announces new production company.", "entity_names": ["Dwayne \"The Rock\" Johnson", "production company"], "entity_types": ["person", "organization"]}
{"sentence": "Record-breaking heatwave hits Western United States, prompting emergency measures.", "entity_names": ["Western United States"], "entity_types": ["location"]}
{"sentence": "New York City Marathon canceled due to extreme weather conditions.", "entity_names": ["New York City", "Marathon"], "entity_types": ["location", "organization"]}
{"sentence": "Coca-Cola plans to launch a new line of flavored sparkling water.", "entity_names": ["Coca-Cola"], "entity_types": ["organization"]}
{"sentence": "Investors speculate on potential merger between PepsiCo and Coca-Cola.", "entity_names": ["PepsiCo", "Coca-Cola"], "entity_types": ["organization", "organization"]}
{"sentence": "Coca-Cola CEO announced significant expansion in Asian markets.", "entity_names": ["Coca-Cola"], "entity_types": ["organization"]}
{"sentence": "Queen Elizabeth II celebrates her 95th birthday.", "entity_names": ["Queen Elizabeth II"], "entity_types": ["person"]}
{"sentence": "Walmart announces plans to open 100 new stores nationwide.", "entity_names": ["Walmart"], "entity_types": ["organization"]}
{"sentence": "The new documentary features interviews with Walmart employees.", "entity_names": ["Walmart"], "entity_types": ["organization"]}
{"sentence": "Aung San Suu Kyi calls for release of political prisoners in Myanmar.", "entity_names": ["Aung San Suu Kyi", "Myanmar"], "entity_types": ["person", "location"]}
{"sentence": "Mumbai police arrest suspects in connection with terrorist plot.", "entity_names": ["Mumbai"], "entity_types": ["location"]}
{"sentence": "New organization in Mumbai aims to provide support for homeless youth.", "entity_names": ["Mumbai"], "entity_types": ["location"]}
{"sentence": "UN reports record number of refugees in Vienna.", "entity_names": ["UN", "Vienna"], "entity_types": ["organization", "location"]}
{"sentence": "Vienna Philharmonic Orchestra cancels upcoming tour due to COVID-19.", "entity_names": ["Vienna Philharmonic Orchestra"], "entity_types": ["organization"]}
{"sentence": "Vienna Mayor announces new public transportation initiatives.", "entity_names": [], "entity_types": []}
{"sentence": "Los Angeles Lakers clinch victory in NBA finals.", "entity_names": ["Los Angeles", "Lakers"], "entity_types": ["location", "organization"]}
{"sentence": "Elon Omar to lead climate change initiative.", "entity_names": ["Elon Omar"], "entity_types": ["person"]}
{"sentence": "United Nations condemns new wave of violence in Middle East.", "entity_names": ["United Nations", "Middle East"], "entity_types": ["organization", "location"]}
{"sentence": "Beijing announces new environmental regulations to reduce air pollution.", "entity_names": ["Beijing"], "entity_types": ["location"]}
{"sentence": "Thousands gather in Beijing to celebrate Chinese New Year.", "entity_names": ["Beijing"], "entity_types": ["location"]}
{"sentence": "Beijing Olympics committee selects renowned architect to design main stadium.", "entity_names": ["Beijing"], "entity_types": ["location"]}
{"sentence": "National Public Radio reports record-breaking viewership for its latest investigative documentary.", "entity_names": ["National Public Radio"], "entity_types": ["organization"]}
{"sentence": "Award-winning journalist John Smith announced as the new host of the National Public Radio morning show.", "entity_names": ["John Smith", "National Public Radio"], "entity_types": ["person", "organization"]}
{"sentence": "National Public Radio investigates claims of corruption within the local government.", "entity_names": ["National Public Radio"], "entity_types": ["organization"]}
{"sentence": "The Coca-Cola Company announces new sustainability initiatives in Sydney.", "entity_names": ["Coca-Cola Company", "Sydney"], "entity_types": ["organization", "location"]}
{"sentence": "Sydney to host international film festival featuring global directors.", "entity_names": ["Sydney"], "entity_types": ["location"]}
{"sentence": "Coca-Cola Company reports record sales for the third quarter.", "entity_names": ["Coca-Cola Company"], "entity_types": ["organization"]}
{"sentence": "Martin Scorsese to premiere new film at London Film Festival.", "entity_names": ["Martin Scorsese", "London"], "entity_types": ["person", "location"]}
{"sentence": "London mayor announces new initiative to combat air pollution in the city.", "entity_names": ["London"], "entity_types": ["location"]}
{"sentence": "Martin Scorsese's latest project to be filmed in London.", "entity_names": ["Martin Scorsese", "London"], "entity_types": ["person", "location"]}
{"sentence": "The government announced a new transportation infrastructure plan for Jakarta.", "entity_names": ["Jakarta"], "entity_types": ["location"]}
{"sentence": "Local residents in Jakarta protest against the construction of a new shopping mall.", "entity_names": ["Jakarta"], "entity_types": ["location"]}
{"sentence": "Jakarta Mayor plans to implement a new waste management system in the city.", "entity_names": ["Jakarta", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "Nancy Pelosi announces plans to address climate change with support from the International Energy Agency.", "entity_names": ["Nancy Pelosi", "International Energy Agency"], "entity_types": ["person", "organization"]}
{"sentence": "The International Energy Agency predicts a global oil demand surge in the coming months.", "entity_names": ["International Energy Agency"], "entity_types": ["organization"]}
{"sentence": "Nancy Pelosi calls for bipartisan cooperation in Congress to pass new infrastructure legislation.", "entity_names": ["Nancy Pelosi"], "entity_types": ["person"]}
{"sentence": "Greenpeace activists protest outside the Hanoi government building.", "entity_names": ["Greenpeace", "Hanoi"], "entity_types": ["organization", "location"]}
{"sentence": "The mayor of Hanoi meets with foreign investors to discuss infrastructure projects.", "entity_names": ["Hanoi"], "entity_types": ["location"]}
{"sentence": "Greenpeace warns of environmental damage in Hanoi due to excessive air pollution.", "entity_names": ["Greenpeace", "Hanoi"], "entity_types": ["organization", "location"]}
{"sentence": "The International Organization for Migration assists refugees fleeing war-torn countries.", "entity_names": ["International Organization for Migration"], "entity_types": ["organization"]}
{"sentence": "The United States Department of Justice announces new initiatives to combat cybercrime.", "entity_names": ["United States Department of Justice"], "entity_types": ["organization"]}
{"sentence": "Thousands of migrants are stranded at the border between Mexico and the United States.", "entity_names": ["Mexico", "United States"], "entity_types": ["location", "location"]}
{"sentence": "Real Madrid defeats Barcelona in a dramatic overtime victory.", "entity_names": ["Real Madrid", "Barcelona"], "entity_types": ["organization", "location"]}
{"sentence": "Protests erupt in Madrid over government decision to raise taxes.", "entity_names": ["Madrid"], "entity_types": ["location"]}
{"sentence": "Renowned chef from Madrid opens new restaurant in London.", "entity_names": ["Madrid", "London"], "entity_types": ["location", "location"]}
{"sentence": "Rosal\u00eda wins big at the Latin Grammy Awards.", "entity_names": ["Rosal\u00eda"], "entity_types": ["person"]}
{"sentence": "Volodymyr Zelensky calls for peace talks in Ukraine.", "entity_names": ["Volodymyr Zelensky", "Ukraine"], "entity_types": ["person", "location"]}
{"sentence": "World Bank Group announces new investment initiative in developing countries.", "entity_names": ["World Bank Group"], "entity_types": ["organization"]}
{"sentence": "Elon Musk announces SpaceX's plan to send humans to Mars by 2026.", "entity_names": ["Elon Musk", "SpaceX", "Mars"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Apple's new iPhone 13 to be released next month.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "President Biden signs $1.2 trillion infrastructure bill into law.", "entity_names": ["President Biden"], "entity_types": ["person"]}
{"sentence": "The Indian government announces new policies to address air pollution in Delhi.", "entity_names": ["Indian government", "Delhi"], "entity_types": ["organization", "location"]}
{"sentence": "Protesters gather in Delhi to demand change in government policies.", "entity_names": ["Delhi"], "entity_types": ["location"]}
{"sentence": "Delhi schools to remain closed due to severe air pollution levels.", "entity_names": ["Delhi"], "entity_types": ["location"]}
{"sentence": "United Nations Development Programme allocates $10 million for education projects in Africa.", "entity_names": ["United Nations Development Programme", "Africa"], "entity_types": ["organization", "location"]}
{"sentence": "Renowned economist Joseph Stiglitz joins United Nations Development Programme as senior advisor.", "entity_names": ["Joseph Stiglitz", "United Nations Development Programme"], "entity_types": ["person", "organization"]}
{"sentence": "United Nations Development Programme launches initiative to combat climate change in vulnerable island nations.", "entity_names": ["United Nations Development Programme"], "entity_types": ["organization"]}
{"sentence": "Elon Musk's SpaceX successfully launches 60 more Starlink satellites.", "entity_names": ["Elon Musk", "SpaceX", "Starlink"], "entity_types": ["person", "organization", "organization"]}
{"sentence": "The United Nations reports a record number of refugees fleeing conflict in the Middle East.", "entity_names": ["United Nations", "Middle East"], "entity_types": ["organization", "location"]}
{"sentence": "President Biden signs executive order to address climate change.", "entity_names": ["President Biden"], "entity_types": ["person"]}
{"sentence": "Kourtney Kardashian launches her new lifestyle brand Poosh.", "entity_names": ["Kourtney Kardashian"], "entity_types": ["person"]}
{"sentence": "New York City mayor announces plan to expand affordable housing options.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Tesla's stock reaches record high after strong quarterly earnings report.", "entity_names": ["Tesla"], "entity_types": ["organization"]}
{"sentence": "Elon Musk's SpaceX successfully launches satellite into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "New York City announces plan to invest $100 million in affordable housing.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Apple Inc. reports record-breaking sales for latest iPhone model.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "The International Red Cross and Red Crescent Movement provides aid to victims of the natural disaster in Haiti.", "entity_names": ["International Red Cross and Red Crescent Movement", "Haiti"], "entity_types": ["organization", "location"]}
{"sentence": "Health officials in the Democratic Republic of Congo warn of the spread of Ebola.", "entity_names": ["Democratic Republic of Congo"], "entity_types": ["location"]}
{"sentence": "The United Nations calls for a cease-fire in the ongoing conflict in Syria.", "entity_names": ["United Nations", "Syria"], "entity_types": ["organization", "location"]}
{"sentence": "Edinburgh's population reaches over 500,000 for the first time.", "entity_names": ["Edinburgh"], "entity_types": ["location"]}
{"sentence": "David Beckham invests in new esports organization.", "entity_names": ["David Beckham"], "entity_types": ["person"]}
{"sentence": "Record number of visitors flock to Edinburgh Festival.", "entity_names": ["Edinburgh"], "entity_types": ["location"]}
{"sentence": "The World Trade Organization released a report on global trade tensions.", "entity_names": ["World Trade Organization"], "entity_types": ["organization"]}
{"sentence": "The International Energy Agency predicts a decrease in oil demand for the next quarter.", "entity_names": ["International Energy Agency"], "entity_types": ["organization"]}
{"sentence": "The United States, China, and the European Union are in talks to address trade imbalances.", "entity_names": ["United States", "China", "European Union"], "entity_types": ["location", "location", "organization"]}
{"sentence": "Tesla unveils new electric car with longer battery life.", "entity_names": ["Tesla"], "entity_types": ["organization"]}
{"sentence": "Angela Merkel elected as Chancellor of Germany for the fourth term.", "entity_names": ["Angela Merkel", "Germany"], "entity_types": ["person", "location"]}
{"sentence": "Amazon announces plans to open new headquarters in New York City.", "entity_names": ["Amazon", "New York City"], "entity_types": ["organization", "location"]}
{"sentence": "George W. Bush delivers keynote speech at Nairobi economic summit.", "entity_names": ["George W. Bush", "Nairobi"], "entity_types": ["person", "location"]}
{"sentence": "New business startup in Nairobi sees rapid growth in its first year.", "entity_names": ["Nairobi"], "entity_types": ["location"]}
{"sentence": "George W. Bush Foundation for Military Service members announces new scholarship program.", "entity_names": ["George W. Bush"], "entity_types": ["person"]}
{"sentence": "David Beckham announces his retirement from professional soccer.", "entity_names": ["David Beckham"], "entity_types": ["person"]}
{"sentence": "IBM Corporation reports a 15% increase in quarterly profits.", "entity_names": ["IBM Corporation"], "entity_types": ["organization"]}
{"sentence": "The Democratic National Committee reveals its latest campaign strategy for the upcoming election.", "entity_names": ["Democratic National Committee"], "entity_types": ["organization"]}
{"sentence": "The peace talks in Reykjavik have reached a deadlock.", "entity_names": ["Reykjavik"], "entity_types": ["location"]}
{"sentence": "Sony announces the release of their new gaming console next month.", "entity_names": ["Sony"], "entity_types": ["organization"]}
{"sentence": "Taliban insurgents have launched a series of attacks in the region.", "entity_names": ["Taliban"], "entity_types": ["organization"]}
{"sentence": "The Federal Trade Commission investigates allegations of antitrust violations in the tech industry.", "entity_names": ["Federal Trade Commission"], "entity_types": ["organization"]}
{"sentence": "Vienna to host international summit on climate change next month.", "entity_names": ["Vienna"], "entity_types": ["location"]}
{"sentence": "Amal Clooney speaks at United Nations conference on human rights.", "entity_names": ["Amal Clooney", "United Nations"], "entity_types": ["person", "organization"]}
{"sentence": "The embassy in Moscow remains closed amid escalating tensions.", "entity_names": ["Moscow"], "entity_types": ["location"]}
{"sentence": "Bras\u00edlia mayor announces plan to improve public transportation system.", "entity_names": ["Bras\u00edlia"], "entity_types": ["location"]}
{"sentence": "Prominent artist from Moscow to showcase new exhibition in New York.", "entity_names": ["Moscow"], "entity_types": ["location"]}
{"sentence": "Tesla announces new electric car model.", "entity_names": ["Tesla"], "entity_types": ["organization"]}
{"sentence": "Tokyo Olympics officially begin with opening ceremony.", "entity_names": ["Tokyo"], "entity_types": ["location"]}
{"sentence": "Dr. Johnson appointed as new CEO of pharmaceutical company.", "entity_names": ["Dr. Johnson"], "entity_types": ["person"]}
{"sentence": "Tom Hanks to star in new sci-fi thriller directed by Christopher Nolan.", "entity_names": ["Tom Hanks", "Christopher Nolan"], "entity_types": ["person", "person"]}
{"sentence": "Local charity organization to host fundraising event with special appearance by Tom Hanks.", "entity_names": ["Tom Hanks"], "entity_types": ["person"]}
{"sentence": "Tom Hanks donates $1 million to children's hospital for new research wing.", "entity_names": ["Tom Hanks"], "entity_types": ["person"]}
{"sentence": "Brad Pitt signs deal with major film production company.", "entity_names": ["Brad Pitt"], "entity_types": ["person"]}
{"sentence": "Brad Pitt to attend premiere of new movie in New York City.", "entity_names": ["Brad Pitt", "New York City"], "entity_types": ["person", "location"]}
{"sentence": "Environmental organization led by Brad Pitt launches new conservation initiative.", "entity_names": ["Brad Pitt"], "entity_types": ["person"]}
{"sentence": "Technology company Apple Inc. introduces new line of iPhones.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Jennifer Lopez and Ben Affleck spotted together in Los Angeles.", "entity_names": ["Jennifer Lopez", "Ben Affleck", "Los Angeles"], "entity_types": ["person", "person", "location"]}
{"sentence": "United Nations reports humanitarian crisis in war-torn region.", "entity_names": ["United Nations"], "entity_types": ["organization"]}
{"sentence": "Elon Musk's SpaceX successfully launches new satellite into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "Tropical storm warning issued for Florida coast.", "entity_names": ["Florida"], "entity_types": ["location"]}
{"sentence": "Hollywood actress Angelina Jolie donates $1 million to refugee charity.", "entity_names": ["Angelina Jolie"], "entity_types": ["person"]}
{"sentence": "NATO strengthens its military presence in Eastern Europe.", "entity_names": ["NATO", "Eastern Europe"], "entity_types": ["organization", "location"]}
{"sentence": "Bank of America announces plans to close several branches in the Midwest.", "entity_names": ["Bank of America", "Midwest"], "entity_types": ["organization", "location"]}
{"sentence": "Malala Yousafzai receives the Nobel Peace Prize.", "entity_names": ["Malala Yousafzai"], "entity_types": ["person"]}
{"sentence": "The United Nations Security Council condemns the recent missile tests by North Korea.", "entity_names": ["United Nations Security Council", "North Korea"], "entity_types": ["organization", "location"]}
{"sentence": "Famous chef Jamie Oliver opens new restaurant in New York City.", "entity_names": ["Jamie Oliver", "New York City"], "entity_types": ["person", "location"]}
{"sentence": "United Nations Educational, Scientific and Cultural Organization launches new education initiative in Africa.", "entity_names": ["United Nations Educational, Scientific and Cultural Organization", "Africa"], "entity_types": ["organization", "location"]}
{"sentence": "Renowned chef Gordon Ramsay to open new restaurant in Los Angeles.", "entity_names": ["Gordon Ramsay", "Los Angeles"], "entity_types": ["person", "location"]}
{"sentence": "Stocks surge as Apple Inc. announces record-breaking quarterly earnings.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Tesla unveils new electric car model.", "entity_names": ["Tesla"], "entity_types": ["organization"]}
{"sentence": "President Biden to visit European Union headquarters in Brussels next week.", "entity_names": ["President Biden", "European Union", "Brussels"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Wildfires in California continue to threaten homes and natural habitats.", "entity_names": ["California"], "entity_types": ["location"]}
{"sentence": "Tech giants Apple and Google to release new smartphones next week.", "entity_names": ["Apple", "Google"], "entity_types": ["organization", "organization"]}
{"sentence": "President Biden announces new infrastructure plan.", "entity_names": ["President Biden"], "entity_types": ["person"]}
{"sentence": "Hurricane Florence makes landfall in North Carolina.", "entity_names": ["North Carolina"], "entity_types": ["location"]}
{"sentence": "Canadian Prime Minister Justin Trudeau meets with Colombian singer Shakira to discuss education funding.", "entity_names": ["Justin Trudeau", "Shakira"], "entity_types": ["person", "person"]}
{"sentence": "Shakira's charity organization donates $1 million to the earthquake relief efforts in Haiti.", "entity_names": ["Shakira", "Haiti"], "entity_types": ["person", "location"]}
{"sentence": "Justin Trudeau announces new measures to combat climate change at the United Nations summit in New York.", "entity_names": ["Justin Trudeau", "United Nations", "New York"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Hip-hop mogul Jay-Z launches new streaming service.", "entity_names": ["Jay-Z", "streaming service"], "entity_types": ["person", "organization"]}
{"sentence": "Beyonc\u00e9 and Jay-Z plan to buy a luxury villa in Italy.", "entity_names": ["Beyonc\u00e9", "Jay-Z", "Italy"], "entity_types": ["person", "person", "location"]}
{"sentence": "Jay-Z's music label signs new up-and-coming artist.", "entity_names": ["Jay-Z"], "entity_types": ["person"]}
{"sentence": "Singapore reports record number of new COVID-19 cases.", "entity_names": ["Singapore"], "entity_types": ["location"]}
{"sentence": "Meghan Markle launches new charitable foundation.", "entity_names": ["Meghan Markle"], "entity_types": ["person"]}
{"sentence": "Singapore Airlines announces partnership with international hotel chain.", "entity_names": ["Singapore Airlines"], "entity_types": ["organization"]}
{"sentence": "Charlize Theron wins Best Actress award at the Oscars.", "entity_names": ["Charlize Theron"], "entity_types": ["person"]}
{"sentence": "Panama City announces new plans for public transportation system.", "entity_names": ["Panama City"], "entity_types": ["location"]}
{"sentence": "Beirut explosion leaves hundreds dead and injured.", "entity_names": ["Beirut"], "entity_types": ["location"]}
{"sentence": "Rome mayor announces new COVID-19 restrictions for city residents.", "entity_names": ["Rome"], "entity_types": ["location"]}
{"sentence": "Celebrity chef opens new restaurant in Beirut.", "entity_names": ["Beirut"], "entity_types": ["location"]}
{"sentence": "Academy Award-winner George Clooney to direct and star in new political thriller.", "entity_names": ["George Clooney"], "entity_types": ["person"]}
{"sentence": "Ocean conservation organization, founded by George Clooney, raises $1 million in charity auction.", "entity_names": ["George Clooney"], "entity_types": ["person"]}
{"sentence": "George Clooney's new film tops box office in opening weekend.", "entity_names": ["George Clooney"], "entity_types": ["person"]}
{"sentence": "NATO to increase presence in Eastern Europe to deter Russian aggression.", "entity_names": ["NATO", "Eastern Europe"], "entity_types": ["organization", "location"]}
{"sentence": "Cybersecurity concerns prompt NATO to hold emergency meeting.", "entity_names": ["NATO"], "entity_types": ["organization"]}
{"sentence": "Member nations of NATO commit to increase defense spending.", "entity_names": ["NATO"], "entity_types": ["organization"]}
{"sentence": "Tesla CEO Elon Musk announces plans to build new gigafactory in Texas.", "entity_names": ["Tesla", "Elon Musk", "Texas"], "entity_types": ["organization", "person", "location"]}
{"sentence": "China inaugurates the world's longest sea bridge connecting Hong Kong, Macau, and Zhuhai.", "entity_names": ["China", "Hong Kong", "Macau", "Zhuhai"], "entity_types": ["location", "location", "location", "location"]}
{"sentence": "UNICEF launches vaccination campaign in war-torn Syrian city.", "entity_names": ["UNICEF", "Syrian city"], "entity_types": ["organization", "location"]}
{"sentence": "Michelle Obama to release new memoir next year.", "entity_names": ["Michelle Obama"], "entity_types": ["person"]}
{"sentence": "Robert Downey Jr. to star in new superhero movie.", "entity_names": ["Robert Downey Jr."], "entity_types": ["person"]}
{"sentence": "Zlatan Ibrahimovic signs new contract with LA Galaxy.", "entity_names": ["Zlatan Ibrahimovic", "LA Galaxy"], "entity_types": ["person", "organization"]}
{"sentence": "The Federal Aviation Administration issued an emergency order to fix Boeing 737 MAX planes.", "entity_names": ["Federal Aviation Administration", "Boeing"], "entity_types": ["organization", "organization"]}
{"sentence": "Condoleezza Rice met with world leaders in Singapore to discuss international security.", "entity_names": ["Condoleezza Rice", "Singapore"], "entity_types": ["person", "location"]}
{"sentence": "Singapore announced a new initiative to promote renewable energy.", "entity_names": ["Singapore"], "entity_types": ["location"]}
{"sentence": "Ford Motor Company announces plans to invest $22 billion in electric vehicles.", "entity_names": ["Ford Motor Company"], "entity_types": ["organization"]}
{"sentence": "The CEO of Ford Motor Company predicts a surge in demand for electric cars in the next decade.", "entity_names": ["Ford Motor Company"], "entity_types": ["organization"]}
{"sentence": "Ford Motor Company to open a new manufacturing plant in Mexico.", "entity_names": ["Ford Motor Company", "Mexico"], "entity_types": ["organization", "location"]}
{"sentence": "The United Nations has issued a warning about the humanitarian crisis in Yemen.", "entity_names": ["United Nations", "Yemen"], "entity_types": ["organization", "location"]}
{"sentence": "Tesla announces plans to build a new Gigafactory in Germany.", "entity_names": ["Tesla", "Germany"], "entity_types": ["organization", "location"]}
{"sentence": "Jennifer Lopez and Alex Rodriguez call off their engagement.", "entity_names": ["Jennifer Lopez", "Alex Rodriguez"], "entity_types": ["person", "person"]}
{"sentence": "Ariana Grande's new single breaks record on Twitter.", "entity_names": ["Ariana Grande", "Twitter"], "entity_types": ["person", "organization"]}
{"sentence": "Twitter announces new policy on hate speech.", "entity_names": ["Twitter"], "entity_types": ["organization"]}
{"sentence": "Ariana Grande surprises fans with impromptu performance in New York City.", "entity_names": ["Ariana Grande", "New York City"], "entity_types": ["person", "location"]}
{"sentence": "Marco Rubio visits Riyadh for diplomatic talks.", "entity_names": ["Marco Rubio", "Riyadh"], "entity_types": ["person", "location"]}
{"sentence": "San Francisco's Bay Area Rapid Transit system faces criticism for delays.", "entity_names": ["San Francisco"], "entity_types": ["location"]}
{"sentence": "Breaking news: Riyadh-based organization announces partnership with major tech company.", "entity_names": ["Riyadh"], "entity_types": ["location"]}
{"sentence": "Quentin Tarantino announces his upcoming film project at a press conference in Los Angeles.", "entity_names": ["Quentin Tarantino", "Los Angeles"], "entity_types": ["person", "location"]}
{"sentence": "Planned Parenthood to open a new health clinic in downtown Portland next month.", "entity_names": ["Planned Parenthood", "Portland"], "entity_types": ["organization", "location"]}
{"sentence": "Actress Emma Stone praises Quentin Tarantino's latest movie at the premiere in New York City.", "entity_names": ["Emma Stone", "Quentin Tarantino", "New York City"], "entity_types": ["person", "person", "location"]}
{"sentence": "Al Jazeera Media Network reports record viewership for its latest documentary series on wildlife conservation.", "entity_names": ["Al Jazeera Media Network"], "entity_types": ["organization"]}
{"sentence": "The United Nations condemns the ongoing violence in Myanmar and calls for immediate action to protect civilians.", "entity_names": ["United Nations", "Myanmar"], "entity_types": ["organization", "location"]}
{"sentence": "Environmental activist Greta Thunberg meets with European Union leaders to discuss climate change policy.", "entity_names": ["Greta Thunberg", "European Union"], "entity_types": ["person", "organization"]}
{"sentence": "The OPEC members met in Wellington to discuss oil production cuts.", "entity_names": ["OPEC", "Wellington"], "entity_types": ["organization", "location"]}
{"sentence": "Rio de Janeiro to host the 2024 Summer Olympics.", "entity_names": ["Rio de Janeiro"], "entity_types": ["location"]}
{"sentence": "Iran, Iraq, and Kuwait are key members of OPEC.", "entity_names": ["Iran", "Iraq", "Kuwait", "OPEC"], "entity_types": ["location", "location", "location", "organization"]}
{"sentence": "Xi Jinping calls for increased international cooperation in addressing climate change", "entity_names": ["Xi Jinping"], "entity_types": ["person"]}
{"sentence": "Global leaders gather in New York for United Nations General Assembly", "entity_names": ["New York", "United Nations General Assembly"], "entity_types": ["location", "organization"]}
{"sentence": "Government announces new measures to boost economy and tackle inflation, says Xi Jinping", "entity_names": ["Xi Jinping"], "entity_types": ["person"]}
{"sentence": "Instagram introduces new feature to help users combat cyberbullying.", "entity_names": ["Instagram"], "entity_types": ["organization"]}
{"sentence": "The European Space Agency plans to launch a new mission to study exoplanets.", "entity_names": ["European Space Agency"], "entity_types": ["organization"]}
{"sentence": "Washington D.C. mayor announces new initiative to improve public transportation system.", "entity_names": ["Washington D.C."], "entity_types": ["location"]}
{"sentence": "Cairo to implement new public transportation system.", "entity_names": ["Cairo"], "entity_types": ["location"]}
{"sentence": "Former New York City Mayor David Dinkins passes away at 93.", "entity_names": ["New York City", "David Dinkins"], "entity_types": ["location", "person"]}
{"sentence": "UNESCO and WHO collaborate on global health and education initiative.", "entity_names": ["UNESCO", "WHO"], "entity_types": ["organization", "organization"]}
{"sentence": "Greenpeace activists protest in Caracas over environmental issues.", "entity_names": ["Greenpeace", "Caracas"], "entity_types": ["organization", "location"]}
{"sentence": "Caracas mayor announces new public transportation initiative.", "entity_names": ["Caracas"], "entity_types": ["location"]}
{"sentence": "Greenpeace launches campaign against air pollution in major cities.", "entity_names": ["Greenpeace"], "entity_types": ["organization"]}
{"sentence": "Cairo experiences record-breaking heat wave in July.", "entity_names": ["Cairo"], "entity_types": ["location"]}
{"sentence": "Dallas-based company announces merger with international firm.", "entity_names": ["Dallas"], "entity_types": ["location"]}
{"sentence": "Violent protests erupt in downtown Cairo over economic reforms.", "entity_names": ["Cairo"], "entity_types": ["location"]}
{"sentence": "New York Times reveals investigation into government corruption.", "entity_names": ["New York Times"], "entity_types": ["organization"]}
{"sentence": "Economist John Smith predicts global recession in coming months.", "entity_names": ["John Smith"], "entity_types": ["person"]}
{"sentence": "New York Times investigation uncovers environmental violations by major oil company.", "entity_names": ["New York Times"], "entity_types": ["organization"]}
{"sentence": "Google LLC announces new headquarters in Brussels.", "entity_names": ["Google LLC", "Brussels"], "entity_types": ["organization", "location"]}
{"sentence": "Brussels to host international climate summit.", "entity_names": ["Brussels"], "entity_types": ["location"]}
{"sentence": "Google LLC unveils plans for renewable energy investment.", "entity_names": ["Google LLC"], "entity_types": ["organization"]}
{"sentence": "The American Cancer Society is funding a new research study on potential cancer treatments.", "entity_names": ["American Cancer Society"], "entity_types": ["organization"]}
{"sentence": "The New York Times reported on the increase in cyber attacks targeting government agencies.", "entity_names": ["New York Times"], "entity_types": ["organization"]}
{"sentence": "An American Cancer Society fundraiser in Chicago raised over $100,000 for cancer research.", "entity_names": ["American Cancer Society"], "entity_types": ["organization"]}
{"sentence": "Oxfam International launches emergency appeal for humanitarian aid in war-torn region.", "entity_names": ["Oxfam International"], "entity_types": ["organization"]}
{"sentence": "Renowned economist Dr. Angela Li appointed as the new director of Oxfam International's economic research division.", "entity_names": ["Dr. Angela Li", "Oxfam International"], "entity_types": ["person", "organization"]}
{"sentence": "The annual report from Oxfam International highlights the growing income inequality in developing countries.", "entity_names": ["Oxfam International"], "entity_types": ["organization"]}
{"sentence": "Israel announces new security measures in response to recent attacks in Tel Aviv.", "entity_names": ["Israel", "Tel Aviv"], "entity_types": ["location", "location"]}
{"sentence": "New study shows increasing air pollution levels in Tel Aviv.", "entity_names": ["Tel Aviv"], "entity_types": ["location"]}
{"sentence": "Tel Aviv University receives $10 million donation for new research center.", "entity_names": ["Tel Aviv University"], "entity_types": ["organization"]}
{"sentence": "Montreal Canadiens defeat Toronto Maple Leafs in overtime thriller.", "entity_names": ["Montreal", "Canadiens", "Toronto", "Maple Leafs"], "entity_types": ["location", "organization", "location", "organization"]}
{"sentence": "Prince William visits wildfire-ravaged communities in Western Canada.", "entity_names": ["Prince William", "Western Canada"], "entity_types": ["person", "location"]}
{"sentence": "Lisbon hosts international climate change conference.", "entity_names": ["Lisbon"], "entity_types": ["location"]}
{"sentence": "South Korea reports record high temperatures as heatwave hits Seoul.", "entity_names": ["South Korea", "Seoul"], "entity_types": ["location", "location"]}
{"sentence": "Seoul Mayor announces new initiative to reduce air pollution in the city.", "entity_names": ["Seoul"], "entity_types": ["location"]}
{"sentence": "Major electronics company headquartered in Seoul sees 15% increase in profits.", "entity_names": ["Seoul"], "entity_types": ["location"]}
{"sentence": "Justice Stephen Breyer announces retirement from the Supreme Court.", "entity_names": ["Stephen Breyer", "Supreme Court"], "entity_types": ["person", "organization"]}
{"sentence": "New York City declares state of emergency due to rising COVID-19 cases.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Elon Musk's SpaceX launches another batch of Starlink satellites into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "Cairo, Egypt's capital, experiences record high temperatures.", "entity_names": ["Cairo", "Egypt"], "entity_types": ["location", "location"]}
{"sentence": "International Monetary Fund approves $12 billion loan to Egypt to support economic reform efforts.", "entity_names": ["International Monetary Fund", "Egypt"], "entity_types": ["organization", "location"]}
{"sentence": "Cairo University professor wins prestigious award for research in environmental science.", "entity_names": ["Cairo University"], "entity_types": ["organization"]}
{"sentence": "The United Nations releases report on global poverty and inequality.", "entity_names": ["United Nations"], "entity_types": ["organization"]}
{"sentence": "Elon Musk's SpaceX plans to launch new satellite into orbit next month.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "New York City to invest $10 million in affordable housing initiative.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Elon Musk's SpaceX launches new satellite into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "China's President Xi Jinping visits Russia for trade talks.", "entity_names": ["China", "President Xi Jinping", "Russia"], "entity_types": ["location", "person", "location"]}
{"sentence": "New York City implements new regulations on food delivery services.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Queen Elizabeth attends opening ceremony of new hospital.", "entity_names": ["Queen Elizabeth"], "entity_types": ["person"]}
{"sentence": "Annual meeting of World Health Organization attended by representatives from 194 countries", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}
{"sentence": "Protests in front of Parliament demand resignation of Prime Minister following corruption scandal.", "entity_names": ["Parliament", "Prime Minister"], "entity_types": ["location", "person"]}
{"sentence": "Beauty YouTuber Jeffree Star launches new makeup line.", "entity_names": ["Jeffree Star"], "entity_types": ["person"]}
{"sentence": "Angela Davis gives powerful speech at civil rights event.", "entity_names": ["Angela Davis"], "entity_types": ["person"]}
{"sentence": "Jeffree Star's cosmetics company experiences rapid growth in sales.", "entity_names": ["Jeffree Star"], "entity_types": ["person"]}
{"sentence": "The American Red Cross provides humanitarian aid to flood victims in the Midwest.", "entity_names": ["American Red Cross", "Midwest"], "entity_types": ["organization", "location"]}
{"sentence": "International Organization for Migration assists in relocation efforts for refugees from war-torn regions.", "entity_names": ["International Organization for Migration"], "entity_types": ["organization"]}
{"sentence": "American Red Cross mobilizes volunteers to help with disaster relief efforts in hurricane-affected areas.", "entity_names": ["American Red Cross"], "entity_types": ["organization"]}
{"sentence": "Shakira to headline concert in Atlanta this weekend.", "entity_names": ["Shakira", "Atlanta"], "entity_types": ["person", "location"]}
{"sentence": "The Democratic National Committee releases statement on healthcare legislation.", "entity_names": ["Democratic National Committee"], "entity_types": ["organization"]}
{"sentence": "Atlanta Mayor announces new initiative to improve public transportation.", "entity_names": ["Atlanta"], "entity_types": ["location"]}
{"sentence": "Tesla CEO Elon Musk unveils new electric vehicle model.", "entity_names": ["Tesla", "Elon Musk"], "entity_types": ["organization", "person"]}
{"sentence": "Tropical storm hits Southeast Asia, causing widespread flooding.", "entity_names": ["Southeast Asia"], "entity_types": ["location"]}
{"sentence": "Amazon to acquire popular streaming service for $8 billion.", "entity_names": ["Amazon"], "entity_types": ["organization"]}
{"sentence": "Jeff Goldblum to make guest appearance at Johannesburg film festival.", "entity_names": ["Jeff Goldblum", "Johannesburg"], "entity_types": ["person", "location"]}
{"sentence": "Facebook faces lawsuit over data privacy breach.", "entity_names": ["Facebook"], "entity_types": ["organization"]}
{"sentence": "Johannesburg Mayor renews commitment to solving city's housing crisis.", "entity_names": ["Johannesburg"], "entity_types": ["location"]}
{"sentence": "United Parcel Service announces partnership with Amazon for drone delivery.", "entity_names": ["United Parcel Service", "Amazon"], "entity_types": ["organization", "organization"]}
{"sentence": "Stockholm, Sweden experienced record high temperatures during the heatwave.", "entity_names": ["Stockholm", "Sweden"], "entity_types": ["location", "location"]}
{"sentence": "Mitch McConnell introduces new legislation to address infrastructure issues.", "entity_names": ["Mitch McConnell"], "entity_types": ["person"]}
{"sentence": "Protesters gather outside Mitch McConnell's office in downtown Louisville.", "entity_names": ["Mitch McConnell", "Louisville"], "entity_types": ["person", "location"]}
{"sentence": "Senate Majority Leader Mitch McConnell delivers speech on immigration reform.", "entity_names": ["Mitch McConnell"], "entity_types": ["person"]}
{"sentence": "Mitch McConnell announces new legislation focused on healthcare reform.", "entity_names": ["Mitch McConnell"], "entity_types": ["person"]}
{"sentence": "The stock market experienced a sharp decline following Mitch McConnell's speech.", "entity_names": ["Mitch McConnell"], "entity_types": ["person"]}
{"sentence": "Mitch McConnell meets with leaders from various organizations to discuss economic stimulus plans.", "entity_names": ["Mitch McConnell"], "entity_types": ["person"]}
{"sentence": "Elon Musk's SpaceX successfully launches 60 internet satellites.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "Tokyo Olympics to be held without spectators amid COVID-19 concerns.", "entity_names": ["Tokyo"], "entity_types": ["location"]}
{"sentence": "Apple unveils new line of iPhone models at annual product launch event.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "Nicole Kidman set to star in new thriller film.", "entity_names": ["Nicole Kidman"], "entity_types": ["person"]}
{"sentence": "Microsoft Corporation announces plans to acquire artificial intelligence startup.", "entity_names": ["Microsoft Corporation"], "entity_types": ["organization"]}
{"sentence": "Local woman wins prestigious science award.", "entity_names": [], "entity_types": []}
{"sentence": "Oslo city council introduces new recycling program to reduce waste and promote sustainability.", "entity_names": ["Oslo"], "entity_types": ["location"]}
{"sentence": "Islamabad police arrest suspected kidnapper in ongoing investigation.", "entity_names": ["Islamabad"], "entity_types": ["location"]}
{"sentence": "Islamabad-based organization provides relief aid to flood-affected regions in the country.", "entity_names": ["Islamabad"], "entity_types": ["location"]}
{"sentence": "LeBron James signs multi-year contract with Los Angeles Lakers.", "entity_names": ["LeBron James", "Los Angeles Lakers"], "entity_types": ["person", "organization"]}
{"sentence": "Chevron Corporation to invest $10 billion in renewable energy projects over the next decade.", "entity_names": ["Chevron Corporation"], "entity_types": ["organization"]}
{"sentence": "The annual summit in Davos gathers global leaders to discuss economic and environmental issues.", "entity_names": ["Davos"], "entity_types": ["location"]}
{"sentence": "The International Criminal Police Organization issues a global alert for wanted fugitive.", "entity_names": ["International Criminal Police Organization"], "entity_types": ["organization"]}
{"sentence": "The European Central Bank raises interest rates in response to rising inflation.", "entity_names": ["European Central Bank"], "entity_types": ["organization"]}
{"sentence": "Renowned economist Maria Yellen appointed as the new chair of the International Monetary Fund.", "entity_names": ["Maria Yellen", "International Monetary Fund"], "entity_types": ["person", "organization"]}
{"sentence": "Mitt Romney announces bid for Senate.", "entity_names": ["Mitt Romney"], "entity_types": ["person"]}
{"sentence": "International Criminal Police Organization issues global arrest warrant for notorious drug lord.", "entity_names": ["International Criminal Police Organization"], "entity_types": ["organization"]}
{"sentence": "North Atlantic Treaty Organization responds to increased military activity near Eastern border.", "entity_names": ["North Atlantic Treaty Organization"], "entity_types": ["organization"]}
{"sentence": "Elon Musk's SpaceX launches a new satellite into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "New York City's mayor announces a plan to improve public transportation.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "The World Health Organization warns of a new strain of flu virus.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}
{"sentence": "Tesla announces opening of new electric vehicle factory in Canberra.", "entity_names": ["Tesla", "Canberra"], "entity_types": ["organization", "location"]}
{"sentence": "Canberra experiences record-breaking heatwave as temperatures soar above 40 degrees.", "entity_names": ["Canberra"], "entity_types": ["location"]}
{"sentence": "Investigation reveals that Tesla CEO Elon Musk is under scrutiny for alleged stock manipulation.", "entity_names": ["Tesla", "Elon Musk"], "entity_types": ["organization", "person"]}
{"sentence": "Jennifer Lawrence to star in new Hollywood blockbuster.", "entity_names": ["Jennifer Lawrence"], "entity_types": ["person"]}
{"sentence": "Wikimedia Foundation announces partnership with major tech company.", "entity_names": ["Wikimedia Foundation"], "entity_types": ["organization"]}
{"sentence": "Auckland named top travel destination for 2021.", "entity_names": ["Auckland"], "entity_types": ["location"]}
{"sentence": "Jeffree Star announces new makeup line.", "entity_names": ["Jeffree Star"], "entity_types": ["person"]}
{"sentence": "Sergey Brin steps down as president of Alphabet.", "entity_names": ["Sergey Brin", "Alphabet"], "entity_types": ["person", "organization"]}
{"sentence": "New York City imposes vaccine mandate for indoor activities.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "The International Red Cross and Red Crescent Movement distributes aid to victims of natural disasters around the world.", "entity_names": ["International Red Cross and Red Crescent Movement"], "entity_types": ["organization"]}
{"sentence": "Martin Luther King Jr.'s legacy continues to inspire civil rights activists today.", "entity_names": ["Martin Luther King Jr."], "entity_types": ["person"]}
{"sentence": "United Nations condemns attack on aid convoy in Syria.", "entity_names": ["United Nations", "Syria"], "entity_types": ["organization", "location"]}
{"sentence": "Singapore announces new trade agreement with Japan.", "entity_names": ["Singapore", "Japan"], "entity_types": ["location", "location"]}
{"sentence": "PM Lee Hsien Loong addresses the United Nations General Assembly.", "entity_names": ["PM Lee Hsien Loong", "United Nations General Assembly"], "entity_types": ["person", "organization"]}
{"sentence": "Singapore Airlines wins 'Best Airline' award for the fifth consecutive year.", "entity_names": ["Singapore Airlines"], "entity_types": ["organization"]}
{"sentence": "Recep Tayyip Erdo\u011fan meets with EU officials in Brussels to discuss migration policy.", "entity_names": ["Recep Tayyip Erdo\u011fan", "EU", "Brussels"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Brussels issues new guidelines for vaccine distribution in the region.", "entity_names": ["Brussels"], "entity_types": ["location"]}
{"sentence": "Turkish President Recep Tayyip Erdo\u011fan addresses NATO leaders in Brussels regarding security in the region.", "entity_names": ["Recep Tayyip Erdo\u011fan", "NATO", "Brussels"], "entity_types": ["person", "organization", "location"]}
{"sentence": "The flooding in Jakarta has displaced thousands of residents.", "entity_names": ["Jakarta"], "entity_types": ["location"]}
{"sentence": "President Jokowi announces new economic policies to boost growth in Jakarta.", "entity_names": ["Jokowi", "Jakarta"], "entity_types": ["person", "location"]}
{"sentence": "KFC plans to open 50 new stores in Jakarta by the end of the year.", "entity_names": ["KFC", "Jakarta"], "entity_types": ["organization", "location"]}
{"sentence": "Pope Francis meets with world leaders to discuss climate change.", "entity_names": ["Pope Francis"], "entity_types": ["person"]}
{"sentence": "Facebook announces new policy to combat misinformation on its platform.", "entity_names": ["Facebook"], "entity_types": ["organization"]}
{"sentence": "Pope Francis condemns violence in the Middle East during his visit to Iraq.", "entity_names": ["Pope Francis", "Middle East"], "entity_types": ["person", "location"]}
{"sentence": "Princess Diana exhibit opens at the national museum", "entity_names": ["Princess Diana"], "entity_types": ["person"]}
{"sentence": "New legislation in honor of Princess Diana's charitable work", "entity_names": ["Princess Diana"], "entity_types": ["person"]}
{"sentence": "Memorial service held for Princess Diana in London", "entity_names": ["Princess Diana", "London"], "entity_types": ["person", "location"]}
{"sentence": "National Institutes of Health funds study on mental health effects of social media.", "entity_names": ["National Institutes of Health"], "entity_types": ["organization"]}
{"sentence": "Expert from National Institutes of Health speaks at international conference on infectious diseases.", "entity_names": ["National Institutes of Health"], "entity_types": ["organization"]}
{"sentence": "Researcher at National Institutes of Health discovers potential treatment for rare genetic disorder.", "entity_names": ["National Institutes of Health"], "entity_types": ["organization"]}
{"sentence": "Volodymyr Zelensky, the President of Ukraine, delivers a speech at the United Nations General Assembly.", "entity_names": ["Volodymyr Zelensky", "Ukraine", "United Nations General Assembly"], "entity_types": ["person", "location", "organization"]}
{"sentence": "Reddit announces new policies to combat misinformation and fake news on the platform.", "entity_names": ["Reddit"], "entity_types": ["organization"]}
{"sentence": "Volodymyr Zelensky meets with foreign diplomats to discuss ongoing conflicts in the region.", "entity_names": ["Volodymyr Zelensky"], "entity_types": ["person"]}
{"sentence": "Google announces a new partnership with NASA for quantum computing research.", "entity_names": ["Google", "NASA"], "entity_types": ["organization", "organization"]}
{"sentence": "Quentin Tarantino's new film tops the box office in its opening weekend.", "entity_names": ["Quentin Tarantino"], "entity_types": ["person"]}
{"sentence": "Riyadh to host international summit on climate change next month.", "entity_names": ["Riyadh"], "entity_types": ["location"]}
{"sentence": "Prince William visits Budapest for royal tour", "entity_names": ["Prince William", "Budapest"], "entity_types": ["person", "location"]}
{"sentence": "Budapest Symphony Orchestra to perform at Royal Albert Hall", "entity_names": ["Budapest Symphony Orchestra", "Royal Albert Hall"], "entity_types": ["organization", "location"]}
{"sentence": "New exhibit at Budapest Museum showcases Prince William's personal art collection", "entity_names": ["Budapest Museum", "Prince William"], "entity_types": ["organization", "person"]}
{"sentence": "Mark Zuckerberg donates $10 million to COVID-19 relief efforts.", "entity_names": ["Mark Zuckerberg"], "entity_types": ["person"]}
{"sentence": "Facebook CEO Mark Zuckerberg testifies before Congress on data privacy.", "entity_names": ["Facebook", "Mark Zuckerberg"], "entity_types": ["organization", "person"]}
{"sentence": "Mark Zuckerberg's new charity initiative aims to combat climate change.", "entity_names": ["Mark Zuckerberg"], "entity_types": ["person"]}
{"sentence": "Los Angeles mayor announces new housing initiative.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}
{"sentence": "Prague's historic castle reopens to tourists.", "entity_names": ["Prague"], "entity_types": ["location"]}
{"sentence": "Edinburgh University research team makes breakthrough in cancer treatment.", "entity_names": ["Edinburgh", "University"], "entity_types": ["location", "organization"]}
{"sentence": "Tony Robbins to host motivational seminar in New York City.", "entity_names": ["Tony Robbins", "New York City"], "entity_types": ["person", "location"]}
{"sentence": "Malala Yousafzai wins Nobel Peace Prize for her activism in education.", "entity_names": ["Malala Yousafzai", "Nobel Peace Prize"], "entity_types": ["person", "organization"]}
{"sentence": "New study shows link between mental health and social media usage.", "entity_names": [], "entity_types": []}
{"sentence": "The CEO of Apple Inc. announced a new product launch event next month.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Tensions rise between Russia and Ukraine over border disputes.", "entity_names": ["Russia", "Ukraine"], "entity_types": ["location", "location"]}
{"sentence": "Renowned chef Gordon Ramsay opens a new restaurant in New York City.", "entity_names": ["Gordon Ramsay", "New York City"], "entity_types": ["person", "location"]}
{"sentence": "Ford Motor Company announces plans to invest $1.2 billion in electric vehicle production in Michigan.", "entity_names": ["Ford Motor Company", "Michigan"], "entity_types": ["organization", "location"]}
{"sentence": "Andr\u00e9s Manuel L\u00f3pez Obrador signs new trade agreement with European Union.", "entity_names": ["Andr\u00e9s Manuel L\u00f3pez Obrador", "European Union"], "entity_types": ["person", "organization"]}
{"sentence": "New York police department launches investigation into cyber attack on local businesses.", "entity_names": ["New York"], "entity_types": ["location"]}
{"sentence": "Floods in Jakarta displace thousands of residents.", "entity_names": ["Jakarta"], "entity_types": ["location"]}
{"sentence": "Jakarta governor announces new public transportation plan.", "entity_names": ["Jakarta"], "entity_types": ["location"]}
{"sentence": "Protests erupt in Jakarta over government corruption scandal.", "entity_names": ["Jakarta"], "entity_types": ["location"]}
{"sentence": "President Biden signs new climate change legislation into law.", "entity_names": ["President Biden"], "entity_types": ["person"]}
{"sentence": "Earthquake hits Tokyo, causing minor damage to buildings.", "entity_names": ["Tokyo"], "entity_types": ["location"]}
{"sentence": "Apple Inc. announces launch date for new iPhone model.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Toronto mayor announces new public transportation initiative.", "entity_names": ["Toronto", "mayor"], "entity_types": ["location", "person"]}
{"sentence": "Local Toronto business receives award for innovation in sustainability.", "entity_names": ["Toronto"], "entity_types": ["location"]}
{"sentence": "Toronto Raptors defeat Golden State Warriors in NBA finals.", "entity_names": ["Toronto", "Raptors", "Golden State Warriors"], "entity_types": ["location", "organization", "organization"]}
{"sentence": "McDonald's Corporation reports record sales for third quarter.", "entity_names": ["McDonald's Corporation"], "entity_types": ["organization"]}
{"sentence": "Protests erupt in front of McDonald's Corporation headquarters.", "entity_names": ["McDonald's Corporation"], "entity_types": ["organization"]}
{"sentence": "CEO of McDonald's Corporation steps down amid financial scandal.", "entity_names": ["CEO", "McDonald's Corporation"], "entity_types": ["person", "organization"]}
{"sentence": "Jair Bolsonaro travels to the United States to meet with President Trump.", "entity_names": ["Jair Bolsonaro", "United States", "President Trump"], "entity_types": ["person", "location", "person"]}
{"sentence": "The controversial policies of President Jair Bolsonaro face backlash from environmental organizations.", "entity_names": ["Jair Bolsonaro"], "entity_types": ["person"]}
{"sentence": "Jair Bolsonaro's economic reforms spark protests across Brazil.", "entity_names": ["Jair Bolsonaro", "Brazil"], "entity_types": ["person", "location"]}
{"sentence": "Tesla announces plans to build a new manufacturing plant in Texas.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "Elon Musk reveals plans for SpaceX's Mars mission.", "entity_names": ["Elon Musk", "SpaceX", "Mars"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Kourtney Kardashian launches new line of skincare products.", "entity_names": ["Kourtney Kardashian"], "entity_types": ["person"]}
{"sentence": "Cape Town experiences record-breaking heatwave.", "entity_names": ["Cape Town"], "entity_types": ["location"]}
{"sentence": "Russian Foreign Minister Sergey Lavrov visits China for bilateral talks.", "entity_names": ["Sergey Lavrov", "China"], "entity_types": ["person", "location"]}
{"sentence": "European Union imposes sanctions on Belarusian officials, prompting strong response from Sergey Lavrov.", "entity_names": ["European Union", "Sergey Lavrov"], "entity_types": ["organization", "person"]}
{"sentence": "United Nations summit in New York sees keynote address by Sergey Lavrov on global security.", "entity_names": ["United Nations", "New York", "Sergey Lavrov"], "entity_types": ["organization", "location", "person"]}
{"sentence": "Marjorie Taylor Greene introduces bill to ban vaccine mandates.", "entity_names": ["Marjorie Taylor Greene"], "entity_types": ["person"]}
{"sentence": "New York City Mayor meets with Marjorie Taylor Greene to discuss infrastructure funding.", "entity_names": ["New York City", "Marjorie Taylor Greene"], "entity_types": ["location", "person"]}
{"sentence": "Marjorie Taylor Greene's controversial comments spark debate in Congress.", "entity_names": ["Marjorie Taylor Greene"], "entity_types": ["person"]}
{"sentence": "Elon Musk's SpaceX launches satellite into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "Parisians protest against new labor law reform.", "entity_names": [], "entity_types": []}
{"sentence": "Dwayne Johnson to star in upcoming action film.", "entity_names": ["Dwayne Johnson"], "entity_types": ["person"]}
{"sentence": "New York City Marathon route to include iconic landmarks.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Zlatan Ibrahimovic signs a new contract with AC Milan.", "entity_names": ["Zlatan Ibrahimovic", "AC Milan"], "entity_types": ["person", "organization"]}
{"sentence": "Fans protest outside of the headquarters of the European Super League.", "entity_names": ["European Super League"], "entity_types": ["organization"]}
{"sentence": "Zlatan Ibrahimovic scores a hat-trick against Juventus.", "entity_names": ["Zlatan Ibrahimovic", "Juventus"], "entity_types": ["person", "organization"]}
{"sentence": "Lionel Messi transferred to Paris Saint-Germain for a record fee.", "entity_names": ["Lionel Messi", "Paris Saint-Germain"], "entity_types": ["person", "organization"]}
{"sentence": "Real Madrid defeats Barcelona in a thrilling El Clasico match.", "entity_names": ["Real Madrid", "Barcelona"], "entity_types": ["organization", "location"]}
{"sentence": "FC Barcelona signs sponsorship deal with a global technology company.", "entity_names": ["FC Barcelona"], "entity_types": ["organization"]}
{"sentence": "Stephen Hawking receives prestigious scientific award.", "entity_names": ["Stephen Hawking"], "entity_types": ["person"]}
{"sentence": "Microsoft Corporation announces plan to acquire small tech startup.", "entity_names": ["Microsoft Corporation"], "entity_types": ["organization"]}
{"sentence": "The renowned physicist Stephen Hawking delivers keynote speech at international conference.", "entity_names": ["Stephen Hawking"], "entity_types": ["person"]}
{"sentence": "The singer Rosal\u00eda wins three Latin Grammy Awards.", "entity_names": ["Rosal\u00eda"], "entity_types": ["person"]}
{"sentence": "Spain's Rosal\u00eda tops the charts with her latest album.", "entity_names": ["Spain", "Rosal\u00eda"], "entity_types": ["location", "person"]}
{"sentence": "Rosal\u00eda collaborates with major fashion brand for new clothing line.", "entity_names": ["Rosal\u00eda"], "entity_types": ["person"]}
{"sentence": "Japan's Tokyo Olympics officially kick off.", "entity_names": ["Japan", "Tokyo", "Olympics"], "entity_types": ["location", "location", "organization"]}
{"sentence": "Supreme Court Justice Ruth Bader Ginsburg undergoes surgery.", "entity_names": ["Ruth Bader Ginsburg"], "entity_types": ["person"]}
{"sentence": "The memorial for Ruth Bader Ginsburg draws thousands of mourners in Washington D.C.", "entity_names": ["Ruth Bader Ginsburg", "Washington D.C."], "entity_types": ["person", "location"]}
{"sentence": "Senate confirms Amy Coney Barrett to succeed Ruth Bader Ginsburg on the Supreme Court.", "entity_names": ["Senate", "Amy Coney Barrett", "Ruth Bader Ginsburg", "Supreme Court"], "entity_types": ["organization", "person", "person", "organization"]}
{"sentence": "Fire breaks out in downtown building, 5 injured.", "entity_names": [], "entity_types": []}
{"sentence": "Apple releases new iPhone with advanced camera technology.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "United States Department of Defense announces a new initiative to improve cybersecurity in military organizations.", "entity_names": ["United States Department of Defense"], "entity_types": ["organization"]}
{"sentence": "Protesters gather outside the United States Department of Defense headquarters in Arlington, Virginia.", "entity_names": ["United States Department of Defense", "Arlington", "Virginia"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Former United States Department of Defense official to testify in congressional hearing on military spending.", "entity_names": ["United States Department of Defense"], "entity_types": ["organization"]}
{"sentence": "The American Civil Liberties Union is filing a lawsuit against the state government over the new voting laws.", "entity_names": ["American Civil Liberties Union"], "entity_types": ["organization"]}
{"sentence": "Protesters gathered outside the courthouse to support the American Civil Liberties Union's case.", "entity_names": ["American Civil Liberties Union"], "entity_types": ["organization"]}
{"sentence": "The American Civil Liberties Union is calling for an investigation into police brutality in the city.", "entity_names": ["American Civil Liberties Union"], "entity_types": ["organization"]}
{"sentence": "Protests in Cairo escalate as government crackdown intensifies.", "entity_names": ["Cairo"], "entity_types": ["location"]}
{"sentence": "Ankara mayor announces plans for new public transportation initiative.", "entity_names": ["Ankara"], "entity_types": ["location"]}
{"sentence": "Queen Elizabeth II meets with leaders at the United Nations General Assembly.", "entity_names": ["Queen Elizabeth II", "United Nations General Assembly"], "entity_types": ["person", "organization"]}
{"sentence": "New COVID-19 variant found in South Africa raises concerns about vaccine effectiveness.", "entity_names": ["South Africa"], "entity_types": ["location"]}
{"sentence": "Investigation reveals Apple's involvement in illegal price-fixing scheme.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "Elon Musk unveils plans for Mars colonization.", "entity_names": ["Elon Musk", "Mars"], "entity_types": ["person", "location"]}
{"sentence": "UK Prime Minister meets with German Chancellor to discuss trade agreements.", "entity_names": ["UK Prime Minister", "German Chancellor"], "entity_types": ["person", "person"]}
{"sentence": "Supreme Court Justice John Roberts to deliver commencement address at Harvard University.", "entity_names": ["John Roberts", "Harvard University"], "entity_types": ["person", "organization"]}
{"sentence": "Tropical storm expected to make landfall in New Orleans over the weekend, causing potential flooding and power outages.", "entity_names": ["New Orleans"], "entity_types": ["location"]}
{"sentence": "Amazon CEO, Jeff Bezos, steps down from his position, handing over the reins to Andy Jassy.", "entity_names": ["Amazon", "Jeff Bezos", "Andy Jassy"], "entity_types": ["organization", "person", "person"]}
{"sentence": "The president of Turkey held a press conference in Ankara.", "entity_names": ["president of Turkey", "Ankara"], "entity_types": ["organization", "location"]}
{"sentence": "Ankara sees record high temperatures as heatwave hits the city.", "entity_names": ["Ankara"], "entity_types": ["location"]}
{"sentence": "The CEO of a major tech company in Ankara announced a new product launch.", "entity_names": ["CEO", "tech company", "Ankara"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Robert Downey Jr. to star in new action thriller film.", "entity_names": ["Robert Downey Jr."], "entity_types": ["person"]}
{"sentence": "Alexei Navalny urges supporters to protest against government crackdown.", "entity_names": ["Alexei Navalny"], "entity_types": ["person"]}
{"sentence": "Queen Elizabeth celebrates 70th anniversary of her accession to the throne.", "entity_names": ["Queen Elizabeth"], "entity_types": ["person"]}
{"sentence": "Prime Minister Johnson visits Reykjavik for diplomatic talks.", "entity_names": ["Prime Minister Johnson", "Reykjavik"], "entity_types": ["person", "location"]}
{"sentence": "Record-breaking snowfall in Reykjavik causes travel disruptions.", "entity_names": ["Reykjavik"], "entity_types": ["location"]}
{"sentence": "Tesla announces plans to open new manufacturing plant in Texas.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "Dr. Anthony Fauci warns of potential winter surge in COVID-19 cases.", "entity_names": ["Dr. Anthony Fauci"], "entity_types": ["person"]}
{"sentence": "European Union imposes sanctions on Belarus over election crackdown.", "entity_names": ["European Union", "Belarus"], "entity_types": ["organization", "location"]}
{"sentence": "Tokyo to host 2021 Summer Olympics.", "entity_names": ["Tokyo"], "entity_types": ["location"]}
{"sentence": "Apple Inc. launches new iPhone model.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "The nuclear negotiations in Tehran have reached a critical juncture.", "entity_names": ["Tehran"], "entity_types": ["location"]}
{"sentence": "Apple Inc. announces plans to open a new research and development center in Tehran.", "entity_names": ["Apple Inc.", "Tehran"], "entity_types": ["organization", "location"]}
{"sentence": "The famous writer J.K. Rowling is set to visit Tehran for a book signing event.", "entity_names": ["J.K. Rowling", "Tehran"], "entity_types": ["person", "location"]}
{"sentence": "United Parcel Service announces plan to expand delivery services in rural areas.", "entity_names": ["United Parcel Service"], "entity_types": ["organization"]}
{"sentence": "Samsung unveils new line of smartphones with advanced camera technology.", "entity_names": ["Samsung"], "entity_types": ["organization"]}
{"sentence": "Save the Children organization raises $1 million for humanitarian aid efforts.", "entity_names": ["Save the Children"], "entity_types": ["organization"]}
{"sentence": "Apple Inc. announces a new iPhone with advanced facial recognition technology.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "National Aeronautics and Space Administration plans to launch a new Mars rover mission next year.", "entity_names": ["National Aeronautics and Space Administration"], "entity_types": ["organization"]}
{"sentence": "Kim Jong Un meets with South Korean president for historic peace talks.", "entity_names": ["Kim Jong Un", "South Korean"], "entity_types": ["person", "location"]}
{"sentence": "Los Angeles prepares for a surge in COVID-19 cases as holiday travel increases.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}
{"sentence": "The International Atomic Energy Agency reports a breach in Iran's nuclear program.", "entity_names": ["International Atomic Energy Agency", "Iran"], "entity_types": ["organization", "location"]}
{"sentence": "The National Broadcasting Company announces new streaming service with exclusive original content.", "entity_names": ["National Broadcasting Company"], "entity_types": ["organization"]}
{"sentence": "Benjamin Netanyahu reelected as Israeli Prime Minister.", "entity_names": ["Benjamin Netanyahu", "Israeli"], "entity_types": ["person", "location"]}
{"sentence": "Scarlett Johansson to star in upcoming thriller film.", "entity_names": ["Scarlett Johansson"], "entity_types": ["person"]}
{"sentence": "New York City unveils plan to improve public transportation infrastructure.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Reddit announces new feature to combat misinformation on the platform.", "entity_names": ["Reddit"], "entity_types": ["organization"]}
{"sentence": "Users on Reddit debate the impact of climate change on global agriculture.", "entity_names": ["Reddit"], "entity_types": ["organization"]}
{"sentence": "Reddit CEO discusses plans for expansion into new international markets.", "entity_names": ["Reddit", "CEO"], "entity_types": ["organization", "person"]}
{"sentence": "The President of Iceland travels to Washington D.C. for official visit.", "entity_names": ["Washington D.C."], "entity_types": ["location"]}
{"sentence": "Renowned chef from Reykjavik opens a new restaurant in New York City.", "entity_names": ["Reykjavik", "New York City"], "entity_types": ["location", "location"]}
{"sentence": "SpaceX, founded by Elon Musk, plans to launch a mission to Mars next year.", "entity_names": ["SpaceX", "Elon Musk", "Mars"], "entity_types": ["organization", "person", "location"]}
{"sentence": "Bloomberg L.P. acquires Tel Aviv-based software company.", "entity_names": ["Bloomberg L.P.", "Tel Aviv"], "entity_types": ["organization", "location"]}
{"sentence": "Tel Aviv to host international technology conference next month.", "entity_names": ["Tel Aviv"], "entity_types": ["location"]}
{"sentence": "Investors speculate on potential merger between two major Tel Aviv banks.", "entity_names": ["Tel Aviv"], "entity_types": ["location"]}
{"sentence": "Stephen Curry scores 45 points in the Warriors' victory over the Lakers.", "entity_names": ["Stephen Curry"], "entity_types": ["person"]}
{"sentence": "Senator Joe Manchin backs infrastructure bill with bipartisan support.", "entity_names": ["Joe Manchin"], "entity_types": ["person"]}
{"sentence": "Golden State Warriors face off against the Brooklyn Nets in the NBA Finals.", "entity_names": ["Golden State Warriors", "Brooklyn Nets"], "entity_types": ["organization", "organization"]}
{"sentence": "Selena Gomez releases new single \"Rare.\"", "entity_names": ["Selena Gomez"], "entity_types": ["person"]}
{"sentence": "Queen Elizabeth celebrates her 95th birthday with family and close friends.", "entity_names": ["Queen Elizabeth"], "entity_types": ["person"]}
{"sentence": "Breaking news: Selena Gomez announces upcoming world tour.", "entity_names": ["Selena Gomez"], "entity_types": ["person"]}
{"sentence": "The CEO of Microsoft unveils new product at tech conference.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}
{"sentence": "The Democratic National Committee announces new fundraising campaign for the upcoming election.", "entity_names": ["Democratic National Committee"], "entity_types": ["organization"]}
{"sentence": "Thousands gather in New York City for annual marathon.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Prince William attends charity event in London.", "entity_names": ["Prince William", "London"], "entity_types": ["person", "location"]}
{"sentence": "New study shows decline in air pollution in major cities, including London and New York.", "entity_names": ["London", "New York"], "entity_types": ["location", "location"]}
{"sentence": "Prince William and Kate Middleton visit wildlife conservation center in Africa.", "entity_names": ["Prince William", "Kate Middleton", "Africa"], "entity_types": ["person", "person", "location"]}
{"sentence": "Construction begins on new skyscraper in Abu Dhabi.", "entity_names": ["Abu Dhabi"], "entity_types": ["location"]}
{"sentence": "Dublin-based tech company announces record profits for Q3.", "entity_names": ["Dublin"], "entity_types": ["location"]}
{"sentence": "Abu Dhabi ruler unveils plan for sustainable development in the city.", "entity_names": ["Abu Dhabi"], "entity_types": ["location"]}
{"sentence": "Tesla's CEO Elon Musk is set to unveil the company's latest electric vehicle model at a press conference next week.", "entity_names": ["Tesla", "Elon Musk"], "entity_types": ["organization", "person"]}
{"sentence": "The United Nations announced plans to send humanitarian aid to the war-torn region of Yemen.", "entity_names": ["United Nations", "Yemen"], "entity_types": ["organization", "location"]}
{"sentence": "Amazon's new headquarters in Arlington, Virginia, is expected to create thousands of job opportunities for local residents.", "entity_names": ["Amazon", "Arlington", "Virginia"], "entity_types": ["organization", "location", "location"]}
{"sentence": "The International Labour Organization reports an increase in child labor in Southeast Asia.", "entity_names": ["International Labour Organization", "Southeast Asia"], "entity_types": ["organization", "location"]}
{"sentence": "Boeing Company CEO resigns amidst controversy over safety concerns.", "entity_names": ["Boeing Company"], "entity_types": ["organization"]}
{"sentence": "Boston Marathon postponed due to pandemic concerns.", "entity_names": ["Boston"], "entity_types": ["location"]}
{"sentence": "Islamabad court sentences former PM to prison for corruption charges.", "entity_names": ["Islamabad"], "entity_types": ["location"]}
{"sentence": "New organization formed in Boston to advocate for climate change policy.", "entity_names": ["Boston"], "entity_types": ["location"]}
{"sentence": "Amazon to open new distribution center in Dallas.", "entity_names": ["Amazon", "Dallas"], "entity_types": ["organization", "location"]}
{"sentence": "E-commerce giant Amazon announces plans to hire 75,000 new employees.", "entity_names": ["Amazon"], "entity_types": ["organization"]}
{"sentence": "Seattle-based technology company Amazon predicts record-breaking holiday sales.", "entity_names": ["Seattle", "Amazon"], "entity_types": ["location", "organization"]}
{"sentence": "Tim Cook announces Apple's new product launch event in Berlin.", "entity_names": ["Tim Cook", "Apple", "Berlin"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Berlin reports a surge in tourism after the easing of travel restrictions in Europe.", "entity_names": ["Berlin", "Europe"], "entity_types": ["location", "location"]}
{"sentence": "Tim Cook, CEO of Apple, visits Berlin to discuss potential collaborations with local tech startups.", "entity_names": ["Tim Cook", "Apple", "Berlin"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Arianna Huffington launches new wellness startup.", "entity_names": ["Arianna Huffington"], "entity_types": ["person"]}
{"sentence": "Arianna Huffington steps down as CEO of her media company.", "entity_names": ["Arianna Huffington"], "entity_types": ["person"]}
{"sentence": "Arianna Huffington's book tops the bestseller list.", "entity_names": ["Arianna Huffington"], "entity_types": ["person"]}
{"sentence": "LeBron James signs a 4-year, $154 million contract with the Los Angeles Lakers.", "entity_names": ["LeBron James", "Los Angeles Lakers"], "entity_types": ["person", "organization"]}
{"sentence": "The Red Cross provides aid to victims of the recent hurricane in the Bahamas.", "entity_names": ["Red Cross", "Bahamas"], "entity_types": ["organization", "location"]}
{"sentence": "Warsaw hosts international climate conference to address global warming issues.", "entity_names": ["Warsaw"], "entity_types": ["location"]}
{"sentence": "Vienna ranked as the most liveable city in the world for the 11th year in a row.", "entity_names": ["Vienna"], "entity_types": ["location"]}
{"sentence": "Reese Witherspoon to star in new romantic comedy film.", "entity_names": ["Reese Witherspoon"], "entity_types": ["person"]}
{"sentence": "The United Nations Organization is set to hold a conference in Vienna next month.", "entity_names": ["United Nations Organization", "Vienna"], "entity_types": ["organization", "location"]}
{"sentence": "Microsoft faces investigation by Securities and Exchange Commission over accounting practices.", "entity_names": ["Microsoft", "Securities and Exchange Commission"], "entity_types": ["organization", "organization"]}
{"sentence": "Former CEO of Microsoft to speak at technology conference in California.", "entity_names": ["Microsoft", "California"], "entity_types": ["organization", "location"]}
{"sentence": "Securities and Exchange Commission announces new regulations for financial sector.", "entity_names": ["Securities and Exchange Commission"], "entity_types": ["organization"]}
{"sentence": "Cape Town experiences record-breaking heatwave, prompting warnings from the World Meteorological Organization.", "entity_names": ["Cape Town", "World Meteorological Organization"], "entity_types": ["location", "organization"]}
{"sentence": "The World Meteorological Organization reports an increase in extreme weather events across the globe.", "entity_names": ["World Meteorological Organization"], "entity_types": ["organization"]}
{"sentence": "Cape Town mayor announces new initiative to combat water scarcity in the region.", "entity_names": ["Cape Town"], "entity_types": ["location"]}
{"sentence": "Earthquake hits Santiago, causing major damage to buildings and infrastructure.", "entity_names": ["Santiago"], "entity_types": ["location"]}
{"sentence": "Santiago mayor announces new initiative to improve public transportation in the city.", "entity_names": ["Santiago"], "entity_types": ["location"]}
{"sentence": "Santiago residents protest government policies, demanding reforms to address economic inequality.", "entity_names": ["Santiago"], "entity_types": ["location"]}
{"sentence": "Thousands march in protest against government corruption in Montreal .", "entity_names": ["Montreal"], "entity_types": ["location"]}
{"sentence": "New study reveals alarming levels of pollution in rivers near Montreal .", "entity_names": ["Montreal"], "entity_types": ["location"]}
{"sentence": "Local Montreal company awarded contract for infrastructure development.", "entity_names": ["Montreal", "company"], "entity_types": ["location", "organization"]}
{"sentence": "Senator Marco Rubio introduces bill to strengthen cybersecurity.", "entity_names": ["Marco Rubio"], "entity_types": ["person"]}
{"sentence": "The trade agreement between the United States and Mexico was supported by Marco Rubio.", "entity_names": ["United States", "Mexico", "Marco Rubio"], "entity_types": ["location", "location", "person"]}
{"sentence": "Marco Rubio visits small businesses in Florida to discuss economic recovery.", "entity_names": ["Marco Rubio", "Florida"], "entity_types": ["person", "location"]}
{"sentence": "Microsoft announces plans to build a new data center in Geneva.", "entity_names": ["Microsoft", "Geneva"], "entity_types": ["organization", "location"]}
{"sentence": "Geneva to host international conference on climate change.", "entity_names": ["Geneva"], "entity_types": ["location"]}
{"sentence": "Microsoft CEO Satya Nadella gives keynote address at developer conference.", "entity_names": ["Microsoft", "Satya Nadella"], "entity_types": ["organization", "person"]}
{"sentence": "Elon Musk's SpaceX launches its new satellite into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "The European Union implements new trade tariffs on American products.", "entity_names": ["European Union", "American"], "entity_types": ["organization", "location"]}
{"sentence": "Dr. Anthony Fauci advises caution as COVID-19 cases rise in several states.", "entity_names": ["Anthony Fauci"], "entity_types": ["person"]}
{"sentence": "Boeing Company to lay off 6,770 US employees.", "entity_names": ["Boeing Company"], "entity_types": ["organization"]}
{"sentence": "George Clooney to star in new thriller film.", "entity_names": ["George Clooney"], "entity_types": ["person"]}
{"sentence": "Joe Biden visits Seattle for campaign rally.", "entity_names": ["Joe Biden", "Seattle"], "entity_types": ["person", "location"]}
{"sentence": "Hong Kong-based company fined $1 million by the Securities and Exchange Commission for insider trading.", "entity_names": ["Hong Kong", "Securities and Exchange Commission"], "entity_types": ["location", "organization"]}
{"sentence": "Pro-democracy activist from Hong Kong arrested for participating in unauthorized assembly.", "entity_names": ["Hong Kong"], "entity_types": ["location"]}
{"sentence": "Major financial institution in Hong Kong announces plans to open new branch in New York.", "entity_names": ["Hong Kong", "New York"], "entity_types": ["location", "location"]}
{"sentence": "Toronto Raptors defeat LA Lakers in NBA Finals Game 3.", "entity_names": ["Toronto Raptors", "LA Lakers"], "entity_types": ["organization", "organization"]}
{"sentence": "Mumbai experiences heavy rainfall causing severe flooding in several areas.", "entity_names": ["Mumbai"], "entity_types": ["location"]}
{"sentence": "Trade talks between Toronto and Mumbai officials gain momentum.", "entity_names": ["Toronto", "Mumbai"], "entity_types": ["location", "location"]}
{"sentence": "Stockholm named as host city for 2026 World Pride event.", "entity_names": ["Stockholm"], "entity_types": ["location"]}
{"sentence": "Istanbul mayor announces new public transportation initiatives.", "entity_names": ["Istanbul"], "entity_types": ["location"]}
{"sentence": "Stockholm-based tech company secures major investment for expansion.", "entity_names": ["Stockholm"], "entity_types": ["location"]}
{"sentence": "Amazon.com has reported a significant increase in online sales amid the COVID-19 pandemic.", "entity_names": ["Amazon.com"], "entity_types": ["organization"]}
{"sentence": "Jennifer Lawrence to star in new sci-fi thriller directed by Denis Villeneuve.", "entity_names": ["Jennifer Lawrence", "Denis Villeneuve"], "entity_types": ["person", "person"]}
{"sentence": "The launch of Amazon.com's new delivery service has sparked competition among e-commerce giants.", "entity_names": ["Amazon.com"], "entity_types": ["organization"]}
{"sentence": "Microsoft Teams surpasses 250 million monthly active users.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}
{"sentence": "The new Microsoft store in New York City will offer exclusive deals on Black Friday.", "entity_names": ["Microsoft", "New York City"], "entity_types": ["organization", "location"]}
{"sentence": "Microsoft CEO Satya Nadella announces plans for expansion into the healthcare industry.", "entity_names": ["Microsoft", "Satya Nadella"], "entity_types": ["organization", "person"]}
{"sentence": "McDonald's Corporation to open 50 new locations in China.", "entity_names": ["McDonald's Corporation", "China"], "entity_types": ["organization", "location"]}
{"sentence": "Renowned chef Gordon Ramsay to open new restaurant in London.", "entity_names": ["Gordon Ramsay", "London"], "entity_types": ["person", "location"]}
{"sentence": "United Parcel Service to open new distribution center in Texas.", "entity_names": ["United Parcel Service", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "Investigation into insider trading at United Parcel Service reveals top executive involvement.", "entity_names": ["United Parcel Service"], "entity_types": ["organization"]}
{"sentence": "Former United Parcel Service CEO appointed as new chairman of board.", "entity_names": ["United Parcel Service", "CEO"], "entity_types": ["organization", "person"]}
{"sentence": "Rosal\u00eda wins Best New Artist at the Latin Grammy Awards.", "entity_names": ["Rosal\u00eda", "Latin Grammy Awards"], "entity_types": ["person", "organization"]}
{"sentence": "Narendra Modi visits United States to discuss trade relations.", "entity_names": ["Narendra Modi", "United States"], "entity_types": ["person", "location"]}
{"sentence": "Apple launches new iPhone with advanced facial recognition technology.", "entity_names": ["Apple", "iPhone"], "entity_types": ["organization", "organization"]}
{"sentence": "Aung San Suu Kyi accepts Nobel Peace Prize in emotional ceremony.", "entity_names": ["Aung San Suu Kyi", "Nobel Peace Prize"], "entity_types": ["person", "organization"]}
{"sentence": "Malala Yousafzai advocates for global education at United Nations.", "entity_names": ["Malala Yousafzai", "United Nations"], "entity_types": ["person", "organization"]}
{"sentence": "Aung San Suu Kyi calls for international aid to address Rohingya refugee crisis.", "entity_names": ["Aung San Suu Kyi", "Rohingya refugee crisis"], "entity_types": ["person", "location"]}
{"sentence": "The United States Agency for International Development has allocated $10 million in aid for the refugee crisis in the region.", "entity_names": ["United States Agency for International Development"], "entity_types": ["organization"]}
{"sentence": "Princess Diana's charity work continues to inspire people around the world, even years after her tragic death.", "entity_names": ["Princess Diana"], "entity_types": ["person"]}
{"sentence": "Doctors Without Borders sends medical teams to provide relief in the aftermath of the natural disaster.", "entity_names": ["Doctors Without Borders"], "entity_types": ["organization"]}
{"sentence": "Doctors Without Borders provide medical aid to remote villages in Africa.", "entity_names": ["Doctors Without Borders", "Africa"], "entity_types": ["organization", "location"]}
{"sentence": "The United Nations and Doctors Without Borders are partnering to help refugees in war-torn regions.", "entity_names": ["United Nations", "Doctors Without Borders"], "entity_types": ["organization", "organization"]}
{"sentence": "Volunteer doctors from Doctors Without Borders offer assistance in the aftermath of natural disasters.", "entity_names": ["Doctors Without Borders"], "entity_types": ["organization"]}
{"sentence": "Nairobi to host international conference on climate change.", "entity_names": ["Nairobi"], "entity_types": ["location"]}
{"sentence": "Madrid-based company announces record profits for the fiscal year.", "entity_names": ["Madrid"], "entity_types": ["location"]}
{"sentence": "Officials from Nairobi and Madrid agree to strengthen trade relations between the two cities.", "entity_names": ["Nairobi", "Madrid"], "entity_types": ["location", "location"]}
{"sentence": "Supreme Court Justice John Roberts delivers the majority opinion in landmark case.", "entity_names": ["Supreme Court", "John Roberts"], "entity_types": ["organization", "person"]}
{"sentence": "New York City Mayor announces plan to tackle homelessness crisis", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Tesla Inc. reports record-breaking profits for the fiscal year.", "entity_names": ["Tesla Inc."], "entity_types": ["organization"]}
{"sentence": "The National Institutes of Health have allocated $100 million for a new research initiative on mental health.", "entity_names": ["National Institutes of Health"], "entity_types": ["organization"]}
{"sentence": "California governor plans to visit the National Institutes of Health next week to discuss medical research funding.", "entity_names": ["California", "National Institutes of Health"], "entity_types": ["location", "organization"]}
{"sentence": "The National Institutes of Health announced a breakthrough in cancer treatment research.", "entity_names": ["National Institutes of Health"], "entity_types": ["organization"]}
{"sentence": "Mike Pence delivers keynote speech at Republican National Convention.", "entity_names": ["Mike Pence"], "entity_types": ["person"]}
{"sentence": "Vice President Mike Pence meets with NATO leaders in Brussels.", "entity_names": ["Mike Pence", "NATO", "Brussels"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Mike Pence tests negative for COVID-19 after potential exposure.", "entity_names": ["Mike Pence", "COVID-19"], "entity_types": ["person", "organization"]}
{"sentence": "Quentin Tarantino announces plans for new film project.", "entity_names": ["Quentin Tarantino"], "entity_types": ["person"]}
{"sentence": "Local theater to feature Quentin Tarantino film festival.", "entity_names": ["Quentin Tarantino"], "entity_types": ["person"]}
{"sentence": "EUROPEAN UNION APPROVES NEW TRADE AGREEMENT WITH CHINA", "entity_names": ["EUROPEAN UNION", "CHINA"], "entity_types": ["organization", "location"]}
{"sentence": "Stockholm experiences record high temperatures for the month of June, reaching 35 degrees Celsius.", "entity_names": ["Stockholm"], "entity_types": ["location"]}
{"sentence": "Renowned chef Gordon Ramsay opens new restaurant in downtown Stockholm.", "entity_names": ["Gordon Ramsay", "Stockholm"], "entity_types": ["person", "location"]}
{"sentence": "Volunteers from Doctors Without Borders are providing medical aid to refugees in war-torn regions.", "entity_names": ["Doctors Without Borders"], "entity_types": ["organization"]}
{"sentence": "The latest outbreak of Ebola in Africa has prompted Doctors Without Borders to deploy medical teams to the affected areas.", "entity_names": ["Ebola", "Doctors Without Borders"], "entity_types": ["location", "organization"]}
{"sentence": "Doctors Without Borders founder receives prestigious humanitarian award for their tireless work in providing medical care to those in need.", "entity_names": ["Doctors Without Borders"], "entity_types": ["organization"]}
{"sentence": "President Biden delivers speech on infrastructure plan in Washington D.C.", "entity_names": ["President Biden", "Washington D.C."], "entity_types": ["person", "location"]}
{"sentence": "European Union imposes sanctions on Belarus over airline incident.", "entity_names": ["European Union", "Belarus"], "entity_types": ["organization", "location"]}
{"sentence": "Senate Majority Leader Mitch McConnell announces plans to push for new stimulus package.", "entity_names": ["Mitch McConnell"], "entity_types": ["person"]}
{"sentence": "Mitch McConnell calls for bipartisan cooperation in passing infrastructure bill.", "entity_names": ["Mitch McConnell"], "entity_types": ["person"]}
{"sentence": "Protesters gather outside Mitch McConnell's office to demand action on climate change.", "entity_names": ["Mitch McConnell"], "entity_types": ["person"]}
{"sentence": "Protests erupt in Caracas as opposition leader is arrested.", "entity_names": ["Caracas"], "entity_types": ["location"]}
{"sentence": "Caracas mayor announces new infrastructure plan for the city.", "entity_names": ["Caracas"], "entity_types": ["location"]}
{"sentence": "Caracas-based oil company reports significant increase in profits.", "entity_names": ["Caracas", "oil company"], "entity_types": ["location", "organization"]}
{"sentence": "Boeing unveils new aircraft design in Dallas.", "entity_names": ["Boeing", "Dallas"], "entity_types": ["organization", "location"]}
{"sentence": "Dallas mayor announces new transportation initiative.", "entity_names": ["Dallas"], "entity_types": ["location"]}
{"sentence": "Boeing employees celebrate record-breaking sales.", "entity_names": ["Boeing"], "entity_types": ["organization"]}
{"sentence": "Tesla Inc. plans to open a new manufacturing plant in Berlin.", "entity_names": ["Tesla Inc.", "Berlin"], "entity_types": ["organization", "location"]}
{"sentence": "The famous singer Adele announced her new album release date.", "entity_names": ["Adele"], "entity_types": ["person"]}
{"sentence": "The United Nations reports a significant increase in refugee numbers in the Middle East.", "entity_names": ["United Nations", "Middle East"], "entity_types": ["organization", "location"]}
{"sentence": "New York Times reports on rising COVID-19 cases in New York City.", "entity_names": ["New York Times", "New York City"], "entity_types": ["organization", "location"]}
{"sentence": "Local residents in Montreal protest against new housing development project.", "entity_names": ["Montreal"], "entity_types": ["location"]}
{"sentence": "Amsterdam to implement new cycling infrastructure to improve safety for cyclists.", "entity_names": ["Amsterdam"], "entity_types": ["location"]}
{"sentence": "Local Amsterdam business owners protest against new tax regulations.", "entity_names": ["Amsterdam"], "entity_types": ["location"]}
{"sentence": "Music festival in Amsterdam canceled due to COVID-19 restrictions.", "entity_names": ["Amsterdam"], "entity_types": ["location"]}
{"sentence": "New Delhi experiences heavy rainfall causing traffic chaos.", "entity_names": ["New Delhi"], "entity_types": ["location"]}
{"sentence": "India's Prime Minister visits New Delhi school to promote education reform.", "entity_names": ["India", "New Delhi"], "entity_types": ["location", "location"]}
{"sentence": "Pollution levels in New Delhi reach hazardous levels, prompting health warnings.", "entity_names": ["New Delhi"], "entity_types": ["location"]}
{"sentence": "Beirut experiences widespread protests after government corruption scandal.", "entity_names": ["Beirut"], "entity_types": ["location"]}
{"sentence": "Athens prepares for upcoming Olympic Games with upgraded facilities and increased security measures.", "entity_names": ["Athens"], "entity_types": ["location"]}
{"sentence": "Local organization in Beirut launches community recycling program to combat pollution.", "entity_names": ["Beirut"], "entity_types": ["location"]}
{"sentence": "Vienna hosts international conference on climate change.", "entity_names": ["Vienna"], "entity_types": ["location"]}
{"sentence": "Ottawa appoints new ambassador to United Nations.", "entity_names": ["Ottawa", "United Nations"], "entity_types": ["location", "organization"]}
{"sentence": "The Vienna Philharmonic Orchestra performs at Ottawa's National Arts Centre.", "entity_names": ["Vienna Philharmonic Orchestra", "Ottawa", "National Arts Centre"], "entity_types": ["organization", "location", "organization"]}
{"sentence": "Reddit announces new features to enhance user experience.", "entity_names": ["Reddit"], "entity_types": ["organization"]}
{"sentence": "Reddit CEO discusses plans for future expansion at tech conference.", "entity_names": ["Reddit", "CEO"], "entity_types": ["organization", "person"]}
{"sentence": "Walmart announces plans to open 20 new stores in the Midwest.", "entity_names": ["Walmart"], "entity_types": ["organization"]}
{"sentence": "Protests erupt outside of Walmart headquarters over labor conditions.", "entity_names": ["Walmart"], "entity_types": ["organization"]}
{"sentence": "Former Walmart CEO appointed to lead government task force on economic recovery.", "entity_names": ["Walmart", "CEO"], "entity_types": ["organization", "person"]}
{"sentence": "NASA's Perseverance rover successfully lands on Mars.", "entity_names": ["NASA", "Mars"], "entity_types": ["organization", "location"]}
{"sentence": "Elon Musk's SpaceX plans to launch a commercial satellite into space next week.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "United Nations reports a humanitarian crisis in war-torn Yemen.", "entity_names": ["United Nations", "Yemen"], "entity_types": ["organization", "location"]}
{"sentence": "Pfizer Inc. announces new COVID-19 vaccine distribution plan.", "entity_names": ["Pfizer Inc."], "entity_types": ["organization"]}
{"sentence": "The Federal Aviation Administration issues new safety guidelines for Boeing 737 MAX planes.", "entity_names": ["Federal Aviation Administration", "Boeing"], "entity_types": ["organization", "organization"]}
{"sentence": "CEO of Pfizer Inc. to step down next month.", "entity_names": ["Pfizer Inc."], "entity_types": ["organization"]}
{"sentence": "Lady Gaga to release new album next month", "entity_names": ["Lady Gaga"], "entity_types": ["person"]}
{"sentence": "Exxon Mobil Corporation to invest 10 million in renewable energy research", "entity_names": ["Exxon Mobil Corporation"], "entity_types": ["organization"]}
{"sentence": "Lady Gaga's new tour dates announced", "entity_names": ["Lady Gaga"], "entity_types": ["person"]}
{"sentence": "The International Organization for Migration helps refugees resettle in new countries.", "entity_names": ["International Organization for Migration"], "entity_types": ["organization"]}
{"sentence": "Two hundred migrants rescued by the International Organization for Migration off the coast of Libya.", "entity_names": ["International Organization for Migration", "Libya"], "entity_types": ["organization", "location"]}
{"sentence": "First Lady Melania Trump visits International Organization for Migration facility in Guatemala.", "entity_names": ["First Lady Melania Trump", "International Organization for Migration", "Guatemala"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Ukrainian President Volodymyr Zelensky visits Dallas for a diplomatic meeting.", "entity_names": ["Volodymyr Zelensky", "Dallas"], "entity_types": ["person", "location"]}
{"sentence": "Dallas-based company announces plans for expansion into international markets.", "entity_names": ["Dallas"], "entity_types": ["location"]}
{"sentence": "Volodymyr Zelensky delivers speech at United Nations headquarters in New York.", "entity_names": ["Volodymyr Zelensky", "United Nations", "New York"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Hong Kong protesters clash with police over extradition bill.", "entity_names": ["Hong Kong"], "entity_types": ["location"]}
{"sentence": "Amazon announces plans to acquire MGM studios for $8.45 billion.", "entity_names": ["Amazon", "MGM"], "entity_types": ["organization", "organization"]}
{"sentence": "Amal Clooney appointed as UN Special Envoy on media freedom.", "entity_names": ["Amal Clooney", "UN"], "entity_types": ["person", "organization"]}
{"sentence": "Earthquake hits Quito, causing widespread damage to buildings and infrastructure.", "entity_names": ["Quito"], "entity_types": ["location"]}
{"sentence": "Local organization in Quito launches campaign to promote recycling and reduce plastic waste.", "entity_names": ["Quito"], "entity_types": ["location"]}
{"sentence": "Nelson Mandela Foundation celebrates the life and legacy of Nelson Mandela on his 100th birthday.", "entity_names": ["Nelson Mandela", "Nelson Mandela"], "entity_types": ["person", "person"]}
{"sentence": "Thousands gather in South Africa to honor Nelson Mandela on the anniversary of his death.", "entity_names": ["South Africa", "Nelson Mandela"], "entity_types": ["location", "person"]}
{"sentence": "President Obama visits Robben Island, where Nelson Mandela spent 18 years as a political prisoner.", "entity_names": ["Obama", "Robben Island", "Nelson Mandela"], "entity_types": ["person", "location", "person"]}
{"sentence": "The World Trade Organization holds virtual meeting to discuss trade policies during the pandemic.", "entity_names": ["The World Trade Organization"], "entity_types": ["organization"]}
{"sentence": "Climate change activist Greta Thunberg addresses the United Nations General Assembly.", "entity_names": ["Greta Thunberg", "United Nations General Assembly"], "entity_types": ["person", "organization"]}
{"sentence": "Canada's Prime Minister Justin Trudeau announces new COVID-19 relief package for small businesses.", "entity_names": ["Canada", "Justin Trudeau"], "entity_types": ["location", "person"]}
{"sentence": "New York City Mayor de Blasio announces new public transportation initiative.", "entity_names": ["New York City", "de Blasio"], "entity_types": ["location", "person"]}
{"sentence": "Apple Inc. unveils new iPhone with advanced camera technology.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Elon Musk's SpaceX launches another successful mission to the International Space Station.", "entity_names": ["Elon Musk", "SpaceX", "International Space Station"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Huge crowds gather in Times Square to celebrate New Year's Eve.", "entity_names": ["Times Square"], "entity_types": ["location"]}
{"sentence": "Toronto Raptors defeat Casablanca Falcons in basketball showdown.", "entity_names": ["Toronto", "Raptors", "Casablanca", "Falcons"], "entity_types": ["location", "organization", "location", "organization"]}
{"sentence": "Police arrest suspect in Toronto bank robbery.", "entity_names": ["Toronto"], "entity_types": ["location"]}
{"sentence": "Casablanca named as host city for international film festival.", "entity_names": ["Casablanca"], "entity_types": ["location"]}
{"sentence": "Bill Gates donates $1 million to Istanbul school.", "entity_names": ["Bill Gates", "Istanbul"], "entity_types": ["person", "location"]}
{"sentence": "Istanbul-based organization raises funds for Syrian refugees.", "entity_names": ["Istanbul"], "entity_types": ["location"]}
{"sentence": "Bill Gates to attend technology conference in Istanbul next month.", "entity_names": ["Bill Gates", "Istanbul"], "entity_types": ["person", "location"]}
{"sentence": "Elon Musk's SpaceX successfully launches a new satellite into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "Paris prepares for a new wave of protests over government policies.", "entity_names": ["Paris"], "entity_types": ["location"]}
{"sentence": "Apple Inc. announces the release of the new iPhone 13.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "The United Nations Educational, Scientific and Cultural Organization announces new global initiative to promote education for all.", "entity_names": ["The United Nations Educational, Scientific and Cultural Organization"], "entity_types": ["organization"]}
{"sentence": "Harvey Weinstein sentenced to 23 years in prison for sexual assault and rape.", "entity_names": ["Harvey Weinstein"], "entity_types": ["person"]}
{"sentence": "Auckland reports record-high temperatures as heatwave continues to sweep through New Zealand.", "entity_names": ["Auckland", "New Zealand"], "entity_types": ["location", "location"]}
{"sentence": "Apple Inc. announces record-breaking profits for the fiscal year.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "The United Nations Security Council convenes emergency meeting on the conflict in the Middle East.", "entity_names": ["United Nations Security Council", "Middle East"], "entity_types": ["organization", "location"]}
{"sentence": "Dr. Anthony Fauci warns of potential surge in COVID-19 cases due to relaxation of safety measures.", "entity_names": ["Dr. Anthony Fauci", "COVID-19"], "entity_types": ["person", "organization"]}
{"sentence": "Al Jazeera announces partnership with BBC to expand global news coverage.", "entity_names": ["Al Jazeera", "BBC"], "entity_types": ["organization", "organization"]}
{"sentence": "The United Nations condemns human rights violations in Myanmar.", "entity_names": ["United Nations", "Myanmar"], "entity_types": ["organization", "location"]}
{"sentence": "Protesters clash with police in downtown Los Angeles.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}
{"sentence": "Tesla announces plans to open new manufacturing facility in Texas.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "President Biden signs executive order to increase federal minimum wage.", "entity_names": ["President Biden"], "entity_types": ["person"]}
{"sentence": "Amazon reports record-breaking quarterly profits, surpassing analyst expectations.", "entity_names": ["Amazon"], "entity_types": ["organization"]}
{"sentence": "World Health Organization declares new strain of flu virus a global health emergency.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}
{"sentence": "India surpasses China as the world's most populous country, according to World Health Organization data.", "entity_names": ["India", "China", "World Health Organization"], "entity_types": ["location", "location", "organization"]}
{"sentence": "Researchers at the National Cancer Institute, working with the World Health Organization, report promising results in cancer treatment trials.", "entity_names": ["National Cancer Institute", "World Health Organization"], "entity_types": ["organization", "organization"]}
{"sentence": "Amazon announces plans to build new headquarters in Arlington, Virginia.", "entity_names": ["Amazon", "Arlington", "Virginia"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Bill Gates foundation donates $10 million to support education in developing countries.", "entity_names": ["Bill Gates"], "entity_types": ["person"]}
{"sentence": "Violent protests erupt in Johannesburg following controversial police shooting.", "entity_names": ["Johannesburg", "police"], "entity_types": ["location", "organization"]}
{"sentence": "New York City announces plan to invest $10 million in public transit improvements.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Famous chef opens new restaurant in downtown Johannesburg.", "entity_names": ["Johannesburg"], "entity_types": ["location"]}
{"sentence": "Montreal to host international film festival next month.", "entity_names": ["Montreal"], "entity_types": ["location"]}
{"sentence": "Riots break out in Johannesburg due to political unrest.", "entity_names": ["Johannesburg"], "entity_types": ["location"]}
{"sentence": "Microsoft announces plans to open new data center in Johannesburg.", "entity_names": ["Microsoft", "Johannesburg"], "entity_types": ["organization", "location"]}
{"sentence": "South African president visits Johannesburg to address issues of poverty and inequality.", "entity_names": ["Johannesburg"], "entity_types": ["location"]}
{"sentence": "Bradley Cooper to star in upcoming movie set in Bangkok.", "entity_names": ["Bradley Cooper", "Bangkok"], "entity_types": ["person", "location"]}
{"sentence": "Bangkok named as host city for 2032 Summer Olympics.", "entity_names": ["Bangkok"], "entity_types": ["location"]}
{"sentence": "New luxury hotel to open in Bangkok next year.", "entity_names": ["Bangkok"], "entity_types": ["location"]}
{"sentence": "Save the Children to launch new education program in Karachi.", "entity_names": ["Save the Children", "Karachi"], "entity_types": ["organization", "location"]}
{"sentence": "National Aeronautics and Space Administration announces new mission to Mars.", "entity_names": ["National Aeronautics and Space Administration", "Mars"], "entity_types": ["organization", "location"]}
{"sentence": "BBC journalist wins prestigious award for investigative reporting.", "entity_names": ["BBC"], "entity_types": ["organization"]}
{"sentence": "New York City Mayor announces new affordable housing initiative.", "entity_names": ["New York City", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "New York City subway system to undergo major renovations next year.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "New York City marathon organizers prepare for upcoming race.", "entity_names": ["New York City", "marathon organizers"], "entity_types": ["location", "organization"]}
{"sentence": "Tel Aviv University researchers discover potential new treatment for Alzheimer's disease.", "entity_names": ["Tel Aviv University"], "entity_types": ["organization"]}
{"sentence": "Record-breaking temperatures in Tel Aviv cause power outages and water shortages.", "entity_names": ["Tel Aviv"], "entity_types": ["location"]}
{"sentence": "Tel Aviv mayor announces new public transportation initiatives to reduce traffic congestion.", "entity_names": ["Tel Aviv", "mayor"], "entity_types": ["location", "person"]}
{"sentence": "The World Meteorological Organization reports record-breaking temperatures in Europe.", "entity_names": ["World Meteorological Organization", "Europe"], "entity_types": ["organization", "location"]}
{"sentence": "New York City Mayor announces plan to invest in renewable energy sources.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "The World Meteorological Organization forecasts extreme weather conditions in the Pacific region.", "entity_names": ["World Meteorological Organization", "Pacific"], "entity_types": ["organization", "location"]}
{"sentence": "The European Union reaches a trade agreement with Canada.", "entity_names": ["European Union", "Canada"], "entity_types": ["organization", "location"]}
{"sentence": "Germany announces plans to implement new environmental regulations, with support from the European Union.", "entity_names": ["Germany", "European Union"], "entity_types": ["location", "organization"]}
{"sentence": "French President Macron meets with leaders of the European Union to discuss economic recovery efforts.", "entity_names": ["French President Macron", "European Union"], "entity_types": ["organization", "organization"]}
{"sentence": "Beijing issues travel advisory for citizens visiting Montreal due to political protests.", "entity_names": ["Beijing", "Montreal"], "entity_types": ["location", "location"]}
{"sentence": "The mayor of Montreal will open the new technology center next week.", "entity_names": ["Montreal"], "entity_types": ["location"]}
{"sentence": "Beijing imposes restrictions on foreign investment in tech sector.", "entity_names": ["Beijing"], "entity_types": ["location"]}
{"sentence": "California governor announces new climate action plan.", "entity_names": ["California", "governor"], "entity_types": ["location", "person"]}
{"sentence": "OPEC announces oil production cut amid global supply concerns.", "entity_names": ["OPEC"], "entity_types": ["organization"]}
{"sentence": "Heavy rain causes flooding in Bras\u00edlia, forcing evacuations.", "entity_names": ["Bras\u00edlia"], "entity_types": ["location"]}
{"sentence": "Investigation reveals corruption scandal involving high-ranking officials in OPEC member countries.", "entity_names": ["OPEC"], "entity_types": ["organization"]}
{"sentence": "The Democratic National Committee issues statement on healthcare reform.", "entity_names": ["Democratic National Committee"], "entity_types": ["organization"]}
{"sentence": "Former Secretary of State to speak at Democratic National Committee fundraiser event.", "entity_names": ["Democratic National Committee"], "entity_types": ["organization"]}
{"sentence": "Controversy erupts over leaked emails from Democratic National Committee officials.", "entity_names": ["Democratic National Committee"], "entity_types": ["organization"]}
{"sentence": "John Roberts sworn in as Supreme Court Chief Justice.", "entity_names": ["John Roberts"], "entity_types": ["person"]}
{"sentence": "Trade negotiations continue between the United States and China, despite recent tensions.", "entity_names": ["United States", "China"], "entity_types": ["location", "location"]}
{"sentence": "Climate change activists protest outside the headquarters of ExxonMobil.", "entity_names": ["ExxonMobil"], "entity_types": ["organization"]}
{"sentence": "Bogot\u00e1's mayor announces new public transportation initiative.", "entity_names": ["Bogot\u00e1"], "entity_types": ["location"]}
{"sentence": "Local Bogot\u00e1 business owners protest new tax laws.", "entity_names": ["Bogot\u00e1"], "entity_types": ["location"]}
{"sentence": "Bogot\u00e1 soccer team advances to national championships.", "entity_names": ["Bogot\u00e1"], "entity_types": ["location"]}
{"sentence": "Budapest to host international film festival next month.", "entity_names": ["Budapest"], "entity_types": ["location"]}
{"sentence": "Renowned chef opens new restaurant in Budapest.", "entity_names": ["Budapest"], "entity_types": ["location"]}
{"sentence": "Budapest Symphony Orchestra to perform at prestigious music festival.", "entity_names": ["Budapest", "Symphony Orchestra"], "entity_types": ["location", "organization"]}
{"sentence": "Tesla announces plans to build new Gigafactory in Texas.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "European Union approves new sanctions against Russia.", "entity_names": ["European Union", "Russia"], "entity_types": ["organization", "location"]}
{"sentence": "Greek authorities have arrested three suspects in the Athens bombing.", "entity_names": ["Greek", "Athens"], "entity_types": ["location", "location"]}
{"sentence": "The mayor of Athens announced a new initiative to improve public transportation.", "entity_names": ["Athens"], "entity_types": ["location"]}
{"sentence": "Investors are closely watching the economic situation in Athens amid the ongoing debt crisis.", "entity_names": ["Athens"], "entity_types": ["location"]}
{"sentence": "Greenpeace protests in Madrid against deforestation.", "entity_names": ["Greenpeace", "Madrid"], "entity_types": ["organization", "location"]}
{"sentence": "Madrid-based organization Greenpeace unveils new climate change campaign.", "entity_names": ["Madrid", "Greenpeace"], "entity_types": ["location", "organization"]}
{"sentence": "Greenpeace activists detained in Madrid after staging oil drilling demonstration.", "entity_names": ["Greenpeace", "Madrid"], "entity_types": ["organization", "location"]}
{"sentence": "The International Criminal Court investigates alleged war crimes in Myanmar.", "entity_names": ["International Criminal Court", "Myanmar"], "entity_types": ["organization", "location"]}
{"sentence": "Protesters gather outside the International Criminal Court to demand justice for human rights violations.", "entity_names": ["International Criminal Court"], "entity_types": ["organization"]}
{"sentence": "U.S. withdraws from the International Criminal Court.", "entity_names": ["U.S.", "International Criminal Court"], "entity_types": ["location", "organization"]}
{"sentence": "Kourtney Kardashian launches new lifestyle brand.", "entity_names": ["Kourtney Kardashian"], "entity_types": ["person"]}
{"sentence": "Kourtney Kardashian spotted with new boyfriend in Paris.", "entity_names": ["Kourtney Kardashian"], "entity_types": ["person"]}
{"sentence": "Kourtney Kardashian's company partners with major fashion retailer.", "entity_names": ["Kourtney Kardashian"], "entity_types": ["person"]}
{"sentence": "Toyota Motor Corporation announces plans to invest $1 billion in a new manufacturing facility in Rome.", "entity_names": ["Toyota Motor Corporation", "Rome"], "entity_types": ["organization", "location"]}
{"sentence": "New York City mayor proposes a new initiative to address homelessness in the city.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Pro-democracy protests continue in Hong Kong as tensions rise with Chinese government.", "entity_names": ["Hong Kong", "Chinese government"], "entity_types": ["location", "organization"]}
{"sentence": "Sydney experiences record-breaking heatwave.", "entity_names": ["Sydney"], "entity_types": ["location"]}
{"sentence": "Sydney native wins gold medal in swimming competition.", "entity_names": ["Sydney"], "entity_types": ["location"]}
{"sentence": "Queen Elizabeth II celebrates her 95th birthday with family.", "entity_names": ["Queen Elizabeth II"], "entity_types": ["person"]}
{"sentence": "Pyongyang fires ballistic missile into Sea of Japan.", "entity_names": ["Pyongyang"], "entity_types": ["location"]}
{"sentence": "United Nations issues statement condemning latest attack in Yemen.", "entity_names": ["United Nations", "Yemen"], "entity_types": ["organization", "location"]}
{"sentence": "Bank of America reports higher-than-expected profits for the second quarter.", "entity_names": ["Bank of America"], "entity_types": ["organization"]}
{"sentence": "Protesters gather outside Bank of America headquarters in Charlotte to demand divestment from fossil fuels.", "entity_names": ["Bank of America", "Charlotte"], "entity_types": ["organization", "location"]}
{"sentence": "Bank of America CEO visits Washington to discuss economic recovery plans with government officials.", "entity_names": ["Bank of America", "Washington"], "entity_types": ["organization", "location"]}
{"sentence": "Apple announces new iPhone launch date.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "President Biden signs new infrastructure bill into law.", "entity_names": ["President Biden"], "entity_types": ["person"]}
{"sentence": "Flooding in India leads to evacuation of thousands.", "entity_names": ["India"], "entity_types": ["location"]}
{"sentence": "Marjorie Taylor Greene introduces new bill to limit funding for National Basketball Association.", "entity_names": ["Marjorie Taylor Greene", "National Basketball Association"], "entity_types": ["person", "organization"]}
{"sentence": "Manila experiences record-breaking heat wave, with temperatures soaring to 40 degrees Celsius.", "entity_names": ["Manila"], "entity_types": ["location"]}
{"sentence": "National Basketball Association announces plans for new team expansion in Mexico City.", "entity_names": ["National Basketball Association", "Mexico City"], "entity_types": ["organization", "location"]}
{"sentence": "Doctors Without Borders sends medical team to assist in earthquake-ravaged region.", "entity_names": ["Doctors Without Borders"], "entity_types": ["organization"]}
{"sentence": "Local community honors Doctors Without Borders volunteers for their humanitarian work in war-torn country.", "entity_names": ["Doctors Without Borders"], "entity_types": ["organization"]}
{"sentence": "Former president of Doctors Without Borders receives Nobel Peace Prize for her dedication to global health initiatives.", "entity_names": ["Doctors Without Borders", "Nobel Peace Prize"], "entity_types": ["organization", "organization"]}
{"sentence": "The European Union imposes sanctions on Russian officials over human rights abuses.", "entity_names": ["European Union"], "entity_types": ["organization"]}
{"sentence": "NATO announces plans to increase presence in Eastern Europe in response to Russian aggression.", "entity_names": ["NATO", "Eastern Europe", "Russian"], "entity_types": ["organization", "location", "location"]}
{"sentence": "European Union leaders discuss trade relations with China at summit in Brussels.", "entity_names": ["European Union", "China", "Brussels"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Shakira announces new world tour.", "entity_names": ["Shakira"], "entity_types": ["person"]}
{"sentence": "Athens experiences record-breaking heatwave.", "entity_names": ["Athens"], "entity_types": ["location"]}
{"sentence": "Greece announces new economic stimulus package to boost growth in Athens.", "entity_names": ["Greece", "Athens"], "entity_types": ["location", "location"]}
{"sentence": "Renowned author to speak at book festival in Athens next week.", "entity_names": ["Athens"], "entity_types": ["location"]}
{"sentence": "Athens Marathon attracts thousands of runners from around the world.", "entity_names": ["Athens"], "entity_types": ["location"]}
{"sentence": "New York City imposes indoor mask mandate as COVID-19 cases surge.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Apple's new iPhone 13 expected to be released in September.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "Tesla announces partnership with Toyota to develop electric vehicle technology.", "entity_names": ["Tesla", "Toyota"], "entity_types": ["organization", "organization"]}
{"sentence": "New York City Mayor holds press conference to address rising crime rates.", "entity_names": ["New York City", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "Scientists discover new species of deep-sea creatures in the Pacific Ocean.", "entity_names": ["Pacific Ocean"], "entity_types": ["location"]}
{"sentence": "Renowned physicist Stephen Hawking delivers a lecture on black holes and the origins of the universe at the University of Cambridge.", "entity_names": ["Stephen Hawking", "University of Cambridge"], "entity_types": ["person", "location"]}
{"sentence": "Stephen Hawking's groundbreaking book 'A Brief History of Time' remains a bestseller, inspiring countless readers with his profound insights into the cosmos.", "entity_names": ["Stephen Hawking"], "entity_types": ["person"]}
{"sentence": "The Stephen Hawking Foundation announces a new research initiative aimed at advancing our understanding of theoretical physics and the nature of time.", "entity_names": ["Stephen Hawking Foundation"], "entity_types": ["organization"]}
{"sentence": "Greenpeace activists protest outside the headquarters of a major oil company.", "entity_names": ["Greenpeace"], "entity_types": ["organization"]}
{"sentence": "TikTok announces new feature to enhance user experience.", "entity_names": ["TikTok"], "entity_types": ["organization"]}
{"sentence": "The CEO of a prominent tech company steps down amid controversy.", "entity_names": ["CEO"], "entity_types": ["person"]}
{"sentence": "The United Nations Security Council votes to impose new sanctions on North Korea.", "entity_names": ["United Nations Security Council", "North Korea"], "entity_types": ["organization", "location"]}
{"sentence": "Elon Musk announces plan to colonize Mars within the next decade.", "entity_names": ["Elon Musk", "Mars"], "entity_types": ["person", "location"]}
{"sentence": "The European Union imposes tariffs on American goods in response to trade dispute.", "entity_names": ["European Union", "American"], "entity_types": ["organization", "location"]}
{"sentence": "Senator Ted Cruz introduces new bill to reform immigration policies.", "entity_names": ["Ted Cruz"], "entity_types": ["person"]}
{"sentence": "Ted Cruz delivers keynote address at Republican National Convention.", "entity_names": ["Ted Cruz"], "entity_types": ["person"]}
{"sentence": "Texan voters reelect Ted Cruz to U.S. Senate.", "entity_names": ["Ted Cruz"], "entity_types": ["person"]}
{"sentence": "Houston-based company announces plans to expand operations.", "entity_names": ["Houston"], "entity_types": ["location"]}
{"sentence": "International Atomic Energy Agency reports progress in nuclear disarmament talks.", "entity_names": ["International Atomic Energy Agency"], "entity_types": ["organization"]}
{"sentence": "Local Houston artist chosen to represent the city in international art exhibition.", "entity_names": ["Houston"], "entity_types": ["location"]}
{"sentence": "New Delhi experiences record-breaking heatwave.", "entity_names": ["New Delhi"], "entity_types": ["location"]}
{"sentence": "Copenhagen to host international climate conference next year.", "entity_names": ["Copenhagen"], "entity_types": ["location"]}
{"sentence": "The mayor of Copenhagen visits New Delhi to discuss urban development strategies.", "entity_names": ["Copenhagen", "New Delhi"], "entity_types": ["location", "location"]}
{"sentence": "City council members to meet with local Black Lives Matter leaders to discuss police reform.", "entity_names": ["Black Lives Matter"], "entity_types": ["organization"]}
{"sentence": "CELEBRITY CHEF GORDON RAMSAY OPENS NEW RESTAURANT IN LAS VEGAS.", "entity_names": ["GORDON RAMSAY", "LAS VEGAS"], "entity_types": ["person", "location"]}
{"sentence": "The International Labour Organization calls for safer working conditions in garment factories.", "entity_names": ["International Labour Organization"], "entity_types": ["organization"]}
{"sentence": "NATO announces deployment of additional troops to Eastern Europe.", "entity_names": ["NATO"], "entity_types": ["organization"]}
{"sentence": "World Bank approval boosts infrastructure development in Sub-Saharan Africa.", "entity_names": ["World Bank", "Sub-Saharan Africa"], "entity_types": ["organization", "location"]}
{"sentence": "Queen Elizabeth II celebrates her 70th year as monarch.", "entity_names": ["Queen Elizabeth II"], "entity_types": ["person"]}
{"sentence": "Buckingham Palace announces Queen Elizabeth II's upcoming state visit to Canada.", "entity_names": ["Buckingham Palace", "Queen Elizabeth II", "Canada"], "entity_types": ["organization", "person", "location"]}
{"sentence": "British Prime Minister meets with Queen Elizabeth II to discuss economic recovery plans.", "entity_names": ["British Prime Minister", "Queen Elizabeth II"], "entity_types": ["person", "person"]}
{"sentence": "Violent riots erupt in Johannesburg following controversial court ruling.", "entity_names": ["Johannesburg"], "entity_types": ["location"]}
{"sentence": "Investigation reveals corruption scandal involving top Johannesburg officials.", "entity_names": ["Johannesburg"], "entity_types": ["location"]}
{"sentence": "Johannesburg University receives record funding for new research center.", "entity_names": ["Johannesburg", "University"], "entity_types": ["location", "organization"]}
{"sentence": "Elon Musk's SpaceX completes successful test flight.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "Wildfires continue to ravage California's Napa Valley.", "entity_names": ["California", "Napa Valley"], "entity_types": ["location", "location"]}
{"sentence": "Apple announces new product launch event next month.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "Judge John Roberts nominated as Supreme Court Chief Justice.", "entity_names": ["John Roberts", "Supreme Court"], "entity_types": ["person", "organization"]}
{"sentence": "Adele announces new album release date.", "entity_names": ["Adele"], "entity_types": ["person"]}
{"sentence": "Local nonprofit organization hosts charity event for the homeless community.", "entity_names": ["nonprofit organization"], "entity_types": ["organization"]}
{"sentence": "Human rights lawyer Amal Clooney delivers an impassioned speech at the United Nations General Assembly.", "entity_names": ["Amal Clooney", "United Nations General Assembly"], "entity_types": ["person", "organization"]}
{"sentence": "Protesters gather outside the White House demanding action on climate change.", "entity_names": ["White House"], "entity_types": ["location"]}
{"sentence": "New York City announces plans to invest $10 million in public transportation improvements.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Planned Parenthood faces backlash over new funding bill.", "entity_names": ["Planned Parenthood"], "entity_types": ["organization"]}
{"sentence": "Singapore economy sees 3.2% growth in second quarter.", "entity_names": ["Singapore"], "entity_types": ["location"]}
{"sentence": "New study links air pollution to increased risk of cardiovascular disease.", "entity_names": [], "entity_types": []}
{"sentence": "Major earthquake hits Tokyo, causing extensive damage to buildings and infrastructure.", "entity_names": ["Tokyo"], "entity_types": ["location"]}
{"sentence": "Jacinda Ardern becomes the youngest female Prime Minister of New Zealand.", "entity_names": ["Jacinda Ardern", "New Zealand"], "entity_types": ["person", "location"]}
{"sentence": "Renowned physicist Stephen Hawking passes away at the age of 76.", "entity_names": ["Stephen Hawking"], "entity_types": ["person"]}
{"sentence": "The United Nations organization declares a humanitarian crisis in war-torn Syria.", "entity_names": ["United Nations", "Syria"], "entity_types": ["organization", "location"]}
{"sentence": "Amazon CEO Jeff Bezos to Step Down, Andy Jassy to Take Over", "entity_names": ["Amazon", "Jeff Bezos", "Andy Jassy"], "entity_types": ["organization", "person", "person"]}
{"sentence": "Snowstorm hits northeastern United States, causing power outages and traffic delays", "entity_names": ["United States"], "entity_types": ["location"]}
{"sentence": "Elon Musk launches new space exploration company, Neuralink.", "entity_names": ["Elon Musk", "Neuralink"], "entity_types": ["person", "organization"]}
{"sentence": "Germany imposes strict lockdown measures in response to rising COVID-19 cases.", "entity_names": ["Germany"], "entity_types": ["location"]}
{"sentence": "Angela Merkel visits United States to discuss trade agreements.", "entity_names": ["Angela Merkel", "United States"], "entity_types": ["person", "location"]}
{"sentence": "Apple's new iPhone release date revealed.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "Facebook introduces new privacy settings to protect user data.", "entity_names": ["Facebook"], "entity_types": ["organization"]}
{"sentence": "Bernie Sanders announces new healthcare policy proposal.", "entity_names": ["Bernie Sanders"], "entity_types": ["person"]}
{"sentence": "TikTok faces backlash over data privacy concerns.", "entity_names": ["TikTok"], "entity_types": ["organization"]}
{"sentence": "Senator Joe Manchin opposes new voting rights legislation.", "entity_names": ["Joe Manchin"], "entity_types": ["person"]}
{"sentence": "Meryl Streep to star in upcoming historical drama film.", "entity_names": ["Meryl Streep"], "entity_types": ["person"]}
{"sentence": "New York City announces partnership with local businesses to promote tourism.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Riyadh to host international investment conference next month.", "entity_names": ["Riyadh"], "entity_types": ["location"]}
{"sentence": "United States Agency for International Development announces new humanitarian aid initiative.", "entity_names": ["United States Agency for International Development"], "entity_types": ["organization"]}
{"sentence": "Actor Tom Hanks to star in upcoming biopic film about President Teddy Roosevelt.", "entity_names": ["Tom Hanks", "Teddy Roosevelt"], "entity_types": ["person", "person"]}
{"sentence": "Arianna Huffington launches new media platform.", "entity_names": ["Arianna Huffington"], "entity_types": ["person"]}
{"sentence": "Tech giant Apple announces partnership with Arianna Huffington's media company.", "entity_names": ["Apple", "Arianna Huffington"], "entity_types": ["organization", "person"]}
{"sentence": "Arianna Huffington to speak at the World Economic Forum in Davos.", "entity_names": ["Arianna Huffington", "World Economic Forum", "Davos"], "entity_types": ["person", "organization", "location"]}
{"sentence": "John Roberts appointed as the new anchor for Fox News.", "entity_names": ["John Roberts", "Fox News"], "entity_types": ["person", "organization"]}
{"sentence": "Fox News reports a record-breaking viewership for their prime time programming.", "entity_names": ["Fox News"], "entity_types": ["organization"]}
{"sentence": "John Roberts interviews the CEO of a major tech company on Fox News.", "entity_names": ["John Roberts", "Fox News"], "entity_types": ["person", "organization"]}
{"sentence": "The International Organization for Standardization announces new global manufacturing standards.", "entity_names": ["International Organization for Standardization"], "entity_types": ["organization"]}
{"sentence": "Washington D.C. mayor declares state of emergency due to extreme weather conditions.", "entity_names": ["Washington D.C."], "entity_types": ["location"]}
{"sentence": "Stocks plummet as investors react to trade tensions with China.", "entity_names": ["China"], "entity_types": ["location"]}
{"sentence": "Planned Parenthood faces backlash for its controversial new ad campaign.", "entity_names": ["Planned Parenthood"], "entity_types": ["organization"]}
{"sentence": "Sergey Brin, co-founder of Google, announces new venture into space exploration.", "entity_names": ["Sergey Brin"], "entity_types": ["person"]}
{"sentence": "Goldman Sachs Group reports record profits for the third quarter.", "entity_names": ["Goldman Sachs Group"], "entity_types": ["organization"]}
{"sentence": "Violent protests erupt in Beirut following government corruption scandal.", "entity_names": ["Beirut"], "entity_types": ["location"]}
{"sentence": "Kamala Harris meets with world leaders to discuss climate change solutions.", "entity_names": ["Kamala Harris"], "entity_types": ["person"]}
{"sentence": "Shanghai imposes strict lockdown measures to control COVID-19 outbreak.", "entity_names": ["Shanghai"], "entity_types": ["location"]}
{"sentence": "New York City Marathon sees record number of participants this year.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "United Nations report shows increase in global poverty rates.", "entity_names": ["United Nations"], "entity_types": ["organization"]}
{"sentence": "Apple Inc. announces the launch of its new iPhone model in September.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Stock market reacts positively to the quarterly earnings report of Apple Inc.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Apple Inc. CEO Tim Cook unveils plans for a new research and development center in China.", "entity_names": ["Apple Inc.", "Tim Cook", "China"], "entity_types": ["organization", "person", "location"]}
{"sentence": "Elon Musk's SpaceX successfully launches another batch of Starlink satellites.", "entity_names": ["Elon Musk", "SpaceX", "Starlink"], "entity_types": ["person", "organization", "organization"]}
{"sentence": "Tropical storm warnings issued for Florida and the Gulf Coast as Hurricane Grace strengthens.", "entity_names": ["Florida", "Gulf Coast", "Hurricane Grace"], "entity_types": ["location", "location", "location"]}
{"sentence": "Apple announces new iPhone 13 release date and upgraded features.", "entity_names": ["Apple", "iPhone 13"], "entity_types": ["organization", "organization"]}
{"sentence": "The World Bank Group announced a new initiative to improve access to clean water in developing countries.", "entity_names": ["World Bank Group"], "entity_types": ["organization"]}
{"sentence": "Instagram launches new feature to combat cyberbullying on its platform.", "entity_names": ["Instagram"], "entity_types": ["organization"]}
{"sentence": "Ruth Bader Ginsburg's legacy continues to influence the Supreme Court and American politics.", "entity_names": ["Ruth Bader Ginsburg"], "entity_types": ["person"]}
{"sentence": "Sonia Sotomayor appointed as the first Hispanic Supreme Court Justice.", "entity_names": ["Sonia Sotomayor", "Supreme Court"], "entity_types": ["person", "organization"]}
{"sentence": "The annual conference will take place in Paris, with Sonia Sotomayor as one of the keynote speakers.", "entity_names": ["Paris", "Sonia Sotomayor"], "entity_types": ["location", "person"]}
{"sentence": "Sonia Sotomayor's new memoir reflects on her journey to the federal bench.", "entity_names": ["Sonia Sotomayor"], "entity_types": ["person"]}
{"sentence": "Bogot\u00e1 mayor announces new public transportation plan.", "entity_names": ["Bogot\u00e1", "mayor"], "entity_types": ["location", "person"]}
{"sentence": "New study shows air pollution in Bogot\u00e1 exceeds safe levels.", "entity_names": ["Bogot\u00e1"], "entity_types": ["location"]}
{"sentence": "Bogot\u00e1-based company awarded government contract for infrastructure project.", "entity_names": ["Bogot\u00e1"], "entity_types": ["location"]}
{"sentence": "Beyonc\u00e9 releases new single, breaking streaming records.", "entity_names": ["Beyonc\u00e9"], "entity_types": ["person"]}
{"sentence": "NATO to hold emergency meeting in response to escalation of conflict in Eastern Europe.", "entity_names": ["NATO"], "entity_types": ["organization"]}
{"sentence": "World Food Programme launches new initiative to combat hunger in developing countries.", "entity_names": ["World Food Programme"], "entity_types": ["organization"]}
{"sentence": "Two prominent politicians from Buenos Aires join forces to form a new political party.", "entity_names": ["Buenos Aires"], "entity_types": ["location"]}
{"sentence": "Violent protests erupt in Buenos Aires following the controversial election results.", "entity_names": ["Buenos Aires"], "entity_types": ["location"]}
{"sentence": "Oil prices surge as Organization of the Petroleum Exporting Countries announces production cuts.", "entity_names": ["Organization of the Petroleum Exporting Countries"], "entity_types": ["organization"]}
{"sentence": "New York City Mayor announces plan to improve public transportation.", "entity_names": ["New York City", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "Facebook faces lawsuit over privacy violations.", "entity_names": ["Facebook"], "entity_types": ["organization"]}
{"sentence": "President Trump signs executive order on immigration.", "entity_names": ["President Trump"], "entity_types": ["person"]}
{"sentence": "Heavy flooding in Islamabad leads to widespread destruction and displacement.", "entity_names": ["Islamabad"], "entity_types": ["location"]}
{"sentence": "Apple launches new line of products at annual event.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "Amal Clooney to lead legal team in human rights case against Abu Dhabi government.", "entity_names": ["Amal Clooney", "Abu Dhabi"], "entity_types": ["person", "location"]}
{"sentence": "Abu Dhabi invests $10 billion in renewable energy projects.", "entity_names": ["Abu Dhabi"], "entity_types": ["location"]}
{"sentence": "Celebrity chef to open new restaurant in Abu Dhabi.", "entity_names": ["Abu Dhabi"], "entity_types": ["location"]}
{"sentence": "Budapest named as host for 2023 World Athletics Championships.", "entity_names": ["Budapest"], "entity_types": ["location"]}
{"sentence": "National Security Agency accused of spying on foreign governments.", "entity_names": ["National Security Agency"], "entity_types": ["organization"]}
{"sentence": "The Budapest Stock Exchange experienced a sharp decline in trading volume.", "entity_names": ["Budapest"], "entity_types": ["location"]}
{"sentence": "Mitt Romney announces support for new tax reform bill.", "entity_names": ["Mitt Romney"], "entity_types": ["person"]}
{"sentence": "Karachi sees a surge in COVID-19 cases.", "entity_names": ["Karachi"], "entity_types": ["location"]}
{"sentence": "President Biden meets with leaders from various environmental organizations.", "entity_names": ["Biden", "organizations"], "entity_types": ["person", "organization"]}
{"sentence": "Moscow announces new regulations on public gatherings.", "entity_names": ["Moscow"], "entity_types": ["location"]}
{"sentence": "Malala Yousafzai appointed as UN Messenger of Peace.", "entity_names": ["Malala Yousafzai"], "entity_types": ["person"]}
{"sentence": "The city of Moscow to invest $10 million in public transportation upgrades.", "entity_names": ["Moscow"], "entity_types": ["location"]}
{"sentence": "Dallas-based company announces new expansion plans.", "entity_names": ["Dallas", "company"], "entity_types": ["location", "organization"]}
{"sentence": "Authorities in Dallas investigate a series of car thefts in the downtown area.", "entity_names": ["Dallas"], "entity_types": ["location"]}
{"sentence": "Dallas Mayor Johnson delivers State of the City address.", "entity_names": ["Dallas", "Mayor Johnson"], "entity_types": ["location", "person"]}
{"sentence": "The Walt Disney Company reports record-breaking theme park attendance.", "entity_names": ["Walt Disney Company"], "entity_types": ["organization"]}
{"sentence": "CEO of The Walt Disney Company steps down amidst controversy.", "entity_names": ["The Walt Disney Company"], "entity_types": ["organization"]}
{"sentence": "New movie release from The Walt Disney Company breaks box office records.", "entity_names": ["The Walt Disney Company"], "entity_types": ["organization"]}
{"sentence": "George Clooney donates $1 million to charity.", "entity_names": ["George Clooney"], "entity_types": ["person"]}
{"sentence": "New York City to host international film festival featuring George Clooney.", "entity_names": ["New York City", "George Clooney"], "entity_types": ["location", "person"]}
{"sentence": "George Clooney's production company signs deal with streaming service for new series.", "entity_names": ["George Clooney"], "entity_types": ["person"]}
{"sentence": "Chevron Corporation reports a 10% increase in quarterly profits.", "entity_names": ["Chevron Corporation"], "entity_types": ["organization"]}
{"sentence": "Bloomberg L.P. announces new partnership with major financial institution.", "entity_names": ["Bloomberg L.P."], "entity_types": ["organization"]}
{"sentence": "Prince Harry spotted at charity event in London.", "entity_names": ["Prince Harry"], "entity_types": ["person"]}
{"sentence": "Donald Trump signs executive order on immigration.", "entity_names": ["Donald Trump"], "entity_types": ["person"]}
{"sentence": "Goldman Sachs Group announces new merger with investment firm.", "entity_names": ["Goldman Sachs Group"], "entity_types": ["organization"]}
{"sentence": "Donald Trump speech at World Economic Forum calls for global cooperation.", "entity_names": ["Donald Trump"], "entity_types": ["person"]}
{"sentence": "Bogot\u00e1 plans to implement new recycling program.", "entity_names": ["Bogot\u00e1"], "entity_types": ["location"]}
{"sentence": "Famous chef opens new restaurant in Bogot\u00e1.", "entity_names": ["Bogot\u00e1"], "entity_types": ["location"]}
{"sentence": "Bogot\u00e1 Mayor announces new public transportation project.", "entity_names": ["Bogot\u00e1", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "Riyadh city council approves new public transportation plan.", "entity_names": ["Riyadh"], "entity_types": ["location"]}
{"sentence": "Atlanta-based tech company announces plans to expand operations overseas.", "entity_names": ["Atlanta"], "entity_types": ["location"]}
{"sentence": "Tourists flock to Riyadh to visit historical landmarks and sample local cuisine.", "entity_names": ["Riyadh"], "entity_types": ["location"]}
{"sentence": "European Union imposes sanctions on individuals linked to Alexei Navalny poisoning.", "entity_names": ["European Union", "Alexei Navalny"], "entity_types": ["organization", "person"]}
{"sentence": "Alexei Navalny's supporters arrested during protests in Moscow.", "entity_names": ["Alexei Navalny", "Moscow"], "entity_types": ["person", "location"]}
{"sentence": "New York City Mayor appoints new police commissioner.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Apple Inc. releases new iPhone with advanced camera technology.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Tokyo Marathon attracts over 37,000 runners from around the world.", "entity_names": ["Tokyo"], "entity_types": ["location"]}
{"sentence": "Pyongyang announces new missile tests.", "entity_names": ["Pyongyang"], "entity_types": ["location"]}
{"sentence": "Barcelona FC wins Champions League.", "entity_names": ["Barcelona FC"], "entity_types": ["organization"]}
{"sentence": "Tensions escalate in the region as Pyongyang conducts nuclear test.", "entity_names": ["Pyongyang"], "entity_types": ["location"]}
{"sentence": "Chinese President Xi Jinping's visit to Russia strengthens bilateral ties.", "entity_names": ["Xi Jinping", "Russia"], "entity_types": ["person", "location"]}
{"sentence": "Stocks surge as investors respond positively to Xi Jinping's economic reforms.", "entity_names": ["Xi Jinping"], "entity_types": ["person"]}
{"sentence": "Xi Jinping meets with leaders of European Union to discuss trade agreement.", "entity_names": ["Xi Jinping", "European Union"], "entity_types": ["person", "organization"]}
{"sentence": "The American Civil Liberties Union files lawsuit against immigration policy.", "entity_names": ["American Civil Liberties Union"], "entity_types": ["organization"]}
{"sentence": "Large demonstration outside courthouse in support of American Civil Liberties Union case.", "entity_names": ["American Civil Liberties Union"], "entity_types": ["organization"]}
{"sentence": "American Civil Liberties Union reports increase in discrimination complaints.", "entity_names": ["American Civil Liberties Union"], "entity_types": ["organization"]}
{"sentence": "Microsoft launches new AI technology to enhance workplace productivity", "entity_names": ["Microsoft"], "entity_types": ["organization"]}
{"sentence": "Coca-Cola announces plans for environmentally friendly packaging", "entity_names": ["Coca-Cola"], "entity_types": ["organization"]}
{"sentence": "Seattle mayor proposes new budget for infrastructure improvements", "entity_names": ["Seattle"], "entity_types": ["location"]}
{"sentence": "ISIS claims responsibility for the bombing in downtown London.", "entity_names": ["ISIS", "London"], "entity_types": ["organization", "location"]}
{"sentence": "Pfizer Inc. announces record-breaking earnings for the third quarter.", "entity_names": ["Pfizer Inc."], "entity_types": ["organization"]}
{"sentence": "Authorities have arrested a suspect in connection with the vandalism of the local Pfizer Inc. research facility.", "entity_names": ["Pfizer Inc."], "entity_types": ["organization"]}
{"sentence": "London Mayor announces new transportation initiatives.", "entity_names": ["London", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "Police arrest suspect in London stabbing.", "entity_names": ["London"], "entity_types": ["location"]}
{"sentence": "London-based company receives funding for renewable energy project.", "entity_names": ["London"], "entity_types": ["location"]}
{"sentence": "New Delhi to implement new traffic regulations to reduce congestion and pollution.", "entity_names": ["New Delhi"], "entity_types": ["location"]}
{"sentence": "India's Prime Minister to meet with President of Nepal for bilateral talks.", "entity_names": ["India", "Prime Minister", "President"], "entity_types": ["location", "person", "person"]}
{"sentence": "New Delhi hosts international summit on climate change and sustainability.", "entity_names": ["New Delhi"], "entity_types": ["location"]}
{"sentence": "The Federal Communications Commission announces new regulations on internet service providers.", "entity_names": ["Federal Communications Commission"], "entity_types": ["organization"]}
{"sentence": "President Biden signs executive order to expand broadband access in rural areas.", "entity_names": ["President Biden"], "entity_types": ["person"]}
{"sentence": "European Union imposes tariffs on American tech companies.", "entity_names": ["European Union"], "entity_types": ["organization"]}
{"sentence": "The International Monetary Fund predicts a global economic downturn in the coming year.", "entity_names": ["International Monetary Fund"], "entity_types": ["organization"]}
{"sentence": "Protesters in Hong Kong continue to demand political reform and freedom from Chinese influence.", "entity_names": ["Hong Kong", "Chinese"], "entity_types": ["location", "organization"]}
{"sentence": "President Biden visits NATO headquarters in Brussels.", "entity_names": ["President Biden", "NATO", "Brussels"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Tsunami warning issued for Pacific coast.", "entity_names": ["Pacific coast"], "entity_types": ["location"]}
{"sentence": "Brazilian president Jair Bolsonaro signs new trade agreement with China.", "entity_names": ["Jair Bolsonaro", "China"], "entity_types": ["person", "location"]}
{"sentence": "Jair Bolsonaro to visit United States for bilateral talks on climate change and economic relations.", "entity_names": ["Jair Bolsonaro", "United States"], "entity_types": ["person", "location"]}
{"sentence": "Environmentalists criticize Jair Bolsonaro's handling of deforestation in the Amazon rainforest.", "entity_names": ["Jair Bolsonaro", "Amazon rainforest"], "entity_types": ["person", "location"]}
{"sentence": "Reuters announces expansion of its news coverage in Atlanta.", "entity_names": ["Reuters", "Atlanta"], "entity_types": ["organization", "location"]}
{"sentence": "Atlanta mayor meets with representatives from major tech companies to discuss job growth.", "entity_names": ["Atlanta"], "entity_types": ["location"]}
{"sentence": "Record-breaking heatwave hits Atlanta, causing power outages and traffic delays.", "entity_names": ["Atlanta"], "entity_types": ["location"]}
{"sentence": "LeBron James signs four-year contract with the Los Angeles Lakers.", "entity_names": ["LeBron James", "Los Angeles Lakers"], "entity_types": ["person", "organization"]}
{"sentence": "The Golden State Warriors secure a spot in the playoffs for the fifth consecutive season.", "entity_names": ["Golden State Warriors"], "entity_types": ["organization"]}
{"sentence": "National Basketball Association announces plans to expand to Mexico City.", "entity_names": ["National Basketball Association", "Mexico City"], "entity_types": ["organization", "location"]}
{"sentence": "Shakira to perform live at the Global Citizen Festival.", "entity_names": ["Shakira", "Global Citizen Festival"], "entity_types": ["person", "organization"]}
{"sentence": "Bradley Cooper in talks to star in new thriller movie.", "entity_names": ["Bradley Cooper"], "entity_types": ["person"]}
{"sentence": "No new COVID-19 cases reported in New Zealand for the third consecutive week.", "entity_names": ["New Zealand"], "entity_types": ["location"]}
{"sentence": "New York City Mayor announces new initiative to tackle homelessness crisis", "entity_names": ["New York City", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "Apple Inc. unveils its latest iPhone model with enhanced camera features", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Scientists discover new species of fish in the depths of the Pacific Ocean", "entity_names": ["Pacific Ocean"], "entity_types": ["location"]}
{"sentence": "Authorities in Yangon partner with the United Nations Development Programme to improve access to clean water.", "entity_names": ["Yangon", "United Nations Development Programme"], "entity_types": ["location", "organization"]}
{"sentence": "The mayor of Yangon announces plans for a new public transportation system in the city.", "entity_names": ["Yangon"], "entity_types": ["location"]}
{"sentence": "United Nations Development Programme issues a report on the impact of climate change in vulnerable regions.", "entity_names": ["United Nations Development Programme"], "entity_types": ["organization"]}
{"sentence": "Greenpeace protests outside London headquarters of oil company.", "entity_names": ["Greenpeace", "London"], "entity_types": ["organization", "location"]}
{"sentence": "Quito mayor announces plan to improve public transportation system.", "entity_names": ["Quito"], "entity_types": ["location"]}
{"sentence": "London police arrest suspect in connection with recent string of burglaries.", "entity_names": ["London"], "entity_types": ["location"]}
{"sentence": "Sonia Sotomayor nominated as the new Chief Justice of the Supreme Court.", "entity_names": ["Sonia Sotomayor", "Supreme Court"], "entity_types": ["person", "organization"]}
{"sentence": "Auckland hit by severe flooding following heavy rainfall.", "entity_names": ["Auckland"], "entity_types": ["location"]}
{"sentence": "Local Auckland organization wins national award for environmental conservation efforts.", "entity_names": ["Auckland"], "entity_types": ["location"]}
{"sentence": "European Union imposes sanctions on individuals tied to Alexei Navalny's poisoning.", "entity_names": ["European Union", "Alexei Navalny"], "entity_types": ["organization", "person"]}
{"sentence": "Protests erupt across Russia demanding release of Alexei Navalny.", "entity_names": ["Russia", "Alexei Navalny"], "entity_types": ["location", "person"]}
{"sentence": "Tesla announces plans to open new gigafactory in Texas, creating thousands of new jobs.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "German Chancellor Angela Merkel visits Rohingya refugee camps in Bangladesh.", "entity_names": ["Angela Merkel", "Rohingya refugee camps", "Bangladesh"], "entity_types": ["person", "location", "location"]}
{"sentence": "Apple unveils its latest iPhone model with a new triple-camera system and 5G capabilities.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "United States Department of Defense announces new military budget for the upcoming fiscal year.", "entity_names": ["United States Department of Defense"], "entity_types": ["organization"]}
{"sentence": "President Biden meets with United States Department of Defense officials to discuss national security issues.", "entity_names": ["President Biden", "United States Department of Defense"], "entity_types": ["person", "organization"]}
{"sentence": "United States Department of Defense issues travel advisory for military personnel stationed in the Middle East.", "entity_names": ["United States Department of Defense", "Middle East"], "entity_types": ["organization", "location"]}
{"sentence": "Tesla announces plans to build new manufacturing plant in Texas.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "Dr. Anthony Fauci warns of potential new COVID-19 variants.", "entity_names": ["Anthony Fauci"], "entity_types": ["person"]}
{"sentence": "European Union proposes new regulations to limit greenhouse gas emissions.", "entity_names": ["European Union"], "entity_types": ["organization"]}
{"sentence": "Sydney declares lockdown as COVID-19 cases surge.", "entity_names": ["Sydney"], "entity_types": ["location"]}
{"sentence": "Meghan Markle launches new podcast series.", "entity_names": ["Meghan Markle"], "entity_types": ["person"]}
{"sentence": "Major oil organizations report decreased profits amid global market uncertainty.", "entity_names": [], "entity_types": []}
{"sentence": "General Electric Company launches new renewable energy initiative.", "entity_names": ["General Electric Company"], "entity_types": ["organization"]}
{"sentence": "CEO of General Electric Company resigns amidst controversy.", "entity_names": ["General Electric Company"], "entity_types": ["organization"]}
{"sentence": "General Electric Company partners with local universities for research and development project.", "entity_names": ["General Electric Company"], "entity_types": ["organization"]}
{"sentence": "The National Football League announces new safety regulations for the upcoming season.", "entity_names": ["National Football League"], "entity_types": ["organization"]}
{"sentence": "Jair Bolsonaro delivers speech on environmental policy in Brazil.", "entity_names": ["Jair Bolsonaro", "Brazil"], "entity_types": ["person", "location"]}
{"sentence": "Dallas company awarded government contract for infrastructure development.", "entity_names": ["Dallas"], "entity_types": ["location"]}
{"sentence": "Tim Cook, CEO of Apple Inc., announces launch of new iPhone model at tech conference.", "entity_names": ["Tim Cook", "Apple Inc."], "entity_types": ["person", "organization"]}
{"sentence": "Joe Biden announces new infrastructure plan for American cities.", "entity_names": ["Joe Biden", "American cities"], "entity_types": ["person", "location"]}
{"sentence": "Stock market hits record high following Joe Biden's economic policies.", "entity_names": ["Joe Biden"], "entity_types": ["person"]}
{"sentence": "Joe Biden meets with European leaders to discuss climate change initiatives.", "entity_names": ["Joe Biden", "European leaders"], "entity_types": ["person", "organization"]}
{"sentence": "Tesla announces new electric vehicles with longer battery life.", "entity_names": ["Tesla"], "entity_types": ["organization"]}
{"sentence": "John Smith elected as the new president of the United Nations General Assembly.", "entity_names": ["John Smith", "United Nations General Assembly"], "entity_types": ["person", "organization"]}
{"sentence": "Hurricane expected to hit the East Coast of the United States in the next 48 hours.", "entity_names": ["East Coast", "United States"], "entity_types": ["location", "location"]}
{"sentence": "The National Rifle Association announces new initiatives to promote gun safety and responsible ownership.", "entity_names": ["National Rifle Association"], "entity_types": ["organization"]}
{"sentence": "Beirut experiences a surge in tourism as the city continues to rebuild after years of conflict.", "entity_names": ["Beirut"], "entity_types": ["location"]}
{"sentence": "Save the Children receives record donations to support their humanitarian efforts in war-torn regions.", "entity_names": ["Save the Children"], "entity_types": ["organization"]}
{"sentence": "Reese Witherspoon and Nicole Kidman collaborate on new TV series.", "entity_names": ["Reese Witherspoon", "Nicole Kidman"], "entity_types": ["person", "person"]}
{"sentence": "The premiere of 'Big Little Lies' features Reese Witherspoon and Nicole Kidman on the red carpet.", "entity_names": ["Reese Witherspoon", "Nicole Kidman"], "entity_types": ["person", "person"]}
{"sentence": "Reese Witherspoon and Nicole Kidman's production company signs deal with major network.", "entity_names": ["Reese Witherspoon", "Nicole Kidman"], "entity_types": ["person", "person"]}
{"sentence": "McDonald's Corporation announces plans to open 400 new locations in China.", "entity_names": ["McDonald's Corporation", "China"], "entity_types": ["organization", "location"]}
{"sentence": "The CEO of McDonald's Corporation steps down amidst controversy.", "entity_names": ["McDonald's Corporation"], "entity_types": ["organization"]}
{"sentence": "McDonald's Corporation reports 10% increase in quarterly profits.", "entity_names": ["McDonald's Corporation"], "entity_types": ["organization"]}
{"sentence": "Amal Clooney delivers powerful speech on human rights at conference in Geneva.", "entity_names": ["Amal Clooney", "Geneva"], "entity_types": ["person", "location"]}
{"sentence": "Geneva hosts international convention on climate change with delegates from over 100 countries in attendance.", "entity_names": ["Geneva"], "entity_types": ["location"]}
{"sentence": "Amal Clooney's legal team wins landmark case at the International Court of Justice in Geneva.", "entity_names": ["Amal Clooney", "International Court of Justice", "Geneva"], "entity_types": ["person", "organization", "location"]}
{"sentence": "LeBron James signs a new contract with the Los Angeles Lakers.", "entity_names": ["LeBron James", "Los Angeles Lakers"], "entity_types": ["person", "organization"]}
{"sentence": "Manila, the capital of the Philippines, experiences heavy flooding after a typhoon hits the city.", "entity_names": ["Manila", "Philippines"], "entity_types": ["location", "location"]}
{"sentence": "New Zealand Prime Minister visits Auckland to discuss climate change.", "entity_names": ["New Zealand", "Auckland"], "entity_types": ["location", "location"]}
{"sentence": "Local company in Auckland wins eco-friendly business award.", "entity_names": ["Auckland"], "entity_types": ["location"]}
{"sentence": "Protesters gather in Auckland to demand government action on affordable housing.", "entity_names": ["Auckland"], "entity_types": ["location"]}
{"sentence": "The CEO of Microsoft, Satya Nadella, announces new technological breakthrough.", "entity_names": ["Microsoft", "Satya Nadella"], "entity_types": ["organization", "person"]}
{"sentence": "The United Nations issues a statement condemning the recent political turmoil in Myanmar.", "entity_names": ["United Nations", "Myanmar"], "entity_types": ["organization", "location"]}
{"sentence": "Lionel Messi signs multi-million dollar contract with Paris Saint-Germain.", "entity_names": ["Lionel Messi", "Paris Saint-Germain"], "entity_types": ["person", "organization"]}
{"sentence": "President Jokowi to visit Jakarta for economic summit.", "entity_names": ["President Jokowi", "Jakarta"], "entity_types": ["person", "location"]}
{"sentence": "Red Cross provides aid to flood victims in Jakarta.", "entity_names": ["Red Cross", "Jakarta"], "entity_types": ["organization", "location"]}
{"sentence": "Jakarta to implement new traffic regulations to reduce congestion.", "entity_names": ["Jakarta"], "entity_types": ["location"]}
{"sentence": "Usain Bolt breaks world record in 100m sprint.", "entity_names": ["Usain Bolt"], "entity_types": ["person"]}
{"sentence": "Usain Bolt signs endorsement deal with Nike.", "entity_names": ["Usain Bolt", "Nike"], "entity_types": ["person", "organization"]}
{"sentence": "Usain Bolt retires from professional athletics.", "entity_names": ["Usain Bolt"], "entity_types": ["person"]}
{"sentence": "The International Civil Aviation Organization sets new safety standards for airlines.", "entity_names": ["International Civil Aviation Organization"], "entity_types": ["organization"]}
{"sentence": "Time Magazine names Bill Gates as the Person of the Year for 2005.", "entity_names": ["Time Magazine", "Bill Gates"], "entity_types": ["organization", "person"]}
{"sentence": "The meeting between President Biden and Prime Minister Johnson will take place at the White House.", "entity_names": ["President Biden", "Prime Minister Johnson", "White House"], "entity_types": ["person", "person", "location"]}
{"sentence": "The World Bank approves $500 million loan for renewable energy projects in India.", "entity_names": ["World Bank", "India"], "entity_types": ["organization", "location"]}
{"sentence": "UNESCO launches education initiative for refugee children in the Middle East with funding from the World Bank.", "entity_names": ["UNESCO", "Middle East", "World Bank"], "entity_types": ["organization", "location", "organization"]}
{"sentence": "South Korea's economy grows by 3.0 percent in the first quarter, exceeding World Bank's projections.", "entity_names": ["South Korea", "World Bank"], "entity_types": ["location", "organization"]}
{"sentence": "Dubai announces plans to build world's tallest skyscraper by 2025.", "entity_names": ["Dubai"], "entity_types": ["location"]}
{"sentence": "The Food and Drug Administration approves new cancer treatment drug.", "entity_names": ["Food and Drug Administration"], "entity_types": ["organization"]}
{"sentence": "Tourist arrivals in Dubai hit record high in 2020.", "entity_names": ["Dubai"], "entity_types": ["location"]}
{"sentence": "Prince William attends Save the Children fundraiser.", "entity_names": ["Prince William", "Save the Children"], "entity_types": ["person", "organization"]}
{"sentence": "Kanye West unveils new fashion line at Paris Fashion Week.", "entity_names": ["Kanye West", "Paris Fashion Week"], "entity_types": ["person", "location"]}
{"sentence": "Save the Children launches emergency response in hurricane-affected areas.", "entity_names": ["Save the Children"], "entity_types": ["organization"]}
{"sentence": "Meghan Markle to produce and create nature docuseries with Netflix.", "entity_names": ["Meghan Markle", "Netflix"], "entity_types": ["person", "organization"]}
{"sentence": "Duchess of Sussex Meghan Markle to speak at virtual summit on gender equality.", "entity_names": ["Meghan Markle"], "entity_types": ["person"]}
{"sentence": "Meghan Markle and Prince Harry announce partnership with Stanford University for new charitable firm.", "entity_names": ["Meghan Markle", "Prince Harry", "Stanford University"], "entity_types": ["person", "person", "organization"]}
{"sentence": "Lebron James scores 45 points in Lakers' victory over Warriors.", "entity_names": ["Lebron James", "Lakers", "Warriors"], "entity_types": ["person", "organization", "organization"]}
{"sentence": "LeBron James signs multi-million dollar endorsement deal with Nike.", "entity_names": ["LeBron James", "Nike"], "entity_types": ["person", "organization"]}
{"sentence": "LeBron James opens new school in his hometown of Akron, Ohio.", "entity_names": ["LeBron James", "Akron", "Ohio"], "entity_types": ["person", "location", "location"]}
{"sentence": "Oslo announces plans for new sustainable transportation initiatives.", "entity_names": ["Oslo"], "entity_types": ["location"]}
{"sentence": "Russia and Norway discuss joint efforts in Arctic exploration.", "entity_names": ["Russia", "Norway"], "entity_types": ["location", "location"]}
{"sentence": "Moscow-based company launches innovative cybersecurity software.", "entity_names": ["Moscow"], "entity_types": ["location"]}
{"sentence": "International Energy Agency predicts a 5% increase in global energy demand.", "entity_names": ["International Energy Agency"], "entity_types": ["organization"]}
{"sentence": "Mike Pence's visit to Japan strengthens bilateral trade ties.", "entity_names": ["Mike Pence", "Japan"], "entity_types": ["person", "location"]}
{"sentence": "Concerns rise as tensions escalate between Iran and the International Energy Agency.", "entity_names": ["Iran", "International Energy Agency"], "entity_types": ["location", "organization"]}
{"sentence": "Boeing announces plans to lay off 10,000 employees as part of restructuring.", "entity_names": ["Boeing"], "entity_types": ["organization"]}
{"sentence": "Shares of Boeing rise after successful test flight of new 737 MAX jet.", "entity_names": ["Boeing"], "entity_types": ["organization"]}
{"sentence": "Union workers at Boeing's plant in Seattle vote to authorize strike over pay and benefits.", "entity_names": ["Boeing", "Seattle"], "entity_types": ["organization", "location"]}
{"sentence": "Tesla announces plans to build new gigafactory in Texas.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "Mayor Garcia to unveil new public transportation initiative next week.", "entity_names": ["Mayor Garcia"], "entity_types": ["person"]}
{"sentence": "Amazon reports record-breaking sales for the holiday season.", "entity_names": ["Amazon"], "entity_types": ["organization"]}
{"sentence": "The mayor of Los Angeles announces new initiatives for affordable housing.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}
{"sentence": "Local tech startup receives $10 million in funding from a Silicon Valley venture capital firm.", "entity_names": ["Silicon Valley"], "entity_types": ["location"]}
{"sentence": "Rising temperatures in southern California prompt fire warnings.", "entity_names": ["southern California"], "entity_types": ["location"]}
{"sentence": "The new airport in Abu Dhabi is expected to handle over 20 million passengers annually.", "entity_names": ["Abu Dhabi"], "entity_types": ["location"]}
{"sentence": "Abu Dhabi Investment Authority announces plans to invest in renewable energy projects.", "entity_names": ["Abu Dhabi Investment Authority"], "entity_types": ["organization"]}
{"sentence": "Abu Dhabi Crown Prince meets with top business leaders to discuss economic development.", "entity_names": ["Abu Dhabi Crown Prince"], "entity_types": ["person"]}
{"sentence": "Donald Rumsfeld visits Kuala Lumpur to discuss regional security issues.", "entity_names": ["Donald Rumsfeld", "Kuala Lumpur"], "entity_types": ["person", "location"]}
{"sentence": "Protesters gather in front of the United Nations headquarters in New York City.", "entity_names": ["United Nations", "New York City"], "entity_types": ["organization", "location"]}
{"sentence": "New York Yankees acquire a new pitcher in a trade deal with the Chicago Cubs.", "entity_names": ["New York Yankees", "Chicago Cubs"], "entity_types": ["organization", "organization"]}
{"sentence": "The United Nations Security Council approves new sanctions against North Korea.", "entity_names": ["United Nations Security Council", "North Korea"], "entity_types": ["organization", "location"]}
{"sentence": "Protests erupt in Hong Kong after the United Nations Security Council fails to pass resolution on democracy.", "entity_names": ["Hong Kong", "United Nations Security Council"], "entity_types": ["location", "organization"]}
{"sentence": "Former president of South Africa addresses United Nations Security Council on climate change.", "entity_names": ["South Africa", "United Nations Security Council"], "entity_types": ["location", "organization"]}
{"sentence": "The Securities and Exchange Commission investigates claims of insider trading within a major tech company.", "entity_names": ["Securities and Exchange Commission"], "entity_types": ["organization"]}
{"sentence": "LeBron James signs a new contract with the National Basketball Association team.", "entity_names": ["LeBron James", "National Basketball Association"], "entity_types": ["person", "organization"]}
{"sentence": "The National Basketball Association announces plans to expand its presence in Europe.", "entity_names": ["National Basketball Association", "Europe"], "entity_types": ["organization", "location"]}
{"sentence": "Instagram's new algorithm prioritizes photos and videos from close friends and family.", "entity_names": ["Instagram"], "entity_types": ["organization"]}
{"sentence": "The International Committee of the Red Cross delivers aid to war-torn regions in the Middle East.", "entity_names": ["International Committee of the Red Cross", "Middle East"], "entity_types": ["organization", "location"]}
{"sentence": "Alexandria Ocasio-Cortez proposes a bill to reform policing practices in the United States.", "entity_names": ["Alexandria Ocasio-Cortez", "United States"], "entity_types": ["person", "location"]}
{"sentence": "The United Nations Security Council votes to impose sanctions on North Korea.", "entity_names": ["United Nations Security Council", "North Korea"], "entity_types": ["organization", "location"]}
{"sentence": "Organization of the Petroleum Exporting Countries to meet in Vienna for oil production discussions.", "entity_names": ["Organization of the Petroleum Exporting Countries", "Vienna"], "entity_types": ["organization", "location"]}
{"sentence": "Tel Aviv celebrates Pride Month with a colorful parade.", "entity_names": ["Tel Aviv"], "entity_types": ["location"]}
{"sentence": "Shanghai launches new recycling program to reduce environmental impact.", "entity_names": ["Shanghai"], "entity_types": ["location"]}
{"sentence": "International Court of Justice rules in favor of Philippines in South China Sea dispute.", "entity_names": ["International Court of Justice", "Philippines", "South China Sea"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Renowned chef opens new restaurant in downtown Los Angeles.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}
{"sentence": "Martin Scorsese's new film hits theaters this weekend.", "entity_names": ["Martin Scorsese"], "entity_types": ["person"]}
{"sentence": "Barcelona, Paris, and London are the top three European cities visited by tourists this summer.", "entity_names": ["Barcelona", "Paris", "London"], "entity_types": ["location", "location", "location"]}
{"sentence": "Beyonc\u00e9's latest album breaks streaming records on all major platforms.", "entity_names": ["Beyonc\u00e9"], "entity_types": ["person"]}
{"sentence": "Charlize Theron to star in new action film.", "entity_names": ["Charlize Theron"], "entity_types": ["person"]}
{"sentence": "Kuala Lumpur to host international conference on climate change.", "entity_names": ["Kuala Lumpur"], "entity_types": ["location"]}
{"sentence": "Local organization in Kuala Lumpur launches charity drive for homeless.", "entity_names": ["Kuala Lumpur"], "entity_types": ["location"]}
{"sentence": "Budapest prepares for annual music festival.", "entity_names": ["Budapest"], "entity_types": ["location"]}
{"sentence": "Local organization in Budapest raises funds for homeless shelters.", "entity_names": ["Budapest"], "entity_types": ["location"]}
{"sentence": "Budapest welcomes record number of tourists in 2021.", "entity_names": ["Budapest"], "entity_types": ["location"]}
{"sentence": "Hundreds of protesters gather in Yangon demanding the release of political prisoners.", "entity_names": ["Yangon"], "entity_types": ["location"]}
{"sentence": "CEO of Tesla, Elon Musk, announces plans to open a new Gigafactory in Yangon .", "entity_names": ["Tesla", "Elon Musk", "Yangon"], "entity_types": ["organization", "person", "location"]}
{"sentence": "Heavy flooding in Yangon causes widespread damage to homes and infrastructure.", "entity_names": ["Yangon"], "entity_types": ["location"]}
{"sentence": "International Red Cross sends aid to disaster-stricken area in Shanghai.", "entity_names": ["International Red Cross", "Shanghai"], "entity_types": ["organization", "location"]}
{"sentence": "Shanghai stocks reach new all-time high after government investment announcement.", "entity_names": ["Shanghai"], "entity_types": ["location"]}
{"sentence": "International Red Cross volunteers provide medical assistance in war-torn region.", "entity_names": ["International Red Cross"], "entity_types": ["organization"]}
{"sentence": "Michael Jackson's new album breaks records in sales.", "entity_names": ["Michael Jackson"], "entity_types": ["person"]}
{"sentence": "Canberra experiences record-breaking heatwave.", "entity_names": ["Canberra"], "entity_types": ["location"]}
{"sentence": "Bernie Sanders proposes new healthcare bill in Senate.", "entity_names": ["Bernie Sanders", "Senate"], "entity_types": ["person", "organization"]}
{"sentence": "Paris launches new initiative to combat climate change.", "entity_names": ["Paris"], "entity_types": ["location"]}
{"sentence": "Bernie Sanders calls for increased funding for public education.", "entity_names": ["Bernie Sanders"], "entity_types": ["person"]}
{"sentence": "Mumbai police arrest three suspects in connection with the bank robbery.", "entity_names": ["Mumbai", "police"], "entity_types": ["location", "organization"]}
{"sentence": "Helsinki Mayor announces new initiative to improve public transportation.", "entity_names": ["Helsinki", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "Investigation reveals that the Helsinki-based company was involved in illegal arms trade.", "entity_names": ["Helsinki", "company"], "entity_types": ["location", "organization"]}
{"sentence": "OPEC agrees to increase oil production as demand rises.", "entity_names": ["OPEC"], "entity_types": ["organization"]}
{"sentence": "The annual carnival in Rio de Janeiro attracts millions of visitors from around the world.", "entity_names": ["Rio de Janeiro"], "entity_types": ["location"]}
{"sentence": "Stock market plummets as fears of recession grow.", "entity_names": [], "entity_types": []}
{"sentence": "Jeff Bezos plans to invest $10 billion in the fight against climate change.", "entity_names": ["Jeff Bezos"], "entity_types": ["person"]}
{"sentence": "Amazon's CEO Jeff Bezos announces plans to step down and transition to the executive chairman role.", "entity_names": ["Amazon", "Jeff Bezos"], "entity_types": ["organization", "person"]}
{"sentence": "Jeff Bezos' Blue Origin successfully launches and lands its suborbital space tourism rocket.", "entity_names": ["Jeff Bezos", "Blue Origin"], "entity_types": ["person", "organization"]}
{"sentence": "Tesla announces plans to open new gigafactory in Texas.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "European Union imposes new sanctions on Russia.", "entity_names": ["European Union", "Russia"], "entity_types": ["organization", "location"]}
{"sentence": "The Federal Bureau of Investigation launches probe into cyber attack on government agencies.", "entity_names": ["Federal Bureau of Investigation"], "entity_types": ["organization"]}
{"sentence": "President Biden meets with Federal Bureau of Investigation director to discuss national security concerns.", "entity_names": ["President Biden", "Federal Bureau of Investigation"], "entity_types": ["person", "organization"]}
{"sentence": "The Federal Bureau of Investigation issues warning about potential terrorist threats during upcoming holiday season.", "entity_names": ["Federal Bureau of Investigation"], "entity_types": ["organization"]}
{"sentence": "Angela Merkel meets with French President Macron to discuss trade relations.", "entity_names": ["Angela Merkel", "Macron"], "entity_types": ["person", "person"]}
{"sentence": "Economy expected to grow under Angela Merkel's leadership.", "entity_names": ["Angela Merkel"], "entity_types": ["person"]}
{"sentence": "Protesters gather outside Angela Merkel's office to demand policy changes.", "entity_names": ["Angela Merkel"], "entity_types": ["person"]}
{"sentence": "Bank of America reports record profits for the third quarter.", "entity_names": ["Bank of America"], "entity_types": ["organization"]}
{"sentence": "Hanoi to host international trade conference next month.", "entity_names": ["Hanoi"], "entity_types": ["location"]}
{"sentence": "Lady Gaga cancels upcoming tour due to illness.", "entity_names": ["Lady Gaga"], "entity_types": ["person"]}
{"sentence": "Bloomberg L.P. announces $10 million grant for minority business owners.", "entity_names": ["Bloomberg L.P."], "entity_types": ["organization"]}
{"sentence": "The Democratic National Committee plans virtual convention amid COVID-19 pandemic.", "entity_names": ["Democratic National Committee"], "entity_types": ["organization"]}
{"sentence": "CEO of Bloomberg L.P. resigns after harassment allegations surface.", "entity_names": ["Bloomberg L.P."], "entity_types": ["organization"]}
{"sentence": "Actress Emma Stone wins Oscar for Best Actress.", "entity_names": ["Emma Stone"], "entity_types": ["person"]}
{"sentence": "Emma Stone to star in new Netflix series.", "entity_names": ["Emma Stone", "Netflix"], "entity_types": ["person", "organization"]}
{"sentence": "Emma Stone visits children's hospital in Los Angeles.", "entity_names": ["Emma Stone", "Los Angeles"], "entity_types": ["person", "location"]}
{"sentence": "Tesla Inc. unveils new electric car model.", "entity_names": ["Tesla Inc."], "entity_types": ["organization"]}
{"sentence": "Thousands protest in London against government policies.", "entity_names": ["London"], "entity_types": ["location"]}
{"sentence": "Ellen DeGeneres announces new partnership with Brad Pitt for upcoming film project.", "entity_names": ["Ellen DeGeneres", "Brad Pitt"], "entity_types": ["person", "person"]}
{"sentence": "Local organization donates to charity for wildlife conservation efforts.", "entity_names": [], "entity_types": []}
{"sentence": "Brad Pitt spotted dining at popular restaurant in Los Angeles with mystery woman.", "entity_names": ["Brad Pitt", "Los Angeles"], "entity_types": ["person", "location"]}
{"sentence": "Elon Musk's SpaceX launches new batch of Starlink satellites.", "entity_names": ["Elon Musk", "SpaceX", "Starlink"], "entity_types": ["person", "organization", "organization"]}
{"sentence": "Mount Everest reopens to climbers after two-year closure due to COVID-19.", "entity_names": ["Mount Everest"], "entity_types": ["location"]}
{"sentence": "Apple announces new iPhone model with improved camera features.", "entity_names": ["Apple", "iPhone"], "entity_types": ["organization", "organization"]}
{"sentence": "International Organization for Migration provides aid to refugees fleeing war-torn region.", "entity_names": ["International Organization for Migration"], "entity_types": ["organization"]}
{"sentence": "Famous actress donates $1 million to International Organization for Migration for refugee relief efforts.", "entity_names": ["International Organization for Migration"], "entity_types": ["organization"]}
{"sentence": "Thousands of migrants rescued by International Organization for Migration in Mediterranean Sea.", "entity_names": ["International Organization for Migration"], "entity_types": ["organization"]}
{"sentence": "Tech giant Apple announces new iPhone release.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "Hurricane Dorian hits Bahamas with devastating force.", "entity_names": ["Bahamas"], "entity_types": ["location"]}
{"sentence": "Actress Emma Watson named ambassador for UN Women.", "entity_names": ["Emma Watson", "UN Women"], "entity_types": ["person", "organization"]}
{"sentence": "UNESCO announces new World Heritage Sites for 2022.", "entity_names": ["UNESCO"], "entity_types": ["organization"]}
{"sentence": "Famous actor to star in upcoming film about climate change.", "entity_names": [], "entity_types": []}
{"sentence": "Seattle Seahawks sign new quarterback.", "entity_names": ["Seattle", "Seahawks"], "entity_types": ["location", "organization"]}
{"sentence": "Local Seattle residents protest against new construction project.", "entity_names": ["Seattle"], "entity_types": ["location"]}
{"sentence": "Seattle-based company Amazon reports record-breaking sales.", "entity_names": ["Seattle", "Amazon"], "entity_types": ["location", "organization"]}
{"sentence": "San Francisco Mayor announces new affordable housing plan.", "entity_names": ["San Francisco", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "Local tech company to open new headquarters in San Francisco.", "entity_names": ["tech company", "San Francisco"], "entity_types": ["organization", "location"]}
{"sentence": "San Francisco Giants defeat Los Angeles Dodgers in extra innings.", "entity_names": ["San Francisco", "Giants", "Los Angeles", "Dodgers"], "entity_types": ["location", "organization", "location", "organization"]}
{"sentence": "New York City mayor announces plan to improve public transportation.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "WHO declares new strain of flu a global health emergency.", "entity_names": ["WHO"], "entity_types": ["organization"]}
{"sentence": "The United States Department of Justice announces a new initiative to combat cybercrime.", "entity_names": ["United States Department of Justice"], "entity_types": ["organization"]}
{"sentence": "According to the Associated Press, the new trade agreement will benefit both countries.", "entity_names": ["Associated Press"], "entity_types": ["organization"]}
{"sentence": "The United States Department of Justice files a lawsuit against the pharmaceutical company for price fixing.", "entity_names": ["United States Department of Justice"], "entity_types": ["organization"]}
{"sentence": "Shakira announces new world tour dates for 2022.", "entity_names": ["Shakira"], "entity_types": ["person"]}
{"sentence": "Shakira 's foundation donates $1 million to children's charity.", "entity_names": ["Shakira"], "entity_types": ["person"]}
{"sentence": "Shakira to perform at annual music festival in Barcelona.", "entity_names": ["Shakira", "Barcelona"], "entity_types": ["person", "location"]}
{"sentence": "Royal Dutch Shell to acquire Miami-based energy company.", "entity_names": ["Royal Dutch Shell", "Miami"], "entity_types": ["organization", "location"]}
{"sentence": "Federal Trade Commission investigates potential antitrust violations by tech giants.", "entity_names": ["Federal Trade Commission"], "entity_types": ["organization"]}
{"sentence": "United States Department of State issues travel advisory for several countries in the Middle East.", "entity_names": ["United States Department of State"], "entity_types": ["organization"]}
{"sentence": "Samsung unveils new line of smartphones at tech conference.", "entity_names": ["Samsung"], "entity_types": ["organization"]}
{"sentence": "CEO of Samsung announces plan to expand manufacturing facilities in Vietnam.", "entity_names": ["Samsung"], "entity_types": ["organization"]}
{"sentence": "Samsung recalls faulty washing machines in North America.", "entity_names": ["Samsung"], "entity_types": ["organization"]}
{"sentence": "Nobel laureate Malala Yousafzai advocates for girls' education in Africa.", "entity_names": ["Malala Yousafzai", "Africa"], "entity_types": ["person", "location"]}
{"sentence": "Apple Inc. unveils new iPhone with enhanced security features.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Mayor Garcetti announces plan to address homelessness crisis in Los Angeles.", "entity_names": ["Garcetti", "Los Angeles"], "entity_types": ["person", "location"]}
{"sentence": "Shanghai reports surge in new COVID-19 cases.", "entity_names": ["Shanghai"], "entity_types": ["location"]}
{"sentence": "Dallas Mavericks advance to NBA playoffs.", "entity_names": ["Dallas", "Mavericks"], "entity_types": ["location", "organization"]}
{"sentence": "Al Jazeera journalist wins prestigious award for investigative reporting.", "entity_names": ["Al Jazeera"], "entity_types": ["organization"]}
{"sentence": "The International Court of Justice rules in favor of Ukraine in dispute with Russia.", "entity_names": ["International Court of Justice", "Ukraine", "Russia"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Former President Obama delivers speech at International Court of Justice conference.", "entity_names": ["Obama", "International Court of Justice"], "entity_types": ["person", "organization"]}
{"sentence": "United States withdraws from International Court of Justice treaty.", "entity_names": ["United States", "International Court of Justice"], "entity_types": ["location", "organization"]}
{"sentence": "Miami Heat wins the championship.", "entity_names": ["Miami", "Heat"], "entity_types": ["location", "organization"]}
{"sentence": "Dublin resident elected as new mayor.", "entity_names": ["Dublin"], "entity_types": ["location"]}
{"sentence": "Healthcare workers in Miami protest for higher wages.", "entity_names": ["Miami"], "entity_types": ["location"]}
{"sentence": "Aung San Suu Kyi meets with top military leader in Myanmar.", "entity_names": ["Aung San Suu Kyi", "Myanmar"], "entity_types": ["person", "location"]}
{"sentence": "Bangkok-based tech company announces partnership with global software giant.", "entity_names": ["Bangkok"], "entity_types": ["location"]}
{"sentence": "Pro-democracy protests flare up in Myanmar after government crackdown.", "entity_names": ["Myanmar"], "entity_types": ["location"]}
{"sentence": "The FDA approves emergency use of the Pfizer Inc. vaccine for adolescents.", "entity_names": ["FDA", "Pfizer Inc."], "entity_types": ["organization", "organization"]}
{"sentence": "Prime Minister Boris Johnson visits Pfizer Inc. headquarters in New York.", "entity_names": ["Boris Johnson", "Pfizer Inc.", "New York"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Pfizer Inc. reports record-breaking quarterly earnings.", "entity_names": ["Pfizer Inc."], "entity_types": ["organization"]}
{"sentence": "Elon Musk's SpaceX successfully launches another batch of Starlink satellites into orbit.", "entity_names": ["Elon Musk", "SpaceX", "Starlink"], "entity_types": ["person", "organization", "organization"]}
{"sentence": "The United Nations condemns the recent human rights violations in Syria.", "entity_names": ["United Nations", "Syria"], "entity_types": ["organization", "location"]}
{"sentence": "Angela Merkel, the Chancellor of Germany, visits the United States to discuss trade agreements.", "entity_names": ["Angela Merkel", "Germany", "United States"], "entity_types": ["person", "location", "location"]}
{"sentence": "Scarlett Johansson to star in upcoming historical drama film.", "entity_names": ["Scarlett Johansson"], "entity_types": ["person"]}
{"sentence": "United States Agency for International Development allocates $10 million in aid to disaster-stricken region.", "entity_names": ["United States Agency for International Development"], "entity_types": ["organization"]}
{"sentence": "New study shows increase in pollution levels in major cities.", "entity_names": [], "entity_types": []}
{"sentence": "Vienna named the most liveable city for the 12th consecutive year.", "entity_names": ["Vienna"], "entity_types": ["location"]}
{"sentence": "Delhi records highest air pollution levels in a decade.", "entity_names": ["Delhi"], "entity_types": ["location"]}
{"sentence": "Amal Clooney gives a powerful speech at the United Nations on human rights issues.", "entity_names": ["Amal Clooney", "United Nations"], "entity_types": ["person", "organization"]}
{"sentence": "Mitt Romney announces his candidacy for Utah Senate seat.", "entity_names": ["Mitt Romney", "Utah"], "entity_types": ["person", "location"]}
{"sentence": "The European Union imposes sanctions on Russia for its actions in Crimea.", "entity_names": ["European Union", "Russia", "Crimea"], "entity_types": ["organization", "location", "location"]}
{"sentence": "French President Emmanuel Macron meets with the CEO of Coca-Cola Company in Paris.", "entity_names": ["Emmanuel Macron", "Coca-Cola Company", "Paris"], "entity_types": ["person", "organization", "location"]}
{"sentence": "The Coca-Cola Company announces plans to open a new production facility in Atlanta, Georgia.", "entity_names": ["Coca-Cola Company", "Atlanta", "Georgia"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Emmanuel Macron delivers a speech at the United Nations General Assembly in New York City.", "entity_names": ["Emmanuel Macron", "United Nations", "New York City"], "entity_types": ["person", "organization", "location"]}
{"sentence": "United Nations calls for immediate ceasefire in war-torn region.", "entity_names": ["United Nations"], "entity_types": ["organization"]}
{"sentence": "Canadian Parliament passes new climate change legislation.", "entity_names": ["Canadian Parliament"], "entity_types": ["organization"]}
{"sentence": "Kourtney Kardashian launches new beauty line.", "entity_names": ["Kourtney Kardashian"], "entity_types": ["person"]}
{"sentence": "Kourtney Kardashian spotted with rumored new boyfriend in Paris.", "entity_names": ["Kourtney Kardashian", "Paris"], "entity_types": ["person", "location"]}
{"sentence": "Kourtney Kardashian announces partnership with sustainable fashion company.", "entity_names": ["Kourtney Kardashian"], "entity_types": ["person"]}
{"sentence": "Tech giant Apple announces plans to build a new campus in North Carolina.", "entity_names": ["Apple", "North Carolina"], "entity_types": ["organization", "location"]}
{"sentence": "Former President Barack Obama delivers commencement address at Ohio State University.", "entity_names": ["Barack Obama", "Ohio State University"], "entity_types": ["person", "organization"]}
{"sentence": "Wildfires continue to impact communities in Northern California.", "entity_names": ["Northern California"], "entity_types": ["location"]}
{"sentence": "Australian Prime Minister Scott Morrison visits Brussels to discuss trade agreements with the European Union.", "entity_names": ["Scott Morrison", "Brussels", "European Union"], "entity_types": ["person", "location", "organization"]}
{"sentence": "Belgium plans to invest $1 billion in renewable energy projects in Brussels.", "entity_names": ["Belgium", "Brussels"], "entity_types": ["location", "location"]}
{"sentence": "Scott Morrison proposes new legislation to improve cybersecurity in Australia.", "entity_names": ["Scott Morrison", "Australia"], "entity_types": ["person", "location"]}
{"sentence": "Lionel Messi signs a contract with Paris Saint-Germain.", "entity_names": ["Lionel Messi", "Paris Saint-Germain"], "entity_types": ["person", "organization"]}
{"sentence": "Helsinki experiences record-breaking heatwave.", "entity_names": ["Helsinki"], "entity_types": ["location"]}
{"sentence": "Barcelona bids farewell to Lionel Messi after 20 years.", "entity_names": ["Barcelona", "Lionel Messi"], "entity_types": ["organization", "person"]}
{"sentence": "George Clooney to produce new documentary on climate change.", "entity_names": ["George Clooney"], "entity_types": ["person"]}
{"sentence": "Bernie Sanders calls for increased minimum wage in Senate speech.", "entity_names": ["Bernie Sanders"], "entity_types": ["person"]}
{"sentence": "Elon Musk reveals plans for new space exploration mission.", "entity_names": ["Elon Musk"], "entity_types": ["person"]}
{"sentence": "The United Nations discusses climate change policies and strategies with representatives from member countries.", "entity_names": ["United Nations"], "entity_types": ["organization"]}
{"sentence": "Paris officials announce new initiative to improve public transportation infrastructure.", "entity_names": ["Paris"], "entity_types": ["location"]}
{"sentence": "Elon Musk's SpaceX successfully launches 60 more Starlink satellites into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "Rising tensions between Russia and Ukraine over Crimea.", "entity_names": ["Russia", "Ukraine", "Crimea"], "entity_types": ["location", "location", "location"]}
{"sentence": "Renowned physicist Stephen Hawking delivers a lecture at Cambridge University.", "entity_names": ["Stephen Hawking", "Cambridge University"], "entity_types": ["person", "location"]}
{"sentence": "Stephen Hawking's groundbreaking theories revolutionized the field of astrophysics.", "entity_names": ["Stephen Hawking"], "entity_types": ["person"]}
{"sentence": "Stephen Hawking Foundation donates $1 million to support research in theoretical physics.", "entity_names": ["Stephen Hawking Foundation"], "entity_types": ["organization"]}
{"sentence": "The International Labour Organization reports an increase in global unemployment rates.", "entity_names": ["International Labour Organization"], "entity_types": ["organization"]}
{"sentence": "Aung San Suu Kyi calls for peaceful resolution to the conflict.", "entity_names": ["Aung San Suu Kyi"], "entity_types": ["person"]}
{"sentence": "The National Football League announces new COVID-19 protocols.", "entity_names": ["National Football League"], "entity_types": ["organization"]}
{"sentence": "National Basketball Association playoffs to begin next week.", "entity_names": ["National Basketball Association"], "entity_types": ["organization"]}
{"sentence": "Five-time Super Bowl champion Tom Brady announces retirement from the National Football League.", "entity_names": ["Tom Brady", "National Football League"], "entity_types": ["person", "organization"]}
{"sentence": "Tesla announces plans to build new factory in Texas.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "Elizabeth Warren calls for increased regulation on Wall Street.", "entity_names": ["Elizabeth Warren", "Wall Street"], "entity_types": ["person", "location"]}
{"sentence": "CDC releases new guidelines for COVID-19 vaccinated individuals.", "entity_names": ["CDC", "COVID-19"], "entity_types": ["organization", "organization"]}
{"sentence": "IBM Corporation to invest $1 billion in new data center.", "entity_names": ["IBM Corporation"], "entity_types": ["organization"]}
{"sentence": "Tech giant IBM Corporation merges with software company Red Hat.", "entity_names": ["IBM Corporation", "Red Hat"], "entity_types": ["organization", "organization"]}
{"sentence": "IBM Corporation CEO, John Doe, steps down amidst company reorganization.", "entity_names": ["IBM Corporation", "John Doe"], "entity_types": ["organization", "person"]}
{"sentence": "Tony Robbins to speak at Wall Street Journal conference.", "entity_names": ["Tony Robbins", "Wall Street Journal"], "entity_types": ["person", "organization"]}
{"sentence": "Kourtney Kardashian launches new skincare line.", "entity_names": ["Kourtney Kardashian"], "entity_types": ["person"]}
{"sentence": "Wall Street Journal reports record breaking stock market performance.", "entity_names": ["Wall Street Journal"], "entity_types": ["organization"]}
{"sentence": "Protesters gather in front of government building in Reykjavik.", "entity_names": ["Reykjavik"], "entity_types": ["location"]}
{"sentence": "Elon Musk announces plans for new electric car model.", "entity_names": ["Elon Musk"], "entity_types": ["person"]}
{"sentence": "Prime Minister Scott Morrison announces new infrastructure plan to boost economy.", "entity_names": ["Scott Morrison"], "entity_types": ["person"]}
{"sentence": "Scott Morrison meets with leaders from G7 countries to discuss climate change.", "entity_names": ["Scott Morrison"], "entity_types": ["person"]}
{"sentence": "Protesters gather outside Scott Morrison's residence to demand action on climate crisis.", "entity_names": ["Scott Morrison"], "entity_types": ["person"]}
{"sentence": "Former New York City Mayor David Dinkins to be honored at annual gala.", "entity_names": ["New York City", "David Dinkins"], "entity_types": ["location", "person"]}
{"sentence": "David Dinkins joins advisory board for urban development project.", "entity_names": ["David Dinkins"], "entity_types": ["person"]}
{"sentence": "Activists call for renaming street to honor David Dinkins in Harlem.", "entity_names": ["David Dinkins", "Harlem"], "entity_types": ["person", "location"]}
{"sentence": "The International Civil Aviation Organization set new safety standards for airline operations.", "entity_names": ["International Civil Aviation Organization"], "entity_types": ["organization"]}
{"sentence": "Jeff Goldblum to star in new action film set in Quito.", "entity_names": ["Jeff Goldblum", "Quito"], "entity_types": ["person", "location"]}
{"sentence": "Quito sees an influx of tourists after being named a UNESCO World Heritage Site.", "entity_names": ["Quito", "UNESCO World Heritage Site"], "entity_types": ["location", "organization"]}
{"sentence": "Save the Children issues urgent appeal for aid in war-torn region.", "entity_names": ["Save the Children"], "entity_types": ["organization"]}
{"sentence": "World Meteorological Organization forecasts record-breaking temperatures for the upcoming summer.", "entity_names": ["World Meteorological Organization"], "entity_types": ["organization"]}
{"sentence": "International summit hosted by Save the Children focuses on child poverty and education.", "entity_names": ["Save the Children"], "entity_types": ["organization"]}
{"sentence": "Tesla stock skyrockets after announcement of new electric vehicle model.", "entity_names": ["Tesla"], "entity_types": ["organization"]}
{"sentence": "New York City prepares for annual Times Square New Year's Eve celebration.", "entity_names": ["New York City", "Times Square"], "entity_types": ["location", "location"]}
{"sentence": "Facebook faces new antitrust lawsuit from state attorneys general.", "entity_names": ["Facebook"], "entity_types": ["organization"]}
{"sentence": "Michelle Obama launches new initiative to promote health and fitness in schools.", "entity_names": ["Michelle Obama"], "entity_types": ["person"]}
{"sentence": "Donald Rumsfeld, former U.S. Secretary of Defense, passes away at the age of 88.", "entity_names": ["Donald Rumsfeld"], "entity_types": ["person"]}
{"sentence": "Shakira concert tour canceled due to COVID-19 restrictions.", "entity_names": ["Shakira"], "entity_types": ["person"]}
{"sentence": "Shakira to perform at the historic Madison Square Garden.", "entity_names": ["Shakira", "Madison Square Garden"], "entity_types": ["person", "location"]}
{"sentence": "Pop sensation Shakira releases new single, \"Hips Don't Lie\".", "entity_names": ["Shakira"], "entity_types": ["person"]}
{"sentence": "The annual international conference on Arctic studies will be held in Reykjavik next month.", "entity_names": ["Reykjavik"], "entity_types": ["location"]}
{"sentence": "The United Nations announced a new initiative to address climate change, with a major conference scheduled to take place in Reykjavik.", "entity_names": ["United Nations", "Reykjavik"], "entity_types": ["organization", "location"]}
{"sentence": "Renowned environmental activist Greta Thunberg will be speaking at a summit in Reykjavik to advocate for sustainable practices.", "entity_names": ["Greta Thunberg", "Reykjavik"], "entity_types": ["person", "location"]}
{"sentence": "Prime Minister Jacinda Ardern announces new housing reforms", "entity_names": ["Jacinda Ardern"], "entity_types": ["person"]}
{"sentence": "Athens to host international conference on climate change", "entity_names": ["Athens"], "entity_types": ["location"]}
{"sentence": "Global organization praises Jacinda Ardern's leadership during pandemic", "entity_names": ["Jacinda Ardern"], "entity_types": ["person"]}
{"sentence": "Queen Elizabeth celebrates her 70th wedding anniversary with Prince Philip.", "entity_names": ["Queen Elizabeth", "Prince Philip"], "entity_types": ["person", "person"]}
{"sentence": "New research from the University of Oxford suggests a link between air pollution and cognitive decline.", "entity_names": ["University of Oxford"], "entity_types": ["organization"]}
{"sentence": "Stocks surge as Apple reaches a new all-time high in market value.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "Helsinki to host international conference on climate change.", "entity_names": ["Helsinki"], "entity_types": ["location"]}
{"sentence": "Tony Robbins' new book ranks number one on bestseller list.", "entity_names": ["Tony Robbins"], "entity_types": ["person"]}
{"sentence": "Organization in Helsinki donates millions to fund clean water projects in Africa.", "entity_names": ["Helsinki"], "entity_types": ["location"]}
{"sentence": "Turkey's President visits Istanbul to discuss economic policies.", "entity_names": ["Turkey", "Istanbul"], "entity_types": ["location", "location"]}
{"sentence": "Four killed in Istanbul explosion.", "entity_names": ["Istanbul"], "entity_types": ["location"]}
{"sentence": "Istanbul to host international film festival next month.", "entity_names": ["Istanbul"], "entity_types": ["location"]}
{"sentence": "European Central Bank announces interest rate cut.", "entity_names": ["European Central Bank"], "entity_types": ["organization"]}
{"sentence": "Rising tensions in Middle East prompt European Central Bank to monitor market stability.", "entity_names": ["Middle East", "European Central Bank"], "entity_types": ["location", "organization"]}
{"sentence": "European Central Bank President calls for fiscal stimulus to counter economic slowdown.", "entity_names": ["European Central Bank"], "entity_types": ["organization"]}
{"sentence": "Marco Rubio delivers a passionate speech on immigration reform.", "entity_names": ["Marco Rubio"], "entity_types": ["person"]}
{"sentence": "Justin Trudeau meets with European leaders to discuss trade agreements.", "entity_names": ["Justin Trudeau"], "entity_types": ["person"]}
{"sentence": "The new policy aims to improve relations between the United States and Russia.", "entity_names": ["United States", "Russia"], "entity_types": ["location", "location"]}
{"sentence": "Tesla reveals plans for new electric vehicle plant in Texas.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "Dr. Patel leads groundbreaking research on cancer treatment.", "entity_names": ["Patel"], "entity_types": ["person"]}
{"sentence": "United Nations calls for immediate ceasefire in conflict-torn region.", "entity_names": ["United Nations"], "entity_types": ["organization"]}
{"sentence": "President Biden to meet with European leaders in Brussels to discuss trade agreements.", "entity_names": ["President Biden", "European", "Brussels"], "entity_types": ["person", "location", "location"]}
{"sentence": "Scientists discover new species of marine life in the Pacific Ocean.", "entity_names": ["Pacific Ocean"], "entity_types": ["location"]}
{"sentence": "The new shopping mall in Yangon is expected to boost the local economy.", "entity_names": ["Yangon"], "entity_types": ["location"]}
{"sentence": "Investors from Singapore are planning to expand their business presence in Yangon .", "entity_names": ["Singapore", "Yangon"], "entity_types": ["location", "location"]}
{"sentence": "The upcoming international conference in Yangon will focus on climate change and sustainable development.", "entity_names": ["Yangon"], "entity_types": ["location"]}
{"sentence": "McDonald's to open 50 new locations nationwide.", "entity_names": ["McDonald's"], "entity_types": ["organization"]}
{"sentence": "Zlatan Ibrahimovic signs one-year contract extension with AC Milan.", "entity_names": ["Zlatan Ibrahimovic", "AC Milan"], "entity_types": ["person", "organization"]}
{"sentence": "Local man arrested for attempted robbery at gas station.", "entity_names": [], "entity_types": []}
{"sentence": "Tesla reveals plans for new electric vehicle model.", "entity_names": ["Tesla"], "entity_types": ["organization"]}
{"sentence": "Angela Merkel set to deliver speech at United Nations General Assembly.", "entity_names": ["Angela Merkel", "United Nations General Assembly"], "entity_types": ["person", "organization"]}
{"sentence": "Biden administration announces new infrastructure investment initiative.", "entity_names": ["Biden administration"], "entity_types": ["organization"]}
{"sentence": "The United States Agency for International Development provides aid to developing countries.", "entity_names": ["United States Agency for International Development"], "entity_types": ["organization"]}
{"sentence": "Sony announces new partnership with Miami-based tech startup.", "entity_names": ["Sony", "Miami"], "entity_types": ["organization", "location"]}
{"sentence": "United States Agency for International Development allocates funds for clean water project in rural Africa.", "entity_names": ["United States Agency for International Development"], "entity_types": ["organization"]}
{"sentence": "The Food and Agriculture Organization reports global hunger on the rise.", "entity_names": ["Food and Agriculture Organization"], "entity_types": ["organization"]}
{"sentence": "Renowned chef Gordon Ramsay opens new restaurant in Las Vegas.", "entity_names": ["Gordon Ramsay", "Las Vegas"], "entity_types": ["person", "location"]}
{"sentence": "Brazilian government partners with Food and Agriculture Organization to improve sustainable farming practices.", "entity_names": ["Brazilian government", "Food and Agriculture Organization"], "entity_types": ["organization", "organization"]}
{"sentence": "Kim Kardashian launches new perfume line.", "entity_names": ["Kim Kardashian"], "entity_types": ["person"]}
{"sentence": "Security forces clash with protesters in downtown Cairo.", "entity_names": ["Cairo"], "entity_types": ["location"]}
{"sentence": "The International Atomic Energy Agency reports progress in nuclear inspections.", "entity_names": ["International Atomic Energy Agency"], "entity_types": ["organization"]}
{"sentence": "United Nations launches global initiative to combat climate change.", "entity_names": ["United Nations"], "entity_types": ["organization"]}
{"sentence": "United Nations Secretary-General meets with leaders to discuss peace efforts in the Middle East.", "entity_names": ["United Nations", "Secretary-General", "Middle East"], "entity_types": ["organization", "person", "location"]}
{"sentence": "Martin Scorsese's latest film receives rave reviews at Beijing Film Festival.", "entity_names": ["Martin Scorsese", "Beijing"], "entity_types": ["person", "location"]}
{"sentence": "New education policy in Beijing aims to improve school facilities and curriculum.", "entity_names": ["Beijing"], "entity_types": ["location"]}
{"sentence": "Martin Scorsese to collaborate with Beijing Film Academy on new film project.", "entity_names": ["Martin Scorsese", "Beijing Film Academy"], "entity_types": ["person", "organization"]}
{"sentence": "Time Magazine names Prague as the top travel destination for 2022.", "entity_names": ["Time Magazine", "Prague"], "entity_types": ["organization", "location"]}
{"sentence": "Prague appoints a new mayor as part of city leadership reshuffle.", "entity_names": ["Prague"], "entity_types": ["location"]}
{"sentence": "Time Magazine's latest article features Prague's rich history and cultural heritage.", "entity_names": ["Time Magazine", "Prague"], "entity_types": ["organization", "location"]}
{"sentence": "Ukrainian President Volodymyr Zelensky meets with foreign leaders at NATO summit.", "entity_names": ["Volodymyr Zelensky", "NATO"], "entity_types": ["person", "organization"]}
{"sentence": "American Cancer Society launches new initiative to improve access to cancer screenings in underserved communities.", "entity_names": ["American Cancer Society"], "entity_types": ["organization"]}
{"sentence": "The Jakarta Post reports record-breaking heatwave in Jakarta.", "entity_names": ["The Jakarta Post", "Jakarta"], "entity_types": ["organization", "location"]}
{"sentence": "President Jokowi announces new infrastructure plan for Jakarta.", "entity_names": ["Jokowi", "Jakarta"], "entity_types": ["person", "location"]}
{"sentence": "Massive protest erupts in central Jakarta over proposed tax reform.", "entity_names": ["Jakarta"], "entity_types": ["location"]}
{"sentence": "Santiago mayor announces new public transportation initiatives.", "entity_names": ["Santiago"], "entity_types": ["location"]}
{"sentence": "World Health Organization reports increase in global diabetes cases.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}
{"sentence": "Local entrepreneur, Maria Garcia, nominated for prestigious business award.", "entity_names": ["Maria Garcia"], "entity_types": ["person"]}
{"sentence": "Apple Inc. announces launch of new iPhone 13.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "President Biden signs infrastructure bill into law.", "entity_names": ["President Biden"], "entity_types": ["person"]}
{"sentence": "Hurricane Ida causes widespread damage in Louisiana and Mississippi.", "entity_names": ["Louisiana", "Mississippi"], "entity_types": ["location", "location"]}
{"sentence": "Apple Inc. launches new iPhone with advanced features.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Johnson & Johnson recalls baby powder due to asbestos contamination concerns.", "entity_names": ["Johnson & Johnson"], "entity_types": ["organization"]}
{"sentence": "President Trump signs executive order to boost infrastructure spending.", "entity_names": ["President Trump"], "entity_types": ["person"]}
{"sentence": "The Federal Trade Commission brings antitrust charges against tech giant.", "entity_names": ["Federal Trade Commission"], "entity_types": ["organization"]}
{"sentence": "Andr\u00e9s Manuel L\u00f3pez Obrador announces new infrastructure project in Mexico City.", "entity_names": ["Andr\u00e9s Manuel L\u00f3pez Obrador", "Mexico City"], "entity_types": ["person", "location"]}
{"sentence": "Investigation reveals Federal Trade Commission's concerns over data privacy violations by social media companies.", "entity_names": ["Federal Trade Commission"], "entity_types": ["organization"]}
{"sentence": "The Nobel Prize in Literature awarded to an American author for his outstanding work.", "entity_names": ["Nobel Prize"], "entity_types": ["organization"]}
{"sentence": "Stockholm experiences record-breaking heat wave as temperatures reach 35 degrees Celsius.", "entity_names": ["Stockholm"], "entity_types": ["location"]}
{"sentence": "New study from the World Health Organization shows alarming increase in obesity rates across Europe.", "entity_names": ["World Health Organization", "Europe"], "entity_types": ["organization", "location"]}
{"sentence": "Rashida Tlaib introduces bill to provide relief for small businesses affected by pandemic.", "entity_names": ["Rashida Tlaib"], "entity_types": ["person"]}
{"sentence": "Protesters gather outside Capitol Building in support of Rashida Tlaib's immigration reform proposal.", "entity_names": ["Capitol Building", "Rashida Tlaib"], "entity_types": ["location", "person"]}
{"sentence": "Rashida Tlaib's climate change legislation faces opposition in Congress.", "entity_names": ["Rashida Tlaib"], "entity_types": ["person"]}
{"sentence": "Actor Dwayne Johnson signs on for a new action-packed film.", "entity_names": ["Dwayne Johnson"], "entity_types": ["person"]}
{"sentence": "Dwayne Johnson's production company, Seven Bucks Productions, greenlights a new TV series.", "entity_names": ["Dwayne Johnson", "Seven Bucks Productions"], "entity_types": ["person", "organization"]}
{"sentence": "Miami, Florida - Dwayne Johnson spotted filming his latest movie in the city.", "entity_names": ["Miami", "Florida", "Dwayne Johnson"], "entity_types": ["location", "location", "person"]}
{"sentence": "Earthquake in Quito leaves buildings damaged and residents displaced.", "entity_names": ["Quito"], "entity_types": ["location"]}
{"sentence": "President to visit Quito to discuss economic and trade agreements with government officials.", "entity_names": ["Quito"], "entity_types": ["location"]}
{"sentence": "Mitch McConnell speaks at Athens Summit on Counter-Terrorism.", "entity_names": ["Mitch McConnell", "Athens"], "entity_types": ["person", "location"]}
{"sentence": "Amsterdam-based company launches new eco-friendly products.", "entity_names": ["Amsterdam"], "entity_types": ["location"]}
{"sentence": "Athens celebrates 200th anniversary of independence.", "entity_names": ["Athens"], "entity_types": ["location"]}
{"sentence": "Michael Jackson's estate sues HBO over documentary.", "entity_names": ["Michael Jackson", "HBO"], "entity_types": ["person", "organization"]}
{"sentence": "Fans gather at Michael Jackson's former home to commemorate his birthday.", "entity_names": ["Michael Jackson"], "entity_types": ["person"]}
{"sentence": "Michael Jackson's music continues to inspire new generations of artists.", "entity_names": ["Michael Jackson"], "entity_types": ["person"]}
{"sentence": "The mayor of New York City proposes a new initiative to address homelessness.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Apple releases new iPhone model with updated camera features.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "Beijing announces new environmental regulations to combat air pollution.", "entity_names": ["Beijing"], "entity_types": ["location"]}
{"sentence": "Chinese tech company Huawei unveils new smartphone in Beijing.", "entity_names": ["Huawei", "Beijing"], "entity_types": ["organization", "location"]}
{"sentence": "Olympic gold medalist Michael Phelps to visit Beijing for swimming competition.", "entity_names": ["Michael Phelps", "Beijing"], "entity_types": ["person", "location"]}
{"sentence": "German Chancellor Angela Merkel delivers a speech at the United Nations General Assembly.", "entity_names": ["Angela Merkel", "United Nations General Assembly"], "entity_types": ["person", "organization"]}
{"sentence": "Stockholm, Sweden prepares to host the Nobel Prize ceremony next week.", "entity_names": ["Stockholm", "Sweden", "Nobel Prize"], "entity_types": ["location", "location", "organization"]}
{"sentence": "Elon Omar speaks out against the recent immigration policy changes.", "entity_names": ["Elon Omar"], "entity_types": ["person"]}
{"sentence": "The University of Washington football team defeated Stanford in a close game.", "entity_names": ["University of Washington", "Stanford"], "entity_types": ["organization", "location"]}
{"sentence": "Lisbon to host international tech conference next month.", "entity_names": ["Lisbon"], "entity_types": ["location"]}
{"sentence": "Oslo-based company receives $10 million in funding.", "entity_names": ["Oslo"], "entity_types": ["location"]}
{"sentence": "New mayor elected in Lisbon, promising improvements in public transportation.", "entity_names": ["Lisbon"], "entity_types": ["location"]}
{"sentence": "SpaceX, founded by Elon Musk, successfully launches 60 Starlink satellites.", "entity_names": ["SpaceX", "Elon Musk"], "entity_types": ["organization", "person"]}
{"sentence": "Tesla, the electric car company led by Elon Musk, announces plans to build a new Gigafactory in Texas.", "entity_names": ["Tesla", "Elon Musk", "Texas"], "entity_types": ["organization", "person", "location"]}
{"sentence": "Elon Musk's SpaceX Starship prototype successfully completes high-altitude flight test.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "Tesla unveils new electric vehicle.", "entity_names": ["Tesla"], "entity_types": ["organization"]}
{"sentence": "London mayor announces new transportation initiative.", "entity_names": ["London"], "entity_types": ["location"]}
{"sentence": "Dr. Smith wins prestigious medical research award.", "entity_names": ["Dr. Smith"], "entity_types": ["person"]}
{"sentence": "Nelson Mandela's foundation continues to work on social justice initiatives", "entity_names": ["Nelson Mandela"], "entity_types": ["person"]}
{"sentence": "Queen Elizabeth celebrates her 70th anniversary on the throne", "entity_names": ["Queen Elizabeth"], "entity_types": ["person"]}
{"sentence": "The Nelson Mandela Foundation and Queen Elizabeth's Commonwealth Trust collaborate on a new charity initiative", "entity_names": ["Nelson Mandela Foundation", "Queen Elizabeth's Commonwealth Trust"], "entity_types": ["organization", "organization"]}
{"sentence": "Marjorie Taylor Greene faces backlash for controversial remarks.", "entity_names": ["Marjorie Taylor Greene"], "entity_types": ["person"]}
{"sentence": "Boris Johnson announces new measures to combat rising Covid-19 cases.", "entity_names": ["Boris Johnson"], "entity_types": ["person"]}
{"sentence": "Recent poll shows majority of voters in Marjorie Taylor Greene's district still support her.", "entity_names": ["Marjorie Taylor Greene"], "entity_types": ["person"]}
{"sentence": "Recep Tayyip Erdo\u011fan wins re-election as President of Turkey.", "entity_names": ["Recep Tayyip Erdo\u011fan", "Turkey"], "entity_types": ["person", "location"]}
{"sentence": "Karachi experiences heavy monsoon rains causing flooding in several areas.", "entity_names": ["Karachi"], "entity_types": ["location"]}
{"sentence": "Leading technology company Apple announces new product launch event.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "Vladimir Putin meets with German Chancellor Angela Merkel to discuss international relations.", "entity_names": ["Vladimir Putin", "Angela Merkel"], "entity_types": ["person", "person"]}
{"sentence": "Stephen Curry leads the Golden State Warriors to victory in a thrilling overtime game.", "entity_names": ["Stephen Curry", "Golden State Warriors"], "entity_types": ["person", "organization"]}
{"sentence": "New York City announces plans for a major overhaul of its public transportation system.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Simone Biles makes history with 7th consecutive national title", "entity_names": ["Simone Biles"], "entity_types": ["person"]}
{"sentence": "Bras\u00edlia embarks on major infrastructure renovation project", "entity_names": ["Bras\u00edlia"], "entity_types": ["location"]}
{"sentence": "Renowned organization selects Bras\u00edlia as the location for its annual conference", "entity_names": ["Bras\u00edlia"], "entity_types": ["location"]}
{"sentence": "Queen Elizabeth II celebrates her 95th birthday with a small family gathering at Windsor Castle.", "entity_names": ["Queen Elizabeth II", "Windsor Castle"], "entity_types": ["person", "location"]}
{"sentence": "New study finds that regular exercise can reduce the risk of heart disease by 30 percent.", "entity_names": [], "entity_types": []}
{"sentence": "The United Nations issues a statement condemning the recent acts of aggression in the Middle East.", "entity_names": ["United Nations", "Middle East"], "entity_types": ["organization", "location"]}
{"sentence": "Amazon.com announces plans to open a new distribution center in Jakarta.", "entity_names": ["Amazon.com", "Jakarta"], "entity_types": ["organization", "location"]}
{"sentence": "Yangon residents protest against government's decision to increase fuel prices.", "entity_names": ["Yangon"], "entity_types": ["location"]}
{"sentence": "Amazon.com CEO Jeff Bezos steps down, Andy Jassy to take over as new CEO.", "entity_names": ["Amazon.com", "Jeff Bezos", "Andy Jassy"], "entity_types": ["organization", "person", "person"]}
{"sentence": "Warren Buffett's Berkshire Hathaway invests $500 million in Brazilian digital bank.", "entity_names": ["Warren Buffett", "Berkshire Hathaway", "digital bank"], "entity_types": ["person", "organization", "organization"]}
{"sentence": "Warren Buffett donates $3.6 billion worth of Berkshire Hathaway shares to charity.", "entity_names": ["Warren Buffett", "Berkshire Hathaway"], "entity_types": ["person", "organization"]}
{"sentence": "Warren Buffett becomes the sixth person to join the $100 billion club.", "entity_names": ["Warren Buffett"], "entity_types": ["person"]}
{"sentence": "Elon Musk announces plans to colonize Mars by 2050.", "entity_names": ["Elon Musk", "Mars"], "entity_types": ["person", "location"]}
{"sentence": "European Union approves new trade agreement with Canada.", "entity_names": ["European Union", "Canada"], "entity_types": ["organization", "location"]}
{"sentence": "Jane Doe appointed as CEO of pharmaceutical company.", "entity_names": ["Jane Doe"], "entity_types": ["person"]}
{"sentence": "Ankara, Turkey's capital, experiences heavy rainfall and flooding.", "entity_names": ["Ankara", "Turkey"], "entity_types": ["location", "location"]}
{"sentence": "The mayor of Ankara unveils plans for a new public transportation system.", "entity_names": ["Ankara"], "entity_types": ["location"]}
{"sentence": "Ankara-based tech company, XYZ Inc., announces record-breaking profits for the quarter.", "entity_names": ["Ankara", "XYZ Inc."], "entity_types": ["location", "organization"]}
{"sentence": "Queen Elizabeth II celebrates her diamond jubilee with a grand parade through London.", "entity_names": ["Elizabeth II", "London"], "entity_types": ["person", "location"]}
{"sentence": "The United Nations Secretary-General meets with Queen Elizabeth II to discuss global peace initiatives.", "entity_names": ["United Nations", "Secretary-General", "Elizabeth II"], "entity_types": ["organization", "organization", "person"]}
{"sentence": "Commonwealth leaders gather in London for a summit hosted by Queen Elizabeth II.", "entity_names": ["Commonwealth", "London", "Elizabeth II"], "entity_types": ["organization", "location", "person"]}
{"sentence": "Elon Musk's SpaceX successfully launches Falcon 9 rocket.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "Apple unveils new iPhone with improved camera technology.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "Bank of America reports record-breaking profits for the third quarter of the year.", "entity_names": ["Bank of America"], "entity_types": ["organization"]}
{"sentence": "Amazon.com announces plans to open a new fulfillment center in Mumbai, India.", "entity_names": ["Amazon.com", "Mumbai"], "entity_types": ["organization", "location"]}
{"sentence": "Istanbul experiences major traffic disruptions due to heavy snowfall.", "entity_names": ["Istanbul"], "entity_types": ["location"]}
{"sentence": "Greenpeace launches new campaign to protect marine life.", "entity_names": ["Greenpeace"], "entity_types": ["organization"]}
{"sentence": "Violent protests erupt in Islamabad following controversial election results.", "entity_names": ["Islamabad"], "entity_types": ["location"]}
{"sentence": "Scientist claims Greenpeace is harming efforts to combat climate change.", "entity_names": ["Greenpeace"], "entity_types": ["organization"]}
{"sentence": "President Bolsonaro delivers speech in Bras\u00edlia.", "entity_names": ["Bolsonaro", "Bras\u00edlia"], "entity_types": ["person", "location"]}
{"sentence": "Heavy rain causes flooding in Bras\u00edlia neighborhoods.", "entity_names": ["Bras\u00edlia"], "entity_types": ["location"]}
{"sentence": "Bras\u00edlia hosts international conference on climate change.", "entity_names": ["Bras\u00edlia"], "entity_types": ["location"]}
{"sentence": "The National Institutes of Health allocates $2.5 million for new research on cancer treatments.", "entity_names": ["National Institutes of Health"], "entity_types": ["organization"]}
{"sentence": "International Labour Organization report shows decrease in global unemployment rates.", "entity_names": ["International Labour Organization"], "entity_types": ["organization"]}
{"sentence": "The CEO of Johnson & Johnson announces plans for expansion into new international markets.", "entity_names": ["Johnson & Johnson"], "entity_types": ["organization"]}
{"sentence": "Dubai police apprehend international drug trafficking ring.", "entity_names": ["Dubai"], "entity_types": ["location"]}
{"sentence": "Central Intelligence Agency reveals new cyber security threat.", "entity_names": ["Central Intelligence Agency"], "entity_types": ["organization"]}
{"sentence": "Dubai-based company wins contract for major construction project.", "entity_names": ["Dubai"], "entity_types": ["location"]}
{"sentence": "Simone Biles withdraws from Olympic team finals due to mental health concerns.", "entity_names": ["Simone Biles", "Olympic"], "entity_types": ["person", "organization"]}
{"sentence": "Warren Buffett sells billions in stocks as market volatility continues.", "entity_names": ["Warren Buffett"], "entity_types": ["person"]}
{"sentence": "Boeing Company faces backlash over safety concerns with 737 MAX planes.", "entity_names": ["Boeing Company"], "entity_types": ["organization"]}
{"sentence": "Tesla CEO Elon Musk announces plans for new electric vehicle lineup.", "entity_names": ["Tesla", "Elon Musk"], "entity_types": ["organization", "person"]}
{"sentence": "New York City experiences record-breaking heat wave.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "UNICEF launches new initiative to provide education to refugee children.", "entity_names": ["UNICEF"], "entity_types": ["organization"]}
{"sentence": "Warren Buffett acquires majority stake in a major tech company.", "entity_names": ["Warren Buffett"], "entity_types": ["person"]}
{"sentence": "Investors eagerly await Warren Buffett's annual letter to shareholders.", "entity_names": ["Warren Buffett"], "entity_types": ["person"]}
{"sentence": "Warren Buffett's Berkshire Hathaway announces record-breaking profits.", "entity_names": ["Warren Buffett", "Berkshire Hathaway"], "entity_types": ["person", "organization"]}
{"sentence": "New York City mayor announces plan to address homelessness crisis.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "European Union imposes sanctions on Russian officials.", "entity_names": ["European Union"], "entity_types": ["organization"]}
{"sentence": "Seattle Mayor announces new affordable housing initiative.", "entity_names": ["Seattle", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "Amazon headquarters in Seattle to expand workforce by 10,000.", "entity_names": ["Amazon", "Seattle"], "entity_types": ["organization", "location"]}
{"sentence": "Seattle-based coffee chain to open new store in downtown area.", "entity_names": ["Seattle"], "entity_types": ["location"]}
{"sentence": "Ford Motor Company faces lawsuit from the Environmental Protection Agency.", "entity_names": ["Ford Motor Company", "Environmental Protection Agency"], "entity_types": ["organization", "organization"]}
{"sentence": "The former CEO of Ford Motor Company resigns amid scandal.", "entity_names": ["Ford Motor Company"], "entity_types": ["organization"]}
{"sentence": "Environmental Protection Agency announces new regulations for air quality standards.", "entity_names": ["Environmental Protection Agency"], "entity_types": ["organization"]}
{"sentence": "Delhi experiences record-breaking heatwave.", "entity_names": ["Delhi"], "entity_types": ["location"]}
{"sentence": "The new metro line in Delhi will significantly reduce commuting time.", "entity_names": ["Delhi"], "entity_types": ["location"]}
{"sentence": "India announces ambitious plan to tackle air pollution in Delhi.", "entity_names": ["India", "Delhi"], "entity_types": ["location", "location"]}
{"sentence": "Tech giant under investigation by Federal Trade Commission for antitrust violations.", "entity_names": ["Federal Trade Commission"], "entity_types": ["organization"]}
{"sentence": "Renowned economist appointed as new chief advisor to the Federal Trade Commission.", "entity_names": ["Federal Trade Commission"], "entity_types": ["organization"]}
{"sentence": "Federal Trade Commission launches probe into alleged consumer privacy violations by social media platform.", "entity_names": ["Federal Trade Commission"], "entity_types": ["organization"]}
{"sentence": "Nelson Mandela delivers powerful speech at United Nations General Assembly.", "entity_names": ["Nelson Mandela", "United Nations General Assembly"], "entity_types": ["person", "organization"]}
{"sentence": "American Cancer Society announces breakthrough in cancer treatment research.", "entity_names": ["American Cancer Society"], "entity_types": ["organization"]}
{"sentence": "Exxon Mobil Corporation faces backlash over environmental concerns in Abu Dhabi.", "entity_names": ["Exxon Mobil Corporation", "Abu Dhabi"], "entity_types": ["organization", "location"]}
{"sentence": "Benjamin Netanyahu reelected as Prime Minister of Israel.", "entity_names": ["Benjamin Netanyahu", "Israel"], "entity_types": ["person", "location"]}
{"sentence": "New study shows increase in cyber attacks on government organizations.", "entity_names": [], "entity_types": []}
{"sentence": "Instagram announces new feature to detect and filter out hate speech.", "entity_names": ["Instagram"], "entity_types": ["organization"]}
{"sentence": "Famous actress reveals her pregnancy news on Instagram.", "entity_names": ["Instagram"], "entity_types": ["organization"]}
{"sentence": "Instagram influencer reaches one million followers milestone.", "entity_names": ["Instagram"], "entity_types": ["organization"]}
{"sentence": "The National Broadcasting Company reports record-breaking ratings for their new television series.", "entity_names": ["National Broadcasting Company"], "entity_types": ["organization"]}
{"sentence": "Reykjavik Mayor announces plans for new environmental initiatives in the city center.", "entity_names": ["Reykjavik", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "A group of tourists visiting Reykjavik witness the eruption of a nearby volcano.", "entity_names": ["Reykjavik"], "entity_types": ["location"]}
{"sentence": "Elon Musk announces new space exploration project on Twitter.", "entity_names": ["Elon Musk", "Twitter"], "entity_types": ["person", "organization"]}
{"sentence": "Protests erupt in downtown Los Angeles, according to Twitter reports.", "entity_names": ["Los Angeles", "Twitter"], "entity_types": ["location", "organization"]}
{"sentence": "Investors react to the company's quarterly earnings report on Twitter.", "entity_names": ["Twitter"], "entity_types": ["organization"]}
{"sentence": "Robert Downey Jr. signs on for new action-packed thriller", "entity_names": ["Robert Downey Jr."], "entity_types": ["person"]}
{"sentence": "Los Angeles Lakers trade star player in blockbuster deal", "entity_names": ["Los Angeles Lakers"], "entity_types": ["organization"]}
{"sentence": "Bomb threat reported at London Heathrow airport", "entity_names": ["London Heathrow"], "entity_types": ["location"]}
{"sentence": "Royal Dutch Shell announces plans for new offshore drilling project in the North Sea.", "entity_names": ["Royal Dutch Shell", "North Sea"], "entity_types": ["organization", "location"]}
{"sentence": "Sergey Lavrov meets with Turkish Foreign Minister to discuss bilateral relations and regional security concerns.", "entity_names": ["Sergey Lavrov", "Turkish Foreign Minister"], "entity_types": ["person", "organization"]}
{"sentence": "Stock market sees a rise in energy sector stocks following Royal Dutch Shell's announcement of major investment in renewable energy.", "entity_names": ["Royal Dutch Shell"], "entity_types": ["organization"]}
{"sentence": "Michelle Obama launches new educational initiative.", "entity_names": ["Michelle Obama"], "entity_types": ["person"]}
{"sentence": "Alexei Navalny sentenced to prison for 2.5 years.", "entity_names": ["Alexei Navalny"], "entity_types": ["person"]}
{"sentence": "Greenpeace protests environmental policy changes outside Congress.", "entity_names": ["Greenpeace"], "entity_types": ["organization"]}
{"sentence": "NATO leaders discuss military presence in Eastern Europe.", "entity_names": ["NATO", "Eastern Europe"], "entity_types": ["organization", "location"]}
{"sentence": "U.S. urges NATO members to increase defense spending.", "entity_names": ["U.S.", "NATO"], "entity_types": ["location", "organization"]}
{"sentence": "Joint military exercise held by NATO and Baltic countries.", "entity_names": ["NATO", "Baltic countries"], "entity_types": ["organization", "location"]}
{"sentence": "Joe Biden signs executive order to address climate change.", "entity_names": ["Joe Biden"], "entity_types": ["person"]}
{"sentence": "Financial aid package announced by Joe Biden to support small businesses.", "entity_names": ["Joe Biden"], "entity_types": ["person"]}
{"sentence": "Joe Biden meets with European leaders to discuss international trade agreements.", "entity_names": ["Joe Biden"], "entity_types": ["person"]}
{"sentence": "Pro-democracy activist Joshua Wong arrested in Hong Kong.", "entity_names": ["Joshua Wong", "Hong Kong"], "entity_types": ["person", "location"]}
{"sentence": "Apple opens new retail store in Hong Kong.", "entity_names": ["Apple", "Hong Kong"], "entity_types": ["organization", "location"]}
{"sentence": "Hong Kong protests result in clashes between police and demonstrators.", "entity_names": ["Hong Kong"], "entity_types": ["location"]}
{"sentence": "George W. Bush to speak at climate change conference.", "entity_names": ["George W. Bush"], "entity_types": ["person"]}
{"sentence": "Harvey Weinstein found guilty of sexual assault.", "entity_names": ["Harvey Weinstein"], "entity_types": ["person"]}
{"sentence": "New York City Mayor announces plan to reduce traffic congestion.", "entity_names": ["New York City", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "J.K. Rowling donates $15 million to literacy charity.", "entity_names": ["J.K. Rowling"], "entity_types": ["person"]}
{"sentence": "New Harry Potter theme park opens in Orlando, Florida featuring attractions based on J.K. Rowling's books.", "entity_names": ["Orlando", "Florida", "J.K. Rowling"], "entity_types": ["location", "location", "person"]}
{"sentence": "J.K. Rowling's new novel tops bestseller lists in the UK and US.", "entity_names": ["J.K. Rowling", "UK", "US"], "entity_types": ["person", "location", "location"]}
{"sentence": "Prime Minister Angela Merkel visits Berlin school to discuss education reform.", "entity_names": ["Angela Merkel", "Berlin"], "entity_types": ["person", "location"]}
{"sentence": "Environmental organization Greenpeace launches campaign against plastic pollution.", "entity_names": ["Greenpeace"], "entity_types": ["organization"]}
{"sentence": "Federal Emergency Management Agency announces new disaster relief funding.", "entity_names": ["Federal Emergency Management Agency"], "entity_types": ["organization"]}
{"sentence": "Donald Rumsfeld denies involvement in controversial military decision.", "entity_names": ["Donald Rumsfeld"], "entity_types": ["person"]}
{"sentence": "New York City implements new safety measures for public transportation.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "The World Economic Forum announces its annual meeting will be held in Davos next year.", "entity_names": ["World Economic Forum", "Davos"], "entity_types": ["organization", "location"]}
{"sentence": "Kim Kardashian launches a new skincare line, sparking excitement among her fans.", "entity_names": ["Kim Kardashian"], "entity_types": ["person"]}
{"sentence": "Tech giants Microsoft and Apple team up to develop new software for educational purposes.", "entity_names": ["Microsoft", "Apple"], "entity_types": ["organization", "organization"]}
{"sentence": "Will Smith to star in new action thriller set in Berlin.", "entity_names": ["Will Smith", "Berlin"], "entity_types": ["person", "location"]}
{"sentence": "Berlin Philharmonic Orchestra to perform at the prestigious concert hall.", "entity_names": ["Berlin Philharmonic Orchestra"], "entity_types": ["organization"]}
{"sentence": "Will Smith's production company to collaborate with Berlin-based film studio on new project.", "entity_names": ["Will Smith", "Berlin"], "entity_types": ["person", "location"]}
{"sentence": "Boeing Company announces plan to cut 12,000 U.S. jobs.", "entity_names": ["Boeing Company"], "entity_types": ["organization"]}
{"sentence": "New York Times reports record digital subscriptions for the third quarter.", "entity_names": ["New York Times"], "entity_types": ["organization"]}
{"sentence": "CEO of Boeing Company resigns amid ongoing investigation.", "entity_names": ["Boeing Company"], "entity_types": ["organization"]}
{"sentence": "Tesla's CEO Elon Musk unveils new electric truck.", "entity_names": ["Tesla", "Elon Musk"], "entity_types": ["organization", "person"]}
{"sentence": "New York City plans to invest $10 million in renewable energy initiatives.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "The World Health Organization announces a new initiative to combat malaria in Africa.", "entity_names": ["World Health Organization", "Africa"], "entity_types": ["organization", "location"]}
{"sentence": "Reddit announces new policy changes to combat misinformation on its platform.", "entity_names": ["Reddit"], "entity_types": ["organization"]}
{"sentence": "The United States Department of State issues a travel advisory for several African countries due to civil unrest.", "entity_names": ["United States Department of State", "African countries"], "entity_types": ["organization", "location"]}
{"sentence": "Users on Reddit criticize the United States Department of State's decision to lift sanctions on a controversial foreign leader.", "entity_names": ["Reddit", "United States Department of State"], "entity_types": ["organization", "organization"]}
{"sentence": "The International Air Transport Association forecasts a 63% drop in global air traffic.", "entity_names": ["International Air Transport Association"], "entity_types": ["organization"]}
{"sentence": "Jennifer Lawrence to star in upcoming Netflix thriller.", "entity_names": ["Jennifer Lawrence"], "entity_types": ["person"]}
{"sentence": "Doctors Without Borders provides aid in war-torn regions.", "entity_names": ["Doctors Without Borders"], "entity_types": ["organization"]}
{"sentence": "The United States Department of State issues travel advisory for Barcelona.", "entity_names": ["United States Department of State", "Barcelona"], "entity_types": ["organization", "location"]}
{"sentence": "Barcelona FC signs United States player as new forward.", "entity_names": ["Barcelona FC", "United States"], "entity_types": ["organization", "location"]}
{"sentence": "Tensions rise between United States and Barcelona over trade agreement.", "entity_names": ["United States", "Barcelona"], "entity_types": ["location", "location"]}
{"sentence": "Serena Williams wins her seventh Grand Slam title.", "entity_names": ["Serena Williams"], "entity_types": ["person"]}
{"sentence": "Ursula von der Leyen becomes the first female President of the European Commission.", "entity_names": ["Ursula von der Leyen", "European Commission"], "entity_types": ["person", "organization"]}
{"sentence": "The National Basketball Association cancels games due to COVID-19 outbreak.", "entity_names": ["National Basketball Association"], "entity_types": ["organization"]}
{"sentence": "Earthquake strikes off the coast of Japan, causing minor damage.", "entity_names": ["Japan"], "entity_types": ["location"]}
{"sentence": "Apple announces plans to open new headquarters in Austin, Texas.", "entity_names": ["Apple", "Austin", "Texas"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Houston Rockets beat Golden State Warriors", "entity_names": ["Houston", "Rockets", "Golden State Warriors"], "entity_types": ["location", "organization", "organization"]}
{"sentence": "Houston, TX experiences record-breaking heatwave", "entity_names": ["Houston, TX"], "entity_types": ["location"]}
{"sentence": "NASA's Johnson Space Center in Houston announces new Mars mission", "entity_names": ["NASA", "Johnson Space Center", "Houston", "Mars"], "entity_types": ["organization", "organization", "location", "location"]}
{"sentence": "Prince Harry and Meghan Markle step back from royal duties.", "entity_names": ["Prince Harry"], "entity_types": ["person"]}
{"sentence": "Martin Scorsese's new film receives rave reviews at Cannes.", "entity_names": ["Martin Scorsese"], "entity_types": ["person"]}
{"sentence": "David Dinkins, first African American mayor of New York City, passes away at 93.", "entity_names": ["David Dinkins", "New York City"], "entity_types": ["person", "location"]}
{"sentence": "South African President Cyril Ramaphosa pays tribute to Nelson Mandela on his 102nd birthday.", "entity_names": ["Cyril Ramaphosa", "Nelson Mandela"], "entity_types": ["person", "person"]}
{"sentence": "The Nelson Mandela Foundation launches a campaign to promote social justice and equality.", "entity_names": ["Nelson Mandela Foundation"], "entity_types": ["organization"]}
{"sentence": "Thousands gather in Johannesburg to celebrate the legacy of Nelson Mandela.", "entity_names": ["Johannesburg", "Nelson Mandela"], "entity_types": ["location", "person"]}
{"sentence": "Tropical storm brings heavy rain and strong winds to Florida coast.", "entity_names": ["Florida"], "entity_types": ["location"]}
{"sentence": "Montreal-based company announces plans for expansion in Istanbul.", "entity_names": ["Montreal", "company", "Istanbul"], "entity_types": ["location", "organization", "location"]}
{"sentence": "Istanbul mayor denies allegations of corruption.", "entity_names": ["Istanbul", "mayor"], "entity_types": ["location", "person"]}
{"sentence": "Montreal artist wins prestigious international award.", "entity_names": ["Montreal"], "entity_types": ["location"]}
{"sentence": "Former President George W. Bush to release new book on leadership and decision-making.", "entity_names": ["George W. Bush"], "entity_types": ["person"]}
{"sentence": "The World Bank Group launches initiative to provide clean water to rural communities in Africa.", "entity_names": ["World Bank Group"], "entity_types": ["organization"]}
{"sentence": "New study shows that George W. Bush's approval rating rose during his second term in office.", "entity_names": ["George W. Bush"], "entity_types": ["person"]}
{"sentence": "Election results show landslide victory for incumbent mayor in Budapest.", "entity_names": ["Budapest"], "entity_types": ["location"]}
{"sentence": "Company based in Budapest announces groundbreaking AI technology.", "entity_names": ["Budapest"], "entity_types": ["location"]}
{"sentence": "Simone Biles withdraws from all-around gymnastics final due to mental health concerns.", "entity_names": ["Simone Biles"], "entity_types": ["person"]}
{"sentence": "Local Miami artist chosen to create mural for the new downtown development project.", "entity_names": ["Miami"], "entity_types": ["location"]}
{"sentence": "Bloomberg L.P. reports record-breaking profits for the third quarter of the year.", "entity_names": ["Bloomberg L.P."], "entity_types": ["organization"]}
{"sentence": "Tech entrepreneur Elon Musk announces plans to build a new manufacturing plant in Texas.", "entity_names": ["Elon Musk", "Texas"], "entity_types": ["person", "location"]}
{"sentence": "The United Nations issues a statement condemning the recent human rights violations in Myanmar.", "entity_names": ["United Nations", "Myanmar"], "entity_types": ["organization", "location"]}
{"sentence": "Tesla announces plans to open new production facility in Europe.", "entity_names": ["Tesla", "Europe"], "entity_types": ["organization", "location"]}
{"sentence": "Dr. Amanda Smith appointed as new head of medical research at the University of California.", "entity_names": ["Amanda Smith", "University of California"], "entity_types": ["person", "organization"]}
{"sentence": "Protests erupt in New York City after controversial police shooting.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "The United Nations Educational, Scientific and Cultural Organization announces new initiatives for preserving cultural heritage.", "entity_names": ["The United Nations Educational, Scientific and Cultural Organization"], "entity_types": ["organization"]}
{"sentence": "Mumbai experiences record-breaking rainfall causing widespread flooding.", "entity_names": ["Mumbai"], "entity_types": ["location"]}
{"sentence": "Cristiano Ronaldo signs historic contract with new football club.", "entity_names": ["Cristiano Ronaldo"], "entity_types": ["person"]}
{"sentence": "Elon Omar speaks at the United Nations Climate Summit in New York City.", "entity_names": ["Elon Omar", "United Nations", "New York City"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Tesla, led by Elon Musk, announces plans to build a new Gigafactory in Texas.", "entity_names": ["Tesla", "Elon Musk", "Texas"], "entity_types": ["organization", "person", "location"]}
{"sentence": "Elon Omar elected as the new chairperson of the Congressional Committee on Energy and Commerce.", "entity_names": ["Elon Omar", "Congressional Committee on Energy and Commerce"], "entity_types": ["person", "organization"]}
{"sentence": "The annual jazz festival in Cape Town has been canceled due to the ongoing pandemic.", "entity_names": ["Cape Town"], "entity_types": ["location"]}
{"sentence": "The United Nations has announced a new aid package for refugees in Cape Town .", "entity_names": ["United Nations", "Cape Town"], "entity_types": ["organization", "location"]}
{"sentence": "Local musician from Cape Town wins prestigious music award.", "entity_names": ["Cape Town"], "entity_types": ["location"]}
{"sentence": "The National Basketball Association announces plans to launch a new team in Houston.", "entity_names": ["National Basketball Association", "Houston"], "entity_types": ["organization", "location"]}
{"sentence": "Houston Rockets secure victory in the National Basketball Association playoffs.", "entity_names": ["Houston Rockets", "National Basketball Association"], "entity_types": ["organization", "organization"]}
{"sentence": "The National Basketball Association finals will be held in Houston next year.", "entity_names": ["National Basketball Association", "Houston"], "entity_types": ["organization", "location"]}
{"sentence": "Bogot\u00e1 experiences a surge in tourism during the holiday season.", "entity_names": ["Bogot\u00e1"], "entity_types": ["location"]}
{"sentence": "Local organization in Bogot\u00e1 launches campaign to reduce plastic pollution in the city.", "entity_names": ["Bogot\u00e1"], "entity_types": ["location"]}
{"sentence": "Renowned chef opens new restaurant in Bogot\u00e1, featuring traditional Colombian cuisine.", "entity_names": ["Bogot\u00e1"], "entity_types": ["location"]}
{"sentence": "TikTok faces potential ban in the United States.", "entity_names": ["TikTok", "United States"], "entity_types": ["organization", "location"]}
{"sentence": "Indian government bans TikTok and 58 other Chinese apps.", "entity_names": ["Indian", "TikTok"], "entity_types": ["location", "organization"]}
{"sentence": "TikTok partners with NFL for exclusive content.", "entity_names": ["TikTok", "NFL"], "entity_types": ["organization", "organization"]}
{"sentence": "Bogot\u00e1 mayor announces new public transportation initiative.", "entity_names": ["Bogot\u00e1"], "entity_types": ["location"]}
{"sentence": "Local artist from Bogot\u00e1 wins international art competition.", "entity_names": ["Bogot\u00e1"], "entity_types": ["location"]}
{"sentence": "Elon Musk announces plans to build a new SpaceX facility in Nairobi.", "entity_names": ["Elon Musk", "Nairobi"], "entity_types": ["person", "location"]}
{"sentence": "Elon Musk unveils new electric vehicle design at Tesla's annual event.", "entity_names": ["Elon Musk", "Tesla"], "entity_types": ["person", "organization"]}
{"sentence": "Bank of America announces plans to increase investment in renewable energy projects.", "entity_names": ["Bank of America"], "entity_types": ["organization"]}
{"sentence": "Goldman Sachs Group forecasts global economic slowdown in the coming year.", "entity_names": ["Goldman Sachs Group"], "entity_types": ["organization"]}
{"sentence": "Apple CEO Tim Cook to deliver keynote speech at technology conference in Brussels.", "entity_names": ["Apple", "Tim Cook", "Brussels"], "entity_types": ["organization", "person", "location"]}
{"sentence": "Wildfires continue to threaten homes in Los Angeles County as firefighters work tirelessly to contain the blaze.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}
{"sentence": "European Union leaders gather in Brussels for emergency summit on immigration crisis.", "entity_names": ["European Union", "Brussels"], "entity_types": ["organization", "location"]}
{"sentence": "Tony Robbins delivers motivational speech to packed arena.", "entity_names": ["Tony Robbins"], "entity_types": ["person"]}
{"sentence": "Kylie Jenner launches new cosmetic line.", "entity_names": ["Kylie Jenner"], "entity_types": ["person"]}
{"sentence": "Billionaire investor Warren Buffett donates $3 billion to charity.", "entity_names": ["Warren Buffett"], "entity_types": ["person"]}
{"sentence": "Shanghai reports record high tourism numbers.", "entity_names": ["Shanghai"], "entity_types": ["location"]}
{"sentence": "China Eastern Airlines opens new route to Shanghai.", "entity_names": ["China Eastern Airlines", "Shanghai"], "entity_types": ["organization", "location"]}
{"sentence": "Shanghai cooperation summit held in Beijing.", "entity_names": ["Shanghai", "Beijing"], "entity_types": ["location", "location"]}
{"sentence": "Houston Rockets defeat Los Angeles Lakers in a close game.", "entity_names": ["Houston", "Los Angeles Lakers"], "entity_types": ["location", "organization"]}
{"sentence": "Yangon to host international trade fair next month.", "entity_names": ["Yangon"], "entity_types": ["location"]}
{"sentence": "NASA's Mars rover discovers evidence of ancient microbial life.", "entity_names": ["NASA", "Mars"], "entity_types": ["organization", "location"]}
{"sentence": "Cairo to host international conference on renewable energy.", "entity_names": ["Cairo"], "entity_types": ["location"]}
{"sentence": "Unrest in Cairo leads to clashes between protestors and police.", "entity_names": ["Cairo"], "entity_types": ["location"]}
{"sentence": "The International Organization for Standardization releases new guidelines for environmental management.", "entity_names": ["International Organization for Standardization"], "entity_types": ["organization"]}
{"sentence": "The United Nations and the International Organization for Standardization collaborate on a project to promote sustainable development in Africa.", "entity_names": ["United Nations", "International Organization for Standardization"], "entity_types": ["organization", "organization"]}
{"sentence": "Brazil becomes the first South American country to achieve ISO 9001 certification for its manufacturing industry, according to the International Organization for Standardization.", "entity_names": ["Brazil", "ISO 9001", "International Organization for Standardization"], "entity_types": ["location", "organization", "organization"]}
{"sentence": "The World Trade Organization reaches landmark agreement on global trade.", "entity_names": ["World Trade Organization"], "entity_types": ["organization"]}
{"sentence": "The Vienna Philharmonic Orchestra performs at British Broadcasting Corporation's annual Proms concert.", "entity_names": ["Vienna", "British Broadcasting Corporation"], "entity_types": ["location", "organization"]}
{"sentence": "Austrian Chancellor Kurz meets with World Trade Organization delegates in Vienna to discuss trade policies.", "entity_names": ["Kurz", "World Trade Organization", "Vienna"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Malaysian Prime Minister visits Kuala Lumpur.", "entity_names": ["Malaysian Prime Minister", "Kuala Lumpur"], "entity_types": ["person", "location"]}
{"sentence": "Singapore Airlines to launch new route from Kuala Lumpur to Sydney.", "entity_names": ["Singapore Airlines", "Kuala Lumpur", "Sydney"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Local university in Kuala Lumpur offers new scholarship program for international students.", "entity_names": ["Kuala Lumpur"], "entity_types": ["location"]}
{"sentence": "Berlin to impose strict lockdown measures to curb the spread of COVID-19.", "entity_names": ["Berlin"], "entity_types": ["location"]}
{"sentence": "Kate Middleton visits children's hospital to raise awareness for mental health support.", "entity_names": ["Kate Middleton"], "entity_types": ["person"]}
{"sentence": "E-commerce giant Amazon reports record-breaking sales for the holiday season.", "entity_names": ["Amazon"], "entity_types": ["organization"]}
{"sentence": "Zlatan Ibrahimovic scores hat-trick to lead team to victory.", "entity_names": ["Zlatan Ibrahimovic"], "entity_types": ["person"]}
{"sentence": "Bill Gates pledges $100 million to combat climate change.", "entity_names": ["Bill Gates"], "entity_types": ["person"]}
{"sentence": "New York City announces plan to build affordable housing in Brooklyn.", "entity_names": ["New York City", "Brooklyn"], "entity_types": ["location", "location"]}
{"sentence": "SpaceX, founded by Elon Musk, successfully launches new satellite.", "entity_names": ["SpaceX", "Elon Musk"], "entity_types": ["organization", "person"]}
{"sentence": "Elon Musk announces plans to build new Tesla manufacturing plant in Texas.", "entity_names": ["Elon Musk", "Tesla", "Texas"], "entity_types": ["person", "organization", "location"]}
{"sentence": "SpaceX's Starship, designed by Elon Musk's company, completes successful test flight.", "entity_names": ["SpaceX", "Elon Musk"], "entity_types": ["organization", "person"]}
{"sentence": "Canberra to host international climate summit next year.", "entity_names": ["Canberra"], "entity_types": ["location"]}
{"sentence": "Prime Minister of Australia travels to Canberra for emergency meeting.", "entity_names": ["Canberra"], "entity_types": ["location"]}
{"sentence": "Canberra residents protest against new housing development in the city.", "entity_names": ["Canberra"], "entity_types": ["location"]}
{"sentence": "Supreme Court Justice Stephen Breyer to retire.", "entity_names": ["Supreme Court", "Stephen Breyer"], "entity_types": ["organization", "person"]}
{"sentence": "New York City imposes indoor mask mandate.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Michael Phelps sets new world record in swimming.", "entity_names": ["Michael Phelps"], "entity_types": ["person"]}
{"sentence": "Google announces plans to establish new research center in Berlin.", "entity_names": ["Google", "Berlin"], "entity_types": ["organization", "location"]}
{"sentence": "Berlin Philharmonic Orchestra cancels upcoming concerts due to strike.", "entity_names": ["Berlin Philharmonic Orchestra"], "entity_types": ["organization"]}
{"sentence": "Robert Downey Jr. to star in new action comedy film.", "entity_names": ["Robert Downey Jr."], "entity_types": ["person"]}
{"sentence": "Sergey Lavrov meets with European Union leaders to discuss economic sanctions.", "entity_names": ["Sergey Lavrov", "European Union"], "entity_types": ["person", "organization"]}
{"sentence": "Black Lives Matter protests continue in major cities across the country.", "entity_names": ["Black Lives Matter"], "entity_types": ["organization"]}
{"sentence": "The American Civil Liberties Union fights for equal rights and social justice.", "entity_names": ["American Civil Liberties Union"], "entity_types": ["organization"]}
{"sentence": "Selena Gomez releases new single 'Baila Conmigo' featuring Rauw Alejandro.", "entity_names": ["Selena Gomez", "Rauw Alejandro"], "entity_types": ["person", "person"]}
{"sentence": "Government officials meet in Paris to discuss climate change goals.", "entity_names": ["Paris"], "entity_types": ["location"]}
{"sentence": "Scarlett Johansson signs on for new action movie", "entity_names": ["Scarlett Johansson"], "entity_types": ["person"]}
{"sentence": "Scarlett Johansson delivers powerful speech at the United Nations", "entity_names": ["Scarlett Johansson", "United Nations"], "entity_types": ["person", "organization"]}
{"sentence": "New York City to honor Scarlett Johansson with a lifetime achievement award", "entity_names": ["New York City", "Scarlett Johansson"], "entity_types": ["location", "person"]}
{"sentence": "Megan Rapinoe scores a winning goal in the Women's World Cup final.", "entity_names": ["Megan Rapinoe", "Women's World Cup"], "entity_types": ["person", "organization"]}
{"sentence": "Japan's Prime Minister Shinzo Abe meets with President Trump to discuss trade agreements.", "entity_names": ["Japan", "Shinzo Abe", "President Trump"], "entity_types": ["location", "person", "person"]}
{"sentence": "Protests erupt in Madrid following government's decision on education funding.", "entity_names": ["Madrid"], "entity_types": ["location"]}
{"sentence": "Spanish singer from Madrid to release new album next month.", "entity_names": ["Madrid"], "entity_types": ["location"]}
{"sentence": "ISTANBUL - TURKISH AIRLINES LAUNCHES NEW DIRECT FLIGHT ROUTE TO LISBON.", "entity_names": ["ISTANBUL", "TURKISH AIRLINES", "LISBON"], "entity_types": ["location", "organization", "location"]}
{"sentence": "Researchers in Lisbon make significant breakthrough in cancer treatment.", "entity_names": ["Lisbon"], "entity_types": ["location"]}
{"sentence": "ISTANBUL TO HOST INTERNATIONAL CONFERENCE ON CLIMATE CHANGE.", "entity_names": ["ISTANBUL"], "entity_types": ["location"]}
{"sentence": "Tesla Motors announces plans to open a new Gigafactory in Texas.", "entity_names": ["Tesla Motors", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "Emma Watson appointed as UN Women Goodwill Ambassador.", "entity_names": ["Emma Watson", "UN Women"], "entity_types": ["person", "organization"]}
{"sentence": "New York City sets record for highest number of daily COVID-19 cases.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Caracas protests escalate as government crackdown continues.", "entity_names": ["Caracas"], "entity_types": ["location"]}
{"sentence": "European Union announces new trade agreement with South Korea.", "entity_names": ["European Union", "South Korea"], "entity_types": ["organization", "location"]}
{"sentence": "Rising star from Caracas signs with European Union soccer club.", "entity_names": ["Caracas", "European Union"], "entity_types": ["location", "organization"]}
{"sentence": "Marco Rubio introduces bill to reform immigration policy.", "entity_names": ["Marco Rubio"], "entity_types": ["person"]}
{"sentence": "Tourists flock to the beaches of Rio de Janeiro for the annual Carnival celebration.", "entity_names": ["Rio de Janeiro"], "entity_types": ["location"]}
{"sentence": "Tel Aviv named as top destination for summer travel.", "entity_names": ["Tel Aviv"], "entity_types": ["location"]}
{"sentence": "Australian Prime Minister Scott Morrison addresses nation regarding economic recovery plan.", "entity_names": ["Australian", "Scott Morrison"], "entity_types": ["location", "person"]}
{"sentence": "New study reveals alarming increase in pollution levels in major cities.", "entity_names": [], "entity_types": []}
{"sentence": "Edinburgh to host international music festival in August.", "entity_names": ["Edinburgh"], "entity_types": ["location"]}
{"sentence": "Local chef from Edinburgh wins culinary competition.", "entity_names": ["Edinburgh"], "entity_types": ["location"]}
{"sentence": "New startup company in Edinburgh secures funding for expansion.", "entity_names": ["Edinburgh"], "entity_types": ["location"]}
{"sentence": "Warsaw hosts international conference on climate change.", "entity_names": ["Warsaw", "climate change"], "entity_types": ["location", "organization"]}
{"sentence": "Local resident from Warsaw wins prestigious science award.", "entity_names": ["Warsaw"], "entity_types": ["location"]}
{"sentence": "Warsaw stock exchange sees significant increase in trading volume.", "entity_names": ["Warsaw"], "entity_types": ["location"]}
{"sentence": "United Parcel Service announces plans to expand international shipping operations.", "entity_names": ["United Parcel Service"], "entity_types": ["organization"]}
{"sentence": "Baron Trump visits the White House with his parents.", "entity_names": ["Baron Trump", "White House"], "entity_types": ["person", "location"]}
{"sentence": "E-commerce giant partners with United Parcel Service for faster deliveries.", "entity_names": ["United Parcel Service"], "entity_types": ["organization"]}
{"sentence": "ExxonMobil announces plans to open new research center in London.", "entity_names": ["ExxonMobil", "London"], "entity_types": ["organization", "location"]}
{"sentence": "London Mayor Sadiq Khan responds to criticism over public transport funding.", "entity_names": ["London", "Sadiq Khan"], "entity_types": ["location", "person"]}
{"sentence": "ExxonMobil partners with London-based environmental organization to promote sustainable energy initiatives.", "entity_names": ["ExxonMobil", "London"], "entity_types": ["organization", "location"]}
{"sentence": "Rio de Janeiro to host international film festival next month.", "entity_names": ["Rio de Janeiro"], "entity_types": ["location"]}
{"sentence": "Local entrepreneur from Rio de Janeiro wins prestigious business award.", "entity_names": ["Rio de Janeiro"], "entity_types": ["location"]}
{"sentence": "Rio de Janeiro sees surge in tourism after lifting COVID-19 restrictions.", "entity_names": ["Rio de Janeiro"], "entity_types": ["location"]}
{"sentence": "Bernie Sanders announces plan to address climate change.", "entity_names": ["Bernie Sanders"], "entity_types": ["person"]}
{"sentence": "ISIS claims responsibility for bombing in Baghdad.", "entity_names": ["ISIS"], "entity_types": ["organization"]}
{"sentence": "Supreme Court Justice Stephen Breyer to retire at the end of the current term.", "entity_names": ["Stephen Breyer"], "entity_types": ["person"]}
{"sentence": "Flooding in Tokyo forces thousands to evacuate as heavy rains continue to pour in the region.", "entity_names": ["Tokyo"], "entity_types": ["location"]}
{"sentence": "NASA's Perseverance Rover successfully collects rock samples from Mars.", "entity_names": ["NASA", "Perseverance Rover", "Mars"], "entity_types": ["organization", "organization", "location"]}
{"sentence": "Elon Musk's SpaceX announces plans to send civilians on a trip around the moon.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "New York Yankees sign star pitcher to record-breaking contract.", "entity_names": ["New York Yankees"], "entity_types": ["organization"]}
{"sentence": "Cape Town braces for heavy rainfall and potential flooding.", "entity_names": ["Cape Town"], "entity_types": ["location"]}
{"sentence": "World Bank Group to offer financial assistance to developing nations for renewable energy projects.", "entity_names": ["World Bank Group"], "entity_types": ["organization"]}
{"sentence": "Residents of Cape Town protest against rising water bills and inadequate service delivery.", "entity_names": ["Cape Town"], "entity_types": ["location"]}
{"sentence": "Sony announces plans to open new headquarters in Riyadh.", "entity_names": ["Sony", "Riyadh"], "entity_types": ["organization", "location"]}
{"sentence": "Sydney to host 2023 international conference on climate change.", "entity_names": ["Sydney"], "entity_types": ["location"]}
{"sentence": "Saudi Arabia welcomes investment from Sony in Riyadh.", "entity_names": ["Saudi Arabia", "Sony", "Riyadh"], "entity_types": ["location", "organization", "location"]}
{"sentence": "Athens to host international conference on climate change next month.", "entity_names": ["Athens"], "entity_types": ["location"]}
{"sentence": "Ankara-based organization launches new initiative to combat poverty in the region.", "entity_names": ["Ankara-based organization"], "entity_types": ["organization"]}
{"sentence": "Vice President Mike Pence visits Barcelona to discuss economic ties.", "entity_names": ["Mike Pence", "Barcelona"], "entity_types": ["person", "location"]}
{"sentence": "Barcelona's famous La Sagrada Familia cathedral reopens to tourists after lockdown.", "entity_names": ["Barcelona"], "entity_types": ["location"]}
{"sentence": "Former Barcelona player Lionel Messi signs with Paris Saint-Germain.", "entity_names": ["Lionel Messi", "Paris Saint-Germain"], "entity_types": ["person", "organization"]}
{"sentence": "Kim Kardashian launches new beauty line.", "entity_names": ["Kim Kardashian"], "entity_types": ["person"]}
{"sentence": "Kim Kardashian spotted in New York City with her children.", "entity_names": ["Kim Kardashian", "New York City"], "entity_types": ["person", "location"]}
{"sentence": "Kim Kardashian's clothing brand reaches $1 billion in sales.", "entity_names": ["Kim Kardashian"], "entity_types": ["person"]}
{"sentence": "The Food and Drug Administration approves Pfizer Inc.'s new cancer drug.", "entity_names": ["Food and Drug Administration", "Pfizer Inc."], "entity_types": ["organization", "organization"]}
{"sentence": "Pfizer Inc. announces plans to acquire a new biotech company for $5 billion.", "entity_names": ["Pfizer Inc."], "entity_types": ["organization"]}
{"sentence": "A former employee of the Food and Drug Administration reveals safety concerns about a popular pharmaceutical company's products.", "entity_names": ["Food and Drug Administration"], "entity_types": ["organization"]}
{"sentence": "Pope Francis visits refugee camp in Greece.", "entity_names": ["Pope Francis", "Greece"], "entity_types": ["person", "location"]}
{"sentence": "New York City Mayor meets with Pope Francis at Vatican.", "entity_names": ["New York City", "Pope Francis", "Vatican"], "entity_types": ["location", "person", "location"]}
{"sentence": "Pope Francis calls for peace in war-torn region.", "entity_names": ["Pope Francis"], "entity_types": ["person"]}
{"sentence": "Earthquake in Manila leaves hundreds homeless.", "entity_names": ["Manila"], "entity_types": ["location"]}
{"sentence": "Apple Inc. announces plans to open a new office in Manila.", "entity_names": ["Apple Inc.", "Manila"], "entity_types": ["organization", "location"]}
{"sentence": "President Duterte of Philippines meets with leaders in Manila to discuss economic reforms.", "entity_names": ["Duterte", "Philippines", "Manila"], "entity_types": ["person", "location", "location"]}
{"sentence": "Prince William visits Warsaw for official state visit.", "entity_names": ["Prince William", "Warsaw"], "entity_types": ["person", "location"]}
{"sentence": "Unrest in Beirut leads to government crackdown on protesters.", "entity_names": ["Beirut"], "entity_types": ["location"]}
{"sentence": "The mayor of Rio de Janeiro announces new infrastructure plan for the city.", "entity_names": ["Rio de Janeiro"], "entity_types": ["location"]}
{"sentence": "Local organization in Rio de Janeiro holds fundraising event for homeless population.", "entity_names": ["organization", "Rio de Janeiro"], "entity_types": ["organization", "location"]}
{"sentence": "Tom Hanks to star in new film set in ancient Rome.", "entity_names": ["Tom Hanks", "Rome"], "entity_types": ["person", "location"]}
{"sentence": "Rome to impose new restrictions on outdoor dining due to COVID-19 surge.", "entity_names": ["Rome"], "entity_types": ["location"]}
{"sentence": "Local organization to host charity event featuring celebrity guest Tom Hanks.", "entity_names": ["Tom Hanks"], "entity_types": ["person"]}
{"sentence": "The Food and Agriculture Organization reported a 10% increase in global wheat production this year.", "entity_names": ["Food and Agriculture Organization"], "entity_types": ["organization"]}
{"sentence": "The International Olympic Committee announced that the 2024 Summer Games will be hosted in Paris.", "entity_names": ["International Olympic Committee", "Paris"], "entity_types": ["organization", "location"]}
{"sentence": "The Food and Agriculture Organization released a statement urging countries to address the issue of food security in the face of climate change.", "entity_names": ["Food and Agriculture Organization"], "entity_types": ["organization"]}
{"sentence": "Xi Jinping delivers speech on economic reform at World Economic Forum.", "entity_names": ["Xi Jinping", "World Economic Forum"], "entity_types": ["person", "organization"]}
{"sentence": "China and US to hold trade talks next month, says Xi Jinping.", "entity_names": ["China", "US", "Xi Jinping"], "entity_types": ["location", "location", "person"]}
{"sentence": "Hillary Clinton delivers keynote address at annual technology conference.", "entity_names": ["Hillary Clinton"], "entity_types": ["person"]}
{"sentence": "Investigation reveals ties between foreign organization and Hillary Clinton campaign.", "entity_names": ["Hillary Clinton"], "entity_types": ["person"]}
{"sentence": "Hillary Clinton speaks at fundraiser for environmental organization.", "entity_names": ["Hillary Clinton"], "entity_types": ["person"]}
{"sentence": "Johnson & Johnson to pay $572 million in landmark opioid trial settlement.", "entity_names": ["Johnson & Johnson"], "entity_types": ["organization"]}
{"sentence": "New York City Mayor announces new initiative to tackle affordable housing crisis.", "entity_names": ["New York City", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "Tesla Inc. CEO Elon Musk unveils plans for new electric vehicle model.", "entity_names": ["Tesla Inc.", "Elon Musk"], "entity_types": ["organization", "person"]}
{"sentence": "Annual earnings report shows significant growth for Microsoft.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}
{"sentence": "New Microsoft Office app launches on iOS and Android devices.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}
{"sentence": "Microsoft CEO Satya Nadella to speak at tech conference next week.", "entity_names": ["Microsoft", "Satya Nadella"], "entity_types": ["organization", "person"]}
{"sentence": "Meghan Markle launches initiative in collaboration with United Nations Children's Fund.", "entity_names": ["Meghan Markle", "United Nations Children's Fund"], "entity_types": ["person", "organization"]}
{"sentence": "United Nations Children's Fund provides aid to refugees in war-torn region.", "entity_names": ["United Nations Children's Fund"], "entity_types": ["organization"]}
{"sentence": "Meghan Markle speaks out against gender-based violence at United Nations event.", "entity_names": ["Meghan Markle", "United Nations"], "entity_types": ["person", "organization"]}
{"sentence": "Dwayne \"The Rock\" Johnson to star in new action movie.", "entity_names": ["Dwayne \"The Rock\" Johnson"], "entity_types": ["person"]}
{"sentence": "New York City announces plans for major subway expansion.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Apple Inc. unveils latest iPhone model with improved camera technology.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Tesla launches new electric car model in California.", "entity_names": ["Tesla", "California"], "entity_types": ["organization", "location"]}
{"sentence": "Celine Dion to start residency in Las Vegas.", "entity_names": ["Celine Dion", "Las Vegas"], "entity_types": ["person", "location"]}
{"sentence": "Apple announces partnership with Microsoft for new software development.", "entity_names": ["Apple", "Microsoft"], "entity_types": ["organization", "organization"]}
{"sentence": "Heavy snowfall in London causes significant travel disruptions.", "entity_names": ["London"], "entity_types": ["location"]}
{"sentence": "Kate Middleton visits children's hospital to meet young patients.", "entity_names": ["Kate Middleton"], "entity_types": ["person"]}
{"sentence": "The European Union member states agree on a new trade deal with South American countries.", "entity_names": ["European Union"], "entity_types": ["organization"]}
{"sentence": "Discussions between European Union leaders and the United Kingdom on Brexit negotiations result in a new agreement.", "entity_names": ["European Union", "United Kingdom"], "entity_types": ["organization", "location"]}
{"sentence": "The Red Cross is providing aid to the victims of the natural disaster.", "entity_names": ["Red Cross"], "entity_types": ["organization"]}
{"sentence": "The Red Cross volunteer, Sarah Johnson, traveled to Haiti to assist with relief efforts.", "entity_names": ["Red Cross", "Sarah Johnson", "Haiti"], "entity_types": ["organization", "person", "location"]}
{"sentence": "Donations to the Red Cross have exceeded $1 million in support of disaster relief.", "entity_names": ["Red Cross"], "entity_types": ["organization"]}
{"sentence": "Indian Prime Minister Narendra Modi addresses the United Nations General Assembly.", "entity_names": ["Narendra Modi", "United Nations General Assembly"], "entity_types": ["person", "organization"]}
{"sentence": "Protests erupt in Delhi after visit of Narendra Modi.", "entity_names": ["Delhi", "Narendra Modi"], "entity_types": ["location", "person"]}
{"sentence": "Narendra Modi announces new economic reforms in India.", "entity_names": ["Narendra Modi", "India"], "entity_types": ["person", "location"]}
{"sentence": "Cape Town experiences record-breaking heat wave as temperatures soar above 40 degrees Celsius.", "entity_names": ["Cape Town"], "entity_types": ["location"]}
{"sentence": "Local organization in Cape Town launches campaign to combat plastic pollution in the city's oceans.", "entity_names": ["Cape Town"], "entity_types": ["location"]}
{"sentence": "Famous musician from Cape Town wins four awards at the international music festival.", "entity_names": ["Cape Town"], "entity_types": ["location"]}
{"sentence": "Elon Musk's SpaceX announces plans to launch manned mission to Mars by 2024.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "U.S. economy shows signs of recovery as jobless claims drop to lowest level in five years.", "entity_names": ["U.S."], "entity_types": ["location"]}
{"sentence": "Istanbul experiences record-breaking heatwave.", "entity_names": ["Istanbul"], "entity_types": ["location"]}
{"sentence": "United States Department of State issues travel advisory for Mexico.", "entity_names": ["United States Department of State", "Mexico"], "entity_types": ["organization", "location"]}
{"sentence": "Tourist bus crash in Istanbul leaves several injured.", "entity_names": ["Istanbul"], "entity_types": ["location"]}
{"sentence": "Tesla announces plans to expand production in China.", "entity_names": ["Tesla", "China"], "entity_types": ["organization", "location"]}
{"sentence": "Former President Obama delivers speech at climate change conference.", "entity_names": ["Obama"], "entity_types": ["person"]}
{"sentence": "Wildfires continue to ravage parts of California.", "entity_names": ["California"], "entity_types": ["location"]}
