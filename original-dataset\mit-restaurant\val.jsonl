{"sentence": "find me a coffee shop", "tokens": ["find", "me", "a", "coffee", "shop"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "where can i find decent price crust near sutherland road", "tokens": ["where", "can", "i", "find", "decent", "price", "crust", "near", "sutherland", "road"], "ner_tags": ["O", "O", "O", "O", "B-Price", "O", "B-Dish", "B-Location", "I-Location", "I-Location"]}
{"sentence": "what time does dunkin donuts open", "tokens": ["what", "time", "does", "dunkin", "donuts", "open"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "is there a place that sells onion ring in the holbrook area", "tokens": ["is", "there", "a", "place", "that", "sells", "onion", "ring", "in", "the", "holbrook", "area"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "I-Dish", "O", "O", "B-Location", "I-Location"]}
{"sentence": "locate burger restaurant", "tokens": ["locate", "burger", "restaurant"], "ner_tags": ["O", "B-Cuisine", "O"]}
{"sentence": "what is the highest rated italian restaurant within fifteen miles of here", "tokens": ["what", "is", "the", "highest", "rated", "italian", "restaurant", "within", "fifteen", "miles", "of", "here"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "i need a pizza place close by that serves beer", "tokens": ["i", "need", "a", "pizza", "place", "close", "by", "that", "serves", "beer"], "ner_tags": ["O", "O", "O", "B-Dish", "O", "B-Location", "I-Location", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "can i use my credit card at the greek lunch truck", "tokens": ["can", "i", "use", "my", "credit", "card", "at", "the", "greek", "lunch", "truck"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Cuisine", "I-Cuisine", "I-Cuisine"]}
{"sentence": "where on exit 26 a on i 40 can i get a steak", "tokens": ["where", "on", "exit", "26", "a", "on", "i", "40", "can", "i", "get", "a", "steak"], "ner_tags": ["O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "would you mind finding me a restaurant that has a kid friendly atmosphere with possible tourist opportunities", "tokens": ["would", "you", "mind", "finding", "me", "a", "restaurant", "that", "has", "a", "kid", "friendly", "atmosphere", "with", "possible", "tourist", "opportunities"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "help me locate a mexican restaurant", "tokens": ["help", "me", "locate", "a", "mexican", "restaurant"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "find the best rated mexican restaurant that is nearby", "tokens": ["find", "the", "best", "rated", "mexican", "restaurant", "that", "is", "nearby"], "ner_tags": ["O", "O", "B-Rating", "O", "B-Cuisine", "O", "O", "O", "B-Location"]}
{"sentence": "id like to find a chinese restaurant nearby that delivers", "tokens": ["id", "like", "to", "find", "a", "chinese", "restaurant", "nearby", "that", "delivers"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Location", "O", "B-Amenity"]}
{"sentence": "whats the best burger place in northeast houston texas", "tokens": ["whats", "the", "best", "burger", "place", "in", "northeast", "houston", "texas"], "ner_tags": ["O", "O", "B-Rating", "B-Dish", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "can you make reservations for 2 at lees fishhouse in myrlte beach south carolina", "tokens": ["can", "you", "make", "reservations", "for", "2", "at", "lees", "fishhouse", "in", "myrlte", "beach", "south", "carolina"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "i would like to eat at a mexican restaurant that has a smoking area", "tokens": ["i", "would", "like", "to", "eat", "at", "a", "mexican", "restaurant", "that", "has", "a", "smoking", "area"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where can i find a lunch buffet", "tokens": ["where", "can", "i", "find", "a", "lunch", "buffet"], "ner_tags": ["O", "O", "O", "O", "O", "B-Hours", "B-Cuisine"]}
{"sentence": "where can i go that has easy parking and soup", "tokens": ["where", "can", "i", "go", "that", "has", "easy", "parking", "and", "soup"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Dish"]}
{"sentence": "arrange my most frequented diners in terms of average price", "tokens": ["arrange", "my", "most", "frequented", "diners", "in", "terms", "of", "average", "price"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Price", "O"]}
{"sentence": "do any of the restaurants in this county allow smoking at the bar", "tokens": ["do", "any", "of", "the", "restaurants", "in", "this", "county", "allow", "smoking", "at", "the", "bar"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "can you direct me to the closest pizzeria", "tokens": ["can", "you", "direct", "me", "to", "the", "closest", "pizzeria"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "B-Cuisine"]}
{"sentence": "im in the mood for chicken tikka masala tonight", "tokens": ["im", "in", "the", "mood", "for", "chicken", "tikka", "masala", "tonight"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "I-Dish", "I-Dish", "O"]}
{"sentence": "can you find an expensive martins home bakery with a parking lot", "tokens": ["can", "you", "find", "an", "expensive", "martins", "home", "bakery", "with", "a", "parking", "lot"], "ner_tags": ["O", "O", "O", "O", "B-Price", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does the cheesecake factory have good reviews", "tokens": ["does", "the", "cheesecake", "factory", "have", "good", "reviews"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Rating", "O"]}
{"sentence": "is there an ice cream store within 5 miles", "tokens": ["is", "there", "an", "ice", "cream", "store", "within", "5", "miles"], "ner_tags": ["O", "O", "O", "B-Dish", "I-Dish", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "is there a cajun restaurant with drivethru frozen margaritas that has high ratings for fried pickles and gator tail", "tokens": ["is", "there", "a", "cajun", "restaurant", "with", "drivethru", "frozen", "margaritas", "that", "has", "high", "ratings", "for", "fried", "pickles", "and", "gator", "tail"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Amenity", "B-Dish", "I-Dish", "O", "O", "B-Rating", "I-Rating", "O", "B-Dish", "I-Dish", "O", "B-Dish", "I-Dish"]}
{"sentence": "i am hungry tell me where i can find a restaurant within ten miles", "tokens": ["i", "am", "hungry", "tell", "me", "where", "i", "can", "find", "a", "restaurant", "within", "ten", "miles"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "is there a taco bell within ten miles", "tokens": ["is", "there", "a", "taco", "bell", "within", "ten", "miles"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location"]}
{"sentence": "wheres the closest restaurant with free wifi", "tokens": ["wheres", "the", "closest", "restaurant", "with", "free", "wifi"], "ner_tags": ["O", "O", "B-Location", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "can you help me find a cheap place for a lot of people to eat", "tokens": ["can", "you", "help", "me", "find", "a", "cheap", "place", "for", "a", "lot", "of", "people", "to", "eat"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Price", "I-Price", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "is there a greek restaurant close by", "tokens": ["is", "there", "a", "greek", "restaurant", "close", "by"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "is there a thai restaurant nearby", "tokens": ["is", "there", "a", "thai", "restaurant", "nearby"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "do you know where i can find some good appetizers", "tokens": ["do", "you", "know", "where", "i", "can", "find", "some", "good", "appetizers"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Rating", "B-Dish"]}
{"sentence": "show me the quickest route to red robin", "tokens": ["show", "me", "the", "quickest", "route", "to", "red", "robin"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where can i get some sushi in the area", "tokens": ["where", "can", "i", "get", "some", "sushi", "in", "the", "area"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "O", "O", "B-Location"]}
{"sentence": "where is a restaurant that has a smoking area", "tokens": ["where", "is", "a", "restaurant", "that", "has", "a", "smoking", "area"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "any unique foods here", "tokens": ["any", "unique", "foods", "here"], "ner_tags": ["O", "B-Cuisine", "O", "O"]}
{"sentence": "im looking for an italian restaurant with a good wine list", "tokens": ["im", "looking", "for", "an", "italian", "restaurant", "with", "a", "good", "wine", "list"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "where is there a mcdonalds", "tokens": ["where", "is", "there", "a", "mcdonalds"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "what hours are the olive garden open", "tokens": ["what", "hours", "are", "the", "olive", "garden", "open"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "where can i get a good price on two dozen bagels", "tokens": ["where", "can", "i", "get", "a", "good", "price", "on", "two", "dozen", "bagels"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "O", "O", "B-Dish", "I-Dish", "I-Dish"]}
{"sentence": "do you know if the pizza shop on tribeca takes credit card", "tokens": ["do", "you", "know", "if", "the", "pizza", "shop", "on", "tribeca", "takes", "credit", "card"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "wheres the nearest place to eat", "tokens": ["wheres", "the", "nearest", "place", "to", "eat"], "ner_tags": ["O", "O", "B-Location", "O", "O", "O"]}
{"sentence": "make a reservation at bouchon for me", "tokens": ["make", "a", "reservation", "at", "bouchon", "for", "me"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "O", "O"]}
{"sentence": "how far away are the best doughnuts within a 15 mile radius", "tokens": ["how", "far", "away", "are", "the", "best", "doughnuts", "within", "a", "15", "mile", "radius"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Dish", "B-Location", "I-Location", "I-Location", "I-Location", "O"]}
{"sentence": "help me find a restaurant with a gluten free menu with organic food that is pet friendly", "tokens": ["help", "me", "find", "a", "restaurant", "with", "a", "gluten", "free", "menu", "with", "organic", "food", "that", "is", "pet", "friendly"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "can you please tell me the names and phone numbers of any kid friendly restaurants within five miles", "tokens": ["can", "you", "please", "tell", "me", "the", "names", "and", "phone", "numbers", "of", "any", "kid", "friendly", "restaurants", "within", "five", "miles"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "is there a long john silvers nearby", "tokens": ["is", "there", "a", "long", "john", "silvers", "nearby"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Location"]}
{"sentence": "can you suggest an italian restaurant", "tokens": ["can", "you", "suggest", "an", "italian", "restaurant"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "find me an arbys", "tokens": ["find", "me", "an", "arbys"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "can i have a list of restaurants that serve italian food", "tokens": ["can", "i", "have", "a", "list", "of", "restaurants", "that", "serve", "italian", "food"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "i want to know where the nearest mcdonald is", "tokens": ["i", "want", "to", "know", "where", "the", "nearest", "mcdonald", "is"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "B-Restaurant_Name", "O"]}
{"sentence": "whats the best place to eat", "tokens": ["whats", "the", "best", "place", "to", "eat"], "ner_tags": ["O", "O", "B-Rating", "O", "O", "O"]}
{"sentence": "where can i get fantastic brisket at 1 pm", "tokens": ["where", "can", "i", "get", "fantastic", "brisket", "at", "1", "pm"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "B-Dish", "O", "B-Hours", "I-Hours"]}
{"sentence": "need a private room to hold 200 people", "tokens": ["need", "a", "private", "room", "to", "hold", "200", "people"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "i am looking for a restaurant with tex mex cuisine", "tokens": ["i", "am", "looking", "for", "a", "restaurant", "with", "tex", "mex", "cuisine"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "im looking for a vegan restaurant with a laid back feel", "tokens": ["im", "looking", "for", "a", "vegan", "restaurant", "with", "a", "laid", "back", "feel"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "im in the mood for a three star or four star restaurant so what cuisine choices do i have and where are they", "tokens": ["im", "in", "the", "mood", "for", "a", "three", "star", "or", "four", "star", "restaurant", "so", "what", "cuisine", "choices", "do", "i", "have", "and", "where", "are", "they"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Rating", "I-Rating", "I-Rating", "I-Rating", "I-Rating", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what is the cheapest hot dog in town", "tokens": ["what", "is", "the", "cheapest", "hot", "dog", "in", "town"], "ner_tags": ["O", "O", "O", "B-Price", "B-Dish", "I-Dish", "B-Location", "I-Location"]}
{"sentence": "where can i go to have a nice dinner for two that serves halal food", "tokens": ["where", "can", "i", "go", "to", "have", "a", "nice", "dinner", "for", "two", "that", "serves", "halal", "food"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "B-Hours", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "is there a mcdonalds on main street", "tokens": ["is", "there", "a", "mcdonalds", "on", "main", "street"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "fine a fast food place that serves hamburgers", "tokens": ["fine", "a", "fast", "food", "place", "that", "serves", "hamburgers"], "ner_tags": ["O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "O", "B-Dish"]}
{"sentence": "car can you please call a french place on south ave", "tokens": ["car", "can", "you", "please", "call", "a", "french", "place", "on", "south", "ave"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Location", "I-Location"]}
{"sentence": "which restaurants in canton ohio are five star", "tokens": ["which", "restaurants", "in", "canton", "ohio", "are", "five", "star"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "O", "B-Rating", "I-Rating"]}
{"sentence": "im looking for the closest mexican restaurant", "tokens": ["im", "looking", "for", "the", "closest", "mexican", "restaurant"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "how many pizza places are there within a mile from me", "tokens": ["how", "many", "pizza", "places", "are", "there", "within", "a", "mile", "from", "me"], "ner_tags": ["O", "O", "B-Dish", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O"]}
{"sentence": "try to find a drive in", "tokens": ["try", "to", "find", "a", "drive", "in"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where should i go for romance", "tokens": ["where", "should", "i", "go", "for", "romance"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity"]}
{"sentence": "are there any places nearby with a variety of diabetic friendly entree choices", "tokens": ["are", "there", "any", "places", "nearby", "with", "a", "variety", "of", "diabetic", "friendly", "entree", "choices"], "ner_tags": ["O", "O", "O", "O", "B-Location", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "B-Dish", "I-Dish"]}
{"sentence": "need a quick cheap place to eat within the next five miles", "tokens": ["need", "a", "quick", "cheap", "place", "to", "eat", "within", "the", "next", "five", "miles"], "ner_tags": ["O", "O", "B-Amenity", "B-Price", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "is there a kentucky fried chicken near the lloyd center", "tokens": ["is", "there", "a", "kentucky", "fried", "chicken", "near", "the", "lloyd", "center"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Location", "I-Location"]}
{"sentence": "can you tell me where a great chinese buffet is at", "tokens": ["can", "you", "tell", "me", "where", "a", "great", "chinese", "buffet", "is", "at"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Rating", "B-Cuisine", "B-Amenity", "O", "O"]}
{"sentence": "is there an irish place with a view nearby", "tokens": ["is", "there", "an", "irish", "place", "with", "a", "view", "nearby"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "B-Location"]}
{"sentence": "where can i find cheap chinese food", "tokens": ["where", "can", "i", "find", "cheap", "chinese", "food"], "ner_tags": ["O", "O", "O", "O", "B-Price", "B-Cuisine", "O"]}
{"sentence": "we have a large group and are looking for a mexican restaurant that can seat us", "tokens": ["we", "have", "a", "large", "group", "and", "are", "looking", "for", "a", "mexican", "restaurant", "that", "can", "seat", "us"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "O"]}
{"sentence": "where can i get breakfast nearby that serves basil dishes", "tokens": ["where", "can", "i", "get", "breakfast", "nearby", "that", "serves", "basil", "dishes"], "ner_tags": ["O", "O", "O", "O", "B-Hours", "B-Location", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "i want to get some drinks and also some good food where can i go", "tokens": ["i", "want", "to", "get", "some", "drinks", "and", "also", "some", "good", "food", "where", "can", "i", "go"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "O", "O", "O", "B-Rating", "O", "O", "O", "O", "O"]}
{"sentence": "i want to find a really nice restaurant that serves an early bird special", "tokens": ["i", "want", "to", "find", "a", "really", "nice", "restaurant", "that", "serves", "an", "early", "bird", "special"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "does amalfis in glen cove have a kids menu", "tokens": ["does", "amalfis", "in", "glen", "cove", "have", "a", "kids", "menu"], "ner_tags": ["O", "B-Restaurant_Name", "O", "B-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "are there any restaurants in this city with a dining room that have a great view of the night skyline", "tokens": ["are", "there", "any", "restaurants", "in", "this", "city", "with", "a", "dining", "room", "that", "have", "a", "great", "view", "of", "the", "night", "skyline"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "does cincinnati have any fondue restaurants", "tokens": ["does", "cincinnati", "have", "any", "fondue", "restaurants"], "ner_tags": ["O", "B-Location", "O", "O", "B-Cuisine", "O"]}
{"sentence": "where is there a fancy place for dinner", "tokens": ["where", "is", "there", "a", "fancy", "place", "for", "dinner"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "O", "O", "B-Hours"]}
{"sentence": "find a fine dining restaurant", "tokens": ["find", "a", "fine", "dining", "restaurant"], "ner_tags": ["O", "O", "B-Cuisine", "B-Amenity", "O"]}
{"sentence": "is far east restaurant open late", "tokens": ["is", "far", "east", "restaurant", "open", "late"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Hours", "I-Hours"]}
{"sentence": "where can i get a churro", "tokens": ["where", "can", "i", "get", "a", "churro"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "is the a wok kitchen with prix fixe menus along my route", "tokens": ["is", "the", "a", "wok", "kitchen", "with", "prix", "fixe", "menus", "along", "my", "route"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity", "I-Amenity", "B-Location", "I-Location", "I-Location"]}
{"sentence": "maybe i need something for my children and wife that is open right after school what do we have here claras diner perfect", "tokens": ["maybe", "i", "need", "something", "for", "my", "children", "and", "wife", "that", "is", "open", "right", "after", "school", "what", "do", "we", "have", "here", "claras", "diner", "perfect"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "find me an italian restaurant within 2 miles of my house", "tokens": ["find", "me", "an", "italian", "restaurant", "within", "2", "miles", "of", "my", "house"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "does the olive garden accept reservations", "tokens": ["does", "the", "olive", "garden", "accept", "reservations"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Amenity", "I-Amenity"]}
{"sentence": "where can i get some tostadas", "tokens": ["where", "can", "i", "get", "some", "tostadas"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "find the cheese cake factory", "tokens": ["find", "the", "cheese", "cake", "factory"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "find me a 5 star rated thai restaurant", "tokens": ["find", "me", "a", "5", "star", "rated", "thai", "restaurant"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "I-Rating", "B-Cuisine", "O"]}
{"sentence": "need breakfast wheres an ihop", "tokens": ["need", "breakfast", "wheres", "an", "ihop"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "im looking for a place to near near me", "tokens": ["im", "looking", "for", "a", "place", "to", "near", "near", "me"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "i want a chocolate chip ice cream shake", "tokens": ["i", "want", "a", "chocolate", "chip", "ice", "cream", "shake"], "ner_tags": ["O", "O", "O", "B-Dish", "I-Dish", "I-Dish", "I-Dish", "I-Dish"]}
{"sentence": "what is a good coffee place near here", "tokens": ["what", "is", "a", "good", "coffee", "place", "near", "here"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Dish", "O", "B-Location", "I-Location"]}
{"sentence": "reservations for sushi", "tokens": ["reservations", "for", "sushi"], "ner_tags": ["B-Amenity", "O", "B-Dish"]}
{"sentence": "list all of the burger joints on this side of town", "tokens": ["list", "all", "of", "the", "burger", "joints", "on", "this", "side", "of", "town"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "is there a romeros restaurant within mile that is open all week", "tokens": ["is", "there", "a", "romeros", "restaurant", "within", "mile", "that", "is", "open", "all", "week"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location", "O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "does chilis have carryout", "tokens": ["does", "chilis", "have", "carryout"], "ner_tags": ["O", "B-Restaurant_Name", "O", "O"]}
{"sentence": "is there a good chinese restaurant open at midnight", "tokens": ["is", "there", "a", "good", "chinese", "restaurant", "open", "at", "midnight"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "of the three star restaurants in a 5 mile radius which one is the cheapest", "tokens": ["of", "the", "three", "star", "restaurants", "in", "a", "5", "mile", "radius", "which", "one", "is", "the", "cheapest"], "ner_tags": ["O", "O", "B-Rating", "I-Rating", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "O", "B-Price"]}
{"sentence": "get the phone number of a chinese take out restaurant", "tokens": ["get", "the", "phone", "number", "of", "a", "chinese", "take", "out", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "I-Cuisine", "O"]}
{"sentence": "i am in the mood for tacos where can i find some", "tokens": ["i", "am", "in", "the", "mood", "for", "tacos", "where", "can", "i", "find", "some"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "O", "O", "O", "O", "O"]}
{"sentence": "searching for a classy greek restaurant", "tokens": ["searching", "for", "a", "classy", "greek", "restaurant"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "where is the nearest malaysian restaurant", "tokens": ["where", "is", "the", "nearest", "malaysian", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "what time does armans open", "tokens": ["what", "time", "does", "armans", "open"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O"]}
{"sentence": "when does dominos close", "tokens": ["when", "does", "dominos", "close"], "ner_tags": ["O", "O", "B-Restaurant_Name", "O"]}
{"sentence": "which restaurant is closest to our house", "tokens": ["which", "restaurant", "is", "closest", "to", "our", "house"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "find restaurant with average meal under 15 dollars", "tokens": ["find", "restaurant", "with", "average", "meal", "under", "15", "dollars"], "ner_tags": ["O", "O", "O", "B-Price", "I-Price", "I-Price", "I-Price", "I-Price"]}
{"sentence": "where can i get something to eat fast", "tokens": ["where", "can", "i", "get", "something", "to", "eat", "fast"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Cuisine"]}
{"sentence": "take me to the nearest fast food place", "tokens": ["take", "me", "to", "the", "nearest", "fast", "food", "place"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "do you know the health ratings of restaurants", "tokens": ["do", "you", "know", "the", "health", "ratings", "of", "restaurants"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "I-Rating", "O", "O"]}
{"sentence": "is there a dress code for lepages", "tokens": ["is", "there", "a", "dress", "code", "for", "lepages"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Restaurant_Name"]}
{"sentence": "what else besides sushi does kangs serve", "tokens": ["what", "else", "besides", "sushi", "does", "kangs", "serve"], "ner_tags": ["O", "O", "O", "B-Dish", "O", "B-Restaurant_Name", "O"]}
{"sentence": "where is the highest ranked fish place in the city", "tokens": ["where", "is", "the", "highest", "ranked", "fish", "place", "in", "the", "city"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "B-Cuisine", "O", "O", "O", "B-Location"]}
{"sentence": "know of any high class kid friendly restaurants in the area", "tokens": ["know", "of", "any", "high", "class", "kid", "friendly", "restaurants", "in", "the", "area"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "im looking for a restaurant serving chinese", "tokens": ["im", "looking", "for", "a", "restaurant", "serving", "chinese"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine"]}
{"sentence": "late night spot", "tokens": ["late", "night", "spot"], "ner_tags": ["B-Hours", "I-Hours", "O"]}
{"sentence": "does oriental kitchen on highwood drive have reasonable prices", "tokens": ["does", "oriental", "kitchen", "on", "highwood", "drive", "have", "reasonable", "prices"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "O", "B-Price", "O"]}
{"sentence": "what kind of food is offered at santa fe", "tokens": ["what", "kind", "of", "food", "is", "offered", "at", "santa", "fe"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "get me to the nearest ice cream parlor", "tokens": ["get", "me", "to", "the", "nearest", "ice", "cream", "parlor"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "I-Cuisine"]}
{"sentence": "can you find me a burger king thats open right now", "tokens": ["can", "you", "find", "me", "a", "burger", "king", "thats", "open", "right", "now"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "is there a family owned burger joint within 5 miles", "tokens": ["is", "there", "a", "family", "owned", "burger", "joint", "within", "5", "miles"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "B-Cuisine", "I-Cuisine", "B-Location", "I-Location", "I-Location"]}
{"sentence": "can i have the phone number to applebees on atlantic avenue", "tokens": ["can", "i", "have", "the", "phone", "number", "to", "applebees", "on", "atlantic", "avenue"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "are there any 24 hour diners near by", "tokens": ["are", "there", "any", "24", "hour", "diners", "near", "by"], "ner_tags": ["O", "O", "O", "B-Hours", "I-Hours", "B-Cuisine", "B-Location", "I-Location"]}
{"sentence": "could you find me a place to eat that is low priced and has great service", "tokens": ["could", "you", "find", "me", "a", "place", "to", "eat", "that", "is", "low", "priced", "and", "has", "great", "service"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Price", "O", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "is there a high end bar or restaurant open 24 hours", "tokens": ["is", "there", "a", "high", "end", "bar", "or", "restaurant", "open", "24", "hours"], "ner_tags": ["O", "O", "O", "B-Price", "I-Price", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "although a good burger would be good time for burger king", "tokens": ["although", "a", "good", "burger", "would", "be", "good", "time", "for", "burger", "king"], "ner_tags": ["O", "O", "B-Rating", "B-Dish", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "can you help me find great food at a long john silvers that is open late", "tokens": ["can", "you", "help", "me", "find", "great", "food", "at", "a", "long", "john", "silvers", "that", "is", "open", "late"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Hours", "I-Hours"]}
{"sentence": "local chinese with smoking area", "tokens": ["local", "chinese", "with", "smoking", "area"], "ner_tags": ["B-Location", "B-Cuisine", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "which restaurants are the closest to the airport", "tokens": ["which", "restaurants", "are", "the", "closest", "to", "the", "airport"], "ner_tags": ["O", "O", "O", "O", "B-Location", "O", "O", "B-Location"]}
{"sentence": "who serves quiche lorraine", "tokens": ["who", "serves", "quiche", "lorraine"], "ner_tags": ["O", "O", "B-Dish", "I-Dish"]}
{"sentence": "is the indian restaurant downtown cheaper than the indian restaurant uptown", "tokens": ["is", "the", "indian", "restaurant", "downtown", "cheaper", "than", "the", "indian", "restaurant", "uptown"], "ner_tags": ["O", "O", "B-Cuisine", "O", "B-Location", "B-Price", "O", "O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "can you give me directions to the closet coffee shop", "tokens": ["can", "you", "give", "me", "directions", "to", "the", "closet", "coffee", "shop"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "wheres the nearest restaurant with live jazz music", "tokens": ["wheres", "the", "nearest", "restaurant", "with", "live", "jazz", "music"], "ner_tags": ["O", "O", "B-Location", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "burger king hours nearby", "tokens": ["burger", "king", "hours", "nearby"], "ner_tags": ["B-Restaurant_Name", "I-Restaurant_Name", "B-Hours", "B-Location"]}
{"sentence": "i need an el pollo loco with catering service", "tokens": ["i", "need", "an", "el", "pollo", "loco", "with", "catering", "service"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i want a big burger with all the fixings", "tokens": ["i", "want", "a", "big", "burger", "with", "all", "the", "fixings"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "O", "O", "O", "B-Dish"]}
{"sentence": "where is the nearest 5 star restaurant", "tokens": ["where", "is", "the", "nearest", "5", "star", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Rating", "I-Rating", "O"]}
{"sentence": "id like to eat lunch at a mexican restaurant", "tokens": ["id", "like", "to", "eat", "lunch", "at", "a", "mexican", "restaurant"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Cuisine", "O"]}
{"sentence": "i want a cheap place i can have a bear and a smoke", "tokens": ["i", "want", "a", "cheap", "place", "i", "can", "have", "a", "bear", "and", "a", "smoke"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "where can i find a good milkshake in the next ten minutes", "tokens": ["where", "can", "i", "find", "a", "good", "milkshake", "in", "the", "next", "ten", "minutes"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Dish", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "im looking for a cheap cambodian place in south end", "tokens": ["im", "looking", "for", "a", "cheap", "cambodian", "place", "in", "south", "end"], "ner_tags": ["O", "O", "O", "O", "B-Price", "B-Cuisine", "B-Location", "O", "B-Location", "I-Location"]}
{"sentence": "find me a one stop shop that is within ten miles of my location", "tokens": ["find", "me", "a", "one", "stop", "shop", "that", "is", "within", "ten", "miles", "of", "my", "location"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O"]}
{"sentence": "when does the tibetan food restaurant in berkeley ca close", "tokens": ["when", "does", "the", "tibetan", "food", "restaurant", "in", "berkeley", "ca", "close"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Location", "I-Location", "O"]}
{"sentence": "what is subway phone number", "tokens": ["what", "is", "subway", "phone", "number"], "ner_tags": ["O", "O", "B-Restaurant_Name", "O", "O"]}
{"sentence": "closest italian restaurant", "tokens": ["closest", "italian", "restaurant"], "ner_tags": ["B-Location", "B-Cuisine", "O"]}
{"sentence": "where can i find a chinese restaurant", "tokens": ["where", "can", "i", "find", "a", "chinese", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "where can i get some fettuccine", "tokens": ["where", "can", "i", "get", "some", "fettuccine"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "what is the closest fast food restaurant", "tokens": ["what", "is", "the", "closest", "fast", "food", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "i need the closest chinese take out", "tokens": ["i", "need", "the", "closest", "chinese", "take", "out"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "B-Amenity", "I-Amenity"]}
{"sentence": "me and a buddy are going to met up for lunch where should we go", "tokens": ["me", "and", "a", "buddy", "are", "going", "to", "met", "up", "for", "lunch", "where", "should", "we", "go"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Hours", "O", "O", "O", "O"]}
{"sentence": "i need a family joint where i can have a beer with my meal", "tokens": ["i", "need", "a", "family", "joint", "where", "i", "can", "have", "a", "beer", "with", "my", "meal"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "O", "O", "O", "O", "O", "B-Dish", "O", "O", "O"]}
{"sentence": "vegan but not fast food", "tokens": ["vegan", "but", "not", "fast", "food"], "ner_tags": ["B-Cuisine", "O", "O", "B-Cuisine", "I-Cuisine"]}
{"sentence": "where can i get some tacos and margaritas", "tokens": ["where", "can", "i", "get", "some", "tacos", "and", "margaritas"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "O", "B-Dish"]}
{"sentence": "whats the phone number for a juice bar", "tokens": ["whats", "the", "phone", "number", "for", "a", "juice", "bar"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine"]}
{"sentence": "are there are sushi bars open within ten miles", "tokens": ["are", "there", "are", "sushi", "bars", "open", "within", "ten", "miles"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "B-Hours", "B-Location", "I-Location", "I-Location"]}
{"sentence": "help me find a good pho place", "tokens": ["help", "me", "find", "a", "good", "pho", "place"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "B-Cuisine", "O"]}
{"sentence": "hi car where can i get a good mojito", "tokens": ["hi", "car", "where", "can", "i", "get", "a", "good", "mojito"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Rating", "B-Dish"]}
{"sentence": "where can i get friend chicken", "tokens": ["where", "can", "i", "get", "friend", "chicken"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "when will thai basil be closed tonight", "tokens": ["when", "will", "thai", "basil", "be", "closed", "tonight"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Hours"]}
{"sentence": "what are nearby high end restaurants", "tokens": ["what", "are", "nearby", "high", "end", "restaurants"], "ner_tags": ["O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "i want to go to a top rated restaurant", "tokens": ["i", "want", "to", "go", "to", "a", "top", "rated", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Rating", "I-Rating", "O"]}
{"sentence": "im looking for cheap mexican food", "tokens": ["im", "looking", "for", "cheap", "mexican", "food"], "ner_tags": ["O", "O", "O", "B-Price", "B-Cuisine", "O"]}
{"sentence": "are there any 24 hour restaurants nearby", "tokens": ["are", "there", "any", "24", "hour", "restaurants", "nearby"], "ner_tags": ["O", "O", "O", "B-Hours", "I-Hours", "O", "B-Location"]}
{"sentence": "where are the closest fast food", "tokens": ["where", "are", "the", "closest", "fast", "food"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine"]}
{"sentence": "find vegetarian food open before 10 am at a good price", "tokens": ["find", "vegetarian", "food", "open", "before", "10", "am", "at", "a", "good", "price"], "ner_tags": ["O", "B-Cuisine", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "O", "O", "B-Price", "O"]}
{"sentence": "are you showing any wendys nearby", "tokens": ["are", "you", "showing", "any", "wendys", "nearby"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "B-Location"]}
{"sentence": "im looking for a kid friendly burger place", "tokens": ["im", "looking", "for", "a", "kid", "friendly", "burger", "place"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Dish", "O"]}
{"sentence": "greek food", "tokens": ["greek", "food"], "ner_tags": ["B-Cuisine", "O"]}
{"sentence": "where is the most expensive and hardest to get into restaurant in town", "tokens": ["where", "is", "the", "most", "expensive", "and", "hardest", "to", "get", "into", "restaurant", "in", "town"], "ner_tags": ["O", "O", "O", "B-Price", "I-Price", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "get me to some grub", "tokens": ["get", "me", "to", "some", "grub"], "ner_tags": ["O", "O", "O", "O", "O"]}
{"sentence": "are there any kosher bars in wichita", "tokens": ["are", "there", "any", "kosher", "bars", "in", "wichita"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Location"]}
{"sentence": "looking for reasonable priced restaurant along the way that are open after 10 p m", "tokens": ["looking", "for", "reasonable", "priced", "restaurant", "along", "the", "way", "that", "are", "open", "after", "10", "p", "m"], "ner_tags": ["O", "O", "B-Price", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Hours", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "i am hungry and have five dollars", "tokens": ["i", "am", "hungry", "and", "have", "five", "dollars"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "I-Price"]}
{"sentence": "where is the nearest wings and salads restaurant", "tokens": ["where", "is", "the", "nearest", "wings", "and", "salads", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "give me the phone number for the nearest buffet style chinese food restaurant", "tokens": ["give", "me", "the", "phone", "number", "for", "the", "nearest", "buffet", "style", "chinese", "food", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "B-Amenity", "I-Amenity", "B-Cuisine", "O", "O"]}
{"sentence": "where is the nearest chipotle", "tokens": ["where", "is", "the", "nearest", "chipotle"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name"]}
{"sentence": "can you find me a restaurant near the bay", "tokens": ["can", "you", "find", "me", "a", "restaurant", "near", "the", "bay"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "is there a restaurant thats close thats open after midnight", "tokens": ["is", "there", "a", "restaurant", "thats", "close", "thats", "open", "after", "midnight"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "can you find me the closest cheesecake factory", "tokens": ["can", "you", "find", "me", "the", "closest", "cheesecake", "factory"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "is there a crazy bowl nearby", "tokens": ["is", "there", "a", "crazy", "bowl", "nearby"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location"]}
{"sentence": "im looking for a family friendly restaurant", "tokens": ["im", "looking", "for", "a", "family", "friendly", "restaurant"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "O"]}
{"sentence": "where is a restaurant that serves pizza and also has a salad bar", "tokens": ["where", "is", "a", "restaurant", "that", "serves", "pizza", "and", "also", "has", "a", "salad", "bar"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "find me a restaurant that is half way to my destination", "tokens": ["find", "me", "a", "restaurant", "that", "is", "half", "way", "to", "my", "destination"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "what is the phone number and name of the closest peruvian restaurant", "tokens": ["what", "is", "the", "phone", "number", "and", "name", "of", "the", "closest", "peruvian", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "special dinner", "tokens": ["special", "dinner"], "ner_tags": ["B-Amenity", "B-Hours"]}
{"sentence": "what restaurants are available within 15 minutes of my current location that serve mexican food", "tokens": ["what", "restaurants", "are", "available", "within", "15", "minutes", "of", "my", "current", "location", "that", "serve", "mexican", "food"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "is there a dress code at russos on the bay", "tokens": ["is", "there", "a", "dress", "code", "at", "russos", "on", "the", "bay"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where is the closest family restaurant", "tokens": ["where", "is", "the", "closest", "family", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Amenity", "O"]}
{"sentence": "where can i eat brisket in newton heights", "tokens": ["where", "can", "i", "eat", "brisket", "in", "newton", "heights"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "O", "B-Location", "I-Location"]}
{"sentence": "i want a pet friendly chinese restaurant", "tokens": ["i", "want", "a", "pet", "friendly", "chinese", "restaurant"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "B-Cuisine", "O"]}
{"sentence": "where is the nearest sit down restaurant that has a 15 plate", "tokens": ["where", "is", "the", "nearest", "sit", "down", "restaurant", "that", "has", "a", "15", "plate"], "ner_tags": ["O", "O", "O", "B-Location", "B-Amenity", "I-Amenity", "O", "O", "O", "O", "B-Price", "I-Price"]}
{"sentence": "locate a cheap thai restaraunt", "tokens": ["locate", "a", "cheap", "thai", "restaraunt"], "ner_tags": ["O", "O", "B-Price", "B-Cuisine", "O"]}
{"sentence": "im looking for a mexican food resturaunt", "tokens": ["im", "looking", "for", "a", "mexican", "food", "resturaunt"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O"]}
{"sentence": "where is the quietest fredas", "tokens": ["where", "is", "the", "quietest", "fredas"], "ner_tags": ["O", "O", "O", "B-Amenity", "B-Restaurant_Name"]}
{"sentence": "i am looking for a chinese restaurant with a cheap price that stays open until 10 pm", "tokens": ["i", "am", "looking", "for", "a", "chinese", "restaurant", "with", "a", "cheap", "price", "that", "stays", "open", "until", "10", "pm"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Price", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "are there any mcdonalds within 5 minutes of here", "tokens": ["are", "there", "any", "mcdonalds", "within", "5", "minutes", "of", "here"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "B-Location", "I-Location", "I-Location", "O", "O"]}
{"sentence": "im looking for an open restaurant within ten miles", "tokens": ["im", "looking", "for", "an", "open", "restaurant", "within", "ten", "miles"], "ner_tags": ["O", "O", "O", "O", "B-Hours", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "id like cheap mexican food", "tokens": ["id", "like", "cheap", "mexican", "food"], "ner_tags": ["O", "O", "B-Price", "B-Cuisine", "O"]}
{"sentence": "do you know of a place that has both donuts and chinese food", "tokens": ["do", "you", "know", "of", "a", "place", "that", "has", "both", "donuts", "and", "chinese", "food"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Dish", "O", "B-Cuisine", "O"]}
{"sentence": "does the country inn ii offer local favorites before 8 am", "tokens": ["does", "the", "country", "inn", "ii", "offer", "local", "favorites", "before", "8", "am"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "can you find a place where famous people can be seen like stephen anthonys restaurant around here", "tokens": ["can", "you", "find", "a", "place", "where", "famous", "people", "can", "be", "seen", "like", "stephen", "anthonys", "restaurant", "around", "here"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location"]}
{"sentence": "find italian restaurant", "tokens": ["find", "italian", "restaurant"], "ner_tags": ["O", "B-Cuisine", "O"]}
{"sentence": "are there any reasonably priced restaurant that opens before 8 am", "tokens": ["are", "there", "any", "reasonably", "priced", "restaurant", "that", "opens", "before", "8", "am"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "where can i find a good italian restaurant near my location", "tokens": ["where", "can", "i", "find", "a", "good", "italian", "restaurant", "near", "my", "location"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "list any restaurants that have a broad menu with many different styles of food that would be good for a large group with diverse tastes", "tokens": ["list", "any", "restaurants", "that", "have", "a", "broad", "menu", "with", "many", "different", "styles", "of", "food", "that", "would", "be", "good", "for", "a", "large", "group", "with", "diverse", "tastes"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Location", "I-Location", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O"]}
{"sentence": "where can i get bean soup in a restaurant thats open until 11 at night", "tokens": ["where", "can", "i", "get", "bean", "soup", "in", "a", "restaurant", "thats", "open", "until", "11", "at", "night"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "I-Dish", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "need fried chicken fast", "tokens": ["need", "fried", "chicken", "fast"], "ner_tags": ["O", "B-Dish", "I-Dish", "B-Amenity"]}
{"sentence": "do any restaurants have a seasonal menu with venison dishes", "tokens": ["do", "any", "restaurants", "have", "a", "seasonal", "menu", "with", "venison", "dishes"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Dish", "O"]}
{"sentence": "i am looking for the closest subway restaurant", "tokens": ["i", "am", "looking", "for", "the", "closest", "subway", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "B-Restaurant_Name", "O"]}
{"sentence": "closest fast food joint", "tokens": ["closest", "fast", "food", "joint"], "ner_tags": ["B-Location", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "find me a restaurant nearby", "tokens": ["find", "me", "a", "restaurant", "nearby"], "ner_tags": ["O", "O", "O", "O", "B-Location"]}
{"sentence": "does the rheinlander have a smoking section", "tokens": ["does", "the", "rheinlander", "have", "a", "smoking", "section"], "ner_tags": ["O", "O", "B-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i want a list of kid friendly burger restaurants in baltimore", "tokens": ["i", "want", "a", "list", "of", "kid", "friendly", "burger", "restaurants", "in", "baltimore"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Dish", "O", "O", "B-Location"]}
{"sentence": "where can i get some crab legs", "tokens": ["where", "can", "i", "get", "some", "crab", "legs"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "wheres a good rib restaurant to take a date", "tokens": ["wheres", "a", "good", "rib", "restaurant", "to", "take", "a", "date"], "ner_tags": ["O", "O", "B-Rating", "B-Dish", "O", "O", "B-Hours", "I-Hours", "B-Amenity"]}
{"sentence": "i need to find a good restaurant", "tokens": ["i", "need", "to", "find", "a", "good", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "O"]}
{"sentence": "i want a salad", "tokens": ["i", "want", "a", "salad"], "ner_tags": ["O", "O", "O", "B-Dish"]}
{"sentence": "pizza hut on columbia ave in holland mi", "tokens": ["pizza", "hut", "on", "columbia", "ave", "in", "holland", "mi"], "ner_tags": ["B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "does anywhere nearby serve breakfast after 10 am", "tokens": ["does", "anywhere", "nearby", "serve", "breakfast", "after", "10", "am"], "ner_tags": ["O", "O", "B-Location", "O", "B-Cuisine", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "which restaurant has halal and is not expensiive", "tokens": ["which", "restaurant", "has", "halal", "and", "is", "not", "expensiive"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Price", "I-Price"]}
{"sentence": "is there anywhere within 5 miles of west and fifth that accepts checks", "tokens": ["is", "there", "anywhere", "within", "5", "miles", "of", "west", "and", "fifth", "that", "accepts", "checks"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "can you find me a restaurant with at least a few star rating", "tokens": ["can", "you", "find", "me", "a", "restaurant", "with", "at", "least", "a", "few", "star", "rating"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Rating", "I-Rating", "I-Rating"]}
{"sentence": "are there any good places to eat in the area", "tokens": ["are", "there", "any", "good", "places", "to", "eat", "in", "the", "area"], "ner_tags": ["O", "O", "O", "B-Rating", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "find me a restaurant with good reviews", "tokens": ["find", "me", "a", "restaurant", "with", "good", "reviews"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "how should i dress when going to ginos italian restaurant", "tokens": ["how", "should", "i", "dress", "when", "going", "to", "ginos", "italian", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "B-Cuisine", "O"]}
{"sentence": "can you find a place where i can have dinner with a party of 10 people or more", "tokens": ["can", "you", "find", "a", "place", "where", "i", "can", "have", "dinner", "with", "a", "party", "of", "10", "people", "or", "more"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "O", "O"]}
{"sentence": "im hungry for chinese any take out places nearby", "tokens": ["im", "hungry", "for", "chinese", "any", "take", "out", "places", "nearby"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Amenity", "I-Amenity", "O", "B-Location"]}
{"sentence": "im looking for a burger joint", "tokens": ["im", "looking", "for", "a", "burger", "joint"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "I-Cuisine"]}
{"sentence": "is forest street irish restaurant considered fine dining", "tokens": ["is", "forest", "street", "irish", "restaurant", "considered", "fine", "dining"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Rating", "B-Amenity", "I-Amenity"]}
{"sentence": "are the reviews good for chichis in gona", "tokens": ["are", "the", "reviews", "good", "for", "chichis", "in", "gona"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "O", "B-Location"]}
{"sentence": "find a restauran that makes jamaican patties", "tokens": ["find", "a", "restauran", "that", "makes", "jamaican", "patties"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "are there any burgers and cupcakes locations outside of manhattan", "tokens": ["are", "there", "any", "burgers", "and", "cupcakes", "locations", "outside", "of", "manhattan"], "ner_tags": ["O", "O", "O", "B-Dish", "O", "B-Dish", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "i would like a hamburger", "tokens": ["i", "would", "like", "a", "hamburger"], "ner_tags": ["O", "O", "O", "O", "B-Dish"]}
{"sentence": "where can i get a fair priced slice of pie within five miles of here", "tokens": ["where", "can", "i", "get", "a", "fair", "priced", "slice", "of", "pie", "within", "five", "miles", "of", "here"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "O", "B-Dish", "I-Dish", "I-Dish", "B-Location", "I-Location", "I-Location", "O", "O"]}
{"sentence": "are there any restaurants that stay open past midnight in arlington", "tokens": ["are", "there", "any", "restaurants", "that", "stay", "open", "past", "midnight", "in", "arlington"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "O", "B-Location"]}
{"sentence": "i am looking for a vegetarian menu in a nice restaurant", "tokens": ["i", "am", "looking", "for", "a", "vegetarian", "menu", "in", "a", "nice", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Rating", "O"]}
{"sentence": "is kabuki deli open on tuesday and wednesday", "tokens": ["is", "kabuki", "deli", "open", "on", "tuesday", "and", "wednesday"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "would like to call the closest applebees", "tokens": ["would", "like", "to", "call", "the", "closest", "applebees"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "B-Restaurant_Name"]}
{"sentence": "are there any fast food places within a mile of me", "tokens": ["are", "there", "any", "fast", "food", "places", "within", "a", "mile", "of", "me"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location", "I-Location", "O", "O"]}
{"sentence": "please find me a restaurant that serves home cooking", "tokens": ["please", "find", "me", "a", "restaurant", "that", "serves", "home", "cooking"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine"]}
{"sentence": "where on sycamore street could i eat a potato pie on the waterfront", "tokens": ["where", "on", "sycamore", "street", "could", "i", "eat", "a", "potato", "pie", "on", "the", "waterfront"], "ner_tags": ["O", "O", "B-Location", "I-Location", "O", "O", "O", "O", "B-Dish", "I-Dish", "O", "O", "B-Amenity"]}
{"sentence": "i want to get some pizza from cobras pizza within the next mile", "tokens": ["i", "want", "to", "get", "some", "pizza", "from", "cobras", "pizza", "within", "the", "next", "mile"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "i want to find a goos delivery pizza place", "tokens": ["i", "want", "to", "find", "a", "goos", "delivery", "pizza", "place"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "B-Amenity", "B-Cuisine", "O"]}
{"sentence": "i want a hamburger and fries where is the nearest fast food joint", "tokens": ["i", "want", "a", "hamburger", "and", "fries", "where", "is", "the", "nearest", "fast", "food", "joint"], "ner_tags": ["O", "O", "O", "B-Dish", "I-Dish", "I-Dish", "O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "i want a 4 star restaurant with a cozy atmosphere for two", "tokens": ["i", "want", "a", "4", "star", "restaurant", "with", "a", "cozy", "atmosphere", "for", "two"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O"]}
{"sentence": "a restaurant that is good for groups", "tokens": ["a", "restaurant", "that", "is", "good", "for", "groups"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "B-Amenity", "I-Amenity"]}
{"sentence": "find a chinese restaurant nearby that is open", "tokens": ["find", "a", "chinese", "restaurant", "nearby", "that", "is", "open"], "ner_tags": ["O", "O", "B-Cuisine", "O", "B-Location", "O", "O", "B-Hours"]}
{"sentence": "how far am i from the best steak in the city", "tokens": ["how", "far", "am", "i", "from", "the", "best", "steak", "in", "the", "city"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Rating", "B-Dish", "B-Location", "I-Location", "I-Location"]}
{"sentence": "brunch", "tokens": ["brunch"], "ner_tags": ["B-Cuisine"]}
{"sentence": "can i pay with a check at griffins", "tokens": ["can", "i", "pay", "with", "a", "check", "at", "griffins"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "O", "B-Restaurant_Name"]}
{"sentence": "what is the highest rated restaurant within a five mile radius", "tokens": ["what", "is", "the", "highest", "rated", "restaurant", "within", "a", "five", "mile", "radius"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "i want to find a burger place that has take out and accepts credit cards price doesnt matter", "tokens": ["i", "want", "to", "find", "a", "burger", "place", "that", "has", "take", "out", "and", "accepts", "credit", "cards", "price", "doesnt", "matter"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Amenity", "I-Amenity", "I-Amenity", "B-Price", "I-Price", "I-Price"]}
{"sentence": "id like a phone number for fuji ya in minneapolis", "tokens": ["id", "like", "a", "phone", "number", "for", "fuji", "ya", "in", "minneapolis"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location"]}
{"sentence": "where can i find the nearest thanh than restaurant thats open 7 days a week", "tokens": ["where", "can", "i", "find", "the", "nearest", "thanh", "than", "restaurant", "thats", "open", "7", "days", "a", "week"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "what the best seafood place around town", "tokens": ["what", "the", "best", "seafood", "place", "around", "town"], "ner_tags": ["O", "O", "B-Rating", "B-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "in the mood for fast food", "tokens": ["in", "the", "mood", "for", "fast", "food"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "I-Cuisine"]}
{"sentence": "is there a dennys on i 5 near bakersfield", "tokens": ["is", "there", "a", "dennys", "on", "i", "5", "near", "bakersfield"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "if i like duck what is a restaurant that i might like to eat at", "tokens": ["if", "i", "like", "duck", "what", "is", "a", "restaurant", "that", "i", "might", "like", "to", "eat", "at"], "ner_tags": ["O", "O", "O", "B-Dish", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "who is serving sashimi thats open", "tokens": ["who", "is", "serving", "sashimi", "thats", "open"], "ner_tags": ["O", "O", "O", "B-Dish", "O", "B-Hours"]}
{"sentence": "is there a restaurant along the route where we can watch people walk by", "tokens": ["is", "there", "a", "restaurant", "along", "the", "route", "where", "we", "can", "watch", "people", "walk", "by"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O"]}
{"sentence": "can i find chicken and waffles in stockton ca", "tokens": ["can", "i", "find", "chicken", "and", "waffles", "in", "stockton", "ca"], "ner_tags": ["O", "O", "O", "B-Dish", "I-Dish", "I-Dish", "O", "B-Location", "I-Location"]}
{"sentence": "what is the name of the restaurant on grand ave that serves the best hamburger", "tokens": ["what", "is", "the", "name", "of", "the", "restaurant", "on", "grand", "ave", "that", "serves", "the", "best", "hamburger"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "O", "B-Rating", "B-Dish"]}
{"sentence": "i want bread sticks and beer", "tokens": ["i", "want", "bread", "sticks", "and", "beer"], "ner_tags": ["O", "O", "B-Dish", "I-Dish", "O", "B-Dish"]}
{"sentence": "search for a restaurant that receives personal checks", "tokens": ["search", "for", "a", "restaurant", "that", "receives", "personal", "checks"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "wheres the best ice cream in this city", "tokens": ["wheres", "the", "best", "ice", "cream", "in", "this", "city"], "ner_tags": ["O", "O", "B-Rating", "B-Dish", "I-Dish", "B-Location", "I-Location", "I-Location"]}
{"sentence": "which restaurant has great ratings in the area", "tokens": ["which", "restaurant", "has", "great", "ratings", "in", "the", "area"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "B-Location", "I-Location", "I-Location"]}
{"sentence": "can you find a good restaurant for a childs birthday", "tokens": ["can", "you", "find", "a", "good", "restaurant", "for", "a", "childs", "birthday"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "O", "B-Amenity", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "which moderately priced mexican restaurants within 10 miles have the best reviews", "tokens": ["which", "moderately", "priced", "mexican", "restaurants", "within", "10", "miles", "have", "the", "best", "reviews"], "ner_tags": ["O", "B-Price", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "get the address for the new indian restaurant", "tokens": ["get", "the", "address", "for", "the", "new", "indian", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "what are the hours for the nearest dennys", "tokens": ["what", "are", "the", "hours", "for", "the", "nearest", "dennys"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "B-Restaurant_Name"]}
{"sentence": "can you find a market that has prix fixe menus", "tokens": ["can", "you", "find", "a", "market", "that", "has", "prix", "fixe", "menus"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Amenity", "I-Amenity", "O"]}
{"sentence": "is there a casual place to get a martini and dine at the bar", "tokens": ["is", "there", "a", "casual", "place", "to", "get", "a", "martini", "and", "dine", "at", "the", "bar"], "ner_tags": ["O", "O", "O", "B-Rating", "O", "O", "O", "O", "B-Dish", "O", "B-Amenity", "O", "O", "B-Amenity"]}
{"sentence": "where can i get greek food in my area", "tokens": ["where", "can", "i", "get", "greek", "food", "in", "my", "area"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "where is the nearest chinese restaurant", "tokens": ["where", "is", "the", "nearest", "chinese", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "show me restaurants that are well rated but inexpensive", "tokens": ["show", "me", "restaurants", "that", "are", "well", "rated", "but", "inexpensive"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "I-Rating", "O", "B-Price"]}
{"sentence": "please look for a bar", "tokens": ["please", "look", "for", "a", "bar"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine"]}
{"sentence": "is there an italian restaurant with authentic pizza around here for people on a budget", "tokens": ["is", "there", "an", "italian", "restaurant", "with", "authentic", "pizza", "around", "here", "for", "people", "on", "a", "budget"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Dish", "I-Dish", "O", "O", "O", "O", "B-Price", "O", "B-Price"]}
{"sentence": "where is jamba juice", "tokens": ["where", "is", "jamba", "juice"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what time does wendys in san lorenzo open", "tokens": ["what", "time", "does", "wendys", "in", "san", "lorenzo", "open"], "ner_tags": ["O", "B-Hours", "O", "B-Restaurant_Name", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Hours"]}
{"sentence": "show me the five star steak restaurants", "tokens": ["show", "me", "the", "five", "star", "steak", "restaurants"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "B-Cuisine", "O"]}
{"sentence": "whats a place thats not too cheap and not too expensive along this road", "tokens": ["whats", "a", "place", "thats", "not", "too", "cheap", "and", "not", "too", "expensive", "along", "this", "road"], "ner_tags": ["O", "O", "O", "O", "B-Price", "I-Price", "I-Price", "O", "B-Price", "I-Price", "I-Price", "B-Location", "I-Location", "I-Location"]}
{"sentence": "how far is quiznos from here", "tokens": ["how", "far", "is", "quiznos", "from", "here"], "ner_tags": ["B-Location", "I-Location", "O", "B-Restaurant_Name", "O", "O"]}
{"sentence": "where can i get a meal in under 5 minutes", "tokens": ["where", "can", "i", "get", "a", "meal", "in", "under", "5", "minutes"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "where can i find a carne asada burrito near by", "tokens": ["where", "can", "i", "find", "a", "carne", "asada", "burrito", "near", "by"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "I-Dish", "I-Dish", "B-Location", "I-Location"]}
{"sentence": "where is the nearest five star seafood place", "tokens": ["where", "is", "the", "nearest", "five", "star", "seafood", "place"], "ner_tags": ["O", "O", "O", "B-Location", "B-Rating", "I-Rating", "B-Cuisine", "O"]}
{"sentence": "are there any suggestions for a vegetarian restaurant south of here", "tokens": ["are", "there", "any", "suggestions", "for", "a", "vegetarian", "restaurant", "south", "of", "here"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "i want get a great steak where should i go that isnt to far", "tokens": ["i", "want", "get", "a", "great", "steak", "where", "should", "i", "go", "that", "isnt", "to", "far"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "where can i find a family style place price doesnt matter", "tokens": ["where", "can", "i", "find", "a", "family", "style", "place", "price", "doesnt", "matter"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Price", "I-Price", "I-Price"]}
{"sentence": "what kind of lunch specials does inman square feature", "tokens": ["what", "kind", "of", "lunch", "specials", "does", "inman", "square", "feature"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "please find me a spanish restaurant close by", "tokens": ["please", "find", "me", "a", "spanish", "restaurant", "close", "by"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "where can i find a place to have a good business meeting at breakfast", "tokens": ["where", "can", "i", "find", "a", "place", "to", "have", "a", "good", "business", "meeting", "at", "breakfast"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Hours"]}
{"sentence": "i want to go to a bar on colonel bell drive with portuguese food", "tokens": ["i", "want", "to", "go", "to", "a", "bar", "on", "colonel", "bell", "drive", "with", "portuguese", "food"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "how should i dress when going to cantinos", "tokens": ["how", "should", "i", "dress", "when", "going", "to", "cantinos"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "listings for all diners", "tokens": ["listings", "for", "all", "diners"], "ner_tags": ["O", "O", "O", "B-Cuisine"]}
{"sentence": "find me directions to times square in new york city new york", "tokens": ["find", "me", "directions", "to", "times", "square", "in", "new", "york", "city", "new", "york"], "ner_tags": ["O", "O", "O", "O", "B-Hours", "B-Location", "O", "B-Location", "I-Location", "I-Location", "O", "O"]}
{"sentence": "is there a czech restaurant in uptown", "tokens": ["is", "there", "a", "czech", "restaurant", "in", "uptown"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Location"]}
{"sentence": "does tuscumbia have any vegan restaurants", "tokens": ["does", "tuscumbia", "have", "any", "vegan", "restaurants"], "ner_tags": ["O", "B-Location", "O", "O", "B-Cuisine", "O"]}
{"sentence": "where is a family style restaurant that has vegetables and is one minute away", "tokens": ["where", "is", "a", "family", "style", "restaurant", "that", "has", "vegetables", "and", "is", "one", "minute", "away"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O", "B-Dish", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "where is the nearest fast food hamburger place", "tokens": ["where", "is", "the", "nearest", "fast", "food", "hamburger", "place"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "I-Cuisine", "O"]}
{"sentence": "is pic a pasta considered fine dining", "tokens": ["is", "pic", "a", "pasta", "considered", "fine", "dining"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where can i get a bite to eat round here", "tokens": ["where", "can", "i", "get", "a", "bite", "to", "eat", "round", "here"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "can you make a reservation for two people at 5 p at bellinis", "tokens": ["can", "you", "make", "a", "reservation", "for", "two", "people", "at", "5", "p", "at", "bellinis"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Hours", "I-Hours", "O", "B-Restaurant_Name"]}
{"sentence": "what restaurants on our route have egg rolls on the menu", "tokens": ["what", "restaurants", "on", "our", "route", "have", "egg", "rolls", "on", "the", "menu"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "O", "B-Dish", "I-Dish", "O", "O", "O"]}
{"sentence": "how late is the closest fast food resturant open until", "tokens": ["how", "late", "is", "the", "closest", "fast", "food", "resturant", "open", "until"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O", "O", "O"]}
{"sentence": "please locate an inexpensive japanese restaurant", "tokens": ["please", "locate", "an", "inexpensive", "japanese", "restaurant"], "ner_tags": ["O", "O", "O", "B-Price", "B-Cuisine", "O"]}
{"sentence": "where can i get a tofurky sandwich", "tokens": ["where", "can", "i", "get", "a", "tofurky", "sandwich"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "where is a sandwich shop", "tokens": ["where", "is", "a", "sandwich", "shop"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "find me a mexican restaurant with large portion sizes", "tokens": ["find", "me", "a", "mexican", "restaurant", "with", "large", "portion", "sizes"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "does arturos have a prix fix menu", "tokens": ["does", "arturos", "have", "a", "prix", "fix", "menu"], "ner_tags": ["O", "B-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "find a restaurant that has carry out in the evening", "tokens": ["find", "a", "restaurant", "that", "has", "carry", "out", "in", "the", "evening"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "whats the best taco truck in the area", "tokens": ["whats", "the", "best", "taco", "truck", "in", "the", "area"], "ner_tags": ["O", "O", "B-Rating", "B-Cuisine", "I-Cuisine", "B-Location", "I-Location", "I-Location"]}
{"sentence": "where can i find crust in sutherland road", "tokens": ["where", "can", "i", "find", "crust", "in", "sutherland", "road"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "looking for a lunch spot how about crepes restaurant", "tokens": ["looking", "for", "a", "lunch", "spot", "how", "about", "crepes", "restaurant"], "ner_tags": ["O", "O", "O", "B-Hours", "O", "O", "O", "B-Restaurant_Name", "O"]}
{"sentence": "is there a cafe rio within ten miles that is not expensive", "tokens": ["is", "there", "a", "cafe", "rio", "within", "ten", "miles", "that", "is", "not", "expensive"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location", "O", "O", "B-Price", "I-Price"]}
{"sentence": "how many burger kings are within ten miles", "tokens": ["how", "many", "burger", "kings", "are", "within", "ten", "miles"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "what kind of feeling is there at sillys food shack", "tokens": ["what", "kind", "of", "feeling", "is", "there", "at", "sillys", "food", "shack"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "show me all the wendys in a five mile radius", "tokens": ["show", "me", "all", "the", "wendys", "in", "a", "five", "mile", "radius"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "i need a restaurant that doesnt need reservations close by", "tokens": ["i", "need", "a", "restaurant", "that", "doesnt", "need", "reservations", "close", "by"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "B-Location", "I-Location"]}
{"sentence": "find a mexican restaurant in denver", "tokens": ["find", "a", "mexican", "restaurant", "in", "denver"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "B-Location"]}
{"sentence": "what night is trivia night at buffalo wild wings in akron ohio", "tokens": ["what", "night", "is", "trivia", "night", "at", "buffalo", "wild", "wings", "in", "akron", "ohio"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "what mexican restaurant has the best reviews and has an outside patio", "tokens": ["what", "mexican", "restaurant", "has", "the", "best", "reviews", "and", "has", "an", "outside", "patio"], "ner_tags": ["O", "B-Cuisine", "O", "O", "O", "B-Rating", "I-Rating", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "need a 4 star place to take a business associate", "tokens": ["need", "a", "4", "star", "place", "to", "take", "a", "business", "associate"], "ner_tags": ["O", "O", "B-Rating", "I-Rating", "B-Location", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "can i have phone numbers to fine dining locations in this area", "tokens": ["can", "i", "have", "phone", "numbers", "to", "fine", "dining", "locations", "in", "this", "area"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "i want irish food at a good price somewhere open till midnight", "tokens": ["i", "want", "irish", "food", "at", "a", "good", "price", "somewhere", "open", "till", "midnight"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "O", "B-Price", "O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "i need a pizza place pronto that is within walking distance", "tokens": ["i", "need", "a", "pizza", "place", "pronto", "that", "is", "within", "walking", "distance"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "what time does ihop open tomorrow for breakfast", "tokens": ["what", "time", "does", "ihop", "open", "tomorrow", "for", "breakfast"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "O", "O", "O"]}
{"sentence": "chinese foooooood", "tokens": ["chinese", "foooooood"], "ner_tags": ["B-Cuisine", "O"]}
{"sentence": "who delivers to south main after midnight on weekends", "tokens": ["who", "delivers", "to", "south", "main", "after", "midnight", "on", "weekends"], "ner_tags": ["O", "B-Amenity", "O", "B-Location", "I-Location", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "where is pizza hut", "tokens": ["where", "is", "pizza", "hut"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "im in a rush today where can i pick up food from", "tokens": ["im", "in", "a", "rush", "today", "where", "can", "i", "pick", "up", "food", "from"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O"]}
{"sentence": "im hungry where can i eat around here", "tokens": ["im", "hungry", "where", "can", "i", "eat", "around", "here"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "where is there a cigar and wine club", "tokens": ["where", "is", "there", "a", "cigar", "and", "wine", "club"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i want to eat at a greasy spoon find one please", "tokens": ["i", "want", "to", "eat", "at", "a", "greasy", "spoon", "find", "one", "please"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O"]}
{"sentence": "do you know if zaxbys has a prix fixe menu", "tokens": ["do", "you", "know", "if", "zaxbys", "has", "a", "prix", "fixe", "menu"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "where can i find a delicious mushroom omelette", "tokens": ["where", "can", "i", "find", "a", "delicious", "mushroom", "omelette"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "is there a restaurant that is open till 1 am and close by", "tokens": ["is", "there", "a", "restaurant", "that", "is", "open", "till", "1", "am", "and", "close", "by"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "O", "B-Location", "I-Location"]}
{"sentence": "wheres the closest taco bell open until midnight", "tokens": ["wheres", "the", "closest", "taco", "bell", "open", "until", "midnight"], "ner_tags": ["O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "who has crawfish etouffee", "tokens": ["who", "has", "crawfish", "etouffee"], "ner_tags": ["O", "O", "B-Dish", "I-Dish"]}
{"sentence": "show me a list of any restaurant that is considered kid friendly but still serves good food that we will like", "tokens": ["show", "me", "a", "list", "of", "any", "restaurant", "that", "is", "considered", "kid", "friendly", "but", "still", "serves", "good", "food", "that", "we", "will", "like"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O", "B-Rating", "O", "O", "O", "O", "O"]}
{"sentence": "look for closest restaurants", "tokens": ["look", "for", "closest", "restaurants"], "ner_tags": ["O", "O", "B-Location", "O"]}
{"sentence": "where is the nearest famous daves", "tokens": ["where", "is", "the", "nearest", "famous", "daves"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "any buffet style restaurant in my area", "tokens": ["any", "buffet", "style", "restaurant", "in", "my", "area"], "ner_tags": ["O", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "how late is taco bell open", "tokens": ["how", "late", "is", "taco", "bell", "open"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "i need to find a orange julius that is less than 10 miles away", "tokens": ["i", "need", "to", "find", "a", "orange", "julius", "that", "is", "less", "than", "10", "miles", "away"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "where can i get really good drinks", "tokens": ["where", "can", "i", "get", "really", "good", "drinks"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "I-Rating", "B-Dish"]}
{"sentence": "i am starving can you help me find a five star italian restaurant", "tokens": ["i", "am", "starving", "can", "you", "help", "me", "find", "a", "five", "star", "italian", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Rating", "I-Rating", "B-Cuisine", "O"]}
{"sentence": "where is the cheapest place in boston to get steamed shrimp", "tokens": ["where", "is", "the", "cheapest", "place", "in", "boston", "to", "get", "steamed", "shrimp"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "B-Location", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "does emilios serve drinks", "tokens": ["does", "emilios", "serve", "drinks"], "ner_tags": ["O", "B-Restaurant_Name", "O", "B-Amenity"]}
{"sentence": "what is the rating on mcdonalds", "tokens": ["what", "is", "the", "rating", "on", "mcdonalds"], "ner_tags": ["O", "O", "O", "B-Rating", "O", "B-Restaurant_Name"]}
{"sentence": "how far away is the closest mcdonalds", "tokens": ["how", "far", "away", "is", "the", "closest", "mcdonalds"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "B-Restaurant_Name"]}
{"sentence": "i need directions to pizza hut", "tokens": ["i", "need", "directions", "to", "pizza", "hut"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what good restaurants are around here", "tokens": ["what", "good", "restaurants", "are", "around", "here"], "ner_tags": ["O", "B-Rating", "O", "O", "B-Location", "I-Location"]}
{"sentence": "where could i get a group sized omelet", "tokens": ["where", "could", "i", "get", "a", "group", "sized", "omelet"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Dish"]}
{"sentence": "is there a mexican restaurant that serves chile rellenos somewhere close", "tokens": ["is", "there", "a", "mexican", "restaurant", "that", "serves", "chile", "rellenos", "somewhere", "close"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Dish", "I-Dish", "B-Location", "I-Location"]}
{"sentence": "i need a fancy high rated restaurant with valet parking", "tokens": ["i", "need", "a", "fancy", "high", "rated", "restaurant", "with", "valet", "parking"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "I-Rating", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "do they make good briskits", "tokens": ["do", "they", "make", "good", "briskits"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Dish"]}
{"sentence": "are there any japanese restaurants down town that offer traditional seating arrangements", "tokens": ["are", "there", "any", "japanese", "restaurants", "down", "town", "that", "offer", "traditional", "seating", "arrangements"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "where can i make reservations for dinner", "tokens": ["where", "can", "i", "make", "reservations", "for", "dinner"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "O", "B-Hours"]}
{"sentence": "do you know if sams have a wine list", "tokens": ["do", "you", "know", "if", "sams", "have", "a", "wine", "list"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "O", "O", "O", "O"]}
{"sentence": "whats a family friendly place thats expensive", "tokens": ["whats", "a", "family", "friendly", "place", "thats", "expensive"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Price"]}
{"sentence": "can you find a romantic restaurant that has a dress code", "tokens": ["can", "you", "find", "a", "romantic", "restaurant", "that", "has", "a", "dress", "code"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "find me the closest indian restaurant", "tokens": ["find", "me", "the", "closest", "indian", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "is there a hotel with asian dining", "tokens": ["is", "there", "a", "hotel", "with", "asian", "dining"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "B-Cuisine", "O"]}
{"sentence": "what restaurants in this area are open at 5 am", "tokens": ["what", "restaurants", "in", "this", "area", "are", "open", "at", "5", "am"], "ner_tags": ["O", "O", "B-Location", "I-Location", "I-Location", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "i need to eat lunch soon but i need vegetarian food", "tokens": ["i", "need", "to", "eat", "lunch", "soon", "but", "i", "need", "vegetarian", "food"], "ner_tags": ["O", "O", "O", "O", "B-Hours", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "find a fast food restaurant with a playground", "tokens": ["find", "a", "fast", "food", "restaurant", "with", "a", "playground"], "ner_tags": ["O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "O", "B-Amenity"]}
{"sentence": "are there any taco bells in new jersey", "tokens": ["are", "there", "any", "taco", "bells", "in", "new", "jersey"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "where can i take my girlfriend for a romantic dinner", "tokens": ["where", "can", "i", "take", "my", "girlfriend", "for", "a", "romantic", "dinner"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "O"]}
{"sentence": "i am in the mood for chinese food what are some nearby restaurants", "tokens": ["i", "am", "in", "the", "mood", "for", "chinese", "food", "what", "are", "some", "nearby", "restaurants"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "B-Location", "O"]}
{"sentence": "what kind of food do they serve at the restaurant located across the street from the hospital", "tokens": ["what", "kind", "of", "food", "do", "they", "serve", "at", "the", "restaurant", "located", "across", "the", "street", "from", "the", "hospital"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "its a nice weather and i want to enjoy outdoor dining", "tokens": ["its", "a", "nice", "weather", "and", "i", "want", "to", "enjoy", "outdoor", "dining"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "know any good drive in restaurants", "tokens": ["know", "any", "good", "drive", "in", "restaurants"], "ner_tags": ["O", "O", "B-Rating", "B-Amenity", "I-Amenity", "O"]}
{"sentence": "id like to find an upscale restaurant with private room or section available for a business dinner for 12 people", "tokens": ["id", "like", "to", "find", "an", "upscale", "restaurant", "with", "private", "room", "or", "section", "available", "for", "a", "business", "dinner", "for", "12", "people"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Amenity", "O", "O", "O", "B-Amenity", "B-Hours", "O", "O", "O"]}
{"sentence": "wheres the best indian food in berkeley", "tokens": ["wheres", "the", "best", "indian", "food", "in", "berkeley"], "ner_tags": ["O", "O", "B-Rating", "B-Cuisine", "O", "O", "B-Location"]}
{"sentence": "where in ashland can i get tea late at night", "tokens": ["where", "in", "ashland", "can", "i", "get", "tea", "late", "at", "night"], "ner_tags": ["O", "O", "B-Location", "O", "O", "O", "B-Cuisine", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "where can i find pasta this time of night", "tokens": ["where", "can", "i", "find", "pasta", "this", "time", "of", "night"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "where can i find a cheap seafood place", "tokens": ["where", "can", "i", "find", "a", "cheap", "seafood", "place"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "B-Cuisine", "O"]}
{"sentence": "are there any restaurants nearby that serve shrimp", "tokens": ["are", "there", "any", "restaurants", "nearby", "that", "serve", "shrimp"], "ner_tags": ["O", "O", "O", "O", "B-Location", "O", "O", "B-Dish"]}
{"sentence": "does the cambridge house of pizza in nashville stays open at 7 p", "tokens": ["does", "the", "cambridge", "house", "of", "pizza", "in", "nashville", "stays", "open", "at", "7", "p"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "who makes the best chili with macaroni noodles in it", "tokens": ["who", "makes", "the", "best", "chili", "with", "macaroni", "noodles", "in", "it"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Dish", "I-Dish", "I-Dish", "I-Dish", "O", "O"]}
{"sentence": "where is that argentinian steakhouse with all the good reviews", "tokens": ["where", "is", "that", "argentinian", "steakhouse", "with", "all", "the", "good", "reviews"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "O", "B-Rating", "O"]}
{"sentence": "wheres the closet place for a good steak", "tokens": ["wheres", "the", "closet", "place", "for", "a", "good", "steak"], "ner_tags": ["O", "O", "B-Location", "O", "O", "O", "B-Rating", "B-Dish"]}
{"sentence": "who serves fresh seafood in kansas city", "tokens": ["who", "serves", "fresh", "seafood", "in", "kansas", "city"], "ner_tags": ["O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "where is a good mexican restaurant with a large vegetarian selection", "tokens": ["where", "is", "a", "good", "mexican", "restaurant", "with", "a", "large", "vegetarian", "selection"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "is there a restaurant called evs usa along my current route", "tokens": ["is", "there", "a", "restaurant", "called", "evs", "usa", "along", "my", "current", "route"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "whos got the most popular thin crust pizza in town", "tokens": ["whos", "got", "the", "most", "popular", "thin", "crust", "pizza", "in", "town"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "B-Dish", "I-Dish", "I-Dish", "B-Location", "I-Location"]}
{"sentence": "do any restaurants in this town offer valet parking", "tokens": ["do", "any", "restaurants", "in", "this", "town", "offer", "valet", "parking"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "do you know what time the lunch specials are over at china king", "tokens": ["do", "you", "know", "what", "time", "the", "lunch", "specials", "are", "over", "at", "china", "king"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Hours", "B-Amenity", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "are there any seafood restaurants that have a smoking section", "tokens": ["are", "there", "any", "seafood", "restaurants", "that", "have", "a", "smoking", "section"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i need a reservation for two in 45 minutes at a fancy restaurant with a big wine list", "tokens": ["i", "need", "a", "reservation", "for", "two", "in", "45", "minutes", "at", "a", "fancy", "restaurant", "with", "a", "big", "wine", "list"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "O", "B-Hours", "I-Hours", "I-Hours", "O", "O", "B-Amenity", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "is there a place near my hotel known for having the best pizza in town", "tokens": ["is", "there", "a", "place", "near", "my", "hotel", "known", "for", "having", "the", "best", "pizza", "in", "town"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "O", "B-Rating", "B-Dish", "O", "O"]}
{"sentence": "what restaurant serves squid", "tokens": ["what", "restaurant", "serves", "squid"], "ner_tags": ["O", "O", "O", "B-Dish"]}
{"sentence": "which restaurant has a great meelet", "tokens": ["which", "restaurant", "has", "a", "great", "meelet"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "what restaurant nearby serves steak", "tokens": ["what", "restaurant", "nearby", "serves", "steak"], "ner_tags": ["O", "O", "B-Location", "O", "B-Dish"]}
{"sentence": "best authentic mexican chimichangas", "tokens": ["best", "authentic", "mexican", "chimichangas"], "ner_tags": ["B-Rating", "B-Cuisine", "I-Cuisine", "B-Dish"]}
{"sentence": "whats the best restaurant nearby", "tokens": ["whats", "the", "best", "restaurant", "nearby"], "ner_tags": ["O", "O", "B-Rating", "O", "B-Location"]}
{"sentence": "how far away is the nearest indian restaurant", "tokens": ["how", "far", "away", "is", "the", "nearest", "indian", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "what time does the closest japanese restaurant open", "tokens": ["what", "time", "does", "the", "closest", "japanese", "restaurant", "open"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Cuisine", "O", "O"]}
{"sentence": "are there any special location dining restaurants that serve lobster roll", "tokens": ["are", "there", "any", "special", "location", "dining", "restaurants", "that", "serve", "lobster", "roll"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "B-Location", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "what is the typical portion size from longhorn steakhouse", "tokens": ["what", "is", "the", "typical", "portion", "size", "from", "longhorn", "steakhouse"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "find a taco johns for taking a date", "tokens": ["find", "a", "taco", "johns", "for", "taking", "a", "date"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "thai restaurants list local", "tokens": ["thai", "restaurants", "list", "local"], "ner_tags": ["B-Cuisine", "O", "O", "B-Location"]}
{"sentence": "where can i go eat that offers televised live sports", "tokens": ["where", "can", "i", "go", "eat", "that", "offers", "televised", "live", "sports"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "does mitchells take personal checks", "tokens": ["does", "mitchells", "take", "personal", "checks"], "ner_tags": ["O", "B-Restaurant_Name", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "is there a ground round grill and bar on baldwin road", "tokens": ["is", "there", "a", "ground", "round", "grill", "and", "bar", "on", "baldwin", "road"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "O", "B-Location", "I-Location"]}
{"sentence": "where can i find a well priced meal", "tokens": ["where", "can", "i", "find", "a", "well", "priced", "meal"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "O", "O"]}
{"sentence": "please find me a soundbites barbecue with a patio and fast service", "tokens": ["please", "find", "me", "a", "soundbites", "barbecue", "with", "a", "patio", "and", "fast", "service"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Amenity", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where in brazil is wendys located", "tokens": ["where", "in", "brazil", "is", "wendys", "located"], "ner_tags": ["O", "O", "B-Location", "O", "B-Restaurant_Name", "O"]}
{"sentence": "what is the highest rated restaurant in the city", "tokens": ["what", "is", "the", "highest", "rated", "restaurant", "in", "the", "city"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "what restaurants offer dining outside", "tokens": ["what", "restaurants", "offer", "dining", "outside"], "ner_tags": ["O", "O", "B-Location", "I-Location", "B-Amenity"]}
{"sentence": "which restaurants in the area provide smoking areas", "tokens": ["which", "restaurants", "in", "the", "area", "provide", "smoking", "areas"], "ner_tags": ["O", "O", "B-Location", "I-Location", "I-Location", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "what fast food restaurants are closest to exit 11", "tokens": ["what", "fast", "food", "restaurants", "are", "closest", "to", "exit", "11"], "ner_tags": ["O", "B-Cuisine", "I-Cuisine", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "which restaurant is close by and quiet", "tokens": ["which", "restaurant", "is", "close", "by", "and", "quiet"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "O", "B-Amenity"]}
{"sentence": "find a kid friendly steak house within 10 miles of here", "tokens": ["find", "a", "kid", "friendly", "steak", "house", "within", "10", "miles", "of", "here"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity", "B-Cuisine", "I-Cuisine", "B-Location", "I-Location", "I-Location", "O", "O"]}
{"sentence": "is there a low cost buffet anywhere near here", "tokens": ["is", "there", "a", "low", "cost", "buffet", "anywhere", "near", "here"], "ner_tags": ["O", "O", "O", "B-Price", "I-Price", "B-Amenity", "B-Location", "I-Location", "I-Location"]}
{"sentence": "who makes the best chicken sandwich", "tokens": ["who", "makes", "the", "best", "chicken", "sandwich"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Dish", "I-Dish"]}
{"sentence": "what is best rated steakhouse in city", "tokens": ["what", "is", "best", "rated", "steakhouse", "in", "city"], "ner_tags": ["O", "O", "B-Rating", "O", "B-Cuisine", "B-Location", "I-Location"]}
{"sentence": "please find a good restaurant with good lighting", "tokens": ["please", "find", "a", "good", "restaurant", "with", "good", "lighting"], "ner_tags": ["O", "O", "O", "B-Rating", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "how vegetarian friendly is bar la grassa", "tokens": ["how", "vegetarian", "friendly", "is", "bar", "la", "grassa"], "ner_tags": ["O", "B-Cuisine", "B-Amenity", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what time does gustavs open on sundays", "tokens": ["what", "time", "does", "gustavs", "open", "on", "sundays"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "O", "B-Hours"]}
{"sentence": "does sparta restaurant have live music and is it close", "tokens": ["does", "sparta", "restaurant", "have", "live", "music", "and", "is", "it", "close"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "do you show any mcdonalds nearby", "tokens": ["do", "you", "show", "any", "mcdonalds", "nearby"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "B-Location"]}
{"sentence": "is there somewhere close that serves raw foods", "tokens": ["is", "there", "somewhere", "close", "that", "serves", "raw", "foods"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "B-Cuisine", "O"]}
{"sentence": "i am looking for a chinese restaurant that has an extensive buffet and has good customer reviews", "tokens": ["i", "am", "looking", "for", "a", "chinese", "restaurant", "that", "has", "an", "extensive", "buffet", "and", "has", "good", "customer", "reviews"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Rating", "I-Rating", "I-Rating"]}
{"sentence": "im looking for something to eat within driving distance of here", "tokens": ["im", "looking", "for", "something", "to", "eat", "within", "driving", "distance", "of", "here"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O"]}
{"sentence": "can you find me a good place for a nice date", "tokens": ["can", "you", "find", "me", "a", "good", "place", "for", "a", "nice", "date"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "please find a wendys with a drive through", "tokens": ["please", "find", "a", "wendys", "with", "a", "drive", "through"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "find the phone number of the sweet tomatoes located about six miles north of here", "tokens": ["find", "the", "phone", "number", "of", "the", "sweet", "tomatoes", "located", "about", "six", "miles", "north", "of", "here"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "find me a chick fil a with valet parking within five miles", "tokens": ["find", "me", "a", "chick", "fil", "a", "with", "valet", "parking", "within", "five", "miles"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location"]}
{"sentence": "i want to find the closest outback", "tokens": ["i", "want", "to", "find", "the", "closest", "outback"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "B-Restaurant_Name"]}
{"sentence": "locate an american diner with car hop service", "tokens": ["locate", "an", "american", "diner", "with", "car", "hop", "service"], "ner_tags": ["O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "which restaurants close by have generous portions at reasonable prices", "tokens": ["which", "restaurants", "close", "by", "have", "generous", "portions", "at", "reasonable", "prices"], "ner_tags": ["O", "O", "B-Location", "I-Location", "O", "B-Amenity", "I-Amenity", "O", "B-Price", "O"]}
{"sentence": "list local gyro shops please", "tokens": ["list", "local", "gyro", "shops", "please"], "ner_tags": ["O", "B-Location", "B-Cuisine", "O", "O"]}
{"sentence": "where can i get sone sunnyside up eggs", "tokens": ["where", "can", "i", "get", "sone", "sunnyside", "up", "eggs"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "I-Dish", "I-Dish"]}
{"sentence": "where can i get a steak around here", "tokens": ["where", "can", "i", "get", "a", "steak", "around", "here"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "B-Location", "I-Location"]}
{"sentence": "does sandersons offer takeout", "tokens": ["does", "sandersons", "offer", "takeout"], "ner_tags": ["O", "B-Restaurant_Name", "O", "B-Amenity"]}
{"sentence": "are there any buffets within 2 miles of here", "tokens": ["are", "there", "any", "buffets", "within", "2", "miles", "of", "here"], "ner_tags": ["O", "O", "O", "B-Amenity", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "show me the closest seafood restaurant", "tokens": ["show", "me", "the", "closest", "seafood", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "is there a hamburger restaurant along the way that has special dining", "tokens": ["is", "there", "a", "hamburger", "restaurant", "along", "the", "way", "that", "has", "special", "dining"], "ner_tags": ["O", "O", "O", "B-Dish", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "thai basil that has dining at bar along the way", "tokens": ["thai", "basil", "that", "has", "dining", "at", "bar", "along", "the", "way"], "ner_tags": ["B-Cuisine", "I-Cuisine", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "B-Location", "I-Location", "I-Location"]}
{"sentence": "i need a restaurant", "tokens": ["i", "need", "a", "restaurant"], "ner_tags": ["O", "O", "O", "O"]}
{"sentence": "where can i find a souplantation", "tokens": ["where", "can", "i", "find", "a", "souplantation"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "is there a place around here to eat that is open right now", "tokens": ["is", "there", "a", "place", "around", "here", "to", "eat", "that", "is", "open", "right", "now"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "im hungry wheres the nearest restaurant", "tokens": ["im", "hungry", "wheres", "the", "nearest", "restaurant"], "ner_tags": ["O", "O", "O", "O", "B-Location", "O"]}
{"sentence": "can you find a bakery along my route", "tokens": ["can", "you", "find", "a", "bakery", "along", "my", "route"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "B-Location", "I-Location", "I-Location"]}
{"sentence": "im looking for yemista", "tokens": ["im", "looking", "for", "yemista"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "where within 4 miles could i find a group place with cool music", "tokens": ["where", "within", "4", "miles", "could", "i", "find", "a", "group", "place", "with", "cool", "music"], "ner_tags": ["O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does this restaurant have a perfect portion size", "tokens": ["does", "this", "restaurant", "have", "a", "perfect", "portion", "size"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "find me a cambodian restaurant near alameda ca", "tokens": ["find", "me", "a", "cambodian", "restaurant", "near", "alameda", "ca"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Location", "I-Location"]}
{"sentence": "where is the nearest fast food place", "tokens": ["where", "is", "the", "nearest", "fast", "food", "place"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "what time does espetus close", "tokens": ["what", "time", "does", "espetus", "close"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O"]}
{"sentence": "make a reservation at the applebees in fall river", "tokens": ["make", "a", "reservation", "at", "the", "applebees", "in", "fall", "river"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "take me to somewhere with good wine", "tokens": ["take", "me", "to", "somewhere", "with", "good", "wine"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "B-Cuisine"]}
{"sentence": "show me where i can get tamales", "tokens": ["show", "me", "where", "i", "can", "get", "tamales"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "i need to find a restaurant that serves sushi near 5 th street one that doesnt have a dress code", "tokens": ["i", "need", "to", "find", "a", "restaurant", "that", "serves", "sushi", "near", "5", "th", "street", "one", "that", "doesnt", "have", "a", "dress", "code"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "B-Location", "I-Location", "I-Location", "I-Location", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is there a smoking friendly steakhouse", "tokens": ["is", "there", "a", "smoking", "friendly", "steakhouse"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O"]}
{"sentence": "where can a guy get a burger", "tokens": ["where", "can", "a", "guy", "get", "a", "burger"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "i need coffee on the way to work", "tokens": ["i", "need", "coffee", "on", "the", "way", "to", "work"], "ner_tags": ["O", "O", "B-Dish", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "i need to use phone can you show me the phone numbers", "tokens": ["i", "need", "to", "use", "phone", "can", "you", "show", "me", "the", "phone", "numbers"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "is krazy karrys fairly inexpensive", "tokens": ["is", "krazy", "karrys", "fairly", "inexpensive"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Price", "I-Price"]}
{"sentence": "are there any korean restaurants in town", "tokens": ["are", "there", "any", "korean", "restaurants", "in", "town"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "is kiosque very pricey", "tokens": ["is", "kiosque", "very", "pricey"], "ner_tags": ["O", "B-Restaurant_Name", "B-Price", "O"]}
{"sentence": "look for a kid friendly burger place with outdoor seating", "tokens": ["look", "for", "a", "kid", "friendly", "burger", "place", "with", "outdoor", "seating"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "B-Cuisine", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where can i drive to get chowder", "tokens": ["where", "can", "i", "drive", "to", "get", "chowder"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "what restaurants with great prices are open before 8 am", "tokens": ["what", "restaurants", "with", "great", "prices", "are", "open", "before", "8", "am"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "which pizza hut in the city has the best reviews", "tokens": ["which", "pizza", "hut", "in", "the", "city", "has", "the", "best", "reviews"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location", "B-Rating", "I-Rating", "I-Rating", "I-Rating"]}
{"sentence": "is there a pasta caffe within one mile", "tokens": ["is", "there", "a", "pasta", "caffe", "within", "one", "mile"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "B-Location", "I-Location", "I-Location"]}
{"sentence": "is there a pizza place nearby", "tokens": ["is", "there", "a", "pizza", "place", "nearby"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "i want to go to a nice diner with an old friend", "tokens": ["i", "want", "to", "go", "to", "a", "nice", "diner", "with", "an", "old", "friend"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "O", "O", "O", "O"]}
{"sentence": "where is the closest and cheap wapo tapo restaurant around here", "tokens": ["where", "is", "the", "closest", "and", "cheap", "wapo", "tapo", "restaurant", "around", "here"], "ner_tags": ["O", "O", "O", "B-Location", "O", "B-Price", "B-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "are there any kfc restaurants along my route", "tokens": ["are", "there", "any", "kfc", "restaurants", "along", "my", "route"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "can you find me someplace with a good wine list this time", "tokens": ["can", "you", "find", "me", "someplace", "with", "a", "good", "wine", "list", "this", "time"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Rating", "B-Amenity", "I-Amenity", "O", "O"]}
{"sentence": "which restaurants will deliver to my hotel after 10 p m", "tokens": ["which", "restaurants", "will", "deliver", "to", "my", "hotel", "after", "10", "p", "m"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "where in sherborn is vegetarian fine dining", "tokens": ["where", "in", "sherborn", "is", "vegetarian", "fine", "dining"], "ner_tags": ["O", "O", "B-Location", "O", "B-Cuisine", "B-Amenity", "I-Amenity"]}
{"sentence": "id like to get the phone number of the best jamaican place around", "tokens": ["id", "like", "to", "get", "the", "phone", "number", "of", "the", "best", "jamaican", "place", "around"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Rating", "B-Cuisine", "O", "B-Location"]}
{"sentence": "what burger joints are there on the way open after 1 am", "tokens": ["what", "burger", "joints", "are", "there", "on", "the", "way", "open", "after", "1", "am"], "ner_tags": ["O", "B-Cuisine", "I-Cuisine", "O", "O", "O", "O", "B-Location", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "what are some good italian places within 5 miles", "tokens": ["what", "are", "some", "good", "italian", "places", "within", "5", "miles"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "are there any chilean restaurants with live music on the south side", "tokens": ["are", "there", "any", "chilean", "restaurants", "with", "live", "music", "on", "the", "south", "side"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Location", "I-Location"]}
{"sentence": "make a reservation for me at dorsia tonight 7 oclock", "tokens": ["make", "a", "reservation", "for", "me", "at", "dorsia", "tonight", "7", "oclock"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "O", "O", "O"]}
{"sentence": "make a reservation for me at an italian restaurant 3 star or more", "tokens": ["make", "a", "reservation", "for", "me", "at", "an", "italian", "restaurant", "3", "star", "or", "more"], "ner_tags": ["O", "O", "B-Location", "O", "O", "O", "O", "B-Cuisine", "O", "B-Rating", "I-Rating", "I-Rating", "I-Rating"]}
{"sentence": "any restaurant close by that serves vegan dish and its affordable", "tokens": ["any", "restaurant", "close", "by", "that", "serves", "vegan", "dish", "and", "its", "affordable"], "ner_tags": ["O", "O", "B-Location", "I-Location", "O", "O", "B-Cuisine", "O", "O", "O", "B-Price"]}
{"sentence": "where can i find a lobster roll", "tokens": ["where", "can", "i", "find", "a", "lobster", "roll"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "i need a cheap mexican place with fresh ratings", "tokens": ["i", "need", "a", "cheap", "mexican", "place", "with", "fresh", "ratings"], "ner_tags": ["O", "O", "O", "B-Price", "B-Cuisine", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "can you find an applebees restaurant within 5 miles", "tokens": ["can", "you", "find", "an", "applebees", "restaurant", "within", "5", "miles"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "what is the best pizza served", "tokens": ["what", "is", "the", "best", "pizza", "served"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Dish", "O"]}
{"sentence": "what is the highest rated sushi restaurant within 8 miles", "tokens": ["what", "is", "the", "highest", "rated", "sushi", "restaurant", "within", "8", "miles"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "is there any place that have music", "tokens": ["is", "there", "any", "place", "that", "have", "music"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity"]}
{"sentence": "what time does bobs diner on ridge and lyceum open at", "tokens": ["what", "time", "does", "bobs", "diner", "on", "ridge", "and", "lyceum", "open", "at"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location", "O", "O"]}
{"sentence": "i want to eat chorizo where can i go", "tokens": ["i", "want", "to", "eat", "chorizo", "where", "can", "i", "go"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "O", "O", "O", "O"]}
{"sentence": "i need some high priced broth", "tokens": ["i", "need", "some", "high", "priced", "broth"], "ner_tags": ["O", "O", "O", "B-Price", "O", "B-Dish"]}
{"sentence": "is there a boston market in seattle", "tokens": ["is", "there", "a", "boston", "market", "in", "seattle"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location"]}
{"sentence": "what time does sparkys burger close", "tokens": ["what", "time", "does", "sparkys", "burger", "close"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "whats the cheapest restaurant down town thats also handicap accessible", "tokens": ["whats", "the", "cheapest", "restaurant", "down", "town", "thats", "also", "handicap", "accessible"], "ner_tags": ["O", "O", "B-Price", "O", "B-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is there a bar at cadete enterprise on west prescott street", "tokens": ["is", "there", "a", "bar", "at", "cadete", "enterprise", "on", "west", "prescott", "street"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "find me an american fast food restaurant", "tokens": ["find", "me", "an", "american", "fast", "food", "restaurant"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "I-Cuisine", "O"]}
{"sentence": "find a restaurant that is kid friendly and has quick service within 10 miles of here", "tokens": ["find", "a", "restaurant", "that", "is", "kid", "friendly", "and", "has", "quick", "service", "within", "10", "miles", "of", "here"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "does the new mexican restaurant have good reviews", "tokens": ["does", "the", "new", "mexican", "restaurant", "have", "good", "reviews"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "search restaurants with wifi access", "tokens": ["search", "restaurants", "with", "wifi", "access"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "who sells fried green tomatoes", "tokens": ["who", "sells", "fried", "green", "tomatoes"], "ner_tags": ["O", "O", "B-Dish", "I-Dish", "I-Dish"]}
{"sentence": "what halal restaurant has the best service in the area", "tokens": ["what", "halal", "restaurant", "has", "the", "best", "service", "in", "the", "area"], "ner_tags": ["O", "B-Cuisine", "O", "O", "O", "B-Rating", "I-Rating", "B-Location", "I-Location", "I-Location"]}
{"sentence": "where can i find a place to dine late at night", "tokens": ["where", "can", "i", "find", "a", "place", "to", "dine", "late", "at", "night"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "indian cuisine with smoking area", "tokens": ["indian", "cuisine", "with", "smoking", "area"], "ner_tags": ["B-Cuisine", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where is the closest chinese restuarant", "tokens": ["where", "is", "the", "closest", "chinese", "restuarant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "who has specials for valentines day", "tokens": ["who", "has", "specials", "for", "valentines", "day"], "ner_tags": ["O", "O", "B-Amenity", "O", "B-Amenity", "O"]}
{"sentence": "looking for a boston market", "tokens": ["looking", "for", "a", "boston", "market"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "any bars around that are open before 11 a m", "tokens": ["any", "bars", "around", "that", "are", "open", "before", "11", "a", "m"], "ner_tags": ["O", "B-Cuisine", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "what time does applebees in san leandro open", "tokens": ["what", "time", "does", "applebees", "in", "san", "leandro", "open"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location", "B-Hours"]}
{"sentence": "i need the nearest burger king you can find", "tokens": ["i", "need", "the", "nearest", "burger", "king", "you", "can", "find"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O"]}
{"sentence": "where can i find afghan food near here", "tokens": ["where", "can", "i", "find", "afghan", "food", "near", "here"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "find me a cheap restaurant for a date", "tokens": ["find", "me", "a", "cheap", "restaurant", "for", "a", "date"], "ner_tags": ["O", "O", "O", "B-Price", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "where is the nearest open restaurant", "tokens": ["where", "is", "the", "nearest", "open", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Hours", "O"]}
{"sentence": "i need a mexican restaurant on the west side of town and a list of their specialties how would i get there", "tokens": ["i", "need", "a", "mexican", "restaurant", "on", "the", "west", "side", "of", "town", "and", "a", "list", "of", "their", "specialties", "how", "would", "i", "get", "there"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "i would like to find a chinese restaurant", "tokens": ["i", "would", "like", "to", "find", "a", "chinese", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "what places are known for pies in baltimore", "tokens": ["what", "places", "are", "known", "for", "pies", "in", "baltimore"], "ner_tags": ["O", "O", "O", "B-Rating", "O", "B-Dish", "O", "B-Location"]}
{"sentence": "feed me", "tokens": ["feed", "me"], "ner_tags": ["O", "O"]}
{"sentence": "i dont like to breathe in other peoples cigarette smoke what restaurants are good at enforcing the no smoking policy in outdoor seating", "tokens": ["i", "dont", "like", "to", "breathe", "in", "other", "peoples", "cigarette", "smoke", "what", "restaurants", "are", "good", "at", "enforcing", "the", "no", "smoking", "policy", "in", "outdoor", "seating"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Rating", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "is cafe abbondanza in fenway an outdoor place", "tokens": ["is", "cafe", "abbondanza", "in", "fenway", "an", "outdoor", "place"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does cherrywoods barbeque have a wonderful atmosphere", "tokens": ["does", "cherrywoods", "barbeque", "have", "a", "wonderful", "atmosphere"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where can i find meat dishes with great prices", "tokens": ["where", "can", "i", "find", "meat", "dishes", "with", "great", "prices"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Price", "O"]}
{"sentence": "find me a place for brunch where celebrities hangout", "tokens": ["find", "me", "a", "place", "for", "brunch", "where", "celebrities", "hangout"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "please find me a fast food joint within a mile of here", "tokens": ["please", "find", "me", "a", "fast", "food", "joint", "within", "a", "mile", "of", "here"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "are there any sushi bars near central park", "tokens": ["are", "there", "any", "sushi", "bars", "near", "central", "park"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "can you find a place to watch people where they serve cajun food for a somewhat high price", "tokens": ["can", "you", "find", "a", "place", "to", "watch", "people", "where", "they", "serve", "cajun", "food", "for", "a", "somewhat", "high", "price"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Price", "I-Price", "O"]}
{"sentence": "are we near a place to get a good bowl of southern grits", "tokens": ["are", "we", "near", "a", "place", "to", "get", "a", "good", "bowl", "of", "southern", "grits"], "ner_tags": ["O", "O", "B-Location", "O", "O", "O", "O", "O", "B-Rating", "B-Dish", "I-Dish", "I-Dish", "I-Dish"]}
{"sentence": "find me a restaurant that doesnt allow smoking inside", "tokens": ["find", "me", "a", "restaurant", "that", "doesnt", "allow", "smoking", "inside"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "where is there a krickets korner featuring live music nearby", "tokens": ["where", "is", "there", "a", "krickets", "korner", "featuring", "live", "music", "nearby"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity", "B-Location"]}
{"sentence": "where can i get sushi in san francisco", "tokens": ["where", "can", "i", "get", "sushi", "in", "san", "francisco"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "O", "B-Location", "I-Location"]}
{"sentence": "tell me the cheapest restaurant in this city with a good rating", "tokens": ["tell", "me", "the", "cheapest", "restaurant", "in", "this", "city", "with", "a", "good", "rating"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "B-Location", "I-Location", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "i am in the mood for pizza whats the nearest place", "tokens": ["i", "am", "in", "the", "mood", "for", "pizza", "whats", "the", "nearest", "place"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "O", "O", "B-Location", "O"]}
{"sentence": "can i get a good snack somewhere", "tokens": ["can", "i", "get", "a", "good", "snack", "somewhere"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "B-Cuisine", "O"]}
{"sentence": "hungry for dessert right now", "tokens": ["hungry", "for", "dessert", "right", "now"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O"]}
{"sentence": "can you get the phone numbers of all pizza delivery places within 10 miles", "tokens": ["can", "you", "get", "the", "phone", "numbers", "of", "all", "pizza", "delivery", "places", "within", "10", "miles"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "B-Amenity", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "im craving burger find me one please", "tokens": ["im", "craving", "burger", "find", "me", "one", "please"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "O", "O"]}
{"sentence": "anything good to eat within 10 miles", "tokens": ["anything", "good", "to", "eat", "within", "10", "miles"], "ner_tags": ["O", "B-Rating", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "how far is closest korean restaurant", "tokens": ["how", "far", "is", "closest", "korean", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "are there any good restaurants in the area", "tokens": ["are", "there", "any", "good", "restaurants", "in", "the", "area"], "ner_tags": ["O", "O", "O", "B-Rating", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "where is the closest authentic chinese restaurant with take out", "tokens": ["where", "is", "the", "closest", "authentic", "chinese", "restaurant", "with", "take", "out"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "give me a good place to eat", "tokens": ["give", "me", "a", "good", "place", "to", "eat"], "ner_tags": ["O", "O", "O", "B-Rating", "O", "O", "O"]}
{"sentence": "where on 3 rd street could i find a colombian restaurant with reasonable prices", "tokens": ["where", "on", "3", "rd", "street", "could", "i", "find", "a", "colombian", "restaurant", "with", "reasonable", "prices"], "ner_tags": ["O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Price", "O"]}
{"sentence": "find nearby restaruants", "tokens": ["find", "nearby", "restaruants"], "ner_tags": ["O", "B-Location", "O"]}
{"sentence": "where is the closest burger restaurant", "tokens": ["where", "is", "the", "closest", "burger", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "where is the closest 5 star chinese restaurant", "tokens": ["where", "is", "the", "closest", "5", "star", "chinese", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Rating", "I-Rating", "B-Cuisine", "O"]}
{"sentence": "where is a kosher restaurant with good service and offers a business breakfast", "tokens": ["where", "is", "a", "kosher", "restaurant", "with", "good", "service", "and", "offers", "a", "business", "breakfast"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Rating", "O", "O", "O", "O", "B-Amenity", "B-Hours"]}
{"sentence": "find us somewhere romantic to eat this evening", "tokens": ["find", "us", "somewhere", "romantic", "to", "eat", "this", "evening"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "O", "B-Hours", "I-Hours"]}
{"sentence": "i would like to find a restaurant", "tokens": ["i", "would", "like", "to", "find", "a", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "find a family style italian restaurant with large portions", "tokens": ["find", "a", "family", "style", "italian", "restaurant", "with", "large", "portions"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity", "B-Cuisine", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "find me a place only with outdoor dining", "tokens": ["find", "me", "a", "place", "only", "with", "outdoor", "dining"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "can you help me find a fun restaurant that serves brisket", "tokens": ["can", "you", "help", "me", "find", "a", "fun", "restaurant", "that", "serves", "brisket"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "O", "O", "O", "B-Dish"]}
{"sentence": "find me a local game stop", "tokens": ["find", "me", "a", "local", "game", "stop"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O"]}
{"sentence": "i need to find a healthy place to eat", "tokens": ["i", "need", "to", "find", "a", "healthy", "place", "to", "eat"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O"]}
{"sentence": "where can i find a decently priced mojito at 8 am", "tokens": ["where", "can", "i", "find", "a", "decently", "priced", "mojito", "at", "8", "am"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "O", "B-Cuisine", "O", "B-Hours", "I-Hours"]}
{"sentence": "i need a kid friendly restaurant that serves vegan food", "tokens": ["i", "need", "a", "kid", "friendly", "restaurant", "that", "serves", "vegan", "food"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "what resturants in this area have the highest reviews", "tokens": ["what", "resturants", "in", "this", "area", "have", "the", "highest", "reviews"], "ner_tags": ["O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "look for an italian restaurant in this area where i can order a la carte", "tokens": ["look", "for", "an", "italian", "restaurant", "in", "this", "area", "where", "i", "can", "order", "a", "la", "carte"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does dennys have any restaurants by the ocean", "tokens": ["does", "dennys", "have", "any", "restaurants", "by", "the", "ocean"], "ner_tags": ["O", "B-Restaurant_Name", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "search for a steak restaurant", "tokens": ["search", "for", "a", "steak", "restaurant"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "where is the nearest place for chinese takeout", "tokens": ["where", "is", "the", "nearest", "place", "for", "chinese", "takeout"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "B-Cuisine", "B-Amenity"]}
{"sentence": "do you have any outbacks nearby", "tokens": ["do", "you", "have", "any", "outbacks", "nearby"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "B-Location"]}
{"sentence": "what is the best thing on the menu", "tokens": ["what", "is", "the", "best", "thing", "on", "the", "menu"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "O", "O", "O"]}
{"sentence": "im starving whats around here", "tokens": ["im", "starving", "whats", "around", "here"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "who has average service thats open until 2 am", "tokens": ["who", "has", "average", "service", "thats", "open", "until", "2", "am"], "ner_tags": ["O", "O", "B-Rating", "I-Rating", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "wheres the nearest ice cream parlor", "tokens": ["wheres", "the", "nearest", "ice", "cream", "parlor"], "ner_tags": ["O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "I-Cuisine"]}
{"sentence": "does callaways serve breakfast", "tokens": ["does", "callaways", "serve", "breakfast"], "ner_tags": ["O", "B-Restaurant_Name", "O", "O"]}
{"sentence": "im looking for chinese food", "tokens": ["im", "looking", "for", "chinese", "food"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "is there a bar close by that has good bbq sauce", "tokens": ["is", "there", "a", "bar", "close", "by", "that", "has", "good", "bbq", "sauce"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Dish", "I-Dish", "I-Dish"]}
{"sentence": "find me a chinese restaurant that has a buffet", "tokens": ["find", "me", "a", "chinese", "restaurant", "that", "has", "a", "buffet"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "B-Amenity"]}
{"sentence": "find the nearest starbucks from my street", "tokens": ["find", "the", "nearest", "starbucks", "from", "my", "street"], "ner_tags": ["O", "O", "B-Location", "B-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "could you find a vietnamese restaurant that has at least a 3 star rating", "tokens": ["could", "you", "find", "a", "vietnamese", "restaurant", "that", "has", "at", "least", "a", "3", "star", "rating"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Rating", "I-Rating", "I-Rating", "I-Rating", "I-Rating", "I-Rating"]}
{"sentence": "do any of the sushi places in town take american express", "tokens": ["do", "any", "of", "the", "sushi", "places", "in", "town", "take", "american", "express"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "can you get me a fast food place that is really cheap", "tokens": ["can", "you", "get", "me", "a", "fast", "food", "place", "that", "is", "really", "cheap"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "B-Location", "O", "O", "B-Price", "I-Price"]}
{"sentence": "i need to feed these kids", "tokens": ["i", "need", "to", "feed", "these", "kids"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity"]}
{"sentence": "i want to eat a vegetarian restaurant which is the best", "tokens": ["i", "want", "to", "eat", "a", "vegetarian", "restaurant", "which", "is", "the", "best"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "B-Rating"]}
{"sentence": "whats the best place to eat on pearl street in boulder colorado", "tokens": ["whats", "the", "best", "place", "to", "eat", "on", "pearl", "street", "in", "boulder", "colorado"], "ner_tags": ["O", "O", "B-Rating", "O", "O", "O", "O", "B-Location", "I-Location", "O", "B-Location", "I-Location"]}
{"sentence": "find a mcdonald with kids playing area", "tokens": ["find", "a", "mcdonald", "with", "kids", "playing", "area"], "ner_tags": ["O", "O", "B-Restaurant_Name", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "can you find a comfortable place on shawsheen avenue that has fine dining", "tokens": ["can", "you", "find", "a", "comfortable", "place", "on", "shawsheen", "avenue", "that", "has", "fine", "dining"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "O", "O", "B-Location", "I-Location", "O", "O", "B-Cuisine", "I-Cuisine"]}
{"sentence": "is there a wendys within a mile of the mobil station", "tokens": ["is", "there", "a", "wendys", "within", "a", "mile", "of", "the", "mobil", "station"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "B-Location", "I-Location", "I-Location", "O", "O", "O", "O"]}
{"sentence": "how many eateries are in the area that have a childrens play area", "tokens": ["how", "many", "eateries", "are", "in", "the", "area", "that", "have", "a", "childrens", "play", "area"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "find an inexpensive greek restaurant", "tokens": ["find", "an", "inexpensive", "greek", "restaurant"], "ner_tags": ["O", "O", "B-Price", "B-Cuisine", "O"]}
{"sentence": "what restaurant has the best selection of alcohol", "tokens": ["what", "restaurant", "has", "the", "best", "selection", "of", "alcohol"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "I-Rating", "O", "B-Cuisine"]}
{"sentence": "where can i find a restaurant in roxbury that serves clam sauce and is open after 11 pm", "tokens": ["where", "can", "i", "find", "a", "restaurant", "in", "roxbury", "that", "serves", "clam", "sauce", "and", "is", "open", "after", "11", "pm"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Dish", "I-Dish", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "need to find steakhouse", "tokens": ["need", "to", "find", "steakhouse"], "ner_tags": ["O", "O", "O", "B-Cuisine"]}
{"sentence": "is there a smoking section in old country buffet", "tokens": ["is", "there", "a", "smoking", "section", "in", "old", "country", "buffet"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "hey do you possibly know a great resturant for a date a place that is romantic and also has different types of food", "tokens": ["hey", "do", "you", "possibly", "know", "a", "great", "resturant", "for", "a", "date", "a", "place", "that", "is", "romantic", "and", "also", "has", "different", "types", "of", "food"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Rating", "O", "B-Amenity", "I-Amenity", "I-Amenity", "O", "O", "O", "O", "B-Amenity", "O", "O", "O", "B-Cuisine", "I-Cuisine", "I-Cuisine", "I-Cuisine"]}
{"sentence": "are there any kid friendly restaurants near the park", "tokens": ["are", "there", "any", "kid", "friendly", "restaurants", "near", "the", "park"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "i want to know what chinese places still have carryout at 10 pm", "tokens": ["i", "want", "to", "know", "what", "chinese", "places", "still", "have", "carryout", "at", "10", "pm"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "where would you recommend we go for a good steak dinner", "tokens": ["where", "would", "you", "recommend", "we", "go", "for", "a", "good", "steak", "dinner"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Dish", "O"]}
{"sentence": "find me a family friendly steak house", "tokens": ["find", "me", "a", "family", "friendly", "steak", "house"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "B-Cuisine", "I-Cuisine"]}
{"sentence": "are there any german restaurants nearby that celebs hangout at", "tokens": ["are", "there", "any", "german", "restaurants", "nearby", "that", "celebs", "hangout", "at"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "get me the phone number of the closest jimmy johns", "tokens": ["get", "me", "the", "phone", "number", "of", "the", "closest", "jimmy", "johns"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "i need to find a good local restaurant do you have any suggestions", "tokens": ["i", "need", "to", "find", "a", "good", "local", "restaurant", "do", "you", "have", "any", "suggestions"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "show me where pizza hut is", "tokens": ["show", "me", "where", "pizza", "hut", "is"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "where can i get a drink and a cheap meal", "tokens": ["where", "can", "i", "get", "a", "drink", "and", "a", "cheap", "meal"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "O", "O", "B-Price", "O"]}
{"sentence": "im hungry for mexican food can you find someplace local", "tokens": ["im", "hungry", "for", "mexican", "food", "can", "you", "find", "someplace", "local"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "O", "B-Location"]}
{"sentence": "i want to get some sushi to go", "tokens": ["i", "want", "to", "get", "some", "sushi", "to", "go"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "B-Amenity", "I-Amenity"]}
{"sentence": "i am looking for a family restaurant were kids can eat free", "tokens": ["i", "am", "looking", "for", "a", "family", "restaurant", "were", "kids", "can", "eat", "free"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "i want wendys", "tokens": ["i", "want", "wendys"], "ner_tags": ["O", "O", "B-Restaurant_Name"]}
{"sentence": "is ihop offer breakfast entrees for dinner", "tokens": ["is", "ihop", "offer", "breakfast", "entrees", "for", "dinner"], "ner_tags": ["O", "B-Restaurant_Name", "O", "O", "O", "O", "O"]}
{"sentence": "is there a pizza parlour with a jukebox near here", "tokens": ["is", "there", "a", "pizza", "parlour", "with", "a", "jukebox", "near", "here"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Amenity", "B-Location", "I-Location"]}
{"sentence": "whats a recommended four star restaurant in the city", "tokens": ["whats", "a", "recommended", "four", "star", "restaurant", "in", "the", "city"], "ner_tags": ["O", "O", "B-Rating", "I-Rating", "I-Rating", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "please provide me with a list of restaurants where you can eat for free on your birthday", "tokens": ["please", "provide", "me", "with", "a", "list", "of", "restaurants", "where", "you", "can", "eat", "for", "free", "on", "your", "birthday"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "i need to find something that is generally healthy but quick and close by i have very little time to eat", "tokens": ["i", "need", "to", "find", "something", "that", "is", "generally", "healthy", "but", "quick", "and", "close", "by", "i", "have", "very", "little", "time", "to", "eat"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Amenity", "O", "B-Location", "I-Location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "is free parking available at sanfords", "tokens": ["is", "free", "parking", "available", "at", "sanfords"], "ner_tags": ["O", "B-Amenity", "I-Amenity", "O", "O", "B-Restaurant_Name"]}
{"sentence": "are there any burger kings within 5 miles of here", "tokens": ["are", "there", "any", "burger", "kings", "within", "5", "miles", "of", "here"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location", "O", "O"]}
{"sentence": "how far is the closest mexican resturant", "tokens": ["how", "far", "is", "the", "closest", "mexican", "resturant"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "list of all spanish restaurants", "tokens": ["list", "of", "all", "spanish", "restaurants"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "find a nashua bar and grill that close by and open 24 hours", "tokens": ["find", "a", "nashua", "bar", "and", "grill", "that", "close", "by", "and", "open", "24", "hours"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Cuisine", "O", "B-Location", "I-Location", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "where can i get some coffee and a bagel", "tokens": ["where", "can", "i", "get", "some", "coffee", "and", "a", "bagel"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "O", "O", "B-Dish"]}
{"sentence": "hungry for southwestern cuisine on north quincy", "tokens": ["hungry", "for", "southwestern", "cuisine", "on", "north", "quincy"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "B-Location", "I-Location"]}
{"sentence": "sushi bars that allow kids", "tokens": ["sushi", "bars", "that", "allow", "kids"], "ner_tags": ["B-Cuisine", "B-Amenity", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "let me know how to get to a wendys", "tokens": ["let", "me", "know", "how", "to", "get", "to", "a", "wendys"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "l want a restaurant with mongolian food", "tokens": ["l", "want", "a", "restaurant", "with", "mongolian", "food"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "are there any outdoor kosher restaurants nearby that are open until midnight", "tokens": ["are", "there", "any", "outdoor", "kosher", "restaurants", "nearby", "that", "are", "open", "until", "midnight"], "ner_tags": ["O", "O", "O", "B-Amenity", "B-Cuisine", "O", "B-Location", "O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "search a place with good seafood", "tokens": ["search", "a", "place", "with", "good", "seafood"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "B-Cuisine"]}
{"sentence": "which restaurant should i go to if i want the best selection of wine and the best service", "tokens": ["which", "restaurant", "should", "i", "go", "to", "if", "i", "want", "the", "best", "selection", "of", "wine", "and", "the", "best", "service"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Rating", "I-Rating", "O", "B-Dish", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "find a restaurant that sells pies to take home", "tokens": ["find", "a", "restaurant", "that", "sells", "pies", "to", "take", "home"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "whats within 2 miles and is a bit pricey", "tokens": ["whats", "within", "2", "miles", "and", "is", "a", "bit", "pricey"], "ner_tags": ["O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "B-Price", "O"]}
{"sentence": "how kid friendly is 112 eatery", "tokens": ["how", "kid", "friendly", "is", "112", "eatery"], "ner_tags": ["O", "B-Amenity", "I-Amenity", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "can you help me find the name of the floating restaurant at the sunswept pier", "tokens": ["can", "you", "help", "me", "find", "the", "name", "of", "the", "floating", "restaurant", "at", "the", "sunswept", "pier"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Location", "I-Location"]}
{"sentence": "burger king resturant", "tokens": ["burger", "king", "resturant"], "ner_tags": ["B-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "should i go to dennys or burgerking", "tokens": ["should", "i", "go", "to", "dennys", "or", "burgerking"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "O", "B-Restaurant_Name"]}
{"sentence": "can you find a vegan bakery within five miles", "tokens": ["can", "you", "find", "a", "vegan", "bakery", "within", "five", "miles"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "B-Location", "I-Location", "I-Location"]}
{"sentence": "can you find a fine dining place for me called the cookhouse", "tokens": ["can", "you", "find", "a", "fine", "dining", "place", "for", "me", "called", "the", "cookhouse"], "ner_tags": ["O", "O", "O", "O", "B-Price", "I-Price", "O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "i need a cheap place with a strong rating", "tokens": ["i", "need", "a", "cheap", "place", "with", "a", "strong", "rating"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "whats the closest restaurant off the highway", "tokens": ["whats", "the", "closest", "restaurant", "off", "the", "highway"], "ner_tags": ["O", "O", "B-Location", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "is there a restaurant serving average priced mojitos nearby", "tokens": ["is", "there", "a", "restaurant", "serving", "average", "priced", "mojitos", "nearby"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "O", "B-Dish", "B-Location"]}
{"sentence": "make a reservation for two at ruths", "tokens": ["make", "a", "reservation", "for", "two", "at", "ruths"], "ner_tags": ["O", "O", "B-Restaurant_Name", "O", "B-Rating", "O", "B-Restaurant_Name"]}
{"sentence": "im looking for nearby sitdown restaurants whose specialty is hot wings", "tokens": ["im", "looking", "for", "nearby", "sitdown", "restaurants", "whose", "specialty", "is", "hot", "wings"], "ner_tags": ["O", "O", "O", "B-Location", "B-Amenity", "I-Amenity", "B-Location", "I-Location", "O", "B-Dish", "I-Dish"]}
{"sentence": "are there any free standing cinnabon restaurants in washington dc", "tokens": ["are", "there", "any", "free", "standing", "cinnabon", "restaurants", "in", "washington", "dc"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "B-Restaurant_Name", "O", "O", "B-Location", "I-Location"]}
{"sentence": "what would be a great place to take the kids for a quick lunch", "tokens": ["what", "would", "be", "a", "great", "place", "to", "take", "the", "kids", "for", "a", "quick", "lunch"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "O", "O", "O", "O", "B-Amenity", "O", "O", "B-Amenity", "B-Hours"]}
{"sentence": "where can i get pad thai within a five mile radius", "tokens": ["where", "can", "i", "get", "pad", "thai", "within", "a", "five", "mile", "radius"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "I-Dish", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "i am looking for any 4 star restaurants in the area", "tokens": ["i", "am", "looking", "for", "any", "4", "star", "restaurants", "in", "the", "area"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "I-Rating", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "today i want 2 glasses of water", "tokens": ["today", "i", "want", "2", "glasses", "of", "water"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "the nearest hot dog stand in my city", "tokens": ["the", "nearest", "hot", "dog", "stand", "in", "my", "city"], "ner_tags": ["O", "B-Location", "B-Cuisine", "I-Cuisine", "I-Cuisine", "B-Location", "I-Location", "I-Location"]}
{"sentence": "is there a sushi place near me that has fresh sushi", "tokens": ["is", "there", "a", "sushi", "place", "near", "me", "that", "has", "fresh", "sushi"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "whats the highest rated lobster pound in maine and how far is it from me", "tokens": ["whats", "the", "highest", "rated", "lobster", "pound", "in", "maine", "and", "how", "far", "is", "it", "from", "me"], "ner_tags": ["O", "O", "B-Rating", "I-Rating", "B-Dish", "I-Dish", "O", "B-Location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "is gaetanos on grove street cheap", "tokens": ["is", "gaetanos", "on", "grove", "street", "cheap"], "ner_tags": ["O", "B-Restaurant_Name", "O", "B-Location", "I-Location", "B-Price"]}
{"sentence": "where can i find halal open late", "tokens": ["where", "can", "i", "find", "halal", "open", "late"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "B-Hours", "I-Hours"]}
{"sentence": "yes we need a to stop at five guys for a nice burger", "tokens": ["yes", "we", "need", "a", "to", "stop", "at", "five", "guys", "for", "a", "nice", "burger"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O", "B-Dish"]}
{"sentence": "find me a burger restaurant", "tokens": ["find", "me", "a", "burger", "restaurant"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "where can i find a massive sausage", "tokens": ["where", "can", "i", "find", "a", "massive", "sausage"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Dish"]}
{"sentence": "tell me where i can find the closest place to eat", "tokens": ["tell", "me", "where", "i", "can", "find", "the", "closest", "place", "to", "eat"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "O", "O", "O"]}
{"sentence": "are there nearby restaurants", "tokens": ["are", "there", "nearby", "restaurants"], "ner_tags": ["O", "O", "B-Location", "O"]}
{"sentence": "do you show a red robin nearby", "tokens": ["do", "you", "show", "a", "red", "robin", "nearby"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location"]}
{"sentence": "any places along this road serve chips", "tokens": ["any", "places", "along", "this", "road", "serve", "chips"], "ner_tags": ["O", "O", "B-Location", "I-Location", "I-Location", "O", "B-Dish"]}
{"sentence": "how long does it take to get to the wendys on bridge st", "tokens": ["how", "long", "does", "it", "take", "to", "get", "to", "the", "wendys", "on", "bridge", "st"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "find me a good japanese restaurant", "tokens": ["find", "me", "a", "good", "japanese", "restaurant"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "O"]}
{"sentence": "who serves the best pizza in town", "tokens": ["who", "serves", "the", "best", "pizza", "in", "town"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Dish", "O", "B-Location"]}
{"sentence": "i would like to find a restaurant 5 minutes from my house that makes strombolis", "tokens": ["i", "would", "like", "to", "find", "a", "restaurant", "5", "minutes", "from", "my", "house", "that", "makes", "strombolis"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "O", "O", "B-Dish"]}
{"sentence": "i am in the mood for japanese can you help", "tokens": ["i", "am", "in", "the", "mood", "for", "japanese", "can", "you", "help"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O"]}
{"sentence": "can you search for a greek restaurant", "tokens": ["can", "you", "search", "for", "a", "greek", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "whats new on panda express menu", "tokens": ["whats", "new", "on", "panda", "express", "menu"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "starving what is the closest restaurant with good food", "tokens": ["starving", "what", "is", "the", "closest", "restaurant", "with", "good", "food"], "ner_tags": ["O", "O", "O", "O", "B-Location", "O", "O", "B-Rating", "O"]}
{"sentence": "please find a restaurant in this area that parking and is open until midnight", "tokens": ["please", "find", "a", "restaurant", "in", "this", "area", "that", "parking", "and", "is", "open", "until", "midnight"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "B-Amenity", "O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "can you find a romantic afghan place", "tokens": ["can", "you", "find", "a", "romantic", "afghan", "place"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "B-Cuisine", "O"]}
{"sentence": "look for a waterfront restaurant that has a live band and serves cajun food", "tokens": ["look", "for", "a", "waterfront", "restaurant", "that", "has", "a", "live", "band", "and", "serves", "cajun", "food"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Cuisine", "O"]}
{"sentence": "can you find me a casual place to eat", "tokens": ["can", "you", "find", "me", "a", "casual", "place", "to", "eat"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O"]}
{"sentence": "how many restaurants in this town put calorie info on the menu", "tokens": ["how", "many", "restaurants", "in", "this", "town", "put", "calorie", "info", "on", "the", "menu"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "i need to find the closest restaurant with a drive thru", "tokens": ["i", "need", "to", "find", "the", "closest", "restaurant", "with", "a", "drive", "thru"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "do you know where i can sushi this late at night", "tokens": ["do", "you", "know", "where", "i", "can", "sushi", "this", "late", "at", "night"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "how many restaurants that serve japanese are there in a 5 mile radius", "tokens": ["how", "many", "restaurants", "that", "serve", "japanese", "are", "there", "in", "a", "5", "mile", "radius"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "are there any italian places nearby where celebrities hang out", "tokens": ["are", "there", "any", "italian", "places", "nearby", "where", "celebrities", "hang", "out"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "do you know if there are any mongolian restaurants around here", "tokens": ["do", "you", "know", "if", "there", "are", "any", "mongolian", "restaurants", "around", "here"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "what is the best seafood restaurant", "tokens": ["what", "is", "the", "best", "seafood", "restaurant"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "O"]}
{"sentence": "do you want to find some good french cuisine", "tokens": ["do", "you", "want", "to", "find", "some", "good", "french", "cuisine"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "are there any italian restaurants within 3 miles that have great service", "tokens": ["are", "there", "any", "italian", "restaurants", "within", "3", "miles", "that", "have", "great", "service"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is there a taco bell on main street", "tokens": ["is", "there", "a", "taco", "bell", "on", "main", "street"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "do you have directions for wendys", "tokens": ["do", "you", "have", "directions", "for", "wendys"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "are there any arbys in this area", "tokens": ["are", "there", "any", "arbys", "in", "this", "area"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "B-Location", "I-Location", "I-Location"]}
{"sentence": "i want to find a restaurant that has a salad bar and also a vegetarian menu", "tokens": ["i", "want", "to", "find", "a", "restaurant", "that", "has", "a", "salad", "bar", "and", "also", "a", "vegetarian", "menu"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Dish", "I-Dish", "O", "O", "O", "B-Amenity", "O"]}
{"sentence": "can you find me the cheapest mexican restaurant nearby", "tokens": ["can", "you", "find", "me", "the", "cheapest", "mexican", "restaurant", "nearby"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "B-Cuisine", "O", "B-Location"]}
{"sentence": "are there any good places with fresh seafood around here", "tokens": ["are", "there", "any", "good", "places", "with", "fresh", "seafood", "around", "here"], "ner_tags": ["O", "O", "O", "B-Rating", "O", "O", "B-Dish", "I-Dish", "O", "O"]}
{"sentence": "where can i get chinese food", "tokens": ["where", "can", "i", "get", "chinese", "food"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "what pricey restaurants are there around here", "tokens": ["what", "pricey", "restaurants", "are", "there", "around", "here"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "give me the best place to eat in glacier national park", "tokens": ["give", "me", "the", "best", "place", "to", "eat", "in", "glacier", "national", "park"], "ner_tags": ["O", "O", "O", "B-Rating", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "can you find me a restaurant that is family friendly", "tokens": ["can", "you", "find", "me", "a", "restaurant", "that", "is", "family", "friendly"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "tell me a close chocolate restaurant", "tokens": ["tell", "me", "a", "close", "chocolate", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "i would like to go to a steampunk flavored bar could you direct me to one", "tokens": ["i", "would", "like", "to", "go", "to", "a", "steampunk", "flavored", "bar", "could", "you", "direct", "me", "to", "one"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Cuisine", "O", "O", "O", "O", "O", "O"]}
{"sentence": "are there any family dining restaurants close by with great prices", "tokens": ["are", "there", "any", "family", "dining", "restaurants", "close", "by", "with", "great", "prices"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location", "O", "B-Price", "O"]}
{"sentence": "id like to find a cheap breakfast", "tokens": ["id", "like", "to", "find", "a", "cheap", "breakfast"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "B-Cuisine"]}
{"sentence": "are there four star restaurants in the area", "tokens": ["are", "there", "four", "star", "restaurants", "in", "the", "area"], "ner_tags": ["O", "O", "B-Rating", "I-Rating", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "im looking for a place to eat", "tokens": ["im", "looking", "for", "a", "place", "to", "eat"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "help me find a upscale restaurant with a smoking section", "tokens": ["help", "me", "find", "a", "upscale", "restaurant", "with", "a", "smoking", "section"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where is the closest pizza restaurant", "tokens": ["where", "is", "the", "closest", "pizza", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Dish", "O"]}
{"sentence": "id like to go to a bar that currently has a happy hour", "tokens": ["id", "like", "to", "go", "to", "a", "bar", "that", "currently", "has", "a", "happy", "hour"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where is a good place to eat nearby", "tokens": ["where", "is", "a", "good", "place", "to", "eat", "nearby"], "ner_tags": ["O", "O", "O", "B-Rating", "O", "O", "O", "B-Location"]}
{"sentence": "its a great day today i want to eat outside could you find a place that has outside seating", "tokens": ["its", "a", "great", "day", "today", "i", "want", "to", "eat", "outside", "could", "you", "find", "a", "place", "that", "has", "outside", "seating"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does the a and w in town have outdoor car dining", "tokens": ["does", "the", "a", "and", "w", "in", "town", "have", "outdoor", "car", "dining"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "im looking for a mongolian barbeque in the medium price range", "tokens": ["im", "looking", "for", "a", "mongolian", "barbeque", "in", "the", "medium", "price", "range"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "B-Price", "O", "O"]}
{"sentence": "can you find the restaurant marco polo", "tokens": ["can", "you", "find", "the", "restaurant", "marco", "polo"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "book me a table for 1 at the closest place that recently got a high rating for their food", "tokens": ["book", "me", "a", "table", "for", "1", "at", "the", "closest", "place", "that", "recently", "got", "a", "high", "rating", "for", "their", "food"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Location", "O", "O", "O", "O", "O", "B-Rating", "O", "O", "O", "O"]}
{"sentence": "is the kookoo cafe a good place to bring children", "tokens": ["is", "the", "kookoo", "cafe", "a", "good", "place", "to", "bring", "children"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O", "O", "O", "B-Amenity"]}
{"sentence": "what kind of food do they serve at mings", "tokens": ["what", "kind", "of", "food", "do", "they", "serve", "at", "mings"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "what is the phone number for greggs", "tokens": ["what", "is", "the", "phone", "number", "for", "greggs"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "other than eighty eight restaurant are there any fair priced restaurants nearby", "tokens": ["other", "than", "eighty", "eight", "restaurant", "are", "there", "any", "fair", "priced", "restaurants", "nearby"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O", "B-Price", "O", "O", "B-Location"]}
{"sentence": "can i eat at the bar at cj gourmet pizza", "tokens": ["can", "i", "eat", "at", "the", "bar", "at", "cj", "gourmet", "pizza"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "can you find butterfields restaurant where i can sit at the bar and eat with fair prices", "tokens": ["can", "you", "find", "butterfields", "restaurant", "where", "i", "can", "sit", "at", "the", "bar", "and", "eat", "with", "fair", "prices"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "O", "O", "O", "B-Price", "O"]}
{"sentence": "how do i get to jacksons in edinburgh", "tokens": ["how", "do", "i", "get", "to", "jacksons", "in", "edinburgh"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "O", "B-Location"]}
{"sentence": "where could i get a well priced steak", "tokens": ["where", "could", "i", "get", "a", "well", "priced", "steak"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "O", "B-Dish"]}
{"sentence": "i am looking for the best mexican food", "tokens": ["i", "am", "looking", "for", "the", "best", "mexican", "food"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Cuisine", "O"]}
{"sentence": "are there any halal restaurants within ten minutes of here that are open before seven am", "tokens": ["are", "there", "any", "halal", "restaurants", "within", "ten", "minutes", "of", "here", "that", "are", "open", "before", "seven", "am"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "bbq ribs and beer sports bar", "tokens": ["bbq", "ribs", "and", "beer", "sports", "bar"], "ner_tags": ["B-Dish", "I-Dish", "O", "B-Dish", "B-Amenity", "I-Amenity"]}
{"sentence": "find me a restaurant that has air conditioning and serves wine by the glass", "tokens": ["find", "me", "a", "restaurant", "that", "has", "air", "conditioning", "and", "serves", "wine", "by", "the", "glass"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Dish", "I-Dish", "I-Dish", "I-Dish"]}
{"sentence": "where is the nearest restaurant", "tokens": ["where", "is", "the", "nearest", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "O"]}
{"sentence": "what is the most highly recommended restaurant in this area", "tokens": ["what", "is", "the", "most", "highly", "recommended", "restaurant", "in", "this", "area"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "I-Rating", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "i would like to find japanese food in my area", "tokens": ["i", "would", "like", "to", "find", "japanese", "food", "in", "my", "area"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Location", "I-Location"]}
{"sentence": "does tempe arizona have a middle eastern eatery", "tokens": ["does", "tempe", "arizona", "have", "a", "middle", "eastern", "eatery"], "ner_tags": ["O", "B-Location", "I-Location", "O", "O", "B-Cuisine", "I-Cuisine", "I-Cuisine"]}
{"sentence": "are there any byob joints on chester st", "tokens": ["are", "there", "any", "byob", "joints", "on", "chester", "st"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "O", "B-Location", "I-Location"]}
{"sentence": "locate a restaurant with gluten free choices", "tokens": ["locate", "a", "restaurant", "with", "gluten", "free", "choices"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "B-Rating", "O"]}
{"sentence": "where is the closest pizza place", "tokens": ["where", "is", "the", "closest", "pizza", "place"], "ner_tags": ["O", "O", "O", "B-Location", "B-Dish", "O"]}
{"sentence": "is there a chinese restaurant close", "tokens": ["is", "there", "a", "chinese", "restaurant", "close"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "find me a casual independent restaurant with outdoor seating that permits dogs", "tokens": ["find", "me", "a", "casual", "independent", "restaurant", "with", "outdoor", "seating", "that", "permits", "dogs"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does mikkis have take out", "tokens": ["does", "mikkis", "have", "take", "out"], "ner_tags": ["O", "B-Restaurant_Name", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i need reservations for a business dinner", "tokens": ["i", "need", "reservations", "for", "a", "business", "dinner"], "ner_tags": ["O", "O", "B-Amenity", "O", "O", "B-Amenity", "B-Hours"]}
{"sentence": "im near usf wheres a chicago style pizza place", "tokens": ["im", "near", "usf", "wheres", "a", "chicago", "style", "pizza", "place"], "ner_tags": ["O", "B-Location", "I-Location", "O", "O", "O", "O", "B-Dish", "O"]}
{"sentence": "where are some good places to eat", "tokens": ["where", "are", "some", "good", "places", "to", "eat"], "ner_tags": ["O", "O", "O", "B-Rating", "O", "O", "O"]}
{"sentence": "navigate me to jollibees", "tokens": ["navigate", "me", "to", "jollibees"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "make reservations for red lobster at 8 pm", "tokens": ["make", "reservations", "for", "red", "lobster", "at", "8", "pm"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Hours", "I-Hours"]}
{"sentence": "im looking for a steak house that is handicapped accessible", "tokens": ["im", "looking", "for", "a", "steak", "house", "that", "is", "handicapped", "accessible"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "what kind of cuisine is served at the bistro", "tokens": ["what", "kind", "of", "cuisine", "is", "served", "at", "the", "bistro"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Cuisine"]}
{"sentence": "is there an olive garden nearby", "tokens": ["is", "there", "an", "olive", "garden", "nearby"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location"]}
{"sentence": "find the nearest restaurants", "tokens": ["find", "the", "nearest", "restaurants"], "ner_tags": ["O", "O", "B-Location", "O"]}
{"sentence": "how do i get to wendys", "tokens": ["how", "do", "i", "get", "to", "wendys"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "find a creole restaurant with live music", "tokens": ["find", "a", "creole", "restaurant", "with", "live", "music"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is there a charos bakery location with carry out and great service nearby", "tokens": ["is", "there", "a", "charos", "bakery", "location", "with", "carry", "out", "and", "great", "service", "nearby"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Rating", "I-Rating", "B-Location"]}
{"sentence": "can you give me a phone number for the nearest five star establishment", "tokens": ["can", "you", "give", "me", "a", "phone", "number", "for", "the", "nearest", "five", "star", "establishment"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Location", "B-Rating", "I-Rating", "O"]}
{"sentence": "i want some mexican food", "tokens": ["i", "want", "some", "mexican", "food"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "hello can you please get me to o gators croc and roc", "tokens": ["hello", "can", "you", "please", "get", "me", "to", "o", "gators", "croc", "and", "roc"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "do you show any fast food locations that are open", "tokens": ["do", "you", "show", "any", "fast", "food", "locations", "that", "are", "open"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "i would like to find a mcdonalds near here", "tokens": ["i", "would", "like", "to", "find", "a", "mcdonalds", "near", "here"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "B-Location", "I-Location"]}
{"sentence": "find a japanese food with sushi", "tokens": ["find", "a", "japanese", "food", "with", "sushi"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "B-Dish"]}
{"sentence": "where can i get greek food late night", "tokens": ["where", "can", "i", "get", "greek", "food", "late", "night"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "B-Hours", "I-Hours"]}
{"sentence": "where is there an expensive spanish fine dining restaurant", "tokens": ["where", "is", "there", "an", "expensive", "spanish", "fine", "dining", "restaurant"], "ner_tags": ["O", "O", "O", "O", "B-Price", "B-Cuisine", "B-Amenity", "I-Amenity", "O"]}
{"sentence": "can you help me find a romantic restaurant near here where i can order a sub", "tokens": ["can", "you", "help", "me", "find", "a", "romantic", "restaurant", "near", "here", "where", "i", "can", "order", "a", "sub"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "O", "B-Location", "I-Location", "O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "what is the cheapest buffet serving lunch", "tokens": ["what", "is", "the", "cheapest", "buffet", "serving", "lunch"], "ner_tags": ["O", "O", "O", "B-Price", "B-Amenity", "O", "B-Hours"]}
{"sentence": "can you get me directions to the closest seafood restaurant", "tokens": ["can", "you", "get", "me", "directions", "to", "the", "closest", "seafood", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "how far of a drive is the closest arbys", "tokens": ["how", "far", "of", "a", "drive", "is", "the", "closest", "arbys"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "B-Restaurant_Name"]}
{"sentence": "are there any restaurants specializing in english food within a 5 mile radius", "tokens": ["are", "there", "any", "restaurants", "specializing", "in", "english", "food", "within", "a", "5", "mile", "radius"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "find us a restaurant close by that serves fried chicken", "tokens": ["find", "us", "a", "restaurant", "close", "by", "that", "serves", "fried", "chicken"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "at which restaurants can i eat outside", "tokens": ["at", "which", "restaurants", "can", "i", "eat", "outside"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "could you find me a sushi eatery that is open late in detroit", "tokens": ["could", "you", "find", "me", "a", "sushi", "eatery", "that", "is", "open", "late", "in", "detroit"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Hours", "I-Hours", "O", "B-Location"]}
{"sentence": "what time does lunch starts at the eatery on maple street", "tokens": ["what", "time", "does", "lunch", "starts", "at", "the", "eatery", "on", "maple", "street"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "where can i find an italian restaurant", "tokens": ["where", "can", "i", "find", "an", "italian", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "i want to eat pancakes", "tokens": ["i", "want", "to", "eat", "pancakes"], "ner_tags": ["O", "O", "O", "O", "B-Dish"]}
{"sentence": "can you make me reservations for russos", "tokens": ["can", "you", "make", "me", "reservations", "for", "russos"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "wheres a great place for dancing and saucy food", "tokens": ["wheres", "a", "great", "place", "for", "dancing", "and", "saucy", "food"], "ner_tags": ["O", "O", "B-Rating", "O", "O", "B-Amenity", "O", "B-Cuisine", "O"]}
{"sentence": "call sonic burger", "tokens": ["call", "sonic", "burger"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "find the closest restaurant that serves potato pancakes", "tokens": ["find", "the", "closest", "restaurant", "that", "serves", "potato", "pancakes"], "ner_tags": ["O", "O", "B-Location", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "i need a list of places with good burgers in town", "tokens": ["i", "need", "a", "list", "of", "places", "with", "good", "burgers", "in", "town"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "B-Cuisine", "B-Dish", "B-Location", "I-Location"]}
{"sentence": "find an indian restaurant nearby", "tokens": ["find", "an", "indian", "restaurant", "nearby"], "ner_tags": ["O", "O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "is there a seafood place in the next 5 miles that has carry out", "tokens": ["is", "there", "a", "seafood", "place", "in", "the", "next", "5", "miles", "that", "has", "carry", "out"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "are there any places open for an early business breakfast other than nu way cafe", "tokens": ["are", "there", "any", "places", "open", "for", "an", "early", "business", "breakfast", "other", "than", "nu", "way", "cafe"], "ner_tags": ["O", "O", "O", "O", "B-Hours", "O", "O", "B-Hours", "B-Amenity", "B-Hours", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "find a restaurant close to me", "tokens": ["find", "a", "restaurant", "close", "to", "me"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O"]}
{"sentence": "where can i go to eat to impress on a first date", "tokens": ["where", "can", "i", "go", "to", "eat", "to", "impress", "on", "a", "first", "date"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "where can i get a sandwich the cheapest", "tokens": ["where", "can", "i", "get", "a", "sandwich", "the", "cheapest"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "O", "B-Price"]}
{"sentence": "is there a diner around here", "tokens": ["is", "there", "a", "diner", "around", "here"], "ner_tags": ["O", "O", "O", "B-Cuisine", "B-Location", "I-Location"]}
{"sentence": "what restaurants are along the way home", "tokens": ["what", "restaurants", "are", "along", "the", "way", "home"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "search for a fast food hamburger place nearby", "tokens": ["search", "for", "a", "fast", "food", "hamburger", "place", "nearby"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "I-Cuisine", "O", "B-Location"]}
{"sentence": "do they have dog treats", "tokens": ["do", "they", "have", "dog", "treats"], "ner_tags": ["O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "where can i find satisfying portions before 8 am", "tokens": ["where", "can", "i", "find", "satisfying", "portions", "before", "8", "am"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "i need a reservation for fifteen people near the water", "tokens": ["i", "need", "a", "reservation", "for", "fifteen", "people", "near", "the", "water"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "is there a taco truck close by", "tokens": ["is", "there", "a", "taco", "truck", "close", "by"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "B-Location", "I-Location"]}
{"sentence": "i am looking for an expensive restaurant", "tokens": ["i", "am", "looking", "for", "an", "expensive", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "O"]}
{"sentence": "locate fast food restaurant", "tokens": ["locate", "fast", "food", "restaurant"], "ner_tags": ["O", "B-Cuisine", "I-Cuisine", "O"]}
