{"prompts": ["假设你是一名数据隐私专家。请生成80个真实的、多样化的、可以归类为姓名的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的姓名类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成50个真实的、多样化的、可以归类为年龄的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的年龄类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成10个真实的、多样化的、可以归类为性别的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的性别类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成30个真实的、多样化的、可以归类为国籍的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的国籍类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成60个真实的、多样化的、可以归类为职业的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的职业类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成20个真实的、多样化的、可以归类为种族的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的种族类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成56个真实的、多样化的、可以归类为民族的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的民族类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成60个真实的、多样化的、可以归类为教育背景的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的教育背景类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成30个真实的、多样化的、可以归类为婚姻状况的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的婚姻状况类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成30个真实的、多样化的、可以归类为政治倾向的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的政治倾向类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成40个真实的、多样化的、可以归类为家庭成员的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的家庭成员类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成80个真实的、多样化的、可以归类为工资数额的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的工资数额类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成70个真实的、多样化的、可以归类为投资产品的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的投资产品类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成60个真实的、多样化的、可以归类为税务记录的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的税务记录类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成60个真实的、多样化的、可以归类为信用记录的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的信用记录类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成60个真实的、多样化的、可以归类为实体资产的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的实体资产类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成70个真实的、多样化的、可以归类为交易信息的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的交易信息类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成40个真实的、多样化的、可以归类为疾病的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的疾病类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成50个真实的、多样化的、可以归类为药物的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的药物类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成40个真实的、多样化的、可以归类为临床表现的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的临床表现类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成50个真实的、多样化的、可以归类为医疗程序的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的医疗程序类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成50个真实的、多样化的、可以归类为过敏信息的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的过敏信息类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成50个真实的、多样化的、可以归类为生育信息的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的生育信息类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成60个真实的、多样化的、可以归类为地理位置的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的地理位置类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n", "假设你是一名数据隐私专家。请生成70个真实的、多样化的、可以归类为行程信息的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的行程信息类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"]}