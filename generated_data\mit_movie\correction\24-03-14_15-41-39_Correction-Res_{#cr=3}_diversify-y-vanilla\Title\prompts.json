{"prompts": ["Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n2. Query: 'What character did <PERSON> play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Show me the trailer for a {{movie}} directed by GP and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Who directed The Princess Bride and {{Rocky Balboa}}?\"\nText Span: \"Rocky Balboa\"\n\n2. Query: \"who played the lead character in the World War I movie {{Paths of Glory}}\"\nText Span: \"Paths of Glory\"\n\n3. Query: \"What is the viewers' rating for the movie {{S}} directed by David Fincher?\"\nText Span: \"S\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me the trailer for a {{movie}} directed by <PERSON> and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n2. Query: 'What character did <PERSON> play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you tell me the year {{Doctor Who: The Movie}} was released and who directed it?\"\nText Span: \"Doctor Who: The Movie\"\n\n2. Query: \"What year did the director of the action-packed movie '{{Die Hard}}' win an award for best director?\"\nText Span: \"Die Hard\"\n\n3. Query: \"What is the plot of the timeless classic movie {{Casablanca}}?\"\nText Span: \"Casablanca\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'What character did <PERSON> play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n3. Query: \"Show me the trailer for a {{movie}} directed by GP and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the viewers' rating for the horror film titled '{{The Babadook}}' released in 2014?\"\nText Span: \"The Babadook\"\n\n2. Query: \"What is the plot of the {{newest movie}} starring Owen Wilson?\"\nText Span: \"newest movie\"\n\n3. Query: \"Which director is known for their work in dystopian films, like '{{Children of Men}}'?\"\nText Span: \"Children of Men\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n2. Query: \"Show me the trailer for a {{movie}} directed by <PERSON> and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n3. Query: 'What character did Tom <PERSON>s play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a movie similar to {{Bridget <PERSON>'s Diary}} that came out in the past year?\"\nText Span: \"Bridget Jones's Diary\"\n\n2. Query: \"What is the viewers' rating for {{The Hunger Games}} film series directed by Sofia Coppola?\"\nText Span: \"The Hunger Games\"\n\n3. Query: \"I'm looking for a comedy film similar to {{American Pie}}, but with a different cast.\"\nText Span: \"American Pie\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me the trailer for a {{movie}} directed by <PERSON> and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n3. Query: 'What character did Tom <PERSON>s play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the viewers' rating for {{Top Gun}}?\"\nText Span: \"Top Gun\"\n\n2. Query: \"Who directed the film {{Jolene}} that is rated R-13?\"\nText Span: \"Jolene\"\n\n3. Query: \"I'm looking for a comedy movie similar to {{Clueless}}.\"\nText Span: \"Clueless\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me the trailer for a {{movie}} directed by <PERSON> and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n3. Query: 'What character did Tom <PERSON>s play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Who directed the film {{E}}, and what year was it released?\"\nText Span: \"E\"\n\n2. Query: \"Who directed the film {{Tarzan}}?\"\nText Span: \"Tarzan\"\n\n3. Query: \"What is the viewers' rating for {{A Clockwork Orange}}?\"\nText Span: \"A Clockwork Orange\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n2. Query: 'What character did <PERSON> play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Show me the trailer for a {{movie}} directed by GP and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Which movie features a character similar to Jon Snow in {{Game of Thrones}}?\"\nText Span: \"Game of Thrones\"\n\n2. Query: \"can you play the movie promo for the film '{{A Beautiful Mind}}' to see its brilliance\"\nText Span: \"A Beautiful Mind\"\n\n3. Query: \"Show me the trailer for a {{movie}} directed by GP and released in 2019\"\nText Span: \"movie\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'What character did <PERSON> play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Show me the trailer for a {{movie}} directed by <PERSON> and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Is {{No Country for Old Men}} considered a classic film?\"\nText Span: \"No Country for Old Men\"\n\n2. Query: \"What was the first look at the next {{Batman}} movie directed by Matt Reeves?\"\nText Span: \"Batman\"\n\n3. Query: \"Show me a teaser video for the movie {{quest for treasure}} released in 2014\"\nText Span: \"quest for treasure\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me the trailer for a {{movie}} directed by <PERSON> and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n3. Query: 'What character did Tom <PERSON>s play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What year did the movie '{{Pride and Prejudice}}' with a romantic plot and a timeless song come out?\"\nText Span: \"Pride and Prejudice\"\n\n2. Query: \"what is the genre of the {{new indiana jones movie}} directed by terrence malick\"\nText Span: \"new indiana jones movie\"\n\n3. Query: \"have the {{Avengers}} movies won any awards\"\nText Span: \"Avengers\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n2. Query: \"Show me the trailer for a {{movie}} directed by <PERSON> and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n3. Query: 'What character did Tom <PERSON>s play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the plot of Jane Campion's latest movie, {{The Power of the Dog}}?\"\nText Span: \"The Power of the Dog\"\n\n2. Query: \"What is the viewers' rating for {{The Matrix}} film directed by the Wachowskis\"\nText Span: \"The Matrix\"\n\n3. Query: \"What is the viewers' rating for the dystopian society movie '{{In Time}}'?\"\nText Span: \"In Time\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n2. Query: \"Show me the trailer for a {{movie}} directed by <PERSON> and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n3. Query: 'What character did Tom <PERSON>s play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the year of the movie {{Blowing in the Wind}} and who is the director?\"\nText Span: \"Blowing in the Wind\"\n\n2. Query: \"Can you recommend a {{Room}}-related movie with a captivating plot?\"\nText Span: \"Room\"\n\n3. Query: \"Can I see a trailer for the movie {{Promo}} with Lars von Trier as the director?\"\nText Span: \"Promo\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'What character did <PERSON> play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Show me the trailer for a {{movie}} directed by <PERSON> and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What {{Movie promo}} features Cinematic brilliance\"\nText Span: \"Movie promo\"\n\n2. Query: \"What is the {{perfect}} exploitation film to watch tonight?\"\nText Span: \"perfect\"\n\n3. Query: \"What is the plot of {{The Godfather Part II}}?\"\nText Span: \"The Godfather Part II\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'What character did <PERSON> play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Show me the trailer for a {{movie}} directed by <PERSON> and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can I watch a trailer of a movie featuring the character Olaf from {{Frozen}}?\"\nText Span: \"Frozen\"\n\n2. Query: \"can I watch a one-minute trailer for the new {{James Bond}} film starring Daniel Craig\"\nText Span: \"James Bond\"\n\n3. Query: \"Show me the trailer for the movie directed by John Singleton, {{Higher Learning}}.\"\nText Span: \"Higher Learning\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me the trailer for a {{movie}} directed by <PERSON> and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n3. Query: 'What character did Tom <PERSON>s play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Is the new {{Harry Potter}} film worth watching?\"\nText Span: \"Harry Potter\"\n\n2. Query: \"What film in {{The Twilight Saga}} has the best viewers' rating?\"\nText Span: \"The Twilight Saga\"\n\n3. Query: \"Show me the trailer for {{Apocalypse}}, the movie with the song from Dirty Dancing.\"\nText Span: \"Apocalypse\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me the trailer for a {{movie}} directed by <PERSON> and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n3. Query: 'What character did Tom <PERSON>s play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"where can I find exclusive footage of the new {{Spider-Man}} movie directed by Jon Watts\"\nText Span: \"Spider-Man\"\n\n2. Query: \"Can you tell me the viewers' rating of the film {{A haunted house}}?\"\nText Span: \"A haunted house\"\n\n3. Query: \"Can you give me a glimpse of the evocative movie {{All You Need Is Love}}?\"\nText Span: \"All You Need Is Love\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me the trailer for a {{movie}} directed by <PERSON> and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n2. Query: 'What character did <PERSON> play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you play a movie clip from {{Inception}} starring Marion Cotillard?\"\nText Span: \"Inception\"\n\n2. Query: \"Can you recommend any movies that are similar to {{Brave New World}} in terms of their plot and themes?\"\nText Span: \"Brave New World\"\n\n3. Query: \"I'd like to watch a movie approved for all ages. How about something from {{The Godfather}} series?\"\nText Span: \"The Godfather\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'What character did <PERSON> play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n3. Query: \"Show me the trailer for a {{movie}} directed by GP and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the plot of {{Scarface}}?\"\nText Span: \"Scarface\"\n\n2. Query: \"Show me a trailer for the new {{Wonder Woman}} movie that includes LGBTQ+ representation\"\nText Span: \"Wonder Woman\"\n\n3. Query: \"Tell me about any movies released in the same year as {{Casablanca}}.\"\nText Span: \"Casablanca\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me the trailer for a {{movie}} directed by <PERSON> and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n2. Query: 'What character did <PERSON> play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Is the international trailer for the movie {{Rocky Balboa}} available to watch online?\"\nText Span: \"Rocky Balboa\"\n\n2. Query: \"What movie genre is best for watching with the whole family, something like {{The Sound of Music}}?\"\nText Span: \"The Sound of Music\"\n\n3. Query: \"Can you recommend a movie with a song as catchy as 'Under the Sea' from {{The Little Mermaid}}?\"\nText Span: \"The Little Mermaid\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n2. Query: 'What character did <PERSON> play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Show me the trailer for a {{movie}} directed by GP and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Who directed the film {{A}} and what is the viewers' rating for it?\"\nText Span: \"A\"\n\n2. Query: \"What year was the movie {{The Breakfast Club}} released and what is its viewers' rating?\"\nText Span: \"The Breakfast Club\"\n\n3. Query: \"Can you provide a one-minute trailer for the new {{Batman}} movie?\"\nText Span: \"Batman\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me the trailer for a {{movie}} directed by <PERSON> and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n2. Query: 'What character did <PERSON> play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Do you have any exclusive footage of the new {{Universal Studios}} movie featuring Hermione Granger?\"\nText Span: \"Universal Studios\"\n\n2. Query: \"what is the plot of the movie {{Scarecrow}}\"\nText Span: \"Scarecrow\"\n\n3. Query: \"who was the director of the film adaptation of the novel '{{to kill a mockingbird}}' and what is the viewers' rating\"\nText Span: \"to kill a mockingbird\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me the trailer for a {{movie}} directed by <PERSON> and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n3. Query: 'What character did Tom <PERSON>s play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"I want to see a trailer for the movie {{Deadpool}}. Is it rated PG-13?\"\nText Span: \"Deadpool\"\n\n2. Query: \"who directed the movie about mountain climbing called {{Everest}}\"\nText Span: \"Everest\"\n\n3. Query: \"Is Reese Witherspoon in any space opera films like {{Million Dollar Baby}}?\"\nText Span: \"Million Dollar Baby\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'What character did <PERSON> play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n3. Query: \"Show me the trailer for a {{movie}} directed by GP and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you provide me with the trailer for the movie {{Selma}} that is rated PG?\"\nText Span: \"Selma\"\n\n2. Query: \"What is the viewers' rating for the movie {{Rocky}}?\"\nText Span: \"Rocky\"\n\n3. Query: \"What is the year of release for the movie with the song {{Superb American}}?\"\nText Span: \"Superb American\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"James Bond\".\n\n2. Query: 'What character did <PERSON> play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Show me the trailer for a {{movie}} directed by GP and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you tell me the plot of the movie {{Quest for redemption}}?\"\nText Span: \"Quest for redemption\"\n\n2. Query: \"who directed the movie {{American Psycho}}\"\nText Span: \"American Psycho\"\n\n3. Query: \"Can you recommend {{A}} captivating movie preview from 1975 with brilliant performances?\"\nText Span: \"A\"", "Here is a spoken query to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Title.\nIn particular, for the given query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type title\n- (B). The span contains a named title entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not title\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named title entity must be the name of a movie. You should always drop starting \"latest\" or \"new\" and trailing \"movie\" or \"film\" words from named movie entity spans.\nGeneral reference to movies such as \"latest film\" and \"newest movie\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me the trailer for a {{movie}} directed by <PERSON> and released in 2019\"\nText Span: \"movie\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Can I find tickets for {{the new James Bond movie}}?\"\nText Span: \"the new James Bond movie\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"<PERSON>\".\n\n3. Query: 'What character did <PERSON> <PERSON>s play in \"{{Forest Gump}}\"?'\nText Span: \"Forest Gump\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following span of text:\n\nQuery: \"can you provide a plot summary for the movie {{inside out}}\"\nText Span: \"inside out\""]}