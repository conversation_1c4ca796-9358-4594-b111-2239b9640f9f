[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成 1 条包含敏感个人信息的中文句子。要求句子自然、合理、完整，包含1-4个以下的实体类型。请标记所有属于以下实体类型的命名实体：姓名, 年龄, 性别, 国籍, 职业, 种族, 民族, 教育背景, 婚姻状况, 政治倾向, 家庭成员, 工资数额, 投资产品, 税务记录, 信用记录, 实体资产, 交易信息, 疾病, 药物, 临床表现, 医疗程序, 过敏信息, 生育信息, 地理位置, 行程信息。\n请在下一行中按照出现的顺序列出这些命名实体及其对应的实体类型。\n如果在生成的句子中没有找到实体，请将方括号留空。\n以下是一些示例句子及其标注，请参考并按照这种格式生成。\n\n示例：\n\nSentence: \"中肠扭转也可出现间歇性的症状 ， 主要见于年长患儿。\"\nNamed Entities: [中 肠 扭 转 也 (临床表现)]\n\nSentence: \"有研究显示 ， 这种方法的远期疗效无论从临床上还是各种检查上 ， 都显示出很高的失败率 ， 尤其在重度 G E R D 患者中。\"\nNamed Entities: [G E R D 患 (疾病)]\n\nSentence: \"中行有关负责人也表示 ， 目前相关工作正在有序进行。\"\nNamed Entities: [有 关 负 责 人 (职业)]\n\nSentence: \"鉴别困难时需行肾活体组织检查。\"\nNamed Entities: [肾 活 体 组 织 检 查 。 (医疗程序)]\n\nSentence: \"每年 6 月的最后一个星期 ， 索维拉会举办著名的纳瓦世界音乐节 ，\"\nNamed Entities: [索 维 拉 (地理位置)]\n\nSentence: \"“这次有一本《集千家注杜工部诗集》 ， 这是明嘉靖明易山人校刻大字本 ， 共十二册 ， 白棉纸线装。 ”\"\nNamed Entities: [明 易 山 人 (姓名)]\n\nSentence: \"蛋白质 2 ～ 4 g / （ k g • d ） （ 母乳 2 g / k g ， 牛乳 3. 5 g / k g ， 大豆蛋白 4 g / k g ） 。\"\nNamed Entities: [蛋 白 质 2 (药物)]\n\n\n---\n\n此外，生成的敏感数据内容应该遵循以下要求：\n主要包含个人信息类敏感实体."}], "temperature": 1, "max_tokens": 1024, "logprobs": true, "seed": 70}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\nSentence: \"李华，男，35岁，汉族，是这家公司的项目经理，他的家庭住址在北京市朝阳区建国路88号。\"\nNamed Entities: [李 华 (姓名), 男 (性别), 35 岁 (年龄), 汉族 (民族), 经理 (职业), 北京市朝阳区建国路88号 (地理位置)]", "role": "assistant"}}], "created": 1745503480, "id": "20250424220439d9f4fb1e94334014", "model": "glm-4-air-250414", "request_id": "20250424220439d9f4fb1e94334014", "usage": {"completion_tokens": 73, "prompt_tokens": 532, "total_tokens": 605}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成 1 条包含敏感个人信息的中文句子。要求句子自然、合理、完整，包含1-4个以下的实体类型。请标记所有属于以下实体类型的命名实体：姓名, 年龄, 性别, 国籍, 职业, 种族, 民族, 教育背景, 婚姻状况, 政治倾向, 家庭成员, 工资数额, 投资产品, 税务记录, 信用记录, 实体资产, 交易信息, 疾病, 药物, 临床表现, 医疗程序, 过敏信息, 生育信息, 地理位置, 行程信息。\n请在下一行中按照出现的顺序列出这些命名实体及其对应的实体类型。\n如果在生成的句子中没有找到实体，请将方括号留空。\n以下是一些示例句子及其标注，请参考并按照这种格式生成。\n\n示例：\n\nSentence: \"每年 6 月的最后一个星期 ， 索维拉会举办著名的纳瓦世界音乐节 ，\"\nNamed Entities: [索 维 拉 (地理位置)]\n\nSentence: \"“这次有一本《集千家注杜工部诗集》 ， 这是明嘉靖明易山人校刻大字本 ， 共十二册 ， 白棉纸线装。 ”\"\nNamed Entities: [明 易 山 人 (姓名)]\n\nSentence: \"鉴别困难时需行肾活体组织检查。\"\nNamed Entities: [肾 活 体 组 织 检 查 。 (医疗程序)]\n\nSentence: \"中肠扭转也可出现间歇性的症状 ， 主要见于年长患儿。\"\nNamed Entities: [中 肠 扭 转 也 (临床表现)]\n\nSentence: \"蛋白质 2 ～ 4 g / （ k g • d ） （ 母乳 2 g / k g ， 牛乳 3. 5 g / k g ， 大豆蛋白 4 g / k g ） 。\"\nNamed Entities: [蛋 白 质 2 (药物)]\n\nSentence: \"有研究显示 ， 这种方法的远期疗效无论从临床上还是各种检查上 ， 都显示出很高的失败率 ， 尤其在重度 G E R D 患者中。\"\nNamed Entities: [G E R D 患 (疾病)]\n\nSentence: \"中行有关负责人也表示 ， 目前相关工作正在有序进行。\"\nNamed Entities: [有 关 负 责 人 (职业)]\n\n\n---\n\n此外，生成的敏感数据内容应该遵循以下要求：\n1. 主要包含个人信息类敏感实体;\n2. 语言语气为日常语气."}], "temperature": 1, "max_tokens": 1024, "logprobs": true, "seed": 42}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\nSentence: \"张伟今年35岁，是一位在北京工作的软件工程师，他患有糖尿病，需要定期服用胰岛素。\"\nNamed Entities: [张 伟 (姓名), 35 (年龄), 软 件 工 程 师 (职业), 北 京 (地理位置), 糖 尿 病 (疾病), 胰 岛 素 (药物)]", "role": "assistant"}}], "created": 1745503480, "id": "202504242204395e677d8b98974c7e", "model": "glm-4-air-250414", "request_id": "202504242204395e677d8b98974c7e", "usage": {"completion_tokens": 82, "prompt_tokens": 542, "total_tokens": 624}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成 1 条包含敏感个人信息的中文句子。要求句子自然、合理、完整，包含1-4个以下的实体类型。请标记所有属于以下实体类型的命名实体：姓名, 年龄, 性别, 国籍, 职业, 种族, 民族, 教育背景, 婚姻状况, 政治倾向, 家庭成员, 工资数额, 投资产品, 税务记录, 信用记录, 实体资产, 交易信息, 疾病, 药物, 临床表现, 医疗程序, 过敏信息, 生育信息, 地理位置, 行程信息。\n请在下一行中按照出现的顺序列出这些命名实体及其对应的实体类型。\n如果在生成的句子中没有找到实体，请将方括号留空。\n以下是一些示例句子及其标注，请参考并按照这种格式生成。\n\n示例：\n\nSentence: \"中肠扭转也可出现间歇性的症状 ， 主要见于年长患儿。\"\nNamed Entities: [中 肠 扭 转 也 (临床表现)]\n\nSentence: \"每年 6 月的最后一个星期 ， 索维拉会举办著名的纳瓦世界音乐节 ，\"\nNamed Entities: [索 维 拉 (地理位置)]\n\nSentence: \"有研究显示 ， 这种方法的远期疗效无论从临床上还是各种检查上 ， 都显示出很高的失败率 ， 尤其在重度 G E R D 患者中。\"\nNamed Entities: [G E R D 患 (疾病)]\n\nSentence: \"鉴别困难时需行肾活体组织检查。\"\nNamed Entities: [肾 活 体 组 织 检 查 。 (医疗程序)]\n\nSentence: \"中行有关负责人也表示 ， 目前相关工作正在有序进行。\"\nNamed Entities: [有 关 负 责 人 (职业)]\n\nSentence: \"蛋白质 2 ～ 4 g / （ k g • d ） （ 母乳 2 g / k g ， 牛乳 3. 5 g / k g ， 大豆蛋白 4 g / k g ） 。\"\nNamed Entities: [蛋 白 质 2 (药物)]\n\nSentence: \"“这次有一本《集千家注杜工部诗集》 ， 这是明嘉靖明易山人校刻大字本 ， 共十二册 ， 白棉纸线装。 ”\"\nNamed Entities: [明 易 山 人 (姓名)]\n\n\n---\n\n此外，生成的敏感数据内容应该遵循以下要求：\n1. 主要包含健康与医疗信息类敏感实体;\n2. 数据上下文为医疗记录;\n3. 数据使用意图为个性化服务."}], "temperature": 1, "max_tokens": 1024, "logprobs": true, "seed": 56}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\nSentence: \"张伟（[张 伟 (姓名)]），一名[男 (性别)]患者，今年[35 (年龄)]岁，因反复发作的[高血压 (疾病)]前来就诊，医生建议其长期服用[降压药 (药物)]并定期复查[肾功能 (临床表现)]。\"\nNamed Entities: [张 伟 (姓名), 男 (性别), 35 (年龄), 高血压 (疾病), 降压药 (药物), 肾功能 (临床表现)]", "role": "assistant"}}], "created": 1745503481, "id": "202504242204391c0f6cae1f2444b8", "model": "glm-4-air-250414", "request_id": "202504242204391c0f6cae1f2444b8", "usage": {"completion_tokens": 102, "prompt_tokens": 555, "total_tokens": 657}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成 1 条包含敏感个人信息的中文句子。要求句子自然、合理、完整，包含1-4个以下的实体类型。请标记所有属于以下实体类型的命名实体：姓名, 年龄, 性别, 国籍, 职业, 种族, 民族, 教育背景, 婚姻状况, 政治倾向, 家庭成员, 工资数额, 投资产品, 税务记录, 信用记录, 实体资产, 交易信息, 疾病, 药物, 临床表现, 医疗程序, 过敏信息, 生育信息, 地理位置, 行程信息。\n请在下一行中按照出现的顺序列出这些命名实体及其对应的实体类型。\n如果在生成的句子中没有找到实体，请将方括号留空。\n以下是一些示例句子及其标注，请参考并按照这种格式生成。\n\n示例：\n\nSentence: \"鉴别困难时需行肾活体组织检查。\"\nNamed Entities: [肾 活 体 组 织 检 查 。 (医疗程序)]\n\nSentence: \"中行有关负责人也表示 ， 目前相关工作正在有序进行。\"\nNamed Entities: [有 关 负 责 人 (职业)]\n\nSentence: \"有研究显示 ， 这种方法的远期疗效无论从临床上还是各种检查上 ， 都显示出很高的失败率 ， 尤其在重度 G E R D 患者中。\"\nNamed Entities: [G E R D 患 (疾病)]\n\nSentence: \"“这次有一本《集千家注杜工部诗集》 ， 这是明嘉靖明易山人校刻大字本 ， 共十二册 ， 白棉纸线装。 ”\"\nNamed Entities: [明 易 山 人 (姓名)]\n\nSentence: \"蛋白质 2 ～ 4 g / （ k g • d ） （ 母乳 2 g / k g ， 牛乳 3. 5 g / k g ， 大豆蛋白 4 g / k g ） 。\"\nNamed Entities: [蛋 白 质 2 (药物)]\n\nSentence: \"每年 6 月的最后一个星期 ， 索维拉会举办著名的纳瓦世界音乐节 ，\"\nNamed Entities: [索 维 拉 (地理位置)]\n\nSentence: \"中肠扭转也可出现间歇性的症状 ， 主要见于年长患儿。\"\nNamed Entities: [中 肠 扭 转 也 (临床表现)]\n\n\n---\n\n此外，生成的敏感数据内容应该遵循以下要求：\n1. 主要包含个人信息类敏感实体;\n2. 数据敏感级别为最高敏感级别."}], "temperature": 1, "max_tokens": 1024, "logprobs": true, "seed": 49}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\nSentence: \"张伟，一位42岁的汉族男性，在北京市海淀区居住，他的家庭医生记录显示他有高血压病史，目前正在服用降压药物氨氯地平，且对青霉素过敏。\"\nNamed Entities: [张 伟 (姓名), 42 岁 (年龄), 汉 族 (民族), 男 性 (性别), 北 京 市 海 淀 区 (地理位置), 高 血 压 病 史 (疾病), 氨 氯 地 平 (药物), 青 霉 素 (过敏信息)]", "role": "assistant"}}], "created": 1745503481, "id": "20250424220439c3043345e4f74430", "model": "glm-4-air-250414", "request_id": "20250424220439c3043345e4f74430", "usage": {"completion_tokens": 115, "prompt_tokens": 544, "total_tokens": 659}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成 1 条包含敏感个人信息的中文句子。要求句子自然、合理、完整，包含1-4个以下的实体类型。请标记所有属于以下实体类型的命名实体：姓名, 年龄, 性别, 国籍, 职业, 种族, 民族, 教育背景, 婚姻状况, 政治倾向, 家庭成员, 工资数额, 投资产品, 税务记录, 信用记录, 实体资产, 交易信息, 疾病, 药物, 临床表现, 医疗程序, 过敏信息, 生育信息, 地理位置, 行程信息。\n请在下一行中按照出现的顺序列出这些命名实体及其对应的实体类型。\n如果在生成的句子中没有找到实体，请将方括号留空。\n以下是一些示例句子及其标注，请参考并按照这种格式生成。\n\n示例：\n\nSentence: \"鉴别困难时需行肾活体组织检查。\"\nNamed Entities: [肾 活 体 组 织 检 查 。 (医疗程序)]\n\nSentence: \"中肠扭转也可出现间歇性的症状 ， 主要见于年长患儿。\"\nNamed Entities: [中 肠 扭 转 也 (临床表现)]\n\nSentence: \"“这次有一本《集千家注杜工部诗集》 ， 这是明嘉靖明易山人校刻大字本 ， 共十二册 ， 白棉纸线装。 ”\"\nNamed Entities: [明 易 山 人 (姓名)]\n\nSentence: \"蛋白质 2 ～ 4 g / （ k g • d ） （ 母乳 2 g / k g ， 牛乳 3. 5 g / k g ， 大豆蛋白 4 g / k g ） 。\"\nNamed Entities: [蛋 白 质 2 (药物)]\n\nSentence: \"有研究显示 ， 这种方法的远期疗效无论从临床上还是各种检查上 ， 都显示出很高的失败率 ， 尤其在重度 G E R D 患者中。\"\nNamed Entities: [G E R D 患 (疾病)]\n\nSentence: \"每年 6 月的最后一个星期 ， 索维拉会举办著名的纳瓦世界音乐节 ，\"\nNamed Entities: [索 维 拉 (地理位置)]\n\nSentence: \"中行有关负责人也表示 ， 目前相关工作正在有序进行。\"\nNamed Entities: [有 关 负 责 人 (职业)]\n\n\n---\n\n此外，生成的敏感数据内容应该遵循以下要求：\n1. 主要包含健康与医疗信息类敏感实体;\n2. 数据上下文为企业内部数据."}], "temperature": 1, "max_tokens": 1024, "logprobs": true, "seed": 63}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\nSentence: \"根据内部报告，李华（[姓名]）因持续[高血压（[疾病]）]需定期服用[降压药（[药物]）]，并记录其[头痛（[临床表现]）]症状及每月[肝功能检查（[医疗程序]）]结果。\"\nNamed Entities: [李 华 (姓名), 高 血 压 (疾病), 降 压 药 (药物), 头 痛 (临床表现), 肝 功 能 检 查 (医疗程序)]", "role": "assistant"}}], "created": 1745503482, "id": "2025042422043957ed41f421c245de", "model": "glm-4-air-250414", "request_id": "2025042422043957ed41f421c245de", "usage": {"completion_tokens": 117, "prompt_tokens": 546, "total_tokens": 663}}]
