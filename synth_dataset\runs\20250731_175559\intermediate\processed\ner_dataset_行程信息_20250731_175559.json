[{"text": "我计划下周三早上8:30从北京首都国际机场起飞前往上海浦东国际机场。", "label": [{"entity": "北京首都国际机场起飞前往上海浦东国际机场", "start_idx": 18, "end_idx": 33, "type": "行程信息"}]}, {"text": "我们预订了2024年1月15日下午3点的航班，从广州白云机场飞往成都双流机场。", "label": [{"entity": "2024年1月15日下午3点", "start_idx": 5, "end_idx": 15, "type": "行程信息"}, {"entity": "广州白云机场", "start_idx": 21, "end_idx": 27, "type": "行程信息"}, {"entity": "成都双流机场", "start_idx": 32, "end_idx": 40, "type": "行程信息"}]}, {"text": "火车将于2023年12月31日晚上10:05从西安北站出发，终点是北京西站。", "label": [{"entity": "2023年12月31日晚上10:05", "start_idx": 4, "end_idx": 15, "type": "行程信息"}, {"entity": "西安北站", "start_idx": 22, "end_idx": 27, "type": "行程信息"}, {"entity": "北京西站", "start_idx": 35, "end_idx": 40, "type": "行程信息"}]}, {"text": "请确认您的航班号CA1234，起飞时间是2024年2月20日早上7:45。", "label": [{"entity": "航班号CA1234", "start_idx": 8, "end_idx": 16, "type": "行程信息"}, {"entity": "2024年2月20日", "start_idx": 20, "end_idx": 28, "type": "行程信息"}, {"entity": "早上7:45", "start_idx": 29, "end_idx": 35, "type": "行程信息"}]}, {"text": "您需要提前到达上海虹桥火车站，因为列车将在2024年3月1日早上6:50发车。", "label": [{"entity": "上海虹桥火车站", "start_idx": 11, "end_idx": 20, "type": "行程信息"}, {"entity": "2024年3月1日早上6:50", "start_idx": 29, "end_idx": 41, "type": "行程信息"}]}, {"text": "我们将于2023年10月5日下午4:10从深圳宝安机场起飞，飞往厦门高崎机场。", "label": [{"entity": "2023年10月5日下午4:10", "start_idx": 5, "end_idx": 19, "type": "行程信息"}, {"entity": "深圳宝安机场", "start_idx": 25, "end_idx": 32, "type": "行程信息"}, {"entity": "厦门高崎机场", "start_idx": 39, "end_idx": 46, "type": "行程信息"}]}, {"text": "请注意，高铁票显示2024年4月10日晚上8:20从武汉站出发，到达长沙南站。", "label": [{"entity": "2024年4月10日晚上8:20", "start_idx": 8, "end_idx": 19, "type": "行程信息"}, {"entity": "武汉站", "start_idx": 23, "end_idx": 26, "type": "行程信息"}, {"entity": "长沙南站", "start_idx": 31, "end_idx": 35, "type": "行程信息"}]}, {"text": "我的行程安排是2023年9月25日早上9:00从重庆江北机场飞往昆明长水机场。", "label": [{"entity": "从重庆江北机场飞往昆明长水机场", "start_idx": 22, "end_idx": 39, "type": "行程信息"}]}, {"text": "我计划在2024年5月15日乘坐MU5137航班从北京首都国际机场出发。", "label": [{"entity": "2024年5月15日", "start_idx": 5, "end_idx": 13, "type": "行程信息"}, {"entity": "MU5137航班", "start_idx": 19, "end_idx": 26, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 32, "end_idx": 43, "type": "行程信息"}]}, {"text": "明天早上8:30，我需要从上海市静安区南京西路1266号出发前往火车站。", "label": [{"entity": "火车站", "start_idx": 28, "end_idx": 31, "type": "行程信息"}]}]