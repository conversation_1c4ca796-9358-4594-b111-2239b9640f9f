{"prompts": ["Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Actor.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type actor\n- (B). The span contains a named actor entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not actor\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Trailer, Song, Review, Character, other].\n\nA named actor entity must be the name of an actor or actress. Only first names, last names, and full names of actors and actresses are considered named actor entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'What character did {{<PERSON> Hanks}} play in \"Forest Gump\"?'\nText Span: \"Tom Hanks\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: 'What is the name of the {{actor}} who played the main character in \"The Shawshank Redemption\"?'\nText Span: \"actor\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you play a movie clip from Inception starring {{Marion Cotillard}}?\"\nText Span: \"Marion Cotillard\"\n\n2. Query: \"Can you recommend a good vampire movie starring {{Brie Larson}} that has a viewers' rating of 8 out of 10?\"\nText Span: \"Brie Larson\"\n\n3. Query: \"Is {{Tyrion Lannister}} in the movie Singin' in the Rain?\"\nText Span: \"Tyrion Lannister\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Actor.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type actor\n- (B). The span contains a named actor entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not actor\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Trailer, Song, Review, Character, other].\n\nA named actor entity must be the name of an actor or actress. Only first names, last names, and full names of actors and actresses are considered named actor entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'What is the name of the {{actor}} who played the main character in \"The Shawshank Redemption\"?'\nText Span: \"actor\"\nLabel: (D). Not a Named Entity.\n\n2. Query: 'What character did {{<PERSON>}} play in \"Forest Gump\"?'\nText Span: \"Tom Hanks\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the plot of the newest movie starring {{<PERSON>}}?\"\nText Span: \"<PERSON> <PERSON>\"\n\n2. Query: \"What compelling movie came out in 1950 starring {{<PERSON> Bogart}} and <PERSON> Hepburn?\"\nText Span: \"<PERSON> Bogart\"\n\n3. Query: \"Can you recommend a movie with a great soundtrack that {{<PERSON> Weas<PERSON>}} starred in?\"\nText Span: \"<PERSON> Weas<PERSON>\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Actor.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type actor\n- (B). The span contains a named actor entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not actor\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Trailer, Song, Review, Character, other].\n\nA named actor entity must be the name of an actor or actress. Only first names, last names, and full names of actors and actresses are considered named actor entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'What is the name of the {{actor}} who played the main character in \"The Shawshank Redemption\"?'\nText Span: \"actor\"\nLabel: (D). Not a Named Entity.\n\n2. Query: 'What character did {{<PERSON>}} play in \"Forest Gump\"?'\nText Span: \"Tom Hanks\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"I'm looking for a thrilling movie involving corporate espionage with <PERSON> or {{Kerry Washington}} in the cast. Do you have any suggestions?\"\nText Span: \"Kerry Washington\"\n\n2. Query: \"What is the viewers' rating for the most recent movie starring {{Ryan Gosling}}?\"\nText Span: \"Ryan Gosling\"\n\n3. Query: \"Show me a review of the latest film starring {{Tom Hanks}}\"\nText Span: \"<PERSON> <PERSON>s\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Actor.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type actor\n- (B). The span contains a named actor entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not actor\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Trailer, Song, Review, Character, other].\n\nA named actor entity must be the name of an actor or actress. Only first names, last names, and full names of actors and actresses are considered named actor entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'What character did {{<PERSON>}} play in \"Forest Gump\"?'\nText Span: \"Tom Hanks\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: 'What is the name of the {{actor}} who played the main character in \"The Shawshank Redemption\"?'\nText Span: \"actor\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the plot of the latest movie featuring <PERSON> <PERSON> and {{Idris Elba}}?\"\nText Span: \"Idris Elba\"\n\n2. Query: \"Show me a trailer for a movie with a high viewers' rating that stars {{Jon Favreau}}.\"\nText Span: \"Jon Favreau\"\n\n3. Query: \"What is the viewers' rating for the movie directed by Woody Allen with {{Mark Ruffalo}} in it?\"\nText Span: \"Mark Ruffalo\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Actor.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type actor\n- (B). The span contains a named actor entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not actor\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Trailer, Song, Review, Character, other].\n\nA named actor entity must be the name of an actor or actress. Only first names, last names, and full names of actors and actresses are considered named actor entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'What character did {{<PERSON>}} play in \"Forest Gump\"?'\nText Span: \"Tom Hanks\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: 'What is the name of the {{actor}} who played the main character in \"The Shawshank Redemption\"?'\nText Span: \"actor\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Is there a movie with the song Superstition by {{<PERSON>}} in the soundtrack?\"\nText Span: \"<PERSON> Wonder\"\n\n2. Query: \"What is the viewers' rating for the movie directed by <PERSON> <PERSON><PERSON> and starring {{<PERSON> L. <PERSON>}}?\"\nText Span: \"<PERSON> L. <PERSON>\"\n\n3. Query: \"I'm looking for a thrilling movie involving corporate espionage with {{<PERSON> <PERSON>}} or <PERSON> Washington in the cast. Do you have any suggestions?\"\nText Span: \"<PERSON> <PERSON>\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Actor.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type actor\n- (B). The span contains a named actor entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not actor\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Trailer, Song, Review, Character, other].\n\nA named actor entity must be the name of an actor or actress. Only first names, last names, and full names of actors and actresses are considered named actor entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'What is the name of the {{actor}} who played the main character in \"The Shawshank Redemption\"?'\nText Span: \"actor\"\nLabel: (D). Not a Named Entity.\n\n2. Query: 'What character did {{<PERSON>}} play in \"Forest Gump\"?'\nText Span: \"Tom Hanks\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a movie with a song by {{<PERSON>}}?\"\nText Span: \"Beyoncé\"\n\n2. Query: \"Who directed the highly-rated action movie from the 90s starring {{Brad Pitt}}?\"\nText Span: \"Brad Pitt\"\n\n3. Query: \"What are some overrated highlights of {{Joffrey Baratheon}}'s acting career?\"\nText Span: \"Joffrey Baratheon\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Actor.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type actor\n- (B). The span contains a named actor entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not actor\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Trailer, Song, Review, Character, other].\n\nA named actor entity must be the name of an actor or actress. Only first names, last names, and full names of actors and actresses are considered named actor entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'What character did {{<PERSON>}} play in \"Forest Gump\"?'\nText Span: \"Tom Hanks\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: 'What is the name of the {{actor}} who played the main character in \"The Shawshank Redemption\"?'\nText Span: \"actor\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is {{<PERSON>}}' highest-rated movie according to viewers\"\nText Span: \"Mel Brooks\"\n\n2. Query: \"What famous book was turned into a movie starring {{Angelina Jolie}}?\"\nText Span: \"Angelina Jolie\"\n\n3. Query: \"Can you recommend a movie that involves family secrets and features an unforgettable performance by {{Viggo Mortensen}}?\"\nText Span: \"Viggo Mortensen\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Actor.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type actor\n- (B). The span contains a named actor entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not actor\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Trailer, Song, Review, Character, other].\n\nA named actor entity must be the name of an actor or actress. Only first names, last names, and full names of actors and actresses are considered named actor entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'What character did {{<PERSON>}} play in \"Forest Gump\"?'\nText Span: \"Tom Hanks\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: 'What is the name of the {{actor}} who played the main character in \"The Shawshank Redemption\"?'\nText Span: \"actor\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"I want to watch a thriller directed by {{<PERSON>}}'s favorite director.\"\nText Span: \"Tom Cruise\"\n\n2. Query: \"Can you recommend any movies starring {{Tessa <PERSON>}} that are both spectacular and touching?\"\nText Span: \"Tessa Thompson\"\n\n3. Query: \"Can you tell me a movie where {{Mark Ruffalo}} plays a lead role?\"\nText Span: \"Mark Ruffalo\"", "Here are 2 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Actor.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type actor\n- (B). The span contains a named actor entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not actor\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Trailer, Song, Review, Character, other].\n\nA named actor entity must be the name of an actor or actress. Only first names, last names, and full names of actors and actresses are considered named actor entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'What character did {{<PERSON>}} play in \"Forest Gump\"?'\nText Span: \"Tom Hanks\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: 'What is the name of the {{actor}} who played the main character in \"The Shawshank Redemption\"?'\nText Span: \"actor\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 2 spans of text:\n\n1. Query: \"What are some upcoming releases that feature {{<PERSON> <PERSON>atham}}?\"\nText Span: \"<PERSON> Statham\"\n\n2. Query: \"Can you recommend a movie with {{<PERSON> <PERSON>}} in a leading role?\"\nText Span: \"Kerry Washington\""]}