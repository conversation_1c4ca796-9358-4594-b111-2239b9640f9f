{"sentence": "a four star restaurant with a bar", "tokens": ["a", "four", "star", "restaurant", "with", "a", "bar"], "ner_tags": ["O", "B-Rating", "I-Rating", "O", "B-Location", "I-Location", "B-Amenity"]}
{"sentence": "any asian cuisine around", "tokens": ["any", "asian", "cuisine", "around"], "ner_tags": ["O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "any bbq places open before 5 nearby", "tokens": ["any", "bbq", "places", "open", "before", "5", "nearby"], "ner_tags": ["O", "B-Cuisine", "O", "B-Hours", "I-Hours", "I-Hours", "B-Location"]}
{"sentence": "any dancing establishments with reasonable pricing", "tokens": ["any", "dancing", "establishments", "with", "reasonable", "pricing"], "ner_tags": ["O", "B-Location", "I-Location", "O", "B-Price", "O"]}
{"sentence": "any good cheap german restaurants nearby", "tokens": ["any", "good", "cheap", "german", "restaurants", "nearby"], "ner_tags": ["O", "O", "B-Price", "B-Cuisine", "O", "B-Location"]}
{"sentence": "any good ice cream parlors around", "tokens": ["any", "good", "ice", "cream", "parlors", "around"], "ner_tags": ["O", "B-Rating", "B-Cuisine", "I-Cuisine", "I-Cuisine", "B-Location"]}
{"sentence": "any good place to get a pie at an affordable price", "tokens": ["any", "good", "place", "to", "get", "a", "pie", "at", "an", "affordable", "price"], "ner_tags": ["O", "B-Rating", "O", "O", "O", "O", "B-Dish", "O", "O", "B-Price", "O"]}
{"sentence": "any good vegan spots nearby", "tokens": ["any", "good", "vegan", "spots", "nearby"], "ner_tags": ["O", "O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "any mexican places have a tameles special today", "tokens": ["any", "mexican", "places", "have", "a", "tameles", "special", "today"], "ner_tags": ["O", "B-Cuisine", "O", "O", "O", "B-Dish", "B-Amenity", "I-Amenity"]}
{"sentence": "any place along the road has a good beer selection that also serves ribs", "tokens": ["any", "place", "along", "the", "road", "has", "a", "good", "beer", "selection", "that", "also", "serves", "ribs"], "ner_tags": ["O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Rating", "B-Dish", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "any places around here that has a nice view", "tokens": ["any", "places", "around", "here", "that", "has", "a", "nice", "view"], "ner_tags": ["O", "O", "B-Location", "I-Location", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "any reasonably priced indian restaurants in the theater district", "tokens": ["any", "reasonably", "priced", "indian", "restaurants", "in", "the", "theater", "district"], "ner_tags": ["O", "B-Price", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "any restaurants open right now", "tokens": ["any", "restaurants", "open", "right", "now"], "ner_tags": ["O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "any restaurants that still allow smoking", "tokens": ["any", "restaurants", "that", "still", "allow", "smoking"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity"]}
{"sentence": "any stores around where i could buy a pasta dish where the prices are not too high", "tokens": ["any", "stores", "around", "where", "i", "could", "buy", "a", "pasta", "dish", "where", "the", "prices", "are", "not", "too", "high"], "ner_tags": ["O", "O", "B-Location", "O", "O", "O", "O", "O", "B-Dish", "O", "O", "O", "B-Price", "I-Price", "I-Price", "I-Price", "I-Price"]}
{"sentence": "anything on the avenue", "tokens": ["anything", "on", "the", "avenue"], "ner_tags": ["O", "O", "O", "B-Location"]}
{"sentence": "anything open after midnight with reasonable prices", "tokens": ["anything", "open", "after", "midnight", "with", "reasonable", "prices"], "ner_tags": ["O", "B-Hours", "I-Hours", "I-Hours", "O", "B-Price", "O"]}
{"sentence": "are children allowed in this particular sitting area", "tokens": ["are", "children", "allowed", "in", "this", "particular", "sitting", "area"], "ner_tags": ["O", "B-Amenity", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "are reservations available for four people for 8 pm tonight at 112 eatery", "tokens": ["are", "reservations", "available", "for", "four", "people", "for", "8", "pm", "tonight", "at", "112", "eatery"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "are the portion at le bec fin large or very small", "tokens": ["are", "the", "portion", "at", "le", "bec", "fin", "large", "or", "very", "small"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O", "O"]}
{"sentence": "are there any 24 hour breakfast places nearby", "tokens": ["are", "there", "any", "24", "hour", "breakfast", "places", "nearby"], "ner_tags": ["O", "O", "O", "B-Hours", "I-Hours", "B-Cuisine", "O", "B-Location"]}
{"sentence": "are there any 50s style diners in glendale", "tokens": ["are", "there", "any", "50s", "style", "diners", "in", "glendale"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "B-Cuisine", "O", "B-Location"]}
{"sentence": "are there any authentic mexican restaurants in the area", "tokens": ["are", "there", "any", "authentic", "mexican", "restaurants", "in", "the", "area"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "are there any bars nearby that serve food like italian or french", "tokens": ["are", "there", "any", "bars", "nearby", "that", "serve", "food", "like", "italian", "or", "french"], "ner_tags": ["O", "O", "O", "B-Cuisine", "B-Location", "O", "O", "O", "O", "B-Cuisine", "O", "B-Cuisine"]}
{"sentence": "are there any brewpubs downtown", "tokens": ["are", "there", "any", "brewpubs", "downtown"], "ner_tags": ["O", "O", "O", "B-Cuisine", "B-Location"]}
{"sentence": "are there any cafeterias near", "tokens": ["are", "there", "any", "cafeterias", "near"], "ner_tags": ["O", "O", "O", "B-Cuisine", "B-Location"]}
{"sentence": "are there any charlestown restaurants open very early for lunch", "tokens": ["are", "there", "any", "charlestown", "restaurants", "open", "very", "early", "for", "lunch"], "ner_tags": ["O", "O", "O", "B-Location", "O", "B-Hours", "I-Hours", "I-Hours", "O", "B-Hours"]}
{"sentence": "are there any chick fil as in the city open on sunday", "tokens": ["are", "there", "any", "chick", "fil", "as", "in", "the", "city", "open", "on", "sunday"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location", "O", "O", "B-Hours"]}
{"sentence": "are there any chicken wing places nearby", "tokens": ["are", "there", "any", "chicken", "wing", "places", "nearby"], "ner_tags": ["O", "O", "O", "B-Dish", "I-Dish", "O", "B-Location"]}
{"sentence": "are there any child friendly restaurants within ten miles", "tokens": ["are", "there", "any", "child", "friendly", "restaurants", "within", "ten", "miles"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "are there any chinese restaurants near cheyenne", "tokens": ["are", "there", "any", "chinese", "restaurants", "near", "cheyenne"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "are there any crab restaurants near here that are open late until 2 a m", "tokens": ["are", "there", "any", "crab", "restaurants", "near", "here", "that", "are", "open", "late", "until", "2", "a", "m"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "are there any dining specials at le bec fin", "tokens": ["are", "there", "any", "dining", "specials", "at", "le", "bec", "fin"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "are there any donut and donuts within 5 minutes drive that has an extensive beer menu", "tokens": ["are", "there", "any", "donut", "and", "donuts", "within", "5", "minutes", "drive", "that", "has", "an", "extensive", "beer", "menu"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location", "I-Location", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "are there any eatery at the hotel downtown", "tokens": ["are", "there", "any", "eatery", "at", "the", "hotel", "downtown"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "are there any exciting joints along the way thats reasonably priced", "tokens": ["are", "there", "any", "exciting", "joints", "along", "the", "way", "thats", "reasonably", "priced"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "B-Location", "I-Location", "I-Location", "O", "B-Price", "O"]}
{"sentence": "are there any fancy cambodian places on seaver street", "tokens": ["are", "there", "any", "fancy", "cambodian", "places", "on", "seaver", "street"], "ner_tags": ["O", "O", "O", "B-Amenity", "B-Cuisine", "O", "O", "B-Location", "I-Location"]}
{"sentence": "are there any fast food joints east of here", "tokens": ["are", "there", "any", "fast", "food", "joints", "east", "of", "here"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "are there any fast food restaurants that are kid friendly", "tokens": ["are", "there", "any", "fast", "food", "restaurants", "that", "are", "kid", "friendly"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "are there any fine dining options within 5 miles of my location", "tokens": ["are", "there", "any", "fine", "dining", "options", "within", "5", "miles", "of", "my", "location"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O"]}
{"sentence": "are there any five star restaurants around here", "tokens": ["are", "there", "any", "five", "star", "restaurants", "around", "here"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "O", "B-Location", "I-Location"]}
{"sentence": "are there any four star restaurants in this town", "tokens": ["are", "there", "any", "four", "star", "restaurants", "in", "this", "town"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "O", "O", "O", "B-Location"]}
{"sentence": "are there any fun restaurants serving brisket in town", "tokens": ["are", "there", "any", "fun", "restaurants", "serving", "brisket", "in", "town"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "O", "B-Dish", "B-Location", "I-Location"]}
{"sentence": "are there any good family style restaurants around boston", "tokens": ["are", "there", "any", "good", "family", "style", "restaurants", "around", "boston"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location"]}
{"sentence": "are there any good soul food restaurants near by", "tokens": ["are", "there", "any", "good", "soul", "food", "restaurants", "near", "by"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "O", "O", "B-Location", "I-Location"]}
{"sentence": "are there any greek restaurants in the area", "tokens": ["are", "there", "any", "greek", "restaurants", "in", "the", "area"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "are there any greek restaurants in the theater district of the back bay", "tokens": ["are", "there", "any", "greek", "restaurants", "in", "the", "theater", "district", "of", "the", "back", "bay"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Location", "I-Location"]}
{"sentence": "are there any hamburger restaurants close by", "tokens": ["are", "there", "any", "hamburger", "restaurants", "close", "by"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "are there any hotels nearby that have private rooms available at 1 am", "tokens": ["are", "there", "any", "hotels", "nearby", "that", "have", "private", "rooms", "available", "at", "1", "am"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Hours", "O"]}
{"sentence": "are there any ice cream shops in my neighborhood that are open right now", "tokens": ["are", "there", "any", "ice", "cream", "shops", "in", "my", "neighborhood", "that", "are", "open", "right", "now"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "are there any indian restaurants on long island", "tokens": ["are", "there", "any", "indian", "restaurants", "on", "long", "island"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Location", "I-Location"]}
{"sentence": "are there any italian eateries nearby", "tokens": ["are", "there", "any", "italian", "eateries", "nearby"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "are there any japanese restaurants in town that do discounts for bulk orders of sushi", "tokens": ["are", "there", "any", "japanese", "restaurants", "in", "town", "that", "do", "discounts", "for", "bulk", "orders", "of", "sushi"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "O", "B-Amenity"]}
{"sentence": "are there any jazz clubs that serve food", "tokens": ["are", "there", "any", "jazz", "clubs", "that", "serve", "food"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O"]}
{"sentence": "are there any kid friendly restaurants close by", "tokens": ["are", "there", "any", "kid", "friendly", "restaurants", "close", "by"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location"]}
{"sentence": "are there any kid friendly restaurants with valet parking", "tokens": ["are", "there", "any", "kid", "friendly", "restaurants", "with", "valet", "parking"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "are there any locally owned franchises that give money to charity", "tokens": ["are", "there", "any", "locally", "owned", "franchises", "that", "give", "money", "to", "charity"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "are there any maid cafe in town", "tokens": ["are", "there", "any", "maid", "cafe", "in", "town"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location"]}
{"sentence": "are there any mcdonalds that i can drive too in 3 minutes", "tokens": ["are", "there", "any", "mcdonalds", "that", "i", "can", "drive", "too", "in", "3", "minutes"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "are there any mid priced restaurants within 5 miles that offer curb side pickup", "tokens": ["are", "there", "any", "mid", "priced", "restaurants", "within", "5", "miles", "that", "offer", "curb", "side", "pickup"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "B-Location", "I-Location", "I-Location", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "are there any nationally known chefs with restaurants in this city", "tokens": ["are", "there", "any", "nationally", "known", "chefs", "with", "restaurants", "in", "this", "city"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "are there any new restaurants nearby that i can try", "tokens": ["are", "there", "any", "new", "restaurants", "nearby", "that", "i", "can", "try"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "B-Location", "O", "O", "O", "O"]}
{"sentence": "are there any nice taco places nearby open for breakfast", "tokens": ["are", "there", "any", "nice", "taco", "places", "nearby", "open", "for", "breakfast"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "O", "B-Location", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "are there any place close by that is open 24 hours", "tokens": ["are", "there", "any", "place", "close", "by", "that", "is", "open", "24", "hours"], "ner_tags": ["O", "O", "O", "O", "B-Location", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "are there any places around here that has tomato sauce based dishes", "tokens": ["are", "there", "any", "places", "around", "here", "that", "has", "tomato", "sauce", "based", "dishes"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Dish", "I-Dish", "I-Dish", "I-Dish"]}
{"sentence": "are there any places left that allow smoking in a restaraunt", "tokens": ["are", "there", "any", "places", "left", "that", "allow", "smoking", "in", "a", "restaraunt"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O"]}
{"sentence": "are there any places near by that sell hamburgers and pizza", "tokens": ["are", "there", "any", "places", "near", "by", "that", "sell", "hamburgers", "and", "pizza"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Dish", "O", "B-Dish"]}
{"sentence": "are there any places near by that serve lunch all day", "tokens": ["are", "there", "any", "places", "near", "by", "that", "serve", "lunch", "all", "day"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "are there any places to eat in the area that offer a two for one special", "tokens": ["are", "there", "any", "places", "to", "eat", "in", "the", "area", "that", "offer", "a", "two", "for", "one", "special"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "are there any places with a notable beer list on white st", "tokens": ["are", "there", "any", "places", "with", "a", "notable", "beer", "list", "on", "white", "st"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Rating", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location"]}
{"sentence": "are there any reasonably priced restaurants on east haverhill street that serve cheese", "tokens": ["are", "there", "any", "reasonably", "priced", "restaurants", "on", "east", "haverhill", "street", "that", "serve", "cheese"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Dish"]}
{"sentence": "are there any restaurant nearby that serve thai food", "tokens": ["are", "there", "any", "restaurant", "nearby", "that", "serve", "thai", "food"], "ner_tags": ["O", "O", "O", "O", "B-Location", "O", "O", "B-Cuisine", "O"]}
{"sentence": "are there any restaurants around with a smoking area", "tokens": ["are", "there", "any", "restaurants", "around", "with", "a", "smoking", "area"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "are there any restaurants for diabetics that serve sugar free desserts", "tokens": ["are", "there", "any", "restaurants", "for", "diabetics", "that", "serve", "sugar", "free", "desserts"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Dish", "I-Dish", "I-Dish"]}
{"sentence": "are there any restaurants nearby that have great reviews and plenty of parking", "tokens": ["are", "there", "any", "restaurants", "nearby", "that", "have", "great", "reviews", "and", "plenty", "of", "parking"], "ner_tags": ["O", "O", "O", "O", "B-Location", "O", "O", "B-Rating", "I-Rating", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "are there any restaurants nearby that have outdoor dining", "tokens": ["are", "there", "any", "restaurants", "nearby", "that", "have", "outdoor", "dining"], "ner_tags": ["O", "O", "O", "O", "B-Location", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "are there any restaurants on kilmarnock street that feature large portions and a brewpub", "tokens": ["are", "there", "any", "restaurants", "on", "kilmarnock", "street", "that", "feature", "large", "portions", "and", "a", "brewpub"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Amenity"]}
{"sentence": "are there any restaurants on the way that serve hamburgers and are open after 1 am", "tokens": ["are", "there", "any", "restaurants", "on", "the", "way", "that", "serve", "hamburgers", "and", "are", "open", "after", "1", "am"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "O", "O", "B-Dish", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "are there any restaurants on the way to my destination that have a fireplace inside", "tokens": ["are", "there", "any", "restaurants", "on", "the", "way", "to", "my", "destination", "that", "have", "a", "fireplace", "inside"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "are there any restaurants on this side of the river", "tokens": ["are", "there", "any", "restaurants", "on", "this", "side", "of", "the", "river"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "are there any restaurants open after 2 am", "tokens": ["are", "there", "any", "restaurants", "open", "after", "2", "am"], "ner_tags": ["O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "are there any restaurants that are open 24 hours", "tokens": ["are", "there", "any", "restaurants", "that", "are", "open", "24", "hours"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Hours", "I-Hours"]}
{"sentence": "are there any restaurants that serve raw and organic food nearby", "tokens": ["are", "there", "any", "restaurants", "that", "serve", "raw", "and", "organic", "food", "nearby"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "I-Cuisine", "O", "B-Location"]}
{"sentence": "are there any restaurants that will let me take my dog in with me", "tokens": ["are", "there", "any", "restaurants", "that", "will", "let", "me", "take", "my", "dog", "in", "with", "me"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "are there any restaurants with happy hour in the area", "tokens": ["are", "there", "any", "restaurants", "with", "happy", "hour", "in", "the", "area"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Location", "I-Location", "I-Location"]}
{"sentence": "are there any restaurants with valet parking and a multilingual staff near here", "tokens": ["are", "there", "any", "restaurants", "with", "valet", "parking", "and", "a", "multilingual", "staff", "near", "here"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Amenity", "I-Amenity", "B-Location", "I-Location"]}
{"sentence": "are there any restaurants within 5 miles that accept travelers checks", "tokens": ["are", "there", "any", "restaurants", "within", "5", "miles", "that", "accept", "travelers", "checks"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "are there any rib joints nearby", "tokens": ["are", "there", "any", "rib", "joints", "nearby"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "B-Location"]}
{"sentence": "are there any seafood restaurants near government center where i can make online reservations", "tokens": ["are", "there", "any", "seafood", "restaurants", "near", "government", "center", "where", "i", "can", "make", "online", "reservations"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "are there any spanish restaurants that are cheap", "tokens": ["are", "there", "any", "spanish", "restaurants", "that", "are", "cheap"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Price"]}
{"sentence": "are there any steak houses within 3 miles of me", "tokens": ["are", "there", "any", "steak", "houses", "within", "3", "miles", "of", "me"], "ner_tags": ["O", "O", "O", "B-Dish", "O", "B-Location", "I-Location", "I-Location", "O", "O"]}
{"sentence": "are there any sub sandwich shops that also serve beer", "tokens": ["are", "there", "any", "sub", "sandwich", "shops", "that", "also", "serve", "beer"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "are there any sushi restaurants near colonel bell drive", "tokens": ["are", "there", "any", "sushi", "restaurants", "near", "colonel", "bell", "drive"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "are there any tapas restaurants with good reviews nearby", "tokens": ["are", "there", "any", "tapas", "restaurants", "with", "good", "reviews", "nearby"], "ner_tags": ["O", "O", "O", "B-Dish", "O", "O", "B-Rating", "I-Rating", "B-Location"]}
{"sentence": "are there any turkish restaurants in florida", "tokens": ["are", "there", "any", "turkish", "restaurants", "in", "florida"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Location"]}
{"sentence": "are there any vegan spots that are open after 11 at night", "tokens": ["are", "there", "any", "vegan", "spots", "that", "are", "open", "after", "11", "at", "night"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "are there any vegetarian restaurants in this town", "tokens": ["are", "there", "any", "vegetarian", "restaurants", "in", "this", "town"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "are there any vegetarian restaurants that allow you to order online ahead of time", "tokens": ["are", "there", "any", "vegetarian", "restaurants", "that", "allow", "you", "to", "order", "online", "ahead", "of", "time"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "are there any vietnamese restaurants nearby", "tokens": ["are", "there", "any", "vietnamese", "restaurants", "nearby"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "are there are any cracker barrells on long island", "tokens": ["are", "there", "are", "any", "cracker", "barrells", "on", "long", "island"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "are there reservations still available for bar la grassa for 2 tomorrow at 7 pm", "tokens": ["are", "there", "reservations", "still", "available", "for", "bar", "la", "grassa", "for", "2", "tomorrow", "at", "7", "pm"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "areas that allow smoking", "tokens": ["areas", "that", "allow", "smoking"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "asian cuisine in my zip code", "tokens": ["asian", "cuisine", "in", "my", "zip", "code"], "ner_tags": ["B-Cuisine", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "at which french restaurant can i dine outdoors", "tokens": ["at", "which", "french", "restaurant", "can", "i", "dine", "outdoors"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "beer and hot wings in town", "tokens": ["beer", "and", "hot", "wings", "in", "town"], "ner_tags": ["B-Dish", "O", "B-Dish", "I-Dish", "B-Location", "I-Location"]}
{"sentence": "best chinese food in the area", "tokens": ["best", "chinese", "food", "in", "the", "area"], "ner_tags": ["B-Rating", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "borscht", "tokens": ["borscht"], "ner_tags": ["B-Dish"]}
{"sentence": "bradford lantern cafe directions", "tokens": ["bradford", "lantern", "cafe", "directions"], "ner_tags": ["B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "brasil cuisine near me", "tokens": ["brasil", "cuisine", "near", "me"], "ner_tags": ["B-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "burgers", "tokens": ["burgers"], "ner_tags": ["B-Dish"]}
{"sentence": "cafes on ashlannd street", "tokens": ["cafes", "on", "ashlannd", "street"], "ner_tags": ["B-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "call cheeseboard in berkeley for me", "tokens": ["call", "cheeseboard", "in", "berkeley", "for", "me"], "ner_tags": ["O", "B-Restaurant_Name", "O", "B-Location", "O", "O"]}
{"sentence": "call chinese", "tokens": ["call", "chinese"], "ner_tags": ["O", "B-Cuisine"]}
{"sentence": "call dominos", "tokens": ["call", "dominos"], "ner_tags": ["O", "B-Restaurant_Name"]}
{"sentence": "call the closest korean restaurant", "tokens": ["call", "the", "closest", "korean", "restaurant"], "ner_tags": ["O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "can i bring my kid to any of the restaurants down town that have bars attached", "tokens": ["can", "i", "bring", "my", "kid", "to", "any", "of", "the", "restaurants", "down", "town", "that", "have", "bars", "attached"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "can i bring my pet iguana to the japanese restaurant", "tokens": ["can", "i", "bring", "my", "pet", "iguana", "to", "the", "japanese", "restaurant"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "can i dine at the barat a nossa casa", "tokens": ["can", "i", "dine", "at", "the", "barat", "a", "nossa", "casa"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "can i find a bar and grill within short walking distance of the shopping district", "tokens": ["can", "i", "find", "a", "bar", "and", "grill", "within", "short", "walking", "distance", "of", "the", "shopping", "district"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "can i find a good chinese buffet within 3 miles from me", "tokens": ["can", "i", "find", "a", "good", "chinese", "buffet", "within", "3", "miles", "from", "me"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "B-Cuisine", "B-Amenity", "B-Location", "I-Location", "I-Location", "O", "O"]}
{"sentence": "can i find any restaurants close by with a meal under 8", "tokens": ["can", "i", "find", "any", "restaurants", "close", "by", "with", "a", "meal", "under", "8"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Price", "I-Price", "I-Price"]}
{"sentence": "can i find good portugues", "tokens": ["can", "i", "find", "good", "portugues"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine"]}
{"sentence": "can i get a chefands table on north bedford street very late at night", "tokens": ["can", "i", "get", "a", "chefands", "table", "on", "north", "bedford", "street", "very", "late", "at", "night"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location", "I-Location", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "can i get a list of close fast food places", "tokens": ["can", "i", "get", "a", "list", "of", "close", "fast", "food", "places"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "can i get gluten free pizza within 10 miles of here", "tokens": ["can", "i", "get", "gluten", "free", "pizza", "within", "10", "miles", "of", "here"], "ner_tags": ["O", "O", "O", "B-Dish", "I-Dish", "I-Dish", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "can i get hambers at lone star", "tokens": ["can", "i", "get", "hambers", "at", "lone", "star"], "ner_tags": ["O", "O", "O", "B-Dish", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "can i get raw vegan food in honolulu", "tokens": ["can", "i", "get", "raw", "vegan", "food", "in", "honolulu"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "B-Location"]}
{"sentence": "can i get sushi on a prix fixe menu with reasonable prices", "tokens": ["can", "i", "get", "sushi", "on", "a", "prix", "fixe", "menu", "with", "reasonable", "prices"], "ner_tags": ["O", "O", "O", "B-Dish", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "O", "B-Price", "O"]}
{"sentence": "can i have the phone number for kfc in los angeles ca", "tokens": ["can", "i", "have", "the", "phone", "number", "for", "kfc", "in", "los", "angeles", "ca"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "can i see hamburger restaurants nearby", "tokens": ["can", "i", "see", "hamburger", "restaurants", "nearby"], "ner_tags": ["O", "O", "O", "B-Dish", "O", "B-Location"]}
{"sentence": "can i valet park at the blue coyote grill", "tokens": ["can", "i", "valet", "park", "at", "the", "blue", "coyote", "grill"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "can i wear shorts", "tokens": ["can", "i", "wear", "shorts"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "can you find a bar that serves tapas and takes reservations for happy hour", "tokens": ["can", "you", "find", "a", "bar", "that", "serves", "tapas", "and", "takes", "reservations", "for", "happy", "hour"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "O", "O", "B-Dish", "O", "O", "B-Amenity", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "can you find a burger joint with a smoking section", "tokens": ["can", "you", "find", "a", "burger", "joint", "with", "a", "smoking", "section"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "can you find a cheap vietnamese restaurant", "tokens": ["can", "you", "find", "a", "cheap", "vietnamese", "restaurant"], "ner_tags": ["O", "O", "O", "O", "B-Price", "B-Cuisine", "O"]}
{"sentence": "can you find a fast food restaurant", "tokens": ["can", "you", "find", "a", "fast", "food", "restaurant"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "can you find a highly rated long john silvers open this late", "tokens": ["can", "you", "find", "a", "highly", "rated", "long", "john", "silvers", "open", "this", "late"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "I-Rating", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "can you find a middle eastern place with great prices", "tokens": ["can", "you", "find", "a", "middle", "eastern", "place", "with", "great", "prices"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "B-Price", "O"]}
{"sentence": "can you find a pizza place with a buffet within 15 miles", "tokens": ["can", "you", "find", "a", "pizza", "place", "with", "a", "buffet", "within", "15", "miles"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "B-Location", "I-Location", "I-Location"]}
{"sentence": "can you find a pub downtown", "tokens": ["can", "you", "find", "a", "pub", "downtown"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "B-Location"]}
{"sentence": "can you find a restaurant that serves bean soup in the morning and isnt too expensive", "tokens": ["can", "you", "find", "a", "restaurant", "that", "serves", "bean", "soup", "in", "the", "morning", "and", "isnt", "too", "expensive"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Dish", "I-Dish", "O", "O", "B-Hours", "O", "B-Price", "I-Price", "I-Price"]}
{"sentence": "can you find a restaurant that serves duck that not cheap near here", "tokens": ["can", "you", "find", "a", "restaurant", "that", "serves", "duck", "that", "not", "cheap", "near", "here"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Dish", "O", "B-Price", "I-Price", "B-Location", "O"]}
{"sentence": "can you find a restaurant under 20 per dish", "tokens": ["can", "you", "find", "a", "restaurant", "under", "20", "per", "dish"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "I-Price", "I-Price", "I-Price"]}
{"sentence": "can you find a seafood restaurant", "tokens": ["can", "you", "find", "a", "seafood", "restaurant"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "can you find a site where i can see reviews on restaurant downtown", "tokens": ["can", "you", "find", "a", "site", "where", "i", "can", "see", "reviews", "on", "restaurant", "downtown"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Location"]}
{"sentence": "can you find a steak house that serves wine", "tokens": ["can", "you", "find", "a", "steak", "house", "that", "serves", "wine"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "B-Dish"]}
{"sentence": "can you find a thai japanese fusion restaurant in town", "tokens": ["can", "you", "find", "a", "thai", "japanese", "fusion", "restaurant", "in", "town"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "can you find an italian restaurant nearby", "tokens": ["can", "you", "find", "an", "italian", "restaurant", "nearby"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "can you find an italian restaurant that serves brunch on sunday", "tokens": ["can", "you", "find", "an", "italian", "restaurant", "that", "serves", "brunch", "on", "sunday"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Hours", "O", "B-Hours"]}
{"sentence": "can you find an italian restaurant where i can wear casual atire", "tokens": ["can", "you", "find", "an", "italian", "restaurant", "where", "i", "can", "wear", "casual", "atire"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "can you find east dedham pizzeria that have a dine at bar location", "tokens": ["can", "you", "find", "east", "dedham", "pizzeria", "that", "have", "a", "dine", "at", "bar", "location"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "B-Location", "B-Cuisine", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "can you find latin american cuisine within a hotel along the way", "tokens": ["can", "you", "find", "latin", "american", "cuisine", "within", "a", "hotel", "along", "the", "way"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "can you find me a burmese restaurant with a parking spot", "tokens": ["can", "you", "find", "me", "a", "burmese", "restaurant", "with", "a", "parking", "spot"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "can you find me a fredas thats not too busy", "tokens": ["can", "you", "find", "me", "a", "fredas", "thats", "not", "too", "busy"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "can you find me a good place to eat chowder", "tokens": ["can", "you", "find", "me", "a", "good", "place", "to", "eat", "chowder"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "O", "O", "O", "B-Dish"]}
{"sentence": "can you find me a kid friendly seafood restaurant", "tokens": ["can", "you", "find", "me", "a", "kid", "friendly", "seafood", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Cuisine", "O"]}
{"sentence": "can you find me a kid friendly sushi restaurant", "tokens": ["can", "you", "find", "me", "a", "kid", "friendly", "sushi", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Cuisine", "O"]}
{"sentence": "can you find me a moderately priced italian restaurant", "tokens": ["can", "you", "find", "me", "a", "moderately", "priced", "italian", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "O", "B-Cuisine", "O"]}
{"sentence": "can you find me a nice italian restaurant that takes reservations", "tokens": ["can", "you", "find", "me", "a", "nice", "italian", "restaurant", "that", "takes", "reservations"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Cuisine", "O", "O", "O", "B-Amenity"]}
{"sentence": "can you find me a not cheap lunch spot that serves pasteur", "tokens": ["can", "you", "find", "me", "a", "not", "cheap", "lunch", "spot", "that", "serves", "pasteur"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "I-Price", "B-Hours", "O", "O", "O", "B-Dish"]}
{"sentence": "can you find me a pizza place", "tokens": ["can", "you", "find", "me", "a", "pizza", "place"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "O"]}
{"sentence": "can you find me a pizzeria that delivers after midnight", "tokens": ["can", "you", "find", "me", "a", "pizzeria", "that", "delivers", "after", "midnight"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Amenity", "B-Hours", "I-Hours"]}
{"sentence": "can you find me a place nearby thats open after 12 pm with bean dishes", "tokens": ["can", "you", "find", "me", "a", "place", "nearby", "thats", "open", "after", "12", "pm", "with", "bean", "dishes"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "O", "O", "B-Hours", "I-Hours", "I-Hours", "O", "B-Dish", "I-Dish"]}
{"sentence": "can you find me a place that serves french fries", "tokens": ["can", "you", "find", "me", "a", "place", "that", "serves", "french", "fries"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "can you find me a restaurant that has a bar in it", "tokens": ["can", "you", "find", "me", "a", "restaurant", "that", "has", "a", "bar", "in", "it"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "can you find me a restaurant that has entrees priced between 15 and 20 dollars", "tokens": ["can", "you", "find", "me", "a", "restaurant", "that", "has", "entrees", "priced", "between", "15", "and", "20", "dollars"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Price", "I-Price", "I-Price", "I-Price", "I-Price"]}
{"sentence": "can you find me a thai restaurant that is caual", "tokens": ["can", "you", "find", "me", "a", "thai", "restaurant", "that", "is", "caual"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity"]}
{"sentence": "can you find me chinese restaurant with a smoking section", "tokens": ["can", "you", "find", "me", "chinese", "restaurant", "with", "a", "smoking", "section"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "can you find me hotel dining with comfort food", "tokens": ["can", "you", "find", "me", "hotel", "dining", "with", "comfort", "food"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Cuisine", "I-Cuisine"]}
{"sentence": "can you find me some malaysian food late at night", "tokens": ["can", "you", "find", "me", "some", "malaysian", "food", "late", "at", "night"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "can you find me some take out ribs", "tokens": ["can", "you", "find", "me", "some", "take", "out", "ribs"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Dish"]}
{"sentence": "can you find out if the best little restaurant has dancing and a good looking atmosphere", "tokens": ["can", "you", "find", "out", "if", "the", "best", "little", "restaurant", "has", "dancing", "and", "a", "good", "looking", "atmosphere"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "can you find starting gate restaurant thats hard to find with a huge price", "tokens": ["can", "you", "find", "starting", "gate", "restaurant", "thats", "hard", "to", "find", "with", "a", "huge", "price"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O", "O", "O", "O", "B-Price", "O"]}
{"sentence": "can you find the closest ihop", "tokens": ["can", "you", "find", "the", "closest", "ihop"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Restaurant_Name"]}
{"sentence": "can you find the nearest health food store and bar", "tokens": ["can", "you", "find", "the", "nearest", "health", "food", "store", "and", "bar"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "I-Cuisine", "O", "B-Cuisine"]}
{"sentence": "can you find the waterfront restaurant albertos deli of course thats open until 11 pm", "tokens": ["can", "you", "find", "the", "waterfront", "restaurant", "albertos", "deli", "of", "course", "thats", "open", "until", "11", "pm"], "ner_tags": ["O", "O", "O", "O", "B-Location", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "can you find us an ice cream shop near haight street", "tokens": ["can", "you", "find", "us", "an", "ice", "cream", "shop", "near", "haight", "street"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "can you get me a list of chinese food places in great neck ny", "tokens": ["can", "you", "get", "me", "a", "list", "of", "chinese", "food", "places", "in", "great", "neck", "ny"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "can you get pork in chinatown", "tokens": ["can", "you", "get", "pork", "in", "chinatown"], "ner_tags": ["O", "O", "O", "B-Dish", "O", "B-Location"]}
{"sentence": "can you give me the name of the restaurant on green st", "tokens": ["can", "you", "give", "me", "the", "name", "of", "the", "restaurant", "on", "green", "st"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "O", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "can you give me the phone number for the nearest mexican restaurant that is both affordable and has good quality food", "tokens": ["can", "you", "give", "me", "the", "phone", "number", "for", "the", "nearest", "mexican", "restaurant", "that", "is", "both", "affordable", "and", "has", "good", "quality", "food"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "O", "O", "O", "O", "B-Price", "O", "O", "B-Rating", "I-Rating", "O"]}
{"sentence": "can you help me find a fancy restaurant with 5 star ratings", "tokens": ["can", "you", "help", "me", "find", "a", "fancy", "restaurant", "with", "5", "star", "ratings"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "O", "O", "B-Rating", "I-Rating", "I-Rating"]}
{"sentence": "can you help me find a high end restaurant where i can have lunch", "tokens": ["can", "you", "help", "me", "find", "a", "high", "end", "restaurant", "where", "i", "can", "have", "lunch"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Price", "I-Price", "O", "O", "O", "O", "O", "B-Amenity"]}
{"sentence": "can you help me find a korean restaurant that is close by", "tokens": ["can", "you", "help", "me", "find", "a", "korean", "restaurant", "that", "is", "close", "by"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "can you help me find a reasonably priced harvard chinese restaurant that lets you byob", "tokens": ["can", "you", "help", "me", "find", "a", "reasonably", "priced", "harvard", "chinese", "restaurant", "that", "lets", "you", "byob"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Price", "O", "B-Location", "B-Cuisine", "O", "O", "O", "O", "B-Amenity"]}
{"sentence": "can you help me find a restaurant that has fast service and is open before 11 am", "tokens": ["can", "you", "help", "me", "find", "a", "restaurant", "that", "has", "fast", "service", "and", "is", "open", "before", "11", "am"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "can you help me find a tong villa that serves small portions", "tokens": ["can", "you", "help", "me", "find", "a", "tong", "villa", "that", "serves", "small", "portions"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "can you help me find inexpensive dining within a five mile radius of my current location", "tokens": ["can", "you", "help", "me", "find", "inexpensive", "dining", "within", "a", "five", "mile", "radius", "of", "my", "current", "location"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "O", "O", "O", "O"]}
{"sentence": "can you help me get to a restaurant where i can get lunch for under 10", "tokens": ["can", "you", "help", "me", "get", "to", "a", "restaurant", "where", "i", "can", "get", "lunch", "for", "under", "10"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Hours", "O", "B-Price", "I-Price"]}
{"sentence": "can you locate a 4 star or higher restaurant that serves italian food", "tokens": ["can", "you", "locate", "a", "4", "star", "or", "higher", "restaurant", "that", "serves", "italian", "food"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "I-Rating", "I-Rating", "I-Rating", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "can you locate a chinese buffet for under 12 within a five minute drive", "tokens": ["can", "you", "locate", "a", "chinese", "buffet", "for", "under", "12", "within", "a", "five", "minute", "drive"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "B-Amenity", "O", "B-Price", "I-Price", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "can you locate a diner that has a smoking section in this area", "tokens": ["can", "you", "locate", "a", "diner", "that", "has", "a", "smoking", "section", "in", "this", "area"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location"]}
{"sentence": "can you locate a restaurant that sell burgers after 12 00 am", "tokens": ["can", "you", "locate", "a", "restaurant", "that", "sell", "burgers", "after", "12", "00", "am"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Dish", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "can you locate the business hours for a restaurant that serves brunch", "tokens": ["can", "you", "locate", "the", "business", "hours", "for", "a", "restaurant", "that", "serves", "brunch"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Hours"]}
{"sentence": "can you make a reservation at pf changs for tonight", "tokens": ["can", "you", "make", "a", "reservation", "at", "pf", "changs", "for", "tonight"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Hours"]}
{"sentence": "can you make me a reservation at the cheapest restaurant with valet parking", "tokens": ["can", "you", "make", "me", "a", "reservation", "at", "the", "cheapest", "restaurant", "with", "valet", "parking"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "O", "O", "B-Price", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "can you make me a reservation at the most expensive restaurant within 15 miles", "tokens": ["can", "you", "make", "me", "a", "reservation", "at", "the", "most", "expensive", "restaurant", "within", "15", "miles"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Price", "I-Price", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "can you make me a reservation for 4 at the nearest upscale steakhouse", "tokens": ["can", "you", "make", "me", "a", "reservation", "for", "4", "at", "the", "nearest", "upscale", "steakhouse"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Location", "B-Price", "B-Cuisine"]}
{"sentence": "can you make me a reservation for todai for thursday for two people", "tokens": ["can", "you", "make", "me", "a", "reservation", "for", "todai", "for", "thursday", "for", "two", "people"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Cuisine", "O", "B-Restaurant_Name", "O", "O", "O"]}
{"sentence": "can you make reservations for two at heartland restaurant for tonight at 7 30", "tokens": ["can", "you", "make", "reservations", "for", "two", "at", "heartland", "restaurant", "for", "tonight", "at", "7", "30"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "can you order me a pizza", "tokens": ["can", "you", "order", "me", "a", "pizza"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "can you please direct me to the nearest chinese restaurant", "tokens": ["can", "you", "please", "direct", "me", "to", "the", "nearest", "chinese", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "can you please find the olive garden restaurant in manhattan", "tokens": ["can", "you", "please", "find", "the", "olive", "garden", "restaurant", "in", "manhattan"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "can you search for the most expensive restaurant", "tokens": ["can", "you", "search", "for", "the", "most", "expensive", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "I-Price", "O"]}
{"sentence": "can you take me to a pizza place", "tokens": ["can", "you", "take", "me", "to", "a", "pizza", "place"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "can you take me to roosevelts restaurant and sin in framingham", "tokens": ["can", "you", "take", "me", "to", "roosevelts", "restaurant", "and", "sin", "in", "framingham"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location"]}
{"sentence": "can you tell me what italian restaurants north of heat rd in fairfax", "tokens": ["can", "you", "tell", "me", "what", "italian", "restaurants", "north", "of", "heat", "rd", "in", "fairfax"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "can you tell me where an affordable burmese place is", "tokens": ["can", "you", "tell", "me", "where", "an", "affordable", "burmese", "place", "is"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Price", "B-Cuisine", "O", "O"]}
{"sentence": "can you tell me where the closest taco bell is", "tokens": ["can", "you", "tell", "me", "where", "the", "closest", "taco", "bell", "is"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "can you tell me where the nearest sushi restaurant is", "tokens": ["can", "you", "tell", "me", "where", "the", "nearest", "sushi", "restaurant", "is"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "O", "O"]}
{"sentence": "can you tell me where the nearest wendys is", "tokens": ["can", "you", "tell", "me", "where", "the", "nearest", "wendys", "is"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "B-Restaurant_Name", "O"]}
{"sentence": "can you tell me where to get some cheap vegetarian food", "tokens": ["can", "you", "tell", "me", "where", "to", "get", "some", "cheap", "vegetarian", "food"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Price", "B-Cuisine", "O"]}
{"sentence": "car look for upscale restaurants downtown and make reservations at the best reviewed", "tokens": ["car", "look", "for", "upscale", "restaurants", "downtown", "and", "make", "reservations", "at", "the", "best", "reviewed"], "ner_tags": ["O", "O", "O", "B-Price", "O", "B-Location", "O", "O", "O", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "cheap place close to home", "tokens": ["cheap", "place", "close", "to", "home"], "ner_tags": ["B-Price", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "chinese food sounds good help me out with a good place", "tokens": ["chinese", "food", "sounds", "good", "help", "me", "out", "with", "a", "good", "place"], "ner_tags": ["B-Cuisine", "O", "O", "O", "O", "O", "O", "O", "O", "B-Rating", "O"]}
{"sentence": "closest sushi bar in town", "tokens": ["closest", "sushi", "bar", "in", "town"], "ner_tags": ["B-Location", "B-Cuisine", "B-Amenity", "O", "O"]}
{"sentence": "cocktails and dancing", "tokens": ["cocktails", "and", "dancing"], "ner_tags": ["B-Dish", "O", "B-Amenity"]}
{"sentence": "could you find a restaurant which plays live music", "tokens": ["could", "you", "find", "a", "restaurant", "which", "plays", "live", "music"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "could you find me a place thats open every day", "tokens": ["could", "you", "find", "me", "a", "place", "thats", "open", "every", "day"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "could you find me some restaurants located along the way to the airport that are open late", "tokens": ["could", "you", "find", "me", "some", "restaurants", "located", "along", "the", "way", "to", "the", "airport", "that", "are", "open", "late"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location", "O", "O", "B-Hours", "I-Hours"]}
{"sentence": "could you help me locate a place for lunch", "tokens": ["could", "you", "help", "me", "locate", "a", "place", "for", "lunch"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Hours"]}
{"sentence": "could you locate an italian restaurant around four miles away with pesto pasta", "tokens": ["could", "you", "locate", "an", "italian", "restaurant", "around", "four", "miles", "away", "with", "pesto", "pasta"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Location", "I-Location", "I-Location", "O", "B-Dish", "I-Dish"]}
{"sentence": "could you locate the closest seafood restaurant", "tokens": ["could", "you", "locate", "the", "closest", "seafood", "restaurant"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "could you make a reservation for me at a nice italian restaurant", "tokens": ["could", "you", "make", "a", "reservation", "for", "me", "at", "a", "nice", "italian", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Rating", "B-Cuisine", "O"]}
{"sentence": "create directions to closest chinese restaurtant", "tokens": ["create", "directions", "to", "closest", "chinese", "restaurtant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "danny cooks home made dinners", "tokens": ["danny", "cooks", "home", "made", "dinners"], "ner_tags": ["B-Restaurant_Name", "I-Restaurant_Name", "B-Amenity", "I-Amenity", "B-Hours"]}
{"sentence": "diner", "tokens": ["diner"], "ner_tags": ["B-Cuisine"]}
{"sentence": "diner locations", "tokens": ["diner", "locations"], "ner_tags": ["B-Cuisine", "O"]}
{"sentence": "dining along the waterfront restaurant is located on sutton street", "tokens": ["dining", "along", "the", "waterfront", "restaurant", "is", "located", "on", "sutton", "street"], "ner_tags": ["B-Cuisine", "B-Amenity", "I-Amenity", "B-Restaurant_Name", "O", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "direct me to a donut shop please", "tokens": ["direct", "me", "to", "a", "donut", "shop", "please"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O"]}
{"sentence": "direct me to the nearest indian restaurant", "tokens": ["direct", "me", "to", "the", "nearest", "indian", "restaurant"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "direct me to the nearest mcdonalds", "tokens": ["direct", "me", "to", "the", "nearest", "mcdonalds"], "ner_tags": ["B-Location", "I-Location", "O", "O", "B-Location", "B-Restaurant_Name"]}
{"sentence": "do any famous people frequent the jimmys pizza too that is close by", "tokens": ["do", "any", "famous", "people", "frequent", "the", "jimmys", "pizza", "too", "that", "is", "close", "by"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "do any fast food restaurants accept out of town checks", "tokens": ["do", "any", "fast", "food", "restaurants", "accept", "out", "of", "town", "checks"], "ner_tags": ["O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "do any of the asian food restaurants use only organic meats and vegetables", "tokens": ["do", "any", "of", "the", "asian", "food", "restaurants", "use", "only", "organic", "meats", "and", "vegetables"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "I-Cuisine", "I-Cuisine"]}
{"sentence": "do any of the authentic italian restaurants sell bottles of imported extra virgin olive oil", "tokens": ["do", "any", "of", "the", "authentic", "italian", "restaurants", "sell", "bottles", "of", "imported", "extra", "virgin", "olive", "oil"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "do any of the chinese restaurants in town do take out after midnight", "tokens": ["do", "any", "of", "the", "chinese", "restaurants", "in", "town", "do", "take", "out", "after", "midnight"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "do any of the family restaurants down town serve strawberry milk", "tokens": ["do", "any", "of", "the", "family", "restaurants", "down", "town", "serve", "strawberry", "milk"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "O", "B-Location", "I-Location", "O", "B-Dish", "I-Dish"]}
{"sentence": "do any of the italian restaurants around here offer an all you can eat buffet", "tokens": ["do", "any", "of", "the", "italian", "restaurants", "around", "here", "offer", "an", "all", "you", "can", "eat", "buffet"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "do any of the middle eastern restaurants close by 7 pm", "tokens": ["do", "any", "of", "the", "middle", "eastern", "restaurants", "close", "by", "7", "pm"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "do any of the restaurants in this town also have a dance floor and live music", "tokens": ["do", "any", "of", "the", "restaurants", "in", "this", "town", "also", "have", "a", "dance", "floor", "and", "live", "music"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "do any restaurants nearby offer a few gluten free options", "tokens": ["do", "any", "restaurants", "nearby", "offer", "a", "few", "gluten", "free", "options"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "do any restaurants serve loaves of patitza around christmas time", "tokens": ["do", "any", "restaurants", "serve", "loaves", "of", "patitza", "around", "christmas", "time"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "I-Dish", "I-Dish", "O", "O", "O"]}
{"sentence": "do fast food restaurants accept checks in baltimore", "tokens": ["do", "fast", "food", "restaurants", "accept", "checks", "in", "baltimore"], "ner_tags": ["O", "B-Cuisine", "I-Cuisine", "O", "B-Amenity", "I-Amenity", "O", "B-Location"]}
{"sentence": "do i need a reservation for kings", "tokens": ["do", "i", "need", "a", "reservation", "for", "kings"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "O", "B-Restaurant_Name"]}
{"sentence": "do know any place around here to take clients", "tokens": ["do", "know", "any", "place", "around", "here", "to", "take", "clients"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "do they have a smoking area", "tokens": ["do", "they", "have", "a", "smoking", "area"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "do they have any restaurants in the mall", "tokens": ["do", "they", "have", "any", "restaurants", "in", "the", "mall"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "do you have any good food recommendations", "tokens": ["do", "you", "have", "any", "good", "food", "recommendations"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "O", "O"]}
{"sentence": "do you have any suggestions for some great tacos", "tokens": ["do", "you", "have", "any", "suggestions", "for", "some", "great", "tacos"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Rating", "B-Dish"]}
{"sentence": "do you have information about any deals they may have", "tokens": ["do", "you", "have", "information", "about", "any", "deals", "they", "may", "have"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "do you have to make a reservation to go to peachtree cafe", "tokens": ["do", "you", "have", "to", "make", "a", "reservation", "to", "go", "to", "peachtree", "cafe"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "do you know any good pizza places open until midnight", "tokens": ["do", "you", "know", "any", "good", "pizza", "places", "open", "until", "midnight"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "B-Dish", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "do you know if elmos have a dress code", "tokens": ["do", "you", "know", "if", "elmos", "have", "a", "dress", "code"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "do you know if i can find fine dining in the theater district", "tokens": ["do", "you", "know", "if", "i", "can", "find", "fine", "dining", "in", "the", "theater", "district"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "do you know if reggianos serve breakfast", "tokens": ["do", "you", "know", "if", "reggianos", "serve", "breakfast"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "O", "B-Hours"]}
{"sentence": "do you know if the burrito place is open this late", "tokens": ["do", "you", "know", "if", "the", "burrito", "place", "is", "open", "this", "late"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "do you know if the purple cactus burrito and wrap bar ongreentree lane have byob", "tokens": ["do", "you", "know", "if", "the", "purple", "cactus", "burrito", "and", "wrap", "bar", "ongreentree", "lane", "have", "byob"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "O", "B-Amenity"]}
{"sentence": "do you know if there are any excellent wine bar close by", "tokens": ["do", "you", "know", "if", "there", "are", "any", "excellent", "wine", "bar", "close", "by"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Rating", "B-Cuisine", "I-Cuisine", "B-Location", "I-Location"]}
{"sentence": "do you know if there are any fine dining cajun restaurant with a fireplace that is within one mile", "tokens": ["do", "you", "know", "if", "there", "are", "any", "fine", "dining", "cajun", "restaurant", "with", "a", "fireplace", "that", "is", "within", "one", "mile"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Cuisine", "O", "O", "O", "B-Amenity", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "do you know if there are any irish restaurants that is not too far away", "tokens": ["do", "you", "know", "if", "there", "are", "any", "irish", "restaurants", "that", "is", "not", "too", "far", "away"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "do you know if there are any reasonably priced cajun restaurants that open at 11 am", "tokens": ["do", "you", "know", "if", "there", "are", "any", "reasonably", "priced", "cajun", "restaurants", "that", "open", "at", "11", "am"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Price", "O", "B-Cuisine", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "do you know if there are any resataurants in the mall", "tokens": ["do", "you", "know", "if", "there", "are", "any", "resataurants", "in", "the", "mall"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "do you know if there are any reviews on monacos", "tokens": ["do", "you", "know", "if", "there", "are", "any", "reviews", "on", "monacos"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "do you know of a bakery that is still open", "tokens": ["do", "you", "know", "of", "a", "bakery", "that", "is", "still", "open"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Hours", "I-Hours"]}
{"sentence": "do you know the name of that restaurant on morgan street that is getting all the raves", "tokens": ["do", "you", "know", "the", "name", "of", "that", "restaurant", "on", "morgan", "street", "that", "is", "getting", "all", "the", "raves"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Rating", "I-Rating", "I-Rating", "I-Rating"]}
{"sentence": "do you know what restaurants have catering", "tokens": ["do", "you", "know", "what", "restaurants", "have", "catering"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity"]}
{"sentence": "do you know where i can find a place to eat that has gotten good reviews", "tokens": ["do", "you", "know", "where", "i", "can", "find", "a", "place", "to", "eat", "that", "has", "gotten", "good", "reviews"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "do you know where i can get some fresh sushi", "tokens": ["do", "you", "know", "where", "i", "can", "get", "some", "fresh", "sushi"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "do you know where there is a wine bar", "tokens": ["do", "you", "know", "where", "there", "is", "a", "wine", "bar"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "do you know where they sell hummus", "tokens": ["do", "you", "know", "where", "they", "sell", "hummus"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "do you think the noodle bar is open", "tokens": ["do", "you", "think", "the", "noodle", "bar", "is", "open"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Hours"]}
{"sentence": "do you think tin whistle has fabulous service", "tokens": ["do", "you", "think", "tin", "whistle", "has", "fabulous", "service"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does angels have a dress code", "tokens": ["does", "angels", "have", "a", "dress", "code"], "ner_tags": ["O", "B-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does any place sells corned beef and cabbage for st patricks day", "tokens": ["does", "any", "place", "sells", "corned", "beef", "and", "cabbage", "for", "st", "patricks", "day"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "I-Dish", "I-Dish", "I-Dish", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "does anyone in town deliver tasty vegan pizza", "tokens": ["does", "anyone", "in", "town", "deliver", "tasty", "vegan", "pizza"], "ner_tags": ["O", "O", "B-Location", "I-Location", "B-Amenity", "B-Rating", "B-Dish", "I-Dish"]}
{"sentence": "does bellinis have any outdoor parking", "tokens": ["does", "bellinis", "have", "any", "outdoor", "parking"], "ner_tags": ["O", "B-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does buffalo wild wings do takeout orders", "tokens": ["does", "buffalo", "wild", "wings", "do", "takeout", "orders"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "O"]}
{"sentence": "does burger king accept credit cards", "tokens": ["does", "burger", "king", "accept", "credit", "cards"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "does caribe have a smoking area", "tokens": ["does", "caribe", "have", "a", "smoking", "area"], "ner_tags": ["O", "B-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does chuck e cheeses have drive thru", "tokens": ["does", "chuck", "e", "cheeses", "have", "drive", "thru"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does dennys have a kids menu", "tokens": ["does", "dennys", "have", "a", "kids", "menu"], "ner_tags": ["O", "B-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does firstwatch breakfast restuarant have outdoor seating", "tokens": ["does", "firstwatch", "breakfast", "restuarant", "have", "outdoor", "seating"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does freds have take out", "tokens": ["does", "freds", "have", "take", "out"], "ner_tags": ["O", "B-Restaurant_Name", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does granit grill at 703 fx have cheap breakfast", "tokens": ["does", "granit", "grill", "at", "703", "fx", "have", "cheap", "breakfast"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "O", "B-Price", "B-Hours"]}
{"sentence": "does gustovs have a bar", "tokens": ["does", "gustovs", "have", "a", "bar"], "ner_tags": ["O", "B-Restaurant_Name", "O", "O", "B-Amenity"]}
{"sentence": "does jaimes bakery have a great decor", "tokens": ["does", "jaimes", "bakery", "have", "a", "great", "decor"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does johnsons steakhouse require formal attire", "tokens": ["does", "johnsons", "steakhouse", "require", "formal", "attire"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does logans serve hamburgers", "tokens": ["does", "logans", "serve", "hamburgers"], "ner_tags": ["O", "B-Restaurant_Name", "O", "B-Dish"]}
{"sentence": "does mcdonalds serve ice cream during breakfast hours", "tokens": ["does", "mcdonalds", "serve", "ice", "cream", "during", "breakfast", "hours"], "ner_tags": ["O", "B-Restaurant_Name", "O", "B-Dish", "I-Dish", "I-Dish", "B-Hours", "I-Hours"]}
{"sentence": "does midys have takeout", "tokens": ["does", "midys", "have", "takeout"], "ner_tags": ["O", "B-Restaurant_Name", "O", "B-Amenity"]}
{"sentence": "does mikes cafe have a smoking section", "tokens": ["does", "mikes", "cafe", "have", "a", "smoking", "section"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does mikes country kitchen have a senior special", "tokens": ["does", "mikes", "country", "kitchen", "have", "a", "senior", "special"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O", "O"]}
{"sentence": "does mister foos take reservations", "tokens": ["does", "mister", "foos", "take", "reservations"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity"]}
{"sentence": "does mortons have a dress code", "tokens": ["does", "mortons", "have", "a", "dress", "code"], "ner_tags": ["O", "B-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does olive garden serve wine", "tokens": ["does", "olive", "garden", "serve", "wine"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Dish"]}
{"sentence": "does osaka sushi express have great portions", "tokens": ["does", "osaka", "sushi", "express", "have", "great", "portions"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does paymon serves white wine", "tokens": ["does", "paymon", "serves", "white", "wine"], "ner_tags": ["O", "B-Restaurant_Name", "O", "B-Dish", "I-Dish"]}
{"sentence": "does pedros mexican restaurant accept credit cards", "tokens": ["does", "pedros", "mexican", "restaurant", "accept", "credit", "cards"], "ner_tags": ["O", "B-Restaurant_Name", "B-Cuisine", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "does pizza hut accept credit", "tokens": ["does", "pizza", "hut", "accept", "credit"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O"]}
{"sentence": "does quang take credit cards", "tokens": ["does", "quang", "take", "credit", "cards"], "ner_tags": ["O", "B-Restaurant_Name", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does ricatonis offer a lunch portion option", "tokens": ["does", "ricatonis", "offer", "a", "lunch", "portion", "option"], "ner_tags": ["O", "B-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "does ruby tuesday on drake avenue have a salad bar", "tokens": ["does", "ruby", "tuesday", "on", "drake", "avenue", "have", "a", "salad", "bar"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does sherrys have an all you can eat buffet", "tokens": ["does", "sherrys", "have", "an", "all", "you", "can", "eat", "buffet"], "ner_tags": ["O", "B-Restaurant_Name", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "does stephanies on newbury have a brunch menu", "tokens": ["does", "stephanies", "on", "newbury", "have", "a", "brunch", "menu"], "ner_tags": ["O", "B-Restaurant_Name", "O", "B-Location", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does target have their own parking spot is it good for bringing my date there", "tokens": ["does", "target", "have", "their", "own", "parking", "spot", "is", "it", "good", "for", "bringing", "my", "date", "there"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Rating", "O", "B-Location", "O", "B-Amenity", "O"]}
{"sentence": "does texas roadhouse open for lunch every day", "tokens": ["does", "texas", "roadhouse", "open", "for", "lunch", "every", "day"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "does tgi fridays have senior discounts", "tokens": ["does", "tgi", "fridays", "have", "senior", "discounts"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does the alicias diner on danton drive offer fixed price menus", "tokens": ["does", "the", "alicias", "diner", "on", "danton", "drive", "offer", "fixed", "price", "menus"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "does the asian restaurant downtown has parking", "tokens": ["does", "the", "asian", "restaurant", "downtown", "has", "parking"], "ner_tags": ["O", "O", "B-Cuisine", "O", "B-Location", "O", "B-Amenity"]}
{"sentence": "does the caranova restaurant at kendall square offer a fixed price menu", "tokens": ["does", "the", "caranova", "restaurant", "at", "kendall", "square", "offer", "a", "fixed", "price", "menu"], "ner_tags": ["O", "O", "B-Restaurant_Name", "O", "O", "B-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "does the chinese buffet on 6 th avenue have a smoking section", "tokens": ["does", "the", "chinese", "buffet", "on", "6", "th", "avenue", "have", "a", "smoking", "section"], "ner_tags": ["O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does the eastside grill have a dress code", "tokens": ["does", "the", "eastside", "grill", "have", "a", "dress", "code"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does the einstein bros bagels offer fine dining and a close location", "tokens": ["does", "the", "einstein", "bros", "bagels", "offer", "fine", "dining", "and", "a", "close", "location"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Location", "O"]}
{"sentence": "does the italian restaurant in the town center offer carry out", "tokens": ["does", "the", "italian", "restaurant", "in", "the", "town", "center", "offer", "carry", "out"], "ner_tags": ["O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location", "I-Location", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does the italian restaurant on 5 th street require a dress code", "tokens": ["does", "the", "italian", "restaurant", "on", "5", "th", "street", "require", "a", "dress", "code"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does the italos bakery along the road have waterfront dining", "tokens": ["does", "the", "italos", "bakery", "along", "the", "road", "have", "waterfront", "dining"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does the kostas pizza and seafood restaurant have grat prices", "tokens": ["does", "the", "kostas", "pizza", "and", "seafood", "restaurant", "have", "grat", "prices"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Price", "O"]}
{"sentence": "does the kyotoyo japanese restaurant in the theater district deliver", "tokens": ["does", "the", "kyotoyo", "japanese", "restaurant", "in", "the", "theater", "district", "deliver"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Location", "I-Location", "B-Amenity"]}
{"sentence": "does the mcdonalds on 7 th avenue offer free parking", "tokens": ["does", "the", "mcdonalds", "on", "7", "th", "avenue", "offer", "free", "parking"], "ner_tags": ["O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does the nearest asian restaurant have a kids menu", "tokens": ["does", "the", "nearest", "asian", "restaurant", "have", "a", "kids", "menu"], "ner_tags": ["O", "O", "B-Location", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does the nearest howard johnsons have room service", "tokens": ["does", "the", "nearest", "howard", "johnsons", "have", "room", "service"], "ner_tags": ["O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does the pho 2000 on state park rd have online reservation options", "tokens": ["does", "the", "pho", "2000", "on", "state", "park", "rd", "have", "online", "reservation", "options"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location", "O", "B-Amenity", "I-Amenity", "O"]}
{"sentence": "does the pizza shop on florida have outdoor parking", "tokens": ["does", "the", "pizza", "shop", "on", "florida", "have", "outdoor", "parking"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does the trendy new place in holbrook have egg rolls", "tokens": ["does", "the", "trendy", "new", "place", "in", "holbrook", "have", "egg", "rolls"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "O", "B-Dish", "I-Dish"]}
{"sentence": "does the wildhorse saloon restaurant have a museum or gift shop", "tokens": ["does", "the", "wildhorse", "saloon", "restaurant", "have", "a", "museum", "or", "gift", "shop"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "does this chinese restaurant have private rooms", "tokens": ["does", "this", "chinese", "restaurant", "have", "private", "rooms"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "does this restaurant have a good family friendly atmosphere", "tokens": ["does", "this", "restaurant", "have", "a", "good", "family", "friendly", "atmosphere"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "dominos pizza joint near my location", "tokens": ["dominos", "pizza", "joint", "near", "my", "location"], "ner_tags": ["B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "farmer boys burgers in my town", "tokens": ["farmer", "boys", "burgers", "in", "my", "town"], "ner_tags": ["B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Location"]}
{"sentence": "fast food restaurant in the area", "tokens": ["fast", "food", "restaurant", "in", "the", "area"], "ner_tags": ["B-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "feel like having a heart attack heart attack grill", "tokens": ["feel", "like", "having", "a", "heart", "attack", "heart", "attack", "grill"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "find a brewpub with entrees under fifteen dollars", "tokens": ["find", "a", "brewpub", "with", "entrees", "under", "fifteen", "dollars"], "ner_tags": ["O", "O", "B-Cuisine", "O", "B-Price", "I-Price", "I-Price", "I-Price"]}
{"sentence": "find a carry out chinese restaurant", "tokens": ["find", "a", "carry", "out", "chinese", "restaurant"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity", "B-Cuisine", "O"]}
{"sentence": "find a cheap brewpub that serves beef", "tokens": ["find", "a", "cheap", "brewpub", "that", "serves", "beef"], "ner_tags": ["O", "O", "B-Price", "B-Cuisine", "O", "O", "B-Dish"]}
{"sentence": "find a chinese place that delivers", "tokens": ["find", "a", "chinese", "place", "that", "delivers"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "B-Amenity"]}
{"sentence": "find a chinese restaurant that will take american express", "tokens": ["find", "a", "chinese", "restaurant", "that", "will", "take", "american", "express"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "find a chinese restaurant with a large buffet", "tokens": ["find", "a", "chinese", "restaurant", "with", "a", "large", "buffet"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "find a classy expensive restaurant with excellent reviews", "tokens": ["find", "a", "classy", "expensive", "restaurant", "with", "excellent", "reviews"], "ner_tags": ["O", "O", "B-Amenity", "B-Price", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "find a clean place to eat that has reasonable prices", "tokens": ["find", "a", "clean", "place", "to", "eat", "that", "has", "reasonable", "prices"], "ner_tags": ["O", "O", "B-Amenity", "O", "O", "O", "O", "O", "B-Price", "O"]}
{"sentence": "find a comella brothersand italian market for our anniversary that is within 10 miles from here", "tokens": ["find", "a", "comella", "brothersand", "italian", "market", "for", "our", "anniversary", "that", "is", "within", "10", "miles", "from", "here"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "B-Location"]}
{"sentence": "find a fine dining restaurant within 6 miles", "tokens": ["find", "a", "fine", "dining", "restaurant", "within", "6", "miles"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "find a high end place that serves pancakes", "tokens": ["find", "a", "high", "end", "place", "that", "serves", "pancakes"], "ner_tags": ["O", "O", "B-Rating", "I-Rating", "O", "O", "O", "B-Dish"]}
{"sentence": "find a local fruit stand", "tokens": ["find", "a", "local", "fruit", "stand"], "ner_tags": ["O", "O", "B-Location", "B-Cuisine", "I-Cuisine"]}
{"sentence": "find a mexican food restaurant that has a very good rating", "tokens": ["find", "a", "mexican", "food", "restaurant", "that", "has", "a", "very", "good", "rating"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "O", "O", "O", "B-Rating", "I-Rating", "I-Rating"]}
{"sentence": "find a mike donuts for non smokers", "tokens": ["find", "a", "mike", "donuts", "for", "non", "smokers"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "find a place to eat that has a drive thru", "tokens": ["find", "a", "place", "to", "eat", "that", "has", "a", "drive", "thru"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "find a place with live music and happy hour specials", "tokens": ["find", "a", "place", "with", "live", "music", "and", "happy", "hour", "specials"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "find a reasonably priced restaurant on highwood drive called oriental kitchen", "tokens": ["find", "a", "reasonably", "priced", "restaurant", "on", "highwood", "drive", "called", "oriental", "kitchen"], "ner_tags": ["O", "O", "B-Price", "O", "O", "O", "B-Location", "I-Location", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "find a thai cuisine within 2 miles", "tokens": ["find", "a", "thai", "cuisine", "within", "2", "miles"], "ner_tags": ["O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "find a wright wright take out for a special occasion", "tokens": ["find", "a", "wright", "wright", "take", "out", "for", "a", "special", "occasion"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Amenity", "I-Amenity", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "find an in and out burger place", "tokens": ["find", "an", "in", "and", "out", "burger", "place"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "find an inexpensive mexican restaurant in the area", "tokens": ["find", "an", "inexpensive", "mexican", "restaurant", "in", "the", "area"], "ner_tags": ["O", "O", "B-Price", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "find an italian resturant that serves family style", "tokens": ["find", "an", "italian", "resturant", "that", "serves", "family", "style"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "find italian restaurants in atlanta ga", "tokens": ["find", "italian", "restaurants", "in", "atlanta", "ga"], "ner_tags": ["O", "B-Cuisine", "O", "O", "B-Location", "I-Location"]}
{"sentence": "find italian restaurants within 10 miles", "tokens": ["find", "italian", "restaurants", "within", "10", "miles"], "ner_tags": ["O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "find me a 3 star rated burger restaurant within 10 miles", "tokens": ["find", "me", "a", "3", "star", "rated", "burger", "restaurant", "within", "10", "miles"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "I-Rating", "B-Dish", "I-Dish", "B-Location", "I-Location", "I-Location"]}
{"sentence": "find me a causal bar and grill", "tokens": ["find", "me", "a", "causal", "bar", "and", "grill"], "ner_tags": ["O", "O", "O", "B-Amenity", "B-Cuisine", "I-Cuisine", "I-Cuisine"]}
{"sentence": "find me a cheap brazilian restaurant", "tokens": ["find", "me", "a", "cheap", "brazilian", "restaurant"], "ner_tags": ["O", "O", "O", "B-Price", "B-Cuisine", "O"]}
{"sentence": "find me a cheap restaurant with a no smoking area", "tokens": ["find", "me", "a", "cheap", "restaurant", "with", "a", "no", "smoking", "area"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "find me a cheaply priced thai restaurant", "tokens": ["find", "me", "a", "cheaply", "priced", "thai", "restaurant"], "ner_tags": ["O", "O", "O", "B-Price", "O", "B-Cuisine", "O"]}
{"sentence": "find me a chinese restaurant nearby", "tokens": ["find", "me", "a", "chinese", "restaurant", "nearby"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "find me a chinese take out restaurant", "tokens": ["find", "me", "a", "chinese", "take", "out", "restaurant"], "ner_tags": ["O", "O", "O", "B-Cuisine", "B-Amenity", "I-Amenity", "O"]}
{"sentence": "find me a close by meat market which sells produce", "tokens": ["find", "me", "a", "close", "by", "meat", "market", "which", "sells", "produce"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "B-Cuisine", "O", "O", "O", "B-Dish"]}
{"sentence": "find me a close by sub shop", "tokens": ["find", "me", "a", "close", "by", "sub", "shop"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "B-Cuisine", "O"]}
{"sentence": "find me a close travel center", "tokens": ["find", "me", "a", "close", "travel", "center"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O"]}
{"sentence": "find me a deli which has an eat in area", "tokens": ["find", "me", "a", "deli", "which", "has", "an", "eat", "in", "area"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "find me a dim sum restaurant open at 8 am", "tokens": ["find", "me", "a", "dim", "sum", "restaurant", "open", "at", "8", "am"], "ner_tags": ["O", "O", "O", "B-Dish", "I-Dish", "I-Dish", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "find me a fancy place to eat", "tokens": ["find", "me", "a", "fancy", "place", "to", "eat"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "O", "O"]}
{"sentence": "find me a fast food restaurant", "tokens": ["find", "me", "a", "fast", "food", "restaurant"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "find me a german restaurant within 10 miles", "tokens": ["find", "me", "a", "german", "restaurant", "within", "10", "miles"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "find me a good buffet near stockton ca", "tokens": ["find", "me", "a", "good", "buffet", "near", "stockton", "ca"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "B-Location", "I-Location", "I-Location"]}
{"sentence": "find me a good burrito truck in the mission", "tokens": ["find", "me", "a", "good", "burrito", "truck", "in", "the", "mission"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Dish", "I-Dish", "B-Location", "I-Location", "I-Location"]}
{"sentence": "find me a good deli in manhattan", "tokens": ["find", "me", "a", "good", "deli", "in", "manhattan"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "O", "B-Location"]}
{"sentence": "find me a good pho restaurant in portland or", "tokens": ["find", "me", "a", "good", "pho", "restaurant", "in", "portland", "or"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "O", "O", "B-Location", "I-Location"]}
{"sentence": "find me a good pub that has a dance floor", "tokens": ["find", "me", "a", "good", "pub", "that", "has", "a", "dance", "floor"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "find me a good vegetarian restaurant", "tokens": ["find", "me", "a", "good", "vegetarian", "restaurant"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "O"]}
{"sentence": "find me a jack in the box thats open", "tokens": ["find", "me", "a", "jack", "in", "the", "box", "thats", "open"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Hours"]}
{"sentence": "find me a kid friendly restaurant within three miles of here", "tokens": ["find", "me", "a", "kid", "friendly", "restaurant", "within", "three", "miles", "of", "here"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location", "I-Location", "O", "O"]}
{"sentence": "find me a local outdoors shop that sells fire wood", "tokens": ["find", "me", "a", "local", "outdoors", "shop", "that", "sells", "fire", "wood"], "ner_tags": ["O", "O", "O", "B-Location", "B-Amenity", "I-Amenity", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "find me a local restaurant serving turkish food", "tokens": ["find", "me", "a", "local", "restaurant", "serving", "turkish", "food"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "B-Cuisine", "O"]}
{"sentence": "find me a nail salon that does pedicures", "tokens": ["find", "me", "a", "nail", "salon", "that", "does", "pedicures"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Amenity"]}
{"sentence": "find me a pizza parlour please", "tokens": ["find", "me", "a", "pizza", "parlour", "please"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "find me a pizza place that takes credit cards", "tokens": ["find", "me", "a", "pizza", "place", "that", "takes", "credit", "cards"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "find me a place that sells burgers closest to me", "tokens": ["find", "me", "a", "place", "that", "sells", "burgers", "closest", "to", "me"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "B-Location", "I-Location", "I-Location"]}
{"sentence": "find me a place that serves chinese takeout", "tokens": ["find", "me", "a", "place", "that", "serves", "chinese", "takeout"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "B-Amenity"]}
{"sentence": "find me a place to eat near by", "tokens": ["find", "me", "a", "place", "to", "eat", "near", "by"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "find me a place to eat that has excellent sushi", "tokens": ["find", "me", "a", "place", "to", "eat", "that", "has", "excellent", "sushi"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Rating", "B-Dish"]}
{"sentence": "find me a place to get pizza nearby", "tokens": ["find", "me", "a", "place", "to", "get", "pizza", "nearby"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "B-Location"]}
{"sentence": "find me a popular local coffee shop", "tokens": ["find", "me", "a", "popular", "local", "coffee", "shop"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Location", "B-Cuisine", "O"]}
{"sentence": "find me a ranch style barbecue that serves lunch at 3 pm about one mile from my current position", "tokens": ["find", "me", "a", "ranch", "style", "barbecue", "that", "serves", "lunch", "at", "3", "pm", "about", "one", "mile", "from", "my", "current", "position"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "I-Cuisine", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours", "B-Location", "I-Location", "I-Location", "O", "O", "O", "O"]}
{"sentence": "find me a restaurant good for a date", "tokens": ["find", "me", "a", "restaurant", "good", "for", "a", "date"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "find me a restaurant near a movie theater thats open at 6 pm on saturday", "tokens": ["find", "me", "a", "restaurant", "near", "a", "movie", "theater", "thats", "open", "at", "6", "pm", "on", "saturday"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "find me a restaurant near the stadium", "tokens": ["find", "me", "a", "restaurant", "near", "the", "stadium"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "find me a restaurant that has a smoking area and serves alcohol and is withing 20 miles of my current location", "tokens": ["find", "me", "a", "restaurant", "that", "has", "a", "smoking", "area", "and", "serves", "alcohol", "and", "is", "withing", "20", "miles", "of", "my", "current", "location"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Dish", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "O"]}
{"sentence": "find me a restaurant that is quick and cheap", "tokens": ["find", "me", "a", "restaurant", "that", "is", "quick", "and", "cheap"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "O", "B-Price"]}
{"sentence": "find me a restaurant that serves hawaiian food", "tokens": ["find", "me", "a", "restaurant", "that", "serves", "hawaiian", "food"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "find me a restaurant with burgers on the menu and large portions that offers business dining", "tokens": ["find", "me", "a", "restaurant", "with", "burgers", "on", "the", "menu", "and", "large", "portions", "that", "offers", "business", "dining"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "find me a restaurant with truck parking and good ratings", "tokens": ["find", "me", "a", "restaurant", "with", "truck", "parking", "and", "good", "ratings"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Rating", "I-Rating"]}
{"sentence": "find me a restaurant with valet access", "tokens": ["find", "me", "a", "restaurant", "with", "valet", "access"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "find me a restaurant within 5 miles of here that is open at 1 am", "tokens": ["find", "me", "a", "restaurant", "within", "5", "miles", "of", "here", "that", "is", "open", "at", "1", "am"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "find me a review of grasons barbeque", "tokens": ["find", "me", "a", "review", "of", "grasons", "barbeque"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "find me a romantic restaurant in 7 hills", "tokens": ["find", "me", "a", "romantic", "restaurant", "in", "7", "hills"], "ner_tags": ["O", "O", "O", "B-Amenity", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "find me a romantic restaurant that has an open table", "tokens": ["find", "me", "a", "romantic", "restaurant", "that", "has", "an", "open", "table"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "find me a sitdown place with a buy one get one free offer", "tokens": ["find", "me", "a", "sitdown", "place", "with", "a", "buy", "one", "get", "one", "free", "offer"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "find me a southwestern restaurant that serves breakfast and is located nearby", "tokens": ["find", "me", "a", "southwestern", "restaurant", "that", "serves", "breakfast", "and", "is", "located", "nearby"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Hours", "O", "O", "B-Location", "I-Location"]}
{"sentence": "find me a spanish restaurant where i can smoke", "tokens": ["find", "me", "a", "spanish", "restaurant", "where", "i", "can", "smoke"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "find me a sports bar that serves food", "tokens": ["find", "me", "a", "sports", "bar", "that", "serves", "food"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "find me a store that sells apple products within fifty miles", "tokens": ["find", "me", "a", "store", "that", "sells", "apple", "products", "within", "fifty", "miles"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "find me a sudbury pizza place thats no more than 10 minutes from here", "tokens": ["find", "me", "a", "sudbury", "pizza", "place", "thats", "no", "more", "than", "10", "minutes", "from", "here"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "find me a take out chinese restaurant", "tokens": ["find", "me", "a", "take", "out", "chinese", "restaurant"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "B-Cuisine", "O"]}
{"sentence": "find me a take out restaurant with fried chicken less than 2 miles from here", "tokens": ["find", "me", "a", "take", "out", "restaurant", "with", "fried", "chicken", "less", "than", "2", "miles", "from", "here"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Dish", "I-Dish", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "find me a tgi fridays near me", "tokens": ["find", "me", "a", "tgi", "fridays", "near", "me"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location"]}
{"sentence": "find me a thai restaurant with a great rating", "tokens": ["find", "me", "a", "thai", "restaurant", "with", "a", "great", "rating"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "find me a vegan restaurant not more than 5 miles away", "tokens": ["find", "me", "a", "vegan", "restaurant", "not", "more", "than", "5", "miles", "away"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "find me a very well priced cuban restaurant", "tokens": ["find", "me", "a", "very", "well", "priced", "cuban", "restaurant"], "ner_tags": ["O", "O", "O", "O", "B-Price", "O", "B-Cuisine", "O"]}
{"sentence": "find me all the local italian joints", "tokens": ["find", "me", "all", "the", "local", "italian", "joints"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine"]}
{"sentence": "find me an ethiopian restaurant with good service", "tokens": ["find", "me", "an", "ethiopian", "restaurant", "with", "good", "service"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "find me an ethiopian restaurant within 5 miles of here", "tokens": ["find", "me", "an", "ethiopian", "restaurant", "within", "5", "miles", "of", "here"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location", "O", "O"]}
{"sentence": "find me an expensive american restaurant", "tokens": ["find", "me", "an", "expensive", "american", "restaurant"], "ner_tags": ["O", "O", "O", "B-Price", "B-Cuisine", "O"]}
{"sentence": "find me an indian restaurant that serves lamb", "tokens": ["find", "me", "an", "indian", "restaurant", "that", "serves", "lamb"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Dish"]}
{"sentence": "find me an italian restaurant which has received high ratings for its service", "tokens": ["find", "me", "an", "italian", "restaurant", "which", "has", "received", "high", "ratings", "for", "its", "service"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "B-Rating", "I-Rating", "I-Rating", "I-Rating", "I-Rating"]}
{"sentence": "find me brazilian food with on location parking", "tokens": ["find", "me", "brazilian", "food", "with", "on", "location", "parking"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "find me chicken places that accept discover card", "tokens": ["find", "me", "chicken", "places", "that", "accept", "discover", "card"], "ner_tags": ["O", "O", "B-Dish", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "find me chinese food", "tokens": ["find", "me", "chinese", "food"], "ner_tags": ["O", "O", "B-Cuisine", "O"]}
{"sentence": "find me fast food that serves salad", "tokens": ["find", "me", "fast", "food", "that", "serves", "salad"], "ner_tags": ["O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "B-Dish"]}
{"sentence": "find me italian restaurants with cheesecake", "tokens": ["find", "me", "italian", "restaurants", "with", "cheesecake"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "B-Dish"]}
{"sentence": "find me restaurant that isnt cheap with chocolate cake on the dessert menu and byob", "tokens": ["find", "me", "restaurant", "that", "isnt", "cheap", "with", "chocolate", "cake", "on", "the", "dessert", "menu", "and", "byob"], "ner_tags": ["O", "O", "O", "O", "B-Price", "I-Price", "O", "B-Dish", "I-Dish", "O", "O", "O", "O", "O", "B-Amenity"]}
{"sentence": "find me route directions from lexington ky to cincinnatti ohio", "tokens": ["find", "me", "route", "directions", "from", "lexington", "ky", "to", "cincinnatti", "ohio"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "I-Location", "O", "B-Location", "I-Location"]}
{"sentence": "find me soul food in los angeles", "tokens": ["find", "me", "soul", "food", "in", "los", "angeles"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "B-Location", "I-Location"]}
{"sentence": "find me the best rated chinese restaurant in the twin cities", "tokens": ["find", "me", "the", "best", "rated", "chinese", "restaurant", "in", "the", "twin", "cities"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "B-Cuisine", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "find me the closest bakers", "tokens": ["find", "me", "the", "closest", "bakers"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine"]}
{"sentence": "find me the closest ihop", "tokens": ["find", "me", "the", "closest", "ihop"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name"]}
{"sentence": "find me the closest sonic drive thru", "tokens": ["find", "me", "the", "closest", "sonic", "drive", "thru"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name", "B-Amenity", "I-Amenity"]}
{"sentence": "find me the closest walmart", "tokens": ["find", "me", "the", "closest", "walmart"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name"]}
{"sentence": "find me the nearest chase bank", "tokens": ["find", "me", "the", "nearest", "chase", "bank"], "ner_tags": ["O", "O", "O", "B-Location", "B-Amenity", "I-Amenity"]}
{"sentence": "find me the nearest mcdonalds please", "tokens": ["find", "me", "the", "nearest", "mcdonalds", "please"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name", "O"]}
{"sentence": "find me the nearest placed to eat", "tokens": ["find", "me", "the", "nearest", "placed", "to", "eat"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "O"]}
{"sentence": "find me the phone number to dominos pizza", "tokens": ["find", "me", "the", "phone", "number", "to", "dominos", "pizza"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "find me the phone number to the closest afghan restaurant", "tokens": ["find", "me", "the", "phone", "number", "to", "the", "closest", "afghan", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "find my someplace around here where i can rock out with a smoothie", "tokens": ["find", "my", "someplace", "around", "here", "where", "i", "can", "rock", "out", "with", "a", "smoothie"], "ner_tags": ["O", "O", "B-Location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "find nearby restaurants with coupons", "tokens": ["find", "nearby", "restaurants", "with", "coupons"], "ner_tags": ["O", "B-Location", "O", "O", "B-Amenity"]}
{"sentence": "find out how to get to the chipotle mexican grill on 12 th and pine", "tokens": ["find", "out", "how", "to", "get", "to", "the", "chipotle", "mexican", "grill", "on", "12", "th", "and", "pine"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "find pizza places", "tokens": ["find", "pizza", "places"], "ner_tags": ["O", "B-Cuisine", "O"]}
{"sentence": "find ratings of all local italian restaurants", "tokens": ["find", "ratings", "of", "all", "local", "italian", "restaurants"], "ner_tags": ["O", "B-Rating", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "find restaurant with italian food", "tokens": ["find", "restaurant", "with", "italian", "food"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "find restaurants within 5 miles with entrees under 15", "tokens": ["find", "restaurants", "within", "5", "miles", "with", "entrees", "under", "15"], "ner_tags": ["O", "O", "B-Location", "I-Location", "I-Location", "O", "B-Price", "I-Price", "I-Price"]}
{"sentence": "find somewhere to eat that is open before 7 am with average pricing along the route", "tokens": ["find", "somewhere", "to", "eat", "that", "is", "open", "before", "7", "am", "with", "average", "pricing", "along", "the", "route"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "O", "B-Price", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "find thai food", "tokens": ["find", "thai", "food"], "ner_tags": ["O", "B-Cuisine", "O"]}
{"sentence": "find the closest brewpub", "tokens": ["find", "the", "closest", "brewpub"], "ner_tags": ["O", "O", "B-Location", "B-Cuisine"]}
{"sentence": "find the closest dunkin donuts", "tokens": ["find", "the", "closest", "dunkin", "donuts"], "ner_tags": ["O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "find the closest homeward bound that has a happy hour", "tokens": ["find", "the", "closest", "homeward", "bound", "that", "has", "a", "happy", "hour"], "ner_tags": ["O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "find the closest sea food restaurant", "tokens": ["find", "the", "closest", "sea", "food", "restaurant"], "ner_tags": ["O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "find the nearest chicken stand", "tokens": ["find", "the", "nearest", "chicken", "stand"], "ner_tags": ["O", "O", "B-Location", "B-Cuisine", "I-Cuisine"]}
{"sentence": "find the nearest irish restaurant", "tokens": ["find", "the", "nearest", "irish", "restaurant"], "ner_tags": ["O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "find the number of the best barbecue joint in town", "tokens": ["find", "the", "number", "of", "the", "best", "barbecue", "joint", "in", "town"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "find us a deli near central park", "tokens": ["find", "us", "a", "deli", "near", "central", "park"], "ner_tags": ["O", "O", "O", "B-Cuisine", "B-Location", "I-Location", "I-Location"]}
{"sentence": "find us a sushi bar near brooklyn heights", "tokens": ["find", "us", "a", "sushi", "bar", "near", "brooklyn", "heights"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "B-Location", "I-Location", "I-Location"]}
{"sentence": "find us a sushi bar near jackson", "tokens": ["find", "us", "a", "sushi", "bar", "near", "jackson"], "ner_tags": ["O", "O", "O", "B-Cuisine", "B-Amenity", "B-Location", "I-Location"]}
{"sentence": "find us locations of china wok", "tokens": ["find", "us", "locations", "of", "china", "wok"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "get me to a good pho restaurant", "tokens": ["get", "me", "to", "a", "good", "pho", "restaurant"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "B-Cuisine", "O"]}
{"sentence": "get me to a mexican place", "tokens": ["get", "me", "to", "a", "mexican", "place"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "get me to the best italian restaurant with the highest rating", "tokens": ["get", "me", "to", "the", "best", "italian", "restaurant", "with", "the", "highest", "rating"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "B-Cuisine", "O", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "give me a list of restaurants that have seafood on the menu", "tokens": ["give", "me", "a", "list", "of", "restaurants", "that", "have", "seafood", "on", "the", "menu"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O"]}
{"sentence": "give me a random restaurant that i havent been to yet", "tokens": ["give", "me", "a", "random", "restaurant", "that", "i", "havent", "been", "to", "yet"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "give me directions to a mcdonalds", "tokens": ["give", "me", "directions", "to", "a", "mcdonalds"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "give me directions to an arbys", "tokens": ["give", "me", "directions", "to", "an", "arbys"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "give me directions to saturn grill", "tokens": ["give", "me", "directions", "to", "saturn", "grill"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "give me the closest place that does sushi", "tokens": ["give", "me", "the", "closest", "place", "that", "does", "sushi"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "O", "B-Dish"]}
{"sentence": "give me the hours for the closest waffle house", "tokens": ["give", "me", "the", "hours", "for", "the", "closest", "waffle", "house"], "ner_tags": ["O", "O", "O", "B-Hours", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine"]}
{"sentence": "give me the locations of the buffets in town", "tokens": ["give", "me", "the", "locations", "of", "the", "buffets", "in", "town"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "B-Cuisine", "B-Location", "I-Location"]}
{"sentence": "give me the names of the three closest pizza parlors", "tokens": ["give", "me", "the", "names", "of", "the", "three", "closest", "pizza", "parlors"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine"]}
{"sentence": "going to shake shack wheres the best place to park", "tokens": ["going", "to", "shake", "shack", "wheres", "the", "best", "place", "to", "park"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O", "O", "O", "O"]}
{"sentence": "gryos nears restaurant serving them", "tokens": ["gryos", "nears", "restaurant", "serving", "them"], "ner_tags": ["B-Dish", "B-Location", "O", "O", "O"]}
{"sentence": "hard rock hotel restaurant near me", "tokens": ["hard", "rock", "hotel", "restaurant", "near", "me"], "ner_tags": ["B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "hello would you please take me to sushi stop", "tokens": ["hello", "would", "you", "please", "take", "me", "to", "sushi", "stop"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "hello yes i need a steakhouse reservations childrens menu and pricing along with reviews of at least 3 stars", "tokens": ["hello", "yes", "i", "need", "a", "steakhouse", "reservations", "childrens", "menu", "and", "pricing", "along", "with", "reviews", "of", "at", "least", "3", "stars"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "B-Amenity", "I-Amenity", "I-Amenity", "O", "O", "O", "O", "O", "O", "B-Rating", "I-Rating", "I-Rating", "I-Rating"]}
{"sentence": "help me find a burger joint", "tokens": ["help", "me", "find", "a", "burger", "joint"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "I-Cuisine"]}
{"sentence": "help me find a five star chinese buffet within ten minutes of my current location", "tokens": ["help", "me", "find", "a", "five", "star", "chinese", "buffet", "within", "ten", "minutes", "of", "my", "current", "location"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "I-Rating", "B-Cuisine", "I-Cuisine", "B-Location", "I-Location", "I-Location", "O", "O", "O", "O"]}
{"sentence": "help me find a good place to eat", "tokens": ["help", "me", "find", "a", "good", "place", "to", "eat"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "O", "O", "O"]}
{"sentence": "help me find a high end restaurant that is open until 11 pm", "tokens": ["help", "me", "find", "a", "high", "end", "restaurant", "that", "is", "open", "until", "11", "pm"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "help me find a place for fast food", "tokens": ["help", "me", "find", "a", "place", "for", "fast", "food"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine"]}
{"sentence": "help me find a place my kids would like to eat", "tokens": ["help", "me", "find", "a", "place", "my", "kids", "would", "like", "to", "eat"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "help me find a sports bar that is smoke friendly", "tokens": ["help", "me", "find", "a", "sports", "bar", "that", "is", "smoke", "friendly"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "help me find jack in the box in beverly hills", "tokens": ["help", "me", "find", "jack", "in", "the", "box", "in", "beverly", "hills"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "hey could you look up a restaurant with the best meatballs in town", "tokens": ["hey", "could", "you", "look", "up", "a", "restaurant", "with", "the", "best", "meatballs", "in", "town"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Rating", "B-Dish", "B-Location", "I-Location"]}
{"sentence": "hey im looking for a great date night idea any suggestions", "tokens": ["hey", "im", "looking", "for", "a", "great", "date", "night", "idea", "any", "suggestions"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Amenity", "I-Amenity", "O", "O", "O"]}
{"sentence": "hey take me to variety food court", "tokens": ["hey", "take", "me", "to", "variety", "food", "court"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "hey tell me where theres a taco bell nearby", "tokens": ["hey", "tell", "me", "where", "theres", "a", "taco", "bell", "nearby"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location"]}
{"sentence": "hi hershel is there any place around here with something good to eat", "tokens": ["hi", "hershel", "is", "there", "any", "place", "around", "here", "with", "something", "good", "to", "eat"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Rating", "O", "O"]}
{"sentence": "hi i would like some thai food is there any nearby", "tokens": ["hi", "i", "would", "like", "some", "thai", "food", "is", "there", "any", "nearby"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "B-Location"]}
{"sentence": "hi please find me a sushi restaurant that has good reviews and that isnt too expensive", "tokens": ["hi", "please", "find", "me", "a", "sushi", "restaurant", "that", "has", "good", "reviews", "and", "that", "isnt", "too", "expensive"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Rating", "I-Rating", "O", "O", "B-Price", "I-Price", "I-Price"]}
{"sentence": "hi would you please find a restaurant with cheap vegan options", "tokens": ["hi", "would", "you", "please", "find", "a", "restaurant", "with", "cheap", "vegan", "options"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Price", "B-Cuisine", "O"]}
{"sentence": "how are the prices at donatellas", "tokens": ["how", "are", "the", "prices", "at", "donatellas"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "how big are the portions at cheddars", "tokens": ["how", "big", "are", "the", "portions", "at", "cheddars"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "how can i get to anthonys cafe on the waterfront", "tokens": ["how", "can", "i", "get", "to", "anthonys", "cafe", "on", "the", "waterfront"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Location"]}
{"sentence": "how can i quickly get to the nearst long john silvers", "tokens": ["how", "can", "i", "quickly", "get", "to", "the", "nearst", "long", "john", "silvers"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "how close is the closest mexican restaurant", "tokens": ["how", "close", "is", "the", "closest", "mexican", "restaurant"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "how close is the nearest olive garden", "tokens": ["how", "close", "is", "the", "nearest", "olive", "garden"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "how do i get from the honolulu zoo to izakaya gazen", "tokens": ["how", "do", "i", "get", "from", "the", "honolulu", "zoo", "to", "izakaya", "gazen"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "how do i get to burger king on oak street", "tokens": ["how", "do", "i", "get", "to", "burger", "king", "on", "oak", "street"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "how do i get to the cloest port of subs", "tokens": ["how", "do", "i", "get", "to", "the", "cloest", "port", "of", "subs"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "how do i get to the nearest hooters", "tokens": ["how", "do", "i", "get", "to", "the", "nearest", "hooters"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "B-Restaurant_Name"]}
{"sentence": "how do i get to the nearest taco bell", "tokens": ["how", "do", "i", "get", "to", "the", "nearest", "taco", "bell"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "how do you get to als diner from here", "tokens": ["how", "do", "you", "get", "to", "als", "diner", "from", "here"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location"]}
{"sentence": "how expensive is olive garden", "tokens": ["how", "expensive", "is", "olive", "garden"], "ner_tags": ["O", "B-Price", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "how expensive is the food at chinese express", "tokens": ["how", "expensive", "is", "the", "food", "at", "chinese", "express"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "how far am i from the nearest bagel shop", "tokens": ["how", "far", "am", "i", "from", "the", "nearest", "bagel", "shop"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "how far am i from true thai right now", "tokens": ["how", "far", "am", "i", "from", "true", "thai", "right", "now"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O"]}
{"sentence": "how far away is a chicago style sub joint", "tokens": ["how", "far", "away", "is", "a", "chicago", "style", "sub", "joint"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "I-Cuisine", "I-Cuisine"]}
{"sentence": "how far away is the closest burger king", "tokens": ["how", "far", "away", "is", "the", "closest", "burger", "king"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "how far away is the nearest applebees", "tokens": ["how", "far", "away", "is", "the", "nearest", "applebees"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "B-Restaurant_Name"]}
{"sentence": "how far away is the nearest steak house", "tokens": ["how", "far", "away", "is", "the", "nearest", "steak", "house"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine"]}
{"sentence": "how far for a burger place", "tokens": ["how", "far", "for", "a", "burger", "place"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "how far is evergreen taiwanese restaurant from the himalayan", "tokens": ["how", "far", "is", "evergreen", "taiwanese", "restaurant", "from", "the", "himalayan"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O", "B-Location"]}
{"sentence": "how far is the arbys", "tokens": ["how", "far", "is", "the", "arbys"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "how far is the english pub that serves a fry up", "tokens": ["how", "far", "is", "the", "english", "pub", "that", "serves", "a", "fry", "up"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "how far is the nearest applebeas", "tokens": ["how", "far", "is", "the", "nearest", "applebeas"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Restaurant_Name"]}
{"sentence": "how far is the nearest olive garden", "tokens": ["how", "far", "is", "the", "nearest", "olive", "garden"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "how far is the taco bell", "tokens": ["how", "far", "is", "the", "taco", "bell"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "how far to the nearest fast food", "tokens": ["how", "far", "to", "the", "nearest", "fast", "food"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine"]}
{"sentence": "how far to the next subway", "tokens": ["how", "far", "to", "the", "next", "subway"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Restaurant_Name"]}
{"sentence": "how late does mcdonalds serve breakfast", "tokens": ["how", "late", "does", "mcdonalds", "serve", "breakfast"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "O"]}
{"sentence": "how late pfchangs in paradise road will be open", "tokens": ["how", "late", "pfchangs", "in", "paradise", "road", "will", "be", "open"], "ner_tags": ["O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "how long will it take me to drive from glasgow to the three chimneys in skye", "tokens": ["how", "long", "will", "it", "take", "me", "to", "drive", "from", "glasgow", "to", "the", "three", "chimneys", "in", "skye"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Location", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location"]}
{"sentence": "how long will it take me to drive to the band box diner from here", "tokens": ["how", "long", "will", "it", "take", "me", "to", "drive", "to", "the", "band", "box", "diner", "from", "here"], "ner_tags": ["O", "B-Hours", "O", "O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "O"]}
{"sentence": "how many burger kings are around", "tokens": ["how", "many", "burger", "kings", "are", "around"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location"]}
{"sentence": "how many burger kings are nearby", "tokens": ["how", "many", "burger", "kings", "are", "nearby"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location"]}
{"sentence": "how many chinese restaurants are there", "tokens": ["how", "many", "chinese", "restaurants", "are", "there"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "O"]}
{"sentence": "how many miles will it take me to get to dominoes", "tokens": ["how", "many", "miles", "will", "it", "take", "me", "to", "get", "to", "dominoes"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "how many pizza restaurants are nearby", "tokens": ["how", "many", "pizza", "restaurants", "are", "nearby"], "ner_tags": ["O", "O", "B-Dish", "O", "O", "B-Location"]}
{"sentence": "how many places serve pizza", "tokens": ["how", "many", "places", "serve", "pizza"], "ner_tags": ["O", "O", "O", "O", "B-Dish"]}
{"sentence": "how many restaurants near me have bathrooms", "tokens": ["how", "many", "restaurants", "near", "me", "have", "bathrooms"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "B-Amenity", "I-Amenity"]}
{"sentence": "how many restaurants that accept reservations are within 10 miles", "tokens": ["how", "many", "restaurants", "that", "accept", "reservations", "are", "within", "10", "miles"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "how much is a plate at the olive garden", "tokens": ["how", "much", "is", "a", "plate", "at", "the", "olive", "garden"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "how much is an average plate at spencers diner", "tokens": ["how", "much", "is", "an", "average", "plate", "at", "spencers", "diner"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "how much is small box of fries at jack in the box", "tokens": ["how", "much", "is", "small", "box", "of", "fries", "at", "jack", "in", "the", "box"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "how much longer is subway open", "tokens": ["how", "much", "longer", "is", "subway", "open"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "B-Hours"]}
{"sentence": "how should i dress when going to francoiss", "tokens": ["how", "should", "i", "dress", "when", "going", "to", "francoiss"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "huges bar and brill thanks", "tokens": ["huges", "bar", "and", "brill", "thanks"], "ner_tags": ["B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "i am diabetic and need to know if there are any health stores in the area", "tokens": ["i", "am", "diabetic", "and", "need", "to", "know", "if", "there", "are", "any", "health", "stores", "in", "the", "area"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "i am having trouble fully waking up could you find some reviews of local coffee shops", "tokens": ["i", "am", "having", "trouble", "fully", "waking", "up", "could", "you", "find", "some", "reviews", "of", "local", "coffee", "shops"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Location", "B-Dish", "O"]}
{"sentence": "i am hungry please find nearby restaurants", "tokens": ["i", "am", "hungry", "please", "find", "nearby", "restaurants"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "O"]}
{"sentence": "i am in the mood for shrimp where is the closet place i can go", "tokens": ["i", "am", "in", "the", "mood", "for", "shrimp", "where", "is", "the", "closet", "place", "i", "can", "go"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "O", "O", "O", "B-Location", "O", "O", "O", "O"]}
{"sentence": "i am in the mood for some chinese food can you find me a place", "tokens": ["i", "am", "in", "the", "mood", "for", "some", "chinese", "food", "can", "you", "find", "me", "a", "place"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "i am looking for a dennys that is 5 miles from here", "tokens": ["i", "am", "looking", "for", "a", "dennys", "that", "is", "5", "miles", "from", "here"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "i am looking for a good place to eat", "tokens": ["i", "am", "looking", "for", "a", "good", "place", "to", "eat"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "O", "O", "O"]}
{"sentence": "i am looking for a local pizza restaurant that delivers", "tokens": ["i", "am", "looking", "for", "a", "local", "pizza", "restaurant", "that", "delivers"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "B-Dish", "O", "O", "B-Amenity"]}
{"sentence": "i am looking for a mediterranean restaurant that delivers", "tokens": ["i", "am", "looking", "for", "a", "mediterranean", "restaurant", "that", "delivers"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Amenity"]}
{"sentence": "i am looking for a mexican restuarant that has a mariachi band", "tokens": ["i", "am", "looking", "for", "a", "mexican", "restuarant", "that", "has", "a", "mariachi", "band"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i am looking for a nice restaurant within 30 miles that accepts credit cards", "tokens": ["i", "am", "looking", "for", "a", "nice", "restaurant", "within", "30", "miles", "that", "accepts", "credit", "cards"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "O", "B-Location", "I-Location", "I-Location", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "i am looking for a restaurant that allows smoking", "tokens": ["i", "am", "looking", "for", "a", "restaurant", "that", "allows", "smoking"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i am looking for a restaurant that serves english food i want the price to be cheap to moderate", "tokens": ["i", "am", "looking", "for", "a", "restaurant", "that", "serves", "english", "food", "i", "want", "the", "price", "to", "be", "cheap", "to", "moderate"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "O", "O", "O", "B-Price", "I-Price", "I-Price"]}
{"sentence": "i am looking for an olive garden are there any close by", "tokens": ["i", "am", "looking", "for", "an", "olive", "garden", "are", "there", "any", "close", "by"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "i am looking for lunch buffets within 15 minutes driving distance", "tokens": ["i", "am", "looking", "for", "lunch", "buffets", "within", "15", "minutes", "driving", "distance"], "ner_tags": ["O", "O", "O", "O", "B-Hours", "B-Amenity", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "i am looking for sandwhiches", "tokens": ["i", "am", "looking", "for", "sandwhiches"], "ner_tags": ["O", "O", "O", "O", "B-Dish"]}
{"sentence": "i am looking for some chinese food", "tokens": ["i", "am", "looking", "for", "some", "chinese", "food"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "i am looking for the best large buffet within 15 miles", "tokens": ["i", "am", "looking", "for", "the", "best", "large", "buffet", "within", "15", "miles"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Amenity", "I-Amenity", "B-Location", "I-Location", "I-Location"]}
{"sentence": "i feel in the mood for spicy food what can you do for me", "tokens": ["i", "feel", "in", "the", "mood", "for", "spicy", "food", "what", "can", "you", "do", "for", "me"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "i feel the need for some killer barbeque help me please", "tokens": ["i", "feel", "the", "need", "for", "some", "killer", "barbeque", "help", "me", "please"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Rating", "B-Dish", "O", "O", "O"]}
{"sentence": "i hate smoke so list off some eateries i can go relax at", "tokens": ["i", "hate", "smoke", "so", "list", "off", "some", "eateries", "i", "can", "go", "relax", "at"], "ner_tags": ["O", "B-Amenity", "I-Amenity", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "O"]}
{"sentence": "i have a coupon for sweet tomatoes where is the nearest one", "tokens": ["i", "have", "a", "coupon", "for", "sweet", "tomatoes", "where", "is", "the", "nearest", "one"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O", "B-Location", "O"]}
{"sentence": "i have a craving for fish and chips but i am on a budget so i need them cheap", "tokens": ["i", "have", "a", "craving", "for", "fish", "and", "chips", "but", "i", "am", "on", "a", "budget", "so", "i", "need", "them", "cheap"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Cuisine", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Price"]}
{"sentence": "i have a dinner date at 5 where should i take her", "tokens": ["i", "have", "a", "dinner", "date", "at", "5", "where", "should", "i", "take", "her"], "ner_tags": ["O", "O", "O", "B-Hours", "B-Amenity", "B-Hours", "I-Hours", "O", "O", "O", "O", "O"]}
{"sentence": "i have alcohol where can i find a good appetizer", "tokens": ["i", "have", "alcohol", "where", "can", "i", "find", "a", "good", "appetizer"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Rating", "B-Cuisine"]}
{"sentence": "i have an important business luncheon and need to find a place that caters to professionals", "tokens": ["i", "have", "an", "important", "business", "luncheon", "and", "need", "to", "find", "a", "place", "that", "caters", "to", "professionals"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "B-Hours", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "i have to be back at the office before 3 pm which restaurant is located within 1 mile from here", "tokens": ["i", "have", "to", "be", "back", "at", "the", "office", "before", "3", "pm", "which", "restaurant", "is", "located", "within", "1", "mile", "from", "here"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "i just need to find the closest diner to pick up some quick cheap breakfast", "tokens": ["i", "just", "need", "to", "find", "the", "closest", "diner", "to", "pick", "up", "some", "quick", "cheap", "breakfast"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "O", "O", "O", "O", "O", "B-Price", "B-Hours"]}
{"sentence": "i just want to eat at home could you direct me to a fast food place that has carry out", "tokens": ["i", "just", "want", "to", "eat", "at", "home", "could", "you", "direct", "me", "to", "a", "fast", "food", "place", "that", "has", "carry", "out"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i need a 4 star rated subway nearby", "tokens": ["i", "need", "a", "4", "star", "rated", "subway", "nearby"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "I-Rating", "B-Restaurant_Name", "B-Location"]}
{"sentence": "i need a 5 star rated sushi bar close by", "tokens": ["i", "need", "a", "5", "star", "rated", "sushi", "bar", "close", "by"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "O", "B-Cuisine", "I-Cuisine", "B-Location", "I-Location"]}
{"sentence": "i need a cheap restaurant for brunch", "tokens": ["i", "need", "a", "cheap", "restaurant", "for", "brunch"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "B-Hours"]}
{"sentence": "i need a close restaurant that is open currently", "tokens": ["i", "need", "a", "close", "restaurant", "that", "is", "open", "currently"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "O", "B-Hours", "I-Hours"]}
{"sentence": "i need a deli that caters", "tokens": ["i", "need", "a", "deli", "that", "caters"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Amenity"]}
{"sentence": "i need a family friendly place to eat tonight", "tokens": ["i", "need", "a", "family", "friendly", "place", "to", "eat", "tonight"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O", "B-Hours"]}
{"sentence": "i need a kid friendly lunch place", "tokens": ["i", "need", "a", "kid", "friendly", "lunch", "place"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "B-Hours", "O"]}
{"sentence": "i need a kid friendly place to get sushi", "tokens": ["i", "need", "a", "kid", "friendly", "place", "to", "get", "sushi"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O", "B-Cuisine"]}
{"sentence": "i need a korean barbecue restaurant", "tokens": ["i", "need", "a", "korean", "barbecue", "restaurant"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "i need a late night spot with good service", "tokens": ["i", "need", "a", "late", "night", "spot", "with", "good", "service"], "ner_tags": ["O", "O", "O", "B-Hours", "I-Hours", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "i need a list of restaurants that take the diners card in a 5 mile radius", "tokens": ["i", "need", "a", "list", "of", "restaurants", "that", "take", "the", "diners", "card", "in", "a", "5", "mile", "radius"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "i need a middle eastern restaurant with friendly service", "tokens": ["i", "need", "a", "middle", "eastern", "restaurant", "with", "friendly", "service"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "i need a place for kids to eat at 12 pm", "tokens": ["i", "need", "a", "place", "for", "kids", "to", "eat", "at", "12", "pm"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "O", "O", "O", "B-Hours", "I-Hours"]}
{"sentence": "i need a place for smoking", "tokens": ["i", "need", "a", "place", "for", "smoking"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity"]}
{"sentence": "i need a place to get enchiladas thats open every day", "tokens": ["i", "need", "a", "place", "to", "get", "enchiladas", "thats", "open", "every", "day"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "i need a quick bite to eat", "tokens": ["i", "need", "a", "quick", "bite", "to", "eat"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "O", "O"]}
{"sentence": "i need a reservation for 12 at 6 pm at the nearest asian restaurant", "tokens": ["i", "need", "a", "reservation", "for", "12", "at", "6", "pm", "at", "the", "nearest", "asian", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Hours", "I-Hours", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "i need a reservation for two at the nearest steakhouse", "tokens": ["i", "need", "a", "reservation", "for", "two", "at", "the", "nearest", "steakhouse"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Location", "B-Cuisine"]}
{"sentence": "i need a restaurant with lots of parking and large portions called nations restaurant news", "tokens": ["i", "need", "a", "restaurant", "with", "lots", "of", "parking", "and", "large", "portions", "called", "nations", "restaurant", "news"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "O", "B-Amenity", "I-Amenity", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "i need a thai place nearby can you find one", "tokens": ["i", "need", "a", "thai", "place", "nearby", "can", "you", "find", "one"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "O", "O", "O", "O"]}
{"sentence": "i need a two pm reservation for hooters thanks kitt", "tokens": ["i", "need", "a", "two", "pm", "reservation", "for", "hooters", "thanks", "kitt"], "ner_tags": ["O", "O", "O", "B-Hours", "I-Hours", "O", "O", "B-Restaurant_Name", "O", "O"]}
{"sentence": "i need an expensive place within 5 miles of here", "tokens": ["i", "need", "an", "expensive", "place", "within", "5", "miles", "of", "here"], "ner_tags": ["O", "O", "O", "B-Price", "O", "B-Location", "I-Location", "I-Location", "O", "O"]}
{"sentence": "i need an inexpensive italian restaurant with big portions", "tokens": ["i", "need", "an", "inexpensive", "italian", "restaurant", "with", "big", "portions"], "ner_tags": ["O", "O", "O", "B-Price", "B-Cuisine", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i need an italian restaurant with a kids menu", "tokens": ["i", "need", "an", "italian", "restaurant", "with", "a", "kids", "menu"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i need an unbelievably priced place on kingsdale st that has a bar atmosphere", "tokens": ["i", "need", "an", "unbelievably", "priced", "place", "on", "kingsdale", "st", "that", "has", "a", "bar", "atmosphere"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "O", "B-Location", "I-Location", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i need directions from my location to luigis pizza", "tokens": ["i", "need", "directions", "from", "my", "location", "to", "luigis", "pizza"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "i need directions to the closest pancake place", "tokens": ["i", "need", "directions", "to", "the", "closest", "pancake", "place"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "B-Dish", "O"]}
{"sentence": "i need directions to the nearest ethiopian restaurant", "tokens": ["i", "need", "directions", "to", "the", "nearest", "ethiopian", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "i need food delivered from near downtown", "tokens": ["i", "need", "food", "delivered", "from", "near", "downtown"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "B-Location", "I-Location"]}
{"sentence": "i need gluten free options on the restauarant menu", "tokens": ["i", "need", "gluten", "free", "options", "on", "the", "restauarant", "menu"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "O", "O", "O", "O"]}
{"sentence": "i need help finding a soul food restaurant", "tokens": ["i", "need", "help", "finding", "a", "soul", "food", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O"]}
{"sentence": "i need info on wendys opening hours", "tokens": ["i", "need", "info", "on", "wendys", "opening", "hours"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "B-Hours", "I-Hours"]}
{"sentence": "i need phone numbers to carry out pizza places within 2 miles", "tokens": ["i", "need", "phone", "numbers", "to", "carry", "out", "pizza", "places", "within", "2", "miles"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "i need reservations for 4 at the nearest sushi bar", "tokens": ["i", "need", "reservations", "for", "4", "at", "the", "nearest", "sushi", "bar"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine"]}
{"sentence": "i need some potato fries on beacon hill with quick service", "tokens": ["i", "need", "some", "potato", "fries", "on", "beacon", "hill", "with", "quick", "service"], "ner_tags": ["O", "O", "O", "B-Dish", "I-Dish", "O", "B-Location", "I-Location", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i need something hot to eat on the way to work", "tokens": ["i", "need", "something", "hot", "to", "eat", "on", "the", "way", "to", "work"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "i need somewhere decently priced", "tokens": ["i", "need", "somewhere", "decently", "priced"], "ner_tags": ["O", "O", "O", "B-Price", "O"]}
{"sentence": "i need the closest chic fil a that is still serving the peach shake", "tokens": ["i", "need", "the", "closest", "chic", "fil", "a", "that", "is", "still", "serving", "the", "peach", "shake"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "i need the hours of operation for lulus restaurant in gulf shores", "tokens": ["i", "need", "the", "hours", "of", "operation", "for", "lulus", "restaurant", "in", "gulf", "shores"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "O", "O", "B-Location", "I-Location"]}
{"sentence": "i need to book a business breakfast at the right price", "tokens": ["i", "need", "to", "book", "a", "business", "breakfast", "at", "the", "right", "price"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "O", "O", "O", "B-Price", "O"]}
{"sentence": "i need to find a galvins harp and bard near here", "tokens": ["i", "need", "to", "find", "a", "galvins", "harp", "and", "bard", "near", "here"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location"]}
{"sentence": "i need to find a place that is open every day", "tokens": ["i", "need", "to", "find", "a", "place", "that", "is", "open", "every", "day"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "i need to find a restaurant int he government center with good service an a decent price", "tokens": ["i", "need", "to", "find", "a", "restaurant", "int", "he", "government", "center", "with", "good", "service", "an", "a", "decent", "price"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "O", "B-Rating", "I-Rating", "O", "O", "B-Price", "O"]}
{"sentence": "i need to know of a place that serves breakfast beginning as early as 5 30 am", "tokens": ["i", "need", "to", "know", "of", "a", "place", "that", "serves", "breakfast", "beginning", "as", "early", "as", "5", "30", "am"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "i really feel like seafood right now whats close", "tokens": ["i", "really", "feel", "like", "seafood", "right", "now", "whats", "close"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Location"]}
{"sentence": "i smell bread take me there", "tokens": ["i", "smell", "bread", "take", "me", "there"], "ner_tags": ["O", "O", "B-Dish", "O", "O", "O"]}
{"sentence": "i think i could go for some mexican food right now can you see if there is anything nearby", "tokens": ["i", "think", "i", "could", "go", "for", "some", "mexican", "food", "right", "now", "can", "you", "see", "if", "there", "is", "anything", "nearby"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Location"]}
{"sentence": "i wanna try something new find me a restaurant that carries sushi", "tokens": ["i", "wanna", "try", "something", "new", "find", "me", "a", "restaurant", "that", "carries", "sushi"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "i want a 5 star restaurant that does carry out", "tokens": ["i", "want", "a", "5", "star", "restaurant", "that", "does", "carry", "out"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i want a beer from the cambridge brewing company", "tokens": ["i", "want", "a", "beer", "from", "the", "cambridge", "brewing", "company"], "ner_tags": ["O", "O", "O", "B-Dish", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "i want a buffet", "tokens": ["i", "want", "a", "buffet"], "ner_tags": ["O", "O", "O", "B-Amenity"]}
{"sentence": "i want a good milkshake where can i find it nearby", "tokens": ["i", "want", "a", "good", "milkshake", "where", "can", "i", "find", "it", "nearby"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Dish", "O", "O", "O", "O", "O", "B-Location"]}
{"sentence": "i want a great milkshake", "tokens": ["i", "want", "a", "great", "milkshake"], "ner_tags": ["O", "O", "O", "O", "B-Dish"]}
{"sentence": "i want a greek sandwich with goat cheese", "tokens": ["i", "want", "a", "greek", "sandwich", "with", "goat", "cheese"], "ner_tags": ["O", "O", "O", "B-Cuisine", "B-Dish", "I-Dish", "I-Dish", "I-Dish"]}
{"sentence": "i want a list of restaurants that are chinese buffets within 5 miles of here", "tokens": ["i", "want", "a", "list", "of", "restaurants", "that", "are", "chinese", "buffets", "within", "5", "miles", "of", "here"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "B-Amenity", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "i want a pad thai place around here thats open all day", "tokens": ["i", "want", "a", "pad", "thai", "place", "around", "here", "thats", "open", "all", "day"], "ner_tags": ["O", "O", "O", "B-Dish", "I-Dish", "O", "B-Location", "I-Location", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "i want a place that allows smoking and serves health food", "tokens": ["i", "want", "a", "place", "that", "allows", "smoking", "and", "serves", "health", "food"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Amenity", "B-Cuisine", "O"]}
{"sentence": "i want a restaurant near the campground that allows smoking", "tokens": ["i", "want", "a", "restaurant", "near", "the", "campground", "that", "allows", "smoking"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i want a restaurant on smith st that serves toast", "tokens": ["i", "want", "a", "restaurant", "on", "smith", "st", "that", "serves", "toast"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Dish"]}
{"sentence": "i want a restaurant that is open after 9 pm that is quite and has nice service", "tokens": ["i", "want", "a", "restaurant", "that", "is", "open", "after", "9", "pm", "that", "is", "quite", "and", "has", "nice", "service"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "O", "O", "B-Amenity", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i want a restaurant where i can order some carry out potato soup", "tokens": ["i", "want", "a", "restaurant", "where", "i", "can", "order", "some", "carry", "out", "potato", "soup"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Dish", "I-Dish"]}
{"sentence": "i want a taco from a taco truck", "tokens": ["i", "want", "a", "taco", "from", "a", "taco", "truck"], "ner_tags": ["O", "O", "O", "B-Dish", "O", "O", "B-Cuisine", "I-Cuisine"]}
{"sentence": "i want an upscale steakhouse that has valet parking", "tokens": ["i", "want", "an", "upscale", "steakhouse", "that", "has", "valet", "parking"], "ner_tags": ["O", "O", "O", "B-Amenity", "B-Cuisine", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i want mcdonalds", "tokens": ["i", "want", "mcdonalds"], "ner_tags": ["O", "O", "B-Restaurant_Name"]}
{"sentence": "i want mexican food thats cheap which restaurant is closest", "tokens": ["i", "want", "mexican", "food", "thats", "cheap", "which", "restaurant", "is", "closest"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "B-Price", "O", "O", "O", "B-Location"]}
{"sentence": "i want pizza", "tokens": ["i", "want", "pizza"], "ner_tags": ["O", "O", "B-Dish"]}
{"sentence": "i want pizza im looking for the best pizza restaurant that is kid friendly and has carry out", "tokens": ["i", "want", "pizza", "im", "looking", "for", "the", "best", "pizza", "restaurant", "that", "is", "kid", "friendly", "and", "has", "carry", "out"], "ner_tags": ["O", "O", "B-Dish", "O", "O", "O", "O", "B-Rating", "B-Dish", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i want restaurants that are pet friendly", "tokens": ["i", "want", "restaurants", "that", "are", "pet", "friendly"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i want some chips and salsa", "tokens": ["i", "want", "some", "chips", "and", "salsa"], "ner_tags": ["O", "O", "O", "B-Dish", "I-Dish", "I-Dish"]}
{"sentence": "i want some taco bell", "tokens": ["i", "want", "some", "taco", "bell"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "i want something full of grease", "tokens": ["i", "want", "something", "full", "of", "grease"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine"]}
{"sentence": "i want something to eat close by", "tokens": ["i", "want", "something", "to", "eat", "close", "by"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "i want something to eat that is not fast food", "tokens": ["i", "want", "something", "to", "eat", "that", "is", "not", "fast", "food"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "I-Cuisine"]}
{"sentence": "i want tacos", "tokens": ["i", "want", "tacos"], "ner_tags": ["O", "O", "B-Dish"]}
{"sentence": "i want take out mexican food right now", "tokens": ["i", "want", "take", "out", "mexican", "food", "right", "now"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity", "B-Cuisine", "O", "O", "O"]}
{"sentence": "i want the spiciest buffalo wings in town where should i go", "tokens": ["i", "want", "the", "spiciest", "buffalo", "wings", "in", "town", "where", "should", "i", "go"], "ner_tags": ["O", "O", "O", "B-Dish", "I-Dish", "I-Dish", "B-Location", "I-Location", "O", "O", "O", "O"]}
{"sentence": "i want to eat at a very classy restaurant", "tokens": ["i", "want", "to", "eat", "at", "a", "very", "classy", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O"]}
{"sentence": "i want to eat fast food italian", "tokens": ["i", "want", "to", "eat", "fast", "food", "italian"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Cuisine"]}
{"sentence": "i want to eat hamburgers", "tokens": ["i", "want", "to", "eat", "hamburgers"], "ner_tags": ["O", "O", "O", "O", "B-Dish"]}
{"sentence": "i want to eat in the best rated restaurant in the area", "tokens": ["i", "want", "to", "eat", "in", "the", "best", "rated", "restaurant", "in", "the", "area"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Rating", "I-Rating", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "i want to eat mexican food", "tokens": ["i", "want", "to", "eat", "mexican", "food"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "i want to eat some pasta", "tokens": ["i", "want", "to", "eat", "some", "pasta"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "i want to eat sushi please find me a place", "tokens": ["i", "want", "to", "eat", "sushi", "please", "find", "me", "a", "place"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "O", "O", "O", "O", "O"]}
{"sentence": "i want to find a burger that isnt fast food", "tokens": ["i", "want", "to", "find", "a", "burger", "that", "isnt", "fast", "food"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "O", "B-Rating", "I-Rating", "I-Rating"]}
{"sentence": "i want to find a german restaurant on the lower east side that serves brunch on saturday and or sunday", "tokens": ["i", "want", "to", "find", "a", "german", "restaurant", "on", "the", "lower", "east", "side", "that", "serves", "brunch", "on", "saturday", "and", "or", "sunday"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Hours", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "i want to find a kosher deli that serves tongue and brisket sandwiches", "tokens": ["i", "want", "to", "find", "a", "kosher", "deli", "that", "serves", "tongue", "and", "brisket", "sandwiches"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "B-Dish", "O", "B-Dish", "I-Dish"]}
{"sentence": "i want to find a nearby coffee shop with the highest customer ratings", "tokens": ["i", "want", "to", "find", "a", "nearby", "coffee", "shop", "with", "the", "highest", "customer", "ratings"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "O", "O", "O", "B-Rating", "I-Rating", "I-Rating"]}
{"sentence": "i want to find a new restaurant in my area that has just opened", "tokens": ["i", "want", "to", "find", "a", "new", "restaurant", "in", "my", "area", "that", "has", "just", "opened"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "O"]}
{"sentence": "i want to find a place that serves beef patties", "tokens": ["i", "want", "to", "find", "a", "place", "that", "serves", "beef", "patties"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "i want to find a place that serves pizza by the slice", "tokens": ["i", "want", "to", "find", "a", "place", "that", "serves", "pizza", "by", "the", "slice"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Dish", "I-Dish", "I-Dish", "I-Dish"]}
{"sentence": "i want to find a place to eat that is very clean and has good service", "tokens": ["i", "want", "to", "find", "a", "place", "to", "eat", "that", "is", "very", "clean", "and", "has", "good", "service"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i want to find a place with spaghetti and meatballs", "tokens": ["i", "want", "to", "find", "a", "place", "with", "spaghetti", "and", "meatballs"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Dish", "I-Dish", "I-Dish"]}
{"sentence": "i want to find a restaurant that has a diet friendly menu", "tokens": ["i", "want", "to", "find", "a", "restaurant", "that", "has", "a", "diet", "friendly", "menu"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O"]}
{"sentence": "i want to find a restaurant with an outdoor dining section that permits smoking", "tokens": ["i", "want", "to", "find", "a", "restaurant", "with", "an", "outdoor", "dining", "section", "that", "permits", "smoking"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i want to find an italian restaurant", "tokens": ["i", "want", "to", "find", "an", "italian", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "i want to get a list of pancake restaurants that are nonsmoking", "tokens": ["i", "want", "to", "get", "a", "list", "of", "pancake", "restaurants", "that", "are", "nonsmoking"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity"]}
{"sentence": "i want to get a reservation at the best michelin rated french restaurant", "tokens": ["i", "want", "to", "get", "a", "reservation", "at", "the", "best", "michelin", "rated", "french", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Rating", "I-Rating", "I-Rating", "B-Cuisine", "O"]}
{"sentence": "i want to get price info on chez yogis restaurant in tow city", "tokens": ["i", "want", "to", "get", "price", "info", "on", "chez", "yogis", "restaurant", "in", "tow", "city"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "i want to get some chinese food", "tokens": ["i", "want", "to", "get", "some", "chinese", "food"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "i want to get to a coffee shop that serves breakfast after 11 am", "tokens": ["i", "want", "to", "get", "to", "a", "coffee", "shop", "that", "serves", "breakfast", "after", "11", "am"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "i want to get to a restauarant as fast as possible", "tokens": ["i", "want", "to", "get", "to", "a", "restauarant", "as", "fast", "as", "possible"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "i want to go dancing at a nearby place and i want scallops while im at it", "tokens": ["i", "want", "to", "go", "dancing", "at", "a", "nearby", "place", "and", "i", "want", "scallops", "while", "im", "at", "it"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "O", "O", "B-Location", "O", "O", "O", "O", "B-Dish", "O", "O", "O", "O"]}
{"sentence": "i want to go to a nicely priced place within 10 minutes that has a good tomato sauce", "tokens": ["i", "want", "to", "go", "to", "a", "nicely", "priced", "place", "within", "10", "minutes", "that", "has", "a", "good", "tomato", "sauce"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Price", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "B-Rating", "B-Dish", "I-Dish"]}
{"sentence": "i want to go to a restaurant with a high zagats rating and average plate cost of less than 20 nearby", "tokens": ["i", "want", "to", "go", "to", "a", "restaurant", "with", "a", "high", "zagats", "rating", "and", "average", "plate", "cost", "of", "less", "than", "20", "nearby"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Rating", "I-Rating", "I-Rating", "O", "B-Price", "I-Price", "I-Price", "O", "B-Price", "I-Price", "I-Price", "B-Location"]}
{"sentence": "i want to go to an indian restaurant downtown", "tokens": ["i", "want", "to", "go", "to", "an", "indian", "restaurant", "downtown"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "i want to go to taco bell or taco jhons what ever is closer", "tokens": ["i", "want", "to", "go", "to", "taco", "bell", "or", "taco", "jhons", "what", "ever", "is", "closer"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O", "O"]}
{"sentence": "i want to have lunch in downtown la", "tokens": ["i", "want", "to", "have", "lunch", "in", "downtown", "la"], "ner_tags": ["O", "O", "O", "O", "B-Hours", "O", "B-Location", "I-Location"]}
{"sentence": "i want to have tacos", "tokens": ["i", "want", "to", "have", "tacos"], "ner_tags": ["O", "O", "O", "O", "B-Dish"]}
{"sentence": "i want to make a reservation at an indian restaurant but i dont know the location of one around here", "tokens": ["i", "want", "to", "make", "a", "reservation", "at", "an", "indian", "restaurant", "but", "i", "dont", "know", "the", "location", "of", "one", "around", "here"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "O", "O", "B-Cuisine", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "i want to make dinner plans for the convention can you take me somewhere that does catering", "tokens": ["i", "want", "to", "make", "dinner", "plans", "for", "the", "convention", "can", "you", "take", "me", "somewhere", "that", "does", "catering"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity"]}
{"sentence": "i want to reserve the back room at bensons grill for my daughters birthday party next friday night is it available", "tokens": ["i", "want", "to", "reserve", "the", "back", "room", "at", "bensons", "grill", "for", "my", "daughters", "birthday", "party", "next", "friday", "night", "is", "it", "available"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O", "O", "O", "O", "B-Hours", "I-Hours", "O", "O", "O"]}
{"sentence": "i want to try a blow fish could you find a good place to try it", "tokens": ["i", "want", "to", "try", "a", "blow", "fish", "could", "you", "find", "a", "good", "place", "to", "try", "it"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "I-Dish", "O", "O", "O", "O", "B-Rating", "O", "O", "O", "O"]}
{"sentence": "i want to try something exotic", "tokens": ["i", "want", "to", "try", "something", "exotic"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine"]}
{"sentence": "i want to try something new for dinner tonight", "tokens": ["i", "want", "to", "try", "something", "new", "for", "dinner", "tonight"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Cuisine", "B-Hours"]}
{"sentence": "i was some expensive dumplings", "tokens": ["i", "was", "some", "expensive", "dumplings"], "ner_tags": ["O", "O", "O", "B-Price", "B-Dish"]}
{"sentence": "i wnat to go to a steakhouse with outdoor dining within 10 miles", "tokens": ["i", "wnat", "to", "go", "to", "a", "steakhouse", "with", "outdoor", "dining", "within", "10", "miles"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Amenity", "I-Amenity", "B-Location", "I-Location", "I-Location"]}
{"sentence": "i would like a list of restaurants that are smoke free near my house", "tokens": ["i", "would", "like", "a", "list", "of", "restaurants", "that", "are", "smoke", "free", "near", "my", "house"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Location", "I-Location", "I-Location"]}
{"sentence": "i would like find where all the nearby food trucks are", "tokens": ["i", "would", "like", "find", "where", "all", "the", "nearby", "food", "trucks", "are"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "i would like some dim sum today that is less than 5 miles away", "tokens": ["i", "would", "like", "some", "dim", "sum", "today", "that", "is", "less", "than", "5", "miles", "away"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "I-Dish", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "i would like some food what restaurants arent closed in the area", "tokens": ["i", "would", "like", "some", "food", "what", "restaurants", "arent", "closed", "in", "the", "area"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Hours", "B-Location", "I-Location", "I-Location"]}
{"sentence": "i would like to eat fish today any recommendations", "tokens": ["i", "would", "like", "to", "eat", "fish", "today", "any", "recommendations"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "B-Hours", "O", "O"]}
{"sentence": "i would like to eat pizza at a place with outdoor seating", "tokens": ["i", "would", "like", "to", "eat", "pizza", "at", "a", "place", "with", "outdoor", "seating"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "i would like to eat tofu at 12 pm for a reasonable price", "tokens": ["i", "would", "like", "to", "eat", "tofu", "at", "12", "pm", "for", "a", "reasonable", "price"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "B-Hours", "I-Hours", "I-Hours", "O", "O", "B-Price", "O"]}
{"sentence": "i would like to find a french restaurant that is rated at least 4 stars", "tokens": ["i", "would", "like", "to", "find", "a", "french", "restaurant", "that", "is", "rated", "at", "least", "4", "stars"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Rating", "I-Rating", "I-Rating", "I-Rating", "I-Rating"]}
{"sentence": "i would like to find a mexican restaurant nearby", "tokens": ["i", "would", "like", "to", "find", "a", "mexican", "restaurant", "nearby"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "i would like to find a soul food restaurant", "tokens": ["i", "would", "like", "to", "find", "a", "soul", "food", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O"]}
{"sentence": "i would like to find a vegan restaurant", "tokens": ["i", "would", "like", "to", "find", "a", "vegan", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "i would like to find a vegeterian restaurant", "tokens": ["i", "would", "like", "to", "find", "a", "vegeterian", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "i would like to go to a sushi restaurant", "tokens": ["i", "would", "like", "to", "go", "to", "a", "sushi", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "O"]}
{"sentence": "i would like to know the location of a chinese restaurant", "tokens": ["i", "would", "like", "to", "know", "the", "location", "of", "a", "chinese", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "i would like to know where the best restaurants are", "tokens": ["i", "would", "like", "to", "know", "where", "the", "best", "restaurants", "are"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Rating", "O", "O"]}
{"sentence": "i would like wendys", "tokens": ["i", "would", "like", "wendys"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "i would llike breakfast foods", "tokens": ["i", "would", "llike", "breakfast", "foods"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "ice cream shop with 20 flavors or more near here", "tokens": ["ice", "cream", "shop", "with", "20", "flavors", "or", "more", "near", "here"], "ner_tags": ["B-Cuisine", "I-Cuisine", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "B-Location", "I-Location"]}
{"sentence": "id like a coffee shop that serves pie", "tokens": ["id", "like", "a", "coffee", "shop", "that", "serves", "pie"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Dish"]}
{"sentence": "id like some comfort food farm vegetables", "tokens": ["id", "like", "some", "comfort", "food", "farm", "vegetables"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "B-Dish", "I-Dish"]}
{"sentence": "id like to eat at a good lunch spot for the right price", "tokens": ["id", "like", "to", "eat", "at", "a", "good", "lunch", "spot", "for", "the", "right", "price"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Price", "I-Price", "I-Price", "O"]}
{"sentence": "id like to eat in a reasonably priced restaurant that is not part of chain and that serves american food", "tokens": ["id", "like", "to", "eat", "in", "a", "reasonably", "priced", "restaurant", "that", "is", "not", "part", "of", "chain", "and", "that", "serves", "american", "food"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Price", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "id like to eat some halal at a bar", "tokens": ["id", "like", "to", "eat", "some", "halal", "at", "a", "bar"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Amenity"]}
{"sentence": "id like to find a breakfast place", "tokens": ["id", "like", "to", "find", "a", "breakfast", "place"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "id like to find a cheap pub with internet access", "tokens": ["id", "like", "to", "find", "a", "cheap", "pub", "with", "internet", "access"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "B-Cuisine", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "id like to find a chinese restaurant nearby", "tokens": ["id", "like", "to", "find", "a", "chinese", "restaurant", "nearby"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "id like to find a diner that has grilled cheese and soup close to here could you suggest one", "tokens": ["id", "like", "to", "find", "a", "diner", "that", "has", "grilled", "cheese", "and", "soup", "close", "to", "here", "could", "you", "suggest", "one"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Dish", "I-Dish", "I-Dish", "I-Dish", "B-Location", "I-Location", "I-Location", "O", "O", "O", "O"]}
{"sentence": "id like to go somewhere off the beaten path to get some middle eastern food", "tokens": ["id", "like", "to", "go", "somewhere", "off", "the", "beaten", "path", "to", "get", "some", "middle", "eastern", "food"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "id like to know the closest starbucks open past 9 pm", "tokens": ["id", "like", "to", "know", "the", "closest", "starbucks", "open", "past", "9", "pm"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "B-Restaurant_Name", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "id really like a thai restaurant that has carryout do you know of one", "tokens": ["id", "really", "like", "a", "thai", "restaurant", "that", "has", "carryout", "do", "you", "know", "of", "one"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "O", "O", "O", "O", "O"]}
{"sentence": "id there a mina bakery in chinatown", "tokens": ["id", "there", "a", "mina", "bakery", "in", "chinatown"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location"]}
{"sentence": "if want only organic vegetables and fruits in my dishes which restaurant is best", "tokens": ["if", "want", "only", "organic", "vegetables", "and", "fruits", "in", "my", "dishes", "which", "restaurant", "is", "best"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "I-Cuisine", "I-Cuisine", "O", "O", "O", "O", "O", "O", "B-Rating"]}
{"sentence": "im craving twice baked potatoes where do they serve them in a ten mile radius", "tokens": ["im", "craving", "twice", "baked", "potatoes", "where", "do", "they", "serve", "them", "in", "a", "ten", "mile", "radius"], "ner_tags": ["O", "O", "B-Dish", "I-Dish", "I-Dish", "O", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "im feeling a little down so id like go somewhere thats really bright and fun for breakfast", "tokens": ["im", "feeling", "a", "little", "down", "so", "id", "like", "go", "somewhere", "thats", "really", "bright", "and", "fun", "for", "breakfast"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "O", "B-Hours"]}
{"sentence": "im hungry and i feel like eating chinese", "tokens": ["im", "hungry", "and", "i", "feel", "like", "eating", "chinese"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Cuisine"]}
{"sentence": "im hungry and want a tasty burger", "tokens": ["im", "hungry", "and", "want", "a", "tasty", "burger"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Dish"]}
{"sentence": "im hungry find me a restaurant with large portions", "tokens": ["im", "hungry", "find", "me", "a", "restaurant", "with", "large", "portions"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "im hungry for a steak any good restaurants around", "tokens": ["im", "hungry", "for", "a", "steak", "any", "good", "restaurants", "around"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "O", "B-Rating", "O", "B-Location"]}
{"sentence": "im hungry for thai", "tokens": ["im", "hungry", "for", "thai"], "ner_tags": ["O", "O", "O", "B-Cuisine"]}
{"sentence": "im hungry lets get some tacos", "tokens": ["im", "hungry", "lets", "get", "some", "tacos"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "im in a hurry wheres a place i can get a quick meal", "tokens": ["im", "in", "a", "hurry", "wheres", "a", "place", "i", "can", "get", "a", "quick", "meal"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "im in the mood for chinese food", "tokens": ["im", "in", "the", "mood", "for", "chinese", "food"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "im in the mood for chinese which restaurants nearby have the best ratings", "tokens": ["im", "in", "the", "mood", "for", "chinese", "which", "restaurants", "nearby", "have", "the", "best", "ratings"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Location", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "im in the mood for mexican food", "tokens": ["im", "in", "the", "mood", "for", "mexican", "food"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "im in the mood for some texas chili", "tokens": ["im", "in", "the", "mood", "for", "some", "texas", "chili"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "im in the mood for something light", "tokens": ["im", "in", "the", "mood", "for", "something", "light"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine"]}
{"sentence": "im in the mood to eat something ive never tried before can you find me something like that", "tokens": ["im", "in", "the", "mood", "to", "eat", "something", "ive", "never", "tried", "before", "can", "you", "find", "me", "something", "like", "that"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "I-Cuisine", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "im looking for a 5 star restaurant whats the closest one", "tokens": ["im", "looking", "for", "a", "5", "star", "restaurant", "whats", "the", "closest", "one"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "I-Rating", "O", "O", "O", "B-Location", "O"]}
{"sentence": "im looking for a chinese restureant thats moderate", "tokens": ["im", "looking", "for", "a", "chinese", "restureant", "thats", "moderate"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Price"]}
{"sentence": "im looking for a diner along my route", "tokens": ["im", "looking", "for", "a", "diner", "along", "my", "route"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "B-Location", "I-Location", "I-Location"]}
{"sentence": "im looking for a family style restaurant so i can eat at the bar anything within two miles", "tokens": ["im", "looking", "for", "a", "family", "style", "restaurant", "so", "i", "can", "eat", "at", "the", "bar", "anything", "within", "two", "miles"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "im looking for a nice place to eat for me and my girlfriends one year anniversary where i can schedule romantic candles and flowers for her", "tokens": ["im", "looking", "for", "a", "nice", "place", "to", "eat", "for", "me", "and", "my", "girlfriends", "one", "year", "anniversary", "where", "i", "can", "schedule", "romantic", "candles", "and", "flowers", "for", "her"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Amenity", "O", "O"]}
{"sentence": "im looking for a pizza restaurant that has buffalo chicken where i can eat in i only have a credit card", "tokens": ["im", "looking", "for", "a", "pizza", "restaurant", "that", "has", "buffalo", "chicken", "where", "i", "can", "eat", "in", "i", "only", "have", "a", "credit", "card"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Dish", "I-Dish", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "im looking for a place that serves apple pie", "tokens": ["im", "looking", "for", "a", "place", "that", "serves", "apple", "pie"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "im looking for a romantic restaurant where are some near me", "tokens": ["im", "looking", "for", "a", "romantic", "restaurant", "where", "are", "some", "near", "me"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "O", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "im looking for a top of the line steak place with valet parking", "tokens": ["im", "looking", "for", "a", "top", "of", "the", "line", "steak", "place", "with", "valet", "parking"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "I-Rating", "I-Rating", "I-Rating", "B-Cuisine", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "im looking for an all you can eat mexican buffet", "tokens": ["im", "looking", "for", "an", "all", "you", "can", "eat", "mexican", "buffet"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "B-Cuisine", "I-Cuisine"]}
{"sentence": "im looking for an asian buffet", "tokens": ["im", "looking", "for", "an", "asian", "buffet"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "B-Amenity"]}
{"sentence": "im looking for cheap spanish cuisine", "tokens": ["im", "looking", "for", "cheap", "spanish", "cuisine"], "ner_tags": ["O", "O", "O", "B-Price", "B-Cuisine", "O"]}
{"sentence": "im looking for some nearby brazilian food for a special occasion", "tokens": ["im", "looking", "for", "some", "nearby", "brazilian", "food", "for", "a", "special", "occasion"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "im looking for somewhere i can get a lot of food for not too much money", "tokens": ["im", "looking", "for", "somewhere", "i", "can", "get", "a", "lot", "of", "food", "for", "not", "too", "much", "money"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "O", "B-Price", "I-Price", "I-Price", "I-Price"]}
{"sentence": "im looking for somewhere to get affordable sushi", "tokens": ["im", "looking", "for", "somewhere", "to", "get", "affordable", "sushi"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Price", "B-Dish"]}
{"sentence": "im looking for somewhere with a good wine list within a mile of here that is open before ten in the morning", "tokens": ["im", "looking", "for", "somewhere", "with", "a", "good", "wine", "list", "within", "a", "mile", "of", "here", "that", "is", "open", "before", "ten", "in", "the", "morning"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Rating", "B-Amenity", "I-Amenity", "B-Location", "I-Location", "I-Location", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "im low on cash where is the nearest atm", "tokens": ["im", "low", "on", "cash", "where", "is", "the", "nearest", "atm"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "O"]}
{"sentence": "im on a budget but i want to eat can you suggest something", "tokens": ["im", "on", "a", "budget", "but", "i", "want", "to", "eat", "can", "you", "suggest", "something"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "im on a really tight budget but im hungry help me out", "tokens": ["im", "on", "a", "really", "tight", "budget", "but", "im", "hungry", "help", "me", "out"], "ner_tags": ["O", "O", "O", "O", "B-Price", "I-Price", "O", "O", "O", "O", "O", "O"]}
{"sentence": "im really hungry take me to the closest restaurant", "tokens": ["im", "really", "hungry", "take", "me", "to", "the", "closest", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "O"]}
{"sentence": "im starving help me find a fast food restaurant", "tokens": ["im", "starving", "help", "me", "find", "a", "fast", "food", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "im starving is there a restaurant that sells shawarma here", "tokens": ["im", "starving", "is", "there", "a", "restaurant", "that", "sells", "shawarma", "here"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Dish", "B-Location"]}
{"sentence": "im starving so fast food will do", "tokens": ["im", "starving", "so", "fast", "food", "will", "do"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "O"]}
{"sentence": "im starving tell me where the closest mcdonalds is", "tokens": ["im", "starving", "tell", "me", "where", "the", "closest", "mcdonalds", "is"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "B-Restaurant_Name", "O"]}
{"sentence": "im thinking spanish tapas is there one in port", "tokens": ["im", "thinking", "spanish", "tapas", "is", "there", "one", "in", "port"], "ner_tags": ["O", "O", "B-Dish", "I-Dish", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "im trying to find a family friendly restaurant with a gift shop within 10 miles of sunswept hotel in orange beach", "tokens": ["im", "trying", "to", "find", "a", "family", "friendly", "restaurant", "with", "a", "gift", "shop", "within", "10", "miles", "of", "sunswept", "hotel", "in", "orange", "beach"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "in which restaurants can one smoke", "tokens": ["in", "which", "restaurants", "can", "one", "smoke"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity"]}
{"sentence": "indian restraunt", "tokens": ["indian", "restraunt"], "ner_tags": ["B-Cuisine", "O"]}
{"sentence": "is a dairy queen nearby", "tokens": ["is", "a", "dairy", "queen", "nearby"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location"]}
{"sentence": "is albertos deli open until 11 pm", "tokens": ["is", "albertos", "deli", "open", "until", "11", "pm"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "is azita restaurant a date spot", "tokens": ["is", "azita", "restaurant", "a", "date", "spot"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is bambinos restaurant close by", "tokens": ["is", "bambinos", "restaurant", "close", "by"], "ner_tags": ["O", "B-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "is chepes restaurant on the way", "tokens": ["is", "chepes", "restaurant", "on", "the", "way"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Location"]}
{"sentence": "is chickfila open today", "tokens": ["is", "chickfila", "open", "today"], "ner_tags": ["O", "B-Restaurant_Name", "B-Hours", "I-Hours"]}
{"sentence": "is dave and busters a good lunch spot", "tokens": ["is", "dave", "and", "busters", "a", "good", "lunch", "spot"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Rating", "B-Hours", "O"]}
{"sentence": "is deluna pizza open after midnight", "tokens": ["is", "deluna", "pizza", "open", "after", "midnight"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Hours", "I-Hours"]}
{"sentence": "is fitzys pub in san jose family friendly", "tokens": ["is", "fitzys", "pub", "in", "san", "jose", "family", "friendly"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location", "B-Amenity", "I-Amenity"]}
{"sentence": "is golden house restaurant kids friendly", "tokens": ["is", "golden", "house", "restaurant", "kids", "friendly"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Amenity", "I-Amenity"]}
{"sentence": "is izzys ice cream shop open right now", "tokens": ["is", "izzys", "ice", "cream", "shop", "open", "right", "now"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "is jade garden within a mile of my current location", "tokens": ["is", "jade", "garden", "within", "a", "mile", "of", "my", "current", "location"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location", "O", "O", "O", "O"]}
{"sentence": "is outback having any specials today", "tokens": ["is", "outback", "having", "any", "specials", "today"], "ner_tags": ["O", "B-Restaurant_Name", "O", "O", "O", "O"]}
{"sentence": "is panera bread open for breakfast", "tokens": ["is", "panera", "bread", "open", "for", "breakfast"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Hours"]}
{"sentence": "is papa johns on cradle way still open", "tokens": ["is", "papa", "johns", "on", "cradle", "way", "still", "open"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "B-Hours", "I-Hours"]}
{"sentence": "is pasquales still located on dayton street", "tokens": ["is", "pasquales", "still", "located", "on", "dayton", "street"], "ner_tags": ["O", "B-Restaurant_Name", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "is quiznos open for breakfast", "tokens": ["is", "quiznos", "open", "for", "breakfast"], "ner_tags": ["O", "B-Restaurant_Name", "O", "O", "B-Hours"]}
{"sentence": "is ruby tuesdays in chattanooga tn romantic", "tokens": ["is", "ruby", "tuesdays", "in", "chattanooga", "tn", "romantic"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "B-Restaurant_Name", "O"]}
{"sentence": "is santa ramen in san mateo busy on thursday nights", "tokens": ["is", "santa", "ramen", "in", "san", "mateo", "busy", "on", "thursday", "nights"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "O", "O", "B-Hours", "I-Hours"]}
{"sentence": "is sidney and hampton an expensive hotel restaurant", "tokens": ["is", "sidney", "and", "hampton", "an", "expensive", "hotel", "restaurant"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Price", "O", "O"]}
{"sentence": "is the a chau restaurant within a mile from here a local favorite", "tokens": ["is", "the", "a", "chau", "restaurant", "within", "a", "mile", "from", "here", "a", "local", "favorite"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "O", "O"]}
{"sentence": "is the amaral manuel near here open after 12 pm", "tokens": ["is", "the", "amaral", "manuel", "near", "here", "open", "after", "12", "pm"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "is the chateau restaurant affordable", "tokens": ["is", "the", "chateau", "restaurant", "affordable"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Price"]}
{"sentence": "is the hoseas restaurant nearby and does it offer live music", "tokens": ["is", "the", "hoseas", "restaurant", "nearby", "and", "does", "it", "offer", "live", "music"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is the mcdonalds near my house open after midnight", "tokens": ["is", "the", "mcdonalds", "near", "my", "house", "open", "after", "midnight"], "ner_tags": ["O", "O", "B-Restaurant_Name", "B-Location", "I-Location", "I-Location", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "is the pasteur restaurant a good place for lunch are they expensive", "tokens": ["is", "the", "pasteur", "restaurant", "a", "good", "place", "for", "lunch", "are", "they", "expensive"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Rating", "O", "O", "B-Hours", "O", "O", "B-Price"]}
{"sentence": "is the patio at the butcher and the boar dog friendly", "tokens": ["is", "the", "patio", "at", "the", "butcher", "and", "the", "boar", "dog", "friendly"], "ner_tags": ["O", "O", "B-Amenity", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Amenity", "I-Amenity"]}
{"sentence": "is the pizza place closer than the chinese restaurant", "tokens": ["is", "the", "pizza", "place", "closer", "than", "the", "chinese", "restaurant"], "ner_tags": ["O", "O", "B-Cuisine", "O", "B-Location", "O", "O", "B-Cuisine", "O"]}
{"sentence": "is the pricing fair", "tokens": ["is", "the", "pricing", "fair"], "ner_tags": ["O", "O", "B-Price", "I-Price"]}
{"sentence": "is the restaurant pushcart open until 11 am", "tokens": ["is", "the", "restaurant", "pushcart", "open", "until", "11", "am"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "B-Hours", "O", "B-Hours", "I-Hours"]}
{"sentence": "is the rubios fresh mex grill within 10 miles from my house a hidden find", "tokens": ["is", "the", "rubios", "fresh", "mex", "grill", "within", "10", "miles", "from", "my", "house", "a", "hidden", "find"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location", "O", "B-Location", "I-Location", "O", "B-Cuisine", "O"]}
{"sentence": "is the seven seas diner expensive", "tokens": ["is", "the", "seven", "seas", "diner", "expensive"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Price"]}
{"sentence": "is the silverbull steakhouse open seven days a week", "tokens": ["is", "the", "silverbull", "steakhouse", "open", "seven", "days", "a", "week"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O", "O", "O"]}
{"sentence": "is the tea garden restaurant in medford good for a date night", "tokens": ["is", "the", "tea", "garden", "restaurant", "in", "medford", "good", "for", "a", "date", "night"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is the twirl pasta in north end a good place for a business lunch", "tokens": ["is", "the", "twirl", "pasta", "in", "north", "end", "a", "good", "place", "for", "a", "business", "lunch"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "O", "O", "O", "O", "O", "B-Amenity", "B-Hours"]}
{"sentence": "is the tylers restaurant in baltimore a little pricey", "tokens": ["is", "the", "tylers", "restaurant", "in", "baltimore", "a", "little", "pricey"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "O", "B-Price", "O"]}
{"sentence": "is there a an indian restaurant in this town", "tokens": ["is", "there", "a", "an", "indian", "restaurant", "in", "this", "town"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "is there a bakery near here", "tokens": ["is", "there", "a", "bakery", "near", "here"], "ner_tags": ["O", "O", "O", "B-Cuisine", "B-Location", "I-Location"]}
{"sentence": "is there a bar that stays open after 2 am that is within a 5 minute distance", "tokens": ["is", "there", "a", "bar", "that", "stays", "open", "after", "2", "am", "that", "is", "within", "a", "5", "minute", "distance"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "is there a benihana in this city", "tokens": ["is", "there", "a", "benihana", "in", "this", "city"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "is there a bojangles in laurel", "tokens": ["is", "there", "a", "bojangles", "in", "laurel"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "B-Location"]}
{"sentence": "is there a breakfast place that has valet parking", "tokens": ["is", "there", "a", "breakfast", "place", "that", "has", "valet", "parking"], "ner_tags": ["O", "O", "O", "B-Hours", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is there a business dining restaurant where i can get cakes until 1 am", "tokens": ["is", "there", "a", "business", "dining", "restaurant", "where", "i", "can", "get", "cakes", "until", "1", "am"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "O", "O", "O", "O", "O", "B-Dish", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "is there a cafe that serves frog legs in fort worth", "tokens": ["is", "there", "a", "cafe", "that", "serves", "frog", "legs", "in", "fort", "worth"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Dish", "I-Dish", "O", "B-Location", "I-Location"]}
{"sentence": "is there a cheap fast food restaurant nearby", "tokens": ["is", "there", "a", "cheap", "fast", "food", "restaurant", "nearby"], "ner_tags": ["O", "O", "O", "B-Price", "B-Cuisine", "I-Cuisine", "O", "B-Location"]}
{"sentence": "is there a cheap vegetarian restaurant nearby", "tokens": ["is", "there", "a", "cheap", "vegetarian", "restaurant", "nearby"], "ner_tags": ["O", "O", "O", "B-Price", "B-Cuisine", "O", "B-Location"]}
{"sentence": "is there a chick fil a within 5 miles of here", "tokens": ["is", "there", "a", "chick", "fil", "a", "within", "5", "miles", "of", "here"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location", "O", "O"]}
{"sentence": "is there a chinese buffet that serves family style", "tokens": ["is", "there", "a", "chinese", "buffet", "that", "serves", "family", "style"], "ner_tags": ["O", "O", "O", "B-Cuisine", "B-Amenity", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is there a chinese restaurant in the midvale mall", "tokens": ["is", "there", "a", "chinese", "restaurant", "in", "the", "midvale", "mall"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "is there a chipotle located with in a mile of where i am now", "tokens": ["is", "there", "a", "chipotle", "located", "with", "in", "a", "mile", "of", "where", "i", "am", "now"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location", "I-Location", "O", "O", "O", "O", "O"]}
{"sentence": "is there a ciao bella on holyoke st", "tokens": ["is", "there", "a", "ciao", "bella", "on", "holyoke", "st"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "is there a club diner in watertown with a bar", "tokens": ["is", "there", "a", "club", "diner", "in", "watertown", "with", "a", "bar"], "ner_tags": ["O", "O", "O", "B-Amenity", "B-Cuisine", "O", "B-Location", "O", "O", "B-Amenity"]}
{"sentence": "is there a deli nearby that takes credit cards and is open right now", "tokens": ["is", "there", "a", "deli", "nearby", "that", "takes", "credit", "cards", "and", "is", "open", "right", "now"], "ner_tags": ["O", "O", "O", "B-Cuisine", "B-Location", "O", "B-Amenity", "I-Amenity", "I-Amenity", "O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "is there a diner with a patio in boxford", "tokens": ["is", "there", "a", "diner", "with", "a", "patio", "in", "boxford"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Amenity", "O", "B-Location"]}
{"sentence": "is there a donut and donuts restaurant within 5 miles with a beer list", "tokens": ["is", "there", "a", "donut", "and", "donuts", "restaurant", "within", "5", "miles", "with", "a", "beer", "list"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is there a dress code and yuris dine in restaurant", "tokens": ["is", "there", "a", "dress", "code", "and", "yuris", "dine", "in", "restaurant"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "is there a five star restaurant or bar open late", "tokens": ["is", "there", "a", "five", "star", "restaurant", "or", "bar", "open", "late"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "O", "O", "O", "B-Hours", "I-Hours"]}
{"sentence": "is there a good diner in oakland", "tokens": ["is", "there", "a", "good", "diner", "in", "oakland"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "is there a good place to eat close by", "tokens": ["is", "there", "a", "good", "place", "to", "eat", "close", "by"], "ner_tags": ["O", "O", "O", "B-Rating", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "is there a good vegan restaurant for kids", "tokens": ["is", "there", "a", "good", "vegan", "restaurant", "for", "kids"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "O", "O", "B-Amenity"]}
{"sentence": "is there a halal place downtown where i could eat outside", "tokens": ["is", "there", "a", "halal", "place", "downtown", "where", "i", "could", "eat", "outside"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is there a hidden tudors biscut world in this town", "tokens": ["is", "there", "a", "hidden", "tudors", "biscut", "world", "in", "this", "town"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "is there a hooters in this area", "tokens": ["is", "there", "a", "hooters", "in", "this", "area"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "B-Location", "I-Location", "I-Location"]}
{"sentence": "is there a jacks restaurant around here", "tokens": ["is", "there", "a", "jacks", "restaurant", "around", "here"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "is there a japanese restraunt near by", "tokens": ["is", "there", "a", "japanese", "restraunt", "near", "by"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "is there a korean restaurant that is smoker friendly", "tokens": ["is", "there", "a", "korean", "restaurant", "that", "is", "smoker", "friendly"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is there a korean restaurant with nice decor and open late", "tokens": ["is", "there", "a", "korean", "restaurant", "with", "nice", "decor", "and", "open", "late"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Hours", "I-Hours"]}
{"sentence": "is there a late night place in west newbury where i can smoke", "tokens": ["is", "there", "a", "late", "night", "place", "in", "west", "newbury", "where", "i", "can", "smoke"], "ner_tags": ["O", "O", "O", "B-Hours", "I-Hours", "O", "O", "B-Location", "I-Location", "O", "O", "O", "B-Amenity"]}
{"sentence": "is there a location of field corner restaurant near by that has a happy hour", "tokens": ["is", "there", "a", "location", "of", "field", "corner", "restaurant", "near", "by", "that", "has", "a", "happy", "hour"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is there a malaysian restaurant near roxbury crossing", "tokens": ["is", "there", "a", "malaysian", "restaurant", "near", "roxbury", "crossing"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Location", "I-Location"]}
{"sentence": "is there a mall with a food court around here", "tokens": ["is", "there", "a", "mall", "with", "a", "food", "court", "around", "here"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "B-Location", "I-Location"]}
{"sentence": "is there a mcdonalds between here and my destination", "tokens": ["is", "there", "a", "mcdonalds", "between", "here", "and", "my", "destination"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "is there a mcdonalds nearby", "tokens": ["is", "there", "a", "mcdonalds", "nearby"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "B-Location"]}
{"sentence": "is there a mcdonalds within two miles of here", "tokens": ["is", "there", "a", "mcdonalds", "within", "two", "miles", "of", "here"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "B-Location", "I-Location", "I-Location", "O", "O"]}
{"sentence": "is there a mexican restaurant near here with large portion sizes", "tokens": ["is", "there", "a", "mexican", "restaurant", "near", "here", "with", "large", "portion", "sizes"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "is there a moderately priced french restaurant in this area", "tokens": ["is", "there", "a", "moderately", "priced", "french", "restaurant", "in", "this", "area"], "ner_tags": ["O", "O", "O", "B-Price", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "is there a nice place in the theater district that wont drain my wallet", "tokens": ["is", "there", "a", "nice", "place", "in", "the", "theater", "district", "that", "wont", "drain", "my", "wallet"], "ner_tags": ["O", "O", "O", "B-Rating", "O", "O", "O", "B-Location", "I-Location", "O", "B-Price", "I-Price", "I-Price", "I-Price"]}
{"sentence": "is there a nicely decorated restaurant in sacramento with a fireplace", "tokens": ["is", "there", "a", "nicely", "decorated", "restaurant", "in", "sacramento", "with", "a", "fireplace"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Location", "O", "O", "B-Amenity"]}
{"sentence": "is there a olive garden nearby", "tokens": ["is", "there", "a", "olive", "garden", "nearby"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location"]}
{"sentence": "is there a pancake house nearby", "tokens": ["is", "there", "a", "pancake", "house", "nearby"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "B-Location"]}
{"sentence": "is there a patio at w a frost", "tokens": ["is", "there", "a", "patio", "at", "w", "a", "frost"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "is there a place near by that serves tapas", "tokens": ["is", "there", "a", "place", "near", "by", "that", "serves", "tapas"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Dish"]}
{"sentence": "is there a place that serves breakfast this late", "tokens": ["is", "there", "a", "place", "that", "serves", "breakfast", "this", "late"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Hours"]}
{"sentence": "is there a place that will let me bring my own alcohol", "tokens": ["is", "there", "a", "place", "that", "will", "let", "me", "bring", "my", "own", "alcohol"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "O", "O", "B-Amenity"]}
{"sentence": "is there a place to eat in the abington theatre district open after midnight", "tokens": ["is", "there", "a", "place", "to", "eat", "in", "the", "abington", "theatre", "district", "open", "after", "midnight"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "B-Hours", "I-Hours"]}
{"sentence": "is there a place to eat that has a friendly and cheerful atmosphere and offers steak on the menu", "tokens": ["is", "there", "a", "place", "to", "eat", "that", "has", "a", "friendly", "and", "cheerful", "atmosphere", "and", "offers", "steak", "on", "the", "menu"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "O", "O", "B-Dish", "O", "O", "O"]}
{"sentence": "is there a place to eat that is still open at 10 pm close by", "tokens": ["is", "there", "a", "place", "to", "eat", "that", "is", "still", "open", "at", "10", "pm", "close", "by"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "B-Location", "I-Location"]}
{"sentence": "is there a place to get high tea nearby", "tokens": ["is", "there", "a", "place", "to", "get", "high", "tea", "nearby"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "B-Cuisine", "I-Cuisine", "B-Location"]}
{"sentence": "is there a place with walking distance of the cinema that serves pizza by the slice", "tokens": ["is", "there", "a", "place", "with", "walking", "distance", "of", "the", "cinema", "that", "serves", "pizza", "by", "the", "slice"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "O", "O", "B-Dish", "I-Dish", "I-Dish", "I-Dish"]}
{"sentence": "is there a place within 10 minutes that has great atmosphere for a special meal", "tokens": ["is", "there", "a", "place", "within", "10", "minutes", "that", "has", "great", "atmosphere", "for", "a", "special", "meal"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O", "O"]}
{"sentence": "is there a ponderosa near here", "tokens": ["is", "there", "a", "ponderosa", "near", "here"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "B-Location", "I-Location"]}
{"sentence": "is there a pool side night club nearby", "tokens": ["is", "there", "a", "pool", "side", "night", "club", "nearby"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "B-Location"]}
{"sentence": "is there a portuguese restaurant nearby", "tokens": ["is", "there", "a", "portuguese", "restaurant", "nearby"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "is there a rancho veo restaurant in north memphis", "tokens": ["is", "there", "a", "rancho", "veo", "restaurant", "in", "north", "memphis"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Location", "I-Location"]}
{"sentence": "is there a real cheap place to eat in kendall square at 8 pm", "tokens": ["is", "there", "a", "real", "cheap", "place", "to", "eat", "in", "kendall", "square", "at", "8", "pm"], "ner_tags": ["O", "O", "O", "B-Price", "I-Price", "O", "O", "O", "O", "B-Location", "I-Location", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "is there a reasonably priced restaurant that has perfect portion sizes", "tokens": ["is", "there", "a", "reasonably", "priced", "restaurant", "that", "has", "perfect", "portion", "sizes"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "is there a red lobster in the area", "tokens": ["is", "there", "a", "red", "lobster", "in", "the", "area"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Location"]}
{"sentence": "is there a restaurant around here that serves chicken wings", "tokens": ["is", "there", "a", "restaurant", "around", "here", "that", "serves", "chicken", "wings"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "is there a restaurant around where i could watch a football game it needs to be kid friendly", "tokens": ["is", "there", "a", "restaurant", "around", "where", "i", "could", "watch", "a", "football", "game", "it", "needs", "to", "be", "kid", "friendly"], "ner_tags": ["O", "O", "O", "O", "B-Location", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is there a restaurant close by that has dancing and serves scallops", "tokens": ["is", "there", "a", "restaurant", "close", "by", "that", "has", "dancing", "and", "serves", "scallops"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Amenity", "O", "O", "B-Dish"]}
{"sentence": "is there a restaurant close by with valet parking", "tokens": ["is", "there", "a", "restaurant", "close", "by", "with", "valet", "parking"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is there a restaurant in inman square that is after 9 pm 7 days a week", "tokens": ["is", "there", "a", "restaurant", "in", "inman", "square", "that", "is", "after", "9", "pm", "7", "days", "a", "week"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "is there a restaurant in the south end that has a fireplace and serves good portions", "tokens": ["is", "there", "a", "restaurant", "in", "the", "south", "end", "that", "has", "a", "fireplace", "and", "serves", "good", "portions"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "O", "B-Amenity", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "is there a restaurant on the way", "tokens": ["is", "there", "a", "restaurant", "on", "the", "way"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location"]}
{"sentence": "is there a restaurant on waverly street that is priced competitively", "tokens": ["is", "there", "a", "restaurant", "on", "waverly", "street", "that", "is", "priced", "competitively"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Price", "I-Price"]}
{"sentence": "is there a restaurant that has a 5 star rating for it that people like", "tokens": ["is", "there", "a", "restaurant", "that", "has", "a", "5", "star", "rating", "for", "it", "that", "people", "like"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Rating", "I-Rating", "I-Rating", "O", "O", "O", "O", "O"]}
{"sentence": "is there a restaurant that serves tacos before noon in charlestown", "tokens": ["is", "there", "a", "restaurant", "that", "serves", "tacos", "before", "noon", "in", "charlestown"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "B-Hours", "I-Hours", "B-Location", "I-Location"]}
{"sentence": "is there a restaurant with a bar scene nearby that serves small portioned meals and snacks", "tokens": ["is", "there", "a", "restaurant", "with", "a", "bar", "scene", "nearby", "that", "serves", "small", "portioned", "meals", "and", "snacks"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Location", "O", "O", "B-Cuisine", "I-Cuisine", "I-Cuisine", "I-Cuisine", "I-Cuisine"]}
{"sentence": "is there a sbarro in the galleria mall", "tokens": ["is", "there", "a", "sbarro", "in", "the", "galleria", "mall"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "O", "B-Location", "I-Location"]}
{"sentence": "is there a sclafani italian bakery nearby with a view", "tokens": ["is", "there", "a", "sclafani", "italian", "bakery", "nearby", "with", "a", "view"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Location", "O", "O", "B-Amenity"]}
{"sentence": "is there a seafood restaurant around here", "tokens": ["is", "there", "a", "seafood", "restaurant", "around", "here"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O"]}
{"sentence": "is there a small place in franklin that has beans", "tokens": ["is", "there", "a", "small", "place", "in", "franklin", "that", "has", "beans"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "B-Location", "I-Location", "O", "O", "B-Dish"]}
{"sentence": "is there a smoking section at olive garden", "tokens": ["is", "there", "a", "smoking", "section", "at", "olive", "garden"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "is there a sports bar within a mile of the minneapolis convention center", "tokens": ["is", "there", "a", "sports", "bar", "within", "a", "mile", "of", "the", "minneapolis", "convention", "center"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "is there a starbucks on the west side of town", "tokens": ["is", "there", "a", "starbucks", "on", "the", "west", "side", "of", "town"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "is there a sudbury pizza place thats within 10 miles", "tokens": ["is", "there", "a", "sudbury", "pizza", "place", "thats", "within", "10", "miles"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "is there a swensens restaurant in the north end", "tokens": ["is", "there", "a", "swensens", "restaurant", "in", "the", "north", "end"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "is there a taco joint near the college", "tokens": ["is", "there", "a", "taco", "joint", "near", "the", "college"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "B-Location", "I-Location", "I-Location"]}
{"sentence": "is there a taqueria el rancho grandes around here for taking a date", "tokens": ["is", "there", "a", "taqueria", "el", "rancho", "grandes", "around", "here", "for", "taking", "a", "date"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "is there a thai restaurant with a great wine list", "tokens": ["is", "there", "a", "thai", "restaurant", "with", "a", "great", "wine", "list"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Rating", "B-Amenity", "I-Amenity"]}
{"sentence": "is there a vegan restaurant within 5 miles", "tokens": ["is", "there", "a", "vegan", "restaurant", "within", "5", "miles"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "is there a very high end pastry place close to me", "tokens": ["is", "there", "a", "very", "high", "end", "pastry", "place", "close", "to", "me"], "ner_tags": ["O", "O", "O", "B-Price", "I-Price", "I-Price", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "is there a wah sang restaurant with a great beer list", "tokens": ["is", "there", "a", "wah", "sang", "restaurant", "with", "a", "great", "beer", "list"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is there a wangs fast food near providence highway", "tokens": ["is", "there", "a", "wangs", "fast", "food", "near", "providence", "highway"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "is there a white castle on berkeley avenue", "tokens": ["is", "there", "a", "white", "castle", "on", "berkeley", "avenue"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "is there an all asia cafe nearby", "tokens": ["is", "there", "an", "all", "asia", "cafe", "nearby"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Location"]}
{"sentence": "is there an apple bees near by or similar bar and grill", "tokens": ["is", "there", "an", "apple", "bees", "near", "by", "or", "similar", "bar", "and", "grill"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "O", "O", "O", "O", "O"]}
{"sentence": "is there an applebees in hicksville ny with a special 10 menu", "tokens": ["is", "there", "an", "applebees", "in", "hicksville", "ny", "with", "a", "special", "10", "menu"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "is there an english style restaurant open until midnight in north weymouth", "tokens": ["is", "there", "an", "english", "style", "restaurant", "open", "until", "midnight", "in", "north", "weymouth"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Hours", "I-Hours", "I-Hours", "B-Location", "I-Location", "I-Location"]}
{"sentence": "is there an environmentally friendly restaurant nearby", "tokens": ["is", "there", "an", "environmentally", "friendly", "restaurant", "nearby"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Location"]}
{"sentence": "is there an expensive hotel named sidney and hampton with dining services", "tokens": ["is", "there", "an", "expensive", "hotel", "named", "sidney", "and", "hampton", "with", "dining", "services"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is there an expensive restaurant near here", "tokens": ["is", "there", "an", "expensive", "restaurant", "near", "here"], "ner_tags": ["O", "O", "O", "B-Price", "O", "B-Location", "I-Location"]}
{"sentence": "is there an ihop near by", "tokens": ["is", "there", "an", "ihop", "near", "by"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "B-Location", "I-Location"]}
{"sentence": "is there an indian restaurant within 5 miles that has a dinner buffet", "tokens": ["is", "there", "an", "indian", "restaurant", "within", "5", "miles", "that", "has", "a", "dinner", "buffet"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "B-Hours", "B-Amenity"]}
{"sentence": "is there an italian bistro nearby", "tokens": ["is", "there", "an", "italian", "bistro", "nearby"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "B-Location"]}
{"sentence": "is there an italian place nearby", "tokens": ["is", "there", "an", "italian", "place", "nearby"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "is there an olive garden in kendall square with private dining rooms", "tokens": ["is", "there", "an", "olive", "garden", "in", "kendall", "square", "with", "private", "dining", "rooms"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "is there an open taco bell nearby", "tokens": ["is", "there", "an", "open", "taco", "bell", "nearby"], "ner_tags": ["O", "O", "O", "B-Hours", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location"]}
{"sentence": "is there an restaurant in this part of town that serves thai food", "tokens": ["is", "there", "an", "restaurant", "in", "this", "part", "of", "town", "that", "serves", "thai", "food"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "O", "O", "B-Cuisine", "O"]}
{"sentence": "is there an s and i to go nearby that has small portions", "tokens": ["is", "there", "an", "s", "and", "i", "to", "go", "nearby", "that", "has", "small", "portions"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Location", "O", "O", "O", "O"]}
{"sentence": "is there any 24 hour deli on the west side of town", "tokens": ["is", "there", "any", "24", "hour", "deli", "on", "the", "west", "side", "of", "town"], "ner_tags": ["O", "O", "O", "B-Hours", "I-Hours", "B-Cuisine", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "is there any chinese food nearby", "tokens": ["is", "there", "any", "chinese", "food", "nearby"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "is there any cuban food nearby with authentic dining", "tokens": ["is", "there", "any", "cuban", "food", "nearby", "with", "authentic", "dining"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is there any good pizza in the area", "tokens": ["is", "there", "any", "good", "pizza", "in", "the", "area"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Dish", "B-Location", "I-Location", "I-Location"]}
{"sentence": "is there any place around that has bring your own liquor", "tokens": ["is", "there", "any", "place", "around", "that", "has", "bring", "your", "own", "liquor"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "is there any place in this area that has outdoor seating", "tokens": ["is", "there", "any", "place", "in", "this", "area", "that", "has", "outdoor", "seating"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is there any place nearby that serves seafood", "tokens": ["is", "there", "any", "place", "nearby", "that", "serves", "seafood"], "ner_tags": ["O", "O", "O", "O", "B-Location", "O", "O", "B-Cuisine"]}
{"sentence": "is there any place on the waterfront that does not allow smoking on the outside decks", "tokens": ["is", "there", "any", "place", "on", "the", "waterfront", "that", "does", "not", "allow", "smoking", "on", "the", "outside", "decks"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is there any place open after 9 pm", "tokens": ["is", "there", "any", "place", "open", "after", "9", "pm"], "ner_tags": ["O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "is there any place that i can get a quick meal that isnt fast food", "tokens": ["is", "there", "any", "place", "that", "i", "can", "get", "a", "quick", "meal", "that", "isnt", "fast", "food"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Cuisine", "I-Cuisine"]}
{"sentence": "is there any place where i can take my pet", "tokens": ["is", "there", "any", "place", "where", "i", "can", "take", "my", "pet"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity"]}
{"sentence": "is there any red lobster by the mall", "tokens": ["is", "there", "any", "red", "lobster", "by", "the", "mall"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location"]}
{"sentence": "is there any restaurant close by has live music", "tokens": ["is", "there", "any", "restaurant", "close", "by", "has", "live", "music"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is there any restaurant in the city that serves congee", "tokens": ["is", "there", "any", "restaurant", "in", "the", "city", "that", "serves", "congee"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Dish"]}
{"sentence": "is there any restaurant near kendall square that delivers tea", "tokens": ["is", "there", "any", "restaurant", "near", "kendall", "square", "that", "delivers", "tea"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "B-Amenity", "B-Dish"]}
{"sentence": "is there any restaurant that serves fried chicken late night", "tokens": ["is", "there", "any", "restaurant", "that", "serves", "fried", "chicken", "late", "night"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "I-Dish", "B-Hours", "I-Hours"]}
{"sentence": "is there any sandwich shop by the high school", "tokens": ["is", "there", "any", "sandwich", "shop", "by", "the", "high", "school"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "is there anywhere in kendall square that has tea and delivers", "tokens": ["is", "there", "anywhere", "in", "kendall", "square", "that", "has", "tea", "and", "delivers"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Dish", "O", "B-Amenity"]}
{"sentence": "is there anywhere near here that is open 24 hours and serves breakfast", "tokens": ["is", "there", "anywhere", "near", "here", "that", "is", "open", "24", "hours", "and", "serves", "breakfast"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "O", "O", "O", "B-Hours", "I-Hours", "O", "O", "B-Cuisine"]}
{"sentence": "is there anywhere near me that i can find a great sandwich", "tokens": ["is", "there", "anywhere", "near", "me", "that", "i", "can", "find", "a", "great", "sandwich"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "O", "O", "O", "O", "O", "B-Rating", "B-Dish"]}
{"sentence": "is there anywhere thats open past 1 am with exceptional prices", "tokens": ["is", "there", "anywhere", "thats", "open", "past", "1", "am", "with", "exceptional", "prices"], "ner_tags": ["O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "O", "B-Price", "O"]}
{"sentence": "is there anywhere to eat after 10 p m", "tokens": ["is", "there", "anywhere", "to", "eat", "after", "10", "p", "m"], "ner_tags": ["O", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "is there caribbean food around here", "tokens": ["is", "there", "caribbean", "food", "around", "here"], "ner_tags": ["O", "O", "B-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "is there food near by", "tokens": ["is", "there", "food", "near", "by"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "is there moderately priced food near", "tokens": ["is", "there", "moderately", "priced", "food", "near"], "ner_tags": ["O", "O", "B-Price", "O", "O", "B-Location"]}
{"sentence": "is there more than one fatty fish restaurant on long island", "tokens": ["is", "there", "more", "than", "one", "fatty", "fish", "restaurant", "on", "long", "island"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Location", "I-Location"]}
{"sentence": "is there parking at pierres", "tokens": ["is", "there", "parking", "at", "pierres"], "ner_tags": ["O", "O", "B-Amenity", "O", "B-Restaurant_Name"]}
{"sentence": "is there somewhere to eat that is kid friendly", "tokens": ["is", "there", "somewhere", "to", "eat", "that", "is", "kid", "friendly"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is this restaurant a hidden find", "tokens": ["is", "this", "restaurant", "a", "hidden", "find"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "is this restaurant a local favorite", "tokens": ["is", "this", "restaurant", "a", "local", "favorite"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Rating"]}
{"sentence": "is this restuarant open 7 days a week", "tokens": ["is", "this", "restuarant", "open", "7", "days", "a", "week"], "ner_tags": ["O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "O", "O"]}
{"sentence": "is togos smoke free", "tokens": ["is", "togos", "smoke", "free"], "ner_tags": ["O", "B-Restaurant_Name", "B-Amenity", "I-Amenity"]}
{"sentence": "is ye olde cottage restaurant on the way i need a place to take some clients", "tokens": ["is", "ye", "olde", "cottage", "restaurant", "on", "the", "way", "i", "need", "a", "place", "to", "take", "some", "clients"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Location", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "its 10 pm is anything open", "tokens": ["its", "10", "pm", "is", "anything", "open"], "ner_tags": ["O", "B-Hours", "I-Hours", "O", "O", "B-Hours"]}
{"sentence": "jeez the traffic is horrible today it looks like we need something quick off to donalds", "tokens": ["jeez", "the", "traffic", "is", "horrible", "today", "it", "looks", "like", "we", "need", "something", "quick", "off", "to", "donalds"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "kid friendly restaurant never design center place with uper nice service", "tokens": ["kid", "friendly", "restaurant", "never", "design", "center", "place", "with", "uper", "nice", "service"], "ner_tags": ["B-Amenity", "I-Amenity", "O", "O", "B-Location", "I-Location", "I-Location", "O", "B-Rating", "B-Amenity", "I-Amenity"]}
{"sentence": "king fung garden two with parking", "tokens": ["king", "fung", "garden", "two", "with", "parking"], "ner_tags": ["B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity"]}
{"sentence": "lets find the fanciest french cuisine in the city", "tokens": ["lets", "find", "the", "fanciest", "french", "cuisine", "in", "the", "city"], "ner_tags": ["O", "O", "O", "B-Amenity", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "lets get some fries", "tokens": ["lets", "get", "some", "fries"], "ner_tags": ["O", "O", "O", "B-Dish"]}
{"sentence": "lets go get a taco", "tokens": ["lets", "go", "get", "a", "taco"], "ner_tags": ["O", "O", "O", "O", "B-Dish"]}
{"sentence": "list all restaurants that cater to families within a ten mile location of my current location", "tokens": ["list", "all", "restaurants", "that", "cater", "to", "families", "within", "a", "ten", "mile", "location", "of", "my", "current", "location"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "B-Location", "I-Location", "I-Location", "I-Location", "B-Amenity", "O", "O", "B-Location", "I-Location"]}
{"sentence": "list close restaurants", "tokens": ["list", "close", "restaurants"], "ner_tags": ["O", "B-Location", "O"]}
{"sentence": "list the nearest mcdonalds to 87 th and ashland", "tokens": ["list", "the", "nearest", "mcdonalds", "to", "87", "th", "and", "ashland"], "ner_tags": ["O", "O", "B-Location", "B-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "lobster roll", "tokens": ["lobster", "roll"], "ner_tags": ["B-Dish", "I-Dish"]}
{"sentence": "local fish tacos", "tokens": ["local", "fish", "tacos"], "ner_tags": ["B-Location", "B-Dish", "I-Dish"]}
{"sentence": "local hooters", "tokens": ["local", "hooters"], "ner_tags": ["B-Location", "B-Restaurant_Name"]}
{"sentence": "local mcdonalds please", "tokens": ["local", "mcdonalds", "please"], "ner_tags": ["B-Location", "B-Restaurant_Name", "O"]}
{"sentence": "local restaurant joints with smoking areas", "tokens": ["local", "restaurant", "joints", "with", "smoking", "areas"], "ner_tags": ["B-Location", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "local subway resturant", "tokens": ["local", "subway", "resturant"], "ner_tags": ["B-Location", "B-Restaurant_Name", "O"]}
{"sentence": "locate a five star restaurant that is open after 10 pm", "tokens": ["locate", "a", "five", "star", "restaurant", "that", "is", "open", "after", "10", "pm"], "ner_tags": ["O", "O", "B-Rating", "I-Rating", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "locate a place to eat that serves milk shakes", "tokens": ["locate", "a", "place", "to", "eat", "that", "serves", "milk", "shakes"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "locate a sushi restaurant and give me their phone number", "tokens": ["locate", "a", "sushi", "restaurant", "and", "give", "me", "their", "phone", "number"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "locate all mcdonalds within 5 miles", "tokens": ["locate", "all", "mcdonalds", "within", "5", "miles"], "ner_tags": ["O", "O", "B-Restaurant_Name", "B-Location", "I-Location", "I-Location"]}
{"sentence": "locate an all you can eat buffet", "tokens": ["locate", "an", "all", "you", "can", "eat", "buffet"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "locate bar with alcohol", "tokens": ["locate", "bar", "with", "alcohol"], "ner_tags": ["O", "O", "O", "B-Dish"]}
{"sentence": "locate the nearest fast food restaurant", "tokens": ["locate", "the", "nearest", "fast", "food", "restaurant"], "ner_tags": ["O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "locate the nearest restraunt", "tokens": ["locate", "the", "nearest", "restraunt"], "ner_tags": ["O", "O", "B-Location", "O"]}
{"sentence": "look for inexpensive authentic mexican restaurants in chattanooga tennessee", "tokens": ["look", "for", "inexpensive", "authentic", "mexican", "restaurants", "in", "chattanooga", "tennessee"], "ner_tags": ["O", "O", "B-Price", "B-Cuisine", "I-Cuisine", "O", "O", "B-Location", "I-Location"]}
{"sentence": "look up the reviews for this new asain restaurant", "tokens": ["look", "up", "the", "reviews", "for", "this", "new", "asain", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "looking for a 5 star restaurant that serves a great steack", "tokens": ["looking", "for", "a", "5", "star", "restaurant", "that", "serves", "a", "great", "steack"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "I-Rating", "O", "O", "O", "B-Rating", "B-Dish"]}
{"sentence": "looking for a diner with comfortable atmosphere and a rustic setting", "tokens": ["looking", "for", "a", "diner", "with", "comfortable", "atmosphere", "and", "a", "rustic", "setting"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "looking for a drive through restaurant close by opened 24 7", "tokens": ["looking", "for", "a", "drive", "through", "restaurant", "close", "by", "opened", "24", "7"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "B-Location", "I-Location", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "looking for a good place to get cheese fries", "tokens": ["looking", "for", "a", "good", "place", "to", "get", "cheese", "fries"], "ner_tags": ["O", "O", "O", "B-Rating", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "looking for a high end italian restaurant within a 5 miles", "tokens": ["looking", "for", "a", "high", "end", "italian", "restaurant", "within", "a", "5", "miles"], "ner_tags": ["O", "O", "O", "B-Price", "B-Rating", "B-Cuisine", "O", "B-Location", "O", "B-Location", "I-Location"]}
{"sentence": "looking for a kfc", "tokens": ["looking", "for", "a", "kfc"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "looking for a kid friendly restaurant in mid price range", "tokens": ["looking", "for", "a", "kid", "friendly", "restaurant", "in", "mid", "price", "range"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Price", "O", "O"]}
{"sentence": "looking for a restaurant with the highest approval ratings", "tokens": ["looking", "for", "a", "restaurant", "with", "the", "highest", "approval", "ratings"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Rating", "I-Rating", "I-Rating"]}
{"sentence": "looking for a strong fair priced restaurant that is near", "tokens": ["looking", "for", "a", "strong", "fair", "priced", "restaurant", "that", "is", "near"], "ner_tags": ["O", "O", "O", "O", "B-Price", "O", "O", "O", "O", "B-Location"]}
{"sentence": "looking for a tai restaurant that is nerby that takes online reservations", "tokens": ["looking", "for", "a", "tai", "restaurant", "that", "is", "nerby", "that", "takes", "online", "reservations"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Location", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "looking for a three star restaurant along my current route within the next hundred miles and not more then a mile off route any suggestions", "tokens": ["looking", "for", "a", "three", "star", "restaurant", "along", "my", "current", "route", "within", "the", "next", "hundred", "miles", "and", "not", "more", "then", "a", "mile", "off", "route", "any", "suggestions"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location", "O", "O"]}
{"sentence": "looking for an expensive seafood place make a reservation for 6 people at 6 00", "tokens": ["looking", "for", "an", "expensive", "seafood", "place", "make", "a", "reservation", "for", "6", "people", "at", "6", "00"], "ner_tags": ["O", "O", "O", "B-Price", "B-Cuisine", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "looking for anything open before 7 a m nearby offering hip music", "tokens": ["looking", "for", "anything", "open", "before", "7", "a", "m", "nearby", "offering", "hip", "music"], "ner_tags": ["O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours", "B-Location", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "looking for breakfast restaurants with byob", "tokens": ["looking", "for", "breakfast", "restaurants", "with", "byob"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "B-Amenity"]}
{"sentence": "looking for californian cuisine with great prices", "tokens": ["looking", "for", "californian", "cuisine", "with", "great", "prices"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "B-Price", "O"]}
{"sentence": "looking for crowd pleasing fatz along the way", "tokens": ["looking", "for", "crowd", "pleasing", "fatz", "along", "the", "way"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location"]}
{"sentence": "looking for d k bakery near here where the stars hang out", "tokens": ["looking", "for", "d", "k", "bakery", "near", "here", "where", "the", "stars", "hang", "out"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Location", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "looking for duck dish with great pricing and good rating", "tokens": ["looking", "for", "duck", "dish", "with", "great", "pricing", "and", "good", "rating"], "ner_tags": ["O", "O", "B-Dish", "I-Dish", "O", "B-Price", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "looking for freebirds world burrito that is open late", "tokens": ["looking", "for", "freebirds", "world", "burrito", "that", "is", "open", "late"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Hours", "I-Hours"]}
{"sentence": "looking for hotel dining on central square with great pricing", "tokens": ["looking", "for", "hotel", "dining", "on", "central", "square", "with", "great", "pricing"], "ner_tags": ["O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location", "O", "B-Price", "O"]}
{"sentence": "looking for local pizzahut", "tokens": ["looking", "for", "local", "pizzahut"], "ner_tags": ["O", "O", "B-Location", "B-Restaurant_Name"]}
{"sentence": "looking for olympic house of pizza for lunch any close by", "tokens": ["looking", "for", "olympic", "house", "of", "pizza", "for", "lunch", "any", "close", "by"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Hours", "O", "B-Location", "I-Location"]}
{"sentence": "looking for reasonably priced diners", "tokens": ["looking", "for", "reasonably", "priced", "diners"], "ner_tags": ["O", "O", "B-Price", "O", "B-Cuisine"]}
{"sentence": "looking for sebastians restaurant offering generous portions", "tokens": ["looking", "for", "sebastians", "restaurant", "offering", "generous", "portions"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "looking for star hangout close by called marios italian restaurant", "tokens": ["looking", "for", "star", "hangout", "close", "by", "called", "marios", "italian", "restaurant"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity", "B-Location", "I-Location", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "looking for sushi with prix fixe menu at a reasonable price", "tokens": ["looking", "for", "sushi", "with", "prix", "fixe", "menu", "at", "a", "reasonable", "price"], "ner_tags": ["O", "O", "B-Dish", "O", "B-Amenity", "I-Amenity", "I-Amenity", "O", "O", "B-Price", "O"]}
{"sentence": "looking for tapas cuisine with great prices", "tokens": ["looking", "for", "tapas", "cuisine", "with", "great", "prices"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "B-Price", "O"]}
{"sentence": "looking for the cheapest place to eat im on a budget", "tokens": ["looking", "for", "the", "cheapest", "place", "to", "eat", "im", "on", "a", "budget"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "looking for tom yum cafe around here with excellent prices", "tokens": ["looking", "for", "tom", "yum", "cafe", "around", "here", "with", "excellent", "prices"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "O", "B-Price", "O"]}
{"sentence": "looking for tuna dishes that offer parking", "tokens": ["looking", "for", "tuna", "dishes", "that", "offer", "parking"], "ner_tags": ["O", "O", "B-Dish", "I-Dish", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "looking for urban gourmet on bay road with great wine lists", "tokens": ["looking", "for", "urban", "gourmet", "on", "bay", "road", "with", "great", "wine", "lists"], "ner_tags": ["O", "O", "B-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location", "O", "B-Rating", "O", "O"]}
{"sentence": "looking for very cheap sake restaurant that allows smoking", "tokens": ["looking", "for", "very", "cheap", "sake", "restaurant", "that", "allows", "smoking"], "ner_tags": ["O", "O", "B-Price", "I-Price", "B-Cuisine", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "make a 5 00 p m reservation for black angus", "tokens": ["make", "a", "5", "00", "p", "m", "reservation", "for", "black", "angus"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "make a reservation for 3 people at 8 pm tonight at the melting pot in towson", "tokens": ["make", "a", "reservation", "for", "3", "people", "at", "8", "pm", "tonight", "at", "the", "melting", "pot", "in", "towson"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "O", "B-Hours", "I-Hours", "I-Hours", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location"]}
{"sentence": "make a reservation for six pm at a french restaurant with at least a 4 star rating", "tokens": ["make", "a", "reservation", "for", "six", "pm", "at", "a", "french", "restaurant", "with", "at", "least", "a", "4", "star", "rating"], "ner_tags": ["O", "O", "B-Amenity", "O", "B-Hours", "I-Hours", "O", "O", "B-Cuisine", "O", "O", "O", "O", "O", "B-Rating", "I-Rating", "I-Rating"]}
{"sentence": "make a reservation for us at the french restaurant for tonight at 8 pm also give us the dress code for the restaurant", "tokens": ["make", "a", "reservation", "for", "us", "at", "the", "french", "restaurant", "for", "tonight", "at", "8", "pm", "also", "give", "us", "the", "dress", "code", "for", "the", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O"]}
{"sentence": "make a reservation tonight for four at billies steakhouse", "tokens": ["make", "a", "reservation", "tonight", "for", "four", "at", "billies", "steakhouse"], "ner_tags": ["O", "O", "B-Amenity", "B-Hours", "B-Amenity", "I-Amenity", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "make me a reservation for a restaurant with a nonsmoking patio and that isnt fancy for 30 minutes from now", "tokens": ["make", "me", "a", "reservation", "for", "a", "restaurant", "with", "a", "nonsmoking", "patio", "and", "that", "isnt", "fancy", "for", "30", "minutes", "from", "now"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "make me reservations for three people at devitos italian", "tokens": ["make", "me", "reservations", "for", "three", "people", "at", "devitos", "italian"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "may i have the business hours for the nearest red lobster", "tokens": ["may", "i", "have", "the", "business", "hours", "for", "the", "nearest", "red", "lobster"], "ner_tags": ["O", "O", "O", "O", "B-Hours", "O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "mexican food", "tokens": ["mexican", "food"], "ner_tags": ["B-Cuisine", "O"]}
{"sentence": "mexican food to go", "tokens": ["mexican", "food", "to", "go"], "ner_tags": ["B-Cuisine", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "mexican restaurant nearby", "tokens": ["mexican", "restaurant", "nearby"], "ner_tags": ["B-Cuisine", "O", "B-Location"]}
{"sentence": "moderately priced seafood restaurant", "tokens": ["moderately", "priced", "seafood", "restaurant"], "ner_tags": ["B-Price", "O", "B-Cuisine", "O"]}
{"sentence": "my date and i would like some espresso thats within 10 min", "tokens": ["my", "date", "and", "i", "would", "like", "some", "espresso", "thats", "within", "10", "min"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Dish", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "my kids need lunch can you help", "tokens": ["my", "kids", "need", "lunch", "can", "you", "help"], "ner_tags": ["O", "B-Amenity", "O", "B-Amenity", "O", "O", "O"]}
{"sentence": "my kids want a happy meal", "tokens": ["my", "kids", "want", "a", "happy", "meal"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "my kids want to sit outside and eat cheeseburgers", "tokens": ["my", "kids", "want", "to", "sit", "outside", "and", "eat", "cheeseburgers"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Dish"]}
{"sentence": "my nephew loves burgers where is the nearest five guys", "tokens": ["my", "nephew", "loves", "burgers", "where", "is", "the", "nearest", "five", "guys"], "ner_tags": ["O", "O", "O", "B-Dish", "O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "name the local buffets", "tokens": ["name", "the", "local", "buffets"], "ner_tags": ["O", "O", "B-Location", "I-Location"]}
{"sentence": "navigate me to a thai restaurant thats 4 stars or higher", "tokens": ["navigate", "me", "to", "a", "thai", "restaurant", "thats", "4", "stars", "or", "higher"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Rating", "I-Rating", "I-Rating", "I-Rating"]}
{"sentence": "nearest fast food restaurant", "tokens": ["nearest", "fast", "food", "restaurant"], "ner_tags": ["B-Location", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "need a four star restaurant in a hotel", "tokens": ["need", "a", "four", "star", "restaurant", "in", "a", "hotel"], "ner_tags": ["O", "O", "B-Rating", "I-Rating", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "need a reservation for six pm at the steak shack in detroit for four people and possibly a baby for tonight", "tokens": ["need", "a", "reservation", "for", "six", "pm", "at", "the", "steak", "shack", "in", "detroit", "for", "four", "people", "and", "possibly", "a", "baby", "for", "tonight"], "ner_tags": ["O", "O", "O", "O", "B-Hours", "I-Hours", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "O", "O", "O", "O", "O", "O", "O", "O", "B-Hours"]}
{"sentence": "newbury street bakery and deli directions", "tokens": ["newbury", "street", "bakery", "and", "deli", "directions"], "ner_tags": ["B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "nyc 5 star pizza parlors", "tokens": ["nyc", "5", "star", "pizza", "parlors"], "ner_tags": ["B-Location", "B-Rating", "I-Rating", "B-Dish", "B-Restaurant_Name"]}
{"sentence": "of the restaurants that require suit jackets for men which have the best service and food", "tokens": ["of", "the", "restaurants", "that", "require", "suit", "jackets", "for", "men", "which", "have", "the", "best", "service", "and", "food"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "O", "O", "O", "B-Rating", "I-Rating", "I-Rating", "I-Rating"]}
{"sentence": "please find a high end restaurant that i can take my clients to", "tokens": ["please", "find", "a", "high", "end", "restaurant", "that", "i", "can", "take", "my", "clients", "to"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "O", "O", "O", "O", "O", "O", "B-Amenity", "O"]}
{"sentence": "please find a japanese place that near an indian restaurant", "tokens": ["please", "find", "a", "japanese", "place", "that", "near", "an", "indian", "restaurant"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Location", "O", "B-Cuisine", "O"]}
{"sentence": "please find a mexican restaurant with a smoking section and more then 1 star review", "tokens": ["please", "find", "a", "mexican", "restaurant", "with", "a", "smoking", "section", "and", "more", "then", "1", "star", "review"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Rating", "I-Rating", "I-Rating", "I-Rating", "I-Rating"]}
{"sentence": "please find a restaurant where i can order cocktails with lunch", "tokens": ["please", "find", "a", "restaurant", "where", "i", "can", "order", "cocktails", "with", "lunch"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "O", "B-Amenity"]}
{"sentence": "please find a taco place near my house", "tokens": ["please", "find", "a", "taco", "place", "near", "my", "house"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "please find italian restaurants that are child friendly", "tokens": ["please", "find", "italian", "restaurants", "that", "are", "child", "friendly"], "ner_tags": ["O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "please find me a cheap chinese restaurant nearby", "tokens": ["please", "find", "me", "a", "cheap", "chinese", "restaurant", "nearby"], "ner_tags": ["O", "O", "O", "O", "B-Price", "B-Cuisine", "O", "B-Location"]}
{"sentence": "please find me a child friendly diner with a tv", "tokens": ["please", "find", "me", "a", "child", "friendly", "diner", "with", "a", "tv"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Cuisine", "O", "O", "B-Amenity"]}
{"sentence": "please find me a non smoking restaurant that serves seafood i also want on site parking", "tokens": ["please", "find", "me", "a", "non", "smoking", "restaurant", "that", "serves", "seafood", "i", "also", "want", "on", "site", "parking"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "please find me the nearest thai place", "tokens": ["please", "find", "me", "the", "nearest", "thai", "place"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "please find the address of international house of pancakes", "tokens": ["please", "find", "the", "address", "of", "international", "house", "of", "pancakes"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "please find the closest midpriced sit down restaurant with chicken tenders or macaroni on the kids meal", "tokens": ["please", "find", "the", "closest", "midpriced", "sit", "down", "restaurant", "with", "chicken", "tenders", "or", "macaroni", "on", "the", "kids", "meal"], "ner_tags": ["O", "O", "O", "B-Location", "B-Price", "B-Amenity", "I-Amenity", "O", "O", "B-Dish", "I-Dish", "O", "B-Dish", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "please get me the street address of the dennys in this area", "tokens": ["please", "get", "me", "the", "street", "address", "of", "the", "dennys", "in", "this", "area"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Restaurant_Name", "O", "O", "B-Location"]}
{"sentence": "please give me directions to the nearest made to order place where i can get a cheeseburger", "tokens": ["please", "give", "me", "directions", "to", "the", "nearest", "made", "to", "order", "place", "where", "i", "can", "get", "a", "cheeseburger"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "B-Dish", "I-Dish", "I-Dish", "O", "O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "please give me the hours of operation for the burger king nearest me", "tokens": ["please", "give", "me", "the", "hours", "of", "operation", "for", "the", "burger", "king", "nearest", "me"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location"]}
{"sentence": "please help me locate a restaurant that allows smoking", "tokens": ["please", "help", "me", "locate", "a", "restaurant", "that", "allows", "smoking"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "please list restaurants that serve alcohol within 5 miles", "tokens": ["please", "list", "restaurants", "that", "serve", "alcohol", "within", "5", "miles"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "B-Location", "I-Location", "I-Location"]}
{"sentence": "please locate a bar off of main street down town that has valet parking is open late and has good reviews", "tokens": ["please", "locate", "a", "bar", "off", "of", "main", "street", "down", "town", "that", "has", "valet", "parking", "is", "open", "late", "and", "has", "good", "reviews"], "ner_tags": ["O", "O", "O", "B-Cuisine", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Hours", "I-Hours", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "please locate a mcdonalds", "tokens": ["please", "locate", "a", "mcdonalds"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "please make me a reservation for tonight at jax cafe at 7 pm for six", "tokens": ["please", "make", "me", "a", "reservation", "for", "tonight", "at", "jax", "cafe", "at", "7", "pm", "for", "six"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Hours", "I-Hours", "O", "O"]}
{"sentence": "please name all restaurants that offer curb side pick up on highway 43 south", "tokens": ["please", "name", "all", "restaurants", "that", "offer", "curb", "side", "pick", "up", "on", "highway", "43", "south"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "please navigate to the closest coffee shop with free trade coffee", "tokens": ["please", "navigate", "to", "the", "closest", "coffee", "shop", "with", "free", "trade", "coffee"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Cuisine", "O", "O", "B-Dish", "I-Dish", "I-Dish"]}
{"sentence": "please show me some reviews about the sushi place", "tokens": ["please", "show", "me", "some", "reviews", "about", "the", "sushi", "place"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "B-Location"]}
{"sentence": "please take me to a highly rated family diner", "tokens": ["please", "take", "me", "to", "a", "highly", "rated", "family", "diner"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "I-Rating", "B-Cuisine", "I-Cuisine"]}
{"sentence": "pub", "tokens": ["pub"], "ner_tags": ["B-Cuisine"]}
{"sentence": "quick i need a hot dog", "tokens": ["quick", "i", "need", "a", "hot", "dog"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "reasonably priced byob irish restaurant", "tokens": ["reasonably", "priced", "byob", "irish", "restaurant"], "ner_tags": ["B-Price", "O", "B-Amenity", "B-Cuisine", "O"]}
{"sentence": "red lobster with no smoking", "tokens": ["red", "lobster", "with", "no", "smoking"], "ner_tags": ["B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "red robins restaurant", "tokens": ["red", "robins", "restaurant"], "ner_tags": ["B-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "restaurant that caters to children and that is fun for parents and kids alike", "tokens": ["restaurant", "that", "caters", "to", "children", "and", "that", "is", "fun", "for", "parents", "and", "kids", "alike"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "restaurants in the area", "tokens": ["restaurants", "in", "the", "area"], "ner_tags": ["O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "restaurants that take visa or master card", "tokens": ["restaurants", "that", "take", "visa", "or", "master", "card"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "restuarnt that cathay owns", "tokens": ["restuarnt", "that", "cathay", "owns"], "ner_tags": ["O", "O", "B-Amenity", "O"]}
{"sentence": "search for a buffet that is a smoke free environment within 5 miles of my location", "tokens": ["search", "for", "a", "buffet", "that", "is", "a", "smoke", "free", "environment", "within", "5", "miles", "of", "my", "location"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "search for a place that serves meatloaf", "tokens": ["search", "for", "a", "place", "that", "serves", "meatloaf"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "search for dine in restaurant", "tokens": ["search", "for", "dine", "in", "restaurant"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity", "O"]}
{"sentence": "search for fast food places that also have a kid play area", "tokens": ["search", "for", "fast", "food", "places", "that", "also", "have", "a", "kid", "play", "area"], "ner_tags": ["O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "search for restaurants with seafood", "tokens": ["search", "for", "restaurants", "with", "seafood"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine"]}
{"sentence": "show me a inexpensive restaurant within 10 miles", "tokens": ["show", "me", "a", "inexpensive", "restaurant", "within", "10", "miles"], "ner_tags": ["O", "O", "O", "B-Price", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "show me all of the local restaurants with a smoking area", "tokens": ["show", "me", "all", "of", "the", "local", "restaurants", "with", "a", "smoking", "area"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "show me all the restaurants that are not franchises in my area", "tokens": ["show", "me", "all", "the", "restaurants", "that", "are", "not", "franchises", "in", "my", "area"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Location", "I-Location", "I-Location"]}
{"sentence": "show me restaurants that serve senior portions", "tokens": ["show", "me", "restaurants", "that", "serve", "senior", "portions"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "show me some cheap eats", "tokens": ["show", "me", "some", "cheap", "eats"], "ner_tags": ["O", "O", "O", "B-Price", "O"]}
{"sentence": "show me some mexican restaurants that arent too far", "tokens": ["show", "me", "some", "mexican", "restaurants", "that", "arent", "too", "far"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "show me some restaurants nearby that have 3 star or higher rating", "tokens": ["show", "me", "some", "restaurants", "nearby", "that", "have", "3", "star", "or", "higher", "rating"], "ner_tags": ["O", "O", "O", "O", "B-Location", "O", "O", "B-Rating", "I-Rating", "I-Rating", "I-Rating", "I-Rating"]}
{"sentence": "show me the moderately priced japanese restaurants in a ten mile radius", "tokens": ["show", "me", "the", "moderately", "priced", "japanese", "restaurants", "in", "a", "ten", "mile", "radius"], "ner_tags": ["O", "O", "O", "B-Price", "O", "B-Cuisine", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "show me the three closest wendys", "tokens": ["show", "me", "the", "three", "closest", "wendys"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Restaurant_Name"]}
{"sentence": "show me the three nearest boston market restaurants", "tokens": ["show", "me", "the", "three", "nearest", "boston", "market", "restaurants"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "show me where the nearest sports bar is", "tokens": ["show", "me", "where", "the", "nearest", "sports", "bar", "is"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "smoke friendly restaurants", "tokens": ["smoke", "friendly", "restaurants"], "ner_tags": ["B-Amenity", "I-Amenity", "O"]}
{"sentence": "some jerky would make my day", "tokens": ["some", "jerky", "would", "make", "my", "day"], "ner_tags": ["O", "B-Dish", "O", "O", "O", "O"]}
{"sentence": "someplace where the prices for food arent bad and i can get something to eat before 8 am", "tokens": ["someplace", "where", "the", "prices", "for", "food", "arent", "bad", "and", "i", "can", "get", "something", "to", "eat", "before", "8", "am"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Price", "I-Price", "O", "O", "O", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "strega restaurant and lounge offering local cuisine", "tokens": ["strega", "restaurant", "and", "lounge", "offering", "local", "cuisine"], "ner_tags": ["B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Cuisine", "O"]}
{"sentence": "suppose im looking for the best fish in sd", "tokens": ["suppose", "im", "looking", "for", "the", "best", "fish", "in", "sd"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Cuisine", "O", "B-Location"]}
{"sentence": "tacos stand near my location", "tokens": ["tacos", "stand", "near", "my", "location"], "ner_tags": ["B-Cuisine", "I-Cuisine", "B-Location", "I-Location", "I-Location"]}
{"sentence": "take me for the best dessert today", "tokens": ["take", "me", "for", "the", "best", "dessert", "today"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "B-Cuisine", "B-Hours"]}
{"sentence": "take me to a hole in the wall that has high reviews is open late and serve drinks", "tokens": ["take", "me", "to", "a", "hole", "in", "the", "wall", "that", "has", "high", "reviews", "is", "open", "late", "and", "serve", "drinks"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "O", "O", "B-Rating", "I-Rating", "O", "B-Hours", "I-Hours", "O", "O", "B-Dish"]}
{"sentence": "take me to a local restaurant", "tokens": ["take", "me", "to", "a", "local", "restaurant"], "ner_tags": ["O", "O", "O", "O", "B-Location", "O"]}
{"sentence": "take me to a nice restaurant with generous portions", "tokens": ["take", "me", "to", "a", "nice", "restaurant", "with", "generous", "portions"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "take me to a restaurant i would like", "tokens": ["take", "me", "to", "a", "restaurant", "i", "would", "like"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "take me to the closest restaurant i can smoke in", "tokens": ["take", "me", "to", "the", "closest", "restaurant", "i", "can", "smoke", "in"], "ner_tags": ["O", "O", "O", "O", "B-Location", "O", "O", "O", "B-Amenity", "O"]}
{"sentence": "take me to the nearest tex mex restaurant", "tokens": ["take", "me", "to", "the", "nearest", "tex", "mex", "restaurant"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "tell me the hours of chipotle in midtown", "tokens": ["tell", "me", "the", "hours", "of", "chipotle", "in", "midtown"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "O", "B-Location"]}
{"sentence": "tell me what restaurants are available within a 5 minute drive", "tokens": ["tell", "me", "what", "restaurants", "are", "available", "within", "a", "5", "minute", "drive"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "tell me where i can find a good meal in this town", "tokens": ["tell", "me", "where", "i", "can", "find", "a", "good", "meal", "in", "this", "town"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Rating", "O", "O", "O", "B-Location"]}
{"sentence": "thai", "tokens": ["thai"], "ner_tags": ["B-Cuisine"]}
{"sentence": "the most expensive restaurant in town with a dress code and reservations", "tokens": ["the", "most", "expensive", "restaurant", "in", "town", "with", "a", "dress", "code", "and", "reservations"], "ner_tags": ["O", "B-Price", "I-Price", "O", "B-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Amenity"]}
{"sentence": "the opening times of nearby pizza places", "tokens": ["the", "opening", "times", "of", "nearby", "pizza", "places"], "ner_tags": ["O", "B-Hours", "I-Hours", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "today is a good day for some tacos off to taco bell", "tokens": ["today", "is", "a", "good", "day", "for", "some", "tacos", "off", "to", "taco", "bell"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Dish", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "tummys grumbling give me the nearest fast food joint", "tokens": ["tummys", "grumbling", "give", "me", "the", "nearest", "fast", "food", "joint"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "want mushrooms near mansfield avenue before 7 am", "tokens": ["want", "mushrooms", "near", "mansfield", "avenue", "before", "7", "am"], "ner_tags": ["O", "B-Dish", "B-Location", "I-Location", "I-Location", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "want to find a restaurant that specilizes in spagetti", "tokens": ["want", "to", "find", "a", "restaurant", "that", "specilizes", "in", "spagetti"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Dish"]}
{"sentence": "we have a crying baby how far is a place to eat that is kid friendly", "tokens": ["we", "have", "a", "crying", "baby", "how", "far", "is", "a", "place", "to", "eat", "that", "is", "kid", "friendly"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "we want t go to dinner and then dance", "tokens": ["we", "want", "t", "go", "to", "dinner", "and", "then", "dance"], "ner_tags": ["O", "O", "O", "O", "O", "B-Hours", "O", "O", "B-Amenity"]}
{"sentence": "what are some fine dining restaurants in the area", "tokens": ["what", "are", "some", "fine", "dining", "restaurants", "in", "the", "area"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "what are some of the best restaurants in this city", "tokens": ["what", "are", "some", "of", "the", "best", "restaurants", "in", "this", "city"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "what are some of the locally favourite restaurants", "tokens": ["what", "are", "some", "of", "the", "locally", "favourite", "restaurants"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "O", "O"]}
{"sentence": "what are some restaurants that serve american food that are with in one mile", "tokens": ["what", "are", "some", "restaurants", "that", "serve", "american", "food", "that", "are", "with", "in", "one", "mile"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "what are the average prices for lunch at mikanos", "tokens": ["what", "are", "the", "average", "prices", "for", "lunch", "at", "mikanos"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Hours", "O", "B-Restaurant_Name"]}
{"sentence": "what are the directions to siu kuen chinese restaurant", "tokens": ["what", "are", "the", "directions", "to", "siu", "kuen", "chinese", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what are the directions to the speakeasy cafe", "tokens": ["what", "are", "the", "directions", "to", "the", "speakeasy", "cafe"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what are the fast food restraurants on the next 6 miles of this road", "tokens": ["what", "are", "the", "fast", "food", "restraurants", "on", "the", "next", "6", "miles", "of", "this", "road"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "what are the hours to verazzanos", "tokens": ["what", "are", "the", "hours", "to", "verazzanos"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "what are the portions like at red lobster", "tokens": ["what", "are", "the", "portions", "like", "at", "red", "lobster"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what are the prices like at gregorys restaurant", "tokens": ["what", "are", "the", "prices", "like", "at", "gregorys", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "O"]}
{"sentence": "what are the reviews on this italian restaurant", "tokens": ["what", "are", "the", "reviews", "on", "this", "italian", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "what are the spiciest local favourites served at cappys pizza and subs", "tokens": ["what", "are", "the", "spiciest", "local", "favourites", "served", "at", "cappys", "pizza", "and", "subs"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what are the three highest rated restaurants in my area", "tokens": ["what", "are", "the", "three", "highest", "rated", "restaurants", "in", "my", "area"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "I-Rating", "O", "O", "B-Location", "I-Location"]}
{"sentence": "what asian restaurants offer carry out", "tokens": ["what", "asian", "restaurants", "offer", "carry", "out"], "ner_tags": ["O", "B-Cuisine", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "what chicken restaurants are in the area", "tokens": ["what", "chicken", "restaurants", "are", "in", "the", "area"], "ner_tags": ["O", "B-Dish", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "what do you know about restaurants that have impressive wine lists", "tokens": ["what", "do", "you", "know", "about", "restaurants", "that", "have", "impressive", "wine", "lists"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "what fast food is near by", "tokens": ["what", "fast", "food", "is", "near", "by"], "ner_tags": ["O", "B-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "what has the best dumplings in town", "tokens": ["what", "has", "the", "best", "dumplings", "in", "town"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Dish", "B-Location", "I-Location"]}
{"sentence": "what high end restaurants are nearby", "tokens": ["what", "high", "end", "restaurants", "are", "nearby"], "ner_tags": ["O", "B-Rating", "I-Rating", "O", "O", "B-Location"]}
{"sentence": "what is a family friendly restaurant in dorche that serves vietnamese food", "tokens": ["what", "is", "a", "family", "friendly", "restaurant", "in", "dorche", "that", "serves", "vietnamese", "food"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Location", "O", "O", "B-Cuisine", "O"]}
{"sentence": "what is an italian restaurant in the area with family style meals", "tokens": ["what", "is", "an", "italian", "restaurant", "in", "the", "area", "with", "family", "style", "meals"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "O", "B-Location", "O", "B-Amenity", "I-Amenity", "O"]}
{"sentence": "what is dominos numbers", "tokens": ["what", "is", "dominos", "numbers"], "ner_tags": ["O", "O", "B-Restaurant_Name", "O"]}
{"sentence": "what is jack in the box phone number", "tokens": ["what", "is", "jack", "in", "the", "box", "phone", "number"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "O"]}
{"sentence": "what is the address to burger king on route 63", "tokens": ["what", "is", "the", "address", "to", "burger", "king", "on", "route", "63"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "what is the average mealcost for townline pizza in my city", "tokens": ["what", "is", "the", "average", "mealcost", "for", "townline", "pizza", "in", "my", "city"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location"]}
{"sentence": "what is the average price for lunch at olive garden", "tokens": ["what", "is", "the", "average", "price", "for", "lunch", "at", "olive", "garden"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Hours", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what is the average price range at ruby tuesday", "tokens": ["what", "is", "the", "average", "price", "range", "at", "ruby", "tuesday"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what is the best coffee house near here", "tokens": ["what", "is", "the", "best", "coffee", "house", "near", "here"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "I-Cuisine", "B-Location", "I-Location"]}
{"sentence": "what is the best ice cream parlor", "tokens": ["what", "is", "the", "best", "ice", "cream", "parlor"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "I-Cuisine", "I-Cuisine"]}
{"sentence": "what is the best mexican restaurant in town", "tokens": ["what", "is", "the", "best", "mexican", "restaurant", "in", "town"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "what is the best pizza place in the theater district", "tokens": ["what", "is", "the", "best", "pizza", "place", "in", "the", "theater", "district"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "what is the best restaurant around", "tokens": ["what", "is", "the", "best", "restaurant", "around"], "ner_tags": ["O", "O", "O", "B-Rating", "O", "B-Location"]}
{"sentence": "what is the best steak house in kansas city mo", "tokens": ["what", "is", "the", "best", "steak", "house", "in", "kansas", "city", "mo"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "what is the closest bar near heidis restaurant", "tokens": ["what", "is", "the", "closest", "bar", "near", "heidis", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what is the closest chic fil a", "tokens": ["what", "is", "the", "closest", "chic", "fil", "a"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what is the closest fast food to me", "tokens": ["what", "is", "the", "closest", "fast", "food", "to", "me"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O", "O"]}
{"sentence": "what is the closest wendys to me", "tokens": ["what", "is", "the", "closest", "wendys", "to", "me"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name", "O", "O"]}
{"sentence": "what is the dress code at fazolis", "tokens": ["what", "is", "the", "dress", "code", "at", "fazolis"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "what is the dress code at the precinct", "tokens": ["what", "is", "the", "dress", "code", "at", "the", "precinct"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what is the dress code for eating at planet hollywood", "tokens": ["what", "is", "the", "dress", "code", "for", "eating", "at", "planet", "hollywood"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what is the dress code for ruth chris steakhouse", "tokens": ["what", "is", "the", "dress", "code", "for", "ruth", "chris", "steakhouse"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what is the entree price at kiosque", "tokens": ["what", "is", "the", "entree", "price", "at", "kiosque"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "B-Restaurant_Name"]}
{"sentence": "what is the fastest route to the sonic", "tokens": ["what", "is", "the", "fastest", "route", "to", "the", "sonic"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "what is the favorite type of food people eat out here and where can i get it", "tokens": ["what", "is", "the", "favorite", "type", "of", "food", "people", "eat", "out", "here", "and", "where", "can", "i", "get", "it"], "ner_tags": ["O", "O", "O", "B-Rating", "O", "O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what is the highest rated thai restaurant in austin", "tokens": ["what", "is", "the", "highest", "rated", "thai", "restaurant", "in", "austin"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "B-Cuisine", "O", "O", "B-Location"]}
{"sentence": "what is the most closest eat in restaurant in the area", "tokens": ["what", "is", "the", "most", "closest", "eat", "in", "restaurant", "in", "the", "area"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "what is the most expensive restaurant with a dress code and valet parking within 15 miles", "tokens": ["what", "is", "the", "most", "expensive", "restaurant", "with", "a", "dress", "code", "and", "valet", "parking", "within", "15", "miles"], "ner_tags": ["O", "O", "O", "B-Price", "I-Price", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Amenity", "I-Amenity", "B-Location", "I-Location", "I-Location"]}
{"sentence": "what is the most popular italian restaurant near me", "tokens": ["what", "is", "the", "most", "popular", "italian", "restaurant", "near", "me"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "what is the name of that expensive greek restaurant in alston", "tokens": ["what", "is", "the", "name", "of", "that", "expensive", "greek", "restaurant", "in", "alston"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Price", "B-Cuisine", "O", "O", "B-Location"]}
{"sentence": "what is the name of the restaurant within 50 miles of chatswin that was built inside a cavern", "tokens": ["what", "is", "the", "name", "of", "the", "restaurant", "within", "50", "miles", "of", "chatswin", "that", "was", "built", "inside", "a", "cavern"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "what is the name or phone number of the coffee shop on seminary street", "tokens": ["what", "is", "the", "name", "or", "phone", "number", "of", "the", "coffee", "shop", "on", "seminary", "street"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Location", "I-Location"]}
{"sentence": "what is the nearest fast food restaurant", "tokens": ["what", "is", "the", "nearest", "fast", "food", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "what is the phone number for the closest chinese restaurant", "tokens": ["what", "is", "the", "phone", "number", "for", "the", "closest", "chinese", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "what is the phone number of a nearby restaurant that serves garlic shrimp", "tokens": ["what", "is", "the", "phone", "number", "of", "a", "nearby", "restaurant", "that", "serves", "garlic", "shrimp"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Location", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "what is the phone number of bills pizza", "tokens": ["what", "is", "the", "phone", "number", "of", "bills", "pizza"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what is the phone number of the mcdonalds on the east side of town", "tokens": ["what", "is", "the", "phone", "number", "of", "the", "mcdonalds", "on", "the", "east", "side", "of", "town"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "what is the phone number to alans on oak street", "tokens": ["what", "is", "the", "phone", "number", "to", "alans", "on", "oak", "street"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "what is the price for a lobster entree at martys marina", "tokens": ["what", "is", "the", "price", "for", "a", "lobster", "entree", "at", "martys", "marina"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what is the price range for dinner at clarkes family restaurant", "tokens": ["what", "is", "the", "price", "range", "for", "dinner", "at", "clarkes", "family", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what is the rating for pho saigon in mesa arizona", "tokens": ["what", "is", "the", "rating", "for", "pho", "saigon", "in", "mesa", "arizona"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location"]}
{"sentence": "what is the rating on arbys", "tokens": ["what", "is", "the", "rating", "on", "arbys"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "what is the rating on burger island", "tokens": ["what", "is", "the", "rating", "on", "burger", "island"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what is this months special at mcdonalds", "tokens": ["what", "is", "this", "months", "special", "at", "mcdonalds"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "what italian restaurant is open 24 hours on dalton street", "tokens": ["what", "italian", "restaurant", "is", "open", "24", "hours", "on", "dalton", "street"], "ner_tags": ["O", "B-Cuisine", "O", "O", "B-Hours", "I-Hours", "I-Hours", "O", "B-Location", "I-Location"]}
{"sentence": "what kind of atmosphere does simpsons have", "tokens": ["what", "kind", "of", "atmosphere", "does", "simpsons", "have"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "O"]}
{"sentence": "what kind of chicken dishes does theos serve", "tokens": ["what", "kind", "of", "chicken", "dishes", "does", "theos", "serve"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "O"]}
{"sentence": "what kind of fondue does the magic kettle have", "tokens": ["what", "kind", "of", "fondue", "does", "the", "magic", "kettle", "have"], "ner_tags": ["O", "O", "O", "B-Dish", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "what kind of food does abc cafe serve", "tokens": ["what", "kind", "of", "food", "does", "abc", "cafe", "serve"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "what kind of food does this place have", "tokens": ["what", "kind", "of", "food", "does", "this", "place", "have"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what kind of food does ubice sell", "tokens": ["what", "kind", "of", "food", "does", "ubice", "sell"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "O"]}
{"sentence": "what kosher restaurants still have smoking sections", "tokens": ["what", "kosher", "restaurants", "still", "have", "smoking", "sections"], "ner_tags": ["O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "what local restaurants have an outdoor play area for young children", "tokens": ["what", "local", "restaurants", "have", "an", "outdoor", "play", "area", "for", "young", "children"], "ner_tags": ["O", "B-Location", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "O", "O", "O"]}
{"sentence": "what nearby is open past midnight and has fabulous service", "tokens": ["what", "nearby", "is", "open", "past", "midnight", "and", "has", "fabulous", "service"], "ner_tags": ["O", "B-Location", "O", "B-Hours", "I-Hours", "I-Hours", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "what nearby places are byob", "tokens": ["what", "nearby", "places", "are", "byob"], "ner_tags": ["O", "B-Location", "O", "O", "B-Amenity"]}
{"sentence": "what nearby restaurant has the best reviews", "tokens": ["what", "nearby", "restaurant", "has", "the", "best", "reviews"], "ner_tags": ["O", "B-Location", "O", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "what nearby restaurants serve grilled tilapia", "tokens": ["what", "nearby", "restaurants", "serve", "grilled", "tilapia"], "ner_tags": ["O", "B-Location", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "what pancake restaurants are nearby", "tokens": ["what", "pancake", "restaurants", "are", "nearby"], "ner_tags": ["O", "B-Dish", "O", "O", "B-Location"]}
{"sentence": "what pizza place has the best toppings and is no farther than 4 miles", "tokens": ["what", "pizza", "place", "has", "the", "best", "toppings", "and", "is", "no", "farther", "than", "4", "miles"], "ner_tags": ["O", "B-Dish", "O", "O", "O", "B-Rating", "I-Rating", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "what place in saugus has the best decor", "tokens": ["what", "place", "in", "saugus", "has", "the", "best", "decor"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "B-Rating", "B-Amenity"]}
{"sentence": "what places are cheap here", "tokens": ["what", "places", "are", "cheap", "here"], "ner_tags": ["O", "O", "O", "B-Price", "B-Location"]}
{"sentence": "what places have great selections of beer", "tokens": ["what", "places", "have", "great", "selections", "of", "beer"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "what places have pretty good prices", "tokens": ["what", "places", "have", "pretty", "good", "prices"], "ner_tags": ["O", "O", "O", "B-Price", "I-Price", "O"]}
{"sentence": "what places in roxbury are open before 8 am and have excellent service", "tokens": ["what", "places", "in", "roxbury", "are", "open", "before", "8", "am", "and", "have", "excellent", "service"], "ner_tags": ["O", "O", "O", "B-Location", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "what places serve dinner late", "tokens": ["what", "places", "serve", "dinner", "late"], "ner_tags": ["O", "O", "O", "B-Hours", "I-Hours"]}
{"sentence": "what portugeese restaurant is on colonel bell drive", "tokens": ["what", "portugeese", "restaurant", "is", "on", "colonel", "bell", "drive"], "ner_tags": ["O", "B-Cuisine", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "what restaurant are cheap and only vegan", "tokens": ["what", "restaurant", "are", "cheap", "and", "only", "vegan"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "B-Cuisine"]}
{"sentence": "what restaurant are open till 10 pm", "tokens": ["what", "restaurant", "are", "open", "till", "10", "pm"], "ner_tags": ["O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "what restaurant has middle eastern food and great service", "tokens": ["what", "restaurant", "has", "middle", "eastern", "food", "and", "great", "service"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "what restaurant is expensive and has entertainment", "tokens": ["what", "restaurant", "is", "expensive", "and", "has", "entertainment"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "B-Amenity"]}
{"sentence": "what restaurant is open till 9 pm", "tokens": ["what", "restaurant", "is", "open", "till", "9", "pm"], "ner_tags": ["O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "what restaurant nearby are affordable", "tokens": ["what", "restaurant", "nearby", "are", "affordable"], "ner_tags": ["O", "O", "B-Location", "O", "B-Price"]}
{"sentence": "what restaurant on the south side has the hottest sauce for their chicken wings", "tokens": ["what", "restaurant", "on", "the", "south", "side", "has", "the", "hottest", "sauce", "for", "their", "chicken", "wings"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Cuisine", "B-Dish", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "what restaurant sells crab close by", "tokens": ["what", "restaurant", "sells", "crab", "close", "by"], "ner_tags": ["O", "O", "O", "B-Dish", "B-Location", "I-Location"]}
{"sentence": "what restaurant serves burritos 7 days per week", "tokens": ["what", "restaurant", "serves", "burritos", "7", "days", "per", "week"], "ner_tags": ["O", "O", "O", "B-Dish", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "what restaurant serves the largest portions of meat within 10 mile", "tokens": ["what", "restaurant", "serves", "the", "largest", "portions", "of", "meat", "within", "10", "mile"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Dish", "B-Location", "I-Location", "I-Location"]}
{"sentence": "what restaurant within 25 miles has the poorest quality food", "tokens": ["what", "restaurant", "within", "25", "miles", "has", "the", "poorest", "quality", "food"], "ner_tags": ["O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Rating", "O", "O"]}
{"sentence": "what restaurants are close to the museum", "tokens": ["what", "restaurants", "are", "close", "to", "the", "museum"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location"]}
{"sentence": "what restaurants are nearby", "tokens": ["what", "restaurants", "are", "nearby"], "ner_tags": ["O", "O", "O", "B-Location"]}
{"sentence": "what restaurants are open in the downtown area", "tokens": ["what", "restaurants", "are", "open", "in", "the", "downtown", "area"], "ner_tags": ["O", "O", "O", "B-Hours", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "what restaurants are open past midnight", "tokens": ["what", "restaurants", "are", "open", "past", "midnight"], "ner_tags": ["O", "O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "what restaurants are still open this late", "tokens": ["what", "restaurants", "are", "still", "open", "this", "late"], "ner_tags": ["O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "what restaurants are within 1 mile and have good prices", "tokens": ["what", "restaurants", "are", "within", "1", "mile", "and", "have", "good", "prices"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Price", "O"]}
{"sentence": "what restaurants around here have free wi fi", "tokens": ["what", "restaurants", "around", "here", "have", "free", "wi", "fi"], "ner_tags": ["O", "O", "B-Location", "I-Location", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "what restaurants downtown is open after 9 p", "tokens": ["what", "restaurants", "downtown", "is", "open", "after", "9", "p"], "ner_tags": ["O", "O", "B-Location", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "what restaurants have a large beer menu and are close to a hotel", "tokens": ["what", "restaurants", "have", "a", "large", "beer", "menu", "and", "are", "close", "to", "a", "hotel"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "what restaurants have nice family friendly brunch on weekends", "tokens": ["what", "restaurants", "have", "nice", "family", "friendly", "brunch", "on", "weekends"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "B-Hours", "O", "B-Hours"]}
{"sentence": "what restaurants nearby serve ribs and ice cream sundaes", "tokens": ["what", "restaurants", "nearby", "serve", "ribs", "and", "ice", "cream", "sundaes"], "ner_tags": ["O", "O", "B-Location", "O", "B-Dish", "O", "B-Dish", "I-Dish", "I-Dish"]}
{"sentence": "what restaurants stay open after 2 am within 5 miles", "tokens": ["what", "restaurants", "stay", "open", "after", "2", "am", "within", "5", "miles"], "ner_tags": ["O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "B-Location", "I-Location", "I-Location"]}
{"sentence": "what route should i take in order to get to the closest fast food place", "tokens": ["what", "route", "should", "i", "take", "in", "order", "to", "get", "to", "the", "closest", "fast", "food", "place"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "what seafood place around here has the biggest portions", "tokens": ["what", "seafood", "place", "around", "here", "has", "the", "biggest", "portions"], "ner_tags": ["O", "B-Cuisine", "O", "B-Location", "I-Location", "O", "O", "O", "O"]}
{"sentence": "what time does is breakfast over at burger king", "tokens": ["what", "time", "does", "is", "breakfast", "over", "at", "burger", "king"], "ner_tags": ["O", "O", "O", "O", "B-Hours", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what time does jollibees open", "tokens": ["what", "time", "does", "jollibees", "open"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "B-Hours"]}
{"sentence": "what time does kareems restaurant open", "tokens": ["what", "time", "does", "kareems", "restaurant", "open"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "O"]}
{"sentence": "what time does king palace serve dimsum until", "tokens": ["what", "time", "does", "king", "palace", "serve", "dimsum", "until"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Amenity", "B-Dish", "B-Hours"]}
{"sentence": "what time does little johns open on sunday", "tokens": ["what", "time", "does", "little", "johns", "open", "on", "sunday"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Hours", "I-Hours"]}
{"sentence": "what time does mcdonalds in main street open", "tokens": ["what", "time", "does", "mcdonalds", "in", "main", "street", "open"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "O", "B-Location", "I-Location", "O"]}
{"sentence": "what time does on the rocks on seminary stop serving food", "tokens": ["what", "time", "does", "on", "the", "rocks", "on", "seminary", "stop", "serving", "food"], "ner_tags": ["O", "B-Hours", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "what time does papa johns close", "tokens": ["what", "time", "does", "papa", "johns", "close"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Hours"]}
{"sentence": "what time does red robin close", "tokens": ["what", "time", "does", "red", "robin", "close"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "what time does sonic burger open", "tokens": ["what", "time", "does", "sonic", "burger", "open"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "what time does sonic open", "tokens": ["what", "time", "does", "sonic", "open"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "B-Hours"]}
{"sentence": "what time does subway open", "tokens": ["what", "time", "does", "subway", "open"], "ner_tags": ["O", "O", "B-Hours", "B-Restaurant_Name", "O"]}
{"sentence": "what time does the burger king on main street closes", "tokens": ["what", "time", "does", "the", "burger", "king", "on", "main", "street", "closes"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "O"]}
{"sentence": "what time does the nearest chipotle close", "tokens": ["what", "time", "does", "the", "nearest", "chipotle", "close"], "ner_tags": ["O", "O", "O", "O", "B-Location", "B-Restaurant_Name", "O"]}
{"sentence": "what time does the pho place in reno close", "tokens": ["what", "time", "does", "the", "pho", "place", "in", "reno", "close"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "B-Hours"]}
{"sentence": "what time does the sushi place near me open", "tokens": ["what", "time", "does", "the", "sushi", "place", "near", "me", "open"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "B-Location", "O", "B-Hours"]}
{"sentence": "what time is happy hour at the juice shop", "tokens": ["what", "time", "is", "happy", "hour", "at", "the", "juice", "shop"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "what time of the day does mcdonals start serving lunch the one near here", "tokens": ["what", "time", "of", "the", "day", "does", "mcdonals", "start", "serving", "lunch", "the", "one", "near", "here"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "O", "O", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "what type of cusine does gates have", "tokens": ["what", "type", "of", "cusine", "does", "gates", "have"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "O"]}
{"sentence": "what vegetarian options does zaxbys offer", "tokens": ["what", "vegetarian", "options", "does", "zaxbys", "offer"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "O"]}
{"sentence": "what would be an impressive restaurant for me to take a future business partner to", "tokens": ["what", "would", "be", "an", "impressive", "restaurant", "for", "me", "to", "take", "a", "future", "business", "partner", "to"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "O", "O"]}
{"sentence": "whats a cheap place near melrose that serves pakistani styled kabobs", "tokens": ["whats", "a", "cheap", "place", "near", "melrose", "that", "serves", "pakistani", "styled", "kabobs"], "ner_tags": ["O", "O", "B-Price", "O", "B-Location", "I-Location", "O", "O", "B-Cuisine", "O", "B-Dish"]}
{"sentence": "whats a good restaurant for a casual date", "tokens": ["whats", "a", "good", "restaurant", "for", "a", "casual", "date"], "ner_tags": ["O", "O", "B-Rating", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "whats a nicely priced southwestern place near here", "tokens": ["whats", "a", "nicely", "priced", "southwestern", "place", "near", "here"], "ner_tags": ["O", "O", "B-Price", "O", "B-Cuisine", "O", "B-Location", "I-Location"]}
{"sentence": "whats a popular place to east", "tokens": ["whats", "a", "popular", "place", "to", "east"], "ner_tags": ["O", "O", "B-Rating", "O", "O", "O"]}
{"sentence": "whats an expensive belgian restaurant in san diego", "tokens": ["whats", "an", "expensive", "belgian", "restaurant", "in", "san", "diego"], "ner_tags": ["O", "O", "B-Price", "B-Cuisine", "O", "O", "B-Location", "I-Location"]}
{"sentence": "whats open after noon", "tokens": ["whats", "open", "after", "noon"], "ner_tags": ["O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "whats the address of best locally owned sub sandwich shop", "tokens": ["whats", "the", "address", "of", "best", "locally", "owned", "sub", "sandwich", "shop"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "B-Location", "B-Amenity", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "whats the best barbeque restaurant in memphis", "tokens": ["whats", "the", "best", "barbeque", "restaurant", "in", "memphis"], "ner_tags": ["O", "O", "B-Rating", "B-Cuisine", "O", "O", "B-Location"]}
{"sentence": "whats the best french style cafe in sacramento", "tokens": ["whats", "the", "best", "french", "style", "cafe", "in", "sacramento"], "ner_tags": ["O", "O", "B-Rating", "B-Cuisine", "O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "whats the best indian restaurant in the area", "tokens": ["whats", "the", "best", "indian", "restaurant", "in", "the", "area"], "ner_tags": ["O", "O", "B-Rating", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "whats the best place around here to get cheese", "tokens": ["whats", "the", "best", "place", "around", "here", "to", "get", "cheese"], "ner_tags": ["O", "O", "B-Rating", "O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "whats the best restaurant within 10 blocks from us", "tokens": ["whats", "the", "best", "restaurant", "within", "10", "blocks", "from", "us"], "ner_tags": ["O", "O", "B-Rating", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "whats the cheapest fast food restaurant that has a play area for children", "tokens": ["whats", "the", "cheapest", "fast", "food", "restaurant", "that", "has", "a", "play", "area", "for", "children"], "ner_tags": ["O", "O", "B-Price", "B-Cuisine", "I-Cuisine", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O"]}
{"sentence": "whats the closest and cheapest bistro nearby", "tokens": ["whats", "the", "closest", "and", "cheapest", "bistro", "nearby"], "ner_tags": ["O", "O", "B-Location", "O", "B-Price", "B-Cuisine", "B-Location"]}
{"sentence": "whats the closest chinese food place", "tokens": ["whats", "the", "closest", "chinese", "food", "place"], "ner_tags": ["O", "O", "B-Location", "B-Amenity", "O", "O"]}
{"sentence": "whats the closest pizza restaurant", "tokens": ["whats", "the", "closest", "pizza", "restaurant"], "ner_tags": ["O", "O", "B-Location", "B-Dish", "O"]}
{"sentence": "whats the closest restaurant with a full salad bar", "tokens": ["whats", "the", "closest", "restaurant", "with", "a", "full", "salad", "bar"], "ner_tags": ["O", "O", "B-Location", "O", "O", "O", "B-Cuisine", "I-Cuisine", "I-Cuisine"]}
{"sentence": "whats the most expensive restaurant on the back bay thats open until midnight", "tokens": ["whats", "the", "most", "expensive", "restaurant", "on", "the", "back", "bay", "thats", "open", "until", "midnight"], "ner_tags": ["O", "O", "B-Price", "I-Price", "O", "O", "O", "B-Location", "I-Location", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "whats the most popular steak house around here", "tokens": ["whats", "the", "most", "popular", "steak", "house", "around", "here"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "I-Cuisine", "B-Location", "I-Location"]}
{"sentence": "whats the nearest pizza place that serves anchovies", "tokens": ["whats", "the", "nearest", "pizza", "place", "that", "serves", "anchovies"], "ner_tags": ["O", "O", "B-Location", "B-Cuisine", "O", "O", "O", "B-Dish"]}
{"sentence": "whats the phone number for that family owned thai restaurant on the north side", "tokens": ["whats", "the", "phone", "number", "for", "that", "family", "owned", "thai", "restaurant", "on", "the", "north", "side"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Cuisine", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "whats the phone number the restaurant i just pass", "tokens": ["whats", "the", "phone", "number", "the", "restaurant", "i", "just", "pass"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "whats the phone number to bouchon in napa", "tokens": ["whats", "the", "phone", "number", "to", "bouchon", "in", "napa"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "O", "B-Location"]}
{"sentence": "whats the shortest route to mcdonalds", "tokens": ["whats", "the", "shortest", "route", "to", "mcdonalds"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "when does better burger open today", "tokens": ["when", "does", "better", "burger", "open", "today"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O"]}
{"sentence": "when does churchs open", "tokens": ["when", "does", "churchs", "open"], "ner_tags": ["O", "O", "B-Restaurant_Name", "O"]}
{"sentence": "when does dominos open", "tokens": ["when", "does", "dominos", "open"], "ner_tags": ["O", "O", "B-Restaurant_Name", "B-Hours"]}
{"sentence": "when does pizza patron open", "tokens": ["when", "does", "pizza", "patron", "open"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Hours"]}
{"sentence": "when does subway open", "tokens": ["when", "does", "subway", "open"], "ner_tags": ["O", "O", "B-Restaurant_Name", "B-Hours"]}
{"sentence": "when does white castle close", "tokens": ["when", "does", "white", "castle", "close"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location"]}
{"sentence": "where are some fast food joints in driving distance to here", "tokens": ["where", "are", "some", "fast", "food", "joints", "in", "driving", "distance", "to", "here"], "ner_tags": ["O", "O", "O", "B-Cuisine", "I-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location", "O", "O"]}
{"sentence": "where are some mexican restaurants", "tokens": ["where", "are", "some", "mexican", "restaurants"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "where are the restaurants with a formal dress code around here", "tokens": ["where", "are", "the", "restaurants", "with", "a", "formal", "dress", "code", "around", "here"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "B-Location", "I-Location"]}
{"sentence": "where can a group be served omelets", "tokens": ["where", "can", "a", "group", "be", "served", "omelets"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "where can get some fried chicken", "tokens": ["where", "can", "get", "some", "fried", "chicken"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "where can i dine outside and eat pork chops", "tokens": ["where", "can", "i", "dine", "outside", "and", "eat", "pork", "chops"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "where can i eat a nice sit down dinner and have some pho", "tokens": ["where", "can", "i", "eat", "a", "nice", "sit", "down", "dinner", "and", "have", "some", "pho"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "O", "O", "O", "B-Dish"]}
{"sentence": "where can i eat african food for cheap", "tokens": ["where", "can", "i", "eat", "african", "food", "for", "cheap"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Price"]}
{"sentence": "where can i eat around here", "tokens": ["where", "can", "i", "eat", "around", "here"], "ner_tags": ["O", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "where can i eat by the water close by", "tokens": ["where", "can", "i", "eat", "by", "the", "water", "close", "by"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "B-Location", "I-Location"]}
{"sentence": "where can i eat pita bread late at night", "tokens": ["where", "can", "i", "eat", "pita", "bread", "late", "at", "night"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "I-Dish", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "where can i eat that delivers food", "tokens": ["where", "can", "i", "eat", "that", "delivers", "food"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where can i eat that has valet parking", "tokens": ["where", "can", "i", "eat", "that", "has", "valet", "parking"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where can i eat that isnt cheap", "tokens": ["where", "can", "i", "eat", "that", "isnt", "cheap"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "I-Price"]}
{"sentence": "where can i eat that wont need a reservation and that i can be at in 20 minutes", "tokens": ["where", "can", "i", "eat", "that", "wont", "need", "a", "reservation", "and", "that", "i", "can", "be", "at", "in", "20", "minutes"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "where can i find a cambodian restaurant that also sells ingredients", "tokens": ["where", "can", "i", "find", "a", "cambodian", "restaurant", "that", "also", "sells", "ingredients"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where can i find a cheap place to eat in peabody", "tokens": ["where", "can", "i", "find", "a", "cheap", "place", "to", "eat", "in", "peabody"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "O", "O", "O", "O", "B-Location"]}
{"sentence": "where can i find a coffee shop within 10 miles that is not part of a chain", "tokens": ["where", "can", "i", "find", "a", "coffee", "shop", "within", "10", "miles", "that", "is", "not", "part", "of", "a", "chain"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "where can i find a coffee zone that has dining at the bar and a good rating", "tokens": ["where", "can", "i", "find", "a", "coffee", "zone", "that", "has", "dining", "at", "the", "bar", "and", "a", "good", "rating"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Amenity", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "where can i find a cracker barrel near miami", "tokens": ["where", "can", "i", "find", "a", "cracker", "barrel", "near", "miami"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location"]}
{"sentence": "where can i find a good brepub that is open after 11 pm", "tokens": ["where", "can", "i", "find", "a", "good", "brepub", "that", "is", "open", "after", "11", "pm"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "where can i find a good place to eat that is in within miles from me", "tokens": ["where", "can", "i", "find", "a", "good", "place", "to", "eat", "that", "is", "in", "within", "miles", "from", "me"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "O", "O"]}
{"sentence": "where can i find a good pork chop", "tokens": ["where", "can", "i", "find", "a", "good", "pork", "chop"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Dish", "I-Dish"]}
{"sentence": "where can i find a good restaurant in the theatre district", "tokens": ["where", "can", "i", "find", "a", "good", "restaurant", "in", "the", "theatre", "district"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "where can i find a high end restaurant on chestnut street open after 11 pm", "tokens": ["where", "can", "i", "find", "a", "high", "end", "restaurant", "on", "chestnut", "street", "open", "after", "11", "pm"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "I-Price", "O", "O", "B-Location", "I-Location", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "where can i find a late night taco joint", "tokens": ["where", "can", "i", "find", "a", "late", "night", "taco", "joint"], "ner_tags": ["O", "O", "O", "O", "O", "B-Hours", "I-Hours", "B-Cuisine", "I-Cuisine"]}
{"sentence": "where can i find a nice gelato place that offers carryout", "tokens": ["where", "can", "i", "find", "a", "nice", "gelato", "place", "that", "offers", "carryout"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity"]}
{"sentence": "where can i find a place for people watching nearby", "tokens": ["where", "can", "i", "find", "a", "place", "for", "people", "watching", "nearby"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "B-Location"]}
{"sentence": "where can i find a place that serves gelato", "tokens": ["where", "can", "i", "find", "a", "place", "that", "serves", "gelato"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "where can i find a place to eat that serves pasta", "tokens": ["where", "can", "i", "find", "a", "place", "to", "eat", "that", "serves", "pasta"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "where can i find a place with a great wine list", "tokens": ["where", "can", "i", "find", "a", "place", "with", "a", "great", "wine", "list"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "where can i find a place within 5 minutes with great prices", "tokens": ["where", "can", "i", "find", "a", "place", "within", "5", "minutes", "with", "great", "prices"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "B-Price", "O"]}
{"sentence": "where can i find a rainforest cafe", "tokens": ["where", "can", "i", "find", "a", "rainforest", "cafe"], "ner_tags": ["O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where can i find a restaurant near me", "tokens": ["where", "can", "i", "find", "a", "restaurant", "near", "me"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "where can i find a restaurant that has good portions and is open at 9 p", "tokens": ["where", "can", "i", "find", "a", "restaurant", "that", "has", "good", "portions", "and", "is", "open", "at", "9", "p"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "where can i find a restaurant with a vegetarian senior discount menu", "tokens": ["where", "can", "i", "find", "a", "restaurant", "with", "a", "vegetarian", "senior", "discount", "menu"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Cuisine", "B-Amenity", "I-Amenity", "O"]}
{"sentence": "where can i find a seafood restaurant that also sells fresh fish", "tokens": ["where", "can", "i", "find", "a", "seafood", "restaurant", "that", "also", "sells", "fresh", "fish"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "where can i find a sports bar in nassau county new york", "tokens": ["where", "can", "i", "find", "a", "sports", "bar", "in", "nassau", "county", "new", "york"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "where can i find a thai restaurant that is non smoking", "tokens": ["where", "can", "i", "find", "a", "thai", "restaurant", "that", "is", "non", "smoking"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where can i find authentic mexican cuisine at a great price", "tokens": ["where", "can", "i", "find", "authentic", "mexican", "cuisine", "at", "a", "great", "price"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "I-Cuisine", "O", "O", "O", "B-Price", "O"]}
{"sentence": "where can i find bobby hacketts restaurant", "tokens": ["where", "can", "i", "find", "bobby", "hacketts", "restaurant"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "where can i find cheap vegan food", "tokens": ["where", "can", "i", "find", "cheap", "vegan", "food"], "ner_tags": ["O", "O", "O", "O", "B-Price", "B-Cuisine", "O"]}
{"sentence": "where can i find good irish food", "tokens": ["where", "can", "i", "find", "good", "irish", "food"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "where can i find halal with a price not so high", "tokens": ["where", "can", "i", "find", "halal", "with", "a", "price", "not", "so", "high"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "B-Price", "I-Price", "I-Price", "I-Price"]}
{"sentence": "where can i find naugles", "tokens": ["where", "can", "i", "find", "naugles"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name"]}
{"sentence": "where can i find somewhere relaxed thats open in the morning around beacon hill", "tokens": ["where", "can", "i", "find", "somewhere", "relaxed", "thats", "open", "in", "the", "morning", "around", "beacon", "hill"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "B-Location", "I-Location", "I-Location"]}
{"sentence": "where can i find the best sushi in town", "tokens": ["where", "can", "i", "find", "the", "best", "sushi", "in", "town"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Dish", "B-Location", "I-Location"]}
{"sentence": "where can i find the cheapest burgers in town", "tokens": ["where", "can", "i", "find", "the", "cheapest", "burgers", "in", "town"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "B-Dish", "B-Location", "I-Location"]}
{"sentence": "where can i find the cheapest pizza", "tokens": ["where", "can", "i", "find", "the", "cheapest", "pizza"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "B-Dish"]}
{"sentence": "where can i find the cheapest take out chinese food", "tokens": ["where", "can", "i", "find", "the", "cheapest", "take", "out", "chinese", "food"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "B-Amenity", "I-Amenity", "B-Cuisine", "O"]}
{"sentence": "where can i find the closest bakery", "tokens": ["where", "can", "i", "find", "the", "closest", "bakery"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "B-Cuisine"]}
{"sentence": "where can i find the highest rated breakfast burrito", "tokens": ["where", "can", "i", "find", "the", "highest", "rated", "breakfast", "burrito"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "I-Rating", "B-Dish", "I-Dish"]}
{"sentence": "where can i find tofu in city hall plaza with parking", "tokens": ["where", "can", "i", "find", "tofu", "in", "city", "hall", "plaza", "with", "parking"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "O", "B-Location", "I-Location", "I-Location", "O", "B-Amenity"]}
{"sentence": "where can i get a banana split", "tokens": ["where", "can", "i", "get", "a", "banana", "split"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "where can i get a burger", "tokens": ["where", "can", "i", "get", "a", "burger"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "where can i get a cheap dinner", "tokens": ["where", "can", "i", "get", "a", "cheap", "dinner"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "B-Hours"]}
{"sentence": "where can i get a cheap slice of pizza in huntington ny", "tokens": ["where", "can", "i", "get", "a", "cheap", "slice", "of", "pizza", "in", "huntington", "ny"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "B-Dish", "I-Dish", "I-Dish", "O", "B-Location", "I-Location"]}
{"sentence": "where can i get a good bagel from", "tokens": ["where", "can", "i", "get", "a", "good", "bagel", "from"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Dish", "O"]}
{"sentence": "where can i get a good deal on two pizzas in manhattan", "tokens": ["where", "can", "i", "get", "a", "good", "deal", "on", "two", "pizzas", "in", "manhattan"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Dish", "O", "B-Location"]}
{"sentence": "where can i get a good sandwich for a good price", "tokens": ["where", "can", "i", "get", "a", "good", "sandwich", "for", "a", "good", "price"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Dish", "O", "O", "B-Price", "O"]}
{"sentence": "where can i get a great tofu omelette in wayland", "tokens": ["where", "can", "i", "get", "a", "great", "tofu", "omelette", "in", "wayland"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "I-Dish", "O", "B-Location"]}
{"sentence": "where can i get a hamburger", "tokens": ["where", "can", "i", "get", "a", "hamburger"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "where can i get a hamburger between thompson falls and plains", "tokens": ["where", "can", "i", "get", "a", "hamburger", "between", "thompson", "falls", "and", "plains"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "where can i get a healthy lunch", "tokens": ["where", "can", "i", "get", "a", "healthy", "lunch"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "B-Hours"]}
{"sentence": "where can i get a milkshake within walking distance", "tokens": ["where", "can", "i", "get", "a", "milkshake", "within", "walking", "distance"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "B-Location", "I-Location", "I-Location"]}
{"sentence": "where can i get a pho king sized bowl", "tokens": ["where", "can", "i", "get", "a", "pho", "king", "sized", "bowl"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "I-Dish", "I-Dish", "I-Dish"]}
{"sentence": "where can i get a pricey drink on kendall square", "tokens": ["where", "can", "i", "get", "a", "pricey", "drink", "on", "kendall", "square"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "O", "B-Location", "I-Location"]}
{"sentence": "where can i get a taco for a dollar", "tokens": ["where", "can", "i", "get", "a", "taco", "for", "a", "dollar"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "B-Price", "I-Price", "I-Price"]}
{"sentence": "where can i get an appitizer in american legion highway", "tokens": ["where", "can", "i", "get", "an", "appitizer", "in", "american", "legion", "highway"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "where can i get bagels", "tokens": ["where", "can", "i", "get", "bagels"], "ner_tags": ["O", "O", "O", "O", "B-Dish"]}
{"sentence": "where can i get barbecue no farther than 5 miles", "tokens": ["where", "can", "i", "get", "barbecue", "no", "farther", "than", "5", "miles"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "O", "O", "B-Location", "I-Location"]}
{"sentence": "where can i get caribbean food not far from here in a romantic setting", "tokens": ["where", "can", "i", "get", "caribbean", "food", "not", "far", "from", "here", "in", "a", "romantic", "setting"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where can i get cheap food at 5 pm in alston", "tokens": ["where", "can", "i", "get", "cheap", "food", "at", "5", "pm", "in", "alston"], "ner_tags": ["O", "O", "O", "O", "B-Price", "O", "O", "B-Hours", "I-Hours", "B-Location", "I-Location"]}
{"sentence": "where can i get decent inexpensive sushi in glen cove ny", "tokens": ["where", "can", "i", "get", "decent", "inexpensive", "sushi", "in", "glen", "cove", "ny"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "B-Dish", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "where can i get doughnuts right now", "tokens": ["where", "can", "i", "get", "doughnuts", "right", "now"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "O", "B-Hours"]}
{"sentence": "where can i get eggrolls", "tokens": ["where", "can", "i", "get", "eggrolls"], "ner_tags": ["O", "O", "O", "O", "B-Dish"]}
{"sentence": "where can i get fish", "tokens": ["where", "can", "i", "get", "fish"], "ner_tags": ["O", "O", "O", "O", "B-Dish"]}
{"sentence": "where can i get large portions of fish with a group of interesting folk", "tokens": ["where", "can", "i", "get", "large", "portions", "of", "fish", "with", "a", "group", "of", "interesting", "folk"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Dish", "O", "O", "B-Amenity", "O", "O", "O"]}
{"sentence": "where can i get nachos within 1 mile of me that is open late", "tokens": ["where", "can", "i", "get", "nachos", "within", "1", "mile", "of", "me", "that", "is", "open", "late"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "B-Location", "I-Location", "I-Location", "O", "O", "O", "O", "B-Hours", "I-Hours"]}
{"sentence": "where can i get reasonably priced sashimi at 8 pm", "tokens": ["where", "can", "i", "get", "reasonably", "priced", "sashimi", "at", "8", "pm"], "ner_tags": ["O", "O", "O", "O", "B-Price", "O", "B-Dish", "O", "B-Hours", "I-Hours"]}
{"sentence": "where can i get some chicken nuggets", "tokens": ["where", "can", "i", "get", "some", "chicken", "nuggets"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "where can i get some cookies", "tokens": ["where", "can", "i", "get", "some", "cookies"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "where can i get some crab nearby that is open until 2 am", "tokens": ["where", "can", "i", "get", "some", "crab", "nearby", "that", "is", "open", "until", "2", "am"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "B-Location", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "where can i get some fast food", "tokens": ["where", "can", "i", "get", "some", "fast", "food"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "I-Cuisine"]}
{"sentence": "where can i get some good coffee", "tokens": ["where", "can", "i", "get", "some", "good", "coffee"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Dish"]}
{"sentence": "where can i get some ice cream", "tokens": ["where", "can", "i", "get", "some", "ice", "cream"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "where can i get some macaroni and cheese", "tokens": ["where", "can", "i", "get", "some", "macaroni", "and", "cheese"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "I-Dish", "I-Dish"]}
{"sentence": "where can i get some shrimp", "tokens": ["where", "can", "i", "get", "some", "shrimp"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "where can i get some slushy", "tokens": ["where", "can", "i", "get", "some", "slushy"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "where can i get some southern food", "tokens": ["where", "can", "i", "get", "some", "southern", "food"], "ner_tags": ["O", "O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "where can i get some spaghetti", "tokens": ["where", "can", "i", "get", "some", "spaghetti"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "where can i get some sushi", "tokens": ["where", "can", "i", "get", "some", "sushi"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "where can i get some waffles", "tokens": ["where", "can", "i", "get", "some", "waffles"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "where can i get something inexpensive to eat near my current location", "tokens": ["where", "can", "i", "get", "something", "inexpensive", "to", "eat", "near", "my", "current", "location"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "O", "O", "B-Location", "O", "B-Location", "I-Location"]}
{"sentence": "where can i get starbucks around me", "tokens": ["where", "can", "i", "get", "starbucks", "around", "me"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "B-Location", "I-Location"]}
{"sentence": "where can i get the best pie", "tokens": ["where", "can", "i", "get", "the", "best", "pie"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Dish"]}
{"sentence": "where can i get the top rated hamburger in baltimore", "tokens": ["where", "can", "i", "get", "the", "top", "rated", "hamburger", "in", "baltimore"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "I-Rating", "B-Dish", "O", "B-Location"]}
{"sentence": "where can i get vegan food", "tokens": ["where", "can", "i", "get", "vegan", "food"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "where can i go for expensive gelato for carry out", "tokens": ["where", "can", "i", "go", "for", "expensive", "gelato", "for", "carry", "out"], "ner_tags": ["O", "O", "O", "O", "O", "B-Price", "B-Dish", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where can i go to eat", "tokens": ["where", "can", "i", "go", "to", "eat"], "ner_tags": ["O", "O", "O", "O", "O", "O"]}
{"sentence": "where can i go to get a sandwich around here", "tokens": ["where", "can", "i", "go", "to", "get", "a", "sandwich", "around", "here"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Dish", "B-Location", "I-Location"]}
{"sentence": "where can i have a glass of wine in jamaica plain that also does not allow smoking", "tokens": ["where", "can", "i", "have", "a", "glass", "of", "wine", "in", "jamaica", "plain", "that", "also", "does", "not", "allow", "smoking"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "O", "B-Cuisine", "O", "B-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "where can i order fried chicken to eat", "tokens": ["where", "can", "i", "order", "fried", "chicken", "to", "eat"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "I-Dish", "O", "O"]}
{"sentence": "where can i take a date nearby for enchiladas", "tokens": ["where", "can", "i", "take", "a", "date", "nearby", "for", "enchiladas"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "B-Location", "O", "B-Dish"]}
{"sentence": "where can i take coworkers for excellent priced food", "tokens": ["where", "can", "i", "take", "coworkers", "for", "excellent", "priced", "food"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "O", "B-Price", "O", "O"]}
{"sentence": "where can i take my 5 year old nephew to eat", "tokens": ["where", "can", "i", "take", "my", "5", "year", "old", "nephew", "to", "eat"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "I-Amenity", "O", "O", "O"]}
{"sentence": "where can i take my date for a cheap meal in charlestown", "tokens": ["where", "can", "i", "take", "my", "date", "for", "a", "cheap", "meal", "in", "charlestown"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "O", "O", "B-Price", "O", "O", "B-Location"]}
{"sentence": "where can i take my kids for good tacos", "tokens": ["where", "can", "i", "take", "my", "kids", "for", "good", "tacos"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "O", "B-Rating", "B-Dish"]}
{"sentence": "where can i take my kids to eat", "tokens": ["where", "can", "i", "take", "my", "kids", "to", "eat"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "O", "O"]}
{"sentence": "where can i take my kids to get an omelet", "tokens": ["where", "can", "i", "take", "my", "kids", "to", "get", "an", "omelet"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "O", "O", "O", "B-Dish"]}
{"sentence": "where can we find a french inspired restaurant", "tokens": ["where", "can", "we", "find", "a", "french", "inspired", "restaurant"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "I-Amenity", "O"]}
{"sentence": "where could i eat sushi on the waterfront", "tokens": ["where", "could", "i", "eat", "sushi", "on", "the", "waterfront"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "O", "O", "B-Location"]}
{"sentence": "where could i find some good tomato sauce thats 10 minutes away with excellent pricing", "tokens": ["where", "could", "i", "find", "some", "good", "tomato", "sauce", "thats", "10", "minutes", "away", "with", "excellent", "pricing"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "B-Dish", "I-Dish", "O", "B-Location", "I-Location", "I-Location", "O", "B-Price", "O"]}
{"sentence": "where could i get some cakes in a business atmosphere thats open until 1 am", "tokens": ["where", "could", "i", "get", "some", "cakes", "in", "a", "business", "atmosphere", "thats", "open", "until", "1", "am"], "ner_tags": ["O", "O", "O", "O", "O", "B-Dish", "O", "O", "B-Amenity", "I-Amenity", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "where could i go for a burger and a movie", "tokens": ["where", "could", "i", "go", "for", "a", "burger", "and", "a", "movie"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "O", "O", "B-Amenity"]}
{"sentence": "where could i go for a roast beef sandwich right now", "tokens": ["where", "could", "i", "go", "for", "a", "roast", "beef", "sandwich", "right", "now"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "I-Dish", "I-Dish", "B-Hours", "I-Hours"]}
{"sentence": "where do i park to go to the butcher and the boar", "tokens": ["where", "do", "i", "park", "to", "go", "to", "the", "butcher", "and", "the", "boar"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where i can get seafood and bring my own drinks", "tokens": ["where", "i", "can", "get", "seafood", "and", "bring", "my", "own", "drinks"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "where in beverly can i bring a group to an italys little kitchen", "tokens": ["where", "in", "beverly", "can", "i", "bring", "a", "group", "to", "an", "italys", "little", "kitchen"], "ner_tags": ["O", "O", "B-Location", "O", "O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where in the theater district is there a restaurant with portions that are a bit small", "tokens": ["where", "in", "the", "theater", "district", "is", "there", "a", "restaurant", "with", "portions", "that", "are", "a", "bit", "small"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "where is a bob evans on diauto drive with moderate portions", "tokens": ["where", "is", "a", "bob", "evans", "on", "diauto", "drive", "with", "moderate", "portions"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where is a cheap restaurant in the north end that serves burritos", "tokens": ["where", "is", "a", "cheap", "restaurant", "in", "the", "north", "end", "that", "serves", "burritos"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Dish"]}
{"sentence": "where is a good place to eat", "tokens": ["where", "is", "a", "good", "place", "to", "eat"], "ner_tags": ["O", "O", "O", "B-Rating", "O", "O", "O"]}
{"sentence": "where is a good place to get seafood", "tokens": ["where", "is", "a", "good", "place", "to", "get", "seafood"], "ner_tags": ["O", "O", "O", "B-Rating", "O", "O", "O", "B-Cuisine"]}
{"sentence": "where is a good restaurant on the water", "tokens": ["where", "is", "a", "good", "restaurant", "on", "the", "water"], "ner_tags": ["O", "O", "O", "B-Rating", "O", "O", "O", "B-Location"]}
{"sentence": "where is a kid friendly pizza place with games", "tokens": ["where", "is", "a", "kid", "friendly", "pizza", "place", "with", "games"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "B-Cuisine", "O", "O", "B-Amenity"]}
{"sentence": "where is a kosher place for lunch", "tokens": ["where", "is", "a", "kosher", "place", "for", "lunch"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Hours", "I-Hours"]}
{"sentence": "where is a late night ethiopian place that we can go after 2 am", "tokens": ["where", "is", "a", "late", "night", "ethiopian", "place", "that", "we", "can", "go", "after", "2", "am"], "ner_tags": ["O", "O", "O", "B-Hours", "I-Hours", "B-Cuisine", "O", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "where is a reasonably price japanese restaurant downtown", "tokens": ["where", "is", "a", "reasonably", "price", "japanese", "restaurant", "downtown"], "ner_tags": ["O", "O", "O", "B-Price", "O", "B-Cuisine", "O", "B-Location"]}
{"sentence": "where is a restaurant open till midnight near west bridgewater with really friendly service", "tokens": ["where", "is", "a", "restaurant", "open", "till", "midnight", "near", "west", "bridgewater", "with", "really", "friendly", "service"], "ner_tags": ["O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "B-Location", "I-Location", "I-Location", "O", "B-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "where is a restaurant that opens 24 hours", "tokens": ["where", "is", "a", "restaurant", "that", "opens", "24", "hours"], "ner_tags": ["O", "O", "O", "O", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "where is a romantic place to get a sub near here", "tokens": ["where", "is", "a", "romantic", "place", "to", "get", "a", "sub", "near", "here"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity", "O", "O", "O", "B-Cuisine", "B-Location", "O"]}
{"sentence": "where is an italian restaurant with outdoor seating", "tokens": ["where", "is", "an", "italian", "restaurant", "with", "outdoor", "seating"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where is an italos bakery along this route that has waterfront dining", "tokens": ["where", "is", "an", "italos", "bakery", "along", "this", "route", "that", "has", "waterfront", "dining"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where is charlotte nc", "tokens": ["where", "is", "charlotte", "nc"], "ner_tags": ["O", "O", "B-Location", "I-Location"]}
{"sentence": "where is dominos", "tokens": ["where", "is", "dominos"], "ner_tags": ["O", "O", "B-Restaurant_Name"]}
{"sentence": "where is dominos pizza", "tokens": ["where", "is", "dominos", "pizza"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where is good ethnic food", "tokens": ["where", "is", "good", "ethnic", "food"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "where is ii moro", "tokens": ["where", "is", "ii", "moro"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where is jack in the box", "tokens": ["where", "is", "jack", "in", "the", "box"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where is long john silvers", "tokens": ["where", "is", "long", "john", "silvers"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where is p f changs", "tokens": ["where", "is", "p", "f", "changs"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where is pizza express", "tokens": ["where", "is", "pizza", "express"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where is roosevelts restaurant and sin in framingham", "tokens": ["where", "is", "roosevelts", "restaurant", "and", "sin", "in", "framingham"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location"]}
{"sentence": "where is some good outdoor dining", "tokens": ["where", "is", "some", "good", "outdoor", "dining"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Amenity", "I-Amenity"]}
{"sentence": "where is sportsway bar at melrose", "tokens": ["where", "is", "sportsway", "bar", "at", "melrose"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location"]}
{"sentence": "where is station donuts", "tokens": ["where", "is", "station", "donuts"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where is subway", "tokens": ["where", "is", "subway"], "ner_tags": ["O", "O", "B-Restaurant_Name"]}
{"sentence": "where is that restaurant where you can order burgers and fries and watch a movie while you eat", "tokens": ["where", "is", "that", "restaurant", "where", "you", "can", "order", "burgers", "and", "fries", "and", "watch", "a", "movie", "while", "you", "eat"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Dish", "O", "B-Dish", "O", "B-Amenity", "I-Amenity", "I-Amenity", "O", "O", "O"]}
{"sentence": "where is the apple store in the area", "tokens": ["where", "is", "the", "apple", "store", "in", "the", "area"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "B-Location", "I-Location", "I-Location"]}
{"sentence": "where is the best italian food in the city", "tokens": ["where", "is", "the", "best", "italian", "food", "in", "the", "city"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "where is the best korean bbq", "tokens": ["where", "is", "the", "best", "korean", "bbq"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "I-Cuisine"]}
{"sentence": "where is the best mediterranean restaurant", "tokens": ["where", "is", "the", "best", "mediterranean", "restaurant"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Cuisine", "O"]}
{"sentence": "where is the best rated fast food establishment", "tokens": ["where", "is", "the", "best", "rated", "fast", "food", "establishment"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "where is the best reviewed place to get a burger and fries near beacon hill", "tokens": ["where", "is", "the", "best", "reviewed", "place", "to", "get", "a", "burger", "and", "fries", "near", "beacon", "hill"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "O", "O", "O", "O", "B-Dish", "I-Dish", "I-Dish", "B-Location", "I-Location", "I-Location"]}
{"sentence": "where is the cheapest place to eat nearest to me", "tokens": ["where", "is", "the", "cheapest", "place", "to", "eat", "nearest", "to", "me"], "ner_tags": ["O", "O", "O", "B-Price", "O", "O", "O", "B-Location", "O", "O"]}
{"sentence": "where is the closest 5 star restaurant", "tokens": ["where", "is", "the", "closest", "5", "star", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Rating", "I-Rating", "O"]}
{"sentence": "where is the closest apple bees", "tokens": ["where", "is", "the", "closest", "apple", "bees"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where is the closest chillis to my current location", "tokens": ["where", "is", "the", "closest", "chillis", "to", "my", "current", "location"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "where is the closest fine dining restaurant", "tokens": ["where", "is", "the", "closest", "fine", "dining", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "where is the closest happy hour", "tokens": ["where", "is", "the", "closest", "happy", "hour"], "ner_tags": ["O", "O", "O", "B-Location", "B-Amenity", "I-Amenity"]}
{"sentence": "where is the closest international brownie", "tokens": ["where", "is", "the", "closest", "international", "brownie"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where is the closest jimmie johns", "tokens": ["where", "is", "the", "closest", "jimmie", "johns"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where is the closest non smoking restaurant", "tokens": ["where", "is", "the", "closest", "non", "smoking", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Amenity", "I-Amenity", "O"]}
{"sentence": "where is the closest pizza hut", "tokens": ["where", "is", "the", "closest", "pizza", "hut"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where is the closest place to get cheap potatoes", "tokens": ["where", "is", "the", "closest", "place", "to", "get", "cheap", "potatoes"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "O", "B-Price", "B-Dish"]}
{"sentence": "where is the closest place to get pizza", "tokens": ["where", "is", "the", "closest", "place", "to", "get", "pizza"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "O", "B-Dish"]}
{"sentence": "where is the closest red robin", "tokens": ["where", "is", "the", "closest", "red", "robin"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where is the closest restaurant that serves chinese food", "tokens": ["where", "is", "the", "closest", "restaurant", "that", "serves", "chinese", "food"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "O", "B-Cuisine", "O"]}
{"sentence": "where is the closest sub or sandwich shop that serves gyros", "tokens": ["where", "is", "the", "closest", "sub", "or", "sandwich", "shop", "that", "serves", "gyros"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "O", "B-Cuisine", "O", "O", "O", "B-Dish"]}
{"sentence": "where is the closest sushi bars to my zip code", "tokens": ["where", "is", "the", "closest", "sushi", "bars", "to", "my", "zip", "code"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "where is the closest tgi fridays", "tokens": ["where", "is", "the", "closest", "tgi", "fridays"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where is the closest tim hortons restaurant", "tokens": ["where", "is", "the", "closest", "tim", "hortons", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where is the different locations of taco bell", "tokens": ["where", "is", "the", "different", "locations", "of", "taco", "bell"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where is the nearest 4 star restaurant", "tokens": ["where", "is", "the", "nearest", "4", "star", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Rating", "I-Rating", "O"]}
{"sentence": "where is the nearest asia express restaurant", "tokens": ["where", "is", "the", "nearest", "asia", "express", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where is the nearest bar with dinning located", "tokens": ["where", "is", "the", "nearest", "bar", "with", "dinning", "located"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "O", "B-Amenity", "O"]}
{"sentence": "where is the nearest buffet", "tokens": ["where", "is", "the", "nearest", "buffet"], "ner_tags": ["O", "O", "O", "B-Location", "B-Amenity"]}
{"sentence": "where is the nearest burger place", "tokens": ["where", "is", "the", "nearest", "burger", "place"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "where is the nearest casual restaurant", "tokens": ["where", "is", "the", "nearest", "casual", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Amenity", "O"]}
{"sentence": "where is the nearest chinese restaurant with more than 3 stars that is under 10 an entree", "tokens": ["where", "is", "the", "nearest", "chinese", "restaurant", "with", "more", "than", "3", "stars", "that", "is", "under", "10", "an", "entree"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "O", "O", "B-Rating", "I-Rating", "I-Rating", "I-Rating", "O", "O", "B-Price", "I-Price", "O", "O"]}
{"sentence": "where is the nearest family restaurant", "tokens": ["where", "is", "the", "nearest", "family", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Amenity", "O"]}
{"sentence": "where is the nearest ice cream shop", "tokens": ["where", "is", "the", "nearest", "ice", "cream", "shop"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "O"]}
{"sentence": "where is the nearest kosher restaurant", "tokens": ["where", "is", "the", "nearest", "kosher", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "where is the nearest mexican restaurant open late for dinner", "tokens": ["where", "is", "the", "nearest", "mexican", "restaurant", "open", "late", "for", "dinner"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "O", "B-Hours", "I-Hours", "O", "B-Hours"]}
{"sentence": "where is the nearest napoli pizzeria", "tokens": ["where", "is", "the", "nearest", "napoli", "pizzeria"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where is the nearest place i can get a smoothie", "tokens": ["where", "is", "the", "nearest", "place", "i", "can", "get", "a", "smoothie"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "O", "O", "O", "B-Dish"]}
{"sentence": "where is the nearest place that serves nachos", "tokens": ["where", "is", "the", "nearest", "place", "that", "serves", "nachos"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "O", "B-Dish"]}
{"sentence": "where is the nearest place with a dining patio", "tokens": ["where", "is", "the", "nearest", "place", "with", "a", "dining", "patio"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where is the nearest place within 5 miles that has a game room for kids", "tokens": ["where", "is", "the", "nearest", "place", "within", "5", "miles", "that", "has", "a", "game", "room", "for", "kids"], "ner_tags": ["O", "O", "O", "B-Location", "O", "B-Location", "I-Location", "I-Location", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "where is the nearest restaurant that allows indoor smoking", "tokens": ["where", "is", "the", "nearest", "restaurant", "that", "allows", "indoor", "smoking"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "where is the nearest restaurant that serves hush puppies", "tokens": ["where", "is", "the", "nearest", "restaurant", "that", "serves", "hush", "puppies"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "O", "B-Dish", "I-Dish"]}
{"sentence": "where is the nearest restaurant that serves steak", "tokens": ["where", "is", "the", "nearest", "restaurant", "that", "serves", "steak"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "O", "B-Dish"]}
{"sentence": "where is the nearest restaurant with a casual kid friendly atmosphere", "tokens": ["where", "is", "the", "nearest", "restaurant", "with", "a", "casual", "kid", "friendly", "atmosphere"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "O"]}
{"sentence": "where is the nearest shell gas station", "tokens": ["where", "is", "the", "nearest", "shell", "gas", "station"], "ner_tags": ["O", "O", "O", "B-Location", "O", "O", "O"]}
{"sentence": "where is the nearest sushi bar", "tokens": ["where", "is", "the", "nearest", "sushi", "bar"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "I-Cuisine"]}
{"sentence": "where is the nearest take out chinese restaurant", "tokens": ["where", "is", "the", "nearest", "take", "out", "chinese", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Amenity", "I-Amenity", "B-Cuisine", "O"]}
{"sentence": "where is the nearest tapas restaurant", "tokens": ["where", "is", "the", "nearest", "tapas", "restaurant"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "where is the nearest thai restaurant that serves breakfast in alston", "tokens": ["where", "is", "the", "nearest", "thai", "restaurant", "that", "serves", "breakfast", "in", "alston"], "ner_tags": ["O", "O", "O", "B-Location", "B-Cuisine", "O", "O", "O", "O", "O", "B-Location"]}
{"sentence": "where is the nearest tomacchios with patio seating located", "tokens": ["where", "is", "the", "nearest", "tomacchios", "with", "patio", "seating", "located"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name", "O", "B-Amenity", "I-Amenity", "O"]}
{"sentence": "where is the nearest uptown espresso", "tokens": ["where", "is", "the", "nearest", "uptown", "espresso"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "O"]}
{"sentence": "where is the next mcdonald", "tokens": ["where", "is", "the", "next", "mcdonald"], "ner_tags": ["O", "O", "O", "B-Location", "B-Restaurant_Name"]}
{"sentence": "where is the place with byob 2 miles from here", "tokens": ["where", "is", "the", "place", "with", "byob", "2", "miles", "from", "here"], "ner_tags": ["O", "O", "O", "O", "O", "B-Amenity", "B-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "where is the reading station coffee depot on norfolk ave do they serve breakfast", "tokens": ["where", "is", "the", "reading", "station", "coffee", "depot", "on", "norfolk", "ave", "do", "they", "serve", "breakfast"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "O", "O", "O", "B-Cuisine"]}
{"sentence": "where is the sportsway bar in melrose", "tokens": ["where", "is", "the", "sportsway", "bar", "in", "melrose"], "ner_tags": ["O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location"]}
{"sentence": "where is there a fatz with interesting people around", "tokens": ["where", "is", "there", "a", "fatz", "with", "interesting", "people", "around"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "O", "B-Amenity", "I-Amenity", "O"]}
{"sentence": "where is there a good mid priced restaurant near 4 th street that serves appetizers", "tokens": ["where", "is", "there", "a", "good", "mid", "priced", "restaurant", "near", "4", "th", "street", "that", "serves", "appetizers"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "B-Price", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "O", "O", "B-Cuisine"]}
{"sentence": "where is there a great steak near here that has a buffet", "tokens": ["where", "is", "there", "a", "great", "steak", "near", "here", "that", "has", "a", "buffet"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "B-Dish", "B-Location", "I-Location", "O", "O", "O", "B-Amenity"]}
{"sentence": "where is wing stop", "tokens": ["where", "is", "wing", "stop"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "where should we go to get some great food", "tokens": ["where", "should", "we", "go", "to", "get", "some", "great", "food"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Rating", "O"]}
{"sentence": "where there a restaurant located within 1 mile from here", "tokens": ["where", "there", "a", "restaurant", "located", "within", "1", "mile", "from", "here"], "ner_tags": ["O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "wheres a good place to get tacos near central park", "tokens": ["wheres", "a", "good", "place", "to", "get", "tacos", "near", "central", "park"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Cuisine", "B-Location", "I-Location", "I-Location"]}
{"sentence": "wheres a kid friendly diner thats within an hour of here", "tokens": ["wheres", "a", "kid", "friendly", "diner", "thats", "within", "an", "hour", "of", "here"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity", "B-Cuisine", "O", "B-Location", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "wheres closest mcdonalds", "tokens": ["wheres", "closest", "mcdonalds"], "ner_tags": ["O", "B-Location", "B-Restaurant_Name"]}
{"sentence": "wheres papa johns", "tokens": ["wheres", "papa", "johns"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "wheres the best korean restaurant in the area", "tokens": ["wheres", "the", "best", "korean", "restaurant", "in", "the", "area"], "ner_tags": ["O", "O", "B-Rating", "B-Cuisine", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "wheres the closes chicken place", "tokens": ["wheres", "the", "closes", "chicken", "place"], "ner_tags": ["O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "wheres the closest 5 star restaurant located", "tokens": ["wheres", "the", "closest", "5", "star", "restaurant", "located"], "ner_tags": ["O", "O", "B-Location", "B-Rating", "I-Rating", "O", "O"]}
{"sentence": "wheres the closest bar", "tokens": ["wheres", "the", "closest", "bar"], "ner_tags": ["O", "O", "B-Location", "B-Cuisine"]}
{"sentence": "wheres the closest pizza place", "tokens": ["wheres", "the", "closest", "pizza", "place"], "ner_tags": ["O", "O", "B-Location", "B-Dish", "O"]}
{"sentence": "wheres the italian restaurant downtown thats open until midnight", "tokens": ["wheres", "the", "italian", "restaurant", "downtown", "thats", "open", "until", "midnight"], "ner_tags": ["O", "O", "B-Cuisine", "O", "B-Location", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "wheres the local el penllo arriba peru", "tokens": ["wheres", "the", "local", "el", "penllo", "arriba", "peru"], "ner_tags": ["O", "O", "B-Location", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "wheres the local soup and sandwich joint", "tokens": ["wheres", "the", "local", "soup", "and", "sandwich", "joint"], "ner_tags": ["O", "O", "B-Location", "B-Cuisine", "I-Cuisine", "I-Cuisine", "O"]}
{"sentence": "wheres the nearest french place to eat", "tokens": ["wheres", "the", "nearest", "french", "place", "to", "eat"], "ner_tags": ["O", "O", "B-Location", "B-Cuisine", "O", "O", "O"]}
{"sentence": "wheres the nearest italian restaurant", "tokens": ["wheres", "the", "nearest", "italian", "restaurant"], "ner_tags": ["O", "O", "B-Location", "B-Cuisine", "O"]}
{"sentence": "wheres the nearest pizza restaurant to the amc white flint movie theater", "tokens": ["wheres", "the", "nearest", "pizza", "restaurant", "to", "the", "amc", "white", "flint", "movie", "theater"], "ner_tags": ["O", "O", "B-Location", "B-Cuisine", "O", "O", "O", "B-Location", "I-Location", "I-Location", "I-Location", "I-Location"]}
{"sentence": "wheres the nearest restaurant", "tokens": ["wheres", "the", "nearest", "restaurant"], "ner_tags": ["O", "O", "B-Location", "O"]}
{"sentence": "wheres the nearest restaurant with a 3 star rating or better that serves a hamburger for under five dollars", "tokens": ["wheres", "the", "nearest", "restaurant", "with", "a", "3", "star", "rating", "or", "better", "that", "serves", "a", "hamburger", "for", "under", "five", "dollars"], "ner_tags": ["O", "O", "B-Location", "O", "O", "O", "B-Rating", "I-Rating", "I-Rating", "I-Rating", "I-Rating", "O", "O", "O", "B-Dish", "O", "B-Price", "I-Price", "I-Price"]}
{"sentence": "wheres the nearest restaurant with a childrens menu", "tokens": ["wheres", "the", "nearest", "restaurant", "with", "a", "childrens", "menu"], "ner_tags": ["O", "O", "B-Location", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "wheres the serborn inn located", "tokens": ["wheres", "the", "serborn", "inn", "located"], "ner_tags": ["O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "O"]}
{"sentence": "which authentic mexican restaurants offer complementary chips and salsa", "tokens": ["which", "authentic", "mexican", "restaurants", "offer", "complementary", "chips", "and", "salsa"], "ner_tags": ["O", "B-Cuisine", "I-Cuisine", "O", "O", "B-Dish", "I-Dish", "I-Dish", "I-Dish"]}
{"sentence": "which five star italian restaurants in manattan have the best reviews", "tokens": ["which", "five", "star", "italian", "restaurants", "in", "manattan", "have", "the", "best", "reviews"], "ner_tags": ["O", "B-Rating", "I-Rating", "B-Cuisine", "O", "O", "B-Location", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "which local restaurant serves only seafood", "tokens": ["which", "local", "restaurant", "serves", "only", "seafood"], "ner_tags": ["O", "B-Location", "O", "O", "B-Amenity", "B-Cuisine"]}
{"sentence": "which nearby restaurant serves the best steak", "tokens": ["which", "nearby", "restaurant", "serves", "the", "best", "steak"], "ner_tags": ["O", "B-Location", "O", "O", "O", "B-Rating", "B-Dish"]}
{"sentence": "which parks are kid friendly", "tokens": ["which", "parks", "are", "kid", "friendly"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "which pizza places will deliver to me", "tokens": ["which", "pizza", "places", "will", "deliver", "to", "me"], "ner_tags": ["O", "B-Cuisine", "O", "O", "B-Amenity", "O", "O"]}
{"sentence": "which places serve large portions", "tokens": ["which", "places", "serve", "large", "portions"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "which restaurant can i get to within 5 minutes which serves healthy portions", "tokens": ["which", "restaurant", "can", "i", "get", "to", "within", "5", "minutes", "which", "serves", "healthy", "portions"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "which restaurant has a better rating on yelp sakura or fuji ya", "tokens": ["which", "restaurant", "has", "a", "better", "rating", "on", "yelp", "sakura", "or", "fuji", "ya"], "ner_tags": ["O", "O", "O", "O", "B-Rating", "I-Rating", "O", "O", "B-Restaurant_Name", "O", "B-Restaurant_Name", "I-Restaurant_Name"]}
{"sentence": "which restaurant has a smoking section", "tokens": ["which", "restaurant", "has", "a", "smoking", "section"], "ner_tags": ["O", "O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "which restaurant has fried pickles for appetizers", "tokens": ["which", "restaurant", "has", "fried", "pickles", "for", "appetizers"], "ner_tags": ["O", "O", "O", "B-Dish", "I-Dish", "O", "B-Amenity"]}
{"sentence": "which restaurant in my city has the highest reviews", "tokens": ["which", "restaurant", "in", "my", "city", "has", "the", "highest", "reviews"], "ner_tags": ["O", "O", "O", "B-Location", "I-Location", "O", "O", "B-Rating", "I-Rating"]}
{"sentence": "which restaurant is open late and has good prices on espressos", "tokens": ["which", "restaurant", "is", "open", "late", "and", "has", "good", "prices", "on", "espressos"], "ner_tags": ["O", "O", "O", "B-Hours", "I-Hours", "O", "O", "B-Price", "O", "O", "B-Dish"]}
{"sentence": "which restaurants are considered vegetarian in my city", "tokens": ["which", "restaurants", "are", "considered", "vegetarian", "in", "my", "city"], "ner_tags": ["O", "O", "O", "O", "B-Cuisine", "B-Location", "I-Location", "I-Location"]}
{"sentence": "which restaurants are open after midnight on a wednesday", "tokens": ["which", "restaurants", "are", "open", "after", "midnight", "on", "a", "wednesday"], "ner_tags": ["O", "O", "O", "B-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours", "I-Hours"]}
{"sentence": "which restaurants around my city have the best deserts", "tokens": ["which", "restaurants", "around", "my", "city", "have", "the", "best", "deserts"], "ner_tags": ["O", "O", "B-Location", "I-Location", "I-Location", "O", "O", "B-Rating", "B-Cuisine"]}
{"sentence": "which restaurants have playgrounds in them", "tokens": ["which", "restaurants", "have", "playgrounds", "in", "them"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "O"]}
{"sentence": "which restaurants near here are open till midnight", "tokens": ["which", "restaurants", "near", "here", "are", "open", "till", "midnight"], "ner_tags": ["O", "O", "B-Location", "I-Location", "O", "B-Hours", "I-Hours", "I-Hours"]}
{"sentence": "which restaurants offer outdoor dining", "tokens": ["which", "restaurants", "offer", "outdoor", "dining"], "ner_tags": ["O", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "which restaurants serving grass fed beef steaks have the best prices", "tokens": ["which", "restaurants", "serving", "grass", "fed", "beef", "steaks", "have", "the", "best", "prices"], "ner_tags": ["O", "O", "O", "B-Dish", "I-Dish", "I-Dish", "I-Dish", "O", "O", "B-Price", "O"]}
{"sentence": "which tim hortons is closest to detroit", "tokens": ["which", "tim", "hortons", "is", "closest", "to", "detroit"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "whixh restaurant has delivery in washington court for a reasonable price", "tokens": ["whixh", "restaurant", "has", "delivery", "in", "washington", "court", "for", "a", "reasonable", "price"], "ner_tags": ["O", "O", "O", "B-Amenity", "O", "B-Location", "I-Location", "O", "O", "B-Price", "O"]}
{"sentence": "who has good burgers around here", "tokens": ["who", "has", "good", "burgers", "around", "here"], "ner_tags": ["O", "O", "B-Rating", "B-Dish", "B-Location", "I-Location"]}
{"sentence": "who has happy hour right now", "tokens": ["who", "has", "happy", "hour", "right", "now"], "ner_tags": ["O", "O", "B-Amenity", "I-Amenity", "B-Hours", "I-Hours"]}
{"sentence": "who has the best lobster in town", "tokens": ["who", "has", "the", "best", "lobster", "in", "town"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Dish", "B-Location", "I-Location"]}
{"sentence": "who has the best pizza that takes credit cards", "tokens": ["who", "has", "the", "best", "pizza", "that", "takes", "credit", "cards"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Dish", "O", "O", "B-Amenity", "I-Amenity"]}
{"sentence": "who has the best selection of micro brew and imported beers", "tokens": ["who", "has", "the", "best", "selection", "of", "micro", "brew", "and", "imported", "beers"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "O", "B-Dish", "I-Dish", "O", "B-Dish", "I-Dish"]}
{"sentence": "who makes the absolute best sausage", "tokens": ["who", "makes", "the", "absolute", "best", "sausage"], "ner_tags": ["O", "O", "O", "B-Rating", "I-Rating", "B-Dish"]}
{"sentence": "who makes the best burgers in town", "tokens": ["who", "makes", "the", "best", "burgers", "in", "town"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Dish", "B-Location", "I-Location"]}
{"sentence": "who makes the best chicken wings in town", "tokens": ["who", "makes", "the", "best", "chicken", "wings", "in", "town"], "ner_tags": ["O", "O", "O", "B-Rating", "B-Dish", "I-Dish", "B-Location", "I-Location"]}
{"sentence": "who serves cali food", "tokens": ["who", "serves", "cali", "food"], "ner_tags": ["O", "O", "B-Cuisine", "O"]}
{"sentence": "who serves the best wings in this town", "tokens": ["who", "serves", "the", "best", "wings", "in", "this", "town"], "ner_tags": ["B-Cuisine", "I-Cuisine", "O", "B-Cuisine", "B-Dish", "B-Location", "I-Location", "I-Location"]}
{"sentence": "who will give me the best priced party subs in town", "tokens": ["who", "will", "give", "me", "the", "best", "priced", "party", "subs", "in", "town"], "ner_tags": ["O", "O", "O", "O", "O", "B-Rating", "O", "B-Dish", "I-Dish", "O", "B-Location"]}
{"sentence": "will i be able to find a romantic restaurant for my date tonight", "tokens": ["will", "i", "be", "able", "to", "find", "a", "romantic", "restaurant", "for", "my", "date", "tonight"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Amenity", "O", "O", "O", "O", "B-Hours"]}
{"sentence": "will waffle house accept a prepaid visa gift card", "tokens": ["will", "waffle", "house", "accept", "a", "prepaid", "visa", "gift", "card"], "ner_tags": ["O", "B-Restaurant_Name", "I-Restaurant_Name", "O", "O", "B-Amenity", "I-Amenity", "I-Amenity", "I-Amenity"]}
{"sentence": "yes please get me mcdonalds phone number in patchogue new york", "tokens": ["yes", "please", "get", "me", "mcdonalds", "phone", "number", "in", "patchogue", "new", "york"], "ner_tags": ["O", "O", "O", "O", "B-Restaurant_Name", "O", "O", "O", "B-Location", "I-Location", "I-Location"]}
{"sentence": "yes the new diner on south street please", "tokens": ["yes", "the", "new", "diner", "on", "south", "street", "please"], "ner_tags": ["O", "O", "O", "B-Cuisine", "O", "B-Location", "I-Location", "O"]}
{"sentence": "yes we need some chicken for our new diet so chik fa lay it is", "tokens": ["yes", "we", "need", "some", "chicken", "for", "our", "new", "diet", "so", "chik", "fa", "lay", "it", "is"], "ner_tags": ["O", "O", "O", "O", "B-Dish", "O", "O", "O", "O", "O", "B-Restaurant_Name", "I-Restaurant_Name", "I-Restaurant_Name", "O", "O"]}
{"sentence": "you can help me with some onion rings", "tokens": ["you", "can", "help", "me", "with", "some", "onion", "rings"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Dish", "I-Dish"]}
