{"prompts": ["Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n2. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n3. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the best {{true crime}} movie released in 1964?\"\nText Span: \"true crime\"\n\n2. Query: \"Can you show me the trailer for the {{awe-inducing science fiction}} movie directed by Steven Spielberg\"\nText Span: \"awe-inducing science fiction\"\n\n3. Query: \"can you recommend a {{road}} movie with a thrilling plot\"\nText Span: \"road\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n2. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n3. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Are there any {{historical}} films featuring a strong female lead like Single Ladies\"\nText Span: \"historical\"\n\n2. Query: \"who directed the {{Children's}} movie Frozen?\"\nText Span: \"Children's\"\n\n3. Query: \"I'm looking for a {{thriller}} movie from 2021 with the song Crazy in Love or Rhythm Nation in it.\"\nText Span: \"thriller\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n2. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you show me an {{adventure}} movie set in a war-torn country from 1989?\"\nText Span: \"adventure\"\n\n2. Query: \"What {{biographical}} films has Matthew McConaughey starred in?\"\nText Span: \"biographical\"\n\n3. Query: \"Tell me about a film released in 1999 with a title that has a {{historical}} setting.\"\nText Span: \"historical\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n3. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"I want to watch a {{thriller}} directed by Tom Cruise's favorite director.\"\nText Span: \"thriller\"\n\n2. Query: \"Can you recommend a great {{emotional}} movie starring Benedict Cumberbatch?\"\nText Span: \"emotional\"\n\n3. Query: \"Could you recommend a movie from 2026 with an {{action genre}}?\"\nText Span: \"action genre\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n2. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n3. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Is the movie Bullitt, starring Steve McQueen, based on a {{True crime}} story with authentic dialogue?\"\nText Span: \"True crime\"\n\n2. Query: \"What {{Reflective}} movie directed by Christopher Nolan has the song Time by Hans Zimmer?\"\nText Span: \"Reflective\"\n\n3. Query: \"I'm looking for a {{comedy}} film directed by Todd Phillips.\"\nText Span: \"comedy\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n2. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Show me the trailer for the new {{action movie}} directed by Kathryn Bigelow\"\nText Span: \"action movie\"\n\n2. Query: \"Could you recommend a movie from 1987 with a {{thrilling}} plot and an intense character?\"\nText Span: \"thrilling\"\n\n3. Query: \"I'm looking for a stoner {{comedy}} released in the last 5 years with great viewers' ratings\"\nText Span: \"comedy\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n2. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"who directed the {{action}} movie with the character john wick\"\nText Span: \"action\"\n\n2. Query: \"Who directed the {{action film}} that features the song Eye of the Tiger?\"\nText Span: \"action film\"\n\n3. Query: \"What is the best {{musical}} film with a romantic plot and a great soundtrack?\"\nText Span: \"musical\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n3. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Who directed the {{sci-fi}} film set in 2030 with a Viewers' Rating of 9 or above?\"\nText Span: \"sci-fi\"\n\n2. Query: \"Can you recommend a movie with a song that features a {{jungle}} theme?\"\nText Span: \"jungle\"\n\n3. Query: \"Can you recommend any {{Period}} films set in the 1920s?\"\nText Span: \"Period\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n2. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the viewers' rating for the movie directed by Wong Kar-wai that explores {{thought-provoking themes}}?\"\nText Span: \"thought-provoking themes\"\n\n2. Query: \"What are the highlights of the latest {{thriller}} movie directed by Christopher Nolan?\"\nText Span: \"thriller\"\n\n3. Query: \"What movie features a {{Dance competition}} and compelling plot twists\"\nText Span: \"Dance competition\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n2. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n3. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend an {{uplifting}} movie directed by Francis Ford Coppola?\"\nText Span: \"uplifting\"\n\n2. Query: \"can you recommend a {{silent}} film with Jake Gyllenhaal that has a NC-17 rating\"\nText Span: \"silent\"\n\n3. Query: \"Can you recommend a {{martial arts}} movie from the 1960s?\"\nText Span: \"martial arts\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n2. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a highly rated {{comedy}} from 2013?\"\nText Span: \"comedy\"\n\n2. Query: \"Can you recommend a must-see {{Film noir}}?\"\nText Span: \"Film noir\"\n\n3. Query: \"What is the best {{adventure}} film released in 1975?\"\nText Span: \"adventure\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n2. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n3. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"is there a {{historical}} film set during the Battle of Waterloo\"\nText Span: \"historical\"\n\n2. Query: \"What year was 12 Angry Men, the {{classic legal drama}}, released?\"\nText Span: \"classic legal drama\"\n\n3. Query: \"What {{indie}} films were released in 2020 directed by Benedict Cumberbatch?\"\nText Span: \"indie\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n2. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Is the Avengers movie considered a must-see {{action}} film?\"\nText Span: \"action\"\n\n2. Query: \"Show me a {{coming-of-age journey}} film with a high viewers' rating\"\nText Span: \"coming-of-age journey\"\n\n3. Query: \"Can you recommend a {{Gothic}} film with a quest for revenge and the discovery of a new world?\"\nText Span: \"Gothic\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n2. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n3. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend any {{Disaster}} movies from 1984 with an apocalyptic event?\"\nText Span: \"Disaster\"\n\n2. Query: \"can you recommend a {{buddy film}} that is appropriate for all ages\"\nText Span: \"buddy film\"\n\n3. Query: \"Can you recommend a movie with a 5-star viewers' rating that falls under the {{Fantasy}} genre\"\nText Span: \"Fantasy\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n2. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What's your favorite {{war}} movie that was released before 1960?\"\nText Span: \"war\"\n\n2. Query: \"What is the MPAA rating of the {{historical}} movie starring Tom Hanks?\"\nText Span: \"historical\"\n\n3. Query: \"Can you recommend a {{disaster}} movie similar to Jurassic Park?\"\nText Span: \"disaster\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n2. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a {{psycho}} film with an impactful soundtrack?\"\nText Span: \"psycho\"\n\n2. Query: \"Can I watch a {{buddy comedy}} movie with Samuel L. Jackson that has the song Sweet Child O' Mine\"\nText Span: \"buddy comedy\"\n\n3. Query: \"Can you recommend a {{Suspense}} movie suitable for all ages?\"\nText Span: \"Suspense\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n3. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"I want to watch a {{superhero}} film, do you have any recommendations?\"\nText Span: \"superhero\"\n\n2. Query: \"Is there a {{cult}} movie with a rating of 5 out of 10?\"\nText Span: \"cult\"\n\n3. Query: \"Are there any {{zombie apocalypse}} movies with a found footage style?\"\nText Span: \"zombie apocalypse\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n3. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Show me an {{Action-comedy}} film with Keanu Reeves\"\nText Span: \"Action-comedy\"\n\n2. Query: \"Can you recommend a {{classic movie}} with the song Livin' on a Prayer?\"\nText Span: \"classic movie\"\n\n3. Query: \"Show me the trailer of the {{action}} movie The Perfect Weapon.\"\nText Span: \"action\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n2. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Show me a trailer for the 2008 {{action}} movie directed by Christopher Nolan.\"\nText Span: \"action\"\n\n2. Query: \"What {{epic}} movie starring Matt Damon was released in 2015?\"\nText Span: \"epic\"\n\n3. Query: \"show me a film teaser for the new {{action}} movie\"\nText Span: \"action\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n3. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a {{classic movie}} featuring Scarlet O'Hara?\"\nText Span: \"classic movie\"\n\n2. Query: \"Show me a list of movies from the 1990s with a {{science fiction}} genre and a plot involving time travel\"\nText Span: \"science fiction\"\n\n3. Query: \"Can you recommend a movie with {{stunning costume design}} and a compelling plot about overcoming adversity?\"\nText Span: \"stunning costume design\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n2. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n3. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a good moving about {{love and war}} set in 1945?\"\nText Span: \"love and war\"\n\n2. Query: \"What is the best {{thriller}} movie from the 90s according to the critics' reviews?\"\nText Span: \"thriller\"\n\n3. Query: \"who directed the movie Inception and what is its {{genre}}\"\nText Span: \"genre\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n2. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n3. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What {{Adventure}} movie features Charlize Theron as the main character?\"\nText Span: \"Adventure\"\n\n2. Query: \"Is Cary Joji Fukunaga known for directing any {{political}} films?\"\nText Span: \"political\"\n\n3. Query: \"Could you recommend a restricted {{Animation}} film from 1989 with a high viewers' rating?\"\nText Span: \"Animation\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n2. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n3. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a movie with an {{Artistic}} plot and a 0-star rating?\"\nText Span: \"Artistic\"\n\n2. Query: \"Can you recommend a movie about {{ocean exploration}} from the 2000s?\"\nText Span: \"ocean exploration\"\n\n3. Query: \"Can you recommend a hilarious {{comedy}} film from the 2010s?\"\nText Span: \"comedy\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n3. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Are there any {{action}} movies about an alien invasion from the year 2010?\"\nText Span: \"action\"\n\n2. Query: \"Could you recommend a {{thought-provoking}} film directed by Christopher Nolan\"\nText Span: \"thought-provoking\"\n\n3. Query: \"Can you recommend a movie with an {{adventurous}} plot and a high viewers' rating?\"\nText Span: \"adventurous\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n2. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend an adrenaline-pumping {{action movie}} released in the 2000s?\"\nText Span: \"action movie\"\n\n2. Query: \"what are the top-rated {{horror movies}} directed by Wes Craven\"\nText Span: \"horror movies\"\n\n3. Query: \"Is there a {{family-friendly}} movie with a TV-Y rating featuring the song 'Take Me Out to the Ball Game'\"\nText Span: \"family-friendly\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n3. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"I would like to watch a {{medical movie}} with a song performed by Piano Man.\"\nText Span: \"medical movie\"\n\n2. Query: \"Can you recommend a {{classic}} movie with an international plot that was a popular hit in the 70s?\"\nText Span: \"classic\"\n\n3. Query: \"Can you recommend any {{post-apocalyptic}} movies from 1998?\"\nText Span: \"post-apocalyptic\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n2. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a {{classic}} sci-fi movie with a five-star rating?\"\nText Span: \"classic\"\n\n2. Query: \"Who directed the {{action}} movie with intense fight scenes and a great soundtrack?\"\nText Span: \"action\"\n\n3. Query: \"Who directed The Hurt Locker and what is the {{genre}} of the film?\"\nText Span: \"genre\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n2. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n3. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"show me a {{Legal}} film directed by Quentin Tarantino\"\nText Span: \"Legal\"\n\n2. Query: \"Is Reese Witherspoon in any {{space opera}} films like Million Dollar Baby?\"\nText Span: \"space opera\"\n\n3. Query: \"Who directed the movie about a young ballerina looking for her big break in the world of {{dance}}?\"\nText Span: \"dance\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n3. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"what is the top-rated {{action}} movie of the 1990s Music \"\nText Span: \"action\"\n\n2. Query: \"Show me a movie from 1961 in the {{drama}} genre featuring a strong female character\"\nText Span: \"drama\"\n\n3. Query: \"Can you recommend an {{epic quest}} movie similar to The Lord of the Rings?\"\nText Span: \"epic quest\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n3. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a {{comedy}} movie with GP and Owen Wilson?\"\nText Span: \"comedy\"\n\n2. Query: \"Can you recommend an {{unsettling}} film to watch?\"\nText Span: \"unsettling\"\n\n3. Query: \"Can you recommend an engrossing movie with a strong {{Sport}} theme?\"\nText Span: \"Sport\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n2. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n3. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What {{slapstick comedies}} were released in 2019 that are considered must-see?\"\nText Span: \"slapstick comedies\"\n\n2. Query: \"Can you tell me about any {{horror}} movies related to cybercrime?\"\nText Span: \"horror\"\n\n3. Query: \"Show me a {{biography}} film with Tom Cruise where they dance to Dancing in the Street\"\nText Span: \"biography\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n3. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you provide a preview snippet for the new {{science fiction}} movie that explores parallel universe exploration?\"\nText Span: \"science fiction\"\n\n2. Query: \"I'm looking for a {{comedy}} film similar to American Pie, but with a different cast.\"\nText Span: \"comedy\"\n\n3. Query: \"Is there a classic film with Al Pacino that features a famous line about {{organized crime}}?\"\nText Span: \"organized crime\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n3. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a movie with {{Outstanding animation}}?\"\nText Span: \"Outstanding animation\"\n\n2. Query: \"Can you recommend any M/PG rated movies that are considered {{propaganda}}?\"\nText Span: \"propaganda\"\n\n3. Query: \"Show me the trailer for the latest {{action}} movie featuring Dwayne Johnson\"\nText Span: \"action\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n3. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"what are the most popular {{horror}} movies from the 1980s?\"\nText Span: \"horror\"\n\n2. Query: \"Show me a {{sport}} movie from the 90s with an intense plot and a catchy trailer.\"\nText Span: \"sport\"\n\n3. Query: \"Who are the actors in the latest blockbuster {{action}} film set in space?\"\nText Span: \"action\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n2. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n3. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Is there a fierce {{action}} movie with a high viewers' rating?\"\nText Span: \"action\"\n\n2. Query: \"I want to watch a {{romantic}} movie with Rachel McAdams. Can you suggest one?\"\nText Span: \"romantic\"\n\n3. Query: \"Could you recommend a {{chilling}} horror film with Charlize Theron as the main lead\"\nText Span: \"chilling\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n3. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend any movies with {{world-building}} that are suitable for adults?\"\nText Span: \"world-building\"\n\n2. Query: \"What historical film did an {{indie}} director, like Tarantino, direct?\"\nText Span: \"indie\"\n\n3. Query: \"Can you recommend any {{Paranormal}} movies with C+ rating?\"\nText Span: \"Paranormal\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n2. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n3. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Could you recommend an exciting and thrilling {{action film}} directed by Christopher Nolan?\"\nText Span: \"action film\"\n\n2. Query: \"Can you show me the green band trailer for the new {{stellar}} sci-fi movie directed by Christopher Nolan\"\nText Span: \"stellar\"\n\n3. Query: \"Is there a {{Mockumentary}} film from 1999 that is highly acclaimed?\"\nText Span: \"Mockumentary\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n2. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n3. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is a good {{mockbuster}} movie directed by Clint Eastwood?\"\nText Span: \"mockbuster\"\n\n2. Query: \"What is a movie with {{Stunning visuals}} that came out in 2008?\"\nText Span: \"Stunning visuals\"\n\n3. Query: \"What is the viewers' rating of the {{historical}} film directed by Roman Polanski?\"\nText Span: \"historical\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n3. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a {{family-friendly}} movie rated PG directed by Stephen Daldry\"\nText Span: \"family-friendly\"\n\n2. Query: \"How many {{disaster}} movies were released in X year?\"\nText Span: \"disaster\"\n\n3. Query: \"What {{Experimental}} films were released in 1950\"\nText Span: \"Experimental\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n2. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n3. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"show me a {{comedy}} movie with Adam Sandler and also a romantic film like Dirty Dancing\"\nText Span: \"comedy\"\n\n2. Query: \"Can you recommend a {{screwball}} comedy directed by Martin Scorsese?\"\nText Span: \"screwball\"\n\n3. Query: \"What are some coming attractions for the new {{Art heist}} movies?\"\nText Span: \"Art heist\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n2. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the best movie with a {{dramatic}} plot and great music?\"\nText Span: \"dramatic\"\n\n2. Query: \"Who directed the {{action movie}} in which Ron Weasley appeared?\"\nText Span: \"action movie\"\n\n3. Query: \"Is there any {{LGBTQ+}} character in the Batman film?\"\nText Span: \"LGBTQ+\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n2. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Did Jason Momoa star in any {{action}} films with the song Lose Yourself?\"\nText Span: \"action\"\n\n2. Query: \"Are there any {{Coming-of-age}} movies with the song 'Lucy in the Sky with Diamonds'?\"\nText Span: \"Coming-of-age\"\n\n3. Query: \"Can you recommend any {{Action}} movies with a high viewers' rating?\"\nText Span: \"Action\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n2. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Is there a movie from 1993 with a high Viewers' Rating about {{Virtual reality}}?\"\nText Span: \"Virtual reality\"\n\n2. Query: \"What {{historical}} films directed by Steven Spielberg have a TV-Y7 rating?\"\nText Span: \"historical\"\n\n3. Query: \"Can you recommend a {{medical}} movie featuring Zach Galifianakis?\"\nText Span: \"medical\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n3. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Who is the director of the {{romantic comedy}} with Julia Roberts and Hugh Grant?\"\nText Span: \"romantic comedy\"\n\n2. Query: \"Can you recommend a {{classic}} film directed by Alfred Hitchcock\"\nText Span: \"classic\"\n\n3. Query: \"Can you recommend a good {{action}} movie from the 1980s NR-17 ?\"\nText Span: \"action\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Genre.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type genre\n- (B). The span contains a named genre entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not genre\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nNamed genre entities are broad and well-recognized movie categories, such as \"documentary\" and \"comedy\". Style descriptors such as \"funny\", \"musical\" and \"animated\" are also named genre entities. Film studios such as \"Marvel\" and \"Disney\" are also named genre entities.\nYou should always drop starting evaluative descriptors such as \"new\" and \"good\" and trailing \"movie\" or \"film\" words from named genre entity spans.\nEnsure to distinguish named genre entities from named plot entities. Named plot entities are specific themes, topics, or elements of a movie storyline, such as \"space exploration\" and \"zombie\".\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Who directed the {{psychological thriller}} \"Black Swan\"?'\nText Span: \"psychological thriller\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you provide a list of {{musical movies}}\"\nText Span: \"musical movies\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"musical\".\n\n3. Query: \"Name a famous [{{heist}}] movie from the 2000s\"\nText Span: \"heist\"\nLabel: (C). Wrong Type. The correct entity type is Plot.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a movie with {{Supernatural powers}} as the main theme?\"\nText Span: \"Supernatural powers\"\n\n2. Query: \"Who directed the highly-rated {{action movie}} from the 90s starring Brad Pitt?\"\nText Span: \"action movie\"\n\n3. Query: \"Can you recommend a good {{action}} movie involving a bank robbery scene?\"\nText Span: \"action\""]}