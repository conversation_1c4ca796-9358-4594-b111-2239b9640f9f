﻿# -*- coding: utf-8 -*-
"""
统计指标计算模块
用于RQ1: 数据集统计与分析
"""

import json
import numpy as np
from collections import Counter, defaultdict
from scipy import stats
from typing import Dict, List, Tuple, Any
import jieba

def calculate_dataset_statistics(dataset: List[Dict]) -> Dict[str, Any]:
    """计算数据集的基本统计信息"""
    stats_result = {
        "total_records": len(dataset),
        "total_entities": 0,
        "entity_types": set(),
        "sentence_lengths": [],
        "entity_counts_per_sentence": [],
        "vocabulary_stats": {},
        "entity_density": {},
        "raw_data": dataset  # 添加原始数据集
    }
    
    all_words = []
    entity_type_counts = Counter()
    
    for item in dataset:
        text = item["text"]
        labels = item.get("label", [])
        
        # 句子长度统计
        stats_result["sentence_lengths"].append(len(text))
        
        # 实体统计
        entity_count = len(labels)
        stats_result["total_entities"] += entity_count
        stats_result["entity_counts_per_sentence"].append(entity_count)
        
        # 实体类型统计
        for label in labels:
            entity_type = label.get("type")
            if entity_type:
                stats_result["entity_types"].add(entity_type)
                entity_type_counts[entity_type] += 1
        
        # 词汇统计
        words = list(jieba.cut(text))
        all_words.extend(words)
    
    # 计算词汇统计
    word_counts = Counter(all_words)
    stats_result["vocabulary_stats"] = {
        "total_words": len(all_words),
        "unique_words": len(word_counts),
        "vocabulary_richness": len(word_counts) / len(all_words) if all_words else 0,
        "most_common_words": word_counts.most_common(10)
    }
    
    # 计算实体密度
    stats_result["entity_density"] = {
        "entities_per_sentence": stats_result["total_entities"] / len(dataset) if dataset else 0,
        "entities_per_word": stats_result["total_entities"] / len(all_words) if all_words else 0,
        "entity_type_distribution": dict(entity_type_counts)
    }
    
    # 转换set为list以便JSON序列化
    stats_result["entity_types"] = list(stats_result["entity_types"])
    
    return stats_result

def compare_distributions(original_stats: Dict, generated_stats: Dict) -> Dict[str, Any]:
    """比较原始数据集和生成数据集的分布差异"""
    comparison = {
        "size_comparison": {
            "original_size": original_stats["total_records"],
            "generated_size": generated_stats["total_records"],
            "size_ratio": generated_stats["total_records"] / original_stats["total_records"] if original_stats["total_records"] > 0 else 0
        },
        "entity_distribution_comparison": {},
        "sentence_length_comparison": {},
        "vocabulary_comparison": {}
    }
    
    # 实体分布比较
    orig_entity_dist = original_stats["entity_density"]["entity_type_distribution"]
    gen_entity_dist = generated_stats["entity_density"]["entity_type_distribution"]
    
    all_entity_types = set(orig_entity_dist.keys()) | set(gen_entity_dist.keys())
    
    for entity_type in all_entity_types:
        orig_count = orig_entity_dist.get(entity_type, 0)
        gen_count = gen_entity_dist.get(entity_type, 0)
        
        # 计算比率时处理特殊情况
        if orig_count == 0:
            if gen_count == 0:
                ratio = 1.0  # 两者都为0，认为比率为1
            else:
                ratio = -1.0  # 原始为0但生成不为0，使用-1表示新增
        else:
            ratio = gen_count / orig_count  # 正常计算比率
        
        comparison["entity_distribution_comparison"][entity_type] = {
            "original_count": orig_count,
            "generated_count": gen_count,
            "difference": gen_count - orig_count,
            "ratio": ratio,
            "status": "unchanged" if ratio == 1.0 else "new" if ratio == -1.0 else "increased" if ratio > 1.0 else "decreased"
        }
    
    # 句子长度分布比较
    comparison["sentence_length_comparison"] = {
        "original_mean": float(np.mean(original_stats["sentence_lengths"])),
        "generated_mean": float(np.mean(generated_stats["sentence_lengths"])),
        "original_std": float(np.std(original_stats["sentence_lengths"])),
        "generated_std": float(np.std(generated_stats["sentence_lengths"])),
        "original_median": float(np.median(original_stats["sentence_lengths"])),
        "generated_median": float(np.median(generated_stats["sentence_lengths"]))
    }
    
    # 词汇统计比较
    comparison["vocabulary_comparison"] = {
        "original_vocabulary_richness": float(original_stats["vocabulary_stats"]["vocabulary_richness"]),
        "generated_vocabulary_richness": float(generated_stats["vocabulary_stats"]["vocabulary_richness"]),
        "original_unique_words": int(original_stats["vocabulary_stats"]["unique_words"]),
        "generated_unique_words": int(generated_stats["vocabulary_stats"]["unique_words"])
    }
    
    return comparison

def statistical_significance_test(original_data: List, generated_data: List, test_type: str = "t_test") -> Dict[str, Any]:
    """进行统计显著性检验"""
    if test_type == "t_test":
        # 独立样本t检验
        statistic, p_value = stats.ttest_ind(original_data, generated_data)
        test_name = "Independent t-test"
    elif test_type == "mann_whitney":
        # Mann-Whitney U检验（非参数）
        statistic, p_value = stats.mannwhitneyu(original_data, generated_data, alternative='two-sided')
        test_name = "Mann-Whitney U test"
    elif test_type == "ks_test":
        # Kolmogorov-Smirnov检验
        statistic, p_value = stats.ks_2samp(original_data, generated_data)
        test_name = "Kolmogorov-Smirnov test"
    elif test_type == "chi_square":
        # 卡方检验（用于分类数据）
        # 需要将数据转换为频率表
        orig_counts = Counter(original_data)
        gen_counts = Counter(generated_data)
        
        all_categories = set(orig_counts.keys()) | set(gen_counts.keys())
        orig_freq = [orig_counts.get(cat, 0) for cat in all_categories]
        gen_freq = [gen_counts.get(cat, 0) for cat in all_categories]
        
        statistic, p_value = stats.chisquare(gen_freq, orig_freq)
        test_name = "Chi-square test"
    else:
        raise ValueError(f"Unsupported test type: {test_type}")
    
    return {
        "test_name": test_name,
        "statistic": float(statistic),
        "p_value": float(p_value),
        "significant": p_value < 0.05,
        "interpretation": "显著差异" if p_value < 0.05 else "无显著差异"
    }

def calculate_entity_density_metrics(dataset: List[Dict]) -> Dict[str, Any]:
    """计算实体密度相关指标"""
    entity_densities = []
    entity_type_densities = defaultdict(list)
    
    for item in dataset:
        text = item["text"]
        labels = item.get("label", [])
        
        text_length = len(text)
        entity_count = len(labels)
        
        # 整体实体密度
        density = entity_count / text_length if text_length > 0 else 0
        entity_densities.append(density)
        
        # 各类型实体密度
        entity_types_in_sentence = Counter()
        for label in labels:
            entity_type = label.get("type")
            if entity_type:
                entity_types_in_sentence[entity_type] += 1
        
        for entity_type, count in entity_types_in_sentence.items():
            type_density = count / text_length if text_length > 0 else 0
            entity_type_densities[entity_type].append(type_density)
    
    # 计算统计量
    result = {
        "overall_density": {
            "mean": np.mean(entity_densities),
            "std": np.std(entity_densities),
            "median": np.median(entity_densities),
            "min": np.min(entity_densities),
            "max": np.max(entity_densities)
        },
        "entity_type_densities": {}
    }
    
    for entity_type, densities in entity_type_densities.items():
        result["entity_type_densities"][entity_type] = {
            "mean": np.mean(densities),
            "std": np.std(densities),
            "median": np.median(densities),
            "count": len(densities)
        }
    
    return result
