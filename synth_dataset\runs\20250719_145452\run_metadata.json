{"timestamp": "20250719_145452", "start_time": "2025-07-19T14:54:52.608054", "status": "completed", "config": {"dataset_path": "format-dataset/privacy_bench_small_10.json", "target_count": 10, "max_iterations": 10, "batch_size": 10, "distribution_threshold": 0.05}, "directories": {"root": "synth_dataset\\runs\\20250719_145452", "config": "synth_dataset\\runs\\20250719_145452\\config", "source": "synth_dataset\\runs\\20250719_145452\\source", "intermediate": "synth_dataset\\runs\\20250719_145452\\intermediate", "intermediate_raw": "synth_dataset\\runs\\20250719_145452\\intermediate\\raw", "intermediate_processed": "synth_dataset\\runs\\20250719_145452\\intermediate\\processed", "intermediate_by_entity": "synth_dataset\\runs\\20250719_145452\\intermediate\\by_entity", "iterations": "synth_dataset\\runs\\20250719_145452\\iterations", "strategies": "synth_dataset\\runs\\20250719_145452\\strategies", "output": "synth_dataset\\runs\\20250719_145452\\output", "evaluation": "synth_dataset\\runs\\20250719_145452\\evaluation", "logs": "synth_dataset\\runs\\20250719_145452\\logs"}, "last_updated": "2025-07-19T14:55:03.991633", "stage": "iteration", "current_iteration": 10, "max_iterations": 10, "final_dataset_path": "synth_dataset\\runs\\20250719_145452\\output\\20250719_145452_final_synthetic_dataset.json", "final_report_path": "synth_dataset\\runs\\20250719_145452\\output\\20250719_145452_synthesis_report.json", "total_iterations": 10, "final_metrics": {"original_dataset_size": 32, "final_dataset_size": 32, "total_iterations": 10, "all_entity_types": ["姓名", "年龄", "性别", "国籍", "职业", "民族", "教育背景", "婚姻状况", "政治倾向", "家庭成员", "工资数额", "投资产品", "税务记录", "信用记录", "实体资产", "交易信息", "疾病", "药物", "临床表现", "医疗程序", "过敏信息", "生育信息", "地理位置", "行程信息"], "original_distribution": {"姓名": 1, "地理位置": 3, "职业": 8, "医疗程序": 1, "疾病": 7, "药物": 8, "临床表现": 3, "国籍": 4, "民族": 1, "教育背景": 3, "性别": 2, "年龄": 2}, "target_distribution": {"姓名": 10, "年龄": 10, "性别": 10, "国籍": 10, "职业": 10, "民族": 10, "教育背景": 10, "婚姻状况": 10, "政治倾向": 10, "家庭成员": 10, "工资数额": 10, "投资产品": 10, "税务记录": 10, "信用记录": 10, "实体资产": 10, "交易信息": 10, "疾病": 10, "药物": 10, "临床表现": 10, "医疗程序": 10, "过敏信息": 10, "生育信息": 10, "地理位置": 10, "行程信息": 10}, "final_distribution": {"姓名": 1, "地理位置": 3, "职业": 8, "医疗程序": 1, "疾病": 7, "药物": 8, "临床表现": 3, "国籍": 4, "民族": 1, "教育背景": 3, "性别": 2, "年龄": 2}, "final_gap": {"姓名": 9, "年龄": 8, "性别": 8, "国籍": 6, "职业": 2, "民族": 9, "教育背景": 7, "婚姻状况": 10, "政治倾向": 10, "家庭成员": 10, "工资数额": 10, "投资产品": 10, "税务记录": 10, "信用记录": 10, "实体资产": 10, "交易信息": 10, "疾病": 3, "药物": 2, "临床表现": 7, "医疗程序": 9, "过敏信息": 10, "生育信息": 10, "地理位置": 7, "行程信息": 10}, "diversity_metrics": {"vocabulary_diversity": 1.0, "syntactic_diversity": 0.8, "semantic_diversity": 0.7, "context_diversity": 0.6, "entity_diversity": 0.75}, "distribution_passed": false, "diversity_passed": false}}