{"prompts": ["Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n2. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n5. Query: \"Who is the actor playing Spider-Man in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What movies from the {{90s}} are directed by Quentin Tarantino and fall under the crime genre?\"\nText Span: \"90s\"\n\n2. Query: \"Did Jim Carrey receive any awards for his performance in a political movie released in {{1983}}?\"\nText Span: \"1983\"\n\n3. Query: \"Can you recommend a horror movie similar to Dracula from the {{1930s}}?\"\nText Span: \"1930s\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Who is the actor playing Spider-Man in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n5. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Who directed the film released in {{1972}} starring Finn?\"\nText Span: \"1972\"\n\n2. Query: \"Can you recommend a gripping thriller film from the {{1990s}}?\"\nText Span: \"1990s\"\n\n3. Query: \"Can you recommend any movies with dynamic performances from the {{1980s}} like Top Gun?\"\nText Span: \"1980s\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n3. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"Who is the actor playing Spider-Man in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n5. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Is Princess Jasmine in any {{recent}} live-action films\"\nText Span: \"recent\"\n\n2. Query: \"Show me a review of the {{latest}} film starring Tom Hanks\"\nText Span: \"latest\"\n\n3. Query: \"What top-notch movies from the {{1980s}} should I watch?\"\nText Span: \"1980s\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"Who is the actor playing Spider-Man in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n5. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Have there been any movies released {{recently}} with the theme of dreams\"\nText Span: \"recently\"\n\n2. Query: \"Can you tell me the viewers' rating for the film released in {{1973}}?\"\nText Span: \"1973\"\n\n3. Query: \"What is the best adventure film released in {{1975}}?\"\nText Span: \"1975\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n3. Query: \"Who is the actor playing Spider-Man in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n5. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\n\n2. Query: \"What is the viewers' rating for the film starring Michelle Pfeiffer that was released in {{1987}}?\"\nText Span: \"1987\"\n\n3. Query: \"Who directed Jurassic Park and in what {{year}} was it released?\"\nText Span: \"year\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n4. Query: \"Who is the actor playing Spider-Man in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n5. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a jaw-dropping movie from {{2016}}?\"\nText Span: \"2016\"\n\n2. Query: \"Show me a trailer for the unrated Meryl Streep film released in {{2010}}.\"\nText Span: \"2010\"\n\n3. Query: \"What five-star not rated movies came out as TV-PG in {{the 90s}}\"\nText Span: \"the 90s\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Who is the actor playing Spider-<PERSON> in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n3. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n5. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Who directed the action movie Lethal Weapon and what {{year}} did it come out?\"\nText Span: \"year\"\n\n2. Query: \"What is the viewers' rating of the movie that features an apocalyptic event and was released in {{1984}}?\"\nText Span: \"1984\"\n\n3. Query: \"In the movie Moonlight, who played the role of the main character in {{2016}}?\"\nText Span: \"2016\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n2. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"Who is the actor playing Spider-Man in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n5. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a good action movie from the {{1980s}} NR-17 ?\"\nText Span: \"1980s\"\n\n2. Query: \"Can you recommend any Film Noir classics from {{1961}}?\"\nText Span: \"1961\"\n\n3. Query: \"What is the highest rated movie from {{1996}} that falls under the fantasy genre?\"\nText Span: \"1996\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Who is the actor playing Spider-<PERSON> in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n4. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n5. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a {{recent}} action movie with a strong female lead?\"\nText Span: \"recent\"\n\n2. Query: \"Can you tell me the name of the director of the movie Psycho from {{1996}}?\"\nText Span: \"1996\"\n\n3. Query: \"Can you recommend any disaster movies from {{1984}} with an apocalyptic event?\"\nText Span: \"1984\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n3. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n5. Query: \"Who is the actor playing Spider-Man in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"How many disaster movies were released in {{X}} year?\"\nText Span: \"X\"\n\n2. Query: \"Is there an R-13 movie from {{2009}} with a female protagonist that I might enjoy?\"\nText Span: \"2009\"\n\n3. Query: \"Who directed Lawrence of Arabia, and in what {{year}} was it released?\"\nText Span: \"year\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n2. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"Who is the actor playing Spider-Man in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n5. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend any highly-rated movies from {{1982}} directed by Darren Aronofsky?\"\nText Span: \"1982\"\n\n2. Query: \"what's a popular comedy film from the {{2000s}} with a high viewers' rating\"\nText Span: \"2000s\"\n\n3. Query: \"I want to watch a comedy movie from the {{1980s}}, can you recommend one?\"\nText Span: \"1980s\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n4. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n5. Query: \"Who is the actor playing Spider-Man in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"I want to watch a biographical film from {{1957}}, maybe about the loss of memory.\"\nText Span: \"1957\"\n\n2. Query: \"I'm looking for a C rated movie directed by Hayao Miyazaki with a {{recent}} trailer release.\"\nText Span: \"recent\"\n\n3. Query: \"Can you recommend a classic movie from {{1966}}?\"\nText Span: \"1966\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n2. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n5. Query: \"Who is the actor playing Spider-Man in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"I'm looking for a comedy film from {{1990}} with John Candy and a trailer featuring the song 'Ghostbusters.'\"\nText Span: \"1990\"\n\n2. Query: \"can you recommend a good survival film from the {{1990s}}\"\nText Span: \"1990s\"\n\n3. Query: \"Show me a list of movies from the {{1980s}} with Kerry Washington as an actress\"\nText Span: \"1980s\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Who is the actor playing Spider-<PERSON> in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n4. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n5. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What was the viewers' rating for the movie The Sweet Caroline released in {{1996}}?\"\nText Span: \"1996\"\n\n2. Query: \"I'm looking for a comedy movie from {{the 1990s}}, directed by Tim Burton.\"\nText Span: \"the 1990s\"\n\n3. Query: \"Please show me a trailer of the {{new}} Richard Linklater movie.\"\nText Span: \"new\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Who is the actor playing Spider-<PERSON> in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n5. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"are there any zombie movies released in {{1993}}\"\nText Span: \"1993\"\n\n2. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\n\n3. Query: \"What is the Viewers' Rating for any Adam Sandler movie released in the year {{2000}}?\"\nText Span: \"2000\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"Who is the actor playing Spider-Man in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n5. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What's the viewers' rating for the {{latest}} Quentin Tarantino film\"\nText Span: \"latest\"\n\n2. Query: \"What year did the movie Raining Men {{come out}} and what genre is it?\"\nText Span: \"come out\"\n\n3. Query: \"Show me a science fiction film released in {{2022}} with Gerard Butler\"\nText Span: \"2022\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n2. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"Who is the actor playing Spider-Man in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n5. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a movie with impeccable direction that was released in the {{1990s}}?\"\nText Span: \"1990s\"\n\n2. Query: \"what is the best-rated movie of {{1983}}?\"\nText Span: \"1983\"\n\n3. Query: \"Can you provide a preview snippet for the {{new}} science fiction movie that explores parallel universe exploration?\"\nText Span: \"new\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Who is the actor playing Spider-<PERSON> in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n5. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the plot of the {{latest}} movie featuring Denzel Washington?\"\nText Span: \"latest\"\n\n2. Query: \"Can you recommend a martial arts movie from the {{1960s}}?\"\nText Span: \"1960s\"\n\n3. Query: \"Show me a teaser video for the {{2008}} movie Iron Man\"\nText Span: \"2008\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"Who is the actor playing Spider-Man in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n5. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"who directed the top-rated movie of {{1995}}\"\nText Span: \"1995\"\n\n2. Query: \"What year was 12 Angry Men, the classic legal drama, {{released}}?\"\nText Span: \"released\"\n\n3. Query: \"What are the top three highest rated movies released in {{2020}} NR-17 ?\"\nText Span: \"2020\"", "Here is a spoken query to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Year.\nIn particular, for the given query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type year\n- (B). The span contains a named year entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not year\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named year entity must be a specific year or a year range indicating movie release, such as \"2000\" or \"last three years\".\nYou should always drop starting \"the\" word from named year entity spans.\nVague temporal descriptors such as \"latest\", \"recent\", \"recent movies\" and \"come out\" are not named entities. Release qualifiers such as \"released\", \"new release\", \"re-release\" and \"first\" are also not named entities. Ambiguous identifiers such as \"year\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is a good character-driven drama film from the {{1990s}}?\"\nText Span: \"1990s\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you tell me the year in which the movie Pulp Fiction was {{released}}\"\nText Span: \"released\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"Show me a sport movie from {{the 90s}} with an intense plot and a catchy trailer.\"\nText Span: \"the 90s\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"90s\".\n\n4. Query: \"Who is the actor playing Spider-Man in the {{latest}} movie\"\nText Span: \"latest\"\nLabel: (D). Not a Named Entity.\n\n5. Query: \"who directed the movie Aquaman and what {{year}} was it released\"\nText Span: \"year\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following span of text:\n\nQuery: \"What is the best Western movie released in {{2007}}\"\nText Span: \"2007\""]}