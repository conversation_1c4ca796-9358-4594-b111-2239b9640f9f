#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多轮标注功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.synth_data.ner_data_generation import find_entity_spans_comprehensive, find_entity_spans_single

def test_comprehensive_annotation():
    """测试多轮标注功能"""
    
    # 测试句子：包含多种实体类型
    test_sentences = [
        "张三在北京的医院工作，是一名医生。",  # 包含姓名、地理位置、职业
        "李四今年30岁，在上海投资了股票。",   # 包含姓名、年龄、地理位置、投资产品
        "王五是一名工程师，在深圳工作。",     # 包含姓名、职业、地理位置
        "赵六患有高血压，需要定期服用药物。", # 包含姓名、疾病、药物
    ]
    
    # 测试实体列表
    test_entities = {
        "姓名": ["张三", "李四", "王五", "赵六"],
        "职业": ["医生", "工程师"],
        "地理位置": ["北京", "上海", "深圳"],
        "年龄": ["30岁"],
        "投资产品": ["股票"],
        "疾病": ["高血压"],
        "药物": ["药物"]
    }
    
    print("=" * 60)
    print("测试多轮标注功能")
    print("=" * 60)
    
    for i, sentence in enumerate(test_sentences, 1):
        print(f"\n测试句子 {i}: {sentence}")
        print("-" * 40)
        
        # 测试不同的目标实体类型
        for entity_type in ["姓名", "职业", "地理位置", "年龄", "投资产品", "疾病", "药物"]:
            entities = test_entities.get(entity_type, [])
            
            print(f"\n目标实体类型: {entity_type}")
            print(f"参考实体列表: {entities}")
            
            # 测试多轮标注
            try:
                comprehensive_result = find_entity_spans_comprehensive(sentence, entities, entity_type)
                print(f"多轮标注结果: {comprehensive_result}")
            except Exception as e:
                print(f"多轮标注失败: {e}")
            
            # 测试单类型标注
            try:
                single_result = find_entity_spans_single(sentence, entities, entity_type)
                print(f"单类型标注结果: {single_result}")
            except Exception as e:
                print(f"单类型标注失败: {e}")
            
            print()

def test_annotation_methods():
    """测试不同标注方法的配置"""
    
    # 测试句子
    test_sentence = "张三在北京的医院工作，是一名医生。"
    test_entities = ["张三", "北京", "医生"]
    
    print("=" * 60)
    print("测试不同标注方法配置")
    print("=" * 60)
    
    # 临时修改配置进行测试
    import json
    from src.synth_data.ner_data_generation import ANNOTATION_METHOD
    
    print(f"当前标注方法配置: {ANNOTATION_METHOD}")
    
    # 测试多轮标注
    print(f"\n测试多轮标注 (comprehensive):")
    try:
        result = find_entity_spans_comprehensive(test_sentence, test_entities, "姓名")
        print(f"结果: {result}")
    except Exception as e:
        print(f"失败: {e}")
    
    # 测试单类型标注
    print(f"\n测试单类型标注 (single):")
    try:
        result = find_entity_spans_single(test_sentence, test_entities, "姓名")
        print(f"结果: {result}")
    except Exception as e:
        print(f"失败: {e}")

if __name__ == "__main__":
    print("开始测试多轮标注功能...")
    
    # 测试1：基本功能测试
    test_comprehensive_annotation()
    
    # 测试2：配置方法测试
    test_annotation_methods()
    
    print("\n测试完成！") 