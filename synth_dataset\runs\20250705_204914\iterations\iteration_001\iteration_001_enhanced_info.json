{
  "iteration": 1,
  "timestamp": "2025-07-05T22:15:48.567019",
  "dataset_size": 1103,
  "entity_gap": {
    "姓名": 34,
    "年龄": 50,
    "性别": 50,
    "国籍": 28,
    "职业": 9,
    "民族": 24,
    "教育背景": 24,
    "婚姻状况": 50,
    "政治倾向": 50,
    "家庭成员": 50,
    "工资数额": 50,
    "投资产品": 50,
    "税务记录": 50,
    "信用记录": 50,
    "实体资产": 50,
    "交易信息": 50,
    "疾病": 16,
    "药物": 38,
    "临床表现": 29,
    "医疗程序": 23,
    "过敏信息": 50,
    "生育信息": 50,
    "地理位置": 5,
    "行程信息": 50
  },
  "diversity_metrics": {
    "vocabulary_diversity": 1.0,
    "syntactic_diversity": 0.8,
    "semantic_diversity": 0.7,
    "context_diversity": 0.6,
    "entity_diversity": 0.75
  },
  "total_gap": 930,
  "detailed_metrics": {
    "basic_statistics": {
      "total_samples": 1103,
      "text_length_stats": {
        "mean": 31.31368993653672,
        "median": 30.0,
        "std": 10.690887286463038,
        "min": 