{"prompts": ["Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n2. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"do you have any {{promos}} for NC-17 movies\"\nText Span: \"promos\"\n\n2. Query: \"Can I watch the {{Preview trailer}} of the movie Billy Elliot directed by Stephen Daldry\"\nText Span: \"Preview trailer\"\n\n3. Query: \"What was the {{first look}} at the next Batman movie directed by Matt Reeves?\"\nText Span: \"first look\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n2. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can I watch a stale, {{one-minute trailer}} of the classic film Casablanca\"\nText Span: \"one-minute trailer\"\n\n2. Query: \"Can you provide a {{Preview}} of the latest movie featuring Kylo Ren?\"\nText Span: \"Preview\"\n\n3. Query: \"I'm looking for a {{trailer}} of a movie featuring Laura Dern as an undercover agent\"\nText Span: \"trailer\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n2. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can I get a first look at the {{film snippet}} for the new Han Solo movie in 2000?\"\nText Span: \"film snippet\"\n\n2. Query: \"Could you show me a {{Movie clip}} of an action film with dynamic performances?\"\nText Span: \"Movie clip\"\n\n3. Query: \"Do you have the {{final trailer}} for the film Glimpse?\"\nText Span: \"final trailer\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Show me the {{Trailer}} for the latest Doctor Who movie.\"\nText Span: \"Trailer\"\n\n2. Query: \"Could you provide an {{excerpt}} from the film 'The Social Network' released in 2010?\"\nText Span: \"excerpt\"\n\n3. Query: \"I'm looking for a film from the 90s with a captivating {{preview}} and a memorable song.\"\nText Span: \"preview\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Could you please show me the {{Official trailer}} for Finding Nemo?\"\nText Span: \"Official trailer\"\n\n2. Query: \"Can I get a {{First look}} at the movie A Beautiful Mind starring Russell Crowe?\"\nText Span: \"First look\"\n\n3. Query: \"What is the viewers' rating of the latest movie with a {{movie trailer}} that features an emotionally powerful song?\"\nText Span: \"movie trailer\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Could you show me the {{cinematic preview}} of the movie 'The Perfect Weapon'\"\nText Span: \"cinematic preview\"\n\n2. Query: \"I'm looking for some {{teaser footage}} of the upcoming sci-fi film suitable for parental guidance.\"\nText Span: \"teaser footage\"\n\n3. Query: \"can you show me the {{teaser trailer}} for the movie with heartfelt performances\"\nText Span: \"teaser trailer\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you show me the {{Character teaser}} for the movie directed by Werner Herzog in the 2020s?\"\nText Span: \"Character teaser\"\n\n2. Query: \"Could you show me the {{trailer}} for the film directed by David Lynch that features Laura Dern?\"\nText Span: \"trailer\"\n\n3. Query: \"Show me a {{trailer}} for The Terminator film directed by James Cameron.\"\nText Span: \"trailer\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Show me a {{featurette}} for a movie directed by E.\"\nText Span: \"featurette\"\n\n2. Query: \"Show me a {{film snippet}} from The Matrix directed by the Wachowskis\"\nText Span: \"film snippet\"\n\n3. Query: \"Show me a {{trailer}} of the latest action film with Scarlett Johansson in it.\"\nText Span: \"trailer\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you show me the {{Official preview}} for the new movie directed by Roman Polanski?\"\nText Span: \"Official preview\"\n\n2. Query: \"Show me a {{trailer}} of the movie The Little Mermaid from 1989.\"\nText Span: \"trailer\"\n\n3. Query: \"show me a trailer for {{Tiny Dancer}} in the film industry\"\nText Span: \"Tiny Dancer\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you show me the {{Teaser trailer}} for the new horror movie with a chilling atmosphere?\"\nText Span: \"Teaser trailer\"\n\n2. Query: \"Show me a {{Preview}} of the film with the character Jafar in it\"\nText Span: \"Preview\"\n\n3. Query: \"Could you show me a {{trailer}} for a movie released in the summer of '69?\"\nText Span: \"trailer\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n2. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Show me a {{movie teaser}} featuring Scarlett Johansson.\"\nText Span: \"movie teaser\"\n\n2. Query: \"Can you show me a {{sneak peek}} of The Jungle Book movie?\"\nText Span: \"sneak peek\"\n\n3. Query: \"Can you recommend a movie directed by Damien Chazelle that has a great {{Preview}}?\"\nText Span: \"Preview\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can I get a {{preview snippet}} of the new action thriller movie with a revenge plot?\"\nText Span: \"preview snippet\"\n\n2. Query: \"Show me a {{trailer segment}} from any revisionist western film.\"\nText Span: \"trailer segment\"\n\n3. Query: \"Show me a {{trailer}} for the film featuring Ben Affleck and directed by Dario Argento\"\nText Span: \"trailer\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you show me the {{trailer}} for the movie featuring Don Vito Corleone?\"\nText Span: \"trailer\"\n\n2. Query: \"I'm looking for the {{announcement trailer}} of a movie released in 2021. Do you have any suggestions?\"\nText Span: \"announcement trailer\"\n\n3. Query: \"Show me a {{Trailer excerpt}} for a popular Martial Arts film\"\nText Span: \"Trailer excerpt\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you show me a {{Film teaser}} for the new James Cameron movie?\"\nText Span: \"Film teaser\"\n\n2. Query: \"Can you show me the {{trailer}} for the latest film by Christopher Nolan?\"\nText Span: \"trailer\"\n\n3. Query: \"Show me a movie with a {{promotional clip}} featuring Michael B. Jordan\"\nText Span: \"promotional clip\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n2. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Could you provide a {{movie snippet}} of the film 'Radioactive' released in 2019?\"\nText Span: \"movie snippet\"\n\n2. Query: \"show me a {{preview}} of the upcoming Chris Evans movie\"\nText Span: \"preview\"\n\n3. Query: \"Can you play a {{preview clip}} from the latest Ava DuVernay film?\"\nText Span: \"preview clip\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n2. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Show me a {{trailer}} for a movie featuring Captain Kirk as an undercover cop.\"\nText Span: \"trailer\"\n\n2. Query: \"Can you show me the {{official trailer}} for the movie Thor: Ragnarok?\"\nText Span: \"official trailer\"\n\n3. Query: \"Is Ryan Gosling starring in the {{film trailer}} for Grease?\"\nText Span: \"film trailer\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n2. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can I get a {{Sneak peek}} of a comedy movie rated TV-Y7?\"\nText Span: \"Sneak peek\"\n\n2. Query: \"Is there a {{trailer}} available for the upcoming Captain Kirk movie?\"\nText Span: \"trailer\"\n\n3. Query: \"Can you play a {{Movie clip}} from Inception starring Marion Cotillard?\"\nText Span: \"Movie clip\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you show me the {{official preview}} for the film Predators?\"\nText Span: \"official preview\"\n\n2. Query: \"Can you provide a {{Film preview}} for the movie that came out in 1979?\"\nText Span: \"Film preview\"\n\n3. Query: \"Can you show me the {{Announcement trailer}} for the new Yorgos Lanthimos movie coming in the 2020s?\"\nText Span: \"Announcement trailer\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you find me the {{trailer}} for the musical movie featuring the song Don't Stop Believin'?\"\nText Span: \"trailer\"\n\n2. Query: \"I want to see a behind-the-scenes glimpse of a new {{character teaser}} for the upcoming action movie.\"\nText Span: \"character teaser\"\n\n3. Query: \"Show me the {{trailer}} for the Avengers movie featuring Mr. Incredible as a character.\"\nText Span: \"trailer\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Do you have an {{Extended preview}} of the film where Martin Riggs is a character?\"\nText Span: \"Extended preview\"\n\n2. Query: \"show me a {{trailer}} for a classic film directed by alfred hitchcock Music \"\nText Span: \"trailer\"\n\n3. Query: \"Can you give me a {{Glimpse}} of the evocative movie All You Need Is Love?\"\nText Span: \"Glimpse\"", "Here are 2 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Trailer.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type trailer\n- (B). The span contains a named trailer entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not trailer\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Song, Review, Character, other].\n\nA named movie trailer entity is a term that indicates a segment or a clip from a movie, such as \"clip\", \"trailer\" and \"preview\".\nYou should always drop starting \"captivating\" or \"fun\" descriptive words from named trailer entity spans.\nYou should always drop starting \"movie\" or \"film\" words from named trailer entity spans.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you play the {{movie clip}} where <PERSON><PERSON> gives his famous advice to <PERSON>?\"\nText Span: \"movie clip\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"clip\".\n\n2. Query: 'Can you show me the {{trailer}} for the movie \"The Matrix\"?'\nText Span: \"trailer\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 2 spans of text:\n\n1. Query: \"Show me a {{trailer}} for Zack Snyder's latest film, Belle, which is directed by Terrence Malick.\"\nText Span: \"trailer\"\n\n2. Query: \"Show me a {{trailer}} for the new Wonder Woman movie that includes LGBTQ+ representation\"\nText Span: \"trailer\""]}