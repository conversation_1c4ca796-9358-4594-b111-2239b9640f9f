{"evaluation_time": "2025-07-19 22:34:15", "dataset_info": {"total_records": 197, "total_entities": 353, "entity_types": ["姓名", "年龄", "性别", "国籍", "职业", "民族", "教育背景", "婚姻状况", "政治倾向", "家庭成员", "工资数额", "投资产品", "税务记录", "信用记录", "实体资产", "交易信息", "疾病", "药物", "临床表现", "医疗程序", "过敏信息", "生育信息", "地理位置", "行程信息"]}, "balance_evaluation": {"passed": false, "overall_score": 0.5470346689975671, "coverage_ratio": 1.0, "actual_distribution": {"姓名": {"count": 17, "percentage": 0.04815864022662889, "unique_entities": 5, "total_occurrences": 17}, "年龄": {"count": 8, "percentage": 0.0226628895184136, "unique_entities": 4, "total_occurrences": 8}, "性别": {"count": 14, "percentage": 0.039660056657223795, "unique_entities": 4, "total_occurrences": 14}, "国籍": {"count": 7, "percentage": 0.019830028328611898, "unique_entities": 5, "total_occurrences": 7}, "职业": {"count": 2, "percentage": 0.0056657223796034, "unique_entities": 1, "total_occurrences": 2}, "民族": {"count": 14, "percentage": 0.039660056657223795, "unique_entities": 7, "total_occurrences": 14}, "教育背景": {"count": 13, "percentage": 0.036827195467422094, "unique_entities": 9, "total_occurrences": 13}, "婚姻状况": {"count": 10, "percentage": 0.028328611898016998, "unique_entities": 2, "total_occurrences": 10}, "政治倾向": {"count": 12, "percentage": 0.0339943342776204, "unique_entities": 5, "total_occurrences": 12}, "家庭成员": {"count": 31, "percentage": 0.08781869688385269, "unique_entities": 21, "total_occurrences": 31}, "工资数额": {"count": 13, "percentage": 0.036827195467422094, "unique_entities": 6, "total_occurrences": 13}, "投资产品": {"count": 10, "percentage": 0.028328611898016998, "unique_entities": 9, "total_occurrences": 10}, "税务记录": {"count": 14, "percentage": 0.039660056657223795, "unique_entities": 10, "total_occurrences": 14}, "信用记录": {"count": 13, "percentage": 0.036827195467422094, "unique_entities": 10, "total_occurrences": 13}, "实体资产": {"count": 19, "percentage": 0.053824362606232294, "unique_entities": 12, "total_occurrences": 19}, "交易信息": {"count": 32, "percentage": 0.0906515580736544, "unique_entities": 23, "total_occurrences": 32}, "疾病": {"count": 3, "percentage": 0.0084985835694051, "unique_entities": 1, "total_occurrences": 3}, "药物": {"count": 2, "percentage": 0.0056657223796034, "unique_entities": 2, "total_occurrences": 2}, "临床表现": {"count": 21, "percentage": 0.059490084985835696, "unique_entities": 13, "total_occurrences": 21}, "医疗程序": {"count": 9, "percentage": 0.025495750708215296, "unique_entities": 4, "total_occurrences": 9}, "过敏信息": {"count": 18, "percentage": 0.05099150141643059, "unique_entities": 5, "total_occurrences": 18}, "生育信息": {"count": 26, "percentage": 0.07365439093484419, "unique_entities": 19, "total_occurrences": 26}, "地理位置": {"count": 12, "percentage": 0.0339943342776204, "unique_entities": 6, "total_occurrences": 12}, "行程信息": {"count": 33, "percentage": 0.09348441926345609, "unique_entities": 13, "total_occurrences": 33}}, "target_distribution": {"姓名": 0.03655913978494624, "年龄": 0.053763440860215055, "性别": 0.053763440860215055, "国籍": 0.030107526881720432, "职业": 0.00967741935483871, "民族": 0.025806451612903226, "教育背景": 0.025806451612903226, "婚姻状况": 0.053763440860215055, "政治倾向": 0.053763440860215055, "家庭成员": 0.053763440860215055, "工资数额": 0.053763440860215055, "投资产品": 0.053763440860215055, "税务记录": 0.053763440860215055, "信用记录": 0.053763440860215055, "实体资产": 0.053763440860215055, "交易信息": 0.053763440860215055, "疾病": 0.017204301075268817, "药物": 0.04086021505376344, "临床表现": 0.03118279569892473, "医疗程序": 0.024731182795698924, "过敏信息": 0.053763440860215055, "生育信息": 0.053763440860215055, "地理位置": 0.005376344086021506, "行程信息": 0.053763440860215055}, "comparison": {"overall_score": 0.5470346689975671, "entity_scores": {"税务记录": 0.7376770538243627, "职业": 0.5854579792256847, "实体资产": 0.9988668555240794, "政治倾向": 0.6322946175637394, "临床表现": 0.0922145159714759, "国籍": 0.6586402266288951, "家庭成员": 0.36657223796034, "性别": 0.7376770538243627, "工资数额": 0.6849858356940509, "投资产品": 0.5269121813031161, "婚姻状况": 0.5269121813031161, "教育背景": 0.5729461756373939, "疾病": 0.4939801699716715, "医疗程序": 0.9690848626678162, "信用记录": 0.6849858356940509, "行程信息": 0.26118980169971673, "民族": 0.4631728045325779, "生育信息": 0.6300283286118982, "姓名": 0.6827195467422098, "过敏信息": 0.948441926345609, "年龄": 0.42152974504249296, "交易信息": 0.31388101983002825, "药物": 0.13866110034292534, "地理位置": 0}, "coverage_ratio": 1.0, "distribution_differences": {"税务记录": {"actual": 0.039660056657223795, "target": 0.053763440860215055, "difference": 0.01410338420299126}, "职业": {"actual": 0.0056657223796034, "target": 0.00967741935483871, "difference": 0.00401169697523531}, "实体资产": {"actual": 0.053824362606232294, "target": 0.053763440860215055, "difference": 6.092174601723954e-05}, "政治倾向": {"actual": 0.0339943342776204, "target": 0.053763440860215055, "difference": 0.019769106582594655}, "临床表现": {"actual": 0.059490084985835696, "target": 0.03118279569892473, "difference": 0.028307289286910967}, "国籍": {"actual": 0.019830028328611898, "target": 0.030107526881720432, "difference": 0.010277498553108534}, "家庭成员": {"actual": 0.08781869688385269, "target": 0.053763440860215055, "difference": 0.03405525602363763}, "性别": {"actual": 0.039660056657223795, "target": 0.053763440860215055, "difference": 0.01410338420299126}, "工资数额": {"actual": 0.036827195467422094, "target": 0.053763440860215055, "difference": 0.01693624539279296}, "投资产品": {"actual": 0.028328611898016998, "target": 0.053763440860215055, "difference": 0.025434828962198057}, "婚姻状况": {"actual": 0.028328611898016998, "target": 0.053763440860215055, "difference": 0.025434828962198057}, "教育背景": {"actual": 0.036827195467422094, "target": 0.025806451612903226, "difference": 0.011020743854518868}, "疾病": {"actual": 0.0084985835694051, "target": 0.017204301075268817, "difference": 0.008705717505863717}, "医疗程序": {"actual": 0.025495750708215296, "target": 0.024731182795698924, "difference": 0.0007645679125163722}, "信用记录": {"actual": 0.036827195467422094, "target": 0.053763440860215055, "difference": 0.01693624539279296}, "行程信息": {"actual": 0.09348441926345609, "target": 0.053763440860215055, "difference": 0.039720978403241035}, "民族": {"actual": 0.039660056657223795, "target": 0.025806451612903226, "difference": 0.01385360504432057}, "生育信息": {"actual": 0.07365439093484419, "target": 0.053763440860215055, "difference": 0.019890950074629134}, "姓名": {"actual": 0.04815864022662889, "target": 0.03655913978494624, "difference": 0.011599500441682654}, "过敏信息": {"actual": 0.05099150141643059, "target": 0.053763440860215055, "difference": 0.0027719394437844616}, "年龄": {"actual": 0.0226628895184136, "target": 0.053763440860215055, "difference": 0.031100551341801456}, "交易信息": {"actual": 0.0906515580736544, "target": 0.053763440860215055, "difference": 0.03688811721343934}, "药物": {"actual": 0.0056657223796034, "target": 0.04086021505376344, "difference": 0.03519449267416004}, "地理位置": {"actual": 0.0339943342776204, "target": 0.005376344086021506, "difference": 0.028617990191598892}}, "missing_entities": [], "excess_entities": []}, "thresholds": {"distribution_tolerance": 0.15, "min_coverage_ratio": 0.8}}, "diversity_evaluation": {"passed": false, "vocabulary_diversity": 0.24722310377657886, "syntactic_diversity": 0.8, "semantic_diversity": 0.9988429450618322, "context_diversity": 1.0, "entity_diversity": {"overall": 0.5531336271919608, "by_type": {"姓名": 0.29411764705882354, "年龄": 0.5, "性别": 0.2857142857142857, "国籍": 0.7142857142857143, "职业": 0.5, "民族": 0.5, "教育背景": 0.6923076923076923, "婚姻状况": 0.2, "政治倾向": 0.4166666666666667, "家庭成员": 0.6774193548387096, "工资数额": 0.46153846153846156, "投资产品": 0.9, "税务记录": 0.7142857142857143, "信用记录": 0.7692307692307693, "实体资产": 0.631578947368421, "交易信息": 0.71875, "疾病": 0.3333333333333333, "药物": 1.0, "临床表现": 0.6190476190476191, "医疗程序": 0.4444444444444444, "过敏信息": 0.2777777777777778, "生育信息": 0.7307692307692307, "地理位置": 0.5, "行程信息": 0.3939393939393939}}, "thresholds": {"vocabulary_diversity": 0.6, "syntactic_diversity": 0.5, "semantic_diversity": 0.4, "context_diversity": 0.5, "entity_diversity": 0.7, "min_unique_entities": 0.3}}, "naturalness_evaluation": {"avg_score": 10.0, "low_score_rate": 0.0, "low_score_examples": [], "threshold": 6.0}, "overall_passed": false}