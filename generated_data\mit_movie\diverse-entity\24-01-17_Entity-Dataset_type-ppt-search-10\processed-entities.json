{"entity-type2entity-names": {"Title": ["The Godfather", "<PERSON>", "Pulp Fiction", "The Shawshank Redemption", "Titanic", "The Matrix", "Jurassic Park", "Gone with the Wind", "Casablanca", "Inception", "The Dark Knight", "The Wizard of Oz", "E.T. the Extra-Terrestrial", "The Lord of the Rings", "<PERSON><PERSON><PERSON>'s List", "<PERSON>", "Psycho", "Jaws", "The Silence of the Lambs", "2001", "Goodfellas", "The Bridge on the River Kwai", "The Sound of Music", "The Exorcist", "Saving Private <PERSON>", "A Clockwork Orange", "Blade Runner", "Gladiator", "Seven Samurai", "The Social Network", "The Grand Budapest Hotel", "Black Panther", "Star Wars", "The Lion King", "<PERSON> and the Beast", "The Little Mermaid", "<PERSON><PERSON><PERSON>", "Toy Story", "The Incredibles", "Finding Nemo", "<PERSON><PERSON><PERSON>", "Frozen", "<PERSON><PERSON>", "Cars", "Up", "Wall-E", "<PERSON><PERSON><PERSON><PERSON>", "The Jungle Book", "<PERSON>", "La La Land", "The Shape of Water", "The Revenant", "Moonlight", "Her", "Black Swan", "<PERSON><PERSON>", "Room", "The Artist", "Avatar", "Spider-Man", "Grease", "The Big Lebowski", "The Princess Bride", "The Terminator", "The Breakfast Club", "<PERSON>'s Day Off", "Dirty Dancing", "Ghostbusters", "Top Gun", "The Goonies", "Back to the Future", "Die Hard", "Indiana Jones", "<PERSON>", "Alien", "The Shining", "<PERSON><PERSON><PERSON>", "Lawrence of Arabia", "The Graduate", "A Streetcar Named Desire", "The Maltese Falcon", "North by Northwest", "Chinatown", "The Great Gatsby", "<PERSON><PERSON>", "The Catcher in the Rye", "Brave New World", "A Tale of Two Cities", "The Old Man and the Sea", "The Scarlet Letter", "<PERSON> and <PERSON>", "The Avengers", "<PERSON> and the Sorcerer's Stone", "Fight Club", "The Departed", "Interstellar", "Cinderella", "<PERSON> and the Seven Dwarfs", "Tangled", "Zootopia", "Inside Out", "Coco", "<PERSON><PERSON>", "Hercules", "Dumbo", "Bambi", "The Lion Guard", "<PERSON><PERSON> the Po<PERSON>", "<PERSON>", "Alice in Wonderland", "Sleeping Beauty", "City of God", "12 Angry Men", "Braveheart", "A Beautiful Mind", "The Green Mile", "The Sixth Sense", "Reservoir Dogs", "Memento", "Kill Bill", "American Psycho", "The Princess Diaries", "Mean Girls", "Parasite", "Shawshank Redemption", "<PERSON><PERSON><PERSON>", "Oldboy", "American Beauty", "The Pianist", "Million Dollar Baby", "No Country for Old Men", "Slumdog Millionaire", "The Hurt Locker", "The King's Speech", "Argo", "12 Years a Slave", "A Star is Born", "The Irishman", "Get Out", "Joker", "Avengers", "King Kong", "The Godfather Part II", "<PERSON> and the Philosopher's Stone", "<PERSON> Jones and the Raiders of the Lost Ark", "The 40-Year-Old Virgin", "<PERSON>", "WALL-E", "The Karate Kid", "A Space Odyssey", "Crash", "The Godfather Part III", "The Pink Panther", "Raging Bull", "The Usual Suspects", "The Truman Show", "The Curious Case of <PERSON>", "Trainspotting", "The Deer Hunter", "Mad <PERSON>", "Star Wars: Episode IV", "The Hunger Games", "The Twilight Saga", "<PERSON> Jones and the Last Crusade", "Good Will Hunting", "The Martian", "Inglourious Basterds", "Little Miss Sunshine", "Juno", "Silver Linings Playbook", "<PERSON><PERSON><PERSON> Unchained", "It's a Wonderful Life", "Singin' in the Rain", "The Seven Samurai", "Whiplash", "The Wolf of Wall Street", "Casino Royale", "Superbad", "<PERSON><PERSON>", "Dunkirk", "The Fault in Our Stars", "The Notebook", "Clueless", "10 Things I Hate About You"], "Viewers' Rating": ["5 stars", "4.5 stars", "A+", "10 out of 10", "Excellent", "Amazing", "Must-see", "Perfect", "Great", "Outstanding", "90%", "9 out of 10", "Phenomenal", "Terrific", "Impressive", "4.8 stars", "A-", "8.5 out of 10", "95%", "9.5 stars", "Top-notch", "Superb", "Aesthetically pleasing", "Masterpiece", "4.7 stars", "4.3 stars", "Solid", "85%", "8 out of 10", "9.8 stars", "4.9 stars", "B+", "9.5 out of 10", "Highly recommended", "4.2 stars", "80%", "4.6 stars", "Remarkable", "Brilliant", "9.7 out of 10", "4.4 stars", "7.5 out of 10", "93%", "8.9 stars", "Exemplary", "One of the best", "A classic", "Top-rated", "Worth watching", "Fantastic", "A masterpiece", "Riveting", "Captivating", "Compelling", "Exceptional", "Spectacular", "Mind-blowing", "Unforgettable", "A gem", "A triumph", "A favorite", "A must-see", "A must-watch", "Highly acclaimed", "Recommended", "A standout", "A winner", "Incredible", "Engrossing", "Memorable", "Touching", "Fun", "Entertaining", "Enjoyable", "Good", "Decent", "Average", "5-star rating", "4-star rating", "3.5-star rating", "8/10 rating", "4 out of 5 stars", "9/10 rating", "3-star rating", "2-star rating", "7/10 rating", "Thumbs up", "Thumbs down", "9 out of 10 rating", "B+ rating", "C- rating", "6/10 rating", "4.5-star rating", "2 out of 5 stars", "A- rating", "D+ rating", "7.5/10 rating", "2.5-star rating", "9.5/10 rating", "3.7-star rating", "8.5/10 rating", "7 out of 10 stars", "5 out of 10 stars", "A rating", "B rating", "8.5/10 stars", "6.5/10 rating", "5.5-star rating", "4.7/5 stars", "2.3-star rating", "1-star rating", "6 out of 10 stars", "C+ rating", "3.2-star rating", "8 out of 10 stars", "B- rating", "1 out of 5 stars", "7.5/10 stars", "D rating", "4.2-star rating", "2.5 out of 5 stars", "9.2/10 rating", "Essential viewing", "Classic", "Awe-inspiring", "Electrifying", "Enthralling", "Engaging", "Thought-provoking", "Inspiring", "Heartwarming", "Powerful", "Emotional", "Raw", "Gripping", "Suspenseful", "Thrilling", "Intense", "Exciting", "Action-packed", "Hilarious", "Amusing", "Witty", "<PERSON><PERSON>", "Delightful", "Enchanting", "Refreshing", "7 out of 10", "6 out of 10", "5 out of 10", "4 out of 10", "3 out of 10", "2 out of 10", "1 out of 10", "A", "B", "B-", "C+", "C", "C-", "D+", "D", "D-", "F", "Fair", "Mediocre", "Poor", "Terrible", "Must-See", "Must-Watch", "Above Average", "Below Average", "Flawless", "Just Okay", "Unimpressive", "Uninspiring", "Overrated", "Underrated", "Mixed Feelings", "Five-star", "Exquisite", "Unmissable", "Awesome", "Sensational", "Epic", "Masterful", "Awe-inducing", "Unmatched", "Must-watch", "Stupendous", "Astounding", "Uplifting", "Alluring", "Extraordinary", "Breathtaking", "Frightening", "Provocative", "Exhilarating", "Addictive", "Popular", "Crowd-pleaser", "Timeless", "Perfect score", "High rating", "Low rating", "Mixed reviews", "Rave reviews", "Critically acclaimed", "Cult classic", "Audience favorite", "Not recommended", "Best of the year", "Worst of the year", "Popular choice", "Quality movie", "Average rating", "Disappointing", "Exceeded expectations", "Great reviews", "Terrible reviews", "Polarizing", "Divisive", "Subpar", "Binge-worthy", "Timeless classic", "Hidden gem", "Instant classic", "Box office hit", "Indie favorite", "Family-friendly", "10/10 rating", "Above average", "5/10 rating", "4/10 rating", "3/10 rating", "2/10 rating", "1/10 rating", "Waste of time", "Not worth watching", "Awful", "0/10 rating", "Lackluster", "Uninspired", "Below average", "Displeasing", "Unconvincing", "Uninteresting", "<PERSON><PERSON><PERSON>", "Dreadful", "0-star rating", "Abysmal", "<PERSON>lop", "Bomb", "Very good", "Watchable", "Avoidable", "Unwatchable"], "Year": ["1975", "2003", "1999", "2016", "1988", "1967", "2010", "1995", "1984", "1972", "2008", "1956", "2015", "1990", "1981", "1977", "2002", "1963", "1987", "2005", "2012", "1997", "1986", "1979", "1970", "2007", "1954", "2018", "1993", "1983", "1971", "2014", "2000", "1969", "1982", "1978", "1965", "2004", "1996", "1992", "1961", "2009", "2019", "1991", "1974", "2021", "1950s", "1985", "2025", "2020", "1980s", "1955", "2022", "1960s", "1957", "1976", "2023", "1952", "1973", "1966", "1959", "2001", "1998", "1989", "1968", "1950", "1980", "1962", "2011", "2020s", "2006", "2013", "1964s", "1999-2001", "2005-2008", "1987-1990", "2010-2012", "1965-1967", "1995-1997", "1983-1985", "2008-2010", "1945", "1990s", "1958", "1994", "2010s", "2017", "2000s", "1964", "1940s", "2030", "2027", "2032", "2028", "2033", "2026", "2031", "2024"], "Genre": ["Action", "Romance", "Comedy", "Thriller", "Horror", "Sci-fi", "Fantasy", "Drama", "Western", "Musical", "Animation", "Adventure", "Mystery", "Crime", "War", "Spy", "Superhero", "Historical", "Martial arts", "Disaster", "Sports", "Family", "Teen", "Romantic comedy", "<PERSON>lasher", "Zombie", "Time travel", "Alien invasion", "Monster", "Found footage", "Mockumentary", "Biographical", "Coming of age", "Cyberpunk", "Film noir", "Satire", "Surreal", "Holocaust", "Political", "Social issues", "Legal", "Indie", "Propaganda", "Neo-noir", "Neo-western", "Documentary", "History", "Science fiction", "Supernatural", "Apocalyptic", "Children's", "Courtroom", "Epic", "Exploitation", "Gothic", "Jungle", "Mind-bending", "Prison", "Psycho", "Revenge", "Road", "Noir", "Musical comedy", "Psychological thriller", "<PERSON>", "Black comedy", "Space opera", "Animal", "Adventure comedy", "Revisionist western", "Silent", "Coming-of-age", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Biopic", "Fairy tale", "Gangster", "Paranormal", "<PERSON><PERSON><PERSON>", "Surrealism", "Tragedy", "Underground", "Urban", "Action-comedy", "Animation musical", "Science Fiction", "Dance", "Martial Arts", "Psychological", "Anime", "Cult", "Found Footage", "Road Trip", "Art House", "Black Comedy", "Experimental", "Women's", "LGBTQ+", "Crime drama", "Independent", "Techno-thriller", "True crime", "Vampire", "Medical", "Space", "Post-apocalyptic", "Eco", "Biography", "Film-Noir", "Music", "Sci-Fi", "Sport", "Period", "Post-Apocalyptic", "Screwball", "Spaghetti Western", "Slapstick", "Stoner", "Film Noir", "Slice of Life", "Suspense", "Time Travel", "Coming of Age", "Crime Drama", "Dark Comedy", "Buddy Comedy", "Courtroom Drama", "Dystopian", "Alien", "Survival", "Coming-of-Age", "<PERSON><PERSON><PERSON>", "Space Opera"], "Director": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Sofia Coppola", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Guillermo <PERSON> Toro", "<PERSON><PERSON>", "<PERSON><PERSON>ho", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> and <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Coen Brothers", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "MPAA Rating": ["G", "PG", "PG-13", "R", "NC-17", "Unrated", "M", "GP", "NR", "X", "U", "A", "C", "E", "S", "MA", "PG-7", "TV-MA", "18+", "TV-PG", "TV-14", "TV-G", "TV-Y7", "TV-Y", "NR-17", "PG-14", "AA", "AO", "Not Rated", "M/PG", "All Ages", "Parental Guidance", "Mature Audiences", "Advisory", "Recommended for Adults", "Restricted", "Suitable for Children and Adults", "R-13", "Approved", "MPAA"], "Plot": ["Love triangle", "Time travel", "Revenge", "Undercover mission", "Treasure hunt", "Space exploration", "Identity theft", "Political conspiracy", "Family inheritance", "Forbidden love", "Zombie apocalypse", "Kidnapping", "Survival in the wilderness", "Amnesia", "Detective investigation", "Alien invasion", "Artificial intelligence uprising", "Secret society", "Historical revolution", "Drug cartel", "Bank heist", "Corporate espionage", "Haunted house", "Dystopian society", "Superhero origin story", "Romantic comedy mishaps", "Dance competition", "Time loop", "Cyber warfare", "Identity crisis", "Undercover agent", "War", "Survival", "Quest for redemption", "Artificial intelligence", "Serial killer", "Wilderness adventure", "Archaeological discovery", "Heist", "Terminal illness", "Family secret", "Courtroom drama", "Bank robbery", "Organized crime", "Nuclear disaster", "Cult initiation", "International espionage", "Undercover operation", "Quest for treasure", "Secret mission", "Forbidden romance", "Revenge killing", "Betrayal", "Human trafficking", "Mercenary mission", "Witness protection", "Corporate corruption", "Bitter rivalry", "Coming-of-age journey", "Race against time", "Mind control", "Post-apocalyptic world", "Mistaken identity", "Parallel universe exploration", "Undercover cop", "Revenge plot", "Small town mystery", "Art heist", "Survival story", "Historical conspiracy", "Pandemic outbreak", "Cybercrime", "Murder investigation", "Coming of age", "War epic", "Political scandal", "Family feud", "Wilderness survival", "Princess rescue", "Mafia power struggle", "Ghost haunting", "Modern-day fairy tale", "Hero's journey", "Quest for immortality", "Environmental disaster.", "Wartime resistance", "Immigrant experience", "Space mission", "Quest for a lost treasure", "High school drama", "Supernatural powers", "Family secrets", "Rags to riches", "Facing inner demons", "Animal journey", "Betrayal and redemption", "True crime investigation", "Minority discrimination", "Gender identity exploration", "Environmental activism", "Cold war espionage", "Sports underdog story", "Quest for power", "Unrequited love", "Apocalypse", "Murder mystery", "Redemption", "Escape from captivity", "Addiction", "War-torn country", "Undercover spy operation", "Romantic comedy", "Historical war drama", "Conspiracy thriller", "Family inheritance dispute", "Political corruption", "Mafia hitman", "Drug cartel operation", "Serial killer pursuit", "Cyber terrorism", "Princess in disguise", "Robotic uprising", "Gladiator rebellion", "Superpower discovery", "Fantasy quest", "Animal kingdom power struggle", "Time loop dilemma", "Quest for revenge", "Battle for survival", "Apocalyptic world", "Cybernetic uprising", "Ocean exploration", "World war", "Vigilante justice", "Robot uprising", "Life after death", "Gladiator competition", "Space colonization", "Environmental disaster", "Virtual reality world", "Family inheritance feud", "Overcoming adversity", "Apocalyptic event", "Loss of memory", "Team of misfits", "A haunted house", "Exotic location", "Military operation", "Pursuit of happiness", "Discovery of a new world", "Sacrifice for a greater cause", "Battle between good and evil", "Princess", "Treasure", "Undercover detective", "Lost artifact", "Mafia boss", "Secret government experiment", "Family curse", "War hero", "Epic quest", "Conspiracy theory", "Evil corporation", "Detective mystery", "Virtual reality", "Survival adventure", "Political intrigue", "Dangerous cult"], "Actor": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON> Jr.", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Gal Gadot", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Al Pacino", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Jude Law", "<PERSON>"], "Trailer": ["Teaser", "Sneak peek", "Preview", "Teaser trailer", "Theatrical trailer", "Official trailer", "Excerpt", "Clip", "Glimpse", "Promotion", "<PERSON><PERSON>", "Preview trailer", "Footage", "Teaser clip", "Highlights", "Featurette", "Promo", "Preview screening", "First look", "Exclusive footage", "Trailer release", "Behind-the-scenes glimpse", "Promotional clip", "Extended preview", "Movie snippet", "Film teaser", "Movie preview", "Clip showcase", "Extended trailer", "Announcement trailer", "Film preview", "Trailer preview", "Cinematic preview", "Promo trailer", "Teaser preview", "Trailer snippet", "Teaser footage", "One-minute trailer", "Coming attractions", "Film snippet", "Movie clip", "Trailer segment", "Preview snippet", "Movie trailer", "TV spot", "Trailer excerpt", "Movie teaser", "Preview footage", "Trailer clip", "Movie promo", "International trailer", "Red band trailer", "Green band trailer", "Final trailer", "Teaser poster", "Character teaser", "Behind-the-scenes featurette", "Exclusive clip", "Upcoming releases", "Film trailer", "Teaser video", "Preview clip", "Trailer", "Announcement", "Highlight reel", "Official preview"], "Song": ["Bohemian Rhapsody", "Purple Haze", "Thriller", "<PERSON>", "Single Ladies", "Hotel California", "Hound Dog", "Smells Like Teen Spirit", "Unforgettable", "Blackbird", "Rolling in the Deep", "<PERSON>", "Sweet Child O' Mine", "My Heart Will Go On", "I Will Always Love You", "Fight for Your Right", "We Will Rock You", "Summer of '69", "Girls Just Want to Have Fun", "Livin' on a Prayer", "Sweet Home Alabama", "Purple Rain", "Hey Jude", "Like a Rolling Stone", "Stairway to Heaven", "All You Need is Love", "Born to Run", "Blowing in the Wind", "American Pie", "What's Going On", "Yesterday", "Crazy in Love", "Eye of the Tiger", "Don't Stop Believin'", "Let It Go", "Sweet Child o' Mine", "Imagine", "Uptown Funk", "I Want to Hold Your Hand", "Let's Get It On", "Red Red Wine", "Raspberry Beret", "Bad Moon Rising", "The Sound of Silence", "Unchained Melody", "You've Got a Friend", "Kiss from a Rose", "Every Breath You Take", "Piano Man", "Dancing Queen", "<PERSON>", "<PERSON><PERSON> and the Jets", "Bad Romance", "<PERSON>", "<PERSON> in the Sky with Diamonds", "Love Shack", "Burning Love", "Sweet Caroline", "My Girl", "Yellow Submarine", "My Way", "Come Together", "Pour Some Sugar on Me", "Help!", "Dancing in the Street", "Rock the Casbah", "Friends in Low Places", "Superstition", "I Will Survive", "Three Little Birds", "Killer Queen", "Respect", "Rhythm Nation", "Boogie Wonderland", "Suspicious Minds", "Another Brick in the Wall", "Raining Men", "Material Girl", "Sound of Silence", "Bridge Over Troubled Water", "Hallelujah", "(I Can’t Get No) Satisfaction", "Sweet Child O’ Mine", "Livin’ on a Prayer", "You Shook Me All Night Long", "Stand by Me", "<PERSON><PERSON>", "Wonderwall", "Dancing in the Dark", "Lose Yourself", "Black Velvet", "Blue Suede Shoes", "I Heard It Through the Grapevine", "<PERSON><PERSON><PERSON>", "Radioactive", "<PERSON><PERSON>", "Smooth Criminal", "Shallow", "Killing Me Softly", "Let It Be", "Thunderstruck", "Dreams", "Chain of Fools", "Smooth Operator", "Waterloo", "Ain't No Mountain High Enough", "<PERSON>", "Walk on the Wild Side", "La Bamba", "Black Dog", "California Dreamin'", "Georgia on My Mind", "I'm So Excited", "Waterfalls", "When Doves Cry", "Love Me Tender", "Born in the U.S.A.", "White Christmas", "Gangnam Style", "Take Me Out to the Ball Game", "Jingle Bells", "What a Wonderful World", "Happy Birthday", "Amazing Grace", "All You Need Is Love"], "Review": ["Gripping", "Riveting", "Spectacular", "Compelling", "Masterpiece", "Unforgettable", "Captivating", "Breathtaking", "Awe-inspiring", "Phenomenal", "Mesmerizing", "Mind-blowing", "Outstanding", "Astonishing", "Excellent", "Impressive", "Remarkable", "Memorable", "Superb", "Marvelous", "Greatest", "Powerful", "Brilliant", "Magnificent", "Exhilarating", "Thrilling", "Fierce", "Fantastic", "Unbelievable", "Superior", "Flawless", "<PERSON><PERSON><PERSON>", "Jaw-dropping", "Perfect", "Exceptional", "Must-see", "Inspiring", "Heartwarming", "Electrifying", "Wonderful", "Intriguing", "Immersive", "Thought-provoking", "Heart-wrenching", "Suspenseful", "Visually stunning", "Emotionally powerful", "Heartfelt", "Engrossing", "Impactful", "Enthralling", "Evocative", "Intense", "Reflective", "Uplifting", "Poignant", "Chilling", "Satisfying", "Masterful", "Refreshing", "Unique", "Epic", "Unpredictable", "Dynamic", "Ambitious", "Wow-inducing", "Moving", "Entertaining", "Emotional", "Spellbinding", "Touching", "Engaging", "Complicated", "Honest", "Original", "Inspirational", "Adrenaline-pumping", "Authentic", "Terrifying", "Hilarious", "Affecting", "Artistic", "Universal", "Exquisite", "Gripping storyline", "Captivating performances", "Immersive cinematography", "Compelling plot twists", "Memorable one-liners", "Thought-provoking themes", "Well-paced narrative", "Emotionally resonant", "Expertly crafted", "Cinematic brilliance", "Riveting from start to finish", "Stellar direction", "Impactful storytelling", "Unforgettable characters", "Superbly written", "Awe-inspiring special effects", "Engaging dialogue", "Unpredictable plot", "Heartfelt performances", "Intriguing premise", "Tension-filled scenes", "Spectacular action sequences", "Hauntingly beautiful", "Perfect balance of humor and drama", "Masterful use of symbolism", "Raw and authentic", "Thoughtful commentary on society", "Gripping suspense", "Relatable characters", "Profound and moving", "Beautifully shot", "Unapologetically honest", "Powerfully acted", "Epic in scale", "Absolute masterpiece", "Refreshingly original", "Provocative and daring", "Razor-sharp dialogue", "Mind-bending plot", "Deeply moving", "Seamless blend of genres", "Unsettling and chilling", "Exhilarating and pulse-pounding", "Unforgettable cinematic experience", "Cheesy", "Overrated", "Underwhelming", "Disappointing", "Resonant", "Dramatic", "Predictable", "Action-packed", "Provocative", "Deep", "Tragic", "Heartbreaking", "Relatable", "Nostalgic", "Insightful", "Unsettling", "Enlightening", "Stale", "Intelligent", "Overhyped", "Complex storyline", "Compelling performances", "Riveting action", "Emotionally impactful", "Masterful direction", "Engaging characters", "Cinematic masterpiece", "Epic cinematography", "Captivating plot twists", "Intense drama", "Stellar special effects", "Chilling atmosphere", "Unique storytelling", "Unforgettable soundtrack", "Outstanding ensemble cast", "Mind-bending", "Award-worthy performances", "Touching portrayal of relationships", "Astonishing visuals", "Enthralling narrative", "Poignant themes", "Phenomenal writing", "Stunning set design", "Seamless editing", "Confidence in presentation", "Emotional rollercoaster", "Authentic period detail", "Solid character development", "Well-executed humor", "Unparalleled on-screen chemistry", "Terrifying suspense", "Outstanding animation", "Character-driven storytelling", "Unforgettable moments", "Immersive world-building", "Nuanced performances", "Stunning costume design", "Evocative score", "Intimate portrayal of human experience", "Powerful social commentary", "Terrific", "Awesome", "Incredible", "Amazing", "Stellar", "Monumental", "Five-star", "Beautifully crafted", "Bold", "Splendid", "Cinematic", "World-building", "Compelling narrative", "Impressive visuals", "Compelling characters", "Multi-layered", "Unforgettable performances", "Brilliance", "Emotionally gripping", "Stunning visuals", "Enchanting", "Enriching", "Spell-binding", "Euphoric", "Transcendent", "Brilliant performances", "Dynamic performances", "Impeccable direction", "Riveting plot", "Memorable quotes", "Heartwarming moments", "Twists and turns", "Outstanding cinematography", "Engrossing narrative", "Authentic dialogue", "Unforgettable climax", "Relatable themes", "Brilliant screenplay", "Captivating visuals", "Powerful emotions", "Remarkable acting", "Iconic scenes", "Shocking plot twists", "Breathtaking visuals", "Compelling storytelling", "Inspiring message", "Exceptional sound design", "Witty and clever dialogue", "Intense action sequences", "Impactful soundtrack", "Stellar performances", "Authentic and genuine", "Evocative and resonant", "Powerful message", "Perfect pacing", "Engaging and entertaining", "Authentic representation", "Satisfying conclusion", "Memorable soundtrack", "Meaningful and thought-provoking", "Rich and layered storytelling"], "Character": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> Vader", "<PERSON>", "<PERSON><PERSON><PERSON>", "The Joker", "Wonder Woman", "<PERSON>", "<PERSON>", "Princess <PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Aragorn", "Captain <PERSON>", "Black Widow", "<PERSON>", "<PERSON>", "Iron Man", "Black Panther", "Rey", "Spider-Man", "<PERSON>", "Captain <PERSON>", "The Hulk", "Captain <PERSON>", "<PERSON>pock", "Wolverine", "<PERSON>", "<PERSON><PERSON>", "Predators", "Optimus Prime", "<PERSON><PERSON>", "Joker", "R2-D2", "C-3PO", "Bumblebee", "Ariel", "Simba", "Scar", "<PERSON><PERSON><PERSON>", "Maleficent", "Captain <PERSON>", "Indiana Jones", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>ly", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>uss in Boots", "Ant-<PERSON>", "Dr. <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Professor <PERSON>", "Mystique", "Legolas", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Neo", "Trinity", "Morpheus", "<PERSON>", "<PERSON><PERSON><PERSON>", "Doctor Who", "<PERSON>", "<PERSON>", "Han Solo", "<PERSON>", "<PERSON>", "<PERSON>", "Mr. <PERSON>", "<PERSON><PERSON><PERSON>", "Dracula", "<PERSON>", "<PERSON> the Platypus", "<PERSON>", "<PERSON>", "<PERSON>", "The Terminator", "Rocky <PERSON>", "The Bride", "<PERSON>", "<PERSON>", "The Dude", "<PERSON>", "Professor <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> the Good Witch", "Wicked Witch of the West", "The Tin Man", "Cowardly Lion", "Scarecrow", "<PERSON>", "Deadpool", "<PERSON><PERSON><PERSON>", "Princess <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Cinderella", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Rambo", "Captain <PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>ar<PERSON>", "<PERSON>", "Nurse Ratched", "<PERSON><PERSON>", "Catwoman", "The Godfather", "<PERSON>", "<PERSON>", "<PERSON>", "Dirty Harry", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>Hara", "<PERSON><PERSON>", "Judge <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Wednesday Addams", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "King Kong", "<PERSON>", "<PERSON>", "T-800", "<PERSON>", "Aquaman", "Princess <PERSON>", "Princess <PERSON>", "<PERSON><PERSON><PERSON>", "Jasmine", "<PERSON><PERSON>", "Mushu", "<PERSON><PERSON>", "<PERSON>", "Cruella de Vil", "Hades", "Beast", "<PERSON>", "Mr. Incredible", "<PERSON>-<PERSON>", "<PERSON>"]}, "meta": {"dataset-name": "mit-movie", "entity-types": ["Title", "Viewers' Rating", "Year", "Genre", "Director", "MPAA Rating", "Plot", "Actor", "Trailer", "Song", "Review", "Character"], "seeded": false, "seed-attribute-name": null, "seed-options": null, "entity-sizes": {"Title": 184, "Viewers' Rating": 257, "Year": 98, "Genre": 133, "Director": 120, "MPAA Rating": 40, "Plot": 168, "Actor": 110, "Trailer": 66, "Song": 125, "Review": 249, "Character": 170}}}