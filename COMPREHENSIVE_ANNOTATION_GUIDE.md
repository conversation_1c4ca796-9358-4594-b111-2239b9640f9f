# 多轮标注功能使用指南

## 概述

为了解决生成句子中可能包含多种实体类型而导致的漏标注问题，我们实现了**多轮标注**功能。

## 功能特点

### 1. 多轮标注（Comprehensive）
- **第一步**：全面标注句子中所有类型的实体
- **第二步**：从结果中提取目标类型的实体
- **优势**：避免因只关注单一类型而遗漏其他实体

### 2. 单类型标注（Single）
- 只标注指定的目标实体类型
- 传统方法，速度快但可能遗漏其他实体

## 配置方法

在 `src/synth_data/ner_config.json` 中设置：

```json
{
  "annotation_method": "comprehensive"  // 或 "single"
}
```

## 支持的实体类型

多轮标注支持以下实体类型：

### 个人信息类
- 姓名、年龄、性别、国籍、职业、民族
- 教育背景、婚姻状况、政治倾向、家庭成员

### 金融财务类
- 工资数额、投资产品、税务记录
- 信用记录、实体资产、交易信息

### 医疗健康类
- 疾病、药物、临床表现
- 医疗程序、过敏信息、生育信息

### 位置信息类
- 地理位置、行程信息

## 使用示例

### 示例1：包含多种实体的句子

**句子**：`"张三在北京的医院工作，是一名医生。"`

**多轮标注结果**：
```json
[
  {"entity": "张三", "start_idx": 0, "end_idx": 1, "type": "姓名"},
  {"entity": "北京", "start_idx": 3, "end_idx": 4, "type": "地理位置"},
  {"entity": "医生", "start_idx": 12, "end_idx": 13, "type": "职业"}
]
```

**单类型标注结果**（只标注"姓名"）：
```json
[
  {"entity": "张三", "start_idx": 0, "end_idx": 1, "type": "姓名"}
]
```

### 示例2：复杂金融场景

**句子**：`"李四今年30岁，在上海投资了股票。"`

**多轮标注结果**：
```json
[
  {"entity": "李四", "start_idx": 0, "end_idx": 1, "type": "姓名"},
  {"entity": "30岁", "start_idx": 4, "end_idx": 6, "type": "年龄"},
  {"entity": "上海", "start_idx": 9, "end_idx": 10, "type": "地理位置"},
  {"entity": "股票", "start_idx": 14, "end_idx": 15, "type": "投资产品"}
]
```

## 优势对比

| 特性 | 多轮标注 | 单类型标注 |
|------|----------|------------|
| 准确性 | 高（避免遗漏） | 中等（可能遗漏） |
| 速度 | 中等 | 快 |
| API调用次数 | 1次 | 1次 |
| 适用场景 | 复杂句子、多实体 | 简单句子、单一实体 |

## 错误处理

### 1. 多轮标注失败回退
如果多轮标注失败，系统会自动回退到单类型标注：

```
[错误] 多轮标注失败：API调用异常
[信息] 回退到单类型标注
```

### 2. 调试信息
当多轮标注没有找到目标类型实体时，会显示调试信息：

```
[调试] 多轮标注发现其他类型实体：['姓名', '职业']，但未找到目标类型：地理位置
```

## 性能优化建议

1. **简单场景**：如果句子通常只包含目标实体类型，建议使用 `"single"`
2. **复杂场景**：如果句子可能包含多种实体类型，建议使用 `"comprehensive"`
3. **批量处理**：可以根据句子复杂度动态选择标注方法

## 测试方法

运行测试脚本验证功能：

```bash
python test_comprehensive_annotation.py
```

## 注意事项

1. **API成本**：多轮标注和单类型标注都只需要一次API调用
2. **结果一致性**：两种方法对目标实体的标注结果应该一致
3. **配置灵活性**：可以随时在配置文件中切换标注方法
4. **向后兼容**：新功能完全向后兼容，不影响现有代码

## 未来扩展

1. **多类型标注**：支持同时标注多种目标实体类型
2. **智能选择**：根据句子复杂度自动选择最佳标注方法
3. **实体关系**：识别实体之间的语义关系
4. **质量评估**：自动评估标注质量并提供改进建议 