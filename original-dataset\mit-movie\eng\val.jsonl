{"sentence": "do you have any silent movies starring harpo marx", "tokens": ["do", "you", "have", "any", "silent", "movies", "starring", "harpo", "marx"], "ner_tags": ["O", "O", "O", "O", "B-<PERSON>NRE", "I-GENRE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "im seeking the film paradox soldiers", "tokens": ["im", "seeking", "the", "film", "paradox", "soldiers"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "did patrick macgoohan appear in a weird motion picture", "tokens": ["did", "patrick", "macgoohan", "appear", "in", "a", "weird", "motion", "picture"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-PLOT", "O", "O"]}
{"sentence": "which movie had the big furry monster who worked with the green one eyed monster", "tokens": ["which", "movie", "had", "the", "big", "furry", "monster", "who", "worked", "with", "the", "green", "one", "eyed", "monster"], "ner_tags": ["O", "O", "O", "O", "B-CHARACTER", "I-CHARACTER", "I-CHARACTER", "O", "O", "O", "O", "B-CHARACTER", "I-CHARACTER", "I-CHARACTER", "I-CHARACTER"]}
{"sentence": "which pg 13 star scarlett johannsen", "tokens": ["which", "pg", "13", "star", "scarlett", "johannsen"], "ner_tags": ["O", "B-GENRE", "I-GENRE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "name a prequel to the wizard of oz", "tokens": ["name", "a", "prequel", "to", "the", "wizard", "of", "oz"], "ner_tags": ["O", "O", "B-PLOT", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "who starred in the film disclosure", "tokens": ["who", "starred", "in", "the", "film", "disclosure"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE"]}
{"sentence": "what r rated crime movie stars andrea parker with really good ratings", "tokens": ["what", "r", "rated", "crime", "movie", "stars", "andrea", "parker", "with", "really", "good", "ratings"], "ner_tags": ["O", "B-RATING", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O"]}
{"sentence": "are there any relationship centered 1950 s movies directed by norman tokar that are rated as ok", "tokens": ["are", "there", "any", "relationship", "centered", "1950", "s", "movies", "directed", "by", "norman", "tokar", "that", "are", "rated", "as", "ok"], "ner_tags": ["O", "O", "O", "B-PLOT", "O", "B-YEAR", "I-YEAR", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "O", "B-RATINGS_AVERAGE"]}
{"sentence": "does kirsten dunst star in any romantic comedies", "tokens": ["does", "kirsten", "dunst", "star", "in", "any", "romantic", "comedies"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-GENRE", "I-GENRE"]}
{"sentence": "what is electroma about", "tokens": ["what", "is", "electroma", "about"], "ner_tags": ["O", "O", "B-TITLE", "O"]}
{"sentence": "what fantasy film stars todd tucker and is unrated", "tokens": ["what", "fantasy", "film", "stars", "todd", "tucker", "and", "is", "unrated"], "ner_tags": ["O", "B-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-RATING"]}
{"sentence": "id appreciate it if you could help me find the movie a man for all seasons", "tokens": ["id", "appreciate", "it", "if", "you", "could", "help", "me", "find", "the", "movie", "a", "man", "for", "all", "seasons"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "this nc 17 war film of the past seven decades featured kathy bates", "tokens": ["this", "nc", "17", "war", "film", "of", "the", "past", "seven", "decades", "featured", "kathy", "bates"], "ner_tags": ["O", "B-RATING", "I-RATING", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "list horror movies directed by james cameron", "tokens": ["list", "horror", "movies", "directed", "by", "james", "cameron"], "ner_tags": ["O", "B-GENRE", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "who directed happy feet two", "tokens": ["who", "directed", "happy", "feet", "two"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "on what film did arnold schwarzenegger stars as a robot from the future", "tokens": ["on", "what", "film", "did", "arnold", "schwarzenegger", "stars", "as", "a", "robot", "from", "the", "future"], "ner_tags": ["O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "what is a 1970 r rated history movie that had eight stars and was directed by tiffany kilbourne", "tokens": ["what", "is", "a", "1970", "r", "rated", "history", "movie", "that", "had", "eight", "stars", "and", "was", "directed", "by", "tiffany", "kilbourne"], "ner_tags": ["O", "O", "O", "B-YEAR", "B-RATING", "O", "B-GENRE", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "show me films directed by gary ross starring tobey maguire", "tokens": ["show", "me", "films", "directed", "by", "gary", "ross", "starring", "tobey", "maguire"], "ner_tags": ["O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what 2010 movie has buzz lightyear and mr potato head in it", "tokens": ["what", "2010", "movie", "has", "buzz", "lightyear", "and", "mr", "potato", "head", "in", "it"], "ner_tags": ["O", "B-YEAR", "O", "O", "B-CHARACTER", "I-CHARACTER", "O", "B-CHARACTER", "I-CHARACTER", "I-CHARACTER", "O", "O"]}
{"sentence": "what is average eight unrated film noir in the last two years", "tokens": ["what", "is", "average", "eight", "unrated", "film", "noir", "in", "the", "last", "two", "years"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "B-RATING", "B-GENRE", "I-GENRE", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "was john huston in a rated r adventure", "tokens": ["was", "john", "huston", "in", "a", "rated", "r", "adventure"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "B-RATING", "B-GENRE"]}
{"sentence": "do you have any r rated movies featuring alien invasion", "tokens": ["do", "you", "have", "any", "r", "rated", "movies", "featuring", "alien", "invasion"], "ner_tags": ["O", "O", "O", "O", "B-RATING", "O", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "what are the titles of some western films given excellent ratings that were directed by elliott lester", "tokens": ["what", "are", "the", "titles", "of", "some", "western", "films", "given", "excellent", "ratings", "that", "were", "directed", "by", "elliott", "lester"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-GENRE", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "what are some movies have been released in the past six decades that have cassandra peters in them that got seven or higher ratings", "tokens": ["what", "are", "some", "movies", "have", "been", "released", "in", "the", "past", "six", "decades", "that", "have", "cassandra", "peters", "in", "them", "that", "got", "seven", "or", "higher", "ratings"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "O", "O", "O"]}
{"sentence": "name a western that is rated r", "tokens": ["name", "a", "western", "that", "is", "rated", "r"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "O", "B-RATING"]}
{"sentence": "in the 1970 s what mad scientist films did gavin hood direct", "tokens": ["in", "the", "1970", "s", "what", "mad", "scientist", "films", "did", "gavin", "hood", "direct"], "ner_tags": ["O", "O", "B-YEAR", "I-YEAR", "O", "B-PLOT", "I-PLOT", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O"]}
{"sentence": "list the movies from 1940 rated pg 13 which feature a vacation", "tokens": ["list", "the", "movies", "from", "1940", "rated", "pg", "13", "which", "feature", "a", "vacation"], "ner_tags": ["O", "O", "O", "O", "B-YEAR", "O", "B-RATING", "I-RATING", "O", "O", "O", "B-PLOT"]}
{"sentence": "what was that movie from 2010 called with buzz lightyear and mr potato head", "tokens": ["what", "was", "that", "movie", "from", "2010", "called", "with", "buzz", "lightyear", "and", "mr", "potato", "head"], "ner_tags": ["O", "O", "O", "O", "O", "B-YEAR", "O", "O", "B-CHARACTER", "I-CHARACTER", "O", "B-CHARACTER", "I-CHARACTER", "I-CHARACTER"]}
{"sentence": "what year was star trek first contact released", "tokens": ["what", "year", "was", "star", "trek", "first", "contact", "released"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "O"]}
{"sentence": "what is the last movie directed by alfred hitchcock", "tokens": ["what", "is", "the", "last", "movie", "directed", "by", "alfred", "hitchcock"], "ner_tags": ["O", "O", "O", "B-YEAR", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "is julie andrews in any films for children", "tokens": ["is", "julie", "andrews", "in", "any", "films", "for", "children"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "B-GENRE"]}
{"sentence": "where did the quote you had me at hellow come from", "tokens": ["where", "did", "the", "quote", "you", "had", "me", "at", "hellow", "come", "from"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "is there a film with kelly preston that is rated r about cruelty", "tokens": ["is", "there", "a", "film", "with", "kelly", "preston", "that", "is", "rated", "r", "about", "cruelty"], "ner_tags": ["O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-RATING", "O", "B-PLOT"]}
{"sentence": "could you please show me a website with clips from indiana jones", "tokens": ["could", "you", "please", "show", "me", "a", "website", "with", "clips", "from", "indiana", "jones"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-TRAILER", "O", "B-CHARACTER", "I-CHARACTER"]}
{"sentence": "name any pg rated fantasy movies that include nicole kidman in the cast", "tokens": ["name", "any", "pg", "rated", "fantasy", "movies", "that", "include", "nicole", "kidman", "in", "the", "cast"], "ner_tags": ["O", "O", "B-RATING", "O", "B-GENRE", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O"]}
{"sentence": "list drama movies starring jodie foster from the 1990s", "tokens": ["list", "drama", "movies", "starring", "jodie", "foster", "from", "the", "1990s"], "ner_tags": ["O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-YEAR"]}
{"sentence": "movie information on brokedown palace", "tokens": ["movie", "information", "on", "brokedown", "palace"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "what are some good mystery films", "tokens": ["what", "are", "some", "good", "mystery", "films"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O"]}
{"sentence": "what are some horror movies directed by david fincher", "tokens": ["what", "are", "some", "horror", "movies", "directed", "by", "david", "fincher"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "what movies in the last 2 years are about country music stars", "tokens": ["what", "movies", "in", "the", "last", "2", "years", "are", "about", "country", "music", "stars"], "ner_tags": ["O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "who directed the rocky movies", "tokens": ["who", "directed", "the", "rocky", "movies"], "ner_tags": ["O", "B-DIRECTOR", "O", "B-TITLE", "O"]}
{"sentence": "does brad pitt star in any films that were rated less than 10 positive", "tokens": ["does", "brad", "pitt", "star", "in", "any", "films", "that", "were", "rated", "less", "than", "10", "positive"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "O", "O", "B-REVIEW", "I-REVIEW", "I-REVIEW", "I-REVIEW"]}
{"sentence": "what actor played painless potter", "tokens": ["what", "actor", "played", "painless", "potter"], "ner_tags": ["O", "O", "O", "B-CHARACTER", "I-CHARACTER"]}
{"sentence": "who stars in the movie the six wives of henry lefay", "tokens": ["who", "stars", "in", "the", "movie", "the", "six", "wives", "of", "henry", "lefay"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "what is a good film starring rene russo that is rated pg 13", "tokens": ["what", "is", "a", "good", "film", "starring", "rene", "russo", "that", "is", "rated", "pg", "13"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-RATING", "I-RATING"]}
{"sentence": "what are some great action movies with suspense from 1975", "tokens": ["what", "are", "some", "great", "action", "movies", "with", "suspense", "from", "1975"], "ner_tags": ["O", "O", "O", "B-REVIEW", "B-GENRE", "O", "O", "B-GENRE", "O", "B-YEAR"]}
{"sentence": "how many r rated mystery films did william t hurtz direct", "tokens": ["how", "many", "r", "rated", "mystery", "films", "did", "william", "t", "hurtz", "direct"], "ner_tags": ["O", "O", "B-RATING", "O", "B-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "I-DIRECTOR", "O"]}
{"sentence": "list a really popular mystery starring michael biehn", "tokens": ["list", "a", "really", "popular", "mystery", "starring", "michael", "biehn"], "ner_tags": ["O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "name a patrick mcgoohan in a disney film title", "tokens": ["name", "a", "patrick", "mcgoohan", "in", "a", "disney", "film", "title"], "ner_tags": ["O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-GENRE", "I-GENRE", "O"]}
{"sentence": "list the really popular family movies about growing up released in the last two years", "tokens": ["list", "the", "really", "popular", "family", "movies", "about", "growing", "up", "released", "in", "the", "last", "two", "years"], "ner_tags": ["O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "O", "O", "B-PLOT", "I-PLOT", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "what is an nc 17 movie about jewish politics", "tokens": ["what", "is", "an", "nc", "17", "movie", "about", "jewish", "politics"], "ner_tags": ["O", "O", "O", "B-RATING", "I-RATING", "O", "O", "B-PLOT", "B-GENRE"]}
{"sentence": "what is a highly rated nc 17 1940 s movie about texas", "tokens": ["what", "is", "a", "highly", "rated", "nc", "17", "1940", "s", "movie", "about", "texas"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-RATING", "I-RATING", "B-YEAR", "I-YEAR", "O", "O", "B-PLOT"]}
{"sentence": "who directed doom", "tokens": ["who", "directed", "doom"], "ner_tags": ["O", "O", "B-TITLE"]}
{"sentence": "find the judd apatow movies released in the 2000s", "tokens": ["find", "the", "judd", "apatow", "movies", "released", "in", "the", "2000s"], "ner_tags": ["O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "O", "B-YEAR"]}
{"sentence": "what movies has rachel mcadams played in", "tokens": ["what", "movies", "has", "rachel", "mcadams", "played", "in"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O"]}
{"sentence": "find a movie about indiana jones", "tokens": ["find", "a", "movie", "about", "indiana", "jones"], "ner_tags": ["O", "O", "O", "O", "B-CHARACTER", "I-CHARACTER"]}
{"sentence": "what was a r rated jimmy stewart film that was about ghosts and came out in the past two decades", "tokens": ["what", "was", "a", "r", "rated", "jimmy", "stewart", "film", "that", "was", "about", "ghosts", "and", "came", "out", "in", "the", "past", "two", "decades"], "ner_tags": ["O", "O", "O", "B-RATING", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "B-PLOT", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "in which science fiction movie ewan mcgregor collaborates with scarlett johansson", "tokens": ["in", "which", "science", "fiction", "movie", "ewan", "mcgregor", "collaborates", "with", "scarlett", "johansson"], "ner_tags": ["O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what action movies are based in texas", "tokens": ["what", "action", "movies", "are", "based", "in", "texas"], "ner_tags": ["O", "B-GENRE", "I-GENRE", "O", "B-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "find me the paul rudd movies rated r", "tokens": ["find", "me", "the", "paul", "rudd", "movies", "rated", "r"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "B-RATING", "I-RATING"]}
{"sentence": "im looking for the movie galaxy of terror do you happen to have it", "tokens": ["im", "looking", "for", "the", "movie", "galaxy", "of", "terror", "do", "you", "happen", "to", "have", "it"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "O", "O", "O", "O", "O", "O"]}
{"sentence": "are there any action films in which the plot is someone going undercover", "tokens": ["are", "there", "any", "action", "films", "in", "which", "the", "plot", "is", "someone", "going", "undercover"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "O", "O", "O", "O", "O", "B-PLOT"]}
{"sentence": "who directed a very merry daughter of the bridge", "tokens": ["who", "directed", "a", "very", "merry", "daughter", "of", "the", "bridge"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "looking for a movie starring renee zellweiger and catharine zeta jones", "tokens": ["looking", "for", "a", "movie", "starring", "renee", "zellweiger", "and", "catharine", "zeta", "jones"], "ner_tags": ["O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "I-ACTOR", "I-ACTOR", "I-ACTOR", "I-ACTOR"]}
{"sentence": "who directed the wanderers", "tokens": ["who", "directed", "the", "wanderers"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "was katharine hepburn ever in a fantasy movie in the 1960 s", "tokens": ["was", "katharine", "hepburn", "ever", "in", "a", "fantasy", "movie", "in", "the", "1960", "s"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "what is hunger about", "tokens": ["what", "is", "hunger", "about"], "ner_tags": ["O", "O", "B-TITLE", "O"]}
{"sentence": "what is the plot of the dead pool", "tokens": ["what", "is", "the", "plot", "of", "the", "dead", "pool"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "is there a teen movie which director is clint eastwood", "tokens": ["is", "there", "a", "teen", "movie", "which", "director", "is", "clint", "eastwood"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "which james bond film stars sean connery in the titular role", "tokens": ["which", "james", "bond", "film", "stars", "sean", "connery", "in", "the", "titular", "role"], "ner_tags": ["O", "B-TITLE", "I-TITLE", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O"]}
{"sentence": "show me the movie where kim novak is a witch and jimmy stewart is in love with her", "tokens": ["show", "me", "the", "movie", "where", "kim", "novak", "is", "a", "witch", "and", "jimmy", "stewart", "is", "in", "love", "with", "her"], "ner_tags": ["O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "B-PLOT", "I-PLOT", "I-PLOT", "O", "B-ACTOR", "I-ACTOR", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "which movie was nominated for best actor in 1995", "tokens": ["which", "movie", "was", "nominated", "for", "best", "actor", "in", "1995"], "ner_tags": ["O", "O", "O", "O", "O", "B-REVIEW", "I-REVIEW", "O", "B-YEAR"]}
{"sentence": "which films are directed by steven spielberg", "tokens": ["which", "films", "are", "directed", "by", "steven", "spielberg"], "ner_tags": ["O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "whats the last pg rated horror film you remember", "tokens": ["whats", "the", "last", "pg", "rated", "horror", "film", "you", "remember"], "ner_tags": ["O", "O", "O", "B-RATING", "O", "B-GENRE", "O", "O", "O"]}
{"sentence": "is there a science fiction movie made in 1980 s which director was ingmar bergman", "tokens": ["is", "there", "a", "science", "fiction", "movie", "made", "in", "1980", "s", "which", "director", "was", "ingmar", "bergman"], "ner_tags": ["O", "O", "O", "B-GENRE", "I-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "looking for a film called the spiral staircase starring dorothy mcguire", "tokens": ["looking", "for", "a", "film", "called", "the", "spiral", "staircase", "starring", "dorothy", "mcguire"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "are there any westerns from the 1960s considered must see", "tokens": ["are", "there", "any", "westerns", "from", "the", "1960s", "considered", "must", "see"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-YEAR", "B-REVIEW", "I-REVIEW", "I-REVIEW"]}
{"sentence": "do you know where i can find a movie about a mistress that was made in the 1970 s with a pg 13 rating", "tokens": ["do", "you", "know", "where", "i", "can", "find", "a", "movie", "about", "a", "mistress", "that", "was", "made", "in", "the", "1970", "s", "with", "a", "pg", "13", "rating"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PLOT", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "B-RATING", "I-RATING", "O"]}
{"sentence": "who starred in sorority babes in the dance a thon of death", "tokens": ["who", "starred", "in", "sorority", "babes", "in", "the", "dance", "a", "thon", "of", "death"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "show me a list of movies with a character called indiana jones from the 1980s", "tokens": ["show", "me", "a", "list", "of", "movies", "with", "a", "character", "called", "indiana", "jones", "from", "the", "1980s"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-CHARACTER", "I-CHARACTER", "O", "O", "B-YEAR"]}
{"sentence": "list a pg 13 movie peopled laughed at released in the last three years", "tokens": ["list", "a", "pg", "13", "movie", "peopled", "laughed", "at", "released", "in", "the", "last", "three", "years"], "ner_tags": ["O", "O", "B-RATING", "I-RATING", "O", "O", "B-GENRE", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "what is a 1970 s film noir starring jack wagner", "tokens": ["what", "is", "a", "1970", "s", "film", "noir", "starring", "jack", "wagner"], "ner_tags": ["O", "O", "O", "B-YEAR", "I-YEAR", "B-GENRE", "I-GENRE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what year did battlefield earth come out", "tokens": ["what", "year", "did", "battlefield", "earth", "come", "out"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE", "O", "O"]}
{"sentence": "show me the movie with sherlock holmes in the 2000s", "tokens": ["show", "me", "the", "movie", "with", "sherlock", "holmes", "in", "the", "2000s"], "ner_tags": ["O", "O", "O", "O", "O", "B-CHARACTER", "I-CHARACTER", "O", "O", "B-YEAR"]}
{"sentence": "would you be able to direct me to where i might find the movie priest", "tokens": ["would", "you", "be", "able", "to", "direct", "me", "to", "where", "i", "might", "find", "the", "movie", "priest"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-TITLE"]}
{"sentence": "who portrayed the creature gollum in the lord of the rings movies", "tokens": ["who", "portrayed", "the", "creature", "gollum", "in", "the", "lord", "of", "the", "rings", "movies"], "ner_tags": ["O", "O", "O", "O", "B-CHARACTER", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "O"]}
{"sentence": "how many pirates of the caribbean films are there", "tokens": ["how", "many", "pirates", "of", "the", "caribbean", "films", "are", "there"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "O", "O", "O"]}
{"sentence": "could you list r rated action movies that have actor rob estes in them", "tokens": ["could", "you", "list", "r", "rated", "action", "movies", "that", "have", "actor", "rob", "estes", "in", "them"], "ner_tags": ["O", "O", "O", "B-RATING", "O", "B-GENRE", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O"]}
{"sentence": "did the soundtrack for moulin rouge win any awards", "tokens": ["did", "the", "soundtrack", "for", "moulin", "rouge", "win", "any", "awards"], "ner_tags": ["O", "O", "B-SONG", "O", "B-TITLE", "I-TITLE", "O", "O", "O"]}
{"sentence": "michel orion scott directed mockumentary rated pg 13 in the past four decades", "tokens": ["michel", "orion", "scott", "directed", "mockumentary", "rated", "pg", "13", "in", "the", "past", "four", "decades"], "ner_tags": ["B-DIRECTOR", "I-DIRECTOR", "I-DIRECTOR", "O", "B-GENRE", "O", "B-RATING", "I-RATING", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "find me the movie with the song my heart will go on", "tokens": ["find", "me", "the", "movie", "with", "the", "song", "my", "heart", "will", "go", "on"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-SONG", "I-SONG", "I-SONG", "I-SONG", "I-SONG"]}
{"sentence": "who played in the world is not enough", "tokens": ["who", "played", "in", "the", "world", "is", "not", "enough"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "show me a movie with the song rock a hula in it", "tokens": ["show", "me", "a", "movie", "with", "the", "song", "rock", "a", "hula", "in", "it"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-SONG", "I-SONG", "I-SONG", "O", "O"]}
{"sentence": "name the movie where the antagonist is an invisible alien", "tokens": ["name", "the", "movie", "where", "the", "antagonist", "is", "an", "invisible", "alien"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "what r rated film released in the 1940 s was liked a lot and starred tucker smallwood", "tokens": ["what", "r", "rated", "film", "released", "in", "the", "1940", "s", "was", "liked", "a", "lot", "and", "starred", "tucker", "smallwood"], "ner_tags": ["O", "B-RATING", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "is nicole kidman slated to make any upcoming biographical movies", "tokens": ["is", "nicole", "kidman", "slated", "to", "make", "any", "upcoming", "biographical", "movies"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "B-GENRE", "O"]}
{"sentence": "id like to see captain ron", "tokens": ["id", "like", "to", "see", "captain", "ron"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "show me a list of movies starring christian bale", "tokens": ["show", "me", "a", "list", "of", "movies", "starring", "christian", "bale"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what is the best war movie ever", "tokens": ["what", "is", "the", "best", "war", "movie", "ever"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O", "O"]}
{"sentence": "do you have the r rated thriller from 1990 directed by rob carpenter", "tokens": ["do", "you", "have", "the", "r", "rated", "thriller", "from", "1990", "directed", "by", "rob", "carpenter"], "ner_tags": ["O", "O", "O", "O", "B-RATING", "O", "B-GENRE", "O", "B-YEAR", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "please show me an old german movie with peter lorre", "tokens": ["please", "show", "me", "an", "old", "german", "movie", "with", "peter", "lorre"], "ner_tags": ["O", "O", "O", "O", "B-YEAR", "B-PLOT", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "did orson welles direct the movie wall e", "tokens": ["did", "orson", "welles", "direct", "the", "movie", "wall", "e"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "list an inception movie", "tokens": ["list", "an", "inception", "movie"], "ner_tags": ["O", "O", "B-TITLE", "O"]}
{"sentence": "whats a good adventure movie", "tokens": ["whats", "a", "good", "adventure", "movie"], "ner_tags": ["O", "O", "O", "B-GENRE", "O"]}
{"sentence": "when did the house of sand and fog come out", "tokens": ["when", "did", "the", "house", "of", "sand", "and", "fog", "come", "out"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "B-YEAR", "I-YEAR"]}
{"sentence": "is there a tale movie starring greg wise", "tokens": ["is", "there", "a", "tale", "movie", "starring", "greg", "wise"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what is the movie wilde about", "tokens": ["what", "is", "the", "movie", "wilde", "about"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "O"]}
{"sentence": "show me reviews for summer romantic comedies", "tokens": ["show", "me", "reviews", "for", "summer", "romantic", "comedies"], "ner_tags": ["O", "O", "O", "O", "O", "B-GENRE", "I-GENRE"]}
{"sentence": "find a trailer for kicking and screaming", "tokens": ["find", "a", "trailer", "for", "kicking", "and", "screaming"], "ner_tags": ["O", "O", "B-TRAILER", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "is there a dylan neal 1970 watchable movie", "tokens": ["is", "there", "a", "dylan", "neal", "1970", "watchable", "movie"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "B-YEAR", "B-RATINGS_AVERAGE", "O"]}
{"sentence": "show movies from the 1980s about classical musicians", "tokens": ["show", "movies", "from", "the", "1980s", "about", "classical", "musicians"], "ner_tags": ["O", "O", "O", "O", "B-YEAR", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "find a comedy starring sean penn", "tokens": ["find", "a", "comedy", "starring", "sean", "penn"], "ner_tags": ["O", "O", "B-GENRE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "are there any more batman movies coming out soon", "tokens": ["are", "there", "any", "more", "batman", "movies", "coming", "out", "soon"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "O", "O", "O", "O"]}
{"sentence": "what 1950 movie stared laura gabbert was rated r with a nine rating in the family genre", "tokens": ["what", "1950", "movie", "stared", "laura", "gabbert", "was", "rated", "r", "with", "a", "nine", "rating", "in", "the", "family", "genre"], "ner_tags": ["O", "B-YEAR", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-RATING", "O", "O", "B-RATINGS_AVERAGE", "O", "O", "O", "B-GENRE", "O"]}
{"sentence": "the godfather", "tokens": ["the", "godfather"], "ner_tags": ["B-TITLE", "I-TITLE"]}
{"sentence": "id like to find a pg musical starring actor curly howard that has an average of nine stars", "tokens": ["id", "like", "to", "find", "a", "pg", "musical", "starring", "actor", "curly", "howard", "that", "has", "an", "average", "of", "nine", "stars"], "ner_tags": ["O", "O", "O", "O", "O", "B-RATING", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "list all movie information on sortie des ateliers vibert", "tokens": ["list", "all", "movie", "information", "on", "sortie", "des", "ateliers", "vibert"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "i am looking for a 1980 s biography for a pg movie about a classical composer and it starred greg kinear what was it", "tokens": ["i", "am", "looking", "for", "a", "1980", "s", "biography", "for", "a", "pg", "movie", "about", "a", "classical", "composer", "and", "it", "starred", "greg", "kinear", "what", "was", "it"], "ner_tags": ["O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "B-GENRE", "O", "O", "B-RATING", "O", "O", "O", "B-PLOT", "I-PLOT", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O"]}
{"sentence": "what is the name of the 1980 s frank darabont mockumentary", "tokens": ["what", "is", "the", "name", "of", "the", "1980", "s", "frank", "darabont", "mockumentary"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "B-DIRECTOR", "I-DIRECTOR", "B-GENRE"]}
{"sentence": "what are some g rated films about classical music that were directed by terry jones that received an eight rating from the last nine years", "tokens": ["what", "are", "some", "g", "rated", "films", "about", "classical", "music", "that", "were", "directed", "by", "terry", "jones", "that", "received", "an", "eight", "rating", "from", "the", "last", "nine", "years"], "ner_tags": ["O", "O", "O", "B-RATING", "O", "O", "O", "B-PLOT", "I-PLOT", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "B-RATINGS_AVERAGE", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "is there a comedy film starring al pacino", "tokens": ["is", "there", "a", "comedy", "film", "starring", "al", "pacino"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "who directed sin city", "tokens": ["who", "directed", "sin", "city"], "ner_tags": ["O", "B-DIRECTOR", "B-TITLE", "I-TITLE"]}
{"sentence": "what rated r fantasy film was released in the last eight decades and starred michael shanks", "tokens": ["what", "rated", "r", "fantasy", "film", "was", "released", "in", "the", "last", "eight", "decades", "and", "starred", "michael", "shanks"], "ner_tags": ["O", "O", "B-RATING", "B-GENRE", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "did you see the unrated version of the war film starring sally field", "tokens": ["did", "you", "see", "the", "unrated", "version", "of", "the", "war", "film", "starring", "sally", "field"], "ner_tags": ["O", "O", "O", "O", "B-RATING", "O", "O", "O", "B-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "did russel crowe star in a movie about a mathematician released in 2001", "tokens": ["did", "russel", "crowe", "star", "in", "a", "movie", "about", "a", "mathematician", "released", "in", "2001"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "O", "B-PLOT", "O", "O", "B-PLOT"]}
{"sentence": "is there an animation film with a four rating in the past two years", "tokens": ["is", "there", "an", "animation", "film", "with", "a", "four", "rating", "in", "the", "past", "two", "years"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "B-RATINGS_AVERAGE", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "what mel gibson film made the least amount at the box office upon release", "tokens": ["what", "mel", "gibson", "film", "made", "the", "least", "amount", "at", "the", "box", "office", "upon", "release"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "who stars in the dark night of the scarecrow", "tokens": ["who", "stars", "in", "the", "dark", "night", "of", "the", "scarecrow"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "thrillers from the 1960s", "tokens": ["thrillers", "from", "the", "1960s"], "ner_tags": ["B-GENRE", "O", "O", "B-YEAR"]}
{"sentence": "sigourney weaver starred in what sci fi franchise in space", "tokens": ["sigourney", "weaver", "starred", "in", "what", "sci", "fi", "franchise", "in", "space"], "ner_tags": ["B-ACTOR", "I-ACTOR", "O", "O", "O", "B-GENRE", "I-GENRE", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "are there any historical pg 13 movies by john huston", "tokens": ["are", "there", "any", "historical", "pg", "13", "movies", "by", "john", "huston"], "ner_tags": ["O", "O", "O", "B-GENRE", "B-RATING", "I-RATING", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "is there an r movie from 1994 that got really good reviews", "tokens": ["is", "there", "an", "r", "movie", "from", "1994", "that", "got", "really", "good", "reviews"], "ner_tags": ["O", "O", "O", "B-RATING", "O", "O", "B-YEAR", "O", "O", "B-REVIEW", "I-REVIEW", "I-REVIEW"]}
{"sentence": "what are some jason biggs movies from the 90s", "tokens": ["what", "are", "some", "jason", "biggs", "movies", "from", "the", "90s"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-YEAR"]}
{"sentence": "who played as agent smith in the matrix", "tokens": ["who", "played", "as", "agent", "smith", "in", "the", "matrix"], "ner_tags": ["O", "O", "O", "B-CHARACTER", "I-CHARACTER", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "who was commander data", "tokens": ["who", "was", "commander", "data"], "ner_tags": ["O", "O", "B-CHARACTER", "I-CHARACTER"]}
{"sentence": "who plays rhett butler in gone with the wind", "tokens": ["who", "plays", "rhett", "butler", "in", "gone", "with", "the", "wind"], "ner_tags": ["O", "O", "B-CHARACTER", "I-CHARACTER", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "has tom cruise ever been in a disaster movie", "tokens": ["has", "tom", "cruise", "ever", "been", "in", "a", "disaster", "movie"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "B-GENRE", "O"]}
{"sentence": "did quentin tarantino direct any teen movies with a nine stars and above rating", "tokens": ["did", "quentin", "tarantino", "direct", "any", "teen", "movies", "with", "a", "nine", "stars", "and", "above", "rating"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-GENRE", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O"]}
{"sentence": "what 1990 s military film revolved around a concentration camp", "tokens": ["what", "1990", "s", "military", "film", "revolved", "around", "a", "concentration", "camp"], "ner_tags": ["O", "B-YEAR", "I-YEAR", "B-GENRE", "O", "O", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "show me comedy films with steve martin in which arent rated r", "tokens": ["show", "me", "comedy", "films", "with", "steve", "martin", "in", "which", "arent", "rated", "r"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-RATING", "I-RATING", "I-RATING"]}
{"sentence": "tell me some fantasy movies that have been made in the last seven decades", "tokens": ["tell", "me", "some", "fantasy", "movies", "that", "have", "been", "made", "in", "the", "last", "seven", "decades"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "what fun movie about jealousy from the last seven decades rated g was directed by john humber", "tokens": ["what", "fun", "movie", "about", "jealousy", "from", "the", "last", "seven", "decades", "rated", "g", "was", "directed", "by", "john", "humber"], "ner_tags": ["O", "B-GENRE", "O", "O", "B-PLOT", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "B-RATING", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "who was hal in 2001 a space odyssey", "tokens": ["who", "was", "hal", "in", "2001", "a", "space", "odyssey"], "ner_tags": ["O", "O", "B-CHARACTER", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "find a newly released comedy with great reviews", "tokens": ["find", "a", "newly", "released", "comedy", "with", "great", "reviews"], "ner_tags": ["B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "who is the director of the avatar movie", "tokens": ["who", "is", "the", "director", "of", "the", "avatar", "movie"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-TITLE", "O"]}
{"sentence": "who stars in the new movie hunger games", "tokens": ["who", "stars", "in", "the", "new", "movie", "hunger", "games"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "what are some animated films with really good ratings", "tokens": ["what", "are", "some", "animated", "films", "with", "really", "good", "ratings"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O"]}
{"sentence": "what is the most recent movie directed by mel brooks", "tokens": ["what", "is", "the", "most", "recent", "movie", "directed", "by", "mel", "brooks"], "ner_tags": ["O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "what is a mockumentary movie that stars actor john wayne", "tokens": ["what", "is", "a", "mockumentary", "movie", "that", "stars", "actor", "john", "wayne"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what year was theres only one jimmy grimble was released", "tokens": ["what", "year", "was", "theres", "only", "one", "jimmy", "grimble", "was", "released"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "O", "O"]}
{"sentence": "do you happen to know where id be able to find an r rated drama from the 1960 s with actor weird al yancovic in it", "tokens": ["do", "you", "happen", "to", "know", "where", "id", "be", "able", "to", "find", "an", "r", "rated", "drama", "from", "the", "1960", "s", "with", "actor", "weird", "al", "yancovic", "in", "it"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-RATING", "O", "B-GENRE", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "B-ACTOR", "I-ACTOR", "I-ACTOR", "O", "O"]}
{"sentence": "is there a very good ratings movie about a tale that stars kellie martin", "tokens": ["is", "there", "a", "very", "good", "ratings", "movie", "about", "a", "tale", "that", "stars", "kellie", "martin"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "O", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what is a short film directed by craig ferguson", "tokens": ["what", "is", "a", "short", "film", "directed", "by", "craig", "ferguson"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "find a highly recommended childrens film from the past six years directed by gregg champion", "tokens": ["find", "a", "highly", "recommended", "childrens", "film", "from", "the", "past", "six", "years", "directed", "by", "gregg", "champion"], "ner_tags": ["O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "can you find me a steven spielberg movie", "tokens": ["can", "you", "find", "me", "a", "steven", "spielberg", "movie"], "ner_tags": ["O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O"]}
{"sentence": "what movie stars samuel l jackson and john travolta", "tokens": ["what", "movie", "stars", "samuel", "l", "jackson", "and", "john", "travolta"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "I-ACTOR", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "did ron howard direct any disaster movies that are rated pg 13", "tokens": ["did", "ron", "howard", "direct", "any", "disaster", "movies", "that", "are", "rated", "pg", "13"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-GENRE", "O", "O", "O", "O", "B-RATING", "I-RATING"]}
{"sentence": "find me the best horror movie from 2002", "tokens": ["find", "me", "the", "best", "horror", "movie", "from", "2002"], "ner_tags": ["O", "O", "O", "B-REVIEW", "B-GENRE", "O", "O", "B-YEAR"]}
{"sentence": "list a film having the title maurice", "tokens": ["list", "a", "film", "having", "the", "title", "maurice"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-TITLE"]}
{"sentence": "when did vantage point come out", "tokens": ["when", "did", "vantage", "point", "come", "out"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "O", "O"]}
{"sentence": "im looking a thriller featuring a forest fires", "tokens": ["im", "looking", "a", "thriller", "featuring", "a", "forest", "fires"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "is there a musical movie starring sean connery", "tokens": ["is", "there", "a", "musical", "movie", "starring", "sean", "connery"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "has gary cooper ever been in a good romantic comedy", "tokens": ["has", "gary", "cooper", "ever", "been", "in", "a", "good", "romantic", "comedy"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "B-GENRE", "I-GENRE"]}
{"sentence": "which film won best picture in 1969", "tokens": ["which", "film", "won", "best", "picture", "in", "1969"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "B-YEAR"]}
{"sentence": "in the 1960 s what pg 13 portrait film with ricardo montalbon received average ratings", "tokens": ["in", "the", "1960", "s", "what", "pg", "13", "portrait", "film", "with", "ricardo", "montalbon", "received", "average", "ratings"], "ner_tags": ["O", "O", "B-YEAR", "I-YEAR", "O", "B-RATING", "I-RATING", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR", "O", "B-RATINGS_AVERAGE", "O"]}
{"sentence": "who what the lady in happy gilmore", "tokens": ["who", "what", "the", "lady", "in", "happy", "gilmore"], "ner_tags": ["O", "O", "O", "B-CHARACTER", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "name the post apocalyptic movie starring viggo mortensen", "tokens": ["name", "the", "post", "apocalyptic", "movie", "starring", "viggo", "mortensen"], "ner_tags": ["O", "O", "B-PLOT", "I-PLOT", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what movie studio produced digital monster x evolution", "tokens": ["what", "movie", "studio", "produced", "digital", "monster", "x", "evolution"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "list any mockumentary made in the past three decades featuring lisa howard", "tokens": ["list", "any", "mockumentary", "made", "in", "the", "past", "three", "decades", "featuring", "lisa", "howard"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "list movies about santa clause from the 1990s", "tokens": ["list", "movies", "about", "santa", "clause", "from", "the", "1990s"], "ner_tags": ["O", "O", "O", "B-CHARACTER", "I-CHARACTER", "O", "O", "B-YEAR"]}
{"sentence": "which character does kathy bates play in titanic", "tokens": ["which", "character", "does", "kathy", "bates", "play", "in", "titanic"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-TITLE"]}
{"sentence": "which animated film included the character jiminy cricket", "tokens": ["which", "animated", "film", "included", "the", "character", "jiminy", "cricket"], "ner_tags": ["O", "B-GENRE", "O", "O", "O", "O", "B-CHARACTER", "I-CHARACTER"]}
{"sentence": "find me a movie that my kids can watch", "tokens": ["find", "me", "a", "movie", "that", "my", "kids", "can", "watch"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-GENRE", "O", "O"]}
{"sentence": "what song was played at the end of titanic", "tokens": ["what", "song", "was", "played", "at", "the", "end", "of", "titanic"], "ner_tags": ["O", "B-SONG", "O", "O", "O", "O", "O", "O", "B-TITLE"]}
{"sentence": "which science fiction film depicts the earth being destroyed by an asteroid", "tokens": ["which", "science", "fiction", "film", "depicts", "the", "earth", "being", "destroyed", "by", "an", "asteroid"], "ner_tags": ["O", "B-GENRE", "I-GENRE", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "list all movie information on the war bride", "tokens": ["list", "all", "movie", "information", "on", "the", "war", "bride"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "what is the top rated martin scorsesy moive", "tokens": ["what", "is", "the", "top", "rated", "martin", "scorsesy", "moive"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-DIRECTOR", "I-DIRECTOR", "O"]}
{"sentence": "who was the star actor in highway runnery", "tokens": ["who", "was", "the", "star", "actor", "in", "highway", "runnery"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "what pg movies has liza minnelli been in from the last six decades", "tokens": ["what", "pg", "movies", "has", "liza", "minnelli", "been", "in", "from", "the", "last", "six", "decades"], "ner_tags": ["O", "B-RATING", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "find earth girls are easy", "tokens": ["find", "earth", "girls", "are", "easy"], "ner_tags": ["O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "who were the actors in the shining and what is the plot of the movie", "tokens": ["who", "were", "the", "actors", "in", "the", "shining", "and", "what", "is", "the", "plot", "of", "the", "movie"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "are there any r rated comedies starring ben stiller", "tokens": ["are", "there", "any", "r", "rated", "comedies", "starring", "ben", "stiller"], "ner_tags": ["O", "O", "O", "B-RATING", "I-RATING", "B-GENRE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "is there a docudrama about the charley chaplin", "tokens": ["is", "there", "a", "docudrama", "about", "the", "charley", "chaplin"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "show me jon voight movies from the 1970s", "tokens": ["show", "me", "jon", "voight", "movies", "from", "the", "1970s"], "ner_tags": ["O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-YEAR"]}
{"sentence": "find movies action movies starring heath ledger", "tokens": ["find", "movies", "action", "movies", "starring", "heath", "ledger"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what thriller received five stars and starred susan sarandon", "tokens": ["what", "thriller", "received", "five", "stars", "and", "starred", "susan", "sarandon"], "ner_tags": ["O", "B-GENRE", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what r rated history movie starring jonathan taylor thomas received a nine rating", "tokens": ["what", "r", "rated", "history", "movie", "starring", "jonathan", "taylor", "thomas", "received", "a", "nine", "rating"], "ner_tags": ["O", "B-RATING", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR", "I-ACTOR", "O", "O", "B-RATINGS_AVERAGE", "O"]}
{"sentence": "is avatar a metaphor for environmental causes", "tokens": ["is", "avatar", "a", "metaphor", "for", "environmental", "causes"], "ner_tags": ["O", "B-TITLE", "O", "O", "O", "O", "O"]}
{"sentence": "list disney movies rated five stars and also rated g for me to watch", "tokens": ["list", "disney", "movies", "rated", "five", "stars", "and", "also", "rated", "g", "for", "me", "to", "watch"], "ner_tags": ["O", "B-GENRE", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "O", "B-RATING", "O", "O", "O", "O"]}
{"sentence": "who starred in the house of sand and fog", "tokens": ["who", "starred", "in", "the", "house", "of", "sand", "and", "fog"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "is there a movie starring matt dillon released in the past ten years about a lion that was rated r and watchable", "tokens": ["is", "there", "a", "movie", "starring", "matt", "dillon", "released", "in", "the", "past", "ten", "years", "about", "a", "lion", "that", "was", "rated", "r", "and", "watchable"], "ner_tags": ["O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "B-PLOT", "O", "O", "O", "B-RATING", "O", "B-RATINGS_AVERAGE"]}
{"sentence": "are there any g rated thrillers about a power struggle", "tokens": ["are", "there", "any", "g", "rated", "thrillers", "about", "a", "power", "struggle"], "ner_tags": ["O", "O", "O", "B-RATING", "O", "B-GENRE", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "what is a highly recommended biography on polish resistance directed by fred williamson", "tokens": ["what", "is", "a", "highly", "recommended", "biography", "on", "polish", "resistance", "directed", "by", "fred", "williamson"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "O", "B-PLOT", "I-PLOT", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "when did sin city release", "tokens": ["when", "did", "sin", "city", "release"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "O"]}
{"sentence": "what was the cast of goodfellas", "tokens": ["what", "was", "the", "cast", "of", "goodfellas"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE"]}
{"sentence": "show me all daniel day lewis movies", "tokens": ["show", "me", "all", "daniel", "day", "lewis", "movies"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "I-ACTOR", "O"]}
{"sentence": "what soundtracks have song by prince", "tokens": ["what", "soundtracks", "have", "song", "by", "prince"], "ner_tags": ["O", "B-SONG", "O", "O", "O", "B-SONG"]}
{"sentence": "what g rated action movie that came out last year was about agents", "tokens": ["what", "g", "rated", "action", "movie", "that", "came", "out", "last", "year", "was", "about", "agents"], "ner_tags": ["O", "B-RATING", "O", "B-GENRE", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "B-PLOT"]}
{"sentence": "did marlon brando ever have a role in a fantasy film", "tokens": ["did", "marlon", "brando", "ever", "have", "a", "role", "in", "a", "fantasy", "film"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "O", "B-GENRE", "O"]}
{"sentence": "who was the lead actor in bourne ultimatum", "tokens": ["who", "was", "the", "lead", "actor", "in", "bourne", "ultimatum"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "what movies have kelly clarkson on the soundtrack", "tokens": ["what", "movies", "have", "kelly", "clarkson", "on", "the", "soundtrack"], "ner_tags": ["O", "O", "O", "B-SONG", "I-SONG", "O", "O", "O"]}
{"sentence": "did john ford ever direct a movie for children in the 2000 s", "tokens": ["did", "john", "ford", "ever", "direct", "a", "movie", "for", "children", "in", "the", "2000", "s"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "O", "O", "B-GENRE", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "what g rated animated film did frank darabont direct", "tokens": ["what", "g", "rated", "animated", "film", "did", "frank", "darabont", "direct"], "ner_tags": ["O", "B-RATING", "O", "B-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O"]}
{"sentence": "all movies in the thriller genre", "tokens": ["all", "movies", "in", "the", "thriller", "genre"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O"]}
{"sentence": "what is the adventures of tintin rated", "tokens": ["what", "is", "the", "adventures", "of", "tintin", "rated"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "B-RATING"]}
{"sentence": "what comic movie that was directed by lisa wolfinger that a ratings average of nine and was rated g but came out in the last ten years", "tokens": ["what", "comic", "movie", "that", "was", "directed", "by", "lisa", "wolfinger", "that", "a", "ratings", "average", "of", "nine", "and", "was", "rated", "g", "but", "came", "out", "in", "the", "last", "ten", "years"], "ner_tags": ["O", "B-GENRE", "O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "O", "O", "O", "B-RATING", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "show me the movie with maggie smith as a scottish schoolteacher", "tokens": ["show", "me", "the", "movie", "with", "maggie", "smith", "as", "a", "scottish", "schoolteacher"], "ner_tags": ["O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-CHARACTER", "I-CHARACTER"]}
{"sentence": "do you have red sonja", "tokens": ["do", "you", "have", "red", "sonja"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "show me a list of movies directed by gus van sant", "tokens": ["show", "me", "a", "list", "of", "movies", "directed", "by", "gus", "van", "sant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "i want a jamie lee curtis romance film", "tokens": ["i", "want", "a", "jamie", "lee", "curtis", "romance", "film"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "I-ACTOR", "B-GENRE", "O"]}
{"sentence": "find me the newest movie directed by david fincher", "tokens": ["find", "me", "the", "newest", "movie", "directed", "by", "david", "fincher"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "when was my cousin vinny released", "tokens": ["when", "was", "my", "cousin", "vinny", "released"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "O"]}
{"sentence": "what animated movie has david lynch directed", "tokens": ["what", "animated", "movie", "has", "david", "lynch", "directed"], "ner_tags": ["O", "B-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O"]}
{"sentence": "how many movies were rated g in 1991", "tokens": ["how", "many", "movies", "were", "rated", "g", "in", "1991"], "ner_tags": ["O", "O", "O", "O", "B-RATING", "I-RATING", "O", "B-YEAR"]}
{"sentence": "name a japanese horror film about mushrooms", "tokens": ["name", "a", "japanese", "horror", "film", "about", "mushrooms"], "ner_tags": ["O", "O", "B-GENRE", "I-GENRE", "O", "O", "B-PLOT"]}
{"sentence": "i want to see a movie about salsa dancers", "tokens": ["i", "want", "to", "see", "a", "movie", "about", "salsa", "dancers"], "ner_tags": ["O", "O", "O", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "which movies were directed by george lucas", "tokens": ["which", "movies", "were", "directed", "by", "george", "lucas"], "ner_tags": ["O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "list a science fiction stewart sugg g film from the last two decades", "tokens": ["list", "a", "science", "fiction", "stewart", "sugg", "g", "film", "from", "the", "last", "two", "decades"], "ner_tags": ["O", "O", "B-GENRE", "I-GENRE", "B-DIRECTOR", "I-DIRECTOR", "B-RATING", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "what is the title of the 1950 s thriller directed by arthur alston that has a rating of six", "tokens": ["what", "is", "the", "title", "of", "the", "1950", "s", "thriller", "directed", "by", "arthur", "alston", "that", "has", "a", "rating", "of", "six"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "B-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE"]}
{"sentence": "what was the third thin man film", "tokens": ["what", "was", "the", "third", "thin", "man", "film"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE", "O"]}
{"sentence": "play a trailer for saving private ryan", "tokens": ["play", "a", "trailer", "for", "saving", "private", "ryan"], "ner_tags": ["O", "O", "B-TRAILER", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "find a 2002 movie about a murderer", "tokens": ["find", "a", "2002", "movie", "about", "a", "murderer"], "ner_tags": ["O", "O", "B-YEAR", "O", "B-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "what film genre is goodfellas", "tokens": ["what", "film", "genre", "is", "goodfellas"], "ner_tags": ["O", "O", "O", "O", "B-TITLE"]}
{"sentence": "is wall e a rip off of short circuit", "tokens": ["is", "wall", "e", "a", "rip", "off", "of", "short", "circuit"], "ner_tags": ["O", "B-TITLE", "I-TITLE", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what film had a character named bunny watson", "tokens": ["what", "film", "had", "a", "character", "named", "bunny", "watson"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-CHARACTER", "I-CHARACTER"]}
{"sentence": "find me a trailer for a romantic comedy from this year", "tokens": ["find", "me", "a", "trailer", "for", "a", "romantic", "comedy", "from", "this", "year"], "ner_tags": ["O", "O", "O", "B-TRAILER", "O", "O", "B-GENRE", "I-GENRE", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "who starred in winters bone", "tokens": ["who", "starred", "in", "winters", "bone"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "did milos forman direct a musical film in the 1970s", "tokens": ["did", "milos", "forman", "direct", "a", "musical", "film", "in", "the", "1970s"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-GENRE", "O", "O", "O", "B-YEAR"]}
{"sentence": "is there a biography movie from the 1960 s with actor joseph gordon levitt and is rated r", "tokens": ["is", "there", "a", "biography", "movie", "from", "the", "1960", "s", "with", "actor", "joseph", "gordon", "levitt", "and", "is", "rated", "r"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "B-ACTOR", "I-ACTOR", "I-ACTOR", "O", "O", "O", "B-RATING"]}
{"sentence": "are there any ok movies about redemption directed by louis malle", "tokens": ["are", "there", "any", "ok", "movies", "about", "redemption", "directed", "by", "louis", "malle"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "O", "O", "B-PLOT", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "where can i find the movie scooby doo and the loch ness monster", "tokens": ["where", "can", "i", "find", "the", "movie", "scooby", "doo", "and", "the", "loch", "ness", "monster"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "list a documentary pg 13 rated movie starring paul newman", "tokens": ["list", "a", "documentary", "pg", "13", "rated", "movie", "starring", "paul", "newman"], "ner_tags": ["O", "O", "B-GENRE", "B-RATING", "I-RATING", "O", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "in the past three decades has jean claude van damme ever done a musical", "tokens": ["in", "the", "past", "three", "decades", "has", "jean", "claude", "van", "damme", "ever", "done", "a", "musical"], "ner_tags": ["O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "B-DIRECTOR", "I-DIRECTOR", "I-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "B-GENRE"]}
{"sentence": "what 90s movies had kurt russell in them", "tokens": ["what", "90s", "movies", "had", "kurt", "russell", "in", "them"], "ner_tags": ["O", "B-YEAR", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O"]}
{"sentence": "looking for a robert altman film about high fashion", "tokens": ["looking", "for", "a", "robert", "altman", "film", "about", "high", "fashion"], "ner_tags": ["O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "what is the best romantic comedy that came out in 2011", "tokens": ["what", "is", "the", "best", "romantic", "comedy", "that", "came", "out", "in", "2011"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "I-GENRE", "O", "O", "O", "O", "O"]}
{"sentence": "where would i find an action movie rated pg 13 starring dougray scott living in the 2000 s", "tokens": ["where", "would", "i", "find", "an", "action", "movie", "rated", "pg", "13", "starring", "dougray", "scott", "living", "in", "the", "2000", "s"], "ner_tags": ["O", "O", "O", "O", "O", "B-GENRE", "O", "O", "B-RATING", "I-RATING", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "find me an nc 17 jaclyn smith movie", "tokens": ["find", "me", "an", "nc", "17", "jaclyn", "smith", "movie"], "ner_tags": ["O", "O", "O", "B-RATING", "I-RATING", "B-ACTOR", "I-ACTOR", "O"]}
{"sentence": "any entertaining movies in the 1990 s directed by lawrence schiller", "tokens": ["any", "entertaining", "movies", "in", "the", "1990", "s", "directed", "by", "lawrence", "schiller"], "ner_tags": ["O", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "what classic movie has rosebud in it", "tokens": ["what", "classic", "movie", "has", "rosebud", "in", "it"], "ner_tags": ["O", "O", "O", "O", "B-PLOT", "O", "O"]}
{"sentence": "last year what unrated survival movies were produced", "tokens": ["last", "year", "what", "unrated", "survival", "movies", "were", "produced"], "ner_tags": ["B-YEAR", "I-YEAR", "O", "B-RATING", "B-PLOT", "O", "O", "O"]}
{"sentence": "has there been a movie that is unrated and fantasy based in the last ten decades that was directed by ryan cavalline", "tokens": ["has", "there", "been", "a", "movie", "that", "is", "unrated", "and", "fantasy", "based", "in", "the", "last", "ten", "decades", "that", "was", "directed", "by", "ryan", "cavalline"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-RATING", "O", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "has anthony rapp been in any movies about friends that was received well by people in the past five years", "tokens": ["has", "anthony", "rapp", "been", "in", "any", "movies", "about", "friends", "that", "was", "received", "well", "by", "people", "in", "the", "past", "five", "years"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "B-PLOT", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "what thrillers have larenz tate been in during the past ten years", "tokens": ["what", "thrillers", "have", "larenz", "tate", "been", "in", "during", "the", "past", "ten", "years"], "ner_tags": ["O", "B-GENRE", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "who stars in jack frost", "tokens": ["who", "stars", "in", "jack", "frost"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "what was the name of the character played by antonio banderas in interview with the vampire", "tokens": ["what", "was", "the", "name", "of", "the", "character", "played", "by", "antonio", "banderas", "in", "interview", "with", "the", "vampire"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "what year was bird on a wire released", "tokens": ["what", "year", "was", "bird", "on", "a", "wire", "released"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "O"]}
{"sentence": "a g rated film starring juliana margulies", "tokens": ["a", "g", "rated", "film", "starring", "juliana", "margulies"], "ner_tags": ["O", "B-RATING", "O", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "i want to watch a tim burton movie about a barber", "tokens": ["i", "want", "to", "watch", "a", "tim", "burton", "movie", "about", "a", "barber"], "ner_tags": ["O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "are there any romance movies with vampires", "tokens": ["are", "there", "any", "romance", "movies", "with", "vampires"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-PLOT"]}
{"sentence": "find movies directed by rob reiner with kevin bacon", "tokens": ["find", "movies", "directed", "by", "rob", "reiner", "with", "kevin", "bacon"], "ner_tags": ["B-ACTOR", "I-ACTOR", "I-ACTOR", "I-ACTOR", "I-ACTOR", "I-ACTOR", "I-ACTOR", "I-ACTOR", "I-ACTOR"]}
{"sentence": "blade runner", "tokens": ["blade", "runner"], "ner_tags": ["B-TITLE", "I-TITLE"]}
{"sentence": "find me the movie with the song youll be in my heart", "tokens": ["find", "me", "the", "movie", "with", "the", "song", "youll", "be", "in", "my", "heart"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-SONG", "I-SONG", "I-SONG", "I-SONG", "I-SONG"]}
{"sentence": "which is the best transformers movie", "tokens": ["which", "is", "the", "best", "transformers", "movie"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "B-TITLE", "O"]}
{"sentence": "worst amp lowest rated movies of the 2000s", "tokens": ["worst", "amp", "lowest", "rated", "movies", "of", "the", "2000s"], "ner_tags": ["B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "O", "B-YEAR"]}
{"sentence": "what are the best movies based on books", "tokens": ["what", "are", "the", "best", "movies", "based", "on", "books"], "ner_tags": ["O", "O", "O", "B-REVIEW", "O", "O", "O", "O"]}
{"sentence": "what was the first film that daniel day lewis starred in", "tokens": ["what", "was", "the", "first", "film", "that", "daniel", "day", "lewis", "starred", "in"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "I-ACTOR", "O", "O"]}
{"sentence": "what are some comedies directed by david lynch made in the 2010 s", "tokens": ["what", "are", "some", "comedies", "directed", "by", "david", "lynch", "made", "in", "the", "2010", "s"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "show me films directed by f gary gray with edward norton", "tokens": ["show", "me", "films", "directed", "by", "f", "gary", "gray", "with", "edward", "norton"], "ner_tags": ["O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "I-DIRECTOR", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "find a movie with lots of explosions", "tokens": ["find", "a", "movie", "with", "lots", "of", "explosions"], "ner_tags": ["O", "O", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "e t the extra terrestrial", "tokens": ["e", "t", "the", "extra", "terrestrial"], "ner_tags": ["B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "what is the mpaa rating on j edgar", "tokens": ["what", "is", "the", "mpaa", "rating", "on", "j", "edgar"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "did clint eastwood ever play a pacifist", "tokens": ["did", "clint", "eastwood", "ever", "play", "a", "pacifist"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "B-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "what scary 1950 movie was deemed all right in ratings", "tokens": ["what", "scary", "1950", "movie", "was", "deemed", "all", "right", "in", "ratings"], "ner_tags": ["O", "B-GENRE", "B-YEAR", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O"]}
{"sentence": "what police movie that has come out in the past decade rated four out of ten that featured david mccallum", "tokens": ["what", "police", "movie", "that", "has", "come", "out", "in", "the", "past", "decade", "rated", "four", "out", "of", "ten", "that", "featured", "david", "mccallum"], "ner_tags": ["O", "B-GENRE", "O", "O", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "B-RATINGS_AVERAGE", "O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "find a john malcovich period film", "tokens": ["find", "a", "john", "malcovich", "period", "film"], "ner_tags": ["O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O"]}
{"sentence": "find me the movie thats has the song joyful joyful", "tokens": ["find", "me", "the", "movie", "thats", "has", "the", "song", "joyful", "joyful"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-SONG", "I-SONG"]}
{"sentence": "what is a science fiction move that is rated r", "tokens": ["what", "is", "a", "science", "fiction", "move", "that", "is", "rated", "r"], "ner_tags": ["O", "O", "O", "B-GENRE", "I-GENRE", "O", "O", "O", "O", "B-RATING"]}
{"sentence": "find the steve martin film noir parody with all the clips from classic movies", "tokens": ["find", "the", "steve", "martin", "film", "noir", "parody", "with", "all", "the", "clips", "from", "classic", "movies"], "ner_tags": ["O", "O", "B-ACTOR", "I-ACTOR", "B-GENRE", "I-GENRE", "I-GENRE", "O", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "were there any good spaghetti westerns in the 1990 s with a pg rating", "tokens": ["were", "there", "any", "good", "spaghetti", "westerns", "in", "the", "1990", "s", "with", "a", "pg", "rating"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "I-GENRE", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "B-RATING", "O"]}
{"sentence": "please list some pg western movies from the last three years", "tokens": ["please", "list", "some", "pg", "western", "movies", "from", "the", "last", "three", "years"], "ner_tags": ["O", "O", "O", "B-RATING", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "is there a fantasy movie starring stan kirsch", "tokens": ["is", "there", "a", "fantasy", "movie", "starring", "stan", "kirsch"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "is there any info about this movie shakes the clown", "tokens": ["is", "there", "any", "info", "about", "this", "movie", "shakes", "the", "clown"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "who directed wildcats", "tokens": ["who", "directed", "wildcats"], "ner_tags": ["O", "O", "B-TITLE"]}
{"sentence": "what sci fi films were very popular in the last four decades that were directed by shirley barrett", "tokens": ["what", "sci", "fi", "films", "were", "very", "popular", "in", "the", "last", "four", "decades", "that", "were", "directed", "by", "shirley", "barrett"], "ner_tags": ["O", "B-GENRE", "I-GENRE", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "would you consider silence of the lambs a really good anthony hopkins mystery movie or more of a horror movie", "tokens": ["would", "you", "consider", "silence", "of", "the", "lambs", "a", "really", "good", "anthony", "hopkins", "mystery", "movie", "or", "more", "of", "a", "horror", "movie"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-ACTOR", "I-ACTOR", "B-GENRE", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "did clint eastwood direct the movie wall e", "tokens": ["did", "clint", "eastwood", "direct", "the", "movie", "wall", "e"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "which movies are similar to gnomeo and juliet", "tokens": ["which", "movies", "are", "similar", "to", "gnomeo", "and", "juliet"], "ner_tags": ["O", "O", "O", "B-REVIEW", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "how many r rated comedies star jonah hill", "tokens": ["how", "many", "r", "rated", "comedies", "star", "jonah", "hill"], "ner_tags": ["O", "O", "B-RATING", "I-RATING", "B-GENRE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "have any fantasy movies been released this year", "tokens": ["have", "any", "fantasy", "movies", "been", "released", "this", "year"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "show me the movie that has the quote you complete me in it", "tokens": ["show", "me", "the", "movie", "that", "has", "the", "quote", "you", "complete", "me", "in", "it"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "find a pg 13 rated romantic drama movie", "tokens": ["find", "a", "pg", "13", "rated", "romantic", "drama", "movie"], "ner_tags": ["O", "O", "B-RATING", "I-RATING", "O", "B-GENRE", "I-GENRE", "O"]}
{"sentence": "what r rated adventure movies starring al pacino are considered must see", "tokens": ["what", "r", "rated", "adventure", "movies", "starring", "al", "pacino", "are", "considered", "must", "see"], "ner_tags": ["O", "B-RATING", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "i want a sci fi movie rated g directed by georgina riedel", "tokens": ["i", "want", "a", "sci", "fi", "movie", "rated", "g", "directed", "by", "georgina", "riedel"], "ner_tags": ["O", "O", "O", "B-GENRE", "I-GENRE", "O", "O", "B-RATING", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "what animated films are rated pg", "tokens": ["what", "animated", "films", "are", "rated", "pg"], "ner_tags": ["O", "B-GENRE", "O", "O", "O", "B-RATING"]}
{"sentence": "name the the movie featuring drew barrymore as a director", "tokens": ["name", "the", "the", "movie", "featuring", "drew", "barrymore", "as", "a", "director"], "ner_tags": ["O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O"]}
{"sentence": "what is the lowest rated movie about sex", "tokens": ["what", "is", "the", "lowest", "rated", "movie", "about", "sex"], "ner_tags": ["O", "O", "O", "B-REVIEW", "I-REVIEW", "O", "O", "B-PLOT"]}
{"sentence": "show me some stuff about johnny mnemonic", "tokens": ["show", "me", "some", "stuff", "about", "johnny", "mnemonic"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "how many films does sergio leone have", "tokens": ["how", "many", "films", "does", "sergio", "leone", "have"], "ner_tags": ["O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O"]}
{"sentence": "run the hidden fortress", "tokens": ["run", "the", "hidden", "fortress"], "ner_tags": ["O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "what is a short pg film from the year 2000", "tokens": ["what", "is", "a", "short", "pg", "film", "from", "the", "year", "2000"], "ner_tags": ["O", "O", "O", "B-GENRE", "B-RATING", "O", "O", "O", "O", "B-YEAR"]}
{"sentence": "looking for a movie starring robert duvall about a has been country singer", "tokens": ["looking", "for", "a", "movie", "starring", "robert", "duvall", "about", "a", "has", "been", "country", "singer"], "ner_tags": ["O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "do you have the four star movie from the 1990 s featuring animation", "tokens": ["do", "you", "have", "the", "four", "star", "movie", "from", "the", "1990", "s", "featuring", "animation"], "ner_tags": ["O", "O", "O", "O", "B-RATINGS_AVERAGE", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "B-GENRE"]}
{"sentence": "what is the first song annette sings in beach blanket bingo", "tokens": ["what", "is", "the", "first", "song", "annette", "sings", "in", "beach", "blanket", "bingo"], "ner_tags": ["O", "O", "O", "B-SONG", "I-SONG", "B-CHARACTER", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "what is a mystery movie", "tokens": ["what", "is", "a", "mystery", "movie"], "ner_tags": ["O", "O", "O", "B-GENRE", "O"]}
{"sentence": "find a decent film about spirit from the past eight years starring richard dreyfus", "tokens": ["find", "a", "decent", "film", "about", "spirit", "from", "the", "past", "eight", "years", "starring", "richard", "dreyfus"], "ner_tags": ["O", "O", "B-RATINGS_AVERAGE", "O", "O", "B-PLOT", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "was a mockumentary ever given nine stars in the 1970 s", "tokens": ["was", "a", "mockumentary", "ever", "given", "nine", "stars", "in", "the", "1970", "s"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "i would like to see the movie review for the movie kiss me kate", "tokens": ["i", "would", "like", "to", "see", "the", "movie", "review", "for", "the", "movie", "kiss", "me", "kate"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-REVIEW", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "are there any movies with athletes in them", "tokens": ["are", "there", "any", "movies", "with", "athletes", "in", "them"], "ner_tags": ["O", "O", "O", "O", "O", "B-PLOT", "O", "O"]}
{"sentence": "how many seasons are there of the tv show gossip girl", "tokens": ["how", "many", "seasons", "are", "there", "of", "the", "tv", "show", "gossip", "girl"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "name a prequel to the silence of the lambs", "tokens": ["name", "a", "prequel", "to", "the", "silence", "of", "the", "lambs"], "ner_tags": ["O", "O", "B-YEAR", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "show me a movie with the song some day my prince will come", "tokens": ["show", "me", "a", "movie", "with", "the", "song", "some", "day", "my", "prince", "will", "come"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-SONG", "I-SONG", "I-SONG", "I-SONG", "I-SONG", "I-SONG"]}
{"sentence": "show me all musicals rated r", "tokens": ["show", "me", "all", "musicals", "rated", "r"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "B-RATING"]}
{"sentence": "what pg 13 anita laselva vehicle was rated six stars", "tokens": ["what", "pg", "13", "anita", "laselva", "vehicle", "was", "rated", "six", "stars"], "ner_tags": ["O", "B-RATING", "I-RATING", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "is there a fantasy movie which director is michelangelo antonioni", "tokens": ["is", "there", "a", "fantasy", "movie", "which", "director", "is", "michelangelo", "antonioni"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "who stars in five across the eyes", "tokens": ["who", "stars", "in", "five", "across", "the", "eyes"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "im looking for an r rated roman polanski drama", "tokens": ["im", "looking", "for", "an", "r", "rated", "roman", "polanski", "drama"], "ner_tags": ["O", "O", "O", "O", "B-RATING", "O", "B-DIRECTOR", "I-DIRECTOR", "B-GENRE"]}
{"sentence": "find me the movie that has the song everybody wants to be a cat", "tokens": ["find", "me", "the", "movie", "that", "has", "the", "song", "everybody", "wants", "to", "be", "a", "cat"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-SONG", "I-SONG", "I-SONG", "I-SONG", "I-SONG", "I-SONG"]}
{"sentence": "the time travelers wife was just an ok husband wife relationship kind of romantic drama", "tokens": ["the", "time", "travelers", "wife", "was", "just", "an", "ok", "husband", "wife", "relationship", "kind", "of", "romantic", "drama"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "B-PLOT", "I-PLOT", "I-PLOT", "O", "O", "B-GENRE", "I-GENRE"]}
{"sentence": "find comedy movies staring john candy", "tokens": ["find", "comedy", "movies", "staring", "john", "candy"], "ner_tags": ["O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "is there a drama movie that centers on painter", "tokens": ["is", "there", "a", "drama", "movie", "that", "centers", "on", "painter"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "O", "B-PLOT"]}
{"sentence": "was john travolta in any rated pg comedy movies", "tokens": ["was", "john", "travolta", "in", "any", "rated", "pg", "comedy", "movies"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-RATING", "B-GENRE", "O"]}
{"sentence": "i am looking for a drama type movie that received four stars", "tokens": ["i", "am", "looking", "for", "a", "drama", "type", "movie", "that", "received", "four", "stars"], "ner_tags": ["O", "O", "O", "O", "O", "B-GENRE", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "what is the movie wall e", "tokens": ["what", "is", "the", "movie", "wall", "e"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "is there a really popular movie about vulgarity starring jason alexander released in the past four years", "tokens": ["is", "there", "a", "really", "popular", "movie", "about", "vulgarity", "starring", "jason", "alexander", "released", "in", "the", "past", "four", "years"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "B-PLOT", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "what scifi movie has the best kill scene", "tokens": ["what", "scifi", "movie", "has", "the", "best", "kill", "scene"], "ner_tags": ["O", "B-GENRE", "O", "O", "O", "O", "O", "O"]}
{"sentence": "are there any dramas about teachers facing adversity", "tokens": ["are", "there", "any", "dramas", "about", "teachers", "facing", "adversity"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "B-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "i am looking for a crime movie directed by joel silverman", "tokens": ["i", "am", "looking", "for", "a", "crime", "movie", "directed", "by", "joel", "silverman"], "ner_tags": ["O", "O", "O", "O", "O", "B-GENRE", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "is there pg 13 rated comedy film which director was fritz lang", "tokens": ["is", "there", "pg", "13", "rated", "comedy", "film", "which", "director", "was", "fritz", "lang"], "ner_tags": ["O", "O", "B-RATING", "I-RATING", "O", "B-GENRE", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "what are the most popular horror movies", "tokens": ["what", "are", "the", "most", "popular", "horror", "movies"], "ner_tags": ["O", "O", "O", "O", "B-RATINGS_AVERAGE", "B-GENRE", "O"]}
{"sentence": "is there an adventure movie starring matt damon", "tokens": ["is", "there", "an", "adventure", "movie", "starring", "matt", "damon"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "show me the star of the movie mirror mirror", "tokens": ["show", "me", "the", "star", "of", "the", "movie", "mirror", "mirror"], "ner_tags": ["O", "O", "O", "B-ACTOR", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "whats a good action movie featuring alan autry", "tokens": ["whats", "a", "good", "action", "movie", "featuring", "alan", "autry"], "ner_tags": ["O", "O", "B-RATINGS_AVERAGE", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what spaghetti westerns are available", "tokens": ["what", "spaghetti", "westerns", "are", "available"], "ner_tags": ["O", "B-GENRE", "I-GENRE", "O", "O"]}
{"sentence": "in what movie is jack nicholson in an insane asylum", "tokens": ["in", "what", "movie", "is", "jack", "nicholson", "in", "an", "insane", "asylum"], "ner_tags": ["O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "id like to watch a 1990 s murder movie with actor michael biehn in it its supposedly full of violence", "tokens": ["id", "like", "to", "watch", "a", "1990", "s", "murder", "movie", "with", "actor", "michael", "biehn", "in", "it", "its", "supposedly", "full", "of", "violence"], "ner_tags": ["O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "B-PLOT", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "O", "B-GENRE"]}
{"sentence": "name a movie where a dog saves his girl", "tokens": ["name", "a", "movie", "where", "a", "dog", "saves", "his", "girl"], "ner_tags": ["O", "O", "O", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "show me the musical with gene kelley and debbie reynolds", "tokens": ["show", "me", "the", "musical", "with", "gene", "kelley", "and", "debbie", "reynolds"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "B-ACTOR", "I-ACTOR", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "list an adventure movie about revenge with actor bruce a young in the 2010 s", "tokens": ["list", "an", "adventure", "movie", "about", "revenge", "with", "actor", "bruce", "a", "young", "in", "the", "2010", "s"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "B-PLOT", "O", "O", "B-ACTOR", "I-ACTOR", "I-ACTOR", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "family movies for kids", "tokens": ["family", "movies", "for", "kids"], "ner_tags": ["B-GENRE", "O", "O", "O"]}
{"sentence": "what movies about a small town have come out in the last seven years", "tokens": ["what", "movies", "about", "a", "small", "town", "have", "come", "out", "in", "the", "last", "seven", "years"], "ner_tags": ["O", "O", "O", "O", "B-PLOT", "I-PLOT", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "greg carter directed a horror devil", "tokens": ["greg", "carter", "directed", "a", "horror", "devil"], "ner_tags": ["B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-GENRE", "B-PLOT"]}
{"sentence": "list an ingmar bergman film", "tokens": ["list", "an", "ingmar", "bergman", "film"], "ner_tags": ["O", "O", "B-DIRECTOR", "I-DIRECTOR", "O"]}
{"sentence": "find me the movie with action", "tokens": ["find", "me", "the", "movie", "with", "action"], "ner_tags": ["O", "O", "O", "O", "O", "B-GENRE"]}
{"sentence": "did brad pitt star with angelina jolie in a comedy movie about spies", "tokens": ["did", "brad", "pitt", "star", "with", "angelina", "jolie", "in", "a", "comedy", "movie", "about", "spies"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-GENRE", "O", "O", "B-PLOT"]}
{"sentence": "do you recall the sam shepard movie about a search for gold it was rated nc 17 and liked by many", "tokens": ["do", "you", "recall", "the", "sam", "shepard", "movie", "about", "a", "search", "for", "gold", "it", "was", "rated", "nc", "17", "and", "liked", "by", "many"], "ner_tags": ["O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT", "O", "O", "O", "B-RATING", "I-RATING", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "movie famous for the quote go ahead make my day", "tokens": ["movie", "famous", "for", "the", "quote", "go", "ahead", "make", "my", "day"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what is the name of the disaster film directed by peter jackson", "tokens": ["what", "is", "the", "name", "of", "the", "disaster", "film", "directed", "by", "peter", "jackson"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-GENRE", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "what movies has james franco been in", "tokens": ["what", "movies", "has", "james", "franco", "been", "in"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O"]}
{"sentence": "whats the average rating of men who stare at goats", "tokens": ["whats", "the", "average", "rating", "of", "men", "who", "stare", "at", "goats"], "ner_tags": ["O", "O", "B-RATINGS_AVERAGE", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "what are the top ten nc 17 films from the last ten years", "tokens": ["what", "are", "the", "top", "ten", "nc", "17", "films", "from", "the", "last", "ten", "years"], "ner_tags": ["O", "O", "O", "B-REVIEW", "I-REVIEW", "B-RATING", "I-RATING", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "list a 1940 s pg biography film", "tokens": ["list", "a", "1940", "s", "pg", "biography", "film"], "ner_tags": ["O", "O", "B-YEAR", "I-YEAR", "B-RATING", "B-GENRE", "O"]}
{"sentence": "how many films had steven spielberg directed", "tokens": ["how", "many", "films", "had", "steven", "spielberg", "directed"], "ner_tags": ["O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O"]}
{"sentence": "how many friday the 13th movies were made", "tokens": ["how", "many", "friday", "the", "13th", "movies", "were", "made"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "O", "O", "O"]}
{"sentence": "list some critically acclaimed gangster movies", "tokens": ["list", "some", "critically", "acclaimed", "gangster", "movies"], "ner_tags": ["O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "O"]}
{"sentence": "list a rated r military film that received ok ratings", "tokens": ["list", "a", "rated", "r", "military", "film", "that", "received", "ok", "ratings"], "ner_tags": ["O", "O", "O", "B-RATING", "B-GENRE", "O", "O", "O", "B-RATINGS_AVERAGE", "O"]}
{"sentence": "has claire danes been in any westerns that got a rating of seven stars", "tokens": ["has", "claire", "danes", "been", "in", "any", "westerns", "that", "got", "a", "rating", "of", "seven", "stars"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-GENRE", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "name a 1980 s melodrama with an nc 17 rating", "tokens": ["name", "a", "1980", "s", "melodrama", "with", "an", "nc", "17", "rating"], "ner_tags": ["O", "O", "B-YEAR", "I-YEAR", "B-GENRE", "O", "O", "B-RATING", "I-RATING", "O"]}
{"sentence": "what movie was jon stewart in", "tokens": ["what", "movie", "was", "jon", "stewart", "in"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "O"]}
{"sentence": "is there a musical movie which director is mel brooks", "tokens": ["is", "there", "a", "musical", "movie", "which", "director", "is", "mel", "brooks"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "are there any drama films from the past three decades directed by douglas s younglove that were rated very good", "tokens": ["are", "there", "any", "drama", "films", "from", "the", "past", "three", "decades", "directed", "by", "douglas", "s", "younglove", "that", "were", "rated", "very", "good"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "is there a movie in the year 1990 s starring actor daniel chan", "tokens": ["is", "there", "a", "movie", "in", "the", "year", "1990", "s", "starring", "actor", "daniel", "chan"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "find me a movie with the song summer nights", "tokens": ["find", "me", "a", "movie", "with", "the", "song", "summer", "nights"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-SONG", "I-SONG"]}
{"sentence": "i want to watch a vampire movie made in the 1960s", "tokens": ["i", "want", "to", "watch", "a", "vampire", "movie", "made", "in", "the", "1960s"], "ner_tags": ["O", "O", "O", "O", "O", "B-PLOT", "O", "O", "O", "O", "B-YEAR"]}
{"sentence": "id like that mediocre pg 13 fantasy film directed by wes anderson about spirits", "tokens": ["id", "like", "that", "mediocre", "pg", "13", "fantasy", "film", "directed", "by", "wes", "anderson", "about", "spirits"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "B-RATING", "I-RATING", "B-GENRE", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "B-PLOT"]}
{"sentence": "what military movies are rated g", "tokens": ["what", "military", "movies", "are", "rated", "g"], "ner_tags": ["O", "B-GENRE", "O", "O", "O", "B-RATING"]}
{"sentence": "what year was the movie the joy luck club released", "tokens": ["what", "year", "was", "the", "movie", "the", "joy", "luck", "club", "released"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "O"]}
{"sentence": "what is just like heaven about", "tokens": ["what", "is", "just", "like", "heaven", "about"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "O"]}
{"sentence": "ice age", "tokens": ["ice", "age"], "ner_tags": ["B-TITLE", "I-TITLE"]}
{"sentence": "whos starring as spiderman in the new film", "tokens": ["whos", "starring", "as", "spiderman", "in", "the", "new", "film"], "ner_tags": ["B-ACTOR", "O", "O", "B-TITLE", "O", "O", "O", "O"]}
{"sentence": "what was the film about hallucination that was directed by james signorelli", "tokens": ["what", "was", "the", "film", "about", "hallucination", "that", "was", "directed", "by", "james", "signorelli"], "ner_tags": ["O", "O", "O", "O", "O", "B-PLOT", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "find me movies since 2002 about a dog", "tokens": ["find", "me", "movies", "since", "2002", "about", "a", "dog"], "ner_tags": ["O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "B-PLOT"]}
{"sentence": "what is the title of the drama with dennis hopper that was rated eight stars", "tokens": ["what", "is", "the", "title", "of", "the", "drama", "with", "dennis", "hopper", "that", "was", "rated", "eight", "stars"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-GENRE", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "find me a thriller starring kim basinger", "tokens": ["find", "me", "a", "thriller", "starring", "kim", "basinger"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "any good war documentaries on tonight", "tokens": ["any", "good", "war", "documentaries", "on", "tonight"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "O"]}
{"sentence": "did the critics like ratatouille", "tokens": ["did", "the", "critics", "like", "ratatouille"], "ner_tags": ["O", "O", "B-RATINGS_AVERAGE", "O", "B-TITLE"]}
{"sentence": "find me a movie that is about pirates", "tokens": ["find", "me", "a", "movie", "that", "is", "about", "pirates"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-PLOT"]}
{"sentence": "who played kyle reese in terminator", "tokens": ["who", "played", "kyle", "reese", "in", "terminator"], "ner_tags": ["O", "O", "B-CHARACTER", "I-CHARACTER", "O", "B-TITLE"]}
{"sentence": "which very popular rated r film starring patrick mcnee was made in the last ten decades", "tokens": ["which", "very", "popular", "rated", "r", "film", "starring", "patrick", "mcnee", "was", "made", "in", "the", "last", "ten", "decades"], "ner_tags": ["O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "B-RATING", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "is there a movie witch genre is mystery made in 1950 s", "tokens": ["is", "there", "a", "movie", "witch", "genre", "is", "mystery", "made", "in", "1950", "s"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-GENRE", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "i want thriller movies with edward norton in them", "tokens": ["i", "want", "thriller", "movies", "with", "edward", "norton", "in", "them"], "ner_tags": ["O", "O", "B-GENRE", "I-GENRE", "O", "B-ACTOR", "I-ACTOR", "O", "O"]}
{"sentence": "find an action movie with al pacino", "tokens": ["find", "an", "action", "movie", "with", "al", "pacino"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what is a very popular r rated documentary with geraint wyne davies", "tokens": ["what", "is", "a", "very", "popular", "r", "rated", "documentary", "with", "geraint", "wyne", "davies"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-RATING", "O", "B-GENRE", "O", "B-ACTOR", "I-ACTOR", "I-ACTOR"]}
{"sentence": "what was the rating of tomorrow never dies", "tokens": ["what", "was", "the", "rating", "of", "tomorrow", "never", "dies"], "ner_tags": ["O", "O", "O", "B-RATING", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "what is a good movie about magic and magicians", "tokens": ["what", "is", "a", "good", "movie", "about", "magic", "and", "magicians"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "which movie in the 1960 s had my favorite things as the soundtrack for one of the well known movie scenes", "tokens": ["which", "movie", "in", "the", "1960", "s", "had", "my", "favorite", "things", "as", "the", "soundtrack", "for", "one", "of", "the", "well", "known", "movie", "scenes"], "ner_tags": ["O", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "B-SONG", "I-SONG", "I-SONG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "looking for a nc 17 or a children movie that was released in the past year with the actress kate jackson", "tokens": ["looking", "for", "a", "nc", "17", "or", "a", "children", "movie", "that", "was", "released", "in", "the", "past", "year", "with", "the", "actress", "kate", "jackson"], "ner_tags": ["O", "O", "O", "B-RATING", "I-RATING", "O", "O", "B-GENRE", "O", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what is the most highly rated pixar movie from 2000 2005", "tokens": ["what", "is", "the", "most", "highly", "rated", "pixar", "movie", "from", "2000", "2005"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-DIRECTOR", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "did roman polanski direct any war movies in the 1980 s", "tokens": ["did", "roman", "polanski", "direct", "any", "war", "movies", "in", "the", "1980", "s"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "show me a film where a dog has a telepathic relationship with a human", "tokens": ["show", "me", "a", "film", "where", "a", "dog", "has", "a", "telepathic", "relationship", "with", "a", "human"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "do you have any films from 2000 starring skeet ulrich", "tokens": ["do", "you", "have", "any", "films", "from", "2000", "starring", "skeet", "ulrich"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-YEAR", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "which elvis presleymovie was about las vegas", "tokens": ["which", "elvis", "presleymovie", "was", "about", "las", "vegas"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "im trying to find an unrated drama from 1980 that has good ratings and was directed by alex proyas", "tokens": ["im", "trying", "to", "find", "an", "unrated", "drama", "from", "1980", "that", "has", "good", "ratings", "and", "was", "directed", "by", "alex", "proyas"], "ner_tags": ["O", "O", "O", "O", "O", "B-RATING", "B-GENRE", "O", "B-YEAR", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "what is a documentary that was rated pg 13 in 2010 that was directed by john connolly", "tokens": ["what", "is", "a", "documentary", "that", "was", "rated", "pg", "13", "in", "2010", "that", "was", "directed", "by", "john", "connolly"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "B-RATING", "I-RATING", "O", "B-YEAR", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "did julia roberts star in a thriller", "tokens": ["did", "julia", "roberts", "star", "in", "a", "thriller"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-GENRE"]}
{"sentence": "list all war movies starring billy warlock from the last six years", "tokens": ["list", "all", "war", "movies", "starring", "billy", "warlock", "from", "the", "last", "six", "years"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "did robert d siegel direct a very popular science fiction film", "tokens": ["did", "robert", "d", "siegel", "direct", "a", "very", "popular", "science", "fiction", "film"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "I-DIRECTOR", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "I-GENRE", "O"]}
{"sentence": "what was the movie where they go around the world in a balloon", "tokens": ["what", "was", "the", "movie", "where", "they", "go", "around", "the", "world", "in", "a", "balloon"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "find me all movies with the title a midsummer nights dream", "tokens": ["find", "me", "all", "movies", "with", "the", "title", "a", "midsummer", "nights", "dream"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "was ricky gervais in any dramas", "tokens": ["was", "ricky", "gervais", "in", "any", "dramas"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O"]}
{"sentence": "do you have the r rated thriller from 2010", "tokens": ["do", "you", "have", "the", "r", "rated", "thriller", "from", "2010"], "ner_tags": ["O", "O", "O", "O", "B-RATING", "O", "B-GENRE", "O", "B-YEAR"]}
{"sentence": "what are some five star films in the crime genre with a pg 13 rating", "tokens": ["what", "are", "some", "five", "star", "films", "in", "the", "crime", "genre", "with", "a", "pg", "13", "rating"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "O", "B-GENRE", "O", "O", "O", "B-RATING", "I-RATING", "O"]}
{"sentence": "show me a film where a bird has a relationship with a man", "tokens": ["show", "me", "a", "film", "where", "a", "bird", "has", "a", "relationship", "with", "a", "man"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "do you have the unrated action film directed by martin kunert from the last nine decades", "tokens": ["do", "you", "have", "the", "unrated", "action", "film", "directed", "by", "martin", "kunert", "from", "the", "last", "nine", "decades"], "ner_tags": ["O", "O", "O", "O", "B-RATING", "B-GENRE", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "list a western pg 13 rated movie made in 2000 s", "tokens": ["list", "a", "western", "pg", "13", "rated", "movie", "made", "in", "2000", "s"], "ner_tags": ["O", "O", "B-GENRE", "B-RATING", "I-RATING", "O", "O", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "what are some kids movies from the 1990 s", "tokens": ["what", "are", "some", "kids", "movies", "from", "the", "1990", "s"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "what was the last film ray liota was in", "tokens": ["what", "was", "the", "last", "film", "ray", "liota", "was", "in"], "ner_tags": ["O", "O", "O", "B-YEAR", "O", "B-ACTOR", "I-ACTOR", "O", "O"]}
{"sentence": "who starred in home alone", "tokens": ["who", "starred", "in", "home", "alone"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "what has half bakeds start rating", "tokens": ["what", "has", "half", "bakeds", "start", "rating"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "please give me a list of g rated adventure films where security was involved from the past six decades", "tokens": ["please", "give", "me", "a", "list", "of", "g", "rated", "adventure", "films", "where", "security", "was", "involved", "from", "the", "past", "six", "decades"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-RATING", "O", "B-GENRE", "O", "O", "B-PLOT", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "has wes anderson directed a documentary lately", "tokens": ["has", "wes", "anderson", "directed", "a", "documentary", "lately"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-GENRE", "O"]}
{"sentence": "in what movie did sammy davis jr sing the rhythm of life", "tokens": ["in", "what", "movie", "did", "sammy", "davis", "jr", "sing", "the", "rhythm", "of", "life"], "ner_tags": ["O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "I-ACTOR", "O", "B-SONG", "I-SONG", "I-SONG", "I-SONG"]}
{"sentence": "can i see all child movies about wilderness in the last seven decades", "tokens": ["can", "i", "see", "all", "child", "movies", "about", "wilderness", "in", "the", "last", "seven", "decades"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O", "O", "B-PLOT", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "i need a list of must see comedies from the late 90s", "tokens": ["i", "need", "a", "list", "of", "must", "see", "comedies", "from", "the", "late", "90s"], "ner_tags": ["O", "O", "O", "O", "O", "B-REVIEW", "I-REVIEW", "B-GENRE", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "is there a western movie starring charlize theron", "tokens": ["is", "there", "a", "western", "movie", "starring", "charlize", "theron"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "has ingmar bergman ever directed any r rated thriller films", "tokens": ["has", "ingmar", "bergman", "ever", "directed", "any", "r", "rated", "thriller", "films"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "B-RATING", "O", "B-GENRE", "O"]}
{"sentence": "is there a film with the word lex in the title", "tokens": ["is", "there", "a", "film", "with", "the", "word", "lex", "in", "the", "title"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-TITLE", "O", "O", "O"]}
{"sentence": "who directed the matrix", "tokens": ["who", "directed", "the", "matrix"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "who stars in twilight", "tokens": ["who", "stars", "in", "twilight"], "ner_tags": ["O", "B-ACTOR", "O", "B-TITLE"]}
{"sentence": "what was the first movie nicholas cage appeared in", "tokens": ["what", "was", "the", "first", "movie", "nicholas", "cage", "appeared", "in"], "ner_tags": ["O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O"]}
{"sentence": "what is an r rated movie starring paudge behan centered around the battlefield", "tokens": ["what", "is", "an", "r", "rated", "movie", "starring", "paudge", "behan", "centered", "around", "the", "battlefield"], "ner_tags": ["O", "O", "O", "B-RATING", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-PLOT"]}
{"sentence": "who played in practical magic", "tokens": ["who", "played", "in", "practical", "magic"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "find a movie with jessica alba", "tokens": ["find", "a", "movie", "with", "jessica", "alba"], "ner_tags": ["O", "O", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what movie has cher and susan sarandon", "tokens": ["what", "movie", "has", "cher", "and", "susan", "sarandon"], "ner_tags": ["O", "O", "O", "B-ACTOR", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "im looking for a kate mulgrew film", "tokens": ["im", "looking", "for", "a", "kate", "mulgrew", "film"], "ner_tags": ["O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O"]}
{"sentence": "was cloris leachman in young frankenstein", "tokens": ["was", "cloris", "leachman", "in", "young", "frankenstein"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "what movie can you tell me about that is about bandits is a spaghetti western and is highly recommended", "tokens": ["what", "movie", "can", "you", "tell", "me", "about", "that", "is", "about", "bandits", "is", "a", "spaghetti", "western", "and", "is", "highly", "recommended"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PLOT", "O", "O", "B-GENRE", "I-GENRE", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "what is the name of the movie that has john candy being chased by a bear", "tokens": ["what", "is", "the", "name", "of", "the", "movie", "that", "has", "john", "candy", "being", "chased", "by", "a", "bear"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "what is the best roman polanski directed film", "tokens": ["what", "is", "the", "best", "roman", "polanski", "directed", "film"], "ner_tags": ["O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O"]}
{"sentence": "show me the title of steven spielbergs movie about an alien befriending a kid", "tokens": ["show", "me", "the", "title", "of", "steven", "spielbergs", "movie", "about", "an", "alien", "befriending", "a", "kid"], "ner_tags": ["O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "show me horror movies from 1979", "tokens": ["show", "me", "horror", "movies", "from", "1979"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "B-YEAR"]}
{"sentence": "is there a good fantasy movie", "tokens": ["is", "there", "a", "good", "fantasy", "movie"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O"]}
{"sentence": "id like to see that mediocre crime movie richard pryor made in the last ten decades", "tokens": ["id", "like", "to", "see", "that", "mediocre", "crime", "movie", "richard", "pryor", "made", "in", "the", "last", "ten", "decades"], "ner_tags": ["O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "B-GENRE", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "the asphalt jungle", "tokens": ["the", "asphalt", "jungle"], "ner_tags": ["B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "list crime movies directed by kevin hooks that involve an assassination attempt and received nine stars this year", "tokens": ["list", "crime", "movies", "directed", "by", "kevin", "hooks", "that", "involve", "an", "assassination", "attempt", "and", "received", "nine", "stars", "this", "year"], "ner_tags": ["O", "B-GENRE", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "B-PLOT", "I-PLOT", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-YEAR", "I-YEAR"]}
{"sentence": "has there ever been a critically acclaimed musical that was rated pg 13", "tokens": ["has", "there", "ever", "been", "a", "critically", "acclaimed", "musical", "that", "was", "rated", "pg", "13"], "ner_tags": ["O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "O", "O", "O", "B-RATING", "I-RATING"]}
{"sentence": "a film called safehouse", "tokens": ["a", "film", "called", "safehouse"], "ner_tags": ["O", "O", "O", "B-TITLE"]}
{"sentence": "tell me the name of charlton hestons final movie before he died", "tokens": ["tell", "me", "the", "name", "of", "charlton", "hestons", "final", "movie", "before", "he", "died"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "O", "O", "O", "O", "O"]}
{"sentence": "im looking for a scary film from the year 1950 starring mathew lillard", "tokens": ["im", "looking", "for", "a", "scary", "film", "from", "the", "year", "1950", "starring", "mathew", "lillard"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O", "O", "O", "O", "B-YEAR", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "most viewed movie in the 90s", "tokens": ["most", "viewed", "movie", "in", "the", "90s"], "ner_tags": ["B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "O", "B-YEAR"]}
{"sentence": "im looking for an all right film from 1990 directed by kyle patrick alvarez about war", "tokens": ["im", "looking", "for", "an", "all", "right", "film", "from", "1990", "directed", "by", "kyle", "patrick", "alvarez", "about", "war"], "ner_tags": ["O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "B-YEAR", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "I-DIRECTOR", "O", "B-GENRE"]}
{"sentence": "show me a well reviewed mystery", "tokens": ["show", "me", "a", "well", "reviewed", "mystery"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE"]}
{"sentence": "who directed chasing amy", "tokens": ["who", "directed", "chasing", "amy"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "when did much ado about nothing come out", "tokens": ["when", "did", "much", "ado", "about", "nothing", "come", "out"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "O", "O"]}
{"sentence": "are there any scifi films where the villain wins", "tokens": ["are", "there", "any", "scifi", "films", "where", "the", "villain", "wins"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "name the director of jaws 3", "tokens": ["name", "the", "director", "of", "jaws", "3"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "is there an unrated decent indians movie with james woods", "tokens": ["is", "there", "an", "unrated", "decent", "indians", "movie", "with", "james", "woods"], "ner_tags": ["O", "O", "O", "B-RATING", "B-RATINGS_AVERAGE", "B-PLOT", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what fantasy movie starring mel gibson came out in the 2000 s", "tokens": ["what", "fantasy", "movie", "starring", "mel", "gibson", "came", "out", "in", "the", "2000", "s"], "ner_tags": ["O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "is there a war movie which director is tim burton", "tokens": ["is", "there", "a", "war", "movie", "which", "director", "is", "tim", "burton"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "list a pg rated film that contains violence directed by jan dunn from the last three years", "tokens": ["list", "a", "pg", "rated", "film", "that", "contains", "violence", "directed", "by", "jan", "dunn", "from", "the", "last", "three", "years"], "ner_tags": ["O", "O", "B-RATING", "O", "O", "O", "O", "B-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "did danny devito star in a movie called twins", "tokens": ["did", "danny", "devito", "star", "in", "a", "movie", "called", "twins"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "B-TITLE"]}
{"sentence": "find me a movie with jack sparrow", "tokens": ["find", "me", "a", "movie", "with", "jack", "sparrow"], "ner_tags": ["O", "O", "O", "O", "O", "B-CHARACTER", "I-CHARACTER"]}
{"sentence": "what is the name of the director of jerry and jumbo", "tokens": ["what", "is", "the", "name", "of", "the", "director", "of", "jerry", "and", "jumbo"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "name kung fu horror movie", "tokens": ["name", "kung", "fu", "horror", "movie"], "ner_tags": ["O", "B-GENRE", "I-GENRE", "I-GENRE", "O"]}
{"sentence": "what is a good mystery", "tokens": ["what", "is", "a", "good", "mystery"], "ner_tags": ["O", "O", "O", "O", "B-GENRE"]}
{"sentence": "name the main actor in jingle all the way", "tokens": ["name", "the", "main", "actor", "in", "jingle", "all", "the", "way"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "are there any new death race movies", "tokens": ["are", "there", "any", "new", "death", "race", "movies"], "ner_tags": ["O", "O", "O", "B-YEAR", "B-TITLE", "I-TITLE", "O"]}
{"sentence": "whats a rock hudson flick from the 1950s", "tokens": ["whats", "a", "rock", "hudson", "flick", "from", "the", "1950s"], "ner_tags": ["O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-YEAR"]}
{"sentence": "watch star trek directed by jj abrams", "tokens": ["watch", "star", "trek", "directed", "by", "jj", "abrams"], "ner_tags": ["O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "can you recommend an oliver stone mystery movie from the 1990 s", "tokens": ["can", "you", "recommend", "an", "oliver", "stone", "mystery", "movie", "from", "the", "1990", "s"], "ner_tags": ["O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "how many films have featured the character robin hood", "tokens": ["how", "many", "films", "have", "featured", "the", "character", "robin", "hood"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-CHARACTER", "I-CHARACTER"]}
{"sentence": "where can i find detailed opinions about the movie purple rain", "tokens": ["where", "can", "i", "find", "detailed", "opinions", "about", "the", "movie", "purple", "rain"], "ner_tags": ["O", "O", "O", "O", "O", "B-REVIEW", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "scrooge", "tokens": ["scrooge"], "ner_tags": ["B-TITLE"]}
{"sentence": "find the movie starring john travolta and selma hayak based on a true crime in the 1940s", "tokens": ["find", "the", "movie", "starring", "john", "travolta", "and", "selma", "hayak", "based", "on", "a", "true", "crime", "in", "the", "1940s"], "ner_tags": ["O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-PLOT", "I-PLOT", "O", "O", "B-YEAR"]}
{"sentence": "what movie uses the song i dont wanna miss a thing on its soundtrack", "tokens": ["what", "movie", "uses", "the", "song", "i", "dont", "wanna", "miss", "a", "thing", "on", "its", "soundtrack"], "ner_tags": ["O", "O", "O", "O", "O", "B-SONG", "I-SONG", "I-SONG", "I-SONG", "I-SONG", "I-SONG", "O", "O", "O"]}
{"sentence": "what are some g rated watchable sci fi movies", "tokens": ["what", "are", "some", "g", "rated", "watchable", "sci", "fi", "movies"], "ner_tags": ["O", "O", "O", "B-RATING", "O", "B-RATINGS_AVERAGE", "B-GENRE", "I-GENRE", "O"]}
{"sentence": "was katharine hepburn in any avant garde movies that received ten stars", "tokens": ["was", "katharine", "hepburn", "in", "any", "avant", "garde", "movies", "that", "received", "ten", "stars"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "B-GENRE", "I-GENRE", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "show me a billy madison trailer", "tokens": ["show", "me", "a", "billy", "madison", "trailer"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE", "B-TRAILER"]}
{"sentence": "find the movie performed by meryl streep and shirley mclaine", "tokens": ["find", "the", "movie", "performed", "by", "meryl", "streep", "and", "shirley", "mclaine"], "ner_tags": ["O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what movie showed pactick swayze as a drag queen", "tokens": ["what", "movie", "showed", "pactick", "swayze", "as", "a", "drag", "queen"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-CHARACTER", "I-CHARACTER"]}
{"sentence": "where there any unrated rescue movies with amanda tapping that received only four stars", "tokens": ["where", "there", "any", "unrated", "rescue", "movies", "with", "amanda", "tapping", "that", "received", "only", "four", "stars"], "ner_tags": ["O", "O", "O", "B-RATING", "B-PLOT", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "receiving seven stars and rated g what 1950 movie starring jeri ryan was about friends", "tokens": ["receiving", "seven", "stars", "and", "rated", "g", "what", "1950", "movie", "starring", "jeri", "ryan", "was", "about", "friends"], "ner_tags": ["O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "B-RATING", "O", "B-YEAR", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-PLOT"]}
{"sentence": "i want to see a comedy with charlie sheen", "tokens": ["i", "want", "to", "see", "a", "comedy", "with", "charlie", "sheen"], "ner_tags": ["O", "O", "O", "O", "O", "B-GENRE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what film was in the 1960 s that had excellent ratings staring crystal bernard the film was unrated and classified as film noir", "tokens": ["what", "film", "was", "in", "the", "1960", "s", "that", "had", "excellent", "ratings", "staring", "crystal", "bernard", "the", "film", "was", "unrated", "and", "classified", "as", "film", "noir"], "ner_tags": ["O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-RATING", "O", "O", "O", "B-GENRE", "I-GENRE"]}
{"sentence": "list a film starring sandra bullock", "tokens": ["list", "a", "film", "starring", "sandra", "bullock"], "ner_tags": ["O", "O", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "is there a really good war movie with judi dench", "tokens": ["is", "there", "a", "really", "good", "war", "movie", "with", "judi", "dench"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what is the name of the movie in the ozarks starring jennifer lawrence", "tokens": ["what", "is", "the", "name", "of", "the", "movie", "in", "the", "ozarks", "starring", "jennifer", "lawrence"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-TITLE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "run a trailer for a super hero flick", "tokens": ["run", "a", "trailer", "for", "a", "super", "hero", "flick"], "ner_tags": ["O", "O", "B-TRAILER", "O", "O", "B-PLOT", "I-PLOT", "O"]}
{"sentence": "what adventure movies came out in 2010 that are must see", "tokens": ["what", "adventure", "movies", "came", "out", "in", "2010", "that", "are", "must", "see"], "ner_tags": ["O", "B-GENRE", "O", "O", "O", "O", "B-YEAR", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "were there any must see dramas in 1998", "tokens": ["were", "there", "any", "must", "see", "dramas", "in", "1998"], "ner_tags": ["O", "O", "O", "B-REVIEW", "I-REVIEW", "B-GENRE", "O", "B-YEAR"]}
{"sentence": "show me a lmovie directed by alfred hitchcock that starred cary grant and had james mason as the bad guy", "tokens": ["show", "me", "a", "lmovie", "directed", "by", "alfred", "hitchcock", "that", "starred", "cary", "grant", "and", "had", "james", "mason", "as", "the", "bad", "guy"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "is there a parody of star wars", "tokens": ["is", "there", "a", "parody", "of", "star", "wars"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "what r rated action film directed by thomas bangalter received eight stars this year", "tokens": ["what", "r", "rated", "action", "film", "directed", "by", "thomas", "bangalter", "received", "eight", "stars", "this", "year"], "ner_tags": ["O", "B-RATING", "O", "B-GENRE", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-YEAR", "I-YEAR"]}
{"sentence": "what crime movie that came out in the last two decades got a rating of four", "tokens": ["what", "crime", "movie", "that", "came", "out", "in", "the", "last", "two", "decades", "got", "a", "rating", "of", "four"], "ner_tags": ["O", "B-GENRE", "O", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "O", "O", "B-RATINGS_AVERAGE"]}
{"sentence": "find me a family film from 2009", "tokens": ["find", "me", "a", "family", "film", "from", "2009"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-YEAR"]}
{"sentence": "what is the rating for the toy story movies", "tokens": ["what", "is", "the", "rating", "for", "the", "toy", "story", "movies"], "ner_tags": ["O", "O", "O", "B-RATING", "O", "O", "B-TITLE", "I-TITLE", "O"]}
{"sentence": "what films did sandra bernhard star in last year", "tokens": ["what", "films", "did", "sandra", "bernhard", "star", "in", "last", "year"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "looking for a movie where colin ferrell is a soldier who doesnt want to go to vietnam", "tokens": ["looking", "for", "a", "movie", "where", "colin", "ferrell", "is", "a", "soldier", "who", "doesnt", "want", "to", "go", "to", "vietnam"], "ner_tags": ["O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "give mw the film with the nice song of enrique", "tokens": ["give", "mw", "the", "film", "with", "the", "nice", "song", "of", "enrique"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-SONG", "I-SONG", "I-SONG"]}
{"sentence": "what is a nine star emotional movie dealing with champions that came out in the 2000 s", "tokens": ["what", "is", "a", "nine", "star", "emotional", "movie", "dealing", "with", "champions", "that", "came", "out", "in", "the", "2000", "s"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "O", "O", "O", "B-PLOT", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "show me the movie with the song when you wish upon a star", "tokens": ["show", "me", "the", "movie", "with", "the", "song", "when", "you", "wish", "upon", "a", "star"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-SONG", "I-SONG", "I-SONG", "I-SONG", "I-SONG", "I-SONG"]}
{"sentence": "are there any rated r fantasy movies", "tokens": ["are", "there", "any", "rated", "r", "fantasy", "movies"], "ner_tags": ["O", "O", "O", "O", "B-RATING", "B-GENRE", "O"]}
{"sentence": "show me a list of superman movies", "tokens": ["show", "me", "a", "list", "of", "superman", "movies"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "O"]}
{"sentence": "is there a funny film that centers on high school", "tokens": ["is", "there", "a", "funny", "film", "that", "centers", "on", "high", "school"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "list all the james bond movies from the 1990s", "tokens": ["list", "all", "the", "james", "bond", "movies", "from", "the", "1990s"], "ner_tags": ["O", "O", "O", "B-CHARACTER", "I-CHARACTER", "O", "O", "O", "B-YEAR"]}
{"sentence": "looking for an ingmar bergman movie with a chess match between death and a knight", "tokens": ["looking", "for", "an", "ingmar", "bergman", "movie", "with", "a", "chess", "match", "between", "death", "and", "a", "knight"], "ner_tags": ["O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "in which movie did jodie foster speak an inmvented language", "tokens": ["in", "which", "movie", "did", "jodie", "foster", "speak", "an", "inmvented", "language"], "ner_tags": ["O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "list a mediocre pg 13 film in the past ten years with larry fine", "tokens": ["list", "a", "mediocre", "pg", "13", "film", "in", "the", "past", "ten", "years", "with", "larry", "fine"], "ner_tags": ["O", "O", "B-RATINGS_AVERAGE", "B-RATING", "I-RATING", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what military spoof movie did charlie sheen do", "tokens": ["what", "military", "spoof", "movie", "did", "charlie", "sheen", "do"], "ner_tags": ["O", "B-GENRE", "I-GENRE", "O", "O", "B-ACTOR", "I-ACTOR", "O"]}
{"sentence": "what is steel magnolias rated", "tokens": ["what", "is", "steel", "magnolias", "rated"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "O"]}
{"sentence": "name a romance move about jealousy", "tokens": ["name", "a", "romance", "move", "about", "jealousy"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "B-PLOT"]}
{"sentence": "what qualifies a movie to be in the scary genre", "tokens": ["what", "qualifies", "a", "movie", "to", "be", "in", "the", "scary", "genre"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-GENRE", "O"]}
{"sentence": "are there any good horror movies that came out last week", "tokens": ["are", "there", "any", "good", "horror", "movies", "that", "came", "out", "last", "week"], "ner_tags": ["O", "O", "O", "B-REVIEW", "B-GENRE", "O", "O", "O", "O", "O", "O"]}
{"sentence": "id like to see the 1975 movie with the quote youre gonna need a bigger boat", "tokens": ["id", "like", "to", "see", "the", "1975", "movie", "with", "the", "quote", "youre", "gonna", "need", "a", "bigger", "boat"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "list a fantasy movie released within the past year with a rating of six stars and starring aidan quinn", "tokens": ["list", "a", "fantasy", "movie", "released", "within", "the", "past", "year", "with", "a", "rating", "of", "six", "stars", "and", "starring", "aidan", "quinn"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "this is an unrated adventure movie about fugitives and directed by lamont johnson which has been out for the last four years do you know it", "tokens": ["this", "is", "an", "unrated", "adventure", "movie", "about", "fugitives", "and", "directed", "by", "lamont", "johnson", "which", "has", "been", "out", "for", "the", "last", "four", "years", "do", "you", "know", "it"], "ner_tags": ["O", "O", "O", "B-RATING", "B-GENRE", "O", "O", "B-PLOT", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "O", "O"]}
{"sentence": "show me a preview of a documentary about steel drum musicians", "tokens": ["show", "me", "a", "preview", "of", "a", "documentary", "about", "steel", "drum", "musicians"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-GENRE", "O", "B-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "im looking for a 1990 mystery film about rob pritts that was rated pg 13 and received an average rating of eight stars", "tokens": ["im", "looking", "for", "a", "1990", "mystery", "film", "about", "rob", "pritts", "that", "was", "rated", "pg", "13", "and", "received", "an", "average", "rating", "of", "eight", "stars"], "ner_tags": ["O", "O", "O", "O", "B-YEAR", "B-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "B-RATING", "I-RATING", "O", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "list a scary evil killer film directed by larry hagman from the 1980 s", "tokens": ["list", "a", "scary", "evil", "killer", "film", "directed", "by", "larry", "hagman", "from", "the", "1980", "s"], "ner_tags": ["O", "O", "B-GENRE", "B-PLOT", "I-PLOT", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "what is a 1990 animated film about a prince directed by katsuhiro ohtomo", "tokens": ["what", "is", "a", "1990", "animated", "film", "about", "a", "prince", "directed", "by", "katsuhiro", "ohtomo"], "ner_tags": ["O", "O", "O", "B-YEAR", "B-GENRE", "O", "O", "O", "B-PLOT", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "are there any pg movies with nudity", "tokens": ["are", "there", "any", "pg", "movies", "with", "nudity"], "ner_tags": ["O", "O", "O", "B-RATING", "O", "O", "B-PLOT"]}
{"sentence": "has there ever been a biography about kathleen turner", "tokens": ["has", "there", "ever", "been", "a", "biography", "about", "kathleen", "turner"], "ner_tags": ["O", "O", "O", "O", "O", "B-GENRE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "who voiced donkey in shrek", "tokens": ["who", "voiced", "donkey", "in", "shrek"], "ner_tags": ["O", "O", "B-CHARACTER", "O", "B-TITLE"]}
{"sentence": "list nine stars rated g movie with mel gibson within the past ten decades", "tokens": ["list", "nine", "stars", "rated", "g", "movie", "with", "mel", "gibson", "within", "the", "past", "ten", "decades"], "ner_tags": ["O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "B-RATING", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "is there a good crime film about an inspector", "tokens": ["is", "there", "a", "good", "crime", "film", "about", "an", "inspector"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O", "O", "O", "B-PLOT"]}
{"sentence": "show me dramatic comedies directed by tyler perry", "tokens": ["show", "me", "dramatic", "comedies", "directed", "by", "tyler", "perry"], "ner_tags": ["O", "O", "B-GENRE", "I-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "what are the best trilogies to watch", "tokens": ["what", "are", "the", "best", "trilogies", "to", "watch"], "ner_tags": ["O", "O", "O", "B-REVIEW", "I-REVIEW", "I-REVIEW", "I-REVIEW"]}
{"sentence": "did joss whedon make any horror movies", "tokens": ["did", "joss", "whedon", "make", "any", "horror", "movies"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-GENRE", "O"]}
{"sentence": "could you find me a mockumentary with ed burns that has at least an eight stars rating", "tokens": ["could", "you", "find", "me", "a", "mockumentary", "with", "ed", "burns", "that", "has", "at", "least", "an", "eight", "stars", "rating"], "ner_tags": ["O", "O", "O", "O", "O", "B-GENRE", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O"]}
{"sentence": "please find the brooke shields movie about new orleans prostitutes", "tokens": ["please", "find", "the", "brooke", "shields", "movie", "about", "new", "orleans", "prostitutes"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "who is the director of the tortured", "tokens": ["who", "is", "the", "director", "of", "the", "tortured"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "show me dramatic movies directed by ron howard", "tokens": ["show", "me", "dramatic", "movies", "directed", "by", "ron", "howard"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "list a r rated family movie with keth szarabajka", "tokens": ["list", "a", "r", "rated", "family", "movie", "with", "keth", "szarabajka"], "ner_tags": ["O", "O", "B-RATING", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "give me some horror movies from the 60s", "tokens": ["give", "me", "some", "horror", "movies", "from", "the", "60s"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "B-YEAR"]}
{"sentence": "what is a mediocre movie starring john callahan and rated r", "tokens": ["what", "is", "a", "mediocre", "movie", "starring", "john", "callahan", "and", "rated", "r"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-RATING"]}
{"sentence": "are there any rated r military films that was rated an eight that stars rob estes", "tokens": ["are", "there", "any", "rated", "r", "military", "films", "that", "was", "rated", "an", "eight", "that", "stars", "rob", "estes"], "ner_tags": ["O", "O", "O", "O", "B-RATING", "B-GENRE", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "has cary grant starred in any independent films that received nine stars and above", "tokens": ["has", "cary", "grant", "starred", "in", "any", "independent", "films", "that", "received", "nine", "stars", "and", "above"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-GENRE", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "find the movie with the song somewhere out there", "tokens": ["find", "the", "movie", "with", "the", "song", "somewhere", "out", "there"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-SONG", "I-SONG", "I-SONG"]}
{"sentence": "psychological movie by stanly kubrick in 1971", "tokens": ["psychological", "movie", "by", "stanly", "kubrick", "in", "1971"], "ner_tags": ["B-GENRE", "I-GENRE", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "B-YEAR"]}
{"sentence": "was there an r rated movie with a retirement home and robert ri chard released in 1970", "tokens": ["was", "there", "an", "r", "rated", "movie", "with", "a", "retirement", "home", "and", "robert", "ri", "chard", "released", "in", "1970"], "ner_tags": ["O", "O", "O", "B-RATING", "O", "O", "O", "O", "B-PLOT", "I-PLOT", "O", "B-ACTOR", "I-ACTOR", "I-ACTOR", "O", "O", "B-YEAR"]}
{"sentence": "is there a good sci fi movie about an empire", "tokens": ["is", "there", "a", "good", "sci", "fi", "movie", "about", "an", "empire"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "I-GENRE", "O", "O", "O", "B-PLOT"]}
{"sentence": "name an unrated adventure that was received well in 1940", "tokens": ["name", "an", "unrated", "adventure", "that", "was", "received", "well", "in", "1940"], "ner_tags": ["O", "O", "B-RATING", "B-GENRE", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "B-YEAR"]}
{"sentence": "with which movie brad pitt stars as achilles", "tokens": ["with", "which", "movie", "brad", "pitt", "stars", "as", "achilles"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-CHARACTER"]}
{"sentence": "is there a romance movie with a bank robbery", "tokens": ["is", "there", "a", "romance", "movie", "with", "a", "bank", "robbery"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "what qualifies a movie as a melodrama", "tokens": ["what", "qualifies", "a", "movie", "as", "a", "melodrama"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-GENRE"]}
{"sentence": "has woody allen ever directed a movie with morgan freeman in it", "tokens": ["has", "woody", "allen", "ever", "directed", "a", "movie", "with", "morgan", "freeman", "in", "it"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O"]}
{"sentence": "is the movie the hunger games based on a kids book", "tokens": ["is", "the", "movie", "the", "hunger", "games", "based", "on", "a", "kids", "book"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "O", "O", "O", "O", "O"]}
{"sentence": "what movie is the line give him an offer he cant refuse from", "tokens": ["what", "movie", "is", "the", "line", "give", "him", "an", "offer", "he", "cant", "refuse", "from"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "is there a mockumentary that is unrated and was liked a lot", "tokens": ["is", "there", "a", "mockumentary", "that", "is", "unrated", "and", "was", "liked", "a", "lot"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-RATING", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "find a funny comedy starring judy garland", "tokens": ["find", "a", "funny", "comedy", "starring", "judy", "garland"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what are some titles of films from the past two years starring steve burton that has a rating of eight stars", "tokens": ["what", "are", "some", "titles", "of", "films", "from", "the", "past", "two", "years", "starring", "steve", "burton", "that", "has", "a", "rating", "of", "eight", "stars"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "what are some excellent rated sci fi movies directed by james whale in the last decade", "tokens": ["what", "are", "some", "excellent", "rated", "sci", "fi", "movies", "directed", "by", "james", "whale", "in", "the", "last", "decade"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "I-GENRE", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "show me information about the movie loverboy", "tokens": ["show", "me", "information", "about", "the", "movie", "loverboy"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-TITLE"]}
{"sentence": "which movie released in 2008 has the best rating on rotten tomatoes", "tokens": ["which", "movie", "released", "in", "2008", "has", "the", "best", "rating", "on", "rotten", "tomatoes"], "ner_tags": ["O", "O", "O", "O", "B-YEAR", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "O"]}
{"sentence": "who is the main character of a bronx tale", "tokens": ["who", "is", "the", "main", "character", "of", "a", "bronx", "tale"], "ner_tags": ["O", "O", "O", "B-CHARACTER", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "find ryan phillipe war movies", "tokens": ["find", "ryan", "phillipe", "war", "movies"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "B-GENRE", "O"]}
{"sentence": "what movies was johnny depp in in the 90s", "tokens": ["what", "movies", "was", "johnny", "depp", "in", "in", "the", "90s"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-YEAR"]}
{"sentence": "has the song rag doll been used in any movies", "tokens": ["has", "the", "song", "rag", "doll", "been", "used", "in", "any", "movies"], "ner_tags": ["O", "O", "O", "B-SONG", "I-SONG", "O", "O", "O", "O", "O"]}
{"sentence": "what movie was britney spears in where she sang satisfaction", "tokens": ["what", "movie", "was", "britney", "spears", "in", "where", "she", "sang", "satisfaction"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "B-SONG"]}
{"sentence": "list of films directed by james cameron", "tokens": ["list", "of", "films", "directed", "by", "james", "cameron"], "ner_tags": ["O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "has sam raimi directed any documentary films", "tokens": ["has", "sam", "raimi", "directed", "any", "documentary", "films"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-GENRE", "O"]}
{"sentence": "is there a documentary film r rated which had six stars and above rating", "tokens": ["is", "there", "a", "documentary", "film", "r", "rated", "which", "had", "six", "stars", "and", "above", "rating"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "B-RATING", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O"]}
{"sentence": "show me robert deniro movies about mobsters", "tokens": ["show", "me", "robert", "deniro", "movies", "about", "mobsters"], "ner_tags": ["O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-PLOT"]}
{"sentence": "looking for a robert altman movie about a futuristic world where everything is frozen", "tokens": ["looking", "for", "a", "robert", "altman", "movie", "about", "a", "futuristic", "world", "where", "everything", "is", "frozen"], "ner_tags": ["O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "i am looking for documentaries about family", "tokens": ["i", "am", "looking", "for", "documentaries", "about", "family"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "I-GENRE", "I-GENRE"]}
{"sentence": "show me a movie with a movie trailer in the movie", "tokens": ["show", "me", "a", "movie", "with", "a", "movie", "trailer", "in", "the", "movie"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-TRAILER", "O", "O", "O"]}
{"sentence": "what was the most popular thriller of the 1960 s", "tokens": ["what", "was", "the", "most", "popular", "thriller", "of", "the", "1960", "s"], "ner_tags": ["O", "O", "O", "O", "O", "B-GENRE", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "what was that really popular military movie in the jungle in the past ten years", "tokens": ["what", "was", "that", "really", "popular", "military", "movie", "in", "the", "jungle", "in", "the", "past", "ten", "years"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "O", "O", "O", "B-PLOT", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "what 90s vampire mivie had brad pitt play a big role in", "tokens": ["what", "90s", "vampire", "mivie", "had", "brad", "pitt", "play", "a", "big", "role", "in"], "ner_tags": ["O", "B-YEAR", "B-PLOT", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O"]}
{"sentence": "is there a 2000 s film with james marsters", "tokens": ["is", "there", "a", "2000", "s", "film", "with", "james", "marsters"], "ner_tags": ["O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what is the 2010 action movie stars maria bello", "tokens": ["what", "is", "the", "2010", "action", "movie", "stars", "maria", "bello"], "ner_tags": ["O", "O", "O", "B-YEAR", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "i would like the title of the pg rated history film from the 2000 s directed by vince vieluf", "tokens": ["i", "would", "like", "the", "title", "of", "the", "pg", "rated", "history", "film", "from", "the", "2000", "s", "directed", "by", "vince", "vieluf"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-RATING", "O", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "was patrick stewart in any military movies", "tokens": ["was", "patrick", "stewart", "in", "any", "military", "movies"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "B-GENRE", "O"]}
{"sentence": "what is a popular movie soundtrack from the late 1990s", "tokens": ["what", "is", "a", "popular", "movie", "soundtrack", "from", "the", "late", "1990s"], "ner_tags": ["O", "O", "O", "B-SONG", "I-SONG", "I-SONG", "O", "O", "O", "B-YEAR"]}
{"sentence": "show me a disney film with witches", "tokens": ["show", "me", "a", "disney", "film", "with", "witches"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-PLOT"]}
{"sentence": "find a john malcovich weird comedy", "tokens": ["find", "a", "john", "malcovich", "weird", "comedy"], "ner_tags": ["O", "O", "B-ACTOR", "I-ACTOR", "O", "B-GENRE"]}
{"sentence": "what year did the nightmare before christmas come out", "tokens": ["what", "year", "did", "the", "nightmare", "before", "christmas", "come", "out"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "O", "O"]}
{"sentence": "show me science fiction films directed by steven spielberg", "tokens": ["show", "me", "science", "fiction", "films", "directed", "by", "steven", "spielberg"], "ner_tags": ["O", "O", "B-GENRE", "I-GENRE", "I-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "i would like a suspense police movie starring lea salonga", "tokens": ["i", "would", "like", "a", "suspense", "police", "movie", "starring", "lea", "salonga"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "B-PLOT", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what are the top 10 scariest films for halloween", "tokens": ["what", "are", "the", "top", "10", "scariest", "films", "for", "halloween"], "ner_tags": ["O", "O", "O", "B-REVIEW", "I-REVIEW", "B-GENRE", "O", "O", "B-PLOT"]}
{"sentence": "who wrote half baked", "tokens": ["who", "wrote", "half", "baked"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "i want more information about the movie fantastic voyage", "tokens": ["i", "want", "more", "information", "about", "the", "movie", "fantastic", "voyage"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "what movie shot brad pitt to stardom", "tokens": ["what", "movie", "shot", "brad", "pitt", "to", "stardom"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O"]}
{"sentence": "where is the movie ran currently available", "tokens": ["where", "is", "the", "movie", "ran", "currently", "available"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "O", "O"]}
{"sentence": "do you know where i can find a documentary from the year 1940 with freddy prinze jr in it which is liked by many", "tokens": ["do", "you", "know", "where", "i", "can", "find", "a", "documentary", "from", "the", "year", "1940", "with", "freddy", "prinze", "jr", "in", "it", "which", "is", "liked", "by", "many"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-GENRE", "O", "O", "O", "B-YEAR", "O", "B-ACTOR", "I-ACTOR", "I-ACTOR", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "what does critic roger ebert think of shortbus", "tokens": ["what", "does", "critic", "roger", "ebert", "think", "of", "shortbus"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-TITLE"]}
{"sentence": "whar beach movie had actor buster keaton doing a cameo appearance", "tokens": ["whar", "beach", "movie", "had", "actor", "buster", "keaton", "doing", "a", "cameo", "appearance"], "ner_tags": ["O", "B-PLOT", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O"]}
{"sentence": "which horror film has the most sequels", "tokens": ["which", "horror", "film", "has", "the", "most", "sequels"], "ner_tags": ["O", "B-GENRE", "O", "O", "O", "O", "O"]}
{"sentence": "the shining", "tokens": ["the", "shining"], "ner_tags": ["B-TITLE", "I-TITLE"]}
{"sentence": "who directed the movie love actually", "tokens": ["who", "directed", "the", "movie", "love", "actually"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "what is the title of the nc 17 movie with malcolm mcdowell", "tokens": ["what", "is", "the", "title", "of", "the", "nc", "17", "movie", "with", "malcolm", "mcdowell"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-RATING", "I-RATING", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "has ian mckellen been in any recent movies", "tokens": ["has", "ian", "mckellen", "been", "in", "any", "recent", "movies"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O"]}
{"sentence": "find a batman movie starring michael keaton", "tokens": ["find", "a", "batman", "movie", "starring", "michael", "keaton"], "ner_tags": ["O", "O", "B-CHARACTER", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what pg 13 movie about accidental death directed by alberto cavalcanti received four stars and was released within the last six decades", "tokens": ["what", "pg", "13", "movie", "about", "accidental", "death", "directed", "by", "alberto", "cavalcanti", "received", "four", "stars", "and", "was", "released", "within", "the", "last", "six", "decades"], "ner_tags": ["O", "B-RATING", "I-RATING", "O", "O", "B-PLOT", "I-PLOT", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "what is the name of the movie starring kristen alphonso about a retirement home", "tokens": ["what", "is", "the", "name", "of", "the", "movie", "starring", "kristen", "alphonso", "about", "a", "retirement", "home"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "show me the highly rated sci fi movies from 1970", "tokens": ["show", "me", "the", "highly", "rated", "sci", "fi", "movies", "from", "1970"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "I-GENRE", "O", "O", "B-YEAR"]}
{"sentence": "tell me the name of the movie starring jason statham as a hired driver", "tokens": ["tell", "me", "the", "name", "of", "the", "movie", "starring", "jason", "statham", "as", "a", "hired", "driver"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "what is the movie taboo about", "tokens": ["what", "is", "the", "movie", "taboo", "about"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "O"]}
{"sentence": "tron", "tokens": ["tron"], "ner_tags": ["B-TITLE"]}
{"sentence": "who stars in the film safe house", "tokens": ["who", "stars", "in", "the", "film", "safe", "house"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "find me the trailer for the newest mission impossible movie", "tokens": ["find", "me", "the", "trailer", "for", "the", "newest", "mission", "impossible", "movie"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "O"]}
{"sentence": "steve burton is a great director", "tokens": ["steve", "burton", "is", "a", "great", "director"], "ner_tags": ["B-ACTOR", "I-ACTOR", "O", "O", "O", "O"]}
{"sentence": "does the movie wall e feature the voice of matt damon", "tokens": ["does", "the", "movie", "wall", "e", "feature", "the", "voice", "of", "matt", "damon"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what action film did tom kalin starred in the year 2000", "tokens": ["what", "action", "film", "did", "tom", "kalin", "starred", "in", "the", "year", "2000"], "ner_tags": ["O", "B-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "O", "B-YEAR"]}
{"sentence": "show me the james bond movie where sean connery fights oddjob", "tokens": ["show", "me", "the", "james", "bond", "movie", "where", "sean", "connery", "fights", "oddjob"], "ner_tags": ["O", "O", "O", "B-CHARACTER", "I-CHARACTER", "O", "O", "B-ACTOR", "I-ACTOR", "B-PLOT", "B-CHARACTER"]}
{"sentence": "i would like to see a rodney grant movie from 1980", "tokens": ["i", "would", "like", "to", "see", "a", "rodney", "grant", "movie", "from", "1980"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-YEAR"]}
{"sentence": "show me the top rated movie starring betty davis", "tokens": ["show", "me", "the", "top", "rated", "movie", "starring", "betty", "davis"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "is there a movie from steve mcqueen about children from the 1980 s", "tokens": ["is", "there", "a", "movie", "from", "steve", "mcqueen", "about", "children", "from", "the", "1980", "s"], "ner_tags": ["O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "B-GENRE", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "what genre was is act of valor", "tokens": ["what", "genre", "was", "is", "act", "of", "valor"], "ner_tags": ["O", "B-GENRE", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "did jim carrey star in the shining", "tokens": ["did", "jim", "carrey", "star", "in", "the", "shining"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "list the movie dark vengeance", "tokens": ["list", "the", "movie", "dark", "vengeance"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "list a historical must see with christian bale", "tokens": ["list", "a", "historical", "must", "see", "with", "christian", "bale"], "ner_tags": ["O", "O", "B-GENRE", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "whats that action flick from last year starring corey feldman", "tokens": ["whats", "that", "action", "flick", "from", "last", "year", "starring", "corey", "feldman"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "B-YEAR", "I-YEAR", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what is a documentary from 1950 that is available", "tokens": ["what", "is", "a", "documentary", "from", "1950", "that", "is", "available"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "B-YEAR", "O", "O", "O"]}
{"sentence": "are there any inspirational sports movies about football", "tokens": ["are", "there", "any", "inspirational", "sports", "movies", "about", "football"], "ner_tags": ["O", "O", "O", "B-PLOT", "B-GENRE", "O", "O", "O"]}
{"sentence": "how many fantasy films will be coming out this year", "tokens": ["how", "many", "fantasy", "films", "will", "be", "coming", "out", "this", "year"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "find me the newest john travolta movie", "tokens": ["find", "me", "the", "newest", "john", "travolta", "movie"], "ner_tags": ["O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O"]}
{"sentence": "find a comedy with tom hanks and meg ryan", "tokens": ["find", "a", "comedy", "with", "tom", "hanks", "and", "meg", "ryan"], "ner_tags": ["O", "O", "B-GENRE", "O", "B-ACTOR", "I-ACTOR", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "do you have the 1970 family film starring michael rapaport", "tokens": ["do", "you", "have", "the", "1970", "family", "film", "starring", "michael", "rapaport"], "ner_tags": ["O", "O", "O", "O", "B-YEAR", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "list a 1960 mystery film directed by ian mccrudden", "tokens": ["list", "a", "1960", "mystery", "film", "directed", "by", "ian", "mccrudden"], "ner_tags": ["O", "O", "B-YEAR", "B-GENRE", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "list films that were directed by charles chaplin", "tokens": ["list", "films", "that", "were", "directed", "by", "charles", "chaplin"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "what is a good thriller with an r rating", "tokens": ["what", "is", "a", "good", "thriller", "with", "an", "r", "rating"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O", "O", "B-RATING", "O"]}
{"sentence": "is the vow a romantic comedy", "tokens": ["is", "the", "vow", "a", "romantic", "comedy"], "ner_tags": ["O", "B-TITLE", "I-TITLE", "O", "B-GENRE", "I-GENRE"]}
{"sentence": "is there a sci fi movie that centers space opera", "tokens": ["is", "there", "a", "sci", "fi", "movie", "that", "centers", "space", "opera"], "ner_tags": ["O", "O", "O", "B-GENRE", "I-GENRE", "O", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "is there any movies starring paul newman", "tokens": ["is", "there", "any", "movies", "starring", "paul", "newman"], "ner_tags": ["O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "did peter mcgennis direct a fantasy film this year", "tokens": ["did", "peter", "mcgennis", "direct", "a", "fantasy", "film", "this", "year"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-GENRE", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "is there a 2010 s movie with jordanna brewster", "tokens": ["is", "there", "a", "2010", "s", "movie", "with", "jordanna", "brewster"], "ner_tags": ["O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what movies has william hurt done that is liked by many", "tokens": ["what", "movies", "has", "william", "hurt", "done", "that", "is", "liked", "by", "many"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "what movies have a hans zimmer soundtrack", "tokens": ["what", "movies", "have", "a", "hans", "zimmer", "soundtrack"], "ner_tags": ["O", "O", "O", "O", "B-SONG", "I-SONG", "O"]}
{"sentence": "is there a movie with the title a bunch of amateurs", "tokens": ["is", "there", "a", "movie", "with", "the", "title", "a", "bunch", "of", "amateurs"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "what was denzel washingtons first movie", "tokens": ["what", "was", "denzel", "washingtons", "first", "movie"], "ner_tags": ["O", "O", "B-ACTOR", "I-ACTOR", "O", "O"]}
{"sentence": "list a really popular sci fi pg 13 movie with takashi ishii in the last nine decades", "tokens": ["list", "a", "really", "popular", "sci", "fi", "pg", "13", "movie", "with", "takashi", "ishii", "in", "the", "last", "nine", "decades"], "ner_tags": ["O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "I-GENRE", "B-RATING", "I-RATING", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "critical reviews of hunger games", "tokens": ["critical", "reviews", "of", "hunger", "games"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "did million dollar baby get considered as a must see", "tokens": ["did", "million", "dollar", "baby", "get", "considered", "as", "a", "must", "see"], "ner_tags": ["O", "B-TITLE", "I-TITLE", "I-TITLE", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "what is julia roberts last movie", "tokens": ["what", "is", "julia", "roberts", "last", "movie"], "ner_tags": ["O", "O", "B-ACTOR", "I-ACTOR", "B-YEAR", "I-YEAR"]}
{"sentence": "what biography was really good and had crystal bernard in it", "tokens": ["what", "biography", "was", "really", "good", "and", "had", "crystal", "bernard", "in", "it"], "ner_tags": ["O", "B-GENRE", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O"]}
{"sentence": "are there any war movies rated pg 13", "tokens": ["are", "there", "any", "war", "movies", "rated", "pg", "13"], "ner_tags": ["O", "O", "O", "B-PLOT", "O", "O", "B-RATING", "I-RATING"]}
{"sentence": "name a flick from the 1970s with the word laser in the title", "tokens": ["name", "a", "flick", "from", "the", "1970s", "with", "the", "word", "laser", "in", "the", "title"], "ner_tags": ["O", "O", "O", "O", "O", "B-YEAR", "O", "O", "O", "B-TITLE", "O", "O", "O"]}
{"sentence": "has david lynch directed any recent action movies", "tokens": ["has", "david", "lynch", "directed", "any", "recent", "action", "movies"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "B-GENRE", "O"]}
{"sentence": "find the movie with the song eye of the tiger", "tokens": ["find", "the", "movie", "with", "the", "song", "eye", "of", "the", "tiger"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-SONG", "I-SONG", "I-SONG", "I-SONG"]}
{"sentence": "is there a color gangster movie", "tokens": ["is", "there", "a", "color", "gangster", "movie"], "ner_tags": ["O", "O", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "what pg action films about police chases directed by screaming mad george received nine stars", "tokens": ["what", "pg", "action", "films", "about", "police", "chases", "directed", "by", "screaming", "mad", "george", "received", "nine", "stars"], "ner_tags": ["O", "B-RATING", "B-GENRE", "O", "O", "B-PLOT", "I-PLOT", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "I-DIRECTOR", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "did orson welles ever direct a musical and if he did what is the name", "tokens": ["did", "orson", "welles", "ever", "direct", "a", "musical", "and", "if", "he", "did", "what", "is", "the", "name"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "B-GENRE", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "is there pg 13 movie from 1970 starring vanessa redgrave which i must see", "tokens": ["is", "there", "pg", "13", "movie", "from", "1970", "starring", "vanessa", "redgrave", "which", "i", "must", "see"], "ner_tags": ["O", "O", "B-RATING", "I-RATING", "O", "O", "B-YEAR", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "name a 1980 s war movie", "tokens": ["name", "a", "1980", "s", "war", "movie"], "ner_tags": ["O", "O", "B-YEAR", "I-YEAR", "B-GENRE", "O"]}
{"sentence": "list a drama movie made in 1960 s", "tokens": ["list", "a", "drama", "movie", "made", "in", "1960", "s"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "is rocky balboa based off of an actual person", "tokens": ["is", "rocky", "balboa", "based", "off", "of", "an", "actual", "person"], "ner_tags": ["O", "B-CHARACTER", "I-CHARACTER", "O", "O", "O", "O", "O", "O"]}
{"sentence": "was john travolta ever in an adventure movie", "tokens": ["was", "john", "travolta", "ever", "in", "an", "adventure", "movie"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-GENRE", "O"]}
{"sentence": "whats the newest american pie film called", "tokens": ["whats", "the", "newest", "american", "pie", "film", "called"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE", "O", "O"]}
{"sentence": "find an action flick with patrick wayne", "tokens": ["find", "an", "action", "flick", "with", "patrick", "wayne"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what nc 17 science fiction movie was directed by don argott", "tokens": ["what", "nc", "17", "science", "fiction", "movie", "was", "directed", "by", "don", "argott"], "ner_tags": ["O", "B-RATING", "I-RATING", "B-GENRE", "I-GENRE", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "list all the pixar films from the last 10 years", "tokens": ["list", "all", "the", "pixar", "films", "from", "the", "last", "10", "years"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "in the last five years what drama films did john hindman direct", "tokens": ["in", "the", "last", "five", "years", "what", "drama", "films", "did", "john", "hindman", "direct"], "ner_tags": ["O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "B-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O"]}
{"sentence": "looking for a thanksgiving movie with the word cooking in the title", "tokens": ["looking", "for", "a", "thanksgiving", "movie", "with", "the", "word", "cooking", "in", "the", "title"], "ner_tags": ["O", "O", "O", "B-PLOT", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "what is a family movie with actor bobbie phillips", "tokens": ["what", "is", "a", "family", "movie", "with", "actor", "bobbie", "phillips"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "i want to watch an action movie with harrison ford", "tokens": ["i", "want", "to", "watch", "an", "action", "movie", "with", "harrison", "ford"], "ner_tags": ["O", "O", "O", "O", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "find a review of pretty woman", "tokens": ["find", "a", "review", "of", "pretty", "woman"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "do you have any nine star films from the past ten years that are pg 13 and star matthew lawrence", "tokens": ["do", "you", "have", "any", "nine", "star", "films", "from", "the", "past", "ten", "years", "that", "are", "pg", "13", "and", "star", "matthew", "lawrence"], "ner_tags": ["O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "B-RATING", "I-RATING", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "who directed the harry potter series of movies", "tokens": ["who", "directed", "the", "harry", "potter", "series", "of", "movies"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE", "O", "O", "O"]}
{"sentence": "what movie used the quote go ahead make my day", "tokens": ["what", "movie", "used", "the", "quote", "go", "ahead", "make", "my", "day"], "ner_tags": ["B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "what is the movie the ice cream man", "tokens": ["what", "is", "the", "movie", "the", "ice", "cream", "man"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "what is an r rated horror movie from 1960 that was liked by many", "tokens": ["what", "is", "an", "r", "rated", "horror", "movie", "from", "1960", "that", "was", "liked", "by", "many"], "ner_tags": ["O", "O", "O", "B-RATING", "O", "B-GENRE", "O", "O", "B-YEAR", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "show me popular films with the actress lindsay lohan", "tokens": ["show", "me", "popular", "films", "with", "the", "actress", "lindsay", "lohan"], "ner_tags": ["O", "O", "B-RATINGS_AVERAGE", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what action movie was received well and was directed by jerry sangiuliano that was rated nc 17 in 1950", "tokens": ["what", "action", "movie", "was", "received", "well", "and", "was", "directed", "by", "jerry", "sangiuliano", "that", "was", "rated", "nc", "17", "in", "1950"], "ner_tags": ["O", "B-GENRE", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "B-RATING", "I-RATING", "O", "B-YEAR"]}
{"sentence": "who was the director of avatar", "tokens": ["who", "was", "the", "director", "of", "avatar"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE"]}
{"sentence": "what r rated lisa howard sci fi film averaged six out of ten in the 2000 s", "tokens": ["what", "r", "rated", "lisa", "howard", "sci", "fi", "film", "averaged", "six", "out", "of", "ten", "in", "the", "2000", "s"], "ner_tags": ["O", "B-RATING", "O", "B-ACTOR", "I-ACTOR", "B-GENRE", "I-GENRE", "O", "O", "B-RATINGS_AVERAGE", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "in 1940 what pg 13 sport movie did shane abbess direct that received an average rating of eight", "tokens": ["in", "1940", "what", "pg", "13", "sport", "movie", "did", "shane", "abbess", "direct", "that", "received", "an", "average", "rating", "of", "eight"], "ner_tags": ["O", "B-YEAR", "O", "B-RATING", "I-RATING", "B-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE"]}
{"sentence": "list a good pg 13 action movie that was in the year 1940", "tokens": ["list", "a", "good", "pg", "13", "action", "movie", "that", "was", "in", "the", "year", "1940"], "ner_tags": ["O", "O", "B-RATINGS_AVERAGE", "B-RATING", "I-RATING", "B-GENRE", "O", "O", "O", "O", "O", "O", "B-YEAR"]}
{"sentence": "find me movies with drama starring halle berry", "tokens": ["find", "me", "movies", "with", "drama", "starring", "halle", "berry"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "how many movies did christian bale star in in 2009", "tokens": ["how", "many", "movies", "did", "christian", "bale", "star", "in", "in", "2009"], "ner_tags": ["O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-YEAR"]}
{"sentence": "im looking for a family drama starring meryl streep", "tokens": ["im", "looking", "for", "a", "family", "drama", "starring", "meryl", "streep"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "I-GENRE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "have you seen any films with laura san giacomo from the last seven decades", "tokens": ["have", "you", "seen", "any", "films", "with", "laura", "san", "giacomo", "from", "the", "last", "seven", "decades"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "I-ACTOR", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "what is a good scary movie that came out last year that you could recommend", "tokens": ["what", "is", "a", "good", "scary", "movie", "that", "came", "out", "last", "year", "that", "you", "could", "recommend"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "show me action movies directed by clint eastwood", "tokens": ["show", "me", "action", "movies", "directed", "by", "clint", "eastwood"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "mention a drama from the last four decades with a rating of at least eight", "tokens": ["mention", "a", "drama", "from", "the", "last", "four", "decades", "with", "a", "rating", "of", "at", "least", "eight"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE"]}
{"sentence": "list a quote from mash", "tokens": ["list", "a", "quote", "from", "mash"], "ner_tags": ["O", "O", "O", "O", "B-TITLE"]}
{"sentence": "who directed thunderpants", "tokens": ["who", "directed", "thunderpants"], "ner_tags": ["O", "O", "B-TITLE"]}
{"sentence": "is there any good romantic drama films made in the 2000 s about a husband wife relationship", "tokens": ["is", "there", "any", "good", "romantic", "drama", "films", "made", "in", "the", "2000", "s", "about", "a", "husband", "wife", "relationship"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "I-GENRE", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "has adam sandler ever been in an action movie that received six stars and above", "tokens": ["has", "adam", "sandler", "ever", "been", "in", "an", "action", "movie", "that", "received", "six", "stars", "and", "above"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "B-GENRE", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "id like to see the 1960 s war film directed by kevin dunn that received eight stars", "tokens": ["id", "like", "to", "see", "the", "1960", "s", "war", "film", "directed", "by", "kevin", "dunn", "that", "received", "eight", "stars"], "ner_tags": ["O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "B-GENRE", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "find me a movies directed by actors in the 2000s", "tokens": ["find", "me", "a", "movies", "directed", "by", "actors", "in", "the", "2000s"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-YEAR"]}
{"sentence": "who played the character of sherlock holmes", "tokens": ["who", "played", "the", "character", "of", "sherlock", "holmes"], "ner_tags": ["O", "O", "O", "O", "O", "B-CHARACTER", "I-CHARACTER"]}
{"sentence": "what is prometheus about", "tokens": ["what", "is", "prometheus", "about"], "ner_tags": ["O", "O", "B-TITLE", "O"]}
{"sentence": "what qualifies a movie as a documentary", "tokens": ["what", "qualifies", "a", "movie", "as", "a", "documentary"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-GENRE"]}
{"sentence": "are there any r rated movies starring george clooney", "tokens": ["are", "there", "any", "r", "rated", "movies", "starring", "george", "clooney"], "ner_tags": ["O", "O", "O", "B-RATING", "I-RATING", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "show me the third star wars film", "tokens": ["show", "me", "the", "third", "star", "wars", "film"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE", "O"]}
{"sentence": "what david m holechek movie that came out in the past seven decades was about friends", "tokens": ["what", "david", "m", "holechek", "movie", "that", "came", "out", "in", "the", "past", "seven", "decades", "was", "about", "friends"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "B-PLOT"]}
{"sentence": "list a movie released in 2010 s that is science fiction and rated pg 13", "tokens": ["list", "a", "movie", "released", "in", "2010", "s", "that", "is", "science", "fiction", "and", "rated", "pg", "13"], "ner_tags": ["O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "B-GENRE", "I-GENRE", "O", "O", "B-RATING", "I-RATING"]}
{"sentence": "who directed the film mulholland drive", "tokens": ["who", "directed", "the", "film", "mulholland", "drive"], "ner_tags": ["O", "B-DIRECTOR", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "whats a good pg 13 rated comedy with robert duvall", "tokens": ["whats", "a", "good", "pg", "13", "rated", "comedy", "with", "robert", "duvall"], "ner_tags": ["O", "O", "O", "B-RATING", "I-RATING", "O", "B-GENRE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what movies featured the voices of mel blanc", "tokens": ["what", "movies", "featured", "the", "voices", "of", "mel", "blanc"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what are the top rated horror movies of the 1970s", "tokens": ["what", "are", "the", "top", "rated", "horror", "movies", "of", "the", "1970s"], "ner_tags": ["O", "O", "O", "B-REVIEW", "I-REVIEW", "B-GENRE", "O", "O", "O", "B-YEAR"]}
{"sentence": "what is a funny scene from a movie where someone gets shot with tasers", "tokens": ["what", "is", "a", "funny", "scene", "from", "a", "movie", "where", "someone", "gets", "shot", "with", "tasers"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PLOT"]}
{"sentence": "who wrote the jaws theme song", "tokens": ["who", "wrote", "the", "jaws", "theme", "song"], "ner_tags": ["O", "O", "O", "B-TITLE", "B-SONG", "I-SONG"]}
{"sentence": "how was private benjamin rated", "tokens": ["how", "was", "private", "benjamin", "rated"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "O"]}
{"sentence": "who was the main character in star wars", "tokens": ["who", "was", "the", "main", "character", "in", "star", "wars"], "ner_tags": ["O", "O", "O", "B-CHARACTER", "I-CHARACTER", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "id like to see a g rated biographical directed by clint eastwood", "tokens": ["id", "like", "to", "see", "a", "g", "rated", "biographical", "directed", "by", "clint", "eastwood"], "ner_tags": ["O", "O", "O", "O", "O", "B-RATING", "O", "B-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "play a trailer for mash the movie", "tokens": ["play", "a", "trailer", "for", "mash", "the", "movie"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "O", "O"]}
{"sentence": "when does casa de mi padre come out", "tokens": ["when", "does", "casa", "de", "mi", "padre", "come", "out"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "O", "O"]}
{"sentence": "who was the lead role in casino", "tokens": ["who", "was", "the", "lead", "role", "in", "casino"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-TITLE"]}
{"sentence": "do you know whether a hard day s night is any good", "tokens": ["do", "you", "know", "whether", "a", "hard", "day", "s", "night", "is", "any", "good"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "O", "B-REVIEW", "I-REVIEW"]}
{"sentence": "who directed my cousin vinney", "tokens": ["who", "directed", "my", "cousin", "vinney"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "find the movie with rosalind russell as a nurse who helps polio patients", "tokens": ["find", "the", "movie", "with", "rosalind", "russell", "as", "a", "nurse", "who", "helps", "polio", "patients"], "ner_tags": ["O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "who directed savannah smiles", "tokens": ["who", "directed", "savannah", "smiles"], "ner_tags": ["O", "B-DIRECTOR", "B-TITLE", "I-TITLE"]}
{"sentence": "id like to find a mediocre r rated western movie about a gold miner", "tokens": ["id", "like", "to", "find", "a", "mediocre", "r", "rated", "western", "movie", "about", "a", "gold", "miner"], "ner_tags": ["O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "B-RATING", "O", "B-GENRE", "O", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "are there any movies that are rated r with murder plots", "tokens": ["are", "there", "any", "movies", "that", "are", "rated", "r", "with", "murder", "plots"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-RATING", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "what animation film stars maurice benard and has an average rating", "tokens": ["what", "animation", "film", "stars", "maurice", "benard", "and", "has", "an", "average", "rating"], "ner_tags": ["O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-RATINGS_AVERAGE", "O"]}
{"sentence": "what are some charlie chaplin dramas", "tokens": ["what", "are", "some", "charlie", "chaplin", "dramas"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "B-GENRE"]}
{"sentence": "list all dracula movies", "tokens": ["list", "all", "dracula", "movies"], "ner_tags": ["O", "O", "B-TITLE", "O"]}
{"sentence": "whats last years must see unrated fantasy", "tokens": ["whats", "last", "years", "must", "see", "unrated", "fantasy"], "ner_tags": ["O", "B-YEAR", "I-YEAR", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-RATING", "B-GENRE"]}
{"sentence": "what is the name of the helen hunt film about tornadoes", "tokens": ["what", "is", "the", "name", "of", "the", "helen", "hunt", "film", "about", "tornadoes"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-PLOT"]}
{"sentence": "is there a pg 13 larry morey mystery 1990 film", "tokens": ["is", "there", "a", "pg", "13", "larry", "morey", "mystery", "1990", "film"], "ner_tags": ["O", "O", "O", "B-RATING", "I-RATING", "B-DIRECTOR", "I-DIRECTOR", "B-GENRE", "B-YEAR", "O"]}
{"sentence": "in the past five decades has tatnya ali done a nc 17 movie", "tokens": ["in", "the", "past", "five", "decades", "has", "tatnya", "ali", "done", "a", "nc", "17", "movie"], "ner_tags": ["O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-RATING", "I-RATING", "O"]}
{"sentence": "show me all well rated movies about mexico", "tokens": ["show", "me", "all", "well", "rated", "movies", "about", "mexico"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "B-PLOT"]}
{"sentence": "is grandmas boy a good movie", "tokens": ["is", "grandmas", "boy", "a", "good", "movie"], "ner_tags": ["O", "B-TITLE", "I-TITLE", "O", "O", "O"]}
{"sentence": "is there a thriller that is highly anticipated due to come out soon", "tokens": ["is", "there", "a", "thriller", "that", "is", "highly", "anticipated", "due", "to", "come", "out", "soon"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what comedies are rated pg 13", "tokens": ["what", "comedies", "are", "rated", "pg", "13"], "ner_tags": ["O", "B-GENRE", "O", "O", "B-RATING", "I-RATING"]}
{"sentence": "find movies set in chicago", "tokens": ["find", "movies", "set", "in", "chicago"], "ner_tags": ["O", "O", "O", "O", "B-PLOT"]}
{"sentence": "tell me which comedy movie with queen latifah is the funniest", "tokens": ["tell", "me", "which", "comedy", "movie", "with", "queen", "latifah", "is", "the", "funniest"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-REVIEW"]}
{"sentence": "what biography was directed by gordon kent in 1980 and received a five rating", "tokens": ["what", "biography", "was", "directed", "by", "gordon", "kent", "in", "1980", "and", "received", "a", "five", "rating"], "ner_tags": ["O", "B-GENRE", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "B-YEAR", "O", "O", "O", "B-RATINGS_AVERAGE", "O"]}
{"sentence": "has florence and the machine been on any movie sountracks", "tokens": ["has", "florence", "and", "the", "machine", "been", "on", "any", "movie", "sountracks"], "ner_tags": ["O", "B-SONG", "I-SONG", "I-SONG", "I-SONG", "O", "O", "O", "O", "O"]}
{"sentence": "where is the movie any given sunday playing", "tokens": ["where", "is", "the", "movie", "any", "given", "sunday", "playing"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "O"]}
{"sentence": "what pg rated fantasy movies earned at least four stars in the past eight years", "tokens": ["what", "pg", "rated", "fantasy", "movies", "earned", "at", "least", "four", "stars", "in", "the", "past", "eight", "years"], "ner_tags": ["O", "B-RATING", "O", "B-GENRE", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "did james stewart act in any movies directed by michelangelo antonioni", "tokens": ["did", "james", "stewart", "act", "in", "any", "movies", "directed", "by", "michelangelo", "antonioni"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "what is the movie elephant rated", "tokens": ["what", "is", "the", "movie", "elephant", "rated"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "O"]}
{"sentence": "find an animation film from the past four years with a ratings average of eight stars", "tokens": ["find", "an", "animation", "film", "from", "the", "past", "four", "years", "with", "a", "ratings", "average", "of", "eight", "stars"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "what is the film that features the song my favorite things", "tokens": ["what", "is", "the", "film", "that", "features", "the", "song", "my", "favorite", "things"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-SONG", "I-SONG", "I-SONG"]}
{"sentence": "find a movie called cecil b demented", "tokens": ["find", "a", "movie", "called", "cecil", "b", "demented"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "what movies starring antonio banderas have reputations as having been received well by the viewing audiences", "tokens": ["what", "movies", "starring", "antonio", "banderas", "have", "reputations", "as", "having", "been", "received", "well", "by", "the", "viewing", "audiences"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "O", "O"]}
{"sentence": "please show me a website with clips of a hard day s night", "tokens": ["please", "show", "me", "a", "website", "with", "clips", "of", "a", "hard", "day", "s", "night"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-TRAILER", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "did james patterson direct a movie based on his books", "tokens": ["did", "james", "patterson", "direct", "a", "movie", "based", "on", "his", "books"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "how many movies were based on alex haleys books", "tokens": ["how", "many", "movies", "were", "based", "on", "alex", "haleys", "books"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "play a preview for santa clause versus the martians", "tokens": ["play", "a", "preview", "for", "santa", "clause", "versus", "the", "martians"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "has vivien leigh ever been in any independent films", "tokens": ["has", "vivien", "leigh", "ever", "been", "in", "any", "independent", "films"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "B-GENRE", "O"]}
{"sentence": "how many documentarys were mad in the past two decades", "tokens": ["how", "many", "documentarys", "were", "mad", "in", "the", "past", "two", "decades"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "what film genre is ferris buelers day off", "tokens": ["what", "film", "genre", "is", "ferris", "buelers", "day", "off"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "i want an r rated family movie from the 1980 s", "tokens": ["i", "want", "an", "r", "rated", "family", "movie", "from", "the", "1980", "s"], "ner_tags": ["O", "O", "O", "B-RATING", "O", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "find the movie with the song fireworks", "tokens": ["find", "the", "movie", "with", "the", "song", "fireworks"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-SONG"]}
{"sentence": "in what year did casablanca come out", "tokens": ["in", "what", "year", "did", "casablanca", "come", "out"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "O", "O"]}
{"sentence": "did val kilmer star in any comedies in the 80s", "tokens": ["did", "val", "kilmer", "star", "in", "any", "comedies", "in", "the", "80s"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-GENRE", "O", "O", "B-YEAR"]}
{"sentence": "how many pg 13 movies have bill murray", "tokens": ["how", "many", "pg", "13", "movies", "have", "bill", "murray"], "ner_tags": ["O", "O", "B-RATING", "I-RATING", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "harmony korine directed a war movie in the 1960 s that received seven stars", "tokens": ["harmony", "korine", "directed", "a", "war", "movie", "in", "the", "1960", "s", "that", "received", "seven", "stars"], "ner_tags": ["B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "what was the most recently released true crime movie", "tokens": ["what", "was", "the", "most", "recently", "released", "true", "crime", "movie"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-GENRE", "O"]}
{"sentence": "what was that 1960 mockumentary called that everyone said was all right", "tokens": ["what", "was", "that", "1960", "mockumentary", "called", "that", "everyone", "said", "was", "all", "right"], "ner_tags": ["O", "O", "O", "B-YEAR", "B-GENRE", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "was gina gershon in a pg 13 science fiction movie in 2000 that received good ratings", "tokens": ["was", "gina", "gershon", "in", "a", "pg", "13", "science", "fiction", "movie", "in", "2000", "that", "received", "good", "ratings"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "B-RATING", "I-RATING", "B-GENRE", "I-GENRE", "O", "O", "B-YEAR", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "what movies were directed by steven spielberg in the 1970s", "tokens": ["what", "movies", "were", "directed", "by", "steven", "spielberg", "in", "the", "1970s"], "ner_tags": ["O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-YEAR"]}
{"sentence": "are there any 1990 pg 13 romance films starring barbara stanwyck", "tokens": ["are", "there", "any", "1990", "pg", "13", "romance", "films", "starring", "barbara", "stanwyck"], "ner_tags": ["O", "O", "O", "B-YEAR", "B-RATING", "I-RATING", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "romantic films with brad pitt in them", "tokens": ["romantic", "films", "with", "brad", "pitt", "in", "them"], "ner_tags": ["B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O"]}
{"sentence": "who directed casino", "tokens": ["who", "directed", "casino"], "ner_tags": ["O", "O", "B-TITLE"]}
{"sentence": "find me movies with the rolling stones", "tokens": ["find", "me", "movies", "with", "the", "rolling", "stones"], "ner_tags": ["O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what is a western r rated movie that stars andrew bednarski from the 1950 s", "tokens": ["what", "is", "a", "western", "r", "rated", "movie", "that", "stars", "andrew", "bednarski", "from", "the", "1950", "s"], "ner_tags": ["O", "O", "O", "B-GENRE", "B-RATING", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "how many sequels were made for back to the future", "tokens": ["how", "many", "sequels", "were", "made", "for", "back", "to", "the", "future"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "has jimmy fallon had any serious movie hits", "tokens": ["has", "jimmy", "fallon", "had", "any", "serious", "movie", "hits"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "B-REVIEW"]}
{"sentence": "looking for the 1970s movie where woody allen plays a robot", "tokens": ["looking", "for", "the", "1970s", "movie", "where", "woody", "allen", "plays", "a", "robot"], "ner_tags": ["O", "O", "O", "B-YEAR", "O", "O", "B-ACTOR", "I-ACTOR", "B-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "are there any movie based on martin luther king", "tokens": ["are", "there", "any", "movie", "based", "on", "martin", "luther", "king"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-CHARACTER", "I-CHARACTER", "I-CHARACTER"]}
{"sentence": "list a six stars science fiction with mimi rogers thats rated r", "tokens": ["list", "a", "six", "stars", "science", "fiction", "with", "mimi", "rogers", "thats", "rated", "r"], "ner_tags": ["O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "I-GENRE", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-RATING"]}
{"sentence": "has deforest kelly been in a movie that was rated r", "tokens": ["has", "deforest", "kelly", "been", "in", "a", "movie", "that", "was", "rated", "r"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "O", "O", "B-RATING"]}
{"sentence": "are there any bank robbery movies out now", "tokens": ["are", "there", "any", "bank", "robbery", "movies", "out", "now"], "ner_tags": ["O", "O", "O", "B-PLOT", "I-PLOT", "O", "O", "O"]}
{"sentence": "what animated fish says keep swimming all the time", "tokens": ["what", "animated", "fish", "says", "keep", "swimming", "all", "the", "time"], "ner_tags": ["O", "B-CHARACTER", "I-CHARACTER", "O", "O", "O", "O", "O", "O"]}
{"sentence": "im looking for an nc 17 mockumentary from the past year directed by vino salame that was liked a lot", "tokens": ["im", "looking", "for", "an", "nc", "17", "mockumentary", "from", "the", "past", "year", "directed", "by", "vino", "salame", "that", "was", "liked", "a", "lot"], "ner_tags": ["O", "O", "O", "O", "B-RATING", "I-RATING", "B-GENRE", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "what famous 80s rock band appeared in the movie empire records", "tokens": ["what", "famous", "80s", "rock", "band", "appeared", "in", "the", "movie", "empire", "records"], "ner_tags": ["O", "O", "O", "O", "B-SONG", "I-SONG", "I-SONG", "I-SONG", "I-SONG", "I-SONG", "I-SONG"]}
{"sentence": "what was that r rated movie called that was released in the last four decades that had dan akroyd in that imaginary world", "tokens": ["what", "was", "that", "r", "rated", "movie", "called", "that", "was", "released", "in", "the", "last", "four", "decades", "that", "had", "dan", "akroyd", "in", "that", "imaginary", "world"], "ner_tags": ["O", "O", "O", "B-RATING", "O", "O", "O", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "find a 1950 s animation film", "tokens": ["find", "a", "1950", "s", "animation", "film"], "ner_tags": ["O", "O", "B-YEAR", "I-YEAR", "B-GENRE", "O"]}
{"sentence": "what is the 1950 horror movie rated nine stars was directed by roland emmerich", "tokens": ["what", "is", "the", "1950", "horror", "movie", "rated", "nine", "stars", "was", "directed", "by", "roland", "emmerich"], "ner_tags": ["O", "O", "O", "B-YEAR", "B-GENRE", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "what is the funniest comedy of all time", "tokens": ["what", "is", "the", "funniest", "comedy", "of", "all", "time"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O", "O", "O"]}
{"sentence": "what is a rated r film with jamie kennedy", "tokens": ["what", "is", "a", "rated", "r", "film", "with", "jamie", "kennedy"], "ner_tags": ["O", "O", "O", "O", "B-RATING", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what was the name of indiana jones director", "tokens": ["what", "was", "the", "name", "of", "indiana", "jones", "director"], "ner_tags": ["O", "O", "O", "O", "O", "B-CHARACTER", "I-CHARACTER", "O"]}
{"sentence": "is there a past decade military movie starring anne bancroft", "tokens": ["is", "there", "a", "past", "decade", "military", "movie", "starring", "anne", "bancroft"], "ner_tags": ["O", "O", "O", "B-YEAR", "I-YEAR", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what was the most suceessful film to receive unlaterally bad critic reviews", "tokens": ["what", "was", "the", "most", "suceessful", "film", "to", "receive", "unlaterally", "bad", "critic", "reviews"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-REVIEW", "I-REVIEW", "I-REVIEW"]}
{"sentence": "what highly recommend movie from 1990 was about a curse", "tokens": ["what", "highly", "recommend", "movie", "from", "1990", "was", "about", "a", "curse"], "ner_tags": ["O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "B-YEAR", "O", "O", "O", "B-PLOT"]}
{"sentence": "guessing by the title is stonerville about getting stoned", "tokens": ["guessing", "by", "the", "title", "is", "stonerville", "about", "getting", "stoned"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "O", "O", "O"]}
{"sentence": "is there a tom selleck horror movie that is average", "tokens": ["is", "there", "a", "tom", "selleck", "horror", "movie", "that", "is", "average"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "B-GENRE", "O", "O", "O", "B-RATINGS_AVERAGE"]}
{"sentence": "which movie includes your touch by the black keys", "tokens": ["which", "movie", "includes", "your", "touch", "by", "the", "black", "keys"], "ner_tags": ["O", "O", "O", "B-SONG", "I-SONG", "O", "O", "B-SONG", "I-SONG"]}
{"sentence": "wanting to know what in the past four years was a watchable murder movie that was good", "tokens": ["wanting", "to", "know", "what", "in", "the", "past", "four", "years", "was", "a", "watchable", "murder", "movie", "that", "was", "good"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "B-RATINGS_AVERAGE", "B-PLOT", "O", "O", "O", "O"]}
{"sentence": "how many family movies have been made in the last six years", "tokens": ["how", "many", "family", "movies", "have", "been", "made", "in", "the", "last", "six", "years"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "show me a comedy from the 1990s starring jim carrey", "tokens": ["show", "me", "a", "comedy", "from", "the", "1990s", "starring", "jim", "carrey"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-YEAR", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "who was the voice of ariel in the little mermaid", "tokens": ["who", "was", "the", "voice", "of", "ariel", "in", "the", "little", "mermaid"], "ner_tags": ["O", "O", "O", "O", "O", "B-CHARACTER", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "what is the title of an animated movie starring tom hanks that is rated pg", "tokens": ["what", "is", "the", "title", "of", "an", "animated", "movie", "starring", "tom", "hanks", "that", "is", "rated", "pg"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-RATING"]}
{"sentence": "does steven spielberg produce a blockbuster film in the 1980s", "tokens": ["does", "steven", "spielberg", "produce", "a", "blockbuster", "film", "in", "the", "1980s"], "ner_tags": ["O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "B-YEAR"]}
{"sentence": "provide a list of science fiction movies from the 1970 s that are rated r", "tokens": ["provide", "a", "list", "of", "science", "fiction", "movies", "from", "the", "1970", "s", "that", "are", "rated", "r"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "I-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "O", "B-RATING"]}
{"sentence": "is there a 1940 crime movie rated r that stars tracie lords and has good ratings", "tokens": ["is", "there", "a", "1940", "crime", "movie", "rated", "r", "that", "stars", "tracie", "lords", "and", "has", "good", "ratings"], "ner_tags": ["O", "O", "O", "B-YEAR", "B-GENRE", "O", "O", "B-RATING", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "what is a good documentary to watch", "tokens": ["what", "is", "a", "good", "documentary", "to", "watch"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O", "O"]}
{"sentence": "could you direct me to where i can find a childrens movie", "tokens": ["could", "you", "direct", "me", "to", "where", "i", "can", "find", "a", "childrens", "movie"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-GENRE", "O"]}
{"sentence": "what is the genre of duel", "tokens": ["what", "is", "the", "genre", "of", "duel"], "ner_tags": ["O", "O", "O", "O", "O", "B-GENRE"]}
{"sentence": "how many versions of a christmas carol are there", "tokens": ["how", "many", "versions", "of", "a", "christmas", "carol", "are", "there"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "O", "O"]}
{"sentence": "i am looking for the top romantic comedies from the 2000s", "tokens": ["i", "am", "looking", "for", "the", "top", "romantic", "comedies", "from", "the", "2000s"], "ner_tags": ["O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "B-GENRE", "I-GENRE", "O", "O", "B-YEAR"]}
{"sentence": "what movies involve growing up", "tokens": ["what", "movies", "involve", "growing", "up"], "ner_tags": ["O", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "what is the last movie that johnny depp starred in", "tokens": ["what", "is", "the", "last", "movie", "that", "johnny", "depp", "starred", "in"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O"]}
{"sentence": "whats the movie with a song by coolio", "tokens": ["whats", "the", "movie", "with", "a", "song", "by", "coolio"], "ner_tags": ["O", "O", "O", "O", "O", "B-SONG", "I-SONG", "I-SONG"]}
{"sentence": "find movie inception directed by charles chaplin", "tokens": ["find", "movie", "inception", "directed", "by", "charles", "chaplin"], "ner_tags": ["O", "O", "B-TITLE", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "find the woody allen movie about movie characters coming to life", "tokens": ["find", "the", "woody", "allen", "movie", "about", "movie", "characters", "coming", "to", "life"], "ner_tags": ["O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "who directed the film godfellas", "tokens": ["who", "directed", "the", "film", "godfellas"], "ner_tags": ["O", "O", "O", "O", "B-TITLE"]}
{"sentence": "what movie was liked by many starring kiefer sutherland", "tokens": ["what", "movie", "was", "liked", "by", "many", "starring", "kiefer", "sutherland"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "find me an r rated thriller", "tokens": ["find", "me", "an", "r", "rated", "thriller"], "ner_tags": ["O", "O", "O", "B-RATING", "I-RATING", "B-GENRE"]}
{"sentence": "is there any movies that johnny depp is in that we should consider seeing", "tokens": ["is", "there", "any", "movies", "that", "johnny", "depp", "is", "in", "that", "we", "should", "consider", "seeing"], "ner_tags": ["O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "what are historical movies from james cameron", "tokens": ["what", "are", "historical", "movies", "from", "james", "cameron"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "did audrey hepburn audition for a role in inception", "tokens": ["did", "audrey", "hepburn", "audition", "for", "a", "role", "in", "inception"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "B-TITLE"]}
{"sentence": "is wall e a good movie for adults", "tokens": ["is", "wall", "e", "a", "good", "movie", "for", "adults"], "ner_tags": ["O", "B-TITLE", "I-TITLE", "O", "O", "O", "O", "O"]}
{"sentence": "what nc 17 crime movie made in 1940 got a rating average of seven", "tokens": ["what", "nc", "17", "crime", "movie", "made", "in", "1940", "got", "a", "rating", "average", "of", "seven"], "ner_tags": ["O", "B-RATING", "I-RATING", "B-GENRE", "O", "O", "O", "B-YEAR", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE"]}
{"sentence": "list war movies that are rated pg 13", "tokens": ["list", "war", "movies", "that", "are", "rated", "pg", "13"], "ner_tags": ["O", "B-GENRE", "O", "O", "O", "O", "B-RATING", "I-RATING"]}
{"sentence": "show me indiana jones movies", "tokens": ["show", "me", "indiana", "jones", "movies"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "O"]}
{"sentence": "i want to watch all the harry potter movies in order by release year", "tokens": ["i", "want", "to", "watch", "all", "the", "harry", "potter", "movies", "in", "order", "by", "release", "year"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "O", "O", "O", "O", "O", "O"]}
{"sentence": "who is the movie fear of the dark", "tokens": ["who", "is", "the", "movie", "fear", "of", "the", "dark"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "who stars into the blue", "tokens": ["who", "stars", "into", "the", "blue"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "find the movie babe", "tokens": ["find", "the", "movie", "babe"], "ner_tags": ["O", "O", "O", "B-TITLE"]}
{"sentence": "i want to watch a movie with a two thumbs up rating that is about crime", "tokens": ["i", "want", "to", "watch", "a", "movie", "with", "a", "two", "thumbs", "up", "rating", "that", "is", "about", "crime"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "O", "O", "B-GENRE"]}
{"sentence": "i want a rated r horror movie starring dina meyer", "tokens": ["i", "want", "a", "rated", "r", "horror", "movie", "starring", "dina", "meyer"], "ner_tags": ["O", "O", "O", "O", "B-RATING", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "is there a 1940 s sci fi movie rated r starring christian slater", "tokens": ["is", "there", "a", "1940", "s", "sci", "fi", "movie", "rated", "r", "starring", "christian", "slater"], "ner_tags": ["O", "O", "O", "B-YEAR", "I-YEAR", "B-GENRE", "I-GENRE", "O", "O", "B-RATING", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what 1980 action movie about assassination is directed by richard curtis and unrated", "tokens": ["what", "1980", "action", "movie", "about", "assassination", "is", "directed", "by", "richard", "curtis", "and", "unrated"], "ner_tags": ["O", "B-YEAR", "B-GENRE", "O", "O", "B-PLOT", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "B-RATING"]}
{"sentence": "how many r rated movies came out in 2011", "tokens": ["how", "many", "r", "rated", "movies", "came", "out", "in", "2011"], "ner_tags": ["O", "O", "B-RATING", "O", "O", "O", "O", "O", "B-YEAR"]}
{"sentence": "are there any movie based on just lions", "tokens": ["are", "there", "any", "movie", "based", "on", "just", "lions"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-PLOT"]}
{"sentence": "what is brian de palma most known for", "tokens": ["what", "is", "brian", "de", "palma", "most", "known", "for"], "ner_tags": ["O", "O", "B-DIRECTOR", "I-DIRECTOR", "I-DIRECTOR", "O", "O", "O"]}
{"sentence": "are there any mystery movies that had a very good or better rating", "tokens": ["are", "there", "any", "mystery", "movies", "that", "had", "a", "very", "good", "or", "better", "rating"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "O"]}
{"sentence": "which r rated short directed by kirk r thatcher received a rating of decent", "tokens": ["which", "r", "rated", "short", "directed", "by", "kirk", "r", "thatcher", "received", "a", "rating", "of", "decent"], "ner_tags": ["O", "B-RATING", "O", "B-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "O", "B-RATINGS_AVERAGE"]}
{"sentence": "who directed who am i", "tokens": ["who", "directed", "who", "am", "i"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "what 1970 s war film starred christopher lambert", "tokens": ["what", "1970", "s", "war", "film", "starred", "christopher", "lambert"], "ner_tags": ["O", "B-YEAR", "I-YEAR", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "show me a list of comedies that star ryan reynold", "tokens": ["show", "me", "a", "list", "of", "comedies", "that", "star", "ryan", "reynold"], "ner_tags": ["O", "O", "O", "O", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "which 1960 s film noir directed by duncan jones received an nc 17 rating", "tokens": ["which", "1960", "s", "film", "noir", "directed", "by", "duncan", "jones", "received", "an", "nc", "17", "rating"], "ner_tags": ["O", "B-YEAR", "I-YEAR", "B-GENRE", "I-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-RATING", "I-RATING", "O"]}
{"sentence": "how many james bond movies were made before the year 2000", "tokens": ["how", "many", "james", "bond", "movies", "were", "made", "before", "the", "year", "2000"], "ner_tags": ["O", "O", "B-CHARACTER", "I-CHARACTER", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "what was the mediocre r rated romance with james woods in it", "tokens": ["what", "was", "the", "mediocre", "r", "rated", "romance", "with", "james", "woods", "in", "it"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "B-RATING", "O", "B-GENRE", "O", "B-ACTOR", "I-ACTOR", "O", "O"]}
{"sentence": "is there a musical starring julia roberts", "tokens": ["is", "there", "a", "musical", "starring", "julia", "roberts"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "a very popular war movie in the last eight years that is unrated", "tokens": ["a", "very", "popular", "war", "movie", "in", "the", "last", "eight", "years", "that", "is", "unrated"], "ner_tags": ["O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "B-RATING"]}
{"sentence": "is there a horror movie starring charlton heston", "tokens": ["is", "there", "a", "horror", "movie", "starring", "charlton", "heston"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "list the movies that billy bob thorton has acted in", "tokens": ["list", "the", "movies", "that", "billy", "bob", "thorton", "has", "acted", "in"], "ner_tags": ["O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "I-ACTOR", "O", "O", "O"]}
{"sentence": "is wall e well rated", "tokens": ["is", "wall", "e", "well", "rated"], "ner_tags": ["O", "B-TITLE", "I-TITLE", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "who played the voice of lightening mcqueen in cars", "tokens": ["who", "played", "the", "voice", "of", "lightening", "mcqueen", "in", "cars"], "ner_tags": ["O", "O", "O", "O", "O", "B-CHARACTER", "I-CHARACTER", "O", "B-TITLE"]}
{"sentence": "what is a good horror movie directed by orson welles", "tokens": ["what", "is", "a", "good", "horror", "movie", "directed", "by", "orson", "welles"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "has sofia coppola ever directed a disaster movie", "tokens": ["has", "sofia", "coppola", "ever", "directed", "a", "disaster", "movie"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "B-GENRE", "O"]}
{"sentence": "find a cowboy western filmed in italy", "tokens": ["find", "a", "cowboy", "western", "filmed", "in", "italy"], "ner_tags": ["O", "O", "B-PLOT", "B-GENRE", "O", "O", "O"]}
{"sentence": "whats the movie directed by steven spielberg about world war ii", "tokens": ["whats", "the", "movie", "directed", "by", "steven", "spielberg", "about", "world", "war", "ii"], "ner_tags": ["O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "B-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "did m night shyamalan direct a western in the 1970 s that was rated eight", "tokens": ["did", "m", "night", "shyamalan", "direct", "a", "western", "in", "the", "1970", "s", "that", "was", "rated", "eight"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "I-DIRECTOR", "O", "O", "B-GENRE", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "O", "B-RATINGS_AVERAGE"]}
{"sentence": "find me movies with suspense from 1997", "tokens": ["find", "me", "movies", "with", "suspense", "from", "1997"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O", "B-YEAR"]}
{"sentence": "what is the cabin in the woods about", "tokens": ["what", "is", "the", "cabin", "in", "the", "woods", "about"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "O"]}
{"sentence": "show me the james bond movie with jane seymour as the tarot card reader", "tokens": ["show", "me", "the", "james", "bond", "movie", "with", "jane", "seymour", "as", "the", "tarot", "card", "reader"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O"]}
{"sentence": "the lost weekend", "tokens": ["the", "lost", "weekend"], "ner_tags": ["B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "which harry potter films were the best", "tokens": ["which", "harry", "potter", "films", "were", "the", "best"], "ner_tags": ["O", "B-TITLE", "I-TITLE", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "what movie is the song brown sugar by the rolling stones in", "tokens": ["what", "movie", "is", "the", "song", "brown", "sugar", "by", "the", "rolling", "stones", "in"], "ner_tags": ["O", "O", "O", "O", "O", "B-SONG", "I-SONG", "O", "O", "B-SONG", "I-SONG", "O"]}
{"sentence": "list a psychological themed movie released within the last seven years rated pg 13 and had excellent ratings by critics", "tokens": ["list", "a", "psychological", "themed", "movie", "released", "within", "the", "last", "seven", "years", "rated", "pg", "13", "and", "had", "excellent", "ratings", "by", "critics"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "B-RATING", "I-RATING", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O"]}
{"sentence": "did george lucas direct any must see action movies", "tokens": ["did", "george", "lucas", "direct", "any", "must", "see", "action", "movies"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "O", "B-GENRE", "O"]}
{"sentence": "find the movie about a cornfield and dead baseball players", "tokens": ["find", "the", "movie", "about", "a", "cornfield", "and", "dead", "baseball", "players"], "ner_tags": ["O", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "was there a halloween movie with a r rating", "tokens": ["was", "there", "a", "halloween", "movie", "with", "a", "r", "rating"], "ner_tags": ["O", "O", "O", "B-TITLE", "O", "O", "O", "B-RATING", "I-RATING"]}
{"sentence": "was martin scorsese offered the job of directing wall e", "tokens": ["was", "martin", "scorsese", "offered", "the", "job", "of", "directing", "wall", "e"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "what is a pg 13 mockumentary from the last nine years with ving rhames that got ok ratings", "tokens": ["what", "is", "a", "pg", "13", "mockumentary", "from", "the", "last", "nine", "years", "with", "ving", "rhames", "that", "got", "ok", "ratings"], "ner_tags": ["O", "O", "O", "B-RATING", "I-RATING", "B-GENRE", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-RATINGS_AVERAGE", "O"]}
{"sentence": "are there any movies with ballet dancers", "tokens": ["are", "there", "any", "movies", "with", "ballet", "dancers"], "ner_tags": ["O", "O", "O", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "intolerance", "tokens": ["intolerance"], "ner_tags": ["B-TITLE"]}
{"sentence": "what genre is the movie my date with the presidents daughter", "tokens": ["what", "genre", "is", "the", "movie", "my", "date", "with", "the", "presidents", "daughter"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "name the 2010 remake of movie about flesh eating fishes", "tokens": ["name", "the", "2010", "remake", "of", "movie", "about", "flesh", "eating", "fishes"], "ner_tags": ["O", "O", "B-YEAR", "O", "O", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "looking for a nicole kidman movie about ghosts", "tokens": ["looking", "for", "a", "nicole", "kidman", "movie", "about", "ghosts"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-PLOT"]}
{"sentence": "in the past three decades is there an rated g adventure movie starring regina taylor", "tokens": ["in", "the", "past", "three", "decades", "is", "there", "an", "rated", "g", "adventure", "movie", "starring", "regina", "taylor"], "ner_tags": ["O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "O", "O", "B-RATING", "B-GENRE", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "where can i find a cast list for a bittersweet life", "tokens": ["where", "can", "i", "find", "a", "cast", "list", "for", "a", "bittersweet", "life"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "what is the last animated movie that came out", "tokens": ["what", "is", "the", "last", "animated", "movie", "that", "came", "out"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O", "O", "O", "O"]}
{"sentence": "what year was the first terminator movie released", "tokens": ["what", "year", "was", "the", "first", "terminator", "movie", "released"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "O", "O"]}
{"sentence": "what movie had nicole kidman and sandra bullock as witches", "tokens": ["what", "movie", "had", "nicole", "kidman", "and", "sandra", "bullock", "as", "witches"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "B-ACTOR", "I-ACTOR", "O", "B-PLOT"]}
{"sentence": "list rated nc 17 sci fi films starring rider king strong that people said was watchable", "tokens": ["list", "rated", "nc", "17", "sci", "fi", "films", "starring", "rider", "king", "strong", "that", "people", "said", "was", "watchable"], "ner_tags": ["O", "O", "B-RATING", "I-RATING", "B-GENRE", "I-GENRE", "O", "O", "B-ACTOR", "I-ACTOR", "I-ACTOR", "O", "O", "O", "O", "B-RATINGS_AVERAGE"]}
{"sentence": "name the the sequel of the movie pitch black starring vin diesel", "tokens": ["name", "the", "the", "sequel", "of", "the", "movie", "pitch", "black", "starring", "vin", "diesel"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "O", "O", "O"]}
{"sentence": "find a pg 13 rated film from the past eight years starring ron perlman", "tokens": ["find", "a", "pg", "13", "rated", "film", "from", "the", "past", "eight", "years", "starring", "ron", "perlman"], "ner_tags": ["O", "O", "B-RATING", "I-RATING", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "has there been any movies about criminals breaking into a safe in the past five years", "tokens": ["has", "there", "been", "any", "movies", "about", "criminals", "breaking", "into", "a", "safe", "in", "the", "past", "five", "years"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PLOT", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "what movies have superman in them", "tokens": ["what", "movies", "have", "superman", "in", "them"], "ner_tags": ["O", "O", "O", "B-PLOT", "O", "O"]}
{"sentence": "has erik macarthur produced a documentary this year", "tokens": ["has", "erik", "macarthur", "produced", "a", "documentary", "this", "year"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-GENRE", "B-YEAR", "I-YEAR"]}
{"sentence": "name the main actors in windtalkers", "tokens": ["name", "the", "main", "actors", "in", "windtalkers"], "ner_tags": ["O", "O", "O", "B-ACTOR", "O", "B-TITLE"]}
{"sentence": "list a seven stars r rated film with tracy scoggins within the last three decades", "tokens": ["list", "a", "seven", "stars", "r", "rated", "film", "with", "tracy", "scoggins", "within", "the", "last", "three", "decades"], "ner_tags": ["O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-RATING", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "what movies rated pg 13 were released in 1999 and deal with childhood memories", "tokens": ["what", "movies", "rated", "pg", "13", "were", "released", "in", "1999", "and", "deal", "with", "childhood", "memories"], "ner_tags": ["O", "O", "O", "B-RATING", "I-RATING", "O", "O", "O", "B-YEAR", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "list all the movies with leonardo dicaprio in the 90s", "tokens": ["list", "all", "the", "movies", "with", "leonardo", "dicaprio", "in", "the", "90s"], "ner_tags": ["O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-YEAR"]}
{"sentence": "is there a movie starring clint eastwood which director is woody allen", "tokens": ["is", "there", "a", "movie", "starring", "clint", "eastwood", "which", "director", "is", "woody", "allen"], "ner_tags": ["O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "what shakespeare films take place in england", "tokens": ["what", "shakespeare", "films", "take", "place", "in", "england"], "ner_tags": ["O", "B-GENRE", "O", "O", "O", "O", "B-PLOT"]}
{"sentence": "list an independent g rated film that received nine stars and above", "tokens": ["list", "an", "independent", "g", "rated", "film", "that", "received", "nine", "stars", "and", "above"], "ner_tags": ["O", "O", "B-GENRE", "B-RATING", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "who voiced puss n boots in shrek", "tokens": ["who", "voiced", "puss", "n", "boots", "in", "shrek"], "ner_tags": ["O", "O", "B-CHARACTER", "I-CHARACTER", "I-CHARACTER", "O", "B-TITLE"]}
{"sentence": "what movie was just released that is based on a book", "tokens": ["what", "movie", "was", "just", "released", "that", "is", "based", "on", "a", "book"], "ner_tags": ["O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "list a movie made in the 1960 s with a rating of nc 17 with actor tanya roberts", "tokens": ["list", "a", "movie", "made", "in", "the", "1960", "s", "with", "a", "rating", "of", "nc", "17", "with", "actor", "tanya", "roberts"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "O", "O", "B-RATING", "I-RATING", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "run alices restaurant", "tokens": ["run", "alices", "restaurant"], "ner_tags": ["B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "has kathy bates starred in any romantic comedies", "tokens": ["has", "kathy", "bates", "starred", "in", "any", "romantic", "comedies"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-GENRE", "I-GENRE"]}
{"sentence": "show me a movie starring jake gelenhal and his sister", "tokens": ["show", "me", "a", "movie", "starring", "jake", "gelenhal", "and", "his", "sister"], "ner_tags": ["O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O"]}
{"sentence": "find a hockey movie", "tokens": ["find", "a", "hockey", "movie"], "ner_tags": ["O", "O", "B-GENRE", "O"]}
{"sentence": "find a korean war movie", "tokens": ["find", "a", "korean", "war", "movie"], "ner_tags": ["O", "O", "B-PLOT", "I-PLOT", "O"]}
{"sentence": "how was the movie wall e directed by david lean", "tokens": ["how", "was", "the", "movie", "wall", "e", "directed", "by", "david", "lean"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "find a pg rated shirley temple movie", "tokens": ["find", "a", "pg", "rated", "shirley", "temple", "movie"], "ner_tags": ["O", "O", "B-RATING", "I-RATING", "B-ACTOR", "I-ACTOR", "O"]}
{"sentence": "looking for the movie where johnny depp plays a cross dressing film director", "tokens": ["looking", "for", "the", "movie", "where", "johnny", "depp", "plays", "a", "cross", "dressing", "film", "director"], "ner_tags": ["O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "what is the name of the animation movie that was unrated and liked by many starring ingo rademacher", "tokens": ["what", "is", "the", "name", "of", "the", "animation", "movie", "that", "was", "unrated", "and", "liked", "by", "many", "starring", "ingo", "rademacher"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-GENRE", "O", "O", "O", "B-RATING", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "what was peter jacksons first film", "tokens": ["what", "was", "peter", "jacksons", "first", "film"], "ner_tags": ["O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O"]}
{"sentence": "find me movies with the beatles on the soundtrack", "tokens": ["find", "me", "movies", "with", "the", "beatles", "on", "the", "soundtrack"], "ner_tags": ["O", "O", "O", "O", "O", "B-SONG", "O", "O", "B-SONG"]}
{"sentence": "summarize the plot of shes all that", "tokens": ["summarize", "the", "plot", "of", "shes", "all", "that"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "find the movie where audrey hepburn is a nun in the belgian congo", "tokens": ["find", "the", "movie", "where", "audrey", "hepburn", "is", "a", "nun", "in", "the", "belgian", "congo"], "ner_tags": ["O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "did billy wilder direct a highly liked animated movie that was rated r", "tokens": ["did", "billy", "wilder", "direct", "a", "highly", "liked", "animated", "movie", "that", "was", "rated", "r"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "O", "O", "O", "O", "B-RATING"]}
{"sentence": "open city", "tokens": ["open", "city"], "ner_tags": ["B-TITLE", "I-TITLE"]}
{"sentence": "who stars in greta", "tokens": ["who", "stars", "in", "greta"], "ner_tags": ["O", "O", "O", "B-TITLE"]}
{"sentence": "frankenstein", "tokens": ["frankenstein"], "ner_tags": ["B-TITLE"]}
{"sentence": "how many movies has bruce willis been in", "tokens": ["how", "many", "movies", "has", "bruce", "willis", "been", "in"], "ner_tags": ["O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O"]}
{"sentence": "list a documentary film r rated that centers a religion which was made in 1990 s and was rated as must see", "tokens": ["list", "a", "documentary", "film", "r", "rated", "that", "centers", "a", "religion", "which", "was", "made", "in", "1990", "s", "and", "was", "rated", "as", "must", "see"], "ner_tags": ["O", "O", "B-GENRE", "O", "B-RATING", "O", "O", "O", "O", "B-PLOT", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "name the horror movie starring edward boase that people give two thumbs up", "tokens": ["name", "the", "horror", "movie", "starring", "edward", "boase", "that", "people", "give", "two", "thumbs", "up"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "movie that came out in 1950 with maria bello that received two thumbs up", "tokens": ["movie", "that", "came", "out", "in", "1950", "with", "maria", "bello", "that", "received", "two", "thumbs", "up"], "ner_tags": ["O", "O", "O", "O", "O", "B-YEAR", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "what 1970 pg 13 movie starring kyle chandler received average ratings", "tokens": ["what", "1970", "pg", "13", "movie", "starring", "kyle", "chandler", "received", "average", "ratings"], "ner_tags": ["O", "B-YEAR", "B-RATING", "I-RATING", "O", "O", "B-ACTOR", "I-ACTOR", "O", "B-RATINGS_AVERAGE", "O"]}
{"sentence": "how many nc 17 film noir movies are there", "tokens": ["how", "many", "nc", "17", "film", "noir", "movies", "are", "there"], "ner_tags": ["O", "O", "B-RATING", "I-RATING", "B-GENRE", "I-GENRE", "O", "O", "O"]}
{"sentence": "doctor zhivago", "tokens": ["doctor", "zhivago"], "ner_tags": ["B-TITLE", "I-TITLE"]}
{"sentence": "is there a thriller movie which director was oliver stone", "tokens": ["is", "there", "a", "thriller", "movie", "which", "director", "was", "oliver", "stone"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "how may comedy movies were released in the 2010 s", "tokens": ["how", "may", "comedy", "movies", "were", "released", "in", "the", "2010", "s"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "who starred in bullitt", "tokens": ["who", "starred", "in", "bullitt"], "ner_tags": ["O", "O", "O", "B-TITLE"]}
{"sentence": "dont you just love the movie that thing you do", "tokens": ["dont", "you", "just", "love", "the", "movie", "that", "thing", "you", "do"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "show me baseball movies from the 1950s", "tokens": ["show", "me", "baseball", "movies", "from", "the", "1950s"], "ner_tags": ["O", "O", "B-PLOT", "O", "O", "O", "B-GENRE"]}
{"sentence": "list where i can buy serenity", "tokens": ["list", "where", "i", "can", "buy", "serenity"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE"]}
{"sentence": "i would like air bud please", "tokens": ["i", "would", "like", "air", "bud", "please"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE", "O"]}
{"sentence": "has there movie made in the last five decades about nuclear attack", "tokens": ["has", "there", "movie", "made", "in", "the", "last", "five", "decades", "about", "nuclear", "attack"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "who was the voice of the frog in thr princess and the frog", "tokens": ["who", "was", "the", "voice", "of", "the", "frog", "in", "thr", "princess", "and", "the", "frog"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-CHARACTER", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "has orson welles ever directed a mockumentary that was rated pg 13", "tokens": ["has", "orson", "welles", "ever", "directed", "a", "mockumentary", "that", "was", "rated", "pg", "13"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "B-GENRE", "O", "O", "O", "B-RATING", "I-RATING"]}
{"sentence": "find a really popular sport film from the 1970 s", "tokens": ["find", "a", "really", "popular", "sport", "film", "from", "the", "1970", "s"], "ner_tags": ["O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "who directed the wicker man", "tokens": ["who", "directed", "the", "wicker", "man"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "list pg tale films centers on scar this year", "tokens": ["list", "pg", "tale", "films", "centers", "on", "scar", "this", "year"], "ner_tags": ["O", "B-RATING", "B-GENRE", "O", "O", "O", "B-PLOT", "B-YEAR", "I-YEAR"]}
{"sentence": "closely watched trains", "tokens": ["closely", "watched", "trains"], "ner_tags": ["B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "when did natural selection premiere", "tokens": ["when", "did", "natural", "selection", "premiere"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE", "O"]}
{"sentence": "list keri russell maid of honor movie with jokes in last six years", "tokens": ["list", "keri", "russell", "maid", "of", "honor", "movie", "with", "jokes", "in", "last", "six", "years"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "B-PLOT", "I-PLOT", "I-PLOT", "O", "O", "B-GENRE", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "what was the plot of days of thunder", "tokens": ["what", "was", "the", "plot", "of", "days", "of", "thunder"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "show me a black and white dramatic film", "tokens": ["show", "me", "a", "black", "and", "white", "dramatic", "film"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-GENRE", "O"]}
{"sentence": "who was the voice of the pig in toy story", "tokens": ["who", "was", "the", "voice", "of", "the", "pig", "in", "toy", "story"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-PLOT", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "i would like a movie titled forgiven", "tokens": ["i", "would", "like", "a", "movie", "titled", "forgiven"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-TITLE"]}
{"sentence": "how many movies has stanley kubrick directed", "tokens": ["how", "many", "movies", "has", "stanley", "kubrick", "directed"], "ner_tags": ["O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O"]}
{"sentence": "i want a film about go go dancers", "tokens": ["i", "want", "a", "film", "about", "go", "go", "dancers"], "ner_tags": ["O", "O", "O", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "is morgan freeman in any mockumentary films that are rated pg 13", "tokens": ["is", "morgan", "freeman", "in", "any", "mockumentary", "films", "that", "are", "rated", "pg", "13"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "B-GENRE", "O", "O", "O", "O", "B-RATING", "I-RATING"]}
{"sentence": "what scary r rated film starred lacey chabert", "tokens": ["what", "scary", "r", "rated", "film", "starred", "lacey", "chabert"], "ner_tags": ["O", "B-GENRE", "B-RATING", "O", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "which star wars movie featured ewoks", "tokens": ["which", "star", "wars", "movie", "featured", "ewoks"], "ner_tags": ["O", "B-TITLE", "I-TITLE", "O", "O", "B-CHARACTER"]}
{"sentence": "who is the male lead actor in childs play", "tokens": ["who", "is", "the", "male", "lead", "actor", "in", "childs", "play"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "according to the viewers what is the best judd apatow movie", "tokens": ["according", "to", "the", "viewers", "what", "is", "the", "best", "judd", "apatow", "movie"], "ner_tags": ["B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O"]}
{"sentence": "was there a trailer for silence of the lambs", "tokens": ["was", "there", "a", "trailer", "for", "silence", "of", "the", "lambs"], "ner_tags": ["O", "O", "O", "B-TRAILER", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "what pg 13 biography released 2010 in was rated very good that featured bette midler in a 1930 s film", "tokens": ["what", "pg", "13", "biography", "released", "2010", "in", "was", "rated", "very", "good", "that", "featured", "bette", "midler", "in", "a", "1930", "s", "film"], "ner_tags": ["O", "B-RATING", "I-RATING", "B-GENRE", "O", "B-YEAR", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-PLOT", "I-PLOT", "O"]}
{"sentence": "just an average adventure film please", "tokens": ["just", "an", "average", "adventure", "film", "please"], "ner_tags": ["O", "O", "B-RATINGS_AVERAGE", "B-GENRE", "O", "O"]}
{"sentence": "find a trailer for moneyball", "tokens": ["find", "a", "trailer", "for", "moneyball"], "ner_tags": ["O", "O", "B-TRAILER", "O", "B-TITLE"]}
{"sentence": "what is the name of the biography that was directed by gary russell in the 1960 s it was a good movie it received seven stars", "tokens": ["what", "is", "the", "name", "of", "the", "biography", "that", "was", "directed", "by", "gary", "russell", "in", "the", "1960", "s", "it", "was", "a", "good", "movie", "it", "received", "seven", "stars"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-GENRE", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-YEAR", "O", "O", "O", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "what musical starring nathan lane got an average rating of four stars", "tokens": ["what", "musical", "starring", "nathan", "lane", "got", "an", "average", "rating", "of", "four", "stars"], "ner_tags": ["O", "B-GENRE", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "O"]}
{"sentence": "what is a really good cowboy film that is rated pg 13", "tokens": ["what", "is", "a", "really", "good", "cowboy", "film", "that", "is", "rated", "pg", "13"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "O", "O", "O", "O", "B-RATING", "I-RATING"]}
{"sentence": "did oliver stone make an animated film in the 1960 s", "tokens": ["did", "oliver", "stone", "make", "an", "animated", "film", "in", "the", "1960", "s"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "what type of movie is the dark knight", "tokens": ["what", "type", "of", "movie", "is", "the", "dark", "knight"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "show me a comedy directed by john waters", "tokens": ["show", "me", "a", "comedy", "directed", "by", "john", "waters"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "name the pg rated brock pierce film from 1940", "tokens": ["name", "the", "pg", "rated", "brock", "pierce", "film", "from", "1940"], "ner_tags": ["O", "O", "B-RATING", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-YEAR"]}
{"sentence": "list all the musicals released in 1987", "tokens": ["list", "all", "the", "musicals", "released", "in", "1987"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-YEAR"]}
{"sentence": "who was the supporting role in army of darkness", "tokens": ["who", "was", "the", "supporting", "role", "in", "army", "of", "darkness"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "what are some of the must see movies of 1973", "tokens": ["what", "are", "some", "of", "the", "must", "see", "movies", "of", "1973"], "ner_tags": ["O", "O", "O", "O", "O", "B-REVIEW", "I-REVIEW", "O", "O", "B-YEAR"]}
{"sentence": "what was the first movie leonardo dicaprio starred in", "tokens": ["what", "was", "the", "first", "movie", "leonardo", "dicaprio", "starred", "in"], "ner_tags": ["O", "O", "O", "B-YEAR", "O", "B-ACTOR", "I-ACTOR", "O", "O"]}
{"sentence": "show me war horror movie trailer", "tokens": ["show", "me", "war", "horror", "movie", "trailer"], "ner_tags": ["O", "O", "B-GENRE", "I-GENRE", "O", "B-TRAILER"]}
{"sentence": "find me a movie with the character named jack skellington", "tokens": ["find", "me", "a", "movie", "with", "the", "character", "named", "jack", "skellington"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-CHARACTER", "I-CHARACTER"]}
{"sentence": "who directed goodfellas", "tokens": ["who", "directed", "goodfellas"], "ner_tags": ["O", "O", "B-TITLE"]}
{"sentence": "list pg 13 action films with excellent ratings from this year", "tokens": ["list", "pg", "13", "action", "films", "with", "excellent", "ratings", "from", "this", "year"], "ner_tags": ["O", "B-RATING", "I-RATING", "B-GENRE", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "list a film with tia carrere that has been released within the last three decades", "tokens": ["list", "a", "film", "with", "tia", "carrere", "that", "has", "been", "released", "within", "the", "last", "three", "decades"], "ner_tags": ["O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "name an adventure movie thats rated pg starring mel gibson", "tokens": ["name", "an", "adventure", "movie", "thats", "rated", "pg", "starring", "mel", "gibson"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "O", "B-RATING", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "find the john boorman movie about king arthur and merlin", "tokens": ["find", "the", "john", "boorman", "movie", "about", "king", "arthur", "and", "merlin"], "ner_tags": ["O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "give me quotes from lord of the rings", "tokens": ["give", "me", "quotes", "from", "lord", "of", "the", "rings"], "ner_tags": ["O", "O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "what is a really popular 1940 s movie starring clint eastwood", "tokens": ["what", "is", "a", "really", "popular", "1940", "s", "movie", "starring", "clint", "eastwood"], "ner_tags": ["O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-YEAR", "I-YEAR", "O", "O", "B-ACTOR", "I-ACTOR"]}
{"sentence": "are there any war movies that were made this year", "tokens": ["are", "there", "any", "war", "movies", "that", "were", "made", "this", "year"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "did alan autry act in a film noir last year that got excellent ratings", "tokens": ["did", "alan", "autry", "act", "in", "a", "film", "noir", "last", "year", "that", "got", "excellent", "ratings"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "B-GENRE", "I-GENRE", "B-YEAR", "I-YEAR", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "what is the title of a must see mockumentary from 1970 directed by barbara kopple", "tokens": ["what", "is", "the", "title", "of", "a", "must", "see", "mockumentary", "from", "1970", "directed", "by", "barbara", "kopple"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "O", "B-YEAR", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "what is the best drama ever directed by ingmar bergman", "tokens": ["what", "is", "the", "best", "drama", "ever", "directed", "by", "ingmar", "bergman"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "do you know where i can find a pg hero movie directed by richard eyre with an average rating", "tokens": ["do", "you", "know", "where", "i", "can", "find", "a", "pg", "hero", "movie", "directed", "by", "richard", "eyre", "with", "an", "average", "rating"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-RATING", "B-PLOT", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "B-RATINGS_AVERAGE", "O"]}
{"sentence": "im looking for a mockumentary rated pg 13 starring rel hunt that was from the last eight years and has an average rating of nine", "tokens": ["im", "looking", "for", "a", "mockumentary", "rated", "pg", "13", "starring", "rel", "hunt", "that", "was", "from", "the", "last", "eight", "years", "and", "has", "an", "average", "rating", "of", "nine"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O", "B-RATING", "I-RATING", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE"]}
{"sentence": "what movie is the character abu from", "tokens": ["what", "movie", "is", "the", "character", "abu", "from"], "ner_tags": ["O", "O", "O", "O", "O", "B-CHARACTER", "O"]}
{"sentence": "what is the title of the nine star g rated tommy chong movie that came out the past decade", "tokens": ["what", "is", "the", "title", "of", "the", "nine", "star", "g", "rated", "tommy", "chong", "movie", "that", "came", "out", "the", "past", "decade"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE", "O", "B-RATING", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR"]}
{"sentence": "what movies have talking gennie pigs in them", "tokens": ["what", "movies", "have", "talking", "gennie", "pigs", "in", "them"], "ner_tags": ["O", "O", "O", "B-PLOT", "I-PLOT", "I-PLOT", "I-PLOT", "I-PLOT"]}
{"sentence": "who played in army of darkness", "tokens": ["who", "played", "in", "army", "of", "darkness"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "who stars in the movie the grinch", "tokens": ["who", "stars", "in", "the", "movie", "the", "grinch"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "i am looking for a scary movie rated nc 17 from the last eight years with actor devon sands that has an average rating of four", "tokens": ["i", "am", "looking", "for", "a", "scary", "movie", "rated", "nc", "17", "from", "the", "last", "eight", "years", "with", "actor", "devon", "sands", "that", "has", "an", "average", "rating", "of", "four"], "ner_tags": ["O", "O", "O", "O", "O", "B-GENRE", "O", "O", "B-RATING", "I-RATING", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "O", "B-RATINGS_AVERAGE"]}
{"sentence": "what actors are in the movie inception", "tokens": ["what", "actors", "are", "in", "the", "movie", "inception"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-TITLE"]}
{"sentence": "name a woman who has won a best director academy award", "tokens": ["name", "a", "woman", "who", "has", "won", "a", "best", "director", "academy", "award"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "I-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "is the movie hells kitchen based on the television series", "tokens": ["is", "the", "movie", "hells", "kitchen", "based", "on", "the", "television", "series"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE", "O", "O", "O", "O", "O"]}
{"sentence": "what fantasy movies have been made in the past nine years", "tokens": ["what", "fantasy", "movies", "have", "been", "made", "in", "the", "past", "nine", "years"], "ner_tags": ["O", "B-GENRE", "O", "O", "O", "O", "O", "O", "B-YEAR", "I-YEAR", "I-YEAR"]}
{"sentence": "list a past decade samurai action movie", "tokens": ["list", "a", "past", "decade", "samurai", "action", "movie"], "ner_tags": ["O", "O", "B-YEAR", "I-YEAR", "B-PLOT", "B-GENRE", "O"]}
{"sentence": "are there any star wars movies on now", "tokens": ["are", "there", "any", "star", "wars", "movies", "on", "now"], "ner_tags": ["O", "O", "O", "B-TITLE", "I-TITLE", "O", "O", "O"]}
{"sentence": "a comedy about police officers from the 2000s", "tokens": ["a", "comedy", "about", "police", "officers", "from", "the", "2000s"], "ner_tags": ["O", "B-GENRE", "O", "B-PLOT", "I-PLOT", "O", "O", "B-YEAR"]}
{"sentence": "list a gangster movie", "tokens": ["list", "a", "gangster", "movie"], "ner_tags": ["O", "O", "B-GENRE", "O"]}
{"sentence": "are there any dramas starring robin williams", "tokens": ["are", "there", "any", "dramas", "starring", "robin", "williams"], "ner_tags": ["B-PLOT", "I-PLOT", "I-PLOT", "B-GENRE", "B-ACTOR", "I-ACTOR", "I-ACTOR"]}
{"sentence": "show me g rated films about dogs rated must see", "tokens": ["show", "me", "g", "rated", "films", "about", "dogs", "rated", "must", "see"], "ner_tags": ["O", "O", "B-RATING", "I-RATING", "O", "O", "B-PLOT", "O", "B-REVIEW", "I-REVIEW"]}
{"sentence": "what 1960 science fiction film was directed by edgar g ulmer", "tokens": ["what", "1960", "science", "fiction", "film", "was", "directed", "by", "edgar", "g", "ulmer"], "ner_tags": ["O", "B-YEAR", "B-GENRE", "I-GENRE", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "show me the top comedies of the 1995", "tokens": ["show", "me", "the", "top", "comedies", "of", "the", "1995"], "ner_tags": ["O", "O", "O", "B-REVIEW", "I-REVIEW", "O", "O", "B-YEAR"]}
{"sentence": "is there a psychological drama rated pg 13 with a downtown plot", "tokens": ["is", "there", "a", "psychological", "drama", "rated", "pg", "13", "with", "a", "downtown", "plot"], "ner_tags": ["O", "O", "O", "B-GENRE", "I-GENRE", "O", "B-RATING", "I-RATING", "O", "O", "B-PLOT", "O"]}
{"sentence": "find a five star karate movie", "tokens": ["find", "a", "five", "star", "karate", "movie"], "ner_tags": ["O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "O"]}
{"sentence": "find me the movie with the song dont stop believin", "tokens": ["find", "me", "the", "movie", "with", "the", "song", "dont", "stop", "believin"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-SONG", "I-SONG", "I-SONG"]}
{"sentence": "did you think the pg 13 robbery film directed by sean macgregor was ok", "tokens": ["did", "you", "think", "the", "pg", "13", "robbery", "film", "directed", "by", "sean", "macgregor", "was", "ok"], "ner_tags": ["O", "O", "O", "O", "B-RATING", "I-RATING", "B-PLOT", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O", "B-RATINGS_AVERAGE"]}
{"sentence": "name a mediocre pg 13 documentary from the 1960 s directed by ryan polito", "tokens": ["name", "a", "mediocre", "pg", "13", "documentary", "from", "the", "1960", "s", "directed", "by", "ryan", "polito"], "ner_tags": ["O", "O", "B-RATINGS_AVERAGE", "B-RATING", "I-RATING", "B-GENRE", "O", "O", "B-YEAR", "I-YEAR", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "in what movie did jennifer aniston first appear", "tokens": ["in", "what", "movie", "did", "jennifer", "aniston", "first", "appear"], "ner_tags": ["O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O"]}
{"sentence": "show me a comedy about a basketball team", "tokens": ["show", "me", "a", "comedy", "about", "a", "basketball", "team"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "the grapes of wrath", "tokens": ["the", "grapes", "of", "wrath"], "ner_tags": ["B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "what fantasy movie in the 1970 s have eight stars and above", "tokens": ["what", "fantasy", "movie", "in", "the", "1970", "s", "have", "eight", "stars", "and", "above"], "ner_tags": ["O", "B-GENRE", "O", "O", "O", "B-YEAR", "I-YEAR", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "I-RATINGS_AVERAGE"]}
{"sentence": "some movies form the sports genre", "tokens": ["some", "movies", "form", "the", "sports", "genre"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O"]}
{"sentence": "find an animated disney film released in the 1990s", "tokens": ["find", "an", "animated", "disney", "film", "released", "in", "the", "1990s"], "ner_tags": ["O", "O", "B-GENRE", "O", "O", "O", "O", "O", "B-YEAR"]}
{"sentence": "name a critically acclaimed film with a heroes plot", "tokens": ["name", "a", "critically", "acclaimed", "film", "with", "a", "heroes", "plot"], "ner_tags": ["O", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "O", "O", "O", "B-PLOT", "O"]}
{"sentence": "did gerald butler do a musical in the 2000s", "tokens": ["did", "gerald", "butler", "do", "a", "musical", "in", "the", "2000s"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "B-YEAR"]}
{"sentence": "show me the title of the tom cruise film about alien invasion", "tokens": ["show", "me", "the", "title", "of", "the", "tom", "cruise", "film", "about", "alien", "invasion"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O", "O", "B-PLOT", "I-PLOT"]}
{"sentence": "was julia roberts ever in a movie directed by federico fellini", "tokens": ["was", "julia", "roberts", "ever", "in", "a", "movie", "directed", "by", "federico", "fellini"], "ner_tags": ["O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "who directed point break", "tokens": ["who", "directed", "point", "break"], "ner_tags": ["O", "O", "B-TITLE", "I-TITLE"]}
{"sentence": "show me movies with ewan macgregor singing", "tokens": ["show", "me", "movies", "with", "ewan", "macgregor", "singing"], "ner_tags": ["O", "O", "O", "O", "B-ACTOR", "I-ACTOR", "O"]}
{"sentence": "what is the plot of showboat", "tokens": ["what", "is", "the", "plot", "of", "showboat"], "ner_tags": ["O", "O", "O", "O", "O", "B-TITLE"]}
{"sentence": "are there any good mockumentary films coming out this year", "tokens": ["are", "there", "any", "good", "mockumentary", "films", "coming", "out", "this", "year"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O", "O", "O", "O", "O"]}
{"sentence": "has tim burton ever directed a mockumentary", "tokens": ["has", "tim", "burton", "ever", "directed", "a", "mockumentary"], "ner_tags": ["O", "B-DIRECTOR", "I-DIRECTOR", "O", "O", "O", "B-GENRE"]}
{"sentence": "is there a biography starring geoffrey rush with a rating of pg 13", "tokens": ["is", "there", "a", "biography", "starring", "geoffrey", "rush", "with", "a", "rating", "of", "pg", "13"], "ner_tags": ["O", "O", "O", "B-GENRE", "O", "B-ACTOR", "I-ACTOR", "O", "O", "O", "O", "B-RATING", "I-RATING"]}
{"sentence": "what 1950 biography was directed by daniel barnz", "tokens": ["what", "1950", "biography", "was", "directed", "by", "daniel", "barnz"], "ner_tags": ["O", "B-YEAR", "B-GENRE", "O", "O", "O", "B-DIRECTOR", "I-DIRECTOR"]}
{"sentence": "find movie with up and coming actor", "tokens": ["find", "movie", "with", "up", "and", "coming", "actor"], "ner_tags": ["O", "O", "O", "B-ACTOR", "I-ACTOR", "I-ACTOR", "O"]}
{"sentence": "what is the public sentiment about willy wonka and the chocolate factory", "tokens": ["what", "is", "the", "public", "sentiment", "about", "willy", "wonka", "and", "the", "chocolate", "factory"], "ner_tags": ["O", "O", "O", "O", "B-REVIEW", "O", "B-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE", "I-TITLE"]}
{"sentence": "what is the last biographical film that was made", "tokens": ["what", "is", "the", "last", "biographical", "film", "that", "was", "made"], "ner_tags": ["O", "O", "O", "O", "B-GENRE", "O", "O", "O", "O"]}
{"sentence": "show me foreign romance movies", "tokens": ["show", "me", "foreign", "romance", "movies"], "ner_tags": ["O", "O", "B-GENRE", "I-GENRE", "O"]}
{"sentence": "list a sci fi r rated movie", "tokens": ["list", "a", "sci", "fi", "r", "rated", "movie"], "ner_tags": ["O", "O", "B-GENRE", "I-GENRE", "B-RATING", "O", "O"]}
{"sentence": "are there any animated r rated movies", "tokens": ["are", "there", "any", "animated", "r", "rated", "movies"], "ner_tags": ["O", "O", "O", "B-GENRE", "B-RATING", "O", "O"]}
{"sentence": "in the last seven years what critically acclaimed war movies did alex steyermark direct", "tokens": ["in", "the", "last", "seven", "years", "what", "critically", "acclaimed", "war", "movies", "did", "alex", "steyermark", "direct"], "ner_tags": ["O", "O", "B-YEAR", "I-YEAR", "I-YEAR", "O", "B-RATINGS_AVERAGE", "I-RATINGS_AVERAGE", "B-GENRE", "O", "O", "B-DIRECTOR", "I-DIRECTOR", "O"]}
