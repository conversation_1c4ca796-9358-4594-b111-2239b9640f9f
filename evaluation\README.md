﻿# NER数据生成系统评估框架

## 概述

本评估框架旨在全面评估NER数据生成系统的性能，包括数据质量、迭代效果、系统有效性等多个维度。框架基于四个研究问题（RQ）设计：

- **RQ1**: 数据集统计与分析
- **RQ2**: 生成数据质量评估  
- **RQ3**: 自动化迭代流程有效性评估
- **RQ4**: 消融实验

## 目录结构

```
evaluation/
 framework/                          # 评估框架核心
    configs/                        # 配置文件
       rq1_config.json            # RQ1配置
       rq2_config.json            # RQ2配置
       rq3_config.json            # RQ3配置
       rq4_config.json            # RQ4配置
    metrics/                        # 评估指标模块
       __init__.py
       statistical_metrics.py     # 统计指标
       quality_metrics.py         # 质量指标
       convergence_metrics.py     # 收敛性指标
       ablation_metrics.py        # 消融实验指标
    utils/                          # 评估工具
       __init__.py
       evaluation_collector.py    # 评估数据收集器
    rq1_dataset_statistics.py      # RQ1评估脚本
    rq2_quality_assessment.py      # RQ2评估脚本（待实现）
    rq3_iteration_analysis.py      # RQ3评估脚本
    rq4_ablation_study.py          # RQ4评估脚本（待实现）
 experiments/                        # 实验设计
    baseline_comparison/            # 基线对比实验
    ablation_studies/               # 消融实验
    parameter_sensitivity/          # 参数敏感性分析
    cross_validation/               # 交叉验证
 results/                            # 评估结果
    rq1_statistics/                 # RQ1结果
    rq2_quality/                    # RQ2结果
    rq3_iteration/                  # RQ3结果
    rq4_ablation/                   # RQ4结果
    comprehensive_report/           # 综合报告
 benchmarks/                         # 基准数据
    original_datasets/              # 原始数据集
    baseline_results/               # 基线结果
    reference_metrics/              # 参考指标
 run_comprehensive_evaluation.py    # 综合评估脚本
```

## 使用方法

### 1. 单独运行RQ评估

#### RQ1: 数据集统计与分析
```bash
python evaluation/framework/rq1_dataset_statistics.py \
    --original path/to/original_dataset.json \
    --generated path/to/generated_dataset.json \
    --output evaluation/results/rq1_statistics/
```

#### RQ3: 迭代流程有效性评估
```bash
python evaluation/framework/rq3_iteration_analysis.py \
    --run-dir synth_dataset/runs/20250705_143000 \
    --output evaluation/results/rq3_iteration/
```

### 2. 运行综合评估

```bash
python evaluation/run_comprehensive_evaluation.py \
    --run-dir synth_dataset/runs/20250705_143000 \
    --original-dataset path/to/original_dataset.json \
    --output evaluation/results/comprehensive/
```

### 3. 选择性评估

```bash
# 跳过某些RQ评估
python evaluation/run_comprehensive_evaluation.py \
    --run-dir synth_dataset/runs/20250705_143000 \
    --original-dataset path/to/original_dataset.json \
    --output evaluation/results/comprehensive/ \
    --skip-rq2 --skip-rq4
```

## 评估指标

### RQ1: 数据集统计与分析

- **数据集规模对比**: 记录数量、实体数量、实体类型数量
- **实体分布分析**: 各实体类型的分布差异
- **句子长度分析**: 句子长度分布对比
- **词汇丰富度**: 词汇多样性指标
- **统计显著性检验**: t检验、Mann-Whitney U检验、KS检验、卡方检验

### RQ2: 生成数据质量评估

- **自然度评估**: 基于语言模型的自然度评分
- **标注准确性**: 实体边界和类型准确性
- **语义一致性**: 上下文语义一致性
- **多样性指标**: 词汇、句法、语义、上下文多样性
- **平衡性指标**: 分布均衡性、覆盖率

### RQ3: 迭代流程有效性评估

- **收敛性分析**: 各指标收敛情况
- **效率分析**: 时间成本、API调用次数
- **目标达成分析**: 实体分布目标、质量阈值达成情况
- **迭代趋势**: 改进率、最优停止点检测

### RQ4: 消融实验

- **组件贡献度**: 各组件对整体性能的贡献
- **交互效应**: 组件间的交互作用
- **基线对比**: 与传统方法的对比

## 配置说明

### RQ1配置 (rq1_config.json)

```json
{
  "rq1_dataset_statistics": {
    "metrics": {
      "dataset_size": {"enabled": true, "compare_with_original": true},
      "entity_distribution": {"enabled": true, "statistical_tests": ["chi_square", "ks_test"]},
      "sentence_length": {"enabled": true, "statistical_tests": ["t_test", "mann_whitney"]},
      "vocabulary_statistics": {"enabled": true},
      "entity_density": {"enabled": true}
    },
    "visualization": {
      "distribution_plots": true,
      "comparison_tables": true,
      "statistical_test_results": true
    }
  }
}
```

### RQ3配置 (rq3_config.json)

```json
{
  "rq3_iteration_analysis": {
    "metrics": {
      "convergence_analysis": {"enabled": true, "convergence_threshold": 0.01},
      "efficiency_analysis": {"enabled": true},
      "target_achievement": {"enabled": true},
      "iteration_trends": {"enabled": true}
    },
    "analysis_methods": {
      "statistical_trend_analysis": true,
      "change_point_detection": true,
      "efficiency_benchmarking": true
    }
  }
}
```

## 输出说明

### 评估报告

每个RQ评估都会生成以下文件：

- `rqX_evaluation_report.json`: 详细的评估报告（JSON格式）
- `rqX_summary.txt`: 文本摘要报告
- 可视化图表（PNG格式）

### 综合报告

综合评估会生成：

- `comprehensive_evaluation_report.json`: 包含所有RQ结果的综合报告
- `executive_summary.txt`: 执行摘要，包含关键发现和建议

## 扩展指南

### 添加新的评估指标

1. 在 `evaluation/framework/metrics/` 目录下创建新的指标模块
2. 实现指标计算函数
3. 在相应的RQ评估脚本中调用新指标
4. 更新配置文件以支持新指标的开关

### 添加新的RQ评估

1. 创建新的RQ评估脚本 `evaluation/framework/rqX_evaluation.py`
2. 创建对应的配置文件 `evaluation/framework/configs/rqX_config.json`
3. 在综合评估脚本中添加对新RQ的调用
4. 更新文档说明

### 自定义可视化

可视化代码位于各RQ评估脚本中，可以根据需要：

- 修改图表样式和颜色
- 添加新的图表类型
- 调整图表布局和大小

## 依赖要求

```bash
# 基础依赖
pip install numpy pandas matplotlib seaborn scipy

# 中文处理
pip install jieba

# 统计分析
pip install scipy statsmodels

# 可视化
pip install plotly
```

## 注意事项

1. **数据路径**: 确保所有数据文件路径正确，支持相对路径和绝对路径
2. **编码格式**: 所有文件使用UTF-8编码
3. **内存使用**: 大数据集可能需要较多内存，建议在评估前检查系统资源
4. **运行时间**: 完整评估可能需要较长时间，可以使用选择性评估来缩短时间

## 故障排除

### 常见问题

1. **找不到数据文件**: 检查文件路径是否正确
2. **编码错误**: 确保文件使用UTF-8编码
3. **内存不足**: 尝试使用更小的数据集或增加系统内存
4. **依赖缺失**: 安装所有必需的Python包

### 调试模式

可以在脚本中添加调试输出：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 更新日志

- **v1.0.0**: 初始版本，实现RQ1和RQ3评估
- **v1.1.0**: 计划添加RQ2和RQ4评估
- **v1.2.0**: 计划添加更多可视化选项和交互式报告
