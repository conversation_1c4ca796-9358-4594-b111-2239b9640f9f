import json
import os
from collections import Counter
from datetime import datetime
from pathlib import Path

def calculate_target_distribution_with_tolerance(current_counts, target_config, tolerance=0.1):
    """计算考虑容差的目标分布"""
    to_augment = {}
    to_trim = {}
    
    for ent_type, target_count in target_config.items():
        current_count = current_counts.get(ent_type, 0)
        tolerance_range = target_count * tolerance  # 10%容差
        
        if current_count < target_count - tolerance_range:
            # 需要补充
            to_augment[ent_type] = target_count - current_count
        elif current_count > target_count + tolerance_range:
            # 需要修剪
            to_trim[ent_type] = current_count - target_count
    
    return to_augment, to_trim

def main(output_dir="reproduce", dataset_path='format-dataset/privacy_bench_small.json'):
    """主函数：生成目标分布文件"""
    try:
        # 文件路径
        DATASET_PATH = dataset_path
        
        # 优先使用运行目录中的配置文件
        output_dir = Path(output_dir)
        run_config_path = output_dir.parent / "config" / "balance_config.json"
        if run_config_path.exists():
            BALANCE_CONFIG_PATH = str(run_config_path)
            print(f"[信息] 使用运行目录配置：{BALANCE_CONFIG_PATH}")
        else:
            BALANCE_CONFIG_PATH = 'src/gen_strat/balance_config.json'
            print(f"[信息] 使用全局配置：{BALANCE_CONFIG_PATH}")
            
        DIVERSITY_CONFIG_PATH = 'src/gen_strat/diversity_config.json'

        # 输出路径（使用传入的output_dir）
        output_dir = Path(output_dir)
        target_dir = output_dir / "entity_target"
        OUTPUT_PATH = target_dir / "privacy_bench_target.json"
        TRIM_PATH = target_dir / "privacy_bench_trim.json"

        print(f"[信息] 输出目录：{output_dir}")
        print(f"[信息] 目标目录：{target_dir}")
        print(f"[信息] 目标文件：{OUTPUT_PATH}")

        # 创建输出目录（如果不存在）
        target_dir.mkdir(parents=True, exist_ok=True)
        
        if not target_dir.exists():
            raise RuntimeError(f"无法创建目标目录：{target_dir}")

        # 读取数据集并统计实体类型分布
        type_counter = Counter()

        with open(DATASET_PATH, 'r', encoding='utf-8') as f:
            dataset = json.load(f)
            for sample in dataset:
                for ent in sample.get("label", []):
                    ent_type = ent.get("type")
                    if ent_type:
                        type_counter[ent_type] += 1

        # 读取平衡目标配置
        with open(BALANCE_CONFIG_PATH, 'r', encoding='utf-8') as f:
            balance_config = json.load(f)

        target_config = balance_config.get("entity_type_targets", {})
        default_target = balance_config.get("balance_target_per_type", 0)
        tolerance = balance_config.get("distribution_tolerance", 0.1)  # 获取容差配置

        # 计算需要补充和需要修剪的实体类型
        to_augment, to_trim = calculate_target_distribution_with_tolerance(
            type_counter, target_config, tolerance
        )

        # 保存需要补充的结果
        with open(OUTPUT_PATH, 'w', encoding='utf-8') as f:
            json.dump(to_augment, f, indent=2, ensure_ascii=False)
            
        if not OUTPUT_PATH.exists():
            raise RuntimeError(f"文件写入失败：{OUTPUT_PATH}")

        # 保存需要修剪的结果
        with open(TRIM_PATH, 'w', encoding='utf-8') as f:
            json.dump(to_trim, f, indent=2, ensure_ascii=False)
            
        if not TRIM_PATH.exists():
            raise RuntimeError(f"文件写入失败：{TRIM_PATH}")

        print(f"[✓] 已生成目标分布文件：{OUTPUT_PATH}")
        print(f"[✓] 已生成修剪分布文件：{TRIM_PATH}")
    
        # 输出详细统计信息
        print(f"\n=== 分布分析 ===")
        print(f"容差设置：{tolerance * 100}%")
        print(f"原始数据集实体分布：")
        for ent_type, count in sorted(type_counter.items()):
            target = target_config.get(ent_type, 0)
            tolerance_range = target * tolerance
            status = ""
            if count < target - tolerance_range:
                status = f" (需要补充 {to_augment.get(ent_type, 0)} 个)"
            elif count > target + tolerance_range:
                status = f" (需要修剪 {to_trim.get(ent_type, 0)} 个)"
            else:
                status = " (在容差范围内)"
            print(f"  {ent_type}: {count} / {target}{status}")
    
        print(f"\n需要补充的实体类型：{len(to_augment)} 种")
        print(f"需要修剪的实体类型：{len(to_trim)} 种")

        # 根据balance_target_per_type更新diversity_config.json的num_entity_variants
        def update_diversity_config():
            """根据balance_target_per_type更新diversity_config.json的num_entity_variants"""
            try:
                # 读取当前的diversity配置
                with open(DIVERSITY_CONFIG_PATH, 'r', encoding='utf-8') as f:
                    diversity_config = json.load(f)
                
                # 获取balance_target_per_type值
                balance_target_per_type = balance_config.get("balance_target_per_type", 0)
                
                if balance_target_per_type <= 0:
                    print(f"[警告] balance_target_per_type值无效：{balance_target_per_type}")
                    return
                
                # 计算合适的num_entity_variants值
                # 策略1：设置为balance_target_per_type的70%（推荐）
                ratio_based = int(balance_target_per_type * 0.7)
                
                # 策略2：确保至少有5个变体，最多不超过balance_target_per_type
                min_variants = 5
                max_variants = balance_target_per_type
                
                # 选择最终值
                suggested_num_entity_variants = max(min_variants, min(max_variants, ratio_based))
                
                # 获取当前值
                old_value = diversity_config.get("num_entity_variants", 10)
                
                # 检查是否需要更新
                if old_value == suggested_num_entity_variants:
                    print(f"[信息] num_entity_variants已经是推荐值：{old_value}")
                    print(f"    balance_target_per_type: {balance_target_per_type}")
                    return
                
                # 更新配置
                diversity_config["num_entity_variants"] = suggested_num_entity_variants
                
                # 保存更新后的配置
                with open(DIVERSITY_CONFIG_PATH, 'w', encoding='utf-8') as f:
                    json.dump(diversity_config, f, indent=2, ensure_ascii=False)
                
                print(f"[✓] 已更新diversity_config.json")
                print(f"    balance_target_per_type: {balance_target_per_type}")
                print(f"    num_entity_variants: {old_value} -> {suggested_num_entity_variants}")
                
                # 输出详细说明
                print(f"    [计算逻辑]")
                print(f"      - 基础比例：{balance_target_per_type} × 70% = {ratio_based}")
                print(f"      - 最小值限制：{min_variants}")
                print(f"      - 最大值限制：{max_variants}")
                print(f"      - 最终值：{suggested_num_entity_variants}")
                
                # 输出建议说明
                if suggested_num_entity_variants < balance_target_per_type:
                    print(f"    [建议] 当前设置为{balance_target_per_type}的{int(suggested_num_entity_variants/balance_target_per_type*100)}%，确保实体多样化")
                else:
                    print(f"    [建议] 当前设置为最大值{balance_target_per_type}，最大化实体多样化")
                    
                # 验证更新结果
                with open(DIVERSITY_CONFIG_PATH, 'r', encoding='utf-8') as f:
                    updated_config = json.load(f)
                    actual_value = updated_config.get("num_entity_variants")
                    if actual_value == suggested_num_entity_variants:
                        print(f"    [验证] 配置更新成功")
                    else:
                        print(f"    [警告] 配置更新验证失败：期望{suggested_num_entity_variants}，实际{actual_value}")
                    
            except FileNotFoundError:
                print(f"[错误] 找不到配置文件：{DIVERSITY_CONFIG_PATH}")
            except json.JSONDecodeError as e:
                print(f"[错误] 配置文件格式错误：{e}")
            except Exception as e:
                print(f"[错误] 更新diversity_config.json失败：{e}")
                import traceback
                traceback.print_exc()

        # 执行更新
        update_diversity_config()

    # 配置验证和总结
    # def validate_configs():
    #     """验证配置的合理性"""
    #     print(f"\n=== 配置验证 ===")
        
    #     try:
    #         # 验证balance_config
    #         with open(BALANCE_CONFIG_PATH, 'r', encoding='utf-8') as f:
    #             balance_config = json.load(f)
            
    #         balance_target = balance_config.get("balance_target_per_type", 0)
    #         entity_targets = balance_config.get("entity_type_targets", {})
            
    #         print(f"balance_config.json:")
    #         print(f"  - balance_target_per_type: {balance_target}")
    #         print(f"  - 实体类型数量: {len(entity_targets)}")
            
    #         # 验证diversity_config
    #         with open(DIVERSITY_CONFIG_PATH, 'r', encoding='utf-8') as f:
    #             diversity_config = json.load(f)
            
    #         num_entity_variants = diversity_config.get("num_entity_variants", 0)
    #         num_sentence_variants = diversity_config.get("num_sentence_variants", 0)
            
    #         print(f"diversity_config.json:")
    #         print(f"  - num_entity_variants: {num_entity_variants}")
    #         print(f"  - num_sentence_variants: {num_sentence_variants}")
            
    #         # 验证配置合理性
    #         print(f"\n配置合理性检查:")
            
    #         if num_entity_variants > balance_target:
    #             print(f"  ⚠️  num_entity_variants({num_entity_variants}) > balance_target_per_type({balance_target})")
    #             print(f"     建议：num_entity_variants不应超过balance_target_per_type")
    #         elif num_entity_variants < 5:
    #             print(f"  ⚠️  num_entity_variants({num_entity_variants}) < 5")
    #             print(f"     建议：num_entity_variants至少应为5")
    #         else:
    #             ratio = num_entity_variants / balance_target * 100
    #             print(f"  ✓ num_entity_variants({num_entity_variants}) / balance_target_per_type({balance_target}) = {ratio:.1f}%")
    #             if 50 <= ratio <= 80:
    #                 print(f"     配置合理，实体多样化程度适中")
    #             elif ratio < 50:
    #                 print(f"     实体多样化程度较低，可考虑增加num_entity_variants")
    #             else:
    #                 print(f"     实体多样化程度较高，确保有足够的实体变体")
            
    #         if num_sentence_variants < 3:
    #             print(f"  ⚠️  num_sentence_variants({num_sentence_variants}) < 3")
    #             print(f"     建议：num_sentence_variants至少应为3")
    #         else:
    #             print(f"  ✓ num_sentence_variants({num_sentence_variants}) 配置合理")
                
    #     except Exception as e:
    #         print(f"[错误] 配置验证失败：{e}")

    # # 执行配置验证
    # validate_configs()

        print(f"\n=== 执行完成 ===")
        print(f"1. 已生成目标分布文件：{OUTPUT_PATH}")
        print(f"2. 已生成修剪分布文件：{TRIM_PATH}")
        print(f"3. 已更新diversity_config.json的num_entity_variants")
        print(f"4. 已验证配置合理性")
        print(f"\n下一步：运行 2-gen_diversity.py 生成多样化策略")
        
        return str(OUTPUT_PATH)
        
    except Exception as e:
        print(f"[错误] 生成目标分布文件失败：{e}")
        raise

if __name__ == "__main__":
    main()