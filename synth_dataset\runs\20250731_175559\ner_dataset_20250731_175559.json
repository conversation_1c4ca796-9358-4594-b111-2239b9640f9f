{"年龄": [{"text": "这位3岁的孩子正在学习认识数字。", "label": [{"entity": "3岁", "start_idx": 2, "end_idx": 4, "type": "年龄"}]}, {"text": "王医生已经工作了28年，经验非常丰富。", "label": [{"entity": "28年", "start_idx": 5, "end_idx": 7, "type": "年龄"}]}, {"text": "小明今年刚满16岁，可以考驾照了。", "label": [{"entity": "16岁", "start_idx": 7, "end_idx": 9, "type": "年龄"}]}, {"text": "那位85岁的老人每天坚持晨练。", "label": [{"entity": "85岁", "start_idx": 3, "end_idx": 5, "type": "年龄"}]}, {"text": "她家的宠物狗已经8岁了，相当于人类的56岁。", "label": [{"entity": "8岁", "start_idx": 7, "end_idx": 9, "type": "年龄"}, {"entity": "56岁", "start_idx": 18, "end_idx": 20, "type": "年龄"}]}, {"text": "弟弟今年4岁，特别喜欢看动画片。", "label": [{"entity": "4岁", "start_idx": 5, "end_idx": 7, "type": "年龄"}]}, {"text": "公司新来的实习生今年22岁，是应届毕业生。", "label": [{"entity": "22岁", "start_idx": 11, "end_idx": 13, "type": "年龄"}]}, {"text": "我祖母今年72岁，身体依然很硬朗。", "label": [{"entity": "72岁", "start_idx": 6, "end_idx": 8, "type": "年龄"}]}], "姓名": [{"text": "张伟明天要去北京参加一个重要的会议。", "label": [{"entity": "张伟", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "李娜的钢琴演奏在音乐会上赢得了满堂彩。", "label": [{"entity": "李娜", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "王芳是新来的数学老师，深受学生们的喜爱。", "label": [{"entity": "王芳", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "刘强在篮球比赛中投中了制胜的一球。", "label": [{"entity": "刘强", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "陈晓的绘画作品在艺术展上获得了评委的一致好评。", "label": [{"entity": "陈晓", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "赵敏今天早上在公园里晨练时遇到了老朋友。", "label": [{"entity": "赵敏", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "老朋友", "start_idx": 11, "end_idx": 13, "type": "姓名"}]}, {"text": "杨帆的公司最近签订了一份大合同。", "label": [{"entity": "杨帆", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "周杰在去年的马拉松比赛中获得了冠军。", "label": [{"entity": "周杰", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "孙红的新书已经在各大书店上架销售了。", "label": [{"entity": "孙红", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}], "国籍": [{"text": "来自日本的游客在京都参观古老的寺庙。", "label": [{"entity": "日本", "start_idx": 3, "end_idx": 5, "type": "国籍"}]}, {"text": "法国的厨师在巴黎的餐厅里烹饪精致的料理。", "label": [{"entity": "法国", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "澳大利亚的运动员在奥运会上赢得了金牌。", "label": [{"entity": "澳大利亚", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "巴西的足球运动员在世界杯上表现出色。", "label": [{"entity": "巴西", "start_idx": 0, "end_idx": 1, "type": "国籍"}]}, {"text": "美国的科学家在实验室里进行前沿研究。", "label": [{"entity": "美国", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "英国的教授在剑桥大学讲授历史课程。", "label": [{"entity": "英国", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}], "性别": [{"text": "张女士正在为明天的演讲准备PPT。", "label": [{"entity": "张", "start_idx": 0, "end_idx": 1, "type": "性别"}, {"entity": "女士", "start_idx": 1, "end_idx": 2, "type": "性别"}]}, {"text": "刘小姐的生日派对将在下周五举行。", "label": [{"entity": "小姐", "start_idx": 1, "end_idx": 2, "type": "性别"}]}, {"text": "张伟是一位男性工程师，他在公司负责软件开发项目。", "label": [{"entity": "男性", "start_idx": 4, "end_idx": 6, "type": "性别"}]}, {"text": "李娜是位女性医生，她每天在医院为病人诊断病情。", "label": [{"entity": "女性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "王强这个男性运动员在比赛中打破了全国纪录。", "label": [{"entity": "男性", "start_idx": 4, "end_idx": 6, "type": "性别"}]}, {"text": "刘芳作为女性教师，她的课堂总是充满活力。", "label": [{"entity": "女性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "陈明这位男性厨师擅长制作各种川菜美食。", "label": [{"entity": "男性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "赵雪是位女性设计师，她的作品多次获得国际奖项。", "label": [{"entity": "女性", "start_idx": 4, "end_idx": 6, "type": "性别"}]}], "职业": [{"text": "医生李华正在为病人进行详细的身体检查。", "label": [{"entity": "医生", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "工程师张伟负责设计了这座桥梁的结构方案。", "label": [{"entity": "工程师", "start_idx": 0, "end_idx": 3, "type": "职业"}]}], "民族": [{"text": "藏族同胞在布达拉宫前展示了精美的唐卡艺术。", "label": [{"entity": "藏族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "维吾尔族姑娘穿着鲜艳的民族服饰参加了节日庆典。", "label": [{"entity": "维吾尔族", "start_idx": 0, "end_idx": 3, "type": "民族"}]}, {"text": "壮族人民在三月三歌圩上唱起了欢快的山歌。", "label": [{"entity": "壮族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "蒙古族牧民在广阔的草原上放牧着成群的牛羊。", "label": [{"entity": "蒙古族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "朝鲜族舞蹈演员的表演赢得了观众的热烈掌声。", "label": [{"entity": "朝鲜族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "彝族村民在火把节上点燃了象征希望的火把。", "label": [{"entity": "彝族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "苗族银饰工艺精湛，吸引了众多游客驻足欣赏。", "label": [{"entity": "苗族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "满族传统节日“颁金节”是庆祝族群诞生的日子。", "label": [{"entity": "满族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "傣族泼水节的欢快氛围感染了每一个参与的人。", "label": [{"entity": "傣族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}], "教育背景": [{"text": "他拥有清华大学计算机科学与技术专业的博士学位。", "label": [{"entity": "清华大学计算机科学与技术专业", "start_idx": 6, "end_idx": 18, "type": "教育背景"}, {"entity": "博士学位", "start_idx": 18, "end_idx": 22, "type": "教育背景"}]}, {"text": "她毕业于北京大学光华管理学院，获得了工商管理硕士学位。", "label": [{"entity": "北京大学光华管理学院", "start_idx": 5, "end_idx": 12, "type": "教育背景"}, {"entity": "工商管理硕士学位", "start_idx": 16, "end_idx": 23, "type": "教育背景"}]}, {"text": "这家公司要求应聘者至少具备上海交通大学电子工程专业的本科学历。", "label": [{"entity": "上海交通大学", "start_idx": 17, "end_idx": 21, "type": "教育背景"}, {"entity": "电子工程专业", "start_idx": 22, "end_idx": 27, "type": "教育背景"}, {"entity": "本科学历", "start_idx": 28, "end_idx": 31, "type": "教育背景"}]}, {"text": "他的教育背景是复旦大学历史学系，获得了学士学位。", "label": [{"entity": "复旦大学历史学系", "start_idx": 9, "end_idx": 17, "type": "教育背景"}, {"entity": "学士学位", "start_idx": 19, "end_idx": 22, "type": "教育背景"}]}, {"text": "这位研究员拥有中国科学院自动化研究所的博士学位。", "label": [{"entity": "中国科学院自动化研究所", "start_idx": 11, "end_idx": 23, "type": "教育背景"}]}, {"text": "招聘广告明确指出，应聘者需持有浙江大学医学院的临床医学硕士学位。", "label": [{"entity": "浙江大学医学院", "start_idx": 27, "end_idx": 35, "type": "教育背景"}, {"entity": "临床医学硕士学位", "start_idx": 36, "end_idx": 47, "type": "教育背景"}]}, {"text": "该职位优先考虑拥有哈佛大学商学院MBA学位的候选人。", "label": [{"entity": "哈佛大学商学院MBA学位", "start_idx": 10, "end_idx": 22, "type": "教育背景"}]}], "婚姻状况": [{"text": "王先生的婚姻状况是已婚，他和妻子已经共同生活了十年。", "label": [{"entity": "已婚", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "李女士的婚姻状况为离异，她正在寻找新的生活伴侣。", "label": [{"entity": "离异", "start_idx": 7, "end_idx": 9, "type": "婚姻状况"}]}, {"text": "张教授的婚姻状况是未婚，他专注于学术研究。", "label": [{"entity": "未婚", "start_idx": 7, "end_idx": 9, "type": "婚姻状况"}]}, {"text": "陈小姐的婚姻状况为丧偶，她独自抚养两个孩子。", "label": [{"entity": "丧偶", "start_idx": 7, "end_idx": 9, "type": "婚姻状况"}]}, {"text": "刘先生的婚姻状况是分居，他和妻子暂时分开居住。", "label": [{"entity": "分居", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "赵女士的婚姻状况为单身，她享受自由的生活。", "label": [{"entity": "单身", "start_idx": 7, "end_idx": 9, "type": "婚姻状况"}]}, {"text": "孙先生的婚姻状况是已婚，他和妻子育有两个孩子。", "label": [{"entity": "已婚", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "钱女士的婚姻状况为离婚，她决定重新开始。", "label": [{"entity": "离婚", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "周先生的婚姻状况是同居，他和女友共同生活。", "label": [{"entity": "同居", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "吴女士的婚姻状况为已婚，她是一位全职母亲。", "label": [{"entity": "已婚", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}], "家庭成员": [{"text": "妈妈今天给我做了红烧肉，味道特别好吃。", "label": [{"entity": "妈妈", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "爸爸的生日快到了，我准备送他一本他喜欢的书。", "label": [{"entity": "爸爸", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "弟弟正在房间里写作业，看起来有点不耐烦。", "label": [{"entity": "弟弟", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "姐姐上周去上海出差了，今天才回来。", "label": [{"entity": "姐姐", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "爷爷喜欢在公园里散步，每天早上都去。", "label": [{"entity": "爷爷", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "奶奶做的饺子是我最爱吃的，每次都吃很多。", "label": [{"entity": "奶奶", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "哥哥今天去参加婚礼了，晚上可能会晚点回来。", "label": [{"entity": "哥哥", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "妹妹今天考试考得不错，老师还表扬了她。", "label": [{"entity": "妹妹", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "叔叔最近搬了新家，周末我们准备去他家吃饭。", "label": [{"entity": "叔叔", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "阿姨今天来家里做客，还带来了她做的拿手菜。", "label": [{"entity": "阿姨", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}], "政治倾向": [{"text": "这位民主党议员在国会会议上发表了关于气候变化的重要演讲。", "label": [{"entity": "民主党", "start_idx": 3, "end_idx": 5, "type": "政治倾向"}]}, {"text": "李先生长期支持中国共产党，积极参与社区党建工作。", "label": [{"entity": "中国共产党", "start_idx": 8, "end_idx": 11, "type": "政治倾向"}, {"entity": "社区党建工作", "start_idx": 19, "end_idx": 24, "type": "政治倾向"}]}, {"text": "作为英国保守党的一员，他坚决反对提高最低工资标准。", "label": [{"entity": "英国保守党", "start_idx": 4, "end_idx": 9, "type": "政治倾向"}]}, {"text": "该社会民主党政府在上任后迅速推动了医疗改革法案。", "label": [{"entity": "社会民主党", "start_idx": 4, "end_idx": 8, "type": "政治倾向"}]}, {"text": "特朗普作为共和党总统，在任期间签署了多项减税政策。", "label": [{"entity": "共和党", "start_idx": 7, "end_idx": 9, "type": "政治倾向"}]}, {"text": "她加入了绿党，致力于推广环保政策与可持续发展理念。", "label": [{"entity": "绿党", "start_idx": 5, "end_idx": 7, "type": "政治倾向"}]}, {"text": "这位自由民主党成员在选举中倡导更严格的枪支管控。", "label": [{"entity": "自由民主党", "start_idx": 6, "end_idx": 11, "type": "政治倾向"}]}, {"text": "该工党领袖承诺若当选将大幅增加社会福利开支。", "label": [{"entity": "工党", "start_idx": 3, "end_idx": 5, "type": "政治倾向"}]}, {"text": "作为法国国民联盟的候选人，他在辩论中强调民族主义立场。", "label": [{"entity": "法国国民联盟", "start_idx": 6, "end_idx": 13, "type": "政治倾向"}, {"entity": "民族主义立场", "start_idx": 28, "end_idx": 36, "type": "政治倾向"}]}, {"text": "该社会党政府通过立法保障了劳工的八小时工作制权利。", "label": [{"entity": "社会党", "start_idx": 2, "end_idx": 5, "type": "政治倾向"}]}], "工资数额": [{"text": "他的月薪是8000元，在一线城市算中等水平。", "label": [{"entity": "8000元", "start_idx": 7, "end_idx": 10, "type": "工资数额"}]}, {"text": "她最近换了工作，现在年薪达到25万元。", "label": [{"entity": "25万元", "start_idx": 12, "end_idx": 15, "type": "工资数额"}]}, {"text": "这份兼职的时薪是50元，比其他工作高不少。", "label": [{"entity": "50元", "start_idx": 11, "end_idx": 13, "type": "工资数额"}]}, {"text": "公司给新员工的起薪定为6000元每月。", "label": [{"entity": "6000元每月", "start_idx": 12, "end_idx": 16, "type": "工资数额"}]}, {"text": "他的年终奖金拿到了3万元，比预期高。", "label": [{"entity": "3万元", "start_idx": 10, "end_idx": 12, "type": "工资数额"}]}, {"text": "这份合同工的月薪是9000元，还有绩效。", "label": [{"entity": "9000元", "start_idx": 10, "end_idx": 13, "type": "工资数额"}]}, {"text": "她的日薪是300元，做的是短期项目。", "label": [{"entity": "300元", "start_idx": 7, "end_idx": 10, "type": "工资数额"}]}, {"text": "他的基本工资是5000元，加上提成更高。", "label": [{"entity": "5000元", "start_idx": 7, "end_idx": 10, "type": "工资数额"}]}, {"text": "她拿到实习工资3000元，对大学生来说不错。", "label": [{"entity": "3000元", "start_idx": 7, "end_idx": 10, "type": "工资数额"}]}, {"text": "这份工作的月薪是12000元，含五险一金。", "label": [{"entity": "12000元", "start_idx": 8, "end_idx": 11, "type": "工资数额"}]}], "投资产品": [{"text": "购买招商银行信用卡积分兑换的招商银行联名信用卡礼券是一种不错的投资。", "label": [{"entity": "招商银行信用卡积分兑换", "start_idx": 3, "end_idx": 10, "type": "投资产品"}, {"entity": "招商银行联名信用卡礼券", "start_idx": 11, "end_idx": 22, "type": "投资产品"}]}, {"text": "我决定将部分资金投入华夏基金旗下的华夏成长混合型证券投资基金。", "label": [{"entity": "华夏基金", "start_idx": 12, "end_idx": 15, "type": "投资产品"}, {"entity": "华夏成长混合型证券投资基金", "start_idx": 16, "end_idx": 38, "type": "投资产品"}]}, {"text": "他的投资组合中包含了国债逆回购和上海证券交易所的国债ETF。", "label": [{"entity": "国债逆回购", "start_idx": 8, "end_idx": 11, "type": "投资产品"}, {"entity": "国债ETF", "start_idx": 18, "end_idx": 21, "type": "投资产品"}]}, {"text": "为了分散风险，她选择了购买中国平安的平安福保险理财产品。", "label": [{"entity": "中国平安", "start_idx": 19, "end_idx": 21, "type": "投资产品"}, {"entity": "平安福保险理财产品", "start_idx": 22, "end_idx": 30, "type": "投资产品"}]}, {"text": "投资者可以通过支付宝平台购买易方达中小盘混合型开放式证券投资基金。", "label": [{"entity": "支付宝平台", "start_idx": 6, "end_idx": 11, "type": "投资产品"}, {"entity": "易方达中小盘混合型开放式证券投资基金", "start_idx": 17, "end_idx": 46, "type": "投资产品"}]}, {"text": "最近，我购买了一份招商银行的朝招金系列现金管理类理财产品。", "label": [{"entity": "招商银行", "start_idx": 7, "end_idx": 10, "type": "投资产品"}, {"entity": "朝招金系列现金管理类理财产品", "start_idx": 11, "end_idx": 23, "type": "投资产品"}]}, {"text": "他通过银行渠道购买了建设银行的建信现金增利货币市场基金。", "label": [{"entity": "建设银行", "start_idx": 10, "end_idx": 12, "type": "投资产品"}, {"entity": "建信现金增利货币市场基金", "start_idx": 13, "end_idx": 20, "type": "投资产品"}]}, {"text": "我考虑将闲置资金用于购买国债，特别是记账式国债品种。", "label": [{"entity": "国债", "start_idx": 11, "end_idx": 13, "type": "投资产品"}, {"entity": "记账式国债", "start_idx": 16, "end_idx": 20, "type": "投资产品"}]}, {"text": "客户可以选择购买工商银行发行的工银瑞信中证金融地产指数基金。", "label": [{"entity": "工银瑞信中证金融地产指数基金", "start_idx": 16, "end_idx": 24, "type": "投资产品"}]}, {"text": "她通过券商账户申购了华泰柏瑞沪深300ETF联接A开放式基金。", "label": [{"entity": "华泰柏瑞沪深300ETF联接A开放式基金", "start_idx": 15, "end_idx": 32, "type": "投资产品"}]}], "税务记录": [{"text": "他的个人所得税完税证明编号为20230001，已成功提交至税务局。", "label": [{"entity": "个人所得税完税证明编号", "start_idx": 5, "end_idx": 12, "type": "税务记录"}, {"entity": "20230001", "start_idx": 13, "end_idx": 19, "type": "税务记录"}]}, {"text": "公司提交的增值税专用发票编号NO.12345678，用于抵扣进项税。", "label": [{"entity": "增值税专用发票编号NO.12345678", "start_idx": 6, "end_idx": 24, "type": "税务记录"}]}, {"text": "她在2022年度企业所得税汇算清缴报告中，申报了利润总额500万元。", "label": [{"entity": "2022年度企业所得税汇算清缴报告", "start_idx": 3, "end_idx": 13, "type": "税务记录"}, {"entity": "利润总额500万元", "start_idx": 20, "end_idx": 26, "type": "税务记录"}]}, {"text": "这份印花税票销账单编号ST-2023-001，记录了合同印花税缴纳情况。", "label": [{"entity": "印花税票销账单编号ST-2023-001", "start_idx": 5, "end_idx": 25, "type": "税务记录"}, {"entity": "合同印花税", "start_idx": 31, "end_idx": 37, "type": "税务记录"}]}, {"text": "他提交的契税完税凭证编号CT20230001，用于办理房产过户手续。", "label": [{"entity": "契税完税凭证编号CT20230001", "start_idx": 7, "end_idx": 23, "type": "税务记录"}]}, {"text": "这份土地增值税清算表编号LDSC-2023-001，已通过税务部门审核。", "label": [{"entity": "土地增值税清算表编号LDSC-2023-001", "start_idx": 3, "end_idx": 24, "type": "税务记录"}]}, {"text": "她的消费税纳税申报表编号XFNS20230001，显示应纳税额为120万元。", "label": [{"entity": "消费税纳税申报表编号XFNS20230001", "start_idx": 3, "end_idx": 26, "type": "税务记录"}, {"entity": "应纳税额为120万元", "start_idx": 28, "end_idx": 38, "type": "税务记录"}]}, {"text": "公司提交的房产税缴纳凭证编号FCSJ20230001，已确认入库。", "label": [{"entity": "FCSJ20230001", "start_idx": 14, "end_idx": 25, "type": "税务记录"}]}, {"text": "这份车辆购置税完税证明编号CQCST20230001，用于办理车辆登记。", "label": [{"entity": "车辆购置税完税证明编号CQCST20230001", "start_idx": 5, "end_idx": 25, "type": "税务记录"}]}, {"text": "他提交的城镇土地使用税申报表编号CSST20230001，已按时完成申报。", "label": [{"entity": "城镇土地使用税申报表编号CSST20230001", "start_idx": 5, "end_idx": 27, "type": "税务记录"}]}], "信用记录": [{"text": "他的个人信用报告显示，最近一年的还款记录全部为“正常”。", "label": [{"entity": "信用报告", "start_idx": 5, "end_idx": 8, "type": "信用记录"}, {"entity": "还款记录", "start_idx": 16, "end_idx": 19, "type": "信用记录"}]}, {"text": "银行查询了他的征信报告，确认其信用卡使用率为35%。", "label": [{"entity": "征信报告", "start_idx": 4, "end_idx": 7, "type": "信用记录"}, {"entity": "信用卡使用率", "start_idx": 15, "end_idx": 19, "type": "信用记录"}, {"entity": "35%", "start_idx": 20, "end_idx": 22, "type": "信用记录"}]}, {"text": "这份信用记录单上标注了“逾期”字样，发生在2023年3月。", "label": [{"entity": "信用记录单", "start_idx": 0, "end_idx": 5, "type": "信用记录"}, {"entity": "逾期", "start_idx": 9, "end_idx": 11, "type": "信用记录"}]}, {"text": "小张的信用报告里，“五级分类”显示为“正常”状态。", "label": [{"entity": "信用报告", "start_idx": 3, "end_idx": 6, "type": "信用记录"}, {"entity": "五级分类", "start_idx": 10, "end_idx": 13, "type": "信用记录"}, {"entity": "正常", "start_idx": 16, "end_idx": 18, "type": "信用记录"}]}, {"text": "根据信用记录，他的贷款审批记录显示有3次成功案例。", "label": [{"entity": "信用记录", "start_idx": 0, "end_idx": 4, "type": "信用记录"}]}, {"text": "这份征信报告的“查询记录”部分记录了最近半年的所有查询。", "label": [{"entity": "征信报告", "start_idx": 2, "end_idx": 6, "type": "信用记录"}, {"entity": "查询记录", "start_idx": 10, "end_idx": 14, "type": "信用记录"}]}, {"text": "他的信用报告中，“公共记录”部分显示有1次行政处罚。", "label": [{"entity": "信用报告", "start_idx": 3, "end_idx": 6, "type": "信用记录"}, {"entity": "公共记录", "start_idx": 8, "end_idx": 11, "type": "信用记录"}, {"entity": "行政处罚", "start_idx": 20, "end_idx": 24, "type": "信用记录"}]}, {"text": "这份信用记录详细列出了他的“信用卡逾期”次数为2次。", "label": [{"entity": "信用记录", "start_idx": 0, "end_idx": 3, "type": "信用记录"}]}, {"text": "银行要求提供信用报告，其中“担保记录”显示有1笔有效担保。", "label": [{"entity": "信用报告", "start_idx": 4, "end_idx": 8, "type": "信用记录"}, {"entity": "担保记录", "start_idx": 12, "end_idx": 16, "type": "信用记录"}]}, {"text": "她的信用记录中，“个人基本信息”部分已更新至2024年1月。", "label": [{"entity": "信用记录", "start_idx": 3, "end_idx": 6, "type": "信用记录"}]}], "实体资产": [{"text": "这家公司的主要资产是一栋位于上海陆家嘴的摩天大楼。", "label": [{"entity": "摩天大楼", "start_idx": 24, "end_idx": 28, "type": "实体资产"}]}, {"text": "我家的车库停着一辆保时捷911跑车。", "label": [{"entity": "保时捷911跑车", "start_idx": 8, "end_idx": 12, "type": "实体资产"}]}, {"text": "仓库里堆满了成卷的工业用铜线。", "label": [{"entity": "工业用铜线", "start_idx": 8, "end_idx": 12, "type": "实体资产"}]}, {"text": "他的投资组合中包括一块位于海南的度假别墅。", "label": [{"entity": "度假别墅", "start_idx": 20, "end_idx": 23, "type": "实体资产"}]}, {"text": "工厂里安装了多台西门子数控机床。", "label": [{"entity": "西门子数控机床", "start_idx": 10, "end_idx": 16, "type": "实体资产"}]}, {"text": "银行的金库里存放着大量瑞士制造的劳力士手表。", "label": [{"entity": "金库", "start_idx": 3, "end_idx": 4, "type": "实体资产"}, {"entity": "劳力士手表", "start_idx": 12, "end_idx": 15, "type": "实体资产"}]}, {"text": "港口停泊着一艘载重5万吨的散货船。", "label": [{"entity": "散货船", "start_idx": 8, "end_idx": 10, "type": "实体资产"}]}, {"text": "公司最近购买了一片位于内蒙古的草原牧场。", "label": [{"entity": "草原牧场", "start_idx": 10, "end_idx": 13, "type": "实体资产"}]}, {"text": "这台笔记本电脑是公司的主要固定资产之一。", "label": [{"entity": "笔记本电脑", "start_idx": 3, "end_idx": 7, "type": "实体资产"}]}, {"text": "她继承了父亲留下的房产和存款作为主要资产。", "label": [{"entity": "房产", "start_idx": 11, "end_idx": 13, "type": "实体资产"}, {"entity": "存款", "start_idx": 14, "end_idx": 16, "type": "实体资产"}]}], "交易信息": [{"text": "张先生通过招商银行转账支付了订单号2023112501的货款。", "label": [{"entity": "转账支付", "start_idx": 12, "end_idx": 15, "type": "交易信息"}, {"entity": "订单号2023112501", "start_idx": 20, "end_idx": 27, "type": "交易信息"}, {"entity": "货款", "start_idx": 28, "end_idx": 30, "type": "交易信息"}]}, {"text": "李女士在京东商城下单，支付方式选择了微信支付，金额为299元。", "label": [{"entity": "微信支付", "start_idx": 11, "end_idx": 14, "type": "交易信息"}, {"entity": "299元", "start_idx": 19, "end_idx": 22, "type": "交易信息"}]}, {"text": "王总使用Visa卡****************完成了此次交易，交易金额为500美元。", "label": [{"entity": "Visa卡****************", "start_idx": 5, "end_idx": 23, "type": "交易信息"}, {"entity": "500美元", "start_idx": 34, "end_idx": 38, "type": "交易信息"}]}, {"text": "刘先生在美团外卖下单，订单号MEI20231126，支付了58元。", "label": [{"entity": "订单号MEI20231126", "start_idx": 12, "end_idx": 23, "type": "交易信息"}, {"entity": "58元", "start_idx": 26, "end_idx": 28, "type": "交易信息"}]}, {"text": "赵女士使用银联卡6225881234567890支付了机票费用，金额为2580元。", "label": [{"entity": "银联卡6225881234567890", "start_idx": 5, "end_idx": 20, "type": "交易信息"}, {"entity": "机票费用", "start_idx": 22, "end_idx": 27, "type": "交易信息"}, {"entity": "2580元", "start_idx": 34, "end_idx": 37, "type": "交易信息"}]}, {"text": "周先生在淘宝下单，订单号Tao20231127，使用花呗支付了199元。", "label": [{"entity": "订单号Tao20231127", "start_idx": 8, "end_idx": 20, "type": "交易信息"}, {"entity": "199元", "start_idx": 33, "end_idx": 36, "type": "交易信息"}]}, {"text": "吴女士通过PayPal账号****************完成了国际购物支付，金额为120欧元。", "label": [{"entity": "PayPal账号", "start_idx": 7, "end_idx": 12, "type": "交易信息"}, {"entity": "<EMAIL>", "start_idx": 12, "end_idx": 26, "type": "交易信息"}, {"entity": "国际购物支付", "start_idx": 28, "end_idx": 37, "type": "交易信息"}, {"entity": "120欧元", "start_idx": 45, "end_idx": 50, "type": "交易信息"}]}, {"text": "孙先生在携程预订酒店，订单号CT20231128，支付了899元。", "label": [{"entity": "携程", "start_idx": 7, "end_idx": 9, "type": "交易信息"}, {"entity": "酒店", "start_idx": 10, "end_idx": 12, "type": "交易信息"}, {"entity": "订单号CT20231128", "start_idx": 13, "end_idx": 25, "type": "交易信息"}, {"entity": "899元", "start_idx": 29, "end_idx": 32, "type": "交易信息"}]}, {"text": "钱小姐使用Apple Pay完成支付，交易金额为88元，设备序列号为A123456789。", "label": [{"entity": "Apple Pay", "start_idx": 6, "end_idx": 13, "type": "交易信息"}, {"entity": "88元", "start_idx": 21, "end_idx": 24, "type": "交易信息"}, {"entity": "A123456789", "start_idx": 34, "end_idx": 43, "type": "交易信息"}]}, {"text": "张先生通过支付宝转账给李女士500元，交易时间为2023年10月15日15:30。", "label": [{"entity": "支付宝", "start_idx": 6, "end_idx": 9, "type": "交易信息"}, {"entity": "500元", "start_idx": 16, "end_idx": 19, "type": "交易信息"}, {"entity": "2023年10月15日15:30", "start_idx": 22, "end_idx": 36, "type": "交易信息"}]}], "药物": [{"text": "阿司匹林是一种常用的解热镇痛药，可以有效缓解轻度头痛。", "label": [{"entity": "阿司匹林", "start_idx": 0, "end_idx": 3, "type": "药物"}, {"entity": "解热镇痛药", "start_idx": 7, "end_idx": 11, "type": "药物"}]}, {"text": "布洛芬属于非甾体抗炎药，常用于治疗肌肉和关节的炎症疼痛。", "label": [{"entity": "布洛芬", "start_idx": 0, "end_idx": 2, "type": "药物"}, {"entity": "非甾体抗炎药", "start_idx": 7, "end_idx": 12, "type": "药物"}]}], "疾病": [{"text": "高血压患者需要长期服用降压药来控制病情。", "label": [{"entity": "高血压", "start_idx": 0, "end_idx": 2, "type": "疾病"}]}, {"text": "糖尿病是导致视网膜病变的主要原因之一。", "label": [{"entity": "糖尿病", "start_idx": 0, "end_idx": 2, "type": "疾病"}, {"entity": "视网膜病变", "start_idx": 6, "end_idx": 9, "type": "疾病"}]}, {"text": "哮喘患者在季节交替时容易发作。", "label": [{"entity": "哮喘", "start_idx": 0, "end_idx": 2, "type": "疾病"}]}], "医疗程序": [{"text": "医生建议他进行冠状动脉造影术以明确诊断。", "label": [{"entity": "冠状动脉造影术", "start_idx": 9, "end_idx": 15, "type": "医疗程序"}]}, {"text": "患者接受了胃镜检查，结果显示十二指肠溃疡。", "label": [{"entity": "胃镜检查", "start_idx": 4, "end_idx": 7, "type": "医疗程序"}]}, {"text": "她预约了乳腺钼靶检查，以筛查早期病变。", "label": [{"entity": "乳腺钼靶检查", "start_idx": 6, "end_idx": 11, "type": "医疗程序"}]}, {"text": "医生为她安排了腹腔镜胆囊切除术。", "label": [{"entity": "腹腔镜胆囊切除术", "start_idx": 7, "end_idx": 16, "type": "医疗程序"}]}, {"text": "他需要完成肺功能测试，评估呼吸状况。", "label": [{"entity": "肺功能测试", "start_idx": 5, "end_idx": 8, "type": "医疗程序"}]}, {"text": "患者接受了经皮冠状动脉介入治疗，恢复良好。", "label": [{"entity": "经皮冠状动脉介入治疗", "start_idx": 5, "end_idx": 10, "type": "医疗程序"}]}, {"text": "她定期进行宫颈癌筛查，包括液基细胞学检测。", "label": [{"entity": "宫颈癌筛查", "start_idx": 8, "end_idx": 12, "type": "医疗程序"}, {"entity": "液基细胞学检测", "start_idx": 15, "end_idx": 21, "type": "医疗程序"}]}, {"text": "医生建议进行核磁共振成像，以观察脑部结构。", "label": [{"entity": "核磁共振成像", "start_idx": 7, "end_idx": 13, "type": "医疗程序"}]}, {"text": "他完成了结肠镜检查，未发现异常病变。", "label": [{"entity": "结肠镜检查", "start_idx": 3, "end_idx": 8, "type": "医疗程序"}]}], "临床表现": [{"text": "患者出现明显的皮疹和瘙痒症状。", "label": [{"entity": "皮疹", "start_idx": 6, "end_idx": 7, "type": "临床表现"}, {"entity": "瘙痒", "start_idx": 9, "end_idx": 10, "type": "临床表现"}]}, {"text": "病人检查发现肝功能异常，表现为转氨酶升高。", "label": [{"entity": "肝功能异常", "start_idx": 6, "end_idx": 9, "type": "临床表现"}, {"entity": "转氨酶升高", "start_idx": 13, "end_idx": 16, "type": "临床表现"}]}, {"text": "他的呼吸系统症状包括持续性的干咳和胸闷。", "label": [{"entity": "持续性的干咳", "start_idx": 11, "end_idx": 17, "type": "临床表现"}, {"entity": "胸闷", "start_idx": 18, "end_idx": 20, "type": "临床表现"}]}, {"text": "患者自述有剧烈的头痛和恶心呕吐。", "label": [{"entity": "剧烈的头痛", "start_idx": 4, "end_idx": 8, "type": "临床表现"}, {"entity": "恶心呕吐", "start_idx": 9, "end_idx": 12, "type": "临床表现"}]}, {"text": "检查结果显示患者有明显的肌肉无力现象。", "label": [{"entity": "肌肉无力", "start_idx": 12, "end_idx": 15, "type": "临床表现"}]}, {"text": "她出现了低热、乏力以及关节疼痛等症状。", "label": [{"entity": "低热", "start_idx": 4, "end_idx": 6, "type": "临床表现"}, {"entity": "乏力", "start_idx": 7, "end_idx": 9, "type": "临床表现"}, {"entity": "关节疼痛", "start_idx": 11, "end_idx": 15, "type": "临床表现"}]}, {"text": "患者主要表现为持续性的腹痛和腹泻。", "label": [{"entity": "持续性的腹痛", "start_idx": 7, "end_idx": 12, "type": "临床表现"}, {"entity": "腹泻", "start_idx": 13, "end_idx": 15, "type": "临床表现"}]}], "过敏信息": [{"text": "他对花生过敏，每次吃到花生酱都会引起严重的皮肤反应。", "label": [{"entity": "花生", "start_idx": 5, "end_idx": 7, "type": "过敏信息"}, {"entity": "花生酱", "start_idx": 16, "end_idx": 19, "type": "过敏信息"}]}, {"text": "她有牛奶蛋白过敏，喝完牛奶后会出现腹泻和呕吐症状。", "label": [{"entity": "牛奶蛋白过敏", "start_idx": 3, "end_idx": 8, "type": "过敏信息"}, {"entity": "牛奶", "start_idx": 12, "end_idx": 14, "type": "过敏信息"}]}, {"text": "小明对青霉素类药物过敏，医生给他换了头孢类抗生素。", "label": [{"entity": "青霉素类药物", "start_idx": 3, "end_idx": 8, "type": "过敏信息"}]}, {"text": "她对芒果皮接触性过敏，剥芒果时手会发痒。", "label": [{"entity": "芒果皮", "start_idx": 4, "end_idx": 6, "type": "过敏信息"}]}, {"text": "他对海鲜过敏，特别是带壳的虾和蟹，食用后呼吸困难。", "label": [{"entity": "海鲜", "start_idx": 3, "end_idx": 5, "type": "过敏信息"}, {"entity": "带壳的虾和蟹", "start_idx": 8, "end_idx": 13, "type": "过敏信息"}]}, {"text": "她有小麦过敏，必须避免食用含麸质的面条和面包。", "label": [{"entity": "小麦", "start_idx": 3, "end_idx": 4, "type": "过敏信息"}, {"entity": "麸质", "start_idx": 17, "end_idx": 18, "type": "过敏信息"}]}, {"text": "他对尘螨过敏，卧室里用了防螨床品后症状有所缓解。", "label": [{"entity": "尘螨", "start_idx": 3, "end_idx": 4, "type": "过敏信息"}]}, {"text": "她有花粉过敏，春天里杨树和柳树的花粉会让她打喷嚏。", "label": [{"entity": "花粉过敏", "start_idx": 2, "end_idx": 6, "type": "过敏信息"}, {"entity": "杨树", "start_idx": 10, "end_idx": 12, "type": "过敏信息"}, {"entity": "柳树", "start_idx": 13, "end_idx": 15, "type": "过敏信息"}]}, {"text": "他对蜂毒过敏，被蜜蜂蜇后需要立即注射肾上腺素。", "label": [{"entity": "蜂毒", "start_idx": 3, "end_idx": 4, "type": "过敏信息"}, {"entity": "蜜蜂", "start_idx": 7, "end_idx": 8, "type": "过敏信息"}]}, {"text": "她有酒精过敏，喝红酒后皮肤会迅速出现红疹。", "label": [{"entity": "酒精过敏", "start_idx": 3, "end_idx": 7, "type": "过敏信息"}, {"entity": "红酒", "start_idx": 10, "end_idx": 12, "type": "过敏信息"}]}], "生育信息": [{"text": "她的预产期是2024年6月15日，医生建议提前准备待产包。", "label": [{"entity": "2024年6月15日", "start_idx": 6, "end_idx": 14, "type": "生育信息"}, {"entity": "待产包", "start_idx": 28, "end_idx": 31, "type": "生育信息"}]}, {"text": "张女士的排卵日是本月5号，计划在排卵期前后安排同房。", "label": [{"entity": "排卵日", "start_idx": 4, "end_idx": 6, "type": "生育信息"}, {"entity": "本月5号", "start_idx": 7, "end_idx": 10, "type": "生育信息"}, {"entity": "排卵期", "start_idx": 19, "end_idx": 21, "type": "生育信息"}, {"entity": "同房", "start_idx": 24, "end_idx": 26, "type": "生育信息"}]}, {"text": "这份产前检查报告显示胎儿心率是145次/分钟，一切正常。", "label": [{"entity": "产前检查", "start_idx": 3, "end_idx": 6, "type": "生育信息"}, {"entity": "胎儿", "start_idx": 8, "end_idx": 10, "type": "生育信息"}, {"entity": "145次/分钟", "start_idx": 13, "end_idx": 19, "type": "生育信息"}]}, {"text": "李先生的精液分析报告显示精子活力为A级，符合生育标准。", "label": [{"entity": "精子活力", "start_idx": 9, "end_idx": 11, "type": "生育信息"}, {"entity": "A级", "start_idx": 12, "end_idx": 13, "type": "生育信息"}, {"entity": "生育标准", "start_idx": 20, "end_idx": 22, "type": "生育信息"}]}, {"text": "小王的末次月经是2023年12月1日，预计孕周为16周。", "label": [{"entity": "2023年12月1日", "start_idx": 5, "end_idx": 11, "type": "生育信息"}, {"entity": "16周", "start_idx": 17, "end_idx": 19, "type": "生育信息"}]}, {"text": "产检结果显示唐氏筛查风险值为1/500，属于低风险范围。", "label": [{"entity": "唐氏筛查", "start_idx": 7, "end_idx": 10, "type": "生育信息"}, {"entity": "1/500", "start_idx": 11, "end_idx": 15, "type": "生育信息"}, {"entity": "低风险范围", "start_idx": 19, "end_idx": 23, "type": "生育信息"}]}, {"text": "刘女士的宫颈粘液检查显示拉丝长度为8厘米，适合受孕。", "label": [{"entity": "8厘米", "start_idx": 14, "end_idx": 17, "type": "生育信息"}, {"entity": "适合受孕", "start_idx": 18, "end_idx": 22, "type": "生育信息"}]}, {"text": "胎心监护图显示胎儿基线心率是130次/分钟，有良好加速反应。", "label": [{"entity": "胎儿基线心率", "start_idx": 6, "end_idx": 11, "type": "生育信息"}, {"entity": "130次/分钟", "start_idx": 12, "end_idx": 18, "type": "生育信息"}, {"entity": "良好加速反应", "start_idx": 24, "end_idx": 31, "type": "生育信息"}]}, {"text": "超声波测量显示胎儿双顶径为6.2厘米，符合孕周发育标准。", "label": [{"entity": "胎儿双顶径", "start_idx": 7, "end_idx": 10, "type": "生育信息"}, {"entity": "6.2厘米", "start_idx": 11, "end_idx": 14, "type": "生育信息"}, {"entity": "孕周发育标准", "start_idx": 22, "end_idx": 28, "type": "生育信息"}]}, {"text": "排卵试纸检测结果为强阳性，提示即将排卵，适合安排受孕。", "label": [{"entity": "排卵试纸检测结果", "start_idx": 0, "end_idx": 7, "type": "生育信息"}, {"entity": "强阳性", "start_idx": 8, "end_idx": 10, "type": "生育信息"}, {"entity": "即将排卵", "start_idx": 13, "end_idx": 17, "type": "生育信息"}, {"entity": "受孕", "start_idx": 27, "end_idx": 29, "type": "生育信息"}]}], "地理位置": [{"text": "北京故宫是中国最著名的古代宫殿之一。", "label": [{"entity": "北京", "start_idx": 0, "end_idx": 1, "type": "地理位置"}, {"entity": "故宫", "start_idx": 2, "end_idx": 3, "type": "地理位置"}]}, {"text": "上海外滩的夜景吸引了无数游客前来观赏。", "label": [{"entity": "上海外滩", "start_idx": 0, "end_idx": 4, "type": "地理位置"}]}, {"text": "杭州西湖的景色四季皆宜，令人流连忘返。", "label": [{"entity": "杭州西湖", "start_idx": 0, "end_idx": 4, "type": "地理位置"}]}, {"text": "广州塔是广东省的地标性建筑，高耸入云。", "label": [{"entity": "广州塔", "start_idx": 0, "end_idx": 2, "type": "地理位置"}, {"entity": "广东省", "start_idx": 6, "end_idx": 8, "type": "地理位置"}]}, {"text": "成都锦里古街保留了传统的川西民俗风情。", "label": [{"entity": "成都", "start_idx": 0, "end_idx": 2, "type": "地理位置"}, {"entity": "锦里古街", "start_idx": 3, "end_idx": 7, "type": "地理位置"}]}, {"text": "西安兵马俑是世界文化遗产，展示了秦朝的辉煌。", "label": [{"entity": "西安", "start_idx": 0, "end_idx": 1, "type": "地理位置"}, {"entity": "兵马俑", "start_idx": 2, "end_idx": 4, "type": "地理位置"}]}, {"text": "南京夫子庙是江南地区重要的文化古迹之一。", "label": [{"entity": "南京夫子庙", "start_idx": 0, "end_idx": 4, "type": "地理位置"}, {"entity": "江南地区", "start_idx": 6, "end_idx": 9, "type": "地理位置"}]}], "行程信息": [{"text": "我计划下周三早上8:30从北京首都国际机场起飞前往上海浦东国际机场。", "label": [{"entity": "北京首都国际机场起飞前往上海浦东国际机场", "start_idx": 18, "end_idx": 33, "type": "行程信息"}]}, {"text": "我们预订了2024年1月15日下午3点的航班，从广州白云机场飞往成都双流机场。", "label": [{"entity": "2024年1月15日下午3点", "start_idx": 5, "end_idx": 15, "type": "行程信息"}, {"entity": "广州白云机场", "start_idx": 21, "end_idx": 27, "type": "行程信息"}, {"entity": "成都双流机场", "start_idx": 32, "end_idx": 40, "type": "行程信息"}]}, {"text": "火车将于2023年12月31日晚上10:05从西安北站出发，终点是北京西站。", "label": [{"entity": "2023年12月31日晚上10:05", "start_idx": 4, "end_idx": 15, "type": "行程信息"}, {"entity": "西安北站", "start_idx": 22, "end_idx": 27, "type": "行程信息"}, {"entity": "北京西站", "start_idx": 35, "end_idx": 40, "type": "行程信息"}]}, {"text": "请确认您的航班号CA1234，起飞时间是2024年2月20日早上7:45。", "label": [{"entity": "航班号CA1234", "start_idx": 8, "end_idx": 16, "type": "行程信息"}, {"entity": "2024年2月20日", "start_idx": 20, "end_idx": 28, "type": "行程信息"}, {"entity": "早上7:45", "start_idx": 29, "end_idx": 35, "type": "行程信息"}]}, {"text": "您需要提前到达上海虹桥火车站，因为列车将在2024年3月1日早上6:50发车。", "label": [{"entity": "上海虹桥火车站", "start_idx": 11, "end_idx": 20, "type": "行程信息"}, {"entity": "2024年3月1日早上6:50", "start_idx": 29, "end_idx": 41, "type": "行程信息"}]}, {"text": "我们将于2023年10月5日下午4:10从深圳宝安机场起飞，飞往厦门高崎机场。", "label": [{"entity": "2023年10月5日下午4:10", "start_idx": 5, "end_idx": 19, "type": "行程信息"}, {"entity": "深圳宝安机场", "start_idx": 25, "end_idx": 32, "type": "行程信息"}, {"entity": "厦门高崎机场", "start_idx": 39, "end_idx": 46, "type": "行程信息"}]}, {"text": "请注意，高铁票显示2024年4月10日晚上8:20从武汉站出发，到达长沙南站。", "label": [{"entity": "2024年4月10日晚上8:20", "start_idx": 8, "end_idx": 19, "type": "行程信息"}, {"entity": "武汉站", "start_idx": 23, "end_idx": 26, "type": "行程信息"}, {"entity": "长沙南站", "start_idx": 31, "end_idx": 35, "type": "行程信息"}]}, {"text": "我的行程安排是2023年9月25日早上9:00从重庆江北机场飞往昆明长水机场。", "label": [{"entity": "从重庆江北机场飞往昆明长水机场", "start_idx": 22, "end_idx": 39, "type": "行程信息"}]}, {"text": "我计划在2024年5月15日乘坐MU5137航班从北京首都国际机场出发。", "label": [{"entity": "2024年5月15日", "start_idx": 5, "end_idx": 13, "type": "行程信息"}, {"entity": "MU5137航班", "start_idx": 19, "end_idx": 26, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 32, "end_idx": 43, "type": "行程信息"}]}, {"text": "明天早上8:30，我需要从上海市静安区南京西路1266号出发前往火车站。", "label": [{"entity": "火车站", "start_idx": 28, "end_idx": 31, "type": "行程信息"}]}]}