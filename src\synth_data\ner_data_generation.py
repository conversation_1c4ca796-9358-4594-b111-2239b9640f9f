# ner_data_generation.py
# 基于目标数量、句子多样化、实体多样化和示例数据生成NER数据集

import json
import os
import re
import random
import requests
import time
import logging
import asyncio
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor, as_completed
from datetime import datetime
from typing import List, Dict, Tuple, Optional, Any, Callable
from tqdm import tqdm
from pathlib import Path
from abc import ABC, abstractmethod
import traceback
import threading
from dataclasses import dataclass
from enum import Enum

# 读取NER生成配置
with open('src/synth_data/ner_config.json', 'r', encoding='utf-8') as f:
    ner_config = json.load(f)

# =====================
# 并行处理和日志配置
# =====================

# 并行处理配置
PARALLEL_CONFIG = ner_config.get("parallel_processing", {
    "enabled": True,
    "strategy": "hybrid",  # "process", "async", "hybrid"
    "max_processes": 4,
    "max_concurrent_tasks": 8,
    "entity_type_batch_size": 2,
    "task_timeout": 300,
    "resource_limits": {
        "max_memory_mb": 2048,
        "max_cpu_percent": 80
    }
})

# 重试配置
RETRY_CONFIG = ner_config.get("retry_config", {
    "max_retries": 3,
    "base_delay": 1.0,
    "max_delay": 60.0,
    "backoff_factor": 2.0,
    "retryable_errors": ["429", "500", "502", "503", "504"],
    "non_retryable_errors": ["400", "401", "403"]
})

# 日志配置
LOG_CONFIG = ner_config.get("log_config", {
    "enabled": True,
    "log_level": "INFO",
    "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "log_dir": "logs",
    "max_log_size_mb": 10,
    "backup_count": 5
})

# 任务状态枚举
class TaskStatus(Enum):
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAILED = "failed"
    RETRYING = "retrying"

# 任务信息数据类
@dataclass
class TaskInfo:
    task_id: str
    entity_type: str
    operation: str
    status: TaskStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    retry_count: int = 0
    error_message: Optional[str] = None
    context: Optional[Dict] = None

# 全局任务管理器
class TaskManager:
    def __init__(self):
        self.tasks: Dict[str, TaskInfo] = {}
        self.lock = threading.Lock()
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        if not LOG_CONFIG.get("enabled", True):
            return logging.getLogger("dummy")
        
        logger = logging.getLogger("task_manager")
        logger.setLevel(getattr(logging, LOG_CONFIG.get("log_level", "INFO")))
        
        # 创建日志目录
        log_dir = Path(LOG_CONFIG.get("log_dir", "logs"))
        log_dir.mkdir(exist_ok=True)
        
        # 文件处理器
        file_handler = logging.FileHandler(
            log_dir / f"task_manager_{datetime.now().strftime('%Y%m%d')}.log",
            encoding='utf-8'
        )
        file_handler.setFormatter(
            logging.Formatter(LOG_CONFIG.get("log_format"))
        )
        logger.addHandler(file_handler)
        
        return logger
    
    def create_task(self, entity_type: str, operation: str, context: Dict = None) -> str:
        """创建新任务"""
        task_id = f"{entity_type}_{operation}_{datetime.now().strftime('%H%M%S%f')}"
        task = TaskInfo(
            task_id=task_id,
            entity_type=entity_type,
            operation=operation,
            status=TaskStatus.PENDING,
            start_time=datetime.now(),
            context=context or {}
        )
        
        with self.lock:
            self.tasks[task_id] = task
        
        self.logger.info(f"创建任务: {task_id} - {entity_type} - {operation}")
        return task_id
    
    def update_task_status(self, task_id: str, status: TaskStatus, error_message: str = None):
        """更新任务状态"""
        with self.lock:
            if task_id in self.tasks:
                task = self.tasks[task_id]
                task.status = status
                if status in [TaskStatus.SUCCESS, TaskStatus.FAILED]:
                    task.end_time = datetime.now()
                if error_message:
                    task.error_message = error_message
                
                self.logger.info(f"任务状态更新: {task_id} - {status.value}")
    
    def log_task_error(self, task_id: str, error: Exception, context: Dict = None):
        """记录任务错误"""
        with self.lock:
            if task_id in self.tasks:
                task = self.tasks[task_id]
                task.retry_count += 1
                task.error_message = str(error)
                
                error_log = {
                    "timestamp": datetime.now().isoformat(),
                    "level": "ERROR",
                    "task_id": task_id,
                    "entity_type": task.entity_type,
                    "operation": task.operation,
                    "error_type": type(error).__name__,
                    "error_message": str(error),
                    "retry_count": task.retry_count,
                    "context": context or task.context,
                    "traceback": traceback.format_exc()
                }
                
                self.logger.error(f"任务错误: {json.dumps(error_log, ensure_ascii=False)}")
    
    def get_task_stats(self) -> Dict:
        """获取任务统计信息"""
        with self.lock:
            stats = {
                "total": len(self.tasks),
                "success": len([t for t in self.tasks.values() if t.status == TaskStatus.SUCCESS]),
                "failed": len([t for t in self.tasks.values() if t.status == TaskStatus.FAILED]),
                "running": len([t for t in self.tasks.values() if t.status == TaskStatus.RUNNING]),
                "pending": len([t for t in self.tasks.values() if t.status == TaskStatus.PENDING])
            }
            return stats

# 全局任务管理器实例
task_manager = TaskManager()

# =====================
# 配置和路径
# =====================

# 读取API配置
with open('src/gen_strat/diversity_config.json', 'r', encoding='utf-8') as f:
    config = json.load(f)

API_KEY_PATH = config["api_key_path"]
with open(API_KEY_PATH, 'r', encoding='utf-8') as f:
    api_key = json.load(f)['api_key']

# API参数
ZHIPU_API_URL = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
MODEL_NAME = "glm-4-air-250414"
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

# 输出目录
def get_output_dir(timestamp=None):
    """获取输出目录路径
    
    Args:
        timestamp: 可选的时间戳，如果不提供则使用当前时间
        
    Returns:
        str: 输出目录的路径
    """
    if timestamp is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    return os.path.join('synth_dataset', 'runs', timestamp)


# 读取生成特征配置
GENERATION_FEATURES = ner_config.get("generation_features", {
    "use_sentence_diversity": True,
    "use_entity_diversity": True,
    "use_example_sentences": True
})

# 读取标注方法配置
ANNOTATION_METHOD = ner_config.get("annotation_method", "comprehensive")

# 批量生成和筛选器配置
BATCH_GENERATION_CONFIG = ner_config.get("batch_generation", {
    "enabled": True,
    "batch_size_strategy": "adaptive",  # adaptive, fixed, dynamic
    "max_batch_size": 50,
    "min_batch_size": 5,
    "default_batch_size": 20
})

SENTENCE_FILTER_CONFIG = ner_config.get("sentence_filtering", {
    "enabled": True,
    "filters": ["length", "naturalness"],
    "quality_thresholds": {
        "min_pass_rate": 0.6,
        "max_rejection_rate": 0.4
    },
    "length_filter": {
        "enabled": True,
        "min_length": 15,
        "max_length": 80,
        "entity_type_specific": {
            "行程信息": {"min_length": 20, "max_length": 100},
            "地理位置": {"min_length": 10, "max_length": 50}
        }
    },
    "naturalness_filter": {
        "enabled": True,
        "threshold": 6.0,
        "batch_size": 10,
        "max_retries": 3
    }
})

MAX_ENTITIES_PER_SENTENCE = ner_config.get("max_entities_per_sentence", 2)
SENTENCE_LENGTH_RANGE = ner_config.get("sentence_length_range", [20, 100])
API_RETRY_TIMES = ner_config.get("api_retry_times", 3)
API_WAIT_TIME = ner_config.get("api_wait_time", 1.5)

# 在文件开头添加实体schema加载
with open('src/gen_strat/entity_schema.json', 'r', encoding='utf-8') as f:
    ENTITY_SCHEMA = json.load(f)

# =====================
# 句子筛选器基类和实现
# =====================

class SentenceFilter(ABC):
    """句子筛选器基类"""
    
    @abstractmethod
    def filter(self, sentences: List[str], entity_type: str = None) -> Tuple[List[str], List[Dict]]:
        """
        筛选句子
        
        Args:
            sentences: 待筛选的句子列表
            entity_type: 实体类型（用于特定类型的筛选规则）
            
        Returns:
            - 通过筛选的句子列表
            - 筛选结果详情（包含被过滤的原因）
        """
        pass

class LengthFilter(SentenceFilter):
    """长度筛选器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.enabled = config.get("enabled", True)
        self.min_length = config.get("min_length", 15)
        self.max_length = config.get("max_length", 80)
        self.entity_type_specific = config.get("entity_type_specific", {})
    
    def filter(self, sentences: List[str], entity_type: str = None) -> Tuple[List[str], List[Dict]]:
        """长度筛选实现"""
        if not self.enabled:
            return sentences, []
        
        # 获取实体类型特定的长度限制
        if entity_type and entity_type in self.entity_type_specific:
            min_len = self.entity_type_specific[entity_type].get("min_length", self.min_length)
            max_len = self.entity_type_specific[entity_type].get("max_length", self.max_length)
        else:
            min_len = self.min_length
            max_len = self.max_length
        
        filtered = []
        rejected = []
        
        for sentence in sentences:
            char_count = len(sentence.strip())
            if min_len <= char_count <= max_len:
                filtered.append(sentence)
            else:
                rejected.append({
                    "sentence": sentence,
                    "reason": f"长度不符合要求({char_count}字符，要求{min_len}-{max_len})",
                    "filter_type": "length"
                })
        
        return filtered, rejected

class NaturalnessFilter(SentenceFilter):
    """自然度筛选器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.enabled = config.get("enabled", True)
        self.threshold = config.get("threshold", 6.0)
        self.batch_size = config.get("batch_size", 10)
        self.max_retries = config.get("max_retries", 3)
    
    def filter(self, sentences: List[str], entity_type: str = None) -> Tuple[List[str], List[Dict]]:
        """自然度筛选实现"""
        if not self.enabled:
            return sentences, []
        
        if not sentences:
            return [], []
        
        # 批量评分
        scores = self._batch_naturalness_scoring(sentences)
        
        filtered = []
        rejected = []
        
        for sentence, score in zip(sentences, scores):
            if score >= self.threshold:
                filtered.append(sentence)
            else:
                rejected.append({
                    "sentence": sentence,
                    "score": score,
                    "reason": f"自然度评分过低({score:.2f} < {self.threshold})",
                    "filter_type": "naturalness"
                })
        
        return filtered, rejected
    
    def _batch_naturalness_scoring(self, sentences: List[str]) -> List[float]:
        """批量自然度评分"""
        scores = []
        
        # 分批处理
        for i in range(0, len(sentences), self.batch_size):
            batch = sentences[i:i + self.batch_size]
            batch_scores = self._score_batch(batch)
            scores.extend(batch_scores)
        
        return scores
    
    def _score_batch(self, batch: List[str]) -> List[float]:
        """对一批句子进行评分"""
        if not batch:
            return []
        
        # 构建批量评分prompt
        prompt = "请对以下句子的自然度进行打分，0分表示极其生硬、完全不符合真实语言，10分表示非常自然、完全符合真实世界表达。请按顺序返回每个句子的分数，用逗号分隔。\n\n"
        for i, sentence in enumerate(batch, 1):
            prompt += f"{i}. {sentence}\n"
        
        for attempt in range(self.max_retries):
            try:
                response = call_zhipu_api(prompt)
                # 解析分数
                score_text = response.strip()
                scores = []
                for score_str in score_text.split(','):
                    try:
                        score = float(score_str.strip())
                        scores.append(score)
                    except ValueError:
                        scores.append(0.0)
                
                # 确保返回的分数数量与句子数量一致
                while len(scores) < len(batch):
                    scores.append(0.0)
                
                return scores[:len(batch)]
                
            except Exception as e:
                print(f"[警告] 自然度评分失败（尝试 {attempt + 1}/{self.max_retries}）：{e}")
                if attempt == self.max_retries - 1:
                    # 最后一次尝试失败，返回默认分数
                    return [10.0] * len(batch)
                time.sleep(API_WAIT_TIME * (attempt + 1))

class FilterChain:
    """筛选器链管理器"""
    
    def __init__(self, filters: List[SentenceFilter]):
        self.filters = filters
    
    def apply_filters(self, sentences: List[str], entity_type: str = None) -> Tuple[List[str], Dict]:
        """
        依次应用所有筛选器
        
        Args:
            sentences: 待筛选的句子列表
            entity_type: 实体类型
            
        Returns:
            - 最终通过筛选的句子
            - 详细的筛选统计信息
        """
        filtered_sentences = sentences
        filter_stats = {
            "total_initial": len(sentences),
            "filter_results": {}
        }
        
        for i, filter_obj in enumerate(self.filters):
            if filtered_sentences:
                filtered_sentences, filter_result = filter_obj.filter(filtered_sentences, entity_type)
                filter_stats["filter_results"][f"filter_{i}"] = {
                    "filter_type": filter_result[0]["filter_type"] if filter_result else "unknown",
                    "passed": len(filtered_sentences),
                    "rejected": len(filter_result),
                    "rejection_rate": len(filter_result) / (len(filtered_sentences) + len(filter_result)) if (len(filtered_sentences) + len(filter_result)) > 0 else 0
                }
        
        filter_stats["total_final"] = len(filtered_sentences)
        filter_stats["overall_rejection_rate"] = (filter_stats["total_initial"] - filter_stats["total_final"]) / filter_stats["total_initial"] if filter_stats["total_initial"] > 0 else 0
        
        return filtered_sentences, filter_stats

# =====================
# 数据加载函数
# =====================

def get_latest_target_file(strategy_dir="reproduce"):
    """获取目标数量文件"""
    target_file = Path(strategy_dir) / "entity_target" / "privacy_bench_target.json"
    if not target_file.exists():
        raise FileNotFoundError(f"未找到目标数量文件：{target_file}")
    
    with open(target_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def load_sentence_diversity(strategy_dir="reproduce"):
    """加载句子多样化配置"""
    diversity_file = Path(strategy_dir) / "sen_diversity" / "sen_diversify_value.json"
    with open(diversity_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
        return data.get('sen_diversify_value', {})

def get_latest_entity_diversity(strategy_dir="reproduce"):
    """获取最新的实体多样化数据"""
    entity_dir = Path(strategy_dir) / "entity_diversity"
    
    if not entity_dir.exists():
        raise FileNotFoundError(f"未找到实体多样化目录：{entity_dir}")
    
    dirs = [d for d in entity_dir.iterdir() if d.is_dir()]
    if not dirs:
        raise FileNotFoundError("未找到实体多样化目录")
    
    latest_dir = sorted(dirs)[-1]
    
    # 加载JSON格式的实体多样化数据
    entity_diversity_file = latest_dir / 'entity_diversity.json'
    if not entity_diversity_file.exists():
        raise FileNotFoundError(f"未找到实体多样化JSON文件：{entity_diversity_file}")
    
    with open(entity_diversity_file, 'r', encoding='utf-8') as f:
        entity_diversity_data = json.load(f)
    
    # 转换为兼容格式
    vanilla_entities = {}
    latent_entities = {}
    
    for entity_type, entity_data in entity_diversity_data.items():
        # 处理vanilla实体
        if "vanilla" in entity_data and entity_data["vanilla"]:
            vanilla_entities[entity_type] = entity_data["vanilla"]
        
        # 处理latent实体
        if "latent" in entity_data and entity_data["latent"]:
            for attr_val, entities in entity_data["latent"].items():
                if attr_val not in latent_entities:
                    latent_entities[attr_val] = {}
                if entities:  # 只有当实体列表不为空时才添加
                    latent_entities[attr_val][entity_type] = entities
    
    return vanilla_entities, latent_entities

def load_example_sentences(strategy_dir="reproduce"):
    """加载示例句子"""
    # 从配置文件加载示例句子
    config_examples = ner_config.get("example_sentences", [])
    
    # 从合并数据集中选择示例，确保每个目标实体类型都有1个例子
    target_entity_types = get_latest_target_file(strategy_dir).keys()
    merged_examples = []
    
    # 实体类型映射：将目标实体类型映射到合并数据集中实际存在的类型
    # 合并数据集中只有：姓名、地理位置、职业
    entity_type_mapping = {
        # 个人PII类
        "姓名": ["姓名"],  # 直接匹配
        "年龄": ["姓名", "职业"],  # 年龄通常与姓名、职业一起出现
        "性别": ["姓名", "职业"],  # 性别通常与姓名、职业一起出现
        "国籍": ["姓名", "地理位置"],  # 国籍与姓名、地理位置相关
        "职业": ["职业"],  # 直接匹配
        "民族": ["姓名", "职业"],  # 民族与姓名、职业相关
        "教育背景": ["姓名", "职业"],  # 教育背景与姓名、职业相关
        "婚姻状况": ["姓名", "职业"],  # 婚姻状况与姓名、职业相关
        "政治倾向": ["姓名", "职业"],  # 政治倾向与姓名、职业相关
        "家庭成员": ["姓名", "职业"],  # 家庭成员与姓名、职业相关
        
        # 金融财务类
        "工资数额": ["姓名", "职业", "地理位置"],  # 工资与姓名、职业、地理位置相关
        "投资产品": ["姓名", "职业", "地理位置"],  # 投资产品与姓名、职业、地理位置相关
        "税务记录": ["姓名", "职业", "地理位置"],  # 税务记录与姓名、职业、地理位置相关
        "信用记录": ["姓名", "职业", "地理位置"],  # 信用记录与姓名、职业、地理位置相关
        "实体资产": ["姓名", "职业", "地理位置"],  # 实体资产与姓名、职业、地理位置相关
        "交易信息": ["姓名", "职业", "地理位置"],  # 交易信息与姓名、职业、地理位置相关
        
        # 医疗健康类
        "疾病": ["姓名", "职业"],  # 疾病与姓名、职业相关
        "药物": ["姓名", "职业"],  # 药物与姓名、职业相关
        "临床表现": ["姓名", "职业"],  # 临床表现与姓名、职业相关
        "医疗程序": ["姓名", "职业"],  # 医疗程序与姓名、职业相关
        "过敏信息": ["姓名", "职业"],  # 过敏信息与姓名、职业相关
        "生育信息": ["姓名", "职业"],  # 生育信息与姓名、职业相关
        
        # 个人位置类
        "地理位置": ["地理位置"],  # 直接匹配
        "行程信息": ["姓名", "地理位置"]  # 行程信息与姓名、地理位置相关
    }
    
    try:
        # 从合并数据集中加载数据
        merged_file = "format-dataset/all_datasets_merged.json"
        if os.path.exists(merged_file):
            with open(merged_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            # 为每个目标实体类型找到对应的示例
            entity_type_examples = {}
            
            for line in lines:
                try:
                    data = json.loads(line.strip())
                    if "text" in data and "label" in data:
                        text = data["text"]
                        labels = data["label"]
                        
                        # 检查是否包含目标实体类型或其映射类型
                        for label in labels:
                            if "type" in label:
                                entity_type = label["type"]
                                # 检查直接匹配
                                if entity_type in target_entity_types:
                                    if entity_type not in entity_type_examples:
                                        entity_type_examples[entity_type] = []
                                    entity_type_examples[entity_type].append(text)
                                    break
                                # 检查映射匹配
                                for target_type, mapped_types in entity_type_mapping.items():
                                    if entity_type in mapped_types and target_type in target_entity_types:
                                        if target_type not in entity_type_examples:
                                            entity_type_examples[target_type] = []
                                        entity_type_examples[target_type].append(text)
                                        break
                except:
                    continue
            
            # 为每个目标实体类型选择1个示例
            selected_count = 0
            for entity_type in target_entity_types:
                if entity_type in entity_type_examples and entity_type_examples[entity_type]:
                    # 随机选择1个示例
                    selected_example = random.choice(entity_type_examples[entity_type])
                    merged_examples.append(selected_example)
                    selected_count += 1
                    print(f"[✓] 为实体类型 '{entity_type}' 选择了示例：{selected_example[:50]}...")
                else:
                    print(f"[警告] 未找到实体类型 '{entity_type}' 的示例，将使用配置文件中的示例")
            
            print(f"[✓] 从合并数据集中为 {selected_count} 个实体类型选择了示例")
                    
    except Exception as e:
        print(f"[警告] 加载合并数据集示例失败：{e}")
    
    # 合并示例
    all_examples = config_examples + merged_examples
    
    # 去重并限制数量
    unique_examples = []
    seen = set()
    for example in all_examples:
        if example not in seen:
            unique_examples.append(example)
            seen.add(example)
    
    print(f"[✓] 总共加载了 {len(unique_examples)} 个示例句子")
    return unique_examples[:15]  # 最多返回15个示例

# =====================
# API调用函数
# =====================

def call_zhipu_api(prompt: str, max_retries: int = None, base_wait: int = None) -> str:
    """调用智谱API"""
    if max_retries is None:
        max_retries = API_RETRY_TIMES
    if base_wait is None:
        base_wait = API_WAIT_TIME
        
    payload = {
        "model": MODEL_NAME,
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.7,
        "top_p": 0.9,
        "max_tokens": 1024
    }

    for attempt in range(max_retries):
        try:
            response = requests.post(ZHIPU_API_URL, headers=headers, json=payload)
            response.raise_for_status()
            time.sleep(base_wait)  # 节流
            return response.json()["choices"][0]["message"]["content"]
        except Exception as e:
            wait_time = base_wait * (attempt + 1)
            print(f"[!] 调用失败（尝试 {attempt + 1}/{max_retries}）：{e}")
            print(f"    正在等待 {wait_time} 秒后重试...")
            time.sleep(wait_time)
    raise RuntimeError(f"[✗] 连续 {max_retries} 次调用失败")

# =====================
# 默认实体生成函数
# =====================

def generate_default_entities(entity_type: str, count: int = 20) -> List[str]:
    """
    生成默认实体作为回退方案
    
    Args:
        entity_type: 实体类型
        count: 生成数量
    
    Returns:
        默认实体列表
    """
    # 从实体schema中获取默认实体
    if entity_type in ENTITY_SCHEMA:
        schema_entities = ENTITY_SCHEMA[entity_type].get("examples", [])
        if schema_entities:
            print(f"[✓] 从schema中为{entity_type}获取了{len(schema_entities)}个默认实体")
            # 如果schema中的实体不够，通过API生成更多
            if len(schema_entities) < count:
                additional_count = count - len(schema_entities)
                try:
                    api_entities = generate_entities_via_api(entity_type, additional_count)
                    schema_entities.extend(api_entities)
                    print(f"[✓] 通过API为{entity_type}额外生成了{len(api_entities)}个实体")
                except Exception as e:
                    print(f"[警告] API生成实体失败：{e}")
            return schema_entities[:count]
    
    # 如果schema中没有，尝试通过API生成
    try:
        entities = generate_entities_via_api(entity_type, count)
        print(f"[✓] 通过API为{entity_type}生成了{len(entities)}个默认实体")
        return entities
    except Exception as e:
        print(f"[警告] API生成实体失败：{e}")
    
    # 最后的回退方案：使用硬编码的通用实体
    fallback_entities = get_hardcoded_fallback_entities(entity_type)
    print(f"[✓] 使用硬编码回退方案为{entity_type}提供了{len(fallback_entities)}个实体")
    return fallback_entities

def generate_entities_via_api(entity_type: str, count: int) -> List[str]:
    """通过API生成指定类型的实体"""
    prompt = f"""请生成{count}个中文的{entity_type}类型实体。

要求：
1. 每个实体一行
2. 实体要真实、常见
3. 不要包含编号或其他标记
4. 仅输出实体名称

示例格式：
实体1
实体2
实体3
"""
    
    try:
        response = call_zhipu_api(prompt)
        # 提取实体列表
        entities = []
        for line in response.strip().split('\n'):
            line = line.strip()
            if line and not line.startswith('#') and not line.startswith('//'):
                # 移除可能的编号
                import re
                line = re.sub(r'^\d+\.?\s*', '', line)
                line = re.sub(r'^[-•]\s*', '', line)
                if line:
                    entities.append(line)
        
        return entities[:count]
    except Exception as e:
        raise RuntimeError(f"API生成实体失败：{e}")

def get_hardcoded_fallback_entities(entity_type: str) -> List[str]:
    """获取硬编码的回退实体"""
    fallback_map = {
        # 个人信息类
        "姓名": ["张三", "李四", "王五", "赵六", "孙七", "周八", "吴九", "郑十"],
        "年龄": ["25岁", "30岁", "35岁", "40岁", "45岁", "28岁", "32岁", "38岁"],
        "性别": ["男", "女"],
        "国籍": ["中国", "美国", "日本", "韩国", "德国", "法国", "英国", "加拿大"],
        "职业": ["工程师", "医生", "教师", "律师", "设计师", "经理", "销售员", "程序员"],
        "民族": ["汉族", "回族", "蒙古族", "藏族", "维吾尔族", "苗族", "彝族", "壮族"],
        "教育背景": ["本科", "硕士", "博士", "高中", "中专", "大专", "博士后", "MBA"],
        "婚姻状况": ["未婚", "已婚", "离异", "丧偶"],
        "政治倾向": ["无党派", "共产党员", "民主党派", "群众"],
        "家庭成员": ["父亲", "母亲", "配偶", "子女", "兄弟", "姐妹"],
        
        # 金融财务类
        "工资数额": ["8000元", "12000元", "15000元", "20000元", "25000元", "30000元"],
        "投资产品": ["股票", "基金", "债券", "理财产品", "保险", "房地产投资"],
        "税务记录": ["个人所得税", "房产税", "车船税", "增值税"],
        "信用记录": ["优秀", "良好", "一般", "较差"],
        "实体资产": ["房产", "汽车", "股权", "珠宝", "艺术品"],
        "交易信息": ["转账", "消费", "投资", "还款", "收入"],
        
        # 医疗健康类
        "疾病": ["高血压", "糖尿病", "心脏病", "感冒", "发烧", "头痛"],
        "药物": ["阿司匹林", "青霉素", "维生素", "感冒药", "止痛药"],
        "临床表现": ["发热", "咳嗽", "头痛", "乏力", "恶心", "呕吐"],
        "医疗程序": ["体检", "手术", "化验", "CT检查", "核磁共振"],
        "过敏信息": ["药物过敏", "食物过敏", "花粉过敏", "尘螨过敏"],
        "生育信息": ["已育", "未育", "计划生育", "不孕不育"],
        
        # 位置信息类
        "地理位置": ["北京", "上海", "广州", "深圳", "杭州", "南京", "成都", "武汉"],
        "行程信息": ["出差", "旅游", "探亲", "商务", "学习", "会议"]
    }
    
    return fallback_map.get(entity_type, [f"{entity_type}实体{i+1}" for i in range(10)])

# =====================
# 句子生成函数
# =====================

def build_sentence_prompt(
    entity_type: str, 
    entities: List[str], 
    sentence_diversity: Dict[str, List[str]], 
    examples: List[str],
    generation_features: Dict = None
) -> str:
    """
    构建生成提示，根据配置决定使用哪些辅助功能
    
    Args:
        entity_type: 实体类型
        entities: 实体列表
        sentence_diversity: 句子多样化配置
        examples: 示例句子
        generation_features: 生成特征配置
    """
    if generation_features is None:
        generation_features = GENERATION_FEATURES
        
    # 判断是否使用实体多样化
    use_entity_diversity = generation_features.get("use_entity_diversity", True)
    use_sentence_diversity = generation_features.get("use_sentence_diversity", True)
    use_examples = generation_features.get("use_example_sentences", True)
    
    # 根据是否使用实体多样化构建不同的基础prompt
    if use_entity_diversity and entities:
        # 启用实体多样化：提供实体选择，但不强制使用
        prompt = f"请生成一个包含{entity_type}类型实体的自然中文句子。\n"
        prompt += f"可参考的{entity_type}实体：{', '.join(entities)}\n\n"
    else:
        # 禁用实体多样化：不提供具体实体，让模型自由生成
        prompt = f"请生成一个包含{entity_type}类型实体的自然中文句子。\n"
        prompt += f"句子中的{entity_type}实体必须是具体的、可标注的真实值，不要使用模糊描述。\n\n"
    
    # 根据配置添加句子多样化信息
    if use_sentence_diversity and sentence_diversity and entity_type in sentence_diversity:
            prompt += f"句子要求：\n"
            for attr, values in sentence_diversity[entity_type].items():
                if values:
                    prompt += f"- {attr}：{random.choice(values)}\n"
                prompt += "\n"
    
    # 根据配置添加示例句子
    if use_examples and examples:
        prompt += f"参考示例：\n"
        # 最多使用3个示例
        selected_examples = random.sample(examples, min(3, len(examples)))
        for example in selected_examples:
            prompt += f"- {example}\n"
        prompt += "\n"
    
    # 构建要求部分
    requirements = [
        "句子自然流畅，符合中文表达习惯",
        "句子中的实体必须是具体的、真实的值，便于准确标注",
        "避免使用模糊、抽象的描述",
        "确保句子长度适中（15-50字）"
    ]
    
    # 根据实体多样化配置调整要求
    if use_entity_diversity and entities:
        requirements.insert(1, f"合理选择是否使用提供的{entity_type}实体，确保语境自然，可以保留句子中本身的实体")
    else:
        requirements.insert(1, f"生成的{entity_type}实体要与句子语境匹配")
    
    prompt += "要求：\n"
    for i, req in enumerate(requirements, 1):
        prompt += f"{i}. {req}\n"
    
    return prompt

def generate_sentence(
    entity_type: str, 
    entities: List[str], 
    sentence_diversity: Dict[str, List[str]], 
    examples: List[str],
    generation_features: Dict = None
) -> str:
    """生成单个句子"""
    if generation_features is None:
        generation_features = GENERATION_FEATURES
        
    prompt = build_sentence_prompt(
        entity_type, 
        entities, 
        sentence_diversity, 
        examples,
        generation_features
    )
    
    response = call_zhipu_api(prompt)
    return response.strip()

# =====================
# 实体标注函数
# =====================

def find_entity_spans(text: str, entities: List[str], entity_type: str) -> List[Dict]:
    """调用智谱API进行实体标注，返回标准NER标注结果
    
    根据配置选择标注方法：
    - comprehensive: 多轮标注（先全面标注所有类型，再提取目标类型）
    - single: 单类型标注（只标注目标类型）
    """
    
    # 根据配置选择标注方法
    if ANNOTATION_METHOD == "comprehensive":
        return find_entity_spans_comprehensive(text, entities, entity_type)
    else:
        return find_entity_spans_single(text, entities, entity_type)

def find_entity_spans_comprehensive(text: str, entities: List[str], entity_type: str) -> List[Dict]:
    """多轮标注：先全面标注所有实体类型，再提取目标类型"""
    
    # 第一步：全面标注所有实体类型
    if entities:
        # 有提供实体列表的情况
        entity_list_str = '，'.join(entities)
        comprehensive_prompt = f"""你是一名中文信息抽取专家，请对下列句子进行全面的命名实体识别标注。

句子：{text}
目标实体类型：{entity_type}
参考实体列表：{entity_list_str}

请完成以下任务：

1. 主要任务：全面识别句子中所有类型的实体，包括但不限于：
   - 姓名、年龄、性别、国籍、职业、民族、教育背景、婚姻状况、政治倾向、家庭成员
   - 工资数额、投资产品、税务记录、信用记录、实体资产、交易信息
   - 疾病、药物、临床表现、医疗程序、过敏信息、生育信息
   - 地理位置、行程信息

2. 重点标注：优先标注{entity_type}类型的实体，确保不遗漏

3. 实体文本必须与句子中的字符完全一致，大小写、空格、标点均需匹配

4. 不要标注参考列表中未在句子中出现的实体

输出格式要求：
请严格按以下 JSON 数组格式输出：
[
  {{"entity": "实体文本", "start_idx": 起始位置, "end_idx": 结束位置, "type": "实体类型"}},
  ...
]

说明：
- 起始位置为实体在句子中的字符索引，索引从0开始，左闭右闭
- 实体类型必须是具体的类型名称（如"姓名"、"职业"、"地理位置"等）
- 若无实体，请输出空数组：[]
- 不要添加任何说明、注释或格式以外的内容。
"""
    else:
        # 没有提供实体列表的情况
        comprehensive_prompt = f"""你是一名中文信息抽取专家，请对下列句子进行全面的命名实体识别标注。

句子：{text}
目标实体类型：{entity_type}

请完成以下任务：

1. 主要任务：全面识别句子中所有类型的实体，包括但不限于：
   - 姓名、年龄、性别、国籍、职业、民族、教育背景、婚姻状况、政治倾向、家庭成员
   - 工资数额、投资产品、税务记录、信用记录、实体资产、交易信息
   - 疾病、药物、临床表现、医疗程序、过敏信息、生育信息
   - 地理位置、行程信息

2. 重点标注：优先标注{entity_type}类型的实体，确保不遗漏

3. 实体必须是具体的、可标注的真实值，不要标注模糊或抽象的描述

输出格式要求：
请严格按以下 JSON 数组格式输出：
[
  {{"entity": "实体文本", "start_idx": 起始位置, "end_idx": 结束位置, "type": "实体类型"}},
  ...
]

说明：
- 起始位置为实体在句子中的字符索引，索引从0开始，左闭右闭
- 实体类型必须是具体的类型名称（如"姓名"、"职业"、"地理位置"等）
- 若无实体，请输出空数组：[]
- 不要添加任何说明、注释或格式以外的内容。
"""
    
    try:
        # 调用API进行全面标注
        response = call_zhipu_api(comprehensive_prompt)
        
        # 提取JSON部分
        match = re.search(r'\[.*\]', response, re.DOTALL)
        if match:
            json_str = match.group(0)
        else:
            json_str = response.strip()
        
        all_spans = json.loads(json_str)
        
        # 第二步：从全面标注结果中提取目标类型的实体
        target_spans = []
        for span in all_spans:
            if all(k in span for k in ("entity", "start_idx", "end_idx", "type")):
                if span["type"] == entity_type:
                    target_spans.append({
                        "entity": span["entity"],
                        "start_idx": span["start_idx"],
                        "end_idx": span["end_idx"],
                        "type": span["type"]
                    })
        
        # 如果多轮标注没有找到目标类型实体，记录调试信息
        if not target_spans and all_spans:
            found_types = list(set(span.get("type", "") for span in all_spans if "type" in span))
            print(f"[调试] 多轮标注发现其他类型实体：{found_types}，但未找到目标类型：{entity_type}")
        
        return target_spans
        
    except Exception as e:
        print(f"[错误] 多轮标注失败：{e}")
        print(f"[信息] 回退到单类型标注")
        
        # 回退到单类型标注方法
        return find_entity_spans_single(text, entities, entity_type)

def find_entity_spans_single(text: str, entities: List[str], entity_type: str) -> List[Dict]:
    """单类型标注：只标注目标类型的实体"""
    if entities:
        # 有提供实体列表的情况
        entity_list_str = '，'.join(entities)
        prompt = f"""你是一名中文信息抽取专家，请对下列句子进行命名实体识别标注任务。

句子：{text}
目标实体类型：{entity_type}
参考实体列表：{entity_list_str}

请完成以下任务：

1. 只标注句子中真实出现的、属于{entity_type}类型的实体；
2. 实体文本必须与句子中的字符完全一致，大小写、空格、标点均需匹配；
3. 不要标注参考列表中未在句子中出现的实体；
4. 仅标注目标类型实体，忽略句中出现的其他类型信息。

输出格式要求：
请严格按以下 JSON 数组格式输出：
[
  {{"entity": "实体文本", "start_idx": 起始位置, "end_idx": 结束位置, "type": "{entity_type}"}},
  ...
]

说明：
- 起始位置为实体在句子中的字符索引，索引从0开始，左闭右闭（即包含起始和结束位置的字符）。
- 若无目标类型实体，请输出空数组：[]
- 不要添加任何说明、注释或格式以外的内容。
    """
    else:
        # 没有提供实体列表的情况
        prompt = f"""你是一名中文信息抽取专家，请对下列句子进行命名实体识别标注任务。

句子：{text}
目标实体类型：{entity_type}

请完成以下任务：

1. 只标注句子中真实出现的、属于{entity_type}类型的实体；
2. 实体文本必须与句子中的字符完全一致，大小写、空格、标点均需匹配；
3. 不要标注参考列表中未在句子中出现的实体；
4. 仅标注目标类型实体，忽略句中出现的其他类型信息。

输出格式要求：
请严格按以下 JSON 数组格式输出：
[
  {{"entity": "实体文本", "start_idx": 起始位置, "end_idx": 结束位置, "type": "{entity_type}"}},
  ...
]

说明：
- 起始位置为实体在句子中的字符索引，索引从0开始，左闭右闭（即包含起始和结束位置的字符）。
- 若无目标类型实体，请输出空数组：[]
- 不要添加任何说明、注释或格式以外的内容。
"""
    
    try:
        response = call_zhipu_api(prompt)
        # 只提取JSON部分
        match = re.search(r'\[.*\]', response, re.DOTALL)
        if match:
            json_str = match.group(0)
        else:
            json_str = response.strip()
        spans = json.loads(json_str)
        # 兼容部分模型输出带有多余字段
        result = []
        for span in spans:
            if all(k in span for k in ("entity", "start_idx", "end_idx", "type")):
                result.append({
                    "entity": span["entity"],
                    "start_idx": span["start_idx"],
                    "end_idx": span["end_idx"],
                    "type": span["type"]
                })
        return result
    except Exception as e:
        print(f"[错误] 智谱API实体标注失败：{e}")
        return []

def validate_and_fix_position(text: str, entity: str, start_idx: int, end_idx: int) -> Tuple[bool, Optional[Tuple[int, int]]]:
    """验证实体位置的准确性，如果不准确则尝试修正
    
    Args:
        text: 原文本
        entity: 实体文本
        start_idx: 起始位置（闭区间）
        end_idx: 结束位置（闭区间）
    
    Returns:
        (is_valid, fixed_position): 
        - is_valid: 位置是否有效
        - fixed_position: 如果位置无效但找到了正确位置，返回修正后的(start, end)，否则返回None
    """
    # 基本位置检查
    if start_idx < 0 or end_idx >= len(text) or start_idx > end_idx:
        # 尝试在文本中查找实体
        real_start = text.find(entity)
        if real_start != -1:
            real_end = real_start + len(entity) - 1
            print(f"[修正] 实体'{entity}'的位置从({start_idx},{end_idx})修正为({real_start},{real_end})")
            return False, (real_start, real_end)
        return False, None
    
    # 检查实体文本是否匹配
    text_span = text[start_idx:end_idx+1]
    if text_span != entity:
        # 尝试在文本中查找实体
        real_start = text.find(entity)
        if real_start != -1:
            real_end = real_start + len(entity) - 1  # 修正：使用闭区间
            print(f"[修正] 实体'{entity}'的位置从({start_idx},{end_idx})修正为({real_start},{real_end})")
            return False, (real_start, real_end)
        return False, None
    
    return True, None

def fix_entity_spans(text: str, spans: List[Dict]) -> List[Dict]:
    """修正一个句子中所有实体的位置
    
    Args:
        text: 原文本
        spans: 实体标注列表
    
    Returns:
        修正后的实体标注列表
    """
    fixed_spans = []
    for span in spans:
        entity = span["entity"]
        start_idx = span["start_idx"]
        end_idx = span["end_idx"]
        
        is_valid, fixed_position = validate_and_fix_position(text, entity, start_idx, end_idx)
        if is_valid:
            # 位置正确，直接添加
            fixed_spans.append(span)
        elif fixed_position:
            # 位置不正确但找到了正确位置
            fixed_span = span.copy()
            fixed_span["start_idx"], fixed_span["end_idx"] = fixed_position
            fixed_spans.append(fixed_span)
        else:
            # 位置不正确且找不到正确位置
            print(f"[警告] 无法找到实体'{entity}'的正确位置")
            continue
    
    return fixed_spans

def validate_annotation(text: str, spans: List[Dict]) -> Tuple[bool, Optional[List[Dict]]]:
    """验证标注的正确性，并在可能的情况下修正错误
    
    Args:
        text: 原文本
        spans: 实体标注列表
    
    Returns:
        (is_valid, fixed_spans):
        - is_valid: 标注是否有效
        - fixed_spans: 如果标注无效但可以修正，返回修正后的标注列表；否则返回None
    """
    if not spans:
        print(f"[警告] 未找到任何实体标注，句子：{text}")
        return False, None
    
    # 尝试修正所有实体的位置
    fixed_spans = fix_entity_spans(text, spans)
    
    # 如果没有任何实体被保留，说明所有实体都无效且无法修正
    if not fixed_spans:
        return False, None
    
    # 如果有实体被修正，返回修正后的结果
    if len(fixed_spans) != len(spans):
        print(f"[信息] 部分实体位置已修正，原有{len(spans)}个实体，保留{len(fixed_spans)}个有效实体")
        return False, fixed_spans
    
    # 检查是否所有实体都需要修正
    needs_correction = False
    for span in spans:
        is_valid, _ = validate_and_fix_position(text, span["entity"], span["start_idx"], span["end_idx"])
        if not is_valid:
            needs_correction = True
            break
    
    if needs_correction:
        return False, fixed_spans
    
    return True, None

# =====================
# 批量生成相关函数
# =====================

def calculate_batch_size(target_count: int, entity_type: str) -> int:
    """
    根据目标数量和实体类型计算合理的批量大小
    
    Args:
        target_count: 目标生成数量
        entity_type: 实体类型
        
    Returns:
        合理的批量大小
    """
    if not BATCH_GENERATION_CONFIG.get("enabled", True):
        return 1  # 禁用批量生成时返回1
    
    strategy = BATCH_GENERATION_CONFIG.get("batch_size_strategy", "adaptive")
    max_batch_size = BATCH_GENERATION_CONFIG.get("max_batch_size", 50)
    min_batch_size = BATCH_GENERATION_CONFIG.get("min_batch_size", 5)
    default_batch_size = BATCH_GENERATION_CONFIG.get("default_batch_size", 20)
    
    if strategy == "fixed":
        return min(max_batch_size, max(min_batch_size, default_batch_size))
    
    elif strategy == "adaptive":
        if target_count <= 20:
            return min(10, target_count)  # 小批量
        elif target_count <= 100:
            return min(20, target_count // 2)  # 中批量
        else:
            return min(max_batch_size, target_count // 3)  # 大批量
    
    elif strategy == "dynamic":
        # 根据实体类型动态调整
        if entity_type in ["行程信息", "地理位置"]:
            # 这些类型可能需要更长的句子，使用较小的批量
            return min(15, max(min_batch_size, target_count // 4))
        else:
            return min(max_batch_size, max(min_batch_size, target_count // 3))
    
    else:
        return default_batch_size

def build_batch_prompt(
    entity_type: str,
    batch_size: int,
    entities: List[str],
    sentence_diversity: Dict[str, List[str]],
    examples: List[str],
    generation_features: Dict = None
) -> str:
    """
    构建批量生成的Prompt
    
    Args:
        entity_type: 实体类型
        batch_size: 批量大小
        entities: 实体列表
        sentence_diversity: 句子多样化配置
        examples: 示例句子
        generation_features: 生成特征配置
        
    Returns:
        批量生成的prompt
    """
    if generation_features is None:
        generation_features = GENERATION_FEATURES
    
    # 基础prompt
    if generation_features.get("use_entity_diversity", True) and entities:
        prompt = f"请生成{batch_size}个包含{entity_type}类型实体的自然中文句子。\n\n"
        prompt += f"可参考的{entity_type}实体：{', '.join(entities[:10])}\n\n"  # 限制实体数量
    else:
        prompt = f"请生成{batch_size}个包含{entity_type}类型实体的自然中文句子。\n\n"
        prompt += f"句子中的{entity_type}实体必须是具体的、可标注的真实值，不要使用模糊描述。\n\n"
    
    # 添加句子多样化要求
    if generation_features.get("use_sentence_diversity", True) and sentence_diversity and entity_type in sentence_diversity:
        prompt += "句子要求：\n"
        for attr, values in sentence_diversity[entity_type].items():
            if values:
                selected_values = random.sample(values, min(3, len(values)))
                prompt += f"- {attr}：{', '.join(selected_values)}\n"
        prompt += "\n"
    
    # 添加示例句子
    if generation_features.get("use_example_sentences", True) and examples:
        prompt += "参考示例：\n"
        selected_examples = random.sample(examples, min(3, len(examples)))
        for example in selected_examples:
            prompt += f"- {example}\n"
        prompt += "\n"
    
    # 添加要求
    requirements = [
        "句子自然流畅，符合中文表达习惯",
        "句子中的实体必须是具体的、真实的值，便于准确标注",
        "避免使用模糊、抽象的描述",
        "确保句子长度适中（15-50字）",
        f"每个句子必须包含{entity_type}类型的实体"
    ]
    
    if generation_features.get("use_entity_diversity", True) and entities:
        requirements.insert(1, f"合理选择是否使用提供的{entity_type}实体，确保语境自然")
    else:
        requirements.insert(1, f"生成的{entity_type}实体要与句子语境匹配")
    
    prompt += "要求：\n"
    for i, req in enumerate(requirements, 1):
        prompt += f"{i}. {req}\n"
    
    prompt += f"\n请生成{batch_size}个句子，每个句子一行，不要编号。"
    
    return prompt

def generate_sentences_batch_optimized(
    entity_type: str,
    target_count: int,
    sentence_diversity: Dict[str, List[str]],
    vanilla_entities: Dict[str, List[str]],
    latent_entities: Dict[str, Dict[str, List[str]]],
    examples: List[str],
    generation_features: Dict = None
) -> List[str]:
    """
    批量生成句子优化版本
    
    Args:
        entity_type: 实体类型
        target_count: 目标生成数量
        sentence_diversity: 句子多样化配置
        vanilla_entities: 普通实体
        latent_entities: 潜在实体
        examples: 示例句子
        generation_features: 生成特征配置
        
    Returns:
        生成的句子列表
    """
    if generation_features is None:
        generation_features = GENERATION_FEATURES
    
    # 准备实体列表
    entities = []
    if generation_features.get("use_entity_diversity", True):
        if vanilla_entities.get(entity_type):
            entities.extend(vanilla_entities[entity_type])
        
        for attr_val, type_entities in latent_entities.items():
            if entity_type in type_entities:
                entities.extend(type_entities[entity_type])
        
        entities = list(set(entities))
        
        if not entities:
            entities = generate_default_entities(entity_type, max(target_count, 20))
        elif len(entities) < target_count:
            additional_entities = generate_default_entities(entity_type, target_count - len(entities))
            entities.extend(additional_entities)
            entities = list(set(entities))
    
    # 随机打乱实体顺序
    random.shuffle(entities)
    
    # 确保有足够的实体
    if len(entities) < target_count:
        if len(entities) == 0:
            # 如果没有实体，生成默认实体
            entities = generate_default_entities(entity_type, target_count)
        else:
            multiplier = (target_count // len(entities)) + 1
            entities = (entities * multiplier)[:target_count]
    
    all_sentences = []
    remaining_count = target_count
    
    while remaining_count > 0:
        # 计算当前批次的批量大小
        current_batch_size = min(calculate_batch_size(target_count, entity_type), remaining_count)
        
        # 选择实体
        if generation_features.get("use_entity_diversity", True) and entities:
            max_entities_per_batch = min(len(entities), 5)  # 每批次最多5个实体
            batch_entities = random.sample(entities, min(max_entities_per_batch, len(entities)))
        else:
            batch_entities = []
        
        # 构建批量prompt
        prompt = build_batch_prompt(
            entity_type,
            current_batch_size,
            batch_entities,
            sentence_diversity,
            examples,
            generation_features
        )
        
        try:
            # 调用API生成句子
            response = call_zhipu_api(prompt)
            
            # 解析生成的句子
            sentences = []
            for line in response.strip().split('\n'):
                line = line.strip()
                if line and not line.startswith('#') and not line.startswith('//'):
                    # 移除可能的编号
                    line = re.sub(r'^\d+\.?\s*', '', line)
                    line = re.sub(r'^[-•]\s*', '', line)
                    if line:
                        sentences.append(line)
            
            # 添加到总结果中
            all_sentences.extend(sentences[:current_batch_size])
            remaining_count -= len(sentences[:current_batch_size])
            
            print(f"[✓] 批量生成完成：{len(sentences)}个句子，剩余目标：{remaining_count}")
            
        except Exception as e:
            print(f"[错误] 批量生成失败：{e}")
            # 如果批量生成失败，回退到单个生成
            for _ in range(current_batch_size):
                try:
                    sentence = generate_sentence(
                        entity_type=entity_type,
                        entities=batch_entities,
                        sentence_diversity=sentence_diversity,
                        examples=examples,
                        generation_features=generation_features
                    )
                    all_sentences.append(sentence)
                    remaining_count -= 1
                except Exception as e2:
                    print(f"[错误] 单个句子生成也失败：{e2}")
                    remaining_count -= 1
    
    return all_sentences[:target_count]

def create_filter_chain() -> FilterChain:
    """创建筛选器链"""
    filters = []
    
    # 添加长度筛选器
    if SENTENCE_FILTER_CONFIG.get("enabled", True):
        length_filter_config = SENTENCE_FILTER_CONFIG.get("length_filter", {})
        if length_filter_config.get("enabled", True):
            filters.append(LengthFilter(length_filter_config))
        
        # 添加自然度筛选器
        naturalness_filter_config = SENTENCE_FILTER_CONFIG.get("naturalness_filter", {})
        if naturalness_filter_config.get("enabled", True):
            filters.append(NaturalnessFilter(naturalness_filter_config))
    
    return FilterChain(filters)

# =====================
# 主生成函数
# =====================

def generate_ner_data(
    entity_type: str,
    target_count: int,
    sentence_diversity: Dict[str, List[str]],
    vanilla_entities: Dict[str, List[str]],
    latent_entities: Dict[str, Dict[str, List[str]]],
    examples: List[str],
    generation_features: Dict = None,
    batch_size: int = 5
) -> List[Dict]:
    """
    生成NER数据，根据配置决定使用哪些辅助功能
    
    Args:
        entity_type: 实体类型
        target_count: 目标生成数量
        sentence_diversity: 句子多样化配置
        vanilla_entities: 普通实体
        latent_entities: 潜在实体
        examples: 示例句子
        generation_features: 生成特征配置
        batch_size: 批处理大小（已弃用，使用批量生成配置）
    """
    if generation_features is None:
        generation_features = GENERATION_FEATURES
        
    print(f"[*] 开始生成 {entity_type} 类型数据，目标数量：{target_count}")
    
    # 检查是否启用批量生成
    if BATCH_GENERATION_CONFIG.get("enabled", True):
        print(f"[*] 使用批量生成模式")
        return generate_ner_data_batch_mode(
            entity_type, target_count, sentence_diversity, vanilla_entities, 
            latent_entities, examples, generation_features
        )
    else:
        print(f"[*] 使用单句生成模式")
        return generate_ner_data_single_mode(
            entity_type, target_count, sentence_diversity, vanilla_entities,
            latent_entities, examples, generation_features
        )

def generate_ner_data_batch_mode(
    entity_type: str,
    target_count: int,
    sentence_diversity: Dict[str, List[str]],
    vanilla_entities: Dict[str, List[str]],
    latent_entities: Dict[str, Dict[str, List[str]]],
    examples: List[str],
    generation_features: Dict = None
) -> List[Dict]:
    """
    批量模式生成NER数据
    
    新流程：
    1. 批量生成句子
    2. 应用句子筛选器
    3. 对筛选后的句子进行实体标注
    4. 如果数量不足，重复步骤1-3
    """
    if generation_features is None:
        generation_features = GENERATION_FEATURES
    
    results = []
    total_generated = 0
    total_filtered = 0
    total_annotated = 0
    
    # 创建筛选器链
    filter_chain = create_filter_chain()
    
    # 计算需要生成的句子数量（考虑筛选率）
    quality_thresholds = SENTENCE_FILTER_CONFIG.get("quality_thresholds", {})
    min_pass_rate = quality_thresholds.get("min_pass_rate", 0.6)
    estimated_generation_count = int(target_count / min_pass_rate) + 10  # 额外生成10个作为缓冲
    
    print(f"[*] 预计生成 {estimated_generation_count} 个句子（考虑筛选率）")
    
    while len(results) < target_count and total_generated < estimated_generation_count * 2:
        # 计算当前需要生成的句子数量
        remaining_needed = target_count - len(results)
        current_batch_size = min(calculate_batch_size(target_count, entity_type), remaining_needed * 2)
        
        print(f"[*] 生成第 {total_generated + 1}-{total_generated + current_batch_size} 个句子")
        
        # 步骤1：批量生成句子
        try:
            sentences = generate_sentences_batch_optimized(
                entity_type=entity_type,
                target_count=current_batch_size,
                sentence_diversity=sentence_diversity,
                vanilla_entities=vanilla_entities,
                latent_entities=latent_entities,
                examples=examples,
                generation_features=generation_features
            )
            total_generated += len(sentences)
            print(f"[✓] 批量生成完成：{len(sentences)}个句子")
            
        except Exception as e:
            print(f"[错误] 批量生成失败：{e}")
            continue
        
        # 步骤2：应用句子筛选器
        if sentences:
            filtered_sentences, filter_stats = filter_chain.apply_filters(sentences, entity_type)
            total_filtered += len(filtered_sentences)
            
            print(f"[✓] 句子筛选完成：")
            print(f"  - 原始句子：{len(sentences)}个")
            print(f"  - 筛选后：{len(filtered_sentences)}个")
            print(f"  - 筛选率：{filter_stats.get('overall_rejection_rate', 0):.2%}")
            
            # 检查筛选结果是否满足质量要求
            if filter_stats.get('overall_rejection_rate', 0) > quality_thresholds.get("max_rejection_rate", 0.4):
                print(f"[警告] 筛选率过高：{filter_stats.get('overall_rejection_rate', 0):.2%}")
        
        # 步骤3：对筛选后的句子进行实体标注
        if filtered_sentences:
            for sentence in filtered_sentences:
                try:
                    # 选择实体（用于标注）
                    batch_entities = []
                    if generation_features.get("use_entity_diversity", True):
                        if vanilla_entities.get(entity_type):
                            batch_entities.extend(vanilla_entities[entity_type])
                        for attr_val, type_entities in latent_entities.items():
                            if entity_type in type_entities:
                                batch_entities.extend(type_entities[entity_type])
                        batch_entities = list(set(batch_entities))
                    
                    # 标注实体
                    spans = find_entity_spans(sentence, batch_entities, entity_type)
                    
                    if spans:  # 只添加成功标注的句子
                        results.append({
                            "text": sentence,
                            "label": spans
                        })
                        total_annotated += 1
                    else:
                        print(f"[警告] 句子无法标注实体，跳过：{sentence[:50]}...")
                        
                except Exception as e:
                    print(f"[错误] 实体标注失败：{e}")
                    continue
        
        print(f"[*] 当前进度：已生成 {len(results)}/{target_count} 个有效数据")
    
    print(f"[✓] 批量模式生成完成：")
    print(f"  - 总生成句子：{total_generated}个")
    print(f"  - 筛选后句子：{total_filtered}个")
    print(f"  - 成功标注：{total_annotated}个")
    print(f"  - 最终结果：{len(results)}个")
    
    return results[:target_count]

def generate_ner_data_single_mode(
    entity_type: str,
    target_count: int,
    sentence_diversity: Dict[str, List[str]],
    vanilla_entities: Dict[str, List[str]],
    latent_entities: Dict[str, Dict[str, List[str]]],
    examples: List[str],
    generation_features: Dict = None
) -> List[Dict]:
    """
    单句模式生成NER数据（原有逻辑）
    """
    if generation_features is None:
        generation_features = GENERATION_FEATURES
        
    results = []
    
    # 根据配置决定使用哪些实体
    entities = []
    
    if generation_features.get("use_entity_diversity", True):
        # 使用实体多样化数据
        if vanilla_entities.get(entity_type):
            entities.extend(vanilla_entities[entity_type])
            print(f"[✓] 从vanilla实体获取了{len(vanilla_entities[entity_type])}个{entity_type}实体")
        
        # 如果有潜在实体，添加到实体列表中
        for attr_val, type_entities in latent_entities.items():
            if entity_type in type_entities:
                entities.extend(type_entities[entity_type])
                print(f"[✓] 从latent实体({attr_val})获取了{len(type_entities[entity_type])}个{entity_type}实体")
        
        # 去重
        entities = list(set(entities))
        print(f"[✓] 去重后共获得{len(entities)}个{entity_type}实体")
    
        # 如果启用了实体多样化但实体不够，使用默认实体生成
        if not entities:
            print(f"[!] {entity_type} 类型没有可用的多样化实体，将使用默认实体生成方案")
            entities = generate_default_entities(entity_type, max(target_count, 20))
        elif len(entities) < target_count:
            print(f"[!] {entity_type} 类型实体数量不足({len(entities)}<{target_count})，补充默认实体")
            additional_entities = generate_default_entities(entity_type, target_count - len(entities))
            entities.extend(additional_entities)
            # 再次去重
            entities = list(set(entities))
    
    # 随机打乱实体顺序
    random.shuffle(entities)
    # 确保有足够的实体用于生成
    if len(entities) < target_count:
        if len(entities) == 0:
            # 如果没有实体，生成默认实体
            entities = generate_default_entities(entity_type, target_count)
        else:
            # 循环使用现有实体
            multiplier = (target_count // len(entities)) + 1
            entities = (entities * multiplier)[:target_count]
        print(f"[*] 循环使用实体，最终实体数量：{len(entities)}")
    else:
        # 禁用实体多样化：不提供任何实体，让模型完全自由生成
        print(f"[*] 实体多样化已禁用，将让模型自由生成{entity_type}实体，不提供预设实体列表")
    
    # 调整生成策略：每次生成一个句子，不再按大批次处理
    print(f"[*] 开始逐句生成，目标数量：{target_count}")
    
    for i in tqdm(range(target_count), desc=f"生成包含 {entity_type} 实体的文本数据"):
        # 根据配置选择实体策略
        if generation_features.get("use_entity_diversity", True):
            if entities:
                # 启用实体多样化且有可用实体：从实体池中随机选择一些实体
                max_entities_per_sentence = min(len(entities), 3)  # 每句最多3个实体
                batch_entities = random.sample(entities, min(max_entities_per_sentence, len(entities)))
            else:
                # 启用实体多样化但没有可用实体：不提供具体实体
                batch_entities = []
        else:
            # 禁用实体多样化：不提供任何实体，让模型完全自由生成
            batch_entities = []
        
        # 生成单个句子
        try:
            sentence = generate_sentence(
                entity_type=entity_type,
                entities=batch_entities,
                sentence_diversity=sentence_diversity if generation_features.get("use_sentence_diversity", True) else {},
                examples=examples if generation_features.get("use_example_sentences", True) else [],
                generation_features=generation_features
            )
            
            # 标注实体
            spans = find_entity_spans(sentence, batch_entities if batch_entities else [], entity_type)
            
            if spans:  # 只添加成功标注的句子
                results.append({
                    "text": sentence,
                    "label": spans
                })
            else:
                print(f"[警告] 句子无法标注实体，跳过：{sentence[:50]}...")
                
        except Exception as e:
            print(f"[错误] 生成第{i+1}个句子时失败：{e}")
            continue
    
    return results[:target_count]

def generate_sentences_batch(
    entity_type: str,
    entity_batches: List[List[str]],
    sentence_diversity: Dict[str, List[str]],
    examples: List[str],
    generation_features: Dict = None,
    batch_size: int = 5
) -> List[str]:
    """批量生成句子
    
    Args:
        entity_type: 实体类型
        entity_batches: 实体批次列表
        sentence_diversity: 句子多样化配置
        examples: 示例句子
        generation_features: 生成特征配置
        batch_size: 批处理大小
    
    Returns:
        生成的句子列表
    """
    if generation_features is None:
        generation_features = GENERATION_FEATURES
        
    sentences = []
    for entities in tqdm(entity_batches, desc=f"生成{entity_type}的句子"):
        try:
            sentence = generate_sentence(
                entity_type=entity_type,
                entities=entities,
                sentence_diversity=sentence_diversity,
                examples=examples,
                generation_features=generation_features
            )
            sentences.append(sentence)
        except Exception as e:
            print(f"生成句子时出错: {e}")
            continue
            
    return sentences

def find_entity_spans_batch(
    sentences: List[str],
    entity_batches: List[List[str]],
    entity_type: str
) -> List[List[Dict]]:
    """批量查找实体位置
    
    Args:
        sentences: 句子列表
        entity_batches: 实体批次列表
        entity_type: 实体类型
    
    Returns:
        每个句子中实体位置的列表
    """
    all_spans = []
    for sentence, entities in zip(sentences, entity_batches):
        try:
            spans = find_entity_spans(sentence, entities, entity_type)
            all_spans.append(spans)
        except Exception as e:
            print(f"查找实体位置时出错: {e}")
            all_spans.append([])
            
    return all_spans

def save_intermediate_results(
    entity_type: str,
    data: List[Dict],
    output_dir: str = None,
    timestamp: str = None
) -> None:
    """保存中间结果
    
    Args:
        entity_type: 实体类型
        data: 要保存的数据
        output_dir: 输出目录（默认使用当前时间戳的runs目录）
        timestamp: 时间戳（默认使用当前时间）
    """
    if output_dir is None:
        output_dir = get_output_dir(timestamp)
    if timestamp is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    # 创建中间结果目录结构
    intermediate_dir = os.path.join(output_dir, "intermediate")
    raw_dir = os.path.join(intermediate_dir, "raw")
    by_entity_dir = os.path.join(intermediate_dir, "by_entity")
    processed_dir = os.path.join(intermediate_dir, "processed")
    
    # 确保所有目录都存在
    for dir_path in [output_dir, intermediate_dir, raw_dir, by_entity_dir, processed_dir]:
        os.makedirs(dir_path, exist_ok=True)
        
    # 保存原始生成数据
    raw_file = os.path.join(raw_dir, f"{timestamp}_raw_generated_data.json")
    with open(raw_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
        
    # 按实体类型保存
    entity_file = os.path.join(by_entity_dir, f"ner_dataset_{entity_type}_{timestamp}.json")
    with open(entity_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
        
    # 保存处理后的数据
    processed_file = os.path.join(processed_dir, f"ner_dataset_{entity_type}_{timestamp}.json")
    with open(processed_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
        
    print(f"[✓] 已保存{entity_type}的中间结果到：{intermediate_dir}")
    print(f"    - 原始数据：{raw_file}")
    print(f"    - 实体数据：{entity_file}")
    print(f"    - 处理数据：{processed_file}")

# =====================
# 主流程
# =====================

def main(strategy_dir="reproduce"):
    """主函数"""
    print("[1/4] 加载配置和数据...")
    
    # 生成时间戳和输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = get_output_dir(timestamp)
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"输出目录：{output_dir}")
    
    # 读取生成特征配置
    generation_features = ner_config.get("generation_features", {
        "use_sentence_diversity": True,
        "use_entity_diversity": True,
        "use_example_sentences": True
    })
    
    print(f"生成特征配置：")
    print(f"- 实体多样化：{'启用' if generation_features.get('use_entity_diversity', True) else '禁用'}")
    print(f"- 句子多样化：{'启用' if generation_features.get('use_sentence_diversity', True) else '禁用'}")
    print(f"- 示例句子：{'启用' if generation_features.get('use_example_sentences', True) else '禁用'}")
    print(f"- 标注方法：{'多轮标注' if ANNOTATION_METHOD == 'comprehensive' else '单类型标注'}")
    print(f"- 批量生成：{'启用' if BATCH_GENERATION_CONFIG.get('enabled', True) else '禁用'}")
    print(f"- 句子筛选：{'启用' if SENTENCE_FILTER_CONFIG.get('enabled', True) else '禁用'}")
    print(f"- 并行处理：{'启用' if PARALLEL_CONFIG.get('enabled', True) else '禁用'}")
    print(f"- 失败重试：{'启用' if RETRY_CONFIG.get('max_retries', 0) > 0 else '禁用'}")
    print(f"- 日志记录：{'启用' if LOG_CONFIG.get('enabled', True) else '禁用'}")
    
    # 显示并行处理配置
    if PARALLEL_CONFIG.get("enabled", True):
        print(f"  并行策略：{PARALLEL_CONFIG.get('strategy', 'hybrid')}")
        print(f"  最大进程数：{PARALLEL_CONFIG.get('max_processes', 4)}")
        print(f"  实体类型批处理大小：{PARALLEL_CONFIG.get('entity_type_batch_size', 2)}")
    
    # 显示重试配置
    if RETRY_CONFIG.get("max_retries", 0) > 0:
        print(f"  最大重试次数：{RETRY_CONFIG.get('max_retries', 3)}")
        print(f"  基础延迟时间：{RETRY_CONFIG.get('base_delay', 1.0)}秒")
        print(f"  最大延迟时间：{RETRY_CONFIG.get('max_delay', 60.0)}秒")
    
    # 显示筛选器配置
    if SENTENCE_FILTER_CONFIG.get("enabled", True):
        print(f"  启用的筛选器：{', '.join(SENTENCE_FILTER_CONFIG.get('filters', []))}")
        if "length" in SENTENCE_FILTER_CONFIG.get("filters", []):
            length_config = SENTENCE_FILTER_CONFIG.get("length_filter", {})
            print(f"  长度筛选：{length_config.get('min_length', 15)}-{length_config.get('max_length', 80)}字符")
        if "naturalness" in SENTENCE_FILTER_CONFIG.get("filters", []):
            naturalness_config = SENTENCE_FILTER_CONFIG.get("naturalness_filter", {})
            print(f"  自然度筛选：阈值 {naturalness_config.get('threshold', 6.0)}分")
    
    # 加载目标分布（必需）
    try:
        target_distribution = get_latest_target_file(strategy_dir)
        print(f"[✓] 已加载目标分布：{len(target_distribution)}个实体类型")
    except Exception as e:
        print(f"[✗] 加载目标分布失败：{e}")
        print("无法继续，程序退出")
        return
    
    # 加载句子多样化配置（可选）
    sentence_diversity = {}
    if generation_features.get("use_sentence_diversity", True):
        try:
            sentence_diversity = load_sentence_diversity(strategy_dir)
            print(f"[✓] 已加载句子多样化配置：{len(sentence_diversity)}个实体类型")
        except Exception as e:
            print(f"[!] 加载句子多样化配置失败：{e}")
            print(f"[!] 将在不使用句子多样化的情况下继续生成")
            generation_features["use_sentence_diversity"] = False
    else:
        print("[*] 句子多样化功能已禁用")
    
    # 加载实体多样化数据（可选）
    vanilla_entities = {}
    latent_entities = {}
    if generation_features.get("use_entity_diversity", True):
        try:
            vanilla_entities, latent_entities = get_latest_entity_diversity(strategy_dir)
            vanilla_count = sum(len(entities) for entities in vanilla_entities.values())
            latent_count = sum(len(type_entities) for attr_entities in latent_entities.values() 
                             for type_entities in attr_entities.values())
            print(f"[✓] 已加载实体多样化数据：{vanilla_count}个vanilla实体，{latent_count}个latent实体")
        except Exception as e:
            print(f"[!] 加载实体多样化数据失败：{e}")
            print(f"[!] 将使用默认实体生成方案")
            generation_features["use_entity_diversity"] = False
    else:
        print("[*] 实体多样化功能已禁用")
    
    # 加载示例句子（可选）
    examples = []
    if generation_features.get("use_example_sentences", True):
        try:
            examples = load_example_sentences(strategy_dir)
            print(f"[✓] 已加载示例句子：{len(examples)}个")
        except Exception as e:
            print(f"[!] 加载示例句子失败：{e}")
            print(f"[!] 将在不使用示例句子的情况下继续生成")
            generation_features["use_example_sentences"] = False
    else:
        print("[*] 示例句子功能已禁用")
    
    print(f"\n[2/4] 开始生成数据...")
    print(f"实际使用的生成特征：")
    print(f"- 实体多样化：{'启用' if generation_features.get('use_entity_diversity', False) else '禁用'}")
    print(f"- 句子多样化：{'启用' if generation_features.get('use_sentence_diversity', False) else '禁用'}")
    print(f"- 示例句子：{'启用' if generation_features.get('use_example_sentences', False) else '禁用'}")
    
    # 使用并行处理生成数据
    start_time = datetime.now()
    
    try:
        all_results = generate_ner_data_parallel(
            target_distribution=target_distribution,
            sentence_diversity=sentence_diversity,
            vanilla_entities=vanilla_entities,
            latent_entities=latent_entities,
            examples=examples,
            generation_features=generation_features
        )
        
        end_time = datetime.now()
        generation_time = (end_time - start_time).total_seconds()
        
        print(f"\n[✓] 数据生成完成，耗时：{generation_time:.2f}秒")
        
        # 统计结果
        total_generated = sum(len(results) for results in all_results.values())
        successful_types = len([results for results in all_results.values() if results])
        failed_types = len(target_distribution) - successful_types
        
        print(f"[统计] 生成结果：")
        print(f"  - 总实体类型：{len(target_distribution)}个")
        print(f"  - 成功生成：{successful_types}个类型")
        print(f"  - 失败生成：{failed_types}个类型")
        print(f"  - 总生成数据：{total_generated}个样本")
        
        # 显示每个实体类型的生成结果
        for entity_type, results in all_results.items():
            target_count = target_distribution.get(entity_type, 0)
            actual_count = len(results)
            success_rate = (actual_count / target_count * 100) if target_count > 0 else 0
            print(f"  - {entity_type}: {actual_count}/{target_count} ({success_rate:.1f}%)")
        
    except Exception as e:
        print(f"[错误] 并行数据生成失败：{e}")
        print("[信息] 回退到串行处理...")
        
        # 回退到串行处理
        all_results = generate_ner_data_sequential(
            target_distribution=target_distribution,
            sentence_diversity=sentence_diversity,
            vanilla_entities=vanilla_entities,
            latent_entities=latent_entities,
            examples=examples,
            generation_features=generation_features
        )
    
    print(f"\n[3/4] 保存最终结果...")
    
    # 保存最终结果
    output_file = os.path.join(output_dir, f"ner_dataset_{timestamp}.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    # 保存中间结果（按实体类型）
    for entity_type, results in all_results.items():
        if results:  # 只保存有结果的实体类型
            save_intermediate_results(entity_type, results, output_dir, timestamp)
    
    # 统计最终结果
    total_generated = sum(len(results) for results in all_results.values())
    successful_types = len([results for results in all_results.values() if results])
    failed_types = [entity_type for entity_type, results in all_results.items() if not results]
    
    print(f"\n[4/4] 生成完成！")
    print(f"- 总共生成数据：{total_generated} 条")
    print(f"- 成功生成类型：{successful_types} 个")
    print(f"- 失败类型：{len(failed_types)} 个")
    if failed_types:
        print(f"- 失败类型列表：{failed_types}")
    print(f"- 输出文件：{output_file}")
    
    # 输出各个特征的最终使用情况
    print("\n最终生成特征使用情况：")
    print(f"- 句子多样化：{'已启用' if generation_features.get('use_sentence_diversity', False) else '未启用'}")
    print(f"- 实体多样化：{'已启用' if generation_features.get('use_entity_diversity', False) else '未启用'}")
    print(f"- 示例句子：{'已启用' if generation_features.get('use_example_sentences', False) else '未启用'}")
    print(f"- 并行处理：{'已启用' if PARALLEL_CONFIG.get('enabled', True) else '未启用'}")
    print(f"- 失败重试：{'已启用' if RETRY_CONFIG.get('max_retries', 0) > 0 else '未启用'}")
    
    return output_file, total_generated, failed_types

# =====================
# 重试逻辑和异步处理
# =====================

def should_retry_error(error: Exception) -> bool:
    """判断错误是否应该重试"""
    if isinstance(error, requests.exceptions.RequestException):
        # 网络错误应该重试
        return True
    
    if hasattr(error, 'response') and error.response is not None:
        status_code = str(error.response.status_code)
        return status_code in RETRY_CONFIG.get("retryable_errors", [])
    
    return False

def calculate_retry_delay(retry_count: int) -> float:
    """计算重试延迟时间"""
    base_delay = RETRY_CONFIG.get("base_delay", 1.0)
    backoff_factor = RETRY_CONFIG.get("backoff_factor", 2.0)
    max_delay = RETRY_CONFIG.get("max_delay", 60.0)
    
    delay = base_delay * (backoff_factor ** retry_count)
    return min(delay, max_delay)

def call_zhipu_api_with_retry(prompt: str, task_id: str = None, max_retries: int = None, base_wait: int = None) -> str:
    """
    带重试逻辑的API调用
    
    Args:
        prompt: 请求prompt
        task_id: 任务ID（用于日志记录）
        max_retries: 最大重试次数
        base_wait: 基础等待时间
        
    Returns:
        API响应内容
    """
    if max_retries is None:
        max_retries = RETRY_CONFIG.get("max_retries", 3)
    if base_wait is None:
        base_wait = RETRY_CONFIG.get("base_delay", 1.5)
    
    for attempt in range(max_retries + 1):
        try:
            if task_id:
                task_manager.update_task_status(task_id, TaskStatus.RUNNING)
            
            response = call_zhipu_api(prompt, max_retries=0, base_wait=base_wait)
            
            if task_id:
                task_manager.update_task_status(task_id, TaskStatus.SUCCESS)
            
            return response
            
        except Exception as e:
            if task_id:
                task_manager.log_task_error(task_id, e, {"prompt_length": len(prompt)})
            
            # 判断是否应该重试
            if attempt < max_retries and should_retry_error(e):
                delay = calculate_retry_delay(attempt)
                print(f"[警告] API调用失败（尝试 {attempt + 1}/{max_retries + 1}）：{e}")
                print(f"[信息] {delay}秒后重试...")
                
                if task_id:
                    task_manager.update_task_status(task_id, TaskStatus.RETRYING)
                
                time.sleep(delay)
                continue
            else:
                if task_id:
                    task_manager.update_task_status(task_id, TaskStatus.FAILED, str(e))
                raise e
    
    raise Exception(f"API调用失败，已重试{max_retries}次")

async def call_zhipu_api_async(prompt: str, task_id: str = None) -> str:
    """
    异步API调用
    
    Args:
        prompt: 请求prompt
        task_id: 任务ID
        
    Returns:
        API响应内容
    """
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(
        None, 
        call_zhipu_api_with_retry, 
        prompt, 
        task_id
    )

def process_entity_type_parallel(
    entity_type: str,
    target_count: int,
    sentence_diversity: Dict[str, List[str]],
    vanilla_entities: Dict[str, List[str]],
    latent_entities: Dict[str, Dict[str, List[str]]],
    examples: List[str],
    generation_features: Dict = None
) -> Tuple[str, List[Dict]]:
    """
    并行处理单个实体类型
    
    Args:
        entity_type: 实体类型
        target_count: 目标数量
        sentence_diversity: 句子多样化配置
        vanilla_entities: 普通实体
        latent_entities: 潜在实体
        examples: 示例句子
        generation_features: 生成特征配置
        
    Returns:
        (实体类型, 生成结果)
    """
    try:
        print(f"[进程] 开始处理实体类型: {entity_type}")
        
        # 创建任务
        task_id = task_manager.create_task(
            entity_type, 
            "entity_generation", 
            {"target_count": target_count}
        )
        
        # 生成数据
        results = generate_ner_data(
            entity_type=entity_type,
            target_count=target_count,
            sentence_diversity=sentence_diversity,
            vanilla_entities=vanilla_entities,
            latent_entities=latent_entities,
            examples=examples,
            generation_features=generation_features
        )
        
        print(f"[进程] 完成处理实体类型: {entity_type}，生成 {len(results)} 个结果")
        return entity_type, results
        
    except Exception as e:
        print(f"[错误] 处理实体类型 {entity_type} 失败: {e}")
        task_manager.log_task_error(task_id, e)
        return entity_type, []

def generate_ner_data_parallel(
    target_distribution: Dict[str, int],
    sentence_diversity: Dict[str, List[str]],
    vanilla_entities: Dict[str, List[str]],
    latent_entities: Dict[str, Dict[str, List[str]]],
    examples: List[str],
    generation_features: Dict = None
) -> Dict[str, List[Dict]]:
    """
    并行生成NER数据
    
    Args:
        target_distribution: 目标分布
        sentence_diversity: 句子多样化配置
        vanilla_entities: 普通实体
        latent_entities: 潜在实体
        examples: 示例句子
        generation_features: 生成特征配置
        
    Returns:
        所有实体类型的生成结果
    """
    if not PARALLEL_CONFIG.get("enabled", True):
        print("[信息] 并行处理已禁用，使用串行处理")
        return generate_ner_data_sequential(
            target_distribution, sentence_diversity, vanilla_entities,
            latent_entities, examples, generation_features
        )
    
    strategy = PARALLEL_CONFIG.get("strategy", "hybrid")
    max_processes = PARALLEL_CONFIG.get("max_processes", 4)
    entity_type_batch_size = PARALLEL_CONFIG.get("entity_type_batch_size", 2)
    
    print(f"[信息] 使用并行策略: {strategy}")
    print(f"[信息] 最大进程数: {max_processes}")
    print(f"[信息] 实体类型批处理大小: {entity_type_batch_size}")
    
    all_results = {}
    
    if strategy == "process":
        # 多进程策略
        with ProcessPoolExecutor(max_workers=max_processes) as executor:
            # 提交所有任务
            future_to_entity = {
                executor.submit(
                    process_entity_type_parallel,
            entity_type,
            target_count,
            sentence_diversity,
            vanilla_entities,
            latent_entities,
            examples,
            generation_features
                ): entity_type
                for entity_type, target_count in target_distribution.items()
            }
            
            # 收集结果
            for future in as_completed(future_to_entity):
                entity_type = future_to_entity[future]
                try:
                    result_entity_type, results = future.result()
                    all_results[result_entity_type] = results
                    print(f"[✓] 实体类型 {result_entity_type} 处理完成")
                except Exception as e:
                    print(f"[错误] 实体类型 {entity_type} 处理失败: {e}")
                    all_results[entity_type] = []
    
    elif strategy == "hybrid":
        # 混合策略：多进程处理实体类型，异步处理单个实体类型内部
        with ProcessPoolExecutor(max_workers=max_processes) as executor:
            # 分批处理实体类型
            entity_types = list(target_distribution.keys())
            for i in range(0, len(entity_types), entity_type_batch_size):
                batch = entity_types[i:i + entity_type_batch_size]
                
                # 提交批次任务
                future_to_entity = {
                    executor.submit(
                        process_entity_type_parallel,
                        entity_type,
                        target_distribution[entity_type],
                        sentence_diversity,
                        vanilla_entities,
                        latent_entities,
                        examples,
                        generation_features
                    ): entity_type
                    for entity_type in batch
                }
                
                # 收集批次结果
                for future in as_completed(future_to_entity):
                    entity_type = future_to_entity[future]
                    try:
                        result_entity_type, results = future.result()
                        all_results[result_entity_type] = results
                        print(f"[✓] 实体类型 {result_entity_type} 处理完成")
                    except Exception as e:
                        print(f"[错误] 实体类型 {entity_type} 处理失败: {e}")
                        all_results[entity_type] = []
    
    else:  # async
        # 异步策略（在单个进程中）
        print("[信息] 使用异步策略")
        all_results = generate_ner_data_sequential(
            target_distribution, sentence_diversity, vanilla_entities,
            latent_entities, examples, generation_features
        )
    
    # 打印任务统计
    stats = task_manager.get_task_stats()
    print(f"[统计] 任务完成情况: {stats}")
    
    return all_results

def generate_ner_data_sequential(
    target_distribution: Dict[str, int],
    sentence_diversity: Dict[str, List[str]],
    vanilla_entities: Dict[str, List[str]],
    latent_entities: Dict[str, Dict[str, List[str]]],
    examples: List[str],
    generation_features: Dict = None
) -> Dict[str, List[Dict]]:
    """
    串行生成NER数据（原有逻辑）
    """
    all_results = {}
    
    for entity_type, target_count in target_distribution.items():
        try:
            print(f"[*] 开始生成 {entity_type} 类型数据，目标数量：{target_count}")
            
            results = generate_ner_data(
                entity_type=entity_type,
                target_count=target_count,
                sentence_diversity=sentence_diversity,
                vanilla_entities=vanilla_entities,
                latent_entities=latent_entities,
                examples=examples,
                generation_features=generation_features
            )
            
            all_results[entity_type] = results
            print(f"[✓] {entity_type} 类型数据生成完成，实际生成：{len(results)} 个")
            
        except Exception as e:
            print(f"[错误] 生成 {entity_type} 类型数据失败: {e}")
            all_results[entity_type] = []
    
    return all_results

if __name__ == "__main__":
    main()
