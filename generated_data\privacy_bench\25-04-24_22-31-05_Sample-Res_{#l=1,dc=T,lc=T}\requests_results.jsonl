[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成 1 条包含敏感个人信息的中文句子。要求句子自然、合理、完整，包含1-4个以下的实体类型。请标记所有属于以下实体类型的命名实体：姓名, 年龄, 性别, 国籍, 职业, 种族, 民族, 教育背景, 婚姻状况, 政治倾向, 家庭成员, 工资数额, 投资产品, 税务记录, 信用记录, 实体资产, 交易信息, 疾病, 药物, 临床表现, 医疗程序, 过敏信息, 生育信息, 地理位置, 行程信息。\n请在下一行中按照出现的顺序列出这些命名实体及其对应的实体类型。\n如果在生成的句子中没有找到实体，请将方括号留空。\n以下是一些示例句子及其标注，请参考并按照这种格式生成。\n\n示例：\n\nSentence: \"中肠扭转也可出现间歇性的症状 ， 主要见于年长患儿。\"\nNamed Entities: [中 肠 扭 转 也 (临床表现)]\n\nSentence: \"有研究显示 ， 这种方法的远期疗效无论从临床上还是各种检查上 ， 都显示出很高的失败率 ， 尤其在重度 G E R D 患者中。\"\nNamed Entities: [G E R D 患 (疾病)]\n\nSentence: \"中行有关负责人也表示 ， 目前相关工作正在有序进行。\"\nNamed Entities: [有 关 负 责 人 (职业)]\n\nSentence: \"鉴别困难时需行肾活体组织检查。\"\nNamed Entities: [肾 活 体 组 织 检 查 。 (医疗程序)]\n\nSentence: \"每年 6 月的最后一个星期 ， 索维拉会举办著名的纳瓦世界音乐节 ，\"\nNamed Entities: [索 维 拉 (地理位置)]\n\nSentence: \"“这次有一本《集千家注杜工部诗集》 ， 这是明嘉靖明易山人校刻大字本 ， 共十二册 ， 白棉纸线装。 ”\"\nNamed Entities: [明 易 山 人 (姓名)]\n\nSentence: \"蛋白质 2 ～ 4 g / （ k g • d ） （ 母乳 2 g / k g ， 牛乳 3. 5 g / k g ， 大豆蛋白 4 g / k g ） 。\"\nNamed Entities: [蛋 白 质 2 (药物)]\n\n\n---\n\n此外，生成的敏感数据内容应该遵循以下要求：\n主要包含个人信息类敏感实体."}], "temperature": 1, "max_tokens": 1024, "logprobs": true, "seed": 70}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\nSentence: \"张明是一位28岁的汉族男性，他的工资数额为每月12000元，目前居住在北京朝阳区。\"\nNamed Entities: [张 明 (姓名), 28 岁 (年龄), 汉 族 (民族), 男 性 (性别), 工资数额 为 每 月 12000 元 (工资数额), 北 京 朝 阳 区 (地理位置)]", "role": "assistant"}}], "created": 1745505069, "id": "20250424223107f546b805d3244c75", "model": "glm-4-air-250414", "request_id": "20250424223107f546b805d3244c75", "usage": {"completion_tokens": 77, "prompt_tokens": 532, "total_tokens": 609}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成 1 条包含敏感个人信息的中文句子。要求句子自然、合理、完整，包含1-4个以下的实体类型。请标记所有属于以下实体类型的命名实体：姓名, 年龄, 性别, 国籍, 职业, 种族, 民族, 教育背景, 婚姻状况, 政治倾向, 家庭成员, 工资数额, 投资产品, 税务记录, 信用记录, 实体资产, 交易信息, 疾病, 药物, 临床表现, 医疗程序, 过敏信息, 生育信息, 地理位置, 行程信息。\n请在下一行中按照出现的顺序列出这些命名实体及其对应的实体类型。\n如果在生成的句子中没有找到实体，请将方括号留空。\n以下是一些示例句子及其标注，请参考并按照这种格式生成。\n\n示例：\n\nSentence: \"每年 6 月的最后一个星期 ， 索维拉会举办著名的纳瓦世界音乐节 ，\"\nNamed Entities: [索 维 拉 (地理位置)]\n\nSentence: \"“这次有一本《集千家注杜工部诗集》 ， 这是明嘉靖明易山人校刻大字本 ， 共十二册 ， 白棉纸线装。 ”\"\nNamed Entities: [明 易 山 人 (姓名)]\n\nSentence: \"鉴别困难时需行肾活体组织检查。\"\nNamed Entities: [肾 活 体 组 织 检 查 。 (医疗程序)]\n\nSentence: \"中肠扭转也可出现间歇性的症状 ， 主要见于年长患儿。\"\nNamed Entities: [中 肠 扭 转 也 (临床表现)]\n\nSentence: \"蛋白质 2 ～ 4 g / （ k g • d ） （ 母乳 2 g / k g ， 牛乳 3. 5 g / k g ， 大豆蛋白 4 g / k g ） 。\"\nNamed Entities: [蛋 白 质 2 (药物)]\n\nSentence: \"有研究显示 ， 这种方法的远期疗效无论从临床上还是各种检查上 ， 都显示出很高的失败率 ， 尤其在重度 G E R D 患者中。\"\nNamed Entities: [G E R D 患 (疾病)]\n\nSentence: \"中行有关负责人也表示 ， 目前相关工作正在有序进行。\"\nNamed Entities: [有 关 负 责 人 (职业)]\n\n\n---\n\n此外，生成的敏感数据内容应该遵循以下要求：\n1. 主要包含个人信息类敏感实体;\n2. 语言语气为日常语气."}], "temperature": 1, "max_tokens": 1024, "logprobs": true, "seed": 42}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\nSentence: \"李明今年35岁，是汉族，在一家银行担任高级经理，他的月工资是15000元，最近还因为过敏不能吃海鲜。\"\nNamed Entities: [李 明 (姓名), 35 岁 (年龄), 汉 族 (民族), 高 级 经 理 (职业), 15000 元 (工资数额), 过 敏 (过敏信息)]", "role": "assistant"}}], "created": 1745505069, "id": "20250424223107ae69d4c421ce456f", "model": "glm-4-air-250414", "request_id": "20250424223107ae69d4c421ce456f", "usage": {"completion_tokens": 82, "prompt_tokens": 542, "total_tokens": 624}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成 1 条包含敏感个人信息的中文句子。要求句子自然、合理、完整，包含1-4个以下的实体类型。请标记所有属于以下实体类型的命名实体：姓名, 年龄, 性别, 国籍, 职业, 种族, 民族, 教育背景, 婚姻状况, 政治倾向, 家庭成员, 工资数额, 投资产品, 税务记录, 信用记录, 实体资产, 交易信息, 疾病, 药物, 临床表现, 医疗程序, 过敏信息, 生育信息, 地理位置, 行程信息。\n请在下一行中按照出现的顺序列出这些命名实体及其对应的实体类型。\n如果在生成的句子中没有找到实体，请将方括号留空。\n以下是一些示例句子及其标注，请参考并按照这种格式生成。\n\n示例：\n\nSentence: \"鉴别困难时需行肾活体组织检查。\"\nNamed Entities: [肾 活 体 组 织 检 查 。 (医疗程序)]\n\nSentence: \"中肠扭转也可出现间歇性的症状 ， 主要见于年长患儿。\"\nNamed Entities: [中 肠 扭 转 也 (临床表现)]\n\nSentence: \"“这次有一本《集千家注杜工部诗集》 ， 这是明嘉靖明易山人校刻大字本 ， 共十二册 ， 白棉纸线装。 ”\"\nNamed Entities: [明 易 山 人 (姓名)]\n\nSentence: \"蛋白质 2 ～ 4 g / （ k g • d ） （ 母乳 2 g / k g ， 牛乳 3. 5 g / k g ， 大豆蛋白 4 g / k g ） 。\"\nNamed Entities: [蛋 白 质 2 (药物)]\n\nSentence: \"有研究显示 ， 这种方法的远期疗效无论从临床上还是各种检查上 ， 都显示出很高的失败率 ， 尤其在重度 G E R D 患者中。\"\nNamed Entities: [G E R D 患 (疾病)]\n\nSentence: \"每年 6 月的最后一个星期 ， 索维拉会举办著名的纳瓦世界音乐节 ，\"\nNamed Entities: [索 维 拉 (地理位置)]\n\nSentence: \"中行有关负责人也表示 ， 目前相关工作正在有序进行。\"\nNamed Entities: [有 关 负 责 人 (职业)]\n\n\n---\n\n此外，生成的敏感数据内容应该遵循以下要求：\n1. 主要包含健康与医疗信息类敏感实体;\n2. 数据上下文为企业内部数据."}], "temperature": 1, "max_tokens": 1024, "logprobs": true, "seed": 63}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\nSentence: \"根据内部健康档案，张三（[张 三 (姓名)])因[高血压 (疾病)]长期服用[降压药 (药物)]，最近一次血压检查结果为150/95mmHg，医生建议调整[剂量 (临床表现)]并注意[肾功能 (临床表现)]监测。\"\nNamed Entities: [张 三 (姓名), 高血压 (疾病), 降压药 (药物), 剂量 (临床表现), 肾功能 (临床表现)]", "role": "assistant"}}], "created": 1745505069, "id": "202504242231075b43e0f90a7b491e", "model": "glm-4-air-250414", "request_id": "202504242231075b43e0f90a7b491e", "usage": {"completion_tokens": 99, "prompt_tokens": 546, "total_tokens": 645}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成 1 条包含敏感个人信息的中文句子。要求句子自然、合理、完整，包含1-4个以下的实体类型。请标记所有属于以下实体类型的命名实体：姓名, 年龄, 性别, 国籍, 职业, 种族, 民族, 教育背景, 婚姻状况, 政治倾向, 家庭成员, 工资数额, 投资产品, 税务记录, 信用记录, 实体资产, 交易信息, 疾病, 药物, 临床表现, 医疗程序, 过敏信息, 生育信息, 地理位置, 行程信息。\n请在下一行中按照出现的顺序列出这些命名实体及其对应的实体类型。\n如果在生成的句子中没有找到实体，请将方括号留空。\n以下是一些示例句子及其标注，请参考并按照这种格式生成。\n\n示例：\n\nSentence: \"中肠扭转也可出现间歇性的症状 ， 主要见于年长患儿。\"\nNamed Entities: [中 肠 扭 转 也 (临床表现)]\n\nSentence: \"每年 6 月的最后一个星期 ， 索维拉会举办著名的纳瓦世界音乐节 ，\"\nNamed Entities: [索 维 拉 (地理位置)]\n\nSentence: \"有研究显示 ， 这种方法的远期疗效无论从临床上还是各种检查上 ， 都显示出很高的失败率 ， 尤其在重度 G E R D 患者中。\"\nNamed Entities: [G E R D 患 (疾病)]\n\nSentence: \"鉴别困难时需行肾活体组织检查。\"\nNamed Entities: [肾 活 体 组 织 检 查 。 (医疗程序)]\n\nSentence: \"中行有关负责人也表示 ， 目前相关工作正在有序进行。\"\nNamed Entities: [有 关 负 责 人 (职业)]\n\nSentence: \"蛋白质 2 ～ 4 g / （ k g • d ） （ 母乳 2 g / k g ， 牛乳 3. 5 g / k g ， 大豆蛋白 4 g / k g ） 。\"\nNamed Entities: [蛋 白 质 2 (药物)]\n\nSentence: \"“这次有一本《集千家注杜工部诗集》 ， 这是明嘉靖明易山人校刻大字本 ， 共十二册 ， 白棉纸线装。 ”\"\nNamed Entities: [明 易 山 人 (姓名)]\n\n\n---\n\n此外，生成的敏感数据内容应该遵循以下要求：\n1. 主要包含健康与医疗信息类敏感实体;\n2. 数据上下文为医疗记录;\n3. 数据使用意图为个性化服务."}], "temperature": 1, "max_tokens": 1024, "logprobs": true, "seed": 56}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\nSentence: \"张三（[张 三 (姓名)]），一位[40岁 (年龄)]的[男性 (性别)]，因持续[高血压 (疾病)]前来就诊，医生建议调整[阿司匹林 (药物)]的用量，并记录了他的[头晕 (临床表现)]症状。\"\nNamed Entities: [张 三 (姓名), 40 岁 (年龄), 男性 (性别), 高血压 (疾病), 阿司匹林 (药物), 头晕 (临床表现)]", "role": "assistant"}}], "created": 1745505069, "id": "202504242231077497f4c7c58f4d25", "model": "glm-4-air-250414", "request_id": "202504242231077497f4c7c58f4d25", "usage": {"completion_tokens": 104, "prompt_tokens": 555, "total_tokens": 659}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成 1 条包含敏感个人信息的中文句子。要求句子自然、合理、完整，包含1-4个以下的实体类型。请标记所有属于以下实体类型的命名实体：姓名, 年龄, 性别, 国籍, 职业, 种族, 民族, 教育背景, 婚姻状况, 政治倾向, 家庭成员, 工资数额, 投资产品, 税务记录, 信用记录, 实体资产, 交易信息, 疾病, 药物, 临床表现, 医疗程序, 过敏信息, 生育信息, 地理位置, 行程信息。\n请在下一行中按照出现的顺序列出这些命名实体及其对应的实体类型。\n如果在生成的句子中没有找到实体，请将方括号留空。\n以下是一些示例句子及其标注，请参考并按照这种格式生成。\n\n示例：\n\nSentence: \"鉴别困难时需行肾活体组织检查。\"\nNamed Entities: [肾 活 体 组 织 检 查 。 (医疗程序)]\n\nSentence: \"中行有关负责人也表示 ， 目前相关工作正在有序进行。\"\nNamed Entities: [有 关 负 责 人 (职业)]\n\nSentence: \"有研究显示 ， 这种方法的远期疗效无论从临床上还是各种检查上 ， 都显示出很高的失败率 ， 尤其在重度 G E R D 患者中。\"\nNamed Entities: [G E R D 患 (疾病)]\n\nSentence: \"“这次有一本《集千家注杜工部诗集》 ， 这是明嘉靖明易山人校刻大字本 ， 共十二册 ， 白棉纸线装。 ”\"\nNamed Entities: [明 易 山 人 (姓名)]\n\nSentence: \"蛋白质 2 ～ 4 g / （ k g • d ） （ 母乳 2 g / k g ， 牛乳 3. 5 g / k g ， 大豆蛋白 4 g / k g ） 。\"\nNamed Entities: [蛋 白 质 2 (药物)]\n\nSentence: \"每年 6 月的最后一个星期 ， 索维拉会举办著名的纳瓦世界音乐节 ，\"\nNamed Entities: [索 维 拉 (地理位置)]\n\nSentence: \"中肠扭转也可出现间歇性的症状 ， 主要见于年长患儿。\"\nNamed Entities: [中 肠 扭 转 也 (临床表现)]\n\n\n---\n\n此外，生成的敏感数据内容应该遵循以下要求：\n1. 主要包含个人信息类敏感实体;\n2. 数据敏感级别为最高敏感级别."}], "temperature": 1, "max_tokens": 1024, "logprobs": true, "seed": 49}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\nSentence: \"张伟，一个35岁的中国男性程序员，在2023年7月因高血压住院，医生开具了硝苯地平片并记录了他的血压和头晕症状。\"\nNamed Entities: [张 伟 (姓名), 35 岁 (年龄), 中 国 (国籍), 男 性 (性别), 程 序 员 (职业), 高 血 压 (疾病), 硝 苯 地 平 片 (药物), 血 压 (临床表现), 头 晕 (临床表现)]", "role": "assistant"}}], "created": 1745505069, "id": "20250424223107f3e1128cd0494510", "model": "glm-4-air-250414", "request_id": "20250424223107f3e1128cd0494510", "usage": {"completion_tokens": 111, "prompt_tokens": 544, "total_tokens": 655}}]
