{"sentence": "are there any good romantic comedies out right now", "entity_names": ["romantic comedies", "right now"], "entity_types": ["Genre", "Year"]}
{"sentence": "show me a movie about cars that talk", "entity_names": ["cars that talk"], "entity_types": ["Plot"]}
{"sentence": "list the five star rated movies starring mel gibson", "entity_names": ["five star", "mel gibson"], "entity_types": ["Viewers' Rating", "Actor"]}
{"sentence": "what science fiction films have come out recently", "entity_names": ["science fiction", "recently"], "entity_types": ["Genre", "Year"]}
{"sentence": "did the same director make all of the harry potter movies", "entity_names": ["harry potter"], "entity_types": ["Title"]}
{"sentence": "show me 1980s action movies", "entity_names": ["1980s", "action"], "entity_types": ["Year", "Genre"]}
{"sentence": "what is the name of the third movie in the star trek series", "entity_names": ["star trek series"], "entity_types": ["Title"]}
{"sentence": "can you get a soundtrac for the harry potter films", "entity_names": ["soundtrac", "harry potter films"], "entity_types": ["Song", "Title"]}
{"sentence": "find me science fiction movies since 2005", "entity_names": ["science fiction", "since 2005"], "entity_types": ["Genre", "Year"]}
{"sentence": "what is the most current movie featuring mat damon", "entity_names": ["current", "mat damon"], "entity_types": ["Year", "Actor"]}
{"sentence": "show me films where jim carrey is a detective", "entity_names": ["jim carrey", "detective"], "entity_types": ["Actor", "Character"]}
{"sentence": "did george clooney make a musical in the 1980s", "entity_names": ["george clooney", "musical", "1980s"], "entity_types": ["Actor", "Genre", "Year"]}
{"sentence": "show me films with both matt damon ad ben affleck", "entity_names": ["matt damon", "ben affleck"], "entity_types": ["Actor", "Actor"]}
{"sentence": "what is the borrowers movie", "entity_names": ["borrowers"], "entity_types": ["Title"]}
{"sentence": "have u movie hm about to pg 18", "entity_names": ["pg 18"], "entity_types": ["MPAA Rating"]}
{"sentence": "find rated g films with flying cars", "entity_names": ["g", "flying cars"], "entity_types": ["MPAA Rating", "Plot"]}
{"sentence": "what was the best rated stanley kubrick film", "entity_names": ["best", "stanley kubrick"], "entity_types": ["Review", "Director"]}
{"sentence": "are there any films directed by shawn levy about large families", "entity_names": ["shawn levy", "large families"], "entity_types": ["Director", "Plot"]}
{"sentence": "list pg rated movies about cars released in the 1990s", "entity_names": ["pg", "cars", "1990s"], "entity_types": ["MPAA Rating", "Plot", "Year"]}
{"sentence": "what movie won best picure at the 2012 oscars", "entity_names": ["best picure", "2012"], "entity_types": ["Viewers' Rating", "Viewers' Rating"]}
{"sentence": "find me childrens movies with daniel radcliffe from the 2000s", "entity_names": ["childrens movies", "daniel radcliffe", "2000s"], "entity_types": ["Genre", "Actor", "Year"]}
{"sentence": "what was the plot behind 3 days of the condor", "entity_names": ["3 days of the condor"], "entity_types": ["Title"]}
{"sentence": "what are considered the must see sci fi movies of the 1970s", "entity_names": ["sci fi movies", "1970s"], "entity_types": ["Genre", "Year"]}
{"sentence": "who directed the film pulp fiction that starred john travolta", "entity_names": ["directed", "pulp fiction", "john travolta"], "entity_types": ["Director", "Title", "Actor"]}
{"sentence": "which film has the highest viewer rating this year", "entity_names": ["highest viewer"], "entity_types": ["Viewers' Rating"]}
{"sentence": "what was the first movie in color", "entity_names": ["color"], "entity_types": ["Plot"]}
{"sentence": "may i have the highly acclaimed film from 1985 directed by sylvester stallone", "entity_names": ["highly acclaimed", "1985", "sylvester stallone"], "entity_types": ["Review", "Viewers' Rating", "Director"]}
{"sentence": "list the action films starring hugh jackman", "entity_names": ["action films", "hugh jackman"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what is a bronx tale rated", "entity_names": ["bronx tale", "rated"], "entity_types": ["Title", "MPAA Rating"]}
{"sentence": "is there a pg 13 movie thats scary", "entity_names": ["pg 13", "scary"], "entity_types": ["MPAA Rating", "Genre"]}
{"sentence": "what movies made in 2004 were pg", "entity_names": ["2004", "pg"], "entity_types": ["Year", "MPAA Rating"]}
{"sentence": "find movies with robert diniero in it", "entity_names": ["robert diniero"], "entity_types": ["Actor"]}
{"sentence": "have pg 13 movies for the kidz", "entity_names": ["pg 13", "kidz"], "entity_types": ["MPAA Rating", "Genre"]}
{"sentence": "list the science fiction movies directed by shawn levy", "entity_names": ["science fiction", "shawn levy"], "entity_types": ["Genre", "Director"]}
{"sentence": "what was the last film elizabeth montgomery starred in", "entity_names": ["last", "elizabeth montgomery"], "entity_types": ["Year", "Actor"]}
{"sentence": "did george clooney direct any comedy films", "entity_names": ["george clooney", "comedy"], "entity_types": ["Director", "Genre"]}
{"sentence": "what is brad pitts first movie", "entity_names": ["brad pitts"], "entity_types": ["Actor"]}
{"sentence": "find me the movie that has a aerosmith song", "entity_names": ["aerosmith song"], "entity_types": ["Song"]}
{"sentence": "are there any drama movies with seth green", "entity_names": ["drama", "seth green"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what is the movie with an aerosmith song", "entity_names": ["aerosmith"], "entity_types": ["Song"]}
{"sentence": "what is the highest rated kids new release", "entity_names": ["highest rated", "kids", "new release"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "worst review for 2011 movies", "entity_names": ["worst review", "2011"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "show me the milos forman movies from the 1980s", "entity_names": ["milos forman", "1980s"], "entity_types": ["Actor", "Year"]}
{"sentence": "lets find an independent film company", "entity_names": ["independent film"], "entity_types": ["Genre"]}
{"sentence": "which movies were based off of video games besides resident evil", "entity_names": ["based off of video games", "resident evil"], "entity_types": ["Plot", "Title"]}
{"sentence": "did dame judy dench star in a british film about queen elizabeth", "entity_names": ["dame judy dench", "british", "queen elizabeth"], "entity_types": ["Actor", "Plot", "Character"]}
{"sentence": "present list of family movies that chris columbus directed", "entity_names": ["family movies", "chris columbus"], "entity_types": ["Genre", "Director"]}
{"sentence": "find a movie with dogs as the main character", "entity_names": ["dogs"], "entity_types": ["Plot"]}
{"sentence": "show me movies about horse racing", "entity_names": ["horse racing"], "entity_types": ["Plot"]}
{"sentence": "show me morgan freeman movies from the 90s", "entity_names": ["morgan freeman", "90s"], "entity_types": ["Actor", "Year"]}
{"sentence": "find me the movies that starred anne hathaway and julie andrews", "entity_names": ["anne hathaway", "julie andrews"], "entity_types": ["Actor", "Actor"]}
{"sentence": "has sandra bullock made any g rated movies", "entity_names": ["sandra bullock", "g rated"], "entity_types": ["Actor", "MPAA Rating"]}
{"sentence": "list the dirty harry films from the 1980s", "entity_names": ["dirty harry", "1980s"], "entity_types": ["Title", "Year"]}
{"sentence": "show me movies about strippers", "entity_names": ["strippers"], "entity_types": ["Plot"]}
{"sentence": "are there any meg ryan romantic comedy movies that are considered must see", "entity_names": ["meg ryan", "romantic comedy", "must see"], "entity_types": ["Actor", "Genre", "Review"]}
{"sentence": "what was james camerons directorial debut", "entity_names": ["james camerons"], "entity_types": ["Director"]}
{"sentence": "what are top 50 movies of all time", "entity_names": ["top 50", "all time"], "entity_types": ["Viewers' Rating", "Viewers' Rating"]}
{"sentence": "find me comedy movies with liam hemsworth", "entity_names": ["comedy", "liam hemsworth"], "entity_types": ["Genre", "Actor"]}
{"sentence": "i want to see cradle 2 the grave", "entity_names": ["cradle 2 the grave"], "entity_types": ["Title"]}
{"sentence": "what was the most popular movie from 2004", "entity_names": ["most popular movie", "2004"], "entity_types": ["Review", "Year"]}
{"sentence": "what is the g rated movie about rabbits looking for a new home", "entity_names": ["g rated", "rabbits looking for a new home"], "entity_types": ["MPAA Rating", "Plot"]}
{"sentence": "what latest 3d movie of any genre is reccommend", "entity_names": ["3d", "reccommend"], "entity_types": ["Plot", "Viewers' Rating"]}
{"sentence": "how many movies have tim burton and johnny depp done together", "entity_names": ["tim burton", "johnny depp"], "entity_types": ["Actor", "Actor"]}
{"sentence": "how many times has matt damon been jason bourne", "entity_names": ["matt damon", "jason bourne"], "entity_types": ["Actor", "Character"]}
{"sentence": "whats the latetest foreign romantic movie with lots of sex and sadness", "entity_names": ["latetest", "foreign romantic", "sex", "sadness"], "entity_types": ["Year", "Genre", "Plot", "Plot"]}
{"sentence": "list movies with jeremy piven released in the 1990s", "entity_names": ["jeremy piven", "1990s"], "entity_types": ["Actor", "Year"]}
{"sentence": "show me the collection of action movies of arnold", "entity_names": ["action movies", "arnold"], "entity_types": ["Genre", "Actor"]}
{"sentence": "are there comic book movies that are over pg 13", "entity_names": ["comic book movies", "pg 13"], "entity_types": ["Genre", "MPAA Rating"]}
{"sentence": "the new batman movie looks epic", "entity_names": ["batman"], "entity_types": ["Title"]}
{"sentence": "show me movies who won awards", "entity_names": ["awards"], "entity_types": ["Viewers' Rating"]}
{"sentence": "what is the year that dirty dancing was released", "entity_names": ["year", "dirty dancing"], "entity_types": ["Year", "Title"]}
{"sentence": "favorite quote from action movies", "entity_names": ["action movies"], "entity_types": ["Genre"]}
{"sentence": "find me the g rated movies with dogs that were released in the 2000s", "entity_names": ["g rated", "dogs", "2000s"], "entity_types": ["MPAA Rating", "Plot", "Year"]}
{"sentence": "are there any silent movies made after 1930", "entity_names": ["silent", "after 1930"], "entity_types": ["Genre", "Year"]}
{"sentence": "who was the actress in the goodbye girl with richard dreyfuss", "entity_names": ["the goodbye girl", "richard dreyfuss"], "entity_types": ["Title", "Actor"]}
{"sentence": "which movies are made with video game plots", "entity_names": ["video game"], "entity_types": ["Plot"]}
{"sentence": "what video game movies are due to release in 2013", "entity_names": ["video game movies", "2013"], "entity_types": ["Plot", "Year"]}
{"sentence": "id like the not to be missed nicole kidman musical", "entity_names": ["nicole kidman", "musical"], "entity_types": ["Actor", "Genre"]}
{"sentence": "what movie has the most remakes", "entity_names": ["most"], "entity_types": ["Review"]}
{"sentence": "what shakespeare films take place in italy", "entity_names": ["shakespeare", "italy"], "entity_types": ["Plot", "Plot"]}
{"sentence": "what was channing tatums first movie", "entity_names": ["channing tatums", "first"], "entity_types": ["Actor", "Year"]}
{"sentence": "i would like a list of movies about dancing from the past 10 years", "entity_names": ["dancing", "past 10 years"], "entity_types": ["Plot", "Year"]}
{"sentence": "who stars in project x", "entity_names": ["project x"], "entity_types": ["Title"]}
{"sentence": "find action movies featuring comic book characters", "entity_names": ["action", "comic book characters"], "entity_types": ["Genre", "Plot"]}
{"sentence": "what are some g rated movies with fairies", "entity_names": ["g", "fairies"], "entity_types": ["MPAA Rating", "Plot"]}
{"sentence": "name a movie starring britney spears", "entity_names": ["britney spears"], "entity_types": ["Actor"]}
{"sentence": "what movie did rod serling write", "entity_names": ["rod serling"], "entity_types": ["Director"]}
{"sentence": "is there an animated adult fantasy movie", "entity_names": ["animated adult fantasy"], "entity_types": ["Genre"]}
{"sentence": "the song sunshine on my shoulders was the soundtrack for what movie", "entity_names": ["sunshine on my shoulders"], "entity_types": ["Song"]}
{"sentence": "are there any movies about popular game shows", "entity_names": ["popular game shows"], "entity_types": ["Plot"]}
{"sentence": "find me the name of the actor that played v in v for vendetta", "entity_names": ["v", "v for vendetta"], "entity_types": ["Character", "Title"]}
{"sentence": "what was the last terminator sequel", "entity_names": ["terminator"], "entity_types": ["Title"]}
{"sentence": "what science fiction movies were directed by george lucas", "entity_names": ["science fiction", "george lucas"], "entity_types": ["Genre", "Director"]}
{"sentence": "who directed princess bride", "entity_names": ["princess bride"], "entity_types": ["Title"]}
{"sentence": "who directed james and the giant peach", "entity_names": ["directed", "james and the giant peach"], "entity_types": ["Director", "Title"]}
{"sentence": "name a western comedy", "entity_names": ["western comedy"], "entity_types": ["Genre"]}
{"sentence": "what type of movie is nine", "entity_names": ["nine"], "entity_types": ["Title"]}
{"sentence": "how many movies have starred brad pitt", "entity_names": ["brad pitt"], "entity_types": ["Actor"]}
{"sentence": "i am looking for a movie about talking animals", "entity_names": ["talking animals"], "entity_types": ["Plot"]}
{"sentence": "find a john malcovich thriller", "entity_names": ["john malcovich", "thriller"], "entity_types": ["Actor", "Genre"]}
{"sentence": "channing tatum has played what starring roles", "entity_names": ["channing tatum"], "entity_types": ["Actor"]}
{"sentence": "list of actors a beautiful mind", "entity_names": ["a beautiful mind"], "entity_types": ["Title"]}
{"sentence": "avatar came out when and what did it gross", "entity_names": ["avatar", "when"], "entity_types": ["Title", "Year"]}
{"sentence": "did angelina jolie play a russian in salt", "entity_names": ["angelina jolie", "russian", "salt"], "entity_types": ["Actor", "Plot", "Title"]}
{"sentence": "did they make a movie version of absolutely fabulous", "entity_names": ["absolutely fabulous"], "entity_types": ["Title"]}
{"sentence": "show me a comedy movie with eddie murphy", "entity_names": ["comedy", "eddie murphy"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what is brave about", "entity_names": ["brave"], "entity_types": ["Title"]}
{"sentence": "has ashton kutcher made any movies with zombies", "entity_names": ["ashton kutcher", "zombies"], "entity_types": ["Actor", "Plot"]}
{"sentence": "what movie stars christopher walken an sean penn", "entity_names": ["christopher walken", "sean penn"], "entity_types": ["Actor", "Actor"]}
{"sentence": "show me movies starring michael j fox from 1993", "entity_names": ["michael j fox", "1993"], "entity_types": ["Actor", "Year"]}
{"sentence": "who starred in marley and me", "entity_names": ["marley and me"], "entity_types": ["Title"]}
{"sentence": "what movie has the song down with the sickness", "entity_names": ["down with the sickness"], "entity_types": ["Song"]}
{"sentence": "did simon pegg write any movies", "entity_names": ["simon pegg"], "entity_types": ["Actor"]}
{"sentence": "are there any pg movies with car chases", "entity_names": ["pg movies", "car chases"], "entity_types": ["MPAA Rating", "Plot"]}
{"sentence": "what movie won the most awards in 2005", "entity_names": ["most awards", "2005"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "what is puss in boots about", "entity_names": ["puss in boots"], "entity_types": ["Title"]}
{"sentence": "how many movies have been released in the last ten years that involved terrorism", "entity_names": ["last ten years", "terrorism"], "entity_types": ["Year", "Plot"]}
{"sentence": "who directed runaway jury", "entity_names": ["directed", "runaway jury"], "entity_types": ["Director", "Title"]}
{"sentence": "show me a good spy movie based in england", "entity_names": ["spy", "england"], "entity_types": ["Genre", "Plot"]}
{"sentence": "what popular films released last month", "entity_names": ["popular", "last month"], "entity_types": ["Review", "Year"]}
{"sentence": "did dean parisot direct sigourney weaver and tim allen in a comedy", "entity_names": ["dean parisot", "sigourney weaver", "tim allen", "comedy"], "entity_types": ["Director", "Actor", "Actor", "Genre"]}
{"sentence": "who directed the help", "entity_names": ["the help"], "entity_types": ["Title"]}
{"sentence": "what is the title of woody allens first movie", "entity_names": ["woody allens", "first"], "entity_types": ["Actor", "Year"]}
{"sentence": "what are some police dramas from the 90s", "entity_names": ["police", "dramas", "90s"], "entity_types": ["Plot", "Genre", "Year"]}
{"sentence": "how many films did clive owen play in", "entity_names": ["clive owen"], "entity_types": ["Actor"]}
{"sentence": "are there any films that harmony korine regrets directing andor releasing to the public", "entity_names": ["harmony korine", "regrets"], "entity_types": ["Director", "Review"]}
{"sentence": "show me an alfred hitchcock movie about trains", "entity_names": ["alfred hitchcock", "trains"], "entity_types": ["Director", "Plot"]}
{"sentence": "who stars in rumble in the bronx", "entity_names": ["rumble in the bronx"], "entity_types": ["Title"]}
{"sentence": "what was the first movie patrick stewart played in", "entity_names": ["first", "patrick stewart"], "entity_types": ["Year", "Actor"]}
{"sentence": "what is the film john carter about", "entity_names": ["john carter"], "entity_types": ["Title"]}
{"sentence": "what is the longest running time for a movie in film history", "entity_names": ["longest running time"], "entity_types": ["Review"]}
{"sentence": "what was the release year for notes on a scandal", "entity_names": ["release year", "notes on a scandal"], "entity_types": ["Year", "Title"]}
{"sentence": "did milos forman direct a film with edward norton", "entity_names": ["milos forman", "edward norton"], "entity_types": ["Director", "Actor"]}
{"sentence": "how many movies did christopher walkin star in", "entity_names": ["christopher walkin"], "entity_types": ["Actor"]}
{"sentence": "i want to find a list of pg rated comedies of this year", "entity_names": ["pg rated", "comedies", "this year"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "what are some movies from the 80s starring bruce willis", "entity_names": ["80s", "bruce willis"], "entity_types": ["Year", "Actor"]}
{"sentence": "waht was the plot of the deep blue sea", "entity_names": ["the deep blue sea"], "entity_types": ["Title"]}
{"sentence": "did guillermo del toro direct any moves rated pg", "entity_names": ["guillermo del toro", "pg"], "entity_types": ["Director", "MPAA Rating"]}
{"sentence": "who played james in james and the giant peach", "entity_names": ["james", "james and the giant peach"], "entity_types": ["Character", "Title"]}
{"sentence": "what movies from 2006 star val kilmer", "entity_names": ["2006", "val kilmer"], "entity_types": ["Year", "Actor"]}
{"sentence": "what kind of movie is wanderlust", "entity_names": ["kind", "wanderlust"], "entity_types": ["Review", "Title"]}
{"sentence": "did whitney houston sing in the preachers wife", "entity_names": ["whitney houston", "the preachers wife"], "entity_types": ["Actor", "Title"]}
{"sentence": "what techno thriller gets a low rating", "entity_names": ["techno", "low rating"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "any good horror films that came out in 2008", "entity_names": ["good", "horror", "2008"], "entity_types": ["Review", "Genre", "Year"]}
{"sentence": "where can i watch a preview of moneyball", "entity_names": ["preview", "moneyball"], "entity_types": ["Trailer", "Title"]}
{"sentence": "what is the most recent sean connery film", "entity_names": ["most recent", "sean connery"], "entity_types": ["Year", "Actor"]}
{"sentence": "are there any scary pg rated movies", "entity_names": ["scary", "pg rated"], "entity_types": ["Genre", "MPAA Rating"]}
{"sentence": "what is a recent george clooney movie with high viewers rating", "entity_names": ["george clooney", "high viewers rating"], "entity_types": ["Actor", "Viewers' Rating"]}
{"sentence": "which animated childrens movies are considered timeless", "entity_names": ["animated childrens movies", "considered timeless"], "entity_types": ["Genre", "Review"]}
{"sentence": "find me the movie with the song my heart will go on", "entity_names": ["my heart will go on"], "entity_types": ["Song"]}
{"sentence": "what are some batman movies from the 1990s", "entity_names": ["batman movies", "1990s"], "entity_types": ["Title", "Year"]}
{"sentence": "what horror movies have marilyn manson on the soundtrack", "entity_names": ["horror", "marilyn manson"], "entity_types": ["Genre", "Actor"]}
{"sentence": "show me terry gilliam movies starring jeff bridges", "entity_names": ["terry gilliam", "jeff bridges"], "entity_types": ["Director", "Actor"]}
{"sentence": "was there a trailer for bowling for columbine", "entity_names": ["bowling for columbine"], "entity_types": ["Title"]}
{"sentence": "when did interview with a vampire come out", "entity_names": ["interview with a vampire"], "entity_types": ["Title"]}
{"sentence": "who starred in who framed roger rabbit", "entity_names": ["who framed roger rabbit"], "entity_types": ["Title"]}
{"sentence": "who directed 310 to yuma", "entity_names": ["directed", "310 to yuma"], "entity_types": ["Director", "Title"]}
{"sentence": "what movies have batman and robin", "entity_names": ["batman and robin"], "entity_types": ["Plot"]}
{"sentence": "is there a horror movie starring jennifer lopez", "entity_names": ["horror", "jennifer lopez"], "entity_types": ["Genre", "Actor"]}
{"sentence": "did the mpaa give doubt a pg 13 rating", "entity_names": ["doubt", "pg 13"], "entity_types": ["Title", "MPAA Rating"]}
{"sentence": "did vin diesel star in any comdedies", "entity_names": ["vin diesel", "comdedies"], "entity_types": ["Actor", "Genre"]}
{"sentence": "did ray liota direct any films", "entity_names": ["ray liota"], "entity_types": ["Director"]}
{"sentence": "did gwyneth paltrow play a mathematician in a movie with anthony hopkins", "entity_names": ["gwyneth paltrow", "mathematician", "anthony hopkins"], "entity_types": ["Actor", "Plot", "Actor"]}
{"sentence": "is there a movie based on a shakespeare play", "entity_names": ["shakespeare play"], "entity_types": ["Plot"]}
{"sentence": "find me a movie with the song lets hear it for the boys", "entity_names": ["lets hear it for the boys"], "entity_types": ["Song"]}
{"sentence": "what was the fog rated", "entity_names": ["the fog"], "entity_types": ["Title"]}
{"sentence": "what was johnny depps first movie", "entity_names": ["johnny depps", "first"], "entity_types": ["Actor", "Year"]}
{"sentence": "what was goodfellas rated", "entity_names": ["goodfellas", "rated"], "entity_types": ["Title", "MPAA Rating"]}
{"sentence": "what was the name of the donkey in shrek", "entity_names": ["donkey", "shrek"], "entity_types": ["Character", "Title"]}
{"sentence": "did joe pesci direct any films", "entity_names": ["joe pesci"], "entity_types": ["Director"]}
{"sentence": "what are some horror movies from the 1970s", "entity_names": ["horror", "1970s"], "entity_types": ["Genre", "Year"]}
{"sentence": "show me a deborah harry movie", "entity_names": ["deborah harry"], "entity_types": ["Actor"]}
{"sentence": "kung fu panda 2", "entity_names": ["kung fu panda 2"], "entity_types": ["Title"]}
{"sentence": "what year was the movie the seven year itch made", "entity_names": ["year", "the seven year itch"], "entity_types": ["Year", "Title"]}
{"sentence": "is there a comedy crime drama", "entity_names": ["comedy crime drama"], "entity_types": ["Genre"]}
{"sentence": "what movie stared john travolta and debra winger", "entity_names": ["john travolta", "debra winger"], "entity_types": ["Actor", "Actor"]}
{"sentence": "directors of all the batman movies", "entity_names": ["batman"], "entity_types": ["Title"]}
{"sentence": "what are the best werewolf movies", "entity_names": ["best", "werewolf"], "entity_types": ["Review", "Plot"]}
{"sentence": "who voices shrek in shrek", "entity_names": ["shrek", "shrek"], "entity_types": ["Character", "Title"]}
{"sentence": "was ray liota in any comedies", "entity_names": ["ray liota", "comedies"], "entity_types": ["Actor", "Genre"]}
{"sentence": "what movie stars reese witherspoon in 2004", "entity_names": ["reese witherspoon", "2004"], "entity_types": ["Actor", "Year"]}
{"sentence": "what movie did ursula andress first appear in", "entity_names": ["ursula andress"], "entity_types": ["Actor"]}
{"sentence": "are there any movies set in the middle east starring george clooney", "entity_names": ["middle east", "george clooney"], "entity_types": ["Plot", "Actor"]}
{"sentence": "what was a love story about a woman who had alzheimers", "entity_names": ["love story about a woman who had alzheimers"], "entity_types": ["Plot"]}
{"sentence": "whats a john huston flick from the 1970s", "entity_names": ["john huston", "1970s"], "entity_types": ["Director", "Year"]}
{"sentence": "who played as princess fiona in shrek", "entity_names": ["princess fiona", "shrek"], "entity_types": ["Character", "Title"]}
{"sentence": "is the last airbender rated g", "entity_names": ["the last airbender", "rated g"], "entity_types": ["Title", "MPAA Rating"]}
{"sentence": "show me some grat action movies from the 90s", "entity_names": ["action movies", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "show me dramas about the british royal family", "entity_names": ["dramas", "british royal family"], "entity_types": ["Genre", "Plot"]}
{"sentence": "what was the first movie ever released", "entity_names": ["first"], "entity_types": ["Year"]}
{"sentence": "are there any movies with catherine ohara that were set during christmas time", "entity_names": ["catherine ohara", "set during christmas time"], "entity_types": ["Actor", "Plot"]}
{"sentence": "show me a james bond movie starring sean connery", "entity_names": ["james bond", "sean connery"], "entity_types": ["Character", "Actor"]}
{"sentence": "what was the title of the bio pic about robert e howard", "entity_names": ["robert e howard"], "entity_types": ["Actor"]}
{"sentence": "what is the theme song to stand by me", "entity_names": ["stand by me"], "entity_types": ["Title"]}
{"sentence": "when did runaway jury come out", "entity_names": ["runaway jury"], "entity_types": ["Title"]}
{"sentence": "who directed the japanese film versus", "entity_names": ["versus"], "entity_types": ["Title"]}
{"sentence": "show me a film that ben stiller directed", "entity_names": ["ben stiller"], "entity_types": ["Director"]}
{"sentence": "name a kirk douglas science fiction film", "entity_names": ["kirk douglas", "science fiction"], "entity_types": ["Actor", "Genre"]}
{"sentence": "are there any five star kevin bacon movies", "entity_names": ["five star", "kevin bacon"], "entity_types": ["Viewers' Rating", "Actor"]}
{"sentence": "who stars in the girl with the dragon tattoo", "entity_names": ["girl with the dragon tattoo"], "entity_types": ["Title"]}
{"sentence": "who was the male lead in gone with the wind", "entity_names": ["gone with the wind"], "entity_types": ["Title"]}
{"sentence": "who played jake sully in avatar", "entity_names": ["jake sully", "avatar"], "entity_types": ["Character", "Title"]}
{"sentence": "who directed pirates of the caribbean", "entity_names": ["pirates of the caribbean"], "entity_types": ["Title"]}
{"sentence": "what are some fun highly rated action movieds", "entity_names": ["fun", "highly rated", "action"], "entity_types": ["Review", "Viewers' Rating", "Genre"]}
{"sentence": "did people like or hate the last twilight movie", "entity_names": ["like", "hate", "twilight"], "entity_types": ["Review", "Review", "Title"]}
{"sentence": "what are the best johnny depp movies", "entity_names": ["johnny depp"], "entity_types": ["Actor"]}
{"sentence": "show me movies directed by alexander payne from the 2000s", "entity_names": ["alexander payne", "2000s"], "entity_types": ["Director", "Year"]}
{"sentence": "what are some good horror movies from 1974", "entity_names": ["good", "horror", "1974"], "entity_types": ["Review", "Genre", "Year"]}
{"sentence": "find me a movie with the song over the rainbow in it", "entity_names": ["over the rainbow"], "entity_types": ["Song"]}
{"sentence": "i want a 1960s zombie flick", "entity_names": ["i want a 1960s zombie flick"], "entity_types": ["Genre"]}
{"sentence": "how many lord of the rings films are there", "entity_names": ["lord of the rings"], "entity_types": ["Title"]}
{"sentence": "show me the latest trailer for the avengers", "entity_names": ["trailer", "the avengers"], "entity_types": ["Trailer", "Title"]}
{"sentence": "who directed beaty and the beast", "entity_names": ["beaty and the beast"], "entity_types": ["Title"]}
{"sentence": "show me a comedy with nick swardson", "entity_names": ["comedy", "nick swardson"], "entity_types": ["Genre", "Actor"]}
{"sentence": "how many movies are in the sex and the city series", "entity_names": ["sex and the city"], "entity_types": ["Title"]}
{"sentence": "did martin scorsese and johnny depp ever work together", "entity_names": ["martin scorsese", "johnny depp"], "entity_types": ["Director", "Actor"]}
{"sentence": "who directed the film the lorax", "entity_names": ["the lorax"], "entity_types": ["Title"]}
{"sentence": "has samuel jackson been in any thriller flicks", "entity_names": ["samuel jackson", "thriller"], "entity_types": ["Actor", "Genre"]}
{"sentence": "todd", "entity_names": ["todd"], "entity_types": ["Character"]}
{"sentence": "what was the film about brandon teena", "entity_names": ["brandon teena"], "entity_types": ["Character"]}
{"sentence": "who starred in drive", "entity_names": ["drive"], "entity_types": ["Title"]}
{"sentence": "what is kingdom of heaven rated", "entity_names": ["kingdom of heaven"], "entity_types": ["Title"]}
{"sentence": "super man", "entity_names": ["super man"], "entity_types": ["Character"]}
{"sentence": "what is a monty python film about king arthur", "entity_names": ["monty python", "king arthur"], "entity_types": ["Plot", "Plot"]}
{"sentence": "who was the actor in the movie teen wolf", "entity_names": ["teen wolf"], "entity_types": ["Title"]}
{"sentence": "show me classic comedies starring bill murray", "entity_names": ["classic comedies", "bill murray"], "entity_types": ["Genre", "Actor"]}
{"sentence": "when did underworld come out", "entity_names": ["underworld"], "entity_types": ["Title"]}
{"sentence": "find a film with animated skeletons", "entity_names": ["animated skeletons"], "entity_types": ["Plot"]}
{"sentence": "what animated voices has eddie murphy done", "entity_names": ["animated", "eddie murphy"], "entity_types": ["Genre", "Actor"]}
{"sentence": "please list rated r noir films", "entity_names": ["rated r", "noir"], "entity_types": ["MPAA Rating", "Genre"]}
{"sentence": "show me the trailer for rage", "entity_names": ["trailer", "rage"], "entity_types": ["Trailer", "Title"]}
{"sentence": "are there any films with volcanos", "entity_names": ["volcanos"], "entity_types": ["Plot"]}
{"sentence": "what is act of valor rated", "entity_names": ["act of valor", "rated"], "entity_types": ["Title", "MPAA Rating"]}
{"sentence": "who directed terminator 2 judgement day", "entity_names": ["terminator 2 judgement day"], "entity_types": ["Title"]}
{"sentence": "what horror movies have snakes", "entity_names": ["horror", "snakes"], "entity_types": ["Genre", "Plot"]}
{"sentence": "what was the name of clive owens character in sin city", "entity_names": ["clive owens", "sin city"], "entity_types": ["Actor", "Title"]}
{"sentence": "how many charlies angels movies are there", "entity_names": ["charlies angels"], "entity_types": ["Title"]}
{"sentence": "i am looking for a pg rated disney movie", "entity_names": ["pg", "disney"], "entity_types": ["MPAA Rating", "Genre"]}
{"sentence": "how many star wars films are there", "entity_names": ["star wars"], "entity_types": ["Title"]}
{"sentence": "who is directing the hobbit", "entity_names": ["the hobbit"], "entity_types": ["Title"]}
{"sentence": "what did critics think of j edgar", "entity_names": ["j edgar"], "entity_types": ["Title"]}
{"sentence": "who starred in the movie jurassic park", "entity_names": ["jurassic park"], "entity_types": ["Title"]}
{"sentence": "how many movies has clint eastwood directed since 1995", "entity_names": ["clint eastwood", "since 1995"], "entity_types": ["Director", "Year"]}
{"sentence": "what is the name of the highest grossing movie of 2011", "entity_names": ["2011"], "entity_types": ["Year"]}
{"sentence": "when did the game come out", "entity_names": ["the game"], "entity_types": ["Title"]}
{"sentence": "has john candy made any must see movies", "entity_names": ["john candy", "must see"], "entity_types": ["Actor", "Review"]}
{"sentence": "are there any horror films with don rickles", "entity_names": ["horror", "don rickles"], "entity_types": ["Genre", "Actor"]}
{"sentence": "find me the movie with the song married life", "entity_names": ["married life"], "entity_types": ["Song"]}
{"sentence": "show me an oscar winning science fiction movie", "entity_names": ["oscar winning", "science fiction"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "give me a list of legal dramas from the 70s", "entity_names": ["legal", "dramas", "70s"], "entity_types": ["Plot", "Genre", "Year"]}
{"sentence": "rainbow 6", "entity_names": ["rainbow 6"], "entity_types": ["Title"]}
{"sentence": "show me a wes craven movie", "entity_names": ["wes craven"], "entity_types": ["Actor"]}
{"sentence": "show me action movies that deal with stealing cars", "entity_names": ["action", "stealing cars"], "entity_types": ["Genre", "Plot"]}
{"sentence": "when did the first hobbit movie come out", "entity_names": ["hobbit", "come out"], "entity_types": ["Plot", "Year"]}
{"sentence": "what were the reviews like for the man who shot liberty valance", "entity_names": ["the man who shot liberty valance"], "entity_types": ["Title"]}
{"sentence": "show me any action movies that are in theatres right now", "entity_names": ["action", "right now"], "entity_types": ["Genre", "Year"]}
{"sentence": "i want an fantasy action adventure film", "entity_names": ["fantasy action adventure"], "entity_types": ["Genre"]}
{"sentence": "what films did pearce brosnan star in", "entity_names": ["pearce brosnan"], "entity_types": ["Actor"]}
{"sentence": "what is a good romance movie with jennifer aniston", "entity_names": ["good", "romance", "jennifer aniston"], "entity_types": ["Review", "Genre", "Actor"]}
{"sentence": "what is the plot of the wild bunch", "entity_names": ["the wild bunch"], "entity_types": ["Title"]}
{"sentence": "who starred in good burger", "entity_names": ["good burger"], "entity_types": ["Title"]}
{"sentence": "which harry potter movies have robert pattinson", "entity_names": ["harry potter", "robert pattinson"], "entity_types": ["Title", "Actor"]}
{"sentence": "when did goodfellas come out", "entity_names": ["goodfellas"], "entity_types": ["Title"]}
{"sentence": "show me the highest viwer rated movies from 1993", "entity_names": ["highest", "1993"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "is princess bride a good film for children", "entity_names": ["princess bride"], "entity_types": ["Title"]}
{"sentence": "does the film speed racer use the original song from the tv show", "entity_names": ["speed racer"], "entity_types": ["Title"]}
{"sentence": "what was the third sequel in the star wars film series", "entity_names": ["star wars"], "entity_types": ["Title"]}
{"sentence": "what is twilight rated", "entity_names": ["twilight"], "entity_types": ["Title"]}
{"sentence": "what is the best viewer rated vampire film", "entity_names": ["best", "vampire"], "entity_types": ["Viewers' Rating", "Plot"]}
{"sentence": "find me the number of samurai films made in the 1960s", "entity_names": ["samurai", "1960s"], "entity_types": ["Plot", "Year"]}
{"sentence": "what movie have action and suspense", "entity_names": ["action", "suspense"], "entity_types": ["Genre", "Genre"]}
{"sentence": "did clint eastwood make any cowboy movies in the 1970s", "entity_names": ["clint eastwood", "1970s"], "entity_types": ["Actor", "Year"]}
{"sentence": "how many academy awards has daniel day lewis won", "entity_names": ["daniel day lewis"], "entity_types": ["Actor"]}
{"sentence": "which movie take place in london", "entity_names": ["take place in london"], "entity_types": ["Plot"]}
{"sentence": "show me the half baked cover", "entity_names": ["half baked"], "entity_types": ["Title"]}
{"sentence": "who was the lead character in billy madison", "entity_names": ["billy madison"], "entity_types": ["Title"]}
{"sentence": "show me listing that have micheal in them", "entity_names": ["micheal"], "entity_types": ["Actor"]}
{"sentence": "whats the movie with the line home is where you hang your hat", "entity_names": [], "entity_types": []}
{"sentence": "name the director of 2001 a space odyssey", "entity_names": ["2001 a space odyssey"], "entity_types": ["Title"]}
{"sentence": "find me a movie by james cameron", "entity_names": ["james cameron"], "entity_types": ["Director"]}
{"sentence": "show the 1953 western starring alan ladd", "entity_names": ["1953", "western", "alan ladd"], "entity_types": ["Year", "Genre", "Actor"]}
{"sentence": "who played dwight mccarthy in sin city", "entity_names": ["dwight mccarthy", "sin city"], "entity_types": ["Character", "Title"]}
{"sentence": "what year did the original black and white casablanca come out", "entity_names": ["black and white", "casablanca"], "entity_types": ["Genre", "Title"]}
{"sentence": "i am looking for a movie starring elvis with scenes at a beach", "entity_names": ["elvis", "beach"], "entity_types": ["Actor", "Plot"]}
{"sentence": "what movie has seals kiss from a rose in it", "entity_names": ["kiss from a rose"], "entity_types": ["Song"]}
{"sentence": "i am looking for jennifer love hewitt movies from the 1990s", "entity_names": ["jennifer love hewitt", "1990s"], "entity_types": ["Actor", "Year"]}
{"sentence": "was there a romantic film noir", "entity_names": ["romantic film noir"], "entity_types": ["Genre"]}
{"sentence": "which romantic comedies are rated pg", "entity_names": ["romantic", "pg"], "entity_types": ["Genre", "MPAA Rating"]}
{"sentence": "list a quote from jaws", "entity_names": ["jaws"], "entity_types": ["Title"]}
{"sentence": "find all romantic period pieces starring hugh grant", "entity_names": ["romantic", "period pieces", "hugh grant"], "entity_types": ["Genre", "Plot", "Actor"]}
{"sentence": "what star wars movie had darth maul", "entity_names": ["darth maul"], "entity_types": ["Character"]}
{"sentence": "find a trailer for space 2010", "entity_names": ["trailer", "space 2010"], "entity_types": ["Trailer", "Title"]}
{"sentence": "what was the name of ariels seagull in the little mermaid", "entity_names": ["ariels seagull", "the little mermaid"], "entity_types": ["Character", "Title"]}
{"sentence": "what year did blazing saddles release", "entity_names": ["blazing saddles"], "entity_types": ["Title"]}
{"sentence": "first fairy tale disney movie", "entity_names": ["fairy tale disney"], "entity_types": ["Genre"]}
{"sentence": "what was the movie of the year in 1994", "entity_names": ["movie of the year", "1994"], "entity_types": ["Review", "Year"]}
{"sentence": "is there any iron man 3", "entity_names": ["iron man 3"], "entity_types": ["Title"]}
{"sentence": "what was the third harry potter movie called", "entity_names": ["third harry potter movie"], "entity_types": ["Title"]}
{"sentence": "name the director if the fifth harry potter movie", "entity_names": ["fifth harry potter movie"], "entity_types": ["Title"]}
{"sentence": "was bill paxton in twister", "entity_names": ["bill paxton", "twister"], "entity_types": ["Actor", "Title"]}
{"sentence": "did the director of pet shop make any other movies", "entity_names": ["pet shop"], "entity_types": ["Title"]}
{"sentence": "what city was the famous car chase from the french connection in", "entity_names": ["car chase", "french connection"], "entity_types": ["Plot", "Title"]}
{"sentence": "what movie was directed by steven spielberg with the alien", "entity_names": ["steven spielberg", "alien"], "entity_types": ["Director", "Plot"]}
{"sentence": "show me gene hackmans first film", "entity_names": ["gene hackmans"], "entity_types": ["Actor"]}
{"sentence": "the must see movie in chinese", "entity_names": ["chinese"], "entity_types": ["Plot"]}
{"sentence": "what movies got a great review by roger ebert in 2011", "entity_names": ["great review", "2011"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "show a spy movie from the 1930s", "entity_names": ["spy", "1930s"], "entity_types": ["Plot", "Year"]}
{"sentence": "look for the movie mean girls", "entity_names": ["mean girls"], "entity_types": ["Title"]}
{"sentence": "name a movie with carmen miranda and the marx brothers", "entity_names": ["carmen miranda", "the marx brothers"], "entity_types": ["Actor", "Actor"]}
{"sentence": "cheech and chong movies", "entity_names": ["cheech and chong"], "entity_types": ["Actor"]}
{"sentence": "what was twister rated", "entity_names": ["twister", "rated"], "entity_types": ["Title", "MPAA Rating"]}
{"sentence": "what is the newest version of cat people", "entity_names": ["cat people"], "entity_types": ["Title"]}
{"sentence": "show me harry potter", "entity_names": ["harry potter"], "entity_types": ["Character"]}
{"sentence": "can you find me a romantic comedy with natalie portman", "entity_names": ["romantic", "comedy", "natalie portman"], "entity_types": ["Plot", "Genre", "Actor"]}
{"sentence": "did gregg kinnear star in any movies directed my michael mann", "entity_names": ["gregg kinnear", "michael mann"], "entity_types": ["Actor", "Director"]}
{"sentence": "name the theme song for 2001 a space odyssey", "entity_names": ["theme song", "2001 a space odyssey"], "entity_types": ["Song", "Title"]}
{"sentence": "what movies had the biggest explosions", "entity_names": ["biggest explosions"], "entity_types": ["Plot"]}
{"sentence": "what are the police academy films", "entity_names": ["police academy"], "entity_types": ["Title"]}
{"sentence": "who sang my heart will go on from titanic", "entity_names": ["my heart will go on", "titanic"], "entity_types": ["Song", "Title"]}
{"sentence": "was the rock in drive", "entity_names": ["rock", "drive"], "entity_types": ["Actor", "Title"]}
{"sentence": "show me a list of comedies from the 1970s", "entity_names": ["comedies", "1970s"], "entity_types": ["Genre", "Year"]}
{"sentence": "name a movie with joseph cotton and orson welles", "entity_names": ["joseph cotton", "orson welles"], "entity_types": ["Actor", "Actor"]}
{"sentence": "get a nostradamous movie", "entity_names": ["nostradamous"], "entity_types": ["Character"]}
{"sentence": "name the theme song of war horse", "entity_names": ["theme song", "war horse"], "entity_types": ["Song", "Title"]}
{"sentence": "was there a time travelling high school student film", "entity_names": ["time travelling high school student"], "entity_types": ["Plot"]}
{"sentence": "what is a horror film released in 1981", "entity_names": ["horror", "1981"], "entity_types": ["Genre", "Year"]}
{"sentence": "what did michael bay direct besides transformers", "entity_names": ["michael bay", "transformers"], "entity_types": ["Director", "Title"]}
{"sentence": "look for pirates of the caribbean series", "entity_names": ["pirates of the caribbean"], "entity_types": ["Title"]}
{"sentence": "how many saw films are there", "entity_names": ["saw"], "entity_types": ["Title"]}
{"sentence": "what was elvis presleys last film", "entity_names": ["elvis presleys"], "entity_types": ["Actor"]}
{"sentence": "name a flick from the 1980s with the word laser in the title", "entity_names": ["1980s", "laser"], "entity_types": ["Year", "Title"]}
{"sentence": "find movies directed by judd apatow", "entity_names": ["judd apatow"], "entity_types": ["Director"]}
{"sentence": "what romance films star rachel mcadams", "entity_names": ["romance", "rachel mcadams"], "entity_types": ["Genre", "Actor"]}
{"sentence": "find romantic comedies starring jennifer lopez", "entity_names": ["romantic comedies", "jennifer lopez"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what were the reviews for temple grandin", "entity_names": ["temple grandin"], "entity_types": ["Title"]}
{"sentence": "was a character called the torch in a flick", "entity_names": ["the torch"], "entity_types": ["Character"]}
{"sentence": "look for dramas that have denzel washington and are academy award winners or nominees", "entity_names": ["dramas", "denzel washington", "academy award winners or nominees"], "entity_types": ["Genre", "Actor", "Viewers' Rating"]}
{"sentence": "show me movies directed by herbert ross with dancing as part of its story line", "entity_names": ["herbert ross", "dancing"], "entity_types": ["Director", "Plot"]}
{"sentence": "where can i find a full length trailer for dark shadows", "entity_names": ["trailer", "dark shadows"], "entity_types": ["Trailer", "Title"]}
{"sentence": "whats a 1940s bernard herman soundtrack film", "entity_names": ["1940s"], "entity_types": ["Year"]}
{"sentence": "show me which movie won the academy award for best picture in 2009", "entity_names": ["academy award for best picture", "2009"], "entity_types": ["Review", "Year"]}
{"sentence": "who directed blazing saddles", "entity_names": ["blazing saddles"], "entity_types": ["Title"]}
{"sentence": "in what spiderman film did peter parker turns evil", "entity_names": ["spiderman", "peter parker", "turns evil"], "entity_types": ["Title", "Actor", "Plot"]}
{"sentence": "best crome movie from almodovar", "entity_names": ["best", "almodovar"], "entity_types": ["Review", "Director"]}
{"sentence": "find any elvis presleyovies", "entity_names": ["elvis presleyovies"], "entity_types": ["Actor"]}
{"sentence": "who wrote the them song for the harry potter movies", "entity_names": ["harry potter"], "entity_types": ["Character"]}
{"sentence": "what film genre is the nightmare before christmas", "entity_names": ["the nightmare before christmas"], "entity_types": ["Title"]}
{"sentence": "whats the name of the movie where the world ends", "entity_names": ["the world ends"], "entity_types": ["Plot"]}
{"sentence": "name the lindsay lohan movie that features a living race car", "entity_names": ["lindsay lohan"], "entity_types": ["Actor"]}
{"sentence": "play a trailer for erin brockovich", "entity_names": ["erin brockovich"], "entity_types": ["Title"]}
{"sentence": "name the director of the boy in the striped pajamas", "entity_names": ["director", "the boy in the striped pajamas"], "entity_types": ["Director", "Title"]}
{"sentence": "whats an old terry gilliam film", "entity_names": ["old", "terry gilliam"], "entity_types": ["Year", "Director"]}
{"sentence": "how many oscars was bridesmaids nominated for", "entity_names": ["bridesmaids"], "entity_types": ["Title"]}
{"sentence": "how many pirates of the caribbean movies is johnny depp in", "entity_names": ["pirates of the caribbean", "johnny depp"], "entity_types": ["Title", "Actor"]}
{"sentence": "who played napoleon dynamite in the movie of the same name", "entity_names": ["napoleon dynamite", "movie of the same name"], "entity_types": ["Character", "Title"]}
{"sentence": "was sean astin in the goonies", "entity_names": ["sean astin", "the goonies"], "entity_types": ["Actor", "Title"]}
{"sentence": "what is the movie made by james cameron in 2008", "entity_names": ["james cameron", "2008"], "entity_types": ["Director", "Year"]}
{"sentence": "i would like to watch a romance about soccer", "entity_names": ["romance", "soccer"], "entity_types": ["Genre", "Plot"]}
{"sentence": "what is the rating of the hunger games", "entity_names": ["rating", "the hunger games"], "entity_types": ["MPAA Rating", "Title"]}
{"sentence": "how much did the rock make in drive", "entity_names": ["the rock", "drive"], "entity_types": ["Actor", "Title"]}
{"sentence": "the 100 wrost movies ever", "entity_names": ["wrost movies ever"], "entity_types": ["Review"]}
{"sentence": "was oprah in any movies", "entity_names": ["oprah"], "entity_types": ["Actor"]}
{"sentence": "what year did terminator 2 come out", "entity_names": ["terminator 2"], "entity_types": ["Title"]}
{"sentence": "show me the harry potter series", "entity_names": ["harry potter"], "entity_types": ["Character"]}
{"sentence": "what film features the theme song there youll be", "entity_names": ["there youll be"], "entity_types": ["Song"]}
{"sentence": "play a song from blow soundtrack", "entity_names": ["blow"], "entity_types": ["Song"]}
{"sentence": "who played as morpheus in the matrix", "entity_names": ["morpheus", "the matrix"], "entity_types": ["Character", "Title"]}
{"sentence": "show me movies directed by barbra streisand", "entity_names": ["barbra streisand"], "entity_types": ["Director"]}
{"sentence": "find harrison ford movies", "entity_names": ["harrison ford"], "entity_types": ["Actor"]}
{"sentence": "did josh radnor direct any movies", "entity_names": ["josh radnor"], "entity_types": ["Director"]}
{"sentence": "show me a movie with bicycles and children from the 1980s", "entity_names": ["bicycles and children", "1980s"], "entity_types": ["Plot", "Year"]}
{"sentence": "show me a list of adam sandler movies", "entity_names": ["adam sandler"], "entity_types": ["Actor"]}
{"sentence": "what was tom hanks first movie", "entity_names": ["tom hanks", "first"], "entity_types": ["Actor", "Year"]}
{"sentence": "find comedy movies with kevin kline from the 1980s", "entity_names": ["comedy", "kevin kline", "1980s"], "entity_types": ["Genre", "Actor", "Year"]}
{"sentence": "list any movies directed by steven spielberg", "entity_names": ["steven spielberg"], "entity_types": ["Director"]}
{"sentence": "look for the movies similar to gossip girl", "entity_names": ["gossip girl"], "entity_types": ["Title"]}
{"sentence": "list uma thurman movies", "entity_names": ["uma thurman"], "entity_types": ["Actor"]}
{"sentence": "show 1939 films directed by william wyler", "entity_names": ["1939", "william wyler"], "entity_types": ["Year", "Director"]}
{"sentence": "show me a list of independent comedy films made in 2009", "entity_names": ["independent comedy", "2009"], "entity_types": ["Title", "Year"]}
{"sentence": "list all science fiction movies playing the next 12 hours", "entity_names": ["science fiction"], "entity_types": ["Genre"]}
{"sentence": "is there a burt lancaster circus movie", "entity_names": ["burt lancaster", "circus"], "entity_types": ["Actor", "Plot"]}
{"sentence": "run twilight", "entity_names": ["twilight"], "entity_types": ["Title"]}
{"sentence": "did denzel washington act in any romantic comedy movies", "entity_names": ["denzel washington", "romantic comedy"], "entity_types": ["Actor", "Genre"]}
{"sentence": "did michael jordan made a cartoon film", "entity_names": ["cartoon film"], "entity_types": ["Genre"]}
{"sentence": "who was the cast of terminator", "entity_names": ["cast", "terminator"], "entity_types": ["Actor", "Title"]}
{"sentence": "show me a trailer for an alec guiness flick", "entity_names": ["trailer", "alec guiness"], "entity_types": ["Trailer", "Actor"]}
{"sentence": "can you find me a list of romantic comedies from 2008", "entity_names": ["romantic comedies", "2008"], "entity_types": ["Genre", "Year"]}
{"sentence": "how many comedy films did arnold schwarzenegger appear in", "entity_names": ["arnold schwarzenegger"], "entity_types": ["Actor"]}
{"sentence": "who played leo marvin in what about bob", "entity_names": ["leo marvin", "what about bob"], "entity_types": ["Character", "Title"]}
{"sentence": "what was the first disney movie", "entity_names": ["first"], "entity_types": ["Year"]}
{"sentence": "best action thriller to watch with date", "entity_names": ["best", "action thriller"], "entity_types": ["Review", "Genre"]}
{"sentence": "what was thirteen candles rated", "entity_names": ["thirteen candles", "rated"], "entity_types": ["Title", "MPAA Rating"]}
{"sentence": "what is the movie that has the song zip a dee doo dah", "entity_names": ["zip a dee doo dah"], "entity_types": ["Song"]}
{"sentence": "who stared in the movie the bank job", "entity_names": ["the bank job"], "entity_types": ["Title"]}
{"sentence": "name the film that features the song sway by bic runga", "entity_names": ["sway by bic runga"], "entity_types": ["Song"]}
{"sentence": "who directed star wars", "entity_names": ["star wars"], "entity_types": ["Title"]}
{"sentence": "find movies called best action movie of the year", "entity_names": ["best action movie of the year"], "entity_types": ["Review"]}
{"sentence": "what family movie got the best rating from kids", "entity_names": ["family movie", "best rating"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "list all versions of south pacific", "entity_names": ["south pacific"], "entity_types": ["Title"]}
{"sentence": "show me the trailer for the ryan gosling movie", "entity_names": ["trailer", "ryan gosling"], "entity_types": ["Trailer", "Actor"]}
{"sentence": "which movies about the mob are rated r", "entity_names": ["mob", "rated r"], "entity_types": ["Plot", "MPAA Rating"]}
{"sentence": "who acted in the title role of michael collins", "entity_names": ["michael collins"], "entity_types": ["Title"]}
{"sentence": "in which movies did leonardo dicaprio play a cop or federal agent", "entity_names": ["leonardo dicaprio", "cop", "federal agent"], "entity_types": ["Actor", "Character", "Character"]}
{"sentence": "what is a good r rated mystery", "entity_names": ["good", "r", "mystery"], "entity_types": ["Review", "MPAA Rating", "Genre"]}
{"sentence": "did jessica alba make any new movies", "entity_names": ["jessica alba"], "entity_types": ["Actor"]}
{"sentence": "did diablo cody direct any movies this year", "entity_names": ["diablo cody", "this year"], "entity_types": ["Director", "Year"]}
{"sentence": "ill never let go is a famous quote from what 1990s movie", "entity_names": ["1990s"], "entity_types": ["Year"]}
{"sentence": "show me a quentin tarantino film with uma thurman", "entity_names": ["quentin tarantino", "uma thurman"], "entity_types": ["Director", "Actor"]}
{"sentence": "find me a horror movie that fans really loved", "entity_names": ["horror", "fans really loved"], "entity_types": ["Genre", "Review"]}
{"sentence": "show me the title of the film thought to be a real snuff film", "entity_names": ["a real snuff film"], "entity_types": ["Plot"]}
{"sentence": "whats a film based on a best selling contemporary novel", "entity_names": ["best selling contemporary novel"], "entity_types": ["Plot"]}
{"sentence": "what role won sean connery an academy award", "entity_names": ["sean connery"], "entity_types": ["Actor"]}
{"sentence": "find 1980s comedy films with an r rating", "entity_names": ["1980s", "comedy", "r rating"], "entity_types": ["Year", "Genre", "MPAA Rating"]}
{"sentence": "what film genre is young frankenstein", "entity_names": ["young frankenstein"], "entity_types": ["Title"]}
{"sentence": "what movie did alen menken score", "entity_names": ["alen menken"], "entity_types": ["Song"]}
{"sentence": "find an animated movie about the beatles", "entity_names": ["animated", "beatles"], "entity_types": ["Genre", "Plot"]}
{"sentence": "show me a shrek clip", "entity_names": ["shrek", "clip"], "entity_types": ["Title", "Trailer"]}
{"sentence": "what is the worst viewer rated vampire film", "entity_names": ["worst viewer rated", "vampire"], "entity_types": ["Viewers' Rating", "Plot"]}
{"sentence": "what movies did judy garland star in", "entity_names": ["judy garland"], "entity_types": ["Actor"]}
{"sentence": "what is the name of john travoltas character in the film pulp fiction", "entity_names": ["john travoltas", "character", "pulp fiction"], "entity_types": ["Actor", "Character", "Title"]}
{"sentence": "find a romance movie with tom cruise", "entity_names": ["romance", "tom cruise"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what is a rated r film that features katherine heigl", "entity_names": ["rated r", "katherine heigl"], "entity_types": ["MPAA Rating", "Actor"]}
{"sentence": "what character did michael j fox voice in homeward bound", "entity_names": ["michael j fox", "homeward bound"], "entity_types": ["Actor", "Title"]}
{"sentence": "whats a monty python flick about philosophy", "entity_names": ["monty python"], "entity_types": ["Character"]}
{"sentence": "show me a list of movies starring shirley temple", "entity_names": ["shirley temple"], "entity_types": ["Actor"]}
{"sentence": "who said asta la vista baby", "entity_names": [], "entity_types": []}
{"sentence": "i want a jamie lee curtis horror film", "entity_names": ["jamie lee curtis", "horror"], "entity_types": ["Actor", "Genre"]}
{"sentence": "what are some funny betty white movies", "entity_names": ["funny", "betty white"], "entity_types": ["Genre", "Actor"]}
{"sentence": "show me a film where a dog has a relationship with a cat", "entity_names": ["a dog has a relationship with a cat"], "entity_types": ["Plot"]}
{"sentence": "find movies with the stones songs performed in it", "entity_names": ["stones songs"], "entity_types": ["Song"]}
{"sentence": "what film genre is blazing saddles", "entity_names": ["blazing saddles"], "entity_types": ["Title"]}
{"sentence": "show me a list of family movies with holiday themes", "entity_names": ["family", "holiday themes"], "entity_types": ["Genre", "Plot"]}
{"sentence": "true stories about hispanic musicians", "entity_names": ["true", "hispanic musicians"], "entity_types": ["Genre", "Plot"]}
{"sentence": "list all movies staring brad pitt", "entity_names": ["brad pitt"], "entity_types": ["Actor"]}
{"sentence": "what movies has ellen page been in", "entity_names": ["ellen page"], "entity_types": ["Actor"]}
{"sentence": "show me action movies with vin diesel", "entity_names": ["vin diesel"], "entity_types": ["Actor"]}
{"sentence": "run repo man", "entity_names": ["repo man"], "entity_types": ["Title"]}
{"sentence": "can you show me the movies cher was in", "entity_names": ["cher"], "entity_types": ["Actor"]}
{"sentence": "what was the name of harrison fords character in star wars", "entity_names": ["harrison fords", "star wars"], "entity_types": ["Actor", "Title"]}
{"sentence": "i want a r rated film that was later changed to pg", "entity_names": ["r rated", "pg"], "entity_types": ["MPAA Rating", "MPAA Rating"]}
{"sentence": "who played andy duphrane in shawshank redemption", "entity_names": ["andy duphrane", "shawshank redemption"], "entity_types": ["Character", "Title"]}
{"sentence": "what was james deans last film", "entity_names": ["james deans"], "entity_types": ["Actor"]}
{"sentence": "find an action flick with martin lawrence", "entity_names": ["martin lawrence"], "entity_types": ["Actor"]}
{"sentence": "were any movies adapted from stage in the 2000s", "entity_names": ["2000s"], "entity_types": ["Year"]}
{"sentence": "who directed drive", "entity_names": ["drive"], "entity_types": ["Title"]}
{"sentence": "show movies from the 1960s directed by stanley kubrick", "entity_names": ["1960s", "stanley kubrick"], "entity_types": ["Year", "Director"]}
{"sentence": "who directed blow", "entity_names": ["blow"], "entity_types": ["Title"]}
{"sentence": "show me action movies starring sylvester stallone", "entity_names": ["action", "sylvester stallone"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what movies has mariah carey been in", "entity_names": ["mariah carey"], "entity_types": ["Actor"]}
{"sentence": "what horror movies came out in the 80s", "entity_names": ["horror", "80s"], "entity_types": ["Genre", "Year"]}
{"sentence": "i want to see a action comedy", "entity_names": ["action comedy"], "entity_types": ["Genre"]}
{"sentence": "disney cartoon with classical music and animals", "entity_names": ["disney cartoon"], "entity_types": ["Plot"]}
{"sentence": "are there any horror movies with a g rating", "entity_names": ["horror", "g"], "entity_types": ["Genre", "MPAA Rating"]}
{"sentence": "whats the highest rated drama of 1990", "entity_names": ["highest rated", "drama", "1990"], "entity_types": ["Review", "Genre", "Year"]}
{"sentence": "what movies have been made in the 21st century pertaining to animal rescue", "entity_names": ["21st century", "animal rescue"], "entity_types": ["Year", "Plot"]}
{"sentence": "whats the movie with the trailer that has a teenage girl flashing a crowd", "entity_names": ["trailer", "teenage girl flashing a crowd"], "entity_types": ["Trailer", "Plot"]}
{"sentence": "what is the mpaa rating for star wars episode 5", "entity_names": ["mpaa", "star wars episode 5"], "entity_types": ["MPAA Rating", "Title"]}
{"sentence": "show me all of the films directed by clint eastwood", "entity_names": ["clint eastwood"], "entity_types": ["Director"]}
{"sentence": "who directed the pirates of the carribean movie", "entity_names": ["pirates of the carribean"], "entity_types": ["Title"]}
{"sentence": "name a movie where beer is important to the plot", "entity_names": ["beer"], "entity_types": ["Plot"]}
{"sentence": "who directed avatar", "entity_names": ["avatar"], "entity_types": ["Title"]}
{"sentence": "what was the best rated comedy of 2000", "entity_names": ["2000"], "entity_types": ["Year"]}
{"sentence": "search some movies with violence", "entity_names": ["violence"], "entity_types": ["Plot"]}
{"sentence": "who was the cast of airplane", "entity_names": ["airplane"], "entity_types": ["Title"]}
{"sentence": "find me a comedy movie from the past decade that is considered must see", "entity_names": ["comedy", "past decade", "must see"], "entity_types": ["Genre", "Year", "Review"]}
{"sentence": "find me the review of black swan", "entity_names": ["review", "black swan"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "give me a list of star wars movies", "entity_names": ["star wars"], "entity_types": ["Title"]}
{"sentence": "is the trailer for the dark knight rises out on the internet", "entity_names": ["trailer", "the dark knight rises"], "entity_types": ["Trailer", "Title"]}
{"sentence": "show the blow soundtrack", "entity_names": ["show the blow"], "entity_types": ["Title"]}
{"sentence": "which films feature both ashton kutcher and brittany murphy", "entity_names": ["ashton kutcher", "brittany murphy"], "entity_types": ["Actor", "Actor"]}
{"sentence": "what funny movie has danny devito in it", "entity_names": ["funny", "danny devito"], "entity_types": ["Review", "Actor"]}
{"sentence": "can you find the soundtrack of the austin powers movies from the 1990s", "entity_names": ["soundtrack", "austin powers", "1990s"], "entity_types": ["Song", "Title", "Year"]}
{"sentence": "who directed dracula dead and loving it", "entity_names": ["directed", "dracula dead and loving it"], "entity_types": ["Director", "Title"]}
{"sentence": "which movie stars emma watson playing another role aside from hermione granger", "entity_names": ["emma watson", "playing another role aside from", "hermione granger"], "entity_types": ["Actor", "Plot", "Character"]}
{"sentence": "show me movies with oscar winning actors", "entity_names": ["oscar winning actors"], "entity_types": ["Actor"]}
{"sentence": "who played batman in the first batman movie", "entity_names": ["batman", "batman"], "entity_types": ["Character", "Title"]}
{"sentence": "in what movies has kate winslet starred", "entity_names": ["kate winslet"], "entity_types": ["Actor"]}
{"sentence": "what film did richard gere and julia roberts star together", "entity_names": ["richard gere", "julia roberts"], "entity_types": ["Actor", "Actor"]}
{"sentence": "what year did the first terminator come out", "entity_names": ["terminator"], "entity_types": ["Title"]}
{"sentence": "list all films based on the wizard of oz books", "entity_names": ["wizard of oz"], "entity_types": ["Title"]}
{"sentence": "was pierce brosnan in sex in the city", "entity_names": ["pierce brosnan", "sex in the city"], "entity_types": ["Actor", "Title"]}
{"sentence": "what star trek movie had the best action scenes", "entity_names": ["star trek", "best action scenes"], "entity_types": ["Title", "Review"]}
{"sentence": "show me a non peter sellers film where the lead actor plays just one role", "entity_names": ["peter sellers"], "entity_types": ["Actor"]}
{"sentence": "list rebecca de mornay movies", "entity_names": ["rebecca de mornay"], "entity_types": ["Actor"]}
{"sentence": "which pg 13 movies has tom cruise starred in", "entity_names": ["pg 13", "tom cruise"], "entity_types": ["MPAA Rating", "Actor"]}
{"sentence": "what was the title song for kissing cousins", "entity_names": ["kissing cousins"], "entity_types": ["Title"]}
{"sentence": "i want to watch a science fiction movie with a score from john williams", "entity_names": ["science fiction", "score from john williams"], "entity_types": ["Genre", "Song"]}
{"sentence": "is there a john wayne mongol movie", "entity_names": ["john wayne", "mongol"], "entity_types": ["Actor", "Plot"]}
{"sentence": "find a pg 13 rated action movie starring harrison ford", "entity_names": ["pg 13", "action", "harrison ford"], "entity_types": ["MPAA Rating", "Genre", "Actor"]}
{"sentence": "what movies did quentin tarantino direct", "entity_names": ["quentin tarantino"], "entity_types": ["Director"]}
{"sentence": "find me all the movies with jason bourne", "entity_names": ["jason bourne"], "entity_types": ["Character"]}
{"sentence": "who directed space balls", "entity_names": ["space balls"], "entity_types": ["Title"]}
{"sentence": "find me the movie with the song i wanna be sedated", "entity_names": ["i wanna be sedated"], "entity_types": ["Song"]}
{"sentence": "who starred in avatar", "entity_names": ["avatar"], "entity_types": ["Title"]}
{"sentence": "how many sequels dim the movie critters have", "entity_names": ["critters"], "entity_types": ["Title"]}
{"sentence": "show me rambo movies from the 1980s", "entity_names": ["rambo", "1980s"], "entity_types": ["Title", "Year"]}
{"sentence": "what is the best movie about sharks", "entity_names": ["best", "sharks"], "entity_types": ["Review", "Plot"]}
{"sentence": "whats an old harvey coreman film", "entity_names": ["old", "harvey coreman"], "entity_types": ["Year", "Director"]}
{"sentence": "what is dorothys last name in the wizard of oz", "entity_names": ["dorothys", "the wizard of oz"], "entity_types": ["Character", "Title"]}
{"sentence": "did hans zimmer write the music for pirates of the carribean", "entity_names": ["hans zimmer", "pirates of the carribean"], "entity_types": ["Song", "Song"]}
{"sentence": "play a trailer for teen wolf", "entity_names": ["trailer", "teen wolf"], "entity_types": ["Trailer", "Title"]}
{"sentence": "what year was disneys pocohontas released", "entity_names": ["pocohontas"], "entity_types": ["Title"]}
{"sentence": "are there any batman movies rated pg", "entity_names": ["batman", "pg"], "entity_types": ["Title", "MPAA Rating"]}
{"sentence": "who directed superbad", "entity_names": ["superbad"], "entity_types": ["Title"]}
{"sentence": "what pg 13 movies feature goldie hawn", "entity_names": ["pg 13", "goldie hawn"], "entity_types": ["MPAA Rating", "Actor"]}
{"sentence": "are there any movies from 1996 that involve aliens", "entity_names": ["1996", "aliens"], "entity_types": ["Year", "Plot"]}
{"sentence": "i want to find the movie starring meryl streep and anne hathaway", "entity_names": ["meryl streep", "anne hathaway"], "entity_types": ["Actor", "Actor"]}
{"sentence": "in what year did the film vegas vacation come out", "entity_names": ["vegas vacation"], "entity_types": ["Title"]}
{"sentence": "what movie had the earth explode in the trailer", "entity_names": [], "entity_types": []}
{"sentence": "list a quote from the pirates of the carribean", "entity_names": ["pirates of the carribean"], "entity_types": ["Title"]}
{"sentence": "did leonardo dicaprio star in any movies based on a play by shakespeare", "entity_names": ["leonardo dicaprio", "based on a play by shakespeare"], "entity_types": ["Actor", "Plot"]}
{"sentence": "what year did star wars a new hope come out", "entity_names": ["star wars a new hope"], "entity_types": ["Title"]}
{"sentence": "what is the mpaa rating on 2001 a space odyssey", "entity_names": ["2001 a space odyssey"], "entity_types": ["Title"]}
{"sentence": "what is the most recent peter weller film", "entity_names": ["most recent", "peter weller"], "entity_types": ["Year", "Director"]}
{"sentence": "where can i see the trailer for the new 2012 avengers movie", "entity_names": ["trailer", "2012", "avengers"], "entity_types": ["Trailer", "Year", "Title"]}
{"sentence": "find the newest harry potter movie", "entity_names": ["harry potter"], "entity_types": ["Character"]}
{"sentence": "what is drive rated", "entity_names": ["drive"], "entity_types": ["Title"]}
{"sentence": "what character did morgan freeman play in shawshank redemption", "entity_names": ["morgan freeman", "shawshank redemption"], "entity_types": ["Actor", "Title"]}
{"sentence": "which professional football player stared in shaft", "entity_names": ["shaft"], "entity_types": ["Title"]}
{"sentence": "what was the last terminator film to be released", "entity_names": ["terminator"], "entity_types": ["Title"]}
{"sentence": "what are some funny arnold schwarzenegger comedies", "entity_names": ["arnold schwarzenegger", "comedies"], "entity_types": ["Actor", "Genre"]}
{"sentence": "i want a g rated film that was later changed to pg", "entity_names": ["g rated", "pg"], "entity_types": ["MPAA Rating", "MPAA Rating"]}
{"sentence": "whats a mainstream movie about pornos", "entity_names": ["mainstream", "about pornos"], "entity_types": ["Review", "Plot"]}
{"sentence": "what films use the song bird is the word", "entity_names": ["bird is the word"], "entity_types": ["Song"]}
{"sentence": "run a trailer for an audry murphy show", "entity_names": ["audry murphy"], "entity_types": ["Actor"]}
{"sentence": "find a horror movie with creepy little girls", "entity_names": ["horror", "creepy little girls"], "entity_types": ["Genre", "Plot"]}
{"sentence": "run a trailer for excalibur", "entity_names": ["trailer", "excalibur"], "entity_types": ["Trailer", "Title"]}
{"sentence": "show me a british film about wwii in the germany", "entity_names": ["wwii", "germany"], "entity_types": ["Plot", "Plot"]}
{"sentence": "i am looking for the blockbuster movies of 2001", "entity_names": ["blockbuster", "2001"], "entity_types": ["Genre", "Year"]}
{"sentence": "what superhero film stars hugh jackman in the lead role", "entity_names": ["superhero", "hugh jackman"], "entity_types": ["Genre", "Actor"]}
{"sentence": "were any movies about talking dolphins", "entity_names": ["talking dolphins"], "entity_types": ["Plot"]}
{"sentence": "find a chuck norris movie with a lot of martial arts fighting in it", "entity_names": ["chuck norris", "martial arts fighting"], "entity_types": ["Actor", "Plot"]}
{"sentence": "what is a fun action movie", "entity_names": ["fun", "action"], "entity_types": ["Review", "Genre"]}
{"sentence": "how many movies did bela lugosi appear in", "entity_names": ["bela lugosi"], "entity_types": ["Actor"]}
{"sentence": "was there a supernatural film noir", "entity_names": ["supernatural film noir"], "entity_types": ["Genre"]}
{"sentence": "show me the second star wars film", "entity_names": ["star wars"], "entity_types": ["Title"]}
{"sentence": "what was the release year of the last star wars movie", "entity_names": ["star wars"], "entity_types": ["Title"]}
{"sentence": "who directed saw 1", "entity_names": ["saw 1"], "entity_types": ["Title"]}
{"sentence": "are there any sean connery movies on tonight", "entity_names": ["sean connery"], "entity_types": ["Actor"]}
{"sentence": "dracula the 1960s version", "entity_names": ["dracula", "1960s"], "entity_types": ["Title", "Year"]}
{"sentence": "who directed fight club", "entity_names": ["fight club"], "entity_types": ["Title"]}
{"sentence": "find the movie with the yellow brick road", "entity_names": ["yellow brick road"], "entity_types": ["Plot"]}
{"sentence": "find a review for the last harry potter movie", "entity_names": ["harry potter"], "entity_types": ["Character"]}
{"sentence": "find an elvis presley beach movie", "entity_names": ["elvis presley", "beach movie"], "entity_types": ["Actor", "Plot"]}
{"sentence": "find the oldest movie directed by stephen king", "entity_names": ["stephen king"], "entity_types": ["Director"]}
{"sentence": "all scary movies from 2010", "entity_names": ["scary", "2010"], "entity_types": ["Genre", "Year"]}
{"sentence": "show movies about food", "entity_names": ["food"], "entity_types": ["Plot"]}
{"sentence": "what was the name of ariels prince in the little mermaid", "entity_names": ["ariels", "little mermaid"], "entity_types": ["Character", "Title"]}
{"sentence": "what year did goodfellas release", "entity_names": ["goodfellas"], "entity_types": ["Title"]}
{"sentence": "what movie had jim carrey as a dr seuss character", "entity_names": ["jim carrey", "dr seuss character"], "entity_types": ["Actor", "Character"]}
{"sentence": "are there any movies from the 2000s rated g that involve fish", "entity_names": ["2000s", "g", "fish"], "entity_types": ["Year", "MPAA Rating", "Plot"]}
{"sentence": "show me a movie with arnold schwarzenegger that is directed by james cameron", "entity_names": ["arnold schwarzenegger", "james cameron"], "entity_types": ["Actor", "Director"]}
{"sentence": "are there any mpaa rated tv ma films for elvis presley", "entity_names": ["tv ma", "elvis presley"], "entity_types": ["MPAA Rating", "Actor"]}
{"sentence": "what is the most recent movie directed by steven speilberg", "entity_names": ["most recent", "steven speilberg"], "entity_types": ["Year", "Director"]}
{"sentence": "which film won the raspberry award for halle berry", "entity_names": ["halle berry"], "entity_types": ["Actor"]}
{"sentence": "show me a wwii movie directed by steven spielberg and starring liam neeson", "entity_names": ["wwii movie", "steven spielberg", "liam neeson"], "entity_types": ["Plot", "Director", "Actor"]}
{"sentence": "was there a documentary about a russian electronics professor", "entity_names": ["documentary", "russian electronics professor"], "entity_types": ["Genre", "Plot"]}
{"sentence": "who starred in sex in the city", "entity_names": ["sex in the city"], "entity_types": ["Title"]}
{"sentence": "what chucky movie didnt have andy in it", "entity_names": ["chucky", "andy"], "entity_types": ["Character", "Actor"]}
{"sentence": "whats a john huston flick from the 1950s", "entity_names": ["john huston"], "entity_types": ["Actor"]}
{"sentence": "who was the main character in the last terminator movie", "entity_names": ["character", "terminator"], "entity_types": ["Character", "Title"]}
{"sentence": "what is the plot of upstaurs downstairs", "entity_names": ["upstaurs downstairs"], "entity_types": ["Title"]}
{"sentence": "get a yul brenner action flick", "entity_names": ["yul brenner", "action"], "entity_types": ["Actor", "Genre"]}
{"sentence": "i want a preview for a horror comedy", "entity_names": ["preview", "horror comedy"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "what is the movie step up rated", "entity_names": ["step up"], "entity_types": ["Title"]}
{"sentence": "what role did carrie ann moss play in the matrix", "entity_names": ["carrie ann moss", "the matrix"], "entity_types": ["Actor", "Title"]}
{"sentence": "which movies does charlie sheen star in", "entity_names": ["charlie sheen"], "entity_types": ["Actor"]}
{"sentence": "please list all childrens movies with mary kate and ashley", "entity_names": ["childrens", "mary kate", "ashley"], "entity_types": ["Genre", "Actor", "Actor"]}
{"sentence": "show me a comedy about a football team", "entity_names": ["comedy", "football team"], "entity_types": ["Genre", "Plot"]}
{"sentence": "is there an animated adult horror movie", "entity_names": ["animated adult horror"], "entity_types": ["Genre"]}
{"sentence": "cartoon with the voice of justin timberlake from the 2000s", "entity_names": ["cartoon", "justin timberlake", "2000s"], "entity_types": ["Genre", "Actor", "Year"]}
{"sentence": "find the movie with the song its hard out there for a pimp", "entity_names": ["its hard out there for a pimp"], "entity_types": ["Song"]}
{"sentence": "whats a rated r movie with jessica alba", "entity_names": ["r", "jessica alba"], "entity_types": ["MPAA Rating", "Actor"]}
{"sentence": "are there any good heist movies with a pg 13 rating", "entity_names": ["heist movies", "pg 13 rating"], "entity_types": ["Plot", "MPAA Rating"]}
{"sentence": "what was the name of the movie with brad pitt about robbing a casino", "entity_names": ["brad pitt", "robbing a casino"], "entity_types": ["Actor", "Plot"]}
{"sentence": "which tom hanks film features him as a stranded fed ex employee", "entity_names": ["tom hanks", "stranded fed ex employee"], "entity_types": ["Actor", "Plot"]}
{"sentence": "tell the plot of amistad", "entity_names": ["amistad"], "entity_types": ["Title"]}
{"sentence": "who directed twister", "entity_names": ["twister"], "entity_types": ["Title"]}
{"sentence": "what is the viewers rating of the favorite star wars episode", "entity_names": ["viewers rating", "star wars episode"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "what is the name of cameron diazs character in bad teacher", "entity_names": ["bad teacher"], "entity_types": ["Title"]}
{"sentence": "who directed stepbrothers", "entity_names": ["stepbrothers"], "entity_types": ["Title"]}
{"sentence": "find pg 13 movies with kevin costner", "entity_names": ["pg 13", "kevin costner"], "entity_types": ["MPAA Rating", "Actor"]}
{"sentence": "what is the name of the killer in the first saw", "entity_names": ["killer", "saw"], "entity_types": ["Character", "Title"]}
{"sentence": "what movie has a great car chase", "entity_names": ["great car chase"], "entity_types": ["Plot"]}
{"sentence": "show me an eartha kitt film about a torch singer", "entity_names": ["eartha kitt", "torch singer"], "entity_types": ["Actor", "Plot"]}
{"sentence": "any good crime noir films on", "entity_names": ["good", "crime noir"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "who directed take five", "entity_names": ["take five"], "entity_types": ["Title"]}
{"sentence": "what is the most famous movie about horses", "entity_names": ["most famous", "horses"], "entity_types": ["Review", "Plot"]}
{"sentence": "what year did space jam come out", "entity_names": ["year", "space jam"], "entity_types": ["Year", "Title"]}
{"sentence": "which films have sherlock holmes as a character", "entity_names": ["sherlock holmes"], "entity_types": ["Character"]}
{"sentence": "find all movies by m night shyamalan", "entity_names": ["m night shyamalan"], "entity_types": ["Director"]}
{"sentence": "who was the old lady in happy gilmore", "entity_names": ["old lady", "happy gilmore"], "entity_types": ["Character", "Title"]}
{"sentence": "top 10 movies from 1990", "entity_names": ["1990"], "entity_types": ["Year"]}
{"sentence": "get a fantasy film about a unicorn", "entity_names": ["fantasy", "unicorn"], "entity_types": ["Genre", "Plot"]}
{"sentence": "are there any movies about bank robberies", "entity_names": ["bank robberies"], "entity_types": ["Plot"]}
{"sentence": "was sean connery in the league of extraordinary gentlemen", "entity_names": ["sean connery", "the league of extraordinary gentlemen"], "entity_types": ["Actor", "Title"]}
{"sentence": "where was the presidio filmed", "entity_names": ["the presidio"], "entity_types": ["Title"]}
{"sentence": "what film features halle berre as a mental patient", "entity_names": ["halle berre", "mental patient"], "entity_types": ["Actor", "Plot"]}
{"sentence": "what was the plot of the time bandits", "entity_names": ["time bandits"], "entity_types": ["Title"]}
{"sentence": "list actor matt damons top films", "entity_names": ["matt damons", "top films"], "entity_types": ["Actor", "Review"]}
{"sentence": "name a film with brian entwhistle in it", "entity_names": ["brian entwhistle"], "entity_types": ["Actor"]}
{"sentence": "name the movie in which clint eastwood sings", "entity_names": ["clint eastwood", "sings"], "entity_types": ["Actor", "Plot"]}
{"sentence": "who played tank in the matrix", "entity_names": ["tank", "the matrix"], "entity_types": ["Character", "Title"]}
{"sentence": "find me a movie suitable for kids", "entity_names": ["suitable", "kids"], "entity_types": ["Review", "Genre"]}
{"sentence": "name a well known director of japanese samurai movies", "entity_names": ["japanese samurai"], "entity_types": ["Genre"]}
{"sentence": "find all movies directed by steven speilburg", "entity_names": ["steven speilburg"], "entity_types": ["Director"]}
{"sentence": "are you able to name an anime film that is in black and white", "entity_names": ["anime film", "black and white"], "entity_types": ["Genre", "Plot"]}
{"sentence": "find an action movie starring harrison ford", "entity_names": ["action", "harrison ford"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what year was elizabethtown released", "entity_names": ["year", "elizabethtown"], "entity_types": ["Year", "Title"]}
{"sentence": "show me a bruce willis movie about ghosts", "entity_names": ["bruce willis", "movie about ghosts"], "entity_types": ["Actor", "Plot"]}
{"sentence": "find the air bud movie about baseball", "entity_names": ["air bud", "baseball"], "entity_types": ["Character", "Plot"]}
{"sentence": "is there a drama starring angelina jolie and antonio banderas", "entity_names": ["drama", "angelina jolie", "antonio banderas"], "entity_types": ["Genre", "Actor", "Actor"]}
{"sentence": "show me the movie by peter jackson", "entity_names": ["peter jackson"], "entity_types": ["Director"]}
{"sentence": "what were the critic reviews for lady in the water", "entity_names": ["critic reviews", "lady in the water"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "show me a movie starring tim allen about christmas", "entity_names": ["tim allen", "christmas"], "entity_types": ["Actor", "Plot"]}
{"sentence": "list all the tom cruise movies from 2000s", "entity_names": ["tom cruise", "2000s"], "entity_types": ["Actor", "Year"]}
{"sentence": "find me a romance starring patrick swayze", "entity_names": ["romance", "patrick swayze"], "entity_types": ["Genre", "Actor"]}
{"sentence": "are there any movies with assassins", "entity_names": ["assassins"], "entity_types": ["Plot"]}
{"sentence": "find a movie that starred marlon brando and jack nicholson", "entity_names": ["marlon brando", "jack nicholson"], "entity_types": ["Actor", "Actor"]}
{"sentence": "please find the movie about tyrone power working in a carnival", "entity_names": ["tyrone power", "working in a carnival"], "entity_types": ["Actor", "Plot"]}
{"sentence": "was the character chucky in any films that were sci fi", "entity_names": ["chucky", "sci fi"], "entity_types": ["Character", "Genre"]}
{"sentence": "what movies did jack black do in the 2000s", "entity_names": ["jack black", "2000s"], "entity_types": ["Actor", "Year"]}
{"sentence": "show me a movie set in japan with bill murray and scarlett johannson", "entity_names": ["movie set in japan", "bill murray", "scarlett johannson"], "entity_types": ["Plot", "Actor", "Actor"]}
{"sentence": "what zombie movie features a zombie tiger", "entity_names": ["zombie movie", "zombie tiger"], "entity_types": ["Genre", "Character"]}
{"sentence": "which actor played captain january", "entity_names": ["captain january"], "entity_types": ["Character"]}
{"sentence": "how many movies were released in the year 2009", "entity_names": ["2009"], "entity_types": ["Year"]}
{"sentence": "run a trailer for journey back to oz", "entity_names": ["trailer", "journey back to oz"], "entity_types": ["Trailer", "Title"]}
{"sentence": "show me a movie about cars that is rated g", "entity_names": ["about cars", "g"], "entity_types": ["Plot", "MPAA Rating"]}
{"sentence": "is there a color slapstick comedy movie", "entity_names": ["color slapstick comedy"], "entity_types": ["Genre"]}
{"sentence": "find the movie where maggie gyllanhall and james spader have an s amp m relationship", "entity_names": ["maggie gyllanhall", "james spader", "s amp m relationship"], "entity_types": ["Actor", "Actor", "Plot"]}
{"sentence": "did zach braff compose the garden state soundtrack himself", "entity_names": ["zach braff", "garden state", "soundtrack"], "entity_types": ["Actor", "Title", "Song"]}
{"sentence": "in what movies does mark wahlberg say im a big star", "entity_names": ["mark wahlberg"], "entity_types": ["Actor"]}
{"sentence": "find me the comedy movie elf starring will ferrell", "entity_names": ["comedy", "elf", "will ferrell"], "entity_types": ["Genre", "Title", "Actor"]}
{"sentence": "show me all the movies zac efron has been in", "entity_names": ["zac efron"], "entity_types": ["Actor"]}
{"sentence": "what movies are directed by garry marshall", "entity_names": ["garry marshall"], "entity_types": ["Director"]}
{"sentence": "what is the plot of requiem for a heavyweight", "entity_names": ["requiem for a heavyweight"], "entity_types": ["Title"]}
{"sentence": "name a paul williams musical film", "entity_names": ["paul williams", "musical film"], "entity_types": ["Director", "Genre"]}
{"sentence": "show me the movie that judy holliday won an oscar for", "entity_names": ["judy holliday", "oscar"], "entity_types": ["Actor", "Review"]}
{"sentence": "locate that movie with sandra bullock and keanu reaves", "entity_names": ["sandra bullock", "keanu reaves"], "entity_types": ["Actor", "Actor"]}
{"sentence": "find me a movie with the song a whole new world", "entity_names": ["a whole new world"], "entity_types": ["Song"]}
{"sentence": "is there a drama with kate winslet thats rated r", "entity_names": ["drama", "kate winslet", "rated r"], "entity_types": ["Genre", "Actor", "MPAA Rating"]}
{"sentence": "find movies about the first gulf war", "entity_names": ["first gulf war"], "entity_types": ["Plot"]}
{"sentence": "find me a movie about serial killers", "entity_names": ["serial killers"], "entity_types": ["Plot"]}
{"sentence": "is there a movie with kate hudson as a love struck columnist", "entity_names": ["kate hudson"], "entity_types": ["Actor"]}
{"sentence": "whats a mainstream movie about super heros", "entity_names": ["mainstream", "super heros"], "entity_types": ["Genre", "Plot"]}
{"sentence": "show me a movie about characters named kermit and miss piggy", "entity_names": ["kermit", "miss piggy"], "entity_types": ["Character", "Character"]}
{"sentence": "find a movie with corey haim and corey feldman", "entity_names": ["corey haim", "corey feldman"], "entity_types": ["Actor", "Actor"]}
{"sentence": "who directed the legend of bagger vance", "entity_names": ["the legend of bagger vance"], "entity_types": ["Director"]}
{"sentence": "what year did scott pilgrim vs the world come out", "entity_names": ["scott pilgrim vs the world"], "entity_types": ["Title"]}
{"sentence": "has robert downey jr directed any movies", "entity_names": ["robert downey jr"], "entity_types": ["Actor"]}
{"sentence": "find a movie with jude law and julia roberts", "entity_names": ["jude law", "julia roberts"], "entity_types": ["Actor", "Actor"]}
{"sentence": "find a comedy starring john ritter", "entity_names": ["comedy", "john ritter"], "entity_types": ["Genre", "Actor"]}
{"sentence": "looking for a list of hammer studios vampire films", "entity_names": ["looking for a list of hammer studios vampire films"], "entity_types": ["Genre"]}
{"sentence": "what movie featured the quote say hello to my little friend", "entity_names": [], "entity_types": []}
{"sentence": "who played sabrina in the 21st century remake of the audrey hepburn movie", "entity_names": ["sabrina", "21st century remake of the audrey hepburn movie"], "entity_types": ["Character", "Plot"]}
{"sentence": "show all horror movies that star the character freddy kruger", "entity_names": ["horror", "freddy kruger"], "entity_types": ["Genre", "Character"]}
{"sentence": "show me the classic western with claudia cardinale and henry fonda", "entity_names": ["classic", "western", "claudia cardinale", "henry fonda"], "entity_types": ["Review", "Genre", "Actor", "Actor"]}
{"sentence": "find me a movie that won best film in 2009", "entity_names": ["best film", "2009"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "on what film did samuel l jackson first appears as nick fury", "entity_names": ["on what film did samuel l jackson first appears as nick fury"], "entity_types": ["Character"]}
{"sentence": "please show me the film where robert de niro threatens nick nolte and his family", "entity_names": ["robert de niro"], "entity_types": ["Actor"]}
{"sentence": "which actors have played james bond", "entity_names": ["james bond"], "entity_types": ["Title"]}
{"sentence": "find a well reviewed family film from the 1980s", "entity_names": ["well reviewed", "family", "1980s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "what year was drive released", "entity_names": ["drive released"], "entity_types": ["Year"]}
{"sentence": "what are steve martins upcoming film projects", "entity_names": ["steve martins"], "entity_types": ["Actor"]}
{"sentence": "is there a science fiction film that starts on a tower", "entity_names": ["science fiction", "starts on a tower"], "entity_types": ["Genre", "Plot"]}
{"sentence": "find me the r rated comedy horrible bosses with jason bateman", "entity_names": ["r rated", "comedy", "horrible bosses", "jason bateman"], "entity_types": ["MPAA Rating", "Genre", "Title", "Actor"]}
{"sentence": "name a patrick mcgoohan in a canadian film title", "entity_names": ["patrick mcgoohan"], "entity_types": ["Actor"]}
{"sentence": "find meryl streep dramas set in africa", "entity_names": ["meryl streep", "dramas", "set in africa"], "entity_types": ["Actor", "Genre", "Plot"]}
{"sentence": "show me all release dates for the lord of the rings movies", "entity_names": ["lord of the rings"], "entity_types": ["Title"]}
{"sentence": "did tina fey appear in a flick with jack black", "entity_names": ["tina fey", "jack black"], "entity_types": ["Actor", "Actor"]}
{"sentence": "find an action movie from the 1990s with nicolas cage", "entity_names": ["action", "1990s", "nicolas cage"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "which movie about a time travelling car was directed by robert zemeckis", "entity_names": ["time travelling car", "robert zemeckis"], "entity_types": ["Plot", "Director"]}
{"sentence": "are there any pg disney movies", "entity_names": ["pg", "disney"], "entity_types": ["MPAA Rating", "Genre"]}
{"sentence": "what movie does someone say life was like a box of chocolates you never know what youre gonna getcategories used quote", "entity_names": [], "entity_types": []}
{"sentence": "list movies directed by peter jackson in the 2000s", "entity_names": ["peter jackson", "2000s"], "entity_types": ["Director", "Year"]}
{"sentence": "whats a mexican romance film", "entity_names": ["mexican", "romance"], "entity_types": ["Plot", "Genre"]}
{"sentence": "is there a color farce tradgedy movie", "entity_names": ["color farce tradgedy"], "entity_types": ["Genre"]}
{"sentence": "are there any movies about michael jackson", "entity_names": ["michael jackson"], "entity_types": ["Character"]}
{"sentence": "what is a comedy from the 1990s with a car chase", "entity_names": ["1990s", "car chase"], "entity_types": ["Year", "Plot"]}
{"sentence": "what are the top 10 science fiction movies of all time", "entity_names": ["top 10", "science fiction", "of all time"], "entity_types": ["Review", "Genre", "Review"]}
{"sentence": "find me biopics starring country music stars", "entity_names": ["biopics", "country music stars"], "entity_types": ["Genre", "Actor"]}
{"sentence": "find me mocumentries with christopher guest", "entity_names": ["mocumentries", "christopher guest"], "entity_types": ["Genre", "Director"]}
{"sentence": "did sigourney weaver play a character named gwen in a comedy", "entity_names": ["comedy"], "entity_types": ["Genre"]}
{"sentence": "is denzel washington in any comedies from the 1980s", "entity_names": ["denzel washington", "comedies", "1980s"], "entity_types": ["Actor", "Genre", "Year"]}
{"sentence": "find a viewer review for the enemy below", "entity_names": ["the enemy below"], "entity_types": ["Title"]}
{"sentence": "show me a drama starring james woods", "entity_names": ["drama", "james woods"], "entity_types": ["Genre", "Actor"]}
{"sentence": "find a drama about gangs in la", "entity_names": ["la"], "entity_types": ["Plot"]}
{"sentence": "did steven soderbergh direct any movies released in 2010", "entity_names": ["steven soderbergh", "2010"], "entity_types": ["Director", "Year"]}
{"sentence": "looking for the movie with elizabeth tayor rock hudson and james dean", "entity_names": ["elizabeth tayor rock hudson", "james dean"], "entity_types": ["Actor", "Actor"]}
{"sentence": "find me the jodie foster movie with kristen stewart", "entity_names": ["jodie foster", "kristen stewart"], "entity_types": ["Actor", "Actor"]}
{"sentence": "did edward norton star in a drama called american history x", "entity_names": ["edward norton", "drama", "american history x"], "entity_types": ["Actor", "Genre", "Title"]}
{"sentence": "find the movie starring ruth gordon about a housekeeper who is killed", "entity_names": ["ruth gordon", "housekeeper who is killed"], "entity_types": ["Actor", "Plot"]}
{"sentence": "who provided the voice talent for the tiger in kung fu panda", "entity_names": ["tiger", "kung fu panda"], "entity_types": ["Character", "Title"]}
{"sentence": "show me a famous 1940s movie made in france during the nazi occupation", "entity_names": ["1940s"], "entity_types": ["Year"]}
{"sentence": "looking for the movie where orson welles was a nazi hiding in new england", "entity_names": ["orson welles", "nazi hiding in new england"], "entity_types": ["Actor", "Plot"]}
{"sentence": "who stars in the movie titled happythankyoumoreplease", "entity_names": ["happythankyoumoreplease"], "entity_types": ["Title"]}
{"sentence": "show me all the police academy movies", "entity_names": ["police academy"], "entity_types": ["Title"]}
{"sentence": "show me films with wyatt earp from the 1980s", "entity_names": ["wyatt earp", "1980s"], "entity_types": ["Character", "Year"]}
{"sentence": "what are the movies with the best twist endings", "entity_names": ["best", "twist endings"], "entity_types": ["Review", "Plot"]}
{"sentence": "find the movie with robin williams as a dj in vietnam", "entity_names": ["robin williams", "dj in vietnam"], "entity_types": ["Actor", "Plot"]}
{"sentence": "looking for a list of max ophuls films", "entity_names": ["max ophuls"], "entity_types": ["Director"]}
{"sentence": "historical movies of true events with romance", "entity_names": ["historical movies", "romance"], "entity_types": ["Genre", "Genre"]}
{"sentence": "find me movies about the end of the world", "entity_names": ["end of the world"], "entity_types": ["Plot"]}
{"sentence": "was there a movie with tony curtis and eva gabor", "entity_names": ["tony curtis and eva gabor"], "entity_types": ["Actor"]}
{"sentence": "find movies set in los angeles in the future", "entity_names": ["los angeles in the future"], "entity_types": ["Plot"]}
{"sentence": "who is the actor that stars in the new tv show awake", "entity_names": ["awake"], "entity_types": ["Title"]}
{"sentence": "who starred in the girl with the dragon tattoo", "entity_names": ["the girl with the dragon tattoo"], "entity_types": ["Title"]}
{"sentence": "show me a movie with christina aguleria and cher", "entity_names": ["christina aguleria", "cher"], "entity_types": ["Actor", "Actor"]}
{"sentence": "i need a comedy from the 80s that is rated pg", "entity_names": ["comedy", "80s", "pg"], "entity_types": ["Genre", "Year", "MPAA Rating"]}
{"sentence": "find me the first rocky movie", "entity_names": ["rocky"], "entity_types": ["Title"]}
{"sentence": "show me the disney movie featuring hayley mills on a greek island", "entity_names": ["disney", "hayley mills"], "entity_types": ["Genre", "Actor"]}
{"sentence": "looking for a comedy with darryl hannah and steve martin", "entity_names": ["comedy", "darryl hannah", "steve martin"], "entity_types": ["Genre", "Actor", "Actor"]}
{"sentence": "whats an isaak pearlman movie", "entity_names": ["isaak pearlman"], "entity_types": ["Director"]}
{"sentence": "i want to see the movie about a character named ferris bueller", "entity_names": ["ferris bueller"], "entity_types": ["Character"]}
{"sentence": "show hit movies from the 2000s starring jack black", "entity_names": ["hit movies", "2000s", "jack black"], "entity_types": ["Viewers' Rating", "Year", "Actor"]}
{"sentence": "what songs were on the soundtrack for center stage", "entity_names": ["center stage"], "entity_types": ["Title"]}
{"sentence": "who played the role of leonidas in 300", "entity_names": ["leonidas", "300"], "entity_types": ["Character", "Title"]}
{"sentence": "what movies are about canadian sports", "entity_names": ["canadian sports"], "entity_types": ["Plot"]}
{"sentence": "find a movie with the song for hes a jolly good fellow", "entity_names": ["for hes a jolly good fellow"], "entity_types": ["Song"]}
{"sentence": "what year was the last james bond film released", "entity_names": ["year", "james bond"], "entity_types": ["Year", "Title"]}
{"sentence": "show me an r rated comedy called american pie", "entity_names": ["r rated", "comedy", "american pie"], "entity_types": ["MPAA Rating", "Genre", "Title"]}
{"sentence": "whats a spy film with gold in the title", "entity_names": ["spy", "gold"], "entity_types": ["Plot", "Title"]}
{"sentence": "find a mathew modine movie", "entity_names": ["mathew modine"], "entity_types": ["Actor"]}
{"sentence": "show me an animated movie about pandas", "entity_names": ["animated", "pandas"], "entity_types": ["Genre", "Plot"]}
{"sentence": "find any musicals with a g rating on today", "entity_names": ["musicals", "g", "today"], "entity_types": ["Genre", "MPAA Rating", "Year"]}
{"sentence": "what r rated movies from 2011 do not have full frontal nudity in them", "entity_names": ["r rated", "2011", "full frontal nudity"], "entity_types": ["MPAA Rating", "Year", "Plot"]}
{"sentence": "what was the title of the bio pic about langston hughs", "entity_names": ["bio pic", "langston hughs"], "entity_types": ["Genre", "Character"]}
{"sentence": "what movies feature the character wooster", "entity_names": ["wooster"], "entity_types": ["Character"]}
{"sentence": "did kevin kline start in a musical in the 1980s", "entity_names": ["kevin kline", "musical", "1980s"], "entity_types": ["Actor", "Genre", "Year"]}
{"sentence": "are there any movies based on british versions", "entity_names": ["movies based on british versions"], "entity_types": ["Plot"]}
{"sentence": "are there any g rated musicals from the 1980s", "entity_names": ["g rated", "musicals", "1980s"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "show me movies about king kong", "entity_names": ["king kong"], "entity_types": ["Plot"]}
{"sentence": "are there any musicals from the 1990s", "entity_names": ["1990s"], "entity_types": ["Year"]}
{"sentence": "what is the vuewers rating for heidi", "entity_names": ["heidi"], "entity_types": ["Title"]}
{"sentence": "show me a movie starring meryl streep and robert deniro", "entity_names": ["meryl streep", "robert deniro"], "entity_types": ["Actor", "Actor"]}
{"sentence": "find a scary movie directed by stephen spillburg", "entity_names": ["scary", "stephen spillburg"], "entity_types": ["Genre", "Director"]}
{"sentence": "show me a movie about aliens attacking earth", "entity_names": ["about aliens"], "entity_types": ["Plot"]}
{"sentence": "what horror movies featured freddy and were rated r", "entity_names": ["horror", "freddy", "rated r"], "entity_types": ["Genre", "Character", "MPAA Rating"]}
{"sentence": "who created lord of the rings", "entity_names": ["lord of the rings"], "entity_types": ["Title"]}
{"sentence": "are there any g rated movies with paris in the title", "entity_names": ["g rated", "paris in the title"], "entity_types": ["MPAA Rating", "Plot"]}
{"sentence": "who directed the horse in the grey flannel suit", "entity_names": ["the horse in the grey flannel suit"], "entity_types": ["Title"]}
{"sentence": "find the sherlock holmes movie about a large killer dog", "entity_names": ["sherlock holmes movie about a large killer dog"], "entity_types": ["Plot"]}
{"sentence": "show me movies whoopi goldberg played in", "entity_names": ["whoopi goldberg"], "entity_types": ["Actor"]}
{"sentence": "find movies about football", "entity_names": ["football"], "entity_types": ["Plot"]}
{"sentence": "what is mpaa rating for the rhino brothers", "entity_names": ["the rhino brothers"], "entity_types": ["Title"]}
{"sentence": "who said you can buy anything but you cant buy backbone", "entity_names": [], "entity_types": []}
{"sentence": "what movie uses harpsicord music", "entity_names": ["harpsicord music"], "entity_types": ["Plot"]}
{"sentence": "looking for a 1960s southern gothic movie starring bette davis and olivia de haviland", "entity_names": ["1960s", "southern gothic", "bette davis", "olivia de haviland"], "entity_types": ["Year", "Plot", "Actor", "Actor"]}
{"sentence": "show me the lindsey anderson movies starring malcolm mcdowell", "entity_names": ["lindsey anderson", "malcolm mcdowell"], "entity_types": ["Director", "Actor"]}
{"sentence": "what movie is r2d2 in", "entity_names": ["r2d2"], "entity_types": ["Character"]}
{"sentence": "show me an action movie starring clint eastwood", "entity_names": ["action", "clint eastwood"], "entity_types": ["Genre", "Actor"]}
{"sentence": "please show me a list of busby berkley movies", "entity_names": ["busby berkley movies"], "entity_types": ["Director"]}
{"sentence": "which actor starred in the disney movie gus", "entity_names": ["actor", "disney movie", "gus"], "entity_types": ["Actor", "Genre", "Title"]}
{"sentence": "in which film did keira knightley stars as a pirate lord", "entity_names": ["keira knightley", "pirate lord"], "entity_types": ["Actor", "Plot"]}
{"sentence": "tell me the names of all critically acclaimed horror films", "entity_names": ["critically acclaimed", "horror"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "what is the best movie of all time rated by viewers", "entity_names": ["viewers"], "entity_types": ["Viewers' Rating"]}
{"sentence": "show me movies about the military", "entity_names": ["movies about the military"], "entity_types": ["Plot"]}
{"sentence": "find a romantic comedy starring daryl hannah", "entity_names": ["romantic comedy", "daryl hannah"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what was the best football movie ever made", "entity_names": ["football movie"], "entity_types": ["Genre"]}
{"sentence": "show me a movie with lots of sky diving in it", "entity_names": ["sky diving"], "entity_types": ["Plot"]}
{"sentence": "have any movies been made about robot ninjas", "entity_names": ["robot ninjas"], "entity_types": ["Plot"]}
{"sentence": "what animated movies were nominated for oscars", "entity_names": ["animated", "nominated for oscars"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "please find the robert altman movie set during the korean war", "entity_names": ["robert altman", "korean war"], "entity_types": ["Actor", "Plot"]}
{"sentence": "what are the most recent julia roberts romance films from the last four years", "entity_names": ["julia roberts"], "entity_types": ["Actor"]}
{"sentence": "show me the french film about a postman who is obsessed with an opera star", "entity_names": ["french film", "about a postman who is obsessed with an opera star"], "entity_types": ["Genre", "Plot"]}
{"sentence": "find a comedy starring groucho marx", "entity_names": ["comedy", "groucho marx"], "entity_types": ["Genre", "Actor"]}
{"sentence": "where is the quote heres looking at you kid from", "entity_names": [], "entity_types": []}
{"sentence": "show me movies about shark attacks", "entity_names": ["show me movies about shark attacks"], "entity_types": ["Plot"]}
{"sentence": "find me the release date of catching fire", "entity_names": ["catching fire"], "entity_types": ["Title"]}
{"sentence": "what is the movie made by stephen stieldberg", "entity_names": ["stephen stieldberg"], "entity_types": ["Director"]}
{"sentence": "what movies feature the character jeeves", "entity_names": ["jeeves"], "entity_types": ["Character"]}
{"sentence": "i want to see a preview of george clooneys latest movie", "entity_names": ["preview", "george clooneys", "latest"], "entity_types": ["Trailer", "Actor", "Year"]}
{"sentence": "what movie has liev shrieber and sean william scott in it as hockey players", "entity_names": ["liev shrieber", "sean william scott", "hockey players"], "entity_types": ["Actor", "Actor", "Plot"]}
{"sentence": "what film directed by zack snyder shows the forces of king leonidas", "entity_names": ["zack snyder"], "entity_types": ["Actor"]}
{"sentence": "what is the best movie trailer", "entity_names": ["best movie trailer"], "entity_types": ["Trailer"]}
{"sentence": "which 1982 science fiction film was directed by steven lisberger", "entity_names": ["1982", "science fiction", "steven lisberger"], "entity_types": ["Year", "Genre", "Director"]}
{"sentence": "show me a movie about a dog starring jennifer aniston", "entity_names": ["about a dog", "jennifer aniston"], "entity_types": ["Plot", "Actor"]}
{"sentence": "show me the film based on a stephen king book about the flu killing most of humanity", "entity_names": ["stephen king", "flu killing most of humanity"], "entity_types": ["Genre", "Plot"]}
{"sentence": "did dean koontz direct a movie based on his books", "entity_names": ["dean koontz", "based on his books"], "entity_types": ["Director", "Plot"]}
{"sentence": "show me the movie where cary grant has two aunts that poison and bury old men", "entity_names": ["cary grant", "two aunts that poison and bury old men"], "entity_types": ["Actor", "Plot"]}
{"sentence": "list the romance films directed by james cameron rated must see", "entity_names": ["romance", "james cameron", "must see"], "entity_types": ["Genre", "Actor", "Viewers' Rating"]}
{"sentence": "what live action movie based on action figure toys was released in 2009", "entity_names": ["live action", "action figure toys", "2009"], "entity_types": ["Genre", "Plot", "Year"]}
{"sentence": "is there an action film with katherine hepburn in it", "entity_names": ["action", "katherine hepburn"], "entity_types": ["Genre", "Actor"]}
{"sentence": "whats the movie with the line doomed is your soul and damned is your life", "entity_names": [], "entity_types": []}
{"sentence": "who directed who framed roger rabbit", "entity_names": ["who framed roger rabbit"], "entity_types": ["Director"]}
{"sentence": "find me movies about balloon races", "entity_names": ["about balloon races"], "entity_types": ["Plot"]}
{"sentence": "show me a 1990s movie about children and an attic", "entity_names": ["1990s", "about children and an attic"], "entity_types": ["MPAA Rating", "Plot"]}
{"sentence": "find the movie about gangsters with jack nicholson and matt damon", "entity_names": ["gangsters", "jack nicholson", "matt damon"], "entity_types": ["Plot", "Actor", "Actor"]}
{"sentence": "what was the hit song from the movie armageddon", "entity_names": ["hit song", "armageddon"], "entity_types": ["Song", "Title"]}
{"sentence": "did patrick stewart appear in a romantic motion picture", "entity_names": ["romantic motion picture"], "entity_types": ["Genre"]}
{"sentence": "show me all the movies brad pitt has been in", "entity_names": ["brad pitt"], "entity_types": ["Actor"]}
{"sentence": "did the treat williams appear in a film", "entity_names": ["treat williams"], "entity_types": ["Actor"]}
{"sentence": "what movies directed by carlos saura use typical spanish dances", "entity_names": ["carlos saura", "typical spanish dances"], "entity_types": ["Director", "Plot"]}
{"sentence": "what is the newest version of batman", "entity_names": ["batman"], "entity_types": ["Title"]}
{"sentence": "which actor potrayed jackie robinson", "entity_names": ["jackie robinson"], "entity_types": ["Character"]}
{"sentence": "who was the director of target with gene hackman", "entity_names": ["target", "gene hackman"], "entity_types": ["Title", "Actor"]}
{"sentence": "what year was the goonies released", "entity_names": ["what year", "the goonies"], "entity_types": ["Year", "Title"]}
{"sentence": "which well received film features the songs come what may and your song", "entity_names": ["well received film", "come what may", "your song"], "entity_types": ["Viewers' Rating", "Song", "Song"]}
{"sentence": "whats the best song to end a scary movie", "entity_names": ["best song", "scary movie"], "entity_types": ["Review", "Genre"]}
{"sentence": "show me a list of spy movies from the 1960s", "entity_names": ["spy", "1960s"], "entity_types": ["Plot", "Year"]}
{"sentence": "what was the plot of sudden death", "entity_names": ["plot", "sudden death"], "entity_types": ["Plot", "Title"]}
{"sentence": "looking for a movie about a huge killer snake in the amazon", "entity_names": ["huge killer snake in the amazon"], "entity_types": ["Plot"]}
{"sentence": "what movie has the highest viewers rating", "entity_names": ["highest viewers rating"], "entity_types": ["Viewers' Rating"]}
{"sentence": "speed racer was released in what year", "entity_names": ["speed racer", "what year"], "entity_types": ["Title", "Year"]}
{"sentence": "look for a movie filmed in california", "entity_names": [], "entity_types": []}
{"sentence": "i am looking for a movie starring jada pinkett about bank robbery", "entity_names": ["jada pinkett", "bank robbery"], "entity_types": ["Actor", "Plot"]}
{"sentence": "when is johnny depps new movie coming out", "entity_names": ["johnny depps", "coming out"], "entity_types": ["Actor", "Title"]}
{"sentence": "show me a linda hunt movie", "entity_names": ["linda hunt"], "entity_types": ["Actor"]}
{"sentence": "are there any r movies with johnny depp", "entity_names": ["r", "johnny depp"], "entity_types": ["MPAA Rating", "Actor"]}
{"sentence": "how many stars did scarface recieve in reviews", "entity_names": ["stars", "scarface"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "find me the action movie seven starring brad pitt", "entity_names": ["action", "seven", "brad pitt"], "entity_types": ["Genre", "Title", "Actor"]}
{"sentence": "what were the viewer ratings for the movie world trade center", "entity_names": ["world trade center"], "entity_types": ["Title"]}
{"sentence": "find me a movie with sandra bullock and keanu reeves", "entity_names": ["sandra bullock", "keanu reeves"], "entity_types": ["Actor", "Actor"]}
{"sentence": "did george lucas direct any well reviewed comedies", "entity_names": ["george lucas", "well reviewed", "comedies"], "entity_types": ["Director", "Viewers' Rating", "Genre"]}
{"sentence": "find a comedy about basketball", "entity_names": ["comedy", "basketball"], "entity_types": ["Genre", "Plot"]}
{"sentence": "find the comedy with matt dillon as a fired bartender and liv tyler is his girlfriend", "entity_names": ["comedy", "matt dillon", "fired bartender", "liv tyler"], "entity_types": ["Genre", "Actor", "Plot", "Actor"]}
{"sentence": "i want 1980s romance movies", "entity_names": ["1980s", "romance"], "entity_types": ["Year", "Genre"]}
{"sentence": "what is beetlejuice rated", "entity_names": ["beetlejuice"], "entity_types": ["Title"]}
{"sentence": "whats a monty python flick about jesus", "entity_names": ["monty python", "jesus"], "entity_types": ["Title", "Plot"]}
{"sentence": "show me a musical from the 1940s", "entity_names": ["musical", "1940s"], "entity_types": ["Genre", "Year"]}
{"sentence": "show me the movie about strange men who repossess cars starring emilio estevez", "entity_names": ["show me the movie about strange men who repossess cars", "emilio estevez"], "entity_types": ["Plot", "Actor"]}
{"sentence": "list any shirley temple movies playing today", "entity_names": ["shirley temple"], "entity_types": ["Actor"]}
{"sentence": "tell me the title of the movie about ghosts starring nicole kidman", "entity_names": ["ghosts", "nicole kidman"], "entity_types": ["Plot", "Actor"]}
{"sentence": "what movie used the song the good ship lollipop", "entity_names": ["the good ship lollipop"], "entity_types": ["Song"]}
{"sentence": "find movies with halle berry", "entity_names": ["halle berry"], "entity_types": ["Actor"]}
{"sentence": "what was the last movie released in 2011", "entity_names": ["2011"], "entity_types": ["Year"]}
{"sentence": "show me a movie with the song making christmas", "entity_names": ["making christmas"], "entity_types": ["Song"]}
{"sentence": "did elvis play a singer in a picture", "entity_names": ["elvis", "play a singer"], "entity_types": ["Actor", "Plot"]}
{"sentence": "is there a movie where a little girl enters a beauty contest", "entity_names": ["is there a movie where a little girl enters a beauty contest"], "entity_types": ["Plot"]}
{"sentence": "show me the judd apatow film about a middle aged mans journey to finally have sex", "entity_names": ["judd apatow", "middle aged mans journey to finally have sex"], "entity_types": ["Director", "Plot"]}
{"sentence": "what year was pretty woman released", "entity_names": ["pretty woman"], "entity_types": ["Title"]}
{"sentence": "show me a movie with the song somewhere over the rainbow", "entity_names": ["somewhere over the rainbow"], "entity_types": ["Song"]}
{"sentence": "please find the comedy starring queen latifah and steve martin", "entity_names": ["comedy", "queen latifah", "steve martin"], "entity_types": ["Genre", "Actor", "Actor"]}
{"sentence": "did thomas newman compose the soundtrack for edward scissorhands", "entity_names": ["soundtrack", "edward scissorhands"], "entity_types": ["Song", "Title"]}
{"sentence": "has rubert grint made any movies other than harry potter", "entity_names": ["rubert grint", "harry potter"], "entity_types": ["Director", "Title"]}
{"sentence": "is there a sequal to crash of the titans", "entity_names": ["crash of the titans"], "entity_types": ["Title"]}
{"sentence": "find me the movie where someone says i love the smell of napalm in the morning", "entity_names": [], "entity_types": []}
{"sentence": "which actor starred in the fish that saved pittsburgh", "entity_names": ["the fish that saved pittsburgh"], "entity_types": ["Title"]}
{"sentence": "show me a comedy from the 1980s with billy crystal", "entity_names": ["comedy", "1980s", "billy crystal"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "find the movie starring jack lemmon and alan arkin as real estate salesmen", "entity_names": ["jack lemmon", "alan arkin"], "entity_types": ["Actor", "Actor"]}
{"sentence": "looking for a 1990s movie starring robert redford and sidney poitier about a decoding device", "entity_names": ["1990s movie", "robert redford", "sidney poitier", "about a decoding device"], "entity_types": ["Year", "Actor", "Actor", "Plot"]}
{"sentence": "find me a comedy with ted danson", "entity_names": ["comedy", "ted danson"], "entity_types": ["Genre", "Actor"]}
{"sentence": "find me a film with the song under the sea", "entity_names": ["under the sea"], "entity_types": ["Song"]}
{"sentence": "how many movies has al pacino been in", "entity_names": ["al pacino"], "entity_types": ["Actor"]}
{"sentence": "find a boxing movie", "entity_names": ["boxing"], "entity_types": ["Plot"]}
{"sentence": "that is the mpaa rating for mutiny on the bounty", "entity_names": ["mutiny on the bounty"], "entity_types": ["Title"]}
{"sentence": "find me a movie with pirates", "entity_names": ["pirates"], "entity_types": ["Plot"]}
{"sentence": "what romance movies came out in the 90s", "entity_names": ["romance", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "what movies has edward norton been in", "entity_names": ["edward norton"], "entity_types": ["Actor"]}
{"sentence": "when was maverick released", "entity_names": ["maverick"], "entity_types": ["Title"]}
{"sentence": "what movie was harpo in with oprah winfrey", "entity_names": ["harpo", "oprah winfrey"], "entity_types": ["Actor", "Actor"]}
{"sentence": "what movie won the most awards", "entity_names": ["awards"], "entity_types": ["Review"]}
{"sentence": "whats the james cameron movie with the blue aliens", "entity_names": ["james cameron", "the blue aliens"], "entity_types": ["Director", "Plot"]}
{"sentence": "show all independent films starring a list celebrities", "entity_names": ["independent films"], "entity_types": ["Genre"]}
{"sentence": "when is american reunion being released", "entity_names": ["american reunion"], "entity_types": ["Title"]}
{"sentence": "show me a pg 13 movie about aliens", "entity_names": ["pg 13"], "entity_types": ["MPAA Rating"]}
{"sentence": "what movies has alfred hitchcock been in", "entity_names": ["alfred hitchcock"], "entity_types": ["Actor"]}
{"sentence": "when does that jonah hill movie where he is a cop come out on dvd", "entity_names": ["jonah hill", "where he is a cop"], "entity_types": ["Actor", "Plot"]}
{"sentence": "find the movie about broadway starring roy scheider", "entity_names": ["about broadway", "roy scheider"], "entity_types": ["Plot", "Actor"]}
{"sentence": "is there a burt lancaster political movie", "entity_names": ["burt lancaster", "political"], "entity_types": ["Actor", "Plot"]}
{"sentence": "what steven speilberg movies came out in 1980s", "entity_names": ["steven speilberg", "1980s"], "entity_types": ["Director", "Year"]}
{"sentence": "find meg ryan films from the 1990s with angels", "entity_names": ["1990s"], "entity_types": ["Year"]}
{"sentence": "show me funny movies from 1983", "entity_names": ["funny", "1983"], "entity_types": ["Plot", "Title"]}
{"sentence": "name a science fiction film from 1961", "entity_names": ["science fiction", "1961"], "entity_types": ["Genre", "Year"]}
{"sentence": "show me harry potter movies from the 2000s", "entity_names": ["harry potter", "2000s"], "entity_types": ["Character", "Year"]}
{"sentence": "what are roger eberts best films", "entity_names": ["roger eberts best films"], "entity_types": ["Viewers' Rating"]}
{"sentence": "name the theme song for teen wolf", "entity_names": ["theme song", "teen wolf"], "entity_types": ["Song", "Title"]}
{"sentence": "what movie featured drew barrymore in her breakout role", "entity_names": ["drew barrymore"], "entity_types": ["Actor"]}
{"sentence": "find the movie with sean connery as an fbi agent", "entity_names": ["sean connery", "fbi agent"], "entity_types": ["Actor", "Plot"]}
{"sentence": "i want a murder mystery movie from 1995", "entity_names": ["murder mystery", "1995"], "entity_types": ["Genre", "Year"]}
{"sentence": "where was red tails filmed", "entity_names": ["red tails"], "entity_types": ["Title"]}
{"sentence": "how many comedies were released in 1980", "entity_names": ["comedies", "1980"], "entity_types": ["Genre", "Year"]}
{"sentence": "are there any movies about the korean war", "entity_names": ["korean war"], "entity_types": ["Plot"]}
{"sentence": "who is doing the soundtrack for prometheus", "entity_names": ["who is doing the soundtrack for prometheus"], "entity_types": ["Song"]}
{"sentence": "i want a 1970s sailing flick", "entity_names": ["1970s", "sailing"], "entity_types": ["Year", "Plot"]}
{"sentence": "what year was mystic pizza released", "entity_names": ["mystic pizza"], "entity_types": ["Title"]}
{"sentence": "show me a list of r rated movies about aliens", "entity_names": ["r rated", "aliens"], "entity_types": ["MPAA Rating", "Plot"]}
{"sentence": "were there any r rated animated films made in the 80s", "entity_names": ["r rated", "animated", "80s"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "show me a movie starring marlon brando and val kilmer", "entity_names": ["marlon brando", "val kilmer"], "entity_types": ["Actor", "Actor"]}
{"sentence": "did christopher nolan direct any highly regarded action films", "entity_names": ["christopher nolan", "highly regarded", "action"], "entity_types": ["Director", "Review", "Genre"]}
{"sentence": "what movies from the 90s did jeremy irons star in", "entity_names": ["90s", "jeremy irons"], "entity_types": ["Year", "Actor"]}
{"sentence": "what movie has all eddie vedder songs in it", "entity_names": ["eddie vedder songs"], "entity_types": ["Song"]}
{"sentence": "whats a rock hudson flick from the 1960s", "entity_names": ["rock hudson", "1960s"], "entity_types": ["Actor", "Year"]}
{"sentence": "find the award winning 1970s movie where paul newman and robert redford are con artists", "entity_names": ["award winning", "1970s", "paul newman", "robert redford", "con artists"], "entity_types": ["Viewers' Rating", "Year", "Actor", "Actor", "Plot"]}
{"sentence": "are there any r rated movies with pirates", "entity_names": ["r rated", "pirates"], "entity_types": ["MPAA Rating", "Plot"]}
{"sentence": "look for a movie with jessica alba", "entity_names": ["jessica alba"], "entity_types": ["Actor"]}
{"sentence": "did sacha baron cohen have a show", "entity_names": ["sacha baron cohen"], "entity_types": ["Actor"]}
{"sentence": "who starred in thw gumball rally", "entity_names": ["thw gumball rally"], "entity_types": ["Title"]}
{"sentence": "i want to find a movie directed by michael moore", "entity_names": ["michael moore"], "entity_types": ["Director"]}
{"sentence": "find movies from the 1990s with strong female leads", "entity_names": ["1990s", "strong female leads"], "entity_types": ["Year", "Plot"]}
{"sentence": "find me a gangster movie with robert deniro", "entity_names": ["gangster", "robert deniro"], "entity_types": ["Genre", "Actor"]}
{"sentence": "show me a movie with daniel radcliff", "entity_names": ["daniel radcliff"], "entity_types": ["Actor"]}
{"sentence": "who played in psycho", "entity_names": ["psycho"], "entity_types": ["Title"]}
{"sentence": "what police film had a main character called popeye doyle", "entity_names": ["police", "popeye doyle"], "entity_types": ["Genre", "Character"]}
{"sentence": "show me the spike lee movie about a serial killer", "entity_names": ["spike lee", "about a serial killer"], "entity_types": ["Director", "Plot"]}
{"sentence": "what movies from the 90s did clint eastwood direct", "entity_names": ["90s", "clint eastwood"], "entity_types": ["Year", "Director"]}
{"sentence": "what movies was leonardo dicaprio in during the 1990s", "entity_names": ["leonardo dicaprio", "1990s"], "entity_types": ["Actor", "Year"]}
{"sentence": "name a flick from the 1980s with the word heat in the title", "entity_names": ["1980s"], "entity_types": ["Year"]}
{"sentence": "best jim carrey quotes", "entity_names": ["best", "jim carrey"], "entity_types": ["Review", "Actor"]}
{"sentence": "who is the star of twilight", "entity_names": ["twilight"], "entity_types": ["Title"]}
{"sentence": "what is a 1960s film about a charles dickens character", "entity_names": ["1960s", "about a charles dickens character"], "entity_types": ["Year", "Plot"]}
{"sentence": "when is the last season of desperate housewives going to air in europe", "entity_names": ["desperate housewives"], "entity_types": ["Title"]}
{"sentence": "what movie has a character called the robot", "entity_names": ["the robot"], "entity_types": ["Character"]}
{"sentence": "what are some good animated films", "entity_names": ["good", "animated"], "entity_types": ["Review", "Genre"]}
{"sentence": "find the 1960s movie about a group of schoolboys marooned on an island", "entity_names": ["1960s", "about a group of schoolboys marooned on an island"], "entity_types": ["Year", "Plot"]}
{"sentence": "show me richard gere thriller movies", "entity_names": ["richard gere", "thriller"], "entity_types": ["Actor", "Genre"]}
{"sentence": "find me the soundtrack for scarface", "entity_names": ["scarface"], "entity_types": ["Title"]}
{"sentence": "show me documentaries about birds", "entity_names": ["documentaries", "about birds"], "entity_types": ["Genre", "Plot"]}
{"sentence": "what year was psycho released", "entity_names": ["psycho"], "entity_types": ["Title"]}
{"sentence": "find me all movies directed by cameron crowe", "entity_names": ["cameron crowe"], "entity_types": ["Director"]}
{"sentence": "are there any r rated movies about zombies", "entity_names": ["r rated", "zombies"], "entity_types": ["MPAA Rating", "Plot"]}
{"sentence": "name of movie where ring is thrown into volcano", "entity_names": ["volcano"], "entity_types": ["Plot"]}
{"sentence": "show me arnold schwartzenegger movies with robots", "entity_names": ["arnold schwartzenegger", "robots"], "entity_types": ["Actor", "Plot"]}
{"sentence": "show me the night of the lepus", "entity_names": ["the night of the lepus"], "entity_types": ["Title"]}
{"sentence": "tell me the top ten romance movies of all time", "entity_names": ["top ten", "romance"], "entity_types": ["Review", "Genre"]}
{"sentence": "find a review for coach carter", "entity_names": ["review", "coach carter"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "what films has john travolta directed", "entity_names": ["john travolta"], "entity_types": ["Director"]}
{"sentence": "what is the best action movie this year", "entity_names": ["best", "action movie", "this year"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "who directed the bee movie", "entity_names": ["bee movie"], "entity_types": ["Title"]}
{"sentence": "what is the mpaa rating for necessary roughness", "entity_names": ["necessary roughness"], "entity_types": ["Title"]}
{"sentence": "who directed one flew over the cuckoos nest", "entity_names": ["one flew over the cuckoos nest"], "entity_types": ["Title"]}
{"sentence": "whats a monty python flick about fish", "entity_names": ["monty python", "fish"], "entity_types": ["Character", "Plot"]}
{"sentence": "i would like to see alien movies from the 1950s", "entity_names": ["alien movies", "1950s"], "entity_types": ["Plot", "Year"]}
{"sentence": "show me a movie with the muppets", "entity_names": ["show me a movie with the muppets"], "entity_types": ["Character"]}
{"sentence": "what is the most positively reviewed romance movie", "entity_names": ["most positively reviewed", "romance"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "what are some pg 13 romantic comedies", "entity_names": ["pg 13", "romantic comedies"], "entity_types": ["MPAA Rating", "Genre"]}
{"sentence": "what m night shyamalan movie got the best reviews", "entity_names": ["m night shyamalan", "best reviews"], "entity_types": ["Director", "Viewers' Rating"]}
{"sentence": "who starred in the surfing movie north shore", "entity_names": ["north shore"], "entity_types": ["Title"]}
{"sentence": "what movies did kevin smith direct", "entity_names": ["kevin smith"], "entity_types": ["Actor"]}
{"sentence": "whats the name of that movie with the song missing you", "entity_names": ["missing you"], "entity_types": ["Song"]}
{"sentence": "find a review for bend it like beckham", "entity_names": ["review", "bend it like beckham"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "what films stars both nicole kidman and will ferrell", "entity_names": ["nicole kidman", "will ferrell"], "entity_types": ["Actor", "Actor"]}
{"sentence": "are there any movies directed by peter jackson except lord of the rings", "entity_names": ["peter jackson"], "entity_types": ["Director"]}
{"sentence": "show me the mel brooks western movie", "entity_names": ["mel brooks", "western"], "entity_types": ["Director", "Genre"]}
{"sentence": "name the 2011 film starring mila kunis and justin timberlake", "entity_names": ["2011", "mila kunis", "justin timberlake"], "entity_types": ["Year", "Actor", "Actor"]}
{"sentence": "find a 1980s movie with danny glover", "entity_names": ["1980s", "danny glover"], "entity_types": ["Year", "Actor"]}
{"sentence": "in which 2004 film directed by mel gibson portrays the last hours in the life of jesus", "entity_names": ["2004", "mel gibson", "the last hours in the life of jesus"], "entity_types": ["Year", "Director", "Plot"]}
{"sentence": "which film stars both leslie nielsen and anna nicole smith", "entity_names": ["leslie nielsen and anna nicole smith"], "entity_types": ["Actor"]}
{"sentence": "what was the first dirty harry movie", "entity_names": ["first", "dirty harry"], "entity_types": ["Year", "Title"]}
{"sentence": "what comedys had eddie murphy play a big role in", "entity_names": ["eddie murphy"], "entity_types": ["Actor"]}
{"sentence": "what is the plot of mad max", "entity_names": ["mad max"], "entity_types": ["Title"]}
{"sentence": "who will replace johnny depp in the pirates of the carribean movies", "entity_names": ["johnny depp", "pirates of the carribean"], "entity_types": ["Actor", "Title"]}
{"sentence": "name the the 2010 movie directed by ben affleck", "entity_names": ["2010", "ben affleck"], "entity_types": ["Year", "Director"]}
{"sentence": "what is a horror film released in 1987", "entity_names": ["horror", "1987"], "entity_types": ["Genre", "Year"]}
{"sentence": "i want to find the movies with music by the bee gees", "entity_names": ["music by the bee gees"], "entity_types": ["Song"]}
{"sentence": "find the suspense movie starring anthony hopkins and ryan gosling", "entity_names": ["suspense", "anthony hopkins", "ryan gosling"], "entity_types": ["Genre", "Actor", "Actor"]}
{"sentence": "who directed the film 300", "entity_names": ["directed", "300"], "entity_types": ["Director", "Title"]}
{"sentence": "what film had the song the power of love", "entity_names": ["the power of love"], "entity_types": ["Song"]}
{"sentence": "are there any r rated cartoons", "entity_names": ["r rated", "cartoons"], "entity_types": ["MPAA Rating", "Genre"]}
{"sentence": "looking for the controversial mel gibson film about jesus", "entity_names": ["controversial", "mel gibson", "jesus"], "entity_types": ["Review", "Director", "Character"]}
{"sentence": "shoe me movies starring johnny depp", "entity_names": ["johnny depp"], "entity_types": ["Actor"]}
{"sentence": "what david fincher movie had a soundtrack by the dust brothers", "entity_names": ["david fincher", "the dust brothers"], "entity_types": ["Director", "Song"]}
{"sentence": "who plays cindy in the scary movie films", "entity_names": ["cindy", "scary movie"], "entity_types": ["Character", "Title"]}
{"sentence": "are there any movies starring halle berry before 1997", "entity_names": ["halle berry"], "entity_types": ["Actor"]}
{"sentence": "how many people actually like to review boring movies", "entity_names": ["how many people actually like to review boring movies"], "entity_types": ["Review"]}
{"sentence": "is the hunger games a violent movie", "entity_names": ["the hunger games", "violent"], "entity_types": ["Title", "Plot"]}
{"sentence": "looking for the movie with a black man as an alien directed by john sayles", "entity_names": ["black man as an alien", "john sayles"], "entity_types": ["Plot", "Director"]}
{"sentence": "find a trailer for chariots of fire", "entity_names": ["find a trailer for chariots of fire"], "entity_types": ["Trailer"]}
{"sentence": "get a socrates movie", "entity_names": ["socrates"], "entity_types": ["Character"]}
{"sentence": "what was charlie sheens first movie", "entity_names": ["charlie sheens"], "entity_types": ["Actor"]}
{"sentence": "are there any pg 13 movies about gay romance", "entity_names": ["pg 13", "gay romance"], "entity_types": ["MPAA Rating", "Plot"]}
{"sentence": "did critics rate any of the alien movies as must see", "entity_names": ["critics rate", "alien", "must see"], "entity_types": ["Viewers' Rating", "Title", "Review"]}
{"sentence": "what year was saving private ryan released in theaters", "entity_names": ["saving private ryan"], "entity_types": ["Title"]}
{"sentence": "is there a trailer out for advengers yet", "entity_names": ["trailer", "advengers"], "entity_types": ["Trailer", "Title"]}
{"sentence": "find the 1990s movie with a disco soundtrack about a male porn star", "entity_names": ["1990s", "disco soundtrack", "a male porn star"], "entity_types": ["Year", "Song", "Plot"]}
{"sentence": "give me a list of mike myers comedies in the 90s", "entity_names": ["mike myers", "comedies", "90s"], "entity_types": ["Actor", "Genre", "Year"]}
{"sentence": "show me a james bond movie that starred timothy dalton", "entity_names": ["james bond", "timothy dalton"], "entity_types": ["Character", "Actor"]}
{"sentence": "show me a list of movies starring kristen stewart as bella", "entity_names": ["kristen stewart", "bella"], "entity_types": ["Actor", "Character"]}
{"sentence": "show me a horror movie from the 1950s", "entity_names": ["horror", "1950s"], "entity_types": ["Genre", "Year"]}
{"sentence": "was christin slater in the movie billy jean", "entity_names": ["christin slater", "billy jean"], "entity_types": ["Actor", "Title"]}
{"sentence": "find all movies starring elvis presley", "entity_names": ["elvis presley"], "entity_types": ["Actor"]}
{"sentence": "what is the scariest horror movie from the 90s", "entity_names": ["scariest horror", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "what g rated movies were released in 2010", "entity_names": ["g rated", "2010"], "entity_types": ["MPAA Rating", "Year"]}
{"sentence": "what was the name of the comedy where cary elwes plays robin hood", "entity_names": ["comedy", "cary elwes", "robin hood"], "entity_types": ["Genre", "Actor", "Character"]}
{"sentence": "did russel crowe appear in a movie about a mathematician", "entity_names": ["russel crowe", "about a mathematician"], "entity_types": ["Actor", "Plot"]}
{"sentence": "can you find me a pg 13 movie about a bank robbery", "entity_names": ["pg 13", "bank robbery"], "entity_types": ["MPAA Rating", "Plot"]}
{"sentence": "what year did wild wild west come out", "entity_names": ["wild wild west"], "entity_types": ["Title"]}
{"sentence": "is there a documentary about a kiss tribute band", "entity_names": ["documentary", "about a kiss tribute band"], "entity_types": ["Genre", "Plot"]}
{"sentence": "where was lord of the rings filmed", "entity_names": ["lord of the rings"], "entity_types": ["Title"]}
{"sentence": "give me rated r movies that came out in 1995", "entity_names": ["r", "1995"], "entity_types": ["MPAA Rating", "Year"]}
{"sentence": "what movies are rated pg 13 that adam sandler has acted in", "entity_names": ["rated pg 13", "adam sandler"], "entity_types": ["Genre", "Actor"]}
{"sentence": "which actor do you prefer will smith or denzel washington", "entity_names": ["will smith", "denzel washington"], "entity_types": ["Actor", "Actor"]}
{"sentence": "show me a cameron diaz movie from the 90s", "entity_names": ["cameron diaz", "90s"], "entity_types": ["Actor", "Year"]}
{"sentence": "what year was jaws released", "entity_names": ["jaws"], "entity_types": ["Title"]}
{"sentence": "find me all movies based on tv shows released in the last 5 years", "entity_names": ["movies based on tv shows", "last 5 years"], "entity_types": ["Plot", "Year"]}
{"sentence": "what film features the song kiss from a rose by seal", "entity_names": ["kiss from a rose"], "entity_types": ["Song"]}
{"sentence": "was there a christopher reeves time spanning drama film", "entity_names": ["christopher reeves"], "entity_types": ["Actor"]}
{"sentence": "how many movies did woody allen make", "entity_names": ["woody allen"], "entity_types": ["Director"]}
{"sentence": "list movies that are rated pg and involve car chases", "entity_names": ["rated pg", "involve car chases"], "entity_types": ["MPAA Rating", "Plot"]}
{"sentence": "find me a well reviewed comedy starring john candy", "entity_names": ["well reviewed", "comedy", "john candy"], "entity_types": ["Viewers' Rating", "Genre", "Actor"]}
{"sentence": "who directed the bedford incident", "entity_names": ["the bedford incident"], "entity_types": ["Title"]}
{"sentence": "find a movie with katherine hepburn and spencer tracy", "entity_names": ["katherine hepburn", "spencer tracy"], "entity_types": ["Actor", "Actor"]}
{"sentence": "im looking for a movie about werewolves and vampires", "entity_names": ["about werewolves and vampires"], "entity_types": ["Plot"]}
{"sentence": "are there any adult animated movies available", "entity_names": ["adult animated"], "entity_types": ["Genre"]}
{"sentence": "show me disney dealing with aging movies", "entity_names": ["disney", "aging"], "entity_types": ["Genre", "Plot"]}
{"sentence": "show me a movie starring sigourney weaver", "entity_names": ["sigourney weaver"], "entity_types": ["Actor"]}
{"sentence": "find a movie with natalie wood and christopher walken", "entity_names": ["natalie wood", "christopher walken"], "entity_types": ["Actor", "Actor"]}
{"sentence": "show me what films were directed by the wachowski brothers", "entity_names": ["wachowski brothers"], "entity_types": ["Director"]}
{"sentence": "show me a childrens movie by disney with a character named simba", "entity_names": ["childrens movie", "simba"], "entity_types": ["Genre", "Character"]}
{"sentence": "which nc 17 film had the highest box office gross", "entity_names": ["box office gross"], "entity_types": ["Viewers' Rating"]}
{"sentence": "find me the best mafia movies", "entity_names": ["best", "mafia"], "entity_types": ["Viewers' Rating", "Plot"]}
{"sentence": "in what genre is the movie par 6", "entity_names": ["genre", "par 6"], "entity_types": ["Genre", "Title"]}
{"sentence": "is there a documentary about the sports agency industry", "entity_names": ["documentary", "about the sports agency industry"], "entity_types": ["Genre", "Plot"]}
{"sentence": "what films use the song zippity do da", "entity_names": ["zippity do da"], "entity_types": ["Song"]}
{"sentence": "did marilyn monroe star in any drama films", "entity_names": ["marilyn monroe", "drama"], "entity_types": ["Actor", "Genre"]}
{"sentence": "find childrens movies about puppets", "entity_names": ["childrens", "puppets"], "entity_types": ["Genre", "Plot"]}
{"sentence": "looking for an old black and white british movie called the holly and the ivy", "entity_names": ["black and white british", "the holly and the ivy"], "entity_types": ["Genre", "Title"]}
{"sentence": "has johnny depp directed any movies", "entity_names": ["johnny depp"], "entity_types": ["Director"]}
{"sentence": "play a trailer from the others", "entity_names": ["trailer", "the others"], "entity_types": ["Trailer", "Title"]}
{"sentence": "the hunger games star actress", "entity_names": ["hunger games"], "entity_types": ["Title"]}
{"sentence": "who is the director of the breakfast club", "entity_names": ["who is the director of the breakfast club"], "entity_types": ["Director"]}
{"sentence": "looking for the film with kathy bates as the deranged fan of a captive writer", "entity_names": ["kathy bates", "deranged fan of a captive writer"], "entity_types": ["Actor", "Plot"]}
{"sentence": "what is the plot of happy gilmore", "entity_names": ["what is the plot of happy gilmore"], "entity_types": ["Plot"]}
{"sentence": "are there any pg 13 movies featuring john travolta", "entity_names": ["pg 13", "john travolta"], "entity_types": ["MPAA Rating", "Actor"]}
{"sentence": "who starred in switchuing goals", "entity_names": ["switchuing goals"], "entity_types": ["Title"]}
{"sentence": "show me 1960 horror movies with zombies", "entity_names": ["1960", "horror movies", "zombies"], "entity_types": ["Year", "Genre", "Plot"]}
{"sentence": "who said nobody puts baby in the corner", "entity_names": [], "entity_types": []}
{"sentence": "show me movies based on tv shows", "entity_names": ["based on tv shows"], "entity_types": ["Plot"]}
{"sentence": "what movie has a fight scene with kim kardashian and carmen electra", "entity_names": ["fight scene", "kim kardashian", "carmen electra"], "entity_types": ["Plot", "Actor", "Actor"]}
{"sentence": "in what year was crimson tide released", "entity_names": ["in what year was", "crimson tide", "released"], "entity_types": ["Year", "Title", "Year"]}
{"sentence": "show me the peter greenaway movie based on the tempest", "entity_names": ["peter greenaway", "the tempest"], "entity_types": ["Director", "Title"]}
{"sentence": "are there any r rated movies about vampires", "entity_names": ["r rated", "vampires"], "entity_types": ["Genre", "Plot"]}
{"sentence": "who played both boys in the errol flynn movie the prince and the pauper", "entity_names": ["errol flynn", "the prince and the pauper"], "entity_types": ["Actor", "Title"]}
{"sentence": "help me find a movie with the song when you really love a woman", "entity_names": ["when you really love a woman"], "entity_types": ["Song"]}
{"sentence": "find an action flick with john wayne", "entity_names": ["action", "john wayne"], "entity_types": ["Genre", "Actor"]}
{"sentence": "tell me a movie made by peter jackson", "entity_names": ["peter jackson"], "entity_types": ["Director"]}
{"sentence": "name the disaster movie featuring bill paxton and a small role by phillip seymour hoffman", "entity_names": ["disaster", "bill paxton", "phillip seymour hoffman"], "entity_types": ["Plot", "Actor", "Actor"]}
{"sentence": "find me the movie with the character katniss in it", "entity_names": ["katniss"], "entity_types": ["Character"]}
{"sentence": "show me a 1980s movie starring steve martin", "entity_names": ["1980s", "steve martin"], "entity_types": ["Year", "Actor"]}
{"sentence": "find the melissa leo movie about smuggling illegals across the border", "entity_names": ["melissa leo movie", "about smuggling illegals across the border"], "entity_types": ["Actor", "Plot"]}
{"sentence": "did clint eastwood ever play a cowboy", "entity_names": ["clint eastwood", "cowboy"], "entity_types": ["Actor", "Plot"]}
{"sentence": "show me a movie with drew barrymore and adam sandler", "entity_names": ["drew barrymore", "adam sandler"], "entity_types": ["Actor", "Actor"]}
{"sentence": "find the movie hans conried and a gigantic piano keyboard", "entity_names": ["hans conried", "gigantic piano keyboard"], "entity_types": ["Actor", "Plot"]}
{"sentence": "im looking for an r rated movie starring tom hanks", "entity_names": ["r rated", "tom hanks"], "entity_types": ["MPAA Rating", "Actor"]}
{"sentence": "find the movie gummo released in the 1990s a punkcult movie", "entity_names": ["gummo", "1990s", "punkcult"], "entity_types": ["Title", "Year", "Genre"]}
{"sentence": "the song hold on by 90s band wilson phillips is used in which film soundtracks", "entity_names": ["hold on", "90s", "wilson phillips"], "entity_types": ["Song", "Year", "Song"]}
{"sentence": "find a review for the blue bird", "entity_names": ["find a review for the blue bird"], "entity_types": ["Viewers' Rating"]}
{"sentence": "what movie features keanu reeves as an exorcist", "entity_names": ["keanu reeves", "as an exorcist"], "entity_types": ["Actor", "Character"]}
{"sentence": "did jessica alba made any new movies after spy kids 4", "entity_names": ["jessica alba"], "entity_types": ["Actor"]}
{"sentence": "find a viewers review for the fast and the furious", "entity_names": ["review", "the fast and the furious"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "show me an lee meriweather film about a torch singer", "entity_names": ["lee meriweather", "torch singer"], "entity_types": ["Actor", "Plot"]}
{"sentence": "what film featured mel gibson danny glover and jet li", "entity_names": ["mel gibson danny glover", "jet li"], "entity_types": ["Actor", "Actor"]}
{"sentence": "which movie features jack blacks voice as a panda", "entity_names": ["jack blacks", "voice as a panda"], "entity_types": ["Actor", "Plot"]}
{"sentence": "what movies has jason segel been in", "entity_names": ["jason segel"], "entity_types": ["Actor"]}
{"sentence": "find me the movie that has darth vader in it", "entity_names": ["darth vader"], "entity_types": ["Character"]}
{"sentence": "did josh radnor direct any pg 13 movies", "entity_names": ["josh radnor", "pg 13"], "entity_types": ["Director", "MPAA Rating"]}
{"sentence": "find the movie directed by james cameron about blue people on another planet", "entity_names": ["james cameron", "blue people on another planet"], "entity_types": ["Director", "Plot"]}
{"sentence": "in what year was kid galahad released", "entity_names": ["kid galahad"], "entity_types": ["Title"]}
{"sentence": "how many movies featured both kirk douglas and michael douglas", "entity_names": ["kirk douglas", "michael douglas"], "entity_types": ["Actor", "Actor"]}
{"sentence": "what movie grossed the most amount of money opening weekend in 2011", "entity_names": ["grossed the most amount of money", "2011"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "name the last charlie boyer movie", "entity_names": ["charlie boyer"], "entity_types": ["Actor"]}
{"sentence": "which was the first harry potter movie in the series", "entity_names": ["first", "harry potter"], "entity_types": ["Year", "Title"]}
{"sentence": "rated r comedy movies 1990s", "entity_names": ["r", "comedy", "1990s"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "what was the name of the stepmother played by allison janney in juno", "entity_names": ["allison janney", "juno"], "entity_types": ["Actor", "Title"]}
{"sentence": "a 1980 well rated pg 13 that involves fighting of any kind", "entity_names": ["1980", "well rated", "pg 13", "fighting"], "entity_types": ["Year", "Viewers' Rating", "MPAA Rating", "Plot"]}
{"sentence": "a 1990 rated g film that has actor marcus testory in it that received a nine rating that is film noir", "entity_names": ["1990", "g", "marcus testory", "nine", "film noir"], "entity_types": ["Year", "MPAA Rating", "Actor", "Viewers' Rating", "Genre"]}
{"sentence": "a nc 17 movie with james horan in the past two decades", "entity_names": ["nc 17", "james horan", "past two decades"], "entity_types": ["MPAA Rating", "Actor", "Year"]}
{"sentence": "a film that was highly recommended from 1960 starring marilyn monroe", "entity_names": ["highly recommended", "1960", "marilyn monroe"], "entity_types": ["Viewers' Rating", "Year", "Actor"]}
{"sentence": "a highly liked teen movie with jim carrey please", "entity_names": ["highly liked", "teen", "jim carrey"], "entity_types": ["Viewers' Rating", "Genre", "Actor"]}
{"sentence": "a list of sci fi movies in 2010", "entity_names": ["sci fi"], "entity_types": ["Genre"]}
{"sentence": "a mediocre movie with a computer plot with a pg 13 rating", "entity_names": ["mediocre", "computer", "pg 13"], "entity_types": ["Viewers' Rating", "Plot", "MPAA Rating"]}
{"sentence": "a movie released in the past ten decades with a nc 17 rating starring kevin anderson was liked a lot", "entity_names": ["past ten decades", "nc 17", "kevin anderson", "was liked a lot"], "entity_types": ["Year", "MPAA Rating", "Actor", "Viewers' Rating"]}
{"sentence": "a movie that was directed by tennyson bardwell that is rated pg 13 that is mockumentary", "entity_names": ["tennyson bardwell", "pg 13", "mockumentary"], "entity_types": ["Director", "MPAA Rating", "Genre"]}
{"sentence": "a movie with actress judy davis was made prior to the past six decades would be", "entity_names": ["judy davis", "past six decades"], "entity_types": ["Actor", "Year"]}
{"sentence": "a must see romance film from 1990 with a pg 13 rating", "entity_names": ["must see", "romance", "1990", "pg 13"], "entity_types": ["Viewers' Rating", "Genre", "Year", "MPAA Rating"]}
{"sentence": "a rated g very popular william lustig family movie", "entity_names": ["g", "very popular", "william lustig", "family"], "entity_types": ["MPAA Rating", "Viewers' Rating", "Director", "Genre"]}
{"sentence": "amanda fire directed this family film", "entity_names": ["amanda fire", "family"], "entity_types": ["Director", "Genre"]}
{"sentence": "andrew lane produced what four star biography about a prisoner of war", "entity_names": ["andrew lane", "four star", "biography", "prisoner of war"], "entity_types": ["Director", "Viewers' Rating", "Genre", "Plot"]}
{"sentence": "any g rated movies with violence come out in the past decade", "entity_names": ["g", "violence", "past decade"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "any drama films about becoming a champion", "entity_names": ["drama", "champion"], "entity_types": ["Genre", "Plot"]}
{"sentence": "any film noir film from 1990 starring maria bello", "entity_names": ["film noir", "1990", "maria bello"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "any good harrison ford thrillers released", "entity_names": ["harrison ford", "thrillers"], "entity_types": ["Actor", "Genre"]}
{"sentence": "any good r rated action movies from the 1980 s out there", "entity_names": ["r", "action", "1980 s"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "any good actors in the movie double dagger", "entity_names": ["double dagger"], "entity_types": ["Title"]}
{"sentence": "any highly recommended movies for the 1980 s about military guerrilla warfare", "entity_names": ["highly recommended", "1980 s", "military", "guerrilla warfare"], "entity_types": ["Viewers' Rating", "Year", "Genre", "Plot"]}
{"sentence": "any rated r small town 1990 s movies that stars vanessa marcil", "entity_names": ["r", "small town", "1990 s", "vanessa marcil"], "entity_types": ["MPAA Rating", "Plot", "Year", "Actor"]}
{"sentence": "any science fiction movies with billy wilder", "entity_names": ["science fiction", "billy wilder"], "entity_types": ["Genre", "Director"]}
{"sentence": "any war type rated r movies set in the 1940 s that received five stars", "entity_names": ["war", "r", "1940 s", "five stars"], "entity_types": ["Genre", "MPAA Rating", "Year", "Viewers' Rating"]}
{"sentence": "are there any nc 17 kids movies from the past four decades", "entity_names": ["nc 17", "kids", "past four decades"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "are there any r rated children movies", "entity_names": ["children"], "entity_types": ["Genre"]}
{"sentence": "are there any r rated romance movie released in 2010 with four stars", "entity_names": ["r", "romance", "2010", "four stars"], "entity_types": ["MPAA Rating", "Genre", "Year", "Viewers' Rating"]}
{"sentence": "are there any scarlett johansson drama movies from the 1990 s", "entity_names": ["scarlett johansson", "drama", "1990 s"], "entity_types": ["Actor", "Genre", "Year"]}
{"sentence": "are there any action movies about terrorist that are pg 13 being released this year", "entity_names": ["action", "terrorist", "pg 13"], "entity_types": ["Genre", "Plot", "MPAA Rating"]}
{"sentence": "are there any adventure films directed by sofia coppola coming out soon", "entity_names": ["adventure", "sofia coppola"], "entity_types": ["Genre", "Director"]}
{"sentence": "are there any animation films from 2010 with actress cybill shepherd", "entity_names": ["animation", "2010", "cybill shepherd"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "are there any biography films about the 1930 s directed gregg sacon that are unrated and produced during the last six years", "entity_names": ["biography", "1930 s", "gregg sacon", "unrated", "last six years"], "entity_types": ["Genre", "Plot", "Director", "MPAA Rating", "Year"]}
{"sentence": "are there any crime movies with jennifer love hewitt in them", "entity_names": ["crime", "jennifer love hewitt"], "entity_types": ["Genre", "Actor"]}
{"sentence": "are there any documentary movies about peoples rights", "entity_names": ["documentary", "rights"], "entity_types": ["Genre", "Plot"]}
{"sentence": "are there any family movies tarring hettie macdonald in the last five decades", "entity_names": ["family", "hettie macdonald", "last five decades"], "entity_types": ["Genre", "Director", "Year"]}
{"sentence": "are there any independent films directed by orson welles", "entity_names": ["independent", "orson welles"], "entity_types": ["Genre", "Director"]}
{"sentence": "are there any independent movies from the 2010 s", "entity_names": ["independent", "2010 s"], "entity_types": ["Genre", "Year"]}
{"sentence": "are there any movies about the lion king featuring seth green", "entity_names": ["the lion king", "seth green"], "entity_types": ["Plot", "Actor"]}
{"sentence": "are there any movies on affairs in the past year", "entity_names": ["affairs", "past year"], "entity_types": ["Plot", "Year"]}
{"sentence": "are there any movies starring jodie foster with good ratings", "entity_names": ["jodie foster", "good ratings"], "entity_types": ["Actor", "Viewers' Rating"]}
{"sentence": "are there any movies starring stan laurel and oliver hardy from the last two years available", "entity_names": ["stan laurel and oliver hardy", "last two years"], "entity_types": ["Actor", "Year"]}
{"sentence": "are there any movies with children", "entity_names": ["children"], "entity_types": ["Genre"]}
{"sentence": "are there any new mockumentary films coming out soon", "entity_names": ["mockumentary"], "entity_types": ["Genre"]}
{"sentence": "are there any scary 1970 s movies starring sissy spacek", "entity_names": ["scary", "1970 s", "sissy spacek"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "are there any scary g rated films starring kelly ripa from the 1990 s", "entity_names": ["scary", "g", "kelly ripa", "1990 s"], "entity_types": ["Genre", "MPAA Rating", "Actor", "Year"]}
{"sentence": "audrey hepburn starred in this pg 13 crime film", "entity_names": ["audrey hepburn", "pg 13", "crime"], "entity_types": ["Actor", "MPAA Rating", "Genre"]}
{"sentence": "auto recovery", "entity_names": ["auto recovery"], "entity_types": ["Title"]}
{"sentence": "barry jenkins directed the greatest r rated mystery film during the last three years", "entity_names": ["barry jenkins", "r", "mystery", "last three years"], "entity_types": ["Director", "MPAA Rating", "Genre", "Year"]}
{"sentence": "billy warlock starred what sport movie in the 1980 s", "entity_names": ["billy warlock", "sport", "1980 s"], "entity_types": ["Actor", "Genre", "Year"]}
{"sentence": "can you give me a list of r rated scary movies from the year 1980", "entity_names": ["r", "scary", "1980"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "can you help me find a documentary rated pg that received two thumbs up from the 1960 s directed by aaron katz", "entity_names": ["documentary", "pg", "two thumbs up", "1960 s", "aaron katz"], "entity_types": ["Genre", "MPAA Rating", "Viewers' Rating", "Year", "Director"]}
{"sentence": "can you help me find a film noir rated pg 13 starring richard jaeckel", "entity_names": ["film noir", "pg 13", "richard jaeckel"], "entity_types": ["Genre", "MPAA Rating", "Actor"]}
{"sentence": "can you help me find an r rated gangster movie about a dark past from the last four years with tom berenger in it", "entity_names": ["r", "gangster", "dark past", "last four years", "tom berenger"], "entity_types": ["MPAA Rating", "Genre", "Plot", "Year", "Actor"]}
{"sentence": "can you help me find some good comedy movies", "entity_names": ["comedy"], "entity_types": ["Genre"]}
{"sentence": "can you list all the mediocre adventure movies made in the last two years", "entity_names": ["mediocre", "adventure", "last two years"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "can you name a western movie rated r", "entity_names": ["western", "r"], "entity_types": ["Genre", "MPAA Rating"]}
{"sentence": "can you please recommend a fantasy movie about the occult from the 1980 s", "entity_names": ["fantasy", "occult", "1980 s"], "entity_types": ["Genre", "Plot", "Year"]}
{"sentence": "can you please recommend a good fantasy movie that came out last year", "entity_names": ["fantasy"], "entity_types": ["Genre"]}
{"sentence": "can you provide a list of movies that james dean was in", "entity_names": ["james dean"], "entity_types": ["Actor"]}
{"sentence": "can you recommend a pg 13 rated military movie from the year 2000", "entity_names": ["pg 13", "military", "2000"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "can you recommend a classic crime movie made in the 1980 s starring clint eastwood that has a pg 13 rating", "entity_names": ["crime", "1980 s", "clint eastwood", "pg 13"], "entity_types": ["Genre", "Year", "Actor", "MPAA Rating"]}
{"sentence": "can you recommend a movie with a rating of five that stars sigourney weaver", "entity_names": ["five", "sigourney weaver"], "entity_types": ["Viewers' Rating", "Actor"]}
{"sentence": "can you recommend an horror movie that pauly shore was in", "entity_names": ["horror", "pauly shore"], "entity_types": ["Genre", "Actor"]}
{"sentence": "can you show me where i can find a movie about violence from 1970 that was directed by grace lee", "entity_names": ["violence", "1970", "grace lee"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "can you tell me the name of a movie i saw in the late 90 s that was set in a futuristic utopia and it was rated r and the director was i think something like steve stamatiadis", "entity_names": ["utopia", "r", "steve stamatiadis"], "entity_types": ["Plot", "MPAA Rating", "Director"]}
{"sentence": "could you direct me to where i can find an r rated romance movie", "entity_names": ["r", "romance"], "entity_types": ["MPAA Rating", "Genre"]}
{"sentence": "could you help me find a movie starring larenz tate that was made in the past eight years that was rated well", "entity_names": ["larenz tate", "past eight years", "rated well"], "entity_types": ["Actor", "Year", "Viewers' Rating"]}
{"sentence": "could you recommend some movies about marriage that are r rated", "entity_names": ["marriage", "r"], "entity_types": ["Plot", "MPAA Rating"]}
{"sentence": "d b sweeney stared in a pg 13 musical in the 1950", "entity_names": ["d b sweeney", "pg 13", "musical", "1950"], "entity_types": ["Actor", "MPAA Rating", "Genre", "Year"]}
{"sentence": "dating 1980 s movie that was rated pg 13 and was rated four stars", "entity_names": ["dating", "1980 s", "pg 13", "four"], "entity_types": ["Plot", "Year", "MPAA Rating", "Viewers' Rating"]}
{"sentence": "details about scooby doo and the loch ness monster kids movie", "entity_names": ["scooby doo and the loch ness monster"], "entity_types": ["Title"]}
{"sentence": "did al lewis play in an r rated highly rated biography in 2010", "entity_names": ["al lewis", "r", "highly rated", "biography", "2010"], "entity_types": ["Actor", "MPAA Rating", "Viewers' Rating", "Genre", "Year"]}
{"sentence": "did alfred hitchcock ever direct a documentary", "entity_names": ["alfred hitchcock", "documentary"], "entity_types": ["Director", "Genre"]}
{"sentence": "did bette davis ever star in a movie directed by martin scorsese", "entity_names": ["bette davis", "martin scorsese"], "entity_types": ["Actor", "Director"]}
{"sentence": "did brian de palma direct any movies for children", "entity_names": ["brian de palma", "children"], "entity_types": ["Director", "Genre"]}
{"sentence": "did chad martin direct a western in the 1990 s", "entity_names": ["chad martin", "western", "1990 s"], "entity_types": ["Director", "Genre", "Year"]}
{"sentence": "did clint eastwood direct inception", "entity_names": ["clint eastwood", "inception"], "entity_types": ["Director", "Title"]}
{"sentence": "did clint eastwood direct any historical movies in the 2000 s", "entity_names": ["clint eastwood", "historical", "2000 s"], "entity_types": ["Director", "Genre", "Year"]}
{"sentence": "did damon wayons star in an unrated nine stars movie in 1940", "entity_names": ["damon wayons", "unrated", "nine stars", "1940"], "entity_types": ["Actor", "MPAA Rating", "Viewers' Rating", "Year"]}
{"sentence": "did david fincher direct a movie about war in the 1990 s", "entity_names": ["david fincher", "war", "1990 s"], "entity_types": ["Director", "Genre", "Year"]}
{"sentence": "did david hyde pierce ever star in a movie involving abandonment", "entity_names": ["david hyde pierce", "abandonment"], "entity_types": ["Actor", "Plot"]}
{"sentence": "did david lean direct any fantasy movies in the 2000 s", "entity_names": ["david lean", "fantasy"], "entity_types": ["Director", "Genre"]}
{"sentence": "did david lean ever direct a drama in the 1980 s", "entity_names": ["david lean", "drama", "1980 s"], "entity_types": ["Director", "Genre", "Year"]}
{"sentence": "did dylan neal do a movie child movie", "entity_names": ["dylan neal", "child"], "entity_types": ["Actor", "Genre"]}
{"sentence": "did francis ford coppola direct a thriller in the 1970 s", "entity_names": ["francis ford coppola", "thriller", "1970 s"], "entity_types": ["Director", "Genre", "Year"]}
{"sentence": "did francois truffaut direct any documentary films", "entity_names": ["francois truffaut", "documentary"], "entity_types": ["Director", "Genre"]}
{"sentence": "did frank capra do and ok animated movie", "entity_names": ["frank capra", "ok", "animated"], "entity_types": ["Director", "Viewers' Rating", "Genre"]}
{"sentence": "did frank capra ever direct an r rated musical", "entity_names": ["frank capra", "r", "musical"], "entity_types": ["Director", "MPAA Rating", "Genre"]}
{"sentence": "did george carlin star in any r rated or family films in the last decade", "entity_names": ["george carlin", "r", "family", "last decade"], "entity_types": ["Actor", "MPAA Rating", "Genre", "Year"]}
{"sentence": "did george lucas direct a fantasy movie in the 1980 s", "entity_names": ["george lucas", "fantasy", "1980 s"], "entity_types": ["Director", "Genre", "Year"]}
{"sentence": "did hal mohr direct a movie about gags in the 1950 s that was given six stars", "entity_names": ["hal mohr", "gags", "1950 s", "six stars"], "entity_types": ["Director", "Genre", "Year", "Viewers' Rating"]}
{"sentence": "did hayao miyazaki ever direct and films for children", "entity_names": ["hayao miyazaki", "children"], "entity_types": ["Director", "Genre"]}
{"sentence": "did jacob ransom have a movie about fear of marriage that was rated seven in the last five decades", "entity_names": ["jacob ransom", "fear of marriage", "seven", "last five decades"], "entity_types": ["Director", "Plot", "Viewers' Rating", "Year"]}
{"sentence": "did james stewart act in any highly recommended film noir movies", "entity_names": ["james stewart", "highly recommended", "film noir"], "entity_types": ["Actor", "Viewers' Rating", "Genre"]}
{"sentence": "did jerry obach do any shorts", "entity_names": ["jerry obach", "shorts"], "entity_types": ["Actor", "Genre"]}
{"sentence": "did jerry obach ever do an adventure movie", "entity_names": ["jerry obach", "adventure"], "entity_types": ["Actor", "Genre"]}
{"sentence": "did john huston direct a mystery film", "entity_names": ["john huston", "mystery"], "entity_types": ["Director", "Genre"]}
{"sentence": "did leonardo dicaprio star in a film directed by brian de palma", "entity_names": ["leonardo dicaprio", "brian de palma"], "entity_types": ["Actor", "Director"]}
{"sentence": "did lucille ball appear in any pg 13 movies in the 1990 s", "entity_names": ["lucille ball", "pg 13", "1990 s"], "entity_types": ["Actor", "MPAA Rating", "Year"]}
{"sentence": "did neil h weiss direct a murder film in the past ten years that got eight stars", "entity_names": ["neil h weiss", "murder", "past ten years", "eight"], "entity_types": ["Director", "Plot", "Year", "Viewers' Rating"]}
{"sentence": "did nick stagliano direct any pg 13 mystery movies in the 1980 s rated around eight stars", "entity_names": ["nick stagliano", "pg 13", "mystery", "1980 s", "eight stars"], "entity_types": ["Director", "MPAA Rating", "Genre", "Year", "Viewers' Rating"]}
{"sentence": "did orson welles ever direct a romantic comedy", "entity_names": ["orson welles", "romantic comedy"], "entity_types": ["Director", "Genre"]}
{"sentence": "did paul mazursky direct a film about redemption in the past two years", "entity_names": ["paul mazursky", "redemption", "past two years"], "entity_types": ["Director", "Plot", "Year"]}
{"sentence": "did robby benson direct a short movie that was rated eight stars in 1990", "entity_names": ["robby benson", "short", "eight stars", "1990"], "entity_types": ["Director", "Genre", "Viewers' Rating", "Year"]}
{"sentence": "did robert zemeckis ever direct a fantasy movie", "entity_names": ["robert zemeckis", "fantasy"], "entity_types": ["Director", "Genre"]}
{"sentence": "did roman polanski direct any pg 13 thriller movies that were highly liked", "entity_names": ["roman polanski", "pg 13", "thriller", "highly liked"], "entity_types": ["Director", "MPAA Rating", "Genre", "Viewers' Rating"]}
{"sentence": "did ron howard direct a science fiction film", "entity_names": ["ron howard", "science fiction"], "entity_types": ["Director", "Genre"]}
{"sentence": "did ron howard ever direct a musical in the 1960 s", "entity_names": ["ron howard", "musical", "1960 s"], "entity_types": ["Director", "Genre", "Year"]}
{"sentence": "did ron howard ever direct an independent film", "entity_names": ["ron howard", "independent"], "entity_types": ["Director", "Genre"]}
{"sentence": "did sam raimi direct any mystery movies that are rated r in the 1990 s", "entity_names": ["sam raimi", "mystery", "r", "1990 s"], "entity_types": ["Director", "Genre", "MPAA Rating", "Year"]}
{"sentence": "did sergio leone direct any fantasy movies that are rated pg 13", "entity_names": ["sergio leone", "fantasy", "pg 13"], "entity_types": ["Director", "Genre", "MPAA Rating"]}
{"sentence": "did sergio leone ever direct a movie in the children genre", "entity_names": ["sergio leone", "children"], "entity_types": ["Director", "Genre"]}
{"sentence": "did spike lee direct any adventure movies", "entity_names": ["spike lee", "adventure"], "entity_types": ["Director", "Genre"]}
{"sentence": "did spike lee ever direct an avant garde film", "entity_names": ["spike lee", "avant garde"], "entity_types": ["Director", "Genre"]}
{"sentence": "did stanley kubrick ever direct any romantic comedy films", "entity_names": ["stanley kubrick", "romantic comedy"], "entity_types": ["Director", "Genre"]}
{"sentence": "did stephen j anderson star in any sci fi movie during the 1950 s", "entity_names": ["stephen j anderson", "sci fi", "1950 s"], "entity_types": ["Director", "Genre", "Year"]}
{"sentence": "did steve barron direct any comedy movies that people thought was all right", "entity_names": ["steve barron", "comedy", "all right"], "entity_types": ["Director", "Genre", "Viewers' Rating"]}
{"sentence": "did steven shainberg star in a very popular mystery movie that was rated r", "entity_names": ["steven shainberg", "very popular", "mystery", "r"], "entity_types": ["Director", "Viewers' Rating", "Genre", "MPAA Rating"]}
{"sentence": "did steven soderbergh direct a mockumentary", "entity_names": ["steven soderbergh", "mockumentary"], "entity_types": ["Director", "Genre"]}
{"sentence": "did steven soderbergh direct an r rated film noir movie", "entity_names": ["steven soderbergh", "r", "film noir"], "entity_types": ["Director", "MPAA Rating", "Genre"]}
{"sentence": "did steven spielberg direct wall e", "entity_names": ["steven spielberg", "wall e"], "entity_types": ["Director", "Title"]}
{"sentence": "did steven spielberg direct any horror movies in the 1980 s", "entity_names": ["steven spielberg", "horror", "1980 s"], "entity_types": ["Director", "Genre", "Year"]}
{"sentence": "did terence fisher direct a movie about first love in the past two decades", "entity_names": ["terence fisher", "first love", "past two decades"], "entity_types": ["Director", "Plot", "Year"]}
{"sentence": "did the shining receive a rating of five stars and above", "entity_names": ["the shining", "five stars and above"], "entity_types": ["Title", "Viewers' Rating"]}
{"sentence": "did tom jones starring in pg 13 film centers on human in the past eight years", "entity_names": ["tom jones", "pg 13", "human", "past eight years"], "entity_types": ["Actor", "MPAA Rating", "Plot", "Year"]}
{"sentence": "did vivien leigh act in any historical movies in the 1950 s", "entity_names": ["vivien leigh", "historical", "1950 s"], "entity_types": ["Actor", "Genre", "Year"]}
{"sentence": "did director stefan popescu make a movie about veganism", "entity_names": ["stefan popescu", "veganism"], "entity_types": ["Director", "Plot"]}
{"sentence": "did you enjoy pink flamingos", "entity_names": ["pink flamingos"], "entity_types": ["Title"]}
{"sentence": "did you enjoy the worlds greatest dad", "entity_names": ["worlds greatest dad"], "entity_types": ["Title"]}
{"sentence": "did you love mamma mia", "entity_names": ["mamma mia"], "entity_types": ["Title"]}
{"sentence": "did you see custers last man i survived little big horn", "entity_names": ["custers last man i survived little big horn"], "entity_types": ["Title"]}
{"sentence": "do the coen brothers ever direct action movies", "entity_names": ["the coen brothers", "action"], "entity_types": ["Director", "Genre"]}
{"sentence": "do you carry the movie the a team", "entity_names": ["the a team"], "entity_types": ["Title"]}
{"sentence": "do you happen to carry the movie gold diggers of 1933", "entity_names": ["gold diggers of 1933"], "entity_types": ["Title"]}
{"sentence": "do you happen to know where i might find the movie every which way but loose", "entity_names": ["every which way but loose"], "entity_types": ["Title"]}
{"sentence": "do you have colossus the forbin project", "entity_names": ["colossus the forbin project"], "entity_types": ["Title"]}
{"sentence": "do you have a debra winger adventure movie with an average ratings of a six from the last seven decades about degradation", "entity_names": ["debra winger", "adventure", "six", "last seven decades", "degradation"], "entity_types": ["Actor", "Genre", "Viewers' Rating", "Year", "Plot"]}
{"sentence": "do you have a pg 13 history film about a political state that stars elizabeth taylor", "entity_names": ["pg 13", "history", "political state", "elizabeth taylor"], "entity_types": ["MPAA Rating", "Genre", "Plot", "Actor"]}
{"sentence": "do you have a comedy from the 1940 s directed by joey stewart", "entity_names": ["comedy", "1940 s", "joey stewart"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "do you have a decent film from the 2010 s that stars randolph mantooth", "entity_names": ["decent", "2010 s", "randolph mantooth"], "entity_types": ["Viewers' Rating", "Year", "Actor"]}
{"sentence": "do you have a fantasy film from last year directed by randy barbato", "entity_names": ["fantasy", "last year", "randy barbato"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "do you have a film from the 1950 s liked by many people that starred william forsythe", "entity_names": ["1950 s", "liked by many", "william forsythe"], "entity_types": ["Year", "Viewers' Rating", "Actor"]}
{"sentence": "do you have a must see war film rated pg 13 from the past seven decades", "entity_names": ["must see", "war", "pg 13", "past seven decades"], "entity_types": ["Viewers' Rating", "Genre", "MPAA Rating", "Year"]}
{"sentence": "do you have any kenneth branagh movies from the 1980 s", "entity_names": ["kenneth branagh", "1980 s"], "entity_types": ["Actor", "Year"]}
{"sentence": "do you have any nc 17 rated 1980 drama movie starring cameron starman", "entity_names": ["nc 17", "1980", "drama", "cameron starman"], "entity_types": ["MPAA Rating", "Year", "Genre", "Actor"]}
{"sentence": "do you have any pg 13 family films from the last two years that was liked by many and starred chris noth", "entity_names": ["pg 13", "family", "last two years", "liked by many", "chris noth"], "entity_types": ["MPAA Rating", "Genre", "Year", "Viewers' Rating", "Actor"]}
{"sentence": "do you have any information about the movie buying the cow", "entity_names": ["buying the cow"], "entity_types": ["Title"]}
{"sentence": "do you have any sci fi movies that were directed by jon knautz from this year", "entity_names": ["sci fi", "jon knautz", "this year"], "entity_types": ["Genre", "Director", "Year"]}
{"sentence": "do you have that really popular romance flick from the 2010 s rated pg 13 directed by robin blazak", "entity_names": ["really popular", "romance", "2010 s", "pg 13", "robin blazak"], "entity_types": ["Viewers' Rating", "Genre", "Year", "MPAA Rating", "Director"]}
{"sentence": "do you have the 1970 s r rated horror film starring robert deniro", "entity_names": ["1970 s", "r", "horror", "robert deniro"], "entity_types": ["Year", "MPAA Rating", "Genre", "Actor"]}
{"sentence": "do you have the la monte edwards science fiction film from the 1970 s about shapeshifting", "entity_names": ["la monte edwards", "science fiction", "1970 s", "shapeshifting"], "entity_types": ["Director", "Genre", "Year", "Plot"]}
{"sentence": "do you have the pg 13 movie from 1970 about a road trip directed by karen harley thats good for a laugh", "entity_names": ["pg 13", "1970", "road trip", "karen harley", "laugh"], "entity_types": ["MPAA Rating", "Year", "Plot", "Director", "Genre"]}
{"sentence": "do you have the average pg 13 film starring joaquin phoenix", "entity_names": ["average", "pg 13", "joaquin phoenix"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Actor"]}
{"sentence": "do you have the film the loss of sexual innocence", "entity_names": ["the loss of sexual innocence"], "entity_types": ["Title"]}
{"sentence": "do you have the gangster film from the past four years directed by rob hardy about a missing prisoner", "entity_names": ["gangster", "past four years", "rob hardy", "missing prisoner"], "entity_types": ["Genre", "Year", "Director", "Plot"]}
{"sentence": "do you have the movie key largo", "entity_names": ["key largo"], "entity_types": ["Title"]}
{"sentence": "do you have the movie spawn", "entity_names": ["spawn"], "entity_types": ["Title"]}
{"sentence": "do you have the movie suspiria", "entity_names": ["suspiria"], "entity_types": ["Title"]}
{"sentence": "do you have the movie the level", "entity_names": ["the level"], "entity_types": ["Title"]}
{"sentence": "do you have the sports film rated nc 17 and directed by erik canuel", "entity_names": ["sports", "nc 17", "erik canuel"], "entity_types": ["Genre", "MPAA Rating", "Director"]}
{"sentence": "do you know an unrated really popular biography set in brooklyn", "entity_names": ["unrated", "really popular", "biography", "brooklyn"], "entity_types": ["MPAA Rating", "Viewers' Rating", "Genre", "Plot"]}
{"sentence": "do you know of the r rated movie starring mark dacascos in a war movie during the vietcong vietnam that was liked by many", "entity_names": ["r", "mark dacascos", "war", "vietcong vietnam", "liked by many"], "entity_types": ["MPAA Rating", "Actor", "Genre", "Plot", "Viewers' Rating"]}
{"sentence": "do you know the title to the documentary that rutger hauer appeared in some time around 1950", "entity_names": ["documentary", "rutger hauer", "1950"], "entity_types": ["Genre", "Actor", "Year"]}
{"sentence": "do you know where i can find a nc 17 rated extraterrestrial movie directed by martin mcdonagh", "entity_names": ["nc 17", "extraterrestrial", "martin mcdonagh"], "entity_types": ["MPAA Rating", "Plot", "Director"]}
{"sentence": "do you know where i can find a pg sports movie from the 1940 s that was directed by wych kaosayananda", "entity_names": ["pg", "sports", "1940 s", "wych kaosayananda"], "entity_types": ["MPAA Rating", "Genre", "Year", "Director"]}
{"sentence": "do you know where i can find the movie paradox of the andes", "entity_names": ["paradox of the andes"], "entity_types": ["Title"]}
{"sentence": "do you know where i might find the movie must love death", "entity_names": ["must love death"], "entity_types": ["Title"]}
{"sentence": "do you know where id be able to find an nc 17 rated 1970 movie of the tale genre", "entity_names": ["nc 17", "1970", "tale"], "entity_types": ["MPAA Rating", "Year", "Genre"]}
{"sentence": "do you know where id be able to find an good r rated movie about a family that was directed by josh stone", "entity_names": ["good", "r", "family", "josh stone"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Plot", "Director"]}
{"sentence": "do you know where to find the movie welcome to mooseport", "entity_names": ["welcome to mooseport"], "entity_types": ["Title"]}
{"sentence": "do you know who starred in hollywood daffy", "entity_names": ["hollywood daffy"], "entity_types": ["Title"]}
{"sentence": "do you think youd be able to help me find the penguins of madagascar operation dvd premier", "entity_names": ["the penguins of madagascar operation dvd premier"], "entity_types": ["Title"]}
{"sentence": "does a j langer have a g movie", "entity_names": ["a j langer", "g"], "entity_types": ["Actor", "MPAA Rating"]}
{"sentence": "does adam sandler appear in a crime film", "entity_names": ["adam sandler", "crime"], "entity_types": ["Actor", "Genre"]}
{"sentence": "does julia roberts have a voice in wall e", "entity_names": ["julia roberts", "wall e"], "entity_types": ["Actor", "Title"]}
{"sentence": "does a 1970 s john byrum horror movie that got seven stars exist", "entity_names": ["1970 s", "john byrum", "horror", "seven stars"], "entity_types": ["Year", "Director", "Genre", "Viewers' Rating"]}
{"sentence": "eddie cibrian starred in what merit badge tale that people found ok made in the past four years", "entity_names": ["eddie cibrian", "merit badge", "tale", "ok", "past four years"], "entity_types": ["Actor", "Plot", "Genre", "Viewers' Rating", "Year"]}
{"sentence": "find the shining movie which director was john huston", "entity_names": ["the shining", "john huston"], "entity_types": ["Title", "Director"]}
{"sentence": "find a 1950 nc 17 car chase movie directed by marcus warren", "entity_names": ["1950", "nc 17", "car chase", "marcus warren"], "entity_types": ["Year", "MPAA Rating", "Plot", "Director"]}
{"sentence": "find a 1990 s film about a curse", "entity_names": ["1990 s", "curse"], "entity_types": ["Year", "Plot"]}
{"sentence": "find a pg 13 western movie rated seven stars and above", "entity_names": ["pg 13", "western", "seven stars and above"], "entity_types": ["MPAA Rating", "Genre", "Viewers' Rating"]}
{"sentence": "find a cowboy movie rated pg starring bridget fonda", "entity_names": ["cowboy", "pg", "bridget fonda"], "entity_types": ["Genre", "MPAA Rating", "Actor"]}
{"sentence": "find a critically acclaimed 1980 sci fi film", "entity_names": ["critically acclaimed", "1980", "sci fi"], "entity_types": ["Viewers' Rating", "Year", "Genre"]}
{"sentence": "find a fantasy movie", "entity_names": ["fantasy"], "entity_types": ["Genre"]}
{"sentence": "find a film noir rated pg 13", "entity_names": ["film noir", "pg 13"], "entity_types": ["Genre", "MPAA Rating"]}
{"sentence": "find a film titled shut", "entity_names": ["shut"], "entity_types": ["Title"]}
{"sentence": "find a film with the title suicide killers", "entity_names": ["suicide killers"], "entity_types": ["Title"]}
{"sentence": "find a military film from the last six years starring stacey dash", "entity_names": ["military", "last six years", "stacey dash"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "find a mockumentary movie with the plot being societal issues", "entity_names": ["mockumentary", "societal issues"], "entity_types": ["Genre", "Plot"]}
{"sentence": "find a movie called crossover", "entity_names": ["crossover"], "entity_types": ["Title"]}
{"sentence": "find a romantic comedy starring hugh jackman", "entity_names": ["romantic comedy", "hugh jackman"], "entity_types": ["Genre", "Actor"]}
{"sentence": "find a sci fi film", "entity_names": ["sci fi"], "entity_types": ["Genre"]}
{"sentence": "find a thriller movie with lies being the plot", "entity_names": ["thriller", "lies"], "entity_types": ["Genre", "Plot"]}
{"sentence": "find any biography movies from the last two decades", "entity_names": ["biography", "last two decades"], "entity_types": ["Genre", "Year"]}
{"sentence": "find average science fiction movies rated g from 1990", "entity_names": ["average", "science fiction", "g", "1990"], "entity_types": ["Viewers' Rating", "Genre", "MPAA Rating", "Year"]}
{"sentence": "find me a fantasy movie made withing the past eight decades", "entity_names": ["fantasy", "past eight decades"], "entity_types": ["Genre", "Year"]}
{"sentence": "frank darabont made good movies around 1950 s any good war films by him", "entity_names": ["frank darabont", "1950 s", "war"], "entity_types": ["Director", "Year", "Genre"]}
{"sentence": "give details of the movie wall e starring hugh jackman", "entity_names": ["wall e", "hugh jackman"], "entity_types": ["Title", "Actor"]}
{"sentence": "give me info about bright young things", "entity_names": ["bright young things"], "entity_types": ["Title"]}
{"sentence": "give me more details about the movie top gun", "entity_names": ["top gun"], "entity_types": ["Title"]}
{"sentence": "had david lynch ever directed an avant garde movie that is rated r", "entity_names": ["david lynch", "avant garde", "r"], "entity_types": ["Director", "Genre", "MPAA Rating"]}
{"sentence": "has andrei tarkovsky directed any rated g biographical movies that got two thumbs up", "entity_names": ["andrei tarkovsky", "g", "biographical", "two thumbs up"], "entity_types": ["Director", "MPAA Rating", "Genre", "Viewers' Rating"]}
{"sentence": "has billy wilder directed and rated r horror films", "entity_names": ["billy wilder", "r", "horror"], "entity_types": ["Director", "MPAA Rating", "Genre"]}
{"sentence": "has bobbie phillips been in a r rated war movie that got a rating of seven in the last six decades", "entity_names": ["bobbie phillips", "r", "war", "seven", "last six decades"], "entity_types": ["Actor", "MPAA Rating", "Genre", "Viewers' Rating", "Year"]}
{"sentence": "has charlton heston ever been a voice in an animated movie", "entity_names": ["charlton heston", "animated"], "entity_types": ["Actor", "Genre"]}
{"sentence": "has charlton heston ever been in a musical", "entity_names": ["charlton heston", "musical"], "entity_types": ["Actor", "Genre"]}
{"sentence": "has david denneen directed a rated r sci fi on the past decade", "entity_names": ["david denneen", "r", "sci fi", "past decade"], "entity_types": ["Director", "MPAA Rating", "Genre", "Year"]}
{"sentence": "has david lynch ever directed any historical films", "entity_names": ["david lynch", "historical"], "entity_types": ["Director", "Genre"]}
{"sentence": "has francois truffaut ever directed a disaster movie", "entity_names": ["francois truffaut", "disaster"], "entity_types": ["Director", "Genre"]}
{"sentence": "has frank capra directed any action movies that received two thumbs up", "entity_names": ["frank capra", "action", "two thumbs up"], "entity_types": ["Director", "Genre", "Viewers' Rating"]}
{"sentence": "has frank darabont directed any westerns", "entity_names": ["frank darabont", "westerns"], "entity_types": ["Director", "Genre"]}
{"sentence": "has fritz lang directed any teen movies in the past year", "entity_names": ["fritz lang", "teen"], "entity_types": ["Director", "Genre"]}
{"sentence": "has gary cooper been in any good historical films", "entity_names": ["gary cooper", "historical"], "entity_types": ["Actor", "Genre"]}
{"sentence": "has gary cooper ever been in a movie directed by steven spielberg", "entity_names": ["gary cooper", "steven spielberg"], "entity_types": ["Actor", "Director"]}
{"sentence": "has guillermo del toro ever been in a children movie", "entity_names": ["guillermo del toro", "children"], "entity_types": ["Director", "Genre"]}
{"sentence": "has ingrid bergman ever won an oscar", "entity_names": ["ingrid bergman"], "entity_types": ["Actor"]}
{"sentence": "has james dean acted in any drama films", "entity_names": ["james dean", "drama"], "entity_types": ["Actor", "Genre"]}
{"sentence": "has jennifer connelly ever been in a critically acclaimed nc 17 movie", "entity_names": ["jennifer connelly", "critically acclaimed", "nc 17"], "entity_types": ["Actor", "Viewers' Rating", "MPAA Rating"]}
{"sentence": "has john huston ever directed any avant garde movies that are rated pg", "entity_names": ["john huston", "avant garde", "pg"], "entity_types": ["Director", "Genre", "MPAA Rating"]}
{"sentence": "has john ford ever directed and biographical movies", "entity_names": ["john ford", "biographical"], "entity_types": ["Director", "Genre"]}
{"sentence": "has judi dench been in an adventure movie", "entity_names": ["judi dench", "adventure"], "entity_types": ["Actor", "Genre"]}
{"sentence": "has julie andrews ever been a rated r film noir movie", "entity_names": ["julie andrews", "r", "film noir"], "entity_types": ["Actor", "MPAA Rating", "Genre"]}
{"sentence": "has kevin spacey appeared in an comedy films that are rated g", "entity_names": ["kevin spacey", "comedy", "g"], "entity_types": ["Actor", "Genre", "MPAA Rating"]}
{"sentence": "has kevin spacey ever been in a romantic comedy", "entity_names": ["kevin spacey", "romantic comedy"], "entity_types": ["Actor", "Genre"]}
{"sentence": "has michael keusch directed any rated r musicals in the past ten decades", "entity_names": ["michael keusch", "r", "musicals", "past ten decades"], "entity_types": ["Director", "MPAA Rating", "Genre", "Year"]}
{"sentence": "has michelangelo antonioni ever directed a musical", "entity_names": ["michelangelo antonioni", "musical"], "entity_types": ["Director", "Genre"]}
{"sentence": "has michelangelo antonioni ever directed a western", "entity_names": ["michelangelo antonioni", "western"], "entity_types": ["Director", "Genre"]}
{"sentence": "has nicole kidman ever been in a thriller movie", "entity_names": ["nicole kidman", "thriller"], "entity_types": ["Actor", "Genre"]}
{"sentence": "has orson welles directed and drama movies with a pg 13 rating", "entity_names": ["orson welles", "drama", "pg 13"], "entity_types": ["Director", "Genre", "MPAA Rating"]}
{"sentence": "has pascal franchot directed any really popular animation in the last two years", "entity_names": ["pascal franchot", "really popular", "animation", "last two years"], "entity_types": ["Director", "Viewers' Rating", "Genre", "Year"]}
{"sentence": "has paul newman been in a thriller that received eight stars and above", "entity_names": ["paul newman", "thriller", "eight stars and above"], "entity_types": ["Actor", "Genre", "Viewers' Rating"]}
{"sentence": "has phylicia rashad done any r rated action movies in the past six years", "entity_names": ["phylicia rashad", "r", "action", "past six years"], "entity_types": ["Actor", "MPAA Rating", "Genre", "Year"]}
{"sentence": "has quentin tarantino ever directed a horror film", "entity_names": ["quentin tarantino", "horror"], "entity_types": ["Director", "Genre"]}
{"sentence": "has ridley scott ever directed an avant garde movie", "entity_names": ["ridley scott", "avant garde"], "entity_types": ["Director", "Genre"]}
{"sentence": "has ridley scott ever directed an independent film", "entity_names": ["ridley scott", "independent"], "entity_types": ["Director", "Genre"]}
{"sentence": "has sergio leone directed any animated films", "entity_names": ["sergio leone", "animated"], "entity_types": ["Director", "Genre"]}
{"sentence": "has sofia coppola ever directed a thriller movie", "entity_names": ["sofia coppola", "thriller"], "entity_types": ["Director", "Genre"]}
{"sentence": "has traci bingham made any movies in the last five years", "entity_names": ["traci bingham", "last five years"], "entity_types": ["Actor", "Year"]}
{"sentence": "has woody allen ever directed a teen movie", "entity_names": ["woody allen", "teen"], "entity_types": ["Director", "Genre"]}
{"sentence": "has there been an adventure pg 13 movies in the last two decades that mark pavia has directed", "entity_names": ["adventure", "pg 13", "last two decades", "mark pavia"], "entity_types": ["Genre", "MPAA Rating", "Year", "Director"]}
{"sentence": "have you ever watched the vantage point", "entity_names": ["vantage point"], "entity_types": ["Title"]}
{"sentence": "have you seen my last five girlfriends", "entity_names": ["my last five girlfriends"], "entity_types": ["Title"]}
{"sentence": "have you seen a charlton heston disaster movie", "entity_names": ["charlton heston", "disaster"], "entity_types": ["Actor", "Genre"]}
{"sentence": "have you seen the movie inception with jodie foster", "entity_names": ["inception", "jodie foster"], "entity_types": ["Title", "Actor"]}
{"sentence": "how come i do not see as many mockumentary movies being made these days", "entity_names": ["mockumentary"], "entity_types": ["Genre"]}
{"sentence": "how come they never did a sequel to the shining", "entity_names": ["the shining"], "entity_types": ["Title"]}
{"sentence": "how did you like 4 months 3 weeks and 2 days", "entity_names": ["4 months 3 weeks and 2 days"], "entity_types": ["Title"]}
{"sentence": "how did you like men in black", "entity_names": ["men in black"], "entity_types": ["Title"]}
{"sentence": "how is the movie death to smoochy", "entity_names": ["death to smoochy"], "entity_types": ["Title"]}
{"sentence": "how likely is antony sher to have been in a movie as early as 1950 that was rated at least seven stars", "entity_names": ["antony sher", "1950", "seven stars"], "entity_types": ["Actor", "Year", "Viewers' Rating"]}
{"sentence": "how many r rated emotional movies have been made in the past five decades", "entity_names": ["r", "emotional", "past five decades"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "how many comedy movies has meryl streep has been in", "entity_names": ["comedy", "meryl streep"], "entity_types": ["Genre", "Actor"]}
{"sentence": "how many films featured stacey dash in 1950", "entity_names": ["stacey dash", "1950"], "entity_types": ["Actor", "Year"]}
{"sentence": "how many levels of dreams are in the movie inception", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "how would you rate who killed who", "entity_names": ["who killed who"], "entity_types": ["Title"]}
{"sentence": "i am interested in a pg 13 fantasy movie that was directed by dusty nelson and has an average rating of seven stars", "entity_names": ["pg 13", "fantasy", "dusty nelson", "seven stars"], "entity_types": ["MPAA Rating", "Genre", "Director", "Viewers' Rating"]}
{"sentence": "i am looking for a pg rated thriller that was made sometime in the last decade that was directed by daniel zirilli", "entity_names": ["pg", "thriller", "last decade", "daniel zirilli"], "entity_types": ["MPAA Rating", "Genre", "Year", "Director"]}
{"sentence": "i am looking for a post apocalypse plot unrated movie that was directed by george gallo from the last six years and has a seven stars ratings average", "entity_names": ["post apocalypse", "unrated", "george gallo", "last six years", "seven stars"], "entity_types": ["Plot", "MPAA Rating", "Director", "Year", "Viewers' Rating"]}
{"sentence": "i am looking for a sci fi from 1970 with excellent ratings", "entity_names": ["sci fi", "1970", "excellent ratings"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "i am looking for a thriller that was directed by alex pires sometime in the past five years", "entity_names": ["thriller", "alex pires", "past five years"], "entity_types": ["Genre", "Director", "Year"]}
{"sentence": "i am looking for a unrated disney movie about a teddy bear starring julie pinson with a four star ratings average", "entity_names": ["unrated", "disney", "teddy bear", "julie pinson", "four star"], "entity_types": ["MPAA Rating", "Genre", "Plot", "Actor", "Viewers' Rating"]}
{"sentence": "i am looking for an arnold schwarzenegger action movie from the past five years", "entity_names": ["arnold schwarzenegger", "action", "past five years"], "entity_types": ["Actor", "Genre", "Year"]}
{"sentence": "i am looking for an unrated science fiction movie from this year", "entity_names": ["unrated", "science fiction", "this year"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "i am looking for some highly recommended family films from 1950", "entity_names": ["highly recommended", "family", "1950"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "i am trying to find a police movie rated pg 13 that had a rating of six and starred jane campion", "entity_names": ["police", "pg 13", "six", "jane campion"], "entity_types": ["Genre", "MPAA Rating", "Viewers' Rating", "Director"]}
{"sentence": "i want a 1970 history movie starring patricia richards", "entity_names": ["1970", "history", "patricia richards"], "entity_types": ["Year", "Genre", "Actor"]}
{"sentence": "i want a pg 2010 documentary that was directed by hiromichi matano", "entity_names": ["pg", "2010", "documentary", "hiromichi matano"], "entity_types": ["MPAA Rating", "Year", "Genre", "Director"]}
{"sentence": "i want a documentary starring helena bonham carter from the past three years that was received well", "entity_names": ["documentary", "helena bonham carter", "past three years", "received well"], "entity_types": ["Genre", "Actor", "Year", "Viewers' Rating"]}
{"sentence": "i want a movie from 2000 that is r rated and stars danielle fishel", "entity_names": ["2000", "r", "danielle fishel"], "entity_types": ["Year", "MPAA Rating", "Actor"]}
{"sentence": "i want an eight stars rated mystery movie", "entity_names": ["eight stars", "mystery"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "i want to find a 2010 movie that was directed by alek keshishian with the plot being about striking it rich", "entity_names": ["2010", "alek keshishian", "striking it rich"], "entity_types": ["Year", "Director", "Plot"]}
{"sentence": "i want to find out about a sam neill movie made in the 1990 s that is rated pg and was rated nine out of 10", "entity_names": ["sam neill", "1990 s", "pg", "nine"], "entity_types": ["Actor", "Year", "MPAA Rating", "Viewers' Rating"]}
{"sentence": "i want to get the movie when nature calls", "entity_names": ["when nature calls"], "entity_types": ["Title"]}
{"sentence": "i want to see inception by francis ford coppola", "entity_names": ["inception", "francis ford coppola"], "entity_types": ["Title", "Director"]}
{"sentence": "i want to see a history movie made in 1970 is there any", "entity_names": ["history", "1970"], "entity_types": ["Genre", "Year"]}
{"sentence": "i want to watch an emotional pg 13 1990 boxer movie that has a rating of six", "entity_names": ["emotional", "pg 13", "1990", "boxer", "six"], "entity_types": ["Genre", "MPAA Rating", "Year", "Plot", "Viewers' Rating"]}
{"sentence": "i would a movie about gangs with actor barry pepper", "entity_names": ["gangs", "barry pepper"], "entity_types": ["Plot", "Actor"]}
{"sentence": "i would like a biography directed by ian mackenzie that had an average rating of six", "entity_names": ["biography", "ian mackenzie", "six"], "entity_types": ["Genre", "Director", "Viewers' Rating"]}
{"sentence": "i would like a list of political films that were rated a six", "entity_names": ["political", "six"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "i would like a movie from 1990 with a plot about despair", "entity_names": ["1990", "despair"], "entity_types": ["Year", "Plot"]}
{"sentence": "i would like a must see adventure about a mountain climber", "entity_names": ["must see", "adventure", "mountain climber"], "entity_types": ["Viewers' Rating", "Genre", "Plot"]}
{"sentence": "i would like a war movie directed by harry elfont from last year rated pg 13", "entity_names": ["war", "harry elfont", "last year", "pg 13"], "entity_types": ["Plot", "Director", "Year", "MPAA Rating"]}
{"sentence": "i would like an emotional type movie", "entity_names": ["emotional"], "entity_types": ["Genre"]}
{"sentence": "i would like the movie mongolian death worm", "entity_names": ["mongolian death worm"], "entity_types": ["Title"]}
{"sentence": "i would like the title of the pg 13 rated movie with dan haggerty that was rated well", "entity_names": ["pg 13", "dan haggerty", "rated well"], "entity_types": ["MPAA Rating", "Actor", "Viewers' Rating"]}
{"sentence": "i would like to find a movie with the title cobb", "entity_names": ["cobb"], "entity_types": ["Title"]}
{"sentence": "i would like to find a two thumbs up rated bill cosby movie from 1990", "entity_names": ["two thumbs up", "bill cosby", "1990"], "entity_types": ["Viewers' Rating", "Actor", "Year"]}
{"sentence": "i would like to get the reflecting skin movie", "entity_names": ["the reflecting skin"], "entity_types": ["Title"]}
{"sentence": "i would like to see the well rated action movie about security from the 1940 s that was rated g and directed by javier mariscal", "entity_names": ["well rated", "action", "security", "1940 s", "g", "javier mariscal"], "entity_types": ["Viewers' Rating", "Genre", "Plot", "Year", "MPAA Rating", "Director"]}
{"sentence": "i would love to see a 1960 s melodrama made by melville shavelson with a nine rating", "entity_names": ["1960 s", "melodrama", "melville shavelson", "nine"], "entity_types": ["Year", "Genre", "Director", "Viewers' Rating"]}
{"sentence": "id like a list of girl movies filmed in the year 1990 which star bobbie phillips", "entity_names": ["girl", "1990", "bobbie phillips"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "id like that really good emotional film starring carmen electra about an artist", "entity_names": ["really good", "emotional", "carmen electra", "artist"], "entity_types": ["Viewers' Rating", "Genre", "Actor", "Plot"]}
{"sentence": "id like that watchable and unrated film noir movie from 1950", "entity_names": ["watchable", "unrated", "film noir", "1950"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre", "Year"]}
{"sentence": "id like to find a 1960 s mockumentary film", "entity_names": ["1960 s", "mockumentary"], "entity_types": ["Year", "Genre"]}
{"sentence": "id like to find a pg 13 movie about promises that was directed by steven silver", "entity_names": ["pg 13", "promises", "steven silver"], "entity_types": ["MPAA Rating", "Plot", "Director"]}
{"sentence": "id like to find a cowboy movie with actor dermot mulroney featured in it", "entity_names": ["cowboy", "dermot mulroney"], "entity_types": ["Genre", "Actor"]}
{"sentence": "id love a suggestion for a 1950 war movie", "entity_names": ["1950", "war"], "entity_types": ["Year", "Genre"]}
{"sentence": "id really like to see the 1970 drama by alan j pakula", "entity_names": ["1970", "drama", "alan j pakula"], "entity_types": ["Year", "Genre", "Director"]}
{"sentence": "im looking for a 1970 s political film about finance", "entity_names": ["1970 s", "political", "finance"], "entity_types": ["Year", "Genre", "Plot"]}
{"sentence": "im looking for a kate jackson sci fi film from 2000 that got excellent ratings", "entity_names": ["kate jackson", "sci fi", "2000", "excellent ratings"], "entity_types": ["Actor", "Genre", "Year", "Viewers' Rating"]}
{"sentence": "im looking for a pg movie featuring animation and starring michael gross that rated an average of nine", "entity_names": ["pg", "animation", "michael gross", "nine"], "entity_types": ["MPAA Rating", "Genre", "Actor", "Viewers' Rating"]}
{"sentence": "im looking for a pg 13 documentary directed by shuhei morita", "entity_names": ["pg 13", "documentary", "shuhei morita"], "entity_types": ["MPAA Rating", "Genre", "Director"]}
{"sentence": "im looking for a pg 13 thriller starring damiano damiani", "entity_names": ["pg 13", "thriller", "damiano damiani"], "entity_types": ["MPAA Rating", "Genre", "Director"]}
{"sentence": "im looking for a western", "entity_names": ["western"], "entity_types": ["Genre"]}
{"sentence": "im looking for a biography film from 1980 with an irish italian plot directed by tim blake nelson", "entity_names": ["biography", "1980", "irish italian", "tim blake nelson"], "entity_types": ["Genre", "Year", "Plot", "Director"]}
{"sentence": "im looking for a comedy starring laurence olivier rated five stars and above", "entity_names": ["comedy", "laurence olivier", "five stars and above"], "entity_types": ["Genre", "Actor", "Viewers' Rating"]}
{"sentence": "im looking for a documentary about urban living that was critically acclaimed and rated pg 13", "entity_names": ["documentary", "urban living", "critically acclaimed", "pg 13"], "entity_types": ["Genre", "Plot", "Viewers' Rating", "MPAA Rating"]}
{"sentence": "im looking for a fantasy movie directed by samuel l jackson thats rated pg 13", "entity_names": ["fantasy", "samuel l jackson", "pg 13"], "entity_types": ["Genre", "Actor", "MPAA Rating"]}
{"sentence": "im looking for a gangster movie", "entity_names": ["gangster"], "entity_types": ["Genre"]}
{"sentence": "im looking for a mockumentary from the last ten years with the actor lea solunga in it", "entity_names": ["mockumentary", "last ten years", "lea solunga"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "im looking for a movie with actor paul reiser in it", "entity_names": ["paul reiser"], "entity_types": ["Actor"]}
{"sentence": "im looking for a scary movie from the past seven decades with a rating of four stars", "entity_names": ["scary", "past seven decades", "four stars"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "im looking for a science fiction movie", "entity_names": ["science fiction"], "entity_types": ["Genre"]}
{"sentence": "im looking for a short film starring jared leto that was liked by many rated r and made in the past four decades", "entity_names": ["short", "jared leto", "liked by many", "r", "past four decades"], "entity_types": ["Genre", "Actor", "Viewers' Rating", "MPAA Rating", "Year"]}
{"sentence": "im looking for a six star movie starring vanessa marcil from the 1980 s", "entity_names": ["six star", "vanessa marcil", "1980 s"], "entity_types": ["Viewers' Rating", "Actor", "Year"]}
{"sentence": "im looking for a very popular 1950 movie about a midlife crisis starring spencer tracey and was rated pg 13", "entity_names": ["very popular", "1950", "midlife crisis", "spencer tracey", "pg 13"], "entity_types": ["Viewers' Rating", "Year", "Plot", "Actor", "MPAA Rating"]}
{"sentence": "im looking for a war film from the past ten years rated nc 17 and directed by putipong saisikaew", "entity_names": ["war", "past ten years", "nc 17", "putipong saisikaew"], "entity_types": ["Genre", "Year", "MPAA Rating", "Director"]}
{"sentence": "im looking for an nc 17 biography about a mental patient released in the last eight years and directed by strathford hamilton", "entity_names": ["nc 17", "biography", "mental patient", "last eight years", "strathford hamilton"], "entity_types": ["MPAA Rating", "Genre", "Plot", "Year", "Director"]}
{"sentence": "im looking for an nc 17 rated film about adventure that was made in the past three years", "entity_names": ["nc 17", "adventure", "past three years"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "im looking for an ok movie rated pg 13 featuring william hurt and made in 1970", "entity_names": ["ok", "pg 13", "william hurt", "1970"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Actor", "Year"]}
{"sentence": "im looking for an r rated drama from the past four decades", "entity_names": ["r", "drama", "past four decades"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "im looking for an r rated movie starring michale pare that was made in the past eight decades in the thriller genre", "entity_names": ["r", "michale pare", "past eight decades", "thriller"], "entity_types": ["MPAA Rating", "Actor", "Year", "Genre"]}
{"sentence": "im looking for an emotional movie rated pg 13", "entity_names": ["emotional", "pg 13"], "entity_types": ["Genre", "MPAA Rating"]}
{"sentence": "im looking for an entertainment film about from the past seven decades about a genius starring alan autry", "entity_names": ["entertainment", "past seven decades", "genius", "alan autry"], "entity_types": ["Genre", "Year", "Plot", "Actor"]}
{"sentence": "im looking for an unrated musical from the past two decades", "entity_names": ["unrated", "musical", "past two decades"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "im looking for animation flicks from the last nine decades", "entity_names": ["animation", "last nine decades"], "entity_types": ["Genre", "Year"]}
{"sentence": "im looking for any drama films from the past four decades that scored at least nine out of ten", "entity_names": ["drama", "past four decades", "nine"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "im looking for that childrens movie starring sylvester stallone from the 1980 s", "entity_names": ["childrens", "sylvester stallone", "1980 s"], "entity_types": ["Genre", "Director", "Year"]}
{"sentence": "im looking for the 1950 g rated psychological movie about a missing prisoner directed by michael staininger", "entity_names": ["1950", "g", "psychological", "missing prisoner", "michael staininger"], "entity_types": ["Year", "MPAA Rating", "Genre", "Plot", "Director"]}
{"sentence": "im looking for the fantasy movie from the past eight years that starred kurt russell and was about fairies", "entity_names": ["fantasy", "past eight years", "kurt russell", "fairies"], "entity_types": ["Genre", "Year", "Actor", "Plot"]}
{"sentence": "im looking for the sports movie from 2000", "entity_names": ["sports", "2000"], "entity_types": ["Genre", "Year"]}
{"sentence": "im looking for the unrated 1940 s violent movie that was liked by many and starred tom selleck", "entity_names": ["unrated", "1940 s", "violent", "liked by many", "tom selleck"], "entity_types": ["MPAA Rating", "Year", "Genre", "Viewers' Rating", "Actor"]}
{"sentence": "im looking for the unrated sports film directed by lewis milestone from the past four years that was considered all right", "entity_names": ["unrated", "sports", "lewis milestone", "past four years", "all right"], "entity_types": ["MPAA Rating", "Genre", "Director", "Year", "Viewers' Rating"]}
{"sentence": "im searching for a 1950 s r rated watchable family movie about someone held captive", "entity_names": ["1950 s", "r", "watchable", "family", "held captive"], "entity_types": ["Year", "MPAA Rating", "Viewers' Rating", "Genre", "Plot"]}
{"sentence": "im trying to find a pg 13 drama from the 1950 s starring levar burton", "entity_names": ["pg 13", "drama", "1950 s", "levar burton"], "entity_types": ["MPAA Rating", "Genre", "Year", "Actor"]}
{"sentence": "im trying to find a very good funny movie that was filmed in 1940 do you have it", "entity_names": ["very good", "funny", "1940"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "im trying to remember the name of a pg 13 movie with brendan fletcher about a mountain climber from the past year", "entity_names": ["pg 13", "brendan fletcher", "mountain climber", "past year"], "entity_types": ["MPAA Rating", "Director", "Plot", "Year"]}
{"sentence": "im want to watch an all right western from 1980 where they search for gold", "entity_names": ["all right", "western", "1980", "search for gold"], "entity_types": ["Viewers' Rating", "Genre", "Year", "Plot"]}
{"sentence": "in 1950 what really good thriller was directed by jeremy okeefe", "entity_names": ["1950", "really good", "thriller", "jeremy okeefe"], "entity_types": ["Year", "Viewers' Rating", "Genre", "Director"]}
{"sentence": "in 1970 was deidre hall in an unrated short film", "entity_names": ["1970", "deidre hall", "unrated", "short"], "entity_types": ["Year", "Actor", "MPAA Rating", "Genre"]}
{"sentence": "in 1970 was kellie martin in a pg 13 science fiction movie that was highly recommended", "entity_names": ["1970", "kellie martin", "pg 13", "science fiction", "highly recommended"], "entity_types": ["Year", "Actor", "MPAA Rating", "Genre", "Viewers' Rating"]}
{"sentence": "in 1970 what unrated movie was maria bello in that received an average rating of seven", "entity_names": ["1970", "unrated", "maria bello", "seven"], "entity_types": ["Year", "MPAA Rating", "Actor", "Viewers' Rating"]}
{"sentence": "in 1980 what sport movie did john dunson direct", "entity_names": ["1980", "sport", "john dunson"], "entity_types": ["Year", "Genre", "Director"]}
{"sentence": "in 1990 what sport movie did cynthia daniel star in that was rated pg 13", "entity_names": ["1990", "sport", "cynthia daniel", "pg 13"], "entity_types": ["Year", "Genre", "Actor", "MPAA Rating"]}
{"sentence": "in 2010 s how many pg 13 horror movies are there", "entity_names": ["2010 s", "pg 13", "horror"], "entity_types": ["Year", "MPAA Rating", "Genre"]}
{"sentence": "in 2010 s what action movies was dina meyer in", "entity_names": ["2010 s", "action", "dina meyer"], "entity_types": ["Year", "Genre", "Actor"]}
{"sentence": "in the 1940 s what science fiction films did jesse v johnson direct", "entity_names": ["1940 s", "science fiction", "jesse v johnson"], "entity_types": ["Year", "Genre", "Director"]}
{"sentence": "in the 1950 s did mike barnett direct a pg 13 science fiction bounty hunter movie", "entity_names": ["1950 s", "mike barnett", "pg 13", "science fiction", "bounty hunter"], "entity_types": ["Year", "Director", "MPAA Rating", "Genre", "Plot"]}
{"sentence": "in the 1960 s did cory edwards direct an unrated sci fi that received an average rating of six", "entity_names": ["1960 s", "cory edwards", "unrated", "sci fi", "six"], "entity_types": ["Year", "Director", "MPAA Rating", "Genre", "Viewers' Rating"]}
{"sentence": "in the 1960 s what pg 13 police film about a fugitive received eight stars", "entity_names": ["1960 s", "pg 13", "police", "fugitive", "eight stars"], "entity_types": ["Year", "MPAA Rating", "Genre", "Plot", "Viewers' Rating"]}
{"sentence": "in the 1960 s what r rated movies was brooke shields in", "entity_names": ["1960 s", "r", "brooke shields"], "entity_types": ["Year", "MPAA Rating", "Actor"]}
{"sentence": "in the 1980 s what military soldier movies came out", "entity_names": ["1980 s", "military", "soldier"], "entity_types": ["Year", "Genre", "Plot"]}
{"sentence": "in the 1990 s was there an r rated adventure movie that had an average rating of eight", "entity_names": ["1990 s", "r", "adventure", "eight"], "entity_types": ["Year", "MPAA Rating", "Genre", "Viewers' Rating"]}
{"sentence": "in the 1990 s what nc 17 biography was talisa soto in that received an average rating of six", "entity_names": ["1990 s", "nc 17", "biography", "talisa soto", "six"], "entity_types": ["Year", "MPAA Rating", "Genre", "Actor", "Viewers' Rating"]}
{"sentence": "in the 2000 s what pg 13 was a well rated movie about drama and guilt", "entity_names": ["2000 s", "pg 13", "well rated", "drama", "guilt"], "entity_types": ["Year", "MPAA Rating", "Viewers' Rating", "Genre", "Plot"]}
{"sentence": "in the 2000 s what movies was sheree j wilson in", "entity_names": ["2000 s", "sheree j wilson"], "entity_types": ["Year", "Actor"]}
{"sentence": "in the 2010 s was albert finney in an r rated spaghetti western", "entity_names": ["2010 s", "albert finney", "r", "spaghetti western"], "entity_types": ["Year", "Actor", "MPAA Rating", "Genre"]}
{"sentence": "in the last decade what history films by adam wingard were really good", "entity_names": ["last decade", "history", "adam wingard", "really good"], "entity_types": ["Year", "Genre", "Director", "Viewers' Rating"]}
{"sentence": "in the last five decades was there a family film that aaron woolf directed", "entity_names": ["last five decades", "family", "aaron woolf"], "entity_types": ["Year", "Genre", "Director"]}
{"sentence": "in the last five years was benicio del torro in a pg 13 movie", "entity_names": ["last five years", "benicio del torro", "pg 13"], "entity_types": ["Year", "Actor", "MPAA Rating"]}
{"sentence": "in the last four years which movie did donnie walberg appear in that was pg 13", "entity_names": ["last four years", "donnie walberg", "pg 13"], "entity_types": ["Year", "Actor", "MPAA Rating"]}
{"sentence": "in the last seven years was there an r rated violence film that received an average rating of five", "entity_names": ["last seven years", "r", "violence", "five"], "entity_types": ["Year", "MPAA Rating", "Genre", "Viewers' Rating"]}
{"sentence": "in the last six decades what movies has adam storke been in", "entity_names": ["last six decades", "adam storke"], "entity_types": ["Year", "Actor"]}
{"sentence": "in the last six years has there been a film about artists with alexandre rockwell in it", "entity_names": ["last six years", "artists", "alexandre rockwell"], "entity_types": ["Year", "Plot", "Director"]}
{"sentence": "in the last ten years did gene kelly star in any very popular fantasy movies", "entity_names": ["last ten years", "gene kelly", "very popular", "fantasy"], "entity_types": ["Year", "Actor", "Viewers' Rating", "Genre"]}
{"sentence": "in the last three decades were there any emotional movies about boxing with an rating of seven starring kelly preston", "entity_names": ["last three decades", "emotional", "boxing", "seven", "kelly preston"], "entity_types": ["Year", "Genre", "Plot", "Viewers' Rating", "Actor"]}
{"sentence": "in the past decade what r rated comedy directed by milan konjevic was liked by many", "entity_names": ["past decade", "r", "comedy", "milan konjevic", "liked by many"], "entity_types": ["Year", "MPAA Rating", "Genre", "Director", "Viewers' Rating"]}
{"sentence": "in the past decade what funny unrated movies were directed by earnest harris", "entity_names": ["past decade", "funny", "unrated", "earnest harris"], "entity_types": ["Year", "Genre", "MPAA Rating", "Director"]}
{"sentence": "in the past decade what war movies were highly rated", "entity_names": ["past decade", "war", "highly rated"], "entity_types": ["Year", "Genre", "Viewers' Rating"]}
{"sentence": "in the past nine decades did ken bruce direct a g rated sci fi about galactic war that received an average rating of eight", "entity_names": ["past nine decades", "ken bruce", "g", "sci fi", "galactic war", "eight"], "entity_types": ["Year", "Director", "MPAA Rating", "Genre", "Plot", "Viewers' Rating"]}
{"sentence": "in the past nine decades what movies have come out for children", "entity_names": ["past nine decades", "children"], "entity_types": ["Year", "Genre"]}
{"sentence": "in the past seven decades was mimi rogers an many adventure films", "entity_names": ["past seven decades", "mimi rogers", "adventure"], "entity_types": ["Year", "Actor", "Genre"]}
{"sentence": "in the past seven years has there been a horror film starring brenden sexton jr with good ratings", "entity_names": ["past seven years", "horror", "brenden sexton jr", "good ratings"], "entity_types": ["Year", "Genre", "Actor", "Viewers' Rating"]}
{"sentence": "in the past year what highly recommended pg 13 adventure movie about chaos featured andy dick", "entity_names": ["past year", "highly recommended", "pg 13", "adventure", "chaos", "andy dick"], "entity_types": ["Year", "Viewers' Rating", "MPAA Rating", "Genre", "Plot", "Actor"]}
{"sentence": "in what year was the hire hostage released", "entity_names": ["the hire hostage"], "entity_types": ["Title"]}
{"sentence": "is billy wilder the director of inception", "entity_names": ["billy wilder", "inception"], "entity_types": ["Director", "Title"]}
{"sentence": "is david fincher working on any drama films currently", "entity_names": ["david fincher", "drama"], "entity_types": ["Director", "Genre"]}
{"sentence": "is david lean a highly recommended director", "entity_names": ["david lean"], "entity_types": ["Director"]}
{"sentence": "is don c in traitor", "entity_names": ["traitor"], "entity_types": ["Title"]}
{"sentence": "is edward norton in a science fiction movie that is just ok", "entity_names": ["edward norton", "science fiction", "ok"], "entity_types": ["Actor", "Genre", "Viewers' Rating"]}
{"sentence": "is gene hackman in any good action movies that are rated pg 13", "entity_names": ["gene hackman", "action", "pg 13"], "entity_types": ["Actor", "Genre", "MPAA Rating"]}
{"sentence": "is george lucas the director of the science fiction movie star wars", "entity_names": ["george lucas", "science fiction"], "entity_types": ["Director", "Genre"]}
{"sentence": "is geraint wyne davies a mediocre movie of war", "entity_names": ["geraint wyne davies", "mediocre", "war"], "entity_types": ["Actor", "Viewers' Rating", "Genre"]}
{"sentence": "is heath ledger in and rated g film noir movies that got two thumbs up", "entity_names": ["heath ledger", "g", "film noir", "two thumbs up"], "entity_types": ["Actor", "MPAA Rating", "Genre", "Viewers' Rating"]}
{"sentence": "is james cagney in the shining", "entity_names": ["james cagney", "the shining"], "entity_types": ["Actor", "Title"]}
{"sentence": "is james cameron currently working on any movies", "entity_names": ["james cameron"], "entity_types": ["Director"]}
{"sentence": "is john travolta starring in any childrens movie", "entity_names": ["john travolta", "childrens"], "entity_types": ["Actor", "Genre"]}
{"sentence": "is quentin tarantino planning on working on any animated films soon", "entity_names": ["quentin tarantino", "animated"], "entity_types": ["Director", "Genre"]}
{"sentence": "is robert de niro in any disaster movies", "entity_names": ["robert de niro", "disaster"], "entity_types": ["Actor", "Genre"]}
{"sentence": "is sweet rush eng subs a good movie to watch", "entity_names": ["sweet rush eng subs"], "entity_types": ["Title"]}
{"sentence": "is the only good indian stars any known actors in it", "entity_names": ["the only good indian"], "entity_types": ["Title"]}
{"sentence": "is the shining based on a true story", "entity_names": ["the shining"], "entity_types": ["Title"]}
{"sentence": "is tim doiron a good biography director", "entity_names": ["tim doiron", "biography"], "entity_types": ["Director", "Genre"]}
{"sentence": "is it true that kate mulgrew was born in 1940", "entity_names": ["kate mulgrew", "1940"], "entity_types": ["Actor", "Year"]}
{"sentence": "is that movie wall e out yet", "entity_names": ["wall e"], "entity_types": ["Title"]}
{"sentence": "is the movie lorenzos oil any good to see", "entity_names": ["lorenzos oil"], "entity_types": ["Title"]}
{"sentence": "is the movie open season animated", "entity_names": ["open season"], "entity_types": ["Title"]}
{"sentence": "is their a film called street revenge", "entity_names": ["street revenge"], "entity_types": ["Title"]}
{"sentence": "is there a 1940 hugh piper horror film with an imaginary friend", "entity_names": ["1940", "hugh piper", "horror", "imaginary friend"], "entity_types": ["Year", "Director", "Genre", "Plot"]}
{"sentence": "is there a 1970 s avant garde film", "entity_names": ["1970 s", "avant garde"], "entity_types": ["Year", "Genre"]}
{"sentence": "is there a bill paxton movie in the last seven decades", "entity_names": ["bill paxton", "last seven decades"], "entity_types": ["Actor", "Year"]}
{"sentence": "is there a callie khouri highly liked 2010 s family movie", "entity_names": ["callie khouri", "highly liked", "2010 s", "family"], "entity_types": ["Director", "Viewers' Rating", "Year", "Genre"]}
{"sentence": "is there a dean martin action film that was liked a lot this past year", "entity_names": ["dean martin", "action", "was liked a lot", "past year"], "entity_types": ["Actor", "Genre", "Viewers' Rating", "Year"]}
{"sentence": "is there a john travolta comedy from the past two years", "entity_names": ["john travolta", "comedy", "past two years"], "entity_types": ["Actor", "Genre", "Year"]}
{"sentence": "is there a lea thompson movie about god that took place in the 1950 s with an average rating of six", "entity_names": ["lea thompson", "god", "1950 s", "six"], "entity_types": ["Actor", "Plot", "Year", "Viewers' Rating"]}
{"sentence": "is there a movie with director charles stewart in the past seven years on military bravery that is good", "entity_names": ["charles stewart", "past seven years", "military", "bravery", "good"], "entity_types": ["Director", "Year", "Genre", "Plot", "Viewers' Rating"]}
{"sentence": "is there a pg documentary in the past nine decades with tim allen", "entity_names": ["pg", "documentary", "past nine decades", "tim allen"], "entity_types": ["MPAA Rating", "Genre", "Year", "Actor"]}
{"sentence": "is there a pg movie starring carrot top that was rated well", "entity_names": ["pg", "carrot top", "rated well"], "entity_types": ["MPAA Rating", "Actor", "Viewers' Rating"]}
{"sentence": "is there a pg rated crime movie which director was john hughes", "entity_names": ["pg", "crime", "john hughes"], "entity_types": ["MPAA Rating", "Genre", "Director"]}
{"sentence": "is there a pg 13 documentary about illegal operation that is watchable", "entity_names": ["pg 13", "documentary", "illegal operation", "watchable"], "entity_types": ["MPAA Rating", "Genre", "Plot", "Viewers' Rating"]}
{"sentence": "is there a pg 13 film out there from the 2000 s that focuses on an evil character", "entity_names": ["pg 13", "2000 s", "evil"], "entity_types": ["MPAA Rating", "Year", "Plot"]}
{"sentence": "is there a pg 13 mockumentary that is liked by many and starring nicolas cage from the last nine years", "entity_names": ["pg 13", "mockumentary", "liked by many", "nicolas cage", "last nine years"], "entity_types": ["MPAA Rating", "Genre", "Viewers' Rating", "Actor", "Year"]}
{"sentence": "is there a paul hunter movie that was very popular and released in 1970 with a pg rating that was about global power", "entity_names": ["paul hunter", "very popular", "1970", "pg", "global power"], "entity_types": ["Director", "Viewers' Rating", "Year", "MPAA Rating", "Plot"]}
{"sentence": "is there a r rated fantasy film which director was roman polanski", "entity_names": ["r", "fantasy", "roman polanski"], "entity_types": ["MPAA Rating", "Genre", "Director"]}
{"sentence": "is there a wall e movie starring robert redford", "entity_names": ["wall e", "robert redford"], "entity_types": ["Title", "Actor"]}
{"sentence": "is there a animation pg 13 movie with trisha romance", "entity_names": ["animation", "pg 13", "trisha romance"], "entity_types": ["Genre", "MPAA Rating", "Actor"]}
{"sentence": "is there a biographical movie by the director andrei tarkovsky", "entity_names": ["biographical", "andrei tarkovsky"], "entity_types": ["Genre", "Director"]}
{"sentence": "is there a comedy with a rating of r", "entity_names": ["comedy", "r"], "entity_types": ["Genre", "MPAA Rating"]}
{"sentence": "is there a crime film starring robin williams", "entity_names": ["crime", "robin williams"], "entity_types": ["Genre", "Actor"]}
{"sentence": "is there a crime movie withing the last eight years that had an average eight star rating", "entity_names": ["crime", "last eight years", "eight star"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "is there a drama good rated r 1940 s movie", "entity_names": ["drama", "good", "r", "1940 s"], "entity_types": ["Genre", "Viewers' Rating", "MPAA Rating", "Year"]}
{"sentence": "is there a evil horror movie with at least four stars", "entity_names": ["evil", "horror", "four"], "entity_types": ["Plot", "Genre", "Viewers' Rating"]}
{"sentence": "is there a fantasy movie directed by irving lerner in the past six decades that has an average rating of six stars", "entity_names": ["fantasy", "irving lerner", "past six decades", "six stars"], "entity_types": ["Genre", "Director", "Year", "Viewers' Rating"]}
{"sentence": "is there a film with the title f", "entity_names": ["f"], "entity_types": ["Title"]}
{"sentence": "is there a film noir genre movie which director was mel brooks", "entity_names": ["film noir", "mel brooks"], "entity_types": ["Genre", "Director"]}
{"sentence": "is there a five star jenna elfman romantic comedy type movie about an unhappy marriage set in the 1960 s", "entity_names": ["five", "jenna elfman", "romantic comedy", "unhappy marriage", "1960 s"], "entity_types": ["Viewers' Rating", "Actor", "Genre", "Plot", "Year"]}
{"sentence": "is there a funny movie that came out recently about a misadventure", "entity_names": ["funny", "misadventure"], "entity_types": ["Genre", "Plot"]}
{"sentence": "is there a gold strike starring danny glover in the last decade", "entity_names": ["gold strike", "danny glover", "last decade"], "entity_types": ["Plot", "Actor", "Year"]}
{"sentence": "is there a good r rated comedy about a comedy troupe", "entity_names": ["r", "comedy", "comedy troupe"], "entity_types": ["MPAA Rating", "Genre", "Plot"]}
{"sentence": "is there a good robin williams movie that is an adventure", "entity_names": ["robin williams", "adventure"], "entity_types": ["Actor", "Genre"]}
{"sentence": "is there a good action movie about redemption", "entity_names": ["action", "redemption"], "entity_types": ["Genre", "Plot"]}
{"sentence": "is there a good action movie based on a mission that came out in 2011", "entity_names": ["action", "mission"], "entity_types": ["Genre", "Plot"]}
{"sentence": "is there a good comedy that is rated pg 13 about sexual desire", "entity_names": ["comedy", "pg 13", "sexual desire"], "entity_types": ["Genre", "MPAA Rating", "Plot"]}
{"sentence": "is there a good fantasy movie", "entity_names": ["fantasy"], "entity_types": ["Genre"]}
{"sentence": "is there a good film noir movie that can be recommended", "entity_names": ["film noir"], "entity_types": ["Genre"]}
{"sentence": "is there a good horror movie about a curse", "entity_names": ["horror", "curse"], "entity_types": ["Genre", "Plot"]}
{"sentence": "is there a good movie in the film noir genre", "entity_names": ["film noir"], "entity_types": ["Genre"]}
{"sentence": "is there a good psychological drama bout a struggle", "entity_names": ["psychological drama", "struggle"], "entity_types": ["Genre", "Plot"]}
{"sentence": "is there a good rated r military movie about camp", "entity_names": ["r", "military", "camp"], "entity_types": ["MPAA Rating", "Genre", "Plot"]}
{"sentence": "is there a good romance movie i can take my girlfriend to", "entity_names": ["romance"], "entity_types": ["Genre"]}
{"sentence": "is there a good romantic comedy about jealousy", "entity_names": ["romantic comedy", "jealousy"], "entity_types": ["Genre", "Plot"]}
{"sentence": "is there a good scary movie that is rated pg 13 about extrasensory perception", "entity_names": ["scary", "pg 13", "extrasensory perception"], "entity_types": ["Genre", "MPAA Rating", "Plot"]}
{"sentence": "is there a good sci fi movie that could be recommended", "entity_names": ["sci fi"], "entity_types": ["Genre"]}
{"sentence": "is there a good thriller movie out", "entity_names": ["thriller"], "entity_types": ["Genre"]}
{"sentence": "is there a good thriller that is rated pg 13 that came out in the 2010 s", "entity_names": ["thriller", "pg 13", "2010 s"], "entity_types": ["Genre", "MPAA Rating", "Year"]}
{"sentence": "is there a highly rated drama film that stars julie andrews", "entity_names": ["highly rated", "drama", "julie andrews"], "entity_types": ["Viewers' Rating", "Genre", "Actor"]}
{"sentence": "is there a highly recommended teen movie by roman polanski thats rated g", "entity_names": ["highly recommended", "teen", "roman polanski", "g"], "entity_types": ["Viewers' Rating", "Genre", "Director", "MPAA Rating"]}
{"sentence": "is there a highly recommended war movie from 1990 that johan renck directed", "entity_names": ["highly recommended", "war", "1990", "johan renck"], "entity_types": ["Viewers' Rating", "Genre", "Year", "Director"]}
{"sentence": "is there a historical movie starring morgan freeman", "entity_names": ["historical", "morgan freeman"], "entity_types": ["Genre", "Actor"]}
{"sentence": "is there a historical movie which director is hayao miyazaki", "entity_names": ["historical", "hayao miyazaki"], "entity_types": ["Genre", "Director"]}
{"sentence": "is there a historical movie which director was robert zemeckis", "entity_names": ["historical", "robert zemeckis"], "entity_types": ["Genre", "Director"]}
{"sentence": "is there a horror film starring charles chaplin", "entity_names": ["horror", "charles chaplin"], "entity_types": ["Genre", "Director"]}
{"sentence": "is there a horror movie that centers on zombie child", "entity_names": ["horror", "zombie child"], "entity_types": ["Genre", "Plot"]}
{"sentence": "is there a horror sequel titled zombi 2", "entity_names": ["zombi 2"], "entity_types": ["Title"]}
{"sentence": "is there a movie the shining", "entity_names": ["the shining"], "entity_types": ["Title"]}
{"sentence": "is there a movie called 2001 maniacs", "entity_names": ["2001 maniacs"], "entity_types": ["Title"]}
{"sentence": "is there a movie called black rain", "entity_names": ["black rain"], "entity_types": ["Title"]}
{"sentence": "is there a movie called manfast", "entity_names": ["manfast"], "entity_types": ["Title"]}
{"sentence": "is there a movie called one man one cow one planet", "entity_names": ["one man one cow one planet"], "entity_types": ["Title"]}
{"sentence": "is there a movie called seconds apart", "entity_names": ["seconds apart"], "entity_types": ["Title"]}
{"sentence": "is there a movie called unanswered prayers", "entity_names": ["unanswered prayers"], "entity_types": ["Title"]}
{"sentence": "is there a movie called wall e that was directed by roman polanski", "entity_names": ["wall e", "roman polanski"], "entity_types": ["Title", "Director"]}
{"sentence": "is there a movie called the night on earth", "entity_names": ["night on earth"], "entity_types": ["Title"]}
{"sentence": "is there a movie for children starring liam neeson", "entity_names": ["children", "liam neeson"], "entity_types": ["Genre", "Actor"]}
{"sentence": "is there a movie from 2000 that starred chris rock", "entity_names": ["2000", "chris rock"], "entity_types": ["Year", "Actor"]}
{"sentence": "is there a movie from the 1990 s about romance and rated r starring julie andrews and has excellent ratings", "entity_names": ["1990 s", "romance", "r", "julie andrews", "excellent ratings"], "entity_types": ["Year", "Genre", "MPAA Rating", "Actor", "Viewers' Rating"]}
{"sentence": "is there a movie set in the 1940 s that has a plot of a runaway that might be really popular", "entity_names": ["1940 s", "runaway", "really popular"], "entity_types": ["Year", "Plot", "Viewers' Rating"]}
{"sentence": "is there a movie starring cary grant witch director was hayao miyazaki", "entity_names": ["cary grant", "hayao miyazaki"], "entity_types": ["Actor", "Director"]}
{"sentence": "is there a movie starring james cagney", "entity_names": ["james cagney"], "entity_types": ["Actor"]}
{"sentence": "is there a movie titled buffalo dreams", "entity_names": ["buffalo dreams"], "entity_types": ["Title"]}
{"sentence": "is there a movie titled confidence", "entity_names": ["confidence"], "entity_types": ["Title"]}
{"sentence": "is there a movie titled supergirl", "entity_names": ["supergirl"], "entity_types": ["Title"]}
{"sentence": "is there a movie titled wall e", "entity_names": ["wall e"], "entity_types": ["Title"]}
{"sentence": "is there a movie which director was mel brooks", "entity_names": ["mel brooks"], "entity_types": ["Director"]}
{"sentence": "is there a movie with six stars rated r with fantasy", "entity_names": ["six stars", "r", "fantasy"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre"]}
{"sentence": "is there a musical movie which director is francis ford coppola", "entity_names": ["musical", "francis ford coppola"], "entity_types": ["Genre", "Director"]}
{"sentence": "is there a rated g western movie with director faith granger", "entity_names": ["g", "western", "faith granger"], "entity_types": ["MPAA Rating", "Genre", "Director"]}
{"sentence": "is there a rated r biography within the past five years that received well ratings", "entity_names": ["r", "biography", "past five years", "received well"], "entity_types": ["MPAA Rating", "Genre", "Year", "Viewers' Rating"]}
{"sentence": "is there a really good love movie about falling in love in the 1940 era", "entity_names": ["really good", "love", "falling in love", "1940"], "entity_types": ["Viewers' Rating", "Genre", "Plot", "Year"]}
{"sentence": "is there a really popular confession r rated starring howard stern", "entity_names": ["really popular", "confession", "r", "howard stern"], "entity_types": ["Viewers' Rating", "Plot", "MPAA Rating", "Actor"]}
{"sentence": "is there a romance very popular film with robert pratten in the past eight decades", "entity_names": ["romance", "very popular", "robert pratten", "past eight decades"], "entity_types": ["Genre", "Viewers' Rating", "Director", "Year"]}
{"sentence": "is there a sci fi past eight decades ago that was directed by tim hill", "entity_names": ["sci fi", "past eight decades", "tim hill"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "is there a seven rating rated g musical starring tea leoni", "entity_names": ["seven", "g", "musical", "tea leoni"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre", "Actor"]}
{"sentence": "is there a short dean stockwell movie rated nc 17 from the past eight decades", "entity_names": ["short", "dean stockwell", "nc 17", "past eight decades"], "entity_types": ["Genre", "Actor", "MPAA Rating", "Year"]}
{"sentence": "is there a short movie", "entity_names": ["short"], "entity_types": ["Genre"]}
{"sentence": "is there a spaghetti western about a sheriff with an average rating", "entity_names": ["spaghetti western", "sheriff", "average"], "entity_types": ["Genre", "Plot", "Viewers' Rating"]}
{"sentence": "is there a sport movie with a rating of pg 13 starring katherine hepburn", "entity_names": ["sport", "pg 13", "katherine hepburn"], "entity_types": ["Genre", "MPAA Rating", "Actor"]}
{"sentence": "is there a very popular pg rated romantic comedy about adoption set in the 1940 s starring tim robbins", "entity_names": ["very popular", "pg", "romantic comedy", "adoption", "1940 s", "tim robbins"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre", "Plot", "Year", "Actor"]}
{"sentence": "is there a war movie which director was frank capra", "entity_names": ["war", "frank capra"], "entity_types": ["Genre", "Director"]}
{"sentence": "is there a well rated western movie starring jack lemmon", "entity_names": ["well rated", "western", "jack lemmon"], "entity_types": ["Viewers' Rating", "Genre", "Actor"]}
{"sentence": "is there a western movie rated nc 17 and directed by grant harvey", "entity_names": ["western", "nc 17", "grant harvey"], "entity_types": ["Genre", "MPAA Rating", "Director"]}
{"sentence": "is there a western movie starring charlton heston", "entity_names": ["western", "charlton heston"], "entity_types": ["Genre", "Actor"]}
{"sentence": "is there an r rated childrens movie about sibling rivalry in the 1980 s that was rated a seven on average", "entity_names": ["r", "childrens", "sibling rivalry", "1980 s", "seven"], "entity_types": ["MPAA Rating", "Genre", "Plot", "Year", "Viewers' Rating"]}
{"sentence": "is there an r rated fantasy directed by robert taylor that is liked by many", "entity_names": ["r", "fantasy", "robert taylor", "liked by many"], "entity_types": ["MPAA Rating", "Genre", "Director", "Viewers' Rating"]}
{"sentence": "is there an action movie starring sean connery", "entity_names": ["action", "sean connery"], "entity_types": ["Genre", "Actor"]}
{"sentence": "is there an action movie that centers on vigilante", "entity_names": ["action", "vigilante"], "entity_types": ["Genre", "Plot"]}
{"sentence": "is there an action movie within the last six decades that has a rating of seven stars", "entity_names": ["action", "last six decades", "seven stars"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "is there an all right 1960 s mockumentary from steven dupler with an r rating", "entity_names": ["all right", "1960 s", "mockumentary", "steven dupler", "r"], "entity_types": ["Viewers' Rating", "Year", "Genre", "Director", "MPAA Rating"]}
{"sentence": "is there an animated movie that is directed by christopher nolan", "entity_names": ["animated", "christopher nolan"], "entity_types": ["Genre", "Director"]}
{"sentence": "is there an avant garde movie starring angelina jolie", "entity_names": ["avant garde", "angelina jolie"], "entity_types": ["Genre", "Actor"]}
{"sentence": "is there an inception movie starring heath ledger", "entity_names": ["inception", "heath ledger"], "entity_types": ["Title", "Actor"]}
{"sentence": "is there an underground documentary from 1980 rated pg 13", "entity_names": ["underground", "documentary", "1980", "pg 13"], "entity_types": ["Plot", "Genre", "Year", "MPAA Rating"]}
{"sentence": "is there an unrated adventure film released this year that was directed by michael campus and was considered ok", "entity_names": ["unrated", "adventure", "this year", "michael campus", "ok"], "entity_types": ["MPAA Rating", "Genre", "Year", "Director", "Viewers' Rating"]}
{"sentence": "is there any r rated history movie filmed in the year 1950 which received a rating average of seven", "entity_names": ["r", "history", "1950", "seven"], "entity_types": ["MPAA Rating", "Genre", "Year", "Viewers' Rating"]}
{"sentence": "is there any r rated portrait film starring billy bob thornton", "entity_names": ["r", "portrait", "billy bob thornton"], "entity_types": ["MPAA Rating", "Genre", "Actor"]}
{"sentence": "is there any documentary films about religion that received two thumbs up", "entity_names": ["documentary", "religion", "two thumbs up"], "entity_types": ["Genre", "Plot", "Viewers' Rating"]}
{"sentence": "is there any drama movie witch director is federico fellini", "entity_names": ["drama", "federico fellini"], "entity_types": ["Genre", "Director"]}
{"sentence": "is there any good adventure films that were made this year", "entity_names": ["adventure"], "entity_types": ["Genre"]}
{"sentence": "is there any good mockumentary films coming out this year", "entity_names": ["mockumentary"], "entity_types": ["Genre"]}
{"sentence": "is there any good rated r crime movies coming out next month", "entity_names": ["r", "crime"], "entity_types": ["MPAA Rating", "Genre"]}
{"sentence": "is there any good teen movies that are supposed to be coming out this year", "entity_names": ["teen"], "entity_types": ["Genre"]}
{"sentence": "is there any historical movies about world war 2 coming out this year", "entity_names": ["historical"], "entity_types": ["Genre"]}
{"sentence": "is there any movie for children witch director was terrence malick", "entity_names": ["children", "terrence malick"], "entity_types": ["Genre", "Director"]}
{"sentence": "is there any rated g military movies", "entity_names": ["g", "military"], "entity_types": ["MPAA Rating", "Genre"]}
{"sentence": "is there any really good fantasy movies that came out in 2011", "entity_names": ["really good", "fantasy"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "is there any scary sequels coming out soon like another saw movie", "entity_names": ["scary"], "entity_types": ["Genre"]}
{"sentence": "is there any western films about mining", "entity_names": ["western", "mining"], "entity_types": ["Genre", "Plot"]}
{"sentence": "is there any western films that are rated g", "entity_names": ["western", "g"], "entity_types": ["Genre", "MPAA Rating"]}
{"sentence": "is there biographical movie witch director is fritz lang", "entity_names": ["biographical", "fritz lang"], "entity_types": ["Genre", "Director"]}
{"sentence": "is there five stars and above rating spaghetti western movie made in 1960 s", "entity_names": ["five stars and above", "spaghetti western", "1960 s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "james spader acted in which middle earth fantasy movie in 1970", "entity_names": ["james spader", "middle earth", "fantasy", "1970"], "entity_types": ["Actor", "Plot", "Genre", "Year"]}
{"sentence": "jennifer lien starred in this action film of the the last six years that received a really good rating", "entity_names": ["jennifer lien", "action", "last six years", "really good"], "entity_types": ["Actor", "Genre", "Year", "Viewers' Rating"]}
{"sentence": "june lockheart was featured in this unrated animation film of the last decade", "entity_names": ["june lockheart", "unrated", "animation", "last decade"], "entity_types": ["Actor", "MPAA Rating", "Genre", "Year"]}
{"sentence": "ken shapiro directed a pg 13 fantasy movie about an evil wizard have you seen this in the last nine years", "entity_names": ["ken shapiro", "pg 13", "fantasy", "evil wizard", "last nine years"], "entity_types": ["Director", "MPAA Rating", "Genre", "Plot", "Year"]}
{"sentence": "list home alone 4", "entity_names": ["home alone 4"], "entity_types": ["Title"]}
{"sentence": "list lucille ball movies rated a five", "entity_names": ["lucille ball", "five"], "entity_types": ["Actor", "Viewers' Rating"]}
{"sentence": "list pg 13 rated independent movie which director was steven soderbergh and people said that i must see", "entity_names": ["pg 13", "independent", "steven soderbergh", "must see"], "entity_types": ["MPAA Rating", "Genre", "Director", "Viewers' Rating"]}
{"sentence": "list pg 13 history movies from last year", "entity_names": ["pg 13", "history", "last year"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "list pg 13 movies about love and depression with john cusak in the past ten years", "entity_names": ["pg 13", "love", "depression", "john cusak", "past ten years"], "entity_types": ["MPAA Rating", "Genre", "Plot", "Actor", "Year"]}
{"sentence": "list r rated movies starring clint eastwood in the last ten decades with a fugitive plot style", "entity_names": ["r", "clint eastwood", "last ten decades", "fugitive"], "entity_types": ["MPAA Rating", "Actor", "Year", "Plot"]}
{"sentence": "list samuel l jackson movies", "entity_names": ["samuel l jackson"], "entity_types": ["Actor"]}
{"sentence": "list the shining film starring clark gable", "entity_names": ["the shining", "clark gable"], "entity_types": ["Title", "Actor"]}
{"sentence": "list a 1950 s spaghetti western that involves indian and is rated pg 13", "entity_names": ["1950 s", "spaghetti western", "indian", "pg"], "entity_types": ["Year", "Genre", "Plot", "MPAA Rating"]}
{"sentence": "list a 1980 biography that was liked by many", "entity_names": ["1980", "biography", "liked by many"], "entity_types": ["Year", "Genre", "Viewers' Rating"]}
{"sentence": "list a 1990 rufus sewell adventure movie that was rated well", "entity_names": ["1990", "rufus sewell", "adventure", "rated well"], "entity_types": ["Year", "Actor", "Genre", "Viewers' Rating"]}
{"sentence": "list a 2000 s teen movie directed by federico fellini", "entity_names": ["2000 s", "teen", "federico fellini"], "entity_types": ["Year", "Genre", "Director"]}
{"sentence": "list a christopher lloyd animation movie from the past nine years", "entity_names": ["christopher lloyd", "animation", "past nine years"], "entity_types": ["Actor", "Genre", "Year"]}
{"sentence": "list a david lean musical", "entity_names": ["david lean", "musical"], "entity_types": ["Director", "Genre"]}
{"sentence": "list a humphrey bogart thriller", "entity_names": ["humphrey bogart", "thriller"], "entity_types": ["Actor", "Genre"]}
{"sentence": "list a nc 17 rated military films directed by zelda barron in 1980 that was ok", "entity_names": ["nc 17", "military", "zelda barron", "1980", "ok"], "entity_types": ["MPAA Rating", "Genre", "Director", "Year", "Viewers' Rating"]}
{"sentence": "list a nc 17 rated movie with actress kathie lee gifford from the year 1940", "entity_names": ["nc 17", "kathie lee gifford", "1940"], "entity_types": ["MPAA Rating", "Actor", "Year"]}
{"sentence": "list a pg biography from 1970 s", "entity_names": ["pg", "biography", "1970 s"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "list a pg rated fantasy movie", "entity_names": ["pg", "fantasy"], "entity_types": ["MPAA Rating", "Genre"]}
{"sentence": "list a pg rated funny movie that centers on road trip", "entity_names": ["pg", "funny", "road trip"], "entity_types": ["MPAA Rating", "Genre", "Plot"]}
{"sentence": "list a pg 13 rated teen movie made in 1950 s starring mel gibson", "entity_names": ["pg 13", "teen", "1950 s", "mel gibson"], "entity_types": ["MPAA Rating", "Genre", "Year", "Actor"]}
{"sentence": "list a pg 13 biography film that centers on mafia directed by david green for the past five years", "entity_names": ["pg 13", "biography", "mafia", "david green", "past five years"], "entity_types": ["MPAA Rating", "Genre", "Plot", "Director", "Year"]}
{"sentence": "list a pg 13 film from the past three decades starring chase masterson", "entity_names": ["pg 13", "past three decades", "chase masterson"], "entity_types": ["MPAA Rating", "Year", "Actor"]}
{"sentence": "list a tom hanks thriller", "entity_names": ["tom hanks", "thriller"], "entity_types": ["Actor", "Genre"]}
{"sentence": "list a wall e movie which director were the coen brothers", "entity_names": ["wall e", "the coen brothers"], "entity_types": ["Title", "Director"]}
{"sentence": "list a wall e movie", "entity_names": ["wall e"], "entity_types": ["Title"]}
{"sentence": "list a biographical movie that has excellent ratings starring john travolta", "entity_names": ["biographical", "has excellent ratings", "john travolta"], "entity_types": ["Genre", "Viewers' Rating", "Actor"]}
{"sentence": "list a chick film in the past ten decades", "entity_names": ["chick", "past ten decades"], "entity_types": ["Genre", "Year"]}
{"sentence": "list a comedy from 1950 with a ratings average of five stars starring andre braugher", "entity_names": ["comedy", "1950", "five stars", "andre braugher"], "entity_types": ["Genre", "Year", "Viewers' Rating", "Actor"]}
{"sentence": "list a comedy movie", "entity_names": ["comedy"], "entity_types": ["Genre"]}
{"sentence": "list a crime film directed by jonathan demme", "entity_names": ["crime", "jonathan demme"], "entity_types": ["Genre", "Director"]}
{"sentence": "list a critically acclaimed 1990 nc 17 sci fi film", "entity_names": ["critically acclaimed", "1990", "nc 17", "sci fi"], "entity_types": ["Viewers' Rating", "Year", "MPAA Rating", "Genre"]}
{"sentence": "list a disaster movie that is highly recommended", "entity_names": ["disaster", "highly recommended"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "list a drama film within the last four decades with actor curly howard", "entity_names": ["drama", "last four decades", "curly howard"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "list a eight stars nazi movie in the year 2000 s directed by sean doyle", "entity_names": ["eight stars", "nazi", "2000 s", "sean doyle"], "entity_types": ["Viewers' Rating", "Plot", "Year", "Director"]}
{"sentence": "list a film like olly olly oxen free", "entity_names": ["olly olly oxen free"], "entity_types": ["Title"]}
{"sentence": "list a film noir which director was ingmar bergman", "entity_names": ["film noir", "ingmar bergman"], "entity_types": ["Genre", "Director"]}
{"sentence": "list a film starring della reese", "entity_names": ["della reese"], "entity_types": ["Actor"]}
{"sentence": "list a funny movie which centers on road trip", "entity_names": ["funny", "road trip"], "entity_types": ["Genre", "Plot"]}
{"sentence": "list a gangster film with a nc 17 rating directed by sean ellis in the year 2010", "entity_names": ["gangster", "nc 17", "sean ellis", "2010"], "entity_types": ["Genre", "MPAA Rating", "Director", "Year"]}
{"sentence": "list a good biography directed by federico rivia", "entity_names": ["good", "biography", "federico rivia"], "entity_types": ["Viewers' Rating", "Genre", "Director"]}
{"sentence": "list a good thriller movie made in 1960 s starring cate blanchett", "entity_names": ["good", "thriller", "1960 s", "cate blanchett"], "entity_types": ["Viewers' Rating", "Genre", "Year", "Actor"]}
{"sentence": "list a highly liked horror film that is rated r", "entity_names": ["highly liked", "horror", "r"], "entity_types": ["Viewers' Rating", "Genre", "MPAA Rating"]}
{"sentence": "list a horror film about corporal punishment from the past six decades directed by darren stein", "entity_names": ["horror", "corporal punishment", "past six decades", "darren stein"], "entity_types": ["Genre", "Plot", "Year", "Director"]}
{"sentence": "list a kid film about growing up", "entity_names": ["kid", "growing up"], "entity_types": ["Genre", "Plot"]}
{"sentence": "list a move of the gags genre that was directed by j s cardone and uses satire in its plot", "entity_names": ["gags", "j s cardone", "satire"], "entity_types": ["Genre", "Director", "Plot"]}
{"sentence": "list a movie wall e", "entity_names": ["wall e"], "entity_types": ["Title"]}
{"sentence": "list a movie about family betrayal directed by arthur marks", "entity_names": ["family betrayal", "arthur marks"], "entity_types": ["Plot", "Director"]}
{"sentence": "list a movie like moontrap", "entity_names": ["moontrap"], "entity_types": ["Title"]}
{"sentence": "list a movie starring edward norton", "entity_names": ["edward norton"], "entity_types": ["Actor"]}
{"sentence": "list a movie starring tom hanks", "entity_names": ["tom hanks"], "entity_types": ["Actor"]}
{"sentence": "list a musical directed by susan muska", "entity_names": ["musical", "susan muska"], "entity_types": ["Genre", "Director"]}
{"sentence": "list a must see rated g movie about an alien hunter with actor ginger rogers", "entity_names": ["must see", "g", "alien hunter", "ginger rogers"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Plot", "Actor"]}
{"sentence": "list a must see thriller made last year but is pg 13", "entity_names": ["must see", "thriller", "last year", "pg 13"], "entity_types": ["Viewers' Rating", "Genre", "Year", "MPAA Rating"]}
{"sentence": "list a police film", "entity_names": ["police"], "entity_types": ["Genre"]}
{"sentence": "list a psychological drama movie that centers on lost experiences", "entity_names": ["psychological drama", "lost experiences"], "entity_types": ["Genre", "Plot"]}
{"sentence": "list a rated nc 17 war movie this year", "entity_names": ["nc 17", "war", "this year"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "list a rated r watchable christopher walkin film noir", "entity_names": ["r", "watchable", "christopher walkin", "film noir"], "entity_types": ["MPAA Rating", "Viewers' Rating", "Actor", "Genre"]}
{"sentence": "list a really good action film thats rated pg 13", "entity_names": ["really good", "action", "pg 13"], "entity_types": ["Viewers' Rating", "Genre", "MPAA Rating"]}
{"sentence": "list a really good crime pg 13 rated movie starring morgan freeman", "entity_names": ["really good", "crime", "pg 13", "morgan freeman"], "entity_types": ["Viewers' Rating", "Genre", "MPAA Rating", "Actor"]}
{"sentence": "list a romantic drama movie", "entity_names": ["romantic drama"], "entity_types": ["Genre"]}
{"sentence": "list a sci fi movie", "entity_names": ["sci fi"], "entity_types": ["Genre"]}
{"sentence": "list a science fiction pg 13 film with william forsythe", "entity_names": ["science fiction", "pg 13", "william forsythe"], "entity_types": ["Genre", "MPAA Rating", "Actor"]}
{"sentence": "list a short film last year with sissy spacek", "entity_names": ["short", "last year", "sissy spacek"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "list a six stars and above rating romantic comedy starring vivien leigh", "entity_names": ["six stars and above", "romantic comedy", "vivien leigh"], "entity_types": ["Viewers' Rating", "Genre", "Actor"]}
{"sentence": "list a six stars horror film within the last six years with robin wright", "entity_names": ["six stars", "horror", "last six years", "robin wright"], "entity_types": ["Viewers' Rating", "Genre", "Year", "Actor"]}
{"sentence": "list a spaghetti western movie", "entity_names": ["spaghetti western"], "entity_types": ["Genre"]}
{"sentence": "list a suspense movie within the past three years", "entity_names": ["suspense", "past three years"], "entity_types": ["Genre", "Year"]}
{"sentence": "list a teen movie thats rated pg 13 that received a five stars and above rating", "entity_names": ["teen", "pg 13", "five stars and above"], "entity_types": ["Genre", "MPAA Rating", "Viewers' Rating"]}
{"sentence": "list a thriller film starring meryl streep", "entity_names": ["thriller", "meryl streep"], "entity_types": ["Genre", "Actor"]}
{"sentence": "list a very good movie for children starring nicole kidman", "entity_names": ["very good", "children", "nicole kidman"], "entity_types": ["Viewers' Rating", "Genre", "Actor"]}
{"sentence": "list a war film starring robert evan", "entity_names": ["war", "robert evan"], "entity_types": ["Genre", "Actor"]}
{"sentence": "list a watchable tommy lee jones r 1960 s movie", "entity_names": ["watchable", "tommy lee jones", "r", "1960 s"], "entity_types": ["Viewers' Rating", "Actor", "MPAA Rating", "Year"]}
{"sentence": "list a well rated pg 13 teen movie", "entity_names": ["well rated", "pg 13", "teen"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre"]}
{"sentence": "list action movies that star satoshi kon", "entity_names": ["action", "satoshi kon"], "entity_types": ["Genre", "Director"]}
{"sentence": "list adventure films all about air battles released within the last year that were rated a six", "entity_names": ["adventure", "air battles", "last year", "six"], "entity_types": ["Genre", "Plot", "Year", "Viewers' Rating"]}
{"sentence": "list all 1950 s bounty hunter themed r rated movies that got nine stars and had matthew porretta in them", "entity_names": ["1950 s", "bounty hunter", "r", "nine stars", "matthew porretta"], "entity_types": ["Year", "Plot", "MPAA Rating", "Viewers' Rating", "Actor"]}
{"sentence": "list all g rated movies about a road trip that received an average rating", "entity_names": ["g", "road trip", "average"], "entity_types": ["MPAA Rating", "Plot", "Viewers' Rating"]}
{"sentence": "list all r rated film noir movies", "entity_names": ["r", "film noir"], "entity_types": ["MPAA Rating", "Genre"]}
{"sentence": "list all adventure movies that came out in 2000", "entity_names": ["adventure", "2000"], "entity_types": ["Genre", "Year"]}
{"sentence": "list all fantasy movies that have been released in the past decade", "entity_names": ["fantasy", "past decade"], "entity_types": ["Genre", "Year"]}
{"sentence": "list all horror movies directed by amos kollek in the past five decades", "entity_names": ["horror", "amos kollek", "past five decades"], "entity_types": ["Genre", "Director", "Year"]}
{"sentence": "list all romance movies made in 1950", "entity_names": ["romance", "1950"], "entity_types": ["Genre", "Year"]}
{"sentence": "list all tales that were rated r", "entity_names": ["tales", "r"], "entity_types": ["Genre", "MPAA Rating"]}
{"sentence": "list an inception movie which director was frank capra", "entity_names": ["inception", "frank capra"], "entity_types": ["Title", "Director"]}
{"sentence": "list an nc 17 rated biography film that centers on composer staring fisher stevens in the last nine decades", "entity_names": ["nc 17", "biography", "composer", "fisher stevens", "last nine decades"], "entity_types": ["MPAA Rating", "Genre", "Plot", "Actor", "Year"]}
{"sentence": "list an pg rated action jujitsu with ratings average nine starring peter cushing in 1990", "entity_names": ["pg", "action", "jujitsu", "nine", "peter cushing", "1990"], "entity_types": ["MPAA Rating", "Genre", "Plot", "Viewers' Rating", "Actor", "Year"]}
{"sentence": "list an pg 13 eight star movie with robert ri chard in the year 1950", "entity_names": ["pg 13", "eight", "robert ri chard", "1950"], "entity_types": ["MPAA Rating", "Viewers' Rating", "Actor", "Year"]}
{"sentence": "list an r rated drama starring yasmine bleeth", "entity_names": ["r", "drama", "yasmine bleeth"], "entity_types": ["MPAA Rating", "Genre", "Actor"]}
{"sentence": "list an r rated film that centers on quest with excellent ratings for the past decade", "entity_names": ["r", "quest", "excellent ratings", "past decade"], "entity_types": ["MPAA Rating", "Plot", "Viewers' Rating", "Year"]}
{"sentence": "list an r rated highly recommended political film from the past year", "entity_names": ["r", "highly recommended", "political", "past year"], "entity_types": ["MPAA Rating", "Viewers' Rating", "Genre", "Year"]}
{"sentence": "list an r rated thriller that stars jimmy smits and has an eight star rating", "entity_names": ["r", "thriller", "jimmy smits", "eight"], "entity_types": ["MPAA Rating", "Genre", "Actor", "Viewers' Rating"]}
{"sentence": "list an adventure movie in the 1960 s about an information team directed by aleks rosenberg", "entity_names": ["adventure", "1960 s", "information team", "aleks rosenberg"], "entity_types": ["Genre", "Year", "Plot", "Director"]}
{"sentence": "list an animation from the past three years that has excellent ratings and andrew douglas is the director", "entity_names": ["animation", "past three years", "excellent ratings", "andrew douglas"], "entity_types": ["Genre", "Year", "Viewers' Rating", "Director"]}
{"sentence": "list an eight stars and above independent movie starring judi dench", "entity_names": ["eight stars and above", "independent", "judi dench"], "entity_types": ["Viewers' Rating", "Genre", "Actor"]}
{"sentence": "list an emotional affair movie from director j mackye gruber that is rated pg 13 in 1960", "entity_names": ["emotional", "affair", "j mackye gruber", "pg 13", "1960"], "entity_types": ["Genre", "Plot", "Director", "MPAA Rating", "Year"]}
{"sentence": "list an emotional movie during the year 1990 s", "entity_names": ["emotional", "1990 s"], "entity_types": ["Genre", "Year"]}
{"sentence": "list an independent movie starring judi dench", "entity_names": ["independent", "judi dench"], "entity_types": ["Genre", "Actor"]}
{"sentence": "list an independent movie", "entity_names": ["independent"], "entity_types": ["Genre"]}
{"sentence": "list an unrated adventure from the past ten decades", "entity_names": ["unrated", "adventure", "past ten decades"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "list an unrated must see drama", "entity_names": ["unrated", "must see", "drama"], "entity_types": ["MPAA Rating", "Viewers' Rating", "Genre"]}
{"sentence": "list childrens movies that included john wayne in the cast", "entity_names": ["childrens", "john wayne"], "entity_types": ["Genre", "Actor"]}
{"sentence": "list crime movies from the 2000 s that were rated nc 17", "entity_names": ["crime", "2000 s", "nc 17"], "entity_types": ["Genre", "Year", "MPAA Rating"]}
{"sentence": "list documentaries that had robert downey jr in them", "entity_names": ["documentaries", "robert downey jr"], "entity_types": ["Genre", "Actor"]}
{"sentence": "list fantasy movies from past seven decades", "entity_names": ["fantasy", "past seven decades"], "entity_types": ["Genre", "Year"]}
{"sentence": "list good g rated documentaries about religion", "entity_names": ["g", "documentaries", "religion"], "entity_types": ["MPAA Rating", "Genre", "Plot"]}
{"sentence": "list horror movies directed by paul abascal that was liked by many", "entity_names": ["horror", "paul abascal", "liked by many"], "entity_types": ["Genre", "Director", "Viewers' Rating"]}
{"sentence": "list independent movie starring audrey hepburn", "entity_names": ["independent", "audrey hepburn"], "entity_types": ["Genre", "Actor"]}
{"sentence": "list movies that starred charlton heston", "entity_names": ["charlton heston"], "entity_types": ["Actor"]}
{"sentence": "list of pg 13 adventure movies from 2010", "entity_names": ["pg 13", "adventure"], "entity_types": ["MPAA Rating", "Genre"]}
{"sentence": "list rated pg 13 crime films that centers assassination attempt starring christopher walkin", "entity_names": ["pg 13", "crime", "assassination attempt", "christopher walkin"], "entity_types": ["MPAA Rating", "Genre", "Plot", "Actor"]}
{"sentence": "list rated r comedy within last seven years", "entity_names": ["r", "comedy", "last seven years"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "list romance films starring barbara eden", "entity_names": ["romance", "barbara eden"], "entity_types": ["Genre", "Actor"]}
{"sentence": "list scary movies with a terror centric plot", "entity_names": ["scary", "terror"], "entity_types": ["Genre", "Plot"]}
{"sentence": "list several funny films", "entity_names": ["funny"], "entity_types": ["Genre"]}
{"sentence": "list six rating pg 13 animation films in 2010", "entity_names": ["six", "pg 13", "animation", "2010"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre", "Year"]}
{"sentence": "list six rating food poisoning entertaining films starring helen hunt", "entity_names": ["six", "food poisoning", "entertaining", "helen hunt"], "entity_types": ["Viewers' Rating", "Plot", "Genre", "Actor"]}
{"sentence": "list some g rated family movies from jerry london", "entity_names": ["g", "family", "jerry london"], "entity_types": ["MPAA Rating", "Genre", "Director"]}
{"sentence": "list some classic mystery films", "entity_names": ["mystery"], "entity_types": ["Genre"]}
{"sentence": "list some comedy movie", "entity_names": ["comedy"], "entity_types": ["Genre"]}
{"sentence": "list some good sport movies from the past decade", "entity_names": ["sport", "past decade"], "entity_types": ["Genre", "Year"]}
{"sentence": "list some movies featuring graphic sexual horror", "entity_names": ["graphic sexual horror"], "entity_types": ["Title"]}
{"sentence": "list some really good mystery movies", "entity_names": ["really good", "mystery"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "list the g rated films with susan hargrove from the 1960 s", "entity_names": ["g", "susan hargrove", "1960 s"], "entity_types": ["MPAA Rating", "Actor", "Year"]}
{"sentence": "list the animation films that were rated five stars", "entity_names": ["animation", "five stars"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "list the documentary films that came out in the past ten years", "entity_names": ["documentary", "past ten years"], "entity_types": ["Genre", "Year"]}
{"sentence": "list the film wall e", "entity_names": ["wall e"], "entity_types": ["Title"]}
{"sentence": "list the films directed by roland emmerich from the past nine years that involved indians", "entity_names": ["roland emmerich", "past nine years", "indians"], "entity_types": ["Director", "Year", "Plot"]}
{"sentence": "list the plot of excess baggage", "entity_names": ["excess baggage"], "entity_types": ["Title"]}
{"sentence": "list the science fiction films directed by deborah kaplan from the last six decades that was rated very good by viewers", "entity_names": ["science fiction", "deborah kaplan", "last six decades", "very good"], "entity_types": ["Genre", "Director", "Year", "Viewers' Rating"]}
{"sentence": "look up the movie title i love hong kong", "entity_names": ["i love hong kong"], "entity_types": ["Title"]}
{"sentence": "looking for a movie with a military setting and that some folks might call mediocre that took place in the past decade", "entity_names": ["military", "mediocre", "past decade"], "entity_types": ["Genre", "Viewers' Rating", "Year"]}
{"sentence": "looking for an ok terrorist film to watch", "entity_names": ["ok", "terrorist"], "entity_types": ["Viewers' Rating", "Plot"]}
{"sentence": "looking for an average rated r crime movie that stars known actors", "entity_names": ["average", "r", "crime"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre"]}
{"sentence": "mention an unrated horror movie from director lamberto bava", "entity_names": ["unrated", "horror", "lamberto bava"], "entity_types": ["MPAA Rating", "Genre", "Director"]}
{"sentence": "michael bay action movie", "entity_names": ["michael bay", "action"], "entity_types": ["Director", "Genre"]}
{"sentence": "movie information on south of the border", "entity_names": ["south of the border"], "entity_types": ["Title"]}
{"sentence": "my husband wants to see the film noir rated nc 17 with nicole kidman in it", "entity_names": ["film noir", "nc 17", "nicole kidman"], "entity_types": ["Genre", "MPAA Rating", "Actor"]}
{"sentence": "name a 1950 wilderness film for children", "entity_names": ["1950", "wilderness", "children"], "entity_types": ["Year", "Plot", "Genre"]}
{"sentence": "name a 2010 s drama", "entity_names": ["2010 s", "drama"], "entity_types": ["Year", "Genre"]}
{"sentence": "name a bruce boxleitner comedy that is rated g", "entity_names": ["bruce boxleitner", "comedy", "g"], "entity_types": ["Actor", "Genre", "MPAA Rating"]}
{"sentence": "name a jonathan segal fantasy film", "entity_names": ["jonathan segal", "fantasy"], "entity_types": ["Director", "Genre"]}
{"sentence": "name a pg 13 film from the 2000 s that starred kathie lee gifford", "entity_names": ["pg 13", "2000 s", "kathie lee gifford"], "entity_types": ["MPAA Rating", "Year", "Actor"]}
{"sentence": "name a rick bieber directed family film rated pg 13 from the past nine decades", "entity_names": ["rick bieber", "family", "pg 13", "past nine decades"], "entity_types": ["Director", "Genre", "MPAA Rating", "Year"]}
{"sentence": "name a sport themed movie directed by chris nahon that everyone says you must see", "entity_names": ["sport", "chris nahon", "must see"], "entity_types": ["Genre", "Director", "Viewers' Rating"]}
{"sentence": "name a comedy released in 1940", "entity_names": ["comedy", "1940"], "entity_types": ["Genre", "Year"]}
{"sentence": "name a critically acclaimed adventure film directed by dennis hopper", "entity_names": ["critically acclaimed", "adventure", "dennis hopper"], "entity_types": ["Viewers' Rating", "Genre", "Director"]}
{"sentence": "name a dark peta wilson movie", "entity_names": ["dark", "peta wilson"], "entity_types": ["Genre", "Actor"]}
{"sentence": "name a drama film that is rated r", "entity_names": ["drama", "r"], "entity_types": ["Genre", "MPAA Rating"]}
{"sentence": "name a film about a kung fu master", "entity_names": ["kung fu master"], "entity_types": ["Plot"]}
{"sentence": "name a film that stars deidre hall and was released in the past six decades", "entity_names": ["deidre hall", "past six decades"], "entity_types": ["Actor", "Year"]}
{"sentence": "name a highly liked r rated funny film released in the last five decades", "entity_names": ["highly liked", "r", "funny", "last five decades"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre", "Year"]}
{"sentence": "name a musical that was directed by billy wilder", "entity_names": ["musical", "billy wilder"], "entity_types": ["Genre", "Director"]}
{"sentence": "name a mystery with a serial killer that was rated seven", "entity_names": ["mystery", "serial killer", "seven"], "entity_types": ["Genre", "Plot", "Viewers' Rating"]}
{"sentence": "name a seven star r rated biography released in 2000 that stars bruce willis", "entity_names": ["seven star", "r", "biography", "2000", "bruce willis"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre", "Year", "Actor"]}
{"sentence": "name a thriller of the 1990 s with an average rating of nine", "entity_names": ["thriller", "1990 s", "nine"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "name a very good pg 13 rated drama", "entity_names": ["very good", "pg 13", "drama"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre"]}
{"sentence": "name all pg 13 movies from the past four decades starring spencer tracey", "entity_names": ["pg 13", "past four decades", "spencer tracey"], "entity_types": ["MPAA Rating", "Year", "Actor"]}
{"sentence": "name all movies in the past ten decades that star jonathon saech", "entity_names": ["past ten decades", "jonathon saech"], "entity_types": ["Year", "Actor"]}
{"sentence": "name all the r rated dramas that had a rating of four stars that starred alberta watson", "entity_names": ["r", "dramas", "four stars", "alberta watson"], "entity_types": ["MPAA Rating", "Genre", "Viewers' Rating", "Actor"]}
{"sentence": "name an actor from stargate sg 1 children of the gods final cut", "entity_names": ["stargate sg 1 children of the gods final cut"], "entity_types": ["Title"]}
{"sentence": "name an emotional film from 2000 that is highly liked", "entity_names": ["emotional", "2000", "highly liked"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "name decently rated fun party movies of the past nine decades", "entity_names": ["decently", "fun", "party", "past nine decades"], "entity_types": ["Viewers' Rating", "Genre", "Plot", "Year"]}
{"sentence": "name some 1950 s history movies", "entity_names": ["1950 s", "history"], "entity_types": ["Year", "Genre"]}
{"sentence": "name the 1940 s military movie by directory florian baxmeyer with a government assassin plot", "entity_names": ["1940 s", "military", "florian baxmeyer", "government assassin"], "entity_types": ["Year", "Genre", "Director", "Plot"]}
{"sentence": "name the harold f kress unrated film with good ratings that centers on a parole hearing", "entity_names": ["harold f kress", "unrated", "good", "parole hearing"], "entity_types": ["Director", "MPAA Rating", "Viewers' Rating", "Plot"]}
{"sentence": "notorious is an instant classic", "entity_names": ["notorious"], "entity_types": ["Title"]}
{"sentence": "pg 13 comedy directed by scott f evans", "entity_names": ["pg 13", "comedy", "scott f evans"], "entity_types": ["MPAA Rating", "Genre", "Director"]}
{"sentence": "please list a 1940 s short movie rated pg 13 starring jon stewart", "entity_names": ["1940 s", "short", "pg 13", "jon stewart"], "entity_types": ["Year", "Genre", "MPAA Rating", "Actor"]}
{"sentence": "please list a kids move about being held captive that is rated r and was released in the past year", "entity_names": ["kids", "held captive", "r", "past year"], "entity_types": ["Genre", "Plot", "MPAA Rating", "Year"]}
{"sentence": "please list any movies about sports", "entity_names": ["sports"], "entity_types": ["Genre"]}
{"sentence": "please list film noir movies", "entity_names": ["film noir"], "entity_types": ["Genre"]}
{"sentence": "please list movies in the crime genre that were given an average rating of six", "entity_names": ["crime", "six"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "please list some mediocre film noir movies released in the past two decades directed by robert hiltzik", "entity_names": ["mediocre", "film noir", "past two decades", "robert hiltzik"], "entity_types": ["Viewers' Rating", "Genre", "Year", "Director"]}
{"sentence": "please provide a list of several r rated war movies", "entity_names": ["r", "war"], "entity_types": ["MPAA Rating", "Genre"]}
{"sentence": "please suggest some animation movies from the past nine decades", "entity_names": ["animation", "past nine decades"], "entity_types": ["Genre", "Year"]}
{"sentence": "please tell me where i can find a fantasy movie", "entity_names": ["fantasy"], "entity_types": ["Genre"]}
{"sentence": "ray brady directed sci fi pg 13 this year with ratings of eight", "entity_names": ["ray brady", "sci fi", "pg 13", "this year", "eight"], "entity_types": ["Director", "Genre", "MPAA Rating", "Year", "Viewers' Rating"]}
{"sentence": "recall a crime movie released in the last five decades that was directed by jesse t cook", "entity_names": ["crime", "last five decades", "jesse t cook"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "romance movies with average ratings", "entity_names": ["romance", "average"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "rose jackson starred in this g rated film of the 2010 s", "entity_names": ["rose jackson", "g", "2010 s"], "entity_types": ["Actor", "MPAA Rating", "Year"]}
{"sentence": "show a family movie with a ratings average of five", "entity_names": ["family", "five"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "show me all action films that were rated very good", "entity_names": ["action", "very good"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "show me all unrated scary moves about gore", "entity_names": ["unrated", "scary", "gore"], "entity_types": ["MPAA Rating", "Genre", "Plot"]}
{"sentence": "show me information about marine boy", "entity_names": ["marine boy"], "entity_types": ["Title"]}
{"sentence": "show me information about prime", "entity_names": ["prime"], "entity_types": ["Title"]}
{"sentence": "show me some movies about witches", "entity_names": ["witches"], "entity_types": ["Plot"]}
{"sentence": "tell me about green lantern emerald knights", "entity_names": ["green lantern emerald knights"], "entity_types": ["Title"]}
{"sentence": "tell me about a highly rated animation film starring theresa randall", "entity_names": ["highly rated", "animation", "theresa randall"], "entity_types": ["Viewers' Rating", "Genre", "Actor"]}
{"sentence": "tell me about the movie lemonade mouth", "entity_names": ["lemonade mouth"], "entity_types": ["Title"]}
{"sentence": "tell me about the movie little children", "entity_names": ["little children"], "entity_types": ["Title"]}
{"sentence": "tell me if there is a well rated comedy with robert duvall in it", "entity_names": ["well rated", "comedy", "robert duvall"], "entity_types": ["Viewers' Rating", "Genre", "Actor"]}
{"sentence": "tell me some 1950 s adventure movies that are about a fight", "entity_names": ["1950 s", "adventure", "fight"], "entity_types": ["Year", "Genre", "Plot"]}
{"sentence": "tell me some movies starring al lewis", "entity_names": ["al lewis"], "entity_types": ["Actor"]}
{"sentence": "tell me the name of a susan seidelman tale movie that came out in the past two decades and was about a warthog", "entity_names": ["susan seidelman", "tale", "past two decades", "warthog"], "entity_types": ["Director", "Genre", "Year", "Plot"]}
{"sentence": "that comedy was so funny that id give it ten stars", "entity_names": ["funny", "ten stars"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "the tree came out when", "entity_names": ["the tree"], "entity_types": ["Title"]}
{"sentence": "the actor brad renfro starred in the highly recommended film noir in the 1960 s", "entity_names": ["brad renfro", "highly recommended", "film noir", "1960 s"], "entity_types": ["Actor", "Viewers' Rating", "Genre", "Year"]}
{"sentence": "the director april maiya directed many romantic wedding movies in the 2000 s", "entity_names": ["april maiya", "romantic", "wedding", "2000 s"], "entity_types": ["Director", "Genre", "Plot", "Year"]}
{"sentence": "the movie blue steel", "entity_names": ["blue steel"], "entity_types": ["Title"]}
{"sentence": "the movie the wind that shakes the barley what is it about", "entity_names": ["the wind that shakes the barley"], "entity_types": ["Title"]}
{"sentence": "the name of the fantasy movie with billy bob thornton that was given two thumbs up", "entity_names": ["fantasy", "billy bob thornton", "two thumbs up"], "entity_types": ["Genre", "Actor", "Viewers' Rating"]}
{"sentence": "theres this one brendan fraser movie from 2000 i think its pg 13 im trying to find more information", "entity_names": ["brendan fraser", "2000", "pg 13"], "entity_types": ["Actor", "Year", "MPAA Rating"]}
{"sentence": "this 1950 horror film was directed by alex chapple", "entity_names": ["1950", "horror", "alex chapple"], "entity_types": ["Year", "Genre", "Director"]}
{"sentence": "this mockumentary of the last decade received four stars", "entity_names": ["mockumentary", "last decade", "four stars"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "tim abell was featured in what g rated 1970 s italian american film with an average rating of four", "entity_names": ["tim abell", "g", "1970", "italian american", "four"], "entity_types": ["Actor", "MPAA Rating", "Year", "Plot", "Viewers' Rating"]}
{"sentence": "unrated mystery in the past seven years that has four stars", "entity_names": ["unrated", "mystery", "past seven years", "four stars"], "entity_types": ["MPAA Rating", "Genre", "Year", "Viewers' Rating"]}
{"sentence": "wanted to know if in the past two years has there been an all right action movie that came to have a standoff", "entity_names": ["past two years", "all right", "action", "standoff"], "entity_types": ["Year", "Viewers' Rating", "Genre", "Plot"]}
{"sentence": "wanted to know is there a rated r movie set in war made in 2010 that has the actor yasmine bleeth in it", "entity_names": ["r", "war", "2010", "yasmine bleeth"], "entity_types": ["MPAA Rating", "Genre", "Year", "Actor"]}
{"sentence": "was anne bancroft in a movie about mexico last year", "entity_names": ["anne bancroft", "mexico", "last year"], "entity_types": ["Actor", "Plot", "Year"]}
{"sentence": "was bruce boxleitners 1950 movie rated r", "entity_names": ["bruce boxleitners", "1950", "r"], "entity_types": ["Actor", "Year", "MPAA Rating"]}
{"sentence": "was cate blanchett in any independent films", "entity_names": ["cate blanchett", "independent"], "entity_types": ["Actor", "Genre"]}
{"sentence": "was charlton heston ever in a romantic comedy", "entity_names": ["charlton heston", "romantic comedy"], "entity_types": ["Actor", "Genre"]}
{"sentence": "was david lynch offered to direct the shining", "entity_names": ["david lynch", "the shining"], "entity_types": ["Director", "Title"]}
{"sentence": "was gene hackman ever a narrator for a biographical film", "entity_names": ["gene hackman", "biographical"], "entity_types": ["Actor", "Genre"]}
{"sentence": "was guillermo del toro ever in any western films", "entity_names": ["guillermo del toro", "western"], "entity_types": ["Director", "Genre"]}
{"sentence": "was hugh jackman ever in an r rated war film that is highly recommended", "entity_names": ["hugh jackman", "r", "war", "highly recommended"], "entity_types": ["Actor", "MPAA Rating", "Genre", "Viewers' Rating"]}
{"sentence": "was humphrey bogart in a western movie that has excellent ratings", "entity_names": ["humphrey bogart", "western", "has excellent ratings"], "entity_types": ["Actor", "Genre", "Viewers' Rating"]}
{"sentence": "was humphrey bogart in any good children movies", "entity_names": ["humphrey bogart", "children"], "entity_types": ["Actor", "Genre"]}
{"sentence": "was humphrey bogart in any good thriller films", "entity_names": ["humphrey bogart", "thriller"], "entity_types": ["Actor", "Genre"]}
{"sentence": "was ian mckellen offered a role in the shining", "entity_names": ["ian mckellen", "the shining"], "entity_types": ["Actor", "Title"]}
{"sentence": "was john travolta ever in a movie about disaster", "entity_names": ["john travolta", "disaster"], "entity_types": ["Actor", "Genre"]}
{"sentence": "was kriss kristopherson in a mediocre pg biography", "entity_names": ["kriss kristopherson", "mediocre", "pg", "biography"], "entity_types": ["Actor", "Viewers' Rating", "MPAA Rating", "Genre"]}
{"sentence": "was matt damon even alive when the shining was made", "entity_names": ["matt damon", "the shining"], "entity_types": ["Actor", "Title"]}
{"sentence": "was nicole kidman in any r rated adventure movies", "entity_names": ["nicole kidman", "r", "adventure"], "entity_types": ["Actor", "MPAA Rating", "Genre"]}
{"sentence": "was nikki cox in an animation movie", "entity_names": ["nikki cox", "animation"], "entity_types": ["Actor", "Genre"]}
{"sentence": "was pam grier in an r rated movie that critics said was very good", "entity_names": ["pam grier", "r", "very good"], "entity_types": ["Actor", "MPAA Rating", "Viewers' Rating"]}
{"sentence": "was samuel l jackson in any crime movies in the 1960 s when he was a child", "entity_names": ["samuel l jackson", "crime", "1960 s"], "entity_types": ["Actor", "Genre", "Year"]}
{"sentence": "was vivien leigh in a musical that was rated r and received nine stars and above", "entity_names": ["vivien leigh", "musical", "r", "nine stars and above"], "entity_types": ["Actor", "Genre", "MPAA Rating", "Viewers' Rating"]}
{"sentence": "was that michael bergin scary pg 13 movie made in the 2000 s all right", "entity_names": ["michael bergin", "scary", "pg 13", "2000 s", "all right"], "entity_types": ["Actor", "Genre", "MPAA Rating", "Year", "Viewers' Rating"]}
{"sentence": "was there a biography about jeff bridges made in the last year", "entity_names": ["biography", "jeff bridges", "last year"], "entity_types": ["Genre", "Actor", "Year"]}
{"sentence": "was there a crime movie with cheech marin in 1970", "entity_names": ["crime", "cheech marin", "1970"], "entity_types": ["Genre", "Actor", "Year"]}
{"sentence": "was there a decent watchable romance in the past year that starred tim roth", "entity_names": ["watchable", "romance", "past year", "tim roth"], "entity_types": ["Viewers' Rating", "Genre", "Year", "Actor"]}
{"sentence": "was there a highly recommended romance released in the 1960 s", "entity_names": ["highly recommended", "romance", "1960 s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "was there a movie called bronson", "entity_names": ["bronson"], "entity_types": ["Title"]}
{"sentence": "was there a science fiction last year", "entity_names": ["science fiction", "last year"], "entity_types": ["Genre", "Year"]}
{"sentence": "was there a very good film starring jane seymour the debuted in 1950", "entity_names": ["very good", "jane seymour", "1950"], "entity_types": ["Viewers' Rating", "Actor", "Year"]}
{"sentence": "was there an r rated family movie starring chad fees that got nine stars that was released in the past eight decades", "entity_names": ["r", "family", "chad fees", "nine stars", "past eight decades"], "entity_types": ["MPAA Rating", "Genre", "Actor", "Viewers' Rating", "Year"]}
{"sentence": "was there ever a movie about a magic book that was rated nc 17", "entity_names": ["magic book", "nc 17"], "entity_types": ["Plot", "MPAA Rating"]}
{"sentence": "were there many family movies liked by many in 1940", "entity_names": ["family", "liked by many", "1940"], "entity_types": ["Genre", "Viewers' Rating", "Year"]}
{"sentence": "what 1940 family movie starring kevin anderson was about a childhood sweetheart", "entity_names": ["1940", "family", "kevin anderson", "childhood sweetheart"], "entity_types": ["Year", "Genre", "Actor", "Plot"]}
{"sentence": "what 1940 hit received all right ratings and was based on a culture clash directed by mark romanek", "entity_names": ["1940", "all right", "culture clash", "mark romanek"], "entity_types": ["Year", "Viewers' Rating", "Plot", "Director"]}
{"sentence": "what 1950 action movie stars clancy brown", "entity_names": ["1950", "action", "clancy brown"], "entity_types": ["Year", "Genre", "Actor"]}
{"sentence": "what 1950 mockumentary had eight stars starring james garner", "entity_names": ["1950", "mockumentary", "eight", "james garner"], "entity_types": ["Year", "Genre", "Viewers' Rating", "Actor"]}
{"sentence": "what 1950 s war movies are about snipers", "entity_names": ["1950 s", "war", "snipers"], "entity_types": ["Year", "Genre", "Plot"]}
{"sentence": "what 1960 film about an information team got two thumbs up and featured actor melinda clarke", "entity_names": ["1960", "information team", "two thumbs up", "melinda clarke"], "entity_types": ["Year", "Plot", "Viewers' Rating", "Actor"]}
{"sentence": "what 1960 s drama received good ratings", "entity_names": ["1960 s", "drama", "good ratings"], "entity_types": ["Year", "Genre", "Viewers' Rating"]}
{"sentence": "what 1970 short film directed by james de frond got a pg 13 rating", "entity_names": ["1970", "short", "james de frond", "pg 13"], "entity_types": ["Year", "Genre", "Director", "MPAA Rating"]}
{"sentence": "what 1980 s cowboy film was directed morten lindberg", "entity_names": ["1980 s", "cowboy", "morten lindberg"], "entity_types": ["Year", "Genre", "Director"]}
{"sentence": "what 1980 s horror movie starred james cagney", "entity_names": ["1980 s", "horror", "james cagney"], "entity_types": ["Year", "Genre", "Actor"]}
{"sentence": "what 1990 horror movie about demons was directed by philip adrian booth", "entity_names": ["1990", "horror", "demons", "philip adrian booth"], "entity_types": ["Year", "Genre", "Plot", "Director"]}
{"sentence": "what 1990 s animation film was rated g", "entity_names": ["1990 s", "animation", "g"], "entity_types": ["Year", "Genre", "MPAA Rating"]}
{"sentence": "what 2000 highly recommended kristen scott thomas movie about a runaway was rated nc 17", "entity_names": ["2000", "highly recommended", "kristen scott thomas", "runaway", "nc 17"], "entity_types": ["Year", "Viewers' Rating", "Actor", "Plot", "MPAA Rating"]}
{"sentence": "what 2010 pg rated adventure film stars wayne a harold", "entity_names": ["2010", "pg", "adventure", "wayne a harold"], "entity_types": ["Year", "MPAA Rating", "Genre", "Director"]}
{"sentence": "what 2010 movies did robert evan star in", "entity_names": ["2010", "robert evan"], "entity_types": ["Year", "Actor"]}
{"sentence": "what 2010 well rated romantic comedy about friends starred kim delaney and received an r rating", "entity_names": ["2010", "well rated", "romantic comedy", "friends", "kim delaney", "r"], "entity_types": ["Year", "Viewers' Rating", "Genre", "Plot", "Actor", "MPAA Rating"]}
{"sentence": "what 2010 s movie starring david caruso is about a utopia", "entity_names": ["2010 s", "david caruso", "utopia"], "entity_types": ["Year", "Actor", "Plot"]}
{"sentence": "what bentley dean kid movie in the last seven years received excellent ratings", "entity_names": ["bentley dean", "kid", "last seven years", "excellent ratings"], "entity_types": ["Director", "Genre", "Year", "Viewers' Rating"]}
{"sentence": "what carroll oconner musical is liked by many", "entity_names": ["carroll oconner", "musical", "liked by many"], "entity_types": ["Actor", "Genre", "Viewers' Rating"]}
{"sentence": "what g rated 1980 disaster movie that was directed by yi seung jun with a ratings average of eight called", "entity_names": ["g", "1980", "disaster", "yi seung jun", "eight"], "entity_types": ["MPAA Rating", "Year", "Plot", "Director", "Viewers' Rating"]}
{"sentence": "what g rated horror movie released within the last four years that was directed by william richert", "entity_names": ["g", "horror", "last four years", "william richert"], "entity_types": ["MPAA Rating", "Genre", "Year", "Director"]}
{"sentence": "what g rated movie with moira kelly was produced in the 1990 s and was received well", "entity_names": ["g", "moira kelly", "1990 s", "received well"], "entity_types": ["MPAA Rating", "Actor", "Year", "Viewers' Rating"]}
{"sentence": "what g rated tale was liked a lot", "entity_names": ["g", "tale", "was liked a lot"], "entity_types": ["MPAA Rating", "Genre", "Viewers' Rating"]}
{"sentence": "what gary cooper thriller in the last nine years has a very popular rating", "entity_names": ["gary cooper", "thriller", "last nine years", "very popular"], "entity_types": ["Actor", "Genre", "Year", "Viewers' Rating"]}
{"sentence": "what humphrey bogart movie would a family in the 1990 s find really popular", "entity_names": ["humphrey bogart", "family", "1990 s", "really popular"], "entity_types": ["Actor", "Genre", "Year", "Viewers' Rating"]}
{"sentence": "what jose quiroz romances have come out in the past seven years", "entity_names": ["jose quiroz", "romances", "past seven years"], "entity_types": ["Director", "Genre", "Year"]}
{"sentence": "what nc 17 movie starred dean stockwell", "entity_names": ["nc 17", "dean stockwell"], "entity_types": ["MPAA Rating", "Actor"]}
{"sentence": "what nc 17 movies about women received eight stars", "entity_names": ["nc 17", "women", "eight stars"], "entity_types": ["MPAA Rating", "Genre", "Viewers' Rating"]}
{"sentence": "what nc 17 sport movie did chris farley star in that received mediocre ratings", "entity_names": ["nc 17", "sport", "chris farley", "mediocre"], "entity_types": ["MPAA Rating", "Genre", "Actor", "Viewers' Rating"]}
{"sentence": "what ok crime movie was directed by taylor hackford", "entity_names": ["ok", "crime", "taylor hackford"], "entity_types": ["Viewers' Rating", "Genre", "Director"]}
{"sentence": "what pg and well rated movie stars tyra banks within the laugh genre", "entity_names": ["pg", "well rated", "tyra banks", "laugh"], "entity_types": ["MPAA Rating", "Viewers' Rating", "Actor", "Genre"]}
{"sentence": "what pg movie released within the last two years featured david cassidy and was liked by many", "entity_names": ["pg", "last two years", "david cassidy", "liked by many"], "entity_types": ["MPAA Rating", "Year", "Actor", "Viewers' Rating"]}
{"sentence": "what pg 13 romantic comedy starred tom hanks", "entity_names": ["pg 13", "romantic comedy", "tom hanks"], "entity_types": ["MPAA Rating", "Genre", "Actor"]}
{"sentence": "what pg 13 war movie has spencer tracy starred in", "entity_names": ["pg 13", "war", "spencer tracy"], "entity_types": ["MPAA Rating", "Genre", "Actor"]}
{"sentence": "what pg 13 rated documentary about politics was made in the 1990 s", "entity_names": ["pg 13", "documentary", "politics", "1990 s"], "entity_types": ["MPAA Rating", "Genre", "Plot", "Year"]}
{"sentence": "what pg 13 james bedford mystery got two thumbs up in the 1980 s", "entity_names": ["pg 13", "james bedford", "mystery", "two thumbs up", "1980 s"], "entity_types": ["MPAA Rating", "Director", "Genre", "Viewers' Rating", "Year"]}
{"sentence": "what pg 13 animation movie was liked by many and starred jonathon pryce", "entity_names": ["pg 13", "animation", "liked by many", "jonathon pryce"], "entity_types": ["MPAA Rating", "Genre", "Viewers' Rating", "Actor"]}
{"sentence": "what pg 13 bandolero movie was aaron kwok in that received five stars", "entity_names": ["pg 13", "bandolero", "aaron kwok", "five stars"], "entity_types": ["MPAA Rating", "Plot", "Actor", "Viewers' Rating"]}
{"sentence": "what pg 13 crime movie starring danny devito was released within the last ten years and received an ok rating", "entity_names": ["pg 13", "crime", "danny devito", "last ten years", "ok"], "entity_types": ["MPAA Rating", "Genre", "Actor", "Year", "Viewers' Rating"]}
{"sentence": "what pg 13 fantasy movie did jane fonda star in that got good ratings", "entity_names": ["pg 13", "fantasy", "jane fonda", "good ratings"], "entity_types": ["MPAA Rating", "Genre", "Actor", "Viewers' Rating"]}
{"sentence": "what pg 13 film was based on alien intrusion", "entity_names": ["pg 13", "alien intrusion"], "entity_types": ["MPAA Rating", "Plot"]}
{"sentence": "what pg 13 films have an occult plot", "entity_names": ["pg 13", "occult"], "entity_types": ["MPAA Rating", "Plot"]}
{"sentence": "what pg 13 movie about an investigation did david green direct and get an average rating of seven stars", "entity_names": ["pg 13", "investigation", "david green", "seven stars"], "entity_types": ["MPAA Rating", "Plot", "Director", "Viewers' Rating"]}
{"sentence": "what pg 13 movie did warren p sonoda direct about the western times in the 1970 s that received mediocre ratings", "entity_names": ["pg 13", "warren p sonoda", "western", "1970 s", "mediocre"], "entity_types": ["MPAA Rating", "Director", "Genre", "Year", "Viewers' Rating"]}
{"sentence": "what pg 13 movie with an average rating of nine stars michael pare", "entity_names": ["pg 13", "nine", "michael pare"], "entity_types": ["MPAA Rating", "Viewers' Rating", "Actor"]}
{"sentence": "what pg 13 mystery movies of the 2010 s are at least four stars rating", "entity_names": ["pg 13", "mystery", "2010 s", "four"], "entity_types": ["MPAA Rating", "Genre", "Year", "Viewers' Rating"]}
{"sentence": "what pg 13 rated film directed by john huston had a prisoner of war plot and an average rating of seven stars", "entity_names": ["pg 13", "john huston", "prisoner of war", "seven stars"], "entity_types": ["MPAA Rating", "Director", "Plot", "Viewers' Rating"]}
{"sentence": "what r rated movies did robert vaughn star in this year", "entity_names": ["r", "robert vaughn", "this year"], "entity_types": ["MPAA Rating", "Actor", "Year"]}
{"sentence": "what r rated rebel movie was peta wilson in that received an average rating of five", "entity_names": ["r", "rebel", "peta wilson", "five"], "entity_types": ["MPAA Rating", "Plot", "Actor", "Viewers' Rating"]}
{"sentence": "what r rated suspense movies came out in 1990", "entity_names": ["r", "suspense", "1990"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "what r rated western with a rating of eight that was directed by ken wheat", "entity_names": ["r", "western", "eight", "ken wheat"], "entity_types": ["MPAA Rating", "Genre", "Viewers' Rating", "Director"]}
{"sentence": "what r rated howard stern movies are there from the past two decades", "entity_names": ["r", "howard stern", "past two decades"], "entity_types": ["MPAA Rating", "Actor", "Year"]}
{"sentence": "what r rated movie was released this year about the 1960 s that has an eight star rating", "entity_names": ["r", "this year", "1960 s", "eight star"], "entity_types": ["MPAA Rating", "Year", "Plot", "Viewers' Rating"]}
{"sentence": "what r rated short came out in 2010 and was rated average", "entity_names": ["r", "short", "2010", "average"], "entity_types": ["MPAA Rating", "Genre", "Year", "Viewers' Rating"]}
{"sentence": "what rachel perkins short came out in 1940", "entity_names": ["rachel perkins", "short", "1940"], "entity_types": ["Director", "Genre", "Year"]}
{"sentence": "what rated r evil and scary movies from mihalis kakogiannis came out last year", "entity_names": ["r", "evil", "scary", "mihalis kakogiannis", "last year"], "entity_types": ["MPAA Rating", "Plot", "Genre", "Director", "Year"]}
{"sentence": "what spike lee movie stars clint eastwood", "entity_names": ["spike lee", "clint eastwood"], "entity_types": ["Director", "Actor"]}
{"sentence": "what tanya hamilton directed biography received a ok rating", "entity_names": ["tanya hamilton", "biography", "ok"], "entity_types": ["Director", "Genre", "Viewers' Rating"]}
{"sentence": "what western movies are there from the 2010 s", "entity_names": ["western", "2010 s"], "entity_types": ["Genre", "Year"]}
{"sentence": "what woody allen film s featured paul newman", "entity_names": ["woody allen", "paul newman"], "entity_types": ["Director", "Actor"]}
{"sentence": "what action films earned a rating of five stars and above", "entity_names": ["action", "five stars and above"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "what actor stars in a pg 13 action movie set in the year 1940 that was received well", "entity_names": ["pg 13", "action", "1940", "received well"], "entity_types": ["MPAA Rating", "Genre", "Year", "Viewers' Rating"]}
{"sentence": "what adventure movie about a secret mission rated pg 13 starring moira kelly and released last year averaged seven stars", "entity_names": ["adventure", "secret mission", "pg 13", "moira kelly", "last year", "seven stars"], "entity_types": ["Genre", "Plot", "MPAA Rating", "Actor", "Year", "Viewers' Rating"]}
{"sentence": "what adventure movie about murder that was released in the last decade got nine stars and starred jerry obach", "entity_names": ["adventure", "murder", "last decade", "nine stars", "jerry obach"], "entity_types": ["Genre", "Plot", "Year", "Viewers' Rating", "Actor"]}
{"sentence": "what adventure movies did dave r watkins direct", "entity_names": ["adventure", "dave r watkins"], "entity_types": ["Genre", "Director"]}
{"sentence": "what are all the musicals from the 2000 s", "entity_names": ["musicals", "2000 s"], "entity_types": ["Genre", "Year"]}
{"sentence": "what are any titles of horror films rate r from the past nine years starring joe pantiliano", "entity_names": ["horror", "r", "past nine years", "joe pantiliano"], "entity_types": ["Genre", "MPAA Rating", "Year", "Actor"]}
{"sentence": "what are movies with the word ringmaster in the title", "entity_names": ["ringmaster"], "entity_types": ["Title"]}
{"sentence": "what are several titles of must see films rated r about fantasy and cruelty from the past three decades", "entity_names": ["must see", "r", "fantasy", "cruelty", "past three decades"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre", "Plot", "Year"]}
{"sentence": "what are several titles of unrated action movies starring jason priestley", "entity_names": ["unrated", "action", "jason priestley"], "entity_types": ["MPAA Rating", "Genre", "Actor"]}
{"sentence": "what are some ok r biographys that came out in the 1970 s", "entity_names": ["ok", "r", "biographys", "1970 s"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre", "Year"]}
{"sentence": "what are some pg movies that came out last year and had something to do with the british", "entity_names": ["pg", "last year", "british"], "entity_types": ["MPAA Rating", "Year", "Plot"]}
{"sentence": "what are some pg 13 thrillers", "entity_names": ["pg 13", "thrillers"], "entity_types": ["MPAA Rating", "Genre"]}
{"sentence": "what are some r rated drama films that were directed by john geddes", "entity_names": ["r", "drama", "john geddes"], "entity_types": ["MPAA Rating", "Genre", "Director"]}
{"sentence": "what are some r rated fantasy movies by guy magar", "entity_names": ["r", "fantasy", "guy magar"], "entity_types": ["MPAA Rating", "Genre", "Director"]}
{"sentence": "what are some r rated gangster movies from the 1990 s", "entity_names": ["r", "gangster", "1990 s"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "what are some r rated military movies", "entity_names": ["r", "military"], "entity_types": ["MPAA Rating", "Genre"]}
{"sentence": "what are some r rated movies that combine adventure and cia espionage", "entity_names": ["r", "adventure", "cia"], "entity_types": ["MPAA Rating", "Genre", "Plot"]}
{"sentence": "what are some r rated western movies that received ten stars", "entity_names": ["r", "western", "ten stars"], "entity_types": ["MPAA Rating", "Genre", "Viewers' Rating"]}
{"sentence": "what are some action films that are pg 13", "entity_names": ["action", "pg 13"], "entity_types": ["Genre", "MPAA Rating"]}
{"sentence": "what are some animation movies that received nine stars", "entity_names": ["animation", "nine stars"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "what are some childrens films directed by george lucas", "entity_names": ["childrens", "george lucas"], "entity_types": ["Genre", "Director"]}
{"sentence": "what are some classic action films", "entity_names": ["action"], "entity_types": ["Genre"]}
{"sentence": "what are some classic military movies", "entity_names": ["military"], "entity_types": ["Genre"]}
{"sentence": "what are some classic mystery films", "entity_names": ["mystery"], "entity_types": ["Genre"]}
{"sentence": "what are some dramas starring james stewart", "entity_names": ["dramas", "james stewart"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what are some fantasy movies directed by glen morgan in the past three decades", "entity_names": ["fantasy", "glen morgan", "past three decades"], "entity_types": ["Genre", "Director", "Year"]}
{"sentence": "what are some fantasy movies in the past six years directed by corey yuen", "entity_names": ["fantasy", "past six years", "corey yuen"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "what are some good drama films", "entity_names": ["drama"], "entity_types": ["Genre"]}
{"sentence": "what are some good kids movies starring adrian pasdar", "entity_names": ["good", "kids", "adrian pasdar"], "entity_types": ["Viewers' Rating", "Genre", "Actor"]}
{"sentence": "what are some highly rated thriller films about being haunted that are rated r", "entity_names": ["highly rated", "thriller", "haunted", "r"], "entity_types": ["Viewers' Rating", "Genre", "Plot", "MPAA Rating"]}
{"sentence": "what are some history movies rated r with an average rating of six", "entity_names": ["history", "r", "six"], "entity_types": ["Genre", "MPAA Rating", "Viewers' Rating"]}
{"sentence": "what are some independent films", "entity_names": ["independent"], "entity_types": ["Genre"]}
{"sentence": "what are some must see westerns from the 1950 s", "entity_names": ["must see", "westerns", "1950 s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "what are some police themed movies from the 1990 s", "entity_names": ["police", "1990 s"], "entity_types": ["Genre", "Year"]}
{"sentence": "what are some really good crime films", "entity_names": ["crime"], "entity_types": ["Genre"]}
{"sentence": "what are some sci fi g rated films from 1970", "entity_names": ["sci fi", "g", "1970"], "entity_types": ["Genre", "MPAA Rating", "Year"]}
{"sentence": "what are some science fictions films that have randolph mantooth in them", "entity_names": ["science fictions", "randolph mantooth"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what are some spaghetti westerns that were received well", "entity_names": ["spaghetti westerns", "received well"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "what are some titles of animation films directed by tomm coker that received average ratings", "entity_names": ["animation", "tomm coker", "average"], "entity_types": ["Genre", "Director", "Viewers' Rating"]}
{"sentence": "what are some titles of any spy films rated r that were directed by rob walker from the last two years and liked by many", "entity_names": ["spy", "r", "rob walker", "last two years", "liked by many"], "entity_types": ["Plot", "MPAA Rating", "Director", "Year", "Viewers' Rating"]}
{"sentence": "what are some titles of crime films from the 1950 s", "entity_names": ["crime", "1950 s"], "entity_types": ["Genre", "Year"]}
{"sentence": "what are some very popular documentary movies rated r from the 2010 s", "entity_names": ["very popular", "documentary", "r", "2010 s"], "entity_types": ["Viewers' Rating", "Genre", "MPAA Rating", "Year"]}
{"sentence": "what are some well known independent films from the 1960 s", "entity_names": ["independent", "1960 s"], "entity_types": ["Genre", "Year"]}
{"sentence": "what are the pg 13 fight movies that were well rated and lasted seven years", "entity_names": ["pg 13", "fight", "well rated", "lasted seven years"], "entity_types": ["MPAA Rating", "Plot", "Viewers' Rating", "Year"]}
{"sentence": "what are the best disney movies", "entity_names": ["disney"], "entity_types": ["Genre"]}
{"sentence": "what are the movies released the last eight years with james dean", "entity_names": ["last eight years", "james dean"], "entity_types": ["Year", "Actor"]}
{"sentence": "what are the names of all the actors in 1960 s suspense movies dealing with survival", "entity_names": ["1960 s", "suspense", "survival"], "entity_types": ["Year", "Genre", "Plot"]}
{"sentence": "what are the names of the drama movies directed by woody allen", "entity_names": ["drama", "woody allen"], "entity_types": ["Genre", "Director"]}
{"sentence": "what are the titles of any action movies rated five stars and above directed by ron howard", "entity_names": ["action", "five stars and above", "ron howard"], "entity_types": ["Genre", "Viewers' Rating", "Director"]}
{"sentence": "what are the titles of any animation films from the past nine years that were rated four stars in which leonard nimoy appeared", "entity_names": ["animation", "past nine years", "four", "leonard nimoy"], "entity_types": ["Genre", "Year", "Viewers' Rating", "Actor"]}
{"sentence": "what are the titles of any family movies starring frank langella from the last nine decades that received eight stars ratings", "entity_names": ["family", "frank langella", "last nine decades", "eight stars"], "entity_types": ["Genre", "Actor", "Year", "Viewers' Rating"]}
{"sentence": "what are the titles of several drama films", "entity_names": ["drama"], "entity_types": ["Genre"]}
{"sentence": "what are the titles of some films about history from the past three years directed by josh rubin", "entity_names": ["history", "past three years", "josh rubin"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "what are the titles of some science fiction movies that were produced in the 2000 s", "entity_names": ["science fiction", "2000 s"], "entity_types": ["Genre", "Year"]}
{"sentence": "what are the worst sci fi movies rated pg 13 in the last seven decades", "entity_names": ["sci fi", "pg 13", "last seven decades"], "entity_types": ["Genre", "MPAA Rating", "Year"]}
{"sentence": "what are your most favorite biographical movies", "entity_names": ["biographical"], "entity_types": ["Genre"]}
{"sentence": "what avante garde film is the director orson welles best known for", "entity_names": ["avante garde", "orson welles"], "entity_types": ["Genre", "Director"]}
{"sentence": "what biographical movie did christopher walken star in", "entity_names": ["biographical", "christopher walken"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what biography movies are about warsaw poland", "entity_names": ["biography", "warsaw poland"], "entity_types": ["Genre", "Plot"]}
{"sentence": "what biography movies released in the last seven years were rated pg 13 and directed by jonathan wacks", "entity_names": ["biography", "last seven years", "pg 13", "jonathan wacks"], "entity_types": ["Genre", "Year", "MPAA Rating", "Director"]}
{"sentence": "what biography received a six rating that was directed by brad haynes", "entity_names": ["biography", "six", "brad haynes"], "entity_types": ["Genre", "Viewers' Rating", "Director"]}
{"sentence": "what chick flicks has michael pare done in the past nine decades", "entity_names": ["chick", "michael pare", "past nine decades"], "entity_types": ["Genre", "Actor", "Year"]}
{"sentence": "what comedies has samuel l jackson been in", "entity_names": ["samuel l jackson"], "entity_types": ["Actor"]}
{"sentence": "what cowboy film directed by iren koster received two thumbs up", "entity_names": ["cowboy", "iren koster", "two thumbs up"], "entity_types": ["Genre", "Director", "Viewers' Rating"]}
{"sentence": "what cowboy movie from a past year starring john belushi is about a gold miner", "entity_names": ["cowboy", "past year", "john belushi", "gold miner"], "entity_types": ["Genre", "Year", "Actor", "Plot"]}
{"sentence": "what crime movies are there from the 1970 s that were directed by lincoln ruchti", "entity_names": ["crime", "1970 s", "lincoln ruchti"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "what crime movies has will smith been in", "entity_names": ["crime", "will smith"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what critically acclaimed movies starred ben stiller or his parents in the last five decades", "entity_names": ["critically acclaimed", "ben stiller", "last five decades"], "entity_types": ["Viewers' Rating", "Actor", "Year"]}
{"sentence": "what decent drama of the 2000 s featured a plot about a secret society", "entity_names": ["decent", "drama", "2000 s", "secret society"], "entity_types": ["Viewers' Rating", "Genre", "Year", "Plot"]}
{"sentence": "what decent movie released last year was a pg 13 fantasy that involved elves", "entity_names": ["decent", "last year", "pg 13", "fantasy", "elves"], "entity_types": ["Viewers' Rating", "Year", "MPAA Rating", "Genre", "Plot"]}
{"sentence": "what exactly is the ubaldo terzani horror show", "entity_names": ["ubaldo terzani horror show"], "entity_types": ["Title"]}
{"sentence": "what family film was rated r that was based on retirement directed by ryan shiraki", "entity_names": ["family", "r", "retirement", "ryan shiraki"], "entity_types": ["Genre", "MPAA Rating", "Plot", "Director"]}
{"sentence": "what family movie directed by jefery levy is rated pg", "entity_names": ["family", "jefery levy", "pg"], "entity_types": ["Genre", "Director", "MPAA Rating"]}
{"sentence": "what fantasy movie was released in 1990 and rated well about a magic book starring scott speedman", "entity_names": ["fantasy", "1990", "rated well", "magic book", "scott speedman"], "entity_types": ["Genre", "Year", "Viewers' Rating", "Plot", "Actor"]}
{"sentence": "what fantasy movies were released in 2011", "entity_names": ["fantasy"], "entity_types": ["Genre"]}
{"sentence": "what films has christopher nolan directed", "entity_names": ["christopher nolan"], "entity_types": ["Director"]}
{"sentence": "what five star movies from the 1950 s featured sophia loren", "entity_names": ["five", "1950 s", "sophia loren"], "entity_types": ["Viewers' Rating", "Year", "Actor"]}
{"sentence": "what funny pg 13 movies would you recommend", "entity_names": ["funny", "pg 13"], "entity_types": ["Genre", "MPAA Rating"]}
{"sentence": "what genre is the movie dive", "entity_names": ["dive"], "entity_types": ["Title"]}
{"sentence": "what genre is the movie the presidio", "entity_names": ["the presidio"], "entity_types": ["Title"]}
{"sentence": "what genre of film does hayao miyazaki usually direct", "entity_names": ["hayao miyazaki"], "entity_types": ["Director"]}
{"sentence": "what good crime movie revolves around crime bosses", "entity_names": ["crime", "crime bosses"], "entity_types": ["Genre", "Plot"]}
{"sentence": "what good crime movie was made in the 1990 s", "entity_names": ["crime", "1990 s"], "entity_types": ["Genre", "Year"]}
{"sentence": "what good government documentary can i watch for a research project about republicans", "entity_names": ["government", "documentary"], "entity_types": ["Plot", "Genre"]}
{"sentence": "what good movie made in the 1990 s is scary and about a haunting", "entity_names": ["1990 s", "scary", "haunting"], "entity_types": ["Year", "Genre", "Plot"]}
{"sentence": "what good rated pg 13 women movies did grant brown direct in the last six years", "entity_names": ["good", "pg 13", "women", "grant brown", "last six years"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre", "Director", "Year"]}
{"sentence": "what good romantic comedy can i watch that is about star crossed lovers", "entity_names": ["romantic comedy", "star crossed lovers"], "entity_types": ["Genre", "Plot"]}
{"sentence": "what highly liked pg 13 movie was about a teenage boy", "entity_names": ["highly liked", "pg 13", "teenage boy"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Plot"]}
{"sentence": "what highly rated musical was tamera mowry in within the past seven decades", "entity_names": ["highly rated", "musical", "tamera mowry", "past seven decades"], "entity_types": ["Viewers' Rating", "Genre", "Actor", "Year"]}
{"sentence": "what highly rated sport movie starring juliana margulies released in the past six decades has been highly rated", "entity_names": ["highly rated", "sport", "juliana margulies", "past six decades"], "entity_types": ["Viewers' Rating", "Genre", "Actor", "Year"]}
{"sentence": "what history movie starred eva larue and got good reviews", "entity_names": ["history", "eva larue", "good"], "entity_types": ["Genre", "Actor", "Viewers' Rating"]}
{"sentence": "what history movies in the past eight decades starred george carlin", "entity_names": ["history", "past eight decades", "george carlin"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "what info can i get about music of the heart", "entity_names": ["music of the heart"], "entity_types": ["Title"]}
{"sentence": "what is abes tomb", "entity_names": ["abes tomb"], "entity_types": ["Title"]}
{"sentence": "what is annihilation earth about", "entity_names": ["annihilation earth"], "entity_types": ["Title"]}
{"sentence": "what is curse of the swamp creature about", "entity_names": ["curse of the swamp creature"], "entity_types": ["Title"]}
{"sentence": "what is domino about", "entity_names": ["domino"], "entity_types": ["Title"]}
{"sentence": "what is eugene about", "entity_names": ["eugene"], "entity_types": ["Title"]}
{"sentence": "what is hellhounds about", "entity_names": ["hellhounds"], "entity_types": ["Title"]}
{"sentence": "what is horrors of the black museum", "entity_names": ["horrors of the black museum"], "entity_types": ["Title"]}
{"sentence": "what is in the electric mist", "entity_names": ["in the electric mist"], "entity_types": ["Title"]}
{"sentence": "what is lord of the flies about", "entity_names": ["lord of the flies"], "entity_types": ["Title"]}
{"sentence": "what is matt damons highest rated movie", "entity_names": ["matt damons"], "entity_types": ["Actor"]}
{"sentence": "what is the godfather part iii about", "entity_names": ["the godfather part iii"], "entity_types": ["Title"]}
{"sentence": "what is the governess about", "entity_names": ["the governess"], "entity_types": ["Title"]}
{"sentence": "what is the rock about", "entity_names": ["the rock"], "entity_types": ["Title"]}
{"sentence": "what is the sensei about", "entity_names": ["the sensei"], "entity_types": ["Title"]}
{"sentence": "what is the first 20 million is always the hardest about", "entity_names": ["the first 20 million is always the hardest"], "entity_types": ["Title"]}
{"sentence": "what is wall e about", "entity_names": ["wall e"], "entity_types": ["Title"]}
{"sentence": "what is welcome to the rileys about", "entity_names": ["welcome to the rileys"], "entity_types": ["Title"]}
{"sentence": "what is a 1940 nc 17 short movie with a average rating of five directed by david vonallmen", "entity_names": ["1940", "nc 17", "short", "five", "david vonallmen"], "entity_types": ["Year", "MPAA Rating", "Genre", "Viewers' Rating", "Director"]}
{"sentence": "what is a 1950 romance movie starring glenn close", "entity_names": ["1950", "romance", "glenn close"], "entity_types": ["Year", "Genre", "Actor"]}
{"sentence": "what is a 1950 s family film directed by jeff betancourt", "entity_names": ["1950 s", "family", "jeff betancourt"], "entity_types": ["Year", "Genre", "Director"]}
{"sentence": "what is a 1950 s sports themed movie that has a four stars rating", "entity_names": ["1950 s", "sports", "four stars"], "entity_types": ["Year", "Genre", "Viewers' Rating"]}
{"sentence": "what is a 1990 rated r film staring randolph mantooth", "entity_names": ["1990", "r", "randolph mantooth"], "entity_types": ["Year", "MPAA Rating", "Actor"]}
{"sentence": "what is a disney movie about a runaway that was directed by brian burns in 1980", "entity_names": ["disney", "runaway", "brian burns", "1980"], "entity_types": ["Genre", "Plot", "Director", "Year"]}
{"sentence": "what is a g rated movie released in the past four years about concentration camps", "entity_names": ["g", "past four years", "concentration camps"], "entity_types": ["MPAA Rating", "Year", "Plot"]}
{"sentence": "what is a pg thriller rated seven stars directed by hironobu sakaguchi", "entity_names": ["pg", "thriller", "seven stars", "hironobu sakaguchi"], "entity_types": ["MPAA Rating", "Genre", "Viewers' Rating", "Director"]}
{"sentence": "what is a pg 13 quenton tarantino movie made in 1970 featuring rejection", "entity_names": ["pg 13", "quenton tarantino", "1970", "rejection"], "entity_types": ["MPAA Rating", "Actor", "Year", "Plot"]}
{"sentence": "what is a pg 13 tracey nelson history movie with good rating from the 1970 s", "entity_names": ["pg 13", "tracey nelson", "history", "good rating", "1970 s"], "entity_types": ["MPAA Rating", "Actor", "Genre", "Viewers' Rating", "Year"]}
{"sentence": "what is a pg 13 adventure with a car chase in the past two decades", "entity_names": ["pg 13", "adventure", "car chase", "past two decades"], "entity_types": ["MPAA Rating", "Genre", "Plot", "Year"]}
{"sentence": "what is a sci fi film directed by jorge solis about a war against machines that received two thumbs up", "entity_names": ["sci fi", "jorge solis", "war against machines", "two thumbs up"], "entity_types": ["Genre", "Director", "Plot", "Viewers' Rating"]}
{"sentence": "what is a decent pg science fiction movie that came out in the last four years and stars tim robbins", "entity_names": ["decent", "pg", "science fiction", "last four years", "tim robbins"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre", "Year", "Actor"]}
{"sentence": "what is a decent movie that stars don johnson", "entity_names": ["decent", "don johnson"], "entity_types": ["Viewers' Rating", "Actor"]}
{"sentence": "what is a emotional marty belafsky film about illness that was rated r and came out in 1960", "entity_names": ["emotional", "marty belafsky", "illness", "r", "1960"], "entity_types": ["Genre", "Actor", "Plot", "MPAA Rating", "Year"]}
{"sentence": "what is a four star nc 17 movie involving witches starring kate jackson", "entity_names": ["four star", "nc 17", "witches", "kate jackson"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Plot", "Actor"]}
{"sentence": "what is a four star revenge spaghetti western film directed by graham reznick in the last three decades", "entity_names": ["four star", "revenge", "spaghetti western", "graham reznick", "last three decades"], "entity_types": ["Viewers' Rating", "Plot", "Genre", "Director", "Year"]}
{"sentence": "what is a funny pg movie on vulgarity that came out in the last ten years", "entity_names": ["funny", "pg", "vulgarity", "last ten years"], "entity_types": ["Genre", "MPAA Rating", "Plot", "Year"]}
{"sentence": "what is a g rated film animation film that was released within the past six years and has a ratings average of six stars", "entity_names": ["g", "animation", "past six years", "six stars"], "entity_types": ["MPAA Rating", "Genre", "Year", "Viewers' Rating"]}
{"sentence": "what is a german army documentary directed by adam tierney", "entity_names": ["german army", "documentary", "adam tierney"], "entity_types": ["Plot", "Genre", "Director"]}
{"sentence": "what is a good 1950 animation movie with actor rudolf valentino and thats rated pg 13", "entity_names": ["1950", "animation", "rudolf valentino", "pg 13"], "entity_types": ["Year", "Genre", "Actor", "MPAA Rating"]}
{"sentence": "what is a good 1960 s history movie rated pg", "entity_names": ["1960 s", "history", "pg"], "entity_types": ["Year", "Genre", "MPAA Rating"]}
{"sentence": "what is a good 1990 s romance movie starring kelsy grammer", "entity_names": ["1990 s", "romance", "kelsy grammer"], "entity_types": ["Year", "Genre", "Actor"]}
{"sentence": "what is a good g rated biography film released within the past eight years", "entity_names": ["g", "biography", "past eight years"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "what is a good pg 13 science fiction film that michael kastenbaum directed in the last eight decades", "entity_names": ["pg 13", "science fiction", "michael kastenbaum", "last eight decades"], "entity_types": ["MPAA Rating", "Genre", "Director", "Year"]}
{"sentence": "what is a good children movie with humphrey bogart in it", "entity_names": ["children", "humphrey bogart"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what is a good comedy from the 1990 s", "entity_names": ["comedy", "1990 s"], "entity_types": ["Genre", "Year"]}
{"sentence": "what is a good comedy that was made in the 1970 s besides caddyshack", "entity_names": ["comedy", "1970 s"], "entity_types": ["Genre", "Year"]}
{"sentence": "what is a good mockumentary", "entity_names": ["mockumentary"], "entity_types": ["Genre"]}
{"sentence": "what is a good movie about the military that has an r rating", "entity_names": ["military", "r"], "entity_types": ["Genre", "MPAA Rating"]}
{"sentence": "what is a good movie starring andy garcia released within the past six decades", "entity_names": ["andy garcia", "past six decades"], "entity_types": ["Actor", "Year"]}
{"sentence": "what is a good thriller that is rated r", "entity_names": ["thriller", "r"], "entity_types": ["Genre", "MPAA Rating"]}
{"sentence": "what is a good war movie released within the past four decades", "entity_names": ["war", "past four decades"], "entity_types": ["Genre", "Year"]}
{"sentence": "what is a highly recommended r rated film with a found dead plot", "entity_names": ["highly recommended", "r", "found dead"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Plot"]}
{"sentence": "what is a history film that stars melanie griffith with a five star rating", "entity_names": ["history", "melanie griffith", "five star"], "entity_types": ["Genre", "Actor", "Viewers' Rating"]}
{"sentence": "what is a history movie starring tommy lee jones", "entity_names": ["history", "tommy lee jones"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what is a horror film from the past three years directed by gerson sanginitto", "entity_names": ["horror", "past three years", "gerson sanginitto"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "what is a movie about decisions with at least an average rating", "entity_names": ["decisions", "average"], "entity_types": ["Plot", "Viewers' Rating"]}
{"sentence": "what is a movie withing the last three years with a six star rating about an arrest starring jeri ryan", "entity_names": ["last three years", "six star", "arrest", "jeri ryan"], "entity_types": ["Year", "Viewers' Rating", "Plot", "Actor"]}
{"sentence": "what is a must see 1950 s movie about police", "entity_names": ["must see", "1950 s", "police"], "entity_types": ["Viewers' Rating", "Year", "Genre"]}
{"sentence": "what is a must see pg short film directed by jonathan demme", "entity_names": ["must see", "pg", "short", "jonathan demme"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre", "Director"]}
{"sentence": "what is a rated r fantasy movie with haley mills", "entity_names": ["r", "fantasy", "haley mills"], "entity_types": ["MPAA Rating", "Genre", "Actor"]}
{"sentence": "what is a really good pg 13 science fiction movie that was directed by franco steffanino", "entity_names": ["really good", "pg 13", "science fiction", "franco steffanino"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre", "Director"]}
{"sentence": "what is a really good r rated sci fi movie", "entity_names": ["really good", "r", "sci fi"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre"]}
{"sentence": "what is a really good action movie from 1970 with a pg 13 rating", "entity_names": ["really good", "action", "1970", "pg 13"], "entity_types": ["Viewers' Rating", "Genre", "Year", "MPAA Rating"]}
{"sentence": "what is a really good biography", "entity_names": ["really good", "biography"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "what is a really good scary movie about an axe murder", "entity_names": ["really good", "scary", "axe murder"], "entity_types": ["Viewers' Rating", "Genre", "Plot"]}
{"sentence": "what is a science fiction film starring roma downey that is rated pg 13 and released during the 1950 s", "entity_names": ["science fiction", "roma downey", "pg 13", "1950 s"], "entity_types": ["Genre", "Actor", "MPAA Rating", "Year"]}
{"sentence": "what is a sport movie directed by susan seidelman in the last ten decades", "entity_names": ["sport", "susan seidelman", "last ten decades"], "entity_types": ["Genre", "Director", "Year"]}
{"sentence": "what is a summary of the plot of the shining", "entity_names": ["the shining"], "entity_types": ["Title"]}
{"sentence": "what is a summary of the plot of the movie wall e", "entity_names": ["wall e"], "entity_types": ["Title"]}
{"sentence": "what is a trickery movie with excellent ratings", "entity_names": ["trickery", "excellent ratings"], "entity_types": ["Plot", "Viewers' Rating"]}
{"sentence": "what is a very good western film in the past seven decades", "entity_names": ["very good", "western", "past seven decades"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "what is a very popular pg 13 sci fi movie", "entity_names": ["very popular", "pg 13", "sci fi"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre"]}
{"sentence": "what is a very popular r rated la monte edwards film that was made in 1960 and took place in los angeles", "entity_names": ["very popular", "r", "la monte edwards", "1960", "los angeles"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Director", "Year", "Plot"]}
{"sentence": "what is a watchable nc 17 1980 s sci fi movie with rel hunt in it", "entity_names": ["watchable", "nc 17", "1980 s", "sci fi", "rel hunt"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Year", "Genre", "Actor"]}
{"sentence": "what is a watchable pg 13 suspense film directed by ward powers than features a dead child and was made in the past eight decades", "entity_names": ["watchable", "pg 13", "suspense", "ward powers", "dead child", "past eight decades"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre", "Director", "Plot", "Year"]}
{"sentence": "what is a well rated action movie about prisoners", "entity_names": ["well rated", "action", "prisoners"], "entity_types": ["Viewers' Rating", "Genre", "Plot"]}
{"sentence": "what is a well rated comedy with courtney cox", "entity_names": ["well rated", "comedy", "courtney cox"], "entity_types": ["Viewers' Rating", "Genre", "Actor"]}
{"sentence": "what is a western movie from the past ten decades that is directed by rob carpenter", "entity_names": ["western", "past ten decades", "rob carpenter"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "what is an all right movie from 1970 that is about a rise to power", "entity_names": ["all right", "1970", "rise to power"], "entity_types": ["Viewers' Rating", "Year", "Plot"]}
{"sentence": "what is an animated film that habib azar directed", "entity_names": ["animated", "habib azar"], "entity_types": ["Genre", "Director"]}
{"sentence": "what is an animated movie with a five star rating", "entity_names": ["animated", "five star"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "what is an average movie of the last ten decades about a gold miner", "entity_names": ["average", "last ten decades", "gold miner"], "entity_types": ["Viewers' Rating", "Year", "Plot"]}
{"sentence": "what is an entertaining tracey moffatt film that has come out in the past year", "entity_names": ["entertaining", "tracey moffatt", "past year"], "entity_types": ["Genre", "Director", "Year"]}
{"sentence": "what is an unrated sport movie with robert picardo", "entity_names": ["unrated", "sport", "robert picardo"], "entity_types": ["MPAA Rating", "Genre", "Actor"]}
{"sentence": "what is some high rated thrillers", "entity_names": ["thrillers"], "entity_types": ["Genre"]}
{"sentence": "what is that six star chick flick with andie macdowell in it", "entity_names": ["six", "chick", "andie macdowell"], "entity_types": ["Viewers' Rating", "Genre", "Actor"]}
{"sentence": "what is the 1970 unrated movie with excellent ratings starring faye dunaway about unrequited love", "entity_names": ["1970", "unrated", "excellent ratings", "faye dunaway", "unrequited love"], "entity_types": ["Year", "MPAA Rating", "Viewers' Rating", "Actor", "Plot"]}
{"sentence": "what is the city of god about", "entity_names": ["city of god"], "entity_types": ["Title"]}
{"sentence": "what is the domino effect about", "entity_names": ["domino effect"], "entity_types": ["Title"]}
{"sentence": "what is the fists of fury", "entity_names": ["fists of fury"], "entity_types": ["Title"]}
{"sentence": "what is the best comedy that was made in the 1970 s", "entity_names": ["comedy", "1970 s"], "entity_types": ["Genre", "Year"]}
{"sentence": "what is the best horror movies made in 1977", "entity_names": ["horror"], "entity_types": ["Genre"]}
{"sentence": "what is the best mockumentary", "entity_names": ["mockumentary"], "entity_types": ["Genre"]}
{"sentence": "what is the best movie starring daniela pestova", "entity_names": ["daniela pestova"], "entity_types": ["Actor"]}
{"sentence": "what is the best romance movie made in the 2010 s", "entity_names": ["romance", "2010 s"], "entity_types": ["Genre", "Year"]}
{"sentence": "what is the highest rated movie that charlie chaplin was in", "entity_names": ["charlie chaplin"], "entity_types": ["Actor"]}
{"sentence": "what is the highest rated thriller that alfred hitchcock directed", "entity_names": ["thriller", "alfred hitchcock"], "entity_types": ["Genre", "Director"]}
{"sentence": "what is the last movie did roman polanski directed", "entity_names": ["roman polanski"], "entity_types": ["Director"]}
{"sentence": "what is the last rated r fantasy movie that came out", "entity_names": ["r", "fantasy"], "entity_types": ["MPAA Rating", "Genre"]}
{"sentence": "what is the last science fiction film that was rated r and directed by hayao miyazaki", "entity_names": ["science fiction", "r", "hayao miyazaki"], "entity_types": ["Genre", "MPAA Rating", "Director"]}
{"sentence": "what is the last science fiction movie that charlize theron was in is is aeon flux", "entity_names": ["science fiction", "charlize theron"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what is the last science fiction movie that george lucas directed", "entity_names": ["science fiction", "george lucas"], "entity_types": ["Genre", "Director"]}
{"sentence": "what is the last teen movie that john huston directed", "entity_names": ["teen", "john huston"], "entity_types": ["Genre", "Director"]}
{"sentence": "what is the last war movie that came out about nazi germany", "entity_names": ["war", "nazi germany"], "entity_types": ["Genre", "Plot"]}
{"sentence": "what is the last war movie that received two thumbs up about a general", "entity_names": ["war", "two thumbs up", "general"], "entity_types": ["Genre", "Viewers' Rating", "Plot"]}
{"sentence": "what is the latest movie starring sam shepard", "entity_names": ["sam shepard"], "entity_types": ["Actor"]}
{"sentence": "what is the mockumentary by yann samuell", "entity_names": ["mockumentary", "yann samuell"], "entity_types": ["Genre", "Director"]}
{"sentence": "what is the most recent western to get five stars and above", "entity_names": ["western", "five stars and above"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "what is the most well known oliver stone mystery movie", "entity_names": ["oliver stone", "mystery"], "entity_types": ["Director", "Genre"]}
{"sentence": "what is the movie angel camouflaged about", "entity_names": ["angel camouflaged"], "entity_types": ["Title"]}
{"sentence": "what is the movie bitten", "entity_names": ["bitten"], "entity_types": ["Title"]}
{"sentence": "what is the movie easy a about", "entity_names": ["easy a"], "entity_types": ["Title"]}
{"sentence": "what is the movie phantasm ii about", "entity_names": ["phantasm ii"], "entity_types": ["Title"]}
{"sentence": "what is the movie primer about", "entity_names": ["primer"], "entity_types": ["Title"]}
{"sentence": "what is the movie ripped off madoff and the scamming of america about", "entity_names": ["ripped off madoff and the scamming of america"], "entity_types": ["Title"]}
{"sentence": "what is the movie the shunning about", "entity_names": ["the shunning"], "entity_types": ["Title"]}
{"sentence": "what is the movie from 1970 starring jensen ackles", "entity_names": ["1970", "jensen ackles"], "entity_types": ["Year", "Actor"]}
{"sentence": "what is the movie with a seven star rating from 2000 starring jennifer connelly with a pg 13 rating", "entity_names": ["seven star", "2000", "jennifer connelly", "pg 13"], "entity_types": ["Viewers' Rating", "Year", "Actor", "MPAA Rating"]}
{"sentence": "what is the musical remake of the vampire movie that roman polanski directed and is it a g rating", "entity_names": ["musical", "roman polanski", "g"], "entity_types": ["Genre", "Director", "MPAA Rating"]}
{"sentence": "what is the name of r rated gangster movie from 1940 that matthew lawrence was in", "entity_names": ["r", "gangster", "1940", "matthew lawrence"], "entity_types": ["MPAA Rating", "Genre", "Year", "Actor"]}
{"sentence": "what is the name of a pg rated movie thriller that has stabbing in it", "entity_names": ["pg", "thriller", "stabbing"], "entity_types": ["MPAA Rating", "Genre", "Plot"]}
{"sentence": "what is the name of a movie for children directed by robert zemeckis", "entity_names": ["children", "robert zemeckis"], "entity_types": ["Genre", "Director"]}
{"sentence": "what is the name of a very popular mystery movie starring roger howarth", "entity_names": ["very popular", "mystery", "roger howarth"], "entity_types": ["Viewers' Rating", "Genre", "Actor"]}
{"sentence": "what is the name of an animated film from the 1990 s directed by fritz lang", "entity_names": ["animated", "1990 s", "fritz lang"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "what is the name of that comedy movie series about police", "entity_names": ["police"], "entity_types": ["Genre"]}
{"sentence": "what is the name of the barbara stanwyck movie about a murderer it was made within the last year", "entity_names": ["barbara stanwyck", "murderer", "last year"], "entity_types": ["Actor", "Plot", "Year"]}
{"sentence": "what is the name of the g rated animated movie about a hyena directed by shunji iwai", "entity_names": ["g", "animated", "hyena", "shunji iwai"], "entity_types": ["MPAA Rating", "Genre", "Plot", "Director"]}
{"sentence": "what is the name of the pg biography directed by steven goldmann", "entity_names": ["pg", "biography", "steven goldmann"], "entity_types": ["MPAA Rating", "Genre", "Director"]}
{"sentence": "what is the name of the pg movie about a president directed by stoney sharp that was liked by many", "entity_names": ["pg", "president", "stoney sharp", "liked by many"], "entity_types": ["MPAA Rating", "Plot", "Director", "Viewers' Rating"]}
{"sentence": "what is the name of the robert duvall biography with a pg 13 rating and an average review of eight", "entity_names": ["robert duvall", "biography", "pg 13", "eight"], "entity_types": ["Actor", "Genre", "MPAA Rating", "Viewers' Rating"]}
{"sentence": "what is the name of the crime film from 1940 directed by don siegel", "entity_names": ["crime", "1940", "don siegel"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "what is the name of the psychological movie about a lawyer starring david mccallum", "entity_names": ["psychological", "lawyer", "david mccallum"], "entity_types": ["Genre", "Plot", "Actor"]}
{"sentence": "what is the name of the six stars animation starring jamie lee curtis about a barbie doll", "entity_names": ["six stars", "animation", "jamie lee curtis", "barbie doll"], "entity_types": ["Viewers' Rating", "Genre", "Actor", "Plot"]}
{"sentence": "what is the name of the unrated military movie about d day released in 2010 directed by peter kuran", "entity_names": ["unrated", "military", "d day", "2010", "peter kuran"], "entity_types": ["MPAA Rating", "Genre", "Plot", "Year", "Director"]}
{"sentence": "what is the name of the year 2000 movie starring richard chamberlain about body snatching and that received nine stars out of ten", "entity_names": ["2000", "richard chamberlain", "body snatching", "nine stars"], "entity_types": ["Year", "Actor", "Plot", "Viewers' Rating"]}
{"sentence": "what is the plot for the shining", "entity_names": ["the shining"], "entity_types": ["Title"]}
{"sentence": "what is the plot of christmas nightmare", "entity_names": ["christmas nightmare"], "entity_types": ["Title"]}
{"sentence": "what is the plot of the film titled dakota", "entity_names": ["dakota"], "entity_types": ["Title"]}
{"sentence": "what is the plot of the film titled death sentence", "entity_names": ["death sentence"], "entity_types": ["Title"]}
{"sentence": "what is the plot of the movie us now", "entity_names": ["us now"], "entity_types": ["Title"]}
{"sentence": "what is the plot of the movie titled navy seals", "entity_names": ["navy seals"], "entity_types": ["Title"]}
{"sentence": "what is the rated r movie that marleen gorris directed with the emotional decision aspect to it receiving an average rating", "entity_names": ["r", "marleen gorris", "emotional", "decision", "average"], "entity_types": ["MPAA Rating", "Director", "Genre", "Plot", "Viewers' Rating"]}
{"sentence": "what is the title of a documentary starring lynda carter", "entity_names": ["documentary", "lynda carter"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what is the title of a fantasy storytelling movie rated r that was released in the past two years and directed by patrick pierre", "entity_names": ["fantasy", "storytelling", "r", "past two years", "patrick pierre"], "entity_types": ["Genre", "Plot", "MPAA Rating", "Year", "Director"]}
{"sentence": "what is the title of a highly recommended 1990 mystery directed by elizabeth harrison with only a pg rating", "entity_names": ["highly recommended", "1990", "mystery", "elizabeth harrison", "pg"], "entity_types": ["Viewers' Rating", "Year", "Genre", "Director", "MPAA Rating"]}
{"sentence": "what is the title of a movie about a dog starring corey feldman that had a rating of nine", "entity_names": ["dog", "corey feldman", "nine"], "entity_types": ["Plot", "Actor", "Viewers' Rating"]}
{"sentence": "what is the title of a movie about redemption that was released this year and directed by pitof", "entity_names": ["redemption", "this year", "pitof"], "entity_types": ["Plot", "Year", "Director"]}
{"sentence": "what is the title of a movie starring ciarn hinds from 2010 that was rated r", "entity_names": ["ciarn hinds", "2010", "r"], "entity_types": ["Actor", "Year", "MPAA Rating"]}
{"sentence": "what is the title of an animated film by tim burton", "entity_names": ["animated", "tim burton"], "entity_types": ["Genre", "Director"]}
{"sentence": "what is the title of the jim byrnes film that received nine stars in 1980", "entity_names": ["jim byrnes", "nine stars", "1980"], "entity_types": ["Actor", "Viewers' Rating", "Year"]}
{"sentence": "what is the title of the pg 13 fantasy movie with mark paul gosselar about a dark hero that came out in the last ten years", "entity_names": ["pg 13", "fantasy", "mark paul gosselar", "dark hero", "last ten years"], "entity_types": ["MPAA Rating", "Genre", "Actor", "Plot", "Year"]}
{"sentence": "what is the title of the mystery film with paul reiser that was rated an eight", "entity_names": ["mystery", "paul reiser", "eight"], "entity_types": ["Genre", "Actor", "Viewers' Rating"]}
{"sentence": "what is the title of the sci fi pg film with robin shou that came out in the past ten decades", "entity_names": ["sci fi", "pg", "robin shou", "past ten decades"], "entity_types": ["Genre", "MPAA Rating", "Actor", "Year"]}
{"sentence": "what is your favorite pg 13 romance of the 1980 s", "entity_names": ["pg 13", "romance", "1980 s"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "what is your favorite scary movie about evil", "entity_names": ["evil"], "entity_types": ["Plot"]}
{"sentence": "what is your favorite highly liked mystery from the past seven decades", "entity_names": ["highly liked", "mystery", "past seven decades"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "what kid movie about a fight that came out in the last eight decades and starred martin landau", "entity_names": ["kid", "fight", "last eight decades", "martin landau"], "entity_types": ["Genre", "Plot", "Year", "Actor"]}
{"sentence": "what kid movie about man versus machine wad directed by robert pratten", "entity_names": ["kid", "man versus machine", "robert pratten"], "entity_types": ["Genre", "Plot", "Director"]}
{"sentence": "what liked by many people 1950 s war movie had paul champion as the director", "entity_names": ["liked by many", "1950 s", "war", "paul champion"], "entity_types": ["Viewers' Rating", "Year", "Genre", "Director"]}
{"sentence": "what mediocre movies was peter wingfield in", "entity_names": ["mediocre", "peter wingfield"], "entity_types": ["Viewers' Rating", "Actor"]}
{"sentence": "what mockumentary about politics that was rated pg 13 received five stars and above", "entity_names": ["mockumentary", "politics", "pg 13", "five stars and above"], "entity_types": ["Genre", "Plot", "MPAA Rating", "Viewers' Rating"]}
{"sentence": "what month in 2010 did the most thriller movies come out", "entity_names": ["2010", "thriller"], "entity_types": ["Year", "Genre"]}
{"sentence": "what movie came out in the last four years and is about new york", "entity_names": ["last four years", "new york"], "entity_types": ["Year", "Plot"]}
{"sentence": "what movie could be classified as a mystery", "entity_names": ["mystery"], "entity_types": ["Genre"]}
{"sentence": "what movie did george lucas direct in the 1980 s that is a comedy and rated pg 13", "entity_names": ["george lucas", "1980 s", "comedy", "pg 13"], "entity_types": ["Director", "Year", "Genre", "MPAA Rating"]}
{"sentence": "what movie does kenneth branagh star in that was made in the 1940 s with a megacorporation plot that was liked by many", "entity_names": ["kenneth branagh", "1940 s", "megacorporation", "liked by many"], "entity_types": ["Actor", "Year", "Plot", "Viewers' Rating"]}
{"sentence": "what movie from 1980 stars donnie walberg", "entity_names": ["1980", "donnie walberg"], "entity_types": ["Year", "Actor"]}
{"sentence": "what movie has cicely tyson been in the past five years", "entity_names": ["cicely tyson", "past five years"], "entity_types": ["Actor", "Year"]}
{"sentence": "what movie involved larry hagman in a galactic war in the 1990 s", "entity_names": ["larry hagman", "galactic war", "1990 s"], "entity_types": ["Director", "Plot", "Year"]}
{"sentence": "what movie is known as oliver stones best work", "entity_names": ["oliver stones"], "entity_types": ["Director"]}
{"sentence": "what movie is rated g that is based on a career starring luke perry in the last year", "entity_names": ["g", "career", "luke perry", "last year"], "entity_types": ["MPAA Rating", "Plot", "Actor", "Year"]}
{"sentence": "what movie stared terry farrell as a cowboy in mexico", "entity_names": ["terry farrell", "cowboy", "mexico"], "entity_types": ["Actor", "Genre", "Plot"]}
{"sentence": "what movie starred susan lucci in the musical genre and had an average rating of six", "entity_names": ["susan lucci", "musical", "six"], "entity_types": ["Actor", "Genre", "Viewers' Rating"]}
{"sentence": "what movie stars albert finney and is a fantasy", "entity_names": ["albert finney", "fantasy"], "entity_types": ["Actor", "Genre"]}
{"sentence": "what movies about a merit badge are directed by toshiyuki kubooka", "entity_names": ["merit badge", "toshiyuki kubooka"], "entity_types": ["Plot", "Director"]}
{"sentence": "what movies did steve mcqueen have a role in", "entity_names": ["steve mcqueen"], "entity_types": ["Actor"]}
{"sentence": "what movies has david lean directed", "entity_names": ["david lean"], "entity_types": ["Director"]}
{"sentence": "what movies has edward woodward been in", "entity_names": ["edward woodward"], "entity_types": ["Actor"]}
{"sentence": "what movies has fritz lang directed", "entity_names": ["fritz lang"], "entity_types": ["Director"]}
{"sentence": "what movies has phillip seymour hoffman been in", "entity_names": ["phillip seymour hoffman"], "entity_types": ["Actor"]}
{"sentence": "what movies have starred john malkovic in the past four years", "entity_names": ["john malkovic", "past four years"], "entity_types": ["Actor", "Year"]}
{"sentence": "what movies involving james marsters and the holocaust filed in 1950 were unrated", "entity_names": ["james marsters", "holocaust", "1950", "unrated"], "entity_types": ["Actor", "Plot", "Year", "MPAA Rating"]}
{"sentence": "what movies is james cagney in", "entity_names": ["james cagney"], "entity_types": ["Actor"]}
{"sentence": "what movies star rose mcgowan and are from the 1980 s", "entity_names": ["rose mcgowan", "1980 s"], "entity_types": ["Actor", "Year"]}
{"sentence": "what movies star sarah brown that have good ratings", "entity_names": ["sarah brown", "good"], "entity_types": ["Actor", "Viewers' Rating"]}
{"sentence": "what movies starring gabriel byrne in the last eight decades got mediocre ratings", "entity_names": ["gabriel byrne", "last eight decades", "mediocre"], "entity_types": ["Actor", "Year", "Viewers' Rating"]}
{"sentence": "what movies that are about the city did martin gero direct", "entity_names": ["city", "martin gero"], "entity_types": ["Plot", "Director"]}
{"sentence": "what movies was david hyde pierce in", "entity_names": ["david hyde pierce"], "entity_types": ["Actor"]}
{"sentence": "what movies with danielle mccormack were highly recommended", "entity_names": ["danielle mccormack", "highly recommended"], "entity_types": ["Actor", "Viewers' Rating"]}
{"sentence": "what musicals with a pg rating have played in the past three decades", "entity_names": ["musicals", "pg", "past three decades"], "entity_types": ["Genre", "MPAA Rating", "Year"]}
{"sentence": "what must see r rated horror movie was directed by john hillcoat", "entity_names": ["must see", "r", "horror", "john hillcoat"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre", "Director"]}
{"sentence": "what must see battle movie with angelina jolie was given in r rating in 1980", "entity_names": ["must see", "battle", "angelina jolie", "r", "1980"], "entity_types": ["Viewers' Rating", "Plot", "Actor", "MPAA Rating", "Year"]}
{"sentence": "what mystery movie starred henry jaglom and is rated pg", "entity_names": ["mystery", "henry jaglom", "pg"], "entity_types": ["Genre", "Director", "MPAA Rating"]}
{"sentence": "what mystery starring jane seymour is about lies", "entity_names": ["mystery", "jane seymour", "lies"], "entity_types": ["Genre", "Actor", "Plot"]}
{"sentence": "what nuclear war movies are rated pg 13", "entity_names": ["nuclear war", "pg 13"], "entity_types": ["Plot", "MPAA Rating"]}
{"sentence": "what pollution mockumentary in the last ten years was directed by stephan elliott and rated pg 13", "entity_names": ["pollution", "mockumentary", "last ten years", "stephan elliott", "pg 13"], "entity_types": ["Plot", "Genre", "Year", "Director", "MPAA Rating"]}
{"sentence": "what prison movies have at least a seven star rating", "entity_names": ["prison", "seven star"], "entity_types": ["Plot", "Viewers' Rating"]}
{"sentence": "what qualifies a movie to be in the musical genre", "entity_names": ["musical"], "entity_types": ["Genre"]}
{"sentence": "what qualifies a movie to be in the sci fi genre", "entity_names": ["sci fi"], "entity_types": ["Genre"]}
{"sentence": "what rated r action movie starring tracy scoggins was based on forbidden love in the 1950 s and got excellent ratings", "entity_names": ["r", "action", "tracy scoggins", "forbidden love", "1950 s", "excellent ratings"], "entity_types": ["MPAA Rating", "Genre", "Actor", "Plot", "Year", "Viewers' Rating"]}
{"sentence": "what rated r horror is there involving a shower murder", "entity_names": ["r", "horror", "shower murder"], "entity_types": ["MPAA Rating", "Genre", "Plot"]}
{"sentence": "what rated r mockumentary was jeremy brett in", "entity_names": ["r", "mockumentary", "jeremy brett"], "entity_types": ["MPAA Rating", "Genre", "Actor"]}
{"sentence": "what rated r movie directed by edward zwick was based on the post world war one era", "entity_names": ["r", "edward zwick", "post world war one"], "entity_types": ["MPAA Rating", "Director", "Plot"]}
{"sentence": "what rated r movie has gary david goldberg directed about satire in the past seven years", "entity_names": ["r", "gary david goldberg", "satire", "past seven years"], "entity_types": ["MPAA Rating", "Director", "Plot", "Year"]}
{"sentence": "what rating is the film looney tunes robin hood daffy", "entity_names": ["looney tunes robin hood daffy"], "entity_types": ["Title"]}
{"sentence": "what rating is the movie busting", "entity_names": ["busting"], "entity_types": ["Title"]}
{"sentence": "what really good family movie came out in the last three years", "entity_names": ["really good", "family", "last three years"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "what recent action movies received seven stars and above", "entity_names": ["action", "seven stars and above"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "what scary movies about a mad scientist have been made in the last five decades", "entity_names": ["scary", "mad scientist", "last five decades"], "entity_types": ["Genre", "Plot", "Year"]}
{"sentence": "what scary movies have sylvain chomet directed", "entity_names": ["scary", "sylvain chomet"], "entity_types": ["Genre", "Director"]}
{"sentence": "what sci fi film is rated g starring john lithgow", "entity_names": ["sci fi", "g", "john lithgow"], "entity_types": ["Genre", "MPAA Rating", "Actor"]}
{"sentence": "what sci fi movie did carol burnette star in during the last decade", "entity_names": ["sci fi", "carol burnette", "last decade"], "entity_types": ["Genre", "Actor", "Year"]}
{"sentence": "what sci fi films received nine stars and above that were made in the 1970 s", "entity_names": ["sci fi", "nine stars and above", "1970 s"], "entity_types": ["Genre", "Viewers' Rating", "Year"]}
{"sentence": "what science fiction film made within the past six years featured kate winslet and received four stars", "entity_names": ["science fiction", "past six years", "kate winslet", "four stars"], "entity_types": ["Genre", "Year", "Actor", "Viewers' Rating"]}
{"sentence": "what science fiction films did j randolph harrison direct from 1970", "entity_names": ["science fiction", "j randolph harrison", "1970"], "entity_types": ["Genre", "Director", "Year"]}
{"sentence": "what science fiction movie of the past ten decades starred vincent gallo", "entity_names": ["science fiction", "past ten decades", "vincent gallo"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "what seven star rated 1970 s movie starred adrian paul and was about sports", "entity_names": ["seven star", "1970 s", "adrian paul", "sports"], "entity_types": ["Viewers' Rating", "Year", "Actor", "Genre"]}
{"sentence": "what short films by bob gray received excellent ratings", "entity_names": ["short", "bob gray", "excellent ratings"], "entity_types": ["Genre", "Director", "Viewers' Rating"]}
{"sentence": "what spaghetti western was directed by randall cole based on a ranch with an average rating of nine", "entity_names": ["spaghetti western", "randall cole", "ranch", "nine"], "entity_types": ["Genre", "Director", "Plot", "Viewers' Rating"]}
{"sentence": "what sport did martin donovan play in for the past four decades", "entity_names": ["sport", "martin donovan", "past four decades"], "entity_types": ["Genre", "Actor", "Year"]}
{"sentence": "what sport movie was released in 1990 that was directed by ian david diaz", "entity_names": ["sport", "1990", "ian david diaz"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "what the name of the very popular first love movie from lisa wolfinger in 2010", "entity_names": ["very popular", "first love", "lisa wolfinger", "2010"], "entity_types": ["Viewers' Rating", "Plot", "Director", "Year"]}
{"sentence": "what thriller did blaxwell smart direct that got very good ratings", "entity_names": ["thriller", "blaxwell smart", "very good"], "entity_types": ["Genre", "Director", "Viewers' Rating"]}
{"sentence": "what thriller film did best at the box office in 2011", "entity_names": ["thriller"], "entity_types": ["Genre"]}
{"sentence": "what unrated film noir movie that was liked by many and came out in the last ten years and was directed by perce pearce", "entity_names": ["unrated", "film noir", "liked by many", "last ten years", "perce pearce"], "entity_types": ["MPAA Rating", "Genre", "Viewers' Rating", "Year", "Director"]}
{"sentence": "what unrated movie came out in 2000 and stars james van der beek", "entity_names": ["unrated", "2000", "james van der beek"], "entity_types": ["MPAA Rating", "Year", "Actor"]}
{"sentence": "what unrated movies has lena horne acted in within the last six decades", "entity_names": ["unrated", "lena horne", "last six decades"], "entity_types": ["MPAA Rating", "Actor", "Year"]}
{"sentence": "what urban legend movies did martin barnewitz direct", "entity_names": ["urban legend", "martin barnewitz"], "entity_types": ["Plot", "Director"]}
{"sentence": "what warsaw poland movie directed by carl bessai received two thumbs up", "entity_names": ["warsaw poland", "carl bessai", "two thumbs up"], "entity_types": ["Plot", "Director", "Viewers' Rating"]}
{"sentence": "what was john wanyes most famous movie", "entity_names": ["john wanyes"], "entity_types": ["Actor"]}
{"sentence": "what was julie andrews first role", "entity_names": ["julie andrews"], "entity_types": ["Actor"]}
{"sentence": "what was planet of the vampires about", "entity_names": ["planet of the vampires"], "entity_types": ["Title"]}
{"sentence": "what was the woods have eyes rated", "entity_names": ["the woods have eyes"], "entity_types": ["Title"]}
{"sentence": "what was a 1980 science fiction film with pam grier in it", "entity_names": ["1980", "science fiction", "pam grier"], "entity_types": ["Year", "Genre", "Actor"]}
{"sentence": "what was a nc 17 rated documentary directed by michael spierig", "entity_names": ["nc 17", "documentary", "michael spierig"], "entity_types": ["MPAA Rating", "Genre", "Director"]}
{"sentence": "what was a very popular science fiction movie that came out in the last nine years and was directed by rusty cundieff", "entity_names": ["very popular", "science fiction", "last nine years", "rusty cundieff"], "entity_types": ["Viewers' Rating", "Genre", "Year", "Director"]}
{"sentence": "what was an r rated crime movie directed by stephen cornwell during the past nine years that received nine stars", "entity_names": ["r", "crime", "stephen cornwell", "past nine years", "nine stars"], "entity_types": ["MPAA Rating", "Genre", "Director", "Year", "Viewers' Rating"]}
{"sentence": "what was that pg musical with jack haley in it in the 1980 s", "entity_names": ["pg", "musical", "jack haley", "1980 s"], "entity_types": ["MPAA Rating", "Genre", "Actor", "Year"]}
{"sentence": "what was that movie directed by yasushi muraki in the 1990 s that was rated pg 13 had a white castle and that people said was average", "entity_names": ["yasushi muraki", "1990 s", "pg 13", "white castle", "average"], "entity_types": ["Director", "Year", "MPAA Rating", "Plot", "Viewers' Rating"]}
{"sentence": "what was that movie that had a ratings average of four and rated nc 17 that came out in the last ten decades about a composer which luca guadagnino directed", "entity_names": ["four", "nc 17", "last ten decades", "composer", "luca guadagnino"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Year", "Plot", "Director"]}
{"sentence": "what was that unrated spaghetti western with reese witherspoon that had a five star rating average and came out in the 2000 s", "entity_names": ["unrated", "spaghetti western", "reese witherspoon", "five star", "2000 s"], "entity_types": ["MPAA Rating", "Genre", "Actor", "Viewers' Rating", "Year"]}
{"sentence": "what was the clark gable wall street portrait film that got eight stars in the year 2000", "entity_names": ["clark gable", "wall street", "portrait", "eight stars", "2000"], "entity_types": ["Actor", "Plot", "Genre", "Viewers' Rating", "Year"]}
{"sentence": "what was the hoyt yeatman disney wildlife film that averaged seven out of ten", "entity_names": ["hoyt yeatman", "disney", "wildlife", "seven"], "entity_types": ["Director", "Genre", "Plot", "Viewers' Rating"]}
{"sentence": "what was the nc 17 crime movie that antony szeto directed that got good ratings and was about missing people that came out in the 1960 s", "entity_names": ["nc 17", "crime", "antony szeto", "good ratings", "missing people", "1960 s"], "entity_types": ["MPAA Rating", "Genre", "Director", "Viewers' Rating", "Plot", "Year"]}
{"sentence": "what was the nc 17 film about everlasting love that morten willoch directed", "entity_names": ["nc 17", "everlasting love", "morten willoch"], "entity_types": ["MPAA Rating", "Plot", "Director"]}
{"sentence": "what was the average pg 13 movie about history that had howard stern in it", "entity_names": ["average", "pg 13", "history", "howard stern"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre", "Actor"]}
{"sentence": "what was the best mystery film released in the last seven years", "entity_names": ["mystery", "last seven years"], "entity_types": ["Genre", "Year"]}
{"sentence": "what was the best spaghetti western that was made in the 1990 s", "entity_names": ["spaghetti western", "1990 s"], "entity_types": ["Genre", "Year"]}
{"sentence": "what was the first crime movie that marlon brando was in was it the godfather", "entity_names": ["crime", "marlon brando"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what was the last fantasy film that tim burton directed that was rated r", "entity_names": ["fantasy", "tim burton", "r"], "entity_types": ["Genre", "Director", "MPAA Rating"]}
{"sentence": "what was the last film noir movie that came out", "entity_names": ["film noir"], "entity_types": ["Genre"]}
{"sentence": "what was the last movie that ridley scott directed", "entity_names": ["ridley scott"], "entity_types": ["Director"]}
{"sentence": "what was the last science fiction movie that liam neeson was in", "entity_names": ["science fiction", "liam neeson"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what was the main plot of doubt", "entity_names": ["doubt"], "entity_types": ["Title"]}
{"sentence": "what was the most highly rated movie during the 1960 s", "entity_names": ["highly rated", "1960 s"], "entity_types": ["Viewers' Rating", "Plot"]}
{"sentence": "what was the most recent adventure film that featured akira kurosawa", "entity_names": ["adventure", "akira kurosawa"], "entity_types": ["Genre", "Director"]}
{"sentence": "what was the name of that thriller film with kevin spacey brad pitt and morgan freeman", "entity_names": ["thriller", "kevin spacey"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what was the name of the nc 17 kathrine windfeld movie about companionship that has a ratings average of nine", "entity_names": ["nc 17", "kathrine windfeld", "companionship", "nine"], "entity_types": ["MPAA Rating", "Director", "Plot", "Viewers' Rating"]}
{"sentence": "what was the name of the r rated documentary that was received well and starred angelina jolie", "entity_names": ["r", "documentary", "received well", "angelina jolie"], "entity_types": ["MPAA Rating", "Genre", "Viewers' Rating", "Actor"]}
{"sentence": "what was the name of the movie that martin scorsese directed that was about crime was rated pg 13 and received ten stars", "entity_names": ["martin scorsese", "crime", "pg 13", "ten stars"], "entity_types": ["Director", "Genre", "MPAA Rating", "Viewers' Rating"]}
{"sentence": "what was the rated r crime movie that was released in the past four years and that also got rated well", "entity_names": ["r", "crime", "past four years", "rated well"], "entity_types": ["MPAA Rating", "Genre", "Year", "Viewers' Rating"]}
{"sentence": "what was the title of that 1980 s r rated crime drama with james marsden which some critics called average", "entity_names": ["1980 s", "r", "crime", "james marsden", "average"], "entity_types": ["Year", "MPAA Rating", "Genre", "Actor", "Viewers' Rating"]}
{"sentence": "what was the title of the 1970 s r rated comedy nicole holofcener played in", "entity_names": ["1970 s", "r", "comedy", "nicole holofcener"], "entity_types": ["Year", "MPAA Rating", "Genre", "Director"]}
{"sentence": "what watchable rated g animation did richard robinson direct last year", "entity_names": ["watchable", "g", "animation", "richard robinson", "last year"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre", "Director", "Year"]}
{"sentence": "what westerns did roman polanski direct", "entity_names": ["westerns", "roman polanski"], "entity_types": ["Genre", "Director"]}
{"sentence": "what would be a disaster film starrring paul newman from the decade of the 1960 s", "entity_names": ["disaster", "paul newman", "1960 s"], "entity_types": ["Genre", "Actor", "Year"]}
{"sentence": "what would be a science fiction film with a rating of really good", "entity_names": ["science fiction", "really good"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "what year did the lizzie mcguire movie come out", "entity_names": ["the lizzie mcguire movie"], "entity_types": ["Title"]}
{"sentence": "what year did ip man come out", "entity_names": ["ip man"], "entity_types": ["Title"]}
{"sentence": "what year did like minds come out", "entity_names": ["like minds"], "entity_types": ["Title"]}
{"sentence": "what year did we are what we are come out", "entity_names": ["we are what we are"], "entity_types": ["Title"]}
{"sentence": "what year did young sherlock holmes come out", "entity_names": ["young sherlock holmes"], "entity_types": ["Title"]}
{"sentence": "what year did the movie die come out", "entity_names": ["die"], "entity_types": ["Title"]}
{"sentence": "what year was mrs doubtfire released", "entity_names": ["mrs doubtfire"], "entity_types": ["Title"]}
{"sentence": "what year was northwest hounded police film was released", "entity_names": ["northwest hounded police"], "entity_types": ["Title"]}
{"sentence": "whats fright night about", "entity_names": ["fright night"], "entity_types": ["Title"]}
{"sentence": "whats a pg 13 movie about a pianist and starring katherine kelly that got seven stars", "entity_names": ["pg 13", "pianist", "katherine kelly", "seven stars"], "entity_types": ["MPAA Rating", "Plot", "Actor", "Viewers' Rating"]}
{"sentence": "whats a good funny movie", "entity_names": ["funny"], "entity_types": ["Genre"]}
{"sentence": "whats a good sci fi movie about lightsabers", "entity_names": ["sci fi", "lightsabers"], "entity_types": ["Genre", "Plot"]}
{"sentence": "whats a nine star reviewed movie from last year that features chris tucker", "entity_names": ["nine star", "last year", "chris tucker"], "entity_types": ["Viewers' Rating", "Year", "Actor"]}
{"sentence": "whats an animation from 1980 that is at least watchable and is directed by todd tucker", "entity_names": ["animation", "1980", "watchable", "todd tucker"], "entity_types": ["Genre", "Year", "Viewers' Rating", "Director"]}
{"sentence": "whats an average tyler christopher movie", "entity_names": ["average", "tyler christopher"], "entity_types": ["Viewers' Rating", "Actor"]}
{"sentence": "whats the animated unconventional romance movie set in 1960 with calista flockhart", "entity_names": ["animated", "unconventional romance", "1960", "calista flockhart"], "entity_types": ["Genre", "Plot", "Year", "Actor"]}
{"sentence": "whats the name of a scary movie with michael dudikoff in it", "entity_names": ["scary", "michael dudikoff"], "entity_types": ["Genre", "Actor"]}
{"sentence": "whats the name of the nc 17 romance movie directed by lev l spiro that came out this year", "entity_names": ["nc 17", "romance", "lev l spiro", "this year"], "entity_types": ["MPAA Rating", "Genre", "Director", "Year"]}
{"sentence": "whats the science fiction movie in 1990 that was rated r averaged really good ratings directed by brett ratner", "entity_names": ["science fiction", "1990", "r", "really good", "brett ratner"], "entity_types": ["Genre", "Year", "MPAA Rating", "Viewers' Rating", "Director"]}
{"sentence": "whats a good drama that is well rated", "entity_names": ["drama", "well rated"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "whats a good thriller directed by quentin tarantino", "entity_names": ["thriller", "quentin tarantino"], "entity_types": ["Genre", "Director"]}
{"sentence": "whats a good thriller", "entity_names": ["thriller"], "entity_types": ["Genre"]}
{"sentence": "when did alpha and omega come out", "entity_names": ["alpha and omega"], "entity_types": ["Title"]}
{"sentence": "when did gallants come out", "entity_names": ["gallants"], "entity_types": ["Title"]}
{"sentence": "when did the darwin awards come out", "entity_names": ["the darwin awards"], "entity_types": ["Title"]}
{"sentence": "when did the wiz come out", "entity_names": ["the wiz"], "entity_types": ["Title"]}
{"sentence": "when did war gods of the deep come out", "entity_names": ["war gods of the deep"], "entity_types": ["Title"]}
{"sentence": "when did the reflecting skin come out", "entity_names": ["the reflecting skin"], "entity_types": ["Title"]}
{"sentence": "when did the movie titled b girl come out", "entity_names": ["b girl"], "entity_types": ["Title"]}
{"sentence": "when did you see that must see biography about the king", "entity_names": ["must see", "biography"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "when is a good romantic comedy based on a book coming out", "entity_names": ["romantic comedy"], "entity_types": ["Genre"]}
{"sentence": "when was the movie sculpture released", "entity_names": ["sculpture"], "entity_types": ["Title"]}
{"sentence": "when was the movie stake land made", "entity_names": ["stake land"], "entity_types": ["Title"]}
{"sentence": "where can i find last of the dogmen", "entity_names": ["last of the dogmen"], "entity_types": ["Title"]}
{"sentence": "where can i find a biography on raymond massey", "entity_names": ["biography", "raymond massey"], "entity_types": ["Genre", "Actor"]}
{"sentence": "where can i find a nc 17 thriller from the 1970 s", "entity_names": ["nc 17", "thriller", "1970 s"], "entity_types": ["MPAA Rating", "Genre", "Year"]}
{"sentence": "where can i find a biography containing people famous in the 1990 s", "entity_names": ["biography", "1990 s"], "entity_types": ["Genre", "Year"]}
{"sentence": "where can i find a fantasy movie that takes place in a forest", "entity_names": ["fantasy", "forest"], "entity_types": ["Genre", "Plot"]}
{"sentence": "where can i find the movie boy", "entity_names": ["boy"], "entity_types": ["Title"]}
{"sentence": "where can i find the movie mrs miracle to purchase", "entity_names": ["mrs miracle"], "entity_types": ["Title"]}
{"sentence": "where can i find the movie squanto a warriors tale", "entity_names": ["squanto a warriors tale"], "entity_types": ["Title"]}
{"sentence": "where can i rent satellite in the sky", "entity_names": ["satellite in the sky"], "entity_types": ["Title"]}
{"sentence": "where can i watch big deal on madonna street subbed", "entity_names": ["big deal on madonna street subbed"], "entity_types": ["Title"]}
{"sentence": "where could i find the movie prey for rock and roll", "entity_names": ["prey for rock and roll"], "entity_types": ["Title"]}
{"sentence": "where is the best place to buy the virginity hit", "entity_names": ["the virginity hit"], "entity_types": ["Title"]}
{"sentence": "where is the film hurricane season available to purchase", "entity_names": ["hurricane season"], "entity_types": ["Title"]}
{"sentence": "where is the movie hachiko a dogs story set", "entity_names": ["hachiko a dogs story"], "entity_types": ["Title"]}
{"sentence": "where there any derek jacobi fantasy movies made in the last eight decades", "entity_names": ["derek jacobi", "fantasy", "last eight decades"], "entity_types": ["Actor", "Genre", "Year"]}
{"sentence": "where was the shining filmed", "entity_names": ["the shining"], "entity_types": ["Title"]}
{"sentence": "which luke greenfield documentary made in 1940 received eight stars", "entity_names": ["luke greenfield", "documentary", "1940", "eight stars"], "entity_types": ["Director", "Genre", "Year", "Viewers' Rating"]}
{"sentence": "which r rated family movie starring jerry obach was released within the past eight years", "entity_names": ["r", "family", "jerry obach", "past eight years"], "entity_types": ["MPAA Rating", "Genre", "Actor", "Year"]}
{"sentence": "which animation film stars dustin diamond", "entity_names": ["animation", "dustin diamond"], "entity_types": ["Genre", "Actor"]}
{"sentence": "which biography about a classical composer featuring deforest kelly received an eight star rating", "entity_names": ["biography", "classical composer", "deforest kelly", "eight star"], "entity_types": ["Genre", "Plot", "Actor", "Viewers' Rating"]}
{"sentence": "which documentary did wayne wang direct", "entity_names": ["documentary", "wayne wang"], "entity_types": ["Genre", "Director"]}
{"sentence": "which documentary received a rating of six", "entity_names": ["documentary", "six"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "which drama movie released in the past decade starred phil hartman", "entity_names": ["drama", "past decade", "phil hartman"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "which horror films portray power struggles", "entity_names": ["horror", "power"], "entity_types": ["Genre", "Plot"]}
{"sentence": "which horror movie about a dead child starring paul rudd was rated well and released within the last two decades", "entity_names": ["horror", "dead child", "paul rudd", "rated well", "last two decades"], "entity_types": ["Genre", "Plot", "Actor", "Viewers' Rating", "Year"]}
{"sentence": "which movie had good ratings from the last five years that was a mockumentary directed by joseph graham", "entity_names": ["good ratings", "last five years", "mockumentary", "joseph graham"], "entity_types": ["Viewers' Rating", "Year", "Genre", "Director"]}
{"sentence": "which unrated documentary starring jason priestley was rated as all right", "entity_names": ["unrated", "documentary", "jason priestley", "all right"], "entity_types": ["MPAA Rating", "Genre", "Actor", "Viewers' Rating"]}
{"sentence": "which western directed by wes anderson was liked by many people and involved indians", "entity_names": ["western", "wes anderson", "liked by many", "indians"], "entity_types": ["Genre", "Director", "Viewers' Rating", "Plot"]}
{"sentence": "who are the actors in the movie couples retreat", "entity_names": ["couples retreat"], "entity_types": ["Title"]}
{"sentence": "who directed all star superman", "entity_names": ["all star superman"], "entity_types": ["Title"]}
{"sentence": "who directed but im a cheerleader", "entity_names": ["but im a cheerleader"], "entity_types": ["Title"]}
{"sentence": "who directed crimson rivers 2 angels of the apocalypse", "entity_names": ["crimson rivers 2 angels of the apocalypse"], "entity_types": ["Title"]}
{"sentence": "who directed heat wave", "entity_names": ["heat wave"], "entity_types": ["Title"]}
{"sentence": "who directed pt raiders", "entity_names": ["pt raiders"], "entity_types": ["Title"]}
{"sentence": "who directed snakes on a plane", "entity_names": ["snakes on a plane"], "entity_types": ["Title"]}
{"sentence": "who directed the back up plan", "entity_names": ["the back up plan"], "entity_types": ["Title"]}
{"sentence": "who directed the departed", "entity_names": ["the departed"], "entity_types": ["Title"]}
{"sentence": "who directed wound", "entity_names": ["wound"], "entity_types": ["Title"]}
{"sentence": "who directed a movie called panic at rock island", "entity_names": ["panic at rock island"], "entity_types": ["Title"]}
{"sentence": "who directed the r rated bette davis film that came out in 1960", "entity_names": ["r", "bette davis", "1960"], "entity_types": ["MPAA Rating", "Actor", "Year"]}
{"sentence": "who is in the movie spanglish", "entity_names": ["spanglish"], "entity_types": ["Title"]}
{"sentence": "who is the director of bigger stronger faster", "entity_names": ["bigger stronger faster"], "entity_types": ["Title"]}
{"sentence": "who is the director of war of resistance", "entity_names": ["war of resistance"], "entity_types": ["Title"]}
{"sentence": "who is the lead actor in the movie stranger in our house", "entity_names": ["stranger in our house"], "entity_types": ["Title"]}
{"sentence": "who is the main character in the movie tenshi no tamago", "entity_names": ["tenshi no tamago"], "entity_types": ["Title"]}
{"sentence": "who is the main star in demolition man", "entity_names": ["demolition man"], "entity_types": ["Title"]}
{"sentence": "who is the movie father of invention", "entity_names": ["father of invention"], "entity_types": ["Title"]}
{"sentence": "who starred in tarzan the ape man", "entity_names": ["tarzan the ape man"], "entity_types": ["Title"]}
{"sentence": "who starred in a thin line between love and hate", "entity_names": ["a thin line between love and hate"], "entity_types": ["Title"]}
{"sentence": "who starred in blue collar", "entity_names": ["blue collar"], "entity_types": ["Title"]}
{"sentence": "who starred in bridesmaids", "entity_names": ["bridesmaids"], "entity_types": ["Title"]}
{"sentence": "who starred in dance of the dragon", "entity_names": ["dance of the dragon"], "entity_types": ["Title"]}
{"sentence": "who starred in flightplan", "entity_names": ["flightplan"], "entity_types": ["Title"]}
{"sentence": "who starred in little go beep", "entity_names": ["little go beep"], "entity_types": ["Title"]}
{"sentence": "who starred in monty python and the holy grail", "entity_names": ["monty python and the holy grail"], "entity_types": ["Title"]}
{"sentence": "who starred in the movie dirty love", "entity_names": ["dirty love"], "entity_types": ["Title"]}
{"sentence": "who starred in the movie the willies", "entity_names": ["the willies"], "entity_types": ["Title"]}
{"sentence": "who starred in the movie the luck of the irish", "entity_names": ["the luck of the irish"], "entity_types": ["Title"]}
{"sentence": "who stars in beatdown", "entity_names": ["beatdown"], "entity_types": ["Title"]}
{"sentence": "who stars in beverly hills cop", "entity_names": ["beverly hills cop"], "entity_types": ["Title"]}
{"sentence": "who stars in boogeyman 2", "entity_names": ["boogeyman 2"], "entity_types": ["Title"]}
{"sentence": "who stars in easy rider", "entity_names": ["easy rider"], "entity_types": ["Title"]}
{"sentence": "who stars in the conspirator", "entity_names": ["the conspirator"], "entity_types": ["Title"]}
{"sentence": "who stars in the gods must be crazy ii", "entity_names": ["the gods must be crazy ii"], "entity_types": ["Title"]}
{"sentence": "who stars in west is west", "entity_names": ["west is west"], "entity_types": ["Title"]}
{"sentence": "who stars in world for ransom", "entity_names": ["world for ransom"], "entity_types": ["Title"]}
{"sentence": "who stars in the movie becoming jesse tate", "entity_names": ["becoming jesse tate"], "entity_types": ["Title"]}
{"sentence": "who was in child of darkness child of light", "entity_names": ["child of darkness child of light"], "entity_types": ["Title"]}
{"sentence": "who was the main character in the movie the hills have eyes", "entity_names": ["the hills have eyes"], "entity_types": ["Title"]}
{"sentence": "who wrote and directed head above water", "entity_names": ["head above water"], "entity_types": ["Title"]}
{"sentence": "would you be able to find me a pg 13 movie from the 1980 s starring lara fabian", "entity_names": ["pg 13", "1980 s", "lara fabian"], "entity_types": ["MPAA Rating", "Year", "Actor"]}
{"sentence": "would you be able to help me find the cheetah girls 2 movie", "entity_names": ["the cheetah girls 2"], "entity_types": ["Title"]}
{"sentence": "would you be able to show me where i can find a drama starring keri russell", "entity_names": ["drama", "keri russell"], "entity_types": ["Genre", "Actor"]}
{"sentence": "did charles haid have a war movie in the last three years", "entity_names": ["charles haid", "war", "last three years"], "entity_types": ["Director", "Genre", "Year"]}
{"sentence": "did christian bale star in a science fiction in the past four years", "entity_names": ["christian bale", "science fiction", "past four years"], "entity_types": ["Actor", "Genre", "Year"]}
{"sentence": "did christopher nolan direct a pg 13 disaster film", "entity_names": ["christopher nolan", "pg 13", "disaster"], "entity_types": ["Director", "MPAA Rating", "Genre"]}
{"sentence": "did sofia coppola direct a rated r biographical", "entity_names": ["sofia coppola", "r", "biographical"], "entity_types": ["Director", "MPAA Rating", "Genre"]}
{"sentence": "did steven soderbergh direct a film noir movie", "entity_names": ["steven soderbergh", "film noir"], "entity_types": ["Director", "Genre"]}
{"sentence": "did the shining scare charlize theron when she was a kid", "entity_names": ["the shining", "charlize theron"], "entity_types": ["Title", "Actor"]}
{"sentence": "do you know any good spaghetti western movies about horse drawn carraiges", "entity_names": ["spaghetti western", "horse drawn carraiges"], "entity_types": ["Genre", "Plot"]}
{"sentence": "does meryl streep star in a western", "entity_names": ["meryl streep", "western"], "entity_types": ["Actor", "Genre"]}
{"sentence": "find highly rated pg 13 science fiction movie from past six decades", "entity_names": ["highly rated", "pg 13", "science fiction", "past six decades"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre", "Year"]}
{"sentence": "is anthony hopkins in a musical", "entity_names": ["anthony hopkins", "musical"], "entity_types": ["Actor", "Genre"]}
{"sentence": "is clark gable in a romantic comedy", "entity_names": ["clark gable", "romantic comedy"], "entity_types": ["Actor", "Genre"]}
{"sentence": "is francis ford coppola the director for the shining", "entity_names": ["francis ford coppola", "the shining"], "entity_types": ["Director", "Title"]}
{"sentence": "is katharine hepburn in the shining", "entity_names": ["katharine hepburn", "the shining"], "entity_types": ["Actor", "Title"]}
{"sentence": "is wall e a good movie", "entity_names": ["wall e"], "entity_types": ["Title"]}
{"sentence": "is there a a film noir movie with ridley scott from the 2000 s", "entity_names": ["film noir", "ridley scott", "2000 s"], "entity_types": ["Genre", "Director", "Year"]}
{"sentence": "is there a good g rated john hughes comedy", "entity_names": ["g", "john hughes", "comedy"], "entity_types": ["MPAA Rating", "Director", "Genre"]}
{"sentence": "is there a good christian bale adventure movie", "entity_names": ["christian bale", "adventure"], "entity_types": ["Actor", "Genre"]}
{"sentence": "list animated pg 13 rated movie starring sean connery", "entity_names": ["animated", "pg 13", "sean connery"], "entity_types": ["Genre", "MPAA Rating", "Actor"]}
{"sentence": "list isolation adventure movies that are critically acclaimed and directed by jack sholder in the past decade", "entity_names": ["isolation", "adventure", "critically acclaimed", "jack sholder", "past decade"], "entity_types": ["Plot", "Genre", "Viewers' Rating", "Director", "Year"]}
{"sentence": "list movies named grave encounters", "entity_names": ["grave encounters"], "entity_types": ["Title"]}
{"sentence": "list some films starring hugh jackman", "entity_names": ["hugh jackman"], "entity_types": ["Actor"]}
{"sentence": "list some mockumentary movies", "entity_names": ["mockumentary"], "entity_types": ["Genre"]}
{"sentence": "list some war movies with nazi", "entity_names": ["war", "nazi"], "entity_types": ["Genre", "Plot"]}
{"sentence": "list well rated mystery movies from the year 2000", "entity_names": ["well rated", "mystery", "2000"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "name a action movie with a shootout rated pg", "entity_names": ["action", "shootout", "pg"], "entity_types": ["Genre", "Plot", "MPAA Rating"]}
{"sentence": "name a comedy from francois truffaut", "entity_names": ["comedy", "francois truffaut"], "entity_types": ["Genre", "Director"]}
{"sentence": "name a fantasy film with sandra bullock", "entity_names": ["fantasy", "sandra bullock"], "entity_types": ["Genre", "Actor"]}
{"sentence": "name a mystery by orson welles", "entity_names": ["mystery", "orson welles"], "entity_types": ["Genre", "Director"]}
{"sentence": "name a mystery starring kevin spacey", "entity_names": ["mystery", "kevin spacey"], "entity_types": ["Genre", "Actor"]}
{"sentence": "name a sci fi about a human", "entity_names": ["sci fi", "human"], "entity_types": ["Genre", "Plot"]}
{"sentence": "name a science fiction with artificial intelligence from the 2010 s", "entity_names": ["science fiction", "artificial intelligence", "2010 s"], "entity_types": ["Genre", "Plot", "Year"]}
{"sentence": "name a teen movie with steve mcqueen that was received well", "entity_names": ["teen", "steve mcqueen", "received well"], "entity_types": ["Genre", "Actor", "Viewers' Rating"]}
{"sentence": "name an adventure movie directed by francis ford coppola", "entity_names": ["adventure", "francis ford coppola"], "entity_types": ["Genre", "Director"]}
{"sentence": "name some guillermo del toro action movies", "entity_names": ["guillermo del toro", "action"], "entity_types": ["Director", "Genre"]}
{"sentence": "name some john huston films about children", "entity_names": ["john huston", "children"], "entity_types": ["Director", "Genre"]}
{"sentence": "name some movies starring robert redford", "entity_names": ["robert redford"], "entity_types": ["Actor"]}
{"sentence": "was mel gibson in inception", "entity_names": ["mel gibson", "inception"], "entity_types": ["Actor", "Title"]}
{"sentence": "what r rated movies with john cusak got all right ratings", "entity_names": ["r", "john cusak", "all right"], "entity_types": ["MPAA Rating", "Actor", "Viewers' Rating"]}
{"sentence": "what is a fantasy rated pg starring christian bale", "entity_names": ["fantasy", "pg", "christian bale"], "entity_types": ["Genre", "MPAA Rating", "Actor"]}
{"sentence": "what is a good pg 13 drama with andrew keegan in the 2010 s", "entity_names": ["good", "pg 13", "drama", "andrew keegan", "2010 s"], "entity_types": ["Viewers' Rating", "MPAA Rating", "Genre", "Actor", "Year"]}
{"sentence": "what movie can be called a thriller", "entity_names": ["thriller"], "entity_types": ["Genre"]}
{"sentence": "what police movie is there that you would recommend where somebody is kidnapped", "entity_names": ["police", "kidnapped"], "entity_types": ["Genre", "Plot"]}
{"sentence": "whats a pg 13 crime movie by orson welles", "entity_names": ["pg 13", "crime", "orson welles"], "entity_types": ["MPAA Rating", "Genre", "Director"]}
{"sentence": "whats a comedy directed by david fincher that was highly liked", "entity_names": ["comedy", "david fincher", "highly liked"], "entity_types": ["Genre", "Director", "Viewers' Rating"]}
{"sentence": "whats a documentary about youth identity", "entity_names": ["documentary", "youth identity"], "entity_types": ["Genre", "Plot"]}
{"sentence": "whats a good action film about on the run", "entity_names": ["action", "on the run"], "entity_types": ["Genre", "Plot"]}
{"sentence": "who directed the r rated film made in the year 2000 that starred helen barkin", "entity_names": ["r", "2000", "helen barkin"], "entity_types": ["MPAA Rating", "Year", "Actor"]}
{"sentence": "i would like to get the details of the movie purple rain on imdb com", "entity_names": ["details", "purple rain"], "entity_types": ["Review", "Title"]}
{"sentence": "show blogs regarding evita", "entity_names": ["blogs", "evita"], "entity_types": ["Review", "Title"]}
{"sentence": "could you please show me a website with reviews of indiana jones", "entity_names": ["reviews", "indiana jones"], "entity_types": ["Review", "Character"]}
{"sentence": "could you send me to a website with user reviews of willy wonka the chocolate factory", "entity_names": ["reviews", "willy wonka the chocolate factory"], "entity_types": ["Review", "Title"]}
{"sentence": "what do you think the best part of a hard day s night was", "entity_names": ["think", "a hard day s night"], "entity_types": ["Review", "Title"]}
{"sentence": "can u please show me the trailer of kiss me kate", "entity_names": ["trailer", "kiss me kate"], "entity_types": ["Trailer", "Title"]}
{"sentence": "can i see the trailer for kiss me kate", "entity_names": ["trailer", "kiss me kate"], "entity_types": ["Trailer", "Title"]}
{"sentence": "please show me some of indiana s major scenes in the movie", "entity_names": ["scenes"], "entity_types": ["Trailer"]}
{"sentence": "where can i see some short clips of a hard day s night", "entity_names": ["clips", "a hard day s night"], "entity_types": ["Trailer", "Title"]}
{"sentence": "what was the name of that 1997 movie that had a character named dr evil in it", "entity_names": ["1997", "dr evil"], "entity_types": ["Year", "Character"]}
{"sentence": "which is the film in which a major role was done by dr evil released in 1997", "entity_names": ["dr evil", "1997"], "entity_types": ["Character", "Year"]}
{"sentence": "what is the movie directed by george lucas which casts darth vader and yoda in it", "entity_names": ["george lucas", "darth vader", "yoda"], "entity_types": ["Director", "Character", "Character"]}
{"sentence": "what is that george lucas movie with yoda and darth vader", "entity_names": ["george lucas", "yoda", "darth vader"], "entity_types": ["Director", "Character", "Character"]}
{"sentence": "which movie of 2010 had buzz lightyear and mr potato head in it", "entity_names": ["2010", "buzz lightyear", "mr potato head"], "entity_types": ["Year", "Character", "Character"]}
{"sentence": "what 1960 s movie had that song my favourite things in it", "entity_names": ["1960 s", "my favourite things"], "entity_types": ["Year", "Song"]}
{"sentence": "what is that movie from the 60 s that had the song my favorite things in it", "entity_names": ["60 s", "my favorite things"], "entity_types": ["Year", "Song"]}
{"sentence": "there is a 1960 s movie where the song my favorite things is sung", "entity_names": ["1960 s", "my favorite things"], "entity_types": ["Year", "Song"]}
{"sentence": "what movie did the song come what may play in", "entity_names": ["come what may"], "entity_types": ["Song"]}
{"sentence": "american beauty", "entity_names": ["american beauty"], "entity_types": ["Title"]}
{"sentence": "crouching tiger, hidden dragon", "entity_names": ["crouching tiger , hidden dragon"], "entity_types": ["Title"]}
{"sentence": "million dollar baby", "entity_names": ["million dollar baby"], "entity_types": ["Title"]}
{"sentence": "life of oharu", "entity_names": ["life of oharu"], "entity_types": ["Title"]}
{"sentence": "the leopard", "entity_names": ["the leopard"], "entity_types": ["Title"]}
{"sentence": "empire strikes back", "entity_names": ["empire strikes back"], "entity_types": ["Title"]}
{"sentence": "snow white and the seven dwarfs", "entity_names": ["snow white and the seven dwarfs"], "entity_types": ["Title"]}
{"sentence": "monty python and the holy grail", "entity_names": ["monty python and the holy grail"], "entity_types": ["Title"]}
{"sentence": "the graduate", "entity_names": ["the graduate"], "entity_types": ["Title"]}
{"sentence": "blow up", "entity_names": ["blow up"], "entity_types": ["Title"]}
{"sentence": "american graffiti", "entity_names": ["american graffiti"], "entity_types": ["Title"]}
{"sentence": "oliver twist", "entity_names": ["oliver twist"], "entity_types": ["Title"]}
{"sentence": "lord of the rings the fellowship of the rings", "entity_names": ["lord of the rings the fellowship of the rings"], "entity_types": ["Title"]}
{"sentence": "sunset boulevard", "entity_names": ["sunset boulevard"], "entity_types": ["Title"]}
{"sentence": "sansho the bailiff", "entity_names": ["sansho the bailiff"], "entity_types": ["Title"]}
{"sentence": "pulp fiction", "entity_names": ["pulp fiction"], "entity_types": ["Title"]}
{"sentence": "lord of the rings the return of the king", "entity_names": ["lord of the rings the return of the king"], "entity_types": ["Title"]}
{"sentence": "broken blossoms", "entity_names": ["broken blossoms"], "entity_types": ["Title"]}
{"sentence": "wizard of oz", "entity_names": ["wizard of oz"], "entity_types": ["Title"]}
{"sentence": "freaks", "entity_names": ["freaks"], "entity_types": ["Title"]}
{"sentence": "the children of paradise", "entity_names": ["the children of paradise"], "entity_types": ["Title"]}
{"sentence": "city of god", "entity_names": ["city of god"], "entity_types": ["Title"]}
{"sentence": "finding nemo", "entity_names": ["finding nemo"], "entity_types": ["Title"]}
{"sentence": "duck soup", "entity_names": ["duck soup"], "entity_types": ["Title"]}
{"sentence": "play the trailer", "entity_names": ["trailer"], "entity_types": ["Trailer"]}
{"sentence": "play trailer of on stranger tides", "entity_names": ["trailer", "on stranger tides"], "entity_types": ["Trailer", "Title"]}
{"sentence": "play the trailer of dark of the moon", "entity_names": ["trailer", "dark of the moon"], "entity_types": ["Trailer", "Title"]}
{"sentence": "play trailer dark of the moon", "entity_names": ["trailer", "dark of the moon"], "entity_types": ["Trailer", "Title"]}
{"sentence": "play the trailer dark of the moon", "entity_names": ["trailer", "dark of the moon"], "entity_types": ["Trailer", "Title"]}
{"sentence": "show me the good thief review", "entity_names": ["the good thief", "review"], "entity_types": ["Title", "Review"]}
{"sentence": "any james bond movies", "entity_names": ["james bond"], "entity_types": ["Character"]}
{"sentence": "find me some adventure movies with indiana jones", "entity_names": ["adventure", "indiana jones"], "entity_types": ["Genre", "Character"]}
{"sentence": "what s the title of the movie about captain jack sparrow", "entity_names": ["captain jack sparrow"], "entity_types": ["Character"]}
