#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成合成策略脚本
输入：原始数据集文件路径和生成数量
输出：目标分布、句子多样化、实体多样化策略文件

使用方法：
python scripts/1_generate_strategy.py --dataset path/to/dataset.json --target-count 100
"""

import os
import sys
import json
import argparse
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def setup_environment():
    """设置环境变量和路径"""
    # 确保必要的目录存在
    os.makedirs("reproduce/entity_target", exist_ok=True)
    os.makedirs("reproduce/sen_diversity", exist_ok=True)
    os.makedirs("reproduce/entity_diversity", exist_ok=True)
    
    print("[✓] 环境设置完成")

def update_balance_config(target_count: int):
    """更新balance_config.json中的目标数量"""
    balance_config_path = "src/gen_strat/balance_config.json"
    
    try:
        # 读取当前配置
        with open(balance_config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 更新balance_target_per_type
        old_target = config.get("balance_target_per_type", 0)
        config["balance_target_per_type"] = target_count
        
        # 更新所有实体类型的目标数量
        entity_targets = config.get("entity_type_targets", {})
        for entity_type in entity_targets:
            entity_targets[entity_type] = target_count
        
        # 保存更新后的配置
        with open(balance_config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"[✓] 已更新balance_config.json")
        print(f"    balance_target_per_type: {old_target} -> {target_count}")
        print(f"    所有实体类型目标数量: {target_count}")
        
    except Exception as e:
        print(f"[错误] 更新balance_config.json失败：{e}")
        raise

def update_dataset_path(dataset_path: str):
    """更新1-gen_distribution_target.py中的数据集路径"""
    script_path = "src/gen_strat/1-gen_distribution_target.py"
    
    try:
        # 读取脚本内容
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新数据集路径
        old_path = 'format-dataset/privacy_bench.json'
        content = content.replace(old_path, dataset_path)
        
        # 保存更新后的脚本
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"[✓] 已更新数据集路径：{dataset_path}")
        
    except Exception as e:
        print(f"[错误] 更新数据集路径失败：{e}")
        raise

def run_distribution_target_generation():
    """运行目标分布生成"""
    print("\n=== 步骤1：生成目标分布 ===")
    
    try:
        # 导入并运行目标分布生成模块
        sys.path.insert(0, str(project_root / "src" / "gen_strat"))
        
        # 导入目标分布生成模块
        import importlib.util
        spec = importlib.util.spec_from_file_location("gen_distribution_target", "src/gen_strat/1-gen_distribution_target.py")
        gen_distribution_target = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(gen_distribution_target)
        
        # 运行目标分布生成
        gen_distribution_target.main()
        
        print("[✓] 目标分布生成完成")
        
    except ImportError as e:
        print(f"[警告] 无法导入目标分布生成模块，尝试直接运行脚本：{e}")
        # 直接运行脚本
        script_path = "src/gen_strat/1-gen_distribution_target.py"
        result = os.system(f"python {script_path}")
        
        if result == 0:
            print("[✓] 目标分布生成完成")
        else:
            raise Exception(f"脚本执行失败，返回码：{result}")
        
    except Exception as e:
        print(f"[错误] 目标分布生成失败：{e}")
        raise

def run_diversity_generation():
    """运行多样化策略生成"""
    print("\n=== 步骤2：生成多样化策略 ===")
    
    try:
        # 导入并运行多样化策略生成模块
        sys.path.insert(0, str(project_root / "src" / "gen_strat"))
        
        # 导入多样化策略生成模块
        import importlib.util
        spec = importlib.util.spec_from_file_location("gen_diversity", "src/gen_strat/2-gen_diversity.py")
        gen_diversity = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(gen_diversity)
        
        # 运行多样化策略生成
        gen_diversity.main()
        
        print("[✓] 多样化策略生成完成")
        
    except ImportError as e:
        print(f"[警告] 无法导入多样化策略生成模块，尝试直接运行脚本：{e}")
        # 直接运行脚本
        script_path = "src/gen_strat/2-gen_diversity.py"
        result = os.system(f"python {script_path}")
        
        if result == 0:
            print("[✓] 多样化策略生成完成")
        else:
            raise Exception(f"脚本执行失败，返回码：{result}")
        
    except Exception as e:
        print(f"[错误] 多样化策略生成失败：{e}")
        raise

def get_latest_files():
    """获取生成的文件路径"""
    files = {}
    
    # 获取目标分布文件（固定文件名）
    target_file = "reproduce/entity_target/privacy_bench_target.json"
    if os.path.exists(target_file):
        files['target'] = target_file
    
    # 获取句子多样化文件（固定文件名）
    sen_diversity_file = "reproduce/sen_diversity/sen_diversify_value.json"
    if os.path.exists(sen_diversity_file):
        files['sentence_diversity'] = sen_diversity_file
    
    # 获取最新的实体多样化目录
    entity_diversity_dir = "reproduce/entity_diversity"
    if os.path.exists(entity_diversity_dir):
        entity_dirs = [d for d in os.listdir(entity_diversity_dir) if os.path.isdir(os.path.join(entity_diversity_dir, d))]
        if entity_dirs:
            latest_entity = sorted(entity_dirs)[-1]
            entity_diversity_path = os.path.join(entity_diversity_dir, latest_entity)
            # 检查是否存在entity_diversity.json文件
            entity_json_file = os.path.join(entity_diversity_path, 'entity_diversity.json')
            if os.path.exists(entity_json_file):
                files['entity_diversity'] = entity_diversity_path
    
    return files

def create_summary_report(dataset_path: str, target_count: int, generated_files: dict):
    """创建执行总结报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_path = f"strategy_generation_report_{timestamp}.txt"
    
    report_content = f"""
合成策略生成报告
==================

生成时间：{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
原始数据集：{dataset_path}
目标数量：{target_count}（每个实体类型）

生成的文件：
"""
    
    for file_type, file_path in generated_files.items():
        report_content += f"{file_type}: {file_path}\n"
    
    report_content += f"""
配置更新：
- balance_config.json: balance_target_per_type = {target_count}
- diversity_config.json: num_entity_variants = {int(target_count * 0.7)}

下一步：
运行 scripts/2_generate_data.py 生成合成数据
"""
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"\n[✓] 执行报告已保存：{report_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成合成策略脚本")
    parser.add_argument("--dataset", required=True, help="原始数据集路径")
    parser.add_argument("--target-count", type=int, required=True, help="合成目标数量（每个实体类型）")
    
    args = parser.parse_args()
    
    # 检查数据集文件是否存在
    if not os.path.exists(args.dataset):
        print(f"[错误] 数据集文件不存在：{args.dataset}")
        sys.exit(1)
    
    print("=== 开始生成合成策略 ===")
    print(f"原始数据集：{args.dataset}")
    print(f"目标数量：{args.target_count}")
    
    try:
        # 1. 设置环境
        setup_environment()
        
        # 2. 更新配置
        update_balance_config(args.target_count)
        update_dataset_path(args.dataset)
        
        # 3. 生成目标分布
        run_distribution_target_generation()
        
        # 4. 生成多样化策略
        run_diversity_generation()
        
        # 5. 获取生成的文件
        generated_files = get_latest_files()
        
        # 6. 创建总结报告
        create_summary_report(args.dataset, args.target_count, generated_files)
        
        print(f"\n=== 合成策略生成完成 ===")
        print("生成的文件：")
        for file_type, file_path in generated_files.items():
            print(f"  {file_type}: {file_path}")
        print("\n下一步：运行 scripts/2_generate_data.py 生成合成数据")
        
    except Exception as e:
        print(f"\n[错误] 合成策略生成失败：{e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 