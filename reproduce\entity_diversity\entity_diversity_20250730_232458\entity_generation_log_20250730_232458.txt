=== 实体生成日志 ===
开始时间: 2025-07-30 23:24:58
目标数量: 50


[23:24:58] 开始处理实体类型: 姓名 - Latent生成
目标数量: 50, 当前收集: 25, 缺口: 25
[姓名] Latent[网络用语] 第1轮生成:
- API返回: 7个
- 过滤后: 7个
- 去重后: 7个
当前总数: 32/50
[姓名] Latent[社交场合] 第1轮生成:
- API返回: 7个
- 过滤后: 7个
- 去重后: 7个
当前总数: 39/50
[姓名] Latent[新闻报道] 第1轮生成:
- API返回: 7个
- 过滤后: 7个
- 去重后: 7个
当前总数: 46/50
[姓名] Latent[法律文本] 第1轮生成:
- API返回: 7个
- 过滤后: 7个
- 去重后: 7个
当前总数: 53/50
最终结果: 达标 (53/50)


[23:25:01] 开始处理实体类型: 年龄 - Latent生成
目标数量: 50, 当前收集: 24, 缺口: 26
[年龄] Latent[社交场合] 第1轮生成:
- API返回: 7个
- 过滤后: 0个
- 去重后: 0个
当前总数: 24/50
[年龄] Latent[社交场合] 第1轮生成:
- API返回: 7个
- 过滤后: 0个
- 去重后: 0个
当前总数: 24/50
[年龄] Latent[社交场合] 第1轮生成:
- API返回: 7个
- 过滤后: 0个
- 去重后: 0个
当前总数: 24/50
[年龄] Latent[新闻报道] 第1轮生成:
- API返回: 7个
- 过滤后: 7个
- 去重后: 5个
当前总数: 29/50
[年龄] Latent[医学描述] 第1轮生成:
- API返回: 7个
- 过滤后: 0个
- 去重后: 0个
当前总数: 29/50
[年龄] Latent[医学描述] 第1轮生成:
- API返回: 7个
- 过滤后: 0个
- 去重后: 0个
当前总数: 29/50
[年龄] Latent[医学描述] 第1轮生成:
- API返回: 7个
- 过滤后: 0个
- 去重后: 0个
当前总数: 29/50
[年龄] Latent[法律文本] 第1轮生成:
- API返回: 7个
- 过滤后: 7个
- 去重后: 7个
当前总数: 36/50
[年龄] Latent[口语表达] 第1轮生成:
- API返回: 7个
- 过滤后: 0个
- 去重后: 0个
当前总数: 36/50
[年龄] Latent[口语表达] 第1轮生成:
- API返回: 7个
- 过滤后: 0个
- 去重后: 0个
当前总数: 36/50
[年龄] Latent[口语表达] 第1轮生成:
- API返回: 7个
- 过滤后: 0个
- 去重后: 0个
当前总数: 36/50
最终结果: 未达标 (36/50)


[23:25:12] 开始处理实体类型: 性别
目标数量: 50, 当前收集: 20, 缺口: 30
策略: 补vanilla到20个，剩余19个由latent补充
[性别] Vanilla 第1轮生成:
- API返回: 16个
- 过滤后: 16个
- 去重后: 16个
当前总数: 36/50

[23:25:13] 开始处理实体类型: 性别 - Latent生成
目标数量: 50, 当前收集: 36, 缺口: 19
[性别] Latent[新闻报道] 第1轮生成:
- API返回: 1个
- 过滤后: 1个
- 去重后: 0个
当前总数: 36/50
[性别] Latent[新闻报道] 第1轮生成:
- API返回: 1个
- 过滤后: 1个
- 去重后: 0个
当前总数: 36/50
[性别] Latent[新闻报道] 第1轮生成:
- API返回: 1个
- 过滤后: 1个
- 去重后: 0个
当前总数: 36/50
[性别] Latent[法律文本] 第1轮生成:
- API返回: 4个
- 过滤后: 4个
- 去重后: 1个
当前总数: 37/50
[性别] Latent[法律文本] 第1轮生成:
- API返回: 3个
- 过滤后: 3个
- 去重后: 2个
当前总数: 39/50
最终结果: 未达标 (39/50)


[23:25:16] 开始处理实体类型: 国籍
目标数量: 50, 当前收集: 20, 缺口: 30
策略: 补vanilla到20个，剩余28个由latent补充
[国籍] Vanilla 第1轮生成:
- API返回: 3个
- 过滤后: 2个
- 去重后: 2个
当前总数: 22/50

[23:25:16] 开始处理实体类型: 国籍 - Latent生成
目标数量: 50, 当前收集: 22, 缺口: 28
[国籍] Latent[新闻报道] 第1轮生成:
- API返回: 12个
- 过滤后: 12个
- 去重后: 11个
当前总数: 33/50
[国籍] Latent[法律文本] 第1轮生成:
- API返回: 13个
- 过滤后: 0个
- 去重后: 0个
当前总数: 33/50
[国籍] Latent[法律文本] 第1轮生成:
- API返回: 13个
- 过滤后: 0个
- 去重后: 0个
当前总数: 33/50
[国籍] Latent[法律文本] 第1轮生成:
- API返回: 13个
- 过滤后: 0个
- 去重后: 0个
当前总数: 33/50
[国籍] Latent[学术论文] 第1轮生成:
- API返回: 12个
- 过滤后: 0个
- 去重后: 0个
当前总数: 33/50
[国籍] Latent[学术论文] 第1轮生成:
- API返回: 12个
- 过滤后: 0个
- 去重后: 0个
当前总数: 33/50
[国籍] Latent[学术论文] 第1轮生成:
- API返回: 12个
- 过滤后: 0个
- 去重后: 0个
当前总数: 33/50
最终结果: 未达标 (33/50)


[23:25:24] 开始处理实体类型: 职业 - Latent生成
目标数量: 50, 当前收集: 24, 缺口: 26
[职业] Latent[社交场合] 第1轮生成:
- API返回: 9个
- 过滤后: 9个
- 去重后: 9个
当前总数: 33/50
[职业] Latent[新闻报道] 第1轮生成:
- API返回: 9个
- 过滤后: 9个
- 去重后: 9个
当前总数: 42/50
[职业] Latent[法律文本] 第1轮生成:
- API返回: 9个
- 过滤后: 8个
- 去重后: 8个
当前总数: 50/50
最终结果: 达标 (50/50)


[23:25:27] 开始处理实体类型: 民族 - Latent生成
目标数量: 50, 当前收集: 25, 缺口: 25
[民族] Latent[新闻报道] 第1轮生成:
- API返回: 12个
- 过滤后: 12个
- 去重后: 0个
当前总数: 25/50
[民族] Latent[新闻报道] 第1轮生成:
- API返回: 12个
- 过滤后: 12个
- 去重后: 2个
当前总数: 27/50
[民族] Latent[新闻报道] 第1轮生成:
- API返回: 9个
- 过滤后: 9个
- 去重后: 3个
当前总数: 30/50
[民族] Latent[法律文本] 第1轮生成:
- API返回: 12个
- 过滤后: 12个
- 去重后: 0个
当前总数: 30/50
[民族] Latent[法律文本] 第1轮生成:
- API返回: 12个
- 过滤后: 12个
- 去重后: 0个
当前总数: 30/50
[民族] Latent[法律文本] 第1轮生成:
- API返回: 12个
- 过滤后: 12个
- 去重后: 0个
当前总数: 30/50
[民族] Latent[学术论文] 第1轮生成:
- API返回: 12个
- 过滤后: 12个
- 去重后: 12个
当前总数: 42/50
最终结果: 未达标 (42/50)


[23:25:34] 开始处理实体类型: 教育背景 - Latent生成
目标数量: 50, 当前收集: 21, 缺口: 29
[教育背景] Latent[社交场合] 第1轮生成:
- API返回: 10个
- 过滤后: 10个
- 去重后: 10个
当前总数: 31/50
[教育背景] Latent[新闻报道] 第1轮生成:
- API返回: 10个
- 过滤后: 9个
- 去重后: 9个
当前总数: 40/50
[教育背景] Latent[法律文本] 第1轮生成:
- API返回: 10个
- 过滤后: 10个
- 去重后: 10个
当前总数: 50/50
最终结果: 达标 (50/50)


[23:25:37] 开始处理实体类型: 婚姻状况 - Latent生成
目标数量: 50, 当前收集: 22, 缺口: 28
[婚姻状况] Latent[社交场合] 第1轮生成:
- API返回: 10个
- 过滤后: 8个
- 去重后: 8个
当前总数: 30/50
[婚姻状况] Latent[法律文本] 第1轮生成:
- API返回: 10个
- 过滤后: 5个
- 去重后: 5个
当前总数: 35/50
[婚姻状况] Latent[法律文本] 第1轮生成:
- API返回: 3个
- 过滤后: 1个
- 去重后: 1个
当前总数: 36/50
[婚姻状况] Latent[法律文本] 第1轮生成:
- API返回: 1个
- 过滤后: 1个
- 去重后: 1个
当前总数: 37/50
[婚姻状况] Latent[新闻报道] 第1轮生成:
- API返回: 10个
- 过滤后: 9个
- 去重后: 8个
当前总数: 45/50
[婚姻状况] Latent[口语表达] 第1轮生成:
- API返回: 10个
- 过滤后: 7个
- 去重后: 7个
当前总数: 52/50
最终结果: 达标 (52/50)


[23:25:42] 开始处理实体类型: 政治倾向 - Latent生成
目标数量: 50, 当前收集: 22, 缺口: 28
[政治倾向] Latent[新闻报道] 第1轮生成:
- API返回: 13个
- 过滤后: 13个
- 去重后: 13个
当前总数: 35/50
[政治倾向] Latent[法律文本] 第1轮生成:
- API返回: 13个
- 过滤后: 13个
- 去重后: 11个
当前总数: 46/50
[政治倾向] Latent[学术论文] 第1轮生成:
- API返回: 13个
- 过滤后: 13个
- 去重后: 13个
当前总数: 59/50
最终结果: 达标 (59/50)


[23:25:46] 开始处理实体类型: 家庭成员
目标数量: 50, 当前收集: 20, 缺口: 30
策略: 补vanilla到20个，剩余29个由latent补充
[家庭成员] Vanilla 第1轮生成:
- API返回: 1个
- 过滤后: 1个
- 去重后: 0个
当前总数: 20/50
[家庭成员] Vanilla 第1轮生成:
- API返回: 1个
- 过滤后: 0个
- 去重后: 0个
当前总数: 20/50
[家庭成员] Vanilla 第1轮生成:
- API返回: 1个
- 过滤后: 1个
- 去重后: 1个
当前总数: 20/50

[23:25:47] 开始处理实体类型: 家庭成员 - Latent生成
目标数量: 50, 当前收集: 20, 缺口: 29
[家庭成员] Latent[社交场合] 第1轮生成:
- API返回: 12个
- 过滤后: 0个
- 去重后: 0个
当前总数: 20/50
[家庭成员] Latent[社交场合] 第1轮生成:
- API返回: 12个
- 过滤后: 0个
- 去重后: 0个
当前总数: 20/50
[家庭成员] Latent[社交场合] 第1轮生成:
- API返回: 12个
- 过滤后: 1个
- 去重后: 1个
当前总数: 21/50
[家庭成员] Latent[新闻报道] 第1轮生成:
- API返回: 13个
- 过滤后: 8个
- 去重后: 4个
当前总数: 25/50
[家庭成员] Latent[新闻报道] 第1轮生成:
- API返回: 7个
- 过滤后: 2个
- 去重后: 0个
当前总数: 25/50
[家庭成员] Latent[新闻报道] 第1轮生成:
- API返回: 7个
- 过滤后: 5个
- 去重后: 1个
当前总数: 26/50
[家庭成员] Latent[口语表达] 第1轮生成:
- API返回: 13个
- 过滤后: 1个
- 去重后: 1个
当前总数: 27/50
[家庭成员] Latent[口语表达] 第1轮生成:
- API返回: 12个
- 过滤后: 2个
- 去重后: 1个
当前总数: 28/50
[家庭成员] Latent[口语表达] 第1轮生成:
- API返回: 10个
- 过滤后: 0个
- 去重后: 0个
当前总数: 28/50
最终结果: 未达标 (28/50)


[23:25:55] 开始处理实体类型: 工资数额
目标数量: 50, 当前收集: 21, 缺口: 29
策略: 补vanilla到20个，剩余24个由latent补充
[工资数额] Vanilla 第1轮生成:
- API返回: 7个
- 过滤后: 7个
- 去重后: 7个
当前总数: 28/50

[23:25:56] 开始处理实体类型: 工资数额 - Latent生成
目标数量: 50, 当前收集: 28, 缺口: 24
[工资数额] Latent[法律文本] 第1轮生成:
- API返回: 13个
- 过滤后: 13个
- 去重后: 13个
当前总数: 41/50
[工资数额] Latent[商务场合] 第1轮生成:
- API返回: 13个
- 过滤后: 13个
- 去重后: 12个
当前总数: 53/50
最终结果: 达标 (53/50)


[23:26:02] 开始处理实体类型: 投资产品
目标数量: 50, 当前收集: 19, 缺口: 31
策略: 补vanilla到20个，剩余28个由latent补充
[投资产品] Vanilla 第1轮生成:
- API返回: 4个
- 过滤后: 4个
- 去重后: 4个
当前总数: 23/50

[23:26:03] 开始处理实体类型: 投资产品 - Latent生成
目标数量: 50, 当前收集: 23, 缺口: 28
[投资产品] Latent[法律文本] 第1轮生成:
- API返回: 19个
- 过滤后: 19个
- 去重后: 19个
当前总数: 42/50
[投资产品] Latent[商务场合] 第1轮生成:
- API返回: 19个
- 过滤后: 19个
- 去重后: 19个
当前总数: 61/50
最终结果: 达标 (61/50)


[23:26:09] 开始处理实体类型: 税务记录
目标数量: 50, 当前收集: 22, 缺口: 28
策略: 补vanilla到20个，剩余19个由latent补充
[税务记录] Vanilla 第1轮生成:
- API返回: 13个
- 过滤后: 13个
- 去重后: 13个
当前总数: 35/50

[23:26:10] 开始处理实体类型: 税务记录 - Latent生成
目标数量: 50, 当前收集: 35, 缺口: 19
[税务记录] Latent[法律文本] 第1轮生成:
- API返回: 4个
- 过滤后: 3个
- 去重后: 3个
当前总数: 38/50
[税务记录] Latent[学术论文] 第1轮生成:
- API返回: 6个
- 过滤后: 6个
- 去重后: 6个
当前总数: 44/50
最终结果: 未达标 (44/50)


[23:26:12] 开始处理实体类型: 信用记录
目标数量: 50, 当前收集: 21, 缺口: 29
策略: 补vanilla到20个，剩余24个由latent补充
[信用记录] Vanilla 第1轮生成:
- API返回: 7个
- 过滤后: 7个
- 去重后: 7个
当前总数: 28/50

[23:26:13] 开始处理实体类型: 信用记录 - Latent生成
目标数量: 50, 当前收集: 28, 缺口: 24
[信用记录] Latent[法律文本] 第1轮生成:
- API返回: 13个
- 过滤后: 10个
- 去重后: 10个
当前总数: 38/50
[信用记录] Latent[商务场合] 第1轮生成:
- API返回: 13个
- 过滤后: 13个
- 去重后: 13个
当前总数: 51/50
最终结果: 达标 (51/50)


[23:26:17] 开始处理实体类型: 实体资产
目标数量: 50, 当前收集: 24, 缺口: 26
策略: 补vanilla到20个，剩余16个由latent补充
[实体资产] Vanilla 第1轮生成:
- API返回: 15个
- 过滤后: 15个
- 去重后: 15个
当前总数: 39/50

[23:26:18] 开始处理实体类型: 实体资产 - Latent生成
目标数量: 50, 当前收集: 39, 缺口: 16
[实体资产] Latent[法律文本] 第1轮生成:
- API返回: 1个
- 过滤后: 1个
- 去重后: 1个
当前总数: 40/50
[实体资产] Latent[新闻报道] 第1轮生成:
- API返回: 1个
- 过滤后: 1个
- 去重后: 1个
当前总数: 41/50
最终结果: 未达标 (41/50)


[23:26:19] 开始处理实体类型: 交易信息
目标数量: 50, 当前收集: 22, 缺口: 28
策略: 补vanilla到20个，剩余17个由latent补充
[交易信息] Vanilla 第1轮生成:
- API返回: 16个
- 过滤后: 16个
- 去重后: 16个
当前总数: 38/50

[23:26:22] 开始处理实体类型: 交易信息 - Latent生成
目标数量: 50, 当前收集: 38, 缺口: 17
[交易信息] Latent[法律文本] 第1轮生成:
- API返回: 1个
- 过滤后: 1个
- 去重后: 1个
当前总数: 39/50
[交易信息] Latent[商务场合] 第1轮生成:
- API返回: 3个
- 过滤后: 3个
- 去重后: 3个
当前总数: 42/50
最终结果: 未达标 (42/50)


[23:26:23] 开始处理实体类型: 疾病 - Latent生成
目标数量: 50, 当前收集: 22, 缺口: 28
[疾病] Latent[医学描述] 第1轮生成:
- API返回: 13个
- 过滤后: 13个
- 去重后: 12个
当前总数: 34/50
[疾病] Latent[新闻报道] 第1轮生成:
- API返回: 13个
- 过滤后: 13个
- 去重后: 6个
当前总数: 40/50
[疾病] Latent[新闻报道] 第1轮生成:
- API返回: 4个
- 过滤后: 4个
- 去重后: 2个
当前总数: 42/50
[疾病] Latent[新闻报道] 第1轮生成:
- API返回: 1个
- 过滤后: 1个
- 去重后: 0个
当前总数: 42/50
[疾病] Latent[法律文本] 第1轮生成:
- API返回: 13个
- 过滤后: 13个
- 去重后: 5个
当前总数: 47/50
[疾病] Latent[法律文本] 第1轮生成:
- API返回: 6个
- 过滤后: 6个
- 去重后: 3个
当前总数: 50/50
最终结果: 达标 (50/50)


[23:26:30] 开始处理实体类型: 药物
目标数量: 50, 当前收集: 20, 缺口: 30
策略: 补vanilla到20个，剩余29个由latent补充
[药物] Vanilla 第1轮生成:
- API返回: 1个
- 过滤后: 1个
- 去重后: 1个
当前总数: 21/50

[23:26:30] 开始处理实体类型: 药物 - Latent生成
目标数量: 50, 当前收集: 21, 缺口: 29
[药物] Latent[医学描述] 第1轮生成:
- API返回: 12个
- 过滤后: 12个
- 去重后: 9个
当前总数: 30/50
[药物] Latent[学术论文] 第1轮生成:
- API返回: 13个
- 过滤后: 13个
- 去重后: 10个
当前总数: 40/50
[药物] Latent[法律文本] 第1轮生成:
- API返回: 13个
- 过滤后: 13个
- 去重后: 7个
当前总数: 47/50
[药物] Latent[法律文本] 第1轮生成:
- API返回: 3个
- 过滤后: 3个
- 去重后: 3个
当前总数: 50/50
最终结果: 达标 (50/50)


[23:26:36] 开始处理实体类型: 临床表现
目标数量: 50, 当前收集: 20, 缺口: 30
策略: 补vanilla到20个，剩余28个由latent补充
[临床表现] Vanilla 第1轮生成:
- API返回: 3个
- 过滤后: 3个
- 去重后: 3个
当前总数: 23/50

[23:26:37] 开始处理实体类型: 临床表现 - Latent生成
目标数量: 50, 当前收集: 23, 缺口: 28
[临床表现] Latent[医学描述] 第1轮生成:
- API返回: 19个
- 过滤后: 19个
- 去重后: 19个
当前总数: 42/50
[临床表现] Latent[学术论文] 第1轮生成:
- API返回: 19个
- 过滤后: 19个
- 去重后: 18个
当前总数: 60/50
最终结果: 达标 (60/50)


[23:26:42] 开始处理实体类型: 医疗程序
目标数量: 50, 当前收集: 20, 缺口: 30
策略: 补vanilla到20个，剩余28个由latent补充
[医疗程序] Vanilla 第1轮生成:
- API返回: 3个
- 过滤后: 3个
- 去重后: 3个
当前总数: 23/50

[23:26:42] 开始处理实体类型: 医疗程序 - Latent生成
目标数量: 50, 当前收集: 23, 缺口: 28
[医疗程序] Latent[医学描述] 第1轮生成:
- API返回: 19个
- 过滤后: 19个
- 去重后: 18个
当前总数: 41/50
[医疗程序] Latent[学术论文] 第1轮生成:
- API返回: 19个
- 过滤后: 19个
- 去重后: 14个
当前总数: 55/50
最终结果: 达标 (55/50)


[23:26:47] 开始处理实体类型: 过敏信息
目标数量: 50, 当前收集: 20, 缺口: 30
策略: 补vanilla到20个，剩余29个由latent补充
[过敏信息] Vanilla 第1轮生成:
- API返回: 1个
- 过滤后: 1个
- 去重后: 1个
当前总数: 21/50

[23:26:47] 开始处理实体类型: 过敏信息 - Latent生成
目标数量: 50, 当前收集: 21, 缺口: 29
[过敏信息] Latent[医学描述] 第1轮生成:
- API返回: 20个
- 过滤后: 20个
- 去重后: 19个
当前总数: 39/50
[过敏信息] Latent[法律文本] 第1轮生成:
- API返回: 21个
- 过滤后: 21个
- 去重后: 19个
当前总数: 58/50
最终结果: 达标 (58/50)


[23:26:52] 开始处理实体类型: 生育信息
目标数量: 50, 当前收集: 19, 缺口: 31
策略: 补vanilla到20个，剩余27个由latent补充
[生育信息] Vanilla 第1轮生成:
- API返回: 6个
- 过滤后: 6个
- 去重后: 6个
当前总数: 25/50

[23:26:54] 开始处理实体类型: 生育信息 - Latent生成
目标数量: 50, 当前收集: 25, 缺口: 27
[生育信息] Latent[医学描述] 第1轮生成:
- API返回: 12个
- 过滤后: 12个
- 去重后: 12个
当前总数: 37/50
[生育信息] Latent[法律文本] 第1轮生成:
- API返回: 12个
- 过滤后: 9个
- 去重后: 9个
当前总数: 46/50
[生育信息] Latent[新闻报道] 第1轮生成:
- API返回: 12个
- 过滤后: 11个
- 去重后: 11个
当前总数: 57/50
最终结果: 达标 (57/50)


[23:27:00] 开始处理实体类型: 地理位置
目标数量: 50, 当前收集: 20, 缺口: 30
策略: 补vanilla到20个，剩余23个由latent补充
[地理位置] Vanilla 第1轮生成:
- API返回: 10个
- 过滤后: 10个
- 去重后: 10个
当前总数: 30/50

[23:27:01] 开始处理实体类型: 地理位置 - Latent生成
目标数量: 50, 当前收集: 30, 缺口: 23
[地理位置] Latent[社交场合] 第1轮生成:
- API返回: 6个
- 过滤后: 6个
- 去重后: 6个
当前总数: 36/50
[地理位置] Latent[新闻报道] 第1轮生成:
- API返回: 6个
- 过滤后: 6个
- 去重后: 6个
当前总数: 42/50
[地理位置] Latent[法律文本] 第1轮生成:
- API返回: 9个
- 过滤后: 9个
- 去重后: 9个
当前总数: 51/50
最终结果: 达标 (51/50)


[23:27:04] 开始处理实体类型: 行程信息
目标数量: 50, 当前收集: 18, 缺口: 32
策略: 补vanilla到20个，剩余27个由latent补充
[行程信息] Vanilla 第1轮生成:
- API返回: 7个
- 过滤后: 7个
- 去重后: 7个
当前总数: 25/50

[23:27:05] 开始处理实体类型: 行程信息 - Latent生成
目标数量: 50, 当前收集: 25, 缺口: 27
[行程信息] Latent[新闻报道] 第1轮生成:
- API返回: 12个
- 过滤后: 12个
- 去重后: 12个
当前总数: 37/50
[行程信息] Latent[法律文本] 第1轮生成:
- API返回: 12个
- 过滤后: 12个
- 去重后: 12个
当前总数: 49/50
[行程信息] Latent[口语表达] 第1轮生成:
- API返回: 12个
- 过滤后: 12个
- 去重后: 12个
当前总数: 61/50
最终结果: 达标 (61/50)

