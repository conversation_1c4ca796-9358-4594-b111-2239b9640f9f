{"prompts": ["假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文nationality实体，每行一个。这些实体将用于命名实体识别任务中的nationality类型。\n国籍实体示例：中国、美国、法国、日本、韩国等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文nationality实体，每行一个。这些实体将用于命名实体识别任务中的nationality类型。\n国籍实体示例：中国、美国、法国、日本、韩国等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文nationality实体，每行一个。这些实体将用于命名实体识别任务中的nationality类型。\n国籍实体示例：中国、美国、法国、日本、韩国等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文nationality实体，每行一个。这些实体将用于命名实体识别任务中的nationality类型。\n国籍实体示例：中国、美国、法国、日本、韩国等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文nationality实体，每行一个。这些实体将用于命名实体识别任务中的nationality类型。\n国籍实体示例：中国、美国、法国、日本、韩国等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文nationality实体，每行一个。这些实体将用于命名实体识别任务中的nationality类型。\n国籍实体示例：中国、美国、法国、日本、韩国等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文nationality实体，每行一个。这些实体将用于命名实体识别任务中的nationality类型。\n国籍实体示例：中国、美国、法国、日本、韩国等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文education实体，每行一个。这些实体将用于命名实体识别任务中的education类型。\n教育水平实体示例：本科、硕士、博士、专科、MBA等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文education实体，每行一个。这些实体将用于命名实体识别任务中的education类型。\n教育水平实体示例：本科、硕士、博士、专科、MBA等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文education实体，每行一个。这些实体将用于命名实体识别任务中的education类型。\n教育水平实体示例：本科、硕士、博士、专科、MBA等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文education实体，每行一个。这些实体将用于命名实体识别任务中的education类型。\n教育水平实体示例：本科、硕士、博士、专科、MBA等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文education实体，每行一个。这些实体将用于命名实体识别任务中的education类型。\n教育水平实体示例：本科、硕士、博士、专科、MBA等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文education实体，每行一个。这些实体将用于命名实体识别任务中的education类型。\n教育水平实体示例：本科、硕士、博士、专科、MBA等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文education实体，每行一个。这些实体将用于命名实体识别任务中的education类型。\n教育水平实体示例：本科、硕士、博士、专科、MBA等。", "假设你是一名人力资源专家，正在处理各类简历。请生成75个多样化的中文location实体，每行一个。这些实体将用于命名实体识别任务中的location类型。\n地点实体示例：北京市、上海市、广州市、深圳市、杭州市等。", "假设你是一名人力资源专家，正在处理各类简历。请生成75个多样化的中文location实体，每行一个。这些实体将用于命名实体识别任务中的location类型。\n地点实体示例：北京市、上海市、广州市、深圳市、杭州市等。", "假设你是一名人力资源专家，正在处理各类简历。请生成75个多样化的中文location实体，每行一个。这些实体将用于命名实体识别任务中的location类型。\n地点实体示例：北京市、上海市、广州市、深圳市、杭州市等。", "假设你是一名人力资源专家，正在处理各类简历。请生成75个多样化的中文location实体，每行一个。这些实体将用于命名实体识别任务中的location类型。\n地点实体示例：北京市、上海市、广州市、深圳市、杭州市等。", "假设你是一名人力资源专家，正在处理各类简历。请生成75个多样化的中文location实体，每行一个。这些实体将用于命名实体识别任务中的location类型。\n地点实体示例：北京市、上海市、广州市、深圳市、杭州市等。", "假设你是一名人力资源专家，正在处理各类简历。请生成75个多样化的中文location实体，每行一个。这些实体将用于命名实体识别任务中的location类型。\n地点实体示例：北京市、上海市、广州市、深圳市、杭州市等。", "假设你是一名人力资源专家，正在处理各类简历。请生成75个多样化的中文location实体，每行一个。这些实体将用于命名实体识别任务中的location类型。\n地点实体示例：北京市、上海市、广州市、深圳市、杭州市等。", "假设你是一名人力资源专家，正在处理各类简历。请生成75个多样化的中文name实体，每行一个。这些实体将用于命名实体识别任务中的name类型。\n姓名实体示例：张三、李四、王五、赵六、钱七等。", "假设你是一名人力资源专家，正在处理各类简历。请生成75个多样化的中文name实体，每行一个。这些实体将用于命名实体识别任务中的name类型。\n姓名实体示例：张三、李四、王五、赵六、钱七等。", "假设你是一名人力资源专家，正在处理各类简历。请生成75个多样化的中文name实体，每行一个。这些实体将用于命名实体识别任务中的name类型。\n姓名实体示例：张三、李四、王五、赵六、钱七等。", "假设你是一名人力资源专家，正在处理各类简历。请生成75个多样化的中文name实体，每行一个。这些实体将用于命名实体识别任务中的name类型。\n姓名实体示例：张三、李四、王五、赵六、钱七等。", "假设你是一名人力资源专家，正在处理各类简历。请生成75个多样化的中文name实体，每行一个。这些实体将用于命名实体识别任务中的name类型。\n姓名实体示例：张三、李四、王五、赵六、钱七等。", "假设你是一名人力资源专家，正在处理各类简历。请生成75个多样化的中文name实体，每行一个。这些实体将用于命名实体识别任务中的name类型。\n姓名实体示例：张三、李四、王五、赵六、钱七等。", "假设你是一名人力资源专家，正在处理各类简历。请生成75个多样化的中文name实体，每行一个。这些实体将用于命名实体识别任务中的name类型。\n姓名实体示例：张三、李四、王五、赵六、钱七等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文ethnicity实体，每行一个。这些实体将用于命名实体识别任务中的ethnicity类型。\n民族实体示例：汉族、满族、蒙古族、回族、藏族等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文ethnicity实体，每行一个。这些实体将用于命名实体识别任务中的ethnicity类型。\n民族实体示例：汉族、满族、蒙古族、回族、藏族等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文ethnicity实体，每行一个。这些实体将用于命名实体识别任务中的ethnicity类型。\n民族实体示例：汉族、满族、蒙古族、回族、藏族等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文ethnicity实体，每行一个。这些实体将用于命名实体识别任务中的ethnicity类型。\n民族实体示例：汉族、满族、蒙古族、回族、藏族等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文ethnicity实体，每行一个。这些实体将用于命名实体识别任务中的ethnicity类型。\n民族实体示例：汉族、满族、蒙古族、回族、藏族等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文ethnicity实体，每行一个。这些实体将用于命名实体识别任务中的ethnicity类型。\n民族实体示例：汉族、满族、蒙古族、回族、藏族等。", "假设你是一名人力资源专家，正在处理各类简历。请生成50个多样化的中文ethnicity实体，每行一个。这些实体将用于命名实体识别任务中的ethnicity类型。\n民族实体示例：汉族、满族、蒙古族、回族、藏族等。"]}