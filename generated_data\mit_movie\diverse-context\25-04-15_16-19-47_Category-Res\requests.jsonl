{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 10 diverse movie query categories for movie queries to the dialog system."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 42}
{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 10 diverse movie query categories for movie queries to the dialog system."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 49}
{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 10 diverse movie query categories for movie queries to the dialog system."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 56}
{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 6 different user demographics, i.e. different age groups and genders, for movie queries to the dialog system. Some examples are [young, age-friendly, older, family-friendly]."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 63}
{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 6 different user demographics, i.e. different age groups and genders, for movie queries to the dialog system. Some examples are [young, age-friendly, older, family-friendly]."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 70}
{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 6 different user demographics, i.e. different age groups and genders, for movie queries to the dialog system. Some examples are [family-friendly, older, young, age-friendly]."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 77}
{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 10 different user moods for movie queries to the dialog system. Some examples are [curious, frustrated]."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 84}
{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 10 different user moods for movie queries to the dialog system. Some examples are [curious, frustrated]."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 91}
{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 10 different user moods for movie queries to the dialog system. Some examples are [frustrated, curious]."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 98}
{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 8 different language variations for movie queries to the dialog system. Some examples are [informal, formal, vague, colloquial, indirect, technical, casual, straightforward, slang]."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 105}
{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 8 different language variations for movie queries to the dialog system. Some examples are [vague, colloquial, straightforward, slang, formal, informal, technical, indirect, casual]."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 112}
{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 8 different language variations for movie queries to the dialog system. Some examples are [vague, slang, indirect, informal, straightforward, technical, colloquial, casual, formal]."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 119}
