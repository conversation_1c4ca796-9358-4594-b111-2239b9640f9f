[{"实体类型": "工资数额", "原始数量": 0, "最终数量": 12, "目标差值": 2, "需标红": true}, {"实体类型": "税务记录", "原始数量": 0, "最终数量": 17, "目标差值": 7, "需标红": true}, {"实体类型": "生育信息", "原始数量": 0, "最终数量": 27, "目标差值": 17, "需标红": true}, {"实体类型": "性别", "原始数量": 2, "最终数量": 14, "目标差值": 4, "需标红": true}, {"实体类型": "疾病", "原始数量": 7, "最终数量": 10, "目标差值": 0, "需标红": false}, {"实体类型": "婚姻状况", "原始数量": 0, "最终数量": 10, "目标差值": 0, "需标红": false}, {"实体类型": "年龄", "原始数量": 2, "最终数量": 11, "目标差值": 1, "需标红": false}, {"实体类型": "临床表现", "原始数量": 3, "最终数量": 24, "目标差值": 14, "需标红": true}, {"实体类型": "民族", "原始数量": 1, "最终数量": 17, "目标差值": 7, "需标红": true}, {"实体类型": "药物", "原始数量": 8, "最终数量": 10, "目标差值": 0, "需标红": false}, {"实体类型": "过敏信息", "原始数量": 0, "最终数量": 18, "目标差值": 8, "需标红": true}, {"实体类型": "医疗程序", "原始数量": 1, "最终数量": 10, "目标差值": 0, "需标红": false}, {"实体类型": "信用记录", "原始数量": 0, "最终数量": 16, "目标差值": 6, "需标红": true}, {"实体类型": "家庭成员", "原始数量": 0, "最终数量": 37, "目标差值": 27, "需标红": true}, {"实体类型": "行程信息", "原始数量": 0, "最终数量": 33, "目标差值": 23, "需标红": true}, {"实体类型": "职业", "原始数量": 8, "最终数量": 10, "目标差值": 0, "需标红": false}, {"实体类型": "国籍", "原始数量": 4, "最终数量": 12, "目标差值": 2, "需标红": true}, {"实体类型": "实体资产", "原始数量": 0, "最终数量": 19, "目标差值": 9, "需标红": true}, {"实体类型": "交易信息", "原始数量": 0, "最终数量": 34, "目标差值": 24, "需标红": true}, {"实体类型": "投资产品", "原始数量": 0, "最终数量": 11, "目标差值": 1, "需标红": false}, {"实体类型": "政治倾向", "原始数量": 0, "最终数量": 15, "目标差值": 5, "需标红": true}, {"实体类型": "教育背景", "原始数量": 3, "最终数量": 13, "目标差值": 3, "需标红": true}, {"实体类型": "姓名", "原始数量": 1, "最终数量": 15, "目标差值": 5, "需标红": true}, {"实体类型": "地理位置", "原始数量": 3, "最终数量": 19, "目标差值": 9, "需标红": true}]