{"iteration": 3, "timestamp": "2025-07-19T14:54:54.613855", "dataset_size": 32, "entity_gap": {"姓名": 9, "年龄": 8, "性别": 8, "国籍": 6, "职业": 2, "民族": 9, "教育背景": 7, "婚姻状况": 10, "政治倾向": 10, "家庭成员": 10, "工资数额": 10, "投资产品": 10, "税务记录": 10, "信用记录": 10, "实体资产": 10, "交易信息": 10, "疾病": 3, "药物": 2, "临床表现": 7, "医疗程序": 9, "过敏信息": 10, "生育信息": 10, "地理位置": 7, "行程信息": 10}, "diversity_metrics": {"vocabulary_diversity": 1.0, "syntactic_diversity": 0.8, "semantic_diversity": 0.7, "context_diversity": 0.6, "entity_diversity": 0.75}, "total_gap": 197, "detailed_metrics": {"basic_statistics": {"total_samples": 32, "text_length_stats": {"mean": 35.78125, "median": 34.5, "std": 14.447608744615836, "min": 12, "max": 68, "percentiles": {"25": 26.25, "75": 44.0, "90": 54.500000000000014, "95": 59.599999999999994}}, "entity_count_stats": {"mean": 1.34375, "median": 1.0, "std": 0.6427176187875979, "min": 1, "max": 4}, "label_distribution": {"姓名": 1, "地理位置": 3, "职业": 8, "医疗程序": 1, "疾病": 7, "药物": 8, "临床表现": 3, "国籍": 4, "民族": 1, "教育背景": 3, "性别": 2, "年龄": 2}}, "entity_analysis": {"entity_type_distribution": {"姓名": 1, "地理位置": 3, "职业": 8, "医疗程序": 1, "疾病": 7, "药物": 8, "临床表现": 3, "国籍": 4, "民族": 1, "教育背景": 3, "性别": 2, "年龄": 2}, "entity_length_analysis": {"姓名": {"count": 1, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "地理位置": {"count": 3, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "职业": {"count": 8, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "医疗程序": {"count": 1, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "疾病": {"count": 7, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "药物": {"count": 8, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "临床表现": {"count": 3, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "国籍": {"count": 4, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "民族": {"count": 1, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "教育背景": {"count": 3, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "性别": {"count": 2, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "年龄": {"count": 2, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}}, "entity_position_analysis": {"姓名": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "地理位置": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "职业": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "医疗程序": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "疾病": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "药物": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "临床表现": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "国籍": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "民族": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "教育背景": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "性别": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "年龄": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}}, "entity_density_analysis": {"mean_density": 0.04308958369378606, "median_density": 0.03571428571428571, "std_density": 0.021132877931565024, "max_density": 0.08333333333333333}, "entity_overlap_analysis": {"total_overlaps": 0, "overlap_rate": 0.0, "overlap_details": []}}, "linguistic_analysis": {"vocabulary_analysis": {"total_words": 634, "unique_words": 407, "vocabulary_diversity": 0.6419558359621451, "total_chars": 1145, "unique_chars": 533, "most_common_words": [["，", 44], ["。", 26], ["（", 15], ["的", 14], ["）", 14], ["、", 7], ["或", 7], ["：", 6], ["和", 6], ["中国", 5], ["心动过速", 5], ["可", 4], ["国籍", 4], ["无", 4], ["境外", 4], ["居留权", 4], ["对", 3], ["了", 3], ["时", 3], ["到", 3]], "word_length_distribution": {"mean_word_length": 1.8059936908517351, "median_word_length": 2.0, "std_word_length": 1.1935269843705933, "word_length_distribution": {"single_char": 0.4274447949526814, "two_chars": 0.4353312302839117, "three_chars": 0.083596214511041, "four_plus_chars": 0.05362776025236593}}}, "sentence_structure_analysis": {"mean_sentence_length": 19.8125, "median_sentence_length": 18.0, "std_sentence_length": 7.8159992163510355, "sentence_length_distribution": {"short": 0.09375, "medium": 0.4375, "long": 0.46875}}, "punctuation_analysis": {"total_punctuation": 117, "punctuation_diversity": 10, "punctuation_distribution": {"。": 26, "，": 44, "；": 2, "：": 6, "（": 15, "）": 14, "、": 7, "？": 1, "【": 1, "】": 1}}, "word_frequency_analysis": {}}, "diversity_analysis": {"lexical_diversity": {"type_token_ratio": 0.6419558359621451, "hapax_legomena_ratio": 0.8132678132678133, "vocabulary_richness": 2842.106836904032}, "syntactic_diversity": {"unique_patterns": 9, "pattern_diversity": 0.28125, "most_common_patterns": [["LONG_COMMA_END_PUNCT", 12], ["MEDIUM_COMMA_END_PUNCT", 4], ["LONG_END_PUNCT", 4], ["LONG_COMMA_END_PUNCT_MID_PUNCT", 3], ["MEDIUM_MID_PUNCT", 2], ["LONG_COMMA", 2], ["MEDIUM_END_PUNCT", 2], ["MEDIUM_COMMA_MID_PUNCT", 2], ["MEDIUM_COMMA_END_PUNCT_MID_PUNCT", 1]]}, "semantic_diversity": {}, "entity_context_diversity": {"姓名": {"total_contexts": 1, "unique_contexts": 1, "context_diversity": 1.0}, "地理位置": {"total_contexts": 3, "unique_contexts": 3, "context_diversity": 1.0}, "职业": {"total_contexts": 8, "unique_contexts": 8, "context_diversity": 1.0}, "医疗程序": {"total_contexts": 1, "unique_contexts": 1, "context_diversity": 1.0}, "疾病": {"total_contexts": 7, "unique_contexts": 6, "context_diversity": 0.8571428571428571}, "药物": {"total_contexts": 8, "unique_contexts": 5, "context_diversity": 0.625}, "临床表现": {"total_contexts": 3, "unique_contexts": 3, "context_diversity": 1.0}, "国籍": {"total_contexts": 4, "unique_contexts": 4, "context_diversity": 1.0}, "民族": {"total_contexts": 1, "unique_contexts": 1, "context_diversity": 1.0}, "教育背景": {"total_contexts": 3, "unique_contexts": 2, "context_diversity": 0.6666666666666666}, "性别": {"total_contexts": 2, "unique_contexts": 1, "context_diversity": 0.5}, "年龄": {"total_contexts": 2, "unique_contexts": 1, "context_diversity": 0.5}}}, "quality_indicators": {"annotation_quality": {"total_annotations": 43, "valid_annotations": 0, "boundary_accuracy": 0.0, "boundary_errors": 43, "type_consistency_errors": 0, "type_consistency_rate": 1.0}, "text_quality": {"mean_quality_score": 0.971875, "median_quality_score": 1.0, "std_quality_score": 0.05144399260360727, "quality_distribution": {"high": 1.0, "medium": 0.0, "low": 0.0}}, "consistency_indicators": {}}}, "strategy_metadata": {}, "convergence_indicators": {"gap_reduction_rate": 0.0, "diversity_improvement": 0.231, "stability_score": 0.6210115494897472}}