#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量生成和句子筛选器功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.synth_data.ner_data_generation import (
    calculate_batch_size,
    build_batch_prompt,
    generate_sentences_batch_optimized,
    create_filter_chain,
    LengthFilter,
    NaturalnessFilter,
    FilterChain
)

def test_batch_size_calculation():
    """测试批量大小计算"""
    print("=" * 60)
    print("测试批量大小计算")
    print("=" * 60)
    
    test_cases = [
        (10, "姓名"),
        (50, "职业"),
        (100, "地理位置"),
        (200, "行程信息")
    ]
    
    for target_count, entity_type in test_cases:
        batch_size = calculate_batch_size(target_count, entity_type)
        print(f"目标数量: {target_count}, 实体类型: {entity_type} -> 批量大小: {batch_size}")

def test_batch_prompt_building():
    """测试批量prompt构建"""
    print("\n" + "=" * 60)
    print("测试批量prompt构建")
    print("=" * 60)
    
    entity_type = "姓名"
    batch_size = 10
    entities = ["张三", "李四", "王五", "赵六"]
    sentence_diversity = {
        "姓名": {
            "语气": ["正式", "轻松", "严肃"],
            "场景": ["工作", "生活", "学习"]
        }
    }
    examples = ["张三在北京工作。", "李四是一名医生。"]
    generation_features = {
        "use_entity_diversity": True,
        "use_sentence_diversity": True,
        "use_example_sentences": True
    }
    
    prompt = build_batch_prompt(
        entity_type, batch_size, entities, 
        sentence_diversity, examples, generation_features
    )
    
    print("生成的批量prompt:")
    print("-" * 40)
    print(prompt)
    print("-" * 40)

def test_length_filter():
    """测试长度筛选器"""
    print("\n" + "=" * 60)
    print("测试长度筛选器")
    print("=" * 60)
    
    # 创建长度筛选器
    config = {
        "enabled": True,
        "min_length": 10,
        "max_length": 50,
        "entity_type_specific": {
            "行程信息": {"min_length": 15, "max_length": 80}
        }
    }
    
    filter_obj = LengthFilter(config)
    
    # 测试句子
    test_sentences = [
        "张三工作。",  # 太短
        "张三在北京的医院工作，是一名医生。",  # 合适
        "张三在北京的医院工作，是一名医生，每天都很忙，经常加班到很晚。",  # 太长
        "李四是一名工程师。",  # 合适
        "王五在上海投资了股票，获得了不错的收益。",  # 合适
    ]
    
    print("测试句子:")
    for i, sentence in enumerate(test_sentences, 1):
        print(f"{i}. {sentence} ({len(sentence)}字符)")
    
    # 测试普通实体类型
    filtered, rejected = filter_obj.filter(test_sentences, "姓名")
    print(f"\n筛选结果（姓名类型）:")
    print(f"通过: {len(filtered)}个")
    print(f"拒绝: {len(rejected)}个")
    
    for item in rejected:
        print(f"  - 拒绝: {item['sentence']} (原因: {item['reason']})")
    
    # 测试特定实体类型
    filtered2, rejected2 = filter_obj.filter(test_sentences, "行程信息")
    print(f"\n筛选结果（行程信息类型）:")
    print(f"通过: {len(filtered2)}个")
    print(f"拒绝: {len(rejected2)}个")

def test_filter_chain():
    """测试筛选器链"""
    print("\n" + "=" * 60)
    print("测试筛选器链")
    print("=" * 60)
    
    # 创建筛选器链
    filter_chain = create_filter_chain()
    
    # 测试句子
    test_sentences = [
        "张三工作。",  # 太短
        "张三在北京的医院工作，是一名医生。",  # 合适
        "李四是一名工程师。",  # 合适
        "王五在上海投资了股票。",  # 合适
    ]
    
    print("测试句子:")
    for i, sentence in enumerate(test_sentences, 1):
        print(f"{i}. {sentence}")
    
    # 应用筛选器链
    filtered, stats = filter_chain.apply_filters(test_sentences, "姓名")
    
    print(f"\n筛选器链结果:")
    print(f"原始句子: {stats['total_initial']}个")
    print(f"最终句子: {stats['total_final']}个")
    print(f"整体拒绝率: {stats['overall_rejection_rate']:.2%}")
    
    for filter_name, filter_result in stats['filter_results'].items():
        print(f"  {filter_name}: {filter_result['filter_type']}")
        print(f"    通过: {filter_result['passed']}个")
        print(f"    拒绝: {filter_result['rejected']}个")
        print(f"    拒绝率: {filter_result['rejection_rate']:.2%}")

def test_batch_generation_integration():
    """测试批量生成集成"""
    print("\n" + "=" * 60)
    print("测试批量生成集成")
    print("=" * 60)
    
    # 模拟配置
    entity_type = "姓名"
    target_count = 5  # 小数量用于测试
    sentence_diversity = {}
    vanilla_entities = {"姓名": ["张三", "李四", "王五"]}
    latent_entities = {}
    examples = ["张三在北京工作。"]
    generation_features = {
        "use_entity_diversity": True,
        "use_sentence_diversity": False,
        "use_example_sentences": True
    }
    
    print(f"测试参数:")
    print(f"- 实体类型: {entity_type}")
    print(f"- 目标数量: {target_count}")
    print(f"- 可用实体: {vanilla_entities.get(entity_type, [])}")
    
    try:
        # 注意：这个测试需要API调用，可能会失败
        print("\n开始批量生成测试...")
        sentences = generate_sentences_batch_optimized(
            entity_type, target_count, sentence_diversity,
            vanilla_entities, latent_entities, examples, generation_features
        )
        
        print(f"批量生成结果: {len(sentences)}个句子")
        for i, sentence in enumerate(sentences, 1):
            print(f"{i}. {sentence}")
            
    except Exception as e:
        print(f"批量生成测试失败（这是预期的，因为需要API调用）: {e}")

if __name__ == "__main__":
    print("开始测试批量生成和句子筛选器功能...")
    
    # 测试1：批量大小计算
    test_batch_size_calculation()
    
    # 测试2：批量prompt构建
    test_batch_prompt_building()
    
    # 测试3：长度筛选器
    test_length_filter()
    
    # 测试4：筛选器链
    test_filter_chain()
    
    # 测试5：批量生成集成（可选，需要API）
    # test_batch_generation_integration()
    
    print("\n测试完成！") 