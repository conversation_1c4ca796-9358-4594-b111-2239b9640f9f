﻿# -*- coding: utf-8 -*-
"""
消融实验指标计算模块
用于RQ4: 消融实验
"""

import json
import numpy as np
from typing import Dict, List, Any, Tuple
from collections import Counter

def calculate_component_contribution(full_system_results: Dict, ablation_results: Dict) -> Dict[str, Any]:
    """计算各组件的贡献度"""
    contributions = {}
    
    # 基准性能（完整系统）
    baseline_performance = full_system_results.get("overall_score", 0)
    
    for component, results in ablation_results.items():
        ablated_performance = results.get("overall_score", 0)
        
        # 计算性能下降
        performance_drop = baseline_performance - ablated_performance
        
        # 计算相对贡献
        relative_contribution = performance_drop / baseline_performance if baseline_performance > 0 else 0
        
        contributions[component] = {
            "performance_drop": performance_drop,
            "relative_contribution": relative_contribution,
            "ablated_performance": ablated_performance,
            "baseline_performance": baseline_performance
        }
    
    return contributions

def compare_ablation_results(results_dict: Dict[str, Dict]) -> Dict[str, Any]:
    """比较消融实验结果"""
    comparison = {
        "performance_ranking": [],
        "component_importance": {},
        "interaction_effects": {}
    }
    
    # 性能排名
    performance_scores = [(name, results.get("overall_score", 0)) 
                         for name, results in results_dict.items()]
    performance_scores.sort(key=lambda x: x[1], reverse=True)
    
    comparison["performance_ranking"] = performance_scores
    
    # 组件重要性分析
    if "full_system" in results_dict:
        full_score = results_dict["full_system"].get("overall_score", 0)
        
        for name, results in results_dict.items():
            if name != "full_system":
                score = results.get("overall_score", 0)
                importance = (full_score - score) / full_score if full_score > 0 else 0
                comparison["component_importance"][name] = importance
    
    return comparison

def analyze_interaction_effects(results_dict: Dict[str, Dict]) -> Dict[str, Any]:
    """分析组件间交互效应"""
    interaction_effects = {
        "pairwise_interactions": {},
        "synergy_effects": {},
        "antagonistic_effects": {}
    }
    
    # 这里实现交互效应分析的逻辑
    # 由于需要更复杂的实验设计，这里提供一个简化版本
    
    return interaction_effects

def calculate_ablation_metrics(component_name: str, full_results: Dict, 
                             ablated_results: Dict) -> Dict[str, Any]:
    """计算单个组件的消融指标"""
    metrics = {
        "component_name": component_name,
        "performance_impact": {},
        "quality_impact": {},
        "efficiency_impact": {}
    }
    
    # 性能影响
    full_score = full_results.get("overall_score", 0)
    ablated_score = ablated_results.get("overall_score", 0)
    
    metrics["performance_impact"] = {
        "score_difference": full_score - ablated_score,
        "relative_impact": (full_score - ablated_score) / full_score if full_score > 0 else 0,
        "performance_retention": ablated_score / full_score if full_score > 0 else 0
    }
    
    # 质量影响
    full_quality = full_results.get("quality_metrics", {})
    ablated_quality = ablated_results.get("quality_metrics", {})
    
    for metric_name in full_quality.keys():
        if metric_name in ablated_quality:
            full_value = full_quality[metric_name]
            ablated_value = ablated_quality[metric_name]
            
            if metric_name not in metrics["quality_impact"]:
                metrics["quality_impact"][metric_name] = {}
            
            metrics["quality_impact"][metric_name] = {
                "full_value": full_value,
                "ablated_value": ablated_value,
                "difference": full_value - ablated_value,
                "relative_change": (full_value - ablated_value) / full_value if full_value > 0 else 0
            }
    
    # 效率影响
    full_efficiency = full_results.get("efficiency_metrics", {})
    ablated_efficiency = ablated_results.get("efficiency_metrics", {})
    
    for metric_name in full_efficiency.keys():
        if metric_name in ablated_efficiency:
            full_value = full_efficiency[metric_name]
            ablated_value = ablated_efficiency[metric_name]
            
            if metric_name not in metrics["efficiency_impact"]:
                metrics["efficiency_impact"][metric_name] = {}
            
            metrics["efficiency_impact"][metric_name] = {
                "full_value": full_value,
                "ablated_value": ablated_value,
                "difference": full_value - ablated_value,
                "relative_change": (full_value - ablated_value) / full_value if full_value > 0 else 0
            }
    
    return metrics

def run_ablation_experiment(base_config: Dict, component_to_ablate: str) -> Dict[str, Any]:
    """运行单个消融实验"""
    # 这是一个模拟函数，实际应该调用相应的生成系统
    
    ablated_config = base_config.copy()
    
    # 根据要消融的组件修改配置
    if component_to_ablate == "sentence_diversity":
        ablated_config["use_sentence_diversity"] = False
    elif component_to_ablate == "entity_diversity":
        ablated_config["use_entity_diversity"] = False
    elif component_to_ablate == "global_cache":
        ablated_config["use_global_cache"] = False
    elif component_to_ablate == "latent_scenarios":
        ablated_config["use_latent_scenarios"] = False
    elif component_to_ablate == "iterative_optimization":
        ablated_config["max_iterations"] = 1
    
    # 模拟实验结果
    results = {
        "config": ablated_config,
        "component_ablated": component_to_ablate,
        "overall_score": simulate_performance_score(ablated_config),
        "quality_metrics": simulate_quality_metrics(ablated_config),
        "efficiency_metrics": simulate_efficiency_metrics(ablated_config)
    }
    
    return results

def simulate_performance_score(config: Dict) -> float:
    """模拟性能得分"""
    base_score = 0.8
    
    # 根据配置调整得分
    if not config.get("use_sentence_diversity", True):
        base_score -= 0.1
    if not config.get("use_entity_diversity", True):
        base_score -= 0.15
    if not config.get("use_global_cache", True):
        base_score -= 0.05
    if not config.get("use_latent_scenarios", True):
        base_score -= 0.08
    if config.get("max_iterations", 10) == 1:
        base_score -= 0.12
    
    return max(0.0, min(1.0, base_score))

def simulate_quality_metrics(config: Dict) -> Dict[str, float]:
    """模拟质量指标"""
    base_metrics = {
        "naturalness": 0.75,
        "diversity": 0.70,
        "accuracy": 0.85,
        "consistency": 0.80
    }
    
    # 根据配置调整指标
    if not config.get("use_sentence_diversity", True):
        base_metrics["diversity"] -= 0.15
        base_metrics["naturalness"] -= 0.05
    
    if not config.get("use_entity_diversity", True):
        base_metrics["diversity"] -= 0.20
        base_metrics["accuracy"] -= 0.10
    
    if not config.get("use_latent_scenarios", True):
        base_metrics["diversity"] -= 0.10
        base_metrics["consistency"] -= 0.05
    
    if config.get("max_iterations", 10) == 1:
        base_metrics["accuracy"] -= 0.15
        base_metrics["consistency"] -= 0.10
    
    # 确保所有值在合理范围内
    for key in base_metrics:
        base_metrics[key] = max(0.0, min(1.0, base_metrics[key]))
    
    return base_metrics

def simulate_efficiency_metrics(config: Dict) -> Dict[str, float]:
    """模拟效率指标"""
    base_metrics = {
        "time_cost": 100.0,  # 秒
        "api_calls": 50.0,
        "memory_usage": 1024.0  # MB
    }
    
    # 根据配置调整指标
    if not config.get("use_global_cache", True):
        base_metrics["time_cost"] *= 1.5
        base_metrics["api_calls"] *= 1.3
    
    if not config.get("use_sentence_diversity", True):
        base_metrics["time_cost"] *= 0.8
        base_metrics["api_calls"] *= 0.7
    
    if not config.get("use_entity_diversity", True):
        base_metrics["time_cost"] *= 0.6
        base_metrics["api_calls"] *= 0.5
    
    if config.get("max_iterations", 10) == 1:
        base_metrics["time_cost"] *= 0.3
        base_metrics["api_calls"] *= 0.2
    
    return base_metrics

def generate_ablation_report(ablation_results: Dict[str, Dict]) -> Dict[str, Any]:
    """生成消融实验报告"""
    report = {
        "experiment_summary": {
            "total_experiments": len(ablation_results),
            "components_tested": list(ablation_results.keys())
        },
        "component_contributions": {},
        "performance_ranking": [],
        "recommendations": []
    }
    
    # 计算组件贡献
    if "full_system" in ablation_results:
        full_results = ablation_results["full_system"]
        
        for component, results in ablation_results.items():
            if component != "full_system":
                contribution = calculate_component_contribution(
                    full_results, {component: results}
                )
                report["component_contributions"][component] = contribution[component]
    
    # 性能排名
    performance_scores = [(name, results.get("overall_score", 0)) 
                         for name, results in ablation_results.items()]
    performance_scores.sort(key=lambda x: x[1], reverse=True)
    report["performance_ranking"] = performance_scores
    
    # 生成建议
    if report["component_contributions"]:
        # 找出最重要的组件
        most_important = max(report["component_contributions"].items(), 
                           key=lambda x: x[1]["relative_contribution"])
        
        report["recommendations"].append(
            f"最重要的组件是{most_important[0]}，"
            f"其贡献度为{most_important[1]['relative_contribution']:.2%}"
        )
        
        # 找出可以优化的组件
        least_important = min(report["component_contributions"].items(), 
                            key=lambda x: x[1]["relative_contribution"])
        
        if least_important[1]["relative_contribution"] < 0.05:
            report["recommendations"].append(
                f"组件{least_important[0]}的贡献度较低，可以考虑简化或移除"
            )
    
    return report
