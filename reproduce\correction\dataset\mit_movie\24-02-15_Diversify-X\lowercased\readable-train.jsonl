{"sentence": "where can i find tickets for the new james bond movie in theaters", "entity_names": ["james bond"], "entity_types": ["Title"]}
{"sentence": "i need to know the showtimes for the latest avengers film right now", "entity_names": ["avengers"], "entity_types": ["Title"]}
{"sentence": "what's the fastest way to get tickets for the upcoming star wars movie", "entity_names": ["star wars"], "entity_types": ["Title"]}
{"sentence": "can you tell me the plot of the latest cinderella movie?", "entity_names": ["cinderella"], "entity_types": ["Title"]}
{"sentence": "is there a movie directed by greta gerwig with a heartwarming storyline?", "entity_names": ["greta gerwig", "heartwarming"], "entity_types": ["Director", "Plot"]}
{"sentence": "which film featuring zendaya has the most romantic plot?", "entity_names": ["zendaya", "romantic"], "entity_types": ["Actor", "Plot"]}
{"sentence": "can i buy tickets for the latest james bond movie?", "entity_names": ["james bond"], "entity_types": ["Title"]}
{"sentence": "is there a sci-fi movie with a soundtrack by hans zimmer playing at the theater today?", "entity_names": ["sci-fi", "hans zimmer"], "entity_types": ["Genre", "Song"]}
{"sentence": "i'm looking for a movie directed by christopher nolan and rated r. do you have any suggestions?", "entity_names": ["christopher nolan", "r"], "entity_types": ["Director", "MPAA Rating"]}
{"sentence": "can you recommend a film with an iconic soundtrack from the 80s?", "entity_names": ["iconic", "80s"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "i'm looking for a movie with a character who performs a memorable musical number. can you suggest one?", "entity_names": ["musical number"], "entity_types": ["Character"]}
{"sentence": "can you provide me with the director of the documentary on the making of the dark knight?", "entity_names": ["documentary on the making of the dark knight"], "entity_types": ["Title"]}
{"sentence": "i'm interested in a film about the history of visual effects in cinema, what options do you have?", "entity_names": ["film", "history of visual effects in cinema"], "entity_types": ["Title", "Plot"]}
{"sentence": "show me a movie featuring a detailed behind-the-scenes look at the special effects in the lord of the rings trilogy.", "entity_names": ["behind-the-scenes look", "special effects", "lord of the rings trilogy"], "entity_types": ["Plot", "Plot", "Title"]}
{"sentence": "can you tell me about the director of the movie inception?", "entity_names": ["director", "inception"], "entity_types": ["Director", "Title"]}
{"sentence": "show me a movie with a really intense fight scene.", "entity_names": ["intense fight scene"], "entity_types": ["Plot"]}
{"sentence": "can you tell me about the special effects in the latest avengers movie?", "entity_names": ["avengers"], "entity_types": ["Title"]}
{"sentence": "who directed the transformers movie that came out in 2007?", "entity_names": ["directed", "transformers", "2007"], "entity_types": ["Director", "Title", "Year"]}
{"sentence": "show me a trailer for the new spider-man movie with tom holland.", "entity_names": ["trailer", "spider-man", "tom holland"], "entity_types": ["Trailer", "Title", "Actor"]}
{"sentence": "can you tell me about the director of the recent coming-of-age romantic comedy film?", "entity_names": ["coming-of-age", "romantic comedy"], "entity_types": ["Genre", "Genre"]}
{"sentence": "are there any action movies with a female lead that came out last year?", "entity_names": ["action", "female lead", "last year"], "entity_types": ["Genre", "Character", "Year"]}
{"sentence": "i'm interested in learning more about the special effects in the new science fiction movie, can you share some details?", "entity_names": ["special effects", "science fiction"], "entity_types": ["Plot", "Genre"]}
{"sentence": "i want to see a trailer for a thriller film directed by christopher nolan", "entity_names": ["trailer", "thriller", "christopher nolan"], "entity_types": ["Trailer", "Genre", "Director"]}
{"sentence": "can you recommend a movie teaser with a strong female lead and a song by beyonc\u00e9", "entity_names": ["teaser", "strong female lead", "beyonc\u00e9"], "entity_types": ["Trailer", "Character", "Song"]}
{"sentence": "show me a preview of a sci-fi movie from the 90s that has a cult following", "entity_names": ["preview", "sci-fi", "90s", "cult following"], "entity_types": ["Trailer", "Genre", "Year", "Genre"]}
{"sentence": "can you tell me about a movie with a famous song in it?", "entity_names": ["famous song"], "entity_types": ["Song"]}
{"sentence": "hey there! can you show me a trailer for the new james bond movie?", "entity_names": ["trailer", "james bond"], "entity_types": ["Trailer", "Title"]}
{"sentence": "hi! i'm in the mood for some action. can you recommend a movie teaser with lots of explosions?", "entity_names": ["action", "movie teaser", "explosions"], "entity_types": ["Genre", "Trailer", "Plot"]}
{"sentence": "hello! i'm looking for a romantic comedy. could you show me a teaser for a popular one?", "entity_names": ["romantic comedy", "teaser", "popular"], "entity_types": ["Genre", "Trailer", "Viewers' Rating"]}
{"sentence": "can you provide me with behind-the-scenes footage of the avengers: endgame movie", "entity_names": ["behind-the-scenes footage", "avengers: endgame"], "entity_types": ["Plot", "Title"]}
{"sentence": "who directed the film inception and can you show me some behind-the-scenes clips", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "can you suggest me a sci-fi movie that includes time travel?", "entity_names": ["sci-fi", "time travel"], "entity_types": ["Genre", "Plot"]}
{"sentence": "what action movies with a superhero theme have been popular lately?", "entity_names": ["action", "superhero"], "entity_types": ["Genre", "Plot"]}
{"sentence": "i'm looking for a horror film with zombies. can you recommend any?", "entity_names": ["horror", "zombies"], "entity_types": ["Genre", "Plot"]}
{"sentence": "can you recommend a movie with a behind-the-scenes featurette about its special effects and stunts?", "entity_names": ["behind-the-scenes featurette", "special effects and stunts"], "entity_types": ["Plot", "Genre"]}
{"sentence": "is there a sci-fi film from the 90s with a bonus feature showing the actors' training for their roles?", "entity_names": ["sci-fi film", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "i'm curious to know more about the storyline of a recent action movie, could you provide some details?", "entity_names": ["storyline", "action movie"], "entity_types": ["Plot", "Genre"]}
{"sentence": "i'm looking for a film that has a really intriguing plot, do you have any recommendations?", "entity_names": ["intriguing plot"], "entity_types": ["Plot"]}
{"sentence": "i'm interested in finding out more about a classic movie that has a compelling storyline, can you help me with that?", "entity_names": ["classic movie", "compelling storyline"], "entity_types": ["Title", "Plot"]}
{"sentence": "can you show me a trailer for the latest action movie directed by michael bay?", "entity_names": ["trailer", "action", "michael bay"], "entity_types": ["Trailer", "Genre", "Director"]}
{"sentence": "what movie released in 1994 has the song 'i will always love you' in it?", "entity_names": ["1994", "i will always love you"], "entity_types": ["Year", "Song"]}
{"sentence": "is there a movie with the character jack sparrow that has received a high viewers' rating?", "entity_names": ["jack sparrow", "high viewers' rating"], "entity_types": ["Character", "Viewers' Rating"]}
{"sentence": "hey, can you tell me about a movie directed by quentin tarantino that gives a behind-the-scenes look at filmmaking?", "entity_names": ["quentin tarantino", "behind-the-scenes"], "entity_types": ["Director", "Plot"]}
{"sentence": "i'm so pumped to learn about a classic action movie from the 80s with a killer soundtrack. can you show me a behind-the-scenes clip?", "entity_names": ["classic action", "80s", "killer soundtrack", "behind-the-scenes"], "entity_types": ["Genre", "Year", "Song", "Trailer"]}
{"sentence": "i'm dying to know about a suspenseful thriller directed by alfred hitchcock. can you please share some behind-the-scenes secrets with me?", "entity_names": ["suspenseful thriller", "alfred hitchcock", "behind-the-scenes"], "entity_types": ["Genre", "Director", "Plot"]}
{"sentence": "is the movie inception available to stream?", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "what are the top-rated 80s action movies available to stream?", "entity_names": ["80s", "action"], "entity_types": ["Year", "Genre"]}
{"sentence": "could you provide me with the trailer for the movie the dark knight", "entity_names": ["trailer", "the dark knight"], "entity_types": ["Trailer", "Title"]}
{"sentence": "i am interested in watching a movie directed by steven spielberg. can you recommend a film with a captivating trailer?", "entity_names": ["steven spielberg", "trailer"], "entity_types": ["Director", "Trailer"]}
{"sentence": "what is the viewers' rating for the movie inception", "entity_names": ["viewers' rating", "inception"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "what are the showtimes for the new marvel movie?", "entity_names": ["marvel"], "entity_types": ["Genre"]}
{"sentence": "do you have tickets available for the re-release of the lion king?", "entity_names": ["the lion king"], "entity_types": ["Title"]}
{"sentence": "can you tell me about the director of the latest marvel movie and any special features on the blu-ray edition?", "entity_names": ["marvel movie", "special features"], "entity_types": ["Title", "Plot"]}
{"sentence": "i'm looking for a movie from the 90s with a popular soundtrack and some bloopers from the filming.", "entity_names": ["90s", "popular soundtrack", "bloopers"], "entity_types": ["Year", "Song", "Plot"]}
{"sentence": "are there any recent action movies with an audio commentary by the director or main cast members?", "entity_names": ["action movies", "audio commentary", "director", "main cast members"], "entity_types": ["Genre", "Plot", "Director", "Actor"]}
{"sentence": "can you recommend a movie with tom hanks and meg ryan?", "entity_names": ["tom hanks", "meg ryan"], "entity_types": ["Actor", "Actor"]}
{"sentence": "can you recommend a movie with a really intense trailer?", "entity_names": [], "entity_types": []}
{"sentence": "what are some popular movies with amazing teasers?", "entity_names": ["popular", "amazing", "teasers"], "entity_types": ["Viewers' Rating", "Review", "Trailer"]}
{"sentence": "which movie has the most intriguing teaser of all time?", "entity_names": ["intriguing", "teaser"], "entity_types": ["Review", "Trailer"]}
{"sentence": "can you recommend any movies with the song 'unchained melody'?", "entity_names": ["unchained melody"], "entity_types": ["Song"]}
{"sentence": "what movie from the 1950s has the best soundtrack?", "entity_names": ["1950s", "best soundtrack"], "entity_types": ["Year", "Review"]}
{"sentence": "are there any old movies with a great love song?", "entity_names": ["great love song"], "entity_types": ["Review"]}
{"sentence": "can i watch the movie sleepless in seattle on any streaming platforms?", "entity_names": ["sleepless in seattle"], "entity_types": ["Title"]}
{"sentence": "is the movie when harry met sally available to stream anywhere?", "entity_names": ["when harry met sally"], "entity_types": ["Title"]}
{"sentence": "are there any classic romantic comedies like pretty woman that i can watch on a streaming service?", "entity_names": ["romantic comedies", "pretty woman"], "entity_types": ["Genre", "Title"]}
{"sentence": "can you recommend a movie with an amazing soundtrack?", "entity_names": ["amazing soundtrack"], "entity_types": ["Review"]}
{"sentence": "who composed the music for the movie inception?", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "what are some of the best musical movies of all time?", "entity_names": ["best musical"], "entity_types": ["Viewers' Rating"]}
{"sentence": "are there any g-rated animated movies playing today?", "entity_names": ["g-rated", "animated"], "entity_types": ["MPAA Rating", "Genre"]}
{"sentence": "what family-friendly movies are showing this weekend?", "entity_names": ["family-friendly"], "entity_types": ["Viewers' Rating"]}
{"sentence": "i'm looking for showtimes and tickets for the movie inception directed by christopher nolan", "entity_names": ["inception", "christopher nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "can you find me tickets for the latest marvel movie, with a high viewers' rating?", "entity_names": ["marvel", "high viewers' rating"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "i want to watch a movie released in 2020 with a compelling plot and a great soundtrack.", "entity_names": ["2020", "compelling plot", "great soundtrack"], "entity_types": ["Year", "Plot", "Song"]}
{"sentence": "hey, what's the name of the movie with the cool song 'eye of the tiger' in it?", "entity_names": ["eye of the tiger"], "entity_types": ["Song"]}
{"sentence": "do you know any good movies with awesome music like 'bohemian rhapsody' in them?", "entity_names": ["bohemian rhapsody"], "entity_types": ["Song"]}
{"sentence": "i'm looking for a film with a killer soundtrack, like 'purple rain' or 'footloose'. any recommendations?", "entity_names": ["purple rain", "footloose"], "entity_types": ["Song", "Song"]}
{"sentence": "can you give me a brief summary of the film inception", "entity_names": ["summary", "inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "what is the storyline for the movie the shawshank redemption", "entity_names": ["storyline", "the shawshank redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "tell me about the plot of the action movie mad max: fury road", "entity_names": ["plot", "mad max: fury road"], "entity_types": ["Plot", "Title"]}
{"sentence": "are there any streaming platforms where i can watch the movie inception?", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "can you recommend a movie from the 1980s that has a great soundtrack?", "entity_names": ["1980s", "great soundtrack"], "entity_types": ["Year", "Review"]}
{"sentence": "i'm looking for a comedy film directed by christopher guest, do you have any suggestions?", "entity_names": ["comedy", "christopher guest"], "entity_types": ["Genre", "Director"]}
{"sentence": "can you recommend a highly-rated action movie from the 1990s?", "entity_names": ["highly-rated", "action movie", "1990s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "show me a film with a compelling plot and strong performances from meryl streep.", "entity_names": ["meryl streep"], "entity_types": ["Actor"]}
{"sentence": "who's in that new action movie where stuff explodes?", "entity_names": ["new action movie"], "entity_types": ["Genre"]}
{"sentence": "what's that movie where tom hanks plays a character stranded on an island?", "entity_names": ["tom hanks"], "entity_types": ["Actor"]}
{"sentence": "do you know who directed the latest comedy film with jennifer lawrence?", "entity_names": ["latest comedy film", "jennifer lawrence"], "entity_types": ["Genre", "Actor"]}
{"sentence": "can you tell me what awards and nominations the shawshank redemption received?", "entity_names": ["awards and nominations", "shawshank redemption"], "entity_types": ["Review", "Title"]}
{"sentence": "i'm looking for an action movie from the 2000s with a high viewers' rating. any suggestions?", "entity_names": ["action", "2000s", "high viewers' rating"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "can you tell me the showtimes for the avengers: endgame?", "entity_names": ["avengers: endgame"], "entity_types": ["Title"]}
{"sentence": "are there any available tickets for the latest fast and furious movie?", "entity_names": ["fast and furious"], "entity_types": ["Title"]}
{"sentence": "what is the genre of the latest james bond film?", "entity_names": ["james bond"], "entity_types": ["Character"]}
{"sentence": "what's the highest rated movie of 2021 so far?", "entity_names": ["highest rated", "2021"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "give me the best action film of all time now!", "entity_names": ["best", "action", "all time"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "could you show me the trailer for the latest james bond film?", "entity_names": ["trailer", "james bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "i'm in the mood for something exciting! can you recommend a movie with an action-packed teaser?", "entity_names": ["exciting", "action-packed", "teaser"], "entity_types": ["Viewers' Rating", "Genre", "Trailer"]}
{"sentence": "i'm feeling really curious about the new animated film. can you play a teaser for it?", "entity_names": ["curious", "animated", "teaser"], "entity_types": ["Viewers' Rating", "Genre", "Trailer"]}
{"sentence": "i'm not sure what to watch tonight, can you give me a brief overview of a movie with a thrilling plot?", "entity_names": ["thrilling"], "entity_types": ["Plot"]}
{"sentence": "i can't decide what movie to watch, do you have any recommendations for a film with a suspenseful storyline?", "entity_names": ["recommendations", "suspenseful"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "i'm in the mood for a movie with an engaging storyline, can you tell me about a film that has a captivating plot?", "entity_names": ["engaging"], "entity_types": ["Genre"]}
{"sentence": "i'm looking for a movie with a captivating and unpredictable plot", "entity_names": ["captivating and unpredictable"], "entity_types": ["Plot"]}
{"sentence": "can you recommend a film with an intriguing storyline and unexpected twists?", "entity_names": ["intriguing storyline and unexpected twists"], "entity_types": ["Plot"]}
{"sentence": "i want to watch a movie with a gripping and mind-bending plot", "entity_names": ["gripping and mind-bending"], "entity_types": ["Plot"]}
{"sentence": "what movie features the song 'let it go'?", "entity_names": ["let it go"], "entity_types": ["Song"]}
{"sentence": "i want to watch a film with a romantic soundtrack, can you suggest one?", "entity_names": ["romantic", "soundtrack"], "entity_types": ["Genre", "Plot"]}
{"sentence": "what children's movies are in the fantasy genre?", "entity_names": ["children's movies", "fantasy"], "entity_types": ["Genre", "Genre"]}
{"sentence": "can you recommend a family-friendly movie in the animated musical genre?", "entity_names": ["family-friendly", "animated musical"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "what animated films can you suggest that fall under the adventure genre?", "entity_names": ["animated films", "adventure"], "entity_types": ["Genre", "Genre"]}
{"sentence": "can you tell me about the making-of the newest star wars movie?", "entity_names": ["making-of", "newest star wars movie"], "entity_types": ["Plot", "Title"]}
{"sentence": "is there a documentary about the special effects in the marvel superhero movies?", "entity_names": ["documentary", "special effects", "marvel superhero movies"], "entity_types": ["Genre", "Plot", "Title"]}
{"sentence": "i'm looking for a film that shows the director's cut and deleted scenes from the dark knight.", "entity_names": ["director's cut", "deleted scenes", "the dark knight"], "entity_types": ["Plot", "Plot", "Title"]}
{"sentence": "can you tell me when and where i can see the new spider-man movie?", "entity_names": ["spider-man"], "entity_types": ["Title"]}
{"sentence": "i'm really looking forward to watching a romantic comedy with julia roberts, can you suggest one for me?", "entity_names": ["romantic comedy", "julia roberts"], "entity_types": ["Genre", "Actor"]}
{"sentence": "i heard great things about the new james bond movie, can you help me find tickets for it?", "entity_names": ["james bond"], "entity_types": ["Title"]}
{"sentence": "can you tell me about a recent action movie with a thrilling story?", "entity_names": ["action movie", "thrilling story"], "entity_types": ["Genre", "Plot"]}
{"sentence": "who directed the romantic comedy that everyone loved last year?", "entity_names": ["romantic comedy", "last year"], "entity_types": ["Genre", "Year"]}
{"sentence": "i'm interested in a movie with a powerful soundtrack. can you recommend one?", "entity_names": ["powerful soundtrack"], "entity_types": ["Song"]}
{"sentence": "can you recommend a highly rated comedy from the 2000s?", "entity_names": ["highly rated", "comedy", "2000s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "who are the main actors in the latest sci-fi movie directed by christopher nolan?", "entity_names": ["sci-fi", "christopher nolan"], "entity_types": ["Genre", "Director"]}
{"sentence": "i'm looking for a popular action movie with great special effects. can you suggest one?", "entity_names": ["popular", "action", "great special effects"], "entity_types": ["Viewers' Rating", "Genre", "Review"]}
{"sentence": "can you tell me the plot of the movie inception directed by christopher nolan?", "entity_names": ["plot", "inception", "christopher nolan"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "i'm looking for a movie with an exciting and mysterious plot. can you recommend a film for me?", "entity_names": [], "entity_types": []}
{"sentence": "i want to watch a movie that has a thrilling and engaging storyline. can you suggest one?", "entity_names": ["thrilling and engaging"], "entity_types": ["Plot"]}
{"sentence": "can you show me the trailer for the new james bond movie?", "entity_names": ["trailer", "james bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "i'm looking for a movie teaser with an epic battle scene. can you recommend one?", "entity_names": ["teaser", "epic battle scene"], "entity_types": ["Trailer", "Plot"]}
{"sentence": "could you play a sneak peek of the upcoming marvel movie?", "entity_names": ["sneak peek", "marvel"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "can you give me a brief overview of the plot for the movie inception?", "entity_names": ["plot", "inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "i'm looking for a movie directed by christopher nolan with a mind-bending plot and a thrilling soundtrack.", "entity_names": ["christopher nolan", "mind-bending plot", "thrilling soundtrack"], "entity_types": ["Director", "Plot", "Song"]}
{"sentence": "show me a film featuring an ensemble cast, a gripping plot, and a high viewers' rating.", "entity_names": ["ensemble cast", "gripping plot", "high viewers' rating"], "entity_types": ["Character", "Plot", "Viewers' Rating"]}
{"sentence": "what's that movie with the really cool fight scenes and is considered a classic martial arts film?", "entity_names": ["really cool fight scenes", "classic", "martial arts"], "entity_types": ["Plot", "Viewers' Rating", "Genre"]}
{"sentence": "who directed that awesome horror movie with a killer clown that scared the pants off everyone?", "entity_names": ["horror", "killer clown", "scared the pants off everyone"], "entity_types": ["Genre", "Character", "Review"]}
{"sentence": "is there a romantic comedy with a really catchy theme song that came out in the 90s?", "entity_names": ["romantic comedy", "catchy theme song", "90s"], "entity_types": ["Genre", "Song", "Year"]}
{"sentence": "can you find me a movie teaser that reveals just enough, but not too much?", "entity_names": ["movie teaser"], "entity_types": ["Trailer"]}
{"sentence": "is there a film out there with a sneak peek that's worth watching?", "entity_names": ["sneak peek"], "entity_types": ["Trailer"]}
{"sentence": "could you show me a preview for a new release that leaves something to the imagination?", "entity_names": ["preview"], "entity_types": ["Trailer"]}
{"sentence": "can i stream the movie arrival on any platform?", "entity_names": ["arrival"], "entity_types": ["Title"]}
{"sentence": "is there a streaming service where i can watch the movie titanic?", "entity_names": ["streaming service", "titanic"], "entity_types": ["Plot", "Title"]}
{"sentence": "which platform has the movie the dark knight available for streaming?", "entity_names": ["platform", "the dark knight"], "entity_types": ["Plot", "Title"]}
{"sentence": "i'm so bored, can you recommend a thriller movie directed by alfred hitchcock?", "entity_names": ["thriller", "alfred hitchcock"], "entity_types": ["Genre", "Director"]}
{"sentence": "i need something to watch, how about a comedy film from the 1990s?", "entity_names": ["comedy", "1990s"], "entity_types": ["Genre", "Year"]}
{"sentence": "i'm bored out of my mind, show me a classic romance movie featuring audrey hepburn.", "entity_names": ["romance", "audrey hepburn"], "entity_types": ["Genre", "Actor"]}
{"sentence": "i'm looking for a feel-good movie that has won multiple awards, any recommendations?", "entity_names": ["feel-good", "multiple awards"], "entity_types": ["Genre", "Review"]}
{"sentence": "can you tell me about the making-of the movie interstellar?", "entity_names": ["interstellar"], "entity_types": ["Title"]}
{"sentence": "who directed the behind-the-scenes documentary for the movie jurassic park?", "entity_names": ["jurassic park"], "entity_types": ["Title"]}
{"sentence": "show me some exclusive interviews with the cast of the movie titanic.", "entity_names": ["cast", "titanic"], "entity_types": ["Character", "Title"]}
{"sentence": "who directed the action thriller film with the highest viewers' rating?", "entity_names": ["action thriller", "highest viewers' rating"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "can you tell me which movie from the 90s has denzel washington as the lead actor?", "entity_names": ["90s", "denzel washington"], "entity_types": ["Year", "Actor"]}
{"sentence": "i'm looking for a fantasy movie directed by a female director, can you recommend one?", "entity_names": ["fantasy", "female director"], "entity_types": ["Genre", "Director"]}
{"sentence": "can you recommend a heartwarming romance movie from the 90s with a strong female lead", "entity_names": ["romance", "90s", "strong female lead"], "entity_types": ["Genre", "Year", "Character"]}
{"sentence": "i'm looking for a feel-good drama film from the 80s that showcases family values", "entity_names": ["feel-good", "drama", "80s", "family values"], "entity_types": ["Genre", "Genre", "Year", "Plot"]}
{"sentence": "what comedy movie directed by nancy meyers would you suggest for a girls' night out", "entity_names": ["comedy", "nancy meyers", "girls' night out"], "entity_types": ["Genre", "Director", "Viewers' Rating"]}
{"sentence": "can i see the trailer for the new high school musical movie?", "entity_names": ["trailer", "the new high school musical movie"], "entity_types": ["Trailer", "Title"]}
{"sentence": "what's a good coming-of-age film that came out this year that i can watch?", "entity_names": ["coming-of-age", "this year"], "entity_types": ["Genre", "Year"]}
{"sentence": "what is the viewers' rating of the film the shawshank redemption?", "entity_names": ["viewers' rating", "the shawshank redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "could you recommend a movie with meryl streep as the lead actress?", "entity_names": ["meryl streep"], "entity_types": ["Actor"]}
{"sentence": "can you tell me about the director of the movie lawrence of arabia?", "entity_names": ["director", "lawrence of arabia"], "entity_types": ["Director", "Title"]}
{"sentence": "i'm a fan of robert de niro, can you recommend a movie he starred in?", "entity_names": ["robert de niro"], "entity_types": ["Actor"]}
{"sentence": "what is the viewers' rating for the film the godfather?", "entity_names": ["viewers' rating", "the godfather"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "can you tell me about the making-of featurette for the movie inception?", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "who was the director of the behind-the-scenes documentary for the film jurassic park?", "entity_names": ["jurassic park"], "entity_types": ["Title"]}
{"sentence": "i'm curious about the special effects in the movie avatar, can you show me a behind-the-scenes clip?", "entity_names": ["avatar"], "entity_types": ["Title"]}
{"sentence": "what kind of movie is the dark knight? like, is it action or thriller or what?", "entity_names": ["the dark knight"], "entity_types": ["Title"]}
{"sentence": "can you recommend a rom-com from the 90s? you know, something lighthearted and funny?", "entity_names": ["rom-com", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "who directed that sci-fi movie about time travel and stuff? i think it's called inception or something like that.", "entity_names": ["sci-fi", "time travel", "inception"], "entity_types": ["Genre", "Plot", "Title"]}
{"sentence": "can you tell me about any award-winning movies from the 1990s", "entity_names": ["award-winning", "1990s"], "entity_types": ["Review", "Year"]}
{"sentence": "i'm interested in a critically acclaimed film from the 2010s", "entity_names": ["critically acclaimed", "2010s"], "entity_types": ["Review", "Year"]}
{"sentence": "could you recommend a movie that has received multiple nominations?", "entity_names": ["multiple nominations"], "entity_types": ["Review"]}
{"sentence": "who directed the fantasy film with the best visual effects of all time?", "entity_names": ["fantasy", "visual effects"], "entity_types": ["Genre", "Genre"]}
{"sentence": "can you recommend a movie directed by martin scorsese that features leonardo dicaprio?", "entity_names": ["martin scorsese", "leonardo dicaprio"], "entity_types": ["Director", "Actor"]}
{"sentence": "what is the highest-rated comedy film that stars jim carrey?", "entity_names": ["highest-rated", "comedy", "jim carrey"], "entity_types": ["Viewers' Rating", "Genre", "Actor"]}
{"sentence": "i am interested in learning more about the filming locations of the movie the godfather. can you assist me with that?", "entity_names": ["filming locations", "the godfather"], "entity_types": ["Plot", "Title"]}
{"sentence": "what are some highly acclaimed science fiction movies from the 1980s?", "entity_names": ["science fiction", "1980s"], "entity_types": ["Genre", "Year"]}
{"sentence": "what is the title of the movie featuring the song 'my heart will go on'?", "entity_names": ["my heart will go on"], "entity_types": ["Song"]}
{"sentence": "can you tell me the name of a movie with a great soundtrack?", "entity_names": ["great soundtrack"], "entity_types": ["Review"]}
{"sentence": "do you have any information on a movie with a famous song by elvis presley?", "entity_names": ["famous song by elvis presley"], "entity_types": ["Song"]}
{"sentence": "i'm curious, is there a trailer for the new martin scorsese movie about organized crime?", "entity_names": ["trailer", "martin scorsese", "organized crime"], "entity_types": ["Trailer", "Director", "Plot"]}
{"sentence": "what's the behind-the-scenes story of the latest star wars movie?", "entity_names": ["behind-the-scenes story", "star wars"], "entity_types": ["Plot", "Title"]}
{"sentence": "yo, who's the director of that new action flick with all the explosions and stuff?", "entity_names": ["action flick", "explosions"], "entity_types": ["Genre", "Plot"]}
{"sentence": "what's the deal with that movie where the dude from avengers plays a spy or something?", "entity_names": ["spy"], "entity_types": ["Genre"]}
{"sentence": "i heard there's a film coming out soon with that badass actress from the last sci-fi movie. what's it called?", "entity_names": ["badass actress", "sci-fi movie"], "entity_types": ["Actor", "Genre"]}
{"sentence": "can you play the trailer for the new animated movie featuring talking animals?", "entity_names": ["trailer", "new", "animated", "talking animals"], "entity_types": ["Trailer", "Year", "Genre", "Plot"]}
{"sentence": "what family-friendly movie with a catchy theme song was released in the past year?", "entity_names": ["family-friendly", "catchy theme song", "past year"], "entity_types": ["Genre", "Song", "Year"]}
{"sentence": "who directed the latest fantasy adventure film for kids?", "entity_names": ["fantasy adventure", "kids"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "i need a quick rundown of the plot for the latest james bond film", "entity_names": ["james bond"], "entity_types": ["Character"]}
{"sentence": "can you tell me what the new marvel superhero movie is all about?", "entity_names": ["marvel", "superhero"], "entity_types": ["Genre", "Genre"]}
{"sentence": "i'm in a rush, but i need to know the storyline of that award-winning romantic movie from last year", "entity_names": ["romantic", "last year"], "entity_types": ["Genre", "Year"]}
{"sentence": "can you recommend a movie with an iconic soundtrack that is perfect for road trips?", "entity_names": ["iconic soundtrack", "road trips"], "entity_types": ["Song", "Genre"]}
{"sentence": "show me a movie with a thrilling action scene accompanied by an exciting musical score.", "entity_names": ["thrilling action scene", "exciting musical score"], "entity_types": ["Plot", "Song"]}
{"sentence": "i'm looking for a film with an unforgettable song that perfectly captures the adventurous spirit of the characters.", "entity_names": ["unforgettable song", "adventurous spirit"], "entity_types": ["Song", "Character"]}
{"sentence": "what is the viewers' rating for the movie inception directed by christopher nolan", "entity_names": ["viewers' rating", "inception", "christopher nolan"], "entity_types": ["Viewers' Rating", "Title", "Director"]}
{"sentence": "could you provide the plot summary for the film shawshank redemption", "entity_names": ["plot summary", "shawshank redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "show me the trailer for the latest james bond movie", "entity_names": ["trailer", "latest", "james bond"], "entity_types": ["Trailer", "Year", "Character"]}
{"sentence": "can you tell me which movies feature the song 'bohemian rhapsody' by queen?", "entity_names": ["bohemian rhapsody"], "entity_types": ["Song"]}
{"sentence": "i'm looking for a movie with an epic original soundtrack that will blow my mind. can you suggest one?", "entity_names": ["epic original soundtrack"], "entity_types": ["Song"]}
{"sentence": "what film has the most iconic music of all time?", "entity_names": ["most iconic music"], "entity_types": ["Review"]}
{"sentence": "what awards did the movie the shape of water win?", "entity_names": ["the shape of water"], "entity_types": ["Title"]}
{"sentence": "has the director of parasite received any nominations for the film?", "entity_names": ["parasite"], "entity_types": ["Title"]}
{"sentence": "what are some action movies with an epic soundtrack?", "entity_names": ["action movies", "epic soundtrack"], "entity_types": ["Genre", "Song"]}
{"sentence": "show me a movie from the 90s with a killer soundtrack.", "entity_names": ["90s", "killer soundtrack"], "entity_types": ["Year", "Song"]}
{"sentence": "can you recommend a movie with an awesome theme song?", "entity_names": ["awesome theme song"], "entity_types": ["Song"]}
{"sentence": "what is the specific subgenre of horror does the movie hereditary fall under", "entity_names": ["specific subgenre", "horror", "hereditary"], "entity_types": ["Review", "Genre", "Title"]}
{"sentence": "can you recommend a crime movie directed by quentin tarantino that has a neo-noir style", "entity_names": ["crime", "quentin tarantino", "neo-noir"], "entity_types": ["Genre", "Director", "Genre"]}
{"sentence": "which science fiction film from the 1980s directed by james cameron is known for its groundbreaking special effects", "entity_names": ["science fiction", "1980s", "james cameron", "groundbreaking special effects"], "entity_types": ["Genre", "Year", "Director", "Review"]}
{"sentence": "can i watch the movie the shawshank redemption on any streaming platform?", "entity_names": ["the shawshank redemption"], "entity_types": ["Title"]}
{"sentence": "is the movie inception available for streaming anywhere?", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "i'm in the mood for a classic movie. where can i watch casablanca?", "entity_names": ["casablanca"], "entity_types": ["Title"]}
{"sentence": "can you recommend a must-see action movie from the 90s directed by michael bay?", "entity_names": ["must-see", "action", "90s", "michael bay"], "entity_types": ["Viewers' Rating", "Genre", "Year", "Director"]}
{"sentence": "i'm looking for a critically acclaimed drama film from the 2000s with a strong performance by meryl streep.", "entity_names": ["critically acclaimed", "drama", "2000s", "meryl streep"], "entity_types": ["Review", "Genre", "Year", "Actor"]}
{"sentence": "can you tell me about the plot of the movie blade runner 2049 directed by denis villeneuve?", "entity_names": ["blade runner 2049", "denis villeneuve"], "entity_types": ["Title", "Director"]}
{"sentence": "what is the storyline of the romantic movie la la land?", "entity_names": ["romantic", "la la land"], "entity_types": ["Genre", "Title"]}
{"sentence": "could you provide a summary of the plot for the film inception directed by christopher nolan?", "entity_names": ["plot", "inception", "christopher nolan"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "can you tell me what the latest liam neeson movie is about?", "entity_names": ["liam neeson"], "entity_types": ["Actor"]}
{"sentence": "hey, what's the deal with that new horror flick with the haunted house?", "entity_names": ["horror", "haunted house"], "entity_types": ["Genre", "Plot"]}
{"sentence": "what's the story behind that tom hanks movie where he gets stranded on a desert island?", "entity_names": ["tom hanks", "desert island"], "entity_types": ["Actor", "Plot"]}
{"sentence": "who directed the movie titanic and what is the viewers' rating for it?", "entity_names": ["directed", "titanic", "viewers' rating"], "entity_types": ["Director", "Title", "Viewers' Rating"]}
{"sentence": "can you recommend me a movie with meryl streep as the lead actor?", "entity_names": ["meryl streep", "lead actor"], "entity_types": ["Actor", "Character"]}
{"sentence": "what is the plot of the film inception directed by christopher nolan?", "entity_names": ["plot", "inception", "christopher nolan"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "hey, what kind of movie is the avengers? like, is it an action movie or something else?", "entity_names": ["the avengers", "action"], "entity_types": ["Title", "Genre"]}
{"sentence": "i'm in the mood for a scary movie. can you recommend a good horror film from the 80s or 90s?", "entity_names": ["scary movie", "horror film", "80s or 90s"], "entity_types": ["Genre", "Genre", "Year"]}
{"sentence": "do you know if there are any good romantic comedy movies coming out soon? i could use a good laugh and some lovey-dovey stuff.", "entity_names": ["romantic comedy"], "entity_types": ["Genre"]}
{"sentence": "can you recommend a good thriller from the 90s?", "entity_names": ["recommend", "thriller", "90s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "can you tell me who directed the movie 'the goonies', and if it's as good as i remember?", "entity_names": ["directed", "the goonies"], "entity_types": ["Director", "Title"]}
{"sentence": "i remember a movie with tom hanks and meg ryan, can you remind me of the title and the year it came out?", "entity_names": ["tom hanks", "meg ryan", "title", "year"], "entity_types": ["Actor", "Actor", "Title", "Year"]}
{"sentence": "i loved the soundtrack in 'dirty dancing', can you tell me the name of the song that played during the final dance scene?", "entity_names": ["soundtrack", "dirty dancing"], "entity_types": ["Song", "Title"]}
{"sentence": "who directed the movie the shawshank redemption?", "entity_names": ["the shawshank redemption"], "entity_types": ["Title"]}
{"sentence": "show me a movie starring meryl streep.", "entity_names": ["meryl streep"], "entity_types": ["Actor"]}
{"sentence": "can you recommend a movie with a top-notch plot and a gripping review?", "entity_names": ["top-notch", "gripping"], "entity_types": ["Viewers' Rating", "Review"]}
{"sentence": "what movie from the 90s has an exceptional rating and a memorable song in its soundtrack?", "entity_names": ["90s", "exceptional", "memorable"], "entity_types": ["Year", "Viewers' Rating", "Song"]}
{"sentence": "i'm looking for a film that received rave reviews and features an outstanding performance by a renowned actor, can you suggest one?", "entity_names": ["rave reviews", "outstanding", "renowned actor"], "entity_types": ["Review", "Viewers' Rating", "Actor"]}
{"sentence": "can you recommend a movie with a heartbreaking plot that received high viewers' ratings?", "entity_names": ["heartbreaking", "high viewers' ratings"], "entity_types": ["Plot", "Viewers' Rating"]}
{"sentence": "i'm feeling down. can you suggest a movie with a touching storyline and a beautiful song?", "entity_names": ["touching storyline", "beautiful song"], "entity_types": ["Plot", "Song"]}
{"sentence": "i need a movie that will make me cry. can you find a title with a heartwarming plot and a highly rated director?", "entity_names": ["make me cry", "heartwarming plot", "highly rated director"], "entity_types": ["Review", "Plot", "Director"]}
{"sentence": "what's the storyline for the highest rated animated movie of all time?", "entity_names": ["storyline", "highest rated", "animated"], "entity_types": ["Plot", "Viewers' Rating", "Genre"]}
{"sentence": "i'm curious, could you tell me the basic premise of the most recent romantic comedy?", "entity_names": ["basic premise", "most recent", "romantic comedy"], "entity_types": ["Plot", "Year", "Genre"]}
{"sentence": "can you tell me the plot of the movie inception", "entity_names": ["plot", "inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "what is the storyline of the film interstellar", "entity_names": ["storyline", "interstellar"], "entity_types": ["Plot", "Title"]}
{"sentence": "describe the synopsis of the movie the shawshank redemption", "entity_names": ["synopsis", "the shawshank redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "i'm not sure what type of movie i'm in the mood for, maybe something thrilling or suspenseful?", "entity_names": ["thrilling", "suspenseful"], "entity_types": ["Genre", "Genre"]}
{"sentence": "i'm trying to decide on a movie to watch tonight, should i go for something classic or a more modern film?", "entity_names": ["classic", "modern"], "entity_types": ["Genre", "Genre"]}
{"sentence": "i can't make up my mind if i want to watch a comedy or a romantic comedy, what do you suggest?", "entity_names": ["comedy", "romantic comedy"], "entity_types": ["Genre", "Genre"]}
{"sentence": "i don't have much time, just tell me the storyline for the film the shawshank redemption", "entity_names": ["storyline", "the shawshank redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "i need a brief synopsis for the movie pulp fiction, hurry up!", "entity_names": ["brief synopsis", "pulp fiction"], "entity_types": ["Plot", "Title"]}
{"sentence": "who directed the science fiction film from 1997 that received the highest rating from viewers?", "entity_names": ["science fiction film", "1997", "highest rating"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "can you tell me about the making of the film 'inception' directed by christopher nolan?", "entity_names": ["inception", "christopher nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "what are some iconic historical films directed by steven spielberg that i should watch?", "entity_names": ["historical films", "steven spielberg", "should watch"], "entity_types": ["Genre", "Director", "Viewers' Rating"]}
{"sentence": "what is the primary genre and subgenre of the film blade runner 2049?", "entity_names": ["blade runner 2049"], "entity_types": ["Title"]}
{"sentence": "can you recommend a movie with a complex plot that falls under the science fiction genre?", "entity_names": ["complex plot", "science fiction"], "entity_types": ["Plot", "Genre"]}
{"sentence": "which director is known for creating movies in the psychological thriller subgenre?", "entity_names": ["psychological thriller"], "entity_types": ["Genre"]}
{"sentence": "i'm not sure where to watch the movie the shawshank redemption, can you tell me?", "entity_names": ["the shawshank redemption"], "entity_types": ["Title"]}
{"sentence": "i'm not sure if i should watch a comedy or a thriller, do you have any recommendations of movies available on streaming platforms?", "entity_names": ["comedy", "thriller"], "entity_types": ["Genre", "Genre"]}
{"sentence": "i can't decide what to watch tonight, is there a popular movie from the 90s available for streaming?", "entity_names": ["90s"], "entity_types": ["Year"]}
{"sentence": "retrieve the trailer for the 2020 science fiction film directed by christopher nolan.", "entity_names": ["2020", "science fiction", "christopher nolan"], "entity_types": ["Year", "Genre", "Director"]}
{"sentence": "find me the teaser for the latest installment of the james bond series.", "entity_names": ["james bond"], "entity_types": ["Character"]}
{"sentence": "show me the sneak peek of the upcoming marvel superhero movie scheduled for release next year.", "entity_names": ["marvel", "next year"], "entity_types": ["Genre", "Year"]}
{"sentence": "can i stream the movie goodfellas?", "entity_names": ["goodfellas"], "entity_types": ["Title"]}
{"sentence": "is there a streaming platform that has the godfather?", "entity_names": ["the godfather"], "entity_types": ["Title"]}
{"sentence": "where can i watch the shawshank redemption online?", "entity_names": ["the shawshank redemption"], "entity_types": ["Title"]}
{"sentence": "hey, what's the latest comedy that's playing in theaters right now?", "entity_names": ["latest comedy"], "entity_types": ["Genre"]}
{"sentence": "can you tell me if there are any action movies from the 90s showing at the theater nearby?", "entity_names": ["action movies", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "is there a drama film directed by christopher nolan that i can watch this weekend?", "entity_names": ["drama", "christopher nolan", "this weekend"], "entity_types": ["Genre", "Director", "Viewers' Rating"]}
{"sentence": "i want to know about the making of the movie inception", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "show me a documentary about the filming process of the dark knight", "entity_names": ["the dark knight"], "entity_types": ["Title"]}
{"sentence": "what insider details can you tell me about the production of the shawshank redemption", "entity_names": ["the shawshank redemption"], "entity_types": ["Title"]}
{"sentence": "i'm curious to learn about the creative process behind a classic movie that was quite influential. can you provide insight into the making of this film?", "entity_names": ["classic movie", "influential"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "i'm interested in finding out more about a film that has an intriguing backstory in terms of its production. can you share some details about this?", "entity_names": ["intriguing backstory", "production"], "entity_types": ["Plot", "Genre"]}
{"sentence": "i'd like to know about a movie that had a famous director known for their unique approach to filmmaking. can you tell me more about this film and its behind-the-scenes dynamics?", "entity_names": ["famous director", "unique approach"], "entity_types": ["Director", "Director"]}
{"sentence": "who directed the movie 'jurassic park'?", "entity_names": ["jurassic park"], "entity_types": ["Title"]}
{"sentence": "what year did the movie 'the avengers' come out?", "entity_names": ["the avengers"], "entity_types": ["Title"]}
{"sentence": "can you show me a trailer for the movie 'star wars'?", "entity_names": ["trailer", "star wars"], "entity_types": ["Trailer", "Title"]}
{"sentence": "has that movie with the intense car chase scene won any awards?", "entity_names": ["intense car chase scene"], "entity_types": ["Plot"]}
{"sentence": "did the film about the family road trip get any nominations?", "entity_names": ["family road trip"], "entity_types": ["Plot"]}
{"sentence": "hey, has the movie where the guy saves the world from aliens received any accolades?", "entity_names": ["saves the world from aliens"], "entity_types": ["Plot"]}
{"sentence": "hey, can you show me a trailer for the upcoming james bond movie?", "entity_names": ["trailer", "james bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "i heard there's a new superhero movie coming out soon, can you give me a sneak peek?", "entity_names": ["superhero", "sneak peek"], "entity_types": ["Genre", "Trailer"]}
{"sentence": "is there a teaser for the new animated disney movie with the song 'let it go'?", "entity_names": ["teaser", "let it go"], "entity_types": ["Trailer", "Song"]}
{"sentence": "yo, what's the name of that movie with that dope song 'eye of the tiger'?", "entity_names": ["eye of the tiger"], "entity_types": ["Song"]}
{"sentence": "hey, can you tell me a movie with that lit song 'old town road' in it?", "entity_names": ["old town road"], "entity_types": ["Song"]}
{"sentence": "yo, which movie has that fire track 'can't stop the feeling' by justin timberlake?", "entity_names": ["can't stop the feeling"], "entity_types": ["Song"]}
{"sentence": "what is the making-of documentary for the movie the lord of the rings: the fellowship of the ring", "entity_names": ["the lord of the rings: the fellowship of the ring"], "entity_types": ["Title"]}
{"sentence": "can you tell me about the special features of the film inception? i'm bored.", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "show me interviews with the director and cast members of the dark knight. i need something to do.", "entity_names": ["director", "the dark knight"], "entity_types": ["Director", "Title"]}
{"sentence": "who directed the making-of documentary for the movie inception?", "entity_names": ["directed", "making-of", "inception"], "entity_types": ["Director", "Genre", "Title"]}
{"sentence": "what is the song featured in the end credits of the latest james bond movie?", "entity_names": ["end credits", "latest", "james bond"], "entity_types": ["Plot", "Year", "Character"]}
{"sentence": "show me a trailer for the new quentin tarantino film, i can't wait any longer!", "entity_names": ["trailer", "new", "quentin tarantino"], "entity_types": ["Trailer", "Year", "Director"]}
{"sentence": "can you tell me the showtimes for the latest james bond movie?", "entity_names": ["james bond"], "entity_types": ["Title"]}
{"sentence": "is there a movie theater nearby playing the new action film?", "entity_names": ["action film"], "entity_types": ["Genre"]}
{"sentence": "can you check out the trailer for that new action flick starring keanu reeves?", "entity_names": ["trailer", "action", "keanu reeves"], "entity_types": ["Trailer", "Genre", "Actor"]}
{"sentence": "hey, could you recommend a cool movie preview to watch later?", "entity_names": ["movie preview"], "entity_types": ["Trailer"]}
{"sentence": "do you have any teasers for the upcoming marvel superhero film?", "entity_names": ["teasers", "marvel"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "can you recommend a detective movie where the main character is a retired cop hunting down a serial killer", "entity_names": ["detective movie", "retired cop", "serial killer"], "entity_types": ["Genre", "Character", "Plot"]}
{"sentence": "who directed the movie shawshank redemption?", "entity_names": ["shawshank redemption"], "entity_types": ["Title"]}
{"sentence": "what are the top-rated movies by steven spielberg?", "entity_names": ["steven spielberg"], "entity_types": ["Director"]}
{"sentence": "can you recommend a classic film featuring audrey hepburn?", "entity_names": ["classic", "audrey hepburn"], "entity_types": ["Viewers' Rating", "Actor"]}
{"sentence": "can you show me a preview of the latest action movie?", "entity_names": ["preview", "latest action movie"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "is there a movie with an iconic song that i can watch a teaser for?", "entity_names": ["iconic song", "teaser"], "entity_types": ["Song", "Trailer"]}
{"sentence": "which director has released a new film with a thrilling plot that i can get a sneak peek of?", "entity_names": ["new film", "sneak peek"], "entity_types": ["Year", "Trailer"]}
{"sentence": "i'm in the mood for an action movie with a strong female lead. can you recommend a recent film with that kind of plot?", "entity_names": ["action", "strong female lead"], "entity_types": ["Genre", "Character"]}
{"sentence": "i'm in the mood for a classic romantic comedy. can you suggest a movie that will make me laugh and feel good?", "entity_names": ["romantic comedy"], "entity_types": ["Genre"]}
{"sentence": "i'm looking for a thought-provoking science fiction movie with a unique storyline. do you have any recommendations?", "entity_names": ["thought-provoking", "science fiction"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "i'm feeling really down. can you recommend a drama movie that will make me cry?", "entity_names": ["drama"], "entity_types": ["Genre"]}
{"sentence": "i'm in the mood for a tearjerker. can you suggest a romance movie with a tragic plot?", "entity_names": ["romance", "tragic"], "entity_types": ["Genre", "Plot"]}
{"sentence": "i need a movie to watch that will tug at my heartstrings. do you have any recommendations for a coming-of-age film?", "entity_names": ["coming-of-age"], "entity_types": ["Genre"]}
{"sentence": "can you tell me about the director and the filming process of the movie the sound of music?", "entity_names": ["director", "the sound of music"], "entity_types": ["Director", "Title"]}
{"sentence": "do you have any documentaries about the making of classic hollywood movies from the 1950s?", "entity_names": ["documentaries", "classic hollywood movies from the 1950s"], "entity_types": ["Genre", "Genre"]}
{"sentence": "can you recommend a movie that showcases the behind-the-scenes work of a famous director like alfred hitchcock?", "entity_names": ["behind-the-scenes work", "alfred hitchcock"], "entity_types": ["Plot", "Director"]}
{"sentence": "i need to find a movie with the best original song from the 80s, can you help?", "entity_names": ["best original song", "80s"], "entity_types": ["Song", "Year"]}
{"sentence": "i'm in a rush to find a movie with a killer soundtrack, any recommendations?", "entity_names": [], "entity_types": []}
{"sentence": "i'm dying to watch a movie with the most iconic theme music of all time, what do you suggest?", "entity_names": ["most iconic theme music"], "entity_types": ["Song"]}
{"sentence": "can you tell me the director of the movie inception?", "entity_names": ["director", "inception"], "entity_types": ["Director", "Title"]}
{"sentence": "what is the viewers' rating for the film the shawshank redemption?", "entity_names": ["viewers' rating", "the shawshank redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "which actor played the lead role in the film the godfather?", "entity_names": ["lead role", "the godfather"], "entity_types": ["Character", "Title"]}
{"sentence": "what is the viewers' rating for the movie the shawshank redemption directed by frank darabont?", "entity_names": ["the shawshank redemption", "frank darabont"], "entity_types": ["Title", "Director"]}
{"sentence": "i need to watch a movie tonight, but i don't know what's playing. can you tell me the showtimes for 'inception'?", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "i'm running late and i don't want to miss the start of the movie. please show me the nearest theater with tickets available for 'the lion king'?", "entity_names": ["the lion king"], "entity_types": ["Title"]}
{"sentence": "i have been looking forward to this movie all week, but i can't find any tickets anywhere. can you help me find tickets for 'avengers: endgame'?", "entity_names": ["avengers: endgame"], "entity_types": ["Title"]}
{"sentence": "what's the best romantic comedy film from the 2000s that features a love triangle", "entity_names": ["romantic comedy", "2000s", "love triangle"], "entity_types": ["Genre", "Year", "Plot"]}
{"sentence": "can you recommend a fantasy movie for kids with dragons and magic spells", "entity_names": ["fantasy", "kids", "dragons and magic spells"], "entity_types": ["Genre", "Viewers' Rating", "Plot"]}
{"sentence": "who directed the animated film about a brave princess who saves her kingdom from an evil sorcerer", "entity_names": ["animated", "princess", "evil sorcerer"], "entity_types": ["Genre", "Character", "Character"]}
{"sentence": "can i watch any good action movies on streaming platforms?", "entity_names": ["action movies"], "entity_types": ["Genre"]}
{"sentence": "is there a classic romantic film available for streaming?", "entity_names": ["classic romantic film"], "entity_types": ["Genre"]}
{"sentence": "are there any popular movies from the 90s that i can watch online?", "entity_names": ["popular movies", "90s"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "can you tell me about the making-of process for the latest mission: impossible movie?", "entity_names": ["making-of process", "mission: impossible"], "entity_types": ["Plot", "Title"]}
{"sentence": "show me a movie with the behind-the-scenes footage of a famous action sequence.", "entity_names": ["behind-the-scenes footage", "action sequence"], "entity_types": ["Plot", "Plot"]}
{"sentence": "i want to learn more about the special effects in a recent fantasy film.", "entity_names": ["special effects", "fantasy"], "entity_types": ["Plot", "Genre"]}
{"sentence": "can i get tickets for the new james bond movie?", "entity_names": ["james bond"], "entity_types": ["Title"]}
{"sentence": "are there any theaters playing the new marvel movie tonight?", "entity_names": ["new marvel"], "entity_types": ["Genre"]}
{"sentence": "can i watch the movie inception on any streaming platform?", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "is the director's cut of blade runner available for streaming anywhere?", "entity_names": ["director's cut", "blade runner"], "entity_types": ["Plot", "Title"]}
{"sentence": "do you really think the new tarantino movie lives up to the hype? some people say it's overrated.", "entity_names": ["new", "tarantino", "overrated"], "entity_types": ["Year", "Director", "Review"]}
{"sentence": "i heard that the latest action film starring tom hardy got mixed reviews. what's the story about anyway?", "entity_names": ["action film", "tom hardy", "mixed reviews", "story"], "entity_types": ["Genre", "Actor", "Review", "Plot"]}
{"sentence": "what's the deal with that new movie everyone's talking about?", "entity_names": ["new movie", "everyone's talking about"], "entity_types": ["Title", "Review"]}
{"sentence": "who's the actor in that action movie with all the explosions?", "entity_names": ["actor", "action movie", "explosions"], "entity_types": ["Actor", "Genre", "Plot"]}
{"sentence": "is there a funny movie from the 90s that's suitable for kids?", "entity_names": ["funny movie", "90s", "suitable for kids"], "entity_types": ["Review", "Year", "MPAA Rating"]}
{"sentence": "can you recommend a good sci-fi comedy movie?", "entity_names": ["sci-fi comedy"], "entity_types": ["Genre"]}
{"sentence": "i'm in the mood for a classic romance film. do you have any recommendations?", "entity_names": ["classic romance"], "entity_types": ["Genre"]}
{"sentence": "i'm looking for a thriller with elements of mystery. any suggestions?", "entity_names": ["thriller", "mystery"], "entity_types": ["Genre", "Genre"]}
{"sentence": "i'm curious about a movie with a memorable song, maybe something romantic, like 'my heart will go on'. any recommendations?", "entity_names": ["my heart will go on"], "entity_types": ["Song"]}
{"sentence": "i heard a fantastic movie with an amazing soundtrack and it's set in the 80s. do you know which one i'm talking about?", "entity_names": ["80s"], "entity_types": ["Year"]}
{"sentence": "i'm trying to find a film with an iconic song that's been featured in many other movies. any ideas?", "entity_names": ["iconic song"], "entity_types": ["Song"]}
{"sentence": "can you tell me the highest-rated animated movies of all time?", "entity_names": ["highest-rated", "animated"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "what's the plot of the new disney princess movie that came out this year?", "entity_names": ["new disney princess movie"], "entity_types": ["Genre"]}
{"sentence": "i'm looking for a romantic comedy with strong female characters. can you recommend one?", "entity_names": ["romantic comedy", "strong female characters"], "entity_types": ["Genre", "Character"]}
{"sentence": "can you just give me a quick summary of the movie 'inception'?", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "i need to know the plot of 'the godfather' right now.", "entity_names": ["the godfather"], "entity_types": ["Title"]}
{"sentence": "i don't have all day, just tell me about the story of 'the shawshank redemption'.", "entity_names": ["the shawshank redemption"], "entity_types": ["Title"]}
{"sentence": "can you tell me about the director of the movie 'the dark knight' and any behind-the-scenes stories?", "entity_names": ["director", "the dark knight", "behind-the-scenes stories"], "entity_types": ["Director", "Title", "Plot"]}
{"sentence": "i'm interested in knowing the song used in the movie 'grease' during the behind-the-scenes footage. can you show me a clip?", "entity_names": ["song", "grease", "behind-the-scenes footage", "clip"], "entity_types": ["Song", "Title", "Plot", "Trailer"]}
{"sentence": "what behind-the-scenes information can you provide about the making of the movie 'jurassic park' directed by steven spielberg?", "entity_names": ["making of", "jurassic park", "steven spielberg"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "what is the name of the movie with the iconic song 'my heart will go on'?", "entity_names": ["my heart will go on"], "entity_types": ["Song"]}
{"sentence": "can you recommend a film with an amazing soundtrack that i won't be able to stop listening to?", "entity_names": ["amazing soundtrack"], "entity_types": ["Review"]}
{"sentence": "i'm looking for a movie that has an unforgettable musical number. can you help me find one?", "entity_names": ["unforgettable musical number"], "entity_types": ["Review"]}
{"sentence": "can you recommend a movie featuring leonardo dicaprio and directed by christopher nolan?", "entity_names": ["leonardo dicaprio", "christopher nolan"], "entity_types": ["Actor", "Director"]}
{"sentence": "what are some popular action movies from the 90s?", "entity_names": ["action", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "i'm looking for a comedy film starring emma stone and ryan reynolds, can you help me find one?", "entity_names": ["comedy", "emma stone", "ryan reynolds"], "entity_types": ["Genre", "Actor", "Actor"]}
{"sentence": "can you tell me who directed the film inception?", "entity_names": ["directed", "inception"], "entity_types": ["Director", "Title"]}
{"sentence": "what is the viewers' rating for the shawshank redemption?", "entity_names": ["viewers' rating", "the shawshank redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "show me a movie with meryl streep as the lead character.", "entity_names": ["meryl streep"], "entity_types": ["Actor"]}
{"sentence": "can you tell me about the reviews for the movie inception?", "entity_names": ["reviews", "inception"], "entity_types": ["Review", "Title"]}
{"sentence": "i'm not sure if the movie the godfather is as good as they say. can you provide its viewers' rating?", "entity_names": ["the godfather", "viewers' rating"], "entity_types": ["Title", "Viewers' Rating"]}
{"sentence": "i don't trust the director of the movie avatar. what can you tell me about james cameron's work on it?", "entity_names": ["avatar", "james cameron"], "entity_types": ["Title", "Director"]}
{"sentence": "what songs were featured in the musical film the sound of music?", "entity_names": ["songs", "musical", "the sound of music"], "entity_types": ["Song", "Genre", "Title"]}
{"sentence": "can you tell me about the actress who played dorothy in the wizard of oz and any interesting behind-the-scenes facts?", "entity_names": ["actress", "dorothy", "the wizard of oz", "behind-the-scenes"], "entity_types": ["Actor", "Character", "Title", "Plot"]}
{"sentence": "hey, what's the name of that movie with the awesome soundtrack that has a bunch of 80s hits?", "entity_names": ["80s hits"], "entity_types": ["Genre"]}
{"sentence": "can you recommend a good movie with a killer score, like something with a really epic theme song?", "entity_names": ["killer score", "epic theme song"], "entity_types": ["Review", "Song"]}
{"sentence": "i heard there's a new movie coming out with a killer soundtrack, do you know the name of it?", "entity_names": ["new movie", "killer soundtrack"], "entity_types": ["Title", "Song"]}
{"sentence": "what movie from the 1990s is a must-see action film?", "entity_names": ["1990s", "must-see", "action"], "entity_types": ["Year", "Viewers' Rating", "Genre"]}
{"sentence": "who directed the science fiction movie with a pg-13 rating that came out last year?", "entity_names": ["science fiction", "pg-13", "last year"], "entity_types": ["Genre", "MPAA Rating", "Year"]}
{"sentence": "what are some highly-rated action movies from the 1990s?", "entity_names": ["highly-rated", "action", "1990s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "i'm looking for a romantic comedy with a catchy soundtrack. can you recommend one?", "entity_names": ["romantic comedy", "catchy soundtrack"], "entity_types": ["Genre", "Song"]}
{"sentence": "what is the highest-rated musical film of all time?", "entity_names": ["highest-rated", "musical"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "can you give me a summary of the plot for the movie sleepless in seattle", "entity_names": ["sleepless in seattle"], "entity_types": ["Title"]}
{"sentence": "what is the storyline for the movie steel magnolias", "entity_names": ["steel magnolias"], "entity_types": ["Title"]}
{"sentence": "please tell me about the plot of the bridges of madison county", "entity_names": ["the bridges of madison county"], "entity_types": ["Title"]}
{"sentence": "what movie has a catchy song that kids would love?", "entity_names": ["catchy song", "kids"], "entity_types": ["Song", "Viewers' Rating"]}
{"sentence": "could you recommend a family-friendly movie with a strong female character as the lead?", "entity_names": ["family-friendly", "lead"], "entity_types": ["Viewers' Rating", "Character"]}
{"sentence": "is the movie 'la la land' available for streaming anywhere?", "entity_names": ["la la land"], "entity_types": ["Title"]}
{"sentence": "can i watch 'the devil wears prada' on any streaming platform?", "entity_names": ["the devil wears prada"], "entity_types": ["Title"]}
{"sentence": "is 'inception' available to stream?", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "can you recommend any action movies with a strong female lead from the 2000s?", "entity_names": ["action", "strong female lead", "2000s"], "entity_types": ["Genre", "Character", "Year"]}
{"sentence": "what's a good coming-of-age film that deals with lgbtq+ themes?", "entity_names": ["coming-of-age", "lgbtq+ themes"], "entity_types": ["Genre", "Genre"]}
{"sentence": "i'm looking for a romantic comedy with a diverse cast, preferably released in the last five years.", "entity_names": ["romantic comedy", "diverse cast", "last five years"], "entity_types": ["Genre", "Character", "Year"]}
{"sentence": "can you tell me about the director of the movie casablanca and some interesting behind-the-scenes stories?", "entity_names": ["director", "casablanca"], "entity_types": ["Director", "Title"]}
{"sentence": "what are some highly rated classic musical films from the 1950s that i should watch?", "entity_names": ["highly rated", "musical", "1950s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "i'm interested in learning about the making of the movie the godfather and the impact it had on the crime genre.", "entity_names": ["making of", "the godfather", "crime"], "entity_types": ["Plot", "Title", "Genre"]}
{"sentence": "can i find the movie 'casablanca' on any streaming platforms?", "entity_names": ["casablanca"], "entity_types": ["Title"]}
{"sentence": "what are some classic western movies available for streaming?", "entity_names": ["classic western movies"], "entity_types": ["Genre"]}
{"sentence": "is the director's cut of 'blade runner' available on any streaming services?", "entity_names": ["director's cut", "blade runner"], "entity_types": ["Plot", "Title"]}
{"sentence": "i need to watch a comedy movie right now, is 'the hangover' available on any streaming platform?", "entity_names": ["comedy", "the hangover"], "entity_types": ["Genre", "Title"]}
{"sentence": "i can't find 'inception' anywhere, is it on any streaming platform?", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "i'm in the mood for a classic, is 'casablanca' streaming anywhere?", "entity_names": ["classic", "casablanca"], "entity_types": ["Genre", "Title"]}
{"sentence": "can you tell me the plot of the movie sleepless in seattle from 1993?", "entity_names": ["sleepless in seattle", "1993"], "entity_types": ["Title", "Year"]}
{"sentence": "i'm looking for a movie with a romantic plot that is set in the 1950s", "entity_names": ["romantic", "1950s"], "entity_types": ["Genre", "Year"]}
{"sentence": "what film has a plot about a woman who travels back in time to the 19th century?", "entity_names": ["19th century"], "entity_types": ["Year"]}
{"sentence": "can you provide me with the director and main actors of the movie the social network?", "entity_names": ["director", "main actors", "the social network"], "entity_types": ["Director", "Actor", "Title"]}
{"sentence": "i am interested in learning about the soundtrack and filming locations of the movie la la land", "entity_names": ["soundtrack", "filming locations", "la la land"], "entity_types": ["Song", "Plot", "Title"]}
{"sentence": "what's the buzz on the new thriller film that just came out?", "entity_names": ["thriller film", "just came out"], "entity_types": ["Genre", "Year"]}
{"sentence": "can you recommend a movie with a really high viewers' rating?", "entity_names": ["really high viewers' rating"], "entity_types": ["Viewers' Rating"]}
{"sentence": "i'm looking for a classic film directed by a renowned filmmaker.", "entity_names": ["classic film", "renowned filmmaker"], "entity_types": ["Genre", "Director"]}
{"sentence": "what movie did meryl streep star in with the song 'dancing queen'?", "entity_names": ["meryl streep", "dancing queen"], "entity_types": ["Actor", "Song"]}
{"sentence": "show me a film with a high viewers' rating that was directed by christopher nolan", "entity_names": ["high viewers' rating", "christopher nolan"], "entity_types": ["Viewers' Rating", "Director"]}
{"sentence": "who directed the sci-fi film interstellar, and what is its viewers' rating?", "entity_names": ["sci-fi", "interstellar", "viewers' rating"], "entity_types": ["Genre", "Title", "Viewers' Rating"]}
{"sentence": "can you provide me with the plot of the movie inception and the name of the actor who played the lead role?", "entity_names": ["plot", "inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "show me the trailer for the film titanic and tell me the name of the main character.", "entity_names": ["trailer", "titanic", "main character"], "entity_types": ["Trailer", "Title", "Character"]}
{"sentence": "can you tell me which movie won the most awards in the 1990s?", "entity_names": ["most awards", "1990s"], "entity_types": ["Review", "Year"]}
{"sentence": "i'm looking for a critically acclaimed film with the highest number of nominations for best picture.", "entity_names": ["critically acclaimed", "highest number of nominations for best picture"], "entity_types": ["Review", "Review"]}
{"sentence": "is there a movie with zac efron and vanessa hudgens from the musical genre?", "entity_names": ["zac efron", "vanessa hudgens", "musical"], "entity_types": ["Actor", "Actor", "Genre"]}
{"sentence": "can you tell me the names of all the actors in the movie 'frozen' and show me its trailer?", "entity_names": ["actors", "frozen", "trailer"], "entity_types": ["Actor", "Title", "Trailer"]}
{"sentence": "yo, what kind of movie is the matrix? like, is it sci-fi or what?", "entity_names": ["the matrix", "sci-fi"], "entity_types": ["Title", "Genre"]}
{"sentence": "i'm lookin' for a movie that's like, action but also crime, you know what i mean?", "entity_names": ["action", "crime"], "entity_types": ["Genre", "Genre"]}
{"sentence": "what's that movie with will ferrell that's both comedy and sports at the same time?", "entity_names": ["will ferrell", "comedy", "sports"], "entity_types": ["Actor", "Genre", "Genre"]}
{"sentence": "can i watch the latest marvel movie at the theater tonight?", "entity_names": ["marvel"], "entity_types": ["Genre"]}
{"sentence": "is there a showing of the new christopher nolan film at the cinema near me?", "entity_names": ["christopher nolan"], "entity_types": ["Director"]}
{"sentence": "what are the showtimes for the action movie with tom cruise in it?", "entity_names": ["action", "tom cruise"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what was the last film directed by quentin tarantino and featuring leonardo dicaprio?", "entity_names": ["quentin tarantino", "leonardo dicaprio"], "entity_types": ["Director", "Actor"]}
{"sentence": "can you recommend a sci-fi movie with sigourney weaver in the cast?", "entity_names": ["sci-fi", "sigourney weaver"], "entity_types": ["Genre", "Actor"]}
{"sentence": "i'm looking for a film with tom hanks in a leading role, directed by steven spielberg and released in the 1990s", "entity_names": ["tom hanks", "steven spielberg", "1990s"], "entity_types": ["Actor", "Director", "Year"]}
{"sentence": "what are the most popular action movie trailers of 2021?", "entity_names": ["popular", "action", "trailers", "2021"], "entity_types": ["Viewers' Rating", "Genre", "Trailer", "Year"]}
{"sentence": "who directed the film with the most intriguing teaser?", "entity_names": ["intriguing", "teaser"], "entity_types": ["Review", "Trailer"]}
{"sentence": "can you recommend a highly-rated movie from the 1990s?", "entity_names": ["highly-rated", "1990s"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "who directed the top action movie of all time?", "entity_names": ["top action movie"], "entity_types": ["Review"]}
{"sentence": "show me trailers for science fiction films with female lead characters.", "entity_names": ["science fiction", "female lead characters"], "entity_types": ["Genre", "Character"]}
{"sentence": "can you suggest a family-friendly film with a lot of nominations for major awards?", "entity_names": ["family-friendly", "nominations for major awards"], "entity_types": ["Viewers' Rating", "Review"]}
{"sentence": "i need to watch a movie with an intense plot and a twist ending, do you have any recommendations?", "entity_names": ["intense plot", "twist ending"], "entity_types": ["Plot", "Plot"]}
{"sentence": "show me a trailer for a classic film that was both critically acclaimed and commercially successful.", "entity_names": ["trailer", "classic film", "critically acclaimed"], "entity_types": ["Trailer", "Title", "Review"]}
{"sentence": "could you please provide me with the trailer for the latest james bond film?", "entity_names": ["trailer", "james bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "i would like to see a teaser for the upcoming marvel superhero movie, if possible.", "entity_names": ["teaser", "marvel"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "do you have the trailer for the new steven spielberg film?", "entity_names": ["trailer", "steven spielberg"], "entity_types": ["Trailer", "Director"]}
{"sentence": "hey, can you tell me if the movie 'the shawshank redemption' won any awards?", "entity_names": ["the shawshank redemption"], "entity_types": ["Title"]}
{"sentence": "i'm curious, did 'la la land' get any nominations for its music?", "entity_names": ["la la land", "nominations", "music"], "entity_types": ["Title", "Title", "Song"]}
{"sentence": "do you know if 'forrest gump' received any honors for its special effects?", "entity_names": ["forrest gump", "special effects"], "entity_types": ["Title", "Genre"]}
{"sentence": "can you recommend a highly-rated movie with a gripping plot and stunning visuals?", "entity_names": ["highly-rated", "gripping plot", "stunning visuals"], "entity_types": ["Viewers' Rating", "Plot", "Review"]}
{"sentence": "what is the best movie of the year so far, according to the critics?", "entity_names": ["best movie", "year so far", "critics"], "entity_types": ["Review", "Year", "Viewers' Rating"]}
{"sentence": "i'm looking for an action-packed movie with a top-notch performance by the lead actor. any recommendations?", "entity_names": ["action-packed", "top-notch performance", "lead actor"], "entity_types": ["Genre", "Review", "Actor"]}
{"sentence": "hey, can you tell me who directed that awesome sci-fi movie with the killer soundtrack?", "entity_names": ["sci-fi", "killer soundtrack"], "entity_types": ["Genre", "Song"]}
{"sentence": "i heard there's a new action movie coming out soon, who's starring in it and what's the plot like?", "entity_names": ["action"], "entity_types": ["Genre"]}
{"sentence": "i need some recommendations for feel-good movies from the 90s, any ideas?", "entity_names": ["feel-good", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "can you recommend a movie with an iconic soundtrack featuring the song 'eye of the tiger'?", "entity_names": ["eye of the tiger"], "entity_types": ["Song"]}
{"sentence": "what movie from the 80s has a memorable soundtrack with the song 'take my breath away'?", "entity_names": ["80s", "take my breath away"], "entity_types": ["Year", "Song"]}
{"sentence": "i'm in the mood for a movie with a captivating soundtrack like 'i will always love you.' any suggestions?", "entity_names": ["i will always love you"], "entity_types": ["Song"]}
{"sentence": "can you recommend a feel-good movie with a strong female lead and a great soundtrack?", "entity_names": ["feel-good", "strong female lead"], "entity_types": ["Viewers' Rating", "Character"]}
{"sentence": "what are some highly-rated movies from the 80s that are suitable for family viewing?", "entity_names": ["highly-rated", "80s", "family viewing"], "entity_types": ["Viewers' Rating", "Year", "MPAA Rating"]}
{"sentence": "who directed the top-rated romantic comedy of the past decade?", "entity_names": ["top-rated", "romantic comedy", "past decade"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "what movie from the 90s has the best soundtrack of all time?", "entity_names": ["90s", "best soundtrack of all time"], "entity_types": ["Year", "Review"]}
{"sentence": "can you recommend a movie with an iconic song from the 80s?", "entity_names": ["iconic song", "80s"], "entity_types": ["Song", "Year"]}
{"sentence": "do you know any recent movies with a score by a female composer?", "entity_names": ["recent movies"], "entity_types": ["Genre"]}
{"sentence": "i'm in a rush, so tell me who directed the highest-rated action film of last year", "entity_names": ["highest-rated", "action", "last year"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "i need to know right now, which actor starred in the romantic comedy with the catchy theme song from the 90s", "entity_names": ["actor", "romantic comedy", "catchy theme song", "90s"], "entity_types": ["Actor", "Genre", "Song", "Year"]}
{"sentence": "can you show me the trailer for a heartwarming movie to lift my spirits?", "entity_names": ["trailer", "heartwarming"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "i'm feeling down, can you recommend a movie with a touching plot that will cheer me up?", "entity_names": ["touching plot"], "entity_types": ["Plot"]}
{"sentence": "what film has a beautiful and emotional song in it?", "entity_names": ["beautiful and emotional song"], "entity_types": ["Song"]}
{"sentence": "hey, can i get a peek at the trailer for the new james bond movie?", "entity_names": ["trailer", "james bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "do you know if there's a trailer out for the upcoming superhero movie?", "entity_names": ["trailer", "upcoming superhero movie"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "what's the latest teaser for the animated film about the magical creatures?", "entity_names": ["teaser", "animated film", "magical creatures"], "entity_types": ["Trailer", "Genre", "Plot"]}
{"sentence": "can you recommend a family-friendly movie with a high viewers' rating?", "entity_names": ["family-friendly", "high viewers' rating"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "what is the plot of the latest action movie directed by christopher nolan?", "entity_names": ["latest action movie", "christopher nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "i'm looking for a classic film with audrey hepburn as the lead actress, can you suggest one?", "entity_names": ["classic film", "audrey hepburn"], "entity_types": ["Genre", "Actor"]}
{"sentence": "are there any showtimes available for the latest marvel movie?", "entity_names": ["marvel"], "entity_types": ["Genre"]}
{"sentence": "can i buy tickets for the new james bond film?", "entity_names": ["james bond"], "entity_types": ["Character"]}
{"sentence": "what time is the next screening for the movie directed by christopher nolan?", "entity_names": ["christopher nolan"], "entity_types": ["Director"]}
{"sentence": "what are people saying about the new marvel movie directed by jon watts?", "entity_names": ["marvel movie", "jon watts"], "entity_types": ["Title", "Director"]}
{"sentence": "show me a movie from the 90s with a high viewers' rating.", "entity_names": ["90s", "high viewers' rating"], "entity_types": ["Year", "Viewers' Rating"]}
{"sentence": "can you recommend a family-friendly animated movie with a catchy soundtrack?", "entity_names": ["family-friendly", "animated", "catchy soundtrack"], "entity_types": ["Genre", "Genre", "Song"]}
{"sentence": "i'm curious about the director behind the scenes of the classic film casablanca, what can you tell me about the person who made it?", "entity_names": ["director", "casablanca"], "entity_types": ["Director", "Title"]}
{"sentence": "i'm interested in the making-of details of the movie psycho, could you provide some insights into the person responsible for its creation?", "entity_names": ["making-of details", "psycho"], "entity_types": ["Plot", "Title"]}
{"sentence": "i'd like to know more about the creative mind behind the making of the godfather, what can you tell me about the person who directed it?", "entity_names": ["creative mind", "the godfather"], "entity_types": ["Director", "Title"]}
{"sentence": "please provide me with the plot summary of the movie inception.", "entity_names": ["plot summary", "inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "can you give me a synopsis of the film the shawshank redemption?", "entity_names": ["synopsis", "the shawshank redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "i would like to hear the plot summary of the science fiction movie blade runner 2049.", "entity_names": ["plot summary", "science fiction", "blade runner 2049"], "entity_types": ["Plot", "Genre", "Title"]}
{"sentence": "can you tell me who directed the making of the movie 'inception'?", "entity_names": ["directed", "inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "what new sci-fi movie has an interesting behind-the-scenes featurette?", "entity_names": ["sci-fi"], "entity_types": ["Genre"]}
{"sentence": "i heard there's a documentary about the making of a classic horror film, can you provide more information?", "entity_names": ["documentary", "classic horror film"], "entity_types": ["Genre", "Plot"]}
{"sentence": "can you really find me a movie with an iconic soundtrack that is worth listening to?", "entity_names": ["iconic soundtrack"], "entity_types": ["Song"]}
{"sentence": "i'm not sure if there are any good movies out there with a memorable song. can you prove me wrong?", "entity_names": ["memorable song"], "entity_types": ["Song"]}
{"sentence": "is there any movie with a soundtrack that truly stands out, or am i just setting myself up for disappointment?", "entity_names": ["soundtrack that truly stands out"], "entity_types": ["Song"]}
{"sentence": "can you play a sneak peek of the new spider-man movie?", "entity_names": ["sneak peek", "spider-man"], "entity_types": ["Trailer", "Title"]}
{"sentence": "i'm dying to see a preview of the next james bond film. can you show me a bit?", "entity_names": ["preview", "james bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "i need to get a glimpse of the upcoming star wars movie. do you have the trailer?", "entity_names": ["glimpse", "star wars"], "entity_types": ["Trailer", "Title"]}
{"sentence": "can you provide me with a brief plot synopsis of the movie the shawshank redemption?", "entity_names": ["plot synopsis", "the shawshank redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "what is the storyline of the film inception directed by christopher nolan?", "entity_names": ["storyline", "inception", "christopher nolan"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "tell me about the plot of the sci-fi movie blade runner 2049 released in 2017.", "entity_names": ["plot", "sci-fi", "blade runner 2049", "2017"], "entity_types": ["Plot", "Genre", "Title", "Year"]}
{"sentence": "can you recommend a movie with a thrilling plot and a touch of mystery?", "entity_names": ["thrilling", "mystery"], "entity_types": ["Genre", "Genre"]}
{"sentence": "i'm looking for a film that has a mix of action and romance, any suggestions?", "entity_names": ["action", "romance"], "entity_types": ["Genre", "Genre"]}
{"sentence": "do you know of any movies that fall into the fantasy genre, maybe with some elements of adventure?", "entity_names": ["fantasy", "adventure"], "entity_types": ["Genre", "Genre"]}
{"sentence": "can you tell me about the awards and nominations for the animated movie frozen?", "entity_names": ["frozen"], "entity_types": ["Title"]}
{"sentence": "is the movie finding nemo appropriate for kids, and has it won any awards?", "entity_names": ["finding nemo", "kids"], "entity_types": ["Title", "Viewers' Rating"]}
{"sentence": "i'm looking for a family-friendly movie that has won multiple awards, can you recommend one?", "entity_names": ["family-friendly"], "entity_types": ["Viewers' Rating"]}
{"sentence": "can you show me the trailer for the latest marvel movie?", "entity_names": ["trailer", "marvel"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "what's an action movie with a great song in it?", "entity_names": ["action", "great song"], "entity_types": ["Genre", "Song"]}
{"sentence": "who directed the highest-rated horror movie of the year?", "entity_names": ["highest-rated", "horror", "the year"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "who directed the making-of documentary for the lord of the rings trilogy?", "entity_names": ["lord of the rings"], "entity_types": ["Title"]}
{"sentence": "are there any good behind-the-scenes featurettes for the harry potter movies?", "entity_names": ["behind-the-scenes featurettes", "harry potter"], "entity_types": ["Plot", "Title"]}
{"sentence": "can you recommend a film about the history and production of disney animated classics?", "entity_names": ["disney animated classics"], "entity_types": ["Genre"]}
{"sentence": "can you tell me if the movie mean girls won any awards?", "entity_names": ["mean girls", "awards"], "entity_types": ["Title", "Viewers' Rating"]}
{"sentence": "i'm curious if the film to all the boys i've loved before received any nominations?", "entity_names": ["to all the boys i've loved before", "nominations"], "entity_types": ["Title", "Review"]}
{"sentence": "which movie from the 1990s has the most insightful director commentary?", "entity_names": ["1990s", "insightful", "director commentary"], "entity_types": ["Year", "Review", "Plot"]}
{"sentence": "i'm interested in a film with a compelling featurette about its special effects. can you recommend one?", "entity_names": ["compelling", "featurette", "special effects"], "entity_types": ["Review", "Plot", "Plot"]}
{"sentence": "is there a movie that has a really cool adventure plot?", "entity_names": ["adventure plot"], "entity_types": ["Plot"]}
{"sentence": "what's the best horror movie of the 2010s?", "entity_names": ["horror movie", "2010s"], "entity_types": ["Genre", "Year"]}
{"sentence": "who directed the classic romantic comedy with the song 'you've got a friend in me'?", "entity_names": ["romantic comedy", "you've got a friend in me"], "entity_types": ["Genre", "Song"]}
{"sentence": "can you recommend a recent action film with a strong female lead?", "entity_names": ["action film", "strong female lead"], "entity_types": ["Genre", "Character"]}
{"sentence": "what movie features the song 'my heart will go on'?", "entity_names": ["my heart will go on"], "entity_types": ["Song"]}
{"sentence": "can you recommend a film with an amazing soundtrack?", "entity_names": ["amazing soundtrack"], "entity_types": ["Review"]}
{"sentence": "what is the title of the movie with the best music of all time?", "entity_names": ["best music of all time"], "entity_types": ["Review"]}
{"sentence": "what's the deal with that new tarantino movie, is it any good?", "entity_names": ["tarantino movie", "any good"], "entity_types": ["Title", "Review"]}
{"sentence": "hey, have you heard about that horror movie with the killer clown? what's the viewers' rating on that one?", "entity_names": ["horror movie", "killer clown", "viewers' rating"], "entity_types": ["Genre", "Character", "Viewers' Rating"]}
{"sentence": "can you tell me a cool action movie from the 90s, something like die hard or terminator?", "entity_names": ["action movie", "90s", "die hard", "terminator"], "entity_types": ["Genre", "Year", "Title", "Title"]}
{"sentence": "can you recommend a highly-rated action movie from the 2000s?", "entity_names": ["highly-rated", "action movie", "2000s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "who is the director of the top-rated science fiction film of all time?", "entity_names": ["top-rated", "science fiction film", "all time"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "i'm looking for a critically acclaimed drama with meryl streep, any suggestions?", "entity_names": ["critically acclaimed", "drama", "meryl streep"], "entity_types": ["Review", "Genre", "Actor"]}
{"sentence": "can you recommend a movie with a great romantic song that came out in the 1980s?", "entity_names": ["romantic song", "1980s"], "entity_types": ["Song", "Year"]}
{"sentence": "i'm looking for a feel-good movie with a catchy soundtrack. any suggestions?", "entity_names": ["feel-good", "catchy soundtrack"], "entity_types": ["Genre", "Review"]}
{"sentence": "who composed the music for the classic film casablanca?", "entity_names": ["composed the music", "casablanca"], "entity_types": ["Director", "Title"]}
{"sentence": "what's the viewers' rating for the new quentin tarantino movie?", "entity_names": ["quentin tarantino"], "entity_types": ["Director"]}
{"sentence": "show me a movie with a high imdb rating and a plot twist", "entity_names": ["high imdb rating", "plot twist"], "entity_types": ["Viewers' Rating", "Plot"]}
{"sentence": "can you recommend a recent action film with a strong female lead and a catchy soundtrack?", "entity_names": ["action film", "strong female lead", "catchy soundtrack"], "entity_types": ["Genre", "Character", "Song"]}
{"sentence": "can i see the trailer for the new james bond movie?", "entity_names": ["trailer", "james bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "which movie has the most exciting teaser of all time?", "entity_names": ["most exciting"], "entity_types": ["Review"]}
{"sentence": "are there any must-see films with intense action movie trailers?", "entity_names": ["must-see", "action", "movie trailers"], "entity_types": ["Viewers' Rating", "Genre", "Trailer"]}
{"sentence": "can you recommend a horror movie with an atmospheric setting and a gripping plot?", "entity_names": ["horror", "atmospheric setting"], "entity_types": ["Genre", "Plot"]}
{"sentence": "show me a romantic comedy with a heartwarming storyline and great chemistry between the lead actors", "entity_names": ["romantic comedy", "heartwarming storyline"], "entity_types": ["Genre", "Plot"]}
{"sentence": "what is the most iconic science fiction film with a dystopian future setting?", "entity_names": ["science fiction", "dystopian future setting"], "entity_types": ["Genre", "Plot"]}
{"sentence": "can you recommend a good action movie that came out in the 1990s", "entity_names": ["action movie", "1990s"], "entity_types": ["Genre", "Year"]}
{"sentence": "i'm in the mood for a suspense thriller with a strong female lead, can you suggest one for me", "entity_names": ["suspense thriller", "strong female lead"], "entity_types": ["Genre", "Character"]}
{"sentence": "what are some classic romantic comedies from the 1980s", "entity_names": ["romantic comedies", "1980s"], "entity_types": ["Genre", "Year"]}
{"sentence": "can you show me a trailer for the latest romantic comedy directed by nancy meyers?", "entity_names": ["romantic comedy", "nancy meyers"], "entity_types": ["Genre", "Director"]}
{"sentence": "what are the top-rated dramas from the 1980s that i should definitely watch?", "entity_names": ["top-rated", "dramas", "1980s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "i'd like to see a teaser for the new action movie starring tom hardy.", "entity_names": ["teaser", "action", "tom hardy"], "entity_types": ["Trailer", "Genre", "Actor"]}
{"sentence": "i'm bored, can you show me a teaser for the latest james bond movie?", "entity_names": ["teaser", "james bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "i need something to watch, how about a movie with an epic battle scene?", "entity_names": ["epic battle scene"], "entity_types": ["Plot"]}
{"sentence": "i'm feeling uninspired, could you recommend a film directed by christopher nolan?", "entity_names": ["christopher nolan"], "entity_types": ["Director"]}
{"sentence": "can i find any showtimes for a movie with a superhero character?", "entity_names": ["superhero character"], "entity_types": ["Character"]}
{"sentence": "is there a movie playing with a song by michael jackson?", "entity_names": ["michael jackson"], "entity_types": ["Actor"]}
{"sentence": "are there any highly-rated movies from the 1980s?", "entity_names": ["highly-rated", "1980s"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "who directed the action movie starring matt damon and julia stiles with a pg-13 rating", "entity_names": ["action movie", "matt damon", "julia stiles", "pg-13"], "entity_types": ["Genre", "Actor", "Actor", "MPAA Rating"]}
{"sentence": "what film features meryl streep and is known for its captivating storyline and a high viewers' rating", "entity_names": ["meryl streep", "captivating storyline", "high viewers' rating"], "entity_types": ["Actor", "Plot", "Viewers' Rating"]}
{"sentence": "can you provide me with the name of the director of the science fiction movie released in 2010", "entity_names": ["science fiction movie", "2010"], "entity_types": ["Genre", "Year"]}
{"sentence": "can i watch casablanca on any streaming platforms?", "entity_names": ["casablanca"], "entity_types": ["Title"]}
{"sentence": "are there any classic musicals available for streaming?", "entity_names": ["classic musicals"], "entity_types": ["Genre"]}
{"sentence": "which streaming service has the movie starring audrey hepburn and directed by billy wilder?", "entity_names": ["audrey hepburn", "billy wilder"], "entity_types": ["Actor", "Director"]}
{"sentence": "who directed the movie inception and who were the main actors?", "entity_names": ["directed", "inception", "main actors"], "entity_types": ["Director", "Title", "Actor"]}
{"sentence": "i need to know the year the film pulp fiction was released and the genre of the movie.", "entity_names": ["year", "pulp fiction", "genre"], "entity_types": ["Year", "Title", "Genre"]}
{"sentence": "give me the name of the actor who starred in the godfather and the viewers' rating for the film now.", "entity_names": ["actor", "the godfather", "viewers' rating"], "entity_types": ["Actor", "Title", "Viewers' Rating"]}
{"sentence": "who directed the making-of documentary for the movie titanic", "entity_names": ["directed", "making-of documentary", "titanic"], "entity_types": ["Director", "Plot", "Title"]}
{"sentence": "can you recommend a behind-the-scenes film about the marvel cinematic universe", "entity_names": ["behind-the-scenes film", "marvel cinematic universe"], "entity_types": ["Plot", "Title"]}
{"sentence": "show me a documentary about the special effects in the star wars movies", "entity_names": ["documentary", "special effects", "star wars"], "entity_types": ["Plot", "Plot", "Title"]}
{"sentence": "what kind of movie is to all the boys i've loved before?", "entity_names": ["to all the boys i've loved before"], "entity_types": ["Title"]}
{"sentence": "can you recommend a romantic comedy for me to watch with my friends?", "entity_names": ["romantic comedy"], "entity_types": ["Genre"]}
{"sentence": "i heard about a movie called crazy rich asians, what's it about?", "entity_names": ["crazy rich asians"], "entity_types": ["Title"]}
{"sentence": "can you provide information on the making-of documentary for the film the lord of the rings: the fellowship of the ring?", "entity_names": ["making-of documentary", "the lord of the rings: the fellowship of the ring"], "entity_types": ["Plot", "Title"]}
{"sentence": "i'm interested in learning about the director's commentary for the movie inception. where can i find it?", "entity_names": ["director's commentary", "inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "are there any special features or bonus content available for the blu-ray edition of the film jurassic park?", "entity_names": ["bonus content", "jurassic park"], "entity_types": ["Plot", "Title"]}
{"sentence": "i'm looking for a drama film with hugh jackman. do you have any recommendations?", "entity_names": ["drama", "hugh jackman"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what are the top-rated action films from the 90s?", "entity_names": ["top-rated", "action films", "90s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "can you recommend a movie with a memorable soundtrack?", "entity_names": [], "entity_types": []}
{"sentence": "what's a movie with a great score from the 80s?", "entity_names": ["great score", "80s"], "entity_types": ["Song", "Year"]}
{"sentence": "show me a film with an iconic theme song.", "entity_names": ["iconic theme song"], "entity_types": ["Song"]}
{"sentence": "i'm excited about watching a movie tonight. can you tell me if there are any showings for the film 'inception' at the nearby theaters?", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "i'm really looking forward to seeing a new action movie. what are the showtimes for 'mad max: fury road' in 3d?", "entity_names": ["mad max: fury road"], "entity_types": ["Title"]}
{"sentence": "i'm in the mood for a comedy this weekend. are there any theaters playing 'bridesmaids'?", "entity_names": ["comedy", "bridesmaids"], "entity_types": ["Genre", "Title"]}
{"sentence": "can you recommend a movie with high viewers' rating and a thrilling plot for young adults?", "entity_names": ["high viewers' rating", "thrilling"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "who directed the highest rated movie for young adults in the past 5 years?", "entity_names": ["highest rated", "young adults", "past 5 years"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "i'm looking for a movie with a strong female lead and positive reviews from the past decade.", "entity_names": ["strong female lead", "positive reviews", "past decade"], "entity_types": ["Character", "Review", "Year"]}
{"sentence": "can you tell me which movies tom hanks starred in during the 1990s?", "entity_names": ["tom hanks", "1990s"], "entity_types": ["Actor", "Year"]}
{"sentence": "what is the viewer rating for the movie the shawshank redemption?", "entity_names": ["viewer rating", "the shawshank redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "what is the name of the actor who plays iron man in the marvel movies?", "entity_names": ["actor", "iron man", "marvel movies"], "entity_types": ["Actor", "Character", "Genre"]}
{"sentence": "can you tell me the genre of the movie the matrix?", "entity_names": ["genre", "the matrix"], "entity_types": ["Genre", "Title"]}
{"sentence": "can you tell me about the highest-rated film in terms of viewers' ratings", "entity_names": ["highest-rated"], "entity_types": ["Viewers' Rating"]}
{"sentence": "what is the plot of the movie inception", "entity_names": ["plot", "inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "can you give me a synopsis of the film the shawshank redemption", "entity_names": ["synopsis", "the shawshank redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "tell me about the story of the movie the godfather", "entity_names": ["story", "the godfather"], "entity_types": ["Plot", "Title"]}
{"sentence": "can you tell me the plot of the classic film the sound of music?", "entity_names": ["plot", "the sound of music"], "entity_types": ["Plot", "Title"]}
{"sentence": "i'm feeling nostalgic for a 90s movie with a compelling storyline, could you recommend one?", "entity_names": ["90s", "compelling storyline"], "entity_types": ["Year", "Plot"]}
{"sentence": "what's the synopsis of the steven spielberg-directed adventure movie jurassic park?", "entity_names": ["synopsis", "steven spielberg", "jurassic park"], "entity_types": ["Plot", "Director", "Title"]}
{"sentence": "can you show me some teaser for a new action movie?", "entity_names": ["teaser", "action movie"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "what's the latest movie trailer available for viewing?", "entity_names": ["latest", "movie trailer"], "entity_types": ["Year", "Trailer"]}
{"sentence": "do you have any sneak peeks of upcoming mystery movies?", "entity_names": ["sneak peeks", "mystery movies"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "can you provide information about the director of the movie the social network?", "entity_names": ["director", "the social network"], "entity_types": ["Director", "Title"]}
{"sentence": "i am looking for a film with a poignant soundtrack. can you recommend one?", "entity_names": ["poignant soundtrack"], "entity_types": ["Song"]}
{"sentence": "which movie from the 90s has the most authentic portrayal of a historical event?", "entity_names": ["90s", "historical event"], "entity_types": ["Year", "Plot"]}
{"sentence": "can you tell me which movie starred both leonardo dicaprio and kate winslet?", "entity_names": ["leonardo dicaprio", "kate winslet"], "entity_types": ["Actor", "Actor"]}
{"sentence": "i'm looking for a classic film directed by alfred hitchcock, with a suspenseful plot and a high viewers' rating.", "entity_names": ["classic", "alfred hitchcock", "suspenseful", "high viewers' rating"], "entity_types": ["Genre", "Director", "Plot", "Viewers' Rating"]}
{"sentence": "show me a movie featuring meryl streep, directed by steven spielberg, and released in the 1980s.", "entity_names": ["meryl streep", "steven spielberg", "1980s"], "entity_types": ["Actor", "Director", "Year"]}
{"sentence": "can you provide the showtimes for the new james bond film and let me know if tickets are available?", "entity_names": ["james bond"], "entity_types": ["Title"]}
{"sentence": "i'm interested in seeing a movie from the thriller genre this weekend. can you tell me the showtimes for any recent releases?", "entity_names": ["thriller"], "entity_types": ["Genre"]}
{"sentence": "i need to book tickets for a movie directed by christopher nolan. what are my options?", "entity_names": ["christopher nolan"], "entity_types": ["Director"]}
{"sentence": "i'm looking for a film with a famous actor and a plot full of suspense.", "entity_names": ["famous actor", "plot full of suspense"], "entity_types": ["Actor", "Plot"]}
{"sentence": "can you recommend a movie from the 90s with a lot of action?", "entity_names": ["90s", "a lot of action"], "entity_types": ["Year", "Genre"]}
{"sentence": "can you recommend a movie with a beautiful love song in it?", "entity_names": ["beautiful love song"], "entity_types": ["Song"]}
{"sentence": "i'm looking for a film from the 1950s with a classic jazz soundtrack, do you have any suggestions?", "entity_names": ["1950s", "classic jazz"], "entity_types": ["Year", "Genre"]}
{"sentence": "what movie features the song 'unchained melody' and was directed by a female filmmaker?", "entity_names": ["unchained melody", "female filmmaker"], "entity_types": ["Song", "Director"]}
{"sentence": "on which streaming platform can i watch the movie the shawshank redemption directed by frank darabont?", "entity_names": ["the shawshank redemption", "frank darabont"], "entity_types": ["Title", "Director"]}
{"sentence": "is the movie inception with leonardo dicaprio available for streaming anywhere?", "entity_names": ["inception", "leonardo dicaprio"], "entity_types": ["Title", "Actor"]}
{"sentence": "what is the viewers' rating for the film the dark knight released in 2008 on any streaming platform?", "entity_names": ["viewers' rating", "the dark knight", "2008"], "entity_types": ["Viewers' Rating", "Title", "Year"]}
{"sentence": "what is the viewers' rating for the movie inception?", "entity_names": ["viewers' rating", "inception"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "can you tell me the plot of the movie the shawshank redemption?", "entity_names": ["plot", "the shawshank redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "what are the showtimes for the newest superhero movie in theaters right now?", "entity_names": ["newest superhero movie"], "entity_types": ["Genre"]}
{"sentence": "can i buy tickets for the romantic comedy movie that's getting great reviews?", "entity_names": ["romantic comedy movie", "great reviews"], "entity_types": ["Genre", "Review"]}
{"sentence": "are there any action movies directed by christopher nolan playing this weekend?", "entity_names": ["action movies", "christopher nolan", "this weekend"], "entity_types": ["Genre", "Director", "Year"]}
{"sentence": "hey, when is the next showing for the latest fast & furious movie?", "entity_names": ["fast & furious"], "entity_types": ["Title"]}
{"sentence": "do you know if there are any good action movies playing this weekend?", "entity_names": ["action movies"], "entity_types": ["Genre"]}
{"sentence": "can you find me tickets for the new spider-man movie?", "entity_names": ["spider-man"], "entity_types": ["Title"]}
{"sentence": "can you tell me about the behind-the-scenes details of the movie jaws, directed by steven spielberg?", "entity_names": ["behind-the-scenes details", "jaws", "steven spielberg"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "i'm curious about the making-of process of the godfather. can you provide some insights?", "entity_names": ["making-of process", "the godfather"], "entity_types": ["Plot", "Title"]}
{"sentence": "can you recommend a highly-rated movie directed by christopher nolan?", "entity_names": ["highly-rated", "christopher nolan"], "entity_types": ["Viewers' Rating", "Director"]}
{"sentence": "what is the plot of the movie inception?", "entity_names": ["the plot", "inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "show me a trailer for any action movie released in the past year.", "entity_names": ["trailer", "action", "past year"], "entity_types": ["Trailer", "Genre", "Year"]}
{"sentence": "what's a highly-rated movie with an intense plot that will keep me on the edge of my seat?", "entity_names": ["highly-rated", "intense plot"], "entity_types": ["Viewers' Rating", "Plot"]}
{"sentence": "can you recommend a movie with a mind-blowing twist ending that has received great reviews?", "entity_names": ["mind-blowing twist ending", "great reviews"], "entity_types": ["Plot", "Review"]}
{"sentence": "i need a feel-good movie with a heartwarming plot and positive viewers' ratings to lift my spirits.", "entity_names": ["feel-good", "heartwarming plot", "positive viewers' ratings"], "entity_types": ["Genre", "Plot", "Viewers' Rating"]}
{"sentence": "hey, can you tell me the plot of the new james bond movie? i'm so excited to hear all about it!", "entity_names": ["plot", "james bond"], "entity_types": ["Plot", "Character"]}
{"sentence": "what's the storyline of the latest romantic comedy that everyone's raving about? i can't wait to hear the plot!", "entity_names": ["storyline", "romantic comedy"], "entity_types": ["Plot", "Genre"]}
{"sentence": "i'm dying to know the synopsis of the upcoming action-packed superhero film. could you give me some details about the plot, please?", "entity_names": ["synopsis", "action-packed", "superhero film"], "entity_types": ["Plot", "Genre", "Genre"]}
{"sentence": "can you give me a brief synopsis of the movie casablanca?", "entity_names": ["casablanca"], "entity_types": ["Title"]}
{"sentence": "who directed the movie gone with the wind and what's it about?", "entity_names": ["directed", "gone with the wind"], "entity_types": ["Director", "Title"]}
{"sentence": "i'm interested in a movie from the 1950s with a romantic plot - do you have any suggestions?", "entity_names": ["1950s", "romantic"], "entity_types": ["Year", "Genre"]}
{"sentence": "can i stream the movie 'the shawshank redemption' anywhere?", "entity_names": ["the shawshank redemption"], "entity_types": ["Title"]}
{"sentence": "is there a way to watch 'inception' online?", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "where can i find 'the godfather' to stream?", "entity_names": ["the godfather"], "entity_types": ["Title"]}
{"sentence": "i can't wait to learn more about the director's cut of blade runner, when will it be released?", "entity_names": ["director's cut", "blade runner", "released"], "entity_types": ["Plot", "Title", "Year"]}
{"sentence": "i'm really eager to watch a film that dives into the special effects of jurassic park, do you have any recommendations?", "entity_names": ["special effects", "jurassic park"], "entity_types": ["Plot", "Title"]}
{"sentence": "yo, who starred in that new superhero flick?", "entity_names": ["superhero flick"], "entity_types": ["Genre"]}
{"sentence": "who directed that crazy action movie with all the explosions?", "entity_names": ["crazy action movie", "explosions"], "entity_types": ["Genre", "Plot"]}
{"sentence": "what's the deal with that rom-com that everyone's talking about? who's in it?", "entity_names": ["rom-com"], "entity_types": ["Genre"]}
{"sentence": "show me a film with a diverse cast that received critical acclaim", "entity_names": ["critical acclaim"], "entity_types": ["Review"]}
{"sentence": "what time is the next showing of the movie dirty dancing?", "entity_names": ["dirty dancing"], "entity_types": ["Title"]}
{"sentence": "can i still get tickets to see the film steel magnolias this weekend?", "entity_names": ["steel magnolias"], "entity_types": ["Title"]}
{"sentence": "is the movie fried green tomatoes playing at any theaters nearby?", "entity_names": ["fried green tomatoes"], "entity_types": ["Title"]}
{"sentence": "show me a list of films starring meryl streep.", "entity_names": ["meryl streep"], "entity_types": ["Actor"]}
{"sentence": "what's the viewers' rating for the movie the shawshank redemption?", "entity_names": ["viewers' rating", "the shawshank redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "can you tell me which movies featured leonardo dicaprio and kate winslet as co-stars?", "entity_names": ["leonardo dicaprio", "kate winslet"], "entity_types": ["Actor", "Actor"]}
{"sentence": "can you tell me about any acclaimed movies in terms of recognition?", "entity_names": ["acclaimed movies", "recognition"], "entity_types": ["Genre", "Review"]}
{"sentence": "what are some highly-decorated movies recognized in the film industry?", "entity_names": ["highly-decorated movies", "recognized"], "entity_types": ["Title", "Review"]}
{"sentence": "i'm looking for a movie from the 90s that was nominated for multiple oscars, can you help me find one?", "entity_names": ["90s", "nominated for multiple oscars"], "entity_types": ["Year", "Review"]}
{"sentence": "what movie features the song 'can't help falling in love' by elvis presley?", "entity_names": ["can't help falling in love"], "entity_types": ["Song"]}
{"sentence": "i'm in the mood for a movie with a great soundtrack. can you recommend a film with iconic songs from the 80s?", "entity_names": ["great soundtrack", "iconic songs", "80s"], "entity_types": ["Review", "Song", "Year"]}
{"sentence": "i need a feel-good movie with an uplifting soundtrack. can you suggest a film with classic hits from the 70s?", "entity_names": ["feel-good", "uplifting soundtrack", "classic hits", "70s"], "entity_types": ["Review", "Plot", "Song", "Year"]}
{"sentence": "what are the showtimes for the latest action movies", "entity_names": ["latest action movies"], "entity_types": ["Genre"]}
{"sentence": "i need to buy tickets for the top-rated comedy film that just came out", "entity_names": ["comedy"], "entity_types": ["Genre"]}
{"sentence": "can i still get tickets for the thriller movie that everyone is talking about", "entity_names": ["thriller movie"], "entity_types": ["Genre"]}
{"sentence": "could you provide information on the making-of the movie inception directed by christopher nolan?", "entity_names": ["inception", "christopher nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "i would like to know more about the behind-the-scenes of the film la la land starring ryan gosling and emma stone.", "entity_names": ["la la land", "ryan gosling", "emma stone"], "entity_types": ["Title", "Actor", "Actor"]}
{"sentence": "please show me a documentary about the making of the classic film citizen kane by orson welles.", "entity_names": ["citizen kane", "orson welles"], "entity_types": ["Title", "Director"]}
{"sentence": "can you tell me the plot for the movie black panther directed by ryan coogler?", "entity_names": ["black panther", "ryan coogler"], "entity_types": ["Title", "Director"]}
{"sentence": "what songs are featured in the film a star is born?", "entity_names": ["songs", "a star is born"], "entity_types": ["Song", "Title"]}
{"sentence": "i'm looking for a new animated film to watch, can you give me the plot of the movie luca?", "entity_names": ["animated", "luca"], "entity_types": ["Genre", "Title"]}
{"sentence": "can you tell me a movie starring meryl streep and directed by steven spielberg?", "entity_names": ["meryl streep", "steven spielberg"], "entity_types": ["Actor", "Director"]}
{"sentence": "what movie features both leonardo dicaprio and kate winslet as the main characters?", "entity_names": ["leonardo dicaprio", "kate winslet"], "entity_types": ["Actor", "Actor"]}
{"sentence": "can you show me the trailer for the movie inception?", "entity_names": ["trailer", "inception"], "entity_types": ["Trailer", "Title"]}
{"sentence": "what are some highly-rated movies with strong female leads?", "entity_names": ["highly-rated", "strong female leads"], "entity_types": ["Viewers' Rating", "Character"]}
{"sentence": "who directed the classic film casablanca?", "entity_names": ["classic", "casablanca"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "hey, who directed that awesome action movie with tom hardy and charlize theron?", "entity_names": ["action", "tom hardy", "charlize theron"], "entity_types": ["Genre", "Actor", "Actor"]}
{"sentence": "what's the name of that one movie where leonardo dicaprio gets mauled by a bear?", "entity_names": ["leonardo dicaprio"], "entity_types": ["Actor"]}
{"sentence": "can you recommend any good sci-fi movies with sigourney weaver?", "entity_names": ["sci-fi", "sigourney weaver"], "entity_types": ["Genre", "Actor"]}
{"sentence": "can you show me the trailer for the film inception?", "entity_names": ["trailer", "inception"], "entity_types": ["Trailer", "Title"]}
{"sentence": "what type of movie genre is the trailer for the new spider-man movie?", "entity_names": ["trailer", "spider-man"], "entity_types": ["Trailer", "Title"]}
{"sentence": "is there a teaser for the upcoming james bond movie?", "entity_names": ["teaser", "james bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "what is the rating for the movie the shawshank redemption", "entity_names": ["the shawshank redemption"], "entity_types": ["Title"]}
{"sentence": "show me a trailer for the latest movie directed by christopher nolan", "entity_names": ["christopher nolan"], "entity_types": ["Director"]}
{"sentence": "are there any streaming platforms that have the movie 'the shawshank redemption'?", "entity_names": ["the shawshank redemption"], "entity_types": ["Title"]}
{"sentence": "is there a way to watch 'forrest gump' on any streaming services?", "entity_names": ["forrest gump"], "entity_types": ["Title"]}
{"sentence": "can i find 'the matrix' on any streaming platform?", "entity_names": ["the matrix"], "entity_types": ["Title"]}
{"sentence": "can you tell me what awards and nominations the movie titanic received?", "entity_names": ["titanic"], "entity_types": ["Title"]}
{"sentence": "which film directed by steven spielberg won the most awards in the 1990s?", "entity_names": ["steven spielberg", "1990s"], "entity_types": ["Director", "Year"]}
{"sentence": "can you play a movie with the song 'my heart will go on'?", "entity_names": ["my heart will go on"], "entity_types": ["Song"]}
{"sentence": "i'm in the mood for a movie with a great soundtrack. can you recommend one?", "entity_names": ["great soundtrack"], "entity_types": ["Review"]}
{"sentence": "is there a film with a memorable original song that i can watch?", "entity_names": ["memorable original song"], "entity_types": ["Song"]}
{"sentence": "is the movie the shawshank redemption really as good as people say it is", "entity_names": ["the shawshank redemption", "good as people say"], "entity_types": ["Title", "Review"]}
{"sentence": "i'm not sure if i believe the hype, but can you tell me about the storyline for the movie the godfather", "entity_names": ["storyline", "the godfather"], "entity_types": ["Plot", "Title"]}
{"sentence": "can you recommend a high-rated action movie from the 1990s?", "entity_names": ["high-rated", "action movie", "1990s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "what is the viewer rating for the latest james cameron film?", "entity_names": ["viewer rating", "latest", "james cameron"], "entity_types": ["Viewers' Rating", "Year", "Director"]}
{"sentence": "tell me about the sci-fi movie interstellar directed by christopher nolan.", "entity_names": ["sci-fi movie", "interstellar", "christopher nolan"], "entity_types": ["Genre", "Title", "Director"]}
{"sentence": "can you provide me with the director of the new disney movie about mermaids?", "entity_names": ["disney", "mermaids"], "entity_types": ["Genre", "Plot"]}
{"sentence": "i want to watch a movie from the 1980s that shows how they made special effects for space adventure films", "entity_names": ["1980s", "special effects", "space adventure"], "entity_types": ["Year", "Plot", "Genre"]}
{"sentence": "i'm looking for a film where i can see how they created the magical costumes and props for a fantasy world", "entity_names": ["fantasy"], "entity_types": ["Genre"]}
{"sentence": "can you recommend a movie with a compelling plot and a strong female lead?", "entity_names": ["compelling plot", "strong female lead"], "entity_types": ["Plot", "Character"]}
{"sentence": "what movie from the 1990s has the highest viewers' rating?", "entity_names": ["1990s", "highest viewers' rating"], "entity_types": ["Year", "Viewers' Rating"]}
{"sentence": "who directed the romantic comedy film that came out in 2005 and received a pg-13 rating?", "entity_names": ["romantic comedy", "2005", "pg-13"], "entity_types": ["Genre", "Year", "MPAA Rating"]}
{"sentence": "show me a movie with tom hanks as the lead actor.", "entity_names": ["tom hanks"], "entity_types": ["Actor"]}
{"sentence": "who stars in that new action movie with the superheroes?", "entity_names": ["action movie", "superheroes"], "entity_types": ["Genre", "Character"]}
{"sentence": "what's the name of the director for the new horror flick with the haunted house?", "entity_names": ["horror flick", "haunted house"], "entity_types": ["Genre", "Plot"]}
{"sentence": "can you tell me the year when that comedy with the talking animals came out?", "entity_names": ["comedy", "talking animals"], "entity_types": ["Genre", "Plot"]}
{"sentence": "i'm looking for showtimes and tickets for the latest marvel movie", "entity_names": ["marvel"], "entity_types": ["Title"]}
{"sentence": "can i see a list of theaters showing the new christopher nolan film?", "entity_names": ["christopher nolan"], "entity_types": ["Director"]}
{"sentence": "i want to buy tickets for a well-rated horror movie that came out last year", "entity_names": ["well-rated horror movie", "last year"], "entity_types": ["Genre", "Year"]}
{"sentence": "who directed the sci-fi thriller film titled interstellar and what technologies were used in its visual effects?", "entity_names": ["directed", "sci-fi thriller", "interstellar", "visual effects"], "entity_types": ["Director", "Genre", "Title", "Plot"]}
{"sentence": "can you recommend a documentary about the production of the film inception and its impact on the science fiction genre?", "entity_names": ["documentary", "production", "inception", "science fiction"], "entity_types": ["Genre", "Plot", "Title", "Genre"]}
{"sentence": "i'm interested in learning about the cinematography techniques used in the classic movie the godfather, could you provide some insights?", "entity_names": ["cinematography techniques", "classic", "the godfather"], "entity_types": ["Plot", "Genre", "Title"]}
{"sentence": "could you provide me with the soundtrack of the movie inception directed by christopher nolan?", "entity_names": ["soundtrack", "inception", "christopher nolan"], "entity_types": ["Song", "Title", "Director"]}
{"sentence": "what is the viewers' rating for the movie la la land, known for its iconic song city of stars?", "entity_names": ["viewers' rating", "la la land", "city of stars"], "entity_types": ["Viewers' Rating", "Title", "Song"]}
{"sentence": "i would like to know the year of release for the movie a star is born featuring the song shallow by lady gaga and bradley cooper.", "entity_names": ["year of release", "a star is born", "shallow", "lady gaga", "bradley cooper"], "entity_types": ["Year", "Title", "Song", "Actor", "Actor"]}
{"sentence": "who directed the action film with a high viewers' rating and sharon stone as the lead actress?", "entity_names": ["action film", "high viewers' rating", "sharon stone"], "entity_types": ["Genre", "Viewers' Rating", "Actor"]}
{"sentence": "can you recommend a comedy film from the 90s with jim carrey and cameron diaz in the cast?", "entity_names": ["comedy film", "90s", "jim carrey", "cameron diaz"], "entity_types": ["Genre", "Year", "Actor", "Actor"]}
{"sentence": "can you recommend a classic western movie with lots of action?", "entity_names": ["classic western", "lots of action"], "entity_types": ["Genre", "Plot"]}
{"sentence": "what is the best romantic comedy film from the 1950s?", "entity_names": ["romantic comedy", "1950s"], "entity_types": ["Genre", "Year"]}
{"sentence": "are there any family-friendly animated movies with talking animals?", "entity_names": ["family-friendly", "animated", "talking animals"], "entity_types": ["Viewers' Rating", "Genre", "Plot"]}
{"sentence": "can you provide a review for the film the shawshank redemption?", "entity_names": ["review", "the shawshank redemption"], "entity_types": ["Review", "Title"]}
{"sentence": "show me the trailer for the latest quentin tarantino movie.", "entity_names": ["trailer", "latest quentin tarantino movie"], "entity_types": ["Trailer", "Title"]}
{"sentence": "can i find tickets for the new james bond movie?", "entity_names": ["james bond"], "entity_types": ["Title"]}
{"sentence": "what are the showtimes for the classic musical movie singin' in the rain?", "entity_names": ["classic musical", "singin' in the rain"], "entity_types": ["Genre", "Title"]}
{"sentence": "is there a movie theater near me showing the award-winning film casablanca?", "entity_names": ["award-winning", "casablanca"], "entity_types": ["Review", "Title"]}
{"sentence": "what are some good action movies from the 1980s?", "entity_names": ["action movies", "1980s"], "entity_types": ["Genre", "Year"]}
{"sentence": "can you recommend a classic western film from the 1960s?", "entity_names": ["classic western film", "1960s"], "entity_types": ["Genre", "Year"]}
{"sentence": "i'm in the mood for a suspense thriller. any recommendations?", "entity_names": ["suspense thriller"], "entity_types": ["Genre"]}
{"sentence": "can you recommend a good movie with a high viewers' rating?", "entity_names": ["good"], "entity_types": ["Viewers' Rating"]}
{"sentence": "who directed the highest-rated movie of 2021?", "entity_names": ["highest-rated", "2021"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "what is the plot of the latest action movie with a female lead?", "entity_names": ["latest", "action", "female lead"], "entity_types": ["Year", "Genre", "Character"]}
{"sentence": "can you tell me the director of the movie the shawshank redemption?", "entity_names": ["director", "the shawshank redemption"], "entity_types": ["Director", "Title"]}
{"sentence": "what year did clint eastwood star in a western film?", "entity_names": ["clint eastwood", "western"], "entity_types": ["Actor", "Genre"]}
{"sentence": "i'm looking for a comedy movie with steve martin, can you recommend one?", "entity_names": ["comedy", "steve martin"], "entity_types": ["Genre", "Actor"]}
{"sentence": "can you recommend a good action film from the 1980s featuring arnold schwarzenegger?", "entity_names": ["action", "1980s", "arnold schwarzenegger"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "i'm a big fan of quentin tarantino's work. can you tell me the plot of his latest movie and if there's a trailer available?", "entity_names": ["quentin tarantino", "latest movie", "trailer"], "entity_types": ["Director", "Title", "Trailer"]}
{"sentence": "can you tell me about the director of that one movie with lots of action scenes?", "entity_names": ["action scenes"], "entity_types": ["Plot"]}
{"sentence": "i'm looking for a film that has a famous actor and a great soundtrack. do you have any suggestions?", "entity_names": ["famous actor", "great soundtrack"], "entity_types": ["Actor", "Song"]}
{"sentence": "what's the deal with that classic movie everyone talks about? who directed it?", "entity_names": ["classic movie"], "entity_types": ["Title"]}
{"sentence": "could you provide me with a brief synopsis of the movie inception directed by christopher nolan?", "entity_names": ["inception", "christopher nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "can you tell me the general plot of the film the shawshank redemption?", "entity_names": ["plot", "the shawshank redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "i am interested in learning more about the film the matrix. could you please give me a synopsis?", "entity_names": ["the matrix", "synopsis"], "entity_types": ["Title", "Plot"]}
{"sentence": "i'm interested in a movie with behind-the-scenes info about war strategies. what do you recommend?", "entity_names": ["war strategies"], "entity_types": ["Plot"]}
{"sentence": "show me a film directed by kathryn bigelow that reveals the backstory of its main character.", "entity_names": ["kathryn bigelow", "backstory", "main character"], "entity_types": ["Director", "Plot", "Character"]}
{"sentence": "can you provide a brief plot summary of the movie inception directed by christopher nolan?", "entity_names": ["plot summary", "inception", "christopher nolan"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "what is the synopsis of the film titanic which was released in 1997?", "entity_names": ["synopsis", "titanic", "1997"], "entity_types": ["Plot", "Title", "Year"]}
{"sentence": "could you give me an overview of the storyline for the movie the shawshank redemption?", "entity_names": ["storyline", "the shawshank redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "i'm looking for a comedy movie from the 1980s with a high viewers' rating. any suggestions?", "entity_names": ["comedy", "1980s", "high viewers' rating"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "hey, what's a really good movie to watch this weekend?", "entity_names": [], "entity_types": []}
{"sentence": "can you find me a movie with a great soundtrack playing nearby?", "entity_names": [], "entity_types": []}
{"sentence": "i'd like to know more about the actors in the film the shawshank redemption", "entity_names": ["actors", "the shawshank redemption"], "entity_types": ["Actor", "Title"]}
{"sentence": "show me a preview of the new marvel movie directed by taika waititi", "entity_names": ["preview", "marvel movie", "taika waititi"], "entity_types": ["Trailer", "Genre", "Director"]}
{"sentence": "can you tell me who directed the movie top gun?", "entity_names": ["directed", "top gun"], "entity_types": ["Director", "Title"]}
{"sentence": "show me a film starring humphrey bogart that is set in the 1940s", "entity_names": ["humphrey bogart", "1940s"], "entity_types": ["Actor", "Year"]}
{"sentence": "what is the viewers' rating for the movie shawshank redemption?", "entity_names": ["viewers' rating", "shawshank redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "what is the genre of the film the godfather?", "entity_names": ["the godfather"], "entity_types": ["Title"]}
{"sentence": "can you tell me which movies tom hanks starred in?", "entity_names": ["tom hanks"], "entity_types": ["Actor"]}
{"sentence": "can i see a trailer for the movie inception", "entity_names": ["trailer", "inception"], "entity_types": ["Trailer", "Title"]}
{"sentence": "what is the viewers' rating for the movie the shawshank redemption", "entity_names": ["viewers' rating", "the shawshank redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "who directed the 2017 movie dunkirk", "entity_names": ["directed", "2017", "dunkirk"], "entity_types": ["Director", "Year", "Title"]}
{"sentence": "who directed the making-of documentary for the movie inception", "entity_names": ["directed", "inception"], "entity_types": ["Director", "Title"]}
{"sentence": "what are some popular behind-the-scenes features for fantasy movies from the 2010s", "entity_names": ["fantasy movies", "2010s"], "entity_types": ["Genre", "Year"]}
{"sentence": "can you recommend a movie with a director's commentary by a non-binary filmmaker", "entity_names": ["director's commentary"], "entity_types": ["Plot"]}
{"sentence": "what are some good horror movies with a supernatural plot?", "entity_names": ["horror", "supernatural"], "entity_types": ["Genre", "Plot"]}
{"sentence": "can you recommend any action-packed spy movies from the 2000s?", "entity_names": ["action-packed", "spy", "2000s"], "entity_types": ["Genre", "Plot", "Year"]}
{"sentence": "who directed the classic comedy film about a dysfunctional family?", "entity_names": ["classic comedy", "dysfunctional family"], "entity_types": ["Genre", "Plot"]}
{"sentence": "can you recommend a classic suspense movie from the 1950s directed by alfred hitchcock?", "entity_names": ["classic suspense", "1950s", "alfred hitchcock"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "i'm in the mood for a light-hearted romantic comedy with a happy ending. do you have any suggestions?", "entity_names": ["light-hearted romantic comedy", "happy ending"], "entity_types": ["Genre", "Plot"]}
{"sentence": "i'm interested in exploring some independent drama films that have received critical acclaim. any recommendations?", "entity_names": ["independent drama films", "critical acclaim"], "entity_types": ["Genre", "Review"]}
{"sentence": "what are the different subgenres of action movies?", "entity_names": ["action movies"], "entity_types": ["Genre"]}
{"sentence": "can you recommend a classic romance movie from the 1950s?", "entity_names": ["romance movie", "1950s"], "entity_types": ["Genre", "Year"]}
{"sentence": "who directed the popular science fiction film from the 1970s?", "entity_names": ["science fiction film", "1970s"], "entity_types": ["Genre", "Year"]}
{"sentence": "i heard there's a popular movie with a great soundtrack, is it available to stream anywhere?", "entity_names": ["popular movie", "great soundtrack"], "entity_types": ["Title", "Song"]}
{"sentence": "i'm interested in a classic film from the 1950s, are there any well-rated options to watch online?", "entity_names": ["classic film", "1950s", "well-rated options", "watch online"], "entity_types": ["Genre", "Year", "Viewers' Rating", "Review"]}
{"sentence": "i'm looking for a movie with a compelling plot and strong performances, is it possible to find such a film on a streaming platform?", "entity_names": ["compelling plot", "strong performances", "streaming platform"], "entity_types": ["Plot", "Review", "Genre"]}
{"sentence": "which action movie from the 2000s has the best special effects and stunts, and who was the stunt coordinator for that film?", "entity_names": ["action movie", "2000s", "special effects", "stunts"], "entity_types": ["Genre", "Year", "Plot", "Plot"]}
{"sentence": "what is the most intense horror movie to watch with friends and what was the inspiration behind the set design and overall atmosphere?", "entity_names": ["horror movie", "friends", "inspiration", "set design", "atmosphere"], "entity_types": ["Genre", "Viewers' Rating", "Plot", "Plot", "Plot"]}
{"sentence": "i'm bored, can you show me a trailer for the latest action movie?", "entity_names": ["trailer", "action movie"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "i have nothing to do, can you recommend a movie with a catchy theme song?", "entity_names": ["catchy theme song"], "entity_types": ["Song"]}
{"sentence": "i need something to entertain me, show me a movie with a mind-bending plot twist.", "entity_names": ["mind-bending plot twist"], "entity_types": ["Plot"]}
{"sentence": "hey, who's the director of the new action movie starring tom hardy?", "entity_names": ["action movie", "tom hardy"], "entity_types": ["Genre", "Actor"]}
{"sentence": "show me a movie with keanu reeves and sandra bullock together. it has to be a rom-com or a drama.", "entity_names": ["keanu reeves", "sandra bullock", "rom-com", "drama"], "entity_types": ["Actor", "Actor", "Genre", "Genre"]}
{"sentence": "what's that movie where leonardo dicaprio plays a con artist in the 90s?", "entity_names": ["leonardo dicaprio", "con artist", "90s"], "entity_types": ["Actor", "Character", "Year"]}
{"sentence": "can you show me a movie trailer for the latest horror film directed by jordan peele", "entity_names": ["horror film", "jordan peele"], "entity_types": ["Genre", "Director"]}
{"sentence": "what movie from the 90s has the best soundtrack", "entity_names": ["90s", "best soundtrack"], "entity_types": ["Year", "Review"]}
{"sentence": "can you tell me the leading actor in the shawshank redemption?", "entity_names": ["the shawshank redemption"], "entity_types": ["Title"]}
{"sentence": "what is the genre of the movie the matrix?", "entity_names": ["the matrix"], "entity_types": ["Title"]}
{"sentence": "who directed the making of the movie avatar", "entity_names": ["directed", "making of", "avatar"], "entity_types": ["Director", "Plot", "Title"]}
{"sentence": "can you recommend a film with a behind-the-scenes look at the special effects", "entity_names": ["behind-the-scenes", "special effects"], "entity_types": ["Plot", "Plot"]}
{"sentence": "show me a documentary about the production of the lord of the rings trilogy", "entity_names": ["production", "lord of the rings", "trilogy"], "entity_types": ["Plot", "Title", "Plot"]}
{"sentence": "i'm looking for a romantic comedy from the 2000s with a diverse cast and strong lgbtq+ representation. any suggestions?", "entity_names": ["romantic comedy", "2000s", "diverse cast", "lgbtq+ representation"], "entity_types": ["Genre", "Year", "Character", "Plot"]}
{"sentence": "who directed the making-of documentary for the titanic movie", "entity_names": ["directed", "making-of documentary", "titanic"], "entity_types": ["Director", "Plot", "Title"]}
{"sentence": "can you recommend a movie with a behind-the-scenes look at the special effects used in action films", "entity_names": ["action films"], "entity_types": ["Genre"]}
{"sentence": "is there a documentary about the history of animation films and the filmmakers behind them", "entity_names": ["documentary", "history of animation films", "filmmakers"], "entity_types": ["Genre", "Plot", "Director"]}
{"sentence": "what is the name of the movie where the song 'my heart will go on' is featured?", "entity_names": ["my heart will go on"], "entity_types": ["Song"]}
{"sentence": "can you recommend a movie with a great soundtrack from the 80s?", "entity_names": ["80s"], "entity_types": ["Year"]}
{"sentence": "i'm looking for a film with a famous actor that has a jazz music theme, can you suggest one?", "entity_names": ["jazz music"], "entity_types": ["Genre"]}
{"sentence": "can you tell me about a movie with a really interesting storyline", "entity_names": ["really interesting", "storyline"], "entity_types": ["Viewers' Rating", "Plot"]}
{"sentence": "is there a film with a surprising twist ending", "entity_names": ["surprising twist ending"], "entity_types": ["Plot"]}
{"sentence": "i'm in a hurry, can you tell me the genre of the movie forest gump?", "entity_names": ["genre", "forest gump"], "entity_types": ["Genre", "Title"]}
{"sentence": "i need to know the subgenre of the film the matrix right now", "entity_names": ["subgenre", "the matrix"], "entity_types": ["Genre", "Title"]}
{"sentence": "what's the genre of the classic movie casablanca? hurry up!", "entity_names": ["genre", "casablanca"], "entity_types": ["Genre", "Title"]}
{"sentence": "can you recommend a movie with an amazing soundtrack that features the song 'bohemian rhapsody'?", "entity_names": ["bohemian rhapsody"], "entity_types": ["Song"]}
{"sentence": "i'm looking for a movie from the 80s with a fantastic soundtrack that has a high viewers' rating.", "entity_names": ["80s", "high viewers' rating"], "entity_types": ["Year", "Viewers' Rating"]}
{"sentence": "who directed the film with the best original song in the past decade?", "entity_names": ["best original song", "past decade"], "entity_types": ["Song", "Year"]}
{"sentence": "what is the viewers' rating for the movie 'inception' directed by christopher nolan?", "entity_names": ["viewers' rating", "inception", "christopher nolan"], "entity_types": ["Viewers' Rating", "Title", "Director"]}
{"sentence": "can you provide the plot summary for the film 'the shawshank redemption' released in 1994?", "entity_names": ["plot summary", "the shawshank redemption", "1994"], "entity_types": ["Plot", "Title", "Year"]}
{"sentence": "i am looking for a thriller movie directed by david fincher, could you suggest a title?", "entity_names": ["thriller", "david fincher"], "entity_types": ["Genre", "Director"]}
{"sentence": "what movie has the best soundtrack of all time?", "entity_names": ["best"], "entity_types": ["Review"]}
{"sentence": "can you recommend a movie with iconic music from the 80s?", "entity_names": ["iconic music", "80s"], "entity_types": ["Song", "Year"]}
{"sentence": "which movie features the song 'my heart will go on' by celine dion?", "entity_names": ["my heart will go on", "celine dion"], "entity_types": ["Song", "Actor"]}
{"sentence": "can you show me a trailer for a movie about love and loss, something that will make me cry?", "entity_names": ["love and loss"], "entity_types": ["Plot"]}
{"sentence": "i need to watch something that will tug at my heartstrings. show me a movie trailer that's emotional and touching.", "entity_names": ["emotional and touching"], "entity_types": ["Review"]}
{"sentence": "i'm feeling down and could use something to uplift my spirits. can you play a teaser for a movie with a bittersweet storyline?", "entity_names": ["bittersweet"], "entity_types": ["Plot"]}
{"sentence": "what are some highly-rated romantic movies from the 1950s?", "entity_names": ["highly-rated", "romantic", "1950s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "can you recommend a comedy film directed by nancy meyers?", "entity_names": ["comedy", "nancy meyers"], "entity_types": ["Genre", "Director"]}
{"sentence": "show me the trailer for the latest meryl streep movie", "entity_names": ["trailer", "latest", "meryl streep"], "entity_types": ["Trailer", "Year", "Actor"]}
{"sentence": "can you recommend a highly-rated thriller directed by david fincher?", "entity_names": ["highly-rated", "thriller", "david fincher"], "entity_types": ["Viewers' Rating", "Genre", "Director"]}
{"sentence": "could you provide me with a review of the latest quentin tarantino movie?", "entity_names": ["review", "quentin tarantino"], "entity_types": ["Review", "Director"]}
{"sentence": "show me a trailer for a top-rated animated film from the 2010s.", "entity_names": ["trailer", "top-rated", "animated", "2010s"], "entity_types": ["Trailer", "Viewers' Rating", "Genre", "Year"]}
{"sentence": "can i buy tickets for the new james bond movie today?", "entity_names": ["james bond", "today"], "entity_types": ["Character", "Year"]}
{"sentence": "what are the showtimes for the latest tarantino film?", "entity_names": ["showtimes", "tarantino"], "entity_types": ["Review", "Director"]}
{"sentence": "i'm looking for a good action movie to watch this weekend. any recommendations?", "entity_names": ["action movie", "weekend"], "entity_types": ["Genre", "Year"]}
{"sentence": "what are the showtimes for the latest marvel movie?", "entity_names": ["marvel movie"], "entity_types": ["Genre"]}
{"sentence": "can i buy tickets for the new horror film directed by jordan peele?", "entity_names": ["horror film", "jordan peele"], "entity_types": ["Genre", "Director"]}
{"sentence": "is there a romantic comedy playing at the theater this weekend?", "entity_names": ["romantic comedy"], "entity_types": ["Genre"]}
{"sentence": "yo, where can i peep the trailer for that new superhero flick with the kickass special effects?", "entity_names": ["trailer", "superhero flick"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "hey, what's the deal with that movie everyone's gossiping about? is it worth checking out?", "entity_names": ["movie", "everyone's gossiping about"], "entity_types": ["Title", "Viewers' Rating"]}
{"sentence": "yo, who's the mastermind behind that dope action movie from last year with the insane stunts?", "entity_names": ["mastermind", "action movie", "last year", "insane stunts"], "entity_types": ["Director", "Genre", "Year", "Plot"]}
{"sentence": "i'm curious about the director behind the scenes of that action-packed film with the intense fight scenes. can you tell me who directed it?", "entity_names": ["action-packed", "intense fight scenes"], "entity_types": ["Genre", "Plot"]}
{"sentence": "i heard there was a must-see movie from the 90s that had a really interesting making-of story. do you know which one i'm talking about?", "entity_names": ["must-see", "90s", "making-of story"], "entity_types": ["Viewers' Rating", "Year", "Plot"]}
{"sentence": "i'm looking for a film with a legendary actor known for their dedication to perfecting their roles, like in those behind-the-scenes documentaries. can you recommend one?", "entity_names": ["legendary actor", "dedication to perfecting their roles"], "entity_types": ["Actor", "Actor"]}
{"sentence": "are there any good movies playing at the theater right now?", "entity_names": [], "entity_types": []}
{"sentence": "what's the latest movie that's out in theaters?", "entity_names": [], "entity_types": []}
{"sentence": "can you recommend a movie to watch this weekend?", "entity_names": [], "entity_types": []}
{"sentence": "can i stream any movies directed by christopher nolan?", "entity_names": ["christopher nolan"], "entity_types": ["Director"]}
{"sentence": "are there any thriller movies from the 90s that i can watch?", "entity_names": ["thriller", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "which film has been highly praised by critics and viewers alike?", "entity_names": ["highly praised"], "entity_types": ["Review"]}
{"sentence": "can you recommend a recent movie with a stellar cast and a compelling plot?", "entity_names": ["stellar cast", "compelling plot"], "entity_types": ["Actor", "Plot"]}
{"sentence": "i'm interested in a movie that is known for its incredible soundtrack, any suggestions?", "entity_names": ["incredible soundtrack"], "entity_types": ["Song"]}
{"sentence": "are you sure the movie 'the lion king' has the best soundtrack of all time?", "entity_names": ["the lion king", "best soundtrack of all time"], "entity_types": ["Title", "Review"]}
{"sentence": "can you really find the song 'i will always love you' in the movie 'the bodyguard'?", "entity_names": ["i will always love you", "the bodyguard"], "entity_types": ["Song", "Title"]}
{"sentence": "do you really think the movie 'a star is born' has a soundtrack worth listening to?", "entity_names": ["a star is born", "soundtrack worth listening to"], "entity_types": ["Title", "Review"]}
{"sentence": "can you recommend a movie with a great soundtrack featuring love songs from the 80s?", "entity_names": ["love songs from the 80s"], "entity_types": ["Genre"]}
{"sentence": "i'm looking for a movie with a beautiful original score composed by a female artist. any recommendations?", "entity_names": ["original score", "female artist"], "entity_types": ["Song", "Actor"]}
{"sentence": "could you suggest a romantic film with a memorable theme song that became popular in the 90s?", "entity_names": ["romantic film", "theme song", "popular in the 90s"], "entity_types": ["Genre", "Song", "Year"]}
{"sentence": "are there any streaming platforms where i can watch the movie titanic?", "entity_names": ["titanic"], "entity_types": ["Title"]}
{"sentence": "can i find the movie inception available for streaming anywhere?", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "can i watch the trailer for the new james bond movie no time to die?", "entity_names": ["trailer", "james bond", "no time to die"], "entity_types": ["Trailer", "Character", "Title"]}
{"sentence": "what is the viewers' rating for the movie parasite directed by bong joon-ho?", "entity_names": ["viewers' rating", "parasite", "bong joon-ho"], "entity_types": ["Viewers' Rating", "Title", "Director"]}
{"sentence": "is there a comedy movie from the 80s directed by john hughes?", "entity_names": ["comedy", "80s", "john hughes"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "hey, can you play the trailer for the new fast & furious movie?", "entity_names": ["trailer", "fast & furious"], "entity_types": ["Trailer", "Title"]}
{"sentence": "i need to see a sneak peek of the upcoming horror movie with jamie lee curtis", "entity_names": ["sneak peek", "horror", "jamie lee curtis"], "entity_types": ["Trailer", "Genre", "Actor"]}
{"sentence": "yo, show me a preview of that sci-fi flick directed by steven spielberg", "entity_names": ["preview", "sci-fi", "steven spielberg"], "entity_types": ["Trailer", "Genre", "Director"]}
{"sentence": "can you show me the trailer for the latest romantic comedy film?", "entity_names": ["trailer", "romantic comedy"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "what movie has a heartwarming song that i can listen to?", "entity_names": ["heartwarming", "song"], "entity_types": ["Review", "Song"]}
{"sentence": "can you tell me about the awards and nominations received by the movie the shawshank redemption", "entity_names": ["the shawshank redemption"], "entity_types": ["Title"]}
{"sentence": "i'm curious to know the accolades for the film la la land", "entity_names": ["la la land"], "entity_types": ["Title"]}
{"sentence": "what awards did the movie parasite win, and what nominations did it receive?", "entity_names": ["parasite"], "entity_types": ["Title"]}
{"sentence": "can you recommend a movie with an epic soundtrack and action-packed scenes?", "entity_names": ["epic soundtrack", "action-packed scenes"], "entity_types": ["Song", "Plot"]}
{"sentence": "is there a movie with a heavy metal song in its soundtrack that has intense fight scenes?", "entity_names": ["heavy metal song", "intense fight scenes"], "entity_types": ["Song", "Plot"]}
{"sentence": "what movie has the best rock music and high-energy concert scenes?", "entity_names": ["rock music", "high-energy concert scenes"], "entity_types": ["Song", "Plot"]}
{"sentence": "can you find any teasers for the upcoming marvel movie", "entity_names": ["teasers", "marvel"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "i'm not sure if i want to watch a scary movie trailer or a romantic one, can you help me decide?", "entity_names": ["scary", "romantic"], "entity_types": ["Genre", "Genre"]}
{"sentence": "i'm not sure if i want to watch a classic film trailer or something more modern, can you recommend anything?", "entity_names": ["classic", "modern"], "entity_types": ["Genre", "Genre"]}
{"sentence": "do you know if there's a behind-the-scenes featurette for the latest star wars movie? i'd love to see how they created those special effects.", "entity_names": ["star wars", "special effects"], "entity_types": ["Title", "Plot"]}
{"sentence": "can you provide me with the viewers' rating for the movie the shawshank redemption?", "entity_names": ["viewers' rating", "the shawshank redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "please recommend a highly-rated romantic comedy film from the 1990s.", "entity_names": ["highly-rated", "romantic comedy", "1990s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "who directed the film inception and what is the plot of the movie?", "entity_names": ["directed", "inception", "plot"], "entity_types": ["Director", "Title", "Plot"]}
{"sentence": "what movie from the 2010s has won the most awards?", "entity_names": ["2010s", "won the most awards"], "entity_types": ["Year", "Review"]}
{"sentence": "which movie directed by christopher nolan has received the most nominations?", "entity_names": ["christopher nolan", "received the most nominations"], "entity_types": ["Director", "Review"]}
{"sentence": "can you tell me the year in which the film inception starring leonardo dicaprio was released?", "entity_names": ["inception", "leonardo dicaprio"], "entity_types": ["Title", "Actor"]}
{"sentence": "i'm looking for the genre of the movie pulp fiction directed by quentin tarantino.", "entity_names": ["pulp fiction", "quentin tarantino"], "entity_types": ["Title", "Director"]}
{"sentence": "can you recommend me some action thriller movies with a female lead?", "entity_names": ["action thriller", "female lead"], "entity_types": ["Genre", "Character"]}
{"sentence": "i'm in the mood for a mind-bending science fiction film, can you suggest one from the 90s?", "entity_names": ["mind-bending", "science fiction", "90s"], "entity_types": ["Plot", "Genre", "Year"]}
{"sentence": "can you tell me about any awards or nominations that the shawshank redemption received?", "entity_names": ["awards or nominations", "the shawshank redemption"], "entity_types": ["Review", "Title"]}
{"sentence": "i'm open to watching a movie directed by a female filmmaker that received critical acclaim. do you have any recommendations?", "entity_names": ["critical acclaim"], "entity_types": ["Review"]}
{"sentence": "what film did quentin tarantino direct that has a very high viewers' rating?", "entity_names": ["quentin tarantino", "viewers' rating"], "entity_types": ["Director", "Viewers' Rating"]}
{"sentence": "i'm interested in a romantic comedy released in the 1990s, do you have any recommendations?", "entity_names": ["romantic comedy", "1990s"], "entity_types": ["Genre", "Year"]}
{"sentence": "can you provide me with some insights into the making of the film inception?", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "i'm interested in learning about the director of the movie titanic.", "entity_names": ["titanic"], "entity_types": ["Title"]}
{"sentence": "could you show me a documentary about the creation of the lord of the rings series?", "entity_names": ["lord of the rings"], "entity_types": ["Title"]}
{"sentence": "hey, i'm dying to see the teaser for the new christopher nolan movie, could you please show me a snippet?", "entity_names": ["teaser", "christopher nolan"], "entity_types": ["Trailer", "Director"]}
{"sentence": "i'm so pumped for the upcoming marvel movie, can you hook me up with a glimpse of the trailer?", "entity_names": ["marvel", "trailer"], "entity_types": ["Genre", "Trailer"]}
{"sentence": "i'm really hyped for the next star wars film, can you show me a sneak peek of the teaser?", "entity_names": ["star wars", "sneak peek"], "entity_types": ["Title", "Trailer"]}
{"sentence": "can you recommend a movie with an amazing soundtrack that will make me want to go on an adventure?", "entity_names": ["amazing soundtrack", "adventure"], "entity_types": ["Song", "Genre"]}
{"sentence": "i'm looking for a film with a catchy theme song that will get me in the mood for an exciting journey. do you have any recommendations?", "entity_names": ["catchy theme song", "exciting journey"], "entity_types": ["Song", "Plot"]}
{"sentence": "i'm interested in a movie that features an epic soundtrack, preferably in the adventure or fantasy genre. any suggestions?", "entity_names": ["epic soundtrack", "adventure", "fantasy"], "entity_types": ["Song", "Genre", "Genre"]}
{"sentence": "can you tell me about the director of the film the revenant and any behind-the-scenes details?", "entity_names": ["director", "the revenant"], "entity_types": ["Director", "Title"]}
{"sentence": "i'm interested in a movie with an amazing soundtrack. do you have any recommendations and can you provide some behind-the-scenes information about the music production?", "entity_names": ["amazing soundtrack", "behind-the-scenes information"], "entity_types": ["Song", "Plot"]}
{"sentence": "show me a classic movie directed by alfred hitchcock and share some behind-the-scenes stories about the making of the film.", "entity_names": ["alfred hitchcock"], "entity_types": ["Director"]}
{"sentence": "who directed the movie that reveals the making of the titanic film", "entity_names": ["making of", "titanic"], "entity_types": ["Plot", "Title"]}
{"sentence": "what are some must-see films that explore the behind-the-scenes of famous hollywood blockbusters", "entity_names": ["behind-the-scenes", "hollywood blockbusters"], "entity_types": ["Plot", "Genre"]}
{"sentence": "show me a film that delves into the production process of a classic western movie", "entity_names": ["classic western"], "entity_types": ["Genre"]}
{"sentence": "can you tell me which movie features the song 'my heart will go on'?", "entity_names": ["my heart will go on"], "entity_types": ["Song"]}
{"sentence": "show me a movie with a famous theme song that became a chart-topping hit", "entity_names": [], "entity_types": []}
{"sentence": "what film is known for its iconic soundtrack with songs by the beatles?", "entity_names": [], "entity_types": []}
{"sentence": "can you recommend a movie starring leonardo dicaprio?", "entity_names": ["leonardo dicaprio"], "entity_types": ["Actor"]}
{"sentence": "what's the best comedy film from the 2000s?", "entity_names": ["best", "comedy film", "2000s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "what is the name of the movie that features the song 'my heart will go on'?", "entity_names": ["my heart will go on"], "entity_types": ["Song"]}
{"sentence": "show me a movie where the main character is a famous dj", "entity_names": ["famous dj"], "entity_types": ["Character"]}
{"sentence": "can you tell me the plot of the movie inception?", "entity_names": ["plot", "inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "what are some highly-rated science fiction movies from the 1990s?", "entity_names": ["highly-rated", "science fiction", "1990s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "who are the main characters in the film the shawshank redemption?", "entity_names": ["main characters", "the shawshank redemption"], "entity_types": ["Character", "Title"]}
{"sentence": "can you tell me which film has received the highest viewers' rating recently?", "entity_names": ["highest viewers' rating"], "entity_types": ["Viewers' Rating"]}
{"sentence": "i'm curious about the type of movie that quentin tarantino directed in 2019, can you provide some information?", "entity_names": ["quentin tarantino", "2019"], "entity_types": ["Director", "Year"]}
{"sentence": "i'm looking for a classic movie that has an iconic song in it, any recommendations?", "entity_names": [], "entity_types": []}
{"sentence": "what is the storyline of the film the shawshank redemption?", "entity_names": ["storyline", "the shawshank redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "could you give me a brief summary of the movie the godfather?", "entity_names": ["summary", "the godfather"], "entity_types": ["Plot", "Title"]}
{"sentence": "what movie features the song my heart will go on", "entity_names": ["my heart will go on"], "entity_types": ["Song"]}
{"sentence": "can you recommend a film with an iconic soundtrack", "entity_names": ["iconic"], "entity_types": ["Song"]}
{"sentence": "who directed the movie that features the song eye of the tiger", "entity_names": ["eye of the tiger"], "entity_types": ["Song"]}
{"sentence": "i'm not sure which movie to watch tonight, can you recommend a film with a high viewers' rating and a compelling plot?", "entity_names": ["high viewers' rating", "compelling plot"], "entity_types": ["Viewers' Rating", "Plot"]}
{"sentence": "i can't decide on a movie, do you have any suggestions for a classic film directed by alfred hitchcock?", "entity_names": ["classic film", "alfred hitchcock"], "entity_types": ["Title", "Director"]}
{"sentence": "i'm looking for a movie with a strong female lead and a great soundtrack, any recommendations?", "entity_names": ["strong female lead", "great soundtrack"], "entity_types": ["Character", "Song"]}
{"sentence": "can you give me a detailed summary of the plot for the movie inception?", "entity_names": ["detailed summary", "inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "i'm looking for a movie with a mind-bending plot twist. what would you recommend?", "entity_names": ["mind-bending plot twist"], "entity_types": ["Plot"]}
{"sentence": "i want to watch a movie with an intense and gripping storyline. any suggestions?", "entity_names": ["intense and gripping storyline"], "entity_types": ["Plot"]}
{"sentence": "can you recommend a romantic comedy movie from the 1990s with a high viewers' rating?", "entity_names": ["romantic comedy", "1990s", "high"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "i'm looking for a drama film directed by a female director that received positive reviews.", "entity_names": ["drama", "female director", "positive reviews"], "entity_types": ["Genre", "Director", "Review"]}
{"sentence": "which movie starring tom hanks has the highest viewers' rating?", "entity_names": ["tom hanks", "highest"], "entity_types": ["Actor", "Viewers' Rating"]}
{"sentence": "what movie falls under the category of science fiction and has a strong emphasis on time travel?", "entity_names": ["science fiction", "time travel"], "entity_types": ["Genre", "Plot"]}
{"sentence": "show me a movie that belongs to the horror genre and features a haunted house as the main setting.", "entity_names": ["horror", "haunted house"], "entity_types": ["Genre", "Plot"]}
{"sentence": "hey, can i see a sneak peek of the new james bond movie?", "entity_names": ["sneak peek", "james bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "what's the deal with that new horror movie? is it really as scary as they say?", "entity_names": ["horror"], "entity_types": ["Genre"]}
{"sentence": "do you have any trailers for those blockbuster action movies coming out next year?", "entity_names": ["trailers", "action", "next year"], "entity_types": ["Trailer", "Genre", "Year"]}
{"sentence": "can you give me a summary of the movie inception", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "what is the plot of the film the shawshank redemption", "entity_names": ["the shawshank redemption"], "entity_types": ["Title"]}
{"sentence": "tell me about the storyline of the matrix trilogy", "entity_names": ["the matrix"], "entity_types": ["Title"]}
{"sentence": "yo, did that movie with leo dicaprio win any sick awards or get any nominations?", "entity_names": ["leo dicaprio", "nominations"], "entity_types": ["Actor", "Review"]}
{"sentence": "hey, has any movie from the 90s got mad recognition and accolades?", "entity_names": ["90s", "recognition", "accolades"], "entity_types": ["Year", "Review", "Viewers' Rating"]}
{"sentence": "yo, which flick got the most love from the critics and the audience?", "entity_names": ["love from the critics", "audience"], "entity_types": ["Review", "Viewers' Rating"]}
{"sentence": "can you tell me the name of the movie with the song 'let it go'?", "entity_names": ["let it go"], "entity_types": ["Song"]}
{"sentence": "i want to watch a film with the best soundtrack, what do you recommend?", "entity_names": ["best soundtrack"], "entity_types": ["Review"]}
{"sentence": "could you show me a movie with a great love song in it?", "entity_names": ["love song"], "entity_types": ["Song"]}
{"sentence": "who directed the film titanic?", "entity_names": ["directed", "titanic"], "entity_types": ["Director", "Title"]}
{"sentence": "can you recommend a thriller movie from the 1990s?", "entity_names": ["thriller", "1990s"], "entity_types": ["Genre", "Year"]}
{"sentence": "can you tell me about a movie with a really catchy song?", "entity_names": ["catchy song"], "entity_types": ["Song"]}
{"sentence": "do you have any recommendations for movies with great music?", "entity_names": ["great music"], "entity_types": ["Song"]}
{"sentence": "what's a movie that has a famous song in it?", "entity_names": ["famous song"], "entity_types": ["Song"]}
{"sentence": "can you recommend a movie with a great soundtrack?", "entity_names": ["great soundtrack"], "entity_types": ["Review"]}
{"sentence": "show me a movie with an iconic song from the 80s", "entity_names": ["iconic song", "80s"], "entity_types": ["Song", "Year"]}
{"sentence": "is there a movie with a famous song by queen in it?", "entity_names": [], "entity_types": []}
{"sentence": "yo, where can i stream that new spider-man flick?", "entity_names": ["spider-man"], "entity_types": ["Title"]}
{"sentence": "can i find any 80s action movies starring arnold schwarzenegger on any streaming service?", "entity_names": ["80s action", "arnold schwarzenegger"], "entity_types": ["Genre", "Actor"]}
{"sentence": "yo, what's the deal with that horror movie from 2019 with the killer clown? can i watch it anywhere?", "entity_names": ["horror movie", "2019", "killer clown"], "entity_types": ["Genre", "Year", "Character"]}
{"sentence": "can you tell me the showtimes for the movie a star is born?", "entity_names": ["a star is born"], "entity_types": ["Title"]}
{"sentence": "is there a movie theater nearby showing the film the bridges of madison county?", "entity_names": ["the bridges of madison county"], "entity_types": ["Title"]}
{"sentence": "are there any romance movies playing this weekend that are rated pg-13?", "entity_names": ["romance", "pg-13"], "entity_types": ["Genre", "MPAA Rating"]}
{"sentence": "what is the story of the film the shawshank redemption?", "entity_names": ["story", "the shawshank redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "could you give me a summary of the movie the matrix?", "entity_names": ["summary", "the matrix"], "entity_types": ["Plot", "Title"]}
{"sentence": "can you tell me about the director and some interesting behind-the-scenes facts of the movie the notebook?", "entity_names": ["director", "the notebook"], "entity_types": ["Director", "Title"]}
{"sentence": "i'm looking for a movie with a captivating plot and the song 'my heart will go on'. can you suggest one?", "entity_names": ["my heart will go on"], "entity_types": ["Song"]}
{"sentence": "i'm interested in a film from the 1980s with a strong female lead. can you recommend one with some details about the making-of?", "entity_names": ["1980s", "strong female lead"], "entity_types": ["Year", "Character"]}
{"sentence": "can you find a movie with a soundtrack featuring the song 'my heart will go on'?", "entity_names": ["my heart will go on"], "entity_types": ["Song"]}
{"sentence": "which movie directed by christopher nolan has the best soundtrack?", "entity_names": ["christopher nolan"], "entity_types": ["Director"]}
{"sentence": "what are some classic sci-fi films from the 80s directed by steven spielberg", "entity_names": ["sci-fi", "80s", "steven spielberg"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "can you recommend a crime thriller movie with a high viewers' rating?", "entity_names": ["crime thriller", "high viewers' rating"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "i'm looking for a romantic comedy with a catchy soundtrack, any recommendations?", "entity_names": ["romantic comedy", "catchy soundtrack"], "entity_types": ["Genre", "Song"]}
{"sentence": "can you give me a brief summary of the latest romantic comedy film?", "entity_names": ["romantic comedy"], "entity_types": ["Genre"]}
{"sentence": "i'm not sure what type of movie i want to watch. can you tell me about a thriller from the 90s?", "entity_names": ["thriller", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "i'm indecisive about what to watch tonight. can you tell me about a classic drama from the 1950s?", "entity_names": ["classic drama", "1950s"], "entity_types": ["Genre", "Year"]}
{"sentence": "what is the genre of the movie inception directed by christopher nolan?", "entity_names": ["inception", "christopher nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "can you recommend a classic comedy film from the 1980s?", "entity_names": ["classic comedy", "1980s"], "entity_types": ["Genre", "Year"]}
{"sentence": "show me a thriller movie with leonardo dicaprio in the lead role", "entity_names": ["thriller", "leonardo dicaprio"], "entity_types": ["Genre", "Actor"]}
{"sentence": "can you show me a teaser for the new james bond movie?", "entity_names": ["teaser", "james bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "i want to see a trailer for a horror movie that came out this year.", "entity_names": ["trailer", "horror", "this year"], "entity_types": ["Trailer", "Genre", "Year"]}
{"sentence": "are there any teasers for upcoming animated movies?", "entity_names": ["teasers", "upcoming", "animated"], "entity_types": ["Trailer", "Year", "Genre"]}
{"sentence": "yo, what's the deal with that new flick with the crazy plot twist at the end?", "entity_names": ["plot twist"], "entity_types": ["Plot"]}
{"sentence": "can you give me the lowdown on that movie where the guy goes all vigilante on the bad guys?", "entity_names": ["vigilante"], "entity_types": ["Plot"]}
{"sentence": "tell me about that film where the girl kicks butt and takes down the corrupt government, you know what i'm sayin'?", "entity_names": ["kicks butt", "corrupt government"], "entity_types": ["Plot", "Plot"]}
{"sentence": "can you tell me the story of the movie 'jurassic park'?", "entity_names": ["jurassic park"], "entity_types": ["Title"]}
{"sentence": "is there a movie from the 2000s with really cool action scenes?", "entity_names": ["2000s", "action"], "entity_types": ["Year", "Genre"]}
{"sentence": "i want to watch a film about a superhero who can fly - do you know any?", "entity_names": ["superhero", "fly"], "entity_types": ["Genre", "Plot"]}
{"sentence": "can i find the movie the godfather on any streaming platforms?", "entity_names": ["the godfather"], "entity_types": ["Title"]}
{"sentence": "is there a movie with an amazing soundtrack available on any streaming platform?", "entity_names": ["amazing soundtrack"], "entity_types": ["Review"]}
{"sentence": "can you recommend any classic romance movies from the 1950s?", "entity_names": ["classic romance", "1950s"], "entity_types": ["Genre", "Year"]}
{"sentence": "i'm in the mood for a lighthearted comedy set in the 1920s. any suggestions?", "entity_names": ["lighthearted comedy", "1920s"], "entity_types": ["Genre", "Year"]}
{"sentence": "what type of films fall under the crime genre, particularly those involving heists or capers?", "entity_names": ["crime"], "entity_types": ["Genre"]}
{"sentence": "can i watch the latest horror movie tonight?", "entity_names": ["latest horror movie"], "entity_types": ["Genre"]}
{"sentence": "is there a romantic comedy playing this weekend?", "entity_names": ["romantic comedy"], "entity_types": ["Genre"]}
{"sentence": "do you have any action films with tom cruise in the lead role?", "entity_names": ["action films", "tom cruise"], "entity_types": ["Genre", "Actor"]}
{"sentence": "can you actually show me a trailer for the new james bond movie?", "entity_names": ["trailer", "james bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "are there any teasers for the upcoming horror film directed by jordan peele?", "entity_names": ["teasers", "horror", "jordan peele"], "entity_types": ["Trailer", "Genre", "Director"]}
{"sentence": "i'm not sure if it's real, but can you play a snippet of the trailer for the next marvel superhero movie?", "entity_names": ["snippet", "marvel", "superhero"], "entity_types": ["Trailer", "Genre", "Genre"]}
{"sentence": "can you tell me the plot of a thriller movie directed by alfred hitchcock?", "entity_names": ["thriller", "alfred hitchcock"], "entity_types": ["Genre", "Director"]}
{"sentence": "i want to watch a film with a captivating storyline. can you recommend a movie released in the 1990s with a strong female lead?", "entity_names": ["1990s", "strong female lead"], "entity_types": ["Year", "Character"]}
{"sentence": "i'm looking for a movie with a complex narrative. could you suggest a film featuring time travel or alternate realities?", "entity_names": ["time travel"], "entity_types": ["Plot"]}
{"sentence": "i heard that some of the best action movies belong to a specific subgenre. can you tell me what kind of subgenre 'die hard' falls under?", "entity_names": ["die hard"], "entity_types": ["Title"]}
{"sentence": "i'm looking for a movie that has a really unique plot. can you recommend a film that falls under the science fiction genre?", "entity_names": ["science fiction"], "entity_types": ["Genre"]}
{"sentence": "i'm interested in watching a movie with a really intense soundtrack. do you have any recommendations for films that fall under the thriller genre?", "entity_names": ["thriller"], "entity_types": ["Genre"]}
{"sentence": "could you tell me the name of the director for the film inception?", "entity_names": ["director", "inception"], "entity_types": ["Director", "Title"]}
{"sentence": "i'm looking for a movie with meryl streep in it. can you recommend one with a high viewers' rating?", "entity_names": ["meryl streep", "high viewers' rating"], "entity_types": ["Actor", "Viewers' Rating"]}
{"sentence": "show me a movie directed by christopher nolan that has a trailer available.", "entity_names": ["christopher nolan", "trailer"], "entity_types": ["Director", "Trailer"]}
{"sentence": "can you recommend a comedy movie with a strong female lead that is set in the 1990s", "entity_names": ["comedy", "strong female lead", "1990s"], "entity_types": ["Genre", "Character", "Year"]}
{"sentence": "i am looking for a thriller movie with espionage as the main plot in the style of the bourne series", "entity_names": ["thriller", "espionage", "the bourne series"], "entity_types": ["Genre", "Plot", "Title"]}
{"sentence": "do you have any recommendations for science fiction movies that are centered around time travel and have a mind-bending plot twist", "entity_names": ["science fiction", "time travel", "mind-bending plot twist"], "entity_types": ["Genre", "Plot", "Plot"]}
{"sentence": "can you show me a teaser for the upcoming avengers movie", "entity_names": ["teaser", "avengers"], "entity_types": ["Trailer", "Title"]}
{"sentence": "i'm excited about the new star wars film, could you show me a sneak peek", "entity_names": ["star wars", "sneak peek"], "entity_types": ["Title", "Trailer"]}
{"sentence": "i want to see a preview of the new spider-man movie, please", "entity_names": ["preview", "spider-man"], "entity_types": ["Trailer", "Title"]}
{"sentence": "i need to see a preview of the new spider-man movie right now", "entity_names": ["preview", "spider-man"], "entity_types": ["Trailer", "Title"]}
{"sentence": "show me a teaser for the upcoming james bond film immediately", "entity_names": ["teaser", "james bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "i don't have time to waste, i want to watch a sneak peek of the new jurassic park movie", "entity_names": ["sneak peek", "jurassic park"], "entity_types": ["Trailer", "Title"]}
{"sentence": "hey, can you give me a preview of the latest fast and furious film with vin diesel in it?", "entity_names": ["fast and furious", "vin diesel"], "entity_types": ["Title", "Actor"]}
{"sentence": "i heard there's a new pixar animated movie out, can you show me a sneak peek or something?", "entity_names": ["animated"], "entity_types": ["Genre"]}
{"sentence": "who directed the 1994 film forrest gump?", "entity_names": ["directed", "1994", "forrest gump"], "entity_types": ["Director", "Year", "Title"]}
{"sentence": "show me a trailer for the film the matrix.", "entity_names": ["trailer", "the matrix"], "entity_types": ["Trailer", "Title"]}
{"sentence": "hey, can i watch the matrix on any streaming platforms?", "entity_names": ["the matrix"], "entity_types": ["Title"]}
{"sentence": "is there a new action movie out this year that's worth watching on any streaming services?", "entity_names": ["action movie", "worth watching", "streaming services"], "entity_types": ["Genre", "Viewers' Rating", "Genre"]}
{"sentence": "hey, what are some comedy movies from the 90s that i can stream right now?", "entity_names": ["comedy movies", "90s", "stream right now"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "can you please show me a trailer for the new james bond film?", "entity_names": ["trailer", "james bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "i'm dying to see a thrilling movie with an amazing plot. what do you recommend?", "entity_names": ["thrilling", "amazing plot"], "entity_types": ["Genre", "Plot"]}
{"sentence": "do you have any suggestions for must-see films with great soundtracks?", "entity_names": ["must-see", "great soundtracks"], "entity_types": ["Viewers' Rating", "Song"]}
{"sentence": "can i watch the trailer for the latest james bond movie?", "entity_names": ["trailer", "james bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "what movie from the 80s has the best soundtrack?", "entity_names": ["80s", "best soundtrack"], "entity_types": ["Year", "Review"]}
{"sentence": "who directed the science fiction film that won best picture in the 90s?", "entity_names": ["science fiction", "best picture", "90s"], "entity_types": ["Genre", "Review", "Year"]}
{"sentence": "can you give me some behind-the-scenes insights into the making of the latest sci-fi movie about time travel?", "entity_names": ["sci-fi", "time travel"], "entity_types": ["Genre", "Plot"]}
{"sentence": "i'm really curious about the director's vision for the upcoming crime thriller. could you share some behind-the-scenes details about it?", "entity_names": ["crime thriller"], "entity_types": ["Genre"]}
{"sentence": "i'm excited about the new fantasy film coming out. can you provide some behind-the-scenes information about the special effects and cgi used in it?", "entity_names": ["fantasy", "special effects", "cgi"], "entity_types": ["Genre", "Plot", "Plot"]}
{"sentence": "can you tell me about the making of the movie inception?", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "who directed the western movie tombstone?", "entity_names": ["tombstone"], "entity_types": ["Title"]}
{"sentence": "i want to see some behind-the-scenes footage of the 1980s movie top gun", "entity_names": ["1980s", "top gun"], "entity_types": ["Year", "Title"]}
{"sentence": "what are some movies with classic 1950s songs in their soundtracks?", "entity_names": ["classic 1950s songs"], "entity_types": ["Song"]}
{"sentence": "could you recommend a movie with a beautiful instrumental soundtrack for relaxation?", "entity_names": ["relaxation"], "entity_types": ["Viewers' Rating"]}
{"sentence": "which movie features the song 'my heart will go on' by celine dion in its soundtrack?", "entity_names": ["my heart will go on", "celine dion"], "entity_types": ["Song", "Actor"]}
{"sentence": "show me a movie with tom hanks as the lead actor", "entity_names": ["tom hanks"], "entity_types": ["Actor"]}
{"sentence": "can you tell me the plot of the latest james bond movie?", "entity_names": ["james bond"], "entity_types": ["Character"]}
{"sentence": "i need to know the storyline of the highest-rated horror film of 2021 asap", "entity_names": ["horror film", "2021"], "entity_types": ["Genre", "Year"]}
{"sentence": "what's the premise of the upcoming christopher nolan movie?", "entity_names": ["christopher nolan"], "entity_types": ["Director"]}
{"sentence": "has the movie la la land won any awards?", "entity_names": ["la la land"], "entity_types": ["Title"]}
{"sentence": "what nominations did the movie the shape of water receive?", "entity_names": ["nominations", "the shape of water"], "entity_types": ["Plot", "Title"]}
{"sentence": "can you tell me about the director of the movie titanic and how the special effects were created?", "entity_names": ["director", "titanic", "special effects"], "entity_types": ["Director", "Title", "Plot"]}
{"sentence": "i'm so bored, show me some interviews with the cast of the avengers film.", "entity_names": ["interviews", "the avengers"], "entity_types": ["Plot", "Title"]}
{"sentence": "what are some documentaries about the making of classic hitchcock films?", "entity_names": ["documentaries", "making of", "hitchcock"], "entity_types": ["Plot", "Plot", "Director"]}
{"sentence": "can you recommend any classic western films directed by john ford", "entity_names": ["classic western films", "john ford"], "entity_types": ["Genre", "Director"]}
{"sentence": "what is the highest rated sci-fi movie from the 1990s", "entity_names": ["highest rated", "sci-fi", "1990s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "show me a thriller film from the 2000s with a plot twist", "entity_names": ["thriller", "2000s", "plot twist"], "entity_types": ["Genre", "Year", "Plot"]}
{"sentence": "can you tell me about the director of the movie the aviator?", "entity_names": ["director", "the aviator"], "entity_types": ["Director", "Title"]}
{"sentence": "what was the process of creating the special effects for the movie jurassic park?", "entity_names": ["special effects", "jurassic park"], "entity_types": ["Plot", "Title"]}
{"sentence": "i'm interested in knowing about the actors who performed their own stunts in the movie mission: impossible. can you provide some details?", "entity_names": ["actors", "stunts", "mission: impossible"], "entity_types": ["Actor", "Plot", "Title"]}
{"sentence": "can you tell me about the awards and nominations received by the movie the dark knight?", "entity_names": ["the dark knight"], "entity_types": ["Title"]}
{"sentence": "which movie won the most awards in 2020?", "entity_names": ["2020"], "entity_types": ["Year"]}
{"sentence": "can you just tell me the plot of the shawshank redemption already?", "entity_names": ["plot", "the shawshank redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "i don't have time, just give me a quick summary of the movie inception.", "entity_names": ["summary", "inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "i need to know what the movie interstellar is about, can you give me a brief synopsis?", "entity_names": ["interstellar", "synopsis"], "entity_types": ["Title", "Plot"]}
{"sentence": "can you recommend a heart-wrenching movie with a high viewers' rating?", "entity_names": ["heart-wrenching", "high viewers' rating"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "i need a movie that will make me cry. who directed it?", "entity_names": ["make me cry", "directed"], "entity_types": ["Review", "Director"]}
{"sentence": "what's a movie with a tragic plot that received rave reviews?", "entity_names": ["tragic plot", "rave reviews"], "entity_types": ["Plot", "Review"]}
{"sentence": "can you show me the trailer for the classic movie 'the lion king'?", "entity_names": ["trailer", "classic", "the lion king"], "entity_types": ["Trailer", "Viewers' Rating", "Title"]}
{"sentence": "i remember a movie with a catchy song, can you show me a teaser for the film with the song 'my heart will go on'?", "entity_names": ["teaser", "my heart will go on"], "entity_types": ["Trailer", "Song"]}
{"sentence": "i'm feeling nostalgic for an old western movie, can you show me a preview for the film 'the good, the bad and the ugly'?", "entity_names": ["old", "western", "preview", "the good, the bad and the ugly"], "entity_types": ["Year", "Genre", "Trailer", "Title"]}
{"sentence": "can you recommend a movie with a memorable soundtrack that is worth listening to?", "entity_names": ["memorable soundtrack"], "entity_types": ["Review"]}
{"sentence": "which movie from the 80s is known for its iconic song that became a hit?", "entity_names": ["80s", "iconic song"], "entity_types": ["Year", "Song"]}
{"sentence": "could you suggest a film with a beautiful theme song that complements the storyline?", "entity_names": ["storyline"], "entity_types": ["Plot"]}
{"sentence": "hey, what's that super scary horror movie with the killer clown that came out in 2017?", "entity_names": ["horror", "killer clown", "2017"], "entity_types": ["Genre", "Character", "Year"]}
{"sentence": "can you recommend a good action movie from the 90s with bruce willis in it?", "entity_names": ["action", "90s", "bruce willis"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "i'm in the mood for a romantic comedy, can you suggest one that's really heartwarming?", "entity_names": ["romantic comedy", "heartwarming"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "i'm looking for a film that has a memorable soundtrack.", "entity_names": ["memorable soundtrack"], "entity_types": ["Song"]}
{"sentence": "do you know of any movies with a great music score?", "entity_names": ["great music score"], "entity_types": ["Song"]}
{"sentence": "can you tell me about the making of the movie inception directed by christopher nolan?", "entity_names": ["inception", "christopher nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "what behind-the-scenes footage is available for the movie avatar by james cameron?", "entity_names": ["avatar", "james cameron"], "entity_types": ["Title", "Director"]}
{"sentence": "i'm interested in learning more about the production process for the movie the matrix directed by the wachowskis. can you provide any details?", "entity_names": ["the matrix", "wachowskis"], "entity_types": ["Title", "Director"]}
{"sentence": "where can i find the movie interstellar to watch?", "entity_names": ["interstellar"], "entity_types": ["Title"]}
{"sentence": "has the movie 'the shawshank redemption' won any awards or nominations?", "entity_names": ["the shawshank redemption", "awards or nominations"], "entity_types": ["Title", "Review"]}
{"sentence": "what awards did 'parasite' win in the year it was released?", "entity_names": ["awards", "parasite"], "entity_types": ["Review", "Title"]}
{"sentence": "i need to know if 'la la land' received any nominations for its music and songs in the film", "entity_names": ["la la land", "nominations", "music and songs"], "entity_types": ["Title", "Review", "Song"]}
{"sentence": "what are some popular action-adventure films from the 1990s?", "entity_names": ["action-adventure", "1990s"], "entity_types": ["Genre", "Year"]}
{"sentence": "i'm looking for a romantic comedy with a unique storyline and a happy ending.", "entity_names": ["romantic comedy", "unique storyline", "happy ending"], "entity_types": ["Genre", "Plot", "Plot"]}
{"sentence": "hey, are there any showtimes for the new james bond movie?", "entity_names": ["james bond"], "entity_types": ["Title"]}
{"sentence": "do you know if the new horror movie is playing tonight?", "entity_names": ["horror"], "entity_types": ["Genre"]}
{"sentence": "can i get tickets for the new marvel movie at the theater near me?", "entity_names": ["marvel"], "entity_types": ["Title"]}
{"sentence": "what are some classic adventure movies like indiana jones", "entity_names": ["classic adventure", "indiana jones"], "entity_types": ["Genre", "Title"]}
{"sentence": "can you recommend a romantic comedy similar to when harry met sally", "entity_names": ["romantic comedy", "when harry met sally"], "entity_types": ["Genre", "Title"]}
{"sentence": "which crime thriller movies are worth watching", "entity_names": ["crime thriller"], "entity_types": ["Genre"]}
{"sentence": "what is the main genre of the movie the dark knight and what specific subgenre does it belong to?", "entity_names": ["main genre", "the dark knight", "specific subgenre"], "entity_types": ["Genre", "Title", "Genre"]}
{"sentence": "can you recommend a highly-rated comedy film from the 1980s that has an outstanding plot?", "entity_names": ["comedy film", "1980s", "outstanding plot"], "entity_types": ["Genre", "Year", "Plot"]}
{"sentence": "i'm interested in watching a science fiction movie with a focus on time travel. are there any recent ones with good viewer ratings?", "entity_names": ["science fiction movie", "time travel", "recent ones", "good viewer ratings"], "entity_types": ["Genre", "Plot", "Genre", "Viewers' Rating"]}
{"sentence": "yo, can you hook me up with a sneak peek of that new action flick with dwayne johnson?", "entity_names": ["sneak peek", "action", "dwayne johnson"], "entity_types": ["Trailer", "Genre", "Actor"]}
{"sentence": "hey, what's the deal with that horror movie trailer everyone's talking about?", "entity_names": ["horror", "trailer"], "entity_types": ["Genre", "Trailer"]}
{"sentence": "i heard there's a new rom-com dropping soon, can i get a taste of the trailer?", "entity_names": ["rom-com", "taste"], "entity_types": ["Genre", "Trailer"]}
{"sentence": "what are the showtimes for the movie inception directed by christopher nolan", "entity_names": ["inception", "christopher nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "where can i buy tickets for the film wonder woman 1984", "entity_names": ["wonder woman 1984"], "entity_types": ["Title"]}
{"sentence": "can you recommend a movie with an iconic 80s soundtrack?", "entity_names": ["iconic", "80s", "soundtrack"], "entity_types": ["Review", "Year", "Song"]}
{"sentence": "i miss classic musicals with great songs. can you suggest one for me?", "entity_names": ["classic musicals", "great songs"], "entity_types": ["Genre", "Song"]}
{"sentence": "can you recommend any recent sci-fi movies with a strong female lead?", "entity_names": ["recent", "sci-fi", "strong female lead"], "entity_types": ["Year", "Genre", "Character"]}
{"sentence": "what's a good comedy movie that also includes elements of romance and adventure?", "entity_names": ["comedy", "romance", "adventure"], "entity_types": ["Genre", "Genre", "Genre"]}
{"sentence": "i'm in the mood for some historical drama set in ancient times. can you suggest any titles?", "entity_names": ["historical drama", "ancient times"], "entity_types": ["Genre", "Year"]}
{"sentence": "can you tell me what movies emma stone has starred in?", "entity_names": ["emma stone"], "entity_types": ["Actor"]}
{"sentence": "what is the viewer's rating for the film the shawshank redemption?", "entity_names": ["viewer's rating", "the shawshank redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "i heard that there is a new movie out with a title track that everyone is talking about. can you tell me which streaming platform has it?", "entity_names": ["title track"], "entity_types": ["Song"]}
{"sentence": "i'm looking for a film from the 90s with a very intense plot. do you know if it's available on any streaming platforms?", "entity_names": ["90s", "intense plot"], "entity_types": ["Year", "Plot"]}
{"sentence": "i'm interested in a classic movie directed by a well-known director. can you check if it's on any streaming platforms?", "entity_names": ["classic movie", "well-known director"], "entity_types": ["Genre", "Director"]}
{"sentence": "yo, what's the deal with that sci-fi flick about time travel and stuff?", "entity_names": ["sci-fi", "time travel"], "entity_types": ["Genre", "Plot"]}
{"sentence": "hey, i'm looking for a movie with some crazy action scenes, like, fast and furious style.", "entity_names": ["action", "fast and furious style"], "entity_types": ["Genre", "Genre"]}
{"sentence": "can you recommend a romantic comedy that's not super cheesy, but still makes you feel all warm and fuzzy inside?", "entity_names": ["romantic comedy", "warm and fuzzy inside"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "i'd like to see the new marvel superhero movie, can you tell me the showtimes and ticket availability?", "entity_names": ["marvel superhero"], "entity_types": ["Genre"]}
{"sentence": "what is the best-rated action movie of the year and when can i see it?", "entity_names": ["best-rated", "action", "the year"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "i'm looking for a comedy film that's suitable for families, can i get the showtimes and buy tickets?", "entity_names": ["comedy", "families"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "which movie did steven spielberg direct in 1993?", "entity_names": ["steven spielberg", "1993"], "entity_types": ["Director", "Year"]}
{"sentence": "what are some films starring tom hanks and meg ryan?", "entity_names": ["tom hanks", "meg ryan"], "entity_types": ["Actor", "Actor"]}
{"sentence": "can you recommend a thriller directed by christopher nolan?", "entity_names": ["thriller", "christopher nolan"], "entity_types": ["Genre", "Director"]}
{"sentence": "can you tell me the storyline of the latest marvel movie?", "entity_names": ["marvel movie"], "entity_types": ["Title"]}
{"sentence": "do you have any recommendations for a family-friendly film with a heartwarming plot?", "entity_names": ["family-friendly", "heartwarming"], "entity_types": ["MPAA Rating", "Plot"]}
{"sentence": "can i watch inception on any streaming platforms right now?", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "is there any netflix original movie with tom hardy in it?", "entity_names": ["tom hardy"], "entity_types": ["Actor"]}
{"sentence": "what action-packed movie from the 90s can i stream with my friends this weekend?", "entity_names": ["action-packed", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "can you recommend a movie with a soundtrack featuring the song 'i will always love you'?", "entity_names": ["i will always love you"], "entity_types": ["Song"]}
{"sentence": "what's a movie from the 90s with a popular love song in its soundtrack?", "entity_names": ["90s", "popular love song"], "entity_types": ["Year", "Song"]}
{"sentence": "i'm looking for a film with a soundtrack filled with classic rock hits, can you suggest one?", "entity_names": ["classic rock hits"], "entity_types": ["Song"]}
{"sentence": "what is the overall rating for the movie to all the boys i've loved before", "entity_names": ["to all the boys i've loved before"], "entity_types": ["Title"]}
{"sentence": "can you recommend a coming-of-age movie with a strong female lead", "entity_names": ["coming-of-age", "strong female lead"], "entity_types": ["Genre", "Character"]}
{"sentence": "who directed the romantic comedy film with a high viewers' rating that came out last year", "entity_names": ["romantic comedy", "high viewers' rating", "last year"], "entity_types": ["Genre", "Viewers' Rating", "Year"]}
{"sentence": "can you play the trailer for the classic movie ghostbusters?", "entity_names": ["classic movie ghostbusters"], "entity_types": ["Title"]}
{"sentence": "i remember a movie with an iconic soundtrack - can you show me a snippet of the trailer for the bodyguard?", "entity_names": ["iconic soundtrack", "the bodyguard"], "entity_types": ["Song", "Title"]}
{"sentence": "i'm feeling nostalgic for the 80s - can you play the teaser for the film e.t. the extra-terrestrial?", "entity_names": ["nostalgic", "80s", "e.t. the extra-terrestrial"], "entity_types": ["Genre", "Year", "Title"]}
{"sentence": "hey, what's the plot of the new action movie with tom cruise?", "entity_names": ["action", "tom cruise"], "entity_types": ["Genre", "Actor"]}
{"sentence": "i heard about a new thriller film, can you tell me the plot?", "entity_names": ["thriller"], "entity_types": ["Genre"]}
{"sentence": "i can't wait to see a romantic comedy this weekend, can you give me a summary of a good one?", "entity_names": ["romantic comedy"], "entity_types": ["Genre"]}
{"sentence": "can you recommend any romantic comedy movies with a strong female lead?", "entity_names": ["romantic comedy", "strong female lead"], "entity_types": ["Genre", "Character"]}
{"sentence": "i'm in the mood for a classic musical, preferably from the 1950s or 1960s.", "entity_names": ["classic musical", "1950s or 1960s"], "entity_types": ["Genre", "Year"]}
{"sentence": "i'm looking for a heartfelt drama set in a small town, something like steel magnolias.", "entity_names": ["heartfelt drama", "small town", "steel magnolias"], "entity_types": ["Genre", "Plot", "Title"]}
{"sentence": "i'm not sure which movie has the best soundtrack, can you recommend one with a great song that sets the mood?", "entity_names": ["best soundtrack", "great song"], "entity_types": ["Review", "Song"]}
{"sentence": "i'm not sure which movie to watch, can you suggest one with a memorable theme song that's worth listening to?", "entity_names": ["worth listening to"], "entity_types": ["Viewers' Rating"]}
{"sentence": "can you provide me with a trailer for the latest james cameron film?", "entity_names": ["trailer", "james cameron"], "entity_types": ["Trailer", "Director"]}
{"sentence": "what is the plot of the highest-rated comedy movie from the 1990s?", "entity_names": ["highest-rated", "comedy", "1990s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "yo, where can i watch a flick with chris hemsworth kickin' butt? any idea?", "entity_names": ["chris hemsworth"], "entity_types": ["Actor"]}
{"sentence": "what is the showtime for the movie 'inception' at the nearest theater", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "are there any available tickets for the new james bond movie", "entity_names": ["james bond"], "entity_types": ["Character"]}
{"sentence": "can i see the trailer for the latest marvel movie", "entity_names": ["trailer", "marvel"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "can you play the trailer for the newest animated movie for kids?", "entity_names": ["newest animated movie"], "entity_types": ["Genre"]}
{"sentence": "show me the teaser for the family-friendly movie with the talking animals", "entity_names": ["family-friendly", "talking animals"], "entity_types": ["Viewers' Rating", "Plot"]}
{"sentence": "what movie from last year has a fun and exciting preview that kids will love?", "entity_names": ["last year", "preview", "kids"], "entity_types": ["Year", "Trailer", "Viewers' Rating"]}
{"sentence": "yo, where can i catch the latest avengers flick?", "entity_names": ["avengers"], "entity_types": ["Title"]}
{"sentence": "hey, is there a dope action movie playing at the theaters this weekend?", "entity_names": ["action"], "entity_types": ["Genre"]}
{"sentence": "yo, i'm tryna see a scary movie tonight, what's playing?", "entity_names": ["scary"], "entity_types": ["Genre"]}
{"sentence": "what's the highest rated action movie of all time?", "entity_names": ["highest rated", "action movie"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "can you recommend a comedy movie from the 90s with a great plot?", "entity_names": ["comedy movie", "90s", "great plot"], "entity_types": ["Genre", "Year", "Plot"]}
{"sentence": "who directed the best sci-fi movie in recent years?", "entity_names": ["best", "sci-fi movie", "recent years"], "entity_types": ["Review", "Genre", "Year"]}
{"sentence": "can you please show me a teaser for the new james bond movie?", "entity_names": ["teaser", "james bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "i'm dying to see a preview for the highly anticipated marvel movie releasing next year. can you assist with that?", "entity_names": ["preview", "marvel", "next year"], "entity_types": ["Trailer", "Genre", "Year"]}
{"sentence": "i heard there's a thrilling new trailer for the upcoming sci-fi film. i cannot wait to watch it!", "entity_names": ["thrilling", "trailer", "sci-fi"], "entity_types": ["Viewers' Rating", "Trailer", "Genre"]}
{"sentence": "i need to find a movie with an epic soundtrack, filled with songs that give me chills and make my heart race", "entity_names": ["epic soundtrack"], "entity_types": ["Song"]}
{"sentence": "what's the latest movie with an amazing original song? i'm in the mood to discover some new music that will blow my mind!", "entity_names": [], "entity_types": []}
{"sentence": "tell me about a movie with a soundtrack that will have me dancing in my seat! i need to feel the rhythm and groove along with the music", "entity_names": ["soundtrack"], "entity_types": ["Song"]}
{"sentence": "can you tell me the name of the movie directed by christopher nolan that has a surprise ending?", "entity_names": ["christopher nolan", "surprise ending"], "entity_types": ["Director", "Plot"]}
{"sentence": "i'm interested in a film starring meryl streep and tom hanks, can you recommend one?", "entity_names": ["meryl streep", "tom hanks"], "entity_types": ["Actor", "Actor"]}
{"sentence": "what is the highest-rated movie of quentin tarantino?", "entity_names": ["highest-rated", "quentin tarantino"], "entity_types": ["Viewers' Rating", "Director"]}
{"sentence": "can you show me the trailer for the new romantic comedy directed by nancy meyers?", "entity_names": ["romantic comedy", "nancy meyers"], "entity_types": ["Genre", "Director"]}
{"sentence": "i'm looking for a movie with a great soundtrack. can you recommend a film with a memorable song performed by whitney houston?", "entity_names": ["great soundtrack", "memorable song", "whitney houston"], "entity_types": ["Review", "Song", "Actor"]}
{"sentence": "i heard there's a new historical drama featuring meryl streep coming out soon. can i see a teaser for it?", "entity_names": ["historical drama", "meryl streep", "teaser"], "entity_types": ["Genre", "Actor", "Trailer"]}
{"sentence": "what are some mind-bending sci-fi movies that will leave me on the edge of my seat?", "entity_names": ["mind-bending", "sci-fi"], "entity_types": ["Genre", "Genre"]}
{"sentence": "can you recommend some classic western films with intense gunfights and gripping storylines?", "entity_names": ["classic", "western", "intense gunfights"], "entity_types": ["Viewers' Rating", "Genre", "Plot"]}
{"sentence": "i'm craving a horror movie that will scare the daylights out of me. what's a truly terrifying supernatural thriller worth watching?", "entity_names": ["horror", "terrifying", "supernatural"], "entity_types": ["Genre", "Viewers' Rating", "Plot"]}
{"sentence": "can you provide a summary of the movie inception directed by christopher nolan", "entity_names": ["summary", "inception", "christopher nolan"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "what are the top-rated sci-fi movies released in the past 5 years", "entity_names": ["top-rated", "sci-fi", "5 years"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "could you recommend a movie with a strong female lead character and a compelling plot", "entity_names": ["strong female lead"], "entity_types": ["Character"]}
{"sentence": "are there any action movies from the 1990s with a high viewers' rating and can you show me the making-of of the best one?", "entity_names": ["action", "1990s", "high viewers' rating", "making-of"], "entity_types": ["Genre", "Year", "Viewers' Rating", "Plot"]}
{"sentence": "i want to watch a comedy film from the 2000s, can you recommend one with funny bloopers and outtakes?", "entity_names": ["comedy", "2000s", "funny bloopers and outtakes"], "entity_types": ["Genre", "Year", "Plot"]}
{"sentence": "what is the plot of the film interstellar directed by christopher nolan?", "entity_names": ["plot", "interstellar", "christopher nolan"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "could you tell me the storyline of the movie the shawshank redemption?", "entity_names": ["storyline", "the shawshank redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "who directed the movie titanic and who starred in it? i need to know now!", "entity_names": ["directed", "titanic", "starred"], "entity_types": ["Director", "Title", "Actor"]}
{"sentence": "what's the highest rated action movie from the 90s? i really need to find something exciting to watch!", "entity_names": ["highest rated", "action", "90s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "can you tell me the plot of the latest james bond film and who's playing the villain? i can't wait to find out!", "entity_names": ["plot", "latest", "james bond", "playing the villain"], "entity_types": ["Plot", "Year", "Character", "Actor"]}
{"sentence": "can i buy tickets for the new spider-man movie directed by jon watts?", "entity_names": ["spider-man", "jon watts"], "entity_types": ["Title", "Director"]}
{"sentence": "what's the showtime for the action movie with a high viewers' rating?", "entity_names": ["action", "high viewers' rating"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "do you have any tickets available for the musical film directed by damien chazelle?", "entity_names": ["musical", "damien chazelle"], "entity_types": ["Genre", "Director"]}
{"sentence": "what movie has the best soundtrack of all time", "entity_names": ["best soundtrack of all time"], "entity_types": ["Review"]}
{"sentence": "can you recommend a movie with a great song by beyonce", "entity_names": ["great song by beyonce"], "entity_types": ["Song"]}
{"sentence": "show me a movie with an iconic musical number", "entity_names": [], "entity_types": []}
{"sentence": "i heard that the director of the dark knight also made a behind-the-scenes documentary. can you tell me more about it?", "entity_names": ["director", "the dark knight", "behind-the-scenes documentary"], "entity_types": ["Director", "Title", "Genre"]}
{"sentence": "i'm interested in learning about the making-of process of an iconic 90s film. do you have any recommendations?", "entity_names": ["making-of process", "90s"], "entity_types": ["Plot", "Year"]}
{"sentence": "i'm curious about a movie that showcases the development of special effects in the film industry. any suggestions?", "entity_names": ["special effects"], "entity_types": ["Plot"]}
{"sentence": "yo, who's the boss director of that new action flick with the sick car chase scene?", "entity_names": ["director", "action flick", "car chase scene"], "entity_types": ["Director", "Genre", "Plot"]}
{"sentence": "hey, what's the deal with that movie that has that girl from the horror flick last year, you know what i'm talking about?", "entity_names": ["horror flick", "last year"], "entity_types": ["Genre", "Year"]}
{"sentence": "yo, who's the dude that played the funny sidekick in that superhero movie with the dope soundtrack?", "entity_names": ["dude", "funny sidekick", "superhero movie", "dope soundtrack"], "entity_types": ["Actor", "Character", "Genre", "Song"]}
{"sentence": "what is the main genre and subgenre of the movie the dark knight?", "entity_names": ["the dark knight"], "entity_types": ["Title"]}
{"sentence": "can you recommend a movie with a mix of both romance and comedy as the main and subgenres?", "entity_names": ["romance", "comedy"], "entity_types": ["Genre", "Genre"]}
{"sentence": "is there a film that falls into the horror genre with a subgenre of psychological thriller?", "entity_names": ["horror", "psychological thriller"], "entity_types": ["Genre", "Genre"]}
{"sentence": "what's the buzz on the latest action flick?", "entity_names": [], "entity_types": []}
{"sentence": "can you recommend a highly-rated mystery movie from the 1980s?", "entity_names": ["highly-rated", "mystery", "1980s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "who played the lead in that classic romance film?", "entity_names": ["classic romance"], "entity_types": ["Genre"]}
{"sentence": "can you play the trailer for the latest james bond film?", "entity_names": ["trailer", "james bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "what movie has the most intense trailer of all time?", "entity_names": ["intense", "trailer"], "entity_types": ["Review", "Trailer"]}
{"sentence": "show me a teaser for the upcoming marvel movie.", "entity_names": ["teaser", "marvel"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "what's the plot of the movie inception?", "entity_names": ["plot", "inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "can you give me a summary of the movie goodfellas directed by martin scorsese?", "entity_names": ["summary", "goodfellas", "martin scorsese"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "i'm looking for a movie with a mysterious plot twist, can you recommend one?", "entity_names": ["mysterious plot twist"], "entity_types": ["Plot"]}
{"sentence": "what movie has the best trailer of all time?", "entity_names": ["best", "trailer"], "entity_types": ["Review", "Trailer"]}
{"sentence": "can you recommend any classic movies with a catchy song in the teaser?", "entity_names": ["classic", "catchy", "song", "teaser"], "entity_types": ["Genre", "Song", "Song", "Trailer"]}
{"sentence": "which director is known for creating amazing movie teasers?", "entity_names": ["amazing"], "entity_types": ["Review"]}
{"sentence": "i'm in the mood for a musical! can you recommend a movie with a catchy soundtrack from the 1980s?", "entity_names": ["musical", "catchy soundtrack", "1980s"], "entity_types": ["Genre", "Song", "Year"]}
{"sentence": "i'm looking for a feel-good movie with an uplifting song that will brighten my day. can you suggest one that's family-friendly?", "entity_names": ["feel-good", "uplifting song", "family-friendly"], "entity_types": ["Song", "Song", "MPAA Rating"]}
{"sentence": "i'm hosting a movie night and want to feature a film with a memorable love theme. can you recommend a romantic movie from the 1990s?", "entity_names": ["romantic", "1990s"], "entity_types": ["Genre", "Year"]}
{"sentence": "show me a movie starring tom hanks and meg ryan.", "entity_names": ["tom hanks", "meg ryan"], "entity_types": ["Actor", "Actor"]}
{"sentence": "what is the viewers' rating for the film la la land?", "entity_names": ["viewers' rating", "la la land"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "can i find the movie inception with leonardo dicaprio available on any streaming platform?", "entity_names": ["inception", "leonardo dicaprio"], "entity_types": ["Title", "Actor"]}
{"sentence": "i'm looking for a comedy from the 1990s that has a viewers' rating of at least 4 stars. is there any streaming platform offering such a movie?", "entity_names": ["comedy", "1990s", "4 stars"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "is there a movie directed by quentin tarantino with samuel l. jackson that is available for streaming?", "entity_names": ["quentin tarantino", "samuel l. jackson"], "entity_types": ["Director", "Actor"]}
{"sentence": "yo, who's the director of that new action flick with the rock?", "entity_names": ["action flick", "the rock"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what's the deal with that movie starring taraji p. henson and octavia spencer?", "entity_names": ["taraji p. henson", "octavia spencer"], "entity_types": ["Actor", "Actor"]}
{"sentence": "tell me about that film where keanu reeves plays a badass assassin.", "entity_names": ["keanu reeves"], "entity_types": ["Actor"]}
{"sentence": "can you provide me with a documentary on the production process of the movie inception directed by christopher nolan?", "entity_names": ["inception", "christopher nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "i'm interested in learning about the special effects used in the film avatar. could you recommend any behind-the-scenes featurettes or interviews with the visual effects team?", "entity_names": ["avatar"], "entity_types": ["Title"]}
{"sentence": "i'm looking for a movie that delves into the cinematography of lawrence of arabia and its impact on the filmmaking industry.", "entity_names": ["lawrence of arabia"], "entity_types": ["Title"]}
{"sentence": "can you tell me the showtimes for the new james bond movie?", "entity_names": ["showtimes", "james bond"], "entity_types": ["Plot", "Character"]}
{"sentence": "which theater is playing the latest marvel movie and what time does it start?", "entity_names": ["marvel"], "entity_types": ["Title"]}
{"sentence": "are there any science fiction films with high viewers' ratings currently showing?", "entity_names": ["science fiction", "high viewers' ratings"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "can you tell me the plot of the latest spider-man movie?", "entity_names": ["spider-man"], "entity_types": ["Title"]}
{"sentence": "what's the story of the sci-fi movie released in 2020 called tenet?", "entity_names": ["sci-fi", "2020", "tenet"], "entity_types": ["Genre", "Year", "Title"]}
{"sentence": "i want to know the plot of the action film directed by christopher nolan and rated pg-13", "entity_names": ["christopher nolan", "pg-13"], "entity_types": ["Director", "MPAA Rating"]}
{"sentence": "yo, who starred in that heist movie with the badass car chases?", "entity_names": ["heist movie", "car chases"], "entity_types": ["Genre", "Plot"]}
{"sentence": "hey, what's the deal with that flick from the 90s with the killer soundtrack?", "entity_names": ["90s", "killer soundtrack"], "entity_types": ["Year", "Song"]}
{"sentence": "can you hook me up with the deets on the director of that sci-fi flick with the epic special effects?", "entity_names": ["director", "sci-fi flick", "epic special effects"], "entity_types": ["Director", "Genre", "Plot"]}
{"sentence": "do you have any showtimes for the new animated movie 'sing 2'?", "entity_names": ["sing 2"], "entity_types": ["Title"]}
{"sentence": "is the movie 'encanto' suitable for young kids to watch?", "entity_names": ["encanto"], "entity_types": ["Title"]}
{"sentence": "could you recommend a family-friendly movie that came out this year?", "entity_names": ["family-friendly", "this year"], "entity_types": ["Genre", "Year"]}
{"sentence": "what is the genre and subgenre of the film 'the shawshank redemption' directed by frank darabont?", "entity_names": ["the shawshank redemption", "frank darabont"], "entity_types": ["Title", "Director"]}
{"sentence": "can you provide me with the genre and subgenre of the movie 'inception' from 2010?", "entity_names": ["inception", "2010"], "entity_types": ["Title", "Year"]}
{"sentence": "i would like to know the genre and subgenre of the film 'the godfather' directed by francis ford coppola.", "entity_names": ["the godfather", "francis ford coppola"], "entity_types": ["Title", "Director"]}
{"sentence": "\"can i watch titanic on any streaming platforms?", "entity_names": ["titanic"], "entity_types": ["Title"]}
{"sentence": "which streaming platform has the highest rated action movie from the 1990s?", "entity_names": ["streaming platform", "highest rated", "action movie", "1990s"], "entity_types": ["Genre", "Viewers' Rating", "Genre", "Year"]}
{"sentence": "what is the main genre and subgenre of the movie the shawshank redemption?", "entity_names": ["the shawshank redemption"], "entity_types": ["Title"]}
{"sentence": "can you provide the genre and subgenre details for the film inception directed by christopher nolan?", "entity_names": ["inception", "christopher nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "i am interested in knowing the genre and subgenre classifications for the movie the dark knight rises.", "entity_names": ["the dark knight rises"], "entity_types": ["Title"]}
{"sentence": "can you recommend a film from the 80s that falls into the horror genre?", "entity_names": ["80s", "horror"], "entity_types": ["Year", "Genre"]}
{"sentence": "what's a good mystery movie with a unique plot twist?", "entity_names": ["mystery", "unique plot twist"], "entity_types": ["Genre", "Plot"]}
{"sentence": "i'm looking for a movie that blends romance with comedy. any suggestions?", "entity_names": ["romance", "comedy"], "entity_types": ["Genre", "Genre"]}
{"sentence": "hey, what's that movie where tom hanks plays a cop in a big city?", "entity_names": ["tom hanks", "cop in a big city"], "entity_types": ["Actor", "Plot"]}
{"sentence": "can you recommend a horror movie with some supernatural elements?", "entity_names": ["horror", "supernatural elements"], "entity_types": ["Genre", "Plot"]}
{"sentence": "yo, who directed that awesome sci-fi flick with the time-travel storyline?", "entity_names": ["sci-fi", "time-travel storyline"], "entity_types": ["Genre", "Plot"]}
{"sentence": "are there any action movies playing tonight that were directed by james cameron", "entity_names": ["action", "james cameron"], "entity_types": ["Genre", "Director"]}
{"sentence": "can i buy tickets for a sci-fi film that was released in the 1990s and has a high viewers' rating?", "entity_names": ["sci-fi", "1990s", "high"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "what are the showtimes for the latest thriller movie starring denzel washington", "entity_names": ["thriller", "denzel washington"], "entity_types": ["Genre", "Actor"]}
{"sentence": "what's the rating for the latest marvel movie?", "entity_names": ["marvel movie"], "entity_types": ["Title"]}
{"sentence": "who directed the highest-grossing film of last year?", "entity_names": ["directed", "highest-grossing film", "last year"], "entity_types": ["Director", "Title", "Year"]}
{"sentence": "can you recommend a thriller movie with a high imdb rating?", "entity_names": ["thriller movie", "high imdb rating"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "i'm thinking of going to see a popular movie tonight, any suggestions?", "entity_names": ["popular movie"], "entity_types": ["Viewers' Rating"]}
{"sentence": "i heard there's a new film out that everyone's talking about, are there any showtimes available?", "entity_names": ["new film"], "entity_types": ["Title"]}
{"sentence": "i'm trying to find a good movie to watch this weekend, any recommendations?", "entity_names": ["good movie"], "entity_types": ["Viewers' Rating"]}
{"sentence": "can you recommend a highly-rated movie from the 1990s with a thought-provoking plot?", "entity_names": ["highly-rated", "1990s", "thought-provoking"], "entity_types": ["Viewers' Rating", "Year", "Plot"]}
{"sentence": "who directed the classic film with a stellar performance by meryl streep?", "entity_names": ["classic film", "meryl streep"], "entity_types": ["Genre", "Actor"]}
{"sentence": "could you suggest a family-friendly movie with a catchy soundtrack?", "entity_names": ["family-friendly"], "entity_types": ["MPAA Rating"]}
{"sentence": "what is the main genre of the movie the godfather and who directed it?", "entity_names": ["main genre", "the godfather", "directed"], "entity_types": ["Genre", "Title", "Director"]}
{"sentence": "can you recommend a good action movie from the 80s with a strong female lead?", "entity_names": ["action movie", "80s", "strong female lead"], "entity_types": ["Genre", "Year", "Character"]}
{"sentence": "i'm looking for a thriller movie directed by alfred hitchcock, can you suggest one?", "entity_names": ["thriller movie", "alfred hitchcock"], "entity_types": ["Genre", "Director"]}
{"sentence": "can you list the nominations for the movie inception?", "entity_names": ["nominations", "inception"], "entity_types": ["Review", "Title"]}
{"sentence": "i'm curious about the director behind the camera for the movie inception. can you tell me some interesting details about the making of that film?", "entity_names": ["director", "inception", "making of"], "entity_types": ["Director", "Title", "Plot"]}
{"sentence": "i wonder if there are any famous actors who have starred in classic musical films. can you give me some insights about the production of those movies?", "entity_names": ["musical"], "entity_types": ["Genre"]}
{"sentence": "i'm interested in learning about the history behind the making of the sci-fi movie blade runner. can you provide me with some details about the creative process involved in its production?", "entity_names": ["sci-fi", "blade runner", "production"], "entity_types": ["Genre", "Title", "Plot"]}
{"sentence": "can you recommend a movie with a high viewers' rating that came out in the last year?", "entity_names": ["high viewers' rating", "last year"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "who directed the latest disney princess movie with a catchy song?", "entity_names": ["latest disney princess movie", "catchy song"], "entity_types": ["Title", "Song"]}
{"sentence": "i want to watch a movie that is suitable for my age, can you suggest a film with a pg rating?", "entity_names": ["suitable for my age"], "entity_types": ["MPAA Rating"]}
{"sentence": "hey, what's the coolest action movie of all time?", "entity_names": ["coolest", "action"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "tell me about that new superhero movie with the highest ratings.", "entity_names": ["superhero", "highest ratings"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "who directed the animated film with the talking animals that everyone's been talking about?", "entity_names": ["animated", "talking animals"], "entity_types": ["Genre", "Plot"]}
{"sentence": "can you tell me a movie with a great soundtrack?", "entity_names": ["great soundtrack"], "entity_types": ["Song"]}
{"sentence": "i'm looking for a movie with memorable music, do you have any recommendations?", "entity_names": ["memorable music"], "entity_types": ["Song"]}
{"sentence": "i'm searching for a film that has a popular song in it, can you help me find one?", "entity_names": ["popular song"], "entity_types": ["Song"]}
{"sentence": "what's a good sci-fi movie to watch?", "entity_names": ["sci-fi"], "entity_types": ["Genre"]}
{"sentence": "who starred in the top-rated action movie of the year?", "entity_names": ["top-rated", "action", "the year"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "tell me about the new comedy with the catchy theme song.", "entity_names": ["comedy", "catchy theme song"], "entity_types": ["Genre", "Song"]}
{"sentence": "hey, have you heard about that new thriller movie with the crazy plot? what's the viewer rating for it?", "entity_names": ["thriller", "crazy plot", "viewer rating"], "entity_types": ["Genre", "Plot", "Viewers' Rating"]}
{"sentence": "i need a recommendation for a really good comedy film from the 90s, you got any in mind?", "entity_names": ["comedy", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "who starred in that horror movie directed by james wan?", "entity_names": ["horror", "james wan"], "entity_types": ["Genre", "Director"]}
{"sentence": "can i find the dark knight on any streaming platforms?", "entity_names": ["the dark knight"], "entity_types": ["Title"]}
{"sentence": "is there a movie with tom hanks on netflix?", "entity_names": ["tom hanks"], "entity_types": ["Actor"]}
{"sentence": "what are the showtimes for the movie the shawshank redemption?", "entity_names": ["the shawshank redemption"], "entity_types": ["Title"]}
{"sentence": "is the movie casablanca playing in theaters near me?", "entity_names": ["casablanca"], "entity_types": ["Title"]}
{"sentence": "could you tell me if there are any available tickets for the movie lawrence of arabia?", "entity_names": ["lawrence of arabia"], "entity_types": ["Title"]}
{"sentence": "can you recommend a movie with a strong female lead that has a high viewers' rating?", "entity_names": ["strong female lead", "high"], "entity_types": ["Character", "Viewers' Rating"]}
{"sentence": "who directed the top-rated romantic comedy of the year 2020?", "entity_names": ["romantic comedy", "2020"], "entity_types": ["Genre", "Year"]}
{"sentence": "i'm looking for a movie with an amazing soundtrack, particularly one with a popular pop song. can you suggest one?", "entity_names": [], "entity_types": []}
{"sentence": "could you recommend a movie with a memorable soundtrack that features the song 'my heart will go on'?", "entity_names": ["my heart will go on"], "entity_types": ["Song"]}
{"sentence": "i am looking for a movie with a powerful theme song that perfectly complements the storyline. do you have any suggestions?", "entity_names": ["powerful theme song"], "entity_types": ["Song"]}
{"sentence": "can you tell me about the awards and nominations received by the film the shape of water?", "entity_names": ["the shape of water"], "entity_types": ["Title"]}
{"sentence": "can you tell me the plot of the movie sleepless in seattle?", "entity_names": ["plot", "sleepless in seattle"], "entity_types": ["Plot", "Title"]}
{"sentence": "what movie with romance as the genre has a captivating and heartwarming plot?", "entity_names": ["romance", "heartwarming"], "entity_types": ["Genre", "Plot"]}
{"sentence": "could you recommend a movie directed by nora ephron with a plot that involves a love story?", "entity_names": ["nora ephron", "love story"], "entity_types": ["Director", "Plot"]}
{"sentence": "what is the name of the action-adventure film directed by steven spielberg in the 1980s?", "entity_names": ["action-adventure", "steven spielberg", "1980s"], "entity_types": ["Genre", "Director", "Year"]}
{"sentence": "can you tell me about the director of the movie 'the sound of music' and any interesting behind-the-scenes stories about its filming?", "entity_names": ["director", "the sound of music", "behind-the-scenes stories"], "entity_types": ["Director", "Title", "Plot"]}
{"sentence": "i'm interested in finding a classic movie from the 1950s known for its breathtaking scenery. do you have any recommendations?", "entity_names": ["1950s", "breathtaking scenery"], "entity_types": ["Year", "Plot"]}
{"sentence": "can you give me a brief summary of the plot for the movie inception?", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "what is the storyline for the film shawshank redemption?", "entity_names": ["shawshank redemption"], "entity_types": ["Title"]}
{"sentence": "could you provide a synopsis for the movie the godfather?", "entity_names": ["the godfather"], "entity_types": ["Title"]}
{"sentence": "what's the highest rated movie of all time?", "entity_names": ["highest rated", "movie of all time"], "entity_types": ["Viewers' Rating", "Review"]}
{"sentence": "who directed the most recent superhero movie with great special effects?", "entity_names": ["superhero movie", "great special effects"], "entity_types": ["Genre", "Review"]}
{"sentence": "what is the plot of the latest romantic comedy directed by nancy meyers?", "entity_names": ["romantic comedy", "nancy meyers"], "entity_types": ["Genre", "Director"]}
{"sentence": "can you tell me about the storyline of the highest-rated drama starring meryl streep?", "entity_names": ["highest-rated", "drama", "meryl streep"], "entity_types": ["Viewers' Rating", "Genre", "Actor"]}
{"sentence": "i'm looking for a movie with a thrilling plot directed by kathryn bigelow. any suggestions?", "entity_names": ["thrilling plot", "kathryn bigelow"], "entity_types": ["Plot", "Director"]}
{"sentence": "i'm really curious about the new james bond movie, i wonder if there's a preview or teaser available?", "entity_names": ["james bond", "preview or teaser"], "entity_types": ["Character", "Trailer"]}
{"sentence": "i'm looking for a film with a really catchy theme song, any suggestions?", "entity_names": [], "entity_types": []}
{"sentence": "i heard there's a highly acclaimed director working on a new science fiction movie, do you know anything about it?", "entity_names": ["highly acclaimed director", "science fiction"], "entity_types": ["Director", "Genre"]}
{"sentence": "can you tell me the plot of the movie frozen?", "entity_names": ["plot", "frozen"], "entity_types": ["Genre", "Title"]}
{"sentence": "what children's movie from the 90s has a princess as the main character?", "entity_names": ["children's", "90s", "princess"], "entity_types": ["Genre", "Year", "Character"]}
{"sentence": "is there a family-friendly movie with a talking animal as the main character?", "entity_names": ["family-friendly", "talking animal"], "entity_types": ["Viewers' Rating", "Character"]}
{"sentence": "can you tell me the director of the movie moonlight?", "entity_names": ["moonlight"], "entity_types": ["Title"]}
{"sentence": "i'm looking for a film with emma stone and ryan gosling in it, can you recommend something?", "entity_names": ["emma stone", "ryan gosling"], "entity_types": ["Actor", "Actor"]}
{"sentence": "what genre of movies does taika waititi typically direct?", "entity_names": ["taika waititi"], "entity_types": ["Director"]}
{"sentence": "who directed the science fiction movie with a high viewers' rating", "entity_names": ["directed", "science fiction", "viewers' rating"], "entity_types": ["Director", "Genre", "Viewers' Rating"]}
{"sentence": "which movie features the song 'my heart will go on' and who are the main actors", "entity_names": ["my heart will go on", "main actors"], "entity_types": ["Song", "Actor"]}
{"sentence": "recommend a thriller film released in the 90s with an r rating and tell me about the plot", "entity_names": ["thriller", "90s", "r rating", "plot"], "entity_types": ["Genre", "Year", "MPAA Rating", "Plot"]}
{"sentence": "can i see the new spider-man film tonight at the nearest theater?", "entity_names": ["spider-man"], "entity_types": ["Title"]}
{"sentence": "can you provide me with the viewers' rating for the film fargo?", "entity_names": ["viewers' rating", "fargo"], "entity_types": ["Review", "Title"]}
{"sentence": "what year was the movie inception released, and who directed it?", "entity_names": ["inception", "directed"], "entity_types": ["Title", "Director"]}
{"sentence": "i'm interested in seeing a gripping thriller. could you recommend a movie directed by alfred hitchcock?", "entity_names": ["thriller", "alfred hitchcock"], "entity_types": ["Genre", "Director"]}
{"sentence": "has the movie the shawshank redemption won any awards?", "entity_names": ["the shawshank redemption", "awards"], "entity_types": ["Title", "Genre"]}
{"sentence": "which movie from 2019 received the most nominations?", "entity_names": ["2019", "most nominations"], "entity_types": ["Year", "Review"]}
{"sentence": "can you tell me if the film titanic received any accolades?", "entity_names": ["titanic", "accolades"], "entity_types": ["Title", "Review"]}
{"sentence": "can you recommend a romantic comedy film with a high viewers' rating?", "entity_names": ["romantic comedy", "high"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "i'm looking for a sci-fi movie that was released in the 1980s directed by james cameron", "entity_names": ["sci-fi", "1980s", "james cameron"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "show me a classic action movie with a lead role by arnold schwarzenegger and a song by queen", "entity_names": ["classic action", "arnold schwarzenegger", "queen"], "entity_types": ["Genre", "Actor", "Song"]}
{"sentence": "are there any interviews with the cast and crew of the film titanic that i can watch?", "entity_names": ["titanic"], "entity_types": ["Title"]}
{"sentence": "i'm not sure if i believe the story behind the film the social network, can you give me more information about it?", "entity_names": ["story", "the social network"], "entity_types": ["Plot", "Title"]}
{"sentence": "are there any good action movies playing today?", "entity_names": ["action movies"], "entity_types": ["Genre"]}
{"sentence": "can i trust the ratings for the new horror film?", "entity_names": ["ratings", "new horror film"], "entity_types": ["Review", "Genre"]}
{"sentence": "is the director of the romantic comedy film known for making successful movies?", "entity_names": ["director", "romantic comedy film"], "entity_types": ["Director", "Genre"]}
{"sentence": "can you recommend a horror film with a supernatural plot?", "entity_names": ["horror", "supernatural"], "entity_types": ["Genre", "Plot"]}
{"sentence": "who directed the latest science fiction movie set in space?", "entity_names": ["latest", "science fiction", "set in space"], "entity_types": ["Year", "Genre", "Plot"]}
{"sentence": "can you tell me the plot of the latest avengers movie?", "entity_names": ["plot", "avengers"], "entity_types": ["Plot", "Title"]}
{"sentence": "i'm in the mood for a heartwarming movie, can you recommend one with a great plot?", "entity_names": ["heartwarming", "great plot"], "entity_types": ["Genre", "Review"]}
{"sentence": "which movie directed by christopher nolan has the most intriguing plot?", "entity_names": ["christopher nolan", "intriguing plot"], "entity_types": ["Director", "Plot"]}
{"sentence": "can you tell me the name of the actor who played the main character in the movie inception?", "entity_names": ["actor", "main character", "inception"], "entity_types": ["Actor", "Character", "Title"]}
{"sentence": "i'm looking for a movie directed by quentin tarantino that has received a high viewers' rating.", "entity_names": ["quentin tarantino", "high viewers' rating"], "entity_types": ["Director", "Viewers' Rating"]}
{"sentence": "show me a thriller film from the 1990s with a soundtrack by hans zimmer.", "entity_names": ["thriller", "1990s", "hans zimmer"], "entity_types": ["Genre", "Year", "Song"]}
{"sentence": "what famous movies feature the song 'let it go'?", "entity_names": ["let it go"], "entity_types": ["Song"]}
{"sentence": "can you recommend a movie with an awesome soundtrack for a girls' night in?", "entity_names": ["awesome soundtrack", "girls' night"], "entity_types": ["Review", "Viewers' Rating"]}
{"sentence": "who directed the movie with the best 80s music soundtrack?", "entity_names": [], "entity_types": []}
{"sentence": "hey, can you tell me a movie with an awesome soundtrack?", "entity_names": ["awesome soundtrack"], "entity_types": ["Review"]}
{"sentence": "what are some movies with iconic songs in them?", "entity_names": ["iconic songs"], "entity_types": ["Song"]}
{"sentence": "do you know any films with killer background music?", "entity_names": ["killer background music"], "entity_types": ["Review"]}
{"sentence": "can you show me the trailer for the latest star wars movie?", "entity_names": ["trailer", "star wars"], "entity_types": ["Trailer", "Title"]}
{"sentence": "which movie directed by christopher nolan has the highest viewers' rating?", "entity_names": ["christopher nolan", "viewers' rating"], "entity_types": ["Director", "Viewers' Rating"]}
{"sentence": "what are some must-see movies with tom hanks as the lead actor?", "entity_names": ["must-see", "tom hanks"], "entity_types": ["Viewers' Rating", "Actor"]}
{"sentence": "who directed the science fiction film with time travel that came out in 2014?", "entity_names": ["science fiction", "time travel", "2014"], "entity_types": ["Genre", "Plot", "Year"]}
{"sentence": "show me a movie with a plot involving a heist and a high viewers' rating", "entity_names": ["heist", "high viewers' rating"], "entity_types": ["Plot", "Viewers' Rating"]}
{"sentence": "i need a movie that has a thrilling plot with a unique and unexpected twist, something in the suspense or psychological thriller genre.", "entity_names": ["thrilling plot", "suspense or psychological thriller"], "entity_types": ["Plot", "Genre"]}
{"sentence": "i'm feeling really tense right now and need to watch a light-hearted comedy from the 1980s with a feel-good love story.", "entity_names": ["light-hearted comedy", "1980s", "feel-good love story"], "entity_types": ["Genre", "Year", "Plot"]}
{"sentence": "can you tell me what the heck happens in the movie inception?", "entity_names": ["what the heck happens", "inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "i need to know the deal with the movie pulp fiction, can you give me a quick rundown?", "entity_names": ["the deal", "pulp fiction", "quick rundown"], "entity_types": ["Plot", "Title", "Review"]}
{"sentence": "hey, what's the scoop on the shawshank redemption? i want to know what it's all about.", "entity_names": ["the scoop", "the shawshank redemption", "what it's all about"], "entity_types": ["Plot", "Title", "Review"]}
{"sentence": "can you tell me about the awards and nominations received by the movie la la land?", "entity_names": ["awards and nominations", "la la land"], "entity_types": ["Review", "Title"]}
{"sentence": "which movie, directed by steven spielberg, has the most nominations for the academy awards?", "entity_names": ["steven spielberg", "most nominations"], "entity_types": ["Director", "Review"]}
{"sentence": "i'm curious about the golden globe awards won by the movie the shape of water. can you provide me with some information?", "entity_names": ["golden globe awards", "the shape of water"], "entity_types": ["Review", "Title"]}
{"sentence": "can you play the trailer for the movie inception?", "entity_names": ["trailer", "inception"], "entity_types": ["Trailer", "Title"]}
{"sentence": "what movie directed by christopher nolan has the most thrilling trailer?", "entity_names": ["christopher nolan", "thrilling", "trailer"], "entity_types": ["Director", "Review", "Trailer"]}
{"sentence": "show me a movie teaser with an intense action sequence.", "entity_names": ["teaser", "intense action sequence"], "entity_types": ["Trailer", "Plot"]}
{"sentence": "can you play the trailer for the latest marvel movie for me?", "entity_names": ["trailer", "marvel movie"], "entity_types": ["Trailer", "Title"]}
{"sentence": "what are the top-rated horror movies from the 2010s?", "entity_names": ["top-rated", "horror movies", "2010s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "who directed the action movie with tom cruise that came out last year?", "entity_names": ["directed", "action movie", "tom cruise", "last year"], "entity_types": ["Director", "Genre", "Actor", "Year"]}
{"sentence": "are there any tickets available for the new james bond movie?", "entity_names": ["james bond"], "entity_types": ["Title"]}
{"sentence": "can i really trust that the action movie has good reviews?", "entity_names": ["action", "good reviews"], "entity_types": ["Genre", "Review"]}
{"sentence": "is the theater showing the romantic comedy film this weekend?", "entity_names": ["romantic comedy"], "entity_types": ["Genre"]}
{"sentence": "i want to know the plot of the film the shawshank redemption.", "entity_names": ["the shawshank redemption"], "entity_types": ["Title"]}
{"sentence": "tell me about the storyline of the classic movie casablanca.", "entity_names": ["casablanca"], "entity_types": ["Title"]}
{"sentence": "can you hurry up and show me a trailer for the next james bond movie already?", "entity_names": ["trailer", "james bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "i need to see a teaser for the upcoming marvel movie right now!", "entity_names": ["teaser", "marvel"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "i don't have all day, show me the preview for the new action movie with tom cruise in it.", "entity_names": ["preview", "action", "tom cruise"], "entity_types": ["Trailer", "Genre", "Actor"]}
{"sentence": "can you tell me which director is known for creating intense psychological thrillers that keep you on the edge of your seat?", "entity_names": ["intense psychological thrillers", "edge of your seat"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "i'm interested in a movie that showcases the evolution of special effects in the sci-fi genre, can you recommend one?", "entity_names": ["special effects", "sci-fi"], "entity_types": ["Plot", "Genre"]}
{"sentence": "i'm curious to know which actor has worked on the most iconic romantic films throughout their career.", "entity_names": ["iconic romantic films", "career"], "entity_types": ["Genre", "Plot"]}
{"sentence": "can you recommend a movie with a romantic plot and a song by whitney houston?", "entity_names": ["romantic", "whitney houston"], "entity_types": ["Genre", "Song"]}
{"sentence": "what is the best-rated movie directed by nora ephron that was released in the 1990s?", "entity_names": ["best-rated", "nora ephron", "1990s"], "entity_types": ["Viewers' Rating", "Director", "Year"]}
{"sentence": "i'm looking for a classic movie with audrey hepburn in the lead role, can you suggest one?", "entity_names": ["classic", "audrey hepburn"], "entity_types": ["Genre", "Actor"]}
{"sentence": "hey, what kind of movies does quentin tarantino usually direct? i'm looking for something with lots of action and maybe a bit of dark humor.", "entity_names": ["quentin tarantino", "action", "dark humor"], "entity_types": ["Director", "Genre", "Genre"]}
{"sentence": "i'm in the mood for a romantic comedy, but i want something specific. do you know any good movies set in the 1940s, starring audrey hepburn?", "entity_names": ["romantic comedy", "1940s", "audrey hepburn"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "i need a recommendation for a scary movie, but nothing too intense. can you suggest something with a supernatural thriller vibe and maybe a strong female lead?", "entity_names": ["scary movie", "supernatural thriller", "strong female lead"], "entity_types": ["Genre", "Genre", "Character"]}
{"sentence": "what is the viewers' rating for the movie \"the shawshank redemption\"?", "entity_names": ["viewers' rating", "the shawshank redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "who directed the classic film \"casablanca\"?", "entity_names": ["directed", "classic", "casablanca"], "entity_types": ["Director", "Genre", "Title"]}
{"sentence": "can you tell me about the making-of documentary for the lord of the rings movies?", "entity_names": ["making-of documentary", "lord of the rings"], "entity_types": ["Plot", "Title"]}
{"sentence": "i'm looking for a movie that showcases the director's commentary and deleted scenes. any recommendations?", "entity_names": ["director's commentary", "deleted scenes"], "entity_types": ["Plot", "Plot"]}
{"sentence": "i want to watch a film with a featurette on the special effects. what do you suggest?", "entity_names": ["featurette", "special effects"], "entity_types": ["Plot", "Plot"]}
{"sentence": "can you tell me about the director and the behind-the-scenes secrets of the movie inception?", "entity_names": ["director", "behind-the-scenes secrets", "inception"], "entity_types": ["Director", "Plot", "Title"]}
{"sentence": "what are the best rated movies of 2020, and can you share any interesting making-of stories about them?", "entity_names": ["best rated movies", "2020", "making-of stories"], "entity_types": ["Viewers' Rating", "Year", "Plot"]}
{"sentence": "i'm in the mood for a classic film. could you recommend a highly acclaimed black and white movie from the 1950s and share some trivia about the director?", "entity_names": ["highly acclaimed", "black and white", "1950s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "can you provide me with the director of the making-of documentary for the movie inception?", "entity_names": ["making-of documentary", "inception"], "entity_types": ["Genre", "Title"]}
{"sentence": "i'm looking for a movie directed by christopher nolan with a complex and mind-bending storyline", "entity_names": ["christopher nolan", "storyline"], "entity_types": ["Director", "Plot"]}
{"sentence": "i want to watch a drama film from the 90s with a strong female lead and a compelling storyline", "entity_names": ["drama", "90s", "strong female lead", "compelling storyline"], "entity_types": ["Genre", "Year", "Character", "Plot"]}
{"sentence": "i need to know if the film 'titanic' won any awards or nominations", "entity_names": ["titanic", "awards or nominations"], "entity_types": ["Title", "Review"]}
{"sentence": "can you tell me if the movie 'the shawshank redemption' received any accolades?", "entity_names": ["the shawshank redemption", "accolades"], "entity_types": ["Title", "Review"]}
{"sentence": "i'm so curious to find out if 'the lord of the rings: the return of the king' won any oscars", "entity_names": ["the lord of the rings: the return of the king", "oscars"], "entity_types": ["Title", "Review"]}
{"sentence": "i'm not sure if the trailer for the new james bond film truly represents the storyline. can you provide a summary of the plot?", "entity_names": ["trailer", "new james bond film"], "entity_types": ["Trailer", "Title"]}
{"sentence": "can you recommend any comedy movies that are feel-good and family-friendly?", "entity_names": ["comedy", "feel-good", "family-friendly"], "entity_types": ["Genre", "Viewers' Rating", "Viewers' Rating"]}
{"sentence": "what are some classic action films from the 1980s that are considered iconic?", "entity_names": ["action", "1980s", "iconic"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "who directed the thriller movie with a post-apocalyptic plot that was released in 2015?", "entity_names": ["thriller", "post-apocalyptic", "2015"], "entity_types": ["Genre", "Plot", "Year"]}
{"sentence": "can you recommend a coming-of-age movie in the drama genre?", "entity_names": ["coming-of-age", "drama"], "entity_types": ["Genre", "Genre"]}
{"sentence": "what is the best sci-fi film from the 2010s with a strong female lead?", "entity_names": ["sci-fi", "2010s"], "entity_types": ["Genre", "Year"]}
{"sentence": "i'm looking for a feel-good romantic comedy set in a small town. do you have any recommendations?", "entity_names": ["romantic comedy", "small town"], "entity_types": ["Genre", "Plot"]}
{"sentence": "can you find me a movie featuring tom hanks and meg ryan together?", "entity_names": ["tom hanks", "meg ryan"], "entity_types": ["Actor", "Actor"]}
{"sentence": "show me a film where meryl streep played a historical character.", "entity_names": ["meryl streep", "historical character"], "entity_types": ["Actor", "Character"]}
{"sentence": "can you tell me if meryl streep starred in any romantic comedies?", "entity_names": ["meryl streep", "romantic comedies"], "entity_types": ["Actor", "Genre"]}
{"sentence": "show me the trailer for the latest tom hanks film", "entity_names": ["trailer", "tom hanks"], "entity_types": ["Trailer", "Actor"]}
{"sentence": "are there any showtimes for the new james bond film, no time to die, tonight?", "entity_names": ["no time to die"], "entity_types": ["Title"]}
{"sentence": "i'm not sure if i can trust the ratings, but can you tell me the viewers' rating for the movie dune?", "entity_names": ["dune"], "entity_types": ["Title"]}
{"sentence": "i'm not convinced that the genre matches my preference, but could you recommend a comedy film directed by quentin tarantino?", "entity_names": ["comedy", "quentin tarantino"], "entity_types": ["Genre", "Director"]}
{"sentence": "i'm looking for a movie with a mysterious and twisty plot. any suggestions?", "entity_names": ["mysterious", "twisty"], "entity_types": ["Genre", "Plot"]}
{"sentence": "who directed the movie the shawshank redemption, and what's the plot?", "entity_names": ["directed", "the shawshank redemption", "plot"], "entity_types": ["Director", "Title", "Plot"]}
{"sentence": "i'm so bored, is there a movie with the song 'my heart will go on' available on any streaming platform?", "entity_names": ["my heart will go on"], "entity_types": ["Song"]}
{"sentence": "i need something to watch, is there a comedy movie from the 1990s available to stream?", "entity_names": ["comedy", "1990s"], "entity_types": ["Genre", "Year"]}
{"sentence": "i'm bored out of my mind, can i find any action movies directed by james cameron on any streaming platform?", "entity_names": ["action", "james cameron"], "entity_types": ["Genre", "Director"]}
{"sentence": "can you recommend a suspenseful thriller from the 90s with a high viewers' rating?", "entity_names": ["suspenseful thriller", "90s", "high"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "who directed the action-packed blockbuster released in 2018 with a thrilling plot?", "entity_names": ["action-packed blockbuster", "2018", "thrilling"], "entity_types": ["Genre", "Year", "Plot"]}
{"sentence": "show me a romantic movie from the 2000s with an iconic song.", "entity_names": ["romantic", "2000s", "iconic"], "entity_types": ["Genre", "Year", "Song"]}
{"sentence": "hey, what's showing at the theater tonight with tom hanks as the main character?", "entity_names": ["tom hanks"], "entity_types": ["Actor"]}
{"sentence": "can you recommend a good action movie playing right now?", "entity_names": ["action"], "entity_types": ["Genre"]}
{"sentence": "can you tell me the name of the movie that has a behind-the-scenes documentary about its special effects?", "entity_names": ["behind-the-scenes documentary", "special effects"], "entity_types": ["Plot", "Plot"]}
{"sentence": "i'm looking for a movie from the 90s with a director's commentary featuring behind-the-scenes stories.", "entity_names": ["90s", "director's commentary", "behind-the-scenes stories"], "entity_types": ["Year", "Plot", "Plot"]}
{"sentence": "are there any romantic comedies with a making-of featurette about the chemistry between the lead actors?", "entity_names": ["romantic comedies", "making-of featurette", "chemistry between the lead actors"], "entity_types": ["Genre", "Plot", "Plot"]}
{"sentence": "can you recommend a movie with a memorable soundtrack featuring the song 'eye of the tiger'?", "entity_names": ["eye of the tiger"], "entity_types": ["Song"]}
{"sentence": "show me a movie with an iconic theme song similar to 'the imperial march' from star wars", "entity_names": ["the imperial march", "star wars"], "entity_types": ["Song", "Title"]}
{"sentence": "which movie has a famous theme song like 'my heart will go on' by celine dion from titanic?", "entity_names": ["'my heart will go on' by celine dion", "titanic"], "entity_types": ["Song", "Title"]}
{"sentence": "\"can you recommend a movie with a memorable soundtrack featuring the song \"eye of the tiger\"?", "entity_names": ["eye of the tiger"], "entity_types": ["Song"]}
{"sentence": "show me a list of movies from the 80s with iconic song soundtracks.", "entity_names": ["80s", "iconic song soundtracks"], "entity_types": ["Year", "Song"]}
{"sentence": "are there any must-see movies known for their famous musical scores or theme songs?", "entity_names": ["must-see"], "entity_types": ["Viewers' Rating"]}
{"sentence": "can you show me a teaser for that new action movie?", "entity_names": ["teaser", "action"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "what's the latest movie that everyone's talking about?", "entity_names": [], "entity_types": []}
{"sentence": "tell me about a classic film with a great soundtrack.", "entity_names": ["classic"], "entity_types": ["Viewers' Rating"]}
{"sentence": "can you recommend a highly rated comedy from the 1990s?", "entity_names": ["highly rated", "comedy", "1990s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "who directed the action film with a great soundtrack that came out in 2015?", "entity_names": ["action film", "great soundtrack", "2015"], "entity_types": ["Genre", "Song", "Year"]}
{"sentence": "i heard there's a new thriller movie out. what's the plot and who stars in it?", "entity_names": ["thriller", "plot", "stars"], "entity_types": ["Genre", "Plot", "Actor"]}
{"sentence": "show me a film starring leonardo dicaprio and tom hanks.", "entity_names": ["leonardo dicaprio", "tom hanks"], "entity_types": ["Actor", "Actor"]}
{"sentence": "what is the viewers' rating for the film inception?", "entity_names": ["viewers' rating", "inception"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "what is the viewers' rating for the movie inception? i also want to know the name of the song played in the movie. hurry up, please!", "entity_names": ["viewers' rating", "inception", "song"], "entity_types": ["Viewers' Rating", "Title", "Song"]}
{"sentence": "i'm running late for a movie trivia contest! can you tell me the release year and the plot of the film titanic? and who was the main actor? i need this information now!", "entity_names": ["release year", "plot", "titanic", "main actor"], "entity_types": ["Year", "Plot", "Title", "Actor"]}
{"sentence": "i'm looking for showtimes and tickets to a 90s action movie directed by james cameron", "entity_names": ["90s", "action", "james cameron"], "entity_types": ["Year", "Genre", "Director"]}
{"sentence": "i want to see a critically acclaimed romantic comedy from the 2000s with hugh grant in it, can you help me find showtimes?", "entity_names": ["romantic comedy", "2000s", "hugh grant"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "can you recommend a movie from the 80s with an iconic soundtrack?", "entity_names": ["80s", "iconic soundtrack"], "entity_types": ["Year", "Song"]}
{"sentence": "i'm feeling nostalgic for some classic musicals. can you suggest a movie with a standout song and dance number?", "entity_names": ["nostalgic", "classic musicals", "standout song and dance number"], "entity_types": ["Viewers' Rating", "Genre", "Song"]}
{"sentence": "i miss the old romantic comedies. can you tell me a movie with a memorable love song in its soundtrack?", "entity_names": ["old romantic comedies", "memorable love song"], "entity_types": ["Genre", "Song"]}
{"sentence": "can i find showtimes for the latest romantic comedy with hugh grant?", "entity_names": ["romantic comedy", "hugh grant"], "entity_types": ["Genre", "Actor"]}
{"sentence": "is there a theater near me showing a drama film based on a true story?", "entity_names": ["drama", "true story"], "entity_types": ["Genre", "Plot"]}
{"sentence": "i'm looking for a family-friendly movie directed by steven spielberg, is there one playing this weekend?", "entity_names": ["family-friendly", "steven spielberg"], "entity_types": ["Viewers' Rating", "Director"]}
{"sentence": "can you recommend a film starring tom hanks", "entity_names": ["tom hanks"], "entity_types": ["Actor"]}
{"sentence": "what is the viewers' rating for the shawshank redemption", "entity_names": ["viewers' rating", "the shawshank redemption"], "entity_types": ["Viewers' Rating", "Title"]}
