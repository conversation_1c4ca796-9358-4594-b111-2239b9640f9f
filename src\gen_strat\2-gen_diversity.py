# -*- coding: utf-8 -*-
import json
import os
import re
import requests
import time
from datetime import datetime
from tqdm import tqdm
from typing import Dict, List, Set, Tuple, Optional
from pathlib import Path

# 日志相关全局变量
log_main_file = None
log_detail_file = None
log_timestamp = None
NUM_ENTITY_VARIANTS = 10  # 默认值，会在main函数中被覆盖

def setup_logging(output_dir: Path, timestamp: str, target_count: int):
    """设置日志文件"""
    global log_main_file, log_detail_file, log_timestamp
    log_timestamp = timestamp
    
    # 清理同目录下的旧日志文件 - 匹配所有可能的日志文件模式
    log_patterns = [
        "entity_generation_log_*.txt",
        "entity_generation_details_*.txt", 
        "entity_generation_*.txt"
    ]
    
    for pattern in log_patterns:
        for log_file in output_dir.glob(pattern):
            try:
                log_file.unlink()
                print(f"[信息] 清理旧日志文件: {log_file}")
            except Exception as e:
                print(f"[警告] 清理日志文件失败 {log_file}: {e}")
    
    # 创建日志文件路径 - 保存在ENTITY_OUTPUT_DIR目录下
    log_main_file = output_dir / f"entity_generation_log_{timestamp}.txt"
    log_detail_file = output_dir / f"entity_generation_details_{timestamp}.txt"
    
    # 写入日志头
    with open(log_main_file, 'w', encoding='utf-8') as f:
        f.write(f"=== 实体生成日志 ===\n")
        f.write(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"目标数量: {target_count}\n\n")
    
    with open(log_detail_file, 'w', encoding='utf-8') as f:
        f.write(f"=== 实体生成详细日志 ===\n")
        f.write(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

def log_main(message: str, print_to_console: bool = False):
    """写入主日志"""
    global log_main_file
    if log_main_file:
        with open(log_main_file, 'a', encoding='utf-8') as f:
            f.write(f"{message}\n")
    if print_to_console:
        print(message)

def log_detail(message: str, print_to_console: bool = False):
    """写入详细日志"""
    global log_detail_file
    if log_detail_file:
        with open(log_detail_file, 'a', encoding='utf-8') as f:
            f.write(f"{message}\n")
    if print_to_console:
        print(message)

def log_generation_step(entity_type: str, step_type: str, api_count: int, filtered_count: int, final_count: int, details: List[str] = None, filtered_entities: List[Tuple[str, str]] = None):
    """记录生成步骤"""
    step_name = "Vanilla" if step_type == "vanilla" else f"Latent[{step_type}]"
    log_main(f"[{entity_type}] {step_name} 第1轮生成:")
    log_main(f"- API返回: {api_count}个")
    log_main(f"- 过滤后: {filtered_count}个")
    log_main(f"- 去重后: {final_count}个")
    
    if details:
        log_detail(f"\n=== {entity_type} ===")
        log_detail(f"{step_name} 第1轮生成:")
        log_detail(f"生成内容: {details}")
        log_detail(f"保留: {filtered_count}个")
        log_detail(f"过滤: {api_count - filtered_count}个")
        
        if filtered_entities:
            log_detail("过滤详情:")
            for entity, reason in filtered_entities:
                log_detail(f"  - \"{entity}\" ({reason})")

def log_filter_reason(entity: str, reason: str):
    """记录过滤原因"""
    log_detail(f"过滤: [\"{entity}\"]({reason})")

def log_final_result(entity_type: str, target: int, actual: int, success: bool):
    """记录最终结果"""
    status = "达标" if success else "未达标"
    log_main(f"最终结果: {status} ({actual}/{target})")
    log_main("")  # 空行分隔

def main(output_dir="reproduce", use_global_cache=True, timestamp=None):
    """主函数：生成多样化策略
    
    Args:
        output_dir: 输出目录
        use_global_cache: 是否使用全局缓存（默认True）
        timestamp: 外部传入的时间戳，如果不提供则创建新的
    """
    # =====================
    # 配置和路径
    # =====================
    
    # 读取API配置
    with open('src/gen_strat/diversity_config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)

    API_KEY_PATH = config["api_key_path"]
    with open(API_KEY_PATH, 'r', encoding='utf-8') as f:
        api_key = json.load(f)['api_key']

    # 读取实体-语境适配表
    ENTITY_CONTEXT_MAPPING = config.get("entity_context_mapping", {})
    
    # 配置参数
    NUM_SENTENCE_VARIANTS = config.get("num_sentence_variants", 5)
    NUM_ENTITY_VARIANTS = config.get("num_entity_variants", 10)
    SKIP_ENTITY_TYPES = config.get("skip_entity_types", [])

    # API参数
    ZHIPU_API_URL = "https://open.bigmodel.cn/api/paas/v4/chat/completions"
    MODEL_NAME = "glm-4-air-250414"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 路径设置
    output_dir = Path(output_dir)
    if timestamp is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        print(f"[信息] 生成新的时间戳: {timestamp}")
    else:
        print(f"[信息] 使用外部传入的时间戳: {timestamp}")
    
    # 全局缓存目录（项目根目录下的reproduce）
    global_cache_dir = Path("reproduce")
    
    # 运行特定输出目录
    run_output_dir = output_dir
    
    # 句子多样化：优先使用全局缓存，同时复制到运行目录
    if use_global_cache:
        SEN_CACHE_DIR = global_cache_dir / "sen_diversity"
        SEN_OUTPUT_DIR = run_output_dir / "sen_diversity"
    else:
        SEN_CACHE_DIR = run_output_dir / "sen_diversity"
        SEN_OUTPUT_DIR = run_output_dir / "sen_diversity"
    
    # 实体多样化：优先使用全局缓存，同时复制到运行目录
    if use_global_cache:
        ENTITY_CACHE_DIR = global_cache_dir / "entity_diversity"
        ENTITY_OUTPUT_DIR = run_output_dir / "entity_diversity" / f"entity_diversity_{timestamp}"
    else:
        ENTITY_CACHE_DIR = run_output_dir / "entity_diversity"
        ENTITY_OUTPUT_DIR = run_output_dir / "entity_diversity" / f"entity_diversity_{timestamp}"
    
    # 创建目录
    SEN_CACHE_DIR.mkdir(parents=True, exist_ok=True)
    SEN_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
    ENTITY_CACHE_DIR.mkdir(parents=True, exist_ok=True)
    ENTITY_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
    
    # 设置日志 - 在ENTITY_OUTPUT_DIR创建后设置
    setup_logging(ENTITY_OUTPUT_DIR, timestamp, NUM_ENTITY_VARIANTS)
    
    # 文件路径
    FILE_SEN_ATTRIBUTE_CACHE = SEN_CACHE_DIR / 'sen_diversify_attribute.json'
    FILE_SEN_VALUE_CACHE = SEN_CACHE_DIR / 'sen_diversify_value.json'
    FILE_SEN_ATTRIBUTE_OUTPUT = SEN_OUTPUT_DIR / 'sen_diversify_attribute.json'
    FILE_SEN_VALUE_OUTPUT = SEN_OUTPUT_DIR / 'sen_diversify_value.json'
    
    ENTITY_SCHEMA_PATH = 'src/gen_strat/entity_schema.json'
    
    print(f"[信息] 使用全局缓存: {use_global_cache}")
    print(f"[信息] 全局缓存目录: {global_cache_dir}")
    print(f"[信息] 运行输出目录: {run_output_dir}")
    
    # 打印跳过配置
    if SKIP_ENTITY_TYPES:
        print(f"[信息] 配置跳过以下实体类型的生成：{SKIP_ENTITY_TYPES}")
    
    # 定义latent场景类型
    LATENT_SCENARIOS = {
        "医学描述": "医学、医疗、健康相关的专业术语和表达",
        "法律文本": "法律、合同、正式文档中的规范表达",
        "口语表达": "日常对话、非正式场合的通俗表达",
        "学术论文": "学术研究、论文中的专业表达",
        "新闻报道": "新闻媒体、报道中的标准表达",
        "社交场合": "社交、聊天中的常用表达",
        "商务场合": "商务、职场中的正式表达",
        "网络用语": "网络、社交媒体中的流行表达"
    }
    
    # 读取实体schema
    with open(ENTITY_SCHEMA_PATH, 'r', encoding='utf-8') as f:
        schema_json = json.load(f)
        flat_list = schema_json.get("flat_list", [])
        example_map = schema_json.get("example_map", {})

    # 定义小值域实体的基础值（仅对schema中声明的类型）
    SMALL_VALUE_DOMAIN_ENTITIES = {
        "性别": ["男", "女", "男性", "女性", "非二元", "跨性别", "双性", "中性", "雌雄同体"],
        "婚姻状况": ["已婚", "未婚", "离异", "丧偶", "单身", "再婚", "分居", "未说明", "同居", "未登记", "订婚", "未婚同居", "婚姻存续中", "婚姻解除"],
        "国籍": ["中国", "美国", "法国", "日本", "韩国", "英国", "德国", "加拿大", "澳大利亚", "俄罗斯", "印度", "巴西", "意大利", "西班牙", "荷兰", "瑞士", "瑞典", "挪威", "丹麦", "芬兰", "比利时", "奥地利", "葡萄牙", "希腊", "波兰", "捷克", "匈牙利", "罗马尼亚", "保加利亚", "克罗地亚", "斯洛文尼亚", "斯洛伐克", "爱沙尼亚", "拉脱维亚", "立陶宛", "新加坡", "马来西亚", "泰国", "越南", "菲律宾", "印度尼西亚", "蒙古", "朝鲜", "伊朗", "以色列", "埃及", "南非", "墨西哥", "阿根廷", "智利", "新西兰", "土耳其", "沙特阿拉伯", "阿联酋", "卡塔尔", "科威特", "巴林", "也门", "约旦", "黎巴嫩", "叙利亚", "巴勒斯坦", "乌克兰", "白俄罗斯", "格鲁吉亚", "亚美尼亚", "哈萨克斯坦", "乌兹别克斯坦", "吉尔吉斯斯坦", "塔吉克斯坦", "土库曼斯坦", "阿塞拜疆", "摩洛哥", "突尼斯", "阿尔及利亚", "苏丹", "尼日利亚", "加纳", "肯尼亚", "坦桑尼亚", "乌干达", "卢旺达", "布隆迪", "塞内加尔", "安哥拉", "津巴布韦", "赞比亚", "莫桑比克", "马达加斯加", "毛里求斯", "塞舌尔", "古巴", "牙买加", "巴哈马", "多米尼加", "哥伦比亚", "委内瑞拉", "秘鲁", "厄瓜多尔", "玻利维亚", "巴拉圭", "乌拉圭"],
        "民族": ["汉族", "满族", "蒙古族", "回族", "藏族", "维吾尔族", "苗族", "彝族", "壮族", "布依族", "侗族", "瑶族", "白族", "土家族", "哈尼族", "哈萨克族", "傣族", "黎族", "傈僳族", "佤族", "畲族", "高山族", "拉祜族", "水族", "东乡族", "纳西族", "景颇族", "柯尔克孜族", "土族", "达斡尔族", "仫佬族", "羌族", "布朗族", "撒拉族", "毛南族", "仡佬族", "锡伯族", "阿昌族", "普米族", "朝鲜族", "塔吉克族", "怒族", "乌孜别克族", "俄罗斯族", "鄂温克族", "德昂族", "保安族", "裕固族", "京族", "塔塔尔族", "独龙族", "鄂伦春族", "赫哲族", "门巴族", "珞巴族", "基诺族"],
        "政治倾向": ["保守派", "自由派", "中立", "激进派", "温和派", "改革派", "保守主义", "自由主义", "社会主义", "民族主义", "无党派", "环保主义", "女权主义", "进步派", "传统派", "技术官僚", "民粹主义", "中间派", "极左", "极右"],
        "教育背景": ["小学毕业", "初中毕业", "高中毕业", "中专", "大专", "本科", "本科毕业", "硕士", "硕士研究生", "博士", "博士后", "MBA", "EMBA", "技校", "自学", "成人教育", "网络教育", "海外留学"],
        "家庭成员": ["父亲", "母亲", "配偶", "妻子", "丈夫", "儿子", "女儿", "兄弟", "姐妹", "祖父", "祖母", "外祖父", "外祖母", "孙子", "孙女", "外孙", "外孙女", "叔叔", "婶婶", "舅舅", "舅妈", "姑姑", "姑父", "姨", "姨夫", "侄子", "侄女", "表哥", "表姐", "表弟", "表妹", "堂兄", "堂姐", "堂弟", "堂妹", "继父", "继母", "继子", "继女", "养父", "养母", "养子", "养女"]
        }
    
    # =====================
    # API调用函数
    # =====================

    def call_zhipu_api(prompt, max_retries=3, base_wait=5):
        """调用智谱API"""
        # print("\n[API PROMPT] 本次API调用的prompt如下:\n" + prompt + "\n")
        data = {
            "model": MODEL_NAME,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 2000
        }

        for attempt in range(max_retries):
            try:
                response = requests.post(ZHIPU_API_URL, headers=headers, json=data, timeout=30)
                response.raise_for_status()
                result = response.json()
                return result['choices'][0]['message']['content']
            except Exception as e:
                if attempt < max_retries - 1:
                    wait_time = base_wait * (2 ** attempt)
                    print(f"API调用失败，{wait_time}秒后重试: {e}")
                    time.sleep(wait_time)
                else:
                    raise RuntimeError(f"API调用失败: {e}")
            
    def extract_list_items(text):
        """从文本中提取列表项"""
        lines = [line.strip() for line in text.strip().split('\n') if line.strip()]
        items = []
        for line in lines:
            # 只去除真正的编号格式（如 "1.", "2.", "- " 等），保留年份
            clean_line = re.sub(r'^[\d]+[\.\-\•\s]+', '', line.strip())
            if clean_line:
                items.append(clean_line)
        return items
        
    def clean_entity_output(text):
        """清理实体输出，去除无关描述"""
        # 定义需要移除的模式
        patterns_to_remove = [
            r'以下.*?：',
            r'以下.*?：',
            r'请注意.*',
            r'请确保.*',
            r'请遵守.*',
            r'请保密.*',
            r'请安全.*',
            r'避免泄露.*'
        ]
        
        cleaned_text = text
        for pattern in patterns_to_remove:
            cleaned_text = re.sub(pattern, '', cleaned_text, flags=re.IGNORECASE | re.DOTALL)
        
        # 提取实体列表
        entities = extract_list_items(cleaned_text)
        
        # 如果没有通过正则提取到实体，尝试按行分割
        if not entities:
            lines = [line.strip() for line in cleaned_text.strip().split('\n') if line.strip()]
            entities = []
            for line in lines:
                # 去除行首的编号和符号
                clean_line = re.sub(r'^[\d]+[\.\-\•\s]+', '', line.strip())
                if clean_line:
                    entities.append(clean_line)
        
        # 过滤掉太短或太长的实体（可能是描述而不是实体）
        filtered_entities = []
        for entity in entities:
            entity = entity.strip()
            # 过滤掉太短（少于1个字符）或太长（超过100个字符）的实体
            if 1 <= len(entity) <= 100:
                # 过滤掉包含常见描述性词汇的实体
                if not any(word in entity for word in ['请注意', '这些', '实际', '遵守', '确保', '保密', '安全']):
                    filtered_entities.append(entity)
        
        return filtered_entities
        
    def check_existing_file(filename, cache_dir=None):
        """检查是否存在指定的文件且内容不为空"""
        if cache_dir is None:
            cache_dir = SEN_CACHE_DIR
        
        filepath = cache_dir / filename
        if filepath.exists():
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if data:  # 检查内容不为空
                        return data
            except Exception:
                pass
        return None
        
    def copy_to_output_dir(src_file, dst_file):
        """复制文件到输出目录，若源和目标相同则跳过"""
        try:
            import shutil
            import os
            # 确保目标目录存在
            dst_file.parent.mkdir(parents=True, exist_ok=True)
            # 判断是否为同一个文件
            if os.path.abspath(str(src_file)) == os.path.abspath(str(dst_file)):
                print(f"[信息] 源文件和目标文件相同，跳过复制: {src_file}")
                return
            shutil.copy2(src_file, dst_file)
            print(f"[✓] 已复制到运行目录: {dst_file}")
        except Exception as e:
            print(f"[错误] 复制文件失败: {e}")
            print(f"  源文件: {src_file}")
            print(f"  目标文件: {dst_file}")
            raise  # 抛出异常，让调用者知道复制失败

    # === Step 1: 生成句子多样化的属性 ===
    print("=== Step 1: 句子多样化的属性 ===")
    
    # 优先从全局缓存检查
    existing_attributes = check_existing_file('sen_diversify_attribute.json', SEN_CACHE_DIR)
    
    if existing_attributes:
        print(f"[✓] 找到全局缓存的句子多样化属性文件，复用已有数据")
        sen_attributes = existing_attributes.get('sen_diversify_attribute', [])
        
        # 复制到运行目录
        copy_to_output_dir(FILE_SEN_ATTRIBUTE_CACHE, FILE_SEN_ATTRIBUTE_OUTPUT)
        
    else:
        print("[✓] 生成句子多样化的属性")
        try:
            prompt = "你认为生成包含敏感实体的句子要关注哪些重要属性？请简要列出3-5项。"
            answer = call_zhipu_api(prompt)
            sen_attributes = extract_list_items(answer)
        except Exception as e:
            print(f"[警告] API调用失败，使用默认属性: {e}")
            sen_attributes = ["隐私保护", "数据安全", "敏感信息", "个人资料"]
        
        # 保存到全局缓存
        result = {"sen_diversify_attribute": sen_attributes}
        if use_global_cache:
            with open(FILE_SEN_ATTRIBUTE_CACHE, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"[✓] 句子多样化属性已保存至全局缓存: {FILE_SEN_ATTRIBUTE_CACHE}")
        
        # 保存到运行目录
        with open(FILE_SEN_ATTRIBUTE_OUTPUT, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"[✓] 句子多样化属性已保存至运行目录: {FILE_SEN_ATTRIBUTE_OUTPUT}")
    
    # === Step 2: 生成句子多样化的属性对应的值 ===
    print("\n=== Step 2: 句子多样化的属性对应的值 ===")
    
    # 优先从全局缓存检查
    existing_values = check_existing_file('sen_diversify_value.json', SEN_CACHE_DIR)
    
    if existing_values:
        print(f"[✓] 找到全局缓存的句子多样化属性值文件，复用已有数据")
        sen_values = existing_values.get('sen_diversity_value', {})
        
        # 复制到运行目录
        copy_to_output_dir(FILE_SEN_VALUE_CACHE, FILE_SEN_VALUE_OUTPUT)
        
    else:
        print(f"[✓] 针对 {len(sen_attributes)} 个属性生成对应的值")
        sen_values = {}
        
        for sen_attr in tqdm(sen_attributes, desc="属性值生成中"):
            attr_clean = sen_attr.strip("：:").strip()
            try:
                prompt = f"假设你是一位数据隐私专家。请列出 {NUM_SENTENCE_VARIANTS} 种和敏感信息有关的不同'{attr_clean}'。"
                raw = call_zhipu_api(prompt)
                items = extract_list_items(raw)
                sen_values[attr_clean] = items
            except Exception as e:
                print(f"[跳过] 属性 {attr_clean} 处理失败：{e}")
                sen_values[attr_clean] = [f"{attr_clean}示例1", f"{attr_clean}示例2", f"{attr_clean}示例3"]

        # 保存到全局缓存
        result = {"sen_diversity_value": sen_values}
        if use_global_cache:
            with open(FILE_SEN_VALUE_CACHE, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"[✓] 句子多样化属性值已保存至全局缓存: {FILE_SEN_VALUE_CACHE}")
        
        # 保存到运行目录
        with open(FILE_SEN_VALUE_OUTPUT, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"[✓] 句子多样化属性值已保存至运行目录: {FILE_SEN_VALUE_OUTPUT}")
    
    # === Step 3: 实体多样化生成-vanilla/latent ===
    print("\n=== Step 3: 实体多样化生成-vanilla/latent ===")
    print(f"[✓] 实体类型共 {len(flat_list)} 类，开始逐类生成…")
    print(f"[✓] latent模式将根据 {len(LATENT_SCENARIOS)} 个不同场景生成：{list(LATENT_SCENARIOS.keys())}")

    # 优先从全局缓存加载实体多样化数据（带版本号）
    existing_entity_data, loaded_version = load_existing_entity_diversity(use_global_cache, ENTITY_CACHE_DIR, run_output_dir)
    if existing_entity_data:
        print(f"[✓] 找到全局缓存的实体多样化数据（版本：{loaded_version}），将以最新文件为基准")
    else:
        print(f"[信息] 未找到全局缓存的实体多样化数据，将全部新生成")
        existing_entity_data = {}
        loaded_version = 0

    # 存储所有实体多样化数据
    entity_diversity_data = {}
    has_new_generation = False  # 标记是否有新生成的数据

    for entity_type in tqdm(flat_list, desc="处理实体类型"):
        if entity_type in SKIP_ENTITY_TYPES:
            print(f"[跳过] {entity_type} - 直接复用已有数据")
            entity_data = existing_entity_data.get(entity_type, {"vanilla": [], "latent": {}})
            entity_diversity_data[entity_type] = entity_data
            continue
        
        entity_data = existing_entity_data.get(entity_type, {"vanilla": [], "latent": {}})
        entity_data["vanilla"] = [e for e in entity_data.get("vanilla", []) if is_concrete_entity(e, entity_type)]
        adapted_scenarios = ENTITY_CONTEXT_MAPPING.get(entity_type, list(LATENT_SCENARIOS.keys()))
        latent = entity_data.get("latent", {})
        filtered_latent = {}
        for scenario in adapted_scenarios:
            filtered_latent[scenario] = [e for e in latent.get(scenario, []) if is_concrete_entity(e, entity_type)]
        if "_missing_contexts" in latent:
            filtered_latent["_missing_contexts"] = latent["_missing_contexts"]
        entity_data["latent"] = filtered_latent
        
        # 统计已收集数量
        all_entities = get_all_entities(entity_data, adapted_scenarios)
        collected = len(all_entities)
        gap = NUM_ENTITY_VARIANTS - collected
        
        if gap <= 0:
            print(f"[✓] {entity_type} 已满足目标数量({collected}/{NUM_ENTITY_VARIANTS})，跳过生成")
            entity_diversity_data[entity_type] = entity_data
            continue
            
        # 生成逻辑：根据配置决定是否使用占位符兜底
        print(f"[信息] {entity_type} 需要补充 {gap} 个实体变体")
        if config.get("allow_placeholder_generation", False):
            # 占位符模式（仅在明确允许时启用）
            vanilla_entities = []
            for i in range(min(gap // 2, 10)):
                vanilla_entities.append(f"{entity_type}_示例_{i+1}")
            latent_entities = {}
            for scenario in ["医学描述", "新闻报道", "商务场合"]:
                scenario_entities = []
                for i in range(min(gap // 6, 5)):
                    scenario_entities.append(f"[{scenario}]{entity_type}_示例_{i+1}")
                latent_entities[scenario] = scenario_entities
            entity_data["vanilla"] = vanilla_entities
            entity_data["latent"] = latent_entities
            entity_diversity_data[entity_type] = entity_data
            has_new_generation = True
        else:
            # 正常流程：调用现有的池扩充逻辑（含API生成、过滤、去重与兜底）
            entity_data = expand_entity_pool(
                entity_type=entity_type,
                entity_data=entity_data,
                target_count=NUM_ENTITY_VARIANTS,
                current_count=collected,
                example_map=example_map
            )
            entity_diversity_data[entity_type] = entity_data
            has_new_generation = True

    # 保存实体多样化数据
    entity_diversity_file = ENTITY_OUTPUT_DIR / 'entity_diversity.json'
    with open(entity_diversity_file, 'w', encoding='utf-8') as f:
        json.dump(entity_diversity_data, f, ensure_ascii=False, indent=2)
    print(f"[✓] 实体多样化数据已保存至运行目录：{entity_diversity_file}")
    
    # 如果有新生成的数据且使用全局缓存，则更新全局缓存
    if has_new_generation and use_global_cache:
        try:
            # 创建新的缓存目录
            global_entity_diversity_dir = ENTITY_CACHE_DIR / f"entity_diversity_{timestamp}"
            global_entity_diversity_dir.mkdir(parents=True, exist_ok=True)
            global_entity_diversity_file = global_entity_diversity_dir / 'entity_diversity.json'
            
            # 保存数据
            with open(global_entity_diversity_file, 'w', encoding='utf-8') as f:
                json.dump(entity_diversity_data, f, ensure_ascii=False, indent=2)
                
            # 更新版本号
            update_cache_version(ENTITY_CACHE_DIR)
            
            print(f"[✓] 实体多样化数据已更新至全局缓存：{global_entity_diversity_file}")
            
        except Exception as e:
            print(f"[错误] 更新全局缓存失败: {e}")
    
    print(f"[✓] 所有多样化策略处理完成")
    print(f"[✓] 运行目录：{run_output_dir}")
    if use_global_cache:
        print(f"[✓] 全局缓存目录：{global_cache_dir}")

def normalize_entity(entity: str) -> str:
    """规范化实体字符串，用于去重比较
    
    处理:
    1. 去除首尾空格
    2. 统一空格（多个空格变单个）
    3. 统一标点（全角转半角等）
    4. 移除不必要的符号
    """
    import unicodedata
    
    # 转为NFKC标准形式（合并兼容字符）
    text = unicodedata.normalize('NFKC', entity)
    
    # 去除首尾空格，合并中间空格
    text = ' '.join(text.split())
    
    # 移除不必要的符号（可根据需要调整）
    chars_to_remove = ',.;:!?~`"\''
    for c in chars_to_remove:
        text = text.replace(c, '')
    
    return text

def deduplicate_entities(entities: List[str]) -> List[str]:
    """去重实体列表，保持原顺序"""
    seen = set()
    result = []
    for entity in entities:
        normalized = normalize_entity(entity)
        if normalized not in seen:
            seen.add(normalized)
            result.append(entity)  # 保留原始形式
    return result

def deduplicate_entity_data(entity_data: Dict) -> Dict:
    """完整去重实体数据"""
    for entity_type, data in entity_data.items():
        # 去重 vanilla
        if "vanilla" in data:
            data["vanilla"] = deduplicate_entities(data["vanilla"])
        
        # 去重 latent
        if "latent" in data:
            for scenario in data["latent"]:
                if scenario != "_missing_contexts":
                    data["latent"][scenario] = deduplicate_entities(data["latent"][scenario])
    
    return entity_data

def get_cache_version(cache_dir: Path) -> int:
    """获取缓存版本号（使用最新目录的时间戳）"""
    version_file = cache_dir / "version.txt"
    if version_file.exists():
        with open(version_file, 'r') as f:
            return int(f.read().strip())
    return 0

def update_cache_version(cache_dir: Path) -> int:
    """更新缓存版本号"""
    version_file = cache_dir / "version.txt"
    new_version = int(datetime.now().timestamp())
    with open(version_file, 'w') as f:
        f.write(str(new_version))
    return new_version

def merge_entity_diversity_data(old_data: Dict, new_data: Dict) -> Dict:
    """合并两份实体多样化数据，使用规范化去重"""
    merged = {}
    
    # 处理所有实体类型
    all_types = set(old_data.keys()) | set(new_data.keys())
    for entity_type in all_types:
        merged[entity_type] = {
            "vanilla": [],
            "latent": {}
        }
        
        # 合并 vanilla（使用规范化去重）
        old_vanilla = old_data.get(entity_type, {}).get("vanilla", [])
        new_vanilla = new_data.get(entity_type, {}).get("vanilla", [])
        merged[entity_type]["vanilla"] = deduplicate_entities(old_vanilla + new_vanilla)
        
        # 合并 latent
        old_latent = old_data.get(entity_type, {}).get("latent", {})
        new_latent = new_data.get(entity_type, {}).get("latent", {})
        
        # 处理所有场景
        all_scenarios = set(old_latent.keys()) | set(new_latent.keys())
        for scenario in all_scenarios:
            if scenario == "_missing_contexts":
                # 保留最新的未适配场景记录
                merged[entity_type]["latent"][scenario] = new_latent.get(scenario, {})
            else:
                # 合并场景数据（使用规范化去重）
                old_entities = old_latent.get(scenario, [])
                new_entities = new_latent.get(scenario, [])
                merged[entity_type]["latent"][scenario] = deduplicate_entities(old_entities + new_entities)
    
    return merged

def load_existing_entity_diversity(use_global_cache=True, ENTITY_CACHE_DIR=None, run_output_dir=None) -> Tuple[Optional[Dict], int]:
    """加载已存在的实体多样化数据，返回 (数据, 版本号)"""
    # 优先从全局缓存查找
    if use_global_cache:
        entity_diversity_base = ENTITY_CACHE_DIR
    else:
        entity_diversity_base = run_output_dir / "entity_diversity"
        
    if not entity_diversity_base.exists():
        return None, 0
    
    # 获取所有实体多样化目录，排除当前运行时生成的目录
    current_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    current_dir = f'entity_diversity_{current_timestamp}'
    
    entity_dirs = [d for d in entity_diversity_base.iterdir() 
                if d.is_dir() and d.name.startswith('entity_diversity_') 
                and d.name != current_dir]  # 排除当前运行时的目录
    
    if not entity_dirs:
        return None, 0
    
    # 按时间戳排序，获取最新的
    entity_dirs.sort(reverse=True, key=lambda x: x.name)
    latest_dir = entity_dirs[0]
    entity_file = latest_dir / 'entity_diversity.json'
        
    # 读取版本号
    cache_version = get_cache_version(entity_diversity_base)
    
    print(f"\n[信息] 正在与最新实体文件进行比较: {entity_file} (版本: {cache_version})")
        
    if entity_file.exists():
        try:
            with open(entity_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 加载时就进行一次完整去重
            data = deduplicate_entity_data(data)
            
            # 复制整个目录到运行目录
            copy_entity_diversity_directory(latest_dir)
            
            return data, cache_version
        except Exception as e:
            print(f"[警告] 加载已有实体多样化数据失败: {e}")
        
    return None, 0
        
def copy_entity_diversity_directory(source_dir: Path):
    """复制实体多样化目录到运行目录"""
    try:
        import shutil
        # 确保目标目录存在
        ENTITY_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
        
        # 只复制 entity_diversity.json 文件，不复制日志文件
        source_json_file = source_dir / 'entity_diversity.json'
        target_json_file = ENTITY_OUTPUT_DIR / 'entity_diversity.json'
        
        if source_json_file.exists():
            shutil.copy2(source_json_file, target_json_file)
            print(f"[✓] 已复制实体多样化数据到运行目录: {target_json_file}")
        else:
            print(f"[警告] 源目录中未找到 entity_diversity.json: {source_json_file}")
            
    except Exception as e:
        print(f"[错误] 复制实体多样化数据失败: {e}")
        print(f"  源目录: {source_dir}")
        print(f"  目标目录: {ENTITY_OUTPUT_DIR}")
        raise  # 抛出异常，让调用者知道复制失败
        
def is_concrete_entity(entity, entity_type):
    # 针对常见类型，定义具体实例的正则或关键词
    if entity_type == "年龄":
        import re
        # 支持更多年龄表达方式
        age_patterns = [
            r"^\d+\s*岁$",  # 数字+岁：25岁
            r"^[一二三四五六七八九十百]+\s*岁$",  # 中文数字+岁：二十五岁
            r"^\d+\s*岁\s*[半多]?$",  # 带半/多：25岁半、25岁多
            r"^\d+\s*个?\s*月$",  # xx个月：6个月、6月
            r"^\d+\s*岁\s*\d+\s*个?\s*月$",  # 岁+月：5岁3个月
            r"^[一二三四五六七八九十百]+\s*岁\s*[半多]?$",  # 中文数字带半/多：二十岁半
            r"^[一二三四五六七八九十百]+\s*个?\s*月$",  # 中文数字月：六个月
            r"^周?\s*岁$",  # 周岁
            r"^\d+\s*周?\s*岁$",  # 数字+周岁：25周岁
            r"^[一二三四五六七八九十百]+\s*周?\s*岁$",  # 中文数字+周岁：二十五周岁
        ]
        return any(bool(re.match(pattern, entity.strip())) for pattern in age_patterns)
    if entity_type == "国籍":
        return entity.endswith("国") or entity in SMALL_VALUE_DOMAIN_ENTITIES.get("国籍", [])
    if entity_type == "家庭成员":
        return entity in SMALL_VALUE_DOMAIN_ENTITIES.get("家庭成员", [])
    if entity_type == "行程信息":
        # 行程信息允许短格式（如"CA128航班"）和长格式（如"乘坐Z358次列车从西安出发前往乌鲁木齐"）
        # 检查是否包含行程相关的关键词
        travel_keywords = ["航班", "列车", "公交", "地铁", "动车", "火车", "巴士", "专线", "快线", "专列", "旅游", "乘坐", "搭乘", "出发", "前往", "到达", "轨道交通", "高铁", "机场", "航站楼", "码头", "航线", "接驳车", "摆渡车", "大巴"]
        if any(keyword in entity for keyword in travel_keywords):
            # 长度限制放宽到30字符
            if len(entity) > 20 or len(entity) < 1:
                return False
            return True
    
    # 通用过滤：排除明显的类别名、描述性词语
    abstract_keywords = ["类别", "类型", "阶段", "状态", "背景", "信息", "资料", "描述", "情况", "经历", "成员", "对象", "人员", "身份", "时光轴", "阶段"]
    if any(word in entity for word in abstract_keywords):
        return False
    # 长度限制
    if len(entity) > 20 or len(entity) < 1:
        return False
    return True

def check_entity_pool_sufficiency(entity_data: Dict, target_count: int, entity_type: str = None) -> Tuple[bool, int]:
    """检查实体池是否足够（只检查适配场景）"""
    vanilla_count = len(entity_data.get("vanilla", []))
    vanilla_ok = vanilla_count >= target_count
    latent = entity_data.get("latent", {})
    # 只检查适配场景
    adapted_scenarios = ENTITY_CONTEXT_MAPPING.get(entity_type, list(LATENT_SCENARIOS.keys()))
    latent_ok = all(len(latent.get(s, [])) >= target_count for s in adapted_scenarios)
    return vanilla_ok and latent_ok, vanilla_count

def expand_entity_pool(entity_type: str, entity_data: Dict, target_count: int, current_count: int, example_map: Dict = None) -> Dict:
    """动态扩充实体池，带兜底策略"""
    MAX_RETRIES = 3
    MAX_CONSECUTIVE_ZEROS = 2
    FALLBACK_MULTIPLIER = 2.0  # 兜底时扩大生成数量的倍数
    
    if example_map is None:
        example_map = {}
    
    # 初始化数据结构
    if "vanilla" not in entity_data:
        entity_data["vanilla"] = []
    if "latent" not in entity_data:
        entity_data["latent"] = {}
    
    def generate_vanilla(needed_count: int, is_fallback: bool = False) -> List[str]:
        """生成 vanilla 实体，支持兜底策略"""
        generation_count = int(needed_count * (FALLBACK_MULTIPLIER if is_fallback else 1.5))
        vanilla_examples = entity_data.get('vanilla', [])[:5]
        vanilla_examples_str = ', '.join(vanilla_examples) + (' 等' if vanilla_examples else '')
        example = example_map.get(entity_type, "请直接给出真实存在的实例")
        
        # 根据是否兜底调整提示词
        quality_requirements = """
        1. 不要与常见的实体重复
        2. 保持实体的语义和表达方式
        3. 每行一个，不要编号，不要解释
        4. 确保每个实体都是具体的、真实的实例，不要生成抽象或泛化的描述""" if not is_fallback else """
        1. 不要与常见的实体重复
        2. 保持基本语义，但可以更灵活多样
        3. 每行一个，不要编号，不要解释
        4. 实体可以更具创造性，但要保持可理解性"""
                        
        vanilla_expand_prompt = f'''请为"{entity_type}"生成{generation_count}个新的实体变体，要求：
        {quality_requirements}
        5. {example}
        例如常见实体有：{vanilla_examples_str}
        请生成更多不同的变体：'''
        
        vanilla_expanded = call_zhipu_api(vanilla_expand_prompt)
        vanilla_new_entities = clean_entity_output(vanilla_expanded)
            
        # 根据是否兜底使用不同的过滤条件
        if not is_fallback:
            return [e for e in vanilla_new_entities if is_concrete_entity(e, entity_type)]
        else:
            # 兜底时放宽过滤条件
            return [e for e in vanilla_new_entities if len(e.strip()) >= 1 and len(e) <= 30]
    
    # vanilla 扩充逻辑
    retry_count = 0
    consecutive_zeros = 0
    target_vanilla = int(target_count * 0.4)  # vanilla目标为总目标的40%
    
    while len(entity_data["vanilla"]) < target_vanilla and retry_count < MAX_RETRIES + 1:  # +1为兜底轮
        try:
            needed_count = target_vanilla - len(entity_data["vanilla"])
            is_fallback = retry_count == MAX_RETRIES  # 最后一轮使用兜底策略
            
            if is_fallback:
                print(f"[警告] {entity_type} vanilla进入兜底策略")
            
            vanilla_new_entities = generate_vanilla(needed_count, is_fallback)
            
            if len(vanilla_new_entities) == 0:
                consecutive_zeros += 1
                if consecutive_zeros >= MAX_CONSECUTIVE_ZEROS and not is_fallback:
                    print(f"[警告] {entity_type} vanilla连续{MAX_CONSECUTIVE_ZEROS}次生成0个实体，进入兜底策略")
                    retry_count = MAX_RETRIES - 1  # 强制进入兜底轮
            else:
                consecutive_zeros = 0
                entity_data["vanilla"].extend(vanilla_new_entities)
                print(f"[✓] {entity_type} vanilla扩充了{len(vanilla_new_entities)}个实体" + (" (兜底)" if is_fallback else ""))
            retry_count += 1
        except Exception as e:
            print(f"[警告] {entity_type} vanilla扩充失败: {e}")
            if not is_fallback:
                print("[信息] 尝试进入兜底策略")
                retry_count = MAX_RETRIES - 1  # 强制进入兜底轮
            else:
                break
    
    def generate_latent(scenario_name: str, scenario_desc: str, needed_count: int, is_fallback: bool = False) -> List[str]:
        """生成 latent 实体，支持兜底策略"""
        generation_count = int(needed_count * (FALLBACK_MULTIPLIER if is_fallback else 1.5))
        latent_examples = entity_data["latent"].get(scenario_name, [])[:5]
        latent_examples_str = ', '.join(latent_examples) + (' 等' if latent_examples else '')
        example = example_map.get(entity_type, "请直接给出真实存在的实例")
        
        # 根据是否兜底调整提示词
        quality_requirements = """
        1. 符合场景的表达特点
        2. 不要与常见的实体重复
        3. 每行一个，不要编号，不要解释
        4. 确保每个实体都是具体的、真实的实例""" if not is_fallback else """
        1. 保持基本语义，可以更灵活地适应场景
        2. 不要重复已有实体
        3. 每行一个，不要编号，不要解释
        4. 实体可以更具创造性，但要保持可理解性"""
                
        latent_expand_prompt = f'''请为"{entity_type}"在"{scenario_name}"场景（{scenario_desc}）下生成{generation_count}个实体变体，要求：
        {quality_requirements}
        5. {example}
        例如常见实体有：{latent_examples_str}
        请生成更多不同的变体：'''
        
        latent_expanded = call_zhipu_api(latent_expand_prompt)
        latent_new_entities = clean_entity_output(latent_expanded)
        
        # 根据是否兜底使用不同的过滤条件
        if not is_fallback:
            return [e for e in latent_new_entities if is_concrete_entity(e, entity_type)]
        else:
            # 兜底时放宽过滤条件
            return [e for e in latent_new_entities if len(e.strip()) >= 1 and len(e) <= 30]
        
        # 计算当前 vanilla 比例和目标
        current_vanilla_ratio = len(entity_data["vanilla"]) / target_count if target_count > 0 else 0
        MIN_VANILLA_RATIO = 0.3  # vanilla最低比例
        TARGET_VANILLA_RATIO = 0.4  # vanilla目标比例
        
        # 如果 vanilla 比例过低，强制补充
        if current_vanilla_ratio < MIN_VANILLA_RATIO:
            print(f"[警告] {entity_type} vanilla比例过低({current_vanilla_ratio:.1%})，需要补充")
            needed_vanilla = int(target_count * TARGET_VANILLA_RATIO) - len(entity_data["vanilla"])
            if needed_vanilla > 0:
                vanilla_new_entities = generate_vanilla(needed_vanilla, is_fallback=True)
                entity_data["vanilla"].extend(vanilla_new_entities)
                print(f"[✓] {entity_type} vanilla强制补充了{len(vanilla_new_entities)}个实体")
        
        # latent 扩充逻辑
        adapted_scenarios = ENTITY_CONTEXT_MAPPING.get(entity_type, list(LATENT_SCENARIOS.keys()))
        if not adapted_scenarios:
            print(f"[警告] {entity_type} 无适配场景，使用所有场景")
            adapted_scenarios = list(LATENT_SCENARIOS.keys())
        
        missing_contexts = {}
        per_scenario_target = max(1, (target_count - len(entity_data["vanilla"])) // len(adapted_scenarios))
        
        for scenario_name, scenario_desc in LATENT_SCENARIOS.items():
            if scenario_name not in adapted_scenarios:
                missing_contexts[scenario_name] = "该语境下缺乏自然表达，未生成"
                continue
            
            if scenario_name not in entity_data["latent"]:
                entity_data["latent"][scenario_name] = []
            
            retry_count = 0
            consecutive_zeros = 0
            while len(entity_data["latent"][scenario_name]) < per_scenario_target and retry_count < MAX_RETRIES + 1:
                try:
                    needed_count = per_scenario_target - len(entity_data["latent"][scenario_name])
                    is_fallback = retry_count == MAX_RETRIES
                    
                    if is_fallback:
                        print(f"[警告] {entity_type} {scenario_name}进入兜底策略")
                    
                    latent_new_entities = generate_latent(scenario_name, scenario_desc, needed_count, is_fallback)
                    
                    if len(latent_new_entities) == 0:
                        consecutive_zeros += 1
                        if consecutive_zeros >= MAX_CONSECUTIVE_ZEROS and not is_fallback:
                            print(f"[警告] {entity_type} {scenario_name}连续{MAX_CONSECUTIVE_ZEROS}次生成0个实体，进入兜底策略")
                            retry_count = MAX_RETRIES - 1
                    else:
                        consecutive_zeros = 0
                        entity_data["latent"][scenario_name].extend(latent_new_entities)
                        print(f"[✓] {entity_type} {scenario_name}扩充了{len(latent_new_entities)}个实体" + (" (兜底)" if is_fallback else ""))
                    retry_count += 1
                except Exception as e:
                    print(f"[警告] {entity_type} {scenario_name}扩充失败: {e}")
                    if not is_fallback:
                        print("[信息] 尝试进入兜底策略")
                        retry_count = MAX_RETRIES - 1
                    else:
                        break
        # 记录未适配场景
        if missing_contexts:
            entity_data["latent"]["_missing_contexts"] = missing_contexts
        return entity_data

def get_all_entities(entity_data, adapted_scenarios):
    """合并vanilla和所有latent，去重"""
    vanilla = set(entity_data.get("vanilla", []))
    latent = entity_data.get("latent", {})
    all_latent = set()
    for s in adapted_scenarios:
        all_latent.update(latent.get(s, []))
    return vanilla | all_latent

def prioritized_scenarios(adapted_scenarios):
    """优先返回高多样性场景（如网络用语、社交场合），其余按原顺序"""
    high_priority = [s for s in ["网络用语", "社交场合"] if s in adapted_scenarios]
    rest = [s for s in adapted_scenarios if s not in high_priority]
    return high_priority + rest

    # === Step 1: 生成句子多样化的属性 ===
    print("=== Step 1: 句子多样化的属性 ===")
    
    # 优先从全局缓存检查
    existing_attributes = check_existing_file('sen_diversify_attribute.json', SEN_CACHE_DIR)
    
    if existing_attributes:
        print(f"[✓] 找到全局缓存的句子多样化属性文件，复用已有数据")
        sen_attributes = existing_attributes.get('sen_diversify_attribute', [])
        
        # 复制到运行目录
        copy_to_output_dir(FILE_SEN_ATTRIBUTE_CACHE, FILE_SEN_ATTRIBUTE_OUTPUT)
        
    else:
        print("[✓] 生成句子多样化的属性")
        prompt = "你认为生成包含敏感实体的句子要关注哪些重要属性？请简要列出3-5项。"
        answer = call_zhipu_api(prompt)
        sen_attributes = extract_list_items(answer)
        
        # 保存到全局缓存
        result = {"sen_diversify_attribute": sen_attributes}
        if use_global_cache:
            with open(FILE_SEN_ATTRIBUTE_CACHE, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"[✓] 句子多样化属性已保存至全局缓存: {FILE_SEN_ATTRIBUTE_CACHE}")
        
        # 保存到运行目录
        with open(FILE_SEN_ATTRIBUTE_OUTPUT, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"[✓] 句子多样化属性已保存至运行目录: {FILE_SEN_ATTRIBUTE_OUTPUT}")
    
    # === Step 2: 生成句子多样化的属性对应的值 ===
    print("\n=== Step 2: 句子多样化的属性对应的值 ===")
    
    # 优先从全局缓存检查
    existing_values = check_existing_file('sen_diversify_value.json', SEN_CACHE_DIR)
    
    if existing_values:
        print(f"[✓] 找到全局缓存的句子多样化属性值文件，复用已有数据")
        sen_values = existing_values.get('sen_diversify_value', {})
        
        # 复制到运行目录
        copy_to_output_dir(FILE_SEN_VALUE_CACHE, FILE_SEN_VALUE_OUTPUT)
        
    else:
        print(f"[✓] 针对 {len(sen_attributes)} 个属性生成对应的值")
        sen_values = {}
        
        for sen_attr in tqdm(sen_attributes, desc="属性值生成中"):
            attr_clean = sen_attr.strip("：:").strip()
            prompt = f"假设你是一位数据隐私专家。请列出 {NUM_SENTENCE_VARIANTS} 种和敏感信息有关的不同'{attr_clean}'。"
    try:
        raw = call_zhipu_api(prompt)
        items = extract_list_items(raw)
        sen_values[attr_clean] = items
    except RuntimeError as e:
        print(f"[跳过] 属性 {attr_clean} 处理失败：{e}")

        # 保存到全局缓存
        result = {"sen_diversify_value": sen_values}
        if use_global_cache:
            with open(FILE_SEN_VALUE_CACHE, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"[✓] 句子多样化属性值已保存至全局缓存: {FILE_SEN_VALUE_CACHE}")
        
        # 保存到运行目录
        with open(FILE_SEN_VALUE_OUTPUT, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"[✓] 句子多样化属性值已保存至运行目录: {FILE_SEN_VALUE_OUTPUT}")
    
    # === Step 3: 实体多样化生成-vanilla/latent ===
    print("\n=== Step 3: 实体多样化生成-vanilla/latent ===")
    print(f"[✓] 实体类型共 {len(flat_list)} 类，开始逐类生成…")
    print(f"[✓] latent模式将根据 {len(LATENT_SCENARIOS)} 个不同场景生成：{list(LATENT_SCENARIOS.keys())}")

    # 优先从全局缓存加载实体多样化数据（带版本号）
    existing_entity_data, loaded_version = load_existing_entity_diversity(use_global_cache, ENTITY_CACHE_DIR, run_output_dir)
    if existing_entity_data:
        print(f"[✓] 找到全局缓存的实体多样化数据（版本：{loaded_version}），将以最新文件为基准")
    else:
        print(f"[信息] 未找到全局缓存的实体多样化数据，将全部新生成")
        existing_entity_data = {}
        loaded_version = 0

    # 存储所有实体多样化数据
    entity_diversity_data = {}
    has_new_generation = False  # 标记是否有新生成的数据

    for entity_type in tqdm(flat_list, desc="处理实体类型"):
        if entity_type in SKIP_ENTITY_TYPES:
            print(f"[跳过] {entity_type} - 直接复用已有数据")
            entity_data = existing_entity_data.get(entity_type, {"vanilla": [], "latent": {}})
            entity_diversity_data[entity_type] = entity_data
            continue
        entity_data = existing_entity_data.get(entity_type, {"vanilla": [], "latent": {}})
        entity_data["vanilla"] = [e for e in entity_data.get("vanilla", []) if is_concrete_entity(e, entity_type)]
        adapted_scenarios = ENTITY_CONTEXT_MAPPING.get(entity_type, list(LATENT_SCENARIOS.keys()))
        latent = entity_data.get("latent", {})
        filtered_latent = {}
        for scenario in adapted_scenarios:
            filtered_latent[scenario] = [e for e in latent.get(scenario, []) if is_concrete_entity(e, entity_type)]
        if "_missing_contexts" in latent:
            filtered_latent["_missing_contexts"] = latent["_missing_contexts"]
        entity_data["latent"] = filtered_latent
        # 统计已收集数量
        all_entities = get_all_entities(entity_data, adapted_scenarios)
        collected = len(all_entities)
        gap = NUM_ENTITY_VARIANTS - collected
        if gap <= 0:
            print(f"[✓] {entity_type} 已满足目标数量({collected}/{NUM_ENTITY_VARIANTS})，跳过生成")
            entity_diversity_data[entity_type] = entity_data
            continue
        # 生成策略
        vanilla_target = int(NUM_ENTITY_VARIANTS * 0.4)  # vanilla补到40%
        if collected >= int(NUM_ENTITY_VARIANTS * 0.7):
            # 只补latent
            print(f"[信息] {entity_type} 已收集{collected}，仅补latent，缺口{gap}")
            need_vanilla = 0
            need_latent = gap
        else:
            # vanilla补到30~50%，剩余缺口由latent补
            need_vanilla = max(0, vanilla_target - len(entity_data["vanilla"]))
            need_latent = gap - need_vanilla
            print(f"[信息] {entity_type} 收集较少({collected})，补vanilla到{vanilla_target}，剩余补latent")
        # 补vanilla
        if need_vanilla > 0:
            log_main(f"\n[{datetime.now().strftime('%H:%M:%S')}] 开始处理实体类型: {entity_type}")
            log_main(f"目标数量: {NUM_ENTITY_VARIANTS}, 当前收集: {collected}, 缺口: {gap}")
            log_main(f"策略: 补vanilla到{vanilla_target}个，剩余{gap - need_vanilla}个由latent补充")
            
            retry_count = 0
            MAX_RETRIES = 3
            while need_vanilla > 0 and retry_count < MAX_RETRIES:
                gen_count = int(need_vanilla * 1.5)
                vanilla_examples = entity_data.get('vanilla', [])[:5]
                vanilla_examples_str = ', '.join(vanilla_examples) + (' 等' if vanilla_examples else '')
                example = example_map.get(entity_type, "请直接给出真实存在的实例")
                vanilla_expand_prompt = f'''
                你是一位语言数据专家，请为以下任务生成实体变体：

                任务说明：
                - 实体类型为："{entity_type}"
                - 需要生成 {gen_count} 个全新、具体、真实的 {entity_type} 实体
                - 请严格遵守以下要求：

                1. 不要与下列常见实体重复或相近表达
                2. 每个实体应具有清晰边界，可直接用于命名实体识别任务标注
                3. 避免抽象、模糊或概括性描述（如"某人"、"成年人"等）
                4. 保持语义一致，且形式自然、合理
                5. 仅输出实体本体内容，每行一个，不要编号、不要附加解释
                6、每个实体的字符串长度必须小于等于20

                参考常见实体包括（请避免与之重复）：
                {vanilla_examples_str}

                {example}

                请开始生成：
                '''
                vanilla_expanded = call_zhipu_api(vanilla_expand_prompt)
                # 记录API原始返回内容
                log_detail(f"\n=== {entity_type} ===")
                log_detail(f"Vanilla 第{retry_count + 1}轮生成:")
                log_detail(f"API原始返回: {repr(vanilla_expanded)}")
                vanilla_new_entities = clean_entity_output(vanilla_expanded)
                log_detail(f"清洗后内容: {vanilla_new_entities}")
                
                # 记录API返回的原始内容
                log_detail(f"\n=== {entity_type} ===")
                log_detail(f"Vanilla 第{retry_count + 1}轮生成:")
                log_detail(f"生成内容: {vanilla_new_entities}")
                
                # 过滤处理
                before_filter = len(vanilla_new_entities)
                vanilla_filtered = [e for e in vanilla_new_entities if is_concrete_entity(e, entity_type)]
                after_filter = len(vanilla_filtered)
                
                # 记录过滤原因
                filtered_entities = []
                for entity in vanilla_new_entities:
                    if not is_concrete_entity(entity, entity_type):
                        # 判断过滤原因
                        if len(entity) < 1 or len(entity) > 16:
                            reason = "长度"
                        elif any(word in entity for word in ["类别", "类型", "阶段", "状态", "背景", "信息", "资料", "描述", "情况", "经历", "成员", "对象", "人员", "身份", "时光轴", "阶段"]):
                            reason = "抽象"
                        else:
                            reason = "质量"
                        filtered_entities.append((entity, reason))
                
                # 去重处理（使用规范化去重）
                before = set(normalize_entity(e) for e in entity_data["vanilla"])
                vanilla_final = [e for e in vanilla_filtered if normalize_entity(e) not in before]
                after_dedup = len(vanilla_final)
                
                # 记录日志
                log_generation_step(entity_type, "vanilla", before_filter, after_filter, after_dedup, vanilla_new_entities, filtered_entities)
                
                entity_data["vanilla"].extend(vanilla_final)
                entity_data["vanilla"] = deduplicate_entities(entity_data["vanilla"])
                all_entities = get_all_entities(entity_data, adapted_scenarios)
                need_vanilla = max(0, vanilla_target - len(entity_data["vanilla"]))
                
                log_main(f"当前总数: {len(all_entities)}/{NUM_ENTITY_VARIANTS}")
                # 检查是否已达到目标数量，如果达到则跳出vanilla生成循环
                if len(all_entities) >= NUM_ENTITY_VARIANTS:
                    break
                retry_count += 1
        # 补latent，优先高多样性场景
        if need_latent > 0:
            log_main(f"\n[{datetime.now().strftime('%H:%M:%S')}] 开始处理实体类型: {entity_type} - Latent生成")
            log_main(f"目标数量: {NUM_ENTITY_VARIANTS}, 当前收集: {len(get_all_entities(entity_data, adapted_scenarios))}, 缺口: {need_latent}")
            
            scenarios_order = prioritized_scenarios(adapted_scenarios)
            missing_contexts = {}
            per_scene = max(1, need_latent // max(1, len(scenarios_order)))
            for scenario_name in scenarios_order:
                if scenario_name not in LATENT_SCENARIOS:
                    continue
                if scenario_name not in entity_data["latent"]:
                    entity_data["latent"][scenario_name] = []
                retry_count = 0
                MAX_RETRIES = 3
                while len(entity_data["latent"][scenario_name]) < per_scene and retry_count < MAX_RETRIES:
                    gen_count = int((per_scene - len(entity_data["latent"][scenario_name])) * 1.5)
                    latent_examples = entity_data["latent"].get(scenario_name, [])[:5]
                    latent_examples_str = ', '.join(latent_examples) + (' 等' if latent_examples else '')
                    example = example_map.get(entity_type, "请直接给出真实存在的实例")
                    latent_expand_prompt = f'''
                    你是一位语言数据专家，请为以下任务生成实体变体：

                    任务说明：
                    - 实体类型为："{entity_type}"
                    - 需要生成 {gen_count} 个全新、具体、真实的 {entity_type} 实体，要求体现"{scenario_name}"场景（{LATENT_SCENARIOS[scenario_name]}）的表达风格
                    - 请严格遵守以下要求：

                    1. 不要与下列常见实体重复或相近表达
                    2. 每个实体应具有清晰边界，可直接用于命名实体识别任务标注
                    3. 避免抽象、模糊或概括性描述（如"某人"、"成年人"等）
                    4. 保持语义一致，且形式自然、合理
                    5. 仅输出实体本体内容，每行一个，不要编号、不要附加解释
                    6、每个实体的字符串长度必须小于等于20

                    参考常见实体包括（请避免与之重复）：
                    {latent_examples_str}

                    {example}

                    请开始生成：
                    '''
                    latent_expanded = call_zhipu_api(latent_expand_prompt)
                    # 记录API原始返回内容
                    log_detail(f"\n=== {entity_type} ===")
                    log_detail(f"Latent[{scenario_name}] 第{retry_count + 1}轮生成:")
                    log_detail(f"API原始返回: {repr(latent_expanded)}")
                    latent_new_entities = clean_entity_output(latent_expanded)
                    log_detail(f"清洗后内容: {latent_new_entities}")
                    
                    # 记录API返回的原始内容
                    log_detail(f"\n=== {entity_type} ===")
                    log_detail(f"Latent[{scenario_name}] 第{retry_count + 1}轮生成:")
                    log_detail(f"生成内容: {latent_new_entities}")
                    
                    # 过滤处理
                    before_filter = len(latent_new_entities)
                    latent_filtered = [e for e in latent_new_entities if is_concrete_entity(e, entity_type)]
                    after_filter = len(latent_filtered)
                    
                    # 记录过滤原因
                    filtered_entities = []
                    for entity in latent_new_entities:
                        if not is_concrete_entity(entity, entity_type):
                            # 判断过滤原因
                            if len(entity) < 1 or len(entity) > 16:
                                reason = "长度"
                            elif any(word in entity for word in ["类别", "类型", "阶段", "状态", "背景", "信息", "资料", "描述", "情况", "经历", "成员", "对象", "人员", "身份", "时光轴", "阶段"]):
                                reason = "抽象"
                            else:
                                reason = "质量"
                            filtered_entities.append((entity, reason))
                    
                    # 去重处理（使用规范化去重）
                    existing = set()
                    for e in entity_data["latent"][scenario_name]:
                        existing.add(normalize_entity(e))
                    for e in get_all_entities(entity_data, adapted_scenarios):
                        existing.add(normalize_entity(e))
                    
                    latent_final = [e for e in latent_filtered if normalize_entity(e) not in existing]
                    after_dedup = len(latent_final)
                    
                    # 记录日志
                    log_generation_step(entity_type, scenario_name, before_filter, after_filter, after_dedup, latent_new_entities, filtered_entities)
                    
                    entity_data["latent"][scenario_name].extend(latent_final)
                    entity_data["latent"][scenario_name] = deduplicate_entities(entity_data["latent"][scenario_name])
                    all_entities = get_all_entities(entity_data, adapted_scenarios)
                    
                    log_main(f"当前总数: {len(all_entities)}/{NUM_ENTITY_VARIANTS}")
                    if len(all_entities) >= NUM_ENTITY_VARIANTS:
                        break
                    retry_count += 1
                # 检查是否已达到目标数量，如果达到则跳出整个latent生成循环
                if len(all_entities) >= NUM_ENTITY_VARIANTS:
                    break
            # 记录未适配场景
            for scenario_name in LATENT_SCENARIOS:
                if scenario_name not in adapted_scenarios:
                    missing_contexts[scenario_name] = "该语境下缺乏自然表达，未生成"
            if missing_contexts:
                entity_data["latent"]["_missing_contexts"] = missing_contexts
        # 最终合并去重
        all_entities = get_all_entities(entity_data, adapted_scenarios)
        # 记录最终结果
        success = len(all_entities) >= NUM_ENTITY_VARIANTS
        log_final_result(entity_type, NUM_ENTITY_VARIANTS, len(all_entities), success)
        
        # 若仍未达标，警告
        if len(all_entities) < NUM_ENTITY_VARIANTS:
            print(f"[警告] {entity_type} 实体池补充后仍未达标({len(all_entities)}/{NUM_ENTITY_VARIANTS})")
        
        # 确保每个实体类型都被添加到结果中
    entity_diversity_data[entity_type] = entity_data

    # 保存实体多样化数据
    entity_diversity_file = ENTITY_OUTPUT_DIR / 'entity_diversity.json'
    with open(entity_diversity_file, 'w', encoding='utf-8') as f:
        json.dump(entity_diversity_data, f, ensure_ascii=False, indent=2)
    print(f"[✓] 实体多样化数据已保存至运行目录：{entity_diversity_file}")
    
    # 如果有新生成的数据且使用全局缓存，则更新全局缓存
    if has_new_generation and use_global_cache:
        try:
            # 检查版本号是否变化
            current_version = get_cache_version(ENTITY_CACHE_DIR)
            if current_version != loaded_version:
                print(f"[警告] 缓存版本已更新（{loaded_version} -> {current_version}），执行合并")
                # 加载最新缓存
                latest_data, _ = load_existing_entity_diversity()
                if latest_data:
                    # 合并策略：保留双方数据，去重
                    merged_data = merge_entity_diversity_data(latest_data, entity_diversity_data)
                    entity_diversity_data = merged_data
            
            # 创建新的缓存目录
            global_entity_diversity_dir = ENTITY_CACHE_DIR / f"entity_diversity_{timestamp}"
            global_entity_diversity_dir.mkdir(parents=True, exist_ok=True)
            global_entity_diversity_file = global_entity_diversity_dir / 'entity_diversity.json'
        
            # 保存数据
            with open(global_entity_diversity_file, 'w', encoding='utf-8') as f:
                json.dump(entity_diversity_data, f, ensure_ascii=False, indent=2)
                
            # 更新版本号
            update_cache_version(ENTITY_CACHE_DIR)
            
            print(f"[✓] 实体多样化数据已更新至全局缓存：{global_entity_diversity_file}")
            
        except Exception as e:
            print(f"[错误] 更新全局缓存失败: {e}")
    
    print(f"[✓] 所有多样化策略处理完成")
    print(f"[✓] 运行目录：{run_output_dir}")
    if use_global_cache:
        print(f"[✓] 全局缓存目录：{global_cache_dir}")

# 新增：统一的实体提示词生成函数
# def get_entity_prompt(entity_type, num, scenario=None):
#     example = example_map.get(entity_type, "请直接给出真实存在的实例")
#     scenario_str = f"\n场景要求：{scenario}" if scenario else ""
#     return (
#         f"请列出{num}个具体的、真实存在的"{entity_type}"实体。"
#         f"{scenario_str}\n"
#         f"要求：\n"
#         f"1. 每个实体必须是具体的、真实存在的实例，不能是类别名、抽象描述、泛化词语\n"
#         f"2. 直接列出实体名称，每行一个，不要编号，不要解释\n"
#         f"3. {example}"
#     )

if __name__ == "__main__":
    main()