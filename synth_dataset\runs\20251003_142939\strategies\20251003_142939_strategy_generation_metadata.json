{"start_time": "2025-10-03T14:29:39.817673", "output_directory": "synth_dataset\\runs\\20251003_142939\\strategies", "target_entity_types": ["姓名", "年龄", "性别", "国籍", "职业", "民族", "教育背景", "婚姻状况", "政治倾向", "家庭成员", "工资数额", "投资产品", "税务记录", "信用记录", "实体资产", "交易信息", "疾病", "药物", "临床表现", "医疗程序", "过敏信息", "生育信息", "地理位置", "行程信息"], "total_entity_types": 24, "generation_config": {"dataset_path": "format-dataset\\privacy_bench_small_10.json", "target_count": 20, "max_iterations": 10, "batch_size": 10, "distribution_threshold": 0.05, "generation_features": {"use_sentence_diversity": true, "use_entity_diversity": true, "use_example_sentences": true}}, "strategy_files": {"target_distribution": "synth_dataset\\runs\\20251003_142939\\strategies\\entity_target\\privacy_bench_target.json", "sentence_diversity": "synth_dataset\\runs\\20251003_142939\\strategies\\sen_diversity\\sen_diversify_value.json", "entity_diversity": "synth_dataset\\runs\\20251003_142939\\strategies\\entity_diversity\\entity_diversity_20251003_142939\\entity_diversity.json"}, "generation_statistics": {"sentence_diversity_attributes": 0, "entity_diversity_pools": 0, "target_distribution_size": 0}, "strategy_statistics": {"strategy_file_sizes": {"target_distribution": 507, "sentence_diversity": 572, "entity_diversity": 15867}, "sentence_diversity_stats": {"total_attributes": 1, "attributes": ["sen_diversify_value"], "total_values": 1, "attribute_value_counts": {"sen_diversify_value": 1}}, "entity_diversity_stats": {"total_entity_types": 24, "entity_type_counts": {"姓名": 13, "年龄": 13, "性别": 13, "国籍": 13, "职业": 13, "民族": 13, "教育背景": 13, "婚姻状况": 13, "政治倾向": 13, "家庭成员": 13, "工资数额": 13, "投资产品": 13, "税务记录": 13, "信用记录": 13, "实体资产": 13, "交易信息": 13, "疾病": 13, "药物": 13, "临床表现": 13, "医疗程序": 13, "过敏信息": 13, "生育信息": 13, "地理位置": 13, "行程信息": 13}, "vanilla_entity_counts": {"姓名": 7, "年龄": 7, "性别": 7, "国籍": 7, "职业": 7, "民族": 7, "教育背景": 7, "婚姻状况": 7, "政治倾向": 7, "家庭成员": 7, "工资数额": 7, "投资产品": 7, "税务记录": 7, "信用记录": 7, "实体资产": 7, "交易信息": 7, "疾病": 7, "药物": 7, "临床表现": 7, "医疗程序": 7, "过敏信息": 7, "生育信息": 7, "地理位置": 7, "行程信息": 7}, "latent_scenario_counts": {"姓名": 3, "年龄": 3, "性别": 3, "国籍": 3, "职业": 3, "民族": 3, "教育背景": 3, "婚姻状况": 3, "政治倾向": 3, "家庭成员": 3, "工资数额": 3, "投资产品": 3, "税务记录": 3, "信用记录": 3, "实体资产": 3, "交易信息": 3, "疾病": 3, "药物": 3, "临床表现": 3, "医疗程序": 3, "过敏信息": 3, "生育信息": 3, "地理位置": 3, "行程信息": 3}, "total_entities": 312}, "target_distribution_stats": {"total_entity_types": 24, "entity_types": ["姓名", "年龄", "性别", "国籍", "职业", "民族", "教育背景", "婚姻状况", "政治倾向", "家庭成员", "工资数额", "投资产品", "税务记录", "信用记录", "实体资产", "交易信息", "疾病", "药物", "临床表现", "医疗程序", "过敏信息", "生育信息", "地理位置", "行程信息"], "target_counts": {"姓名": 19, "年龄": 18, "性别": 18, "国籍": 16, "职业": 12, "民族": 19, "教育背景": 17, "婚姻状况": 20, "政治倾向": 20, "家庭成员": 20, "工资数额": 20, "投资产品": 20, "税务记录": 20, "信用记录": 20, "实体资产": 20, "交易信息": 20, "疾病": 13, "药物": 12, "临床表现": 17, "医疗程序": 19, "过敏信息": 20, "生育信息": 20, "地理位置": 17, "行程信息": 20}, "total_target_count": 437, "average_target_count": 18.208333333333332, "target_count_distribution": {"19": 3, "18": 2, "16": 1, "12": 2, "17": 3, "20": 12, "13": 1}}}}