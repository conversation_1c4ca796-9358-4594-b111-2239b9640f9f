=== 实体生成详细日志 ===
开始时间: 2025-07-30 23:24:58


=== 姓名 ===
Latent[网络用语] 第1轮生成:
API原始返回: '王狗蛋  \n李菜菜  \n张隔壁  \n刘老六  \n陈网红  \n孙铁柱  \n赵甜心'
清洗后内容: ['王狗蛋', '李菜菜', '张隔壁', '刘老六', '陈网红', '孙铁柱', '赵甜心']

=== 姓名 ===
Latent[网络用语] 第1轮生成:
生成内容: ['王狗蛋', '李菜菜', '张隔壁', '刘老六', '陈网红', '孙铁柱', '赵甜心']

=== 姓名 ===
Latent[网络用语] 第1轮生成:
生成内容: ['王狗蛋', '李菜菜', '张隔壁', '刘老六', '陈网红', '孙铁柱', '赵甜心']
保留: 7个
过滤: 0个

=== 姓名 ===
Latent[社交场合] 第1轮生成:
API原始返回: '刘洋  \n陈晨  \n赵雪  \n周涛  \n孙悦  \n杨帆  \n吴静'
清洗后内容: ['刘洋', '陈晨', '赵雪', '周涛', '孙悦', '杨帆', '吴静']

=== 姓名 ===
Latent[社交场合] 第1轮生成:
生成内容: ['刘洋', '陈晨', '赵雪', '周涛', '孙悦', '杨帆', '吴静']

=== 姓名 ===
Latent[社交场合] 第1轮生成:
生成内容: ['刘洋', '陈晨', '赵雪', '周涛', '孙悦', '杨帆', '吴静']
保留: 7个
过滤: 0个

=== 姓名 ===
Latent[新闻报道] 第1轮生成:
API原始返回: '刘明轩  \n陈思源  \n杨雨晴  \n赵子豪  \n孙佳怡  \n吴俊杰  \n周雨彤'
清洗后内容: ['刘明轩', '陈思源', '杨雨晴', '赵子豪', '孙佳怡', '吴俊杰', '周雨彤']

=== 姓名 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['刘明轩', '陈思源', '杨雨晴', '赵子豪', '孙佳怡', '吴俊杰', '周雨彤']

=== 姓名 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['刘明轩', '陈思源', '杨雨晴', '赵子豪', '孙佳怡', '吴俊杰', '周雨彤']
保留: 7个
过滤: 0个

=== 姓名 ===
Latent[法律文本] 第1轮生成:
API原始返回: '陈明远  \n刘雅婷  \n吴建国  \n赵启明  \n孙秀英  \n周志强  \n郑惠芳'
清洗后内容: ['陈明远', '刘雅婷', '吴建国', '赵启明', '孙秀英', '周志强', '郑惠芳']

=== 姓名 ===
Latent[法律文本] 第1轮生成:
生成内容: ['陈明远', '刘雅婷', '吴建国', '赵启明', '孙秀英', '周志强', '郑惠芳']

=== 姓名 ===
Latent[法律文本] 第1轮生成:
生成内容: ['陈明远', '刘雅婷', '吴建国', '赵启明', '孙秀英', '周志强', '郑惠芳']
保留: 7个
过滤: 0个

=== 年龄 ===
Latent[社交场合] 第1轮生成:
API原始返回: '三十出头  \n快四十了  \n刚满二十  \n奔五的人  \n二十有八  \n年近花甲  \n三十而立'
清洗后内容: ['三十出头', '快四十了', '刚满二十', '奔五的人', '二十有八', '年近花甲', '三十而立']

=== 年龄 ===
Latent[社交场合] 第1轮生成:
生成内容: ['三十出头', '快四十了', '刚满二十', '奔五的人', '二十有八', '年近花甲', '三十而立']

=== 年龄 ===
Latent[社交场合] 第1轮生成:
生成内容: ['三十出头', '快四十了', '刚满二十', '奔五的人', '二十有八', '年近花甲', '三十而立']
保留: 0个
过滤: 7个
过滤详情:
  - "三十出头" (质量)
  - "快四十了" (质量)
  - "刚满二十" (质量)
  - "奔五的人" (质量)
  - "二十有八" (质量)
  - "年近花甲" (质量)
  - "三十而立" (质量)

=== 年龄 ===
Latent[社交场合] 第2轮生成:
API原始返回: '刚满18  \n快奔四了  \n三十出头  \n年方二十  \n近五十岁  \n二十有五  \n刚过三十'
清洗后内容: ['刚满18', '快奔四了', '三十出头', '年方二十', '近五十岁', '二十有五', '刚过三十']

=== 年龄 ===
Latent[社交场合] 第2轮生成:
生成内容: ['刚满18', '快奔四了', '三十出头', '年方二十', '近五十岁', '二十有五', '刚过三十']

=== 年龄 ===
Latent[社交场合] 第1轮生成:
生成内容: ['刚满18', '快奔四了', '三十出头', '年方二十', '近五十岁', '二十有五', '刚过三十']
保留: 0个
过滤: 7个
过滤详情:
  - "刚满18" (质量)
  - "快奔四了" (质量)
  - "三十出头" (质量)
  - "年方二十" (质量)
  - "近五十岁" (质量)
  - "二十有五" (质量)
  - "刚过三十" (质量)

=== 年龄 ===
Latent[社交场合] 第3轮生成:
API原始返回: '刚满二十三  \n三十有五了  \n快四十的人  \n刚过五十坎  \n不到三十岁  \n奔四的年纪  \n快六十岁了'
清洗后内容: ['刚满二十三', '三十有五了', '快四十的人', '刚过五十坎', '不到三十岁', '奔四的年纪', '快六十岁了']

=== 年龄 ===
Latent[社交场合] 第3轮生成:
生成内容: ['刚满二十三', '三十有五了', '快四十的人', '刚过五十坎', '不到三十岁', '奔四的年纪', '快六十岁了']

=== 年龄 ===
Latent[社交场合] 第1轮生成:
生成内容: ['刚满二十三', '三十有五了', '快四十的人', '刚过五十坎', '不到三十岁', '奔四的年纪', '快六十岁了']
保留: 0个
过滤: 7个
过滤详情:
  - "刚满二十三" (质量)
  - "三十有五了" (质量)
  - "快四十的人" (质量)
  - "刚过五十坎" (质量)
  - "不到三十岁" (质量)
  - "奔四的年纪" (质量)
  - "快六十岁了" (质量)

=== 年龄 ===
Latent[新闻报道] 第1轮生成:
API原始返回: '42岁  \n19岁  \n55岁  \n31岁  \n27岁  \n73岁  \n16岁'
清洗后内容: ['42岁', '19岁', '55岁', '31岁', '27岁', '73岁', '16岁']

=== 年龄 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['42岁', '19岁', '55岁', '31岁', '27岁', '73岁', '16岁']

=== 年龄 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['42岁', '19岁', '55岁', '31岁', '27岁', '73岁', '16岁']
保留: 7个
过滤: 0个

=== 年龄 ===
Latent[医学描述] 第1轮生成:
API原始返回: '45岁男性患者  \n72岁女性患者  \n新生儿期  \n青春期早期  \n围绝经期  \n65岁老年人  \n35岁育龄女性'
清洗后内容: ['45岁男性患者', '72岁女性患者', '新生儿期', '青春期早期', '围绝经期', '65岁老年人', '35岁育龄女性']

=== 年龄 ===
Latent[医学描述] 第1轮生成:
生成内容: ['45岁男性患者', '72岁女性患者', '新生儿期', '青春期早期', '围绝经期', '65岁老年人', '35岁育龄女性']

=== 年龄 ===
Latent[医学描述] 第1轮生成:
生成内容: ['45岁男性患者', '72岁女性患者', '新生儿期', '青春期早期', '围绝经期', '65岁老年人', '35岁育龄女性']
保留: 0个
过滤: 7个
过滤详情:
  - "45岁男性患者" (质量)
  - "72岁女性患者" (质量)
  - "新生儿期" (质量)
  - "青春期早期" (质量)
  - "围绝经期" (质量)
  - "65岁老年人" (质量)
  - "35岁育龄女性" (质量)

=== 年龄 ===
Latent[医学描述] 第2轮生成:
API原始返回: '45岁男性患者  \n72岁女性高龄患者  \n38岁育龄期女性  \n60岁动脉粥样硬化患者  \n28岁早产儿  \n55岁绝经后女性  \n19岁青少年患者'
清洗后内容: ['45岁男性患者', '72岁女性高龄患者', '38岁育龄期女性', '60岁动脉粥样硬化患者', '28岁早产儿', '55岁绝经后女性', '19岁青少年患者']

=== 年龄 ===
Latent[医学描述] 第2轮生成:
生成内容: ['45岁男性患者', '72岁女性高龄患者', '38岁育龄期女性', '60岁动脉粥样硬化患者', '28岁早产儿', '55岁绝经后女性', '19岁青少年患者']

=== 年龄 ===
Latent[医学描述] 第1轮生成:
生成内容: ['45岁男性患者', '72岁女性高龄患者', '38岁育龄期女性', '60岁动脉粥样硬化患者', '28岁早产儿', '55岁绝经后女性', '19岁青少年患者']
保留: 0个
过滤: 7个
过滤详情:
  - "45岁男性患者" (质量)
  - "72岁女性高龄患者" (质量)
  - "38岁育龄期女性" (质量)
  - "60岁动脉粥样硬化患者" (质量)
  - "28岁早产儿" (质量)
  - "55岁绝经后女性" (质量)
  - "19岁青少年患者" (质量)

=== 年龄 ===
Latent[医学描述] 第3轮生成:
API原始返回: '55岁男性患者  \n72岁老年女性  \n28岁育龄女性  \n61岁绝经后女性  \n39岁慢性病患者  \n83岁高龄患者  \n47岁围绝经期女性'
清洗后内容: ['55岁男性患者', '72岁老年女性', '28岁育龄女性', '61岁绝经后女性', '39岁慢性病患者', '83岁高龄患者', '47岁围绝经期女性']

=== 年龄 ===
Latent[医学描述] 第3轮生成:
生成内容: ['55岁男性患者', '72岁老年女性', '28岁育龄女性', '61岁绝经后女性', '39岁慢性病患者', '83岁高龄患者', '47岁围绝经期女性']

=== 年龄 ===
Latent[医学描述] 第1轮生成:
生成内容: ['55岁男性患者', '72岁老年女性', '28岁育龄女性', '61岁绝经后女性', '39岁慢性病患者', '83岁高龄患者', '47岁围绝经期女性']
保留: 0个
过滤: 7个
过滤详情:
  - "55岁男性患者" (质量)
  - "72岁老年女性" (质量)
  - "28岁育龄女性" (质量)
  - "61岁绝经后女性" (质量)
  - "39岁慢性病患者" (质量)
  - "83岁高龄患者" (质量)
  - "47岁围绝经期女性" (质量)

=== 年龄 ===
Latent[法律文本] 第1轮生成:
API原始返回: '十八周岁  \n四十五周岁  \n六十三周岁  \n七十二周岁  \n二十一周岁  \n五十八周岁  \n三十三周岁'
清洗后内容: ['十八周岁', '四十五周岁', '六十三周岁', '七十二周岁', '二十一周岁', '五十八周岁', '三十三周岁']

=== 年龄 ===
Latent[法律文本] 第1轮生成:
生成内容: ['十八周岁', '四十五周岁', '六十三周岁', '七十二周岁', '二十一周岁', '五十八周岁', '三十三周岁']

=== 年龄 ===
Latent[法律文本] 第1轮生成:
生成内容: ['十八周岁', '四十五周岁', '六十三周岁', '七十二周岁', '二十一周岁', '五十八周岁', '三十三周岁']
保留: 7个
过滤: 0个

=== 年龄 ===
Latent[口语表达] 第1轮生成:
API原始返回: '二十出头  \n快三十了  \n奔四的人  \n五十好几  \n六十几了  \n刚满十八  \n三十挂零'
清洗后内容: ['二十出头', '快三十了', '奔四的人', '五十好几', '六十几了', '刚满十八', '三十挂零']

=== 年龄 ===
Latent[口语表达] 第1轮生成:
生成内容: ['二十出头', '快三十了', '奔四的人', '五十好几', '六十几了', '刚满十八', '三十挂零']

=== 年龄 ===
Latent[口语表达] 第1轮生成:
生成内容: ['二十出头', '快三十了', '奔四的人', '五十好几', '六十几了', '刚满十八', '三十挂零']
保留: 0个
过滤: 7个
过滤详情:
  - "二十出头" (质量)
  - "快三十了" (质量)
  - "奔四的人" (质量)
  - "五十好几" (质量)
  - "六十几了" (质量)
  - "刚满十八" (质量)
  - "三十挂零" (质量)

=== 年龄 ===
Latent[口语表达] 第2轮生成:
API原始返回: '二十出头  \n快三十了  \n四十几  \n奔五了  \n刚满十八  \n三十好几  \n小三十岁'
清洗后内容: ['二十出头', '快三十了', '四十几', '奔五了', '刚满十八', '三十好几', '小三十岁']

=== 年龄 ===
Latent[口语表达] 第2轮生成:
生成内容: ['二十出头', '快三十了', '四十几', '奔五了', '刚满十八', '三十好几', '小三十岁']

=== 年龄 ===
Latent[口语表达] 第1轮生成:
生成内容: ['二十出头', '快三十了', '四十几', '奔五了', '刚满十八', '三十好几', '小三十岁']
保留: 0个
过滤: 7个
过滤详情:
  - "二十出头" (质量)
  - "快三十了" (质量)
  - "四十几" (质量)
  - "奔五了" (质量)
  - "刚满十八" (质量)
  - "三十好几" (质量)
  - "小三十岁" (质量)

=== 年龄 ===
Latent[口语表达] 第3轮生成:
API原始返回: '快三十了  \n刚满18  \n奔四了  \n二十出头  \n不到三十  \n快五十了  \n二十好几'
清洗后内容: ['快三十了', '刚满18', '奔四了', '二十出头', '不到三十', '快五十了', '二十好几']

=== 年龄 ===
Latent[口语表达] 第3轮生成:
生成内容: ['快三十了', '刚满18', '奔四了', '二十出头', '不到三十', '快五十了', '二十好几']

=== 年龄 ===
Latent[口语表达] 第1轮生成:
生成内容: ['快三十了', '刚满18', '奔四了', '二十出头', '不到三十', '快五十了', '二十好几']
保留: 0个
过滤: 7个
过滤详情:
  - "快三十了" (质量)
  - "刚满18" (质量)
  - "奔四了" (质量)
  - "二十出头" (质量)
  - "不到三十" (质量)
  - "快五十了" (质量)
  - "二十好几" (质量)

=== 性别 ===
Vanilla 第1轮生成:
API原始返回: '雌雄同体  \n双性人  \n顺性别男性  \n顺性别女性  \n间性人  \n非二元性别者  \n性别流动者  \n性别中立  \n双性别  \n无性别  \n跨性别男性  \n跨性别女性  \n第三性别  \n雌雄同体人  \n性别模糊  \n性别分离'
清洗后内容: ['雌雄同体', '双性人', '顺性别男性', '顺性别女性', '间性人', '非二元性别者', '性别流动者', '性别中立', '双性别', '无性别', '跨性别男性', '跨性别女性', '第三性别', '雌雄同体人', '性别模糊', '性别分离']

=== 性别 ===
Vanilla 第1轮生成:
生成内容: ['雌雄同体', '双性人', '顺性别男性', '顺性别女性', '间性人', '非二元性别者', '性别流动者', '性别中立', '双性别', '无性别', '跨性别男性', '跨性别女性', '第三性别', '雌雄同体人', '性别模糊', '性别分离']

=== 性别 ===
Vanilla 第1轮生成:
生成内容: ['雌雄同体', '双性人', '顺性别男性', '顺性别女性', '间性人', '非二元性别者', '性别流动者', '性别中立', '双性别', '无性别', '跨性别男性', '跨性别女性', '第三性别', '雌雄同体人', '性别模糊', '性别分离']
保留: 16个
过滤: 0个

=== 性别 ===
Latent[新闻报道] 第1轮生成:
API原始返回: '跨性别者'
清洗后内容: ['跨性别者']

=== 性别 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['跨性别者']

=== 性别 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['跨性别者']
保留: 1个
过滤: 0个

=== 性别 ===
Latent[新闻报道] 第2轮生成:
API原始返回: '跨性别者'
清洗后内容: ['跨性别者']

=== 性别 ===
Latent[新闻报道] 第2轮生成:
生成内容: ['跨性别者']

=== 性别 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['跨性别者']
保留: 1个
过滤: 0个

=== 性别 ===
Latent[新闻报道] 第3轮生成:
API原始返回: '跨性别者'
清洗后内容: ['跨性别者']

=== 性别 ===
Latent[新闻报道] 第3轮生成:
生成内容: ['跨性别者']

=== 性别 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['跨性别者']
保留: 1个
过滤: 0个

=== 性别 ===
Latent[法律文本] 第1轮生成:
API原始返回: '男性公民  \n女性公民  \n跨性别者  \n性别中立'
清洗后内容: ['男性公民', '女性公民', '跨性别者', '性别中立']

=== 性别 ===
Latent[法律文本] 第1轮生成:
生成内容: ['男性公民', '女性公民', '跨性别者', '性别中立']

=== 性别 ===
Latent[法律文本] 第1轮生成:
生成内容: ['男性公民', '女性公民', '跨性别者', '性别中立']
保留: 4个
过滤: 0个

=== 性别 ===
Latent[法律文本] 第2轮生成:
API原始返回: '男性当事人  \n女性申请人  \n非二元性别者'
清洗后内容: ['男性当事人', '女性申请人', '非二元性别者']

=== 性别 ===
Latent[法律文本] 第2轮生成:
生成内容: ['男性当事人', '女性申请人', '非二元性别者']

=== 性别 ===
Latent[法律文本] 第1轮生成:
生成内容: ['男性当事人', '女性申请人', '非二元性别者']
保留: 3个
过滤: 0个

=== 国籍 ===
Vanilla 第1轮生成:
API原始返回: '冰岛 \n葡萄牙 \n阿根廷'
清洗后内容: ['冰岛', '葡萄牙', '阿根廷']

=== 国籍 ===
Vanilla 第1轮生成:
生成内容: ['冰岛', '葡萄牙', '阿根廷']

=== 国籍 ===
Vanilla 第1轮生成:
生成内容: ['冰岛', '葡萄牙', '阿根廷']
保留: 2个
过滤: 1个
过滤详情:
  - "冰岛" (质量)

=== 国籍 ===
Latent[新闻报道] 第1轮生成:
API原始返回: '日本  \n英国  \n德国  \n加拿大  \n巴西  \n意大利  \n西班牙  \n俄罗斯  \n印度  \n韩国  \n荷兰  \n瑞士'
清洗后内容: ['日本', '英国', '德国', '加拿大', '巴西', '意大利', '西班牙', '俄罗斯', '印度', '韩国', '荷兰', '瑞士']

=== 国籍 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['日本', '英国', '德国', '加拿大', '巴西', '意大利', '西班牙', '俄罗斯', '印度', '韩国', '荷兰', '瑞士']

=== 国籍 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['日本', '英国', '德国', '加拿大', '巴西', '意大利', '西班牙', '俄罗斯', '印度', '韩国', '荷兰', '瑞士']
保留: 12个
过滤: 0个

=== 国籍 ===
Latent[法律文本] 第1轮生成:
API原始返回: '英国国籍  \n加拿大公民  \n澳大利亚籍  \n新西兰公民  \n德国籍  \n意大利籍  \n西班牙籍  \n荷兰籍  \n瑞典籍  \n挪威籍  \n瑞士籍  \n比利时籍  \n丹麦籍'
清洗后内容: ['英国国籍', '加拿大公民', '澳大利亚籍', '新西兰公民', '德国籍', '意大利籍', '西班牙籍', '荷兰籍', '瑞典籍', '挪威籍', '瑞士籍', '比利时籍', '丹麦籍']

=== 国籍 ===
Latent[法律文本] 第1轮生成:
生成内容: ['英国国籍', '加拿大公民', '澳大利亚籍', '新西兰公民', '德国籍', '意大利籍', '西班牙籍', '荷兰籍', '瑞典籍', '挪威籍', '瑞士籍', '比利时籍', '丹麦籍']

=== 国籍 ===
Latent[法律文本] 第1轮生成:
生成内容: ['英国国籍', '加拿大公民', '澳大利亚籍', '新西兰公民', '德国籍', '意大利籍', '西班牙籍', '荷兰籍', '瑞典籍', '挪威籍', '瑞士籍', '比利时籍', '丹麦籍']
保留: 0个
过滤: 13个
过滤详情:
  - "英国国籍" (质量)
  - "加拿大公民" (质量)
  - "澳大利亚籍" (质量)
  - "新西兰公民" (质量)
  - "德国籍" (质量)
  - "意大利籍" (质量)
  - "西班牙籍" (质量)
  - "荷兰籍" (质量)
  - "瑞典籍" (质量)
  - "挪威籍" (质量)
  - "瑞士籍" (质量)
  - "比利时籍" (质量)
  - "丹麦籍" (质量)

=== 国籍 ===
Latent[法律文本] 第2轮生成:
API原始返回: '英国国籍  \n日本国籍  \n德国国籍  \n加拿大国籍  \n澳大利亚国籍  \n巴西国籍  \n印度国籍  \n俄罗斯国籍  \n意大利国籍  \n西班牙国籍  \n荷兰国籍  \n瑞典国籍  \n韩国国籍'
清洗后内容: ['英国国籍', '日本国籍', '德国国籍', '加拿大国籍', '澳大利亚国籍', '巴西国籍', '印度国籍', '俄罗斯国籍', '意大利国籍', '西班牙国籍', '荷兰国籍', '瑞典国籍', '韩国国籍']

=== 国籍 ===
Latent[法律文本] 第2轮生成:
生成内容: ['英国国籍', '日本国籍', '德国国籍', '加拿大国籍', '澳大利亚国籍', '巴西国籍', '印度国籍', '俄罗斯国籍', '意大利国籍', '西班牙国籍', '荷兰国籍', '瑞典国籍', '韩国国籍']

=== 国籍 ===
Latent[法律文本] 第1轮生成:
生成内容: ['英国国籍', '日本国籍', '德国国籍', '加拿大国籍', '澳大利亚国籍', '巴西国籍', '印度国籍', '俄罗斯国籍', '意大利国籍', '西班牙国籍', '荷兰国籍', '瑞典国籍', '韩国国籍']
保留: 0个
过滤: 13个
过滤详情:
  - "英国国籍" (质量)
  - "日本国籍" (质量)
  - "德国国籍" (质量)
  - "加拿大国籍" (质量)
  - "澳大利亚国籍" (质量)
  - "巴西国籍" (质量)
  - "印度国籍" (质量)
  - "俄罗斯国籍" (质量)
  - "意大利国籍" (质量)
  - "西班牙国籍" (质量)
  - "荷兰国籍" (质量)
  - "瑞典国籍" (质量)
  - "韩国国籍" (质量)

=== 国籍 ===
Latent[法律文本] 第3轮生成:
API原始返回: '英国国籍  \n加拿大公民  \n澳大利亚籍  \n新西兰国民  \n德国公民  \n意大利国籍  \n西班牙籍  \n荷兰国籍  \n瑞典公民  \n瑞士国籍  \n比利时公民  \n丹麦国籍  \n葡萄牙籍'
清洗后内容: ['英国国籍', '加拿大公民', '澳大利亚籍', '新西兰国民', '德国公民', '意大利国籍', '西班牙籍', '荷兰国籍', '瑞典公民', '瑞士国籍', '比利时公民', '丹麦国籍', '葡萄牙籍']

=== 国籍 ===
Latent[法律文本] 第3轮生成:
生成内容: ['英国国籍', '加拿大公民', '澳大利亚籍', '新西兰国民', '德国公民', '意大利国籍', '西班牙籍', '荷兰国籍', '瑞典公民', '瑞士国籍', '比利时公民', '丹麦国籍', '葡萄牙籍']

=== 国籍 ===
Latent[法律文本] 第1轮生成:
生成内容: ['英国国籍', '加拿大公民', '澳大利亚籍', '新西兰国民', '德国公民', '意大利国籍', '西班牙籍', '荷兰国籍', '瑞典公民', '瑞士国籍', '比利时公民', '丹麦国籍', '葡萄牙籍']
保留: 0个
过滤: 13个
过滤详情:
  - "英国国籍" (质量)
  - "加拿大公民" (质量)
  - "澳大利亚籍" (质量)
  - "新西兰国民" (质量)
  - "德国公民" (质量)
  - "意大利国籍" (质量)
  - "西班牙籍" (质量)
  - "荷兰国籍" (质量)
  - "瑞典公民" (质量)
  - "瑞士国籍" (质量)
  - "比利时公民" (质量)
  - "丹麦国籍" (质量)
  - "葡萄牙籍" (质量)

=== 国籍 ===
Latent[学术论文] 第1轮生成:
API原始返回: '加拿大籍  \n澳大利亚人  \n德国公民  \n英国国民  \n日本裔  \n俄罗斯籍  \n印度公民  \n巴西国籍  \n西班牙裔  \n荷兰人  \n瑞士籍  \n意大利国民'
清洗后内容: ['加拿大籍', '澳大利亚人', '德国公民', '英国国民', '日本裔', '俄罗斯籍', '印度公民', '巴西国籍', '西班牙裔', '荷兰人', '瑞士籍', '意大利国民']

=== 国籍 ===
Latent[学术论文] 第1轮生成:
生成内容: ['加拿大籍', '澳大利亚人', '德国公民', '英国国民', '日本裔', '俄罗斯籍', '印度公民', '巴西国籍', '西班牙裔', '荷兰人', '瑞士籍', '意大利国民']

=== 国籍 ===
Latent[学术论文] 第1轮生成:
生成内容: ['加拿大籍', '澳大利亚人', '德国公民', '英国国民', '日本裔', '俄罗斯籍', '印度公民', '巴西国籍', '西班牙裔', '荷兰人', '瑞士籍', '意大利国民']
保留: 0个
过滤: 12个
过滤详情:
  - "加拿大籍" (质量)
  - "澳大利亚人" (质量)
  - "德国公民" (质量)
  - "英国国民" (质量)
  - "日本裔" (质量)
  - "俄罗斯籍" (质量)
  - "印度公民" (质量)
  - "巴西国籍" (质量)
  - "西班牙裔" (质量)
  - "荷兰人" (质量)
  - "瑞士籍" (质量)
  - "意大利国民" (质量)

=== 国籍 ===
Latent[学术论文] 第2轮生成:
API原始返回: '英国国籍  \n加拿大籍  \n澳大利亚籍  \n新西兰籍  \n德国籍  \n法国籍  \n日本籍  \n韩国籍  \n俄罗斯籍  \n意大利籍  \n西班牙籍  \n巴西籍'
清洗后内容: ['英国国籍', '加拿大籍', '澳大利亚籍', '新西兰籍', '德国籍', '法国籍', '日本籍', '韩国籍', '俄罗斯籍', '意大利籍', '西班牙籍', '巴西籍']

=== 国籍 ===
Latent[学术论文] 第2轮生成:
生成内容: ['英国国籍', '加拿大籍', '澳大利亚籍', '新西兰籍', '德国籍', '法国籍', '日本籍', '韩国籍', '俄罗斯籍', '意大利籍', '西班牙籍', '巴西籍']

=== 国籍 ===
Latent[学术论文] 第1轮生成:
生成内容: ['英国国籍', '加拿大籍', '澳大利亚籍', '新西兰籍', '德国籍', '法国籍', '日本籍', '韩国籍', '俄罗斯籍', '意大利籍', '西班牙籍', '巴西籍']
保留: 0个
过滤: 12个
过滤详情:
  - "英国国籍" (质量)
  - "加拿大籍" (质量)
  - "澳大利亚籍" (质量)
  - "新西兰籍" (质量)
  - "德国籍" (质量)
  - "法国籍" (质量)
  - "日本籍" (质量)
  - "韩国籍" (质量)
  - "俄罗斯籍" (质量)
  - "意大利籍" (质量)
  - "西班牙籍" (质量)
  - "巴西籍" (质量)

=== 国籍 ===
Latent[学术论文] 第3轮生成:
API原始返回: '英国国籍  \n澳大利亚公民  \n加拿大籍  \n德国籍  \n法国籍  \n日本国籍  \n韩国国籍  \n俄罗斯籍  \n巴西国籍  \n西班牙籍  \n意大利籍  \n荷兰籍'
清洗后内容: ['英国国籍', '澳大利亚公民', '加拿大籍', '德国籍', '法国籍', '日本国籍', '韩国国籍', '俄罗斯籍', '巴西国籍', '西班牙籍', '意大利籍', '荷兰籍']

=== 国籍 ===
Latent[学术论文] 第3轮生成:
生成内容: ['英国国籍', '澳大利亚公民', '加拿大籍', '德国籍', '法国籍', '日本国籍', '韩国国籍', '俄罗斯籍', '巴西国籍', '西班牙籍', '意大利籍', '荷兰籍']

=== 国籍 ===
Latent[学术论文] 第1轮生成:
生成内容: ['英国国籍', '澳大利亚公民', '加拿大籍', '德国籍', '法国籍', '日本国籍', '韩国国籍', '俄罗斯籍', '巴西国籍', '西班牙籍', '意大利籍', '荷兰籍']
保留: 0个
过滤: 12个
过滤详情:
  - "英国国籍" (质量)
  - "澳大利亚公民" (质量)
  - "加拿大籍" (质量)
  - "德国籍" (质量)
  - "法国籍" (质量)
  - "日本国籍" (质量)
  - "韩国国籍" (质量)
  - "俄罗斯籍" (质量)
  - "巴西国籍" (质量)
  - "西班牙籍" (质量)
  - "意大利籍" (质量)
  - "荷兰籍" (质量)

=== 职业 ===
Latent[社交场合] 第1轮生成:
API原始返回: '活动策划师  \n派对主持人  \n婚庆顾问  \n社交秘书  \n茶话会组织者  \n酒会招待员  \n商务社交顾问  \n社区活动策划  \n派对协调员'
清洗后内容: ['活动策划师', '派对主持人', '婚庆顾问', '社交秘书', '茶话会组织者', '酒会招待员', '商务社交顾问', '社区活动策划', '派对协调员']

=== 职业 ===
Latent[社交场合] 第1轮生成:
生成内容: ['活动策划师', '派对主持人', '婚庆顾问', '社交秘书', '茶话会组织者', '酒会招待员', '商务社交顾问', '社区活动策划', '派对协调员']

=== 职业 ===
Latent[社交场合] 第1轮生成:
生成内容: ['活动策划师', '派对主持人', '婚庆顾问', '社交秘书', '茶话会组织者', '酒会招待员', '商务社交顾问', '社区活动策划', '派对协调员']
保留: 9个
过滤: 0个

=== 职业 ===
Latent[新闻报道] 第1轮生成:
API原始返回: '财经记者  \n战地摄影师  \n数据分析师  \n调查记者  \n体育评论员  \n网络编辑  \n纪录片导演  \n特稿撰稿人  \n新闻主播'
清洗后内容: ['财经记者', '战地摄影师', '数据分析师', '调查记者', '体育评论员', '网络编辑', '纪录片导演', '特稿撰稿人', '新闻主播']

=== 职业 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['财经记者', '战地摄影师', '数据分析师', '调查记者', '体育评论员', '网络编辑', '纪录片导演', '特稿撰稿人', '新闻主播']

=== 职业 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['财经记者', '战地摄影师', '数据分析师', '调查记者', '体育评论员', '网络编辑', '纪录片导演', '特稿撰稿人', '新闻主播']
保留: 9个
过滤: 0个

=== 职业 ===
Latent[法律文本] 第1轮生成:
API原始返回: '合同审核专员  \n法律文件起草人  \n合规审查员  \n诉讼代理人  \n知识产权专员  \n法律顾问助理  \n司法鉴定人员  \n仲裁事务专员  \n法律文本校对员'
清洗后内容: ['合同审核专员', '法律文件起草人', '合规审查员', '诉讼代理人', '知识产权专员', '法律顾问助理', '司法鉴定人员', '仲裁事务专员', '法律文本校对员']

=== 职业 ===
Latent[法律文本] 第1轮生成:
生成内容: ['合同审核专员', '法律文件起草人', '合规审查员', '诉讼代理人', '知识产权专员', '法律顾问助理', '司法鉴定人员', '仲裁事务专员', '法律文本校对员']

=== 职业 ===
Latent[法律文本] 第1轮生成:
生成内容: ['合同审核专员', '法律文件起草人', '合规审查员', '诉讼代理人', '知识产权专员', '法律顾问助理', '司法鉴定人员', '仲裁事务专员', '法律文本校对员']
保留: 8个
过滤: 1个
过滤详情:
  - "司法鉴定人员" (抽象)

=== 民族 ===
Latent[新闻报道] 第1轮生成:
API原始返回: '土家族  \n维吾尔族  \n苗族  \n彝族  \n壮族  \n满族  \n侗族  \n瑶族  \n蒙古族  \n白族  \n朝鲜族  \n傣族'
清洗后内容: ['土家族', '维吾尔族', '苗族', '彝族', '壮族', '满族', '侗族', '瑶族', '蒙古族', '白族', '朝鲜族', '傣族']

=== 民族 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['土家族', '维吾尔族', '苗族', '彝族', '壮族', '满族', '侗族', '瑶族', '蒙古族', '白族', '朝鲜族', '傣族']

=== 民族 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['土家族', '维吾尔族', '苗族', '彝族', '壮族', '满族', '侗族', '瑶族', '蒙古族', '白族', '朝鲜族', '傣族']
保留: 12个
过滤: 0个

=== 民族 ===
Latent[新闻报道] 第2轮生成:
API原始返回: '维吾尔族  \n蒙古族  \n朝鲜族  \n傣族  \n纳西族  \n白族  \n苗族  \n彝族  \n壮族  \n土家族  \n羌族  \n布依族'
清洗后内容: ['维吾尔族', '蒙古族', '朝鲜族', '傣族', '纳西族', '白族', '苗族', '彝族', '壮族', '土家族', '羌族', '布依族']

=== 民族 ===
Latent[新闻报道] 第2轮生成:
生成内容: ['维吾尔族', '蒙古族', '朝鲜族', '傣族', '纳西族', '白族', '苗族', '彝族', '壮族', '土家族', '羌族', '布依族']

=== 民族 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['维吾尔族', '蒙古族', '朝鲜族', '傣族', '纳西族', '白族', '苗族', '彝族', '壮族', '土家族', '羌族', '布依族']
保留: 12个
过滤: 0个

=== 民族 ===
Latent[新闻报道] 第3轮生成:
API原始返回: '傣族  \n白族  \n哈尼族  \n傈僳族  \n佤族  \n拉祜族  \n水族  \n东乡族  \n景颇族'
清洗后内容: ['傣族', '白族', '哈尼族', '傈僳族', '佤族', '拉祜族', '水族', '东乡族', '景颇族']

=== 民族 ===
Latent[新闻报道] 第3轮生成:
生成内容: ['傣族', '白族', '哈尼族', '傈僳族', '佤族', '拉祜族', '水族', '东乡族', '景颇族']

=== 民族 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['傣族', '白族', '哈尼族', '傈僳族', '佤族', '拉祜族', '水族', '东乡族', '景颇族']
保留: 9个
过滤: 0个

=== 民族 ===
Latent[法律文本] 第1轮生成:
API原始返回: '朝鲜族  \n蒙古族  \n维吾尔族  \n苗族  \n彝族  \n壮族  \n满族  \n侗族  \n瑶族  \n白族  \n土家族  \n哈尼族'
清洗后内容: ['朝鲜族', '蒙古族', '维吾尔族', '苗族', '彝族', '壮族', '满族', '侗族', '瑶族', '白族', '土家族', '哈尼族']

=== 民族 ===
Latent[法律文本] 第1轮生成:
生成内容: ['朝鲜族', '蒙古族', '维吾尔族', '苗族', '彝族', '壮族', '满族', '侗族', '瑶族', '白族', '土家族', '哈尼族']

=== 民族 ===
Latent[法律文本] 第1轮生成:
生成内容: ['朝鲜族', '蒙古族', '维吾尔族', '苗族', '彝族', '壮族', '满族', '侗族', '瑶族', '白族', '土家族', '哈尼族']
保留: 12个
过滤: 0个

=== 民族 ===
Latent[法律文本] 第2轮生成:
API原始返回: '朝鲜族  \n蒙古族  \n维吾尔族  \n哈萨克族  \n彝族  \n苗族  \n壮族  \n瑶族  \n侗族  \n土家族  \n傣族  \n畲族'
清洗后内容: ['朝鲜族', '蒙古族', '维吾尔族', '哈萨克族', '彝族', '苗族', '壮族', '瑶族', '侗族', '土家族', '傣族', '畲族']

=== 民族 ===
Latent[法律文本] 第2轮生成:
生成内容: ['朝鲜族', '蒙古族', '维吾尔族', '哈萨克族', '彝族', '苗族', '壮族', '瑶族', '侗族', '土家族', '傣族', '畲族']

=== 民族 ===
Latent[法律文本] 第1轮生成:
生成内容: ['朝鲜族', '蒙古族', '维吾尔族', '哈萨克族', '彝族', '苗族', '壮族', '瑶族', '侗族', '土家族', '傣族', '畲族']
保留: 12个
过滤: 0个

=== 民族 ===
Latent[法律文本] 第3轮生成:
API原始返回: '朝鲜族  \n维吾尔族  \n蒙古族  \n彝族  \n苗族  \n壮族  \n满族  \n侗族  \n瑶族  \n白族  \n土家族  \n哈尼族'
清洗后内容: ['朝鲜族', '维吾尔族', '蒙古族', '彝族', '苗族', '壮族', '满族', '侗族', '瑶族', '白族', '土家族', '哈尼族']

=== 民族 ===
Latent[法律文本] 第3轮生成:
生成内容: ['朝鲜族', '维吾尔族', '蒙古族', '彝族', '苗族', '壮族', '满族', '侗族', '瑶族', '白族', '土家族', '哈尼族']

=== 民族 ===
Latent[法律文本] 第1轮生成:
生成内容: ['朝鲜族', '维吾尔族', '蒙古族', '彝族', '苗族', '壮族', '满族', '侗族', '瑶族', '白族', '土家族', '哈尼族']
保留: 12个
过滤: 0个

=== 民族 ===
Latent[学术论文] 第1轮生成:
API原始返回: '赫哲族  \n鄂伦春族  \n鄂温克族  \n达斡尔族  \n塔吉克族  \n乌孜别克族  \n俄罗斯族  \n裕固族  \n保安族  \n塔塔尔族  \n德昂族  \n基诺族'
清洗后内容: ['赫哲族', '鄂伦春族', '鄂温克族', '达斡尔族', '塔吉克族', '乌孜别克族', '俄罗斯族', '裕固族', '保安族', '塔塔尔族', '德昂族', '基诺族']

=== 民族 ===
Latent[学术论文] 第1轮生成:
生成内容: ['赫哲族', '鄂伦春族', '鄂温克族', '达斡尔族', '塔吉克族', '乌孜别克族', '俄罗斯族', '裕固族', '保安族', '塔塔尔族', '德昂族', '基诺族']

=== 民族 ===
Latent[学术论文] 第1轮生成:
生成内容: ['赫哲族', '鄂伦春族', '鄂温克族', '达斡尔族', '塔吉克族', '乌孜别克族', '俄罗斯族', '裕固族', '保安族', '塔塔尔族', '德昂族', '基诺族']
保留: 12个
过滤: 0个

=== 教育背景 ===
Latent[社交场合] 第1轮生成:
API原始返回: '清华读的本科  \n北大硕士毕业  \n复旦读博中  \n人大法学院毕业  \n浙大计算机系  \n武大新闻专业  \n上海交大医学院  \n南大读了个硕士  \n北师大教育学  \n同济建筑专业'
清洗后内容: ['清华读的本科', '北大硕士毕业', '复旦读博中', '人大法学院毕业', '浙大计算机系', '武大新闻专业', '上海交大医学院', '南大读了个硕士', '北师大教育学', '同济建筑专业']

=== 教育背景 ===
Latent[社交场合] 第1轮生成:
生成内容: ['清华读的本科', '北大硕士毕业', '复旦读博中', '人大法学院毕业', '浙大计算机系', '武大新闻专业', '上海交大医学院', '南大读了个硕士', '北师大教育学', '同济建筑专业']

=== 教育背景 ===
Latent[社交场合] 第1轮生成:
生成内容: ['清华读的本科', '北大硕士毕业', '复旦读博中', '人大法学院毕业', '浙大计算机系', '武大新闻专业', '上海交大医学院', '南大读了个硕士', '北师大教育学', '同济建筑专业']
保留: 10个
过滤: 0个

=== 教育背景 ===
Latent[新闻报道] 第1轮生成:
API原始返回: '清华大学计算机科学与技术专业博士  \n北京大学光华管理学院硕士  \n复旦大学新闻传播学学士  \n上海交通大学电子信息专业硕士  \n浙江大学控制科学与工程博士  \n中国人民大学法学院本科  \n南京大学国际关系学院硕士  \n武汉大学水利水电工程专业博士  \n中山大学岭南学院经济学硕士  \n北京师范大学教育学博士'
清洗后内容: ['清华大学计算机科学与技术专业博士', '北京大学光华管理学院硕士', '复旦大学新闻传播学学士', '上海交通大学电子信息专业硕士', '浙江大学控制科学与工程博士', '中国人民大学法学院本科', '南京大学国际关系学院硕士', '武汉大学水利水电工程专业博士', '中山大学岭南学院经济学硕士', '北京师范大学教育学博士']

=== 教育背景 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['清华大学计算机科学与技术专业博士', '北京大学光华管理学院硕士', '复旦大学新闻传播学学士', '上海交通大学电子信息专业硕士', '浙江大学控制科学与工程博士', '中国人民大学法学院本科', '南京大学国际关系学院硕士', '武汉大学水利水电工程专业博士', '中山大学岭南学院经济学硕士', '北京师范大学教育学博士']

=== 教育背景 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['清华大学计算机科学与技术专业博士', '北京大学光华管理学院硕士', '复旦大学新闻传播学学士', '上海交通大学电子信息专业硕士', '浙江大学控制科学与工程博士', '中国人民大学法学院本科', '南京大学国际关系学院硕士', '武汉大学水利水电工程专业博士', '中山大学岭南学院经济学硕士', '北京师范大学教育学博士']
保留: 9个
过滤: 1个
过滤详情:
  - "上海交通大学电子信息专业硕士" (抽象)

=== 教育背景 ===
Latent[法律文本] 第1轮生成:
API原始返回: '法学学士学位  \n法学硕士学位  \n法律专业博士  \n法律职业资格  \n法学研究生学历  \n法律专业本科  \n法学专业硕士  \n法律专业博士研究生  \n法学专业学士学位  \n法律专业硕士学位'
清洗后内容: ['法学学士学位', '法学硕士学位', '法律专业博士', '法律职业资格', '法学研究生学历', '法律专业本科', '法学专业硕士', '法律专业博士研究生', '法学专业学士学位', '法律专业硕士学位']

=== 教育背景 ===
Latent[法律文本] 第1轮生成:
生成内容: ['法学学士学位', '法学硕士学位', '法律专业博士', '法律职业资格', '法学研究生学历', '法律专业本科', '法学专业硕士', '法律专业博士研究生', '法学专业学士学位', '法律专业硕士学位']

=== 教育背景 ===
Latent[法律文本] 第1轮生成:
生成内容: ['法学学士学位', '法学硕士学位', '法律专业博士', '法律职业资格', '法学研究生学历', '法律专业本科', '法学专业硕士', '法律专业博士研究生', '法学专业学士学位', '法律专业硕士学位']
保留: 10个
过滤: 0个

=== 婚姻状况 ===
Latent[社交场合] 第1轮生成:
API原始返回: '单身贵族  \n恋爱中  \n闪婚族  \n丁克夫妻  \n分居状态  \n再婚人士  \n未婚妈妈  \n同居伴侣  \n丧偶状态  \n热恋期'
清洗后内容: ['单身贵族', '恋爱中', '闪婚族', '丁克夫妻', '分居状态', '再婚人士', '未婚妈妈', '同居伴侣', '丧偶状态', '热恋期']

=== 婚姻状况 ===
Latent[社交场合] 第1轮生成:
生成内容: ['单身贵族', '恋爱中', '闪婚族', '丁克夫妻', '分居状态', '再婚人士', '未婚妈妈', '同居伴侣', '丧偶状态', '热恋期']

=== 婚姻状况 ===
Latent[社交场合] 第1轮生成:
生成内容: ['单身贵族', '恋爱中', '闪婚族', '丁克夫妻', '分居状态', '再婚人士', '未婚妈妈', '同居伴侣', '丧偶状态', '热恋期']
保留: 8个
过滤: 2个
过滤详情:
  - "分居状态" (抽象)
  - "丧偶状态" (抽象)

=== 婚姻状况 ===
Latent[法律文本] 第1轮生成:
API原始返回: '丧偶状态  \n分居状态  \n同居关系  \n单身状态  \n复婚状态  \n未婚同居  \n协议离婚  \n事实婚姻  \n解除婚约  \n再婚状态'
清洗后内容: ['丧偶状态', '分居状态', '同居关系', '单身状态', '复婚状态', '未婚同居', '协议离婚', '事实婚姻', '解除婚约', '再婚状态']

=== 婚姻状况 ===
Latent[法律文本] 第1轮生成:
生成内容: ['丧偶状态', '分居状态', '同居关系', '单身状态', '复婚状态', '未婚同居', '协议离婚', '事实婚姻', '解除婚约', '再婚状态']

=== 婚姻状况 ===
Latent[法律文本] 第1轮生成:
生成内容: ['丧偶状态', '分居状态', '同居关系', '单身状态', '复婚状态', '未婚同居', '协议离婚', '事实婚姻', '解除婚约', '再婚状态']
保留: 5个
过滤: 5个
过滤详情:
  - "丧偶状态" (抽象)
  - "分居状态" (抽象)
  - "单身状态" (抽象)
  - "复婚状态" (抽象)
  - "再婚状态" (抽象)

=== 婚姻状况 ===
Latent[法律文本] 第2轮生成:
API原始返回: '丧偶状态  \n婚姻存续  \n分居状态'
清洗后内容: ['丧偶状态', '婚姻存续', '分居状态']

=== 婚姻状况 ===
Latent[法律文本] 第2轮生成:
生成内容: ['丧偶状态', '婚姻存续', '分居状态']

=== 婚姻状况 ===
Latent[法律文本] 第1轮生成:
生成内容: ['丧偶状态', '婚姻存续', '分居状态']
保留: 1个
过滤: 2个
过滤详情:
  - "丧偶状态" (抽象)
  - "分居状态" (抽象)

=== 婚姻状况 ===
Latent[法律文本] 第3轮生成:
API原始返回: '登记结婚'
清洗后内容: ['登记结婚']

=== 婚姻状况 ===
Latent[法律文本] 第3轮生成:
生成内容: ['登记结婚']

=== 婚姻状况 ===
Latent[法律文本] 第1轮生成:
生成内容: ['登记结婚']
保留: 1个
过滤: 0个

=== 婚姻状况 ===
Latent[新闻报道] 第1轮生成:
API原始返回: '单身人士  \n未婚状态  \n离异无子女  \n丧偶独居  \n再婚家庭  \n同居伴侣  \n离异带娃  \n未婚母亲  \n丧偶老人  \n未婚父亲'
清洗后内容: ['单身人士', '未婚状态', '离异无子女', '丧偶独居', '再婚家庭', '同居伴侣', '离异带娃', '未婚母亲', '丧偶老人', '未婚父亲']

=== 婚姻状况 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['单身人士', '未婚状态', '离异无子女', '丧偶独居', '再婚家庭', '同居伴侣', '离异带娃', '未婚母亲', '丧偶老人', '未婚父亲']

=== 婚姻状况 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['单身人士', '未婚状态', '离异无子女', '丧偶独居', '再婚家庭', '同居伴侣', '离异带娃', '未婚母亲', '丧偶老人', '未婚父亲']
保留: 9个
过滤: 1个
过滤详情:
  - "未婚状态" (抽象)

=== 婚姻状况 ===
Latent[口语表达] 第1轮生成:
API原始返回: '单身狗  \n没对象  \n刚离婚  \n结了婚  \n离过婚  \n没结婚  \n对象没了  \n正单身  \n已婚状态  \n离异单身'
清洗后内容: ['单身狗', '没对象', '刚离婚', '结了婚', '离过婚', '没结婚', '对象没了', '正单身', '已婚状态', '离异单身']

=== 婚姻状况 ===
Latent[口语表达] 第1轮生成:
生成内容: ['单身狗', '没对象', '刚离婚', '结了婚', '离过婚', '没结婚', '对象没了', '正单身', '已婚状态', '离异单身']

=== 婚姻状况 ===
Latent[口语表达] 第1轮生成:
生成内容: ['单身狗', '没对象', '刚离婚', '结了婚', '离过婚', '没结婚', '对象没了', '正单身', '已婚状态', '离异单身']
保留: 7个
过滤: 3个
过滤详情:
  - "没对象" (抽象)
  - "对象没了" (抽象)
  - "已婚状态" (抽象)

=== 政治倾向 ===
Latent[新闻报道] 第1轮生成:
API原始返回: '进步主义者  \n自由主义者  \n温和保守派  \n社会民主派  \n中间派人士  \n极右翼分子  \n左翼活动家  \n新保守主义者  \n自由保守派  \n社会自由派  \n民族主义者  \n社会保守派  \n极左翼分子'
清洗后内容: ['进步主义者', '自由主义者', '温和保守派', '社会民主派', '中间派人士', '极右翼分子', '左翼活动家', '新保守主义者', '自由保守派', '社会自由派', '民族主义者', '社会保守派', '极左翼分子']

=== 政治倾向 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['进步主义者', '自由主义者', '温和保守派', '社会民主派', '中间派人士', '极右翼分子', '左翼活动家', '新保守主义者', '自由保守派', '社会自由派', '民族主义者', '社会保守派', '极左翼分子']

=== 政治倾向 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['进步主义者', '自由主义者', '温和保守派', '社会民主派', '中间派人士', '极右翼分子', '左翼活动家', '新保守主义者', '自由保守派', '社会自由派', '民族主义者', '社会保守派', '极左翼分子']
保留: 13个
过滤: 0个

=== 政治倾向 ===
Latent[法律文本] 第1轮生成:
API原始返回: '左翼政治立场  \n右翼政治立场  \n中间政治立场  \n激进政治倾向  \n温和政治倾向  \n进步政治立场  \n保守政治倾向  \n自由政治倾向  \n社会民主主义  \n经济自由主义  \n社会保守主义  \n新自由主义  \n新保守主义'
清洗后内容: ['左翼政治立场', '右翼政治立场', '中间政治立场', '激进政治倾向', '温和政治倾向', '进步政治立场', '保守政治倾向', '自由政治倾向', '社会民主主义', '经济自由主义', '社会保守主义', '新自由主义', '新保守主义']

=== 政治倾向 ===
Latent[法律文本] 第1轮生成:
生成内容: ['左翼政治立场', '右翼政治立场', '中间政治立场', '激进政治倾向', '温和政治倾向', '进步政治立场', '保守政治倾向', '自由政治倾向', '社会民主主义', '经济自由主义', '社会保守主义', '新自由主义', '新保守主义']

=== 政治倾向 ===
Latent[法律文本] 第1轮生成:
生成内容: ['左翼政治立场', '右翼政治立场', '中间政治立场', '激进政治倾向', '温和政治倾向', '进步政治立场', '保守政治倾向', '自由政治倾向', '社会民主主义', '经济自由主义', '社会保守主义', '新自由主义', '新保守主义']
保留: 13个
过滤: 0个

=== 政治倾向 ===
Latent[学术论文] 第1轮生成:
API原始返回: '社会民主主义倾向  \n自由保守主义倾向  \n激进社会主义倾向  \n新自由主义倾向  \n传统保守主义倾向  \n绿党政治倾向  \n进步主义倾向  \n社会保守主义倾向  \n自由主义倾向  \n民族主义倾向  \n共和主义倾向  \n民主社会主义倾向  \n中间偏左倾向'
清洗后内容: ['社会民主主义倾向', '自由保守主义倾向', '激进社会主义倾向', '新自由主义倾向', '传统保守主义倾向', '绿党政治倾向', '进步主义倾向', '社会保守主义倾向', '自由主义倾向', '民族主义倾向', '共和主义倾向', '民主社会主义倾向', '中间偏左倾向']

=== 政治倾向 ===
Latent[学术论文] 第1轮生成:
生成内容: ['社会民主主义倾向', '自由保守主义倾向', '激进社会主义倾向', '新自由主义倾向', '传统保守主义倾向', '绿党政治倾向', '进步主义倾向', '社会保守主义倾向', '自由主义倾向', '民族主义倾向', '共和主义倾向', '民主社会主义倾向', '中间偏左倾向']

=== 政治倾向 ===
Latent[学术论文] 第1轮生成:
生成内容: ['社会民主主义倾向', '自由保守主义倾向', '激进社会主义倾向', '新自由主义倾向', '传统保守主义倾向', '绿党政治倾向', '进步主义倾向', '社会保守主义倾向', '自由主义倾向', '民族主义倾向', '共和主义倾向', '民主社会主义倾向', '中间偏左倾向']
保留: 13个
过滤: 0个

=== 家庭成员 ===
Vanilla 第1轮生成:
API原始返回: '祖父'
清洗后内容: ['祖父']

=== 家庭成员 ===
Vanilla 第1轮生成:
生成内容: ['祖父']

=== 家庭成员 ===
Vanilla 第1轮生成:
生成内容: ['祖父']
保留: 1个
过滤: 0个

=== 家庭成员 ===
Vanilla 第2轮生成:
API原始返回: '小姨'
清洗后内容: ['小姨']

=== 家庭成员 ===
Vanilla 第2轮生成:
生成内容: ['小姨']

=== 家庭成员 ===
Vanilla 第1轮生成:
生成内容: ['小姨']
保留: 0个
过滤: 1个
过滤详情:
  - "小姨" (质量)

=== 家庭成员 ===
Vanilla 第3轮生成:
API原始返回: '表姐'
清洗后内容: ['表姐']

=== 家庭成员 ===
Vanilla 第3轮生成:
生成内容: ['表姐']

=== 家庭成员 ===
Vanilla 第1轮生成:
生成内容: ['表姐']
保留: 1个
过滤: 0个

=== 家庭成员 ===
Latent[社交场合] 第1轮生成:
API原始返回: '小叔子  \n表弟媳  \n大舅哥  \n小姨子  \n亲家母  \n堂哥  \n连襟  \n内侄  \n亲家公  \n小姑子  \n表姐夫  \n大姨子'
清洗后内容: ['小叔子', '表弟媳', '大舅哥', '小姨子', '亲家母', '堂哥', '连襟', '内侄', '亲家公', '小姑子', '表姐夫', '大姨子']

=== 家庭成员 ===
Latent[社交场合] 第1轮生成:
生成内容: ['小叔子', '表弟媳', '大舅哥', '小姨子', '亲家母', '堂哥', '连襟', '内侄', '亲家公', '小姑子', '表姐夫', '大姨子']

=== 家庭成员 ===
Latent[社交场合] 第1轮生成:
生成内容: ['小叔子', '表弟媳', '大舅哥', '小姨子', '亲家母', '堂哥', '连襟', '内侄', '亲家公', '小姑子', '表姐夫', '大姨子']
保留: 0个
过滤: 12个
过滤详情:
  - "小叔子" (质量)
  - "表弟媳" (质量)
  - "大舅哥" (质量)
  - "小姨子" (质量)
  - "亲家母" (质量)
  - "堂哥" (质量)
  - "连襟" (质量)
  - "内侄" (质量)
  - "亲家公" (质量)
  - "小姑子" (质量)
  - "表姐夫" (质量)
  - "大姨子" (质量)

=== 家庭成员 ===
Latent[社交场合] 第2轮生成:
API原始返回: '小姨子  \n堂哥  \n亲家母  \n二舅  \n表妹夫  \n大侄子  \n三叔公  \n小姑子  \n亲家公  \n大伯子  \n小叔子  \n大姨姐'
清洗后内容: ['小姨子', '堂哥', '亲家母', '二舅', '表妹夫', '大侄子', '三叔公', '小姑子', '亲家公', '大伯子', '小叔子', '大姨姐']

=== 家庭成员 ===
Latent[社交场合] 第2轮生成:
生成内容: ['小姨子', '堂哥', '亲家母', '二舅', '表妹夫', '大侄子', '三叔公', '小姑子', '亲家公', '大伯子', '小叔子', '大姨姐']

=== 家庭成员 ===
Latent[社交场合] 第1轮生成:
生成内容: ['小姨子', '堂哥', '亲家母', '二舅', '表妹夫', '大侄子', '三叔公', '小姑子', '亲家公', '大伯子', '小叔子', '大姨姐']
保留: 0个
过滤: 12个
过滤详情:
  - "小姨子" (质量)
  - "堂哥" (质量)
  - "亲家母" (质量)
  - "二舅" (质量)
  - "表妹夫" (质量)
  - "大侄子" (质量)
  - "三叔公" (质量)
  - "小姑子" (质量)
  - "亲家公" (质量)
  - "大伯子" (质量)
  - "小叔子" (质量)
  - "大姨姐" (质量)

=== 家庭成员 ===
Latent[社交场合] 第3轮生成:
API原始返回: '表哥  \n小叔子  \n大姑姐  \n小舅子  \n亲家母  \n亲家公  \n小姨子  \n大伯子  \n堂弟媳  \n表姐夫  \n堂哥  \n小叔媳妇'
清洗后内容: ['表哥', '小叔子', '大姑姐', '小舅子', '亲家母', '亲家公', '小姨子', '大伯子', '堂弟媳', '表姐夫', '堂哥', '小叔媳妇']

=== 家庭成员 ===
Latent[社交场合] 第3轮生成:
生成内容: ['表哥', '小叔子', '大姑姐', '小舅子', '亲家母', '亲家公', '小姨子', '大伯子', '堂弟媳', '表姐夫', '堂哥', '小叔媳妇']

=== 家庭成员 ===
Latent[社交场合] 第1轮生成:
生成内容: ['表哥', '小叔子', '大姑姐', '小舅子', '亲家母', '亲家公', '小姨子', '大伯子', '堂弟媳', '表姐夫', '堂哥', '小叔媳妇']
保留: 1个
过滤: 11个
过滤详情:
  - "小叔子" (质量)
  - "大姑姐" (质量)
  - "小舅子" (质量)
  - "亲家母" (质量)
  - "亲家公" (质量)
  - "小姨子" (质量)
  - "大伯子" (质量)
  - "堂弟媳" (质量)
  - "表姐夫" (质量)
  - "堂哥" (质量)
  - "小叔媳妇" (质量)

=== 家庭成员 ===
Latent[新闻报道] 第1轮生成:
API原始返回: '祖父  \n外祖母  \n继父  \n养母  \n岳父  \n儿媳  \n侄子  \n外甥女  \n堂兄  \n继子  \n前夫  \n继母  \n叔公'
清洗后内容: ['祖父', '外祖母', '继父', '养母', '岳父', '儿媳', '侄子', '外甥女', '堂兄', '继子', '前夫', '继母', '叔公']

=== 家庭成员 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['祖父', '外祖母', '继父', '养母', '岳父', '儿媳', '侄子', '外甥女', '堂兄', '继子', '前夫', '继母', '叔公']

=== 家庭成员 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['祖父', '外祖母', '继父', '养母', '岳父', '儿媳', '侄子', '外甥女', '堂兄', '继子', '前夫', '继母', '叔公']
保留: 8个
过滤: 5个
过滤详情:
  - "岳父" (质量)
  - "儿媳" (质量)
  - "外甥女" (质量)
  - "前夫" (质量)
  - "叔公" (质量)

=== 家庭成员 ===
Latent[新闻报道] 第2轮生成:
API原始返回: '祖父  \n祖母  \n叔父  \n伯母  \n外甥  \n舅父  \n姨妈'
清洗后内容: ['祖父', '祖母', '叔父', '伯母', '外甥', '舅父', '姨妈']

=== 家庭成员 ===
Latent[新闻报道] 第2轮生成:
生成内容: ['祖父', '祖母', '叔父', '伯母', '外甥', '舅父', '姨妈']

=== 家庭成员 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['祖父', '祖母', '叔父', '伯母', '外甥', '舅父', '姨妈']
保留: 2个
过滤: 5个
过滤详情:
  - "叔父" (质量)
  - "伯母" (质量)
  - "外甥" (质量)
  - "舅父" (质量)
  - "姨妈" (质量)

=== 家庭成员 ===
Latent[新闻报道] 第3轮生成:
API原始返回: '生母  \n继父  \n亲弟弟  \n表妹  \n儿子  \n女儿  \n配偶'
清洗后内容: ['生母', '继父', '亲弟弟', '表妹', '儿子', '女儿', '配偶']

=== 家庭成员 ===
Latent[新闻报道] 第3轮生成:
生成内容: ['生母', '继父', '亲弟弟', '表妹', '儿子', '女儿', '配偶']

=== 家庭成员 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['生母', '继父', '亲弟弟', '表妹', '儿子', '女儿', '配偶']
保留: 5个
过滤: 2个
过滤详情:
  - "生母" (质量)
  - "亲弟弟" (质量)

=== 家庭成员 ===
Latent[口语表达] 第1轮生成:
API原始返回: '老妈  \n老爹  \n我媳妇儿  \n我家那位  \n小闺女  \n小子  \n大舅哥  \n小姨子  \n亲家母  \n表弟  \n堂哥  \n二叔  \n三姑妈'
清洗后内容: ['老妈', '老爹', '我媳妇儿', '我家那位', '小闺女', '小子', '大舅哥', '小姨子', '亲家母', '表弟', '堂哥', '二叔', '三姑妈']

=== 家庭成员 ===
Latent[口语表达] 第1轮生成:
生成内容: ['老妈', '老爹', '我媳妇儿', '我家那位', '小闺女', '小子', '大舅哥', '小姨子', '亲家母', '表弟', '堂哥', '二叔', '三姑妈']

=== 家庭成员 ===
Latent[口语表达] 第1轮生成:
生成内容: ['老妈', '老爹', '我媳妇儿', '我家那位', '小闺女', '小子', '大舅哥', '小姨子', '亲家母', '表弟', '堂哥', '二叔', '三姑妈']
保留: 1个
过滤: 12个
过滤详情:
  - "老妈" (质量)
  - "老爹" (质量)
  - "我媳妇儿" (质量)
  - "我家那位" (质量)
  - "小闺女" (质量)
  - "小子" (质量)
  - "大舅哥" (质量)
  - "小姨子" (质量)
  - "亲家母" (质量)
  - "堂哥" (质量)
  - "二叔" (质量)
  - "三姑妈" (质量)

=== 家庭成员 ===
Latent[口语表达] 第2轮生成:
API原始返回: '小叔子  \n大姑姐  \n二姨夫  \n三舅妈  \n堂哥  \n表妹  \n亲家公  \n小舅子  \n大姨子  \n小姑子  \n大伯子  \n堂弟'
清洗后内容: ['小叔子', '大姑姐', '二姨夫', '三舅妈', '堂哥', '表妹', '亲家公', '小舅子', '大姨子', '小姑子', '大伯子', '堂弟']

=== 家庭成员 ===
Latent[口语表达] 第2轮生成:
生成内容: ['小叔子', '大姑姐', '二姨夫', '三舅妈', '堂哥', '表妹', '亲家公', '小舅子', '大姨子', '小姑子', '大伯子', '堂弟']

=== 家庭成员 ===
Latent[口语表达] 第1轮生成:
生成内容: ['小叔子', '大姑姐', '二姨夫', '三舅妈', '堂哥', '表妹', '亲家公', '小舅子', '大姨子', '小姑子', '大伯子', '堂弟']
保留: 2个
过滤: 10个
过滤详情:
  - "小叔子" (质量)
  - "大姑姐" (质量)
  - "二姨夫" (质量)
  - "三舅妈" (质量)
  - "堂哥" (质量)
  - "亲家公" (质量)
  - "小舅子" (质量)
  - "大姨子" (质量)
  - "小姑子" (质量)
  - "大伯子" (质量)

=== 家庭成员 ===
Latent[口语表达] 第3轮生成:
API原始返回: '老妈  \n老爹  \n姐夫  \n小妹  \n大舅  \n二姨  \n小叔  \n三姑  \n小侄  \n大伯'
清洗后内容: ['老妈', '老爹', '姐夫', '小妹', '大舅', '二姨', '小叔', '三姑', '小侄', '大伯']

=== 家庭成员 ===
Latent[口语表达] 第3轮生成:
生成内容: ['老妈', '老爹', '姐夫', '小妹', '大舅', '二姨', '小叔', '三姑', '小侄', '大伯']

=== 家庭成员 ===
Latent[口语表达] 第1轮生成:
生成内容: ['老妈', '老爹', '姐夫', '小妹', '大舅', '二姨', '小叔', '三姑', '小侄', '大伯']
保留: 0个
过滤: 10个
过滤详情:
  - "老妈" (质量)
  - "老爹" (质量)
  - "姐夫" (质量)
  - "小妹" (质量)
  - "大舅" (质量)
  - "二姨" (质量)
  - "小叔" (质量)
  - "三姑" (质量)
  - "小侄" (质量)
  - "大伯" (质量)

=== 工资数额 ===
Vanilla 第1轮生成:
API原始返回: '月薪9800元  \n年薪36万元  \n税前工资12,000元  \n时薪45元  \n季度奖金18,000元  \n月工资6500元  \n税后年薪28万元'
清洗后内容: ['月薪9800元', '年薪36万元', '税前工资12,000元', '时薪45元', '季度奖金18,000元', '月工资6500元', '税后年薪28万元']

=== 工资数额 ===
Vanilla 第1轮生成:
生成内容: ['月薪9800元', '年薪36万元', '税前工资12,000元', '时薪45元', '季度奖金18,000元', '月工资6500元', '税后年薪28万元']

=== 工资数额 ===
Vanilla 第1轮生成:
生成内容: ['月薪9800元', '年薪36万元', '税前工资12,000元', '时薪45元', '季度奖金18,000元', '月工资6500元', '税后年薪28万元']
保留: 7个
过滤: 0个

=== 工资数额 ===
Latent[法律文本] 第1轮生成:
API原始返回: '月薪人民币陆仟捌佰元整  \n税后月收入人民币壹万零贰佰元整  \n年薪人民币拾万元整  \n月薪人民币捌仟贰佰元整  \n税后年收入人民币壹拾肆万元整  \n月薪人民币玖仟元整  \n税后月收入人民币壹万肆仟元整  \n年薪人民币拾贰万元整  \n月薪人民币柒仟元整  \n税后年收入人民币壹拾万元整  \n月薪人民币捌仟伍佰元整  \n税后月收入人民币壹万贰仟元整  \n年薪人民币拾壹万元整'
清洗后内容: ['月薪人民币陆仟捌佰元整', '税后月收入人民币壹万零贰佰元整', '年薪人民币拾万元整', '月薪人民币捌仟贰佰元整', '税后年收入人民币壹拾肆万元整', '月薪人民币玖仟元整', '税后月收入人民币壹万肆仟元整', '年薪人民币拾贰万元整', '月薪人民币柒仟元整', '税后年收入人民币壹拾万元整', '月薪人民币捌仟伍佰元整', '税后月收入人民币壹万贰仟元整', '年薪人民币拾壹万元整']

=== 工资数额 ===
Latent[法律文本] 第1轮生成:
生成内容: ['月薪人民币陆仟捌佰元整', '税后月收入人民币壹万零贰佰元整', '年薪人民币拾万元整', '月薪人民币捌仟贰佰元整', '税后年收入人民币壹拾肆万元整', '月薪人民币玖仟元整', '税后月收入人民币壹万肆仟元整', '年薪人民币拾贰万元整', '月薪人民币柒仟元整', '税后年收入人民币壹拾万元整', '月薪人民币捌仟伍佰元整', '税后月收入人民币壹万贰仟元整', '年薪人民币拾壹万元整']

=== 工资数额 ===
Latent[法律文本] 第1轮生成:
生成内容: ['月薪人民币陆仟捌佰元整', '税后月收入人民币壹万零贰佰元整', '年薪人民币拾万元整', '月薪人民币捌仟贰佰元整', '税后年收入人民币壹拾肆万元整', '月薪人民币玖仟元整', '税后月收入人民币壹万肆仟元整', '年薪人民币拾贰万元整', '月薪人民币柒仟元整', '税后年收入人民币壹拾万元整', '月薪人民币捌仟伍佰元整', '税后月收入人民币壹万贰仟元整', '年薪人民币拾壹万元整']
保留: 13个
过滤: 0个

=== 工资数额 ===
Latent[商务场合] 第1轮生成:
API原始返回: '月薪9800元  \n年薪32万元  \n税前月收入14,500元  \n税后年薪45万  \n月度基本工资11,000元  \n年度总薪酬65万元  \n月薪加奖金共16,800元  \n税后月收入10,200元  \n年薪不含福利18万  \n月薪扣除社保后9,500元  \n年度现金收入28万元  \n月薪含补贴12,300元  \n税前年薪50万'
清洗后内容: ['月薪9800元', '年薪32万元', '税前月收入14,500元', '税后年薪45万', '月度基本工资11,000元', '年度总薪酬65万元', '月薪加奖金共16,800元', '税后月收入10,200元', '年薪不含福利18万', '月薪扣除社保后9,500元', '年度现金收入28万元', '月薪含补贴12,300元', '税前年薪50万']

=== 工资数额 ===
Latent[商务场合] 第1轮生成:
生成内容: ['月薪9800元', '年薪32万元', '税前月收入14,500元', '税后年薪45万', '月度基本工资11,000元', '年度总薪酬65万元', '月薪加奖金共16,800元', '税后月收入10,200元', '年薪不含福利18万', '月薪扣除社保后9,500元', '年度现金收入28万元', '月薪含补贴12,300元', '税前年薪50万']

=== 工资数额 ===
Latent[商务场合] 第1轮生成:
生成内容: ['月薪9800元', '年薪32万元', '税前月收入14,500元', '税后年薪45万', '月度基本工资11,000元', '年度总薪酬65万元', '月薪加奖金共16,800元', '税后月收入10,200元', '年薪不含福利18万', '月薪扣除社保后9,500元', '年度现金收入28万元', '月薪含补贴12,300元', '税前年薪50万']
保留: 13个
过滤: 0个

=== 投资产品 ===
Vanilla 第1轮生成:
API原始返回: '华夏创新前沿股票  \n中信建投稳盈理财2号  \n南方消费升级混合  \n平安银行稳利货币'
清洗后内容: ['华夏创新前沿股票', '中信建投稳盈理财2号', '南方消费升级混合', '平安银行稳利货币']

=== 投资产品 ===
Vanilla 第1轮生成:
生成内容: ['华夏创新前沿股票', '中信建投稳盈理财2号', '南方消费升级混合', '平安银行稳利货币']

=== 投资产品 ===
Vanilla 第1轮生成:
生成内容: ['华夏创新前沿股票', '中信建投稳盈理财2号', '南方消费升级混合', '平安银行稳利货币']
保留: 4个
过滤: 0个

=== 投资产品 ===
Latent[法律文本] 第1轮生成:
API原始返回: '中信证券稳健增值专项资产管理计划  \n华夏基金安心收益债券型证券投资基金  \n平安银行结构性存款产品  \n招商银行现金管理类理财产品  \n光大证券固定收益专项资产管理计划  \n广发基金中短债组合型证券投资基金  \n民生银行大额存单产品  \n兴业证券收益增强专项资产管理计划  \n建行银行个人养老储蓄产品  \n中银基金货币市场基金  \n浦发银行智能存款产品  \n中信银行国债逆回购产品  \n交通银行结构性存款  \n工银瑞信保本混合型证券投资基金  \n农银汇理纯债债券型证券投资基金  \n招商证券收益挂钩专项资产管理计划  \n建信基金养老目标日期基金  \n平安基金稳健收益货币市场基金  \n华泰证券固定收益专项资产管理计划'
清洗后内容: ['中信证券稳健增值专项资产管理计划', '华夏基金安心收益债券型证券投资基金', '平安银行结构性存款产品', '招商银行现金管理类理财产品', '光大证券固定收益专项资产管理计划', '广发基金中短债组合型证券投资基金', '民生银行大额存单产品', '兴业证券收益增强专项资产管理计划', '建行银行个人养老储蓄产品', '中银基金货币市场基金', '浦发银行智能存款产品', '中信银行国债逆回购产品', '交通银行结构性存款', '工银瑞信保本混合型证券投资基金', '农银汇理纯债债券型证券投资基金', '招商证券收益挂钩专项资产管理计划', '建信基金养老目标日期基金', '平安基金稳健收益货币市场基金', '华泰证券固定收益专项资产管理计划']

=== 投资产品 ===
Latent[法律文本] 第1轮生成:
生成内容: ['中信证券稳健增值专项资产管理计划', '华夏基金安心收益债券型证券投资基金', '平安银行结构性存款产品', '招商银行现金管理类理财产品', '光大证券固定收益专项资产管理计划', '广发基金中短债组合型证券投资基金', '民生银行大额存单产品', '兴业证券收益增强专项资产管理计划', '建行银行个人养老储蓄产品', '中银基金货币市场基金', '浦发银行智能存款产品', '中信银行国债逆回购产品', '交通银行结构性存款', '工银瑞信保本混合型证券投资基金', '农银汇理纯债债券型证券投资基金', '招商证券收益挂钩专项资产管理计划', '建信基金养老目标日期基金', '平安基金稳健收益货币市场基金', '华泰证券固定收益专项资产管理计划']

=== 投资产品 ===
Latent[法律文本] 第1轮生成:
生成内容: ['中信证券稳健增值专项资产管理计划', '华夏基金安心收益债券型证券投资基金', '平安银行结构性存款产品', '招商银行现金管理类理财产品', '光大证券固定收益专项资产管理计划', '广发基金中短债组合型证券投资基金', '民生银行大额存单产品', '兴业证券收益增强专项资产管理计划', '建行银行个人养老储蓄产品', '中银基金货币市场基金', '浦发银行智能存款产品', '中信银行国债逆回购产品', '交通银行结构性存款', '工银瑞信保本混合型证券投资基金', '农银汇理纯债债券型证券投资基金', '招商证券收益挂钩专项资产管理计划', '建信基金养老目标日期基金', '平安基金稳健收益货币市场基金', '华泰证券固定收益专项资产管理计划']
保留: 19个
过滤: 0个

=== 投资产品 ===
Latent[商务场合] 第1轮生成:
API原始返回: '华夏基金核心精选混合  \n平安银行稳盈理财产品  \n中证500指数增强基金  \n交通银行安心收益存款  \n汇添富消费行业混合  \n招商银行稳进理财计划  \n嘉实全球互联网股票基金  \n中信建投证券稳健增值信托  \n广发纳斯达克100指数基金  \n中国银行结构性存款产品  \n南方优选成长混合型基金  \n兴业银行财富增值计划  \n易方达中小盘混合基金  \n招商中证白酒指数基金  \n华安纳斯达克100指数基金  \n工商银行安心宝理财产品  \n建信基金股债平衡混合  \n招商银行摩羯智投组合  \n中信证券稳健收益集合计划'
清洗后内容: ['华夏基金核心精选混合', '平安银行稳盈理财产品', '中证500指数增强基金', '交通银行安心收益存款', '汇添富消费行业混合', '招商银行稳进理财计划', '嘉实全球互联网股票基金', '中信建投证券稳健增值信托', '广发纳斯达克100指数基金', '中国银行结构性存款产品', '南方优选成长混合型基金', '兴业银行财富增值计划', '易方达中小盘混合基金', '招商中证白酒指数基金', '华安纳斯达克100指数基金', '工商银行安心宝理财产品', '建信基金股债平衡混合', '招商银行摩羯智投组合', '中信证券稳健收益集合计划']

=== 投资产品 ===
Latent[商务场合] 第1轮生成:
生成内容: ['华夏基金核心精选混合', '平安银行稳盈理财产品', '中证500指数增强基金', '交通银行安心收益存款', '汇添富消费行业混合', '招商银行稳进理财计划', '嘉实全球互联网股票基金', '中信建投证券稳健增值信托', '广发纳斯达克100指数基金', '中国银行结构性存款产品', '南方优选成长混合型基金', '兴业银行财富增值计划', '易方达中小盘混合基金', '招商中证白酒指数基金', '华安纳斯达克100指数基金', '工商银行安心宝理财产品', '建信基金股债平衡混合', '招商银行摩羯智投组合', '中信证券稳健收益集合计划']

=== 投资产品 ===
Latent[商务场合] 第1轮生成:
生成内容: ['华夏基金核心精选混合', '平安银行稳盈理财产品', '中证500指数增强基金', '交通银行安心收益存款', '汇添富消费行业混合', '招商银行稳进理财计划', '嘉实全球互联网股票基金', '中信建投证券稳健增值信托', '广发纳斯达克100指数基金', '中国银行结构性存款产品', '南方优选成长混合型基金', '兴业银行财富增值计划', '易方达中小盘混合基金', '招商中证白酒指数基金', '华安纳斯达克100指数基金', '工商银行安心宝理财产品', '建信基金股债平衡混合', '招商银行摩羯智投组合', '中信证券稳健收益集合计划']
保留: 19个
过滤: 0个

=== 税务记录 ===
Vanilla 第1轮生成:
API原始返回: '个人所得税完税证明  \n企业所得税年度汇算清缴表  \n车辆购置税完税凭证  \n房产税纳税申报单  \n增值税专用发票抵扣联  \n印花税纳税记录单  \n土地增值税清算表  \n契税缴纳证明书  \n消费税纳税申报表  \n企业所得税减免备案表  \n个人所得税年度汇算表  \n企业所得税纳税评估报告  \n个人所得税专项附加扣除表'
清洗后内容: ['个人所得税完税证明', '企业所得税年度汇算清缴表', '车辆购置税完税凭证', '房产税纳税申报单', '增值税专用发票抵扣联', '印花税纳税记录单', '土地增值税清算表', '契税缴纳证明书', '消费税纳税申报表', '企业所得税减免备案表', '个人所得税年度汇算表', '企业所得税纳税评估报告', '个人所得税专项附加扣除表']

=== 税务记录 ===
Vanilla 第1轮生成:
生成内容: ['个人所得税完税证明', '企业所得税年度汇算清缴表', '车辆购置税完税凭证', '房产税纳税申报单', '增值税专用发票抵扣联', '印花税纳税记录单', '土地增值税清算表', '契税缴纳证明书', '消费税纳税申报表', '企业所得税减免备案表', '个人所得税年度汇算表', '企业所得税纳税评估报告', '个人所得税专项附加扣除表']

=== 税务记录 ===
Vanilla 第1轮生成:
生成内容: ['个人所得税完税证明', '企业所得税年度汇算清缴表', '车辆购置税完税凭证', '房产税纳税申报单', '增值税专用发票抵扣联', '印花税纳税记录单', '土地增值税清算表', '契税缴纳证明书', '消费税纳税申报表', '企业所得税减免备案表', '个人所得税年度汇算表', '企业所得税纳税评估报告', '个人所得税专项附加扣除表']
保留: 13个
过滤: 0个

=== 税务记录 ===
Latent[法律文本] 第1轮生成:
API原始返回: '税务登记信息表  \n企业所得税纳税申报表  \n个人所得税完税凭证  \n增值税专用发票存根联'
清洗后内容: ['税务登记信息表', '企业所得税纳税申报表', '个人所得税完税凭证', '增值税专用发票存根联']

=== 税务记录 ===
Latent[法律文本] 第1轮生成:
生成内容: ['税务登记信息表', '企业所得税纳税申报表', '个人所得税完税凭证', '增值税专用发票存根联']

=== 税务记录 ===
Latent[法律文本] 第1轮生成:
生成内容: ['税务登记信息表', '企业所得税纳税申报表', '个人所得税完税凭证', '增值税专用发票存根联']
保留: 3个
过滤: 1个
过滤详情:
  - "税务登记信息表" (抽象)

=== 税务记录 ===
Latent[学术论文] 第1轮生成:
API原始返回: '2021年度增值税申报记录  \n个人所得税明细纳税凭证  \n企业所得税季度预缴报告  \n房产税纳税备案文档  \n消费税专项稽查文件  \n土地使用税缴纳确认书'
清洗后内容: ['2021年度增值税申报记录', '个人所得税明细纳税凭证', '企业所得税季度预缴报告', '房产税纳税备案文档', '消费税专项稽查文件', '土地使用税缴纳确认书']

=== 税务记录 ===
Latent[学术论文] 第1轮生成:
生成内容: ['2021年度增值税申报记录', '个人所得税明细纳税凭证', '企业所得税季度预缴报告', '房产税纳税备案文档', '消费税专项稽查文件', '土地使用税缴纳确认书']

=== 税务记录 ===
Latent[学术论文] 第1轮生成:
生成内容: ['2021年度增值税申报记录', '个人所得税明细纳税凭证', '企业所得税季度预缴报告', '房产税纳税备案文档', '消费税专项稽查文件', '土地使用税缴纳确认书']
保留: 6个
过滤: 0个

=== 信用记录 ===
Vanilla 第1轮生成:
API原始返回: '个人信用报告无污点  \n信用卡连续36期正常还款  \n房贷按揭无逾期历史  \n公积金贷款审批通过  \n无信用卡逾期记录  \n个人信用状况良好  \n商业贷款无违约记录'
清洗后内容: ['个人信用报告无污点', '信用卡连续36期正常还款', '房贷按揭无逾期历史', '公积金贷款审批通过', '无信用卡逾期记录', '个人信用状况良好', '商业贷款无违约记录']

=== 信用记录 ===
Vanilla 第1轮生成:
生成内容: ['个人信用报告无污点', '信用卡连续36期正常还款', '房贷按揭无逾期历史', '公积金贷款审批通过', '无信用卡逾期记录', '个人信用状况良好', '商业贷款无违约记录']

=== 信用记录 ===
Vanilla 第1轮生成:
生成内容: ['个人信用报告无污点', '信用卡连续36期正常还款', '房贷按揭无逾期历史', '公积金贷款审批通过', '无信用卡逾期记录', '个人信用状况良好', '商业贷款无违约记录']
保留: 7个
过滤: 0个

=== 信用记录 ===
Latent[法律文本] 第1轮生成:
API原始返回: '贷款申请人的信用档案摘要  \n涉案公民的银行信用查询报告  \n合同签署方的信用评估明细  \n被告方的个人信用历史记录  \n原告的银行信用状况审查表  \n被执行人的信用报告扫描件  \n合同相对方的信用审查结果  \n关联方的信用档案信息页  \n担保人的信用记录查询文件  \n债务人的信用评级证明材料  \n交易方的信用历史背景调查  \n涉案人员的信用状况备案表  \n合同当事人的信用合规证明'
清洗后内容: ['贷款申请人的信用档案摘要', '涉案公民的银行信用查询报告', '合同签署方的信用评估明细', '被告方的个人信用历史记录', '原告的银行信用状况审查表', '被执行人的信用报告扫描件', '合同相对方的信用审查结果', '关联方的信用档案信息页', '担保人的信用记录查询文件', '债务人的信用评级证明材料', '交易方的信用历史背景调查', '涉案人员的信用状况备案表', '合同当事人的信用合规证明']

=== 信用记录 ===
Latent[法律文本] 第1轮生成:
生成内容: ['贷款申请人的信用档案摘要', '涉案公民的银行信用查询报告', '合同签署方的信用评估明细', '被告方的个人信用历史记录', '原告的银行信用状况审查表', '被执行人的信用报告扫描件', '合同相对方的信用审查结果', '关联方的信用档案信息页', '担保人的信用记录查询文件', '债务人的信用评级证明材料', '交易方的信用历史背景调查', '涉案人员的信用状况备案表', '合同当事人的信用合规证明']

=== 信用记录 ===
Latent[法律文本] 第1轮生成:
生成内容: ['贷款申请人的信用档案摘要', '涉案公民的银行信用查询报告', '合同签署方的信用评估明细', '被告方的个人信用历史记录', '原告的银行信用状况审查表', '被执行人的信用报告扫描件', '合同相对方的信用审查结果', '关联方的信用档案信息页', '担保人的信用记录查询文件', '债务人的信用评级证明材料', '交易方的信用历史背景调查', '涉案人员的信用状况备案表', '合同当事人的信用合规证明']
保留: 10个
过滤: 3个
过滤详情:
  - "关联方的信用档案信息页" (抽象)
  - "交易方的信用历史背景调查" (抽象)
  - "涉案人员的信用状况备案表" (抽象)

=== 信用记录 ===
Latent[商务场合] 第1轮生成:
API原始返回: '王磊先生在建设银行的商业贷款信用记录  \n陈静女士在农业银行的商业信用卡使用档案  \n李强先生在交通银行的商业融资信用报告  \n刘芳女士在兴业银行的商业贷款信用档案  \n张伟先生在中信银行的商业信用卡使用历史  \n王芳女士在民生银行的商业贷款信用记录  \n赵明先生在浦发银行的商业融资信用报告  \n陈华女士在广发银行的商业信用卡使用档案  \n李军先生在华夏银行的商业贷款信用记录  \n刘伟先生在平安银行的商业融资信用档案  \n张丽女士在招商银行的商业信用卡使用历史  \n王刚先生在邮储银行的商业贷款信用报告  \n陈红女士在光大银行的商业融资信用记录'
清洗后内容: ['王磊先生在建设银行的商业贷款信用记录', '陈静女士在农业银行的商业信用卡使用档案', '李强先生在交通银行的商业融资信用报告', '刘芳女士在兴业银行的商业贷款信用档案', '张伟先生在中信银行的商业信用卡使用历史', '王芳女士在民生银行的商业贷款信用记录', '赵明先生在浦发银行的商业融资信用报告', '陈华女士在广发银行的商业信用卡使用档案', '李军先生在华夏银行的商业贷款信用记录', '刘伟先生在平安银行的商业融资信用档案', '张丽女士在招商银行的商业信用卡使用历史', '王刚先生在邮储银行的商业贷款信用报告', '陈红女士在光大银行的商业融资信用记录']

=== 信用记录 ===
Latent[商务场合] 第1轮生成:
生成内容: ['王磊先生在建设银行的商业贷款信用记录', '陈静女士在农业银行的商业信用卡使用档案', '李强先生在交通银行的商业融资信用报告', '刘芳女士在兴业银行的商业贷款信用档案', '张伟先生在中信银行的商业信用卡使用历史', '王芳女士在民生银行的商业贷款信用记录', '赵明先生在浦发银行的商业融资信用报告', '陈华女士在广发银行的商业信用卡使用档案', '李军先生在华夏银行的商业贷款信用记录', '刘伟先生在平安银行的商业融资信用档案', '张丽女士在招商银行的商业信用卡使用历史', '王刚先生在邮储银行的商业贷款信用报告', '陈红女士在光大银行的商业融资信用记录']

=== 信用记录 ===
Latent[商务场合] 第1轮生成:
生成内容: ['王磊先生在建设银行的商业贷款信用记录', '陈静女士在农业银行的商业信用卡使用档案', '李强先生在交通银行的商业融资信用报告', '刘芳女士在兴业银行的商业贷款信用档案', '张伟先生在中信银行的商业信用卡使用历史', '王芳女士在民生银行的商业贷款信用记录', '赵明先生在浦发银行的商业融资信用报告', '陈华女士在广发银行的商业信用卡使用档案', '李军先生在华夏银行的商业贷款信用记录', '刘伟先生在平安银行的商业融资信用档案', '张丽女士在招商银行的商业信用卡使用历史', '王刚先生在邮储银行的商业贷款信用报告', '陈红女士在光大银行的商业融资信用记录']
保留: 13个
过滤: 0个

=== 实体资产 ===
Vanilla 第1轮生成:
API原始返回: '北京市海淀区商铺一间  \n上海浦东新区写字楼一层  \n广州天河区公寓两套  \n深圳南山区厂房一处  \n杭州西湖区别墅一栋  \n成都高新区商铺一间  \n武汉江汉区住宅一套  \n南京鼓楼区商铺一处  \n西安高新区厂房一间  \n重庆渝中区写字楼一层  \n苏州工业园区仓库一座  \n天津和平区商铺一间  \n长沙岳麓区住宅一套  \n青岛崂山区别墅一栋  \n沈阳沈河区商铺一处'
清洗后内容: ['北京市海淀区商铺一间', '上海浦东新区写字楼一层', '广州天河区公寓两套', '深圳南山区厂房一处', '杭州西湖区别墅一栋', '成都高新区商铺一间', '武汉江汉区住宅一套', '南京鼓楼区商铺一处', '西安高新区厂房一间', '重庆渝中区写字楼一层', '苏州工业园区仓库一座', '天津和平区商铺一间', '长沙岳麓区住宅一套', '青岛崂山区别墅一栋', '沈阳沈河区商铺一处']

=== 实体资产 ===
Vanilla 第1轮生成:
生成内容: ['北京市海淀区商铺一间', '上海浦东新区写字楼一层', '广州天河区公寓两套', '深圳南山区厂房一处', '杭州西湖区别墅一栋', '成都高新区商铺一间', '武汉江汉区住宅一套', '南京鼓楼区商铺一处', '西安高新区厂房一间', '重庆渝中区写字楼一层', '苏州工业园区仓库一座', '天津和平区商铺一间', '长沙岳麓区住宅一套', '青岛崂山区别墅一栋', '沈阳沈河区商铺一处']

=== 实体资产 ===
Vanilla 第1轮生成:
生成内容: ['北京市海淀区商铺一间', '上海浦东新区写字楼一层', '广州天河区公寓两套', '深圳南山区厂房一处', '杭州西湖区别墅一栋', '成都高新区商铺一间', '武汉江汉区住宅一套', '南京鼓楼区商铺一处', '西安高新区厂房一间', '重庆渝中区写字楼一层', '苏州工业园区仓库一座', '天津和平区商铺一间', '长沙岳麓区住宅一套', '青岛崂山区别墅一栋', '沈阳沈河区商铺一处']
保留: 15个
过滤: 0个

=== 实体资产 ===
Latent[法律文本] 第1轮生成:
API原始返回: '《中华人民共和国民法典》合同文本一份'
清洗后内容: ['《中华人民共和国民法典》合同文本一份']

=== 实体资产 ===
Latent[法律文本] 第1轮生成:
生成内容: ['《中华人民共和国民法典》合同文本一份']

=== 实体资产 ===
Latent[法律文本] 第1轮生成:
生成内容: ['《中华人民共和国民法典》合同文本一份']
保留: 1个
过滤: 0个

=== 实体资产 ===
Latent[新闻报道] 第1轮生成:
API原始返回: '广州市天河区商业综合体部分楼层'
清洗后内容: ['广州市天河区商业综合体部分楼层']

=== 实体资产 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['广州市天河区商业综合体部分楼层']

=== 实体资产 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['广州市天河区商业综合体部分楼层']
保留: 1个
过滤: 0个

=== 交易信息 ===
Vanilla 第1轮生成:
API原始返回: '交易编号TXN202407011234  \n支付金额560.75元  \n银行转账-购物支出  \n微信转账收款200元  \n信用卡还款980.00元  \nPOS机消费345元  \n现金支付-停车费20元  \n支付宝转账给李四  \n电子账单支付150元  \n交易流水号TXN202407021234  \n支付凭证编号PF202407031234  \n刷卡消费-超市购物  \n微信红包转账100元  \n银行汇款-学费缴纳  \n支付记录编号PR202407041234  \n交易确认码TCM202407051234'
清洗后内容: ['交易编号TXN202407011234', '支付金额560.75元', '银行转账-购物支出', '微信转账收款200元', '信用卡还款980.00元', 'POS机消费345元', '现金支付-停车费20元', '支付宝转账给李四', '电子账单支付150元', '交易流水号TXN202407021234', '支付凭证编号PF202407031234', '刷卡消费-超市购物', '微信红包转账100元', '银行汇款-学费缴纳', '支付记录编号PR202407041234', '交易确认码TCM202407051234']

=== 交易信息 ===
Vanilla 第1轮生成:
生成内容: ['交易编号TXN202407011234', '支付金额560.75元', '银行转账-购物支出', '微信转账收款200元', '信用卡还款980.00元', 'POS机消费345元', '现金支付-停车费20元', '支付宝转账给李四', '电子账单支付150元', '交易流水号TXN202407021234', '支付凭证编号PF202407031234', '刷卡消费-超市购物', '微信红包转账100元', '银行汇款-学费缴纳', '支付记录编号PR202407041234', '交易确认码TCM202407051234']

=== 交易信息 ===
Vanilla 第1轮生成:
生成内容: ['交易编号TXN202407011234', '支付金额560.75元', '银行转账-购物支出', '微信转账收款200元', '信用卡还款980.00元', 'POS机消费345元', '现金支付-停车费20元', '支付宝转账给李四', '电子账单支付150元', '交易流水号TXN202407021234', '支付凭证编号PF202407031234', '刷卡消费-超市购物', '微信红包转账100元', '银行汇款-学费缴纳', '支付记录编号PR202407041234', '交易确认码TCM202407051234']
保留: 16个
过滤: 0个

=== 交易信息 ===
Latent[法律文本] 第1轮生成:
API原始返回: '交易凭证编号JZ202406180056'
清洗后内容: ['交易凭证编号JZ202406180056']

=== 交易信息 ===
Latent[法律文本] 第1轮生成:
生成内容: ['交易凭证编号JZ202406180056']

=== 交易信息 ===
Latent[法律文本] 第1轮生成:
生成内容: ['交易凭证编号JZ202406180056']
保留: 1个
过滤: 0个

=== 交易信息 ===
Latent[商务场合] 第1轮生成:
API原始返回: '交易流水号T20240619ABCD  \n结算凭证号SCF20240619XYZ  \n应收账款金额68000元'
清洗后内容: ['交易流水号T20240619ABCD', '结算凭证号SCF20240619XYZ', '应收账款金额68000元']

=== 交易信息 ===
Latent[商务场合] 第1轮生成:
生成内容: ['交易流水号T20240619ABCD', '结算凭证号SCF20240619XYZ', '应收账款金额68000元']

=== 交易信息 ===
Latent[商务场合] 第1轮生成:
生成内容: ['交易流水号T20240619ABCD', '结算凭证号SCF20240619XYZ', '应收账款金额68000元']
保留: 3个
过滤: 0个

=== 疾病 ===
Latent[医学描述] 第1轮生成:
API原始返回: '慢性阻塞性肺疾病  \n急性心肌梗死  \n脑卒中后遗症  \n类风湿关节炎  \n幽门螺杆菌感染  \n肝功能衰竭  \n急性肾损伤  \n甲状腺功能亢进  \n慢性心力衰竭  \n骨髓增生异常综合征  \n前列腺增生  \n病毒性肝炎  \n多发性硬化症'
清洗后内容: ['慢性阻塞性肺疾病', '急性心肌梗死', '脑卒中后遗症', '类风湿关节炎', '幽门螺杆菌感染', '肝功能衰竭', '急性肾损伤', '甲状腺功能亢进', '慢性心力衰竭', '骨髓增生异常综合征', '前列腺增生', '病毒性肝炎', '多发性硬化症']

=== 疾病 ===
Latent[医学描述] 第1轮生成:
生成内容: ['慢性阻塞性肺疾病', '急性心肌梗死', '脑卒中后遗症', '类风湿关节炎', '幽门螺杆菌感染', '肝功能衰竭', '急性肾损伤', '甲状腺功能亢进', '慢性心力衰竭', '骨髓增生异常综合征', '前列腺增生', '病毒性肝炎', '多发性硬化症']

=== 疾病 ===
Latent[医学描述] 第1轮生成:
生成内容: ['慢性阻塞性肺疾病', '急性心肌梗死', '脑卒中后遗症', '类风湿关节炎', '幽门螺杆菌感染', '肝功能衰竭', '急性肾损伤', '甲状腺功能亢进', '慢性心力衰竭', '骨髓增生异常综合征', '前列腺增生', '病毒性肝炎', '多发性硬化症']
保留: 13个
过滤: 0个

=== 疾病 ===
Latent[新闻报道] 第1轮生成:
API原始返回: '类风湿关节炎  \n慢性阻塞性肺病  \n阿尔茨海默病  \n帕金森综合征  \n多发性硬化症  \n克罗恩病  \n特发性肺纤维化  \n急性心肌梗死  \n脑卒中后遗症  \n慢性肾病  \n重症肌无力  \n银屑病关节炎  \n过敏性哮喘'
清洗后内容: ['类风湿关节炎', '慢性阻塞性肺病', '阿尔茨海默病', '帕金森综合征', '多发性硬化症', '克罗恩病', '特发性肺纤维化', '急性心肌梗死', '脑卒中后遗症', '慢性肾病', '重症肌无力', '银屑病关节炎', '过敏性哮喘']

=== 疾病 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['类风湿关节炎', '慢性阻塞性肺病', '阿尔茨海默病', '帕金森综合征', '多发性硬化症', '克罗恩病', '特发性肺纤维化', '急性心肌梗死', '脑卒中后遗症', '慢性肾病', '重症肌无力', '银屑病关节炎', '过敏性哮喘']

=== 疾病 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['类风湿关节炎', '慢性阻塞性肺病', '阿尔茨海默病', '帕金森综合征', '多发性硬化症', '克罗恩病', '特发性肺纤维化', '急性心肌梗死', '脑卒中后遗症', '慢性肾病', '重症肌无力', '银屑病关节炎', '过敏性哮喘']
保留: 13个
过滤: 0个

=== 疾病 ===
Latent[新闻报道] 第2轮生成:
API原始返回: '阿尔茨海默病  \n急性心肌梗死  \n乳腺癌  \n肺栓塞'
清洗后内容: ['阿尔茨海默病', '急性心肌梗死', '乳腺癌', '肺栓塞']

=== 疾病 ===
Latent[新闻报道] 第2轮生成:
生成内容: ['阿尔茨海默病', '急性心肌梗死', '乳腺癌', '肺栓塞']

=== 疾病 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['阿尔茨海默病', '急性心肌梗死', '乳腺癌', '肺栓塞']
保留: 4个
过滤: 0个

=== 疾病 ===
Latent[新闻报道] 第3轮生成:
API原始返回: '阿尔茨海默病'
清洗后内容: ['阿尔茨海默病']

=== 疾病 ===
Latent[新闻报道] 第3轮生成:
生成内容: ['阿尔茨海默病']

=== 疾病 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['阿尔茨海默病']
保留: 1个
过滤: 0个

=== 疾病 ===
Latent[法律文本] 第1轮生成:
API原始返回: '急性心肌梗死  \n慢性阻塞性肺疾病  \n脑卒中后遗症  \n急性肾衰竭  \n类风湿关节炎  \n甲状腺功能减退症  \n慢性心力衰竭  \n肝功能不全  \n2型糖尿病并发症  \n高血压性心脏病  \n多发性硬化症  \n重症肌无力  \n慢性肾脏病'
清洗后内容: ['急性心肌梗死', '慢性阻塞性肺疾病', '脑卒中后遗症', '急性肾衰竭', '类风湿关节炎', '甲状腺功能减退症', '慢性心力衰竭', '肝功能不全', '2型糖尿病并发症', '高血压性心脏病', '多发性硬化症', '重症肌无力', '慢性肾脏病']

=== 疾病 ===
Latent[法律文本] 第1轮生成:
生成内容: ['急性心肌梗死', '慢性阻塞性肺疾病', '脑卒中后遗症', '急性肾衰竭', '类风湿关节炎', '甲状腺功能减退症', '慢性心力衰竭', '肝功能不全', '2型糖尿病并发症', '高血压性心脏病', '多发性硬化症', '重症肌无力', '慢性肾脏病']

=== 疾病 ===
Latent[法律文本] 第1轮生成:
生成内容: ['急性心肌梗死', '慢性阻塞性肺疾病', '脑卒中后遗症', '急性肾衰竭', '类风湿关节炎', '甲状腺功能减退症', '慢性心力衰竭', '肝功能不全', '2型糖尿病并发症', '高血压性心脏病', '多发性硬化症', '重症肌无力', '慢性肾脏病']
保留: 13个
过滤: 0个

=== 疾病 ===
Latent[法律文本] 第2轮生成:
API原始返回: '冠心病稳定性心绞痛  \n脑梗死后遗症期  \n慢性阻塞性肺疾病  \n特发性肺纤维化  \n甲状腺功能减退症  \n类风湿关节炎活动期'
清洗后内容: ['冠心病稳定性心绞痛', '脑梗死后遗症期', '慢性阻塞性肺疾病', '特发性肺纤维化', '甲状腺功能减退症', '类风湿关节炎活动期']

=== 疾病 ===
Latent[法律文本] 第2轮生成:
生成内容: ['冠心病稳定性心绞痛', '脑梗死后遗症期', '慢性阻塞性肺疾病', '特发性肺纤维化', '甲状腺功能减退症', '类风湿关节炎活动期']

=== 疾病 ===
Latent[法律文本] 第1轮生成:
生成内容: ['冠心病稳定性心绞痛', '脑梗死后遗症期', '慢性阻塞性肺疾病', '特发性肺纤维化', '甲状腺功能减退症', '类风湿关节炎活动期']
保留: 6个
过滤: 0个

=== 药物 ===
Vanilla 第1轮生成:
API原始返回: '厄贝沙坦片'
清洗后内容: ['厄贝沙坦片']

=== 药物 ===
Vanilla 第1轮生成:
生成内容: ['厄贝沙坦片']

=== 药物 ===
Vanilla 第1轮生成:
生成内容: ['厄贝沙坦片']
保留: 1个
过滤: 0个

=== 药物 ===
Latent[医学描述] 第1轮生成:
API原始返回: '甲氨蝶呤片  \n环孢素软胶囊  \n奥美拉唑肠溶胶囊  \n氯雷他定片  \n厄贝沙坦氢氯噻嗪片  \n左氧氟沙星片  \n辛伐他汀片  \n阿莫西林胶囊  \n二甲双胍缓释片  \n布洛芬缓释胶囊  \n雷贝拉唑钠肠溶片  \n氟康唑胶囊'
清洗后内容: ['甲氨蝶呤片', '环孢素软胶囊', '奥美拉唑肠溶胶囊', '氯雷他定片', '厄贝沙坦氢氯噻嗪片', '左氧氟沙星片', '辛伐他汀片', '阿莫西林胶囊', '二甲双胍缓释片', '布洛芬缓释胶囊', '雷贝拉唑钠肠溶片', '氟康唑胶囊']

=== 药物 ===
Latent[医学描述] 第1轮生成:
生成内容: ['甲氨蝶呤片', '环孢素软胶囊', '奥美拉唑肠溶胶囊', '氯雷他定片', '厄贝沙坦氢氯噻嗪片', '左氧氟沙星片', '辛伐他汀片', '阿莫西林胶囊', '二甲双胍缓释片', '布洛芬缓释胶囊', '雷贝拉唑钠肠溶片', '氟康唑胶囊']

=== 药物 ===
Latent[医学描述] 第1轮生成:
生成内容: ['甲氨蝶呤片', '环孢素软胶囊', '奥美拉唑肠溶胶囊', '氯雷他定片', '厄贝沙坦氢氯噻嗪片', '左氧氟沙星片', '辛伐他汀片', '阿莫西林胶囊', '二甲双胍缓释片', '布洛芬缓释胶囊', '雷贝拉唑钠肠溶片', '氟康唑胶囊']
保留: 12个
过滤: 0个

=== 药物 ===
Latent[学术论文] 第1轮生成:
API原始返回: '氯沙坦钾氢氯噻嗪片  \n瑞格列奈二甲双胍胶囊  \n厄贝沙坦氢氯噻嗪片  \n西格列汀二甲双胍片  \n阿托伐他汀钙片  \n厄洛替尼片  \n吉非替尼片  \n奥美拉唑肠溶胶囊  \n兰索拉唑肠溶片  \n氟伐他汀钠胶囊  \n非那雄胺片  \n度他雄胺软胶囊  \n比索洛尔氢氯噻嗪片'
清洗后内容: ['氯沙坦钾氢氯噻嗪片', '瑞格列奈二甲双胍胶囊', '厄贝沙坦氢氯噻嗪片', '西格列汀二甲双胍片', '阿托伐他汀钙片', '厄洛替尼片', '吉非替尼片', '奥美拉唑肠溶胶囊', '兰索拉唑肠溶片', '氟伐他汀钠胶囊', '非那雄胺片', '度他雄胺软胶囊', '比索洛尔氢氯噻嗪片']

=== 药物 ===
Latent[学术论文] 第1轮生成:
生成内容: ['氯沙坦钾氢氯噻嗪片', '瑞格列奈二甲双胍胶囊', '厄贝沙坦氢氯噻嗪片', '西格列汀二甲双胍片', '阿托伐他汀钙片', '厄洛替尼片', '吉非替尼片', '奥美拉唑肠溶胶囊', '兰索拉唑肠溶片', '氟伐他汀钠胶囊', '非那雄胺片', '度他雄胺软胶囊', '比索洛尔氢氯噻嗪片']

=== 药物 ===
Latent[学术论文] 第1轮生成:
生成内容: ['氯沙坦钾氢氯噻嗪片', '瑞格列奈二甲双胍胶囊', '厄贝沙坦氢氯噻嗪片', '西格列汀二甲双胍片', '阿托伐他汀钙片', '厄洛替尼片', '吉非替尼片', '奥美拉唑肠溶胶囊', '兰索拉唑肠溶片', '氟伐他汀钠胶囊', '非那雄胺片', '度他雄胺软胶囊', '比索洛尔氢氯噻嗪片']
保留: 13个
过滤: 0个

=== 药物 ===
Latent[法律文本] 第1轮生成:
API原始返回: '氯沙坦钾片  \n厄贝沙坦胶囊  \n瑞舒伐他汀钙片  \n非那雄胺片  \n甲硝唑片  \n奥美拉唑肠溶胶囊  \n辛伐他汀片  \n氨氯地平片  \n二甲双胍片  \n依那普利片  \n布洛芬缓释胶囊  \n阿奇霉素片  \n特拉唑嗪胶囊'
清洗后内容: ['氯沙坦钾片', '厄贝沙坦胶囊', '瑞舒伐他汀钙片', '非那雄胺片', '甲硝唑片', '奥美拉唑肠溶胶囊', '辛伐他汀片', '氨氯地平片', '二甲双胍片', '依那普利片', '布洛芬缓释胶囊', '阿奇霉素片', '特拉唑嗪胶囊']

=== 药物 ===
Latent[法律文本] 第1轮生成:
生成内容: ['氯沙坦钾片', '厄贝沙坦胶囊', '瑞舒伐他汀钙片', '非那雄胺片', '甲硝唑片', '奥美拉唑肠溶胶囊', '辛伐他汀片', '氨氯地平片', '二甲双胍片', '依那普利片', '布洛芬缓释胶囊', '阿奇霉素片', '特拉唑嗪胶囊']

=== 药物 ===
Latent[法律文本] 第1轮生成:
生成内容: ['氯沙坦钾片', '厄贝沙坦胶囊', '瑞舒伐他汀钙片', '非那雄胺片', '甲硝唑片', '奥美拉唑肠溶胶囊', '辛伐他汀片', '氨氯地平片', '二甲双胍片', '依那普利片', '布洛芬缓释胶囊', '阿奇霉素片', '特拉唑嗪胶囊']
保留: 13个
过滤: 0个

=== 药物 ===
Latent[法律文本] 第2轮生成:
API原始返回: '苯妥英钠片  \n氯氮平片  \n丙戊酸钠缓释片'
清洗后内容: ['苯妥英钠片', '氯氮平片', '丙戊酸钠缓释片']

=== 药物 ===
Latent[法律文本] 第2轮生成:
生成内容: ['苯妥英钠片', '氯氮平片', '丙戊酸钠缓释片']

=== 药物 ===
Latent[法律文本] 第1轮生成:
生成内容: ['苯妥英钠片', '氯氮平片', '丙戊酸钠缓释片']
保留: 3个
过滤: 0个

=== 临床表现 ===
Vanilla 第1轮生成:
API原始返回: '双下肢水肿明显  \n口周发绀明显  \n腹部压痛阳性'
清洗后内容: ['双下肢水肿明显', '口周发绀明显', '腹部压痛阳性']

=== 临床表现 ===
Vanilla 第1轮生成:
生成内容: ['双下肢水肿明显', '口周发绀明显', '腹部压痛阳性']

=== 临床表现 ===
Vanilla 第1轮生成:
生成内容: ['双下肢水肿明显', '口周发绀明显', '腹部压痛阳性']
保留: 3个
过滤: 0个

=== 临床表现 ===
Latent[医学描述] 第1轮生成:
API原始返回: '急性心肌梗死导致的胸骨后压榨性疼痛  \n突发性左下腹剧痛伴肌紧张  \n慢性阻塞性肺疾病引起的气促伴双肺哮鸣音  \n急性肾盂肾炎所致的腰背部叩击痛  \n病毒性脑膜炎引发的喷射性呕吐  \n类风湿关节炎导致的晨僵现象  \n糖尿病酮症酸中毒引起的呼吸深快  \n急性阑尾炎伴右下腹固定压痛点  \n高血压危象引发的剧烈头痛  \n系统性红斑狼疮伴面部蝶形红斑  \n脑卒中后出现的右侧肢体偏瘫  \n慢性心力衰竭导致的端坐呼吸  \n结核性腹膜炎引起的腹部柔韧感  \n急性胰腺炎伴左上腹压痛及反跳痛  \n重症肌无力所致的晨轻暮重症状  \n肝性脑病引发的扑翼样震颤  \n急性荨麻疹伴皮肤风团样改变  \n股骨头坏死导致的跛行步态  \n过敏性休克引起的血压骤降'
清洗后内容: ['急性心肌梗死导致的胸骨后压榨性疼痛', '突发性左下腹剧痛伴肌紧张', '慢性阻塞性肺疾病引起的气促伴双肺哮鸣音', '急性肾盂肾炎所致的腰背部叩击痛', '病毒性脑膜炎引发的喷射性呕吐', '类风湿关节炎导致的晨僵现象', '糖尿病酮症酸中毒引起的呼吸深快', '急性阑尾炎伴右下腹固定压痛点', '高血压危象引发的剧烈头痛', '系统性红斑狼疮伴面部蝶形红斑', '脑卒中后出现的右侧肢体偏瘫', '慢性心力衰竭导致的端坐呼吸', '结核性腹膜炎引起的腹部柔韧感', '急性胰腺炎伴左上腹压痛及反跳痛', '重症肌无力所致的晨轻暮重症状', '肝性脑病引发的扑翼样震颤', '急性荨麻疹伴皮肤风团样改变', '股骨头坏死导致的跛行步态', '过敏性休克引起的血压骤降']

=== 临床表现 ===
Latent[医学描述] 第1轮生成:
生成内容: ['急性心肌梗死导致的胸骨后压榨性疼痛', '突发性左下腹剧痛伴肌紧张', '慢性阻塞性肺疾病引起的气促伴双肺哮鸣音', '急性肾盂肾炎所致的腰背部叩击痛', '病毒性脑膜炎引发的喷射性呕吐', '类风湿关节炎导致的晨僵现象', '糖尿病酮症酸中毒引起的呼吸深快', '急性阑尾炎伴右下腹固定压痛点', '高血压危象引发的剧烈头痛', '系统性红斑狼疮伴面部蝶形红斑', '脑卒中后出现的右侧肢体偏瘫', '慢性心力衰竭导致的端坐呼吸', '结核性腹膜炎引起的腹部柔韧感', '急性胰腺炎伴左上腹压痛及反跳痛', '重症肌无力所致的晨轻暮重症状', '肝性脑病引发的扑翼样震颤', '急性荨麻疹伴皮肤风团样改变', '股骨头坏死导致的跛行步态', '过敏性休克引起的血压骤降']

=== 临床表现 ===
Latent[医学描述] 第1轮生成:
生成内容: ['急性心肌梗死导致的胸骨后压榨性疼痛', '突发性左下腹剧痛伴肌紧张', '慢性阻塞性肺疾病引起的气促伴双肺哮鸣音', '急性肾盂肾炎所致的腰背部叩击痛', '病毒性脑膜炎引发的喷射性呕吐', '类风湿关节炎导致的晨僵现象', '糖尿病酮症酸中毒引起的呼吸深快', '急性阑尾炎伴右下腹固定压痛点', '高血压危象引发的剧烈头痛', '系统性红斑狼疮伴面部蝶形红斑', '脑卒中后出现的右侧肢体偏瘫', '慢性心力衰竭导致的端坐呼吸', '结核性腹膜炎引起的腹部柔韧感', '急性胰腺炎伴左上腹压痛及反跳痛', '重症肌无力所致的晨轻暮重症状', '肝性脑病引发的扑翼样震颤', '急性荨麻疹伴皮肤风团样改变', '股骨头坏死导致的跛行步态', '过敏性休克引起的血压骤降']
保留: 19个
过滤: 0个

=== 临床表现 ===
Latent[学术论文] 第1轮生成:
API原始返回: '外周血白细胞计数升高  \n肺部浸润性病变  \n血清肌钙蛋白水平异常  \n持续性低氧血症  \n心电图ST段抬高  \n肝功能酶学指标异常  \n脑脊液压力升高  \n尿蛋白定量检测阳性  \n凝血功能指标延长  \n关节腔积液形成  \n甲状腺功能减退表现  \n视网膜血管阻塞征象  \n骨髓穿刺细胞学异常  \n皮肤黏膜广泛出血点  \n神经系统反射亢进  \n腹部超声显示包块  \n血培养病原体阳性  \n听力阈值显著下降  \n外周神经传导速度减慢'
清洗后内容: ['外周血白细胞计数升高', '肺部浸润性病变', '血清肌钙蛋白水平异常', '持续性低氧血症', '心电图ST段抬高', '肝功能酶学指标异常', '脑脊液压力升高', '尿蛋白定量检测阳性', '凝血功能指标延长', '关节腔积液形成', '甲状腺功能减退表现', '视网膜血管阻塞征象', '骨髓穿刺细胞学异常', '皮肤黏膜广泛出血点', '神经系统反射亢进', '腹部超声显示包块', '血培养病原体阳性', '听力阈值显著下降', '外周神经传导速度减慢']

=== 临床表现 ===
Latent[学术论文] 第1轮生成:
生成内容: ['外周血白细胞计数升高', '肺部浸润性病变', '血清肌钙蛋白水平异常', '持续性低氧血症', '心电图ST段抬高', '肝功能酶学指标异常', '脑脊液压力升高', '尿蛋白定量检测阳性', '凝血功能指标延长', '关节腔积液形成', '甲状腺功能减退表现', '视网膜血管阻塞征象', '骨髓穿刺细胞学异常', '皮肤黏膜广泛出血点', '神经系统反射亢进', '腹部超声显示包块', '血培养病原体阳性', '听力阈值显著下降', '外周神经传导速度减慢']

=== 临床表现 ===
Latent[学术论文] 第1轮生成:
生成内容: ['外周血白细胞计数升高', '肺部浸润性病变', '血清肌钙蛋白水平异常', '持续性低氧血症', '心电图ST段抬高', '肝功能酶学指标异常', '脑脊液压力升高', '尿蛋白定量检测阳性', '凝血功能指标延长', '关节腔积液形成', '甲状腺功能减退表现', '视网膜血管阻塞征象', '骨髓穿刺细胞学异常', '皮肤黏膜广泛出血点', '神经系统反射亢进', '腹部超声显示包块', '血培养病原体阳性', '听力阈值显著下降', '外周神经传导速度减慢']
保留: 19个
过滤: 0个

=== 医疗程序 ===
Vanilla 第1轮生成:
API原始返回: '经皮椎体成形术  \n关节镜下半月板修复术  \n支气管镜下肺活检术'
清洗后内容: ['经皮椎体成形术', '关节镜下半月板修复术', '支气管镜下肺活检术']

=== 医疗程序 ===
Vanilla 第1轮生成:
生成内容: ['经皮椎体成形术', '关节镜下半月板修复术', '支气管镜下肺活检术']

=== 医疗程序 ===
Vanilla 第1轮生成:
生成内容: ['经皮椎体成形术', '关节镜下半月板修复术', '支气管镜下肺活检术']
保留: 3个
过滤: 0个

=== 医疗程序 ===
Latent[医学描述] 第1轮生成:
API原始返回: '胸腔镜肺叶切除术  \n内镜下黏膜剥离术  \n椎间孔镜下椎间盘摘除术  \n关节镜下膝关节清理术  \n经尿道前列腺电切术  \n超声引导下穿刺活检术  \n支气管镜下异物取出术  \n经皮肾镜碎石取石术  \n腹腔镜下阑尾切除术  \n数字减影血管造影检查  \n经颅多普勒超声检查  \n心脏射频消融术  \n经皮肝穿刺胆道引流术  \n宫腔镜下子宫内膜切除术  \n经鼻内镜下鼻窦开放术  \n动脉粥样硬化斑块切除术  \n经皮椎体成形术  \n腹腔镜下卵巢囊肿剥除术  \n经皮穿刺肺活检术'
清洗后内容: ['胸腔镜肺叶切除术', '内镜下黏膜剥离术', '椎间孔镜下椎间盘摘除术', '关节镜下膝关节清理术', '经尿道前列腺电切术', '超声引导下穿刺活检术', '支气管镜下异物取出术', '经皮肾镜碎石取石术', '腹腔镜下阑尾切除术', '数字减影血管造影检查', '经颅多普勒超声检查', '心脏射频消融术', '经皮肝穿刺胆道引流术', '宫腔镜下子宫内膜切除术', '经鼻内镜下鼻窦开放术', '动脉粥样硬化斑块切除术', '经皮椎体成形术', '腹腔镜下卵巢囊肿剥除术', '经皮穿刺肺活检术']

=== 医疗程序 ===
Latent[医学描述] 第1轮生成:
生成内容: ['胸腔镜肺叶切除术', '内镜下黏膜剥离术', '椎间孔镜下椎间盘摘除术', '关节镜下膝关节清理术', '经尿道前列腺电切术', '超声引导下穿刺活检术', '支气管镜下异物取出术', '经皮肾镜碎石取石术', '腹腔镜下阑尾切除术', '数字减影血管造影检查', '经颅多普勒超声检查', '心脏射频消融术', '经皮肝穿刺胆道引流术', '宫腔镜下子宫内膜切除术', '经鼻内镜下鼻窦开放术', '动脉粥样硬化斑块切除术', '经皮椎体成形术', '腹腔镜下卵巢囊肿剥除术', '经皮穿刺肺活检术']

=== 医疗程序 ===
Latent[医学描述] 第1轮生成:
生成内容: ['胸腔镜肺叶切除术', '内镜下黏膜剥离术', '椎间孔镜下椎间盘摘除术', '关节镜下膝关节清理术', '经尿道前列腺电切术', '超声引导下穿刺活检术', '支气管镜下异物取出术', '经皮肾镜碎石取石术', '腹腔镜下阑尾切除术', '数字减影血管造影检查', '经颅多普勒超声检查', '心脏射频消融术', '经皮肝穿刺胆道引流术', '宫腔镜下子宫内膜切除术', '经鼻内镜下鼻窦开放术', '动脉粥样硬化斑块切除术', '经皮椎体成形术', '腹腔镜下卵巢囊肿剥除术', '经皮穿刺肺活检术']
保留: 19个
过滤: 0个

=== 医疗程序 ===
Latent[学术论文] 第1轮生成:
API原始返回: '超声引导下经皮肾穿刺活检术  \n数字减影血管造影检查  \n内镜下黏膜剥离术  \n冷冻消融治疗  \n立体定向脑活检术  \n经皮椎体成形术  \n高场强MRI扫描  \n腹腔镜下阑尾切除术  \n关节镜下半月板修复术  \n荧光素眼底血管造影  \n射频消融术治疗心律失常  \n胸腔镜下肺叶切除术  \n术中超声引导下神经监测  \nCT引导下肺穿刺活检  \n超声心动图检查  \n内镜逆行胰胆管造影  \n前列腺癌根治性切除术  \n宫腔镜下子宫内膜电切术  \n钬激光碎石术'
清洗后内容: ['超声引导下经皮肾穿刺活检术', '数字减影血管造影检查', '内镜下黏膜剥离术', '冷冻消融治疗', '立体定向脑活检术', '经皮椎体成形术', '高场强MRI扫描', '腹腔镜下阑尾切除术', '关节镜下半月板修复术', '荧光素眼底血管造影', '射频消融术治疗心律失常', '胸腔镜下肺叶切除术', '术中超声引导下神经监测', 'CT引导下肺穿刺活检', '超声心动图检查', '内镜逆行胰胆管造影', '前列腺癌根治性切除术', '宫腔镜下子宫内膜电切术', '钬激光碎石术']

=== 医疗程序 ===
Latent[学术论文] 第1轮生成:
生成内容: ['超声引导下经皮肾穿刺活检术', '数字减影血管造影检查', '内镜下黏膜剥离术', '冷冻消融治疗', '立体定向脑活检术', '经皮椎体成形术', '高场强MRI扫描', '腹腔镜下阑尾切除术', '关节镜下半月板修复术', '荧光素眼底血管造影', '射频消融术治疗心律失常', '胸腔镜下肺叶切除术', '术中超声引导下神经监测', 'CT引导下肺穿刺活检', '超声心动图检查', '内镜逆行胰胆管造影', '前列腺癌根治性切除术', '宫腔镜下子宫内膜电切术', '钬激光碎石术']

=== 医疗程序 ===
Latent[学术论文] 第1轮生成:
生成内容: ['超声引导下经皮肾穿刺活检术', '数字减影血管造影检查', '内镜下黏膜剥离术', '冷冻消融治疗', '立体定向脑活检术', '经皮椎体成形术', '高场强MRI扫描', '腹腔镜下阑尾切除术', '关节镜下半月板修复术', '荧光素眼底血管造影', '射频消融术治疗心律失常', '胸腔镜下肺叶切除术', '术中超声引导下神经监测', 'CT引导下肺穿刺活检', '超声心动图检查', '内镜逆行胰胆管造影', '前列腺癌根治性切除术', '宫腔镜下子宫内膜电切术', '钬激光碎石术']
保留: 19个
过滤: 0个

=== 过敏信息 ===
Vanilla 第1轮生成:
API原始返回: '对芒果皮过敏'
清洗后内容: ['对芒果皮过敏']

=== 过敏信息 ===
Vanilla 第1轮生成:
生成内容: ['对芒果皮过敏']

=== 过敏信息 ===
Vanilla 第1轮生成:
生成内容: ['对芒果皮过敏']
保留: 1个
过滤: 0个

=== 过敏信息 ===
Latent[医学描述] 第1轮生成:
API原始返回: '对花生蛋白致敏原阳性反应  \n对牛奶β-酪蛋白过敏  \n对鱼精蛋白过敏史  \n对芒果皮接触性皮炎  \n对鸡蛋卵白蛋白不耐受  \n对小麦麸质敏感  \n对大豆凝集素过敏  \n对蜜蜂毒液过敏  \n对柑橘类水果皮疹  \n对磺胺类药物过敏  \n对青霉素G皮试阳性  \n对阿司匹林不耐受  \n对镍离子接触性皮炎  \n对羽毛蛋白过敏  \n对蜂胶提取物过敏  \n对酒精代谢障碍  \n对甲醛接触性荨麻疹  \n对氯霉素类抗生素过敏  \n对橡胶乳胶蛋白过敏  \n对甲醛接触性荨麻疹'
清洗后内容: ['对花生蛋白致敏原阳性反应', '对牛奶β-酪蛋白过敏', '对鱼精蛋白过敏史', '对芒果皮接触性皮炎', '对鸡蛋卵白蛋白不耐受', '对小麦麸质敏感', '对大豆凝集素过敏', '对蜜蜂毒液过敏', '对柑橘类水果皮疹', '对磺胺类药物过敏', '对青霉素G皮试阳性', '对阿司匹林不耐受', '对镍离子接触性皮炎', '对羽毛蛋白过敏', '对蜂胶提取物过敏', '对酒精代谢障碍', '对甲醛接触性荨麻疹', '对氯霉素类抗生素过敏', '对橡胶乳胶蛋白过敏', '对甲醛接触性荨麻疹']

=== 过敏信息 ===
Latent[医学描述] 第1轮生成:
生成内容: ['对花生蛋白致敏原阳性反应', '对牛奶β-酪蛋白过敏', '对鱼精蛋白过敏史', '对芒果皮接触性皮炎', '对鸡蛋卵白蛋白不耐受', '对小麦麸质敏感', '对大豆凝集素过敏', '对蜜蜂毒液过敏', '对柑橘类水果皮疹', '对磺胺类药物过敏', '对青霉素G皮试阳性', '对阿司匹林不耐受', '对镍离子接触性皮炎', '对羽毛蛋白过敏', '对蜂胶提取物过敏', '对酒精代谢障碍', '对甲醛接触性荨麻疹', '对氯霉素类抗生素过敏', '对橡胶乳胶蛋白过敏', '对甲醛接触性荨麻疹']

=== 过敏信息 ===
Latent[医学描述] 第1轮生成:
生成内容: ['对花生蛋白致敏原阳性反应', '对牛奶β-酪蛋白过敏', '对鱼精蛋白过敏史', '对芒果皮接触性皮炎', '对鸡蛋卵白蛋白不耐受', '对小麦麸质敏感', '对大豆凝集素过敏', '对蜜蜂毒液过敏', '对柑橘类水果皮疹', '对磺胺类药物过敏', '对青霉素G皮试阳性', '对阿司匹林不耐受', '对镍离子接触性皮炎', '对羽毛蛋白过敏', '对蜂胶提取物过敏', '对酒精代谢障碍', '对甲醛接触性荨麻疹', '对氯霉素类抗生素过敏', '对橡胶乳胶蛋白过敏', '对甲醛接触性荨麻疹']
保留: 20个
过滤: 0个

=== 过敏信息 ===
Latent[法律文本] 第1轮生成:
API原始返回: '对磺胺类药物过敏  \n对青霉素类抗生素过敏  \n对头孢类抗生素过敏  \n对阿司匹林过敏  \n对碘制剂过敏  \n对鱼虾蟹类海鲜过敏  \n对花生及坚果类过敏  \n对牛奶及乳制品过敏  \n对鸡蛋及蛋制品过敏  \n对大豆及豆制品过敏  \n对小麦及麸质过敏  \n对芒果及热带水果过敏  \n对橡胶及乳胶制品过敏  \n对金属镍及镍合金过敏  \n对酒精及乙醇类过敏  \n对甲醛及甲醛制品过敏  \n对苯乙烯及苯系物过敏  \n对氯霉素类抗生素过敏  \n对头孢曲松钠过敏  \n对青霉素V钾过敏  \n对头孢克洛过敏'
清洗后内容: ['对磺胺类药物过敏', '对青霉素类抗生素过敏', '对头孢类抗生素过敏', '对阿司匹林过敏', '对碘制剂过敏', '对鱼虾蟹类海鲜过敏', '对花生及坚果类过敏', '对牛奶及乳制品过敏', '对鸡蛋及蛋制品过敏', '对大豆及豆制品过敏', '对小麦及麸质过敏', '对芒果及热带水果过敏', '对橡胶及乳胶制品过敏', '对金属镍及镍合金过敏', '对酒精及乙醇类过敏', '对甲醛及甲醛制品过敏', '对苯乙烯及苯系物过敏', '对氯霉素类抗生素过敏', '对头孢曲松钠过敏', '对青霉素V钾过敏', '对头孢克洛过敏']

=== 过敏信息 ===
Latent[法律文本] 第1轮生成:
生成内容: ['对磺胺类药物过敏', '对青霉素类抗生素过敏', '对头孢类抗生素过敏', '对阿司匹林过敏', '对碘制剂过敏', '对鱼虾蟹类海鲜过敏', '对花生及坚果类过敏', '对牛奶及乳制品过敏', '对鸡蛋及蛋制品过敏', '对大豆及豆制品过敏', '对小麦及麸质过敏', '对芒果及热带水果过敏', '对橡胶及乳胶制品过敏', '对金属镍及镍合金过敏', '对酒精及乙醇类过敏', '对甲醛及甲醛制品过敏', '对苯乙烯及苯系物过敏', '对氯霉素类抗生素过敏', '对头孢曲松钠过敏', '对青霉素V钾过敏', '对头孢克洛过敏']

=== 过敏信息 ===
Latent[法律文本] 第1轮生成:
生成内容: ['对磺胺类药物过敏', '对青霉素类抗生素过敏', '对头孢类抗生素过敏', '对阿司匹林过敏', '对碘制剂过敏', '对鱼虾蟹类海鲜过敏', '对花生及坚果类过敏', '对牛奶及乳制品过敏', '对鸡蛋及蛋制品过敏', '对大豆及豆制品过敏', '对小麦及麸质过敏', '对芒果及热带水果过敏', '对橡胶及乳胶制品过敏', '对金属镍及镍合金过敏', '对酒精及乙醇类过敏', '对甲醛及甲醛制品过敏', '对苯乙烯及苯系物过敏', '对氯霉素类抗生素过敏', '对头孢曲松钠过敏', '对青霉素V钾过敏', '对头孢克洛过敏']
保留: 21个
过滤: 0个

=== 生育信息 ===
Vanilla 第1轮生成:
API原始返回: '已育二子一女  \n一胎顺产  \n人工流产一次  \n绝育手术完成  \n双胞胎妈妈  \n生育史无异常'
清洗后内容: ['已育二子一女', '一胎顺产', '人工流产一次', '绝育手术完成', '双胞胎妈妈', '生育史无异常']

=== 生育信息 ===
Vanilla 第1轮生成:
生成内容: ['已育二子一女', '一胎顺产', '人工流产一次', '绝育手术完成', '双胞胎妈妈', '生育史无异常']

=== 生育信息 ===
Vanilla 第1轮生成:
生成内容: ['已育二子一女', '一胎顺产', '人工流产一次', '绝育手术完成', '双胞胎妈妈', '生育史无异常']
保留: 6个
过滤: 0个

=== 生育信息 ===
Latent[医学描述] 第1轮生成:
API原始返回: '剖宫产史1次  \n足月顺产1胎  \n双胎妊娠史  \n人工流产2次  \n输卵管结扎术后  \n绝育手术史  \n葡萄胎清宫1次  \n宫颈锥切术后  \n宫外孕手术史  \n早产史1次  \n多囊卵巢综合征  \n不孕症治疗史'
清洗后内容: ['剖宫产史1次', '足月顺产1胎', '双胎妊娠史', '人工流产2次', '输卵管结扎术后', '绝育手术史', '葡萄胎清宫1次', '宫颈锥切术后', '宫外孕手术史', '早产史1次', '多囊卵巢综合征', '不孕症治疗史']

=== 生育信息 ===
Latent[医学描述] 第1轮生成:
生成内容: ['剖宫产史1次', '足月顺产1胎', '双胎妊娠史', '人工流产2次', '输卵管结扎术后', '绝育手术史', '葡萄胎清宫1次', '宫颈锥切术后', '宫外孕手术史', '早产史1次', '多囊卵巢综合征', '不孕症治疗史']

=== 生育信息 ===
Latent[医学描述] 第1轮生成:
生成内容: ['剖宫产史1次', '足月顺产1胎', '双胎妊娠史', '人工流产2次', '输卵管结扎术后', '绝育手术史', '葡萄胎清宫1次', '宫颈锥切术后', '宫外孕手术史', '早产史1次', '多囊卵巢综合征', '不孕症治疗史']
保留: 12个
过滤: 0个

=== 生育信息 ===
Latent[法律文本] 第1轮生成:
API原始返回: '已生育一子且无其他子女的当事人  \n未婚且无生育记录的自然人  \n已育有两名未成年子女的申请人  \n未生育且无领养子女的公民  \n已生育一名子女且符合政策规定的当事人  \n无生育史且无子女抚养记录的申请人  \n已育有一子一女的家庭成员  \n未生育且无子女抚养责任的公民  \n已生育两名子女且均已成年的人员  \n无生育记录且无子女的当事人  \n已育有一名子女且符合计划生育政策的申请人  \n未生育且无子女抚养经历的公民'
清洗后内容: ['已生育一子且无其他子女的当事人', '未婚且无生育记录的自然人', '已育有两名未成年子女的申请人', '未生育且无领养子女的公民', '已生育一名子女且符合政策规定的当事人', '无生育史且无子女抚养记录的申请人', '已育有一子一女的家庭成员', '未生育且无子女抚养责任的公民', '已生育两名子女且均已成年的人员', '无生育记录且无子女的当事人', '已育有一名子女且符合计划生育政策的申请人', '未生育且无子女抚养经历的公民']

=== 生育信息 ===
Latent[法律文本] 第1轮生成:
生成内容: ['已生育一子且无其他子女的当事人', '未婚且无生育记录的自然人', '已育有两名未成年子女的申请人', '未生育且无领养子女的公民', '已生育一名子女且符合政策规定的当事人', '无生育史且无子女抚养记录的申请人', '已育有一子一女的家庭成员', '未生育且无子女抚养责任的公民', '已生育两名子女且均已成年的人员', '无生育记录且无子女的当事人', '已育有一名子女且符合计划生育政策的申请人', '未生育且无子女抚养经历的公民']

=== 生育信息 ===
Latent[法律文本] 第1轮生成:
生成内容: ['已生育一子且无其他子女的当事人', '未婚且无生育记录的自然人', '已育有两名未成年子女的申请人', '未生育且无领养子女的公民', '已生育一名子女且符合政策规定的当事人', '无生育史且无子女抚养记录的申请人', '已育有一子一女的家庭成员', '未生育且无子女抚养责任的公民', '已生育两名子女且均已成年的人员', '无生育记录且无子女的当事人', '已育有一名子女且符合计划生育政策的申请人', '未生育且无子女抚养经历的公民']
保留: 9个
过滤: 3个
过滤详情:
  - "已育有一子一女的家庭成员" (抽象)
  - "已生育两名子女且均已成年的人员" (抽象)
  - "未生育且无子女抚养经历的公民" (抽象)

=== 生育信息 ===
Latent[新闻报道] 第1轮生成:
API原始返回: '已生育一胎顺产  \n计划三胎生育  \n自然分娩两孩  \n剖宫产后再育  \n生育间隔三年  \n二胎政策受益者  \n首次生育经历  \n高龄产妇生育  \n双胞胎生育史  \n生育后再次怀孕  \n符合二孩政策  \n完成生育计划'
清洗后内容: ['已生育一胎顺产', '计划三胎生育', '自然分娩两孩', '剖宫产后再育', '生育间隔三年', '二胎政策受益者', '首次生育经历', '高龄产妇生育', '双胞胎生育史', '生育后再次怀孕', '符合二孩政策', '完成生育计划']

=== 生育信息 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['已生育一胎顺产', '计划三胎生育', '自然分娩两孩', '剖宫产后再育', '生育间隔三年', '二胎政策受益者', '首次生育经历', '高龄产妇生育', '双胞胎生育史', '生育后再次怀孕', '符合二孩政策', '完成生育计划']

=== 生育信息 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['已生育一胎顺产', '计划三胎生育', '自然分娩两孩', '剖宫产后再育', '生育间隔三年', '二胎政策受益者', '首次生育经历', '高龄产妇生育', '双胞胎生育史', '生育后再次怀孕', '符合二孩政策', '完成生育计划']
保留: 11个
过滤: 1个
过滤详情:
  - "首次生育经历" (抽象)

=== 地理位置 ===
Vanilla 第1轮生成:
API原始返回: '贵州省黔东南州凯里市鼓楼广场  \n四川省乐山市五通桥区竹根镇  \n浙江省金华市义乌市稠江街道  \n陕西省延安市宝塔区王家坪  \n广东省深圳市南山区科技园  \n河北省石家庄市长安区东风路  \n山东省青岛市黄岛区薛家岛  \n福建省厦门市思明区环岛路  \n湖南省长沙市岳麓区梅溪湖  \n江苏省南京市江宁区百家湖'
清洗后内容: ['贵州省黔东南州凯里市鼓楼广场', '四川省乐山市五通桥区竹根镇', '浙江省金华市义乌市稠江街道', '陕西省延安市宝塔区王家坪', '广东省深圳市南山区科技园', '河北省石家庄市长安区东风路', '山东省青岛市黄岛区薛家岛', '福建省厦门市思明区环岛路', '湖南省长沙市岳麓区梅溪湖', '江苏省南京市江宁区百家湖']

=== 地理位置 ===
Vanilla 第1轮生成:
生成内容: ['贵州省黔东南州凯里市鼓楼广场', '四川省乐山市五通桥区竹根镇', '浙江省金华市义乌市稠江街道', '陕西省延安市宝塔区王家坪', '广东省深圳市南山区科技园', '河北省石家庄市长安区东风路', '山东省青岛市黄岛区薛家岛', '福建省厦门市思明区环岛路', '湖南省长沙市岳麓区梅溪湖', '江苏省南京市江宁区百家湖']

=== 地理位置 ===
Vanilla 第1轮生成:
生成内容: ['贵州省黔东南州凯里市鼓楼广场', '四川省乐山市五通桥区竹根镇', '浙江省金华市义乌市稠江街道', '陕西省延安市宝塔区王家坪', '广东省深圳市南山区科技园', '河北省石家庄市长安区东风路', '山东省青岛市黄岛区薛家岛', '福建省厦门市思明区环岛路', '湖南省长沙市岳麓区梅溪湖', '江苏省南京市江宁区百家湖']
保留: 10个
过滤: 0个

=== 地理位置 ===
Latent[社交场合] 第1轮生成:
API原始返回: '朝阳区蓝色港湾  \n海淀区五道口  \n静安区嘉里中心  \n陆家嘴金融区  \n上海新天地  \n北京798艺术区'
清洗后内容: ['朝阳区蓝色港湾', '海淀区五道口', '静安区嘉里中心', '陆家嘴金融区', '上海新天地', '北京798艺术区']

=== 地理位置 ===
Latent[社交场合] 第1轮生成:
生成内容: ['朝阳区蓝色港湾', '海淀区五道口', '静安区嘉里中心', '陆家嘴金融区', '上海新天地', '北京798艺术区']

=== 地理位置 ===
Latent[社交场合] 第1轮生成:
生成内容: ['朝阳区蓝色港湾', '海淀区五道口', '静安区嘉里中心', '陆家嘴金融区', '上海新天地', '北京798艺术区']
保留: 6个
过滤: 0个

=== 地理位置 ===
Latent[新闻报道] 第1轮生成:
API原始返回: '北京朝阳区三里屯  \n上海黄浦区外滩  \n广州天河区珠江新城  \n深圳南山区科技园  \n成都锦江区春熙路  \n杭州西湖区灵隐寺'
清洗后内容: ['北京朝阳区三里屯', '上海黄浦区外滩', '广州天河区珠江新城', '深圳南山区科技园', '成都锦江区春熙路', '杭州西湖区灵隐寺']

=== 地理位置 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['北京朝阳区三里屯', '上海黄浦区外滩', '广州天河区珠江新城', '深圳南山区科技园', '成都锦江区春熙路', '杭州西湖区灵隐寺']

=== 地理位置 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['北京朝阳区三里屯', '上海黄浦区外滩', '广州天河区珠江新城', '深圳南山区科技园', '成都锦江区春熙路', '杭州西湖区灵隐寺']
保留: 6个
过滤: 0个

=== 地理位置 ===
Latent[法律文本] 第1轮生成:
API原始返回: '北京市朝阳区建国门外大街甲6号  \n上海市黄浦区南京东路1号  \n广州市天河区珠江新城华夏路10号  \n深圳市南山区科技园南区  \n杭州市西湖区文三路478号  \n成都市锦江区红星路三段1号  \n武汉市江汉区解放大道688号  \n南京市鼓楼区中山北路1号  \n西安市雁塔区高新路25号'
清洗后内容: ['北京市朝阳区建国门外大街甲6号', '上海市黄浦区南京东路1号', '广州市天河区珠江新城华夏路10号', '深圳市南山区科技园南区', '杭州市西湖区文三路478号', '成都市锦江区红星路三段1号', '武汉市江汉区解放大道688号', '南京市鼓楼区中山北路1号', '西安市雁塔区高新路25号']

=== 地理位置 ===
Latent[法律文本] 第1轮生成:
生成内容: ['北京市朝阳区建国门外大街甲6号', '上海市黄浦区南京东路1号', '广州市天河区珠江新城华夏路10号', '深圳市南山区科技园南区', '杭州市西湖区文三路478号', '成都市锦江区红星路三段1号', '武汉市江汉区解放大道688号', '南京市鼓楼区中山北路1号', '西安市雁塔区高新路25号']

=== 地理位置 ===
Latent[法律文本] 第1轮生成:
生成内容: ['北京市朝阳区建国门外大街甲6号', '上海市黄浦区南京东路1号', '广州市天河区珠江新城华夏路10号', '深圳市南山区科技园南区', '杭州市西湖区文三路478号', '成都市锦江区红星路三段1号', '武汉市江汉区解放大道688号', '南京市鼓楼区中山北路1号', '西安市雁塔区高新路25号']
保留: 9个
过滤: 0个

=== 行程信息 ===
Vanilla 第1轮生成:
API原始返回: 'D903次动车  \nF456航班  \nK789次火车  \nBRT12路  \n磁悬浮1号线  \nH567次高铁  \nC912次城际列车'
清洗后内容: ['D903次动车', 'F456航班', 'K789次火车', 'BRT12路', '磁悬浮1号线', 'H567次高铁', 'C912次城际列车']

=== 行程信息 ===
Vanilla 第1轮生成:
生成内容: ['D903次动车', 'F456航班', 'K789次火车', 'BRT12路', '磁悬浮1号线', 'H567次高铁', 'C912次城际列车']

=== 行程信息 ===
Vanilla 第1轮生成:
生成内容: ['D903次动车', 'F456航班', 'K789次火车', 'BRT12路', '磁悬浮1号线', 'H567次高铁', 'C912次城际列车']
保留: 7个
过滤: 0个

=== 行程信息 ===
Latent[新闻报道] 第1轮生成:
API原始返回: '东航MU503航班  \n南航CZ3106次航班  \n海航HU7782次航班  \n春秋9C8265次航班  \n吉祥GJ8831次航班  \n国航CA1834次航班  \n南航CZ3908次航班  \n东航MU2337次航班  \n海航HU7612次航班  \n春秋9C8129次航班  \n吉祥GJ8712次航班  \n国航CA1506次航班'
清洗后内容: ['东航MU503航班', '南航CZ3106次航班', '海航HU7782次航班', '春秋9C8265次航班', '吉祥GJ8831次航班', '国航CA1834次航班', '南航CZ3908次航班', '东航MU2337次航班', '海航HU7612次航班', '春秋9C8129次航班', '吉祥GJ8712次航班', '国航CA1506次航班']

=== 行程信息 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['东航MU503航班', '南航CZ3106次航班', '海航HU7782次航班', '春秋9C8265次航班', '吉祥GJ8831次航班', '国航CA1834次航班', '南航CZ3908次航班', '东航MU2337次航班', '海航HU7612次航班', '春秋9C8129次航班', '吉祥GJ8712次航班', '国航CA1506次航班']

=== 行程信息 ===
Latent[新闻报道] 第1轮生成:
生成内容: ['东航MU503航班', '南航CZ3106次航班', '海航HU7782次航班', '春秋9C8265次航班', '吉祥GJ8831次航班', '国航CA1834次航班', '南航CZ3908次航班', '东航MU2337次航班', '海航HU7612次航班', '春秋9C8129次航班', '吉祥GJ8712次航班', '国航CA1506次航班']
保留: 12个
过滤: 0个

=== 行程信息 ===
Latent[法律文本] 第1轮生成:
API原始返回: '甲方预订的商务差旅安排  \n乙方确认的公务出行计划  \n合同约定的考察团行程  \n双方认可的会议交通方案  \n指定律师的出庭行程表  \n当事人提交的旅行路线单  \n双方签署的出差安排书  \n正式备案的商务访问日程  \n双方同意的商务考察路线  \n合同附件中的交通出行方案  \n双方约定的公务出差规划  \n双方确认的商务洽谈行程'
清洗后内容: ['甲方预订的商务差旅安排', '乙方确认的公务出行计划', '合同约定的考察团行程', '双方认可的会议交通方案', '指定律师的出庭行程表', '当事人提交的旅行路线单', '双方签署的出差安排书', '正式备案的商务访问日程', '双方同意的商务考察路线', '合同附件中的交通出行方案', '双方约定的公务出差规划', '双方确认的商务洽谈行程']

=== 行程信息 ===
Latent[法律文本] 第1轮生成:
生成内容: ['甲方预订的商务差旅安排', '乙方确认的公务出行计划', '合同约定的考察团行程', '双方认可的会议交通方案', '指定律师的出庭行程表', '当事人提交的旅行路线单', '双方签署的出差安排书', '正式备案的商务访问日程', '双方同意的商务考察路线', '合同附件中的交通出行方案', '双方约定的公务出差规划', '双方确认的商务洽谈行程']

=== 行程信息 ===
Latent[法律文本] 第1轮生成:
生成内容: ['甲方预订的商务差旅安排', '乙方确认的公务出行计划', '合同约定的考察团行程', '双方认可的会议交通方案', '指定律师的出庭行程表', '当事人提交的旅行路线单', '双方签署的出差安排书', '正式备案的商务访问日程', '双方同意的商务考察路线', '合同附件中的交通出行方案', '双方约定的公务出差规划', '双方确认的商务洽谈行程']
保留: 12个
过滤: 0个

=== 行程信息 ===
Latent[口语表达] 第1轮生成:
API原始返回: '下周一早上八点的飞机  \n周五晚上十点的火车  \n明天中午十二点的地铁  \n后天下午四点的公交  \n今天晚上七点的轮渡  \n下周三早上六点的班车  \n明天下午五点的长途车  \n后天上午九点的动车  \n本周五晚上八点的城际列车  \n下个月初的北京之行  \n后天中午的上海出发航班  \n明天早上的杭州返程高铁'
清洗后内容: ['下周一早上八点的飞机', '周五晚上十点的火车', '明天中午十二点的地铁', '后天下午四点的公交', '今天晚上七点的轮渡', '下周三早上六点的班车', '明天下午五点的长途车', '后天上午九点的动车', '本周五晚上八点的城际列车', '下个月初的北京之行', '后天中午的上海出发航班', '明天早上的杭州返程高铁']

=== 行程信息 ===
Latent[口语表达] 第1轮生成:
生成内容: ['下周一早上八点的飞机', '周五晚上十点的火车', '明天中午十二点的地铁', '后天下午四点的公交', '今天晚上七点的轮渡', '下周三早上六点的班车', '明天下午五点的长途车', '后天上午九点的动车', '本周五晚上八点的城际列车', '下个月初的北京之行', '后天中午的上海出发航班', '明天早上的杭州返程高铁']

=== 行程信息 ===
Latent[口语表达] 第1轮生成:
生成内容: ['下周一早上八点的飞机', '周五晚上十点的火车', '明天中午十二点的地铁', '后天下午四点的公交', '今天晚上七点的轮渡', '下周三早上六点的班车', '明天下午五点的长途车', '后天上午九点的动车', '本周五晚上八点的城际列车', '下个月初的北京之行', '后天中午的上海出发航班', '明天早上的杭州返程高铁']
保留: 12个
过滤: 0个
