{"start_time": "2025-07-06T16:21:37.521462", "output_directory": "synth_dataset\\runs\\20250706_162137\\strategies", "target_entity_types": ["姓名", "年龄", "性别", "国籍", "职业", "民族", "教育背景", "婚姻状况", "政治倾向", "家庭成员", "工资数额", "投资产品", "税务记录", "信用记录", "实体资产", "交易信息", "疾病", "药物", "临床表现", "医疗程序", "过敏信息", "生育信息", "地理位置", "行程信息"], "total_entity_types": 24, "generation_config": {"dataset_path": "format-dataset/privacy_bench_small.json", "target_count": 10, "max_iterations": 2, "batch_size": 10, "distribution_threshold": 0.05}, "strategy_files": {"target_distribution": "synth_dataset\\runs\\20250706_162137\\strategies\\entity_target\\privacy_bench_target.json", "sentence_diversity": "synth_dataset\\runs\\20250706_162137\\strategies\\sen_diversity\\sen_diversify_value.json", "entity_diversity": "synth_dataset\\runs\\20250706_162137\\strategies\\entity_diversity\\entity_diversity_20250706_162137\\entity_diversity.json"}, "generation_statistics": {"sentence_diversity_attributes": 0, "entity_diversity_pools": 0, "target_distribution_size": 0}, "strategy_statistics": {"strategy_file_sizes": {"target_distribution": 313, "sentence_diversity": 572, "entity_diversity": 309330}, "sentence_diversity_stats": {"total_attributes": 1, "attributes": ["sen_diversify_value"], "total_values": 1, "attribute_value_counts": {"sen_diversify_value": 1}}, "entity_diversity_stats": {"total_entity_types": 24, "entity_type_counts": {"姓名": 431, "年龄": 67, "性别": 65, "国籍": 918, "职业": 421, "民族": 504, "教育背景": 81, "婚姻状况": 74, "政治倾向": 107, "家庭成员": 387, "工资数额": 447, "投资产品": 537, "税务记录": 394, "信用记录": 422, "实体资产": 412, "交易信息": 382, "疾病": 422, "药物": 423, "临床表现": 426, "医疗程序": 424, "过敏信息": 392, "生育信息": 395, "地理位置": 440, "行程信息": 365}, "vanilla_entity_counts": {"姓名": 62, "年龄": 6, "性别": 9, "国籍": 102, "职业": 47, "民族": 56, "教育背景": 18, "婚姻状况": 14, "政治倾向": 20, "家庭成员": 43, "工资数额": 48, "投资产品": 49, "税务记录": 42, "信用记录": 44, "实体资产": 36, "交易信息": 44, "疾病": 49, "药物": 47, "临床表现": 49, "医疗程序": 49, "过敏信息": 49, "生育信息": 36, "地理位置": 47, "行程信息": 47}, "latent_scenario_counts": {"姓名": 8, "年龄": 8, "性别": 8, "国籍": 8, "职业": 8, "民族": 8, "教育背景": 8, "婚姻状况": 8, "政治倾向": 8, "家庭成员": 8, "工资数额": 8, "投资产品": 8, "税务记录": 8, "信用记录": 8, "实体资产": 8, "交易信息": 8, "疾病": 8, "药物": 8, "临床表现": 8, "医疗程序": 8, "过敏信息": 8, "生育信息": 8, "地理位置": 8, "行程信息": 8}, "total_entities": 8936}, "target_distribution_stats": {"total_entity_types": 14, "entity_types": ["年龄", "性别", "婚姻状况", "政治倾向", "家庭成员", "工资数额", "投资产品", "税务记录", "信用记录", "实体资产", "交易信息", "过敏信息", "生育信息", "行程信息"], "target_counts": {"年龄": 10, "性别": 10, "婚姻状况": 10, "政治倾向": 10, "家庭成员": 10, "工资数额": 10, "投资产品": 10, "税务记录": 10, "信用记录": 10, "实体资产": 10, "交易信息": 10, "过敏信息": 10, "生育信息": 10, "行程信息": 10}, "total_target_count": 140, "average_target_count": 10.0, "target_count_distribution": {"10": 14}}}}