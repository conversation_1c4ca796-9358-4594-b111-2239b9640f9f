{"prompts": ["假设你是一名数据隐私专家。请生成15个多样化的、可以归类为姓名的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的姓名类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为年龄的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的年龄类型。", "假设你是一名数据隐私专家。请生成10个多样化的、可以归类为性别的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的性别类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为国籍的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的国籍类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为职业的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的职业类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为种族的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的种族类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为民族的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的民族类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为教育背景的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的教育背景类型。", "假设你是一名数据隐私专家。请生成10个多样化的、可以归类为婚姻状况的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的婚姻状况类型。", "假设你是一名数据隐私专家。请生成10个多样化的、可以归类为政治倾向的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的政治倾向类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为家庭成员的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的家庭成员类型。", "假设你是一名数据隐私专家。请生成25个多样化的、可以归类为工资数额的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的工资数额类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为投资产品的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的投资产品类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为税务记录的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的税务记录类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为信用记录的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的信用记录类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为实体资产的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的实体资产类型。", "假设你是一名数据隐私专家。请生成25个多样化的、可以归类为交易信息的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的交易信息类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为疾病的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的疾病类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为药物的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的药物类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为临床表现的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的临床表现类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为医疗程序的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的医疗程序类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为过敏信息的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的过敏信息类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为生育信息的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的生育信息类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为地理位置的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的地理位置类型。", "假设你是一名数据隐私专家。请生成25个多样化的、可以归类为行程信息的命名实体，每行一个。实体的敏感级别是最高敏感级别这些实体将用于命名实体识别任务中的行程信息类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为姓名的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的姓名类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为年龄的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的年龄类型。", "假设你是一名数据隐私专家。请生成10个多样化的、可以归类为性别的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的性别类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为国籍的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的国籍类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为职业的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的职业类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为种族的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的种族类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为民族的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的民族类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为教育背景的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的教育背景类型。", "假设你是一名数据隐私专家。请生成10个多样化的、可以归类为婚姻状况的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的婚姻状况类型。", "假设你是一名数据隐私专家。请生成10个多样化的、可以归类为政治倾向的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的政治倾向类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为家庭成员的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的家庭成员类型。", "假设你是一名数据隐私专家。请生成25个多样化的、可以归类为工资数额的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的工资数额类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为投资产品的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的投资产品类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为税务记录的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的税务记录类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为信用记录的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的信用记录类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为实体资产的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的实体资产类型。", "假设你是一名数据隐私专家。请生成25个多样化的、可以归类为交易信息的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的交易信息类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为疾病的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的疾病类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为药物的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的药物类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为临床表现的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的临床表现类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为医疗程序的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的医疗程序类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为过敏信息的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的过敏信息类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为生育信息的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的生育信息类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为地理位置的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的地理位置类型。", "假设你是一名数据隐私专家。请生成25个多样化的、可以归类为行程信息的命名实体，每行一个。实体的敏感级别是中等敏感级别这些实体将用于命名实体识别任务中的行程信息类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为姓名的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的姓名类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为年龄的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的年龄类型。", "假设你是一名数据隐私专家。请生成10个多样化的、可以归类为性别的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的性别类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为国籍的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的国籍类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为职业的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的职业类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为种族的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的种族类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为民族的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的民族类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为教育背景的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的教育背景类型。", "假设你是一名数据隐私专家。请生成10个多样化的、可以归类为婚姻状况的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的婚姻状况类型。", "假设你是一名数据隐私专家。请生成10个多样化的、可以归类为政治倾向的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的政治倾向类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为家庭成员的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的家庭成员类型。", "假设你是一名数据隐私专家。请生成25个多样化的、可以归类为工资数额的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的工资数额类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为投资产品的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的投资产品类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为税务记录的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的税务记录类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为信用记录的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的信用记录类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为实体资产的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的实体资产类型。", "假设你是一名数据隐私专家。请生成25个多样化的、可以归类为交易信息的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的交易信息类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为疾病的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的疾病类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为药物的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的药物类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为临床表现的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的临床表现类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为医疗程序的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的医疗程序类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为过敏信息的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的过敏信息类型。", "假设你是一名数据隐私专家。请生成15个多样化的、可以归类为生育信息的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的生育信息类型。", "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为地理位置的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的地理位置类型。", "假设你是一名数据隐私专家。请生成25个多样化的、可以归类为行程信息的命名实体，每行一个。实体的敏感级别是低敏感级别这些实体将用于命名实体识别任务中的行程信息类型。"]}