# main_evaluation.py
# 主评估脚本：整合质量评估和可视化功能

import os
import sys
import json
import argparse
from datetime import datetime
from typing import Dict, Any

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入评估模块
from quality_evaluation import evaluate_dataset_quality, save_evaluation_report
from visualization import create_comprehensive_report, generate_html_report

def collect_run_metadata(args: argparse.Namespace, config: Dict[str, Any]) -> Dict[str, Any]:
    """收集运行元数据，用于结果复现
    
    Args:
        args: 命令行参数
        config: 评估配置
        
    Returns:
        Dict: 包含运行环境、参数、配置等信息的元数据
    """
    import sys
    import platform
    import random
    import numpy as np
    import torch
    
    metadata = {
        "run_info": {
            "timestamp": datetime.now().isoformat(),
            "python_version": sys.version,
            "platform": platform.platform(),
            "command_line_args": vars(args),
        },
        "random_state": {
            "python_seed": random.getstate(),
            "numpy_seed": np.random.get_state()[1][0] if "numpy" in sys.modules else None,
            "torch_seed": torch.initial_seed() if "torch" in sys.modules else None
        },
        "evaluation_config": config
    }
    
    return metadata

def load_evaluation_config() -> Dict[str, Any]:
    """加载评估配置"""
    config_path = "src/synth_eval/evaluation_config.json"
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def create_evaluation_output_dir(base_dir="evaluation") -> str:
    """创建评估输出目录，统一到evaluation/时间戳/"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(base_dir, timestamp)
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def run_comprehensive_evaluation(
    dataset_path: str,
    output_dir: str = None,
    generate_plots: bool = True,
    generate_html: bool = True,
    is_final: bool = True,  # 新增参数，控制是否为最终评估
    original_dataset_path: str = None,  # 新增参数，原数据集路径
    args: argparse.Namespace = None,  # 新增参数，命令行参数
    config: Dict[str, Any] = None,  # 新增参数，评估配置
    iteration_data: List[Dict[str, Any]] = None  # 新增参数，迭代数据
) -> Dict[str, Any]:
    """运行综合评估，支持每轮与最终分离，支持与原数据集对比，支持迭代数据可视化"""
    print("=== 开始综合质量评估 ===")
    print(f"数据集路径：{dataset_path}")
    if original_dataset_path:
        print(f"原数据集路径：{original_dataset_path}")
    
    # 创建输出目录
    if output_dir is None:
        output_dir = create_evaluation_output_dir()
    print(f"输出目录：{output_dir}")
    
    # 生成运行ID（时间戳）
    run_id = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 收集运行元数据
    if args and config:
        metadata = collect_run_metadata(args, config)
    else:
        metadata = {"run_info": {"timestamp": run_id}}
    
    # 执行质量评估
    print("\n1. 执行质量评估...")
    evaluation_result = evaluate_dataset_quality(dataset_path)
    
    # 如果提供了原数据集，也对其进行评估
    original_evaluation_result = None
    if original_dataset_path and os.path.exists(original_dataset_path):
        print("1.1 执行原数据集评估...")
        original_evaluation_result = evaluate_dataset_quality(original_dataset_path)
    
    # 添加元数据到评估结果
    evaluation_result["metadata"] = metadata
    
    # 保存JSON报告（每轮都可保存，带时间戳）
    json_report_path = os.path.join(output_dir, f"evaluation_report_{run_id}.json")
    save_evaluation_report(evaluation_result, json_report_path)
    
    # 仅最终评估时生成图表和HTML报告
    if is_final:
        if generate_plots:
            print("\n2. 生成可视化图表...")
            plots_dir = os.path.join(output_dir, "plots")
            create_comprehensive_report(evaluation_result, plots_dir, original_evaluation_result, iteration_data)
        if generate_html:
            print("\n3. 生成HTML报告...")
            html_report_path = os.path.join(output_dir, f"evaluation_report_{run_id}.html")
            generate_html_report(evaluation_result, html_report_path)
        print("\n4. 生成评估总结...")
        summary_path = os.path.join(output_dir, f"evaluation_summary_{run_id}.txt")
        generate_evaluation_summary(evaluation_result, summary_path)
        print(f"\n=== 评估完成 ===")
        print(f"所有结果已保存到：{output_dir}")
        print(f"运行ID：{run_id}")
    else:
        print("[提示] 本轮为中间评估，仅保存JSON报告，不生成图表和详细报告。")
        print(f"JSON报告：{json_report_path}")
    return evaluation_result

def generate_evaluation_summary(evaluation_result: Dict[str, Any], output_path: str):
    """生成评估总结文本，包含运行元数据"""
    summary_lines = []
    
    # 基本信息
    summary_lines.append("=" * 50)
    summary_lines.append("NER数据集质量评估总结")
    summary_lines.append("=" * 50)
    
    # 运行元数据
    metadata = evaluation_result.get("metadata", {})
    run_info = metadata.get("run_info", {})
    
    summary_lines.append("运行信息：")
    summary_lines.append(f"  评估时间：{evaluation_result['evaluation_time']}")
    summary_lines.append(f"  Python版本：{run_info.get('python_version', '未记录')}")
    summary_lines.append(f"  运行平台：{run_info.get('platform', '未记录')}")
    
    # 命令行参数
    cmd_args = run_info.get("command_line_args", {})
    if cmd_args:
        summary_lines.append("命令行参数：")
        for arg, value in cmd_args.items():
            summary_lines.append(f"  - {arg}: {value}")
    
    # 评估配置
    eval_config = metadata.get("evaluation_config")
    if eval_config:
        summary_lines.append("\n评估配置：")
        summary_lines.append(f"  - 配置文件：{eval_config.get('config_path', '默认配置')}")
        thresholds = eval_config.get("thresholds", {})
        if thresholds:
            summary_lines.append("  - 评估阈值：")
            for metric, value in thresholds.items():
                summary_lines.append(f"    * {metric}: {value}")
    
    summary_lines.append(f"\n整体评估结果：{'通过' if evaluation_result['overall_passed'] else '未通过'}")
    summary_lines.append("")
    
    # 数据集信息
    summary_lines.append("数据集信息：")
    summary_lines.append(f"  总记录数：{evaluation_result['dataset_info']['total_records']}")
    summary_lines.append(f"  总实体数：{evaluation_result['dataset_info']['total_entities']}")
    summary_lines.append(f"  实体类型数：{len(evaluation_result['dataset_info']['entity_types'])}")
    summary_lines.append("")
    
    # 均衡性验证结果
    balance_eval = evaluation_result['balance_evaluation']
    summary_lines.append("均衡性验证：")
    summary_lines.append(f"  验证结果：{'通过' if balance_eval['passed'] else '未通过'}")
    summary_lines.append(f"  整体得分：{balance_eval['overall_score']:.3f}")
    summary_lines.append(f"  覆盖率：{balance_eval['coverage_ratio']:.3f}")
    
    if balance_eval['comparison']['missing_entities']:
        summary_lines.append(f"  缺失实体类型：{balance_eval['comparison']['missing_entities']}")
    if balance_eval['comparison']['excess_entities']:
        summary_lines.append(f"  多余实体类型：{balance_eval['comparison']['excess_entities']}")
    summary_lines.append("")
    
    # 多样性验证结果
    diversity_eval = evaluation_result['diversity_evaluation']
    summary_lines.append("多样性验证：")
    summary_lines.append(f"  验证结果：{'通过' if diversity_eval['passed'] else '未通过'}")
    summary_lines.append(f"  词汇多样性：{diversity_eval['vocabulary_diversity']:.3f}")
    summary_lines.append(f"  句法多样性：{diversity_eval['syntactic_diversity']:.3f}")
    summary_lines.append(f"  语义多样性：{diversity_eval['semantic_diversity']:.3f}")
    summary_lines.append(f"  上下文多样性：{diversity_eval['context_diversity']:.3f}")
    summary_lines.append(f"  实体多样性：{diversity_eval['entity_diversity']['overall']:.3f}")
    summary_lines.append("")
    
    # 详细分布信息
    summary_lines.append("实体分布详情：")
    actual_dist = balance_eval['actual_distribution']
    for entity_type, stats in actual_dist.items():
        summary_lines.append(f"  {entity_type}: {stats['count']} 个 ({stats['percentage']:.1%})")
    summary_lines.append("")
    
    # 建议
    summary_lines.append("改进建议：")
    if not evaluation_result['overall_passed']:
        if not balance_eval['passed']:
            summary_lines.append("  - 均衡性不足：建议调整生成策略，确保各实体类型分布更均衡")
        if not diversity_eval['passed']:
            summary_lines.append("  - 多样性不足：建议增加句子模板和实体池的多样性")
    else:
        summary_lines.append("  - 数据集质量良好，可以投入使用")
    
    # 写入文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(summary_lines))
    
    print(f"评估总结已保存到：{output_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="NER数据集质量评估工具")
    parser.add_argument("dataset_path", help="要评估的数据集文件路径")
    parser.add_argument("--mode", choices=["iter", "final", "compare"], default="final",
                      help="评估模式: iter=迭代评估(快速), final=最终评估(完整), compare=对比评估(需参考数据集)")
    parser.add_argument("--ref-dataset", help="参考数据集路径(compare模式必需)")
    parser.add_argument("--output-dir", help="输出目录路径")
    parser.add_argument("--no-plots", action="store_true", help="不生成可视化图表")
    parser.add_argument("--no-html", action="store_true", help="不生成HTML报告")
    parser.add_argument("--no-naturalness", action="store_true", help="跳过自然度评估(加速迭代评估)")
    parser.add_argument("--config", help="评估配置文件路径")
    args = parser.parse_args()
    
    # 检查数据集文件是否存在
    if not os.path.exists(args.dataset_path):
        print(f"错误：数据集文件不存在：{args.dataset_path}")
        sys.exit(1)
    
    try:
        # 参数验证
        if args.mode == "compare" and not args.ref_dataset:
            print("错误：对比评估模式必须提供参考数据集路径(--ref-dataset)")
            sys.exit(1)
        
        # 加载评估配置
        if args.config:
            with open(args.config, 'r', encoding='utf-8') as f:
                config = json.load(f)
                config["config_path"] = args.config
        else:
            config = load_evaluation_config()
            config["config_path"] = "src/synth_eval/evaluation_config.json"
        
        # 根据模式设置评估参数
        eval_params = {
            "dataset_path": args.dataset_path,
            "output_dir": args.output_dir,
            "mode": args.mode,
            "skip_plots": args.no_plots or args.mode == "iter",
            "skip_naturalness": args.no_naturalness or args.mode == "iter",
            "args": args,
            "config": config
        }
        
        # 对比模式特殊处理
        if args.mode == "compare":
            eval_params["ref_dataset_path"] = args.ref_dataset
            eval_params["generate_plots"] = not args.no_plots  # 对比模式默认生成对比图表
            eval_params["generate_html"] = not args.no_html    # 对比模式默认生成HTML报告
        
        # 运行评估
        evaluation_result = run_comprehensive_evaluation(**eval_params)
        
        # 根据模式输出不同的结果格式
        if args.mode == "iter":
            # 迭代模式：只输出关键指标
            print("\n=== 迭代评估结果 ===")
            print(f"均衡性：{'通过' if evaluation_result['balance_evaluation']['passed'] else '未通过'}")
            print(f"多样性：{evaluation_result['diversity_evaluation']['weighted_score']:.3f}")
            print(f"整体结果：{'通过' if evaluation_result['overall_passed'] else '未通过'}")
            
        elif args.mode == "final":
            # 最终模式：输出完整结果
            print("\n=== 最终评估结果 ===")
            print(f"均衡性：{'通过' if evaluation_result['balance_evaluation']['passed'] else '未通过'}")
            print(f"多样性：{evaluation_result['diversity_evaluation']['weighted_score']:.3f}")
            if not args.no_naturalness:
                print(f"自然度：{evaluation_result['naturalness_evaluation']['avg_score']:.2f}")
            print(f"整体结果：{'通过' if evaluation_result['overall_passed'] else '未通过'}")
            
            if "efficiency_metrics" in evaluation_result:
                print("\n效率指标：")
                metrics = evaluation_result["efficiency_metrics"]
                print(f"总耗时：{metrics['total_evaluation_time']:.2f}秒")
                print(f"处理速度：{metrics['samples_per_second']:.2f}样本/秒")
            
        else:  # compare模式
            # 对比模式：重点展示差异
            print("\n=== 对比评估结果 ===")
            if "comparison" in evaluation_result:
                comp = evaluation_result["comparison"]
                print("\n实体类型变化：")
                print(f"  新增：{comp['distribution_differences']['entity_types']['added']}")
                print(f"  移除：{comp['distribution_differences']['entity_types']['removed']}")
                print("\n指标改进：")
                for metric, change in comp["improvements"].items():
                    print(f"  {metric}: {change:+.3f}")
            print(f"\n整体结果：{'通过' if evaluation_result['overall_passed'] else '未通过'}")
        
    except Exception as e:
        print(f"评估失败：{e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 