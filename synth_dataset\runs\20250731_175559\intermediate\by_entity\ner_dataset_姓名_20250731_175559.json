[{"text": "张伟明天要去北京参加一个重要的会议。", "label": [{"entity": "张伟", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "李娜的钢琴演奏在音乐会上赢得了满堂彩。", "label": [{"entity": "李娜", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "王芳是新来的数学老师，深受学生们的喜爱。", "label": [{"entity": "王芳", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "刘强在篮球比赛中投中了制胜的一球。", "label": [{"entity": "刘强", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "陈晓的绘画作品在艺术展上获得了评委的一致好评。", "label": [{"entity": "陈晓", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "赵敏今天早上在公园里晨练时遇到了老朋友。", "label": [{"entity": "赵敏", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "老朋友", "start_idx": 11, "end_idx": 13, "type": "姓名"}]}, {"text": "杨帆的公司最近签订了一份大合同。", "label": [{"entity": "杨帆", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "周杰在去年的马拉松比赛中获得了冠军。", "label": [{"entity": "周杰", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "孙红的新书已经在各大书店上架销售了。", "label": [{"entity": "孙红", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}]