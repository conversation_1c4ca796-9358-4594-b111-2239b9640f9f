{"sentence": "what is this m night shamalan film starring bruce wilson a child psychiatrist and haley joel osment as a troubled school boy", "tokens": ["what", "is", "this", "m", "night", "shamalan", "film", "starring", "bruce", "wilson", "a", "child", "psychiatrist", "and", "haley", "joel", "osment", "as", "a", "troubled", "school", "boy"], "ner_tags": ["O", "O", "O", "B-Director", "I-Director", "I-Director", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "O", "B-Actor", "I-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what gritty vietnam war drama stars robert den<PERSON> and christopher walken focusing on their capture", "tokens": ["what", "gritty", "vietnam", "war", "drama", "stars", "robert", "deniro", "and", "christopher", "walken", "focusing", "on", "their", "capture"], "ner_tags": ["O", "O", "B-Genre", "I-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Plot"]}
{"sentence": "this 2012 action adventure film was a unique twist on an old fairy tale", "tokens": ["this", "2012", "action", "adventure", "film", "was", "a", "unique", "twist", "on", "an", "old", "fairy", "tale"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "O", "O", "O", "O", "O", "O", "O", "O", "B-Origin", "I-Origin"]}
{"sentence": "comedy starring will ferrell and mark wahlberg as a couple of desk jockeys working for the police who solve a murder", "tokens": ["comedy", "starring", "will", "ferrell", "and", "mark", "wahlberg", "as", "a", "couple", "of", "desk", "jockeys", "working", "for", "the", "police", "who", "solve", "a", "murder"], "ner_tags": ["B-Genre", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie starring t i and some other people in which they steal money and rob a bank", "tokens": ["what", "is", "the", "movie", "starring", "t", "i", "and", "some", "other", "people", "in", "which", "they", "steal", "money", "and", "rob", "a", "bank"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this movie takes place in a small louisiana parish with dolly parton playing one of the lead roles", "tokens": ["this", "movie", "takes", "place", "in", "a", "small", "louisiana", "parish", "with", "dolly", "parton", "playing", "one", "of", "the", "lead", "roles"], "ner_tags": ["O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what romantic comedy has amy adams flying her boyfriend to ireland to take advantage of an old irish tradition", "tokens": ["what", "romantic", "comedy", "has", "amy", "adams", "flying", "her", "boyfriend", "to", "ireland", "to", "take", "advantage", "of", "an", "old", "irish", "tradition"], "ner_tags": ["O", "B-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this movie based on a novel by nicholas sparks tells the story of two lovers separated by war and other various social obstacles", "tokens": ["this", "movie", "based", "on", "a", "novel", "by", "nicholas", "sparks", "tells", "the", "story", "of", "two", "lovers", "separated", "by", "war", "and", "other", "various", "social", "obstacles"], "ner_tags": ["O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 2012 comedy directed by peter hedges starring jennifer garner and joel edgerton", "tokens": ["what", "is", "the", "2012", "comedy", "directed", "by", "peter", "hedges", "starring", "jennifer", "garner", "and", "joel", "edgerton"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "O", "O", "B-Director", "I-Director", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "what movie featured a high school girl accepting gift cards and other goods to help raise her reputation at school", "tokens": ["what", "movie", "featured", "a", "high", "school", "girl", "accepting", "gift", "cards", "and", "other", "goods", "to", "help", "raise", "her", "reputation", "at", "school"], "ner_tags": ["O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "a man gets vision of aliens and builds this mountain out of ingredients in his kitchen", "tokens": ["a", "man", "gets", "vision", "of", "aliens", "and", "builds", "this", "mountain", "out", "of", "ingredients", "in", "his", "kitchen"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of the american movie that is a remake of a famous vampire swedish movie", "tokens": ["what", "is", "the", "name", "of", "the", "american", "movie", "that", "is", "a", "remake", "of", "a", "famous", "vampire", "swedish", "movie"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Relationship", "O", "B-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship"]}
{"sentence": "what 1940 classic film tells the story of a wooden puppet that wants to become a real boy", "tokens": ["what", "1940", "classic", "film", "tells", "the", "story", "of", "a", "wooden", "puppet", "that", "wants", "to", "become", "a", "real", "boy"], "ner_tags": ["O", "B-Year", "B-Opinion", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "2010 movie about a us marshal who helps a stubborn young woman track down her father s killer", "tokens": ["2010", "movie", "about", "a", "us", "marshal", "who", "helps", "a", "stubborn", "young", "woman", "track", "down", "her", "father", "s", "killer"], "ner_tags": ["B-Year", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 2011 american science fiction thriller film directed by j j abrams and starring kyle chandler", "tokens": ["what", "is", "the", "2011", "american", "science", "fiction", "thriller", "film", "directed", "by", "j", "j", "abrams", "and", "starring", "kyle", "chandler"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "I-Genre", "O", "O", "O", "B-Director", "I-Director", "I-Director", "O", "O", "B-Actor", "I-Actor"]}
{"sentence": "can you tell me the name of the film based on the comic strip with the animated dog who causes chaos everywhere he goes", "tokens": ["can", "you", "tell", "me", "the", "name", "of", "the", "film", "based", "on", "the", "comic", "strip", "with", "the", "animated", "dog", "who", "causes", "chaos", "everywhere", "he", "goes"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1971 sergio martino thriller centers around the death of a millionaire and then to the death of his suddenly rich wife", "tokens": ["what", "1971", "sergio", "martino", "thriller", "centers", "around", "the", "death", "of", "a", "millionaire", "and", "then", "to", "the", "death", "of", "his", "suddenly", "rich", "wife"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "B-Genre", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what animated movie has a spell put on the main character", "tokens": ["what", "animated", "movie", "has", "a", "spell", "put", "on", "the", "main", "character"], "ner_tags": ["O", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1972 bob fosse musical is set in berlin during the weimar republic in 1931", "tokens": ["what", "1972", "bob", "fosse", "musical", "is", "set", "in", "berlin", "during", "the", "weimar", "republic", "in", "1931"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "B-Genre", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what is the 1995 film written and directed by jim jarmusch and starring johnny depp", "tokens": ["what", "is", "the", "1995", "film", "written", "and", "directed", "by", "jim", "jarmusch", "and", "starring", "johnny", "depp"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor"]}
{"sentence": "name the 2010 tom cruise film that has a girl that falls in love with a spy", "tokens": ["name", "the", "2010", "tom", "cruise", "film", "that", "has", "a", "girl", "that", "falls", "in", "love", "with", "a", "spy"], "ner_tags": ["O", "O", "B-Year", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "even though there are also orange indigo blue black red and yellow ones this comic book inspired 2011 action hit has ryan reynolds in a superhero suit", "tokens": ["even", "though", "there", "are", "also", "orange", "indigo", "blue", "black", "red", "and", "yellow", "ones", "this", "comic", "book", "inspired", "2011", "action", "hit", "has", "ryan", "reynolds", "in", "a", "superhero", "suit"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "B-Year", "B-Genre", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O"]}
{"sentence": "what ww 2 action film directed by j lee thompson starred gregory peck david niven and anthony quinn", "tokens": ["what", "ww", "2", "action", "film", "directed", "by", "j", "lee", "thompson", "starred", "gregory", "peck", "david", "niven", "and", "anthony", "quinn"], "ner_tags": ["O", "O", "O", "B-Genre", "O", "O", "O", "B-Director", "I-Director", "I-Director", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "in what movie does a robot travel the earth to clean it up for the return of humanity", "tokens": ["in", "what", "movie", "does", "a", "robot", "travel", "the", "earth", "to", "clean", "it", "up", "for", "the", "return", "of", "humanity"], "ner_tags": ["O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "im thinking of the romance drama starring nien jen wu elaine jin and issei ogata about a family asking hard questions about life s meaning", "tokens": ["im", "thinking", "of", "the", "romance", "drama", "starring", "nien", "jen", "wu", "elaine", "jin", "and", "issei", "ogata", "about", "a", "family", "asking", "hard", "questions", "about", "life", "s", "meaning"], "ner_tags": ["O", "O", "O", "O", "B-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what was the name of the pot head what food did he bring to class to share with everyone", "tokens": ["what", "was", "the", "name", "of", "the", "pot", "head", "what", "food", "did", "he", "bring", "to", "class", "to", "share", "with", "everyone"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what dystopian sci fi movie stared harrison ford chasing down rogue robots known as replicants in the future", "tokens": ["what", "dystopian", "sci", "fi", "movie", "stared", "harrison", "ford", "chasing", "down", "rogue", "robots", "known", "as", "replicants", "in", "the", "future"], "ner_tags": ["O", "B-Genre", "I-Genre", "I-Genre", "O", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in this movie a determined cuban immigrant takes over a drug cartel while succumbing to greed", "tokens": ["in", "this", "movie", "a", "determined", "cuban", "immigrant", "takes", "over", "a", "drug", "cartel", "while", "succumbing", "to", "greed"], "ner_tags": ["O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what was the name of the movie starring johnny depp and angelina jolie that was a box office flop", "tokens": ["what", "was", "the", "name", "of", "the", "movie", "starring", "johnny", "depp", "and", "angelina", "jolie", "that", "was", "a", "box", "office", "flop"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O"]}
{"sentence": "tyler perry directed this 2010 movie based on the play by ntozake shange and starring janet jackson and whoopi goldberg", "tokens": ["tyler", "perry", "directed", "this", "2010", "movie", "based", "on", "the", "play", "by", "ntozake", "shange", "and", "starring", "janet", "jackson", "and", "whoopi", "goldberg"], "ner_tags": ["B-Director", "I-Director", "O", "O", "B-Year", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "ryan gosling stars as mysterious hollywood stuntman who finds himself in trouble when he helps out a neighbor in this 2011 crime drama", "tokens": ["ryan", "gosling", "stars", "as", "mysterious", "hollywood", "stuntman", "who", "finds", "himself", "in", "trouble", "when", "he", "helps", "out", "a", "neighbor", "in", "this", "2011", "crime", "drama"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Year", "B-Genre", "I-Genre"]}
{"sentence": "what is the movie about the young little people from lord of the rings growing", "tokens": ["what", "is", "the", "movie", "about", "the", "young", "little", "people", "from", "lord", "of", "the", "rings", "growing"], "ner_tags": ["O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "gawky awkward teen attempts time travel works at a chicken farm and helps his friend run for class president with his dance moves in this cult classic", "tokens": ["gawky", "awkward", "teen", "attempts", "time", "travel", "works", "at", "a", "chicken", "farm", "and", "helps", "his", "friend", "run", "for", "class", "president", "with", "his", "dance", "moves", "in", "this", "cult", "classic"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Genre", "I-Genre"]}
{"sentence": "what 1931 film featured a man hunt in germany for a child killer aided by police and other criminals alike", "tokens": ["what", "1931", "film", "featured", "a", "man", "hunt", "in", "germany", "for", "a", "child", "killer", "aided", "by", "police", "and", "other", "criminals", "alike"], "ner_tags": ["O", "B-Year", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this movie is a 1994 american animated musical drama and features the voice of jonathan taylor thomas", "tokens": ["this", "movie", "is", "a", "1994", "american", "animated", "musical", "drama", "and", "features", "the", "voice", "of", "jonathan", "taylor", "thomas"], "ner_tags": ["O", "O", "O", "O", "B-Year", "O", "B-Genre", "I-Genre", "I-Genre", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "I-Actor"]}
{"sentence": "this 1959 comedy about a boy under a spell was the first live action feature comedy produced by walt disney", "tokens": ["this", "1959", "comedy", "about", "a", "boy", "under", "a", "spell", "was", "the", "first", "live", "action", "feature", "comedy", "produced", "by", "walt", "disney"], "ner_tags": ["O", "B-Year", "B-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "O", "O", "B-Genre", "B-Plot", "I-Plot", "B-Director", "I-Director"]}
{"sentence": "what is the jason statham where he plays a hitman who teaches the art of the job to another guy", "tokens": ["what", "is", "the", "jason", "statham", "where", "he", "plays", "a", "hitman", "who", "teaches", "the", "art", "of", "the", "job", "to", "another", "guy"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "a movie staring bam margera that was the third installment of random hijinks pranks and more", "tokens": ["a", "movie", "staring", "bam", "margera", "that", "was", "the", "third", "installment", "of", "random", "hijinks", "pranks", "and", "more"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Relationship", "I-Relationship", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2001 pixar animated movie stars kevin spacey as the voice of a misfit ant", "tokens": ["what", "2001", "pixar", "animated", "movie", "stars", "kevin", "spacey", "as", "the", "voice", "of", "a", "misfit", "ant"], "ner_tags": ["O", "B-Year", "B-Director", "B-Genre", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "B-Plot", "I-Plot"]}
{"sentence": "what 2012 movie stars channing tatum and jonah hill as underachieving cops sent to go undercover at a high school", "tokens": ["what", "2012", "movie", "stars", "channing", "tatum", "and", "jonah", "hill", "as", "underachieving", "cops", "sent", "to", "go", "undercover", "at", "a", "high", "school"], "ner_tags": ["O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 1966 classic italian spaghetti western starring clint eastwood and directed by sergio leone", "tokens": ["what", "is", "the", "1966", "classic", "italian", "spaghetti", "western", "starring", "clint", "eastwood", "and", "directed", "by", "sergio", "leone"], "ner_tags": ["O", "O", "O", "B-Year", "O", "B-Genre", "I-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Director", "I-Director"]}
{"sentence": "what famous film was the first of a popular movie series to be released yet was subtitled episode iv in the opening scroll", "tokens": ["what", "famous", "film", "was", "the", "first", "of", "a", "popular", "movie", "series", "to", "be", "released", "yet", "was", "subtitled", "episode", "iv", "in", "the", "opening", "scroll"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "which movie stars jason bateman as an agent who is tracking down and escaped alien", "tokens": ["which", "movie", "stars", "jason", "bateman", "as", "an", "agent", "who", "is", "tracking", "down", "and", "escaped", "alien"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "do you remember the movie that has to do with a man wanting to kill a whale", "tokens": ["do", "you", "remember", "the", "movie", "that", "has", "to", "do", "with", "a", "man", "wanting", "to", "kill", "a", "whale"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what animated film gained it s own billing after starring as one portion of disney s famous fantasia", "tokens": ["what", "animated", "film", "gained", "it", "s", "own", "billing", "after", "starring", "as", "one", "portion", "of", "disney", "s", "famous", "fantasia"], "ner_tags": ["O", "B-Genre", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Director", "O", "B-Opinion", "B-Character_Name"]}
{"sentence": "what is the name of that sci fi movie starring drew barrymore as a little girl", "tokens": ["what", "is", "the", "name", "of", "that", "sci", "fi", "movie", "starring", "drew", "barrymore", "as", "a", "little", "girl"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Genre", "I-Genre", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot"]}
{"sentence": "a young teenager without superpowers decides to take the law into his own hands by developing his own heroic identity in this film", "tokens": ["a", "young", "teenager", "without", "superpowers", "decides", "to", "take", "the", "law", "into", "his", "own", "hands", "by", "developing", "his", "own", "heroic", "identity", "in", "this", "film"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O"]}
{"sentence": "what is the movie where superheros such as iron man and thor come together to make a team", "tokens": ["what", "is", "the", "movie", "where", "superheros", "such", "as", "iron", "man", "and", "thor", "come", "together", "to", "make", "a", "team"], "ner_tags": ["O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name", "B-Plot", "B-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the comedy movie starring mike myers where he impersonates a british spy who attempts to track down a dutch criminal", "tokens": ["what", "is", "the", "comedy", "movie", "starring", "mike", "myers", "where", "he", "impersonates", "a", "british", "spy", "who", "attempts", "to", "track", "down", "a", "dutch", "criminal"], "ner_tags": ["O", "O", "O", "B-Genre", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the movie based on a charles portis novel that features a killer joining forces with a young girl", "tokens": ["what", "s", "the", "movie", "based", "on", "a", "charles", "portis", "novel", "that", "features", "a", "killer", "joining", "forces", "with", "a", "young", "girl"], "ner_tags": ["O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "amanda bynes s final film after announcing her retirement from acting she has n t returned ever since", "tokens": ["amanda", "bynes", "s", "final", "film", "after", "announcing", "her", "retirement", "from", "acting", "she", "has", "n", "t", "returned", "ever", "since"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "this is a film in which tom hanks is a cop who has to team up with a dog to catch a murderer", "tokens": ["this", "is", "a", "film", "in", "which", "tom", "hanks", "is", "a", "cop", "who", "has", "to", "team", "up", "with", "a", "dog", "to", "catch", "a", "murderer"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the classic film about a screenwriter who gets trapped by an aged slightly crazy silent film star", "tokens": ["what", "s", "the", "classic", "film", "about", "a", "screenwriter", "who", "gets", "trapped", "by", "an", "aged", "slightly", "crazy", "silent", "film", "star"], "ner_tags": ["O", "O", "O", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1980 harold ramis sport comedy takes place at an exclusive golf course that has a dancing gopher", "tokens": ["what", "1980", "harold", "ramis", "sport", "comedy", "takes", "place", "at", "an", "exclusive", "golf", "course", "that", "has", "a", "dancing", "gopher"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "B-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what was that movie where jack nicholson is chasing around his family in some giant hotel and using an axe to chop into his wifes hiding place", "tokens": ["what", "was", "that", "movie", "where", "jack", "nicholson", "is", "chasing", "around", "his", "family", "in", "some", "giant", "hotel", "and", "using", "an", "axe", "to", "chop", "into", "his", "wifes", "hiding", "place"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the most recent james bond film released in 2012 to great acceptance both in quality and financials", "tokens": ["what", "is", "the", "most", "recent", "james", "bond", "film", "released", "in", "2012", "to", "great", "acceptance", "both", "in", "quality", "and", "financials"], "ner_tags": ["O", "O", "O", "O", "O", "B-Character_Name", "I-Character_Name", "O", "O", "O", "B-Year", "O", "B-Opinion", "I-Opinion", "I-Opinion", "I-Opinion", "I-Opinion", "I-Opinion", "I-Opinion"]}
{"sentence": "i m thinking of a 2011 animation featuring everyone s favorite bear and his friends", "tokens": ["i", "m", "thinking", "of", "a", "2011", "animation", "featuring", "everyone", "s", "favorite", "bear", "and", "his", "friends"], "ner_tags": ["O", "O", "O", "O", "O", "B-Year", "B-Genre", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "ron livingston plays a slacker who would rather woo jennifer aniston than ever go to work again in this 1999 comedy", "tokens": ["ron", "livingston", "plays", "a", "slacker", "who", "would", "rather", "woo", "jennifer", "aniston", "than", "ever", "go", "to", "work", "again", "in", "this", "1999", "comedy"], "ner_tags": ["B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "B-Year", "B-Genre"]}
{"sentence": "made for british television and originally screened by the bbc in april 1995 this film starring amanda root was released theatrically around the rest of the world", "tokens": ["made", "for", "british", "television", "and", "originally", "screened", "by", "the", "bbc", "in", "april", "1995", "this", "film", "starring", "amanda", "root", "was", "released", "theatrically", "around", "the", "rest", "of", "the", "world"], "ner_tags": ["B-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "B-Year", "I-Year", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "i need a horror movie that will keep me up on my sit while watching ready to jump of fear and excitement", "tokens": ["i", "need", "a", "horror", "movie", "that", "will", "keep", "me", "up", "on", "my", "sit", "while", "watching", "ready", "to", "jump", "of", "fear", "and", "excitement"], "ner_tags": ["O", "O", "O", "B-Genre", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what 1998 film starring tom hanks and meg ryan was about a man and a woman that fall in love over the internet", "tokens": ["what", "1998", "film", "starring", "tom", "hanks", "and", "meg", "ryan", "was", "about", "a", "man", "and", "a", "woman", "that", "fall", "in", "love", "over", "the", "internet"], "ner_tags": ["O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what movie about precrime detectives is one of many films based on the writings of phillip k dick", "tokens": ["what", "movie", "about", "precrime", "detectives", "is", "one", "of", "many", "films", "based", "on", "the", "writings", "of", "phillip", "k", "dick"], "ner_tags": ["O", "O", "O", "B-Plot", "I-Plot", "O", "O", "O", "O", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin"]}
{"sentence": "what 1943 movie starring roddy mcdowell follows the bond between joe carraclough and his beloved dog", "tokens": ["what", "1943", "movie", "starring", "roddy", "mcdowell", "follows", "the", "bond", "between", "joe", "carraclough", "and", "his", "beloved", "dog"], "ner_tags": ["O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "david bowie starred as the goblin king in this 1986 jim henson fantasy adventure musical", "tokens": ["david", "bowie", "starred", "as", "the", "goblin", "king", "in", "this", "1986", "jim", "henson", "fantasy", "adventure", "musical"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "B-Character_Name", "I-Character_Name", "O", "O", "B-Year", "B-Director", "I-Director", "B-Genre", "I-Genre", "I-Genre"]}
{"sentence": "what is the claymation movie by tim burton with the skeleton and the kids that kidnap santa clause", "tokens": ["what", "is", "the", "claymation", "movie", "by", "tim", "burton", "with", "the", "skeleton", "and", "the", "kids", "that", "kidnap", "santa", "clause"], "ner_tags": ["O", "O", "O", "B-Genre", "I-Genre", "O", "B-Director", "I-Director", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is that classic movie about a wealthy woman who hires a homeless man to be the family s butler", "tokens": ["what", "is", "that", "classic", "movie", "about", "a", "wealthy", "woman", "who", "hires", "a", "homeless", "man", "to", "be", "the", "family", "s", "butler"], "ner_tags": ["O", "O", "O", "B-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this japanese classic directed by akira kurosawa famously tells the story of a crime from various points of view", "tokens": ["this", "japanese", "classic", "directed", "by", "akira", "kurosawa", "famously", "tells", "the", "story", "of", "a", "crime", "from", "various", "points", "of", "view"], "ner_tags": ["O", "B-Genre", "O", "O", "O", "B-Director", "I-Director", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i m thinking of a 2011 political drama starring and directed by george clooney and also stars ryan gosling", "tokens": ["i", "m", "thinking", "of", "a", "2011", "political", "drama", "starring", "and", "directed", "by", "george", "clooney", "and", "also", "stars", "ryan", "gosling"], "ner_tags": ["O", "O", "O", "O", "O", "B-Year", "B-Genre", "I-Genre", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Actor", "I-Actor"]}
{"sentence": "name the futuristic pixar hit involving a trash organizing robot who falls in love with another robot", "tokens": ["name", "the", "futuristic", "pixar", "hit", "involving", "a", "trash", "organizing", "robot", "who", "falls", "in", "love", "with", "another", "robot"], "ner_tags": ["O", "O", "B-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "the adventures of our 16 th president as a slayer of undead monstrosities in the 1800 s", "tokens": ["the", "adventures", "of", "our", "16", "th", "president", "as", "a", "slayer", "of", "undead", "monstrosities", "in", "the", "1800", "s"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the name of the first movie in which johnny depp stars as captain jack sparrow", "tokens": ["what", "s", "the", "name", "of", "the", "first", "movie", "in", "which", "johnny", "depp", "stars", "as", "captain", "jack", "sparrow"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Character_Name", "I-Character_Name", "I-Character_Name"]}
{"sentence": "what screwball comedy featured tony curtis doing a hilarious impression of cary grant so he could woe marilyn monroe", "tokens": ["what", "screwball", "comedy", "featured", "tony", "curtis", "doing", "a", "hilarious", "impression", "of", "cary", "grant", "so", "he", "could", "woe", "marilyn", "monroe"], "ner_tags": ["O", "B-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Actor", "I-Actor"]}
{"sentence": "what 2011 science fiction horror film concerns a group of scientists who discover an alien in antarctica", "tokens": ["what", "2011", "science", "fiction", "horror", "film", "concerns", "a", "group", "of", "scientists", "who", "discover", "an", "alien", "in", "antarctica"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "an animated masterpiece known mostly for its opening sorcerer s apprentice scene starring mickey mouse", "tokens": ["an", "animated", "masterpiece", "known", "mostly", "for", "its", "opening", "sorcerer", "s", "apprentice", "scene", "starring", "mickey", "mouse"], "ner_tags": ["O", "B-Genre", "B-Opinion", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name"]}
{"sentence": "what s the italian film that is made up of cartoons set to classical music a la fantasia", "tokens": ["what", "s", "the", "italian", "film", "that", "is", "made", "up", "of", "cartoons", "set", "to", "classical", "music", "a", "la", "fantasia"], "ner_tags": ["O", "O", "O", "B-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in what famous sequel do two small hobbits have to convince the trees to stop an evil wizard", "tokens": ["in", "what", "famous", "sequel", "do", "two", "small", "hobbits", "have", "to", "convince", "the", "trees", "to", "stop", "an", "evil", "wizard"], "ner_tags": ["O", "O", "B-Opinion", "B-Relationship", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of the hitchcock classic that features a villain who has kept his mother in their home long after she s died", "tokens": ["what", "is", "the", "name", "of", "the", "hitchcock", "classic", "that", "features", "a", "villain", "who", "has", "kept", "his", "mother", "in", "their", "home", "long", "after", "she", "s", "died"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Origin", "B-Opinion", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this 2010 western was a remake of a 1969 henry hathaway film by the same name", "tokens": ["this", "2010", "western", "was", "a", "remake", "of", "a", "1969", "henry", "hathaway", "film", "by", "the", "same", "name"], "ner_tags": ["O", "B-Year", "B-Genre", "O", "O", "B-Relationship", "B-Origin", "I-Origin", "I-Origin", "B-Director", "I-Director", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin"]}
{"sentence": "what movie tells the story of 2 cia agents fighting for the same girl played by reese whiterspoon", "tokens": ["what", "movie", "tells", "the", "story", "of", "2", "cia", "agents", "fighting", "for", "the", "same", "girl", "played", "by", "reese", "whiterspoon"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Actor", "I-Actor"]}
{"sentence": "set in the cabrini green projects in chicago this movie from 1992 a murderous soul is summoned to reality by a student who is researching the urban legend", "tokens": ["set", "in", "the", "cabrini", "green", "projects", "in", "chicago", "this", "movie", "from", "1992", "a", "murderous", "soul", "is", "summoned", "to", "reality", "by", "a", "student", "who", "is", "researching", "the", "urban", "legend"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Year", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2010 romantic comedy centered on a guy and a girl in a cross country relationship", "tokens": ["what", "2010", "romantic", "comedy", "centered", "on", "a", "guy", "and", "a", "girl", "in", "a", "cross", "country", "relationship"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "which film is about a group of seemingly unrelated characters being isolated and tortured by the villain jigsaw", "tokens": ["which", "film", "is", "about", "a", "group", "of", "seemingly", "unrelated", "characters", "being", "isolated", "and", "tortured", "by", "the", "villain", "jigsaw"], "ner_tags": ["O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name"]}
{"sentence": "selina gomez katie cassidy and leighton meester travel to paris and engage in romantic escapades in what 2011 movie", "tokens": ["selina", "gomez", "katie", "cassidy", "and", "leighton", "meester", "travel", "to", "paris", "and", "engage", "in", "romantic", "escapades", "in", "what", "2011", "movie"], "ner_tags": ["B-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Year", "O"]}
{"sentence": "what s the violent movie about the doomed love between two bank robbers during the great depression", "tokens": ["what", "s", "the", "violent", "movie", "about", "the", "doomed", "love", "between", "two", "bank", "robbers", "during", "the", "great", "depression"], "ner_tags": ["O", "O", "O", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of a movie in which a man tries to take the moon with the help of his minions but ends up adopting 3 little girls and falling in love with them", "tokens": ["what", "is", "the", "name", "of", "a", "movie", "in", "which", "a", "man", "tries", "to", "take", "the", "moon", "with", "the", "help", "of", "his", "minions", "but", "ends", "up", "adopting", "3", "little", "girls", "and", "falling", "in", "love", "with", "them"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is a critically acclaimed movie featuring daniel day lewis as a oil tycoon looking for the big payday", "tokens": ["what", "is", "a", "critically", "acclaimed", "movie", "featuring", "daniel", "day", "lewis", "as", "a", "oil", "tycoon", "looking", "for", "the", "big", "payday"], "ner_tags": ["O", "O", "O", "B-Opinion", "I-Opinion", "O", "O", "B-Actor", "I-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what movie directed by frank oz exposes the dark secret regarding the recently deceased member of a dysfunctional family", "tokens": ["what", "movie", "directed", "by", "frank", "oz", "exposes", "the", "dark", "secret", "regarding", "the", "recently", "deceased", "member", "of", "a", "dysfunctional", "family"], "ner_tags": ["O", "O", "O", "O", "B-Director", "I-Director", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is this 1946 movie about hit men killing an unresisting victim and investigator reardon uncovers his past involvement with beautiful deadly kitty collins", "tokens": ["what", "is", "this", "1946", "movie", "about", "hit", "men", "killing", "an", "unresisting", "victim", "and", "investigator", "reardon", "uncovers", "his", "past", "involvement", "with", "beautiful", "deadly", "kitty", "collins"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name"]}
{"sentence": "what is the name of that movie with sigourney weaver battling a giant inswct like creature in space", "tokens": ["what", "is", "the", "name", "of", "that", "movie", "with", "sigourney", "weaver", "battling", "a", "giant", "inswct", "like", "creature", "in", "space"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "a movie about a teenage girl who pretends to sleep with guys to promote a bad girl image for herself", "tokens": ["a", "movie", "about", "a", "teenage", "girl", "who", "pretends", "to", "sleep", "with", "guys", "to", "promote", "a", "bad", "girl", "image", "for", "herself"], "ner_tags": ["O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2000 ridley scott movie garnered a best leading actor oscar win for russell crowe and best picture oscar win", "tokens": ["what", "2000", "ridley", "scott", "movie", "garnered", "a", "best", "leading", "actor", "oscar", "win", "for", "russell", "crowe", "and", "best", "picture", "oscar", "win"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "O", "O", "O", "B-Award", "I-Award", "I-Award", "I-Award", "I-Award", "O", "B-Actor", "I-Actor", "O", "B-Award", "I-Award", "I-Award", "I-Award"]}
{"sentence": "this film by the mexican director alfonso cuar n was the third installment of a popular series about a wizard", "tokens": ["this", "film", "by", "the", "mexican", "director", "alfonso", "cuar", "n", "was", "the", "third", "installment", "of", "a", "popular", "series", "about", "a", "wizard"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "B-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "B-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this computer animated comedy drama has the voices of both justin long and hayden panettiere starring as wolves in canada", "tokens": ["this", "computer", "animated", "comedy", "drama", "has", "the", "voices", "of", "both", "justin", "long", "and", "hayden", "panettiere", "starring", "as", "wolves", "in", "canada"], "ner_tags": ["O", "B-Genre", "I-Genre", "I-Genre", "I-Genre", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot"]}
{"sentence": "which micheal gondry film stared seth rogen as a masked crime fighter with a sidekick", "tokens": ["which", "micheal", "gondry", "film", "stared", "seth", "rogen", "as", "a", "masked", "crime", "fighter", "with", "a", "sidekick"], "ner_tags": ["O", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2011 animated film itself a sequel starred jack black as a klumsy but lovable bear", "tokens": ["what", "2011", "animated", "film", "itself", "a", "sequel", "starred", "jack", "black", "as", "a", "klumsy", "but", "lovable", "bear"], "ner_tags": ["O", "B-Year", "B-Genre", "O", "B-Relationship", "I-Relationship", "I-Relationship", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what is that thriller film starring brad pitt and morgan freeman as two men hunting down a serial killer played by kevin spacey", "tokens": ["what", "is", "that", "thriller", "film", "starring", "brad", "pitt", "and", "morgan", "freeman", "as", "two", "men", "hunting", "down", "a", "serial", "killer", "played", "by", "kevin", "spacey"], "ner_tags": ["O", "O", "O", "B-Genre", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Actor", "I-Actor"]}
{"sentence": "what documentary directed by errol morris concerned a man wrongfully convicted of murder and put in prison for life", "tokens": ["what", "documentary", "directed", "by", "errol", "morris", "concerned", "a", "man", "wrongfully", "convicted", "of", "murder", "and", "put", "in", "prison", "for", "life"], "ner_tags": ["O", "B-Genre", "O", "O", "B-Director", "I-Director", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1988 comedy is set in toontown and features a blend of live action and animation", "tokens": ["what", "1988", "comedy", "is", "set", "in", "toontown", "and", "features", "a", "blend", "of", "live", "action", "and", "animation"], "ner_tags": ["O", "B-Year", "B-Genre", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Genre", "I-Genre", "O", "B-Genre"]}
{"sentence": "back in the 60 s a green villain took all the joy from christmas or at least wanted to", "tokens": ["back", "in", "the", "60", "s", "a", "green", "villain", "took", "all", "the", "joy", "from", "christmas", "or", "at", "least", "wanted", "to"], "ner_tags": ["O", "O", "O", "B-Year", "I-Year", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2010 movie centered around the inhabitants of a small iowa town plagued by insanity", "tokens": ["what", "2010", "movie", "centered", "around", "the", "inhabitants", "of", "a", "small", "iowa", "town", "plagued", "by", "insanity"], "ner_tags": ["O", "B-Year", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this 2012 science fiction adventure takes place on mars and was produced by disney what is it", "tokens": ["this", "2012", "science", "fiction", "adventure", "takes", "place", "on", "mars", "and", "was", "produced", "by", "disney", "what", "is", "it"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what is the film about a lion who grows up in his father s path and meets lots of friends throughout the way", "tokens": ["what", "is", "the", "film", "about", "a", "lion", "who", "grows", "up", "in", "his", "father", "s", "path", "and", "meets", "lots", "of", "friends", "throughout", "the", "way"], "ner_tags": ["O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this crime film starring jack nicholson matt damon leonardo dicaprio and mark wahlberg is a best picture oscar winner", "tokens": ["this", "crime", "film", "starring", "jack", "nicholson", "matt", "damon", "leonardo", "dicaprio", "and", "mark", "wahlberg", "is", "a", "best", "picture", "oscar", "winner"], "ner_tags": ["O", "B-Genre", "O", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "B-Award", "I-Award", "I-Award", "I-Award"]}
{"sentence": "i am thinking of an 80 s romantic comedy starring melanie griffith as a woman whose boss steals her idea and sells it as her own", "tokens": ["i", "am", "thinking", "of", "an", "80", "s", "romantic", "comedy", "starring", "melanie", "griffith", "as", "a", "woman", "whose", "boss", "steals", "her", "idea", "and", "sells", "it", "as", "her", "own"], "ner_tags": ["O", "O", "O", "O", "O", "B-Year", "I-Year", "B-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i want the remake of the 1941 classic horror story of a man who is bitten by a cursed animal and must try to find a way to break the spell", "tokens": ["i", "want", "the", "remake", "of", "the", "1941", "classic", "horror", "story", "of", "a", "man", "who", "is", "bitten", "by", "a", "cursed", "animal", "and", "must", "try", "to", "find", "a", "way", "to", "break", "the", "spell"], "ner_tags": ["O", "O", "O", "B-Relationship", "O", "O", "B-Year", "B-Opinion", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1969 american road movie was written by peter fonda dennis hopper and terry southern", "tokens": ["what", "1969", "american", "road", "movie", "was", "written", "by", "peter", "fonda", "dennis", "hopper", "and", "terry", "southern"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "this film is steven speilberg s masterpiece that focuses on some lesser known aspects of the holocaust", "tokens": ["this", "film", "is", "steven", "speilberg", "s", "masterpiece", "that", "focuses", "on", "some", "lesser", "known", "aspects", "of", "the", "holocaust"], "ner_tags": ["O", "O", "O", "B-Director", "I-Director", "O", "B-Opinion", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what film based on a popular book starred jessica chastain as a clueless white lady in the south", "tokens": ["what", "film", "based", "on", "a", "popular", "book", "starred", "jessica", "chastain", "as", "a", "clueless", "white", "lady", "in", "the", "south"], "ner_tags": ["O", "O", "O", "O", "O", "B-Origin", "I-Origin", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in what movie does an astronaut land on a mysterious land where primates rule over humans", "tokens": ["in", "what", "movie", "does", "an", "astronaut", "land", "on", "a", "mysterious", "land", "where", "primates", "rule", "over", "humans"], "ner_tags": ["O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2012 paul w s anderson sci fi flick starred milla jovovich sienna guillory and michelle rodriguez", "tokens": ["what", "2012", "paul", "w", "s", "anderson", "sci", "fi", "flick", "starred", "milla", "jovovich", "sienna", "guillory", "and", "michelle", "rodriguez"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "I-Director", "I-Director", "B-Genre", "I-Genre", "O", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "i am thinking of the sequel film that continues the stories of carrie bradshaw and her girlfriends", "tokens": ["i", "am", "thinking", "of", "the", "sequel", "film", "that", "continues", "the", "stories", "of", "carrie", "bradshaw", "and", "her", "girlfriends"], "ner_tags": ["O", "O", "O", "O", "O", "B-Relationship", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name", "B-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i m thinking of a comedy movie that ends with a giant marshmallow man wrecking havoc on new york city", "tokens": ["i", "m", "thinking", "of", "a", "comedy", "movie", "that", "ends", "with", "a", "giant", "marshmallow", "man", "wrecking", "havoc", "on", "new", "york", "city"], "ner_tags": ["O", "O", "O", "O", "O", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1980 s and 1990 s sci fi action movie franchise is actor peter keller best know for", "tokens": ["what", "1980", "s", "and", "1990", "s", "sci", "fi", "action", "movie", "franchise", "is", "actor", "peter", "keller", "best", "know", "for"], "ner_tags": ["O", "B-Year", "I-Year", "I-Year", "I-Year", "I-Year", "B-Genre", "I-Genre", "I-Genre", "O", "B-Relationship", "O", "O", "B-Actor", "I-Actor", "O", "O", "O"]}
{"sentence": "what s the outrageous movie about a us president who must save the world from vampires", "tokens": ["what", "s", "the", "outrageous", "movie", "about", "a", "us", "president", "who", "must", "save", "the", "world", "from", "vampires"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 2010 comedy film starring jason bateman and jennifer aniston who play best friends with each other", "tokens": ["what", "is", "the", "2010", "comedy", "film", "starring", "jason", "bateman", "and", "jennifer", "aniston", "who", "play", "best", "friends", "with", "each", "other"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "O", "O", "O"]}
{"sentence": "what is a great comedy featuring the talents of steve carell as a loser looking for a friend", "tokens": ["what", "is", "a", "great", "comedy", "featuring", "the", "talents", "of", "steve", "carell", "as", "a", "loser", "looking", "for", "a", "friend"], "ner_tags": ["O", "O", "O", "B-Opinion", "B-Genre", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of the dramatic documentary by disney about creatures of the sea and those that are disappearing from it", "tokens": ["what", "is", "the", "name", "of", "the", "dramatic", "documentary", "by", "disney", "about", "creatures", "of", "the", "sea", "and", "those", "that", "are", "disappearing", "from", "it"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Opinion", "B-Genre", "O", "B-Director", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what movie stars jason segel and emily blunt and follows them from their proposal to their walk down the aisle", "tokens": ["what", "movie", "stars", "jason", "segel", "and", "emily", "blunt", "and", "follows", "them", "from", "their", "proposal", "to", "their", "walk", "down", "the", "aisle"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i am thinking of a film about a mildly retarded young man whose exploits manage to have him play a pivotal part in dozens of historic events", "tokens": ["i", "am", "thinking", "of", "a", "film", "about", "a", "mildly", "retarded", "young", "man", "whose", "exploits", "manage", "to", "have", "him", "play", "a", "pivotal", "part", "in", "dozens", "of", "historic", "events"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this 1992 film is the third and final installment in the evil dead trilogy in this movie the main character is trapped in the middle ages where he must battle a host of the undead in order to return to the present what is the name of this film", "tokens": ["this", "1992", "film", "is", "the", "third", "and", "final", "installment", "in", "the", "evil", "dead", "trilogy", "in", "this", "movie", "the", "main", "character", "is", "trapped", "in", "the", "middle", "ages", "where", "he", "must", "battle", "a", "host", "of", "the", "undead", "in", "order", "to", "return", "to", "the", "present", "what", "is", "the", "name", "of", "this", "film"], "ner_tags": ["O", "B-Year", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "i m thinking of a film that features an adventurous archaeologist who searches egypt for an ancient biblical relic", "tokens": ["i", "m", "thinking", "of", "a", "film", "that", "features", "an", "adventurous", "archaeologist", "who", "searches", "egypt", "for", "an", "ancient", "biblical", "relic"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this comedy has four guys that are battling ghosts and other funny creatures in new york city", "tokens": ["this", "comedy", "has", "four", "guys", "that", "are", "battling", "ghosts", "and", "other", "funny", "creatures", "in", "new", "york", "city"], "ner_tags": ["O", "B-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2011 movie has ben stiller and eddie murphy climbing a tall structure to steal something", "tokens": ["what", "2011", "movie", "has", "ben", "stiller", "and", "eddie", "murphy", "climbing", "a", "tall", "structure", "to", "steal", "something"], "ner_tags": ["O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what movie featuring arnold schwarzenegger is about a group of commandos searching for pow s in a south american rain forest", "tokens": ["what", "movie", "featuring", "arnold", "schwarzenegger", "is", "about", "a", "group", "of", "commandos", "searching", "for", "pow", "s", "in", "a", "south", "american", "rain", "forest"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2009 james cameron film starred sam worthington zoe saldana stephen lang and sigourney weaver", "tokens": ["what", "2009", "james", "cameron", "film", "starred", "sam", "worthington", "zoe", "saldana", "stephen", "lang", "and", "sigourney", "weaver"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "tom hulce portrayed a classical composer facing his demons and creating musical masterpieces as told in this 1984 film", "tokens": ["tom", "hulce", "portrayed", "a", "classical", "composer", "facing", "his", "demons", "and", "creating", "musical", "masterpieces", "as", "told", "in", "this", "1984", "film"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "B-Year", "O"]}
{"sentence": "what 1999 movie found haley joel osment as cole sear a boy who communicated with spirits", "tokens": ["what", "1999", "movie", "found", "haley", "joel", "osment", "as", "cole", "sear", "a", "boy", "who", "communicated", "with", "spirits"], "ner_tags": ["O", "B-Year", "O", "O", "B-Actor", "I-Actor", "I-Actor", "O", "B-Character_Name", "I-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the name the movie with ginnifer goodwin and kate hudson where a unlucky in love girl falls in love with her best friend s fiance", "tokens": ["what", "s", "the", "name", "the", "movie", "with", "ginnifer", "goodwin", "and", "kate", "hudson", "where", "a", "unlucky", "in", "love", "girl", "falls", "in", "love", "with", "her", "best", "friend", "s", "fiance"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "movie about a city teen named ren who moves to a small town where rock movie has been banned", "tokens": ["movie", "about", "a", "city", "teen", "named", "ren", "who", "moves", "to", "a", "small", "town", "where", "rock", "movie", "has", "been", "banned"], "ner_tags": ["O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "amanda seyfried stars in this movie about an american woman in verona who seeks out the subject of a romantic mystery", "tokens": ["amanda", "seyfried", "stars", "in", "this", "movie", "about", "an", "american", "woman", "in", "verona", "who", "seeks", "out", "the", "subject", "of", "a", "romantic", "mystery"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "daniel radcliffe starred in this 2012 gothic thriller about a ghost and haunted house in a remote part of england", "tokens": ["daniel", "radcliffe", "starred", "in", "this", "2012", "gothic", "thriller", "about", "a", "ghost", "and", "haunted", "house", "in", "a", "remote", "part", "of", "england"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "B-Year", "B-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is that 2012 musical comedy film directed by jason moore and features an ensemble cast including anna kendrick and alexis knapp", "tokens": ["what", "is", "that", "2012", "musical", "comedy", "film", "directed", "by", "jason", "moore", "and", "features", "an", "ensemble", "cast", "including", "anna", "kendrick", "and", "alexis", "knapp"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "what is the movie where jbiebs does his concerts", "tokens": ["what", "is", "the", "movie", "where", "jbiebs", "does", "his", "concerts"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "B-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1973 coming of age film is co written and directed by george lucas and stars richard dreyfuss and ron howard", "tokens": ["what", "1973", "coming", "of", "age", "film", "is", "co", "written", "and", "directed", "by", "george", "lucas", "and", "stars", "richard", "dreyfuss", "and", "ron", "howard"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "O", "O", "O", "O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "what was the movie with tom cruise where he was saving cameron diaz the entire time", "tokens": ["what", "was", "the", "movie", "with", "tom", "cruise", "where", "he", "was", "saving", "cameron", "diaz", "the", "entire", "time"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Plot", "B-Actor", "I-Actor", "O", "O", "O"]}
{"sentence": "i m thinking of the horror movie where people get murdered by freddie krueger when they fall asleep", "tokens": ["i", "m", "thinking", "of", "the", "horror", "movie", "where", "people", "get", "murdered", "by", "freddie", "krueger", "when", "they", "fall", "asleep"], "ner_tags": ["O", "O", "O", "O", "O", "B-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what humphrey bogart film considered by many to be the best film ever made introduced us to the phrase here s looking at you kid", "tokens": ["what", "humphrey", "bogart", "film", "considered", "by", "many", "to", "be", "the", "best", "film", "ever", "made", "introduced", "us", "to", "the", "phrase", "here", "s", "looking", "at", "you", "kid"], "ner_tags": ["O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "O", "B-Opinion", "I-Opinion", "I-Opinion", "I-Opinion", "O", "O", "O", "O", "O", "B-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote"]}
{"sentence": "in what 2012 movie did michelle pfieffer reunite with director tim burton where she played elizabeth", "tokens": ["in", "what", "2012", "movie", "did", "michelle", "pfieffer", "reunite", "with", "director", "tim", "burton", "where", "she", "played", "elizabeth"], "ner_tags": ["O", "O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "B-Character_Name"]}
{"sentence": "what is the tom cruise movie where he plays the title role based on a book called one shot", "tokens": ["what", "is", "the", "tom", "cruise", "movie", "where", "he", "plays", "the", "title", "role", "based", "on", "a", "book", "called", "one", "shot"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin"]}
{"sentence": "what is the 3 rd movie in a series starring daniel radcliff as a boy wizard on the lookout for a supposed villain who slayed his parents", "tokens": ["what", "is", "the", "3", "rd", "movie", "in", "a", "series", "starring", "daniel", "radcliff", "as", "a", "boy", "wizard", "on", "the", "lookout", "for", "a", "supposed", "villain", "who", "slayed", "his", "parents"], "ner_tags": ["O", "O", "O", "B-Genre", "I-Genre", "O", "B-Relationship", "I-Relationship", "I-Relationship", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie about the lives of small blue creatures that live in mushrooms", "tokens": ["what", "is", "the", "movie", "about", "the", "lives", "of", "small", "blue", "creatures", "that", "live", "in", "mushrooms"], "ner_tags": ["O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the last part of a series of movies about the adventures of buzz woody and jessie", "tokens": ["what", "is", "the", "last", "part", "of", "a", "series", "of", "movies", "about", "the", "adventures", "of", "buzz", "woody", "and", "jessie"], "ner_tags": ["O", "O", "O", "B-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "O", "O", "O", "O", "B-Genre", "O", "B-Character_Name", "I-Character_Name", "O", "B-Character_Name"]}
{"sentence": "this romance film stars julia roberts as a woman traveling around europe in an attempt to improve her life", "tokens": ["this", "romance", "film", "stars", "julia", "roberts", "as", "a", "woman", "traveling", "around", "europe", "in", "an", "attempt", "to", "improve", "her", "life"], "ner_tags": ["O", "B-Genre", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is that movie starring leonardo di caprio and claire danes as two star crossed lovers and based on the famous shakespeare play", "tokens": ["what", "is", "that", "movie", "starring", "leonardo", "di", "caprio", "and", "claire", "danes", "as", "two", "star", "crossed", "lovers", "and", "based", "on", "the", "famous", "shakespeare", "play"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin"]}
{"sentence": "is facebook a real company or is this just a fictional portrayle in the movie", "tokens": ["is", "facebook", "a", "real", "company", "or", "is", "this", "just", "a", "fictional", "portrayle", "in", "the", "movie"], "ner_tags": ["O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O"]}
{"sentence": "what movie based on a stephen king novel depicts a writer s slow descent into madness after becoming a caretaker of a hotel in the winter", "tokens": ["what", "movie", "based", "on", "a", "stephen", "king", "novel", "depicts", "a", "writer", "s", "slow", "descent", "into", "madness", "after", "becoming", "a", "caretaker", "of", "a", "hotel", "in", "the", "winter"], "ner_tags": ["O", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in what classic 1978 movie does marlon brando take on the role of the world s possibly best known super hero", "tokens": ["in", "what", "classic", "1978", "movie", "does", "marlon", "brando", "take", "on", "the", "role", "of", "the", "world", "s", "possibly", "best", "known", "super", "hero"], "ner_tags": ["O", "O", "B-Genre", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "B-Opinion", "I-Opinion", "I-Opinion", "I-Opinion", "I-Opinion", "B-Plot", "I-Plot"]}
{"sentence": "what is the paul mazursky directed film for which jill clayburgh received a best actress oscar nomination", "tokens": ["what", "is", "the", "paul", "mazursky", "directed", "film", "for", "which", "jill", "clayburgh", "received", "a", "best", "actress", "oscar", "nomination"], "ner_tags": ["O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Award", "I-Award", "I-Award", "I-Award"]}
{"sentence": "what is that 2010 film directed by robert rodriguez and starring danny trejo as the main character", "tokens": ["what", "is", "that", "2010", "film", "directed", "by", "robert", "rodriguez", "and", "starring", "danny", "trejo", "as", "the", "main", "character"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O"]}
{"sentence": "what s the violent movie where ryan goslin does all sorts of nasty stuff to bad guys to protect a wife and kid", "tokens": ["what", "s", "the", "violent", "movie", "where", "ryan", "goslin", "does", "all", "sorts", "of", "nasty", "stuff", "to", "bad", "guys", "to", "protect", "a", "wife", "and", "kid"], "ner_tags": ["O", "O", "O", "B-Genre", "O", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in this movie the main character dorothy is transported to a magical land and must find the wizard to get home", "tokens": ["in", "this", "movie", "the", "main", "character", "dorothy", "is", "transported", "to", "a", "magical", "land", "and", "must", "find", "the", "wizard", "to", "get", "home"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Character_Name", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "denzel washington stars as the title character in this 1992 spike lee historic biographical drama", "tokens": ["denzel", "washington", "stars", "as", "the", "title", "character", "in", "this", "1992", "spike", "lee", "historic", "biographical", "drama"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "O", "B-Year", "B-Director", "I-Director", "B-Genre", "I-Genre", "I-Genre"]}
{"sentence": "what actor played bane", "tokens": ["what", "actor", "played", "bane"], "ner_tags": ["O", "O", "O", "B-Character_Name"]}
{"sentence": "what is the movie starring ben affleck about a group of friends from boston who decide to start robbing banks", "tokens": ["what", "is", "the", "movie", "starring", "ben", "affleck", "about", "a", "group", "of", "friends", "from", "boston", "who", "decide", "to", "start", "robbing", "banks"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 2005 american film directed by robert rodriguez and based on the frank miller graphic novel of the same name", "tokens": ["what", "is", "the", "2005", "american", "film", "directed", "by", "robert", "rodriguez", "and", "based", "on", "the", "frank", "miller", "graphic", "novel", "of", "the", "same", "name"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin"]}
{"sentence": "i m thinking of a movie from the mid 2000 s about stoner knights it had natalie portman as the lead female it was a comedy", "tokens": ["i", "m", "thinking", "of", "a", "movie", "from", "the", "mid", "2000", "s", "about", "stoner", "knights", "it", "had", "natalie", "portman", "as", "the", "lead", "female", "it", "was", "a", "comedy"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Year", "I-Year", "I-Year", "O", "B-Plot", "I-Plot", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "O", "B-Genre"]}
{"sentence": "what is that movie starring bradley cooper who takes an experimental drug that makes him more intelligent and charismatic", "tokens": ["what", "is", "that", "movie", "starring", "bradley", "cooper", "who", "takes", "an", "experimental", "drug", "that", "makes", "him", "more", "intelligent", "and", "charismatic"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "the sequel to pixar s hit movie about anthropomorphic cars", "tokens": ["the", "sequel", "to", "pixar", "s", "hit", "movie", "about", "anthropomorphic", "cars"], "ner_tags": ["O", "B-Relationship", "O", "B-Genre", "I-Genre", "O", "B-Plot", "O", "B-Plot", "I-Plot"]}
{"sentence": "what 1967 arthur penn biopic starred warren beatty faye dunaway michael j pollard and gene hackman", "tokens": ["what", "1967", "arthur", "penn", "biopic", "starred", "warren", "beatty", "faye", "dunaway", "michael", "j", "pollard", "and", "gene", "hackman"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "B-Genre", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "what is the name of the musical film starring cher and christina aguilera as aging singers in a club", "tokens": ["what", "is", "the", "name", "of", "the", "musical", "film", "starring", "cher", "and", "christina", "aguilera", "as", "aging", "singers", "in", "a", "club"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Genre", "O", "O", "B-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1969 western had a group of aging outlaws looking for one last big score", "tokens": ["what", "1969", "western", "had", "a", "group", "of", "aging", "outlaws", "looking", "for", "one", "last", "big", "score"], "ner_tags": ["O", "B-Year", "B-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what was that 2010 film directed by jay roach where steve carell s character did mouse taxidermy", "tokens": ["what", "was", "that", "2010", "film", "directed", "by", "jay", "roach", "where", "steve", "carell", "s", "character", "did", "mouse", "taxidermy"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "O", "B-Director", "I-Director", "O", "B-Actor", "I-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "which 2010 comedy starring steve carell involved the invitation of a rising executive by work managers for a night of humor", "tokens": ["which", "2010", "comedy", "starring", "steve", "carell", "involved", "the", "invitation", "of", "a", "rising", "executive", "by", "work", "managers", "for", "a", "night", "of", "humor"], "ner_tags": ["O", "B-Year", "B-Genre", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is that steven spielberg directed classic movie about an alien that comes to earth and becomes friends with a young boy", "tokens": ["what", "is", "that", "steven", "spielberg", "directed", "classic", "movie", "about", "an", "alien", "that", "comes", "to", "earth", "and", "becomes", "friends", "with", "a", "young", "boy"], "ner_tags": ["O", "O", "O", "B-Director", "I-Director", "O", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what shakespearean play has had the following famous actors play the leading and title character laurence olivier kenneth branagh christopher plummer derek jacobi mel gibson kevin kline and ethan hawke", "tokens": ["what", "shakespearean", "play", "has", "had", "the", "following", "famous", "actors", "play", "the", "leading", "and", "title", "character", "laurence", "olivier", "kenneth", "branagh", "christopher", "plummer", "derek", "jacobi", "mel", "gibson", "kevin", "kline", "and", "ethan", "hawke"], "ner_tags": ["O", "B-Origin", "I-Origin", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor"]}
{"sentence": "what is the 1945 british film that centers on a woman tempted to cheat on her husband after meeting a stranger in a railway station", "tokens": ["what", "is", "the", "1945", "british", "film", "that", "centers", "on", "a", "woman", "tempted", "to", "cheat", "on", "her", "husband", "after", "meeting", "a", "stranger", "in", "a", "railway", "station"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this classic shakespearian play is updated for modern day and stars ethan hawke and bill murray", "tokens": ["this", "classic", "shakespearian", "play", "is", "updated", "for", "modern", "day", "and", "stars", "ethan", "hawke", "and", "bill", "murray"], "ner_tags": ["O", "B-Origin", "I-Origin", "I-Origin", "O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "in what movie does johan hill try to get rocker aldous snow to a music venue to play a concert", "tokens": ["in", "what", "movie", "does", "johan", "hill", "try", "to", "get", "rocker", "aldous", "snow", "to", "a", "music", "venue", "to", "play", "a", "concert"], "ner_tags": ["O", "O", "O", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what was the movie that was based on a comic book where ryan reynolds plays a guy with a magic ring", "tokens": ["what", "was", "the", "movie", "that", "was", "based", "on", "a", "comic", "book", "where", "ryan", "reynolds", "plays", "a", "guy", "with", "a", "magic", "ring"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Origin", "I-Origin", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the official name of the famous holiday special from the popular peanuts gang", "tokens": ["what", "is", "the", "official", "name", "of", "the", "famous", "holiday", "special", "from", "the", "popular", "peanuts", "gang"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Opinion", "B-Genre", "O", "O", "O", "B-Opinion", "O", "O"]}
{"sentence": "what is the wes craven horror movie that was a huge 80 s hit about a road", "tokens": ["what", "is", "the", "wes", "craven", "horror", "movie", "that", "was", "a", "huge", "80", "s", "hit", "about", "a", "road"], "ner_tags": ["O", "O", "O", "B-Director", "I-Director", "B-Genre", "O", "O", "O", "O", "O", "B-Year", "I-Year", "B-Opinion", "B-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie about a famous superhero who has a costume underneath and can fly and save people", "tokens": ["what", "is", "the", "movie", "about", "a", "famous", "superhero", "who", "has", "a", "costume", "underneath", "and", "can", "fly", "and", "save", "people"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "1991 dramatic comedy about a housewife who befriends an old lady in a nursing home", "tokens": ["1991", "dramatic", "comedy", "about", "a", "housewife", "who", "befriends", "an", "old", "lady", "in", "a", "nursing", "home"], "ner_tags": ["B-Year", "B-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what popular sci fi film features the son of a virtual world designer who gets trapped in one of the dimensions his father designed", "tokens": ["what", "popular", "sci", "fi", "film", "features", "the", "son", "of", "a", "virtual", "world", "designer", "who", "gets", "trapped", "in", "one", "of", "the", "dimensions", "his", "father", "designed"], "ner_tags": ["O", "B-Opinion", "B-Genre", "I-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what cult classic comedy directed by jared hess stars jon heder as the eponymous awkward teen hero", "tokens": ["what", "cult", "classic", "comedy", "directed", "by", "jared", "hess", "stars", "jon", "heder", "as", "the", "eponymous", "awkward", "teen", "hero"], "ner_tags": ["O", "B-Genre", "B-Opinion", "B-Genre", "O", "O", "B-Director", "I-Director", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O"]}
{"sentence": "tom cruise and cameron diaz star in this action comedy about a girl trying to help a spy clear his name", "tokens": ["tom", "cruise", "and", "cameron", "diaz", "star", "in", "this", "action", "comedy", "about", "a", "girl", "trying", "to", "help", "a", "spy", "clear", "his", "name"], "ner_tags": ["B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this classic film was produced by spielberg and is an emotional story about an alien and young boy", "tokens": ["this", "classic", "film", "was", "produced", "by", "spielberg", "and", "is", "an", "emotional", "story", "about", "an", "alien", "and", "young", "boy"], "ner_tags": ["O", "B-Opinion", "O", "O", "O", "O", "B-Director", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "the adventures of a group of people who go back in time with the use of a pool staple", "tokens": ["the", "adventures", "of", "a", "group", "of", "people", "who", "go", "back", "in", "time", "with", "the", "use", "of", "a", "pool", "staple"], "ner_tags": ["O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what chick flick did julia louie dreyfus character in seinfeld hate with every fiber of her being", "tokens": ["what", "chick", "flick", "did", "julia", "louie", "dreyfus", "character", "in", "seinfeld", "hate", "with", "every", "fiber", "of", "her", "being"], "ner_tags": ["O", "B-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "I-Actor", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of that old movie starring jimmy stewart and he goes to the capital he s like a senator", "tokens": ["what", "is", "the", "name", "of", "that", "old", "movie", "starring", "jimmy", "stewart", "and", "he", "goes", "to", "the", "capital", "he", "s", "like", "a", "senator"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i am thinking of a film adaptation of a suzanne collins novel about a dystopian society where teenagers fight to the death", "tokens": ["i", "am", "thinking", "of", "a", "film", "adaptation", "of", "a", "suzanne", "collins", "novel", "about", "a", "dystopian", "society", "where", "teenagers", "fight", "to", "the", "death"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Relationship", "I-Relationship", "I-Relationship", "B-Origin", "I-Origin", "I-Origin", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the award winning movie adaptation of an alice walker book starring danny glover and whoopi goldberg", "tokens": ["what", "is", "the", "award", "winning", "movie", "adaptation", "of", "an", "alice", "walker", "book", "starring", "danny", "glover", "and", "whoopi", "goldberg"], "ner_tags": ["O", "O", "O", "B-Award", "I-Award", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "what is the scorsese directed movie where an orphan lives in a train station", "tokens": ["what", "is", "the", "scorsese", "directed", "movie", "where", "an", "orphan", "lives", "in", "a", "train", "station"], "ner_tags": ["O", "O", "O", "B-Director", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2010 los angeles based sci fi thriller features eric balfor donald faison brittany daniel and scottie thompson", "tokens": ["what", "2010", "los", "angeles", "based", "sci", "fi", "thriller", "features", "eric", "balfor", "donald", "faison", "brittany", "daniel", "and", "scottie", "thompson"], "ner_tags": ["O", "B-Year", "O", "O", "O", "B-Genre", "I-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "which c s lewis movie is it where the kids return to the magical world and have to take a ship across the sea in that world", "tokens": ["which", "c", "s", "lewis", "movie", "is", "it", "where", "the", "kids", "return", "to", "the", "magical", "world", "and", "have", "to", "take", "a", "ship", "across", "the", "sea", "in", "that", "world"], "ner_tags": ["O", "B-Origin", "I-Origin", "I-Origin", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "a man captures a bunch of people in a game where they try to escape and all die", "tokens": ["a", "man", "captures", "a", "bunch", "of", "people", "in", "a", "game", "where", "they", "try", "to", "escape", "and", "all", "die"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what survival movie is about liam neeson trying to return home while fending off wolves and blizzards", "tokens": ["what", "survival", "movie", "is", "about", "liam", "neeson", "trying", "to", "return", "home", "while", "fending", "off", "wolves", "and", "blizzards"], "ner_tags": ["O", "B-Genre", "O", "O", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie starring brad pitt and anthony hopkins about three brothers and their father living in the remote wilderness in the 1900 s", "tokens": ["what", "is", "the", "movie", "starring", "brad", "pitt", "and", "anthony", "hopkins", "about", "three", "brothers", "and", "their", "father", "living", "in", "the", "remote", "wilderness", "in", "the", "1900", "s"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this dark comedy stars peter sellers in three different roles and focuses on the cold war", "tokens": ["this", "dark", "comedy", "stars", "peter", "sellers", "in", "three", "different", "roles", "and", "focuses", "on", "the", "cold", "war"], "ner_tags": ["O", "B-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 1978 american comedy film directed by warren beatty and buck henry which stars beatty as a football player", "tokens": ["what", "is", "the", "1978", "american", "comedy", "film", "directed", "by", "warren", "beatty", "and", "buck", "henry", "which", "stars", "beatty", "as", "a", "football", "player"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "O", "O", "O", "B-Director", "I-Director", "O", "B-Director", "I-Director", "O", "O", "B-Actor", "O", "O", "O", "O"]}
{"sentence": "in which 2012 halloween gone wrong comedy did johnny knoxville star alongside victoria justice", "tokens": ["in", "which", "2012", "halloween", "gone", "wrong", "comedy", "did", "johnny", "knoxville", "star", "alongside", "victoria", "justice"], "ner_tags": ["O", "O", "B-Year", "B-Plot", "I-Plot", "I-Plot", "B-Genre", "O", "B-Actor", "I-Actor", "O", "O", "B-Actor", "I-Actor"]}
{"sentence": "this comedy classic written and directed by mike judge is about a group of unhappy white collar workers", "tokens": ["this", "comedy", "classic", "written", "and", "directed", "by", "mike", "judge", "is", "about", "a", "group", "of", "unhappy", "white", "collar", "workers"], "ner_tags": ["O", "B-Genre", "I-Genre", "O", "O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the name of the movie that had the marvel superhero who carried a hammer", "tokens": ["what", "s", "the", "name", "of", "the", "movie", "that", "had", "the", "marvel", "superhero", "who", "carried", "a", "hammer"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "which aws is a 1975 american horror thriller film directed by steven spielberg is the story of a giant man eating great white shark attacking beachgoers", "tokens": ["which", "aws", "is", "a", "1975", "american", "horror", "thriller", "film", "directed", "by", "steven", "spielberg", "is", "the", "story", "of", "a", "giant", "man", "eating", "great", "white", "shark", "attacking", "beachgoers"], "ner_tags": ["O", "O", "O", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "a comedy sequel about a clumsy special agent", "tokens": ["a", "comedy", "sequel", "about", "a", "clumsy", "special", "agent"], "ner_tags": ["O", "B-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot"]}
{"sentence": "2011 comedy drama about an idealist who barges into the life of his three sisters", "tokens": ["2011", "comedy", "drama", "about", "an", "idealist", "who", "barges", "into", "the", "life", "of", "his", "three", "sisters"], "ner_tags": ["B-Year", "B-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i am trying to find the name of the sci fi movie directed by stanley kubrick in the 1960 s", "tokens": ["i", "am", "trying", "to", "find", "the", "name", "of", "the", "sci", "fi", "movie", "directed", "by", "stanley", "kubrick", "in", "the", "1960", "s"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Genre", "I-Genre", "O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Year", "I-Year"]}
{"sentence": "a spoon full of sugar makes the medicine go down in this 1964 classic", "tokens": ["a", "spoon", "full", "of", "sugar", "makes", "the", "medicine", "go", "down", "in", "this", "1964", "classic"], "ner_tags": ["B-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "O", "O", "B-Year", "B-Genre"]}
{"sentence": "what is that great nora ephron romantic comedy starring meg ryan and billy crystal as friends who eventually fall in love", "tokens": ["what", "is", "that", "great", "nora", "ephron", "romantic", "comedy", "starring", "meg", "ryan", "and", "billy", "crystal", "as", "friends", "who", "eventually", "fall", "in", "love"], "ner_tags": ["O", "O", "O", "O", "B-Director", "I-Director", "B-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "a concert tour turned into a documentary revolves around a young showman making a rising star out of youtube fame", "tokens": ["a", "concert", "tour", "turned", "into", "a", "documentary", "revolves", "around", "a", "young", "showman", "making", "a", "rising", "star", "out", "of", "youtube", "fame"], "ner_tags": ["O", "B-Origin", "I-Origin", "O", "O", "O", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this movie is a one word three letter title that is the first word of the river bordering texas and mexico", "tokens": ["this", "movie", "is", "a", "one", "word", "three", "letter", "title", "that", "is", "the", "first", "word", "of", "the", "river", "bordering", "texas", "and", "mexico"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie that stars heath ledger and jake gyllenhal as two friends from out west who develop a complex and intimate relationship together", "tokens": ["what", "is", "the", "movie", "that", "stars", "heath", "ledger", "and", "jake", "gyllenhal", "as", "two", "friends", "from", "out", "west", "who", "develop", "a", "complex", "and", "intimate", "relationship", "together"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i need the name of the 70 s movie with that comedian about a monster a scientist brings to life", "tokens": ["i", "need", "the", "name", "of", "the", "70", "s", "movie", "with", "that", "comedian", "about", "a", "monster", "a", "scientist", "brings", "to", "life"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Year", "I-Year", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in what movie does ren maccormack move to a small town where dancing has been banned", "tokens": ["in", "what", "movie", "does", "ren", "maccormack", "move", "to", "a", "small", "town", "where", "dancing", "has", "been", "banned"], "ner_tags": ["O", "O", "O", "O", "B-Character_Name", "I-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i m thinking of the movie that stars the girl from jerry mcguire and she s determined to find love and deal with her issues of weight and insecurity", "tokens": ["i", "m", "thinking", "of", "the", "movie", "that", "stars", "the", "girl", "from", "jerry", "mcguire", "and", "she", "s", "determined", "to", "find", "love", "and", "deal", "with", "her", "issues", "of", "weight", "and", "insecurity"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the black and white critically acclaimed suspense horror film directed by hitchcock released in 1960", "tokens": ["what", "is", "the", "black", "and", "white", "critically", "acclaimed", "suspense", "horror", "film", "directed", "by", "hitchcock", "released", "in", "1960"], "ner_tags": ["O", "O", "O", "B-Genre", "I-Genre", "I-Genre", "B-Opinion", "I-Opinion", "B-Genre", "I-Genre", "O", "O", "O", "B-Director", "O", "O", "B-Year"]}
{"sentence": "i m thinking of a movie about an awkward man who tries to make the move on his crush during a labor day party", "tokens": ["i", "m", "thinking", "of", "a", "movie", "about", "an", "awkward", "man", "who", "tries", "to", "make", "the", "move", "on", "his", "crush", "during", "a", "labor", "day", "party"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the movie starring nathan lane and matthew broderick in which the intentionally try to create the worlds worst play", "tokens": ["what", "s", "the", "movie", "starring", "nathan", "lane", "and", "matthew", "broderick", "in", "which", "the", "intentionally", "try", "to", "create", "the", "worlds", "worst", "play"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what pixar film was the first developed by the studio to not be nominated for an oscar", "tokens": ["what", "pixar", "film", "was", "the", "first", "developed", "by", "the", "studio", "to", "not", "be", "nominated", "for", "an", "oscar"], "ner_tags": ["O", "B-Director", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Award"]}
{"sentence": "this classic horror film features a horrible monster who murders young people in their dreams", "tokens": ["this", "classic", "horror", "film", "features", "a", "horrible", "monster", "who", "murders", "young", "people", "in", "their", "dreams"], "ner_tags": ["O", "B-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this 2011 steve carell movie is about a guy who has n t dated in awhile learning how to pick up girls", "tokens": ["this", "2011", "steve", "carell", "movie", "is", "about", "a", "guy", "who", "has", "n", "t", "dated", "in", "awhile", "learning", "how", "to", "pick", "up", "girls"], "ner_tags": ["O", "B-Year", "B-Actor", "I-Actor", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i m thinking of the story of captain steve rogers who become americas first super solider", "tokens": ["i", "m", "thinking", "of", "the", "story", "of", "captain", "steve", "rogers", "who", "become", "americas", "first", "super", "solider"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Director", "I-Director", "I-Director", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in what 2011 movie does jesse eisenberg have a bomb strapped to his chest and is forced to rob a bank to get the code to disarm it", "tokens": ["in", "what", "2011", "movie", "does", "jesse", "eisenberg", "have", "a", "bomb", "strapped", "to", "his", "chest", "and", "is", "forced", "to", "rob", "a", "bank", "to", "get", "the", "code", "to", "disarm", "it"], "ner_tags": ["O", "O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "which movie features a lonely deformed man hidden away in a bell tower that falls in love with a beautiful woman", "tokens": ["which", "movie", "features", "a", "lonely", "deformed", "man", "hidden", "away", "in", "a", "bell", "tower", "that", "falls", "in", "love", "with", "a", "beautiful", "woman"], "ner_tags": ["O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "which alfred hitchcock films sees cary grant attacked by an airplane and chased across mount rushmore", "tokens": ["which", "alfred", "hitchcock", "films", "sees", "cary", "grant", "attacked", "by", "an", "airplane", "and", "chased", "across", "mount", "rushmore"], "ner_tags": ["O", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what children s movie is based on a lion cub who runs from home after his father is killed", "tokens": ["what", "children", "s", "movie", "is", "based", "on", "a", "lion", "cub", "who", "runs", "from", "home", "after", "his", "father", "is", "killed"], "ner_tags": ["O", "B-Genre", "I-Genre", "I-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 1940 american animated film produced by walt disney that showcases animated segments of classic music", "tokens": ["what", "is", "the", "1940", "american", "animated", "film", "produced", "by", "walt", "disney", "that", "showcases", "animated", "segments", "of", "classic", "music"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "O", "O", "O", "B-Director", "I-Director", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i am thinking of the classic film that stars gregory peck as the lawyer who defends a black man wrongly accused of raping a white woman", "tokens": ["i", "am", "thinking", "of", "the", "classic", "film", "that", "stars", "gregory", "peck", "as", "the", "lawyer", "who", "defends", "a", "black", "man", "wrongly", "accused", "of", "raping", "a", "white", "woman"], "ner_tags": ["O", "O", "O", "O", "O", "B-Genre", "I-Genre", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 1952 american western movie about a marshall once again facing a deadly enemy", "tokens": ["what", "is", "the", "1952", "american", "western", "movie", "about", "a", "marshall", "once", "again", "facing", "a", "deadly", "enemy"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what biographical sports movie did martin scorsese make that start robert de niro and joe pesci", "tokens": ["what", "biographical", "sports", "movie", "did", "martin", "scorsese", "make", "that", "start", "robert", "de", "niro", "and", "joe", "pesci"], "ner_tags": ["O", "B-Genre", "I-Genre", "O", "O", "B-Director", "I-Director", "O", "O", "O", "B-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "name the 2011 comedy in which a group of women go to a brazilian steak restaurant for lunch before going to a chic dress shop while trying on gowns food poisoning strikes and some very unladylike behavior ensues", "tokens": ["name", "the", "2011", "comedy", "in", "which", "a", "group", "of", "women", "go", "to", "a", "brazilian", "steak", "restaurant", "for", "lunch", "before", "going", "to", "a", "chic", "dress", "shop", "while", "trying", "on", "gowns", "food", "poisoning", "strikes", "and", "some", "very", "unladylike", "behavior", "ensues"], "ner_tags": ["O", "O", "B-Year", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i am thinking of a tyler perry film where he crossdresses as a woman who must protect a family s identities", "tokens": ["i", "am", "thinking", "of", "a", "tyler", "perry", "film", "where", "he", "crossdresses", "as", "a", "woman", "who", "must", "protect", "a", "family", "s", "identities"], "ner_tags": ["O", "O", "O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "which film tells the story of two children in 1917 who take a photograph believed by some to be the first scientific evidence of the existence of fairies", "tokens": ["which", "film", "tells", "the", "story", "of", "two", "children", "in", "1917", "who", "take", "a", "photograph", "believed", "by", "some", "to", "be", "the", "first", "scientific", "evidence", "of", "the", "existence", "of", "fairies"], "ner_tags": ["O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "paul newman starred in this 1977 hockey movie about the struggles of a minor league team", "tokens": ["paul", "newman", "starred", "in", "this", "1977", "hockey", "movie", "about", "the", "struggles", "of", "a", "minor", "league", "team"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "B-Year", "B-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of that old frank capra movie that comes on tv every christmas where jimmie stewart plays the character george bailey", "tokens": ["what", "is", "the", "name", "of", "that", "old", "frank", "capra", "movie", "that", "comes", "on", "tv", "every", "christmas", "where", "jimmie", "stewart", "plays", "the", "character", "george", "bailey"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Character_Name", "I-Character_Name"]}
{"sentence": "what is the one word title of the 1980 s akira kurosawa samurai film that is loosely based on shakespeare s king lear", "tokens": ["what", "is", "the", "one", "word", "title", "of", "the", "1980", "s", "akira", "kurosawa", "samurai", "film", "that", "is", "loosely", "based", "on", "shakespeare", "s", "king", "lear"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Year", "I-Year", "B-Director", "I-Director", "B-Genre", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin"]}
{"sentence": "what s the violent coen brothers movie where a guy who finds a lot of money also attracts a killer", "tokens": ["what", "s", "the", "violent", "coen", "brothers", "movie", "where", "a", "guy", "who", "finds", "a", "lot", "of", "money", "also", "attracts", "a", "killer"], "ner_tags": ["O", "O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "robert de niro plays a boxer in this movie with such a violent temper that it leads to his destruction", "tokens": ["robert", "de", "niro", "plays", "a", "boxer", "in", "this", "movie", "with", "such", "a", "violent", "temper", "that", "it", "leads", "to", "his", "destruction"], "ner_tags": ["B-Actor", "I-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "can you give me the name of the fantasy thriller movie featuring knights capturing a witch who is accused of bringing a plague", "tokens": ["can", "you", "give", "me", "the", "name", "of", "the", "fantasy", "thriller", "movie", "featuring", "knights", "capturing", "a", "witch", "who", "is", "accused", "of", "bringing", "a", "plague"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "set in england s sherwood forest this tale surrounds a merry bunch of men and their leader who steal from the rich to give to the poor", "tokens": ["set", "in", "england", "s", "sherwood", "forest", "this", "tale", "surrounds", "a", "merry", "bunch", "of", "men", "and", "their", "leader", "who", "steal", "from", "the", "rich", "to", "give", "to", "the", "poor"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what movie features a martial arts wielding group of animals on a quest to stop an evil peacock", "tokens": ["what", "movie", "features", "a", "martial", "arts", "wielding", "group", "of", "animals", "on", "a", "quest", "to", "stop", "an", "evil", "peacock"], "ner_tags": ["O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the movie with john wayne where you re not sure if he s going to rescue or kill a girl", "tokens": ["what", "s", "the", "movie", "with", "john", "wayne", "where", "you", "re", "not", "sure", "if", "he", "s", "going", "to", "rescue", "or", "kill", "a", "girl"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in what movie does a carpenter roofer by day stripper by night star channing tatum and was considered rather controversial", "tokens": ["in", "what", "movie", "does", "a", "carpenter", "roofer", "by", "day", "stripper", "by", "night", "star", "channing", "tatum", "and", "was", "considered", "rather", "controversial"], "ner_tags": ["O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O"]}
{"sentence": "an infamous movie that often tops critics lists this film contains some of the most well known lines in cinematic history including make him an offer he ca n t refuse", "tokens": ["an", "infamous", "movie", "that", "often", "tops", "critics", "lists", "this", "film", "contains", "some", "of", "the", "most", "well", "known", "lines", "in", "cinematic", "history", "including", "make", "him", "an", "offer", "he", "ca", "n", "t", "refuse"], "ner_tags": ["O", "B-Opinion", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Opinion", "I-Opinion", "I-Opinion", "O", "O", "O", "O", "B-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote"]}
{"sentence": "movie based on drugs with penelope cruz", "tokens": ["movie", "based", "on", "drugs", "with", "penelope", "cruz"], "ner_tags": ["O", "B-Plot", "I-Plot", "I-Plot", "O", "B-Actor", "I-Actor"]}
{"sentence": "what was the first film of new zealand director peter jackson s first fantasy trilogy set in middle earth", "tokens": ["what", "was", "the", "first", "film", "of", "new", "zealand", "director", "peter", "jackson", "s", "first", "fantasy", "trilogy", "set", "in", "middle", "earth"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in 1997 when the us president crashes into manhattan now a giant max security prison a convicted bank robber is sent in for a rescue is the plot of this futuristic action adventure movie", "tokens": ["in", "1997", "when", "the", "us", "president", "crashes", "into", "manhattan", "now", "a", "giant", "max", "security", "prison", "a", "convicted", "bank", "robber", "is", "sent", "in", "for", "a", "rescue", "is", "the", "plot", "of", "this", "futuristic", "action", "adventure", "movie"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "O", "B-Genre", "I-Genre", "I-Genre", "O"]}
{"sentence": "which movie did wes craven create a serial killer who haunted people in their dreams and cut is victims with razor sharp knives on his fingers", "tokens": ["which", "movie", "did", "wes", "craven", "create", "a", "serial", "killer", "who", "haunted", "people", "in", "their", "dreams", "and", "cut", "is", "victims", "with", "razor", "sharp", "knives", "on", "his", "fingers"], "ner_tags": ["O", "O", "O", "B-Director", "I-Director", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i am thinking of a movie set during gulf war when american soldiers are kidnapped and brainwashed by communists to be assassins", "tokens": ["i", "am", "thinking", "of", "a", "movie", "set", "during", "gulf", "war", "when", "american", "soldiers", "are", "kidnapped", "and", "brainwashed", "by", "communists", "to", "be", "assassins"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of the coen brothers movie about fast talking gangsters and dirty cops", "tokens": ["what", "is", "the", "name", "of", "the", "coen", "brothers", "movie", "about", "fast", "talking", "gangsters", "and", "dirty", "cops"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "robert redford starred in both the broadway play and this film version of this neil simon romantic comedy", "tokens": ["robert", "redford", "starred", "in", "both", "the", "broadway", "play", "and", "this", "film", "version", "of", "this", "neil", "simon", "romantic", "comedy"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "O", "B-Origin", "O", "O", "O", "O", "B-Director", "I-Director", "B-Genre", "I-Genre"]}
{"sentence": "in this movie robert de niro is an agent investigation a suspicious package that is of high interest for the russians and for the irish", "tokens": ["in", "this", "movie", "robert", "de", "niro", "is", "an", "agent", "investigation", "a", "suspicious", "package", "that", "is", "of", "high", "interest", "for", "the", "russians", "and", "for", "the", "irish"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "a 2012 american 3 d computer animated fantasy adventure film based on a william joyce series of books", "tokens": ["a", "2012", "american", "3", "d", "computer", "animated", "fantasy", "adventure", "film", "based", "on", "a", "william", "joyce", "series", "of", "books"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "I-Genre", "I-Genre", "I-Genre", "I-Genre", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin"]}
{"sentence": "i am thinking of a 2010 remake of a horror film about a school of flesh eating fish that attack college students during spring break", "tokens": ["i", "am", "thinking", "of", "a", "2010", "remake", "of", "a", "horror", "film", "about", "a", "school", "of", "flesh", "eating", "fish", "that", "attack", "college", "students", "during", "spring", "break"], "ner_tags": ["O", "O", "O", "O", "O", "B-Year", "B-Relationship", "O", "O", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this comedy features rodney dangerfield and chevy chase in a classic golf themed laugh out loud movie gophers included", "tokens": ["this", "comedy", "features", "rodney", "dangerfield", "and", "chevy", "chase", "in", "a", "classic", "golf", "themed", "laugh", "out", "loud", "movie", "gophers", "included"], "ner_tags": ["O", "B-Genre", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "B-Genre", "B-Plot", "O", "B-Opinion", "I-Opinion", "I-Opinion", "O", "B-Plot", "I-Plot"]}
{"sentence": "what is the movie by seth mcfarland about a grown man and his teddy bear buddy", "tokens": ["what", "is", "the", "movie", "by", "seth", "mcfarland", "about", "a", "grown", "man", "and", "his", "teddy", "bear", "buddy"], "ner_tags": ["O", "O", "O", "O", "O", "B-Director", "I-Director", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i m thinking of a kid named ralphie had to convince family friends and teachers what the perfect gift was for christmas", "tokens": ["i", "m", "thinking", "of", "a", "kid", "named", "ralphie", "had", "to", "convince", "family", "friends", "and", "teachers", "what", "the", "perfect", "gift", "was", "for", "christmas"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2012 disney nature documentary movie showcases a 3 year old animal in the wild and is narrated by tim allen", "tokens": ["what", "2012", "disney", "nature", "documentary", "movie", "showcases", "a", "3", "year", "old", "animal", "in", "the", "wild", "and", "is", "narrated", "by", "tim", "allen"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "B-Actor", "I-Actor"]}
{"sentence": "what is the movie with a popular singer reality show judge that has great music and dancing", "tokens": ["what", "is", "the", "movie", "with", "a", "popular", "singer", "reality", "show", "judge", "that", "has", "great", "music", "and", "dancing"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "an adaptation of a comic book franchise this film brings numerous superheros together to unite against a common enemy threatening the earth", "tokens": ["an", "adaptation", "of", "a", "comic", "book", "franchise", "this", "film", "brings", "numerous", "superheros", "together", "to", "unite", "against", "a", "common", "enemy", "threatening", "the", "earth"], "ner_tags": ["O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "jamie lee curtis was awesome in this thriller about a murderer who kills many on the creepiest night of the year only later to find out it s her own brother", "tokens": ["jamie", "lee", "curtis", "was", "awesome", "in", "this", "thriller", "about", "a", "murderer", "who", "kills", "many", "on", "the", "creepiest", "night", "of", "the", "year", "only", "later", "to", "find", "out", "it", "s", "her", "own", "brother"], "ner_tags": ["B-Actor", "I-Actor", "I-Actor", "O", "O", "O", "O", "B-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "an old tv show featuring b a barracus and murdoch that was later turned into a terrible movie", "tokens": ["an", "old", "tv", "show", "featuring", "b", "a", "barracus", "and", "murdoch", "that", "was", "later", "turned", "into", "a", "terrible", "movie"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "O", "O", "O", "O", "O", "O", "B-Opinion", "O"]}
{"sentence": "zach snyder directed this 2010 animated movie which was the first one he made that was not rated r", "tokens": ["zach", "snyder", "directed", "this", "2010", "animated", "movie", "which", "was", "the", "first", "one", "he", "made", "that", "was", "not", "rated", "r"], "ner_tags": ["B-Director", "I-Director", "O", "O", "B-Year", "B-Genre", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "i m thinking of a 1988 american fantasy comedy film directed by robert zemeckis and released by touchstone pictures", "tokens": ["i", "m", "thinking", "of", "a", "1988", "american", "fantasy", "comedy", "film", "directed", "by", "robert", "zemeckis", "and", "released", "by", "touchstone", "pictures"], "ner_tags": ["O", "O", "O", "O", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "O", "O"]}
{"sentence": "what 1941 orson welles movie garnered him an oscar for best writing along with writing partner herman j mankiewicz", "tokens": ["what", "1941", "orson", "welles", "movie", "garnered", "him", "an", "oscar", "for", "best", "writing", "along", "with", "writing", "partner", "herman", "j", "mankiewicz"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "O", "O", "O", "O", "B-Award", "I-Award", "I-Award", "I-Award", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what 1953 crime drama depicts a tough cop taking on a politically powerful crime syndicate", "tokens": ["what", "1953", "crime", "drama", "depicts", "a", "tough", "cop", "taking", "on", "a", "politically", "powerful", "crime", "syndicate"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i m thinking of the movie that tells the store of the state of texas freeing itself from mexico in an epic battle", "tokens": ["i", "m", "thinking", "of", "the", "movie", "that", "tells", "the", "store", "of", "the", "state", "of", "texas", "freeing", "itself", "from", "mexico", "in", "an", "epic", "battle"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in what 2005 robert rodridguez movie does actor nick stahl play the character yellow bastard", "tokens": ["in", "what", "2005", "robert", "rodridguez", "movie", "does", "actor", "nick", "stahl", "play", "the", "character", "yellow", "bastard"], "ner_tags": ["O", "O", "B-Year", "B-Director", "I-Director", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Character_Name", "I-Character_Name"]}
{"sentence": "this is a movie about a family who lives in st louis on the brink of the 1904 worlds fair", "tokens": ["this", "is", "a", "movie", "about", "a", "family", "who", "lives", "in", "st", "louis", "on", "the", "brink", "of", "the", "1904", "worlds", "fair"], "ner_tags": ["O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie with steve carol that is a cartoon about an evil villian", "tokens": ["what", "is", "the", "movie", "with", "steve", "carol", "that", "is", "a", "cartoon", "about", "an", "evil", "villian"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Plot", "O", "B-Plot", "I-Plot", "I-Plot"]}
{"sentence": "an intense and full of action type of movie it should have cars and racing and also romantic scenes in there", "tokens": ["an", "intense", "and", "full", "of", "action", "type", "of", "movie", "it", "should", "have", "cars", "and", "racing", "and", "also", "romantic", "scenes", "in", "there"], "ner_tags": ["O", "O", "O", "O", "O", "B-Genre", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "matthew macfadyen luke evans and ray stevenson are the swashbuckling trio in this action adventure movie", "tokens": ["matthew", "macfadyen", "luke", "evans", "and", "ray", "stevenson", "are", "the", "swashbuckling", "trio", "in", "this", "action", "adventure", "movie"], "ner_tags": ["B-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "B-Genre", "I-Genre", "O"]}
{"sentence": "what is the 1987 movie in which arnold schwarzenegger battles an invisible enemy in the jungle", "tokens": ["what", "is", "the", "1987", "movie", "in", "which", "arnold", "schwarzenegger", "battles", "an", "invisible", "enemy", "in", "the", "jungle"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the movie that starts in a small pennsylvania community goes to vietnam then returns", "tokens": ["what", "s", "the", "movie", "that", "starts", "in", "a", "small", "pennsylvania", "community", "goes", "to", "vietnam", "then", "returns"], "ner_tags": ["O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this movie hyped up the shark scare and is an icon of all shark attack movies", "tokens": ["this", "movie", "hyped", "up", "the", "shark", "scare", "and", "is", "an", "icon", "of", "all", "shark", "attack", "movies"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Opinion", "I-Opinion", "I-Opinion", "I-Opinion", "I-Opinion", "I-Opinion", "B-Genre", "I-Genre", "B-Opinion"]}
{"sentence": "this horror movie has a masked killer that goes on a killing rampage and wants to kill his sister", "tokens": ["this", "horror", "movie", "has", "a", "masked", "killer", "that", "goes", "on", "a", "killing", "rampage", "and", "wants", "to", "kill", "his", "sister"], "ner_tags": ["O", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this 1960 italian film depicting the struggles of a disillusioned journalist in rome won the palme d or golden palm at the 1960 cannes film festival", "tokens": ["this", "1960", "italian", "film", "depicting", "the", "struggles", "of", "a", "disillusioned", "journalist", "in", "rome", "won", "the", "palme", "d", "or", "golden", "palm", "at", "the", "1960", "cannes", "film", "festival"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Award", "I-Award", "I-Award", "I-Award", "I-Award", "O", "O", "B-Year", "O", "O", "O"]}
{"sentence": "what is the name of the most recent super hero movie starring christian bale and anne hathaway", "tokens": ["what", "is", "the", "name", "of", "the", "most", "recent", "super", "hero", "movie", "starring", "christian", "bale", "and", "anne", "hathaway"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Genre", "I-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "tim burton directed this film about a man with unusually sharp body parts", "tokens": ["tim", "burton", "directed", "this", "film", "about", "a", "man", "with", "unusually", "sharp", "body", "parts"], "ner_tags": ["B-Director", "I-Director", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the name of movie where eddie murphy plays a doctor that can talk to animals", "tokens": ["what", "s", "the", "name", "of", "movie", "where", "eddie", "murphy", "plays", "a", "doctor", "that", "can", "talk", "to", "animals"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "bruce willis stars in this 2010 action adventure move about a former black ops agent who reassembles his old team in a last ditch effort to survive and uncover his assailants", "tokens": ["bruce", "willis", "stars", "in", "this", "2010", "action", "adventure", "move", "about", "a", "former", "black", "ops", "agent", "who", "reassembles", "his", "old", "team", "in", "a", "last", "ditch", "effort", "to", "survive", "and", "uncover", "his", "assailants"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "B-Year", "B-Genre", "I-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the name of the movie with the guy who builds a son out of wood and his nose gets really long when he lies", "tokens": ["what", "s", "the", "name", "of", "the", "movie", "with", "the", "guy", "who", "builds", "a", "son", "out", "of", "wood", "and", "his", "nose", "gets", "really", "long", "when", "he", "lies"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the tear jerker of a movie starring shirley maclaine tells the story of a mother and daughter who are both searching for love", "tokens": ["what", "is", "the", "tear", "jerker", "of", "a", "movie", "starring", "shirley", "maclaine", "tells", "the", "story", "of", "a", "mother", "and", "daughter", "who", "are", "both", "searching", "for", "love"], "ner_tags": ["O", "O", "O", "B-Opinion", "I-Opinion", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie released in 2012 about a surfer enlists the help of local legend starring jonny weston gerard butler and elisabeth shue", "tokens": ["what", "is", "the", "movie", "released", "in", "2012", "about", "a", "surfer", "enlists", "the", "help", "of", "local", "legend", "starring", "jonny", "weston", "gerard", "butler", "and", "elisabeth", "shue"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Year", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "what classic british comedy is about a group of knights searching for a fabled biblical artifact", "tokens": ["what", "classic", "british", "comedy", "is", "about", "a", "group", "of", "knights", "searching", "for", "a", "fabled", "biblical", "artifact"], "ner_tags": ["O", "B-Opinion", "B-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the movie written by the guy that writes the family guy show that has that big stuffed bear", "tokens": ["what", "s", "the", "movie", "written", "by", "the", "guy", "that", "writes", "the", "family", "guy", "show", "that", "has", "that", "big", "stuffed", "bear"], "ner_tags": ["O", "O", "O", "O", "B-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this classic 1934 film starring fred astaire and ginger rogers includes the cole porter song night and day", "tokens": ["this", "classic", "1934", "film", "starring", "fred", "astaire", "and", "ginger", "rogers", "includes", "the", "cole", "porter", "song", "night", "and", "day"], "ner_tags": ["O", "B-Genre", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "B-Soundtrack", "I-Soundtrack", "I-Soundtrack", "I-Soundtrack", "I-Soundtrack", "I-Soundtrack"]}
{"sentence": "in this disney animated movie seth green does the voice of a boy who helps in a rescue mission", "tokens": ["in", "this", "disney", "animated", "movie", "seth", "green", "does", "the", "voice", "of", "a", "boy", "who", "helps", "in", "a", "rescue", "mission"], "ner_tags": ["O", "O", "B-Director", "B-Genre", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2011 superhero film involves the god of thunder being striped of his power and exiled to earth", "tokens": ["what", "2011", "superhero", "film", "involves", "the", "god", "of", "thunder", "being", "striped", "of", "his", "power", "and", "exiled", "to", "earth"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "isao takahata directed this sad japanese animation about a young boy and his little sister s struggle to survive in japan during world war ii based on the novel by akiyuki nosaka", "tokens": ["isao", "takahata", "directed", "this", "sad", "japanese", "animation", "about", "a", "young", "boy", "and", "his", "little", "sister", "s", "struggle", "to", "survive", "in", "japan", "during", "world", "war", "ii", "based", "on", "the", "novel", "by", "akiyuki", "nosaka"], "ner_tags": ["B-Director", "I-Director", "O", "O", "B-Opinion", "B-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin"]}
{"sentence": "what 2011 romantic comedy centers around a husband s life change after his wife asks him for a divorce", "tokens": ["what", "2011", "romantic", "comedy", "centers", "around", "a", "husband", "s", "life", "change", "after", "his", "wife", "asks", "him", "for", "a", "divorce"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1990 martin scorsese film featured robert deniro ray liotta and joe pesci as mobsters", "tokens": ["what", "1990", "martin", "scorsese", "film", "featured", "robert", "deniro", "ray", "liotta", "and", "joe", "pesci", "as", "mobsters"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot"]}
{"sentence": "the movie with the song i m going to wash that man right out of my hair", "tokens": ["the", "movie", "with", "the", "song", "i", "m", "going", "to", "wash", "that", "man", "right", "out", "of", "my", "hair"], "ner_tags": ["O", "O", "O", "O", "O", "B-Soundtrack", "I-Soundtrack", "I-Soundtrack", "I-Soundtrack", "I-Soundtrack", "I-Soundtrack", "I-Soundtrack", "I-Soundtrack", "I-Soundtrack", "I-Soundtrack", "I-Soundtrack", "I-Soundtrack"]}
{"sentence": "two friends try sharing an apartment but their ideas of housekeeping and lifestyles are as different as night and day", "tokens": ["two", "friends", "try", "sharing", "an", "apartment", "but", "their", "ideas", "of", "housekeeping", "and", "lifestyles", "are", "as", "different", "as", "night", "and", "day"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s that movie with julie andrews and all those kids that she s taking care of that takes place in the alps or something", "tokens": ["what", "s", "that", "movie", "with", "julie", "andrews", "and", "all", "those", "kids", "that", "she", "s", "taking", "care", "of", "that", "takes", "place", "in", "the", "alps", "or", "something"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O"]}
{"sentence": "what is this timeless horror flick that depicts a savage dog", "tokens": ["what", "is", "this", "timeless", "horror", "flick", "that", "depicts", "a", "savage", "dog"], "ner_tags": ["O", "O", "O", "B-Opinion", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this movie was a mockumentary describing a rock band s successes and blunders in the world of rock and roll an exploding drummer was a common theme", "tokens": ["this", "movie", "was", "a", "mockumentary", "describing", "a", "rock", "band", "s", "successes", "and", "blunders", "in", "the", "world", "of", "rock", "and", "roll", "an", "exploding", "drummer", "was", "a", "common", "theme"], "ner_tags": ["O", "O", "O", "O", "B-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O"]}
{"sentence": "i am thinking of a film that stars ben stiller as the leader of a group of employees who are cheated out of their money by a crooked businessman", "tokens": ["i", "am", "thinking", "of", "a", "film", "that", "stars", "ben", "stiller", "as", "the", "leader", "of", "a", "group", "of", "employees", "who", "are", "cheated", "out", "of", "their", "money", "by", "a", "crooked", "businessman"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this surreal romp from 1967 stars the beatles and features some of their famous hit songs", "tokens": ["this", "surreal", "romp", "from", "1967", "stars", "the", "beatles", "and", "features", "some", "of", "their", "famous", "hit", "songs"], "ner_tags": ["O", "B-Opinion", "O", "O", "B-Year", "O", "O", "B-Character_Name", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the famous disney animated movie about a cute deer", "tokens": ["what", "is", "the", "famous", "disney", "animated", "movie", "about", "a", "cute", "deer"], "ner_tags": ["O", "O", "O", "O", "B-Genre", "I-Genre", "O", "O", "O", "B-Plot", "I-Plot"]}
{"sentence": "what 1977 steven spielberg sci fi adventured found richard dreyfuss making a mountain out of mashed potatoes", "tokens": ["what", "1977", "steven", "spielberg", "sci", "fi", "adventured", "found", "richard", "dreyfuss", "making", "a", "mountain", "out", "of", "mashed", "potatoes"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "B-Genre", "I-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2012 movie revived the american pie chain by bringing back jason biggs alyson hannigan and most of the rest of the cast from the original film", "tokens": ["what", "2012", "movie", "revived", "the", "american", "pie", "chain", "by", "bringing", "back", "jason", "biggs", "alyson", "hannigan", "and", "most", "of", "the", "rest", "of", "the", "cast", "from", "the", "original", "film"], "ner_tags": ["O", "B-Year", "O", "O", "O", "B-Relationship", "I-Relationship", "I-Relationship", "O", "O", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what is the name of the movie about a friendship between a grown man and his stuffed animal", "tokens": ["what", "is", "the", "name", "of", "the", "movie", "about", "a", "friendship", "between", "a", "grown", "man", "and", "his", "stuffed", "animal"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this is a 2012 american action thriller film directed by tony gilroy the film stars jeremy renner rachel weisz and edward norton", "tokens": ["this", "is", "a", "2012", "american", "action", "thriller", "film", "directed", "by", "tony", "gilroy", "the", "film", "stars", "jeremy", "renner", "rachel", "weisz", "and", "edward", "norton"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "this 2003 disney movie about a fish in search of his son won several oscars including best animated feature", "tokens": ["this", "2003", "disney", "movie", "about", "a", "fish", "in", "search", "of", "his", "son", "won", "several", "oscars", "including", "best", "animated", "feature"], "ner_tags": ["O", "B-Year", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Award", "I-Award", "I-Award", "I-Award", "I-Award", "I-Award", "I-Award"]}
{"sentence": "what christmas movie is based on charles dickens classic literary masterpiece featuring george c scott as the main character", "tokens": ["what", "christmas", "movie", "is", "based", "on", "charles", "dickens", "classic", "literary", "masterpiece", "featuring", "george", "c", "scott", "as", "the", "main", "character"], "ner_tags": ["O", "B-Genre", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "B-Opinion", "O", "B-Actor", "I-Actor", "I-Actor", "O", "O", "O", "O"]}
{"sentence": "what is the movie with the rat that sings about overeating at the fair and the pig that loves a spider", "tokens": ["what", "is", "the", "movie", "with", "the", "rat", "that", "sings", "about", "overeating", "at", "the", "fair", "and", "the", "pig", "that", "loves", "a", "spider"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie in which bella swan finally decides between edward and jacob gets married and gets pregnant", "tokens": ["what", "is", "the", "movie", "in", "which", "bella", "swan", "finally", "decides", "between", "edward", "and", "jacob", "gets", "married", "and", "gets", "pregnant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Character_Name", "I-Character_Name", "O", "O", "O", "B-Character_Name", "O", "B-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the name of the movie that had shakespeare as a character and he falls for a girl", "tokens": ["what", "s", "the", "name", "of", "the", "movie", "that", "had", "shakespeare", "as", "a", "character", "and", "he", "falls", "for", "a", "girl"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Character_Name", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1945 elia kazan film centers around a young woman coming of age during the early 1900 s", "tokens": ["what", "1945", "elia", "kazan", "film", "centers", "around", "a", "young", "woman", "coming", "of", "age", "during", "the", "early", "1900", "s"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "the marshmallow goo was actually shaving cream that covered the actors at the end of this 1984 comedy", "tokens": ["the", "marshmallow", "goo", "was", "actually", "shaving", "cream", "that", "covered", "the", "actors", "at", "the", "end", "of", "this", "1984", "comedy"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Year", "B-Genre"]}
{"sentence": "robert j flaherty directs this silent film documentary that follows the life of a native alaskan family", "tokens": ["robert", "j", "flaherty", "directs", "this", "silent", "film", "documentary", "that", "follows", "the", "life", "of", "a", "native", "alaskan", "family"], "ner_tags": ["B-Director", "I-Director", "I-Director", "O", "O", "B-Genre", "I-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is this classic horror movie with freddy kruger as murdering people through their dreams", "tokens": ["what", "is", "this", "classic", "horror", "movie", "with", "freddy", "kruger", "as", "murdering", "people", "through", "their", "dreams"], "ner_tags": ["O", "O", "O", "B-Genre", "I-Genre", "O", "O", "B-Character_Name", "I-Character_Name", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the title of the movie that sigourney weaver starred in this about the silverbacks", "tokens": ["what", "is", "the", "title", "of", "the", "movie", "that", "sigourney", "weaver", "starred", "in", "this", "about", "the", "silverbacks"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what the 2002 musical based in the 1920 s starring ren e zellweger and catherine zeta jones as two girls on death row fighting for there life", "tokens": ["what", "the", "2002", "musical", "based", "in", "the", "1920", "s", "starring", "ren", "e", "zellweger", "and", "catherine", "zeta", "jones", "as", "two", "girls", "on", "death", "row", "fighting", "for", "there", "life"], "ner_tags": ["O", "O", "B-Year", "B-Genre", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "B-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the movie about the two dogs that fall in love and eat spaghetti with each other", "tokens": ["what", "s", "the", "movie", "about", "the", "two", "dogs", "that", "fall", "in", "love", "and", "eat", "spaghetti", "with", "each", "other"], "ner_tags": ["O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie about the two guys who fall in love with the same woman that is a french movie from the 60 s", "tokens": ["what", "is", "the", "movie", "about", "the", "two", "guys", "who", "fall", "in", "love", "with", "the", "same", "woman", "that", "is", "a", "french", "movie", "from", "the", "60", "s"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "B-Genre", "O", "O", "O", "B-Year", "I-Year"]}
{"sentence": "vincente minnelli and judy garland met on this 1944 movie about four sisters and the world s fair", "tokens": ["vincente", "minnelli", "and", "judy", "garland", "met", "on", "this", "1944", "movie", "about", "four", "sisters", "and", "the", "world", "s", "fair"], "ner_tags": ["B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Year", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the new rob reiner movie about based on the book by wendelin van draanen called", "tokens": ["what", "is", "the", "new", "rob", "reiner", "movie", "about", "based", "on", "the", "book", "by", "wendelin", "van", "draanen", "called"], "ner_tags": ["O", "O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O"]}
{"sentence": "what is that 2010 american romantic comedy film directed by garry marshall that features an ensemble cast including jessica alba", "tokens": ["what", "is", "that", "2010", "american", "romantic", "comedy", "film", "directed", "by", "garry", "marshall", "that", "features", "an", "ensemble", "cast", "including", "jessica", "alba"], "ner_tags": ["O", "O", "O", "B-Year", "O", "B-Genre", "I-Genre", "O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor"]}
{"sentence": "what is the movie about a young aang who tries to defeat the fire nation", "tokens": ["what", "is", "the", "movie", "about", "a", "young", "aang", "who", "tries", "to", "defeat", "the", "fire", "nation"], "ner_tags": ["O", "O", "O", "O", "O", "B-Plot", "I-Plot", "B-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is this recent movie featuring mark wahlburg that stars a talking teddy bear", "tokens": ["what", "is", "this", "recent", "movie", "featuring", "mark", "wahlburg", "that", "stars", "a", "talking", "teddy", "bear"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "classic movie about the preppy college student who falls in love with and marries a girl from a lower social class they play in the snow and it s really romantic", "tokens": ["classic", "movie", "about", "the", "preppy", "college", "student", "who", "falls", "in", "love", "with", "and", "marries", "a", "girl", "from", "a", "lower", "social", "class", "they", "play", "in", "the", "snow", "and", "it", "s", "really", "romantic"], "ner_tags": ["B-Opinion", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "B-Opinion", "I-Opinion"]}
{"sentence": "what is this movie based on a classic novel about a newly married women and the ghost of her husband s ex wife", "tokens": ["what", "is", "this", "movie", "based", "on", "a", "classic", "novel", "about", "a", "newly", "married", "women", "and", "the", "ghost", "of", "her", "husband", "s", "ex", "wife"], "ner_tags": ["O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in what movie there was a fake this is based on a true story blurb at the start a rather cold way to increase the drama", "tokens": ["in", "what", "movie", "there", "was", "a", "fake", "this", "is", "based", "on", "a", "true", "story", "blurb", "at", "the", "start", "a", "rather", "cold", "way", "to", "increase", "the", "drama"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Opinion", "I-Opinion", "O", "O", "O", "O"]}
{"sentence": "what 2011 film finds four police officers struggling with their faith and roles as fathers and husbands after a tragedy occurs", "tokens": ["what", "2011", "film", "finds", "four", "police", "officers", "struggling", "with", "their", "faith", "and", "roles", "as", "fathers", "and", "husbands", "after", "a", "tragedy", "occurs"], "ner_tags": ["O", "B-Year", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "animated family movie about a big nosed villain who looks after three young orphan girls and minions", "tokens": ["animated", "family", "movie", "about", "a", "big", "nosed", "villain", "who", "looks", "after", "three", "young", "orphan", "girls", "and", "minions"], "ner_tags": ["B-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is this 1982 movie starring sean penn featuring high school kids growing up very fast", "tokens": ["what", "is", "this", "1982", "movie", "starring", "sean", "penn", "featuring", "high", "school", "kids", "growing", "up", "very", "fast"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "2010 movie about two 8 th graders who start to have feelings for each other even though they re opposites", "tokens": ["2010", "movie", "about", "two", "8", "th", "graders", "who", "start", "to", "have", "feelings", "for", "each", "other", "even", "though", "they", "re", "opposites"], "ner_tags": ["B-Year", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "1970 s horror film based on a book by stephen king directed by stanley kubrick and starring jack nicholson", "tokens": ["1970", "s", "horror", "film", "based", "on", "a", "book", "by", "stephen", "king", "directed", "by", "stanley", "kubrick", "and", "starring", "jack", "nicholson"], "ner_tags": ["B-Year", "I-Year", "B-Genre", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "O", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor"]}
{"sentence": "from book keeper to model in paris audrey hepburn makes this film unforgettable", "tokens": ["from", "book", "keeper", "to", "model", "in", "paris", "audrey", "hepburn", "makes", "this", "film", "unforgettable"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Actor", "I-Actor", "O", "O", "O", "B-Opinion"]}
{"sentence": "what 2011 dc comic superhero movie is focused on an alien ring giving a test pilot superpowers", "tokens": ["what", "2011", "dc", "comic", "superhero", "movie", "is", "focused", "on", "an", "alien", "ring", "giving", "a", "test", "pilot", "superpowers"], "ner_tags": ["O", "B-Year", "O", "B-Genre", "I-Genre", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "bruce willis and tracy morgan star in this buddy cop movie about a rare baseball card that is stolen and their efforts to get it back", "tokens": ["bruce", "willis", "and", "tracy", "morgan", "star", "in", "this", "buddy", "cop", "movie", "about", "a", "rare", "baseball", "card", "that", "is", "stolen", "and", "their", "efforts", "to", "get", "it", "back"], "ner_tags": ["B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of that movie that is based on a george orwell book that parodies communism", "tokens": ["what", "is", "the", "name", "of", "that", "movie", "that", "is", "based", "on", "a", "george", "orwell", "book", "that", "parodies", "communism"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "B-Plot", "I-Plot"]}
{"sentence": "what is the name of the movie that s based on the novel by sara gruen", "tokens": ["what", "is", "the", "name", "of", "the", "movie", "that", "s", "based", "on", "the", "novel", "by", "sara", "gruen"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin"]}
{"sentence": "i am thinking of the george cukor classic remarriage film starring katherine hepburn cary grant and jimmy stewart", "tokens": ["i", "am", "thinking", "of", "the", "george", "cukor", "classic", "remarriage", "film", "starring", "katherine", "hepburn", "cary", "grant", "and", "jimmy", "stewart"], "ner_tags": ["O", "O", "O", "O", "O", "B-Director", "I-Director", "B-Genre", "I-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor"]}
{"sentence": "this is a 2011 adaptation of a famous comic book superhero starring chris evans as steve rogers", "tokens": ["this", "is", "a", "2011", "adaptation", "of", "a", "famous", "comic", "book", "superhero", "starring", "chris", "evans", "as", "steve", "rogers"], "ner_tags": ["O", "O", "O", "B-Year", "B-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "O", "B-Actor", "I-Actor", "O", "B-Character_Name", "I-Character_Name"]}
{"sentence": "in a fantasy world a team of warriors must get an artifact to the right place what movie is this", "tokens": ["in", "a", "fantasy", "world", "a", "team", "of", "warriors", "must", "get", "an", "artifact", "to", "the", "right", "place", "what", "movie", "is", "this"], "ner_tags": ["O", "O", "B-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O"]}
{"sentence": "this clark gable picture released in 1939 is the highest grossing film of all time when adjusted for inflation", "tokens": ["this", "clark", "gable", "picture", "released", "in", "1939", "is", "the", "highest", "grossing", "film", "of", "all", "time", "when", "adjusted", "for", "inflation"], "ner_tags": ["O", "B-Actor", "I-Actor", "O", "O", "O", "B-Year", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "i m thinking of the action movie from 1988 where bruce willis fights terrorists on christmas", "tokens": ["i", "m", "thinking", "of", "the", "action", "movie", "from", "1988", "where", "bruce", "willis", "fights", "terrorists", "on", "christmas"], "ner_tags": ["O", "O", "O", "O", "O", "B-Genre", "I-Genre", "O", "B-Year", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1968 movie directed by stanley kubrick featured an intelligent computer named h a l 9000 on a journey in space", "tokens": ["what", "1968", "movie", "directed", "by", "stanley", "kubrick", "featured", "an", "intelligent", "computer", "named", "h", "a", "l", "9000", "on", "a", "journey", "in", "space"], "ner_tags": ["O", "B-Year", "O", "O", "O", "B-Director", "I-Director", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "B-Character_Name", "I-Character_Name", "I-Character_Name", "I-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i am thinking of a comedy film that tells the story of four men who travel back to the 1980 s and emerge with a positive life changing experience", "tokens": ["i", "am", "thinking", "of", "a", "comedy", "film", "that", "tells", "the", "story", "of", "four", "men", "who", "travel", "back", "to", "the", "1980", "s", "and", "emerge", "with", "a", "positive", "life", "changing", "experience"], "ner_tags": ["O", "O", "O", "O", "O", "B-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what intense gritty 2012 cop drama focuses on the day to day of two los angelos police officers", "tokens": ["what", "intense", "gritty", "2012", "cop", "drama", "focuses", "on", "the", "day", "to", "day", "of", "two", "los", "angelos", "police", "officers"], "ner_tags": ["O", "B-Opinion", "I-Opinion", "B-Year", "B-Genre", "I-Genre", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is thors weapon he uses in this movie it was stolen and then recovered", "tokens": ["what", "is", "thors", "weapon", "he", "uses", "in", "this", "movie", "it", "was", "stolen", "and", "then", "recovered"], "ner_tags": ["O", "O", "B-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "anna faris stars as a woman who looks back at the past twenty men she s had relationships with in her life and wonders if one of them might be her one true love", "tokens": ["anna", "faris", "stars", "as", "a", "woman", "who", "looks", "back", "at", "the", "past", "twenty", "men", "she", "s", "had", "relationships", "with", "in", "her", "life", "and", "wonders", "if", "one", "of", "them", "might", "be", "her", "one", "true", "love"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "which 1985 film based on a 1982 novel by alice walker focuses on the problems faced by african american women during the 1900 s", "tokens": ["which", "1985", "film", "based", "on", "a", "1982", "novel", "by", "alice", "walker", "focuses", "on", "the", "problems", "faced", "by", "african", "american", "women", "during", "the", "1900", "s"], "ner_tags": ["O", "B-Year", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "sigourney weaver had initially been very hesitant to reprise her role as ripley and had rejected numerous offers from fox studios to do any sequels fearing that her character would be poorly written and a sub par sequel could hurt the legacy of the first movie however she was so impressed by the high quality of james cameron s script she finally agreed to do the film", "tokens": ["sigourney", "weaver", "had", "initially", "been", "very", "hesitant", "to", "reprise", "her", "role", "as", "ripley", "and", "had", "rejected", "numerous", "offers", "from", "fox", "studios", "to", "do", "any", "sequels", "fearing", "that", "her", "character", "would", "be", "poorly", "written", "and", "a", "sub", "par", "sequel", "could", "hurt", "the", "legacy", "of", "the", "first", "movie", "however", "she", "was", "so", "impressed", "by", "the", "high", "quality", "of", "james", "cameron", "s", "script", "she", "finally", "agreed", "to", "do", "the", "film"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Character_Name", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Opinion", "I-Opinion", "O", "B-Director", "I-Director", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what is the 2010 american comedy film starring loretta devine and danny glover that is a remake of the british film of the same name", "tokens": ["what", "is", "the", "2010", "american", "comedy", "film", "starring", "loretta", "devine", "and", "danny", "glover", "that", "is", "a", "remake", "of", "the", "british", "film", "of", "the", "same", "name"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Relationship", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "when a plane crash strands a group of workers in the far north the fierce winter weather becomes the least of their problems in man eating wolf territory in this recent film", "tokens": ["when", "a", "plane", "crash", "strands", "a", "group", "of", "workers", "in", "the", "far", "north", "the", "fierce", "winter", "weather", "becomes", "the", "least", "of", "their", "problems", "in", "man", "eating", "wolf", "territory", "in", "this", "recent", "film"], "ner_tags": ["O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O"]}
{"sentence": "what is the movie based on another jrr tolkien book but not the lord of the rings trilogy", "tokens": ["what", "is", "the", "movie", "based", "on", "another", "jrr", "tolkien", "book", "but", "not", "the", "lord", "of", "the", "rings", "trilogy"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "O", "O", "O", "B-Character_Name", "I-Character_Name", "I-Character_Name", "I-Character_Name", "I-Character_Name"]}
{"sentence": "a hilarious comedy by sasha cohen about a dictator from the middle east traveling through america", "tokens": ["a", "hilarious", "comedy", "by", "sasha", "cohen", "about", "a", "dictator", "from", "the", "middle", "east", "traveling", "through", "america"], "ner_tags": ["O", "B-Opinion", "B-Genre", "O", "B-Director", "I-Director", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of the dark comedy starring kevin spacey where he has sex with his daughter s friend", "tokens": ["what", "is", "the", "name", "of", "the", "dark", "comedy", "starring", "kevin", "spacey", "where", "he", "has", "sex", "with", "his", "daughter", "s", "friend"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this science fiction movie is based on phillip k dick s do androids dream of electric sheep", "tokens": ["this", "science", "fiction", "movie", "is", "based", "on", "phillip", "k", "dick", "s", "do", "androids", "dream", "of", "electric", "sheep"], "ner_tags": ["O", "B-Genre", "I-Genre", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin"]}
{"sentence": "what film which won an oscar for best picture was directed by steven spielberg in 1992", "tokens": ["what", "film", "which", "won", "an", "oscar", "for", "best", "picture", "was", "directed", "by", "steven", "spielberg", "in", "1992"], "ner_tags": ["O", "O", "O", "B-Award", "I-Award", "I-Award", "I-Award", "I-Award", "I-Award", "O", "O", "O", "B-Director", "I-Director", "O", "B-Year"]}
{"sentence": "what s the name of the movie where clint eastwood s title character tracks down union soldiers who murdered his wife and kid", "tokens": ["what", "s", "the", "name", "of", "the", "movie", "where", "clint", "eastwood", "s", "title", "character", "tracks", "down", "union", "soldiers", "who", "murdered", "his", "wife", "and", "kid"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie based on the book by stephen king and starring tom hanks as a prison guard", "tokens": ["what", "is", "the", "movie", "based", "on", "the", "book", "by", "stephen", "king", "and", "starring", "tom", "hanks", "as", "a", "prison", "guard"], "ner_tags": ["O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is that 1941 american drama film starring and directed by orson welles and considered one of the greatest films of all time", "tokens": ["what", "is", "that", "1941", "american", "drama", "film", "starring", "and", "directed", "by", "orson", "welles", "and", "considered", "one", "of", "the", "greatest", "films", "of", "all", "time"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "O", "O", "O", "B-Director", "I-Director", "O", "B-Opinion", "I-Opinion", "I-Opinion", "I-Opinion", "I-Opinion", "I-Opinion", "I-Opinion", "I-Opinion", "I-Opinion"]}
{"sentence": "what is the movie set in nyc about a case of mistaken identity crisis that turns out well", "tokens": ["what", "is", "the", "movie", "set", "in", "nyc", "about", "a", "case", "of", "mistaken", "identity", "crisis", "that", "turns", "out", "well"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this 2010 survival film features james franco in a practically one man show in the moab desert", "tokens": ["this", "2010", "survival", "film", "features", "james", "franco", "in", "a", "practically", "one", "man", "show", "in", "the", "moab", "desert"], "ner_tags": ["O", "B-Year", "B-Genre", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i am thinking of a movie that is the second to last in a series about a vampire and the human who falls in love with him", "tokens": ["i", "am", "thinking", "of", "a", "movie", "that", "is", "the", "second", "to", "last", "in", "a", "series", "about", "a", "vampire", "and", "the", "human", "who", "falls", "in", "love", "with", "him"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 1979 movie by steven spielberg that features the line i love the smell of napalm in the morning and stars martin sheen and marlon brando", "tokens": ["what", "is", "the", "1979", "movie", "by", "steven", "spielberg", "that", "features", "the", "line", "i", "love", "the", "smell", "of", "napalm", "in", "the", "morning", "and", "stars", "martin", "sheen", "and", "marlon", "brando"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "B-Director", "I-Director", "O", "O", "O", "O", "B-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "ralphie has to convince his parents teachers and santa that a red ryder b b gun really is the perfect gift in this holiday favorite", "tokens": ["ralphie", "has", "to", "convince", "his", "parents", "teachers", "and", "santa", "that", "a", "red", "ryder", "b", "b", "gun", "really", "is", "the", "perfect", "gift", "in", "this", "holiday", "favorite"], "ner_tags": ["B-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Genre", "B-Plot"]}
{"sentence": "i am thinking of a movie that has a very big ape that was captured off an island and brought back to the states the ape falls in love with a blond woman in the movie and they wind up on top of a skyscraper", "tokens": ["i", "am", "thinking", "of", "a", "movie", "that", "has", "a", "very", "big", "ape", "that", "was", "captured", "off", "an", "island", "and", "brought", "back", "to", "the", "states", "the", "ape", "falls", "in", "love", "with", "a", "blond", "woman", "in", "the", "movie", "and", "they", "wind", "up", "on", "top", "of", "a", "skyscraper"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "a zucker brothers takeoff of 1970 s disaster films this screwball film features leslie nielsen and nba legend kareem abdul jabaar as others making appearances aboard a flight gone awry", "tokens": ["a", "zucker", "brothers", "takeoff", "of", "1970", "s", "disaster", "films", "this", "screwball", "film", "features", "leslie", "nielsen", "and", "nba", "legend", "kareem", "abdul", "jabaar", "as", "others", "making", "appearances", "aboard", "a", "flight", "gone", "awry"], "ner_tags": ["O", "B-Director", "I-Director", "O", "O", "B-Year", "O", "B-Origin", "I-Origin", "O", "B-Genre", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Actor", "I-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this film starring steve carrell and zach galifiankis is about a group of men who invite losers for dinner", "tokens": ["this", "film", "starring", "steve", "carrell", "and", "zach", "galifiankis", "is", "about", "a", "group", "of", "men", "who", "invite", "losers", "for", "dinner"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is that 2010 superhero action comedy movie based on the comic book of the same name starring nicolas cage as big daddy", "tokens": ["what", "is", "that", "2010", "superhero", "action", "comedy", "movie", "based", "on", "the", "comic", "book", "of", "the", "same", "name", "starring", "nicolas", "cage", "as", "big", "daddy"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "B-Actor", "I-Actor", "O", "B-Character_Name", "I-Character_Name"]}
{"sentence": "what is the 2012 blockbuster hit that featured a team of superheros that banded together to fight a supervillain", "tokens": ["what", "is", "the", "2012", "blockbuster", "hit", "that", "featured", "a", "team", "of", "superheros", "that", "banded", "together", "to", "fight", "a", "supervillain"], "ner_tags": ["O", "O", "O", "B-Year", "B-Opinion", "I-Opinion", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in which 1993 did we hear holly hunter use not my speaking voice but my mind s voice", "tokens": ["in", "which", "1993", "did", "we", "hear", "holly", "hunter", "use", "not", "my", "speaking", "voice", "but", "my", "mind", "s", "voice"], "ner_tags": ["O", "O", "B-Year", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote"]}
{"sentence": "which 90 s american slasher film directed by wes craven features an iconically masked murderer who preys on teenagers loosely based on the gainesville ripper", "tokens": ["which", "90", "s", "american", "slasher", "film", "directed", "by", "wes", "craven", "features", "an", "iconically", "masked", "murderer", "who", "preys", "on", "teenagers", "loosely", "based", "on", "the", "gainesville", "ripper"], "ner_tags": ["O", "B-Year", "I-Year", "B-Genre", "I-Genre", "O", "O", "O", "B-Director", "I-Director", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin"]}
{"sentence": "what 1969 western garnered a best leading actor oscar win for john wayne in his role as rooster cogburn", "tokens": ["what", "1969", "western", "garnered", "a", "best", "leading", "actor", "oscar", "win", "for", "john", "wayne", "in", "his", "role", "as", "rooster", "cogburn"], "ner_tags": ["O", "B-Year", "B-Genre", "O", "O", "B-Award", "I-Award", "I-Award", "I-Award", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "B-Character_Name", "I-Character_Name"]}
{"sentence": "what s the name of a spielberg directed film about an amusement park that s a blast from the past", "tokens": ["what", "s", "the", "name", "of", "a", "spielberg", "directed", "film", "about", "an", "amusement", "park", "that", "s", "a", "blast", "from", "the", "past"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Director", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "name the 1962 british epic film that stars peter o toole wearing a long robe and turban", "tokens": ["name", "the", "1962", "british", "epic", "film", "that", "stars", "peter", "o", "toole", "wearing", "a", "long", "robe", "and", "turban"], "ner_tags": ["O", "O", "B-Year", "O", "B-Genre", "O", "O", "O", "B-Actor", "I-Actor", "I-Actor", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what is the movie starring billy bob thorton about the mentally disabled man who kills for the woman helping him", "tokens": ["what", "is", "the", "movie", "starring", "billy", "bob", "thorton", "about", "the", "mentally", "disabled", "man", "who", "kills", "for", "the", "woman", "helping", "him"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 2011 film starring anne hathaway and jim sturgess based on the novel of the same name", "tokens": ["what", "is", "the", "2011", "film", "starring", "anne", "hathaway", "and", "jim", "sturgess", "based", "on", "the", "novel", "of", "the", "same", "name"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin"]}
{"sentence": "what is 2010 american action thriller film directed by tony scott as his final film written by mark bomback and starring denzel washington and chris pine", "tokens": ["what", "is", "2010", "american", "action", "thriller", "film", "directed", "by", "tony", "scott", "as", "his", "final", "film", "written", "by", "mark", "bomback", "and", "starring", "denzel", "washington", "and", "chris", "pine"], "ner_tags": ["O", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "what is the 2000 critically acclaimed ang lee movie starring yun fat chow where two warriors and a nobleman s daughter search for a magical jade sword", "tokens": ["what", "is", "the", "2000", "critically", "acclaimed", "ang", "lee", "movie", "starring", "yun", "fat", "chow", "where", "two", "warriors", "and", "a", "nobleman", "s", "daughter", "search", "for", "a", "magical", "jade", "sword"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in which 1968 film does a group of people hide from bloodthirsty zombies in a farmhouse", "tokens": ["in", "which", "1968", "film", "does", "a", "group", "of", "people", "hide", "from", "bloodthirsty", "zombies", "in", "a", "farmhouse"], "ner_tags": ["O", "O", "B-Year", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2010 movie centers around a cia special forces team that was betrayed and left for dead by their superiors", "tokens": ["what", "2010", "movie", "centers", "around", "a", "cia", "special", "forces", "team", "that", "was", "betrayed", "and", "left", "for", "dead", "by", "their", "superiors"], "ner_tags": ["O", "B-Year", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the movie based on a true story about a baseball team manager who uses a new system to built a winning team", "tokens": ["what", "s", "the", "movie", "based", "on", "a", "true", "story", "about", "a", "baseball", "team", "manager", "who", "uses", "a", "new", "system", "to", "built", "a", "winning", "team"], "ner_tags": ["O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "one of the most popular french language films of the last decade across the globe this features a young quirky french woman and her incessant day dreams", "tokens": ["one", "of", "the", "most", "popular", "french", "language", "films", "of", "the", "last", "decade", "across", "the", "globe", "this", "features", "a", "young", "quirky", "french", "woman", "and", "her", "incessant", "day", "dreams"], "ner_tags": ["O", "O", "O", "B-Opinion", "I-Opinion", "B-Genre", "I-Genre", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 1934 american drama movie directed by philip moeller and starring irene dunne john boles and lionel atwill", "tokens": ["what", "is", "the", "1934", "american", "drama", "movie", "directed", "by", "philip", "moeller", "and", "starring", "irene", "dunne", "john", "boles", "and", "lionel", "atwill"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "what is the 1998 british romantic comedy starring joseph fiennes and gwyneth paltrow who won an academy award for her role", "tokens": ["what", "is", "the", "1998", "british", "romantic", "comedy", "starring", "joseph", "fiennes", "and", "gwyneth", "paltrow", "who", "won", "an", "academy", "award", "for", "her", "role"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what 1975 film featured susan sarandon with the tag line a different set of jaws", "tokens": ["what", "1975", "film", "featured", "susan", "sarandon", "with", "the", "tag", "line", "a", "different", "set", "of", "jaws"], "ner_tags": ["O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "B-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote"]}
{"sentence": "janet leigh was nomiated for an oscar for her role in this 1960 alfred hitchcock classic", "tokens": ["janet", "leigh", "was", "nomiated", "for", "an", "oscar", "for", "her", "role", "in", "this", "1960", "alfred", "hitchcock", "classic"], "ner_tags": ["B-Actor", "I-Actor", "O", "B-Award", "I-Award", "I-Award", "I-Award", "O", "O", "O", "O", "O", "B-Year", "B-Director", "I-Director", "B-Opinion"]}
{"sentence": "what is the movie in which a family tries to support yet distance themselves from one black sheep family member who tends to fall into idealism", "tokens": ["what", "is", "the", "movie", "in", "which", "a", "family", "tries", "to", "support", "yet", "distance", "themselves", "from", "one", "black", "sheep", "family", "member", "who", "tends", "to", "fall", "into", "idealism"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of the 1942 errol flynn movie in which he portrays a real life boxing champion", "tokens": ["what", "is", "the", "name", "of", "the", "1942", "errol", "flynn", "movie", "in", "which", "he", "portrays", "a", "real", "life", "boxing", "champion"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Year", "B-Actor", "I-Actor", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the romance movie with rachel mcadams and channing tatum about a wife who loses all memory of her husband", "tokens": ["what", "is", "the", "romance", "movie", "with", "rachel", "mcadams", "and", "channing", "tatum", "about", "a", "wife", "who", "loses", "all", "memory", "of", "her", "husband"], "ner_tags": ["O", "O", "O", "B-Genre", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "it s the film directed by sofia coppola in which bill murray and scarlett johansen meet at a tokyo hotel", "tokens": ["it", "s", "the", "film", "directed", "by", "sofia", "coppola", "in", "which", "bill", "murray", "and", "scarlett", "johansen", "meet", "at", "a", "tokyo", "hotel"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the second movie in the series about a group of weird looking but lovable creatures traveling across some mythical land", "tokens": ["what", "s", "the", "second", "movie", "in", "the", "series", "about", "a", "group", "of", "weird", "looking", "but", "lovable", "creatures", "traveling", "across", "some", "mythical", "land"], "ner_tags": ["O", "O", "O", "B-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the darren aronofsky directed film starring natalie portman as a ballet dancer that sees strange illusions", "tokens": ["what", "is", "the", "darren", "aronofsky", "directed", "film", "starring", "natalie", "portman", "as", "a", "ballet", "dancer", "that", "sees", "strange", "illusions"], "ner_tags": ["O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "name the 1971 crime film directed by famed director stanley kubrick", "tokens": ["name", "the", "1971", "crime", "film", "directed", "by", "famed", "director", "stanley", "kubrick"], "ner_tags": ["O", "O", "B-Year", "B-Genre", "O", "O", "O", "O", "O", "B-Director", "I-Director"]}
{"sentence": "this stanley kubrick directed hit features malcolm mcdowell and a the government that aims to solve society s crime problem", "tokens": ["this", "stanley", "kubrick", "directed", "hit", "features", "malcolm", "mcdowell", "and", "a", "the", "government", "that", "aims", "to", "solve", "society", "s", "crime", "problem"], "ner_tags": ["O", "B-Director", "I-Director", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this road trip comedy features robert downey jr and zach galifianakis as an unlikely pair traveling across the country", "tokens": ["this", "road", "trip", "comedy", "features", "robert", "downey", "jr", "and", "zach", "galifianakis", "as", "an", "unlikely", "pair", "traveling", "across", "the", "country"], "ner_tags": ["O", "B-Genre", "I-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1968 science fiction film directed by stanley kubrick depicted a spaceship s computer named hal 9000", "tokens": ["what", "1968", "science", "fiction", "film", "directed", "by", "stanley", "kubrick", "depicted", "a", "spaceship", "s", "computer", "named", "hal", "9000"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "O", "O", "O", "B-Director", "I-Director", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the drama sport movie about a failing baseball manager that in last ditch attempt uses computer generated statistics to put together an unbeatable team", "tokens": ["what", "is", "the", "drama", "sport", "movie", "about", "a", "failing", "baseball", "manager", "that", "in", "last", "ditch", "attempt", "uses", "computer", "generated", "statistics", "to", "put", "together", "an", "unbeatable", "team"], "ner_tags": ["O", "O", "O", "B-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what movie stars gwyneth paltrow in the midst of a famous author s life and romance", "tokens": ["what", "movie", "stars", "gwyneth", "paltrow", "in", "the", "midst", "of", "a", "famous", "author", "s", "life", "and", "romance"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1962 movie starring john wayne tells the story of d day from both the allied and german points of view", "tokens": ["what", "1962", "movie", "starring", "john", "wayne", "tells", "the", "story", "of", "d", "day", "from", "both", "the", "allied", "and", "german", "points", "of", "view"], "ner_tags": ["O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of the movie where anakin skywalker races on the planet tattooine", "tokens": ["what", "is", "the", "name", "of", "the", "movie", "where", "anakin", "skywalker", "races", "on", "the", "planet", "tattooine"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Character_Name", "I-Character_Name", "O", "O", "O", "O", "O"]}
{"sentence": "during the shooting of this 2011 film actors daniel craig and rachel weisz fell in love and started a relationship marrying in a discrete ceremony a few months later", "tokens": ["during", "the", "shooting", "of", "this", "2011", "film", "actors", "daniel", "craig", "and", "rachel", "weisz", "fell", "in", "love", "and", "started", "a", "relationship", "marrying", "in", "a", "discrete", "ceremony", "a", "few", "months", "later"], "ner_tags": ["O", "O", "O", "O", "O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O"]}
{"sentence": "in this sequel to a psychological thriller mad killer jigsaw continues his murder spree with new victims", "tokens": ["in", "this", "sequel", "to", "a", "psychological", "thriller", "mad", "killer", "jigsaw", "continues", "his", "murder", "spree", "with", "new", "victims"], "ner_tags": ["O", "O", "B-Relationship", "O", "B-Relationship", "I-Relationship", "I-Relationship", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 2012 film that starred jonny depp as a vampire trying to save his family from financial problems", "tokens": ["what", "is", "the", "2012", "film", "that", "starred", "jonny", "depp", "as", "a", "vampire", "trying", "to", "save", "his", "family", "from", "financial", "problems"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i m thinking of the movie where the man is a doctor and he s framed for his wife s death and he becomes wanted", "tokens": ["i", "m", "thinking", "of", "the", "movie", "where", "the", "man", "is", "a", "doctor", "and", "he", "s", "framed", "for", "his", "wife", "s", "death", "and", "he", "becomes", "wanted"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what pixar movie won two awards at the 82 nd academy awards for best animated feature and academy award for best original score", "tokens": ["what", "pixar", "movie", "won", "two", "awards", "at", "the", "82", "nd", "academy", "awards", "for", "best", "animated", "feature", "and", "academy", "award", "for", "best", "original", "score"], "ner_tags": ["O", "B-Director", "O", "O", "B-Award", "I-Award", "O", "O", "B-Award", "I-Award", "I-Award", "I-Award", "O", "B-Award", "I-Award", "I-Award", "O", "B-Award", "I-Award", "O", "B-Award", "I-Award", "I-Award"]}
{"sentence": "what is the first film starring mel gibson and danny glover two cops working to stop drug smugglers", "tokens": ["what", "is", "the", "first", "film", "starring", "mel", "gibson", "and", "danny", "glover", "two", "cops", "working", "to", "stop", "drug", "smugglers"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this film showed a more feminine side of adam sandler who played himself and his twin sister", "tokens": ["this", "film", "showed", "a", "more", "feminine", "side", "of", "adam", "sandler", "who", "played", "himself", "and", "his", "twin", "sister"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 2011 movie about the god of thunder who becomes one of the greatest defenders of the human race", "tokens": ["what", "is", "the", "2011", "movie", "about", "the", "god", "of", "thunder", "who", "becomes", "one", "of", "the", "greatest", "defenders", "of", "the", "human", "race"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 1961 movie starring david niven and gregory peck about a british team sent across occupied territory to destroy a german gun placement", "tokens": ["what", "is", "the", "1961", "movie", "starring", "david", "niven", "and", "gregory", "peck", "about", "a", "british", "team", "sent", "across", "occupied", "territory", "to", "destroy", "a", "german", "gun", "placement"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what famous tale began as a charles dickens novella and has been adapted to film stage opera", "tokens": ["what", "famous", "tale", "began", "as", "a", "charles", "dickens", "novella", "and", "has", "been", "adapted", "to", "film", "stage", "opera"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "in what movie do a group of friends relive a fateful night to ensure jacob is born", "tokens": ["in", "what", "movie", "do", "a", "group", "of", "friends", "relive", "a", "fateful", "night", "to", "ensure", "jacob", "is", "born"], "ner_tags": ["O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "B-Plot", "I-Plot"]}
{"sentence": "what is the name of this musical movie about the rivalry between the sharks and jets", "tokens": ["what", "is", "the", "name", "of", "this", "musical", "movie", "about", "the", "rivalry", "between", "the", "sharks", "and", "jets"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "buzz lightyear and woody are action figures that have come to life in this 1995 movie", "tokens": ["buzz", "lightyear", "and", "woody", "are", "action", "figures", "that", "have", "come", "to", "life", "in", "this", "1995", "movie"], "ner_tags": ["B-Character_Name", "I-Character_Name", "O", "B-Character_Name", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Year", "O"]}
{"sentence": "this 1991 disney film features a grotesque hero trying to gain the trust and ultimately love of a young woman", "tokens": ["this", "1991", "disney", "film", "features", "a", "grotesque", "hero", "trying", "to", "gain", "the", "trust", "and", "ultimately", "love", "of", "a", "young", "woman"], "ner_tags": ["O", "B-Year", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the discovery channel type documentary that details the life cycle and migration of the most common inhabitants of the south pole", "tokens": ["what", "is", "the", "discovery", "channel", "type", "documentary", "that", "details", "the", "life", "cycle", "and", "migration", "of", "the", "most", "common", "inhabitants", "of", "the", "south", "pole"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2010 spy comedy film featuring jackie chan who plays a retired spy and falls in love with his next door neighbor", "tokens": ["what", "2010", "spy", "comedy", "film", "featuring", "jackie", "chan", "who", "plays", "a", "retired", "spy", "and", "falls", "in", "love", "with", "his", "next", "door", "neighbor"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is that movie 2012 biographical film co directed by curtis hanson about the life of jay moriarty", "tokens": ["what", "is", "that", "movie", "2012", "biographical", "film", "co", "directed", "by", "curtis", "hanson", "about", "the", "life", "of", "jay", "moriarty"], "ner_tags": ["O", "O", "O", "O", "B-Year", "B-Genre", "O", "O", "O", "O", "B-Director", "I-Director", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name"]}
{"sentence": "often refereed to as the zombie film without zombies this film starring lawrence fishburne as the director of the cdc is titled what", "tokens": ["often", "refereed", "to", "as", "the", "zombie", "film", "without", "zombies", "this", "film", "starring", "lawrence", "fishburne", "as", "the", "director", "of", "the", "cdc", "is", "titled", "what"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "i m thinking of the modern classic john singleton coming of age film about the crime ridden lives of south central teenagers", "tokens": ["i", "m", "thinking", "of", "the", "modern", "classic", "john", "singleton", "coming", "of", "age", "film", "about", "the", "crime", "ridden", "lives", "of", "south", "central", "teenagers"], "ner_tags": ["O", "O", "O", "O", "O", "B-Genre", "B-Opinion", "B-Actor", "I-Actor", "B-Plot", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what slapstick comedy starred katherine hepburn as a madcap heiress and cary grant as a shy scientist", "tokens": ["what", "slapstick", "comedy", "starred", "katherine", "hepburn", "as", "a", "madcap", "heiress", "and", "cary", "grant", "as", "a", "shy", "scientist"], "ner_tags": ["O", "B-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "O", "B-Actor", "I-Actor", "O", "O", "O", "O"]}
{"sentence": "what 2012 movie depicts learning how to survive big wave riding in santa cruz california", "tokens": ["what", "2012", "movie", "depicts", "learning", "how", "to", "survive", "big", "wave", "riding", "in", "santa", "cruz", "california"], "ner_tags": ["O", "B-Year", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the adult comedy movie that has will ferrell acting as an irresponsible crazy man running for office", "tokens": ["what", "is", "the", "adult", "comedy", "movie", "that", "has", "will", "ferrell", "acting", "as", "an", "irresponsible", "crazy", "man", "running", "for", "office"], "ner_tags": ["O", "O", "O", "B-Genre", "I-Genre", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "a teenage boy with special powers fight to not be eliminated like his friends", "tokens": ["a", "teenage", "boy", "with", "special", "powers", "fight", "to", "not", "be", "eliminated", "like", "his", "friends"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "which disney animated movie had a prince searching for a woman who wore a glass slipper", "tokens": ["which", "disney", "animated", "movie", "had", "a", "prince", "searching", "for", "a", "woman", "who", "wore", "a", "glass", "slipper"], "ner_tags": ["O", "B-Genre", "I-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "robert redford directed ralph fiennes as charles van doren in this 1994 film set in the 1950 s", "tokens": ["robert", "redford", "directed", "ralph", "fiennes", "as", "charles", "van", "doren", "in", "this", "1994", "film", "set", "in", "the", "1950", "s"], "ner_tags": ["B-Director", "I-Director", "O", "B-Actor", "I-Actor", "O", "B-Character_Name", "I-Character_Name", "I-Character_Name", "O", "O", "B-Year", "O", "O", "O", "O", "O", "O"]}
{"sentence": "the second to last movie about a boy wizard and his quest to destroy the most dangerous wizard alive", "tokens": ["the", "second", "to", "last", "movie", "about", "a", "boy", "wizard", "and", "his", "quest", "to", "destroy", "the", "most", "dangerous", "wizard", "alive"], "ner_tags": ["O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i want the name of the disney movie with classical music and dancing hippos that came out in the 50 s", "tokens": ["i", "want", "the", "name", "of", "the", "disney", "movie", "with", "classical", "music", "and", "dancing", "hippos", "that", "came", "out", "in", "the", "50", "s"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Director", "O", "O", "B-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "O", "O", "O", "B-Year", "I-Year"]}
{"sentence": "tattooine is the home planet for a certain hero in this famous trilogy which is set in a distant galaxy", "tokens": ["tattooine", "is", "the", "home", "planet", "for", "a", "certain", "hero", "in", "this", "famous", "trilogy", "which", "is", "set", "in", "a", "distant", "galaxy"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Relationship", "I-Relationship", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what is the 1994 action thriller film starring keanu reeves and sandra bullock that primarily takes place on a runaway bus", "tokens": ["what", "is", "the", "1994", "action", "thriller", "film", "starring", "keanu", "reeves", "and", "sandra", "bullock", "that", "primarily", "takes", "place", "on", "a", "runaway", "bus"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this female driven buddy film stars susan sarandon and geena davis as a pair of women on the run from the law", "tokens": ["this", "female", "driven", "buddy", "film", "stars", "susan", "sarandon", "and", "geena", "davis", "as", "a", "pair", "of", "women", "on", "the", "run", "from", "the", "law"], "ner_tags": ["O", "O", "O", "B-Genre", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 2010 movie starring matt damon as a warrant officer who has to lead men to find supposed weapons of mass destruction", "tokens": ["what", "is", "the", "2010", "movie", "starring", "matt", "damon", "as", "a", "warrant", "officer", "who", "has", "to", "lead", "men", "to", "find", "supposed", "weapons", "of", "mass", "destruction"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "which steven spielberg starring tom hanks and matt damon movie won the academy award for best picture in 1999", "tokens": ["which", "steven", "spielberg", "starring", "tom", "hanks", "and", "matt", "damon", "movie", "won", "the", "academy", "award", "for", "best", "picture", "in", "1999"], "ner_tags": ["O", "B-Director", "I-Director", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Award", "I-Award", "I-Award", "I-Award", "I-Award", "O", "B-Year"]}
{"sentence": "what 1983 mike nichols biopic featured meryl streep in the starring role as a metallurgy worker", "tokens": ["what", "1983", "mike", "nichols", "biopic", "featured", "meryl", "streep", "in", "the", "starring", "role", "as", "a", "metallurgy", "worker"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "B-Genre", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O"]}
{"sentence": "al pacino played the title as a new york blowing the whistle on police corruption in this 1973 sidney lumet film", "tokens": ["al", "pacino", "played", "the", "title", "as", "a", "new", "york", "blowing", "the", "whistle", "on", "police", "corruption", "in", "this", "1973", "sidney", "lumet", "film"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Year", "B-Director", "I-Director", "O"]}
{"sentence": "what film takes place in a mental institution where a brash rebel rallies the patients to take on the oppressive nurse ratched a woman more dictator than nurse", "tokens": ["what", "film", "takes", "place", "in", "a", "mental", "institution", "where", "a", "brash", "rebel", "rallies", "the", "patients", "to", "take", "on", "the", "oppressive", "nurse", "ratched", "a", "woman", "more", "dictator", "than", "nurse"], "ner_tags": ["O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is this stallone classic that places him as a underdog boxer", "tokens": ["what", "is", "this", "stallone", "classic", "that", "places", "him", "as", "a", "underdog", "boxer"], "ner_tags": ["O", "O", "O", "B-Actor", "B-Opinion", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "im thinking of the third installment of the disney ice age series in which the polar caps have begun to melt", "tokens": ["im", "thinking", "of", "the", "third", "installment", "of", "the", "disney", "ice", "age", "series", "in", "which", "the", "polar", "caps", "have", "begun", "to", "melt"], "ner_tags": ["O", "O", "O", "O", "B-Relationship", "I-Relationship", "O", "O", "B-Director", "B-Relationship", "I-Relationship", "I-Relationship", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1988 dystopian anime film directed by katsuhiro otomo features a boy riding around future tokyo on a red motorcycle", "tokens": ["what", "1988", "dystopian", "anime", "film", "directed", "by", "katsuhiro", "otomo", "features", "a", "boy", "riding", "around", "future", "tokyo", "on", "a", "red", "motorcycle"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "O", "O", "O", "B-Director", "I-Director", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this 1990 american romantic fantasy film directed by tim burton features johnny depp in one of his first starring roles", "tokens": ["this", "1990", "american", "romantic", "fantasy", "film", "directed", "by", "tim", "burton", "features", "johnny", "depp", "in", "one", "of", "his", "first", "starring", "roles"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "O", "O", "B-Director", "I-Director", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "this fun animated film features a pet chameleon who finds his courage in the wild west", "tokens": ["this", "fun", "animated", "film", "features", "a", "pet", "chameleon", "who", "finds", "his", "courage", "in", "the", "wild", "west"], "ner_tags": ["O", "O", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "im thinking of the 4 th movie in a series of horror movies that include a man wearing a white mask and using a machine to change his voice", "tokens": ["im", "thinking", "of", "the", "4", "th", "movie", "in", "a", "series", "of", "horror", "movies", "that", "include", "a", "man", "wearing", "a", "white", "mask", "and", "using", "a", "machine", "to", "change", "his", "voice"], "ner_tags": ["O", "O", "O", "O", "B-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "O", "B-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this movie has a cairn terrier named toto in it and also involves a yellow brick road", "tokens": ["this", "movie", "has", "a", "cairn", "terrier", "named", "toto", "in", "it", "and", "also", "involves", "a", "yellow", "brick", "road"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Character_Name", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot"]}
{"sentence": "im trying to think of the 1974 war documentary of the conflicting attitudes of the opponents of the vietnam war that was directed by peter davis", "tokens": ["im", "trying", "to", "think", "of", "the", "1974", "war", "documentary", "of", "the", "conflicting", "attitudes", "of", "the", "opponents", "of", "the", "vietnam", "war", "that", "was", "directed", "by", "peter", "davis"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Year", "B-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "B-Director", "I-Director"]}
{"sentence": "what 1966 british war film is about a german fighter pilot on the western front during world war i", "tokens": ["what", "1966", "british", "war", "film", "is", "about", "a", "german", "fighter", "pilot", "on", "the", "western", "front", "during", "world", "war", "i"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 2011 american superhero film based on the comic book character of the same name with chris hemworth playing the titular hero", "tokens": ["what", "is", "the", "2011", "american", "superhero", "film", "based", "on", "the", "comic", "book", "character", "of", "the", "same", "name", "with", "chris", "hemworth", "playing", "the", "titular", "hero"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "B-Actor", "I-Actor", "O", "O", "O", "O"]}
{"sentence": "classic movie telling the story of dorothy s journey from kansas to a magical land where an evil witch lives", "tokens": ["classic", "movie", "telling", "the", "story", "of", "dorothy", "s", "journey", "from", "kansas", "to", "a", "magical", "land", "where", "an", "evil", "witch", "lives"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Character_Name", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is a 2011 american post apocalyptic dystopia science fiction action film starring paul bettany as the title character", "tokens": ["what", "is", "a", "2011", "american", "post", "apocalyptic", "dystopia", "science", "fiction", "action", "film", "starring", "paul", "bettany", "as", "the", "title", "character"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "I-Genre", "I-Genre", "I-Genre", "I-Genre", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O"]}
{"sentence": "what is the movie that is based off a book from a series of young adult novels that stars daniel radcliffe and is the very last film to be made starring this character", "tokens": ["what", "is", "the", "movie", "that", "is", "based", "off", "a", "book", "from", "a", "series", "of", "young", "adult", "novels", "that", "stars", "daniel", "radcliffe", "and", "is", "the", "very", "last", "film", "to", "be", "made", "starring", "this", "character"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i m thinking about the classic surfing documentary created in 1966 that follows two surfers just trying to look for the perfect wave", "tokens": ["i", "m", "thinking", "about", "the", "classic", "surfing", "documentary", "created", "in", "1966", "that", "follows", "two", "surfers", "just", "trying", "to", "look", "for", "the", "perfect", "wave"], "ner_tags": ["O", "O", "O", "O", "O", "B-Genre", "I-Genre", "I-Genre", "O", "O", "B-Year", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 2011 movie based on marvel s comic book based on the asgardian god of thunder", "tokens": ["what", "is", "the", "2011", "movie", "based", "on", "marvel", "s", "comic", "book", "based", "on", "the", "asgardian", "god", "of", "thunder"], "ner_tags": ["O", "O", "O", "B-Year", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "which movie consists of the actor liam neeson a plane crash and survivors being hunted by wolves", "tokens": ["which", "movie", "consists", "of", "the", "actor", "liam", "neeson", "a", "plane", "crash", "and", "survivors", "being", "hunted", "by", "wolves"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "a funny romantic movie which involves a guy who meets a girl and thinks she is not into him", "tokens": ["a", "funny", "romantic", "movie", "which", "involves", "a", "guy", "who", "meets", "a", "girl", "and", "thinks", "she", "is", "not", "into", "him"], "ner_tags": ["O", "B-Genre", "I-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i m thinking of a tyler perry movie from 2012 where a corporate businessman falls in love with a cleaning lady", "tokens": ["i", "m", "thinking", "of", "a", "tyler", "perry", "movie", "from", "2012", "where", "a", "corporate", "businessman", "falls", "in", "love", "with", "a", "cleaning", "lady"], "ner_tags": ["O", "O", "O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Year", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the reboot of a superhero movie starring james mcavoy and michael fassbender as professor x and magento respectively", "tokens": ["what", "is", "the", "reboot", "of", "a", "superhero", "movie", "starring", "james", "mcavoy", "and", "michael", "fassbender", "as", "professor", "x", "and", "magento", "respectively"], "ner_tags": ["O", "O", "O", "B-Origin", "O", "O", "B-Genre", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Character_Name", "I-Character_Name", "O", "B-Character_Name", "O"]}
{"sentence": "which movie features the trials and tribulations of mark zuckerberg setting up his digital empire", "tokens": ["which", "movie", "features", "the", "trials", "and", "tribulations", "of", "mark", "zuckerberg", "setting", "up", "his", "digital", "empire"], "ner_tags": ["O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "O", "B-Character_Name", "I-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what movie has demons trying to take over a coffee shop it is known for the old lady climbing the wall", "tokens": ["what", "movie", "has", "demons", "trying", "to", "take", "over", "a", "coffee", "shop", "it", "is", "known", "for", "the", "old", "lady", "climbing", "the", "wall"], "ner_tags": ["O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "a movie based on a video game about a young man in modern day iran", "tokens": ["a", "movie", "based", "on", "a", "video", "game", "about", "a", "young", "man", "in", "modern", "day", "iran"], "ner_tags": ["O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is that 1987 comedy movie directed by the coen brothers and starring nicolas cage and holly hunter", "tokens": ["what", "is", "that", "1987", "comedy", "movie", "directed", "by", "the", "coen", "brothers", "and", "starring", "nicolas", "cage", "and", "holly", "hunter"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "O", "O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "which movie won 7 oscars and is about a factory worker who creates a list of his jewish employees in an effort to save them before they are sent to a concentration camp", "tokens": ["which", "movie", "won", "7", "oscars", "and", "is", "about", "a", "factory", "worker", "who", "creates", "a", "list", "of", "his", "jewish", "employees", "in", "an", "effort", "to", "save", "them", "before", "they", "are", "sent", "to", "a", "concentration", "camp"], "ner_tags": ["O", "O", "O", "B-Award", "I-Award", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "glenn ford is tough cop dave bannion who takes on a politically powerful crime syndicate in this 1953 crime drama", "tokens": ["glenn", "ford", "is", "tough", "cop", "dave", "bannion", "who", "takes", "on", "a", "politically", "powerful", "crime", "syndicate", "in", "this", "1953", "crime", "drama"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "B-Character_Name", "I-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Year", "B-Genre", "I-Genre"]}
{"sentence": "two acquaintances decide to take their relationship to the next level minus the emotional baggage", "tokens": ["two", "acquaintances", "decide", "to", "take", "their", "relationship", "to", "the", "next", "level", "minus", "the", "emotional", "baggage"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this 2011 superhero movie stars seth rogen as an unlikely hero who starts a crime fighting career with the help of his father s assistant kato", "tokens": ["this", "2011", "superhero", "movie", "stars", "seth", "rogen", "as", "an", "unlikely", "hero", "who", "starts", "a", "crime", "fighting", "career", "with", "the", "help", "of", "his", "father", "s", "assistant", "kato"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name"]}
{"sentence": "what is the baz luhrman movie that offers a modern day twist on shakespear s classic", "tokens": ["what", "is", "the", "baz", "luhrman", "movie", "that", "offers", "a", "modern", "day", "twist", "on", "shakespear", "s", "classic"], "ner_tags": ["O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "the movie is set during world war ii in spain where a young girl imagines and travels to a fantasy world", "tokens": ["the", "movie", "is", "set", "during", "world", "war", "ii", "in", "spain", "where", "a", "young", "girl", "imagines", "and", "travels", "to", "a", "fantasy", "world"], "ner_tags": ["O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "which epic war movie featuring tom hanks opened with a violent and gritty portrayal of the d day invasion of normandy", "tokens": ["which", "epic", "war", "movie", "featuring", "tom", "hanks", "opened", "with", "a", "violent", "and", "gritty", "portrayal", "of", "the", "d", "day", "invasion", "of", "normandy"], "ner_tags": ["O", "B-Genre", "I-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie where burt reynolds plays an ex football player who lands in jail and then has to organize a football team of convicts to play a game against the guards of the prison", "tokens": ["what", "is", "the", "movie", "where", "burt", "reynolds", "plays", "an", "ex", "football", "player", "who", "lands", "in", "jail", "and", "then", "has", "to", "organize", "a", "football", "team", "of", "convicts", "to", "play", "a", "game", "against", "the", "guards", "of", "the", "prison"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what was that movie starring ryan reynolds as a superhero based on the famous dc comic book series", "tokens": ["what", "was", "that", "movie", "starring", "ryan", "reynolds", "as", "a", "superhero", "based", "on", "the", "famous", "dc", "comic", "book", "series"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin"]}
{"sentence": "what is the name of the movie that has two best friends who also happen to be spies fall in love with the same girl", "tokens": ["what", "is", "the", "name", "of", "the", "movie", "that", "has", "two", "best", "friends", "who", "also", "happen", "to", "be", "spies", "fall", "in", "love", "with", "the", "same", "girl"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this francois truffaut film is recognized as one of the most important of the nouvelle vague movement among french filmmakers and was the first of his films focusing on fictional character antoine doinel", "tokens": ["this", "francois", "truffaut", "film", "is", "recognized", "as", "one", "of", "the", "most", "important", "of", "the", "nouvelle", "vague", "movement", "among", "french", "filmmakers", "and", "was", "the", "first", "of", "his", "films", "focusing", "on", "fictional", "character", "antoine", "doinel"], "ner_tags": ["O", "B-Director", "I-Director", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Genre", "I-Genre", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Character_Name", "I-Character_Name"]}
{"sentence": "what 1931 crime movie tells the tale of a young man who reaches the top of the local criminal element", "tokens": ["what", "1931", "crime", "movie", "tells", "the", "tale", "of", "a", "young", "man", "who", "reaches", "the", "top", "of", "the", "local", "criminal", "element"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 2010 buddy comedy that has an odd couple racing across the country to be in time for the birth of one of the man s baby", "tokens": ["what", "is", "the", "2010", "buddy", "comedy", "that", "has", "an", "odd", "couple", "racing", "across", "the", "country", "to", "be", "in", "time", "for", "the", "birth", "of", "one", "of", "the", "man", "s", "baby"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1962 film based on the famous novel by author harper lee starred gregory peck in the leading role", "tokens": ["what", "1962", "film", "based", "on", "the", "famous", "novel", "by", "author", "harper", "lee", "starred", "gregory", "peck", "in", "the", "leading", "role"], "ner_tags": ["O", "B-Year", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "B-Actor", "I-Actor", "O", "O", "O", "O"]}
{"sentence": "in 2012 what movie about aliens in the suburbs did ben stiller and vince vaughn star in", "tokens": ["in", "2012", "what", "movie", "about", "aliens", "in", "the", "suburbs", "did", "ben", "stiller", "and", "vince", "vaughn", "star", "in"], "ner_tags": ["O", "B-Year", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O"]}
{"sentence": "what is the will ferrell movie in which he is one of santa s little helpers", "tokens": ["what", "is", "the", "will", "ferrell", "movie", "in", "which", "he", "is", "one", "of", "santa", "s", "little", "helpers"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the second movie emma thompson starred in where she played a knock off of mary poppins", "tokens": ["what", "is", "the", "second", "movie", "emma", "thompson", "starred", "in", "where", "she", "played", "a", "knock", "off", "of", "mary", "poppins"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "a 2010 comedy farce involving dopey teens and the characters of the young adult phenomena twilight", "tokens": ["a", "2010", "comedy", "farce", "involving", "dopey", "teens", "and", "the", "characters", "of", "the", "young", "adult", "phenomena", "twilight"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2010 movie featuring diane lane and john malkovich used the tag line the impossible true story", "tokens": ["what", "2010", "movie", "featuring", "diane", "lane", "and", "john", "malkovich", "used", "the", "tag", "line", "the", "impossible", "true", "story"], "ner_tags": ["O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "i m thinking of a 2002 independent romantic comedy that was one of the most successful independent films ever", "tokens": ["i", "m", "thinking", "of", "a", "2002", "independent", "romantic", "comedy", "that", "was", "one", "of", "the", "most", "successful", "independent", "films", "ever"], "ner_tags": ["O", "O", "O", "O", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "aaron taylor johnson stars as an unnoticed high school student and comic book fan who one day decides to become a super hero even though he has no powers training or meaningful reason to do so in this action comedy", "tokens": ["aaron", "taylor", "johnson", "stars", "as", "an", "unnoticed", "high", "school", "student", "and", "comic", "book", "fan", "who", "one", "day", "decides", "to", "become", "a", "super", "hero", "even", "though", "he", "has", "no", "powers", "training", "or", "meaningful", "reason", "to", "do", "so", "in", "this", "action", "comedy"], "ner_tags": ["B-Actor", "I-Actor", "I-Actor", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Genre", "I-Genre"]}
{"sentence": "in which movie does matt damon plays a mathematical genius working as a janitor who gets help from a psychologist to find direction in his life", "tokens": ["in", "which", "movie", "does", "matt", "damon", "plays", "a", "mathematical", "genius", "working", "as", "a", "janitor", "who", "gets", "help", "from", "a", "psychologist", "to", "find", "direction", "in", "his", "life"], "ner_tags": ["O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 2012 movie starring martin freeman in which the protagonist joins a company of dwarves to reclaim their mountain", "tokens": ["what", "is", "the", "2012", "movie", "starring", "martin", "freeman", "in", "which", "the", "protagonist", "joins", "a", "company", "of", "dwarves", "to", "reclaim", "their", "mountain"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i am thinking of a popular movie that starred seven little men the leader named doc", "tokens": ["i", "am", "thinking", "of", "a", "popular", "movie", "that", "starred", "seven", "little", "men", "the", "leader", "named", "doc"], "ner_tags": ["O", "O", "O", "O", "O", "B-Opinion", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is this 1972 iconic american epic crime film about a man who has an offer you ca n t refuse", "tokens": ["what", "is", "this", "1972", "iconic", "american", "epic", "crime", "film", "about", "a", "man", "who", "has", "an", "offer", "you", "ca", "n", "t", "refuse"], "ner_tags": ["O", "O", "O", "B-Year", "O", "B-Genre", "B-Opinion", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of the very famous christmas movie that was released in the 1940 s and remade in the 90 s", "tokens": ["what", "is", "the", "name", "of", "the", "very", "famous", "christmas", "movie", "that", "was", "released", "in", "the", "1940", "s", "and", "remade", "in", "the", "90", "s"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Opinion", "B-Genre", "O", "O", "O", "O", "O", "O", "B-Year", "I-Year", "O", "O", "O", "O", "B-Relationship", "I-Relationship"]}
{"sentence": "what is the movie where robert de niro has a fast paced occupation in nyc", "tokens": ["what", "is", "the", "movie", "where", "robert", "de", "niro", "has", "a", "fast", "paced", "occupation", "in", "nyc"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what category was this movie horror or suspense", "tokens": ["what", "category", "was", "this", "movie", "horror", "or", "suspense"], "ner_tags": ["O", "O", "O", "O", "O", "B-Genre", "O", "B-Genre"]}
{"sentence": "what is the name of the 2010 animated dreamworks movie about a boy who befriends a dragon", "tokens": ["what", "is", "the", "name", "of", "the", "2010", "animated", "dreamworks", "movie", "about", "a", "boy", "who", "befriends", "a", "dragon"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Year", "B-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of the movie set in a world where alter realities are created starring arnold schwarzenegger", "tokens": ["what", "is", "the", "name", "of", "the", "movie", "set", "in", "a", "world", "where", "alter", "realities", "are", "created", "starring", "arnold", "schwarzenegger"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "B-Actor", "I-Actor"]}
{"sentence": "what is the 2010 movie starring johnny depp and angelina jolie that is based in italy", "tokens": ["what", "is", "the", "2010", "movie", "starring", "johnny", "depp", "and", "angelina", "jolie", "that", "is", "based", "in", "italy"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O"]}
{"sentence": "what is this 1980 classic satirical comedy which parodies an airborne disaster and has peter graves as a pilot who gets food poisoning and robert hayes has to take over", "tokens": ["what", "is", "this", "1980", "classic", "satirical", "comedy", "which", "parodies", "an", "airborne", "disaster", "and", "has", "peter", "graves", "as", "a", "pilot", "who", "gets", "food", "poisoning", "and", "robert", "hayes", "has", "to", "take", "over"], "ner_tags": ["O", "O", "O", "B-Year", "B-Opinion", "B-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "an in depth examination of the way that the vietnam war affects the lives of people in a small industrial town in the usa is the plot of this 1978 drama", "tokens": ["an", "in", "depth", "examination", "of", "the", "way", "that", "the", "vietnam", "war", "affects", "the", "lives", "of", "people", "in", "a", "small", "industrial", "town", "in", "the", "usa", "is", "the", "plot", "of", "this", "1978", "drama"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "O", "B-Year", "B-Genre"]}
{"sentence": "what movie was made by wes craven in 2006 in which a family is the target of mutants after their car breaks down in the desert", "tokens": ["what", "movie", "was", "made", "by", "wes", "craven", "in", "2006", "in", "which", "a", "family", "is", "the", "target", "of", "mutants", "after", "their", "car", "breaks", "down", "in", "the", "desert"], "ner_tags": ["O", "O", "O", "O", "O", "B-Director", "I-Director", "O", "B-Year", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2001 peter jackson movie features elijah wood and is based off of the j r r tolkien book of the same name", "tokens": ["what", "2001", "peter", "jackson", "movie", "features", "elijah", "wood", "and", "is", "based", "off", "of", "the", "j", "r", "r", "tolkien", "book", "of", "the", "same", "name"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin"]}
{"sentence": "which classic alien movie had a scene of a boy riding his bicycle across a full moon", "tokens": ["which", "classic", "alien", "movie", "had", "a", "scene", "of", "a", "boy", "riding", "his", "bicycle", "across", "a", "full", "moon"], "ner_tags": ["O", "B-Opinion", "B-Genre", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the larger than life fantasy film featuring heroes and creatures from greek mythology", "tokens": ["what", "is", "the", "larger", "than", "life", "fantasy", "film", "featuring", "heroes", "and", "creatures", "from", "greek", "mythology"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2011 movie directed by craig brewer takes place largely in a town where dancing and rock music have been outlawed", "tokens": ["what", "2011", "movie", "directed", "by", "craig", "brewer", "takes", "place", "largely", "in", "a", "town", "where", "dancing", "and", "rock", "music", "have", "been", "outlawed"], "ner_tags": ["O", "B-Year", "O", "O", "O", "B-Director", "I-Director", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what movie told the story of boxer jake lamotta and his volatile relationship with his wife vickie", "tokens": ["what", "movie", "told", "the", "story", "of", "boxer", "jake", "lamotta", "and", "his", "volatile", "relationship", "with", "his", "wife", "vickie"], "ner_tags": ["O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s that animated film with the ship in the museum based on an old comic book with the blond kid", "tokens": ["what", "s", "that", "animated", "film", "with", "the", "ship", "in", "the", "museum", "based", "on", "an", "old", "comic", "book", "with", "the", "blond", "kid"], "ner_tags": ["O", "O", "O", "B-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin"]}
{"sentence": "what is the old movie starring robin williams about a board game coming to life", "tokens": ["what", "is", "the", "old", "movie", "starring", "robin", "williams", "about", "a", "board", "game", "coming", "to", "life"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is that 1983 al pacino vehicle directed by brian de palma about the rise and fall of a cuban gangster", "tokens": ["what", "is", "that", "1983", "al", "pacino", "vehicle", "directed", "by", "brian", "de", "palma", "about", "the", "rise", "and", "fall", "of", "a", "cuban", "gangster"], "ner_tags": ["O", "O", "O", "B-Year", "B-Actor", "I-Actor", "O", "O", "O", "B-Director", "I-Director", "I-Director", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "2012 comedy starring will ferrel about two men running for office and their arguments with each other", "tokens": ["2012", "comedy", "starring", "will", "ferrel", "about", "two", "men", "running", "for", "office", "and", "their", "arguments", "with", "each", "other"], "ner_tags": ["B-Year", "B-Genre", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 2010 animated film starring the voices of mandy moore and zachary levi", "tokens": ["what", "is", "the", "2010", "animated", "film", "starring", "the", "voices", "of", "mandy", "moore", "and", "zachary", "levi"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "jodelle ferland played a young girl who had been roasted alive over a bonfire in the 2006 film silent hill three years later she also starred in a film in which her parents attempt to roast her in an oven what is the name of this film", "tokens": ["jodelle", "ferland", "played", "a", "young", "girl", "who", "had", "been", "roasted", "alive", "over", "a", "bonfire", "in", "the", "2006", "film", "silent", "hill", "three", "years", "later", "she", "also", "starred", "in", "a", "film", "in", "which", "her", "parents", "attempt", "to", "roast", "her", "in", "an", "oven", "what", "is", "the", "name", "of", "this", "film"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Year", "O", "O", "O", "B-Year", "I-Year", "I-Year", "O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what s the leisurely french comedy which traces the making of a fictional movie and the characters making it", "tokens": ["what", "s", "the", "leisurely", "french", "comedy", "which", "traces", "the", "making", "of", "a", "fictional", "movie", "and", "the", "characters", "making", "it"], "ner_tags": ["O", "O", "O", "B-Opinion", "B-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 1947 movie about a man who claims to be santa claus and is institutionalized as insane and a lawyer defends him in court", "tokens": ["what", "is", "the", "1947", "movie", "about", "a", "man", "who", "claims", "to", "be", "santa", "claus", "and", "is", "institutionalized", "as", "insane", "and", "a", "lawyer", "defends", "him", "in", "court"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in this movie two men who work for the cia fall for the same girl", "tokens": ["in", "this", "movie", "two", "men", "who", "work", "for", "the", "cia", "fall", "for", "the", "same", "girl"], "ner_tags": ["O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "based on the life of anne sullivan s work with helen keller this 1962 film starred anne bancroft patty duke and victor jory", "tokens": ["based", "on", "the", "life", "of", "anne", "sullivan", "s", "work", "with", "helen", "keller", "this", "1962", "film", "starred", "anne", "bancroft", "patty", "duke", "and", "victor", "jory"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Year", "O", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "what is the 2010 movie about an undefeated racehorse and it s run for the triple crown", "tokens": ["what", "is", "the", "2010", "movie", "about", "an", "undefeated", "racehorse", "and", "it", "s", "run", "for", "the", "triple", "crown"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie based on actual events where a ship crashes into an iceberg", "tokens": ["what", "is", "the", "movie", "based", "on", "actual", "events", "where", "a", "ship", "crashes", "into", "an", "iceberg"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2012 tim story directed romantic comedy features four friends who turn the tables on their women", "tokens": ["what", "2012", "tim", "story", "directed", "romantic", "comedy", "features", "four", "friends", "who", "turn", "the", "tables", "on", "their", "women"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "O", "B-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of the movie that is a re telling of the classic story of rapunzel", "tokens": ["what", "is", "the", "name", "of", "the", "movie", "that", "is", "a", "re", "telling", "of", "the", "classic", "story", "of", "rapunzel"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "B-Character_Name"]}
{"sentence": "what s the comedy where sasha baron cohen plays the leader of a small african nation", "tokens": ["what", "s", "the", "comedy", "where", "sasha", "baron", "cohen", "plays", "the", "leader", "of", "a", "small", "african", "nation"], "ner_tags": ["O", "O", "O", "B-Genre", "O", "B-Actor", "I-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this action film features aaaron johnson as a high school student turned vigilante nicholas cage and chloe moretz are also featured as big daddy and hit girl", "tokens": ["this", "action", "film", "features", "aaaron", "johnson", "as", "a", "high", "school", "student", "turned", "vigilante", "nicholas", "cage", "and", "chloe", "moretz", "are", "also", "featured", "as", "big", "daddy", "and", "hit", "girl"], "ner_tags": ["O", "B-Genre", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "B-Character_Name", "I-Character_Name", "O", "B-Character_Name", "I-Character_Name"]}
{"sentence": "what is this film that revolves around adam a nineteen year old who enters the world of male stripping guided by mike lane who has been in the business for six years", "tokens": ["what", "is", "this", "film", "that", "revolves", "around", "adam", "a", "nineteen", "year", "old", "who", "enters", "the", "world", "of", "male", "stripping", "guided", "by", "mike", "lane", "who", "has", "been", "in", "the", "business", "for", "six", "years"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "chaos ensues when a man tries to expose a dark secret regarding a recently deceased patriarch of a dysfunctional british family is the plot of this frank oz directed movie", "tokens": ["chaos", "ensues", "when", "a", "man", "tries", "to", "expose", "a", "dark", "secret", "regarding", "a", "recently", "deceased", "patriarch", "of", "a", "dysfunctional", "british", "family", "is", "the", "plot", "of", "this", "frank", "oz", "directed", "movie"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "O", "B-Director", "I-Director", "O", "O"]}
{"sentence": "jack black is the voice of our cuddly furry hero who wants to master martial arts in this 2008 animated feature", "tokens": ["jack", "black", "is", "the", "voice", "of", "our", "cuddly", "furry", "hero", "who", "wants", "to", "master", "martial", "arts", "in", "this", "2008", "animated", "feature"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Year", "B-Genre", "O"]}
{"sentence": "which 2012 superhero movie stars emma stone as gwen stacy and andrew garfield as peter parker", "tokens": ["which", "2012", "superhero", "movie", "stars", "emma", "stone", "as", "gwen", "stacy", "and", "andrew", "garfield", "as", "peter", "parker"], "ner_tags": ["O", "B-Year", "B-Genre", "B-Plot", "O", "B-Actor", "I-Actor", "O", "B-Character_Name", "I-Character_Name", "O", "B-Actor", "I-Actor", "O", "B-Character_Name", "I-Character_Name"]}
{"sentence": "what is that woody allen directed film starring owen wilson as an american who finds himself transported to paris in the 1920 s", "tokens": ["what", "is", "that", "woody", "allen", "directed", "film", "starring", "owen", "wilson", "as", "an", "american", "who", "finds", "himself", "transported", "to", "paris", "in", "the", "1920", "s"], "ner_tags": ["O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2012 film reunited director martin mcdonagh with the star of his previous film in bruges colin farrell", "tokens": ["what", "2012", "film", "reunited", "director", "martin", "mcdonagh", "with", "the", "star", "of", "his", "previous", "film", "in", "bruges", "colin", "farrell"], "ner_tags": ["O", "B-Year", "O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "O", "B-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "B-Actor", "I-Actor"]}
{"sentence": "bane executes a plan to tear apart gotham city and there is only one person that can stop him", "tokens": ["bane", "executes", "a", "plan", "to", "tear", "apart", "gotham", "city", "and", "there", "is", "only", "one", "person", "that", "can", "stop", "him"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the name of that pixar film where that old man ties countless balloons to his home", "tokens": ["what", "s", "the", "name", "of", "that", "pixar", "film", "where", "that", "old", "man", "ties", "countless", "balloons", "to", "his", "home"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what classic movie drama features kevin costner and james earl jones about building a baseball diamond in the middle of an iowa cornfield", "tokens": ["what", "classic", "movie", "drama", "features", "kevin", "costner", "and", "james", "earl", "jones", "about", "building", "a", "baseball", "diamond", "in", "the", "middle", "of", "an", "iowa", "cornfield"], "ner_tags": ["O", "B-Opinion", "O", "B-Genre", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this movie is a film adaptation of the george abbott broadway musical about a washington senators fan who makes a pact with the devil to help his baseball team win the league pennant", "tokens": ["this", "movie", "is", "a", "film", "adaptation", "of", "the", "george", "abbott", "broadway", "musical", "about", "a", "washington", "senators", "fan", "who", "makes", "a", "pact", "with", "the", "devil", "to", "help", "his", "baseball", "team", "win", "the", "league", "pennant"], "ner_tags": ["O", "O", "O", "B-Relationship", "I-Relationship", "I-Relationship", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie about dorothy and her dog toto going over the rainbow and having to seek help along the yellow brick road", "tokens": ["what", "is", "the", "movie", "about", "dorothy", "and", "her", "dog", "toto", "going", "over", "the", "rainbow", "and", "having", "to", "seek", "help", "along", "the", "yellow", "brick", "road"], "ner_tags": ["O", "O", "O", "O", "O", "B-Character_Name", "O", "O", "O", "B-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this movie based in part on a true story stars aziz ansari and jesse eisenberg", "tokens": ["this", "movie", "based", "in", "part", "on", "a", "true", "story", "stars", "aziz", "ansari", "and", "jesse", "eisenberg"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "this movie is an early 80 s cult favorite starring emilio estevez and harry dean stanton", "tokens": ["this", "movie", "is", "an", "early", "80", "s", "cult", "favorite", "starring", "emilio", "estevez", "and", "harry", "dean", "stanton"], "ner_tags": ["O", "O", "O", "O", "B-Year", "I-Year", "I-Year", "B-Genre", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "I-Actor"]}
{"sentence": "what is that movie with russel brand in which he plays a rich playboy with all the money in the world", "tokens": ["what", "is", "that", "movie", "with", "russel", "brand", "in", "which", "he", "plays", "a", "rich", "playboy", "with", "all", "the", "money", "in", "the", "world"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this movie stars jesse eisenberg as the founder of facebook mark zuckerburg in his college years at harvard", "tokens": ["this", "movie", "stars", "jesse", "eisenberg", "as", "the", "founder", "of", "facebook", "mark", "zuckerburg", "in", "his", "college", "years", "at", "harvard"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2011 horror flick centers around two young sisters who befriend an invisible entity residing in their home", "tokens": ["what", "2011", "horror", "flick", "centers", "around", "two", "young", "sisters", "who", "befriend", "an", "invisible", "entity", "residing", "in", "their", "home"], "ner_tags": ["O", "B-Year", "B-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "remade by william friedkin in 1977 as sorcerer this 1953 thriller depicts a group of men trying to drive truck load of explosives across dangerous terrain", "tokens": ["remade", "by", "william", "friedkin", "in", "1977", "as", "sorcerer", "this", "1953", "thriller", "depicts", "a", "group", "of", "men", "trying", "to", "drive", "truck", "load", "of", "explosives", "across", "dangerous", "terrain"], "ner_tags": ["O", "O", "B-Director", "I-Director", "O", "B-Year", "O", "B-Plot", "O", "B-Year", "B-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 1941 comedy written and directed by preston the running time is 90 minutes", "tokens": ["what", "is", "the", "1941", "comedy", "written", "and", "directed", "by", "preston", "the", "running", "time", "is", "90", "minutes"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "O", "O", "O", "O", "B-Director", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what is this timeless classic that stars tom hanks as a prison guard", "tokens": ["what", "is", "this", "timeless", "classic", "that", "stars", "tom", "hanks", "as", "a", "prison", "guard"], "ner_tags": ["O", "O", "O", "B-Opinion", "I-Opinion", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot"]}
{"sentence": "the charlestown chiefs are a hockey team coached by reggie dunlop played by paul newman in what 1977 film", "tokens": ["the", "charlestown", "chiefs", "are", "a", "hockey", "team", "coached", "by", "reggie", "dunlop", "played", "by", "paul", "newman", "in", "what", "1977", "film"], "ner_tags": ["O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Year", "O"]}
{"sentence": "kevin bacon butts heads with john lithgow in this 1984 movie about a town that does not allow teenagers to listen to rock and roll", "tokens": ["kevin", "bacon", "butts", "heads", "with", "john", "lithgow", "in", "this", "1984", "movie", "about", "a", "town", "that", "does", "not", "allow", "teenagers", "to", "listen", "to", "rock", "and", "roll"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Year", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i am thinking of a classic cowboy or western movie involving an outlaw and his sidekick", "tokens": ["i", "am", "thinking", "of", "a", "classic", "cowboy", "or", "western", "movie", "involving", "an", "outlaw", "and", "his", "sidekick"], "ner_tags": ["O", "O", "O", "O", "O", "B-Genre", "I-Genre", "O", "B-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of the animated movie that is based on the dr seuss book and takes place in whoville", "tokens": ["what", "is", "the", "name", "of", "the", "animated", "movie", "that", "is", "based", "on", "the", "dr", "seuss", "book", "and", "takes", "place", "in", "whoville"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Genre", "I-Genre", "O", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what film released in 3 d is a sequel to a disney classic about a man stuck in a video game", "tokens": ["what", "film", "released", "in", "3", "d", "is", "a", "sequel", "to", "a", "disney", "classic", "about", "a", "man", "stuck", "in", "a", "video", "game"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Relationship", "O", "O", "B-Origin", "I-Origin", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2012 thriller finds a police psychologist trying to talk down an ex con from jumping off a manhattan hotel while a diamond heist is being committed", "tokens": ["what", "2012", "thriller", "finds", "a", "police", "psychologist", "trying", "to", "talk", "down", "an", "ex", "con", "from", "jumping", "off", "a", "manhattan", "hotel", "while", "a", "diamond", "heist", "is", "being", "committed"], "ner_tags": ["O", "B-Year", "B-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2010 romantic comedy starred america ferrera forest whitaker carlos mencia lance gross and regina king", "tokens": ["what", "2010", "romantic", "comedy", "starred", "america", "ferrera", "forest", "whitaker", "carlos", "mencia", "lance", "gross", "and", "regina", "king"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "a man is accidentally transported to 1300 a d where he must battle the dead and retrieve the necronomicon so he can return home is the plot of this 1992 movie", "tokens": ["a", "man", "is", "accidentally", "transported", "to", "1300", "a", "d", "where", "he", "must", "battle", "the", "dead", "and", "retrieve", "the", "necronomicon", "so", "he", "can", "return", "home", "is", "the", "plot", "of", "this", "1992", "movie"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "O", "B-Year", "O"]}
{"sentence": "alan ladd stars as a weary gunfighter who attempts to settle down with a homestead family are disrupted by a smoldering settler rancher conflict", "tokens": ["alan", "ladd", "stars", "as", "a", "weary", "gunfighter", "who", "attempts", "to", "settle", "down", "with", "a", "homestead", "family", "are", "disrupted", "by", "a", "smoldering", "settler", "rancher", "conflict"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 1998 television movie which is an update of the alfred hitchcock 1954 feature film starring christopher reeve", "tokens": ["what", "is", "the", "1998", "television", "movie", "which", "is", "an", "update", "of", "the", "alfred", "hitchcock", "1954", "feature", "film", "starring", "christopher", "reeve"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "B-Actor", "I-Actor"]}
{"sentence": "this is a 1974 american comedy drama film directed by martin scorsese and written by robert getchell", "tokens": ["this", "is", "a", "1974", "american", "comedy", "drama", "film", "directed", "by", "martin", "scorsese", "and", "written", "by", "robert", "getchell"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "I-Genre", "O", "O", "B-Director", "I-Director", "O", "O", "O", "O", "O"]}
{"sentence": "this will ferrell voiced animated film is about an evil supervillian and his quest to become a hero", "tokens": ["this", "will", "ferrell", "voiced", "animated", "film", "is", "about", "an", "evil", "supervillian", "and", "his", "quest", "to", "become", "a", "hero"], "ner_tags": ["O", "B-Actor", "I-Actor", "O", "B-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is a 1921 american silent romance film produced by famous players lasky directed by george melford and starring rudolph valentino agnes ayres and adolphe menjou", "tokens": ["what", "is", "a", "1921", "american", "silent", "romance", "film", "produced", "by", "famous", "players", "lasky", "directed", "by", "george", "melford", "and", "starring", "rudolph", "valentino", "agnes", "ayres", "and", "adolphe", "menjou"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "I-Genre", "O", "O", "O", "O", "O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "in what alfred hitchcock film does cary grant flee from a bi plane that swoops down on him in a field", "tokens": ["in", "what", "alfred", "hitchcock", "film", "does", "cary", "grant", "flee", "from", "a", "bi", "plane", "that", "swoops", "down", "on", "him", "in", "a", "field"], "ner_tags": ["O", "O", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "thinking of a 1939 blockbuster about love and war", "tokens": ["thinking", "of", "a", "1939", "blockbuster", "about", "love", "and", "war"], "ner_tags": ["O", "O", "O", "B-Year", "B-Opinion", "O", "B-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of that movie with kenau reeves where he fell in love with the daughter of a vineyard owner", "tokens": ["what", "is", "the", "name", "of", "that", "movie", "with", "kenau", "reeves", "where", "he", "fell", "in", "love", "with", "the", "daughter", "of", "a", "vineyard", "owner"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "academy award winning film by steven spielberg about a man and his decision to help jews during world war ii", "tokens": ["academy", "award", "winning", "film", "by", "steven", "spielberg", "about", "a", "man", "and", "his", "decision", "to", "help", "jews", "during", "world", "war", "ii"], "ner_tags": ["B-Award", "I-Award", "I-Award", "O", "O", "B-Director", "I-Director", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "gabourey sidibe breaks out in this 2009 movie about an overweight and illiterate black woman living in harlem", "tokens": ["gabourey", "sidibe", "breaks", "out", "in", "this", "2009", "movie", "about", "an", "overweight", "and", "illiterate", "black", "woman", "living", "in", "harlem"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "O", "B-Year", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the tom hanks movie about a hit man whose son catches him in the act", "tokens": ["what", "is", "the", "tom", "hanks", "movie", "about", "a", "hit", "man", "whose", "son", "catches", "him", "in", "the", "act"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 1988 documentary film by errol morris showcasing the story of randall dale adams a man sentenced to prison for a crime he did not commit", "tokens": ["what", "is", "the", "1988", "documentary", "film", "by", "errol", "morris", "showcasing", "the", "story", "of", "randall", "dale", "adams", "a", "man", "sentenced", "to", "prison", "for", "a", "crime", "he", "did", "not", "commit"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "O", "O", "B-Director", "I-Director", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is a 2012 american crime thriller film starring tyler perry as the titular character and matthew fox as villain picasso", "tokens": ["what", "is", "a", "2012", "american", "crime", "thriller", "film", "starring", "tyler", "perry", "as", "the", "titular", "character", "and", "matthew", "fox", "as", "villain", "picasso"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Character_Name"]}
{"sentence": "this movie is the third iteration of a horror movie that involves spirits murdering people at night while they sleep", "tokens": ["this", "movie", "is", "the", "third", "iteration", "of", "a", "horror", "movie", "that", "involves", "spirits", "murdering", "people", "at", "night", "while", "they", "sleep"], "ner_tags": ["O", "O", "O", "O", "B-Relationship", "I-Relationship", "O", "O", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "name the movie that stars johnny depp as an ordinary chameleon who accidentally winds up in the town of dirt a lawless outpost in the wild west in desperate need of a new sheriff", "tokens": ["name", "the", "movie", "that", "stars", "johnny", "depp", "as", "an", "ordinary", "chameleon", "who", "accidentally", "winds", "up", "in", "the", "town", "of", "dirt", "a", "lawless", "outpost", "in", "the", "wild", "west", "in", "desperate", "need", "of", "a", "new", "sheriff"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2010 scifi western directed by jon favreau was based on a 2006 graphic novel", "tokens": ["what", "2010", "scifi", "western", "directed", "by", "jon", "favreau", "was", "based", "on", "a", "2006", "graphic", "novel"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "O", "O", "B-Director", "I-Director", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin"]}
{"sentence": "what 1950 george sidney biopic won the oscar for best music scoring of a musical picture", "tokens": ["what", "1950", "george", "sidney", "biopic", "won", "the", "oscar", "for", "best", "music", "scoring", "of", "a", "musical", "picture"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "B-Genre", "B-Award", "I-Award", "I-Award", "I-Award", "I-Award", "I-Award", "I-Award", "I-Award", "I-Award", "I-Award", "I-Award"]}
{"sentence": "what is the 1955 cold war movie about a nation that declares war on the us", "tokens": ["what", "is", "the", "1955", "cold", "war", "movie", "about", "a", "nation", "that", "declares", "war", "on", "the", "us"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in this epic finale to a famous adventure trilogy frodo finally reaches his dangerous destination", "tokens": ["in", "this", "epic", "finale", "to", "a", "famous", "adventure", "trilogy", "frodo", "finally", "reaches", "his", "dangerous", "destination"], "ner_tags": ["O", "O", "B-Opinion", "B-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "B-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in this movie kate winslet and leonardo dicaprio two people from a completely different world fall in love aboard a ship before it runs into an iceberg", "tokens": ["in", "this", "movie", "kate", "winslet", "and", "leonardo", "dicaprio", "two", "people", "from", "a", "completely", "different", "world", "fall", "in", "love", "aboard", "a", "ship", "before", "it", "runs", "into", "an", "iceberg"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i am thinking of a sci fi film that pairs characters from the old west with hostile extra terrestrial beings", "tokens": ["i", "am", "thinking", "of", "a", "sci", "fi", "film", "that", "pairs", "characters", "from", "the", "old", "west", "with", "hostile", "extra", "terrestrial", "beings"], "ner_tags": ["O", "O", "O", "O", "O", "B-Genre", "I-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1951 adventure film starred robert morley and katharine hepburn as samuel and rose saye", "tokens": ["what", "1951", "adventure", "film", "starred", "robert", "morley", "and", "katharine", "hepburn", "as", "samuel", "and", "rose", "saye"], "ner_tags": ["O", "B-Year", "B-Genre", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Character_Name", "O", "B-Character_Name", "I-Character_Name"]}
{"sentence": "based on the tennessee williams play this story of a fragile woman named blanche dubois who moves in with her sister and brother in law and watches reality disappear", "tokens": ["based", "on", "the", "tennessee", "williams", "play", "this", "story", "of", "a", "fragile", "woman", "named", "blanche", "dubois", "who", "moves", "in", "with", "her", "sister", "and", "brother", "in", "law", "and", "watches", "reality", "disappear"], "ner_tags": ["B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "which movie starring brad pitt and edward norton contains the first rule is to not talk about it", "tokens": ["which", "movie", "starring", "brad", "pitt", "and", "edward", "norton", "contains", "the", "first", "rule", "is", "to", "not", "talk", "about", "it"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote"]}
{"sentence": "what is the woody allen movie that he stars in and has this relationship in new york", "tokens": ["what", "is", "the", "woody", "allen", "movie", "that", "he", "stars", "in", "and", "has", "this", "relationship", "in", "new", "york"], "ner_tags": ["O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "which movie stars dorothy as its main character and also features the yellow brick road", "tokens": ["which", "movie", "stars", "dorothy", "as", "its", "main", "character", "and", "also", "features", "the", "yellow", "brick", "road"], "ner_tags": ["O", "O", "O", "B-Character_Name", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this baseball classic follows lou gehrig as he stoically finishes his baseball career while succumbing to his namesake disease", "tokens": ["this", "baseball", "classic", "follows", "lou", "gehrig", "as", "he", "stoically", "finishes", "his", "baseball", "career", "while", "succumbing", "to", "his", "namesake", "disease"], "ner_tags": ["O", "B-Genre", "B-Opinion", "O", "B-Character_Name", "I-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "bilbo baggins gandalf the gray and thorin company travel across the land to defeat the dragon smaug in 2012 film by peter jackson", "tokens": ["bilbo", "baggins", "gandalf", "the", "gray", "and", "thorin", "company", "travel", "across", "the", "land", "to", "defeat", "the", "dragon", "smaug", "in", "2012", "film", "by", "peter", "jackson"], "ner_tags": ["B-Character_Name", "I-Character_Name", "I-Character_Name", "I-Character_Name", "I-Character_Name", "O", "B-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "O", "B-Year", "O", "O", "B-Director", "I-Director"]}
{"sentence": "i m thinking of the al pacino movie that ends with him saying say hello to my little friend with a cuban accent while holding a big gun", "tokens": ["i", "m", "thinking", "of", "the", "al", "pacino", "movie", "that", "ends", "with", "him", "saying", "say", "hello", "to", "my", "little", "friend", "with", "a", "cuban", "accent", "while", "holding", "a", "big", "gun"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "steve mcqueen stars in this pete yates directed 1968 classic american fast paced thriller movie", "tokens": ["steve", "mcqueen", "stars", "in", "this", "pete", "yates", "directed", "1968", "classic", "american", "fast", "paced", "thriller", "movie"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "B-Director", "I-Director", "O", "B-Year", "B-Opinion", "B-Genre", "B-Opinion", "I-Opinion", "B-Genre", "O"]}
{"sentence": "what s the movie where a killer is supposed to kill his future self but he does n t", "tokens": ["what", "s", "the", "movie", "where", "a", "killer", "is", "supposed", "to", "kill", "his", "future", "self", "but", "he", "does", "n", "t"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of the movie where janet jackson plays a sort of marriage counselor", "tokens": ["what", "is", "the", "name", "of", "the", "movie", "where", "janet", "jackson", "plays", "a", "sort", "of", "marriage", "counselor"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what movie features the iconic shower scene where the main character is killed off early in the movie", "tokens": ["what", "movie", "features", "the", "iconic", "shower", "scene", "where", "the", "main", "character", "is", "killed", "off", "early", "in", "the", "movie"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O"]}
{"sentence": "what drama film is about a professional football player for the chicago bear who is diagnosed with cancer", "tokens": ["what", "drama", "film", "is", "about", "a", "professional", "football", "player", "for", "the", "chicago", "bear", "who", "is", "diagnosed", "with", "cancer"], "ner_tags": ["O", "B-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 1999 thriller directed by roman polansky that stars johnny depp as a book dealer", "tokens": ["what", "is", "the", "1999", "thriller", "directed", "by", "roman", "polansky", "that", "stars", "johnny", "depp", "as", "a", "book", "dealer"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "O", "O", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot"]}
{"sentence": "which mel brooks comedy parodies star wars and stars john candy and bill pullman as intergalactic heroes", "tokens": ["which", "mel", "brooks", "comedy", "parodies", "star", "wars", "and", "stars", "john", "candy", "and", "bill", "pullman", "as", "intergalactic", "heroes"], "ner_tags": ["O", "B-Director", "I-Director", "B-Genre", "B-Plot", "I-Plot", "I-Plot", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot"]}
{"sentence": "what is that 2011 drama film ensemble film depicting the interconnected lives of nine women and the struggles that they face as women of color", "tokens": ["what", "is", "that", "2011", "drama", "film", "ensemble", "film", "depicting", "the", "interconnected", "lives", "of", "nine", "women", "and", "the", "struggles", "that", "they", "face", "as", "women", "of", "color"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "O", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this is the fourth film in a franchise starring johnny depp as captain jack sparrow trying to navigate the seas during his encounters with the supernatural", "tokens": ["this", "is", "the", "fourth", "film", "in", "a", "franchise", "starring", "johnny", "depp", "as", "captain", "jack", "sparrow", "trying", "to", "navigate", "the", "seas", "during", "his", "encounters", "with", "the", "supernatural"], "ner_tags": ["O", "O", "O", "B-Relationship", "I-Relationship", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Character_Name", "I-Character_Name", "I-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this hong kong arthouse hit from director wong kar wai is n t about two lovers cheating on their spouses it s about their spouses", "tokens": ["this", "hong", "kong", "arthouse", "hit", "from", "director", "wong", "kar", "wai", "is", "n", "t", "about", "two", "lovers", "cheating", "on", "their", "spouses", "it", "s", "about", "their", "spouses"], "ner_tags": ["O", "B-Genre", "I-Genre", "I-Genre", "O", "O", "O", "B-Director", "I-Director", "I-Director", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this movie features a group of young adults that take a tour of nuclear wasteland but they are in for a dramatic turn for the worst", "tokens": ["this", "movie", "features", "a", "group", "of", "young", "adults", "that", "take", "a", "tour", "of", "nuclear", "wasteland", "but", "they", "are", "in", "for", "a", "dramatic", "turn", "for", "the", "worst"], "ner_tags": ["O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "do you know the nicholas sparks story that was turned into a film starring miley cyrus and her fiance liam hemsworth", "tokens": ["do", "you", "know", "the", "nicholas", "sparks", "story", "that", "was", "turned", "into", "a", "film", "starring", "miley", "cyrus", "and", "her", "fiance", "liam", "hemsworth"], "ner_tags": ["O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Actor", "I-Actor"]}
{"sentence": "a young witch on her mandatory year of independent life finds fitting into a new community difficult while she supports herself by running an air courier service is the plot of this japanese animated family movie", "tokens": ["a", "young", "witch", "on", "her", "mandatory", "year", "of", "independent", "life", "finds", "fitting", "into", "a", "new", "community", "difficult", "while", "she", "supports", "herself", "by", "running", "an", "air", "courier", "service", "is", "the", "plot", "of", "this", "japanese", "animated", "family", "movie"], "ner_tags": ["O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "O", "B-Genre", "I-Genre", "I-Genre", "I-Genre"]}
{"sentence": "can you remember the 1990 film about a mischievous youth forgotten at home during christmas defending his home from burglars", "tokens": ["can", "you", "remember", "the", "1990", "film", "about", "a", "mischievous", "youth", "forgotten", "at", "home", "during", "christmas", "defending", "his", "home", "from", "burglars"], "ner_tags": ["O", "O", "O", "O", "B-Year", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i m thinking of an 80 s film starring nicholas cage and holly hunter as kidnappers who steal a baby", "tokens": ["i", "m", "thinking", "of", "an", "80", "s", "film", "starring", "nicholas", "cage", "and", "holly", "hunter", "as", "kidnappers", "who", "steal", "a", "baby"], "ner_tags": ["O", "O", "O", "O", "O", "B-Year", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "O", "B-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie in which marlon brando won a best actor oscar for talking about being a contender", "tokens": ["what", "is", "the", "movie", "in", "which", "marlon", "brando", "won", "a", "best", "actor", "oscar", "for", "talking", "about", "being", "a", "contender"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Award", "I-Award", "I-Award", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie about a cuban refugee who establishes himself as chief of a drug cartel in miami", "tokens": ["what", "is", "the", "movie", "about", "a", "cuban", "refugee", "who", "establishes", "himself", "as", "chief", "of", "a", "drug", "cartel", "in", "miami"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "tom hanks stars as an all female baseball team s manager in this 1992 penny marshall directed movie", "tokens": ["tom", "hanks", "stars", "as", "an", "all", "female", "baseball", "team", "s", "manager", "in", "this", "1992", "penny", "marshall", "directed", "movie"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Year", "B-Director", "I-Director", "O", "O"]}
{"sentence": "this 2011 comedy starring kristen wiig follows the disasters surrounding the wedding of the main characters best friend", "tokens": ["this", "2011", "comedy", "starring", "kristen", "wiig", "follows", "the", "disasters", "surrounding", "the", "wedding", "of", "the", "main", "characters", "best", "friend"], "ner_tags": ["O", "B-Year", "B-Genre", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2011 documentary directed by jon chu followed a canadian pop star and included footage from his 2010 concert tour", "tokens": ["what", "2011", "documentary", "directed", "by", "jon", "chu", "followed", "a", "canadian", "pop", "star", "and", "included", "footage", "from", "his", "2010", "concert", "tour"], "ner_tags": ["O", "B-Year", "B-Genre", "O", "O", "B-Director", "I-Director", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the funny horror movie that features little flesh eating fish as the main antagonists", "tokens": ["what", "s", "the", "funny", "horror", "movie", "that", "features", "little", "flesh", "eating", "fish", "as", "the", "main", "antagonists"], "ner_tags": ["O", "O", "O", "B-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this film takes place in paris and revolves around a man s delema to get a crime ring s head honcho out of the hospital and save his wife from these criminals", "tokens": ["this", "film", "takes", "place", "in", "paris", "and", "revolves", "around", "a", "man", "s", "delema", "to", "get", "a", "crime", "ring", "s", "head", "honcho", "out", "of", "the", "hospital", "and", "save", "his", "wife", "from", "these", "criminals"], "ner_tags": ["O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 2010 movie about a female charged in the assassination trial of abe lincoln", "tokens": ["what", "is", "the", "2010", "movie", "about", "a", "female", "charged", "in", "the", "assassination", "trial", "of", "abe", "lincoln"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what animated film features only music written by elton john as well as garden sculptures", "tokens": ["what", "animated", "film", "features", "only", "music", "written", "by", "elton", "john", "as", "well", "as", "garden", "sculptures"], "ner_tags": ["O", "B-Genre", "O", "O", "O", "B-Soundtrack", "I-Soundtrack", "I-Soundtrack", "I-Soundtrack", "I-Soundtrack", "O", "O", "O", "B-Plot", "I-Plot"]}
{"sentence": "ray liotta robert de niro and joe pesci star in this film about gangsters in organized crime", "tokens": ["ray", "liotta", "robert", "de", "niro", "and", "joe", "pesci", "star", "in", "this", "film", "about", "gangsters", "in", "organized", "crime"], "ner_tags": ["B-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i m searching for the movie about the end of the world when no one can have children anymore and then a woman gets pregnant", "tokens": ["i", "m", "searching", "for", "the", "movie", "about", "the", "end", "of", "the", "world", "when", "no", "one", "can", "have", "children", "anymore", "and", "then", "a", "woman", "gets", "pregnant"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this 2000 foreign film won over 40 and became the highest grossing foreign language film in american history", "tokens": ["this", "2000", "foreign", "film", "won", "over", "40", "and", "became", "the", "highest", "grossing", "foreign", "language", "film", "in", "american", "history"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "B-Award", "I-Award", "I-Award", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "i want to know a famously parodied movie at the end of 1992 s wayne s world", "tokens": ["i", "want", "to", "know", "a", "famously", "parodied", "movie", "at", "the", "end", "of", "1992", "s", "wayne", "s", "world"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Genre", "O", "O", "O", "O", "O", "B-Year", "O", "O", "O", "O"]}
{"sentence": "what classic black and white horror movie and cult classic is often considered to be the father of zombie movies", "tokens": ["what", "classic", "black", "and", "white", "horror", "movie", "and", "cult", "classic", "is", "often", "considered", "to", "be", "the", "father", "of", "zombie", "movies"], "ner_tags": ["O", "B-Genre", "I-Genre", "I-Genre", "I-Genre", "I-Genre", "O", "O", "B-Genre", "I-Genre", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what is the disney movie that involves a lion cub and his ascension to kingship", "tokens": ["what", "is", "the", "disney", "movie", "that", "involves", "a", "lion", "cub", "and", "his", "ascension", "to", "kingship"], "ner_tags": ["O", "O", "O", "B-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is that 2010 british historical film about a king that has a debilitating stutter and shows the process by which he tries to overcome it", "tokens": ["what", "is", "that", "2010", "british", "historical", "film", "about", "a", "king", "that", "has", "a", "debilitating", "stutter", "and", "shows", "the", "process", "by", "which", "he", "tries", "to", "overcome", "it"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 2011 masterpiece directed by john lesster featuring a race car and his race", "tokens": ["what", "is", "the", "2011", "masterpiece", "directed", "by", "john", "lesster", "featuring", "a", "race", "car", "and", "his", "race"], "ner_tags": ["O", "O", "O", "B-Year", "B-Opinion", "O", "O", "B-Director", "I-Director", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this movie is based off a novella by h g wells published in 1897 in this novella made movie a man falls victim to his own experiment involving optics when he ca n t reverse the damage he goes on a reign of terror", "tokens": ["this", "movie", "is", "based", "off", "a", "novella", "by", "h", "g", "wells", "published", "in", "1897", "in", "this", "novella", "made", "movie", "a", "man", "falls", "victim", "to", "his", "own", "experiment", "involving", "optics", "when", "he", "ca", "n", "t", "reverse", "the", "damage", "he", "goes", "on", "a", "reign", "of", "terror"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i m thinking of a movie that is the first in a trilogy about a small creature from middle earth", "tokens": ["i", "m", "thinking", "of", "a", "movie", "that", "is", "the", "first", "in", "a", "trilogy", "about", "a", "small", "creature", "from", "middle", "earth"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the movie where two detectives are searching for a serial killer and it ends with a tragic surprise in a box", "tokens": ["what", "s", "the", "movie", "where", "two", "detectives", "are", "searching", "for", "a", "serial", "killer", "and", "it", "ends", "with", "a", "tragic", "surprise", "in", "a", "box"], "ner_tags": ["O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this movie made spinning heads pea soup vomit and linda blair common household conversation starters", "tokens": ["this", "movie", "made", "spinning", "heads", "pea", "soup", "vomit", "and", "linda", "blair", "common", "household", "conversation", "starters"], "ner_tags": ["O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "B-Actor", "I-Actor", "O", "O", "O", "O"]}
{"sentence": "in which film does a young car thief kill a policeman and try to persuade a girl to hide in italy with him", "tokens": ["in", "which", "film", "does", "a", "young", "car", "thief", "kill", "a", "policeman", "and", "try", "to", "persuade", "a", "girl", "to", "hide", "in", "italy", "with", "him"], "ner_tags": ["O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this 1979 film featuring dennis christopher centers around a teenager obsessed with the italian cycling team", "tokens": ["this", "1979", "film", "featuring", "dennis", "christopher", "centers", "around", "a", "teenager", "obsessed", "with", "the", "italian", "cycling", "team"], "ner_tags": ["O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this action packed sequel stars arnold schwarzenegger as a robot sent back in time this time to protect young john connor", "tokens": ["this", "action", "packed", "sequel", "stars", "arnold", "schwarzenegger", "as", "a", "robot", "sent", "back", "in", "time", "this", "time", "to", "protect", "young", "john", "connor"], "ner_tags": ["O", "B-Genre", "O", "B-Relationship", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name"]}
{"sentence": "what is the name of stanley kubric s 1968 epic sci fi classic", "tokens": ["what", "is", "the", "name", "of", "stanley", "kubric", "s", "1968", "epic", "sci", "fi", "classic"], "ner_tags": ["O", "O", "O", "O", "O", "B-Director", "I-Director", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "I-Genre"]}
{"sentence": "a movie that came out in 1998 about a man and his frustrating work life starring jennifer aniston", "tokens": ["a", "movie", "that", "came", "out", "in", "1998", "about", "a", "man", "and", "his", "frustrating", "work", "life", "starring", "jennifer", "aniston"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Year", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "B-Actor", "I-Actor"]}
{"sentence": "what s the movie an oscar winner about a world leader who has an embarrassing impediment", "tokens": ["what", "s", "the", "movie", "an", "oscar", "winner", "about", "a", "world", "leader", "who", "has", "an", "embarrassing", "impediment"], "ner_tags": ["O", "O", "O", "O", "O", "B-Award", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i m thinking of a movie where a robot goes back in time to save a child in the present from an evil computer", "tokens": ["i", "m", "thinking", "of", "a", "movie", "where", "a", "robot", "goes", "back", "in", "time", "to", "save", "a", "child", "in", "the", "present", "from", "an", "evil", "computer"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the adventure film about four children that go through a wardrobe to a magical land", "tokens": ["what", "is", "the", "adventure", "film", "about", "four", "children", "that", "go", "through", "a", "wardrobe", "to", "a", "magical", "land"], "ner_tags": ["O", "O", "O", "B-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i m thinking of a is a 1995 american crime drama film starring susan sarandon sean penn and directed by tim robbins", "tokens": ["i", "m", "thinking", "of", "a", "is", "a", "1995", "american", "crime", "drama", "film", "starring", "susan", "sarandon", "sean", "penn", "and", "directed", "by", "tim", "robbins"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "O", "O", "B-Director", "I-Director"]}
{"sentence": "what christmas movie did a young boy wish for a lever action red ryder bb gun", "tokens": ["what", "christmas", "movie", "did", "a", "young", "boy", "wish", "for", "a", "lever", "action", "red", "ryder", "bb", "gun"], "ner_tags": ["O", "B-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "name mike judge s cult classic about a group of software writers who decide to try to take money out of their company s bank account", "tokens": ["name", "mike", "judge", "s", "cult", "classic", "about", "a", "group", "of", "software", "writers", "who", "decide", "to", "try", "to", "take", "money", "out", "of", "their", "company", "s", "bank", "account"], "ner_tags": ["O", "B-Director", "I-Director", "O", "O", "B-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what recent sci fi epic features halle berry that was a working progress for tom hanks for over 10 years", "tokens": ["what", "recent", "sci", "fi", "epic", "features", "halle", "berry", "that", "was", "a", "working", "progress", "for", "tom", "hanks", "for", "over", "10", "years"], "ner_tags": ["O", "O", "B-Genre", "I-Genre", "B-Opinion", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O"]}
{"sentence": "i m thinking of the movie that had a little girl superhero named hit girl and nicholas cage", "tokens": ["i", "m", "thinking", "of", "the", "movie", "that", "had", "a", "little", "girl", "superhero", "named", "hit", "girl", "and", "nicholas", "cage"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "B-Character_Name", "I-Character_Name", "O", "B-Actor", "I-Actor"]}
{"sentence": "this movie introduces the concepts of xenomorphs and facehuggers in a scary corporate dystopian future", "tokens": ["this", "movie", "introduces", "the", "concepts", "of", "xenomorphs", "and", "facehuggers", "in", "a", "scary", "corporate", "dystopian", "future"], "ner_tags": ["O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Genre", "O", "O", "O"]}
{"sentence": "what 1939 frank capra drama stars james stewart jean arthur claude rains edward arnold and thomas mitchell", "tokens": ["what", "1939", "frank", "capra", "drama", "stars", "james", "stewart", "jean", "arthur", "claude", "rains", "edward", "arnold", "and", "thomas", "mitchell"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "B-Genre", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "what s the movie about a guy and his crew who go down the amazon in search of the lost city of gold", "tokens": ["what", "s", "the", "movie", "about", "a", "guy", "and", "his", "crew", "who", "go", "down", "the", "amazon", "in", "search", "of", "the", "lost", "city", "of", "gold"], "ner_tags": ["O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of that old movie directed by martin scorsese and starring robert de niro which is about a boxer", "tokens": ["what", "is", "the", "name", "of", "that", "old", "movie", "directed", "by", "martin", "scorsese", "and", "starring", "robert", "de", "niro", "which", "is", "about", "a", "boxer"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what movie stars michael j fox christopher lloyd a time traveling car and a hovering skateboard", "tokens": ["what", "movie", "stars", "michael", "j", "fox", "christopher", "lloyd", "a", "time", "traveling", "car", "and", "a", "hovering", "skateboard"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the animated film that is loosely based on the story of romeo and juliet", "tokens": ["what", "is", "the", "animated", "film", "that", "is", "loosely", "based", "on", "the", "story", "of", "romeo", "and", "juliet"], "ner_tags": ["O", "O", "O", "B-Genre", "I-Genre", "O", "O", "B-Opinion", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Origin", "I-Origin", "I-Origin"]}
{"sentence": "a movie with tom hardy who is a boxer and must fight his brother at the end also stars nick nolte", "tokens": ["a", "movie", "with", "tom", "hardy", "who", "is", "a", "boxer", "and", "must", "fight", "his", "brother", "at", "the", "end", "also", "stars", "nick", "nolte"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Actor", "I-Actor"]}
{"sentence": "what was the movie that starred johnny depp as a animated chameleon living in the fictional town of dirt", "tokens": ["what", "was", "the", "movie", "that", "starred", "johnny", "depp", "as", "a", "animated", "chameleon", "living", "in", "the", "fictional", "town", "of", "dirt"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "name the film where apes try to take over the world and become extremely smart", "tokens": ["name", "the", "film", "where", "apes", "try", "to", "take", "over", "the", "world", "and", "become", "extremely", "smart"], "ner_tags": ["O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in this movie about the wedding of two successful black professionals who was the bride s real mother", "tokens": ["in", "this", "movie", "about", "the", "wedding", "of", "two", "successful", "black", "professionals", "who", "was", "the", "bride", "s", "real", "mother"], "ner_tags": ["O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in this 2010 movie a young boy who is being bullied befriends his next door neighbor who is a vampire", "tokens": ["in", "this", "2010", "movie", "a", "young", "boy", "who", "is", "being", "bullied", "befriends", "his", "next", "door", "neighbor", "who", "is", "a", "vampire"], "ner_tags": ["O", "O", "B-Year", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "the sequel to the movie that scared the world on a low budget a demon haunts the house where a family with a heavy past just moved in", "tokens": ["the", "sequel", "to", "the", "movie", "that", "scared", "the", "world", "on", "a", "low", "budget", "a", "demon", "haunts", "the", "house", "where", "a", "family", "with", "a", "heavy", "past", "just", "moved", "in"], "ner_tags": ["O", "B-Relationship", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this is the first part of the epic conclusion to an 8 part movie series about a boy wizard fighting the forces of evil", "tokens": ["this", "is", "the", "first", "part", "of", "the", "epic", "conclusion", "to", "an", "8", "part", "movie", "series", "about", "a", "boy", "wizard", "fighting", "the", "forces", "of", "evil"], "ner_tags": ["O", "O", "O", "B-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "a giant gorilla terrorizes the town of manhattan and crawls on the empire state building", "tokens": ["a", "giant", "gorilla", "terrorizes", "the", "town", "of", "manhattan", "and", "crawls", "on", "the", "empire", "state", "building"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what quirky comedy set in idaho featured a nerd his brother and his immigrant friend pedro", "tokens": ["what", "quirky", "comedy", "set", "in", "idaho", "featured", "a", "nerd", "his", "brother", "and", "his", "immigrant", "friend", "pedro"], "ner_tags": ["O", "B-Opinion", "B-Genre", "O", "B-Plot", "I-Plot", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name"]}
{"sentence": "movie about an average worker who gets fed up by the mundane office life and slowly plots his revenge against the two bobs", "tokens": ["movie", "about", "an", "average", "worker", "who", "gets", "fed", "up", "by", "the", "mundane", "office", "life", "and", "slowly", "plots", "his", "revenge", "against", "the", "two", "bobs"], "ner_tags": ["O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Character_Name", "I-Character_Name"]}
{"sentence": "what marvel movie s title is the same as the character who wields a mighty hammer", "tokens": ["what", "marvel", "movie", "s", "title", "is", "the", "same", "as", "the", "character", "who", "wields", "a", "mighty", "hammer"], "ner_tags": ["O", "B-Director", "I-Director", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what movie starred william dafoe charlie sheen and johnny depp", "tokens": ["what", "movie", "starred", "william", "dafoe", "charlie", "sheen", "and", "johnny", "depp"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "the movie i m thinking of has two god brothers and one of them gets cast out of their kingdom and sent to earth to live with humans", "tokens": ["the", "movie", "i", "m", "thinking", "of", "has", "two", "god", "brothers", "and", "one", "of", "them", "gets", "cast", "out", "of", "their", "kingdom", "and", "sent", "to", "earth", "to", "live", "with", "humans"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s that old chick flick all girls seem to love with audrey hepburn wearing some big black glasses hat and dress", "tokens": ["what", "s", "that", "old", "chick", "flick", "all", "girls", "seem", "to", "love", "with", "audrey", "hepburn", "wearing", "some", "big", "black", "glasses", "hat", "and", "dress"], "ner_tags": ["O", "O", "O", "O", "B-Genre", "I-Genre", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what is the woody allen movie from the 70 s where he runs into a bunch of cars", "tokens": ["what", "is", "the", "woody", "allen", "movie", "from", "the", "70", "s", "where", "he", "runs", "into", "a", "bunch", "of", "cars"], "ner_tags": ["O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "B-Year", "I-Year", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this movie is about a lovelorn english woman and her attempt to find true love", "tokens": ["this", "movie", "is", "about", "a", "lovelorn", "english", "woman", "and", "her", "attempt", "to", "find", "true", "love"], "ner_tags": ["O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what was the plot in this movie", "tokens": ["what", "was", "the", "plot", "in", "this", "movie"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what is a 1964 musical film adaptation of the lerner and loewe stage musical of the same name based on the 1938 film adaptation of the original stage play pygmalion by george bernard shaw", "tokens": ["what", "is", "a", "1964", "musical", "film", "adaptation", "of", "the", "lerner", "and", "loewe", "stage", "musical", "of", "the", "same", "name", "based", "on", "the", "1938", "film", "adaptation", "of", "the", "original", "stage", "play", "pygmalion", "by", "george", "bernard", "shaw"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Plot", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin"]}
{"sentence": "which film is based off a charles dickens classic and features the character the ghost of christmas past", "tokens": ["which", "film", "is", "based", "off", "a", "charles", "dickens", "classic", "and", "features", "the", "character", "the", "ghost", "of", "christmas", "past"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Origin", "I-Origin", "B-Genre", "O", "O", "O", "O", "B-Plot", "B-Character_Name", "I-Character_Name", "I-Character_Name", "I-Character_Name"]}
{"sentence": "a 2012 movie where a convicted man must save the presidents daughter from a outer space prison to win his freedom", "tokens": ["a", "2012", "movie", "where", "a", "convicted", "man", "must", "save", "the", "presidents", "daughter", "from", "a", "outer", "space", "prison", "to", "win", "his", "freedom"], "ner_tags": ["O", "B-Year", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this movie was a book originally by charles dickens the main character pip becomes a gentleman from the rich ms havasham", "tokens": ["this", "movie", "was", "a", "book", "originally", "by", "charles", "dickens", "the", "main", "character", "pip", "becomes", "a", "gentleman", "from", "the", "rich", "ms", "havasham"], "ner_tags": ["O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "O", "O", "B-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name"]}
{"sentence": "russell brand filmed scenes performing as rock star aldous snow at his sell out comedy show scandalous in front of 20 000 people at the o 2 arena in london for this 2010 comedy", "tokens": ["russell", "brand", "filmed", "scenes", "performing", "as", "rock", "star", "aldous", "snow", "at", "his", "sell", "out", "comedy", "show", "scandalous", "in", "front", "of", "20", "000", "people", "at", "the", "o", "2", "arena", "in", "london", "for", "this", "2010", "comedy"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Year", "B-Genre"]}
{"sentence": "in what movie did faye dunaway bear her father s child making that child both her daughter and her sister", "tokens": ["in", "what", "movie", "did", "faye", "dunaway", "bear", "her", "father", "s", "child", "making", "that", "child", "both", "her", "daughter", "and", "her", "sister"], "ner_tags": ["O", "O", "O", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the alfred hitchcock movie about a man who dresses up like his dead mother", "tokens": ["what", "is", "the", "alfred", "hitchcock", "movie", "about", "a", "man", "who", "dresses", "up", "like", "his", "dead", "mother"], "ner_tags": ["O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is that comedy film starring jon heder in a breakout role as an awkward teen and details his adventures in high school", "tokens": ["what", "is", "that", "comedy", "film", "starring", "jon", "heder", "in", "a", "breakout", "role", "as", "an", "awkward", "teen", "and", "details", "his", "adventures", "in", "high", "school"], "ner_tags": ["O", "O", "O", "B-Genre", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Opinion", "I-Opinion", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "ned beatty was nominated for an oscar for his supporting role in what news show drama", "tokens": ["ned", "beatty", "was", "nominated", "for", "an", "oscar", "for", "his", "supporting", "role", "in", "what", "news", "show", "drama"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "O", "B-Award", "O", "O", "O", "O", "O", "O", "O", "O", "B-Genre"]}
{"sentence": "what comedy drama was inspired by a true story about a man who found out he has cancer", "tokens": ["what", "comedy", "drama", "was", "inspired", "by", "a", "true", "story", "about", "a", "man", "who", "found", "out", "he", "has", "cancer"], "ner_tags": ["O", "B-Genre", "I-Genre", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of the japanese animation movie made by studio ghibli and includes a royal figure in it", "tokens": ["what", "is", "the", "name", "of", "the", "japanese", "animation", "movie", "made", "by", "studio", "ghibli", "and", "includes", "a", "royal", "figure", "in", "it"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Genre", "I-Genre", "O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what is the ridley scott space horror about a spaceship crew trapped with the perfect extraterrestrial killing machine", "tokens": ["what", "is", "the", "ridley", "scott", "space", "horror", "about", "a", "spaceship", "crew", "trapped", "with", "the", "perfect", "extraterrestrial", "killing", "machine"], "ner_tags": ["O", "O", "O", "B-Director", "I-Director", "O", "B-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1988 romantic comedy centered around a baseball fan who has affair with a minor league player", "tokens": ["what", "1988", "romantic", "comedy", "centered", "around", "a", "baseball", "fan", "who", "has", "affair", "with", "a", "minor", "league", "player"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the 1998 romance film that finds joseph fiennes and gwyneth paltrow playing star crossed lovers in elizabethan england", "tokens": ["what", "s", "the", "1998", "romance", "film", "that", "finds", "joseph", "fiennes", "and", "gwyneth", "paltrow", "playing", "star", "crossed", "lovers", "in", "elizabethan", "england"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie starring denzel washington about an out of control train hauling deadly cargo", "tokens": ["what", "is", "the", "movie", "starring", "denzel", "washington", "about", "an", "out", "of", "control", "train", "hauling", "deadly", "cargo"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 1997 film that is one of the highest grossing box office movies of all time", "tokens": ["what", "is", "the", "1997", "film", "that", "is", "one", "of", "the", "highest", "grossing", "box", "office", "movies", "of", "all", "time"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "in what 2010 movie did josh brolin star alongside megan fox as a bounty hunter", "tokens": ["in", "what", "2010", "movie", "did", "josh", "brolin", "star", "alongside", "megan", "fox", "as", "a", "bounty", "hunter"], "ner_tags": ["O", "O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O"]}
{"sentence": "what s the film in which charlie brown gets the whole gang together to celebrate the holidays", "tokens": ["what", "s", "the", "film", "in", "which", "charlie", "brown", "gets", "the", "whole", "gang", "together", "to", "celebrate", "the", "holidays"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Character_Name", "I-Character_Name", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is that movie that has humans trapped in the world while playing a real life like scenario in their brains to escape or stay you are offered a red or blue pill escaping leads to possibly being the one to free them", "tokens": ["what", "is", "that", "movie", "that", "has", "humans", "trapped", "in", "the", "world", "while", "playing", "a", "real", "life", "like", "scenario", "in", "their", "brains", "to", "escape", "or", "stay", "you", "are", "offered", "a", "red", "or", "blue", "pill", "escaping", "leads", "to", "possibly", "being", "the", "one", "to", "free", "them"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of the movie that christopher nolan directed in between the filming of his batman movies", "tokens": ["what", "is", "the", "name", "of", "the", "movie", "that", "christopher", "nolan", "directed", "in", "between", "the", "filming", "of", "his", "batman", "movies"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Director", "I-Director", "O", "B-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship"]}
{"sentence": "reese witherspoon won an oscar for best actress for playing the wife of johnny cash in this film", "tokens": ["reese", "witherspoon", "won", "an", "oscar", "for", "best", "actress", "for", "playing", "the", "wife", "of", "johnny", "cash", "in", "this", "film"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "B-Award", "I-Award", "I-Award", "I-Award", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name", "B-Plot", "I-Plot", "I-Plot"]}
{"sentence": "2012 movie where the evil spirit pitch launches an assault on earth and people need help", "tokens": ["2012", "movie", "where", "the", "evil", "spirit", "pitch", "launches", "an", "assault", "on", "earth", "and", "people", "need", "help"], "ner_tags": ["B-Year", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2012 peter berg sci fi flick starred alexander skarsgard taylor kitsch brooklyn decker and liam neeson", "tokens": ["what", "2012", "peter", "berg", "sci", "fi", "flick", "starred", "alexander", "skarsgard", "taylor", "kitsch", "brooklyn", "decker", "and", "liam", "neeson"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "B-Genre", "I-Genre", "O", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "what 1972 moving starring jon voight and burt reynolds has a rafting trip turn into a fight to stay alive", "tokens": ["what", "1972", "moving", "starring", "jon", "voight", "and", "burt", "reynolds", "has", "a", "rafting", "trip", "turn", "into", "a", "fight", "to", "stay", "alive"], "ner_tags": ["O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1981 biopic starred faye dunaway and depicted the abusive adoptive upbringing of christina crawford", "tokens": ["what", "1981", "biopic", "starred", "faye", "dunaway", "and", "depicted", "the", "abusive", "adoptive", "upbringing", "of", "christina", "crawford"], "ner_tags": ["O", "B-Year", "B-Genre", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name"]}
{"sentence": "what is the name of this animated movie based on a book where food rains down from the sky like rain", "tokens": ["what", "is", "the", "name", "of", "this", "animated", "movie", "based", "on", "a", "book", "where", "food", "rains", "down", "from", "the", "sky", "like", "rain"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Genre", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "name this best picture and best director winning film which featured meryl streep in only her second movie role as well as john cazale in his final role", "tokens": ["name", "this", "best", "picture", "and", "best", "director", "winning", "film", "which", "featured", "meryl", "streep", "in", "only", "her", "second", "movie", "role", "as", "well", "as", "john", "cazale", "in", "his", "final", "role"], "ner_tags": ["O", "O", "B-Award", "I-Award", "I-Award", "I-Award", "I-Award", "I-Award", "I-Award", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O"]}
{"sentence": "this film directed by ben affleck tells the story of a band of men who go through boston with just their guns and their skills robbing banks and armored vehicles and not letting anything get in the way that is until they take a hostage", "tokens": ["this", "film", "directed", "by", "ben", "affleck", "tells", "the", "story", "of", "a", "band", "of", "men", "who", "go", "through", "boston", "with", "just", "their", "guns", "and", "their", "skills", "robbing", "banks", "and", "armored", "vehicles", "and", "not", "letting", "anything", "get", "in", "the", "way", "that", "is", "until", "they", "take", "a", "hostage"], "ner_tags": ["O", "O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this is one of the most famous kung fu movies of all time about two warriors in pursuit of a stolen sword", "tokens": ["this", "is", "one", "of", "the", "most", "famous", "kung", "fu", "movies", "of", "all", "time", "about", "two", "warriors", "in", "pursuit", "of", "a", "stolen", "sword"], "ner_tags": ["O", "B-Opinion", "I-Opinion", "I-Opinion", "I-Opinion", "I-Opinion", "I-Opinion", "B-Genre", "I-Genre", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "stanley kubrick s 1968 masterpiece provided some of the most iconic imagery in all of science fiction", "tokens": ["stanley", "kubrick", "s", "1968", "masterpiece", "provided", "some", "of", "the", "most", "iconic", "imagery", "in", "all", "of", "science", "fiction"], "ner_tags": ["B-Director", "I-Director", "O", "B-Year", "B-Opinion", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Genre", "I-Genre"]}
{"sentence": "what 2010 fantasy adventure film stars jay baruchel in the titular role and nicholas cage as his mentor", "tokens": ["what", "2010", "fantasy", "adventure", "film", "stars", "jay", "baruchel", "in", "the", "titular", "role", "and", "nicholas", "cage", "as", "his", "mentor"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O"]}
{"sentence": "what s the name of the movie about a childish yet brilliant eighteenth century vienna composer", "tokens": ["what", "s", "the", "name", "of", "the", "movie", "about", "a", "childish", "yet", "brilliant", "eighteenth", "century", "vienna", "composer"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what movie had a man with a brutally scarred face who could speak to dead people and he was chasing john malkovich", "tokens": ["what", "movie", "had", "a", "man", "with", "a", "brutally", "scarred", "face", "who", "could", "speak", "to", "dead", "people", "and", "he", "was", "chasing", "john", "malkovich"], "ner_tags": ["O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Actor", "I-Actor"]}
{"sentence": "this movie is an american classic in which a manipulative woman and a roguish man have a love affair in the american south during the civil war and reconstruction", "tokens": ["this", "movie", "is", "an", "american", "classic", "in", "which", "a", "manipulative", "woman", "and", "a", "roguish", "man", "have", "a", "love", "affair", "in", "the", "american", "south", "during", "the", "civil", "war", "and", "reconstruction"], "ner_tags": ["O", "O", "O", "O", "O", "B-Opinion", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2010 movie is the live action adaptation of an american cartoon series of the same title", "tokens": ["what", "2010", "movie", "is", "the", "live", "action", "adaptation", "of", "an", "american", "cartoon", "series", "of", "the", "same", "title"], "ner_tags": ["O", "B-Year", "O", "O", "O", "B-Genre", "I-Genre", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "this science fiction movie directed by stanley kubrick features a mysterious black monolith and a computer named hal", "tokens": ["this", "science", "fiction", "movie", "directed", "by", "stanley", "kubrick", "features", "a", "mysterious", "black", "monolith", "and", "a", "computer", "named", "hal"], "ner_tags": ["O", "B-Genre", "I-Genre", "O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name"]}
{"sentence": "i am thinking of the film adaptation of the ntozake shange feminist stage play the film features an ensemble cast that includes phylicia rashad whoopi goldberg and janet jackson", "tokens": ["i", "am", "thinking", "of", "the", "film", "adaptation", "of", "the", "ntozake", "shange", "feminist", "stage", "play", "the", "film", "features", "an", "ensemble", "cast", "that", "includes", "phylicia", "rashad", "whoopi", "goldberg", "and", "janet", "jackson"], "ner_tags": ["O", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "what year did casino come out and who was the film based on", "tokens": ["what", "year", "did", "casino", "come", "out", "and", "who", "was", "the", "film", "based", "on"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what is the name of this final installment based off off jk rowlings books about a young wizards final battle against the infamous lord voldemort", "tokens": ["what", "is", "the", "name", "of", "this", "final", "installment", "based", "off", "off", "jk", "rowlings", "books", "about", "a", "young", "wizards", "final", "battle", "against", "the", "infamous", "lord", "voldemort"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Relationship", "I-Relationship", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name"]}
{"sentence": "what s the film with cuba gooding jr about life in compton including casual gang violence", "tokens": ["what", "s", "the", "film", "with", "cuba", "gooding", "jr", "about", "life", "in", "compton", "including", "casual", "gang", "violence"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "dustin hoffman famously said are are you trying to seduce me mrs robinson to anne bancroft in this 1967 classic", "tokens": ["dustin", "hoffman", "famously", "said", "are", "are", "you", "trying", "to", "seduce", "me", "mrs", "robinson", "to", "anne", "bancroft", "in", "this", "1967", "classic"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "B-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "I-Quote", "O", "B-Actor", "I-Actor", "O", "O", "B-Year", "B-Genre"]}
{"sentence": "what is the drew barrymore movie about a couple who are trying to keep their romance alive although one is in new york and the other is in california", "tokens": ["what", "is", "the", "drew", "barrymore", "movie", "about", "a", "couple", "who", "are", "trying", "to", "keep", "their", "romance", "alive", "although", "one", "is", "in", "new", "york", "and", "the", "other", "is", "in", "california"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in this 2011 steven spielberg animation adventure movie a boy and a pirate team up to search for treasure", "tokens": ["in", "this", "2011", "steven", "spielberg", "animation", "adventure", "movie", "a", "boy", "and", "a", "pirate", "team", "up", "to", "search", "for", "treasure"], "ner_tags": ["O", "O", "B-Year", "B-Director", "I-Director", "B-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "these dark skinned felines are making me nevus", "tokens": ["these", "dark", "skinned", "felines", "are", "making", "me", "nevus"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what is the family movie that talks about an orphan who lives in the train station", "tokens": ["what", "is", "the", "family", "movie", "that", "talks", "about", "an", "orphan", "who", "lives", "in", "the", "train", "station"], "ner_tags": ["O", "O", "O", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "an robotic assassin from a post apocalyptic future travels back in time to eliminate a waitress is the plot of this james cameron blockbuster", "tokens": ["an", "robotic", "assassin", "from", "a", "post", "apocalyptic", "future", "travels", "back", "in", "time", "to", "eliminate", "a", "waitress", "is", "the", "plot", "of", "this", "james", "cameron", "blockbuster"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "O", "B-Director", "I-Director", "O"]}
{"sentence": "this movie about young wizards and witches was the 1 st in a series of 8", "tokens": ["this", "movie", "about", "young", "wizards", "and", "witches", "was", "the", "1", "st", "in", "a", "series", "of", "8"], "ner_tags": ["O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "when a secretary s idea is stolen by her boss she seizes an opportunity to steal it back by pretending she has her boss s job is the plot of this 1988 comedy", "tokens": ["when", "a", "secretary", "s", "idea", "is", "stolen", "by", "her", "boss", "she", "seizes", "an", "opportunity", "to", "steal", "it", "back", "by", "pretending", "she", "has", "her", "boss", "s", "job", "is", "the", "plot", "of", "this", "1988", "comedy"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "O", "B-Year", "B-Genre"]}
{"sentence": "peter ostrum who plays charlie bucket in this classic family move made no other films he later became a veterinarian", "tokens": ["peter", "ostrum", "who", "plays", "charlie", "bucket", "in", "this", "classic", "family", "move", "made", "no", "other", "films", "he", "later", "became", "a", "veterinarian"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "B-Character_Name", "I-Character_Name", "O", "O", "O", "B-Genre", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what s the name of the dr seuss movie about an elephant and a speck on a flower", "tokens": ["what", "s", "the", "name", "of", "the", "dr", "seuss", "movie", "about", "an", "elephant", "and", "a", "speck", "on", "a", "flower"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Origin", "I-Origin", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the first movie in a series starring arnold schwarzenegger as a cyborg sent back in time to kill john connor the leader of a future resistance against the machines", "tokens": ["what", "is", "the", "first", "movie", "in", "a", "series", "starring", "arnold", "schwarzenegger", "as", "a", "cyborg", "sent", "back", "in", "time", "to", "kill", "john", "connor", "the", "leader", "of", "a", "future", "resistance", "against", "the", "machines"], "ner_tags": ["O", "O", "O", "B-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "meryl streep allegedly put on 15 20 pounds of weight for her role in this 1995 romance as a middle aged housewife", "tokens": ["meryl", "streep", "allegedly", "put", "on", "15", "20", "pounds", "of", "weight", "for", "her", "role", "in", "this", "1995", "romance", "as", "a", "middle", "aged", "housewife"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Year", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what was the 1987 movie starring robert deniro and charles grodin where deniro plays a bounty hunter hired to bring back grodin", "tokens": ["what", "was", "the", "1987", "movie", "starring", "robert", "deniro", "and", "charles", "grodin", "where", "deniro", "plays", "a", "bounty", "hunter", "hired", "to", "bring", "back", "grodin"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Actor"]}
{"sentence": "a 1997 romantic tragic film written and directed by james cameron starring leonardo dicaprio and kate", "tokens": ["a", "1997", "romantic", "tragic", "film", "written", "and", "directed", "by", "james", "cameron", "starring", "leonardo", "dicaprio", "and", "kate"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "O", "O", "O", "O", "O", "B-Director", "I-Director", "O", "B-Actor", "I-Actor", "O", "B-Actor"]}
{"sentence": "what movie features psychopath norman bates and the most famous shower scene in cinematic history", "tokens": ["what", "movie", "features", "psychopath", "norman", "bates", "and", "the", "most", "famous", "shower", "scene", "in", "cinematic", "history"], "ner_tags": ["O", "O", "O", "O", "B-Character_Name", "I-Character_Name", "O", "O", "O", "B-Opinion", "I-Opinion", "I-Opinion", "I-Opinion", "I-Opinion", "I-Opinion"]}
{"sentence": "what is the adventure movie about superheros who gather together to save the country including iron man", "tokens": ["what", "is", "the", "adventure", "movie", "about", "superheros", "who", "gather", "together", "to", "save", "the", "country", "including", "iron", "man"], "ner_tags": ["O", "O", "O", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name"]}
{"sentence": "in what movie did maggie gyllenhaal replace katie holmes as bruce s friend and love interest rachel dawes", "tokens": ["in", "what", "movie", "did", "maggie", "gyllenhaal", "replace", "katie", "holmes", "as", "bruce", "s", "friend", "and", "love", "interest", "rachel", "dawes"], "ner_tags": ["O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "O", "B-Character_Name", "I-Character_Name"]}
{"sentence": "how many drummers exploded during the groups existence what was the name of the album they were promoting with the failed tour", "tokens": ["how", "many", "drummers", "exploded", "during", "the", "groups", "existence", "what", "was", "the", "name", "of", "the", "album", "they", "were", "promoting", "with", "the", "failed", "tour"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2011 movie stars bradley cooper as writer eddie morra who discovers a top secret drug", "tokens": ["what", "2011", "movie", "stars", "bradley", "cooper", "as", "writer", "eddie", "morra", "who", "discovers", "a", "top", "secret", "drug"], "ner_tags": ["O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Character_Name", "I-Character_Name", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the movie based on a ray bradbury novel about a fireman who burns books in the near future", "tokens": ["what", "s", "the", "movie", "based", "on", "a", "ray", "bradbury", "novel", "about", "a", "fireman", "who", "burns", "books", "in", "the", "near", "future"], "ner_tags": ["O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "the life of economist john nash from early success to later struggles with delusions is detailed in this 2001 movie", "tokens": ["the", "life", "of", "economist", "john", "nash", "from", "early", "success", "to", "later", "struggles", "with", "delusions", "is", "detailed", "in", "this", "2001", "movie"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "B-Year", "O"]}
{"sentence": "i m thinking of a comedy about two married men who get a vacation from marriage", "tokens": ["i", "m", "thinking", "of", "a", "comedy", "about", "two", "married", "men", "who", "get", "a", "vacation", "from", "marriage"], "ner_tags": ["O", "O", "O", "O", "O", "B-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "emma stone stars in this 2010 movie about what happens when you send the high school rumor mill spinning", "tokens": ["emma", "stone", "stars", "in", "this", "2010", "movie", "about", "what", "happens", "when", "you", "send", "the", "high", "school", "rumor", "mill", "spinning"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "B-Year", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie about making a broadway show with the song about a road in new york that is from the 30 s", "tokens": ["what", "is", "the", "movie", "about", "making", "a", "broadway", "show", "with", "the", "song", "about", "a", "road", "in", "new", "york", "that", "is", "from", "the", "30", "s"], "ner_tags": ["O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Year", "I-Year"]}
{"sentence": "what is the very famous clint eastwood movie about two bounty hunters who try to beat a third before they find buried treasure", "tokens": ["what", "is", "the", "very", "famous", "clint", "eastwood", "movie", "about", "two", "bounty", "hunters", "who", "try", "to", "beat", "a", "third", "before", "they", "find", "buried", "treasure"], "ner_tags": ["O", "O", "O", "B-Opinion", "I-Opinion", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of that movie with the piano theme music where the two guys try to con another con man", "tokens": ["what", "is", "the", "name", "of", "that", "movie", "with", "the", "piano", "theme", "music", "where", "the", "two", "guys", "try", "to", "con", "another", "con", "man"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the italian film that is about a movie theater in a small town that connects the people in the town", "tokens": ["what", "is", "the", "italian", "film", "that", "is", "about", "a", "movie", "theater", "in", "a", "small", "town", "that", "connects", "the", "people", "in", "the", "town"], "ner_tags": ["O", "O", "O", "B-Genre", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of the sequel in which there s a high tech war going on between cats and dogs that humans do n t know about", "tokens": ["what", "is", "the", "name", "of", "the", "sequel", "in", "which", "there", "s", "a", "high", "tech", "war", "going", "on", "between", "cats", "and", "dogs", "that", "humans", "do", "n", "t", "know", "about"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Relationship", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "which movie tells three interconnected stories about a small group of friends and a christmas eve rave", "tokens": ["which", "movie", "tells", "three", "interconnected", "stories", "about", "a", "small", "group", "of", "friends", "and", "a", "christmas", "eve", "rave"], "ner_tags": ["O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "steve carrel provided the voice for this bad guy who adopts 3 orphans what movie am i thinking about", "tokens": ["steve", "carrel", "provided", "the", "voice", "for", "this", "bad", "guy", "who", "adopts", "3", "orphans", "what", "movie", "am", "i", "thinking", "about"], "ner_tags": ["B-Actor", "I-Actor", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O", "O", "O"]}
{"sentence": "i m thinking of a movie drama in which colin firth is a king with a speech impediment", "tokens": ["i", "m", "thinking", "of", "a", "movie", "drama", "in", "which", "colin", "firth", "is", "a", "king", "with", "a", "speech", "impediment"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Genre", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "masterpiece from quentin tarantino features samuel l jackson and johnny travolta as clumsy thugs along with bruce willis uma thurman and other stars", "tokens": ["masterpiece", "from", "quentin", "tarantino", "features", "samuel", "l", "jackson", "and", "johnny", "travolta", "as", "clumsy", "thugs", "along", "with", "bruce", "willis", "uma", "thurman", "and", "other", "stars"], "ner_tags": ["O", "O", "B-Director", "I-Director", "O", "B-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "O", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "O", "O"]}
{"sentence": "i m thinking of a movie made by disney where we follow a robot in a post apocalyptic world where he has to clean up the mess", "tokens": ["i", "m", "thinking", "of", "a", "movie", "made", "by", "disney", "where", "we", "follow", "a", "robot", "in", "a", "post", "apocalyptic", "world", "where", "he", "has", "to", "clean", "up", "the", "mess"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Director", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 2011 romantic comedy film starring russell brand as a very rich man who falls in love with a tour guide played by greta gerwig", "tokens": ["what", "is", "the", "2011", "romantic", "comedy", "film", "starring", "russell", "brand", "as", "a", "very", "rich", "man", "who", "falls", "in", "love", "with", "a", "tour", "guide", "played", "by", "greta", "gerwig"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Actor", "I-Actor"]}
{"sentence": "what is that 2012 american fantasy comedy film directed by seth macfarlane and starring mark wahlberg", "tokens": ["what", "is", "that", "2012", "american", "fantasy", "comedy", "film", "directed", "by", "seth", "macfarlane", "and", "starring", "mark", "wahlberg"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor"]}
{"sentence": "which animated film is about a kid realizes how much he misses his mother after she has been taken to another planet", "tokens": ["which", "animated", "film", "is", "about", "a", "kid", "realizes", "how", "much", "he", "misses", "his", "mother", "after", "she", "has", "been", "taken", "to", "another", "planet"], "ner_tags": ["O", "B-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of the summer blockbuster movie about a large sea creature that terrorizes a vacation town", "tokens": ["what", "is", "the", "name", "of", "the", "summer", "blockbuster", "movie", "about", "a", "large", "sea", "creature", "that", "terrorizes", "a", "vacation", "town"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of the pixar animated film about a group of toys that come to life when no one is around", "tokens": ["what", "is", "the", "name", "of", "the", "pixar", "animated", "film", "about", "a", "group", "of", "toys", "that", "come", "to", "life", "when", "no", "one", "is", "around"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Director", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 2010 american film starring natalie portman as a ballet dancer and directed by darren aronofsky", "tokens": ["what", "is", "the", "2010", "american", "film", "starring", "natalie", "portman", "as", "a", "ballet", "dancer", "and", "directed", "by", "darren", "aronofsky"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "O", "O", "O", "B-Director", "I-Director"]}
{"sentence": "what is the movie starring mila kunis and justin timberlake about two friends who think they can have sex with each other without forming an attachment", "tokens": ["what", "is", "the", "movie", "starring", "mila", "kunis", "and", "justin", "timberlake", "about", "two", "friends", "who", "think", "they", "can", "have", "sex", "with", "each", "other", "without", "forming", "an", "attachment"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1975 drama does jack nicholson star in as a man that has been admitted to a mental institution", "tokens": ["what", "1975", "drama", "does", "jack", "nicholson", "star", "in", "as", "a", "man", "that", "has", "been", "admitted", "to", "a", "mental", "institution"], "ner_tags": ["O", "B-Year", "B-Genre", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the animated film about a young kid who interacts with dragons with his friends", "tokens": ["what", "s", "the", "animated", "film", "about", "a", "young", "kid", "who", "interacts", "with", "dragons", "with", "his", "friends"], "ner_tags": ["O", "O", "O", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "the name of a comedy movie about a plane that has the song staying alive in it", "tokens": ["the", "name", "of", "a", "comedy", "movie", "about", "a", "plane", "that", "has", "the", "song", "staying", "alive", "in", "it"], "ner_tags": ["O", "O", "O", "O", "B-Genre", "O", "O", "B-Plot", "I-Plot", "O", "O", "O", "O", "B-Soundtrack", "I-Soundtrack", "O", "O"]}
{"sentence": "benicio del toro is a british acotr that returns to his home after his brother died from the hands of what seems to be a large beast but he will discover deep secrets buried in the family history", "tokens": ["benicio", "del", "toro", "is", "a", "british", "acotr", "that", "returns", "to", "his", "home", "after", "his", "brother", "died", "from", "the", "hands", "of", "what", "seems", "to", "be", "a", "large", "beast", "but", "he", "will", "discover", "deep", "secrets", "buried", "in", "the", "family", "history"], "ner_tags": ["B-Actor", "I-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what movie stars bette davis as an aging actress who is threatened by a young actress played by anne bancroft", "tokens": ["what", "movie", "stars", "bette", "davis", "as", "an", "aging", "actress", "who", "is", "threatened", "by", "a", "young", "actress", "played", "by", "anne", "bancroft"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Actor", "I-Actor"]}
{"sentence": "a movie where bruce willis plays in air vents walks on glass and shoots terrorists", "tokens": ["a", "movie", "where", "bruce", "willis", "plays", "in", "air", "vents", "walks", "on", "glass", "and", "shoots", "terrorists"], "ner_tags": ["O", "O", "O", "B-Actor", "I-Actor", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i am thinking of a classic film that stars gregory peck in a story about a man who gets entangled in a bitter land feud", "tokens": ["i", "am", "thinking", "of", "a", "classic", "film", "that", "stars", "gregory", "peck", "in", "a", "story", "about", "a", "man", "who", "gets", "entangled", "in", "a", "bitter", "land", "feud"], "ner_tags": ["O", "O", "O", "O", "O", "B-Genre", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 2010 romantic comedy centers around two single adults becoming caregivers to an orphaned girl when their mutual best friends die in an accident", "tokens": ["what", "2010", "romantic", "comedy", "centers", "around", "two", "single", "adults", "becoming", "caregivers", "to", "an", "orphaned", "girl", "when", "their", "mutual", "best", "friends", "die", "in", "an", "accident"], "ner_tags": ["O", "B-Year", "B-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "matt reeves wrote and directed this thriller about a boy who befriends a female vampire", "tokens": ["matt", "reeves", "wrote", "and", "directed", "this", "thriller", "about", "a", "boy", "who", "befriends", "a", "female", "vampire"], "ner_tags": ["B-Director", "I-Director", "O", "O", "O", "O", "B-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 1961 musical movie about rival gangs in a big city", "tokens": ["what", "is", "the", "1961", "musical", "movie", "about", "rival", "gangs", "in", "a", "big", "city"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 1998 comedy film written and directed by the coen brothers which stars jeff bridges as the dude", "tokens": ["what", "is", "the", "1998", "comedy", "film", "written", "and", "directed", "by", "the", "coen", "brothers", "which", "stars", "jeff", "bridges", "as", "the", "dude"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "O", "O", "O", "O", "O", "O", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor", "O", "O", "O"]}
{"sentence": "what is the 1998 movie starring vince vaughn and julianne morre where a sister and a boyfriend to try figure out what happened at a motel", "tokens": ["what", "is", "the", "1998", "movie", "starring", "vince", "vaughn", "and", "julianne", "morre", "where", "a", "sister", "and", "a", "boyfriend", "to", "try", "figure", "out", "what", "happened", "at", "a", "motel"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the mtv series that is a spin off the series the hills", "tokens": ["what", "is", "the", "mtv", "series", "that", "is", "a", "spin", "off", "the", "series", "the", "hills"], "ner_tags": ["O", "O", "O", "B-Director", "B-Relationship", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the christmas movie in which a guy wishes for things to change but then likes the old way", "tokens": ["what", "is", "the", "christmas", "movie", "in", "which", "a", "guy", "wishes", "for", "things", "to", "change", "but", "then", "likes", "the", "old", "way"], "ner_tags": ["O", "O", "O", "B-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what film tells the story of a secret military project that endangers neo tokyo when it turns a biker gang member into a rampaging psionic psychopath", "tokens": ["what", "film", "tells", "the", "story", "of", "a", "secret", "military", "project", "that", "endangers", "neo", "tokyo", "when", "it", "turns", "a", "biker", "gang", "member", "into", "a", "rampaging", "psionic", "psychopath"], "ner_tags": ["O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this story about a loan collector and minor boxer who suddenly receives a world title fight stars sylvester stallone", "tokens": ["this", "story", "about", "a", "loan", "collector", "and", "minor", "boxer", "who", "suddenly", "receives", "a", "world", "title", "fight", "stars", "sylvester", "stallone"], "ner_tags": ["O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "B-Actor", "I-Actor"]}
{"sentence": "i m thinking of a pixar animated film that features children s toys that come to life", "tokens": ["i", "m", "thinking", "of", "a", "pixar", "animated", "film", "that", "features", "children", "s", "toys", "that", "come", "to", "life"], "ner_tags": ["O", "O", "O", "O", "O", "B-Director", "B-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what action adventure movie features a televised competition to the death to be fought out by a representative teenager chosen at random", "tokens": ["what", "action", "adventure", "movie", "features", "a", "televised", "competition", "to", "the", "death", "to", "be", "fought", "out", "by", "a", "representative", "teenager", "chosen", "at", "random"], "ner_tags": ["O", "B-Genre", "I-Genre", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "movie about college life and fraternity", "tokens": ["movie", "about", "college", "life", "and", "fraternity"], "ner_tags": ["O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what movie is about work superiors hosting a dinner celebrating the idiocy of their guests a rising executive questions it when he s invited just as he befriends a man who would be the perfect guest", "tokens": ["what", "movie", "is", "about", "work", "superiors", "hosting", "a", "dinner", "celebrating", "the", "idiocy", "of", "their", "guests", "a", "rising", "executive", "questions", "it", "when", "he", "s", "invited", "just", "as", "he", "befriends", "a", "man", "who", "would", "be", "the", "perfect", "guest"], "ner_tags": ["O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1976 rene daalder movie centered around a high school student who resorts to murdering bullies", "tokens": ["what", "1976", "rene", "daalder", "movie", "centered", "around", "a", "high", "school", "student", "who", "resorts", "to", "murdering", "bullies"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie starring denzel washington about the message that spike lee was trying to send to an iconic figure", "tokens": ["what", "is", "the", "movie", "starring", "denzel", "washington", "about", "the", "message", "that", "spike", "lee", "was", "trying", "to", "send", "to", "an", "iconic", "figure"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie about a retired detective who investigates the strange activities of his friend s wife", "tokens": ["what", "is", "the", "movie", "about", "a", "retired", "detective", "who", "investigates", "the", "strange", "activities", "of", "his", "friend", "s", "wife"], "ner_tags": ["O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "do you know the name of the musical film based off of a certain style of dancing", "tokens": ["do", "you", "know", "the", "name", "of", "the", "musical", "film", "based", "off", "of", "a", "certain", "style", "of", "dancing"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Genre", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "which movie is the beginning of a series in which christopher reeve portrays a very famous superhero", "tokens": ["which", "movie", "is", "the", "beginning", "of", "a", "series", "in", "which", "christopher", "reeve", "portrays", "a", "very", "famous", "superhero"], "ner_tags": ["O", "O", "O", "O", "B-Relationship", "I-Relationship", "I-Relationship", "I-Relationship", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O"]}
{"sentence": "what is the 1984 movie starring robert de niro about a jewish gangster that returns to brooklyn after 30 years to confront his past", "tokens": ["what", "is", "the", "1984", "movie", "starring", "robert", "de", "niro", "about", "a", "jewish", "gangster", "that", "returns", "to", "brooklyn", "after", "30", "years", "to", "confront", "his", "past"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "B-Actor", "I-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the spike lee biopic about the famous black civil rights leader of the 1960 s", "tokens": ["what", "s", "the", "spike", "lee", "biopic", "about", "the", "famous", "black", "civil", "rights", "leader", "of", "the", "1960", "s"], "ner_tags": ["O", "O", "O", "B-Director", "I-Director", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what was the name of the movie that 5 young friends were chased through texas from thomas hewitt wielding a chainsaw", "tokens": ["what", "was", "the", "name", "of", "the", "movie", "that", "5", "young", "friends", "were", "chased", "through", "texas", "from", "thomas", "hewitt", "wielding", "a", "chainsaw"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "in what sci fi movie are aliens sent to live in interment camps until a government agent becomes infected and befriends one of their kind", "tokens": ["in", "what", "sci", "fi", "movie", "are", "aliens", "sent", "to", "live", "in", "interment", "camps", "until", "a", "government", "agent", "becomes", "infected", "and", "befriends", "one", "of", "their", "kind"], "ner_tags": ["O", "O", "B-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "i m thinking of the movie with jim belushi that involves a fraternity house and toga parties", "tokens": ["i", "m", "thinking", "of", "the", "movie", "with", "jim", "belushi", "that", "involves", "a", "fraternity", "house", "and", "toga", "parties"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the name of the 2010 disney movie in which mandy moore voice the character rapunzel", "tokens": ["what", "is", "the", "name", "of", "the", "2010", "disney", "movie", "in", "which", "mandy", "moore", "voice", "the", "character", "rapunzel"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Year", "B-Director", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Character_Name"]}
{"sentence": "what was the movie where a hobbit named frodo is tasked to destroy an evil ring", "tokens": ["what", "was", "the", "movie", "where", "a", "hobbit", "named", "frodo", "is", "tasked", "to", "destroy", "an", "evil", "ring"], "ner_tags": ["O", "O", "O", "O", "O", "B-Plot", "I-Plot", "O", "B-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this 2010 retelling of the classic story of rapunzel featured mandy moore and zachary levi", "tokens": ["this", "2010", "retelling", "of", "the", "classic", "story", "of", "rapunzel", "featured", "mandy", "moore", "and", "zachary", "levi"], "ner_tags": ["O", "B-Year", "O", "O", "O", "B-Opinion", "B-Plot", "O", "B-Plot", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "in what movie was maggie gylenhaal cast to play rachel bruce s long time friend and love interest", "tokens": ["in", "what", "movie", "was", "maggie", "gylenhaal", "cast", "to", "play", "rachel", "bruce", "s", "long", "time", "friend", "and", "love", "interest"], "ner_tags": ["O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Character_Name", "I-Character_Name", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie where a woman was charged in the conspiracy to kill president abraham lincoln", "tokens": ["what", "is", "the", "movie", "where", "a", "woman", "was", "charged", "in", "the", "conspiracy", "to", "kill", "president", "abraham", "lincoln"], "ner_tags": ["O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 2012 american war film starring chris hemsworth and directed by dan bradley", "tokens": ["what", "is", "the", "2012", "american", "war", "film", "starring", "chris", "hemsworth", "and", "directed", "by", "dan", "bradley"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Director", "I-Director"]}
{"sentence": "what 1980 stanley kubrick film was based on a stephen king novel of the same name", "tokens": ["what", "1980", "stanley", "kubrick", "film", "was", "based", "on", "a", "stephen", "king", "novel", "of", "the", "same", "name"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "O", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "O", "O", "O", "O"]}
{"sentence": "i m thinking of a movie which is involved with a group people who have to battle each in order for one to become victorious", "tokens": ["i", "m", "thinking", "of", "a", "movie", "which", "is", "involved", "with", "a", "group", "people", "who", "have", "to", "battle", "each", "in", "order", "for", "one", "to", "become", "victorious"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this movie is a romantic comedy that revolves about an irish tradition where in leap year the woman can propose to the man while chasing her future fiance she meets a guy and ends up falling in love what is the movie", "tokens": ["this", "movie", "is", "a", "romantic", "comedy", "that", "revolves", "about", "an", "irish", "tradition", "where", "in", "leap", "year", "the", "woman", "can", "propose", "to", "the", "man", "while", "chasing", "her", "future", "fiance", "she", "meets", "a", "guy", "and", "ends", "up", "falling", "in", "love", "what", "is", "the", "movie"], "ner_tags": ["O", "O", "O", "O", "B-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "O", "O"]}
{"sentence": "i am thinking of a disney modern classic film about the intertwined story of king mustafa prince simba and the evil uncle scar", "tokens": ["i", "am", "thinking", "of", "a", "disney", "modern", "classic", "film", "about", "the", "intertwined", "story", "of", "king", "mustafa", "prince", "simba", "and", "the", "evil", "uncle", "scar"], "ner_tags": ["O", "O", "O", "O", "O", "B-Genre", "I-Genre", "I-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "B-Character_Name", "I-Character_Name", "I-Character_Name", "I-Character_Name", "B-Plot", "I-Plot", "B-Character_Name", "I-Character_Name", "I-Character_Name"]}
{"sentence": "what is the name of lewis carrol s famous psychedelic fairy tale most recently adapted by tim burton", "tokens": ["what", "is", "the", "name", "of", "lewis", "carrol", "s", "famous", "psychedelic", "fairy", "tale", "most", "recently", "adapted", "by", "tim", "burton"], "ner_tags": ["O", "O", "O", "O", "O", "B-Origin", "I-Origin", "O", "B-Opinion", "O", "B-Genre", "I-Genre", "O", "O", "B-Origin", "O", "B-Director", "I-Director"]}
{"sentence": "what is the name of the 1978 movie where jill clayburgh plays a wealthy woman from manhattan s upper east side", "tokens": ["what", "is", "the", "name", "of", "the", "1978", "movie", "where", "jill", "clayburgh", "plays", "a", "wealthy", "woman", "from", "manhattan", "s", "upper", "east", "side"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what movie from the mind of joss whedon takes the sloppy formulaic horror movie genre and flips it on its head", "tokens": ["what", "movie", "from", "the", "mind", "of", "joss", "whedon", "takes", "the", "sloppy", "formulaic", "horror", "movie", "genre", "and", "flips", "it", "on", "its", "head"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "O", "B-Genre", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what is that classic martin scorsese directed movie starring robert de niro as jake lamotta a professional boxer", "tokens": ["what", "is", "that", "classic", "martin", "scorsese", "directed", "movie", "starring", "robert", "de", "niro", "as", "jake", "lamotta", "a", "professional", "boxer"], "ner_tags": ["O", "O", "O", "B-Opinion", "B-Director", "I-Director", "O", "O", "O", "B-Actor", "I-Actor", "I-Actor", "O", "B-Character_Name", "I-Character_Name", "O", "O", "O"]}
{"sentence": "what s the movie where paul newman plays a pool hustler who matches his skills against jackie gleason", "tokens": ["what", "s", "the", "movie", "where", "paul", "newman", "plays", "a", "pool", "hustler", "who", "matches", "his", "skills", "against", "jackie", "gleason"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Actor", "I-Actor"]}
{"sentence": "what is the name of the movie in which gandalf and aragorn lead an army against sauron s army so frodo and same can get to mount doom", "tokens": ["what", "is", "the", "name", "of", "the", "movie", "in", "which", "gandalf", "and", "aragorn", "lead", "an", "army", "against", "sauron", "s", "army", "so", "frodo", "and", "same", "can", "get", "to", "mount", "doom"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-Character_Name", "O", "B-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Character_Name", "O", "B-Plot", "I-Plot", "B-Character_Name", "O", "B-Character_Name", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is a 1942 animated classic about a young white tailed deer and his woodland friends", "tokens": ["what", "is", "a", "1942", "animated", "classic", "about", "a", "young", "white", "tailed", "deer", "and", "his", "woodland", "friends"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what 1984 action movie is based on the invasion of a small colorado town by an alliance of the cuban and russian militarily and the some of the wolverines efforts to oust the occupiers", "tokens": ["what", "1984", "action", "movie", "is", "based", "on", "the", "invasion", "of", "a", "small", "colorado", "town", "by", "an", "alliance", "of", "the", "cuban", "and", "russian", "militarily", "and", "the", "some", "of", "the", "wolverines", "efforts", "to", "oust", "the", "occupiers"], "ner_tags": ["O", "B-Year", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "this 2011 comedy film starred steve martin jack black and owen wilson as bird enthusiasts", "tokens": ["this", "2011", "comedy", "film", "starred", "steve", "martin", "jack", "black", "and", "owen", "wilson", "as", "bird", "enthusiasts"], "ner_tags": ["O", "B-Year", "B-Genre", "O", "O", "B-Actor", "I-Actor", "I-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "O"]}
{"sentence": "what action packed blockbuster features sly stallone as well as several other heavy hitters in the blockbuster genre", "tokens": ["what", "action", "packed", "blockbuster", "features", "sly", "stallone", "as", "well", "as", "several", "other", "heavy", "hitters", "in", "the", "blockbuster", "genre"], "ner_tags": ["O", "B-Genre", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "what hitchcock thriller features the classic scene in which cary grant is attacked by a crop dusting plane", "tokens": ["what", "hitchcock", "thriller", "features", "the", "classic", "scene", "in", "which", "cary", "grant", "is", "attacked", "by", "a", "crop", "dusting", "plane"], "ner_tags": ["O", "B-Director", "B-Genre", "O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the classic with bogie and bacall that mostly takes place in a hotel during a hurricane", "tokens": ["what", "s", "the", "classic", "with", "bogie", "and", "bacall", "that", "mostly", "takes", "place", "in", "a", "hotel", "during", "a", "hurricane"], "ner_tags": ["O", "O", "O", "B-Opinion", "O", "B-Actor", "O", "B-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the mel brooks film which also features gene wilder that tells the story of two men trying to create a broadway flop in order to bilk money from investors", "tokens": ["what", "is", "the", "mel", "brooks", "film", "which", "also", "features", "gene", "wilder", "that", "tells", "the", "story", "of", "two", "men", "trying", "to", "create", "a", "broadway", "flop", "in", "order", "to", "bilk", "money", "from", "investors"], "ner_tags": ["O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "name the 2010 remake of a classic story about a man who robs from the rich and gives to the poor", "tokens": ["name", "the", "2010", "remake", "of", "a", "classic", "story", "about", "a", "man", "who", "robs", "from", "the", "rich", "and", "gives", "to", "the", "poor"], "ner_tags": ["O", "O", "B-Year", "B-Relationship", "O", "O", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what movie is an animated film about exotic birds in a foreign country that is fun and has a good message", "tokens": ["what", "movie", "is", "an", "animated", "film", "about", "exotic", "birds", "in", "a", "foreign", "country", "that", "is", "fun", "and", "has", "a", "good", "message"], "ner_tags": ["O", "O", "O", "O", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "can you name the supernatural horror film with a man that has multiple personality disorder and six kids", "tokens": ["can", "you", "name", "the", "supernatural", "horror", "film", "with", "a", "man", "that", "has", "multiple", "personality", "disorder", "and", "six", "kids"], "ner_tags": ["O", "O", "O", "O", "B-Genre", "I-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is that ron howard directed movie where russell crowe plays john nash a very brilliant but troubled mathematician", "tokens": ["what", "is", "that", "ron", "howard", "directed", "movie", "where", "russell", "crowe", "plays", "john", "nash", "a", "very", "brilliant", "but", "troubled", "mathematician"], "ner_tags": ["O", "O", "O", "B-Director", "I-Director", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Character_Name", "I-Character_Name", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what s the name of the first move in the animated series that features buzz lightyear and woody", "tokens": ["what", "s", "the", "name", "of", "the", "first", "move", "in", "the", "animated", "series", "that", "features", "buzz", "lightyear", "and", "woody"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Relationship", "I-Relationship", "O", "O", "B-Genre", "I-Genre", "O", "O", "B-Character_Name", "I-Character_Name", "O", "B-Character_Name"]}
{"sentence": "what 2007 michael bay movie has shia labeouf and megan fox starring amongst the autobots and decepticons", "tokens": ["what", "2007", "michael", "bay", "movie", "has", "shia", "labeouf", "and", "megan", "fox", "starring", "amongst", "the", "autobots", "and", "decepticons"], "ner_tags": ["O", "B-Year", "B-Director", "I-Director", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor", "O", "O", "O", "B-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the movie made in 1981 and remade in 2010 about a demigod persius and aid to zeus in a fight against hades", "tokens": ["what", "is", "the", "movie", "made", "in", "1981", "and", "remade", "in", "2010", "about", "a", "demigod", "persius", "and", "aid", "to", "zeus", "in", "a", "fight", "against", "hades"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Year", "O", "O", "O", "B-Year", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is the 2010 american crime thriller film starring mel gibson and based on the bbc series of the same name", "tokens": ["what", "is", "the", "2010", "american", "crime", "thriller", "film", "starring", "mel", "gibson", "and", "based", "on", "the", "bbc", "series", "of", "the", "same", "name"], "ner_tags": ["O", "O", "O", "B-Year", "B-Genre", "I-Genre", "I-Genre", "O", "O", "B-Actor", "I-Actor", "O", "O", "O", "O", "B-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin", "I-Origin"]}
{"sentence": "a animated movie about a prince that because of his pride and haughty attitude had a spell cast over him and must get the love of a woman to get rid of the spell", "tokens": ["a", "animated", "movie", "about", "a", "prince", "that", "because", "of", "his", "pride", "and", "haughty", "attitude", "had", "a", "spell", "cast", "over", "him", "and", "must", "get", "the", "love", "of", "a", "woman", "to", "get", "rid", "of", "the", "spell"], "ner_tags": ["O", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "name the sylvester stallone film about a underdog boxer who ends up going all the way to win it all", "tokens": ["name", "the", "sylvester", "stallone", "film", "about", "a", "underdog", "boxer", "who", "ends", "up", "going", "all", "the", "way", "to", "win", "it", "all"], "ner_tags": ["O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "the first animated movie based on the peanuts comic strip has aired on television every year since 1965 in december", "tokens": ["the", "first", "animated", "movie", "based", "on", "the", "peanuts", "comic", "strip", "has", "aired", "on", "television", "every", "year", "since", "1965", "in", "december"], "ner_tags": ["O", "O", "B-Genre", "O", "O", "O", "O", "O", "B-Genre", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "B-Year", "B-Plot", "I-Plot"]}
{"sentence": "i m trying to remember a gangster movie that was remade in the 80 s written by oliver stone", "tokens": ["i", "m", "trying", "to", "remember", "a", "gangster", "movie", "that", "was", "remade", "in", "the", "80", "s", "written", "by", "oliver", "stone"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Genre", "O", "O", "O", "B-Relationship", "O", "O", "B-Year", "O", "O", "O", "B-Director", "I-Director"]}
{"sentence": "what is the film that starred ray liotta as a young mobster trying to get into the family", "tokens": ["what", "is", "the", "film", "that", "starred", "ray", "liotta", "as", "a", "young", "mobster", "trying", "to", "get", "into", "the", "family"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "the girl in this disney hit loses her glass slipper", "tokens": ["the", "girl", "in", "this", "disney", "hit", "loses", "her", "glass", "slipper"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "what is this 2012 film starring clint eastwood as a retiring baseball scoot who goes on a final road trip with his daughter played by amy adams", "tokens": ["what", "is", "this", "2012", "film", "starring", "clint", "eastwood", "as", "a", "retiring", "baseball", "scoot", "who", "goes", "on", "a", "final", "road", "trip", "with", "his", "daughter", "played", "by", "amy", "adams"], "ner_tags": ["O", "O", "O", "B-Year", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "O", "O", "B-Actor", "I-Actor"]}
{"sentence": "i am thinking of a classic love story that stars barbra streisand and robert redford", "tokens": ["i", "am", "thinking", "of", "a", "classic", "love", "story", "that", "stars", "barbra", "streisand", "and", "robert", "redford"], "ner_tags": ["O", "O", "O", "O", "O", "B-Opinion", "B-Genre", "I-Genre", "O", "O", "B-Actor", "I-Actor", "O", "B-Actor", "I-Actor"]}
{"sentence": "this film directed by david fincher stars jesse eisenberg as the young man that founds facebook and how that event dissolves his closest friendship", "tokens": ["this", "film", "directed", "by", "david", "fincher", "stars", "jesse", "eisenberg", "as", "the", "young", "man", "that", "founds", "facebook", "and", "how", "that", "event", "dissolves", "his", "closest", "friendship"], "ner_tags": ["O", "O", "O", "O", "B-Director", "I-Director", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "peter jackson directed this smash hit starring jack black as a 1930 s film maker on a mission to make an unforgettable nature documentary who gets more than he bargained for", "tokens": ["peter", "jackson", "directed", "this", "smash", "hit", "starring", "jack", "black", "as", "a", "1930", "s", "film", "maker", "on", "a", "mission", "to", "make", "an", "unforgettable", "nature", "documentary", "who", "gets", "more", "than", "he", "bargained", "for"], "ner_tags": ["B-Director", "I-Director", "O", "O", "B-Opinion", "I-Opinion", "O", "B-Actor", "I-Actor", "O", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "name me a movie starring johnny depp about a skeleton that is the pumpkin king", "tokens": ["name", "me", "a", "movie", "starring", "johnny", "depp", "about", "a", "skeleton", "that", "is", "the", "pumpkin", "king"], "ner_tags": ["O", "O", "O", "O", "O", "B-Actor", "I-Actor", "O", "B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
{"sentence": "the aging patriarch of an organized crime dynasty transfers control of his clandestine empire to his reluctant son", "tokens": ["the", "aging", "patriarch", "of", "an", "organized", "crime", "dynasty", "transfers", "control", "of", "his", "clandestine", "empire", "to", "his", "reluctant", "son"], "ner_tags": ["B-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot", "I-Plot"]}
