#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RQ2: 生成数据质量评估
评估生成数据在自然度、准确性、语义一致性、多样性等方面的质量

使用方法:
python evaluation/framework/rq2_quality_assessment.py --dataset generated.json --output results/rq2/ [--original original.json]
"""

import os
import sys
import json
import argparse
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import random

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入评估指标
from evaluation.framework.metrics.quality_metrics import (
    evaluate_naturalness,
    evaluate_annotation_accuracy,
    evaluate_semantic_consistency,
    calculate_diversity_metrics,
    evaluate_balance_metrics
)

# 导入高级质量指标
from evaluation.framework.metrics.advanced_quality_metrics import (
    evaluate_linguistic_quality,
    evaluate_entity_coverage,
    evaluate_entity_quality,
    evaluate_cross_validation_quality
)

# 导入可视化模块
from evaluation.framework.utils.quality_visualization import (
    create_quality_dashboard,
    create_detailed_quality_report
)

def convert_numpy_types(obj):
    """转换numpy类型为Python原生类型，用于JSON序列化"""
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    else:
        return obj

def load_config():
    """加载RQ2配置"""
    config_path = Path(__file__).parent / "configs" / "rq2_config.json"
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def load_dataset(dataset_path: str) -> List[Dict]:
    """加载数据集"""
    with open(dataset_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def load_target_distribution(strategy_dir: Optional[str] = None) -> Optional[Dict[str, int]]:
    """加载目标分布 - 优先从balance_config.json中读取"""
    # 优先从balance_config.json读取目标分布
    balance_config_file = Path("src/gen_strat/balance_config.json")
    if balance_config_file.exists():
        try:
            with open(balance_config_file, 'r', encoding='utf-8') as f:
                balance_config = json.load(f)
                target_distribution = balance_config.get("entity_type_targets", {})
                if target_distribution:
                    print(f"[✓] 从balance_config.json加载目标分布：{len(target_distribution)}个实体类型")
                    return target_distribution
        except Exception as e:
            print(f"[警告] 从balance_config.json加载目标分布失败：{e}")
    
    # 备用方案：从strategy_dir或默认位置读取
    if strategy_dir:
        target_file = Path(strategy_dir) / "entity_target" / "target_distribution.json"
        possible_paths = [target_file]
    else:
        # 尝试从默认位置加载
        possible_paths = [
            Path("reproduce/entity_target/privacy_bench_target.json"),
            Path("src/gen_strat/target_distribution.json")
        ]
    
    for target_file in possible_paths:
        if target_file and target_file.exists():
            try:
                with open(target_file, 'r', encoding='utf-8') as f:
                        result = json.load(f)
                        print(f"[✓] 从{target_file}加载目标分布：{len(result)}个实体类型")
                        return result
            except Exception as e:
                    print(f"警告: 从{target_file}加载目标分布失败: {e}")
        
        print("[警告] 未找到任何目标分布文件")
        return None

def evaluate_naturalness_with_llm(dataset: List[Dict], sample_size: int = 100, 
                                 api_key_path: str = "auth/zhipu-api-key.json") -> Dict[str, Any]:
    """使用大语言模型评估自然度"""
    print("使用大语言模型评估自然度...")
    
    # 随机采样
    sampled_data = random.sample(dataset, min(sample_size, len(dataset)))
    
    try:
        # 尝试加载API密钥
        if Path(api_key_path).exists():
            with open(api_key_path, 'r', encoding='utf-8') as f:
                api_config = json.load(f)
            
            # 这里可以集成实际的LLM API调用
            # 由于API调用的复杂性，这里提供一个模拟实现
            naturalness_scores = simulate_llm_naturalness_evaluation(sampled_data)
        else:
            print("未找到API密钥，使用规则基础评估")
            naturalness_scores = evaluate_naturalness(sampled_data)
            
    except Exception as e:
        print(f"LLM评估失败，使用规则基础评估: {e}")
        naturalness_scores = evaluate_naturalness(sampled_data)
    
    return naturalness_scores

def simulate_llm_naturalness_evaluation(dataset: List[Dict]) -> Dict[str, Any]:
    """模拟LLM自然度评估"""
    scores = []
    detailed_results = []
    
    for item in dataset:
        text = item.get("text", "")
        
        # 模拟LLM评分（实际应该调用API）
        base_score = 7.0
        
        # 基于文本特征的模拟评分
        if len(text) < 5:
            base_score -= 2.0
        elif len(text) > 100:
            base_score -= 0.5
        
        # 检查语法合理性
        if not text.endswith(('。', '！', '？', '，', '；')):
            base_score -= 0.5
        
        # 检查实体标注合理性
        labels = item.get("label", [])
        for label in labels:
            entity_text = label.get("text", "")
            entity_type = label.get("type", "")
            
            # 简单的合理性检查
            if entity_type == "人名" and len(entity_text) > 5:
                base_score -= 0.3
            elif entity_type == "时间" and not any(char in entity_text for char in "年月日时分秒"):
                base_score -= 0.5
        
        # 添加随机变化模拟真实评估的不确定性
        score = base_score + random.uniform(-0.5, 0.5)
        score = max(1.0, min(10.0, score))
        
        scores.append(score)
        detailed_results.append({
            "text": text,
            "score": score,
            "length": len(text),
            "entity_count": len(labels)
        })
    
    return {
        "avg_score": np.mean(scores),
        "std_score": np.std(scores),
        "min_score": np.min(scores),
        "max_score": np.max(scores),
        "scores": scores,
        "detailed_results": detailed_results,
        "sample_size": len(dataset),
        "evaluation_method": "simulated_llm"
    }

def evaluate_entity_type_consistency(dataset: List[Dict]) -> Dict[str, Any]:
    """评估实体类型一致性"""
    entity_type_examples = {}
    inconsistencies = []
    
    for item_idx, item in enumerate(dataset):
        text = item.get("text", "")
        labels = item.get("label", [])
        
        for label in labels:
            entity_text = label.get("text", "")
            entity_type = label.get("type", "")
            
            if entity_type not in entity_type_examples:
                entity_type_examples[entity_type] = []
            
            entity_type_examples[entity_type].append({
                "text": entity_text,
                "context": text,
                "item_index": item_idx
            })
    
    # 检查一致性
    consistency_scores = {}
    
    for entity_type, examples in entity_type_examples.items():
        # 简单的一致性检查
        consistent_count = 0
        total_count = len(examples)
        
        for example in examples:
            if is_entity_type_consistent(example["text"], entity_type, example["context"]):
                consistent_count += 1
            else:
                inconsistencies.append({
                    "entity_text": example["text"],
                    "entity_type": entity_type,
                    "context": example["context"],
                    "item_index": example["item_index"]
                })
        
        consistency_scores[entity_type] = {
            "consistency_rate": consistent_count / total_count if total_count > 0 else 0,
            "total_examples": total_count,
            "consistent_examples": consistent_count,
            "inconsistent_examples": total_count - consistent_count
        }
    
    overall_consistency = sum(score["consistent_examples"] for score in consistency_scores.values()) / sum(score["total_examples"] for score in consistency_scores.values()) if consistency_scores else 0
    
    return {
        "overall_consistency": overall_consistency,
        "entity_type_consistency": consistency_scores,
        "inconsistencies": inconsistencies,
        "total_entities": sum(score["total_examples"] for score in consistency_scores.values()),
        "total_inconsistencies": len(inconsistencies)
    }

def is_entity_type_consistent(entity_text: str, entity_type: str, context: str) -> bool:
    """检查实体类型是否一致"""
    # 基于规则的一致性检查
    type_patterns = {
        "人名": {
            "positive": ["先生", "女士", "老师", "医生", "同学", "朋友"],
            "negative": ["公司", "医院", "学校", "市", "省", "年", "月", "日"]
        },
        "地名": {
            "positive": ["市", "省", "区", "县", "路", "街", "在", "到", "从", "位于"],
            "negative": ["先生", "女士", "公司", "年", "月", "日"]
        },
        "组织名": {
            "positive": ["公司", "学校", "医院", "银行", "政府", "部门", "机构"],
            "negative": ["先生", "女士", "市", "省", "年", "月", "日"]
        },
        "时间": {
            "positive": ["年", "月", "日", "时", "分", "秒", "今天", "明天", "昨天", "现在"],
            "negative": ["先生", "女士", "公司", "医院", "市", "省"]
        }
    }
    
    if entity_type not in type_patterns:
        return True  # 未知类型默认一致
    
    patterns = type_patterns[entity_type]
    
    # 检查正向模式
    positive_matches = sum(1 for pattern in patterns["positive"] if pattern in context)
    negative_matches = sum(1 for pattern in patterns["negative"] if pattern in context)
    
    # 简单的一致性判断
    if positive_matches > 0 and negative_matches == 0:
        return True
    elif positive_matches == 0 and negative_matches > 0:
        return False
    else:
        return True  # 中性情况默认一致

def evaluate_context_diversity(dataset: List[Dict], window_size: int = 5) -> Dict[str, Any]:
    """评估上下文多样性"""
    entity_contexts = {}
    
    for item in dataset:
        text = item.get("text", "")
        labels = item.get("label", [])
        
        for label in labels:
            entity_type = label.get("type", "")
            start = label.get("start", 0)
            end = label.get("end", 0)
            
            # 提取上下文
            context_start = max(0, start - window_size)
            context_end = min(len(text), end + window_size)
            
            left_context = text[context_start:start]
            right_context = text[end:context_end]
            context = left_context + "[ENTITY]" + right_context
            
            if entity_type not in entity_contexts:
                entity_contexts[entity_type] = []
            
            entity_contexts[entity_type].append(context)
    
    # 计算多样性
    diversity_scores = {}
    
    for entity_type, contexts in entity_contexts.items():
        unique_contexts = len(set(contexts))
        total_contexts = len(contexts)
        
        diversity_scores[entity_type] = {
            "diversity_ratio": unique_contexts / total_contexts if total_contexts > 0 else 0,
            "unique_contexts": unique_contexts,
            "total_contexts": total_contexts,
            "most_common_contexts": get_most_common_contexts(contexts, top_k=3)
        }
    
    overall_diversity = np.mean([score["diversity_ratio"] for score in diversity_scores.values()]) if diversity_scores else 0
    
    return {
        "overall_context_diversity": overall_diversity,
        "entity_type_diversity": diversity_scores,
        "total_entity_types": len(diversity_scores)
    }

def get_most_common_contexts(contexts: List[str], top_k: int = 3) -> List[Dict[str, Any]]:
    """获取最常见的上下文"""
    from collections import Counter
    
    context_counts = Counter(contexts)
    most_common = context_counts.most_common(top_k)
    
    return [{"context": context, "count": count} for context, count in most_common]

def create_quality_visualization(quality_results: Dict[str, Any], output_dir: Path):
    """创建质量评估可视化"""
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 1. 自然度分布图
    if "naturalness_evaluation" in quality_results:
        naturalness = quality_results["naturalness_evaluation"]
        scores = naturalness.get("scores", [])
        
        if scores:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            # 分布直方图
            ax1.hist(scores, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
            ax1.axvline(naturalness["avg_score"], color='red', linestyle='--', 
                       label=f'平均分: {naturalness["avg_score"]:.2f}')
            ax1.set_xlabel('自然度得分')
            ax1.set_ylabel('频次')
            ax1.set_title('自然度得分分布')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 箱线图
            ax2.boxplot(scores, vert=True)
            ax2.set_ylabel('自然度得分')
            ax2.set_title('自然度得分箱线图')
            ax2.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig(output_dir / "naturalness_distribution.png", dpi=300, bbox_inches='tight')
            plt.close()
    
    # 2. 质量指标雷达图
    metrics_data = []
    metric_names = []
    
    if "naturalness_evaluation" in quality_results:
        metrics_data.append(quality_results["naturalness_evaluation"]["avg_score"] / 10)
        metric_names.append("自然度")
    
    if "annotation_accuracy" in quality_results:
        metrics_data.append(quality_results["annotation_accuracy"]["boundary_accuracy"])
        metric_names.append("边界准确率")
        
        metrics_data.append(quality_results["annotation_accuracy"]["type_accuracy"])
        metric_names.append("类型准确率")
    
    if "semantic_consistency" in quality_results:
        metrics_data.append(quality_results["semantic_consistency"]["avg_consistency"])
        metric_names.append("语义一致性")
    
    if "diversity_metrics" in quality_results:
        diversity = quality_results["diversity_metrics"]
        metrics_data.append(diversity.get("vocabulary_diversity", 0))
        metric_names.append("词汇多样性")
        
        metrics_data.append(diversity.get("entity_diversity", 0))
        metric_names.append("实体多样性")
    
    if metrics_data and len(metrics_data) >= 3:
        create_radar_chart(metrics_data, metric_names, output_dir / "quality_radar.png")
    
    # 3. 实体类型一致性图
    if "entity_type_consistency" in quality_results:
        consistency_data = quality_results["entity_type_consistency"]["entity_type_consistency"]
        
        entity_types = list(consistency_data.keys())
        consistency_rates = [data["consistency_rate"] for data in consistency_data.values()]
        
        if entity_types:
            fig, ax = plt.subplots(figsize=(12, 6))
            
            bars = ax.bar(entity_types, consistency_rates, alpha=0.7, color='lightgreen')
            ax.set_xlabel('实体类型')
            ax.set_ylabel('一致性率')
            ax.set_title('各实体类型一致性率')
            ax.set_ylim(0, 1)
            
            # 添加数值标签
            for bar, rate in zip(bars, consistency_rates):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                       f'{rate:.2%}', ha='center', va='bottom')
            
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
            plt.savefig(output_dir / "entity_consistency.png", dpi=300, bbox_inches='tight')
            plt.close()

def create_radar_chart(values: List[float], labels: List[str], save_path: Path):
    """创建雷达图"""
    import math
    
    # 角度计算
    angles = [n / float(len(labels)) * 2 * math.pi for n in range(len(labels))]
    angles += angles[:1]  # 闭合图形
    
    values += values[:1]  # 闭合数据
    
    fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(projection='polar'))
    
    # 绘制雷达图
    ax.plot(angles, values, 'o-', linewidth=2, label='质量指标')
    ax.fill(angles, values, alpha=0.25)
    
    # 设置标签
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(labels)
    
    # 设置范围
    ax.set_ylim(0, 1)
    ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
    ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'])
    
    # 添加网格
    ax.grid(True)
    
    plt.title('数据质量综合评估', size=16, y=1.1)
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

def generate_quality_report(quality_results: Dict[str, Any], output_dir: Path, 
                           config: Dict[str, Any]):
    """生成质量评估报告"""
    report = {
        "evaluation_type": "RQ2: 生成数据质量评估",
        "evaluation_time": datetime.now().isoformat(),
        "config": config,
        "quality_results": convert_numpy_types(quality_results),
        "summary": generate_quality_summary(quality_results),
        "recommendations": generate_quality_recommendations(quality_results)
    }
    
    # 转换所有numpy类型
    report = convert_numpy_types(report)
    
    # 保存详细报告
    with open(output_dir / "rq2_evaluation_report.json", 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    # 生成文本摘要
    generate_quality_text_summary(report, output_dir)
    
    return report

def generate_quality_summary(quality_results: Dict[str, Any]) -> Dict[str, Any]:
    """生成质量评估摘要"""
    summary = {
        "overall_quality_score": 0.0,
        "quality_dimensions": {},
        "key_metrics": {},
        "quality_level": ""
    }
    
    scores = []
    
    # 自然度
    if "naturalness_evaluation" in quality_results:
        naturalness_score = quality_results["naturalness_evaluation"]["avg_score"] / 10
        scores.append(naturalness_score)
        summary["quality_dimensions"]["naturalness"] = {
            "score": naturalness_score,
            "raw_score": quality_results["naturalness_evaluation"]["avg_score"],
            "status": "优秀" if naturalness_score >= 0.8 else "良好" if naturalness_score >= 0.6 else "需改进"
        }
    
    # 准确性
    if "annotation_accuracy" in quality_results:
        accuracy = quality_results["annotation_accuracy"]
        boundary_acc = accuracy["boundary_accuracy"]
        type_acc = accuracy["type_accuracy"]
        avg_accuracy = (boundary_acc + type_acc) / 2
        
        scores.append(avg_accuracy)
        summary["quality_dimensions"]["accuracy"] = {
            "score": avg_accuracy,
            "boundary_accuracy": boundary_acc,
            "type_accuracy": type_acc,
            "status": "优秀" if avg_accuracy >= 0.9 else "良好" if avg_accuracy >= 0.7 else "需改进"
        }
    
    # 一致性
    if "semantic_consistency" in quality_results:
        consistency_score = quality_results["semantic_consistency"]["avg_consistency"]
        scores.append(consistency_score)
        summary["quality_dimensions"]["consistency"] = {
            "score": consistency_score,
            "status": "优秀" if consistency_score >= 0.8 else "良好" if consistency_score >= 0.6 else "需改进"
        }
    
    # 多样性
    if "diversity_metrics" in quality_results:
        diversity = quality_results["diversity_metrics"]
        vocab_div = diversity.get("vocabulary_diversity", 0)
        entity_div = diversity.get("entity_diversity", 0)
        avg_diversity = (vocab_div + entity_div) / 2
        
        scores.append(avg_diversity)
        summary["quality_dimensions"]["diversity"] = {
            "score": avg_diversity,
            "vocabulary_diversity": vocab_div,
            "entity_diversity": entity_div,
            "status": "优秀" if avg_diversity >= 0.7 else "良好" if avg_diversity >= 0.5 else "需改进"
        }
    
    # 计算总体质量得分
    if scores:
        summary["overall_quality_score"] = np.mean(scores)
        
        if summary["overall_quality_score"] >= 0.8:
            summary["quality_level"] = "优秀"
        elif summary["overall_quality_score"] >= 0.6:
            summary["quality_level"] = "良好"
        elif summary["overall_quality_score"] >= 0.4:
            summary["quality_level"] = "一般"
        else:
            summary["quality_level"] = "需改进"
    
    # 关键指标
    summary["key_metrics"] = {
        "total_dimensions_evaluated": len(summary["quality_dimensions"]),
        "excellent_dimensions": sum(1 for dim in summary["quality_dimensions"].values() if dim["status"] == "优秀"),
        "needs_improvement_dimensions": sum(1 for dim in summary["quality_dimensions"].values() if dim["status"] == "需改进")
    }
    
    return summary

def generate_quality_recommendations(quality_results: Dict[str, Any]) -> List[str]:
    """生成质量改进建议"""
    recommendations = []
    
    # 自然度建议
    if "naturalness_evaluation" in quality_results:
        avg_score = quality_results["naturalness_evaluation"]["avg_score"]
        if avg_score < 6.0:
            recommendations.append("自然度得分较低，建议优化句子生成模板和语言模型")
        elif avg_score < 8.0:
            recommendations.append("自然度有提升空间，可以增加更多样化的句子结构")
    
    # 准确性建议
    if "annotation_accuracy" in quality_results:
        accuracy = quality_results["annotation_accuracy"]
        if accuracy["boundary_accuracy"] < 0.9:
            recommendations.append("实体边界准确性需要改进，检查实体识别和标注逻辑")
        if accuracy["type_accuracy"] < 0.9:
            recommendations.append("实体类型准确性需要改进，优化实体分类规则")
    
    # 一致性建议
    if "semantic_consistency" in quality_results:
        consistency = quality_results["semantic_consistency"]["avg_consistency"]
        if consistency < 0.7:
            recommendations.append("语义一致性较低，建议加强上下文相关性检查")
    
    # 多样性建议
    if "diversity_metrics" in quality_results:
        diversity = quality_results["diversity_metrics"]
        if diversity.get("vocabulary_diversity", 0) < 0.5:
            recommendations.append("词汇多样性不足，建议扩大词汇库和同义词替换")
        if diversity.get("entity_diversity", 0) < 0.6:
            recommendations.append("实体多样性不足，建议增加更多实体变体")
    
    # 实体类型一致性建议
    if "entity_type_consistency" in quality_results:
        consistency_data = quality_results["entity_type_consistency"]
        if consistency_data["overall_consistency"] < 0.8:
            recommendations.append("实体类型一致性需要改进，检查实体类型分配规则")
        
        # 针对特定实体类型的建议
        entity_consistency = consistency_data.get("entity_type_consistency", {})
        for entity_type, data in entity_consistency.items():
            if data["consistency_rate"] < 0.7:
                recommendations.append(f"{entity_type}类型的一致性较低，需要优化该类型的生成规则")
    
    if not recommendations:
        recommendations.append("数据质量整体良好，建议继续保持当前的生成策略")
    
    return recommendations

def generate_quality_text_summary(report: Dict[str, Any], output_dir: Path):
    """生成质量评估文本摘要"""
    summary_lines = []
    summary_lines.append("=" * 60)
    summary_lines.append("RQ2: 生成数据质量评估报告")
    summary_lines.append("=" * 60)
    summary_lines.append(f"评估时间: {report['evaluation_time']}")
    summary_lines.append("")
    
    # 总体质量
    summary = report["summary"]
    summary_lines.append("总体质量评估:")
    summary_lines.append(f"  质量等级: {summary['quality_level']}")
    summary_lines.append(f"  综合得分: {summary['overall_quality_score']:.3f}")
    summary_lines.append("")
    
    # 各维度质量
    summary_lines.append("各质量维度评估:")
    for dimension, data in summary["quality_dimensions"].items():
        summary_lines.append(f"  {dimension}: {data['score']:.3f} ({data['status']})")
    summary_lines.append("")
    
    # 详细指标
    quality_results = report["quality_results"]
    
    if "naturalness_evaluation" in quality_results:
        naturalness = quality_results["naturalness_evaluation"]
        summary_lines.append("自然度评估:")
        summary_lines.append(f"  平均得分: {naturalness['avg_score']:.2f}/10")
        summary_lines.append(f"  得分范围: {naturalness['min_score']:.2f} - {naturalness['max_score']:.2f}")
        summary_lines.append(f"  评估样本: {naturalness['sample_size']} 条")
        summary_lines.append("")
    
    if "annotation_accuracy" in quality_results:
        accuracy = quality_results["annotation_accuracy"]
        summary_lines.append("标注准确性:")
        summary_lines.append(f"  边界准确率: {accuracy['boundary_accuracy']:.2%}")
        summary_lines.append(f"  类型准确率: {accuracy['type_accuracy']:.2%}")
        summary_lines.append(f"  有效实体: {accuracy['valid_entities']}/{accuracy['total_entities']}")
        summary_lines.append("")
    
    if "diversity_metrics" in quality_results:
        diversity = quality_results["diversity_metrics"]
        summary_lines.append("多样性指标:")
        summary_lines.append(f"  词汇多样性: {diversity.get('vocabulary_diversity', 0):.3f}")
        summary_lines.append(f"  句子多样性: {diversity.get('sentence_diversity', 0):.3f}")
        summary_lines.append(f"  实体多样性: {diversity.get('entity_diversity', 0):.3f}")
        summary_lines.append("")
    
    # 改进建议
    recommendations = report["recommendations"]
    summary_lines.append("改进建议:")
    for i, rec in enumerate(recommendations, 1):
        summary_lines.append(f"  {i}. {rec}")
    summary_lines.append("")
    
    summary_lines.append("=" * 60)
    
    # 保存摘要
    with open(output_dir / "rq2_summary.txt", 'w', encoding='utf-8') as f:
        f.write('\n'.join(summary_lines))

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="RQ2: 生成数据质量评估")
    parser.add_argument("--dataset", required=True, help="生成数据集路径")
    parser.add_argument("--output", required=True, help="输出目录")
    parser.add_argument("--original", help="原始数据集路径（可选，用于对比）")
    parser.add_argument("--strategy-dir", help="策略目录路径（用于加载目标分布）")
    parser.add_argument("--sample-size", type=int, default=100, help="自然度评估样本大小")
    parser.add_argument("--skip-naturalness", action="store_true", help="跳过自然度评估")
    parser.add_argument("--skip-visualization", action="store_true", help="跳过可视化生成")
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print("=" * 60)
    print("RQ2: 生成数据质量评估")
    print("=" * 60)
    print(f"数据集: {args.dataset}")
    print(f"输出目录: {output_dir}")
    if args.original:
        print(f"原始数据集: {args.original}")
    print()
    
    try:
        # 加载配置
        config = load_config()
        
        # 加载数据集
        print("加载数据集...")
        dataset = load_dataset(args.dataset)
        print(f"数据集包含 {len(dataset)} 条记录")
        
        original_dataset = None
        if args.original:
            original_dataset = load_dataset(args.original)
            print(f"原始数据集包含 {len(original_dataset)} 条记录")
        
        # 加载目标分布
        target_distribution = load_target_distribution(args.strategy_dir)
        if target_distribution:
            print(f"已加载目标分布，包含 {len(target_distribution)} 种实体类型")
        
        quality_results = {}
        
        # 1. 自然度评估
        if not args.skip_naturalness and config["rq2_quality_assessment"]["metrics"]["naturalness"]["enabled"]:
            print("评估自然度...")
            quality_results["naturalness_evaluation"] = evaluate_naturalness_with_llm(
                dataset, 
                sample_size=args.sample_size
            )
            print(f" 自然度评估完成，平均得分: {quality_results['naturalness_evaluation']['avg_score']:.2f}")
        
        # 2. 标注准确性评估
        if config["rq2_quality_assessment"]["metrics"]["annotation_accuracy"]["enabled"]:
            print("评估标注准确性...")
            quality_results["annotation_accuracy"] = evaluate_annotation_accuracy(dataset)
            print(f" 标注准确性评估完成，边界准确率: {quality_results['annotation_accuracy']['boundary_accuracy']:.2%}")
        
        # 3. 语义一致性评估
        if config["rq2_quality_assessment"]["metrics"]["semantic_consistency"]["enabled"]:
            print("评估语义一致性...")
            quality_results["semantic_consistency"] = evaluate_semantic_consistency(
                dataset,
                context_window=config["rq2_quality_assessment"]["metrics"]["semantic_consistency"]["context_window"]
            )
            print(f" 语义一致性评估完成，平均一致性: {quality_results['semantic_consistency']['avg_consistency']:.3f}")
        
        # 4. 多样性评估
        print("评估多样性...")
        quality_results["diversity_metrics"] = calculate_diversity_metrics(dataset)
        diversity = quality_results["diversity_metrics"]
        print(f" 多样性评估完成，词汇多样性: {diversity.get('vocabulary_diversity', 0):.3f}")
        
        # 5. 平衡性评估
        if target_distribution:
            print("评估平衡性...")
            quality_results["balance_metrics"] = evaluate_balance_metrics(dataset, target_distribution)
            balance = quality_results["balance_metrics"]
            print(f" 平衡性评估完成，平衡得分: {balance.get('balance_score', 0):.3f}")
        
        # 6. 实体类型一致性评估
        print("评估实体类型一致性...")
        quality_results["entity_type_consistency"] = evaluate_entity_type_consistency(dataset)
        consistency = quality_results["entity_type_consistency"]
        print(f" 实体类型一致性评估完成，总体一致性: {consistency['overall_consistency']:.3f}")
        
        # 7. 上下文多样性评估
        print("评估上下文多样性...")
        quality_results["context_diversity"] = evaluate_context_diversity(dataset)
        context_div = quality_results["context_diversity"]
        print(f"✓ 上下文多样性评估完成，总体多样性: {context_div['overall_context_diversity']:.3f}")
        
        # 8. 语言学质量评估
        print("评估语言学质量...")
        quality_results["linguistic_quality"] = evaluate_linguistic_quality(dataset)
        linguistic = quality_results["linguistic_quality"]
        print(f"✓ 语言学质量评估完成，语法得分: {linguistic['grammar_score']:.3f}")
        
        # 9. 实体质量评估
        print("评估实体质量...")
        quality_results["entity_quality"] = evaluate_entity_quality(dataset)
        entity_quality = quality_results["entity_quality"]
        problem_count = len(entity_quality.get("problematic_entities", []))
        print(f"✓ 实体质量评估完成，发现问题实体: {problem_count} 个")
        
        # 10. 实体覆盖率评估
        if target_distribution:
            print("评估实体覆盖率...")
            quality_results["entity_coverage"] = evaluate_entity_coverage(dataset, target_distribution)
            coverage = quality_results["entity_coverage"]
            print(f"✓ 实体覆盖率评估完成，类型覆盖率: {coverage['type_coverage']:.2%}")
        
        # 11. 交叉验证质量评估
        if len(dataset) >= 50:  # 只有数据集足够大时才进行交叉验证
            print("进行交叉验证质量评估...")
            quality_results["cross_validation"] = evaluate_cross_validation_quality(dataset, fold_count=5)
            cv_results = quality_results["cross_validation"]
            overall_consistency = cv_results["consistency_analysis"]["overall_consistency"]
            print(f"✓ 交叉验证评估完成，总体一致性: {overall_consistency:.3f}")
        
        # 生成可视化
        if not args.skip_visualization and config["rq2_quality_assessment"]["evaluation_methods"]["automatic_scoring"]:
            print("生成可视化图表...")
            
            # 生成质量仪表板
            create_quality_dashboard(quality_results, output_dir)
            
            # 生成详细质量报告图表
            create_detailed_quality_report(quality_results, output_dir)
            
            # 生成原有的可视化图表
            create_quality_visualization(quality_results, output_dir)
            
            print("✓ 可视化图表已生成")
            print(f"  - 质量仪表板: {output_dir}/quality_dashboard.png")
            print(f"  - 详细分析图表: {output_dir}/*_analysis.png")
        
        # 生成报告
        print("生成质量评估报告...")
        report = generate_quality_report(quality_results, output_dir, config)
        
        print(f"\n RQ2质量评估完成！结果已保存到: {output_dir}")
        print(f"  - 详细报告: {output_dir}/rq2_evaluation_report.json")
        print(f"  - 文本摘要: {output_dir}/rq2_summary.txt")
        if not args.skip_visualization:
            print(f"  - 可视化图表: {output_dir}/*.png")
        
        # 显示质量等级
        summary = report["summary"]
        print(f"\n 质量评估结果:")
        print(f"  质量等级: {summary['quality_level']}")
        print(f"  综合得分: {summary['overall_quality_score']:.3f}")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
