{"prompts": ["Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Title.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Title.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Title.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Title.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Title.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Title.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Title.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Title.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Title.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Title.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Viewers' Rating.\nA Viewers' Rating term is a brief recommendation/popularity level, e.g. \"must see\".", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Viewers' Rating.\nA Viewers' Rating term is a brief recommendation/popularity level, e.g. \"must see\".", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Viewers' Rating.\nA Viewers' Rating term is a rating score, e.g. \"4 out of 5 stars\".", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Viewers' Rating.\nA Viewers' Rating term is a brief recommendation/popularity level, e.g. \"must see\".", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Viewers' Rating.\nA Viewers' Rating term is a brief recommendation/popularity level, e.g. \"must see\".", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Viewers' Rating.\nA Viewers' Rating term is a rating score, e.g. \"4 out of 5 stars\".", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Viewers' Rating.\nA Viewers' Rating term is a brief recommendation/popularity level, e.g. \"must see\".", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Viewers' Rating.\nA Viewers' Rating term is a rating score, e.g. \"4 out of 5 stars\".", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Viewers' Rating.\nA Viewers' Rating term is a brief recommendation/popularity level, e.g. \"must see\".", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Viewers' Rating.\nA Viewers' Rating term is a brief recommendation/popularity level, e.g. \"must see\".", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Year.\nA Year term is a year or a year range.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Year.\nA Year term is a year or a year range.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Year.\nA Year term is a year or a year range.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Year.\nA Year term is a year or a year range.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Year.\nA Year term is a year or a year range.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Year.\nA Year term is a year or a year range.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Year.\nA Year term is a year or a year range.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Year.\nA Year term is a year or a year range.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Year.\nA Year term is a year or a year range.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Year.\nA Year term is a year or a year range.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse named entities that can be categorized as Genre.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse named entities that can be categorized as Genre.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse named entities that can be categorized as Genre.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse named entities that can be categorized as Genre.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse named entities that can be categorized as Genre.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse named entities that can be categorized as Genre.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse named entities that can be categorized as Genre.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse named entities that can be categorized as Genre.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse named entities that can be categorized as Genre.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse named entities that can be categorized as Genre.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Director.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Director.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Director.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Director.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Director.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Director.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Director.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Director.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Director.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Director.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse named entities that can be categorized as MPAA Rating.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse named entities that can be categorized as MPAA Rating.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse named entities that can be categorized as MPAA Rating.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse named entities that can be categorized as MPAA Rating.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse named entities that can be categorized as MPAA Rating.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse named entities that can be categorized as MPAA Rating.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse named entities that can be categorized as MPAA Rating.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse named entities that can be categorized as MPAA Rating.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse named entities that can be categorized as MPAA Rating.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse named entities that can be categorized as MPAA Rating.", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Plot.\nA Plot named entity is a movie theme or plot element.", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Plot.\nA Plot named entity is a movie theme or plot element.", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Plot.\nA Plot named entity is a movie theme or plot element, e.g. \"bounty hunter\".", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Plot.\nA Plot named entity is a movie theme or plot element, e.g. \"bounty hunter\".", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Plot.\nA Plot named entity is a movie theme or plot element.", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Plot.\nA Plot named entity is a movie theme or plot element.", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Plot.\nA Plot named entity is a movie theme or plot element.", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Plot.\nA Plot named entity is a movie theme or plot element, e.g. \"bounty hunter\".", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Plot.\nA Plot named entity is a movie theme or plot element, e.g. \"bounty hunter\".", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Plot.\nA Plot named entity is a movie theme or plot element.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Actor.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Actor.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Actor.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Actor.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Actor.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Actor.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Actor.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Actor.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Actor.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Actor.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse terms that can be categorized as Trailer.\nA Trailer term indicates a segment or a clip from a movie.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse terms that can be categorized as Trailer.\nA Trailer term indicates a segment or a clip from a movie.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse terms that can be categorized as Trailer.\nA Trailer term indicates a segment or a clip from a movie.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse terms that can be categorized as Trailer.\nA Trailer term indicates a segment or a clip from a movie.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse terms that can be categorized as Trailer.\nA Trailer term indicates a segment or a clip from a movie.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse terms that can be categorized as Trailer.\nA Trailer term indicates a segment or a clip from a movie.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse terms that can be categorized as Trailer.\nA Trailer term indicates a segment or a clip from a movie.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse terms that can be categorized as Trailer.\nA Trailer term indicates a segment or a clip from a movie.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse terms that can be categorized as Trailer.\nA Trailer term indicates a segment or a clip from a movie.", "Suppose you are a user of a movie dialog system. Please generate 15 diverse terms that can be categorized as Trailer.\nA Trailer term indicates a segment or a clip from a movie.", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Song.", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Song.", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Song.", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Song.", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Song.", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Song.", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Song.", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Song.", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Song.", "Suppose you are a user of a movie dialog system. Please generate 30 diverse named entities that can be categorized as Song.", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Review.\nA Review term is a detailed movie comment, e.g. \"funniest of all time\".", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Review.\nA Review term is a detailed movie comment, e.g. \"funniest of all time\".", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Review.\nA Review term is a detailed movie comment, e.g. \"funniest of all time\".", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Review.\nA Review term is a detailed movie comment, e.g. \"funniest of all time\".", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Review.\nA Review term is a detailed movie comment, e.g. \"funniest of all time\".", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Review.\nA Review term is a detailed movie comment, e.g. \"funniest of all time\".", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Review.\nA Review term is a detailed movie comment, e.g. \"funniest of all time\".", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Review.\nA Review term is a detailed movie comment, e.g. \"funniest of all time\".", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Review.\nA Review term is a detailed movie comment, e.g. \"funniest of all time\".", "Suppose you are a user of a movie dialog system. Please generate 45 diverse terms that can be categorized as Review.\nA Review term is a detailed movie comment, e.g. \"funniest of all time\".", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Character.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Character.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Character.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Character.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Character.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Character.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Character.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Character.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Character.", "Suppose you are a user of a movie dialog system. Please generate 75 diverse named entities that can be categorized as Character."]}