# NER数据生成系统

## 概述

本系统用于生成命名实体识别（NER）数据集，基于以下四个组件：
1. **目标数量配置**：从 `reproduce/entity_target` 目录获取每个实体类型的目标数量
2. **句子多样化**：从 `reproduce/sen_diversity/sen_diversify_value.json` 获取句子多样化属性和值
3. **实体多样化**：从 `reproduce/entity_diversity` 最新时间戳目录获取实体池
4. **示例数据**：从原始数据集和配置文件加载示例句子

## 文件结构

```
src/synth_data/
├── ner_data_generation.py    # 主生成脚本
├── ner_config.json          # 配置文件
└── README.md                # 说明文档
```

## 配置文件说明

### ner_config.json

```json
{
    "use_entity_diversity": true,        // 是否使用实体多样化
    "max_entities_per_sentence": 2,      // 每句最大实体数
    "sentence_length_range": [20, 50],   // 句子长度范围
    "api_retry_times": 3,                // API重试次数
    "api_wait_time": 1.5,                // API调用间隔
    "example_sentences": [...]           // 示例句子列表
}
```

## 使用方法

### 1. 准备数据

确保以下文件/目录存在：
- `reproduce/entity_target/` 目录下有目标数量配置文件
- `reproduce/sen_diversity/sen_diversify_value.json` 句子多样化配置
- `reproduce/entity_diversity/` 目录下有实体多样化数据
- `auth/zhipu-api-key.json` API密钥文件

### 2. 运行生成

```bash
cd src/synth_data
python ner_data_generation.py
```

### 3. 输出结果

生成的数据将保存在 `synth_dataset/origin/ner_dataset_时间戳.json` 文件中。

## 数据格式

生成的JSON文件包含以下格式的数据：

```json
[
    {
        "text": "张先生今年35岁，是一名工程师。",
        "label": [
            {
                "entity": "张先生",
                "start_idx": 0,
                "end_idx": 3,
                "type": "姓名"
            },
            {
                "entity": "35岁",
                "start_idx": 4,
                "end_idx": 7,
                "type": "年龄"
            }
        ]
    }
]
```

## 功能特性

### 1. 句子生成
- 基于句子多样化属性（文本内容、语言风格等）生成多样化句子
- 使用示例句子作为参考
- 控制句子长度和质量

### 2. 实体标注
- 自动在生成的句子中标注实体位置
- 验证标注的正确性
- 支持多种实体类型

### 3. 质量控制
- API调用失败重试机制
- 标注验证和过滤
- 详细的生成统计信息

### 4. 配置灵活
- 可通过配置文件控制各种参数
- 支持实体多样化开关
- 可调整句子长度和实体数量

## 注意事项

1. **API限制**：系统使用智谱API，请注意调用频率限制
2. **数据依赖**：确保所有依赖的数据文件都已正确生成
3. **输出验证**：生成后请检查输出文件的质量和格式
4. **配置调整**：可根据需要调整配置文件中的参数

## 故障排除

### 常见问题

1. **API调用失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 调整重试参数

2. **数据文件缺失**
   - 确保先运行了句子多样化和实体多样化生成脚本
   - 检查文件路径是否正确

3. **生成质量不佳**
   - 调整句子长度范围
   - 增加示例句子数量
   - 优化提示词模板

## 扩展功能

系统支持以下扩展：
- 添加新的句子多样化属性
- 支持更多实体类型
- 集成其他语言模型API
- 添加数据质量评估功能 