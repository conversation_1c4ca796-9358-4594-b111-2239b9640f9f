{"国籍": [{"text": "日本游客在京都的传统寺庙里拍照留念。", "label": [{"entity": "日本", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "美国工程师正在参与上海某科技项目的开发工作。", "label": [{"entity": "美国", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "法国厨师在巴黎的餐厅里烹饪精致的法式大餐。", "label": [{"entity": "法国", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "澳大利亚学生在北京语言大学学习中文。", "label": [{"entity": "澳大利亚", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "英国首相在伦敦的唐宁街发表重要讲话。", "label": [{"entity": "英国", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "韩国明星在首尔的音乐节上表演了流行歌曲。", "label": [{"entity": "韩国", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "俄罗斯宇航员在国际空间站进行了科学实验。", "label": [{"entity": "俄罗斯", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "德国教授在慕尼黑大学讲授哲学课程。", "label": [{"entity": "德国", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "加拿大游客在温哥华的海滩上享受阳光。", "label": [{"entity": "加拿大", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "意大利艺术家在罗马的广场上创作壁画。", "label": [{"entity": "意大利", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "这位法国游客在巴黎铁塔下拍了许多照片。", "label": [{"entity": "法国", "start_idx": 2, "end_idx": 4, "type": "国籍"}]}, {"text": "那位美国教授正在给学生们讲解量子力学。", "label": [{"entity": "美国", "start_idx": 3, "end_idx": 4, "type": "国籍"}]}, {"text": "日本公司决定在中国投资建设新的生产线。", "label": [{"entity": "日本", "start_idx": 0, "end_idx": 1, "type": "国籍"}]}, {"text": "澳大利亚的留学生对中国的传统文化很感兴趣。", "label": [{"entity": "澳大利亚", "start_idx": 0, "end_idx": 3, "type": "国籍"}, {"entity": "中国", "start_idx": 7, "end_idx": 9, "type": "国籍"}]}, {"text": "德国工程师为这座桥梁的设计提出了宝贵建议。", "label": [{"entity": "德国", "start_idx": 0, "end_idx": 1, "type": "国籍"}]}, {"text": "韩国厨师做的泡菜深受当地居民喜爱。", "label": [{"entity": "韩国", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}], "姓名": [{"text": "张伟今天早上准时到达了公司会议室。", "label": [{"entity": "张伟", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "李娜在去年的羽毛球比赛中获得了冠军。", "label": [{"entity": "李娜", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "王芳和小李一起参加了周末的志愿者活动。", "label": [{"entity": "王芳", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "小李", "start_idx": 3, "end_idx": 5, "type": "姓名"}]}, {"text": "刘洋的数学成绩在班级里一直名列前茅。", "label": [{"entity": "刘洋", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "陈明向老板递交了辞职信，决定去创业。", "label": [{"entity": "陈明", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "赵静昨天去看了周杰伦的演唱会。", "label": [{"entity": "赵静", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "周杰伦", "start_idx": 8, "end_idx": 10, "type": "姓名"}]}, {"text": "孙悦的新书在各大书店都卖得很好。", "label": [{"entity": "孙悦", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "周杰在公司的年度评优中获得了最佳员工奖。", "label": [{"entity": "周杰", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "吴磊和女友计划下个月去巴黎旅行。", "label": [{"entity": "吴磊", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "黄磊在大学里担任了学生会主席的职务。", "label": [{"entity": "黄磊", "start_idx": 0, "end_idx": 1, "type": "姓名"}]}, {"text": "李娜在去年赢得了法国网球公开赛冠军。", "label": [{"entity": "李娜", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "王芳今天去超市购买了许多生活必需品。", "label": [{"entity": "王芳", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "张伟医生为病人进行了详细的心脏检查。", "label": [{"entity": "张伟", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "刘洋教授在学术会议上发表了重要演讲。", "label": [{"entity": "刘洋", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "陈晓在公司的年会上获得了优秀员工奖。", "label": [{"entity": "陈晓", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "赵明和小红在周末一起去郊外野餐了。", "label": [{"entity": "赵明", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "小红", "start_idx": 3, "end_idx": 5, "type": "姓名"}]}, {"text": "孙悦的新书在各大书店已经上架销售。", "label": [{"entity": "孙悦", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "周杰导演的最新电影在电影节上大放异彩。", "label": [{"entity": "周杰", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "吴磊在篮球比赛中投中了关键的三分球。", "label": [{"entity": "吴磊", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}], "年龄": [{"text": "王明今年36岁，已经是公司的技术总监了。", "label": [{"entity": "36", "start_idx": 4, "end_idx": 6, "type": "年龄"}]}, {"text": "李华的孙子今年8岁，正在上小学二年级。", "label": [{"entity": "8岁", "start_idx": 6, "end_idx": 8, "type": "年龄"}]}, {"text": "张阿姨已经68岁了，每天坚持晨练。", "label": [{"entity": "68岁", "start_idx": 5, "end_idx": 7, "type": "年龄"}]}, {"text": "小芳的妹妹才3岁，刚学会走路。", "label": [{"entity": "3岁", "start_idx": 6, "end_idx": 8, "type": "年龄"}]}, {"text": "陈老师教了45年的书，今年已经退休了。", "label": [{"entity": "45年", "start_idx": 3, "end_idx": 5, "type": "年龄"}, {"entity": "今年", "start_idx": 8, "end_idx": 10, "type": "年龄"}]}, {"text": "刘叔叔今年52岁，正在筹备自己的50岁生日派对。", "label": [{"entity": "52岁", "start_idx": 4, "end_idx": 7, "type": "年龄"}, {"entity": "50岁", "start_idx": 16, "end_idx": 19, "type": "年龄"}]}, {"text": "我女儿今年15岁，马上就要中考了。", "label": [{"entity": "15岁", "start_idx": 6, "end_idx": 8, "type": "年龄"}]}, {"text": "那位老人今年87岁，依然精神矍铄。", "label": [{"entity": "87岁", "start_idx": 7, "end_idx": 9, "type": "年龄"}]}, {"text": "小王刚满21岁，今天是他法定饮酒的第一天。", "label": [{"entity": "21岁", "start_idx": 3, "end_idx": 5, "type": "年龄"}]}, {"text": "我的邻居小明今年29岁，刚刚结婚。", "label": [{"entity": "29", "start_idx": 8, "end_idx": 10, "type": "年龄"}]}, {"text": "这位3岁的孩子已经能背诵整首《静夜思》了。", "label": [{"entity": "3岁", "start_idx": 2, "end_idx": 3, "type": "年龄"}]}, {"text": "那个7岁的女孩正在学习弹奏钢琴。", "label": [{"entity": "7岁", "start_idx": 4, "end_idx": 5, "type": "年龄"}]}, {"text": "他今年刚满18岁，就可以合法考取驾照了。", "label": [{"entity": "18岁", "start_idx": 6, "end_idx": 8, "type": "年龄"}]}, {"text": "医生建议65岁的张先生定期进行体检。", "label": [{"entity": "65岁", "start_idx": 5, "end_idx": 7, "type": "年龄"}]}, {"text": "小明的孩子今年只有1岁，刚刚学会走路。", "label": [{"entity": "1岁", "start_idx": 8, "end_idx": 10, "type": "年龄"}]}, {"text": "她将在30岁生日那天举办一场惊喜派对。", "label": [{"entity": "30岁", "start_idx": 5, "end_idx": 7, "type": "年龄"}]}, {"text": "那位90岁的老奶奶依然精神矍铄，每天坚持锻炼。", "label": [{"entity": "90岁", "start_idx": 3, "end_idx": 5, "type": "年龄"}]}, {"text": "公司规定，员工必须年满25岁才能申请晋升。", "label": [{"entity": "25岁", "start_idx": 10, "end_idx": 12, "type": "年龄"}]}], "性别": [{"text": "张伟是一位男性医生，每天为患者提供专业的医疗建议。", "label": [{"entity": "男性", "start_idx": 5, "end_idx": 7, "type": "性别"}]}, {"text": "李娜是女性运动员，她在奥运会上获得了金牌。", "label": [{"entity": "女性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "王强这个男性工程师，负责设计城市的主要桥梁。", "label": [{"entity": "男性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "刘芳作为女性教师，深受学生们的喜爱和尊敬。", "label": [{"entity": "女性", "start_idx": 4, "end_idx": 6, "type": "性别"}]}, {"text": "陈明是男性律师，擅长处理复杂的商业纠纷案件。", "label": [{"entity": "男性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "赵雪这位女性科学家，在生物医学领域有重要发现。", "label": [{"entity": "女性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "周杰这位男性导演，执导的电影多次获得国际奖项。", "label": [{"entity": "男性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "孙婷是女性设计师，她的作品充满了现代艺术气息。", "label": [{"entity": "女性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "吴刚这位男性消防员，在火灾现场英勇救出了多名儿童。", "label": [{"entity": "男性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "郑丽作为女性企业家，创办的公司在行业内取得了巨大成功。", "label": [{"entity": "女性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "张先生今天早上准时到达了会议室。", "label": [{"entity": "先生", "start_idx": 1, "end_idx": 2, "type": "性别"}]}, {"text": "李女士在图书馆里安静地阅读了一整本书。", "label": [{"entity": "女士", "start_idx": 1, "end_idx": 2, "type": "性别"}]}, {"text": "王小姐的生日派对将在下周末举行。", "label": [{"entity": "小姐", "start_idx": 2, "end_idx": 3, "type": "性别"}]}, {"text": "孙女士在超市里挑选了新鲜的蔬菜水果。", "label": [{"entity": "女士", "start_idx": 2, "end_idx": 3, "type": "性别"}]}, {"text": "刘先生的公司明天要去参加行业展会。", "label": [{"entity": "刘", "start_idx": 0, "end_idx": 1, "type": "性别"}]}, {"text": "周女士的花园里种满了五颜六色的花朵。", "label": [{"entity": "女士", "start_idx": 1, "end_idx": 3, "type": "性别"}]}, {"text": "李女士今天早上来公司上班，她的工作效率一直很高。", "label": [{"entity": "女士", "start_idx": 1, "end_idx": 2, "type": "性别"}]}, {"text": "张先生昨天去健身房锻炼，他坚持每周三次的训练计划。", "label": [{"entity": "他", "start_idx": 7, "end_idx": 8, "type": "性别"}]}], "职业": [{"text": "医生为患者进行了详细的身体检查。", "label": [{"entity": "医生", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "医生正在为病人进行详细的身体检查。", "label": [{"entity": "医生", "start_idx": 0, "end_idx": 2, "type": "职业"}, {"entity": "病人", "start_idx": 6, "end_idx": 8, "type": "职业"}]}, {"text": "教师今天给学生们布置了新的作业。", "label": [{"entity": "教师", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "工程师正在设计一座桥梁的结构图。", "label": [{"entity": "工程师", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "厨师正在厨房里准备晚餐的菜肴。", "label": [{"entity": "厨师", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "消防员迅速赶往火灾现场进行救援。", "label": [{"entity": "消防员", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "警察正在调查一起交通事故的原因。", "label": [{"entity": "警察", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "记者正在采访一位重要的政治人物。", "label": [{"entity": "记者", "start_idx": 0, "end_idx": 2, "type": "职业"}, {"entity": "政治人物", "start_idx": 10, "end_idx": 13, "type": "职业"}]}, {"text": "建筑师正在展示他设计的最新建筑方案。", "label": [{"entity": "建筑师", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "医生为患者进行了详细的身体检查。", "label": [{"entity": "医生", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "医生为患者进行了详细的身体检查。", "label": [{"entity": "医生", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "工程师正在调试新研发的机器设备。", "label": [{"entity": "工程师", "start_idx": 0, "end_idx": 2, "type": "职业"}]}], "民族": [{"text": "藏族歌手在高原上唱起了悠扬的藏歌。", "label": [{"entity": "藏族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "维吾尔族的手工艺品色彩鲜艳，极具民族特色。", "label": [{"entity": "维吾尔族", "start_idx": 0, "end_idx": 3, "type": "民族"}]}, {"text": "壮族人民在节日里跳起了欢快的铜鼓舞。", "label": [{"entity": "壮族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "蒙古族牧民在草原上过着游牧的生活。", "label": [{"entity": "蒙古族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "朝鲜族的长鼓舞表演吸引了众多观众。", "label": [{"entity": "朝鲜族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "傣族的泼水节是当地最盛大的传统节日。", "label": [{"entity": "傣族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "苗族姑娘穿着华丽的银饰参加婚礼。", "label": [{"entity": "苗族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "彝族火把节上，人们点燃火把庆祝丰收。", "label": [{"entity": "彝族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "满族的传统服饰旗袍至今仍广受欢迎。", "label": [{"entity": "满族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "土家族的摆手舞展现了独特的民族风情。", "label": [{"entity": "土家族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "藏族同胞在布达拉宫前举行了盛大的节日庆典。", "label": [{"entity": "藏族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "维吾尔族音乐家在音乐会上演奏了传统乐器热瓦普。", "label": [{"entity": "维吾尔族", "start_idx": 0, "end_idx": 3, "type": "民族"}]}, {"text": "朝鲜族农民在延边地区的稻田里辛勤劳作。", "label": [{"entity": "朝鲜族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "壮族姑娘穿着民族服饰参加了山歌比赛。", "label": [{"entity": "壮族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "蒙古族牧民在草原上放牧着成群的牛羊。", "label": [{"entity": "蒙古族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "满族老人在故宫的角楼前讲述着清朝的历史故事。", "label": [{"entity": "满族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "傣族村寨的泼水节吸引了众多游客参与。", "label": [{"entity": "傣族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "回族商人经营的清真餐厅生意非常红火。", "label": [{"entity": "回族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "彝族青年在火把节上跳起了欢快的舞蹈。", "label": [{"entity": "彝族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}], "教育背景": [{"text": "他的教育背景是北京大学计算机科学与技术专业本科。", "label": [{"entity": "北京大学计算机科学与技术专业本科", "start_idx": 8, "end_idx": 25, "type": "教育背景"}]}, {"text": "她拥有清华大学工商管理硕士学位。", "label": [{"entity": "清华大学工商管理硕士学位", "start_idx": 5, "end_idx": 10, "type": "教育背景"}]}, {"text": "他的教育背景显示他是复旦大学历史学专业博士。", "label": [{"entity": "复旦大学历史学专业博士", "start_idx": 13, "end_idx": 25, "type": "教育背景"}]}, {"text": "她毕业于上海交通大学电子信息工程专业。", "label": [{"entity": "上海交通大学电子信息工程专业", "start_idx": 6, "end_idx": 19, "type": "教育背景"}]}, {"text": "他的教育背景是浙江大学法学专业硕士。", "label": [{"entity": "浙江大学法学专业硕士", "start_idx": 8, "end_idx": 20, "type": "教育背景"}]}, {"text": "她拥有中国人民大学经济学学士学位。", "label": [{"entity": "中国人民大学", "start_idx": 9, "end_idx": 12, "type": "教育背景"}, {"entity": "经济学学士学位", "start_idx": 13, "end_idx": 18, "type": "教育背景"}]}, {"text": "他的教育背景是南京大学化学工程专业本科。", "label": [{"entity": "南京大学化学工程专业本科", "start_idx": 8, "end_idx": 20, "type": "教育背景"}]}, {"text": "她毕业于武汉大学新闻传播学专业硕士。", "label": [{"entity": "武汉大学", "start_idx": 6, "end_idx": 9, "type": "教育背景"}, {"entity": "新闻传播学专业", "start_idx": 10, "end_idx": 15, "type": "教育背景"}, {"entity": "硕士", "start_idx": 16, "end_idx": 18, "type": "教育背景"}]}, {"text": "他的教育背景是同济大学土木工程专业博士。", "label": [{"entity": "同济大学土木工程专业博士", "start_idx": 8, "end_idx": 21, "type": "教育背景"}]}, {"text": "她拥有北京师范大学教育学硕士学位。", "label": [{"entity": "北京师范大学教育学硕士学位", "start_idx": 6, "end_idx": 16, "type": "教育背景"}]}, {"text": "王明拥有北京大学计算机科学专业的学士学位。", "label": [{"entity": "北京大学计算机科学专业", "start_idx": 5, "end_idx": 11, "type": "教育背景"}, {"entity": "学士学位", "start_idx": 12, "end_idx": 15, "type": "教育背景"}]}, {"text": "李华毕业于清华大学建筑学硕士学位。", "label": [{"entity": "清华大学建筑学硕士学位", "start_idx": 5, "end_idx": 11, "type": "教育背景"}]}, {"text": "张伟在复旦大学完成了工商管理博士学位的学习。", "label": [{"entity": "复旦大学", "start_idx": 5, "end_idx": 7, "type": "教育背景"}, {"entity": "工商管理博士学位", "start_idx": 8, "end_idx": 12, "type": "教育背景"}]}, {"text": "陈静持有上海交通大学电气工程专业的专科文凭。", "label": [{"entity": "上海交通大学", "start_idx": 8, "end_idx": 12, "type": "教育背景"}, {"entity": "电气工程专业", "start_idx": 13, "end_idx": 17, "type": "教育背景"}, {"entity": "专科文凭", "start_idx": 18, "end_idx": 21, "type": "教育背景"}]}, {"text": "刘芳是浙江大学化学工程专业的本科毕业生。", "label": [{"entity": "浙江大学化学工程专业", "start_idx": 5, "end_idx": 10, "type": "教育背景"}, {"entity": "本科毕业生", "start_idx": 11, "end_idx": 14, "type": "教育背景"}]}, {"text": "赵强获得了南京大学历史学专业的硕士学位证书。", "label": [{"entity": "南京大学历史学专业的硕士学位证书", "start_idx": 6, "end_idx": 23, "type": "教育背景"}]}, {"text": "杨丽在武汉大学修读了英语语言文学专业的博士学位。", "label": [{"entity": "武汉大学", "start_idx": 4, "end_idx": 7, "type": "教育背景"}, {"entity": "英语语言文学专业", "start_idx": 8, "end_idx": 13, "type": "教育背景"}, {"entity": "博士学位", "start_idx": 14, "end_idx": 17, "type": "教育背景"}]}], "婚姻状况": [{"text": "小王目前是未婚状态，他正在努力寻找自己的另一半。", "label": [{"entity": "未婚", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "李女士的婚姻状况是已婚，她与丈夫已经携手走过了十年。", "label": [{"entity": "已婚", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "张先生在离婚后，现在的婚姻状况是单身。", "label": [{"entity": "单身", "start_idx": 12, "end_idx": 14, "type": "婚姻状况"}]}, {"text": "赵阿姨的婚姻状况是离异，她独自抚养着两个孩子。", "label": [{"entity": "离异", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "刘小姐的婚姻状况是未婚，她专注于自己的事业。", "label": [{"entity": "未婚", "start_idx": 7, "end_idx": 9, "type": "婚姻状况"}]}, {"text": "王大哥的婚姻状况是丧偶，他独自承担起家庭的重担。", "label": [{"entity": "丧偶", "start_idx": 7, "end_idx": 9, "type": "婚姻状况"}]}, {"text": "陈女士的婚姻状况是已婚，她与丈夫共同经营着一家小店。", "label": [{"entity": "已婚", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "杨先生的婚姻状况是单身，他享受着自由自在的生活。", "label": [{"entity": "单身", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "周女士的婚姻状况是离异，她决定重新开始新的生活。", "label": [{"entity": "离异", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "孙小姐的婚姻状况是未婚，她计划在未来几年结婚。", "label": [{"entity": "未婚", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "张三的婚姻状况是已婚，他和妻子已经共同生活了十年。", "label": [{"entity": "已婚", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "李四的婚姻状况为未婚，他一直专注于自己的事业发展。", "label": [{"entity": "未婚", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "王五的婚姻状况是离异，他独自抚养着两个孩子。", "label": [{"entity": "离异", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "赵六的婚姻状况为丧偶，他已独自度过了五年的时光。", "label": [{"entity": "丧偶", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "陈七的婚姻状况是已婚，他和伴侣正在计划第二次蜜月旅行。", "label": [{"entity": "已婚", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "周八的婚姻状况为单身，他最近开始尝试在线约会。", "label": [{"entity": "单身", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "吴九的婚姻状况是离异，他正在寻找新的生活伴侣。", "label": [{"entity": "离异", "start_idx": 4, "end_idx": 6, "type": "婚姻状况"}]}, {"text": "郑十的婚姻状况为已婚，他和配偶共同经营着一家小公司。", "label": [{"entity": "已婚", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "孙十一的婚姻状况是未婚，他决定先完成学业再考虑婚姻。", "label": [{"entity": "未婚", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "钱十二的婚姻状况为丧偶，他通过志愿者工作找到了新的意义。", "label": [{"entity": "丧偶", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}], "政治倾向": [{"text": "他在知乎上关注了许多自由主义者的观点。", "label": [{"entity": "自由主义者", "start_idx": 13, "end_idx": 16, "type": "政治倾向"}]}, {"text": "这位保守派议员坚决反对同性婚姻的合法化。", "label": [{"entity": "保守派", "start_idx": 1, "end_idx": 3, "type": "政治倾向"}]}, {"text": "作为一位社会主义者，她支持财富再分配政策。", "label": [{"entity": "社会主义者", "start_idx": 5, "end_idx": 10, "type": "政治倾向"}]}, {"text": "那位无政府主义者认为政府的存在是多余的。", "label": [{"entity": "无政府主义者", "start_idx": 3, "end_idx": 8, "type": "政治倾向"}]}, {"text": "他是一位坚定的共产主义信仰者，支持公有制。", "label": [{"entity": "共产主义", "start_idx": 7, "end_idx": 10, "type": "政治倾向"}, {"entity": "公有制", "start_idx": 15, "end_idx": 17, "type": "政治倾向"}]}, {"text": "这位民族主义者强烈呼吁保护本国文化产业。", "label": [{"entity": "民族主义者", "start_idx": 1, "end_idx": 5, "type": "政治倾向"}]}, {"text": "作为一位环保主义者，她反对核能的开发利用。", "label": [{"entity": "环保主义者", "start_idx": 6, "end_idx": 12, "type": "政治倾向"}]}, {"text": "那位社会民主主义者主张通过税收调节贫富差距。", "label": [{"entity": "社会民主主义者", "start_idx": 3, "end_idx": 8, "type": "政治倾向"}]}, {"text": "他是一位典型的民粹主义者，经常批评精英政治。", "label": [{"entity": "民粹主义者", "start_idx": 8, "end_idx": 12, "type": "政治倾向"}]}, {"text": "这位进步主义者支持LGBTQ+群体的权利法案。", "label": [{"entity": "进步主义者", "start_idx": 3, "end_idx": 8, "type": "政治倾向"}]}, {"text": "这位自由主义者坚持认为政府应该减少对经济的干预。", "label": [{"entity": "自由主义者", "start_idx": 2, "end_idx": 6, "type": "政治倾向"}]}, {"text": "那位保守派反对任何形式的同性婚姻合法化。", "label": [{"entity": "保守派", "start_idx": 3, "end_idx": 5, "type": "政治倾向"}]}, {"text": "作为社会主义者，他主张生产资料公有制。", "label": [{"entity": "社会主义者", "start_idx": 6, "end_idx": 12, "type": "政治倾向"}]}, {"text": "这位无政府主义者反对任何形式的政府权威。", "label": [{"entity": "无政府主义者", "start_idx": 3, "end_idx": 9, "type": "政治倾向"}]}, {"text": "这位民主党支持者认为，共和党的税收政策对中产阶级不利。", "label": [{"entity": "民主党支持者", "start_idx": 3, "end_idx": 10, "type": "政治倾向"}, {"entity": "共和党", "start_idx": 15, "end_idx": 18, "type": "政治倾向"}]}, {"text": "这位自由主义者认为政府应该减少对经济的干预。", "label": [{"entity": "自由主义者", "start_idx": 3, "end_idx": 7, "type": "政治倾向"}]}, {"text": "那位保守派人士坚决反对同性婚姻合法化。", "label": [{"entity": "保守派人士", "start_idx": 3, "end_idx": 8, "type": "政治倾向"}]}, {"text": "民主党支持者呼吁提高最低工资标准。", "label": [{"entity": "民主党", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}]}, {"text": "这位社会主义者主张实行生产资料公有制。", "label": [{"entity": "社会主义者", "start_idx": 2, "end_idx": 5, "type": "政治倾向"}, {"entity": "生产资料公有制", "start_idx": 11, "end_idx": 17, "type": "政治倾向"}]}, {"text": "独立候选人强调需要打破两党政治垄断。", "label": [{"entity": "两党政治", "start_idx": 14, "end_idx": 18, "type": "政治倾向"}]}], "工资数额": [{"text": "这家公司的起薪是每月8000元，非常适合刚毕业的学生。", "label": [{"entity": "8000元", "start_idx": 12, "end_idx": 15, "type": "工资数额"}]}, {"text": "他的年终奖金达到了15000元，比预想的要高不少。", "label": [{"entity": "15000元", "start_idx": 7, "end_idx": 11, "type": "工资数额"}]}, {"text": "这份兼职工作的时薪是45元，比普通兼职高很多。", "label": [{"entity": "45元", "start_idx": 12, "end_idx": 14, "type": "工资数额"}]}, {"text": "她的月薪是12000元，在行业内属于中等水平。", "label": [{"entity": "12000元", "start_idx": 7, "end_idx": 10, "type": "工资数额"}]}, {"text": "这个项目的奖金是5000元，完成难度不小。", "label": [{"entity": "5000元", "start_idx": 8, "end_idx": 11, "type": "工资数额"}]}, {"text": "他的年薪突破了30万元，是公司的高管级别。", "label": [{"entity": "30万元", "start_idx": 6, "end_idx": 9, "type": "工资数额"}]}, {"text": "这份实习的月薪是3000元，包吃住很划算。", "label": [{"entity": "3000元", "start_idx": 11, "end_idx": 14, "type": "工资数额"}]}, {"text": "他的加班费是每小时80元，比平时工资高不少。", "label": [{"entity": "80元", "start_idx": 8, "end_idx": 10, "type": "工资数额"}]}, {"text": "她的工资条显示本月实发9000元，比上个月多了2000元。", "label": [{"entity": "9000元", "start_idx": 12, "end_idx": 15, "type": "工资数额"}, {"entity": "2000元", "start_idx": 20, "end_idx": 23, "type": "工资数额"}]}, {"text": "这个岗位的试用期工资是6000元，转正后会有所提升。", "label": [{"entity": "6000元", "start_idx": 12, "end_idx": 15, "type": "工资数额"}]}, {"text": "他的月薪是8500元，比上一年提高了不少。", "label": [{"entity": "8500元", "start_idx": 7, "end_idx": 10, "type": "工资数额"}]}, {"text": "这家公司的起薪标准是6000元，吸引了大量毕业生。", "label": [{"entity": "6000元", "start_idx": 11, "end_idx": 14, "type": "工资数额"}]}, {"text": "这家公司的月薪标准是8,500元，比同行业平均水平略高一些。", "label": [{"entity": "8,500元", "start_idx": 12, "end_idx": 16, "type": "工资数额"}]}, {"text": "这个月的工资是8500元，比上个月多了500元。", "label": [{"entity": "8500元", "start_idx": 7, "end_idx": 10, "type": "工资数额"}, {"entity": "500元", "start_idx": 14, "end_idx": 17, "type": "工资数额"}]}, {"text": "张经理这个月的工资是12800元，比上个月多了500元。", "label": [{"entity": "12800元", "start_idx": 7, "end_idx": 10, "type": "工资数额"}, {"entity": "500元", "start_idx": 15, "end_idx": 17, "type": "工资数额"}]}, {"text": "这家公司的员工月薪平均为8500元，比去年增长了15%。", "label": [{"entity": "8500元", "start_idx": 14, "end_idx": 17, "type": "工资数额"}, {"entity": "15%", "start_idx": 22, "end_idx": 24, "type": "工资数额"}]}, {"text": "上个月我的工资是8500元，比预想的要高一些。", "label": [{"entity": "8500元", "start_idx": 9, "end_idx": 12, "type": "工资数额"}]}, {"text": "这家公司的月工资标准是8500元，比同行业平均水平要高一些。", "label": [{"entity": "8500元", "start_idx": 11, "end_idx": 14, "type": "工资数额"}]}, {"text": "这个月我的工资是8500元，比上个月多了500元。", "label": [{"entity": "8500元", "start_idx": 8, "end_idx": 11, "type": "工资数额"}, {"entity": "500元", "start_idx": 17, "end_idx": 20, "type": "工资数额"}]}, {"text": "这家公司的月薪标准是7500元，比同行业高出不少。", "label": [{"entity": "7500元", "start_idx": 11, "end_idx": 14, "type": "工资数额"}]}], "家庭成员": [{"text": "我的父亲张伟昨天给我打了个电话。", "label": [{"entity": "父亲", "start_idx": 2, "end_idx": 4, "type": "家庭成员"}]}, {"text": "妈妈李娜今天做了我最爱吃的红烧肉。", "label": [{"entity": "妈妈", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "哥哥刘强下周要带女朋友回家见我们。", "label": [{"entity": "哥哥", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "女朋友", "start_idx": 7, "end_idx": 9, "type": "家庭成员"}]}, {"text": "弟弟王浩的数学成绩一直比我好很多。", "label": [{"entity": "弟弟", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "妻子陈静已经怀孕三个月了，我们很开心。", "label": [{"entity": "妻子", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "儿子小明今年考上了重点高中，全家人都为他骄傲。", "label": [{"entity": "儿子", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "全家人", "start_idx": 9, "end_idx": 12, "type": "家庭成员"}]}, {"text": "女儿小红参加了学校的舞蹈比赛，跳得非常出色。", "label": [{"entity": "女儿", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "叔叔赵明从国外回来，给我们带了好多礼物。", "label": [{"entity": "叔叔", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "姑姑刘芳每年春节都会来我们家团聚。", "label": [{"entity": "姑姑", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "妹妹林小雨刚大学毕业，找到了一份不错的工作。", "label": [{"entity": "妹妹", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "爸爸张伟今天去公司开家庭会议了。", "label": [{"entity": "爸爸", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "李明的妻子王芳和女儿张婷今天一起去了外婆家。", "label": [{"entity": "李明的妻子", "start_idx": 0, "end_idx": 5, "type": "家庭成员"}, {"entity": "王芳", "start_idx": 5, "end_idx": 7, "type": "家庭成员"}, {"entity": "女儿张婷", "start_idx": 9, "end_idx": 11, "type": "家庭成员"}, {"entity": "张婷", "start_idx": 9, "end_idx": 11, "type": "家庭成员"}]}, {"text": "王明和妻子李娜带着儿子小杰去爷爷奶奶家过周末。", "label": [{"entity": "妻子", "start_idx": 3, "end_idx": 5, "type": "家庭成员"}, {"entity": "儿子", "start_idx": 10, "end_idx": 12, "type": "家庭成员"}, {"entity": "爷爷奶奶", "start_idx": 17, "end_idx": 20, "type": "家庭成员"}]}, {"text": "我的爸爸王大明和妈妈李秀英一起去参加了妹妹张小雨的毕业典礼。", "label": [{"entity": "爸爸", "start_idx": 2, "end_idx": 3, "type": "家庭成员"}, {"entity": "妈妈", "start_idx": 8, "end_idx": 9, "type": "家庭成员"}, {"entity": "妹妹", "start_idx": 18, "end_idx": 19, "type": "家庭成员"}]}, {"text": "爷爷今天带着孙子去公园散步了。", "label": [{"entity": "爷爷", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "孙子", "start_idx": 6, "end_idx": 8, "type": "家庭成员"}]}, {"text": "妈妈正在给女儿辅导数学作业。", "label": [{"entity": "妈妈", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "女儿", "start_idx": 6, "end_idx": 8, "type": "家庭成员"}]}, {"text": "爸爸和儿子周末一起去钓鱼了。", "label": [{"entity": "爸爸", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "儿子", "start_idx": 3, "end_idx": 5, "type": "家庭成员"}]}, {"text": "姐姐帮弟弟准备了明天的午餐。", "label": [{"entity": "姐姐", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "弟弟", "start_idx": 4, "end_idx": 6, "type": "家庭成员"}]}, {"text": "外婆给外孙买了一件新衣服。", "label": [{"entity": "外婆", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "外孙", "start_idx": 4, "end_idx": 6, "type": "家庭成员"}]}, {"text": "姑姑带着侄女去看了电影。", "label": [{"entity": "姑姑", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "侄女", "start_idx": 6, "end_idx": 8, "type": "家庭成员"}]}], "投资产品": [{"text": "购买招商中证白酒指数基金可以获得稳定的长期收益。", "label": [{"entity": "招商中证白酒指数基金", "start_idx": 3, "end_idx": 11, "type": "投资产品"}]}, {"text": "华夏上证50ETF是跟踪大盘指数的优质投资选择。", "label": [{"entity": "华夏上证50ETF", "start_idx": 0, "end_idx": 7, "type": "投资产品"}]}, {"text": "我的投资组合里配置了易方达中小盘混合型基金。", "label": [{"entity": "易方达中小盘混合型基金", "start_idx": 10, "end_idx": 17, "type": "投资产品"}]}, {"text": "购买国债逆回购是一种低风险的资金增值方式。", "label": [{"entity": "国债逆回购", "start_idx": 4, "end_idx": 7, "type": "投资产品"}]}, {"text": "购买招商银行招商瑞丰灵活配置混合型证券投资基金是一个稳健的选择。", "label": [{"entity": "招商银行招商瑞丰灵活配置混合型证券投资基金", "start_idx": 7, "end_idx": 20, "type": "投资产品"}]}, {"text": "我选择将部分资金投资于华夏成长混合型证券投资基金。", "label": [{"entity": "华夏成长混合型证券投资基金", "start_idx": 14, "end_idx": 29, "type": "投资产品"}]}, {"text": "我决定将部分资金投资于华夏成长混合型证券投资基金。", "label": [{"entity": "华夏成长混合型证券投资基金", "start_idx": 13, "end_idx": 28, "type": "投资产品"}]}, {"text": "我最近购买了一份招商银行现金管家理财产品，收益还不错。", "label": [{"entity": "招商银行现金管家理财产品", "start_idx": 9, "end_idx": 24, "type": "投资产品"}]}, {"text": "他最近购买了一份华夏回报混合型证券投资基金。", "label": [{"entity": "华夏回报混合型证券投资基金", "start_idx": 11, "end_idx": 23, "type": "投资产品"}]}, {"text": "王女士选择将资金投入了招商银行的一张定期存款产品。", "label": [{"entity": "招商银行", "start_idx": 12, "end_idx": 16, "type": "投资产品"}, {"entity": "一张定期存款产品", "start_idx": 17, "end_idx": 24, "type": "投资产品"}]}, {"text": "李先生通过支付宝购买了蚂蚁财富的余额宝货币基金。", "label": [{"entity": "支付宝", "start_idx": 5, "end_idx": 7, "type": "投资产品"}, {"entity": "蚂蚁财富", "start_idx": 9, "end_idx": 11, "type": "投资产品"}, {"entity": "余额宝货币基金", "start_idx": 12, "end_idx": 17, "type": "投资产品"}]}, {"text": "张总决定配置一部分资金到茅台股票上。", "label": [{"entity": "茅台股票", "start_idx": 11, "end_idx": 14, "type": "投资产品"}]}, {"text": "我建议你考虑一下招商银行的摩羯智投智能投顾服务。", "label": [{"entity": "摩羯智投智能投顾服务", "start_idx": 11, "end_idx": 19, "type": "投资产品"}]}, {"text": "他们家族信托主要配置了国债和地方政府债。", "label": [{"entity": "国债", "start_idx": 13, "end_idx": 15, "type": "投资产品"}, {"entity": "地方政府债", "start_idx": 16, "end_idx": 20, "type": "投资产品"}]}, {"text": "这位投资者持有招商银行的快溢通活期理财产品。", "label": [{"entity": "快溢通活期理财产品", "start_idx": 10, "end_idx": 16, "type": "投资产品"}]}, {"text": "她通过招商银行购买了招商中证白酒指数基金。", "label": [{"entity": "招商银行", "start_idx": 6, "end_idx": 10, "type": "投资产品"}, {"entity": "招商中证白酒指数基金", "start_idx": 14, "end_idx": 23, "type": "投资产品"}]}, {"text": "我们公司为员工提供了平安保险的团体年金险。", "label": [{"entity": "平安保险", "start_idx": 9, "end_idx": 11, "type": "投资产品"}, {"entity": "团体年金险", "start_idx": 12, "end_idx": 15, "type": "投资产品"}]}, {"text": "他长期持有腾讯控股的H股股票作为投资组合的一部分。", "label": [{"entity": "腾讯控股", "start_idx": 7, "end_idx": 10, "type": "投资产品"}, {"entity": "H股股票", "start_idx": 11, "end_idx": 14, "type": "投资产品"}]}, {"text": "我最近购买了一支华夏沪深300ETF，希望它能带来稳定的收益。", "label": [{"entity": "华夏沪深300ETF", "start_idx": 9, "end_idx": 18, "type": "投资产品"}]}, {"text": "他的投资组合中包含了招商银行发行的短期理财债券基金。", "label": [{"entity": "招商银行", "start_idx": 10, "end_idx": 12, "type": "投资产品"}, {"entity": "短期理财债券基金", "start_idx": 13, "end_idx": 19, "type": "投资产品"}]}], "税务记录": [{"text": "请提供具体的税务记录类型实体列表，以便我生成符合要求的句子。例如，您希望句子中包含“增值税发票”、“个人所得税申报表”等具体实体吗？", "label": [{"entity": "增值税发票", "start_idx": 51, "end_idx": 57, "type": "税务记录"}, {"entity": "个人所得税申报表", "start_idx": 58, "end_idx": 73, "type": "税务记录"}]}, {"text": "请提供您希望包含的税务记录类型实体，例如“增值税专用发票”、“个人所得税申报表”等，我将为您生成符合要求的句子。如果没有特定要求，我可以自行选择常见实体生成。", "label": [{"entity": "增值税专用发票", "start_idx": 47, "end_idx": 56, "type": "税务记录"}, {"entity": "个人所得税申报表", "start_idx": 59, "end_idx": 72, "type": "税务记录"}]}, {"text": "个人所得税申报表需要在本月底前提交给税务局。", "label": [{"entity": "个人所得税申报表", "start_idx": 0, "end_idx": 7, "type": "税务记录"}]}, {"text": "企业所得税年度汇算清缴报告已经准备完毕。", "label": [{"entity": "企业所得税年度汇算清缴报告", "start_idx": 0, "end_idx": 10, "type": "税务记录"}]}, {"text": "增值税专用发票的抵扣联需要妥善保管。", "label": [{"entity": "增值税专用发票的抵扣联", "start_idx": 0, "end_idx": 9, "type": "税务记录"}]}, {"text": "印花税纳税申报表的数据已经核对无误。", "label": [{"entity": "印花税纳税申报表", "start_idx": 0, "end_idx": 6, "type": "税务记录"}]}, {"text": "车辆购置税完税证明是办理上牌的必要文件。", "label": [{"entity": "车辆购置税完税证明", "start_idx": 0, "end_idx": 7, "type": "税务记录"}]}, {"text": "土地增值税清算表需要附上详细的计算过程。", "label": [{"entity": "土地增值税清算表", "start_idx": 0, "end_idx": 7, "type": "税务记录"}]}, {"text": "个人所得税纳税申报表是每个纳税人每年必须提交的文件。", "label": [{"entity": "个人所得税纳税申报表", "start_idx": 0, "end_idx": 9, "type": "税务记录"}]}, {"text": "企业所得税年度纳税申报表需要企业在规定时间内完成。", "label": [{"entity": "企业所得税年度纳税申报表", "start_idx": 0, "end_idx": 10, "type": "税务记录"}]}, {"text": "增值税专用发票是纳税人进行进项税抵扣的重要凭证。", "label": [{"entity": "增值税专用发票", "start_idx": 0, "end_idx": 7, "type": "税务记录"}]}, {"text": "印花税纳税记录表用于记录各类合同的印花税缴纳情况。", "label": [{"entity": "印花税纳税记录表", "start_idx": 0, "end_idx": 7, "type": "税务记录"}]}, {"text": "房产税纳税凭证是业主缴纳房产税后的有效证明。", "label": [{"entity": "房产税纳税凭证", "start_idx": 0, "end_idx": 7, "type": "税务记录"}]}, {"text": "车辆购置税完税证明是车辆合法上路的重要文件。", "label": [{"entity": "车辆购置税完税证明", "start_idx": 0, "end_idx": 7, "type": "税务记录"}]}, {"text": "土地增值税清算表用于计算房地产项目应缴纳的土地增值税。", "label": [{"entity": "土地增值税清算表", "start_idx": 0, "end_idx": 7, "type": "税务记录"}, {"entity": "土地增值税", "start_idx": 14, "end_idx": 19, "type": "税务记录"}]}, {"text": "消费税纳税申报表是生产者或进口商必须填写的税务文件。", "label": [{"entity": "消费税纳税申报表", "start_idx": 0, "end_idx": 7, "type": "税务记录"}]}, {"text": "资源税纳税记录表用于记录企业开采资源的资源税缴纳情况。", "label": [{"entity": "资源税纳税记录表", "start_idx": 0, "end_idx": 6, "type": "税务记录"}, {"entity": "资源税缴纳情况", "start_idx": 20, "end_idx": 26, "type": "税务记录"}]}, {"text": "城市维护建设税纳税申报表是纳税人需按期提交的税务文件。", "label": [{"entity": "城市维护建设税纳税申报表", "start_idx": 0, "end_idx": 10, "type": "税务记录"}]}, {"text": "公司提交了2023年度企业所得税汇算清缴申报表。", "label": [{"entity": "2023年度企业所得税汇算清缴申报表", "start_idx": 5, "end_idx": 18, "type": "税务记录"}]}, {"text": "个人需要查询2022年度个人所得税年度汇算记录。", "label": [{"entity": "2022年度个人所得税年度汇算记录", "start_idx": 9, "end_idx": 24, "type": "税务记录"}]}], "实体资产": [{"text": "这家公司拥有两栋位于市中心的商业写字楼。", "label": [{"entity": "两栋", "start_idx": 7, "end_idx": 8, "type": "实体资产"}, {"entity": "商业写字楼", "start_idx": 10, "end_idx": 13, "type": "实体资产"}]}, {"text": "他投资了一处位于海南的三层独栋别墅。", "label": [{"entity": "三层独栋别墅", "start_idx": 15, "end_idx": 21, "type": "实体资产"}]}, {"text": "仓库里堆满了成批的钢铁原材料。", "label": [{"entity": "仓库", "start_idx": 0, "end_idx": 2, "type": "实体资产"}, {"entity": "钢铁原材料", "start_idx": 8, "end_idx": 12, "type": "实体资产"}]}, {"text": "银行的保险库中存放着大量金条。", "label": [{"entity": "金条", "start_idx": 10, "end_idx": 11, "type": "实体资产"}]}, {"text": "农场里饲养了上百头西门塔尔牛。", "label": [{"entity": "西门塔尔牛", "start_idx": 8, "end_idx": 10, "type": "实体资产"}]}, {"text": "港口停泊着几艘货轮和集装箱船。", "label": [{"entity": "货轮", "start_idx": 6, "end_idx": 8, "type": "实体资产"}, {"entity": "集装箱船", "start_idx": 11, "end_idx": 15, "type": "实体资产"}]}, {"text": "他收藏了多幅齐白石的真迹画作。", "label": [{"entity": "真迹画作", "start_idx": 10, "end_idx": 13, "type": "实体资产"}]}, {"text": "地库里停放着一辆劳斯莱斯幻影。", "label": [{"entity": "劳斯莱斯幻影", "start_idx": 7, "end_idx": 12, "type": "实体资产"}]}, {"text": "这座别墅的产权已经正式过户到他的名下。", "label": [{"entity": "这座别墅", "start_idx": 0, "end_idx": 4, "type": "实体资产"}, {"entity": "产权", "start_idx": 5, "end_idx": 7, "type": "实体资产"}, {"entity": "他的名下", "start_idx": 19, "end_idx": 23, "type": "实体资产"}]}, {"text": "公司决定将仓库里的叉车进行定期维护保养。", "label": [{"entity": "仓库", "start_idx": 5, "end_idx": 7, "type": "实体资产"}, {"entity": "叉车", "start_idx": 9, "end_idx": 11, "type": "实体资产"}]}, {"text": "银行的黄金储备量在最近一个月增加了百分之五。", "label": [{"entity": "黄金储备量", "start_idx": 6, "end_idx": 9, "type": "实体资产"}]}, {"text": "他名下的那辆奔驰E级车即将进行年检。", "label": [{"entity": "奔驰E级车", "start_idx": 9, "end_idx": 13, "type": "实体资产"}]}, {"text": "这家工厂拥有多台德国进口的数控机床。", "label": [{"entity": "工厂", "start_idx": 1, "end_idx": 2, "type": "实体资产"}, {"entity": "德国进口的数控机床", "start_idx": 6, "end_idx": 13, "type": "实体资产"}]}, {"text": "她继承了父亲留下的几块限量版劳力士手表。", "label": [{"entity": "限量版劳力士手表", "start_idx": 10, "end_idx": 18, "type": "实体资产"}]}, {"text": "政府拍卖了一批老旧的蒸汽机车用于博物馆收藏。", "label": [{"entity": "蒸汽机车", "start_idx": 11, "end_idx": 13, "type": "实体资产"}]}, {"text": "仓库管理员正在清点货架上的苹果手机库存。", "label": [{"entity": "苹果手机", "start_idx": 10, "end_idx": 12, "type": "实体资产"}]}, {"text": "他在投资组合中持有大量微软公司的股票。", "label": [{"entity": "微软公司", "start_idx": 13, "end_idx": 17, "type": "实体资产"}, {"entity": "股票", "start_idx": 18, "end_idx": 20, "type": "实体资产"}]}, {"text": "这块位于市中心的商业地产价值超过一亿元。", "label": [{"entity": "商业地产", "start_idx": 12, "end_idx": 16, "type": "实体资产"}]}, {"text": "这家公司的主要资产包括一座位于上海的办公楼和一辆奔驰S级轿车。", "label": [{"entity": "办公楼", "start_idx": 23, "end_idx": 25, "type": "实体资产"}, {"entity": "奔驰S级轿车", "start_idx": 32, "end_idx": 36, "type": "实体资产"}]}, {"text": "他名下的实体资产包括一套位于北京的复式公寓和一架波音737飞机。", "label": [{"entity": "一套位于北京的复式公寓", "start_idx": 12, "end_idx": 26, "type": "实体资产"}, {"entity": "一架波音737飞机", "start_idx": 27, "end_idx": 37, "type": "实体资产"}]}], "信用记录": [{"text": "在央行征信系统中，我的信用记录显示为“正常”状态。", "label": [{"entity": "信用记录", "start_idx": 9, "end_idx": 11, "type": "信用记录"}]}, {"text": "办理房贷时，银行查看了我的芝麻信用分，结果是786分。", "label": [{"entity": "786分", "start_idx": 25, "end_idx": 28, "type": "信用记录"}]}, {"text": "公司招聘时，要求提供“良好”的信用记录证明。", "label": [{"entity": "信用记录证明", "start_idx": 11, "end_idx": 15, "type": "信用记录"}]}, {"text": "申请信用卡时，系统提示我的“逾期记录”已影响审批。", "label": [{"entity": "逾期记录", "start_idx": 9, "end_idx": 12, "type": "信用记录"}]}, {"text": "贷款审批过程中，发现我的“失信被执行人”记录被标记。", "label": [{"entity": "失信被执行人", "start_idx": 12, "end_idx": 16, "type": "信用记录"}]}, {"text": "租房签约时，房东要求查看我的“芝麻信用”评分。", "label": [{"entity": "芝麻信用", "start_idx": 19, "end_idx": 22, "type": "信用记录"}]}, {"text": "入职背景调查中，HR核实了我的“个人信用报告”真实性。", "label": [{"entity": "个人信用报告", "start_idx": 16, "end_idx": 23, "type": "信用记录"}]}, {"text": "他的个人信用报告显示，近期的查询记录中包含了招商银行的贷款审批查询。", "label": [{"entity": "个人信用报告", "start_idx": 3, "end_idx": 8, "type": "信用记录"}, {"entity": "查询记录", "start_idx": 14, "end_idx": 17, "type": "信用记录"}, {"entity": "招商银行的贷款审批查询", "start_idx": 24, "end_idx": 42, "type": "信用记录"}]}, {"text": "根据她的信用记录，工商银行信用卡的还款历史一直保持良好状态。", "label": [{"entity": "信用记录", "start_idx": 5, "end_idx": 8, "type": "信用记录"}]}, {"text": "这份企业信用报告中，包含了阿里巴巴的纳税信用等级评定结果。", "label": [{"entity": "阿里巴巴", "start_idx": 13, "end_idx": 16, "type": "信用记录"}]}, {"text": "他的征信报告里，有一项是建设银行房贷的还款逾期记录。", "label": [{"entity": "征信报告", "start_idx": 4, "end_idx": 8, "type": "信用记录"}, {"entity": "建设银行房贷", "start_idx": 14, "end_idx": 19, "type": "信用记录"}, {"entity": "还款逾期记录", "start_idx": 20, "end_idx": 24, "type": "信用记录"}]}, {"text": "在信用记录查询结果中，京东白条的使用情况被详细列出。", "label": [{"entity": "信用记录查询结果", "start_idx": 5, "end_idx": 12, "type": "信用记录"}, {"entity": "信用记录", "start_idx": 5, "end_idx": 8, "type": "信用记录"}]}, {"text": "她的个人信用档案显示，平安银行的贷款审批记录显示为通过。", "label": [{"entity": "信用档案", "start_idx": 3, "end_idx": 6, "type": "信用记录"}, {"entity": "贷款审批记录", "start_idx": 15, "end_idx": 20, "type": "信用记录"}]}, {"text": "这份信用记录显示，腾讯微众银行的小额贷款已按时结清。", "label": [{"entity": "信用记录", "start_idx": 4, "end_idx": 8, "type": "信用记录"}]}, {"text": "他的信用报告中，招商银行信用卡的年费缴纳记录显示为正常。", "label": [{"entity": "信用报告", "start_idx": 3, "end_idx": 6, "type": "信用记录"}, {"entity": "招商银行信用卡", "start_idx": 9, "end_idx": 16, "type": "信用记录"}, {"entity": "年费缴纳记录", "start_idx": 17, "end_idx": 23, "type": "信用记录"}]}, {"text": "企业信用记录中，华为技术有限公司的信用评级为AAA级。", "label": [{"entity": "信用记录", "start_idx": 4, "end_idx": 10, "type": "信用记录"}, {"entity": "AAA级", "start_idx": 27, "end_idx": 30, "type": "信用记录"}]}, {"text": "她的信用记录查询结果中，支付宝的蚂蚁借呗使用记录完整无缺。", "label": [{"entity": "信用记录", "start_idx": 4, "end_idx": 7, "type": "信用记录"}]}, {"text": "查询个人征信报告时，发现“信用卡逾期记录”和“贷款还款记录”均显示正常。", "label": [{"entity": "信用卡逾期记录", "start_idx": 10, "end_idx": 19, "type": "信用记录"}, {"entity": "贷款还款记录", "start_idx": 21, "end_idx": 29, "type": "信用记录"}]}, {"text": "在银行的信用评估系统中，“个人信用分”达到了750分，属于良好水平。", "label": [{"entity": "个人信用分", "start_idx": 9, "end_idx": 12, "type": "信用记录"}, {"entity": "750分", "start_idx": 13, "end_idx": 16, "type": "信用记录"}]}, {"text": "该企业的“税务信用等级”为A级，表明其纳税记录一直保持良好。", "label": [{"entity": "税务信用等级", "start_idx": 5, "end_idx": 12, "type": "信用记录"}, {"entity": "A级", "start_idx": 13, "end_idx": 15, "type": "信用记录"}, {"entity": "纳税记录", "start_idx": 31, "end_idx": 35, "type": "信用记录"}]}], "疾病": [{"text": "糖尿病患者的血糖控制需要长期监测。", "label": [{"entity": "糖尿病", "start_idx": 0, "end_idx": 2, "type": "疾病"}]}, {"text": "高血压是导致中风和心脏病的主要风险因素之一。", "label": [{"entity": "高血压", "start_idx": 0, "end_idx": 2, "type": "疾病"}, {"entity": "中风", "start_idx": 6, "end_idx": 7, "type": "疾病"}, {"entity": "心脏病", "start_idx": 9, "end_idx": 10, "type": "疾病"}]}, {"text": "肺炎患者通常会出现咳嗽和发热的症状。", "label": [{"entity": "肺炎", "start_idx": 0, "end_idx": 2, "type": "疾病"}]}, {"text": "哮喘发作时，患者可能会感到呼吸困难。", "label": [{"entity": "哮喘", "start_idx": 0, "end_idx": 2, "type": "疾病"}]}, {"text": "乳腺癌是女性最常见的恶性肿瘤之一。", "label": [{"entity": "乳腺癌", "start_idx": 0, "end_idx": 2, "type": "疾病"}]}, {"text": "肝炎患者需要避免饮酒以保护肝脏健康。", "label": [{"entity": "肝炎", "start_idx": 0, "end_idx": 2, "type": "疾病"}]}, {"text": "关节炎在老年人中较为常见，尤其是膝关节。", "label": [{"entity": "关节炎", "start_idx": 0, "end_idx": 2, "type": "疾病"}, {"entity": "膝关节", "start_idx": 14, "end_idx": 16, "type": "疾病"}]}, {"text": "肺结核患者需要完成全程抗结核治疗。", "label": [{"entity": "肺结核", "start_idx": 0, "end_idx": 2, "type": "疾病"}]}, {"text": "胃炎患者应避免食用辛辣刺激性食物。", "label": [{"entity": "胃炎", "start_idx": 0, "end_idx": 2, "type": "疾病"}]}, {"text": "艾滋病病毒感染者需要定期接受抗病毒治疗。", "label": [{"entity": "艾滋病病毒", "start_idx": 0, "end_idx": 5, "type": "疾病"}]}, {"text": "这位患者被诊断患有2型糖尿病，需要长期控制血糖。", "label": [{"entity": "2型糖尿病", "start_idx": 9, "end_idx": 12, "type": "疾病"}]}, {"text": "医生建议她进行基因检测，以排查是否携带乳腺癌易感基因。", "label": [{"entity": "乳腺癌", "start_idx": 19, "end_idx": 21, "type": "疾病"}]}, {"text": "他的慢性阻塞性肺疾病在冬季容易加重，需注意保暖。", "label": [{"entity": "慢性阻塞性肺疾病", "start_idx": 3, "end_idx": 9, "type": "疾病"}]}], "药物": [{"text": "阿莫西林胶囊可以用于治疗轻度的呼吸道感染。", "label": [{"entity": "阿莫西林胶囊", "start_idx": 0, "end_idx": 5, "type": "药物"}]}, {"text": "布洛芬缓释胶囊有助于缓解中度至重度的疼痛。", "label": [{"entity": "布洛芬缓释胶囊", "start_idx": 0, "end_idx": 6, "type": "药物"}]}, {"text": "甲硝唑片是治疗滴虫性阴道炎的常用药物。", "label": [{"entity": "甲硝唑片", "start_idx": 0, "end_idx": 4, "type": "药物"}]}, {"text": "辛伐他汀片用于降低高胆固醇血症患者的血脂。", "label": [{"entity": "辛伐他汀片", "start_idx": 0, "end_idx": 4, "type": "药物"}]}, {"text": "奥美拉唑肠溶胶囊可以减轻胃酸反流的症状。", "label": [{"entity": "奥美拉唑肠溶胶囊", "start_idx": 0, "end_idx": 7, "type": "药物"}]}, {"text": "氯雷他定片对缓解过敏性鼻炎的症状效果显著。", "label": [{"entity": "氯雷他定片", "start_idx": 0, "end_idx": 5, "type": "药物"}]}, {"text": "硝苯地平控释片适用于控制高血压患者的血压水平。", "label": [{"entity": "硝苯地平控释片", "start_idx": 0, "end_idx": 6, "type": "药物"}]}, {"text": "阿司匹林肠溶片常用于预防心血管疾病的发作。", "label": [{"entity": "阿司匹林肠溶片", "start_idx": 0, "end_idx": 6, "type": "药物"}]}, {"text": "左氧氟沙星片是治疗细菌性结膜炎的有效药物。", "label": [{"entity": "左氧氟沙星片", "start_idx": 0, "end_idx": 5, "type": "药物"}]}, {"text": "二甲双胍缓释片是治疗2型糖尿病的一线药物。", "label": [{"entity": "二甲双胍缓释片", "start_idx": 0, "end_idx": 6, "type": "药物"}]}, {"text": "医生建议他服用阿莫西林胶囊来治疗他的细菌感染。", "label": [{"entity": "阿莫西林胶囊", "start_idx": 11, "end_idx": 16, "type": "药物"}]}, {"text": "高血压患者每天需要按时服用卡托普利片来控制血压。", "label": [{"entity": "卡托普利片", "start_idx": 16, "end_idx": 19, "type": "药物"}]}], "交易信息": [{"text": "张先生通过招商银行转账给李女士10000元用于房租支付。", "label": [{"entity": "招商银行", "start_idx": 5, "end_idx": 8, "type": "交易信息"}, {"entity": "10000元", "start_idx": 12, "end_idx": 15, "type": "交易信息"}, {"entity": "房租支付", "start_idx": 18, "end_idx": 21, "type": "交易信息"}]}, {"text": "王女士在京东商城下单购买了一台价格为3999元的华为手机。", "label": [{"entity": "京东商城", "start_idx": 5, "end_idx": 8, "type": "交易信息"}, {"entity": "3999元", "start_idx": 15, "end_idx": 18, "type": "交易信息"}, {"entity": "华为手机", "start_idx": 19, "end_idx": 22, "type": "交易信息"}]}, {"text": "刘经理使用Visa信用卡支付了580元的星巴克咖啡费用。", "label": [{"entity": "Visa信用卡", "start_idx": 6, "end_idx": 10, "type": "交易信息"}, {"entity": "580元", "start_idx": 11, "end_idx": 14, "type": "交易信息"}, {"entity": "星巴克咖啡", "start_idx": 15, "end_idx": 19, "type": "交易信息"}]}, {"text": "赵小姐通过支付宝向朋友转账了200元作为生日礼物。", "label": [{"entity": "支付宝", "start_idx": 7, "end_idx": 10, "type": "交易信息"}, {"entity": "200元", "start_idx": 15, "end_idx": 18, "type": "交易信息"}]}, {"text": "陈先生在美团外卖上支付了68元的午餐订单。", "label": [{"entity": "68元", "start_idx": 10, "end_idx": 12, "type": "交易信息"}, {"entity": "午餐订单", "start_idx": 13, "end_idx": 16, "type": "交易信息"}]}, {"text": "孙女士用建设银行储蓄卡支付了1200元的电费账单。", "label": [{"entity": "电费账单", "start_idx": 17, "end_idx": 20, "type": "交易信息"}]}, {"text": "周先生在淘宝网购买了一件价格为299元的耐克运动鞋。", "label": [{"entity": "一件", "start_idx": 7, "end_idx": 8, "type": "交易信息"}, {"entity": "299元", "start_idx": 14, "end_idx": 16, "type": "交易信息"}, {"entity": "耐克运动鞋", "start_idx": 17, "end_idx": 20, "type": "交易信息"}]}, {"text": "吴女士通过微信支付给房东支付了1500元的房租。", "label": [{"entity": "微信支付", "start_idx": 5, "end_idx": 8, "type": "交易信息"}, {"entity": "1500元", "start_idx": 15, "end_idx": 18, "type": "交易信息"}]}, {"text": "杨先生在携程网预订了一间价格为680元的酒店客房。", "label": [{"entity": "680元", "start_idx": 17, "end_idx": 20, "type": "交易信息"}]}, {"text": "林小姐使用银联借记卡支付了450元的超市购物款。", "label": [{"entity": "银联借记卡", "start_idx": 7, "end_idx": 11, "type": "交易信息"}, {"entity": "450元", "start_idx": 15, "end_idx": 18, "type": "交易信息"}, {"entity": "超市购物款", "start_idx": 19, "end_idx": 24, "type": "交易信息"}]}, {"text": "王总使用Visa卡（4111 1111 1111 1111）支付了机票费用，共计2899元。", "label": [{"entity": "Visa卡", "start_idx": 5, "end_idx": 8, "type": "交易信息"}, {"entity": "4111 1111 1111 1111", "start_idx": 9, "end_idx": 25, "type": "交易信息"}, {"entity": "机票费用", "start_idx": 29, "end_idx": 34, "type": "交易信息"}, {"entity": "2899元", "start_idx": 40, "end_idx": 43, "type": "交易信息"}]}, {"text": "刘小姐通过微信支付（18612345678）向商家转账了120元的餐饮费用。", "label": [{"entity": "微信支付", "start_idx": 7, "end_idx": 10, "type": "交易信息"}, {"entity": "（18612345678）", "start_idx": 10, "end_idx": 18, "type": "交易信息"}, {"entity": "商家", "start_idx": 20, "end_idx": 22, "type": "交易信息"}, {"entity": "120元", "start_idx": 28, "end_idx": 31, "type": "交易信息"}, {"entity": "餐饮费用", "start_idx": 31, "end_idx": 36, "type": "交易信息"}]}, {"text": "赵师傅在工商银行ATM机（6222 0000 9876 5432）取现了2000元。", "label": [{"entity": "工商银行ATM机", "start_idx": 5, "end_idx": 12, "type": "交易信息"}, {"entity": "6222 0000 9876 5432", "start_idx": 13, "end_idx": 26, "type": "交易信息"}, {"entity": "2000元", "start_idx": 28, "end_idx": 31, "type": "交易信息"}]}, {"text": "陈经理通过PayPal账户（<EMAIL>）支付了软件订阅费，金额为29.99美元。", "label": [{"entity": "PayPal账户", "start_idx": 6, "end_idx": 11, "type": "交易信息"}, {"entity": "<EMAIL>", "start_idx": 12, "end_idx": 26, "type": "交易信息"}, {"entity": "软件订阅费", "start_idx": 29, "end_idx": 37, "type": "交易信息"}, {"entity": "29.99美元", "start_idx": 45, "end_idx": 51, "type": "交易信息"}]}, {"text": "周女士用银联卡（6214 3456 7890 1234）支付了酒店房费，总计1688元。", "label": [{"entity": "银联卡", "start_idx": 5, "end_idx": 8, "type": "交易信息"}, {"entity": "6214 3456 7890 1234", "start_idx": 9, "end_idx": 22, "type": "交易信息"}, {"entity": "酒店房费", "start_idx": 23, "end_idx": 28, "type": "交易信息"}, {"entity": "1688元", "start_idx": 33, "end_idx": 37, "type": "交易信息"}]}, {"text": "吴先生在京东平台（订单号：JD123456789）确认了支付，金额为399元。", "label": [{"entity": "订单号：JD123456789", "start_idx": 12, "end_idx": 26, "type": "交易信息"}, {"entity": "399元", "start_idx": 32, "end_idx": 35, "type": "交易信息"}]}, {"text": "孙女士通过建设银行手机银行（95533）完成了水电费缴纳，金额为456元。", "label": [{"entity": "水电费缴纳", "start_idx": 21, "end_idx": 26, "type": "交易信息"}, {"entity": "456元", "start_idx": 32, "end_idx": 35, "type": "交易信息"}]}, {"text": "钱先生用Mastercard（5100 1234 5678 9012）支付了海外购物款，共计89欧元。", "label": [{"entity": "Mastercard（5100 1234 5678 9012）", "start_idx": 5, "end_idx": 27, "type": "交易信息"}, {"entity": "89欧元", "start_idx": 28, "end_idx": 32, "type": "交易信息"}]}, {"text": "张先生通过工商银行ATM机完成了1000元人民币的转账交易。", "label": [{"entity": "1000元人民币", "start_idx": 16, "end_idx": 20, "type": "交易信息"}, {"entity": "转账交易", "start_idx": 21, "end_idx": 24, "type": "交易信息"}]}, {"text": "李女士在淘宝上购买了一件299元的夏季连衣裙，使用支付宝支付。", "label": [{"entity": "299元", "start_idx": 10, "end_idx": 13, "type": "交易信息"}, {"entity": "夏季连衣裙", "start_idx": 14, "end_idx": 18, "type": "交易信息"}, {"entity": "支付宝", "start_idx": 24, "end_idx": 27, "type": "交易信息"}]}], "临床表现": [{"text": "患者出现了持续性的胸痛症状，伴有呼吸急促。", "label": [{"entity": "持续性的胸痛症状", "start_idx": 4, "end_idx": 10, "type": "临床表现"}, {"entity": "呼吸急促", "start_idx": 14, "end_idx": 17, "type": "临床表现"}]}, {"text": "他检查时发现了明显的皮疹，且伴有瘙痒感。", "label": [{"entity": "皮疹", "start_idx": 8, "end_idx": 10, "type": "临床表现"}, {"entity": "瘙痒感", "start_idx": 13, "end_idx": 16, "type": "临床表现"}]}, {"text": "病人主诉有剧烈的头痛，并伴有恶心呕吐。", "label": [{"entity": "剧烈的头痛", "start_idx": 5, "end_idx": 9, "type": "临床表现"}, {"entity": "恶心呕吐", "start_idx": 13, "end_idx": 17, "type": "临床表现"}]}, {"text": "她最近出现了频繁的咳嗽，咳痰呈黄色。", "label": [{"entity": "频繁的咳嗽", "start_idx": 5, "end_idx": 8, "type": "临床表现"}, {"entity": "咳痰", "start_idx": 9, "end_idx": 11, "type": "临床表现"}, {"entity": "黄色", "start_idx": 12, "end_idx": 14, "type": "临床表现"}]}, {"text": "医生观察到患者有明显的黄疸，皮肤呈黄色。", "label": [{"entity": "黄疸", "start_idx": 7, "end_idx": 9, "type": "临床表现"}, {"entity": "皮肤呈黄色", "start_idx": 11, "end_idx": 16, "type": "临床表现"}]}, {"text": "患者主诉有腹痛，且伴有腹泻症状。", "label": [{"entity": "腹痛", "start_idx": 4, "end_idx": 6, "type": "临床表现"}, {"entity": "腹泻", "start_idx": 12, "end_idx": 14, "type": "临床表现"}]}, {"text": "他检查时发现手指有明显的麻木感。", "label": [{"entity": "手指有明显的麻木感", "start_idx": 7, "end_idx": 15, "type": "临床表现"}]}, {"text": "病人出现了持续性的低热，体温在37.5℃。", "label": [{"entity": "持续性的低热", "start_idx": 4, "end_idx": 9, "type": "临床表现"}, {"entity": "体温在37.5℃", "start_idx": 10, "end_idx": 16, "type": "临床表现"}]}, {"text": "她描述有视力模糊，伴有眼前黑影。", "label": [{"entity": "视力模糊", "start_idx": 4, "end_idx": 7, "type": "临床表现"}, {"entity": "眼前黑影", "start_idx": 9, "end_idx": 12, "type": "临床表现"}]}, {"text": "患者检查时发现关节肿胀，伴有压痛。", "label": [{"entity": "关节肿胀", "start_idx": 5, "end_idx": 8, "type": "临床表现"}, {"entity": "压痛", "start_idx": 10, "end_idx": 12, "type": "临床表现"}]}, {"text": "患者出现了明显的呼吸困难症状。", "label": [{"entity": "呼吸困难症状", "start_idx": 5, "end_idx": 10, "type": "临床表现"}]}, {"text": "医生记录了患者出现的皮疹和瘙痒。", "label": [{"entity": "皮疹", "start_idx": 8, "end_idx": 9, "type": "临床表现"}, {"entity": "瘙痒", "start_idx": 10, "end_idx": 11, "type": "临床表现"}]}, {"text": "体检时发现患者有明显的淋巴结肿大。", "label": [{"entity": "淋巴结肿大", "start_idx": 10, "end_idx": 13, "type": "临床表现"}]}, {"text": "患者自述有持续性的关节疼痛和僵硬。", "label": [{"entity": "关节疼痛", "start_idx": 6, "end_idx": 8, "type": "临床表现"}, {"entity": "僵硬", "start_idx": 9, "end_idx": 10, "type": "临床表现"}]}, {"text": "患者出现了明显的皮疹和瘙痒症状。", "label": [{"entity": "皮疹", "start_idx": 5, "end_idx": 7, "type": "临床表现"}, {"entity": "瘙痒", "start_idx": 8, "end_idx": 10, "type": "临床表现"}]}, {"text": "他检查时发现有肝区叩击痛和黄疸。", "label": [{"entity": "肝区叩击痛", "start_idx": 7, "end_idx": 10, "type": "临床表现"}, {"entity": "黄疸", "start_idx": 11, "end_idx": 13, "type": "临床表现"}]}, {"text": "医生发现病人有胸骨压痛和呼吸困难。", "label": [{"entity": "胸骨压痛", "start_idx": 7, "end_idx": 9, "type": "临床表现"}, {"entity": "呼吸困难", "start_idx": 10, "end_idx": 12, "type": "临床表现"}]}], "医疗程序": [{"text": "医生建议他进行结肠镜检查以筛查早期癌症。", "label": [{"entity": "结肠镜检查", "start_idx": 9, "end_idx": 14, "type": "医疗程序"}]}, {"text": "患者接受了CT扫描，结果显示肺部有阴影。", "label": [{"entity": "CT扫描", "start_idx": 4, "end_idx": 7, "type": "医疗程序"}]}, {"text": "她预约了超声波检查来评估腹部状况。", "label": [{"entity": "超声波检查", "start_idx": 4, "end_idx": 8, "type": "医疗程序"}]}, {"text": "医院安排了心脏导管插入术以诊断血管问题。", "label": [{"entity": "心脏导管插入术", "start_idx": 5, "end_idx": 10, "type": "医疗程序"}]}, {"text": "医生为他实施了白内障摘除手术。", "label": [{"entity": "白内障摘除手术", "start_idx": 7, "end_idx": 14, "type": "医疗程序"}]}, {"text": "病人需要做胃镜检查以确定消化系统问题。", "label": [{"entity": "胃镜检查", "start_idx": 7, "end_idx": 10, "type": "医疗程序"}]}, {"text": "他进行了MRI检查，以更详细地观察脑部结构。", "label": [{"entity": "MRI检查", "start_idx": 3, "end_idx": 7, "type": "医疗程序"}]}, {"text": "医生推荐她进行肺功能测试，以评估呼吸状况。", "label": [{"entity": "肺功能测试", "start_idx": 7, "end_idx": 11, "type": "医疗程序"}]}, {"text": "患者接受了血液透析，以帮助肾脏功能恢复。", "label": [{"entity": "血液透析", "start_idx": 4, "end_idx": 7, "type": "医疗程序"}]}, {"text": "她预约了牙科根管治疗，以解决牙齿感染问题。", "label": [{"entity": "牙科根管治疗", "start_idx": 4, "end_idx": 9, "type": "医疗程序"}]}, {"text": "医生建议他进行冠状动脉造影术以明确诊断。", "label": [{"entity": "冠状动脉造影术", "start_idx": 11, "end_idx": 18, "type": "医疗程序"}]}, {"text": "患者接受了胃镜检查，发现十二指肠溃疡。", "label": [{"entity": "胃镜检查", "start_idx": 4, "end_idx": 8, "type": "医疗程序"}]}, {"text": "她预约了 tomorrow 的核磁共振成像（MRI）检查。", "label": [{"entity": "核磁共振成像（MRI）检查", "start_idx": 6, "end_idx": 15, "type": "医疗程序"}]}, {"text": "医院安排了患者进行胸腔穿刺术以抽取积液。", "label": [{"entity": "胸腔穿刺术", "start_idx": 8, "end_idx": 11, "type": "医疗程序"}]}, {"text": "外科医生为她实施了腹腔镜胆囊切除术。", "label": [{"entity": "腹腔镜胆囊切除术", "start_idx": 9, "end_idx": 16, "type": "医疗程序"}]}, {"text": "他需要定期进行血液透析以维持肾功能。", "label": [{"entity": "血液透析", "start_idx": 8, "end_idx": 11, "type": "医疗程序"}]}, {"text": "医生决定为患者进行经皮冠状动脉介入治疗。", "label": [{"entity": "经皮冠状动脉介入治疗", "start_idx": 11, "end_idx": 20, "type": "医疗程序"}]}, {"text": "患者接受了超声心动图检查，评估心脏功能。", "label": [{"entity": "超声心动图检查", "start_idx": 5, "end_idx": 10, "type": "医疗程序"}]}, {"text": "她下周将进行白内障超声乳化手术。", "label": [{"entity": "白内障超声乳化手术", "start_idx": 7, "end_idx": 16, "type": "医疗程序"}]}], "过敏信息": [{"text": "他对花生过敏，每次接触都会引发严重的皮肤反应。", "label": [{"entity": "花生", "start_idx": 4, "end_idx": 6, "type": "过敏信息"}]}, {"text": "小明对芒果不过敏，但吃太多会导致肠胃不适。", "label": [{"entity": "芒果", "start_idx": 5, "end_idx": 7, "type": "过敏信息"}]}, {"text": "她对乳胶过敏，使用含乳胶的手套会引发接触性皮炎。", "label": [{"entity": "乳胶", "start_idx": 4, "end_idx": 5, "type": "过敏信息"}]}, {"text": "孩子对鸡蛋过敏，医生建议避免食用任何含鸡蛋的食品。", "label": [{"entity": "鸡蛋", "start_idx": 6, "end_idx": 8, "type": "过敏信息"}]}, {"text": "他对青霉素过敏，注射后可能会出现呼吸困难。", "label": [{"entity": "过敏", "start_idx": 3, "end_idx": 4, "type": "过敏信息"}]}, {"text": "她对海鲜过敏，特别是虾和螃蟹，食用后会有荨麻疹。", "label": [{"entity": "海鲜", "start_idx": 3, "end_idx": 5, "type": "过敏信息"}, {"entity": "虾", "start_idx": 8, "end_idx": 9, "type": "过敏信息"}, {"entity": "螃蟹", "start_idx": 10, "end_idx": 12, "type": "过敏信息"}]}, {"text": "他对小麦过敏，因此饮食中完全不含麸质成分。", "label": [{"entity": "小麦", "start_idx": 3, "end_idx": 5, "type": "过敏信息"}, {"entity": "麸质成分", "start_idx": 17, "end_idx": 20, "type": "过敏信息"}]}, {"text": "她对花粉过敏，春季外出时需要佩戴口罩。", "label": [{"entity": "花粉", "start_idx": 4, "end_idx": 6, "type": "过敏信息"}, {"entity": "春季", "start_idx": 9, "end_idx": 11, "type": "过敏信息"}]}, {"text": "他对坚果过敏，即使是微量的坚果成分也会引发反应。", "label": [{"entity": "坚果", "start_idx": 3, "end_idx": 4, "type": "过敏信息"}]}, {"text": "她对大豆过敏，所以豆浆和豆腐都不能食用。", "label": [{"entity": "大豆", "start_idx": 4, "end_idx": 6, "type": "过敏信息"}, {"entity": "豆浆", "start_idx": 12, "end_idx": 14, "type": "过敏信息"}, {"entity": "豆腐", "start_idx": 17, "end_idx": 19, "type": "过敏信息"}]}, {"text": "小明对花生过敏，每次吃都会出现严重的皮肤反应。", "label": [{"entity": "花生", "start_idx": 5, "end_idx": 7, "type": "过敏信息"}]}, {"text": "她对牛奶蛋白过敏，喝完一杯后就开始呕吐。", "label": [{"entity": "牛奶蛋白", "start_idx": 4, "end_idx": 7, "type": "过敏信息"}]}, {"text": "小华对芒果过敏，每次接触后嘴唇都会肿胀。", "label": [{"entity": "芒果", "start_idx": 6, "end_idx": 8, "type": "过敏信息"}]}, {"text": "他对青霉素类药物过敏，医生建议避免使用。", "label": [{"entity": "青霉素类药物", "start_idx": 4, "end_idx": 8, "type": "过敏信息"}]}, {"text": "她对海鲜中的虾过敏，吃后全身会起疹子。", "label": [{"entity": "虾", "start_idx": 6, "end_idx": 7, "type": "过敏信息"}]}, {"text": "小张对小麦过敏，面包和面条都不能吃。", "label": [{"entity": "小麦", "start_idx": 5, "end_idx": 7, "type": "过敏信息"}, {"entity": "面包", "start_idx": 9, "end_idx": 11, "type": "过敏信息"}, {"entity": "面条", "start_idx": 13, "end_idx": 15, "type": "过敏信息"}]}, {"text": "她对鸡蛋过敏，蛋糕和炒蛋都不能碰。", "label": [{"entity": "鸡蛋", "start_idx": 3, "end_idx": 4, "type": "过敏信息"}, {"entity": "蛋糕", "start_idx": 7, "end_idx": 8, "type": "过敏信息"}, {"entity": "炒蛋", "start_idx": 10, "end_idx": 11, "type": "过敏信息"}]}, {"text": "他对花粉过敏，春天出门必须戴口罩。", "label": [{"entity": "花粉", "start_idx": 3, "end_idx": 4, "type": "过敏信息"}]}, {"text": "她对蜂胶过敏，使用后皮肤会发痒。", "label": [{"entity": "蜂胶", "start_idx": 5, "end_idx": 7, "type": "过敏信息"}]}, {"text": "小王对坚果中的腰果过敏，吃后会呼吸困难。", "label": [{"entity": "腰果", "start_idx": 6, "end_idx": 8, "type": "过敏信息"}]}], "地理位置": [{"text": "北京故宫的游客们正在欣赏红墙黄瓦的建筑。", "label": [{"entity": "北京故宫", "start_idx": 0, "end_idx": 3, "type": "地理位置"}, {"entity": "红墙黄瓦", "start_idx": 10, "end_idx": 13, "type": "地理位置"}]}, {"text": "上海外滩的夜景吸引了无数摄影爱好者前来拍摄。", "label": [{"entity": "上海外滩", "start_idx": 0, "end_idx": 3, "type": "地理位置"}]}, {"text": "杭州西湖的断桥上，一对情侣正在漫步。", "label": [{"entity": "杭州", "start_idx": 0, "end_idx": 2, "type": "地理位置"}, {"entity": "西湖", "start_idx": 3, "end_idx": 5, "type": "地理位置"}, {"entity": "断桥", "start_idx": 6, "end_idx": 8, "type": "地理位置"}]}, {"text": "广州塔的灯光秀每晚都吸引大量市民观看。", "label": [{"entity": "广州塔", "start_idx": 0, "end_idx": 2, "type": "地理位置"}]}, {"text": "成都宽窄巷子的茶馆里，人们悠闲地品着盖碗茶。", "label": [{"entity": "成都", "start_idx": 0, "end_idx": 2, "type": "地理位置"}, {"entity": "宽窄巷子", "start_idx": 3, "end_idx": 6, "type": "地理位置"}]}, {"text": "深圳欢乐谷的过山车让游客们尖叫不断。", "label": [{"entity": "深圳欢乐谷", "start_idx": 0, "end_idx": 4, "type": "地理位置"}]}, {"text": "南京夫子庙的秦淮河畔，游客们乘船游览。", "label": [{"entity": "南京夫子庙", "start_idx": 0, "end_idx": 4, "type": "地理位置"}, {"entity": "秦淮河", "start_idx": 5, "end_idx": 7, "type": "地理位置"}, {"entity": "秦淮河畔", "start_idx": 5, "end_idx": 8, "type": "地理位置"}]}, {"text": "重庆洪崖洞的夜景被誉为“山城明珠”。", "label": [{"entity": "重庆", "start_idx": 0, "end_idx": 2, "type": "地理位置"}, {"entity": "洪崖洞", "start_idx": 3, "end_idx": 5, "type": "地理位置"}]}, {"text": "西安兵马俑博物馆的陶俑展现了秦朝的辉煌。", "label": [{"entity": "西安", "start_idx": 0, "end_idx": 1, "type": "地理位置"}, {"entity": "兵马俑博物馆", "start_idx": 2, "end_idx": 5, "type": "地理位置"}]}, {"text": "厦门鼓浪屿的沙滩上，孩子们在嬉戏玩耍。", "label": [{"entity": "厦门鼓浪屿", "start_idx": 0, "end_idx": 4, "type": "地理位置"}]}, {"text": "北京故宫是中国明清两代的皇家宫殿，位于北京市中心。", "label": [{"entity": "北京故宫", "start_idx": 0, "end_idx": 4, "type": "地理位置"}, {"entity": "中国", "start_idx": 7, "end_idx": 9, "type": "地理位置"}, {"entity": "北京市中心", "start_idx": 18, "end_idx": 22, "type": "地理位置"}]}, {"text": "上海外滩是黄浦江畔的地标性景观，吸引了众多游客。", "label": [{"entity": "上海外滩", "start_idx": 0, "end_idx": 3, "type": "地理位置"}, {"entity": "黄浦江", "start_idx": 7, "end_idx": 9, "type": "地理位置"}]}, {"text": "杭州西湖以其秀美的湖光山色，被誉为“人间天堂”。", "label": [{"entity": "杭州", "start_idx": 0, "end_idx": 2, "type": "地理位置"}, {"entity": "西湖", "start_idx": 3, "end_idx": 5, "type": "地理位置"}]}, {"text": "广州塔是广东省广州市的地标建筑，高约600米。", "label": [{"entity": "广州塔", "start_idx": 0, "end_idx": 2, "type": "地理位置"}, {"entity": "广东省", "start_idx": 6, "end_idx": 8, "type": "地理位置"}, {"entity": "广州市", "start_idx": 9, "end_idx": 11, "type": "地理位置"}]}, {"text": "西安兵马俑是世界文化遗产，位于陕西省西安市临潼区。", "label": [{"entity": "西安兵马俑", "start_idx": 0, "end_idx": 4, "type": "地理位置"}, {"entity": "世界文化遗产", "start_idx": 5, "end_idx": 10, "type": "地理位置"}, {"entity": "陕西省", "start_idx": 14, "end_idx": 17, "type": "地理位置"}, {"entity": "西安市", "start_idx": 18, "end_idx": 20, "type": "地理位置"}, {"entity": "临潼区", "start_idx": 21, "end_idx": 23, "type": "地理位置"}]}, {"text": "成都宽窄巷子是四川省成都市的历史文化街区。", "label": [{"entity": "成都宽窄巷子", "start_idx": 0, "end_idx": 5, "type": "地理位置"}, {"entity": "四川省", "start_idx": 7, "end_idx": 9, "type": "地理位置"}, {"entity": "成都市", "start_idx": 10, "end_idx": 12, "type": "地理位置"}]}, {"text": "厦门鼓浪屿是福建省厦门市的一个小岛，以音乐文化闻名。", "label": [{"entity": "厦门鼓浪屿", "start_idx": 0, "end_idx": 4, "type": "地理位置"}, {"entity": "福建省", "start_idx": 7, "end_idx": 9, "type": "地理位置"}, {"entity": "厦门市", "start_idx": 10, "end_idx": 12, "type": "地理位置"}]}], "生育信息": [{"text": "张女士在2023年3月15日进行了第一次产前检查。", "label": [{"entity": "2023年3月15日", "start_idx": 6, "end_idx": 13, "type": "生育信息"}, {"entity": "第一次产前检查", "start_idx": 17, "end_idx": 24, "type": "生育信息"}]}, {"text": "李先生和妻子在2021年8月选择了体外受精技术。", "label": [{"entity": "体外受精技术", "start_idx": 17, "end_idx": 21, "type": "生育信息"}]}, {"text": "王医生建议孕妇在孕周20周时进行四维彩超检查。", "label": [{"entity": "孕妇", "start_idx": 7, "end_idx": 9, "type": "生育信息"}, {"entity": "孕周20周", "start_idx": 12, "end_idx": 15, "type": "生育信息"}]}, {"text": "赵女士的预产期定在了2024年5月10日。", "label": [{"entity": "2024年5月10日", "start_idx": 6, "end_idx": 12, "type": "生育信息"}]}, {"text": "刘女士的胎儿在孕周28周时被诊断出先天性心脏病。", "label": [{"entity": "胎儿", "start_idx": 5, "end_idx": 7, "type": "生育信息"}, {"entity": "孕周28周", "start_idx": 10, "end_idx": 14, "type": "生育信息"}]}, {"text": "孙医生记录了产妇在2022年6月7日的分娩方式为剖宫产。", "label": [{"entity": "2022年6月7日", "start_idx": 9, "end_idx": 15, "type": "生育信息"}, {"entity": "剖宫产", "start_idx": 23, "end_idx": 26, "type": "生育信息"}]}, {"text": "杨女士在孕周16周时进行了唐氏筛查。", "label": [{"entity": "孕周16周", "start_idx": 6, "end_idx": 11, "type": "生育信息"}, {"entity": "唐氏筛查", "start_idx": 14, "end_idx": 18, "type": "生育信息"}]}, {"text": "周先生的精子活力检测结果显示为A级。", "label": [{"entity": "A级", "start_idx": 11, "end_idx": 13, "type": "生育信息"}]}, {"text": "李女士的预产期是2024年6月15日，需要按时产检。", "label": [{"entity": "2024年6月15日", "start_idx": 4, "end_idx": 10, "type": "生育信息"}, {"entity": "产检", "start_idx": 16, "end_idx": 18, "type": "生育信息"}]}, {"text": "张先生的精子活力检测报告显示A级精子占比为35%。", "label": [{"entity": "A级精子占比为35%", "start_idx": 10, "end_idx": 21, "type": "生育信息"}]}, {"text": "王女士的卵泡监测结果显示优势卵泡直径为18mm。", "label": [{"entity": "卵泡监测结果", "start_idx": 4, "end_idx": 8, "type": "生育信息"}, {"entity": "优势卵泡", "start_idx": 10, "end_idx": 13, "type": "生育信息"}, {"entity": "18mm", "start_idx": 21, "end_idx": 24, "type": "生育信息"}]}, {"text": "陈医生建议赵女士在孕12周时进行NT检查。", "label": [{"entity": "孕12周", "start_idx": 12, "end_idx": 15, "type": "生育信息"}]}, {"text": "刘先生的精液分析报告显示液化时间为30分钟。", "label": [{"entity": "液化时间为30分钟", "start_idx": 9, "end_idx": 19, "type": "生育信息"}]}, {"text": "孙女士的孕期血糖检测结果为空腹血糖5.2mmol/L。", "label": [{"entity": "孕期血糖检测结果", "start_idx": 4, "end_idx": 9, "type": "生育信息"}, {"entity": "空腹血糖", "start_idx": 10, "end_idx": 12, "type": "生育信息"}, {"entity": "5.2mmol/L", "start_idx": 13, "end_idx": 18, "type": "生育信息"}]}, {"text": "周先生的精子浓度检测值为2000万/ml。", "label": [{"entity": "精子浓度检测值", "start_idx": 3, "end_idx": 8, "type": "生育信息"}, {"entity": "2000万/ml", "start_idx": 9, "end_idx": 13, "type": "生育信息"}]}, {"text": "吴女士的B超检查显示胎儿双顶径为7.2cm。", "label": [{"entity": "胎儿双顶径", "start_idx": 10, "end_idx": 14, "type": "生育信息"}, {"entity": "7.2cm", "start_idx": 15, "end_idx": 18, "type": "生育信息"}]}, {"text": "郑先生的精液pH值检测结果为7.8。", "label": [{"entity": "精液pH值检测结果", "start_idx": 3, "end_idx": 9, "type": "生育信息"}]}, {"text": "钱女士的孕期体重增长曲线显示每周增加0.5kg。", "label": [{"entity": "孕期体重增长曲线", "start_idx": 4, "end_idx": 11, "type": "生育信息"}, {"entity": "每周增加0.5kg", "start_idx": 14, "end_idx": 21, "type": "生育信息"}]}, {"text": "医生记录了患者最后一次月经是2023年5月12日。", "label": [{"entity": "最后一次月经", "start_idx": 10, "end_idx": 15, "type": "生育信息"}, {"entity": "2023年5月12日", "start_idx": 16, "end_idx": 23, "type": "生育信息"}]}, {"text": "胎儿的超声检查显示头围为95毫米，孕周为24周。", "label": [{"entity": "胎儿的超声检查", "start_idx": 0, "end_idx": 6, "type": "生育信息"}, {"entity": "头围为95毫米", "start_idx": 7, "end_idx": 13, "type": "生育信息"}, {"entity": "孕周为24周", "start_idx": 14, "end_idx": 20, "type": "生育信息"}]}], "行程信息": [{"text": "今天下午3点，我的航班将从北京首都国际机场起飞前往上海浦东国际机场。", "label": [{"entity": "北京首都国际机场", "start_idx": 18, "end_idx": 22, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 26, "end_idx": 30, "type": "行程信息"}]}, {"text": "我预订了6月15日晚上7点的机票，从广州白云机场飞往成都双流机场。", "label": [{"entity": "6月15日晚上7点", "start_idx": 5, "end_idx": 11, "type": "行程信息"}, {"entity": "广州白云机场", "start_idx": 17, "end_idx": 24, "type": "行程信息"}, {"entity": "成都双流机场", "start_idx": 28, "end_idx": 36, "type": "行程信息"}]}, {"text": "请确认您的航班号CA1234，预计下午2:45抵达深圳宝安机场。", "label": [{"entity": "航班号CA1234", "start_idx": 7, "end_idx": 16, "type": "行程信息"}, {"entity": "下午2:45", "start_idx": 17, "end_idx": 22, "type": "行程信息"}, {"entity": "深圳宝安机场", "start_idx": 27, "end_idx": 35, "type": "行程信息"}]}, {"text": "我的行程安排是9月1日早上6:50从南京禄口机场飞往西安咸阳机场。", "label": [{"entity": "9月1日", "start_idx": 7, "end_idx": 10, "type": "行程信息"}, {"entity": "早上6:50", "start_idx": 11, "end_idx": 16, "type": "行程信息"}, {"entity": "南京禄口机场", "start_idx": 17, "end_idx": 22, "type": "行程信息"}, {"entity": "西安咸阳机场", "start_idx": 26, "end_idx": 31, "type": "行程信息"}]}, {"text": "预订的机票显示，7月10日早上7:30从武汉天河机场飞往长沙黄花机场。", "label": [{"entity": "7月10日早上7:30", "start_idx": 7, "end_idx": 15, "type": "行程信息"}, {"entity": "武汉天河机场", "start_idx": 17, "end_idx": 23, "type": "行程信息"}, {"entity": "长沙黄花机场", "start_idx": 26, "end_idx": 32, "type": "行程信息"}]}, {"text": "请注意，您的航班MU5678将于明天中午12:15从重庆江北机场起飞。", "label": [{"entity": "MU5678", "start_idx": 13, "end_idx": 18, "type": "行程信息"}, {"entity": "明天中午12:15", "start_idx": 19, "end_idx": 26, "type": "行程信息"}, {"entity": "重庆江北机场", "start_idx": 31, "end_idx": 38, "type": "行程信息"}]}, {"text": "我的返程机票是12月5日晚上9:00，从青岛胶东机场飞往哈尔滨太平机场。", "label": [{"entity": "返程机票", "start_idx": 2, "end_idx": 6, "type": "行程信息"}, {"entity": "12月5日晚上9:00", "start_idx": 7, "end_idx": 15, "type": "行程信息"}, {"entity": "青岛胶东机场", "start_idx": 21, "end_idx": 26, "type": "行程信息"}, {"entity": "哈尔滨太平机场", "start_idx": 29, "end_idx": 36, "type": "行程信息"}]}, {"text": "今天我从北京首都国际机场搭乘CA1234航班前往上海浦东国际机场。", "label": [{"entity": "北京首都国际机场", "start_idx": 9, "end_idx": 19, "type": "行程信息"}, {"entity": "CA1234航班", "start_idx": 20, "end_idx": 25, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 27, "end_idx": 39, "type": "行程信息"}]}, {"text": "下周三早上8点，我的火车票是从广州南站到深圳北站的高铁G6789次。", "label": [{"entity": "下周三早上8点", "start_idx": 0, "end_idx": 6, "type": "行程信息"}, {"entity": "广州南站", "start_idx": 11, "end_idx": 15, "type": "行程信息"}, {"entity": "深圳北站", "start_idx": 17, "end_idx": 21, "type": "行程信息"}, {"entity": "高铁G6789次", "start_idx": 22, "end_idx": 27, "type": "行程信息"}]}, {"text": "我计划在2023年12月15日乘坐MU5137航班从成都双流机场飞往杭州萧山机场。", "label": [{"entity": "2023年12月15日", "start_idx": 5, "end_idx": 14, "type": "行程信息"}, {"entity": "MU5137航班", "start_idx": 18, "end_idx": 26, "type": "行程信息"}, {"entity": "成都双流机场", "start_idx": 30, "end_idx": 38, "type": "行程信息"}, {"entity": "杭州萧山机场", "start_idx": 44, "end_idx": 53, "type": "行程信息"}]}, {"text": "明天下午3点，我需要在南京禄口机场办理CA4567航班的登机手续。", "label": [{"entity": "CA4567航班", "start_idx": 16, "end_idx": 22, "type": "行程信息"}, {"entity": "登机手续", "start_idx": 25, "end_idx": 29, "type": "行程信息"}]}, {"text": "我的出差行程是从上海虹桥站乘坐D301次动车前往北京西站，时间是9月20日。", "label": [{"entity": "D301次动车", "start_idx": 17, "end_idx": 23, "type": "行程信息"}, {"entity": "9月20日", "start_idx": 36, "end_idx": 40, "type": "行程信息"}]}, {"text": "下个月1日，我将搭乘HU7123航班从深圳宝安机场飞往西安咸阳机场。", "label": [{"entity": "下个月1日", "start_idx": 0, "end_idx": 5, "type": "行程信息"}, {"entity": "HU7123", "start_idx": 11, "end_idx": 16, "type": "行程信息"}, {"entity": "深圳宝安机场", "start_idx": 20, "end_idx": 25, "type": "行程信息"}, {"entity": "西安咸阳机场", "start_idx": 28, "end_idx": 33, "type": "行程信息"}]}, {"text": "今天下午2点，我从武汉天河站乘坐K512次列车前往长沙南站。", "label": [{"entity": "K512次列车", "start_idx": 16, "end_idx": 21, "type": "行程信息"}]}, {"text": "我的返程航班是2023年10月5日从重庆江北机场飞往天津滨海机场的CA9876。", "label": [{"entity": "返程航班", "start_idx": 3, "end_idx": 6, "type": "行程信息"}, {"entity": "2023年10月5日", "start_idx": 7, "end_idx": 16, "type": "行程信息"}, {"entity": "重庆江北机场", "start_idx": 20, "end_idx": 27, "type": "行程信息"}, {"entity": "天津滨海机场", "start_idx": 31, "end_idx": 38, "type": "行程信息"}, {"entity": "CA9876", "start_idx": 40, "end_idx": 44, "type": "行程信息"}]}, {"text": "我的航班将于2023年12月15日早上8:30从北京首都国际机场起飞。", "label": [{"entity": "2023年12月15日", "start_idx": 7, "end_idx": 15, "type": "行程信息"}, {"entity": "早上8:30", "start_idx": 16, "end_idx": 21, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 28, "end_idx": 38, "type": "行程信息"}]}, {"text": "下周三下午3点，我需要从上海虹桥站乘坐G123次高铁前往杭州。", "label": [{"entity": "G123次高铁", "start_idx": 17, "end_idx": 22, "type": "行程信息"}]}, {"text": "请确认您的火车票，车次为K567，出发时间是2024年1月20日晚上9:40。", "label": [{"entity": "K567", "start_idx": 11, "end_idx": 14, "type": "行程信息"}, {"entity": "2024年1月20日晚上9:40", "start_idx": 15, "end_idx": 28, "type": "行程信息"}]}, {"text": "我们将于2023年11月5日早上7:15从广州白云机场搭乘CA1234次航班。", "label": [{"entity": "2023年11月5日", "start_idx": 5, "end_idx": 13, "type": "行程信息"}, {"entity": "早上7:15", "start_idx": 14, "end_idx": 20, "type": "行程信息"}, {"entity": "广州白云机场", "start_idx": 24, "end_idx": 31, "type": "行程信息"}, {"entity": "CA1234次航班", "start_idx": 37, "end_idx": 44, "type": "行程信息"}]}, {"text": "明天的巴士将在早上6:30从深圳北站出发，终点站是惠州南站。", "label": [{"entity": "巴士", "start_idx": 3, "end_idx": 5, "type": "行程信息"}, {"entity": "早上6:30", "start_idx": 7, "end_idx": 11, "type": "行程信息"}, {"entity": "深圳北站", "start_idx": 15, "end_idx": 19, "type": "行程信息"}, {"entity": "惠州南站", "start_idx": 28, "end_idx": 32, "type": "行程信息"}]}]}