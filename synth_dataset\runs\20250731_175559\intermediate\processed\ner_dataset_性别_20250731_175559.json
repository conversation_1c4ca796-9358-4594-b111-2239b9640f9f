[{"text": "张女士正在为明天的演讲准备PPT。", "label": [{"entity": "张", "start_idx": 0, "end_idx": 1, "type": "性别"}, {"entity": "女士", "start_idx": 1, "end_idx": 2, "type": "性别"}]}, {"text": "刘小姐的生日派对将在下周五举行。", "label": [{"entity": "小姐", "start_idx": 1, "end_idx": 2, "type": "性别"}]}, {"text": "张伟是一位男性工程师，他在公司负责软件开发项目。", "label": [{"entity": "男性", "start_idx": 4, "end_idx": 6, "type": "性别"}]}, {"text": "李娜是位女性医生，她每天在医院为病人诊断病情。", "label": [{"entity": "女性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "王强这个男性运动员在比赛中打破了全国纪录。", "label": [{"entity": "男性", "start_idx": 4, "end_idx": 6, "type": "性别"}]}, {"text": "刘芳作为女性教师，她的课堂总是充满活力。", "label": [{"entity": "女性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "陈明这位男性厨师擅长制作各种川菜美食。", "label": [{"entity": "男性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "赵雪是位女性设计师，她的作品多次获得国际奖项。", "label": [{"entity": "女性", "start_idx": 4, "end_idx": 6, "type": "性别"}]}]