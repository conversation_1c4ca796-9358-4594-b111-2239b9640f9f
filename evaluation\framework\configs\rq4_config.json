{"rq4_ablation_study": {"description": "消融实验配置", "components": {"sentence_diversity_strategy": {"enabled": true, "description": "句子多样化策略", "baseline": "random_templates"}, "entity_diversity_strategy": {"enabled": true, "description": "实体多样化策略", "baseline": "fixed_entity_pool"}, "global_cache_mechanism": {"enabled": true, "description": "全局缓存机制", "baseline": "no_cache"}, "latent_scenarios": {"enabled": true, "description": "latent场景多样化", "baseline": "vanilla_only"}, "iterative_optimization": {"enabled": true, "description": "迭代优化流程", "baseline": "single_shot_generation"}}, "experimental_design": {"full_system": "all_components_enabled", "ablation_conditions": ["no_sentence_diversity", "no_entity_diversity", "no_global_cache", "no_latent_scenarios", "no_iterative_optimization"], "baseline_methods": ["random_generation", "template_based_generation", "existing_method_comparison"]}, "evaluation_metrics": {"quality_metrics": ["naturalness", "accuracy", "diversity"], "efficiency_metrics": ["time_cost", "api_calls", "memory_usage"], "effectiveness_metrics": ["target_achievement", "convergence_speed"]}}}