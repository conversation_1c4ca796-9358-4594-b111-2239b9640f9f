{"timestamp": "20250705_204914", "start_time": "2025-07-05T20:49:14.232399", "status": "failed", "config": {"dataset_path": "format-dataset/privacy_bench_small.json", "target_count": 50, "max_iterations": 3, "batch_size": 10, "distribution_threshold": 0.05}, "directories": {"root": "synth_dataset\\runs\\20250705_204914", "config": "synth_dataset\\runs\\20250705_204914\\config", "source": "synth_dataset\\runs\\20250705_204914\\source", "intermediate": "synth_dataset\\runs\\20250705_204914\\intermediate", "intermediate_raw": "synth_dataset\\runs\\20250705_204914\\intermediate\\raw", "intermediate_processed": "synth_dataset\\runs\\20250705_204914\\intermediate\\processed", "intermediate_by_entity": "synth_dataset\\runs\\20250705_204914\\intermediate\\by_entity", "iterations": "synth_dataset\\runs\\20250705_204914\\iterations", "strategies": "synth_dataset\\runs\\20250705_204914\\strategies", "output": "synth_dataset\\runs\\20250705_204914\\output", "evaluation": "synth_dataset\\runs\\20250705_204914\\evaluation", "logs": "synth_dataset\\runs\\20250705_204914\\logs"}, "last_updated": "2025-07-05T22:15:48.592254", "stage": "iteration", "current_iteration": 1, "max_iterations": 3, "error": "Object of type int32 is not JSON serializable"}