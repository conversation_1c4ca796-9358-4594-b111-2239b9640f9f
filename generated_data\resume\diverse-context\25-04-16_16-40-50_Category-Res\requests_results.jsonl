[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名人力资源专员。列出8种不同的教育水平，用于中文简历处理。，即不同的教育水平，一些例子是[本科, 硕士, 博士, 大专, 高中]。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 49}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n以下是8种不同的教育水平，适用于中文简历处理：  \n\n1. **高中**  \n2. **中专**  \n3. **大专**  \n4. **本科**  \n5. **硕士**  \n6. **博士**  \n7. **博士后**  \n8. **研究生**（通常指在读硕士或博士阶段）  \n\n这些分类涵盖了从基础教育到高等教育的不同层次，适用于大多数简历筛选场景。", "role": "assistant"}}], "created": 1744792853, "id": "2025041616405020594811f6ab454f", "model": "glm-4-air-250414", "request_id": "2025041616405020594811f6ab454f", "usage": {"completion_tokens": 89, "prompt_tokens": 50, "total_tokens": 139}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名人力资源专员。列出10种不同的行业领域，用于中文简历处理。，即不同的行业领域，一些例子是[医疗, 金融, 制造业, 教育, IT]。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 56}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n好的，作为一名人力资源专员，以下是为中文简历处理列出的10种不同的行业领域：\n\n1.  **医疗 (Healthcare/Medical)**：涵盖医院、诊所、医药研发、医疗器械、健康管理等。\n2.  **金融 (Finance)**：包括银行、证券、保险、基金、信托、投资等机构。\n3.  **制造业 (Manufacturing)**：指各类生产实体产品的行业，如汽车、电子、机械、化工、食品加工等。\n4.  **教育 (Education)**：包括公立/私立学校、大学、职业培训机构、教育科技公司等。\n5.  **IT (Information Technology)**：涵盖软件开发、互联网、电子商务、云计算、大数据、人工智能、网络安全等。\n6.  **零售 (Retail)**：包括实体店铺（百货、超市、专卖店等）和电子商务零售平台。\n7.  **房地产 (Real Estate)**：涉及房地产开发、物业管理、房地产中介、咨询等。\n8.  **能源 (Energy)**：包括电力、石油、天然气、新能源（如太阳能、风能）等。\n9.  **咨询 (Consulting)**：提供战略、管理、人力资源、财务、技术等方面的专业咨询服务。\n10. **文化传媒 (Culture and Media)**：包括新闻出版、广播影视、广告、公关、娱乐、艺术等领域。", "role": "assistant"}}], "created": 1744792857, "id": "20250416164050c69d3afb5c5d4cad", "model": "glm-4-air-250414", "request_id": "20250416164050c69d3afb5c5d4cad", "usage": {"completion_tokens": 272, "prompt_tokens": 46, "total_tokens": 318}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名人力资源专员。列出10种不同类型的简历，用于中文简历处理。，即不同类型的简历，一些例子是[经验简历, 学生简历, 求职简历, 应届生简历, 实习简历]。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 42}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n以下是10种不同类型的中文简历类型，适用于不同的求职场景和背景：\n\n1. **经验简历**：重点突出个人过往的工作经验、成就和技能，适合有多年工作经验的求职者。\n2. **学生简历**：面向在校学生，重点展示学业成绩、校园活动、社团经历等，可能较少工作经验。\n3. **求职简历**：通用型简历，用于求职，涵盖教育背景、技能、工作经验等基本信息，适合多种岗位申请。\n4. **应届生简历**：专门为即将毕业或刚毕业的学生设计，重点突出实习经历、校园实践、个人技能等。\n5. **实习简历**：适用于寻找实习机会的学生或初入职场者，着重展示实习经验、学习能力和职业规划。\n6. **技能导向简历**：重点突出个人的专业技能和专长，适合技术类岗位或需要特定技能的职位。\n7. **创意简历**：设计独特、具有创意的简历，适合设计、艺术、广告等创意行业，通过视觉效果吸引招聘者注意。\n8. **项目经验简历**：重点展示个人参与的项目经历、项目成果和贡献，适合项目导向型工作。\n9. **管理经验简历**：突出个人的管理经验、领导能力和团队管理能力，适合管理类岗位。\n10. **学术简历**：适用于学术研究、教育领域，重点展示教育背景、研究成果、发表论文、学术会议等。", "role": "assistant"}}], "created": 1744792858, "id": "20250416164050d17e8cdb18d54a98", "model": "glm-4-air-250414", "request_id": "20250416164050d17e8cdb18d54a98", "usage": {"completion_tokens": 293, "prompt_tokens": 56, "total_tokens": 349}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名人力资源专员。列出8种不同的工作经验水平，用于中文简历处理。，即不同的工作经验水平，一些例子是[5-10年, 3-5年, 1-3年, 10年以上, 应届生]。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 63}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n好的，作为一名人力资源专员，以下是8种不同的工作经验水平，可用于中文简历处理：\n\n1.  **应届生/实习生 (Recent Graduate / Intern):** 适用于刚毕业或在校期间参与实习的学生。\n2.  **1年以下 (Less than 1 year):** 适用于刚踏入职场不久，工作经验非常有限的求职者。\n3.  **1-3年 (1-3 years):** 适用于具备一定基础工作经验，通常能独立完成本职工作的初级职位候选人。\n4.  **3-5年 (3-5 years):** 适用于经验相对丰富，可能开始承担一些指导或小型项目管理工作的中级职位候选人。\n5.  **5-8年 (5-8 years):** 适用于经验较丰富，通常在专业领域有一定深度，可能担任骨干或初级管理角色的候选人。\n6.  **8-10年 (8-10 years):** 适用于经验丰富，通常在专业领域有较深积累，可能担任重要岗位、项目负责人或中层管理角色的候选人。\n7.  **10年以上 (More than 10 years):** 适用于经验非常丰富，通常在行业或领域内有资深资历，可能担任高级管理、专家顾问或核心领导角色的候选人。\n8.  **资深专家/管理层 (Senior Expert / Management Level):** 适用于工作经验远超10年，并在特定领域达到顶尖水平，或担任企业高层管理职位，经验难以简单用年限衡量的候选人。", "role": "assistant"}}], "created": 1744792858, "id": "202504161640507856a24b74244f2d", "model": "glm-4-air-250414", "request_id": "202504161640507856a24b74244f2d", "usage": {"completion_tokens": 308, "prompt_tokens": 59, "total_tokens": 367}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名人力资源专员。列出8种不同的语言风格，用于中文简历处理。，即不同的语言风格，一些例子是[创新, 简洁, 正式, 专业, 详细]。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 70}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 创新风格：强调独特性和创造力，使用富有想象力的词汇和句式。\n   - 例：通过颠覆性的市场策略，我们实现了销售额的飞跃式增长。\n\n2. 简洁风格：言简意赅，直截了当，避免冗余和复杂的表达。\n   - 例：负责项目管理，确保项目按时完成，达成预期目标。\n\n3. 正式风格：使用规范的、符合礼仪的词汇和句式，显得庄重和严谨。\n   - 例：我荣幸地担任过学生会主席，组织并参与了多项校级活动。\n\n4. 专业风格：使用行业术语和专业技术词汇，体现专业性和深度。\n   - 例：运用数据挖掘技术，对用户行为进行分析，提出优化建议。\n\n5. 详细风格：提供丰富的细节和背景信息，使读者对内容有全面的了解。\n   - 例：在为期三个月的市场调研中，我们走访了多个城市，与超过200位消费者进行了深入交流。\n\n6. 亲和风格：使用友好、亲切的语言，拉近与读者的距离。\n   - 例：作为团队的一员，我总是乐于助人，与同事们建立了深厚的友谊。\n\n7. 动态风格：使用生动、有活力的词汇和句式，展示个性和热情。\n   - 例：我充满激情地投入每一次工作挑战，不断追求卓越。\n\n8. 精炼风格：在简洁的基础上，进一步提炼语言，使其更加精炼和有力。\n   - 例：领导团队，高效执行，成功交付多个关键项目。", "role": "assistant"}}], "created": 1744792858, "id": "20250416164050c84489c25b674649", "model": "glm-4-air-250414", "request_id": "20250416164050c84489c25b674649", "usage": {"completion_tokens": 321, "prompt_tokens": 48, "total_tokens": 369}}]
