{"attribute2categories": {"news-category": ["World News", "Politics", "Technology", "Sports", "Entertainment", "Health", "Business", "Environment", "Breaking News", "Human Interest Stories", "Local Stories", "Economics", "International Events", "Science", "Education", "Travel", "Crime", "Fashion", "Food", "Religion", "Art", "Music", "Film", "Literature", "Automotive", "Lifestyle", "Weather", "Wildlife", "Education", "Immigrant Stories", "Politics", "Health", "Science", "Environment", "Economics", "Business", "Local stories", "Sports", "Technology", "Human interest stories", "Culture", "International events", "Breaking news", "Education", "Crime", "Travel", "Entertainment", "Food and drink", "Fashion", "Religion", "Weather", "Social issues", "LGBTQ+ issues", "Immigration", "Wildlife", "Space exploration", "Military", "Art and literature", "Automotive", "History", "Entertainment", "Politics", "Sports", "Health", "Technology", "Business", "Science", "Environment", "Education", "Travel", "Religion", "Lifestyle", "Fashion", "Food", "Music", "Art", "Film", "Literature", "Weather", "Crime", "Breaking News", "Human interest stories", "International events", "Local stories", "Economics", "Culture", "Immigration", "Social issues", "Innovation", "Celebrity news"], "writing-style": ["Breaking news reports", "Investigative journalism", "Human interest stories", "Roundup or summary articles", "Interview-based articles", "Photo and video journalism", "Data-driven reporting", "Editorial or commentary pieces", "Straightforward", "Narrative", "Concise", "Factual", "Analytical", "Objective", "Clear", "Investigative", "Hard news reporting", "Investigative journalism", "Informative news articles", "Human interest stories", "Broadcast news", "Long-form journalism", "Photojournalism", "Data-driven journalism"]}, "meta": {"dataset-name": "conll2003", "attributes": ["news-category", "writing-style"], "entity-sizes": {"news-category": 90, "writing-style": 24}}}