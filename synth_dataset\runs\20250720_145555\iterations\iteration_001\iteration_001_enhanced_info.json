{"iteration": 1, "timestamp": "2025-07-20T15:12:40.781116", "dataset_size": 229, "entity_gap": {"姓名": 9, "年龄": 8, "性别": 8, "国籍": 6, "职业": 2, "民族": 9, "教育背景": 7, "婚姻状况": 10, "政治倾向": 10, "家庭成员": 10, "工资数额": 10, "投资产品": 10, "税务记录": 10, "信用记录": 10, "实体资产": 10, "交易信息": 10, "疾病": 3, "药物": 2, "临床表现": 7, "医疗程序": 9, "过敏信息": 10, "生育信息": 10, "地理位置": 7, "行程信息": 10}, "diversity_metrics": {"vocabulary_diversity": 1.0, "syntactic_diversity": 0.8, "semantic_diversity": 0.7, "context_diversity": 0.6, "entity_diversity": 0.75}, "total_gap": 197, "detailed_metrics": {"basic_statistics": {"total_samples": 229, "text_length_stats": {"mean": 30.117903930131003, "median": 28.0, "std": 8.262562644725877, "min": 12, "max": 68, "percentiles": {"25": 25.0, "75": 34.0, "90": 41.0, "95": 44.0}}, "entity_count_stats": {"mean": 1.8078602620087336, "median": 2.0, "std": 1.027027447468278, "min": 1, "max": 7}, "label_distribution": {"姓名": 15, "地理位置": 19, "职业": 10, "医疗程序": 10, "疾病": 10, "药物": 10, "临床表现": 24, "国籍": 12, "民族": 17, "教育背景": 13, "性别": 14, "年龄": 11, "婚姻状况": 10, "政治倾向": 15, "家庭成员": 37, "工资数额": 12, "投资产品": 11, "税务记录": 17, "信用记录": 16, "实体资产": 19, "交易信息": 34, "过敏信息": 18, "生育信息": 27, "行程信息": 33}}, "entity_analysis": {"entity_type_distribution": {"姓名": 15, "地理位置": 19, "职业": 10, "医疗程序": 10, "疾病": 10, "药物": 10, "临床表现": 24, "国籍": 12, "民族": 17, "教育背景": 13, "性别": 14, "年龄": 11, "婚姻状况": 10, "政治倾向": 15, "家庭成员": 37, "工资数额": 12, "投资产品": 11, "税务记录": 17, "信用记录": 16, "实体资产": 19, "交易信息": 34, "过敏信息": 18, "生育信息": 27, "行程信息": 33}, "entity_length_analysis": {"姓名": {"count": 15, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "地理位置": {"count": 19, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "职业": {"count": 10, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "医疗程序": {"count": 10, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "疾病": {"count": 10, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "药物": {"count": 10, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "临床表现": {"count": 24, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "国籍": {"count": 12, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "民族": {"count": 17, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "教育背景": {"count": 13, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "性别": {"count": 14, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "年龄": {"count": 11, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "婚姻状况": {"count": 10, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "政治倾向": {"count": 15, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "家庭成员": {"count": 37, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "工资数额": {"count": 12, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "投资产品": {"count": 11, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "税务记录": {"count": 17, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "信用记录": {"count": 16, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "实体资产": {"count": 19, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "交易信息": {"count": 34, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "过敏信息": {"count": 18, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "生育信息": {"count": 27, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "行程信息": {"count": 33, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}}, "entity_position_analysis": {"姓名": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "地理位置": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "职业": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "医疗程序": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "疾病": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "药物": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "临床表现": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "国籍": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "民族": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "教育背景": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "性别": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "年龄": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "婚姻状况": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "政治倾向": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "家庭成员": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "工资数额": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "投资产品": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "税务记录": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "信用记录": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "实体资产": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "交易信息": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "过敏信息": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "生育信息": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "行程信息": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}}, "entity_density_analysis": {"mean_density": 0.06160361589124509, "median_density": 0.05128205128205128, "std_density": 0.03393535545956637, "max_density": 0.28}, "entity_overlap_analysis": {"total_overlaps": 0, "overlap_rate": 0.0, "overlap_details": []}}, "linguistic_analysis": {"vocabulary_analysis": {"total_words": 3793, "unique_words": 1090, "vocabulary_diversity": 0.2873714737674664, "total_chars": 6897, "unique_chars": 960, "most_common_words": [["。", 223], ["的", 201], ["，", 176], ["了", 79], ["和", 48], ["在", 44], ["我", 41], ["是", 36], ["为", 28], ["他", 28], ["公司", 26], ["年", 25], ["\"", 24], ["这位", 21], ["（", 20], ["小明", 20], ["张", 20], ["15", 20], ["）", 19], ["患者", 19]], "word_length_distribution": {"mean_word_length": 1.8187813241888684, "median_word_length": 2.0, "std_word_length": 1.0318717795758496, "word_length_distribution": {"single_char": 0.4175679240305988, "two_chars": 0.45106831970456346, "three_chars": 0.0670007913479293, "four_plus_chars": 0.06436296491690846}}}, "sentence_structure_analysis": {"mean_sentence_length": 16.563318777292576, "median_sentence_length": 16.0, "std_sentence_length": 4.557412445641834, "sentence_length_distribution": {"short": 0.017467248908296942, "medium": 0.7903930131004366, "long": 0.19213973799126638}}, "punctuation_analysis": {"total_punctuation": 490, "punctuation_diversity": 11, "punctuation_distribution": {"。": 223, "，": 176, "；": 2, "：": 6, "（": 20, "）": 19, "、": 17, "？": 1, "【": 1, "】": 1, "\"": 24}}, "word_frequency_analysis": {}}, "diversity_analysis": {"lexical_diversity": {"type_token_ratio": 0.2873714737674664, "hapax_legomena_ratio": 0.5871559633027523, "vocabulary_richness": 347.43659483770347}, "syntactic_diversity": {"unique_patterns": 9, "pattern_diversity": 0.039301310043668124, "most_common_patterns": [["MEDIUM_COMMA_END_PUNCT", 73], ["LONG_COMMA_END_PUNCT", 64], ["MEDIUM_END_PUNCT", 53], ["LONG_END_PUNCT", 29], ["LONG_COMMA_END_PUNCT_MID_PUNCT", 3], ["MEDIUM_MID_PUNCT", 2], ["LONG_COMMA", 2], ["MEDIUM_COMMA_MID_PUNCT", 2], ["MEDIUM_COMMA_END_PUNCT_MID_PUNCT", 1]]}, "semantic_diversity": {}, "entity_context_diversity": {"姓名": {"total_contexts": 15, "unique_contexts": 8, "context_diversity": 0.5333333333333333}, "地理位置": {"total_contexts": 19, "unique_contexts": 9, "context_diversity": 0.47368421052631576}, "职业": {"total_contexts": 10, "unique_contexts": 10, "context_diversity": 1.0}, "医疗程序": {"total_contexts": 10, "unique_contexts": 4, "context_diversity": 0.4}, "疾病": {"total_contexts": 10, "unique_contexts": 7, "context_diversity": 0.7}, "药物": {"total_contexts": 10, "unique_contexts": 7, "context_diversity": 0.7}, "临床表现": {"total_contexts": 24, "unique_contexts": 6, "context_diversity": 0.25}, "国籍": {"total_contexts": 12, "unique_contexts": 10, "context_diversity": 0.8333333333333334}, "民族": {"total_contexts": 17, "unique_contexts": 5, "context_diversity": 0.29411764705882354}, "教育背景": {"total_contexts": 13, "unique_contexts": 5, "context_diversity": 0.38461538461538464}, "性别": {"total_contexts": 14, "unique_contexts": 8, "context_diversity": 0.5714285714285714}, "年龄": {"total_contexts": 11, "unique_contexts": 5, "context_diversity": 0.45454545454545453}, "婚姻状况": {"total_contexts": 10, "unique_contexts": 6, "context_diversity": 0.6}, "政治倾向": {"total_contexts": 15, "unique_contexts": 7, "context_diversity": 0.4666666666666667}, "家庭成员": {"total_contexts": 37, "unique_contexts": 8, "context_diversity": 0.21621621621621623}, "工资数额": {"total_contexts": 12, "unique_contexts": 4, "context_diversity": 0.3333333333333333}, "投资产品": {"total_contexts": 11, "unique_contexts": 7, "context_diversity": 0.6363636363636364}, "税务记录": {"total_contexts": 17, "unique_contexts": 7, "context_diversity": 0.4117647058823529}, "信用记录": {"total_contexts": 16, "unique_contexts": 9, "context_diversity": 0.5625}, "实体资产": {"total_contexts": 19, "unique_contexts": 4, "context_diversity": 0.21052631578947367}, "交易信息": {"total_contexts": 34, "unique_contexts": 2, "context_diversity": 0.058823529411764705}, "过敏信息": {"total_contexts": 18, "unique_contexts": 1, "context_diversity": 0.05555555555555555}, "生育信息": {"total_contexts": 27, "unique_contexts": 4, "context_diversity": 0.14814814814814814}, "行程信息": {"total_contexts": 33, "unique_contexts": 4, "context_diversity": 0.12121212121212122}}}, "quality_indicators": {"annotation_quality": {"total_annotations": 414, "valid_annotations": 0, "boundary_accuracy": 0.0, "boundary_errors": 414, "type_consistency_errors": 0, "type_consistency_rate": 1.0}, "text_quality": {"mean_quality_score": 0.9768558951965067, "median_quality_score": 1.0, "std_quality_score": 0.044197662633641895, "quality_distribution": {"high": 1.0, "medium": 0.0, "low": 0.0}}, "consistency_indicators": {}}}, "strategy_metadata": {}, "convergence_indicators": {"gap_reduction_rate": 0.0, "diversity_improvement": 0.077, "stability_score": 0.7485597343025387}}