[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成10个多样化的、可以归类为性别的命名实体，每行一个。这些实体将用于命名实体识别任务中的性别类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 56}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. <PERSON>  \n2. <PERSON>  \n3. <PERSON>  \n4. <PERSON>  \n5. <PERSON>  \n6. <PERSON>  \n7. <PERSON>  \n8. <PERSON>  \n9. <PERSON>  \n10. <PERSON>", "role": "assistant"}}], "created": 1745144044, "id": "20250420181403b476137a831f462d", "model": "glm-4-air-250414", "request_id": "20250420181403b476137a831f462d", "usage": {"completion_tokens": 41, "prompt_tokens": 44, "total_tokens": 85}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个多样化的、可以归类为种族的命名实体，每行一个。这些实体将用于命名实体识别任务中的种族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 77}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Asian\n2. Black\n3. White\n4. Hispanic\n5. Native American\n6. African American\n7. Caucasian\n8. Latino\n9. Indigenous\n10. Middle Eastern\n11. Pacific Islander\n12. South Asian\n13. East Asian\n14. Southeast Asian\n15. Multi-racial\n16. Biracial\n17. Arab\n18. South Asian American\n19. North African\n20. Caribbean", "role": "assistant"}}], "created": 1745144045, "id": "202504201814020c0493b2bccd4d4c", "model": "glm-4-air-250414", "request_id": "202504201814020c0493b2bccd4d4c", "usage": {"completion_tokens": 95, "prompt_tokens": 44, "total_tokens": 139}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成30个多样化的、可以归类为国籍的命名实体，每行一个。这些实体将用于命名实体识别任务中的国籍类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 63}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. American\n2. Chinese\n3. British\n4. Indian\n5. Canadian\n6. Australian\n7. German\n8. French\n9. Japanese\n10. Brazilian\n11. Mexican\n12. Russian\n13. Italian\n14. Spanish\n15. South Korean\n16. Nigerian\n17. Egyptian\n18. Turkish\n19. Polish\n20. Dutch\n21. Swedish\n22. Norwegian\n23. Greek\n24. Portuguese\n25. Argentinian\n26. Iranian\n27. Filipino\n28. Vietnamese\n29. Thai\n30. New Zealander", "role": "assistant"}}], "created": 1745144046, "id": "20250420181403d5cf7be2d6de42f3", "model": "glm-4-air-250414", "request_id": "20250420181403d5cf7be2d6de42f3", "usage": {"completion_tokens": 127, "prompt_tokens": 44, "total_tokens": 171}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成30个多样化的、可以归类为政治倾向的命名实体，每行一个。这些实体将用于命名实体识别任务中的政治倾向类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 105}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. ConservativeParty\n2. LiberalDemocracy\n3. SocialistMovement\n4. RepublicanUnion\n5. GreenParty\n6. LabourCoalition\n7. ProgressiveAlliance\n8. NationalFront\n9. CommunistLeague\n10. LibertarianNetwork\n11. DemocratsForAmerica\n12. IndependenceSociety\n13. PatrioticUnion\n14. CentristGroup\n15. WorkersParty\n16. FreedomCoalition\n17. ModerateBloc\n18. JusticeParty\n19. SocialistInternational\n20. RightWingAlliance\n21. LeftWingCaucus\n22. FederalistSociety\n23. AnarchistCollective\n24. MonarchistLeague\n25. EnvironmentalBloc\n26. HumanistFront\n27. TechnocratGroup\n28. AgrarianParty\n29. MilitantFront\n30. SecularCoalition", "role": "assistant"}}], "created": 1745144047, "id": "20250420181403b94863cc332a4098", "model": "glm-4-air-250414", "request_id": "20250420181403b94863cc332a4098", "usage": {"completion_tokens": 179, "prompt_tokens": 46, "total_tokens": 225}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成30个多样化的、可以归类为婚姻状况的命名实体，每行一个。这些实体将用于命名实体识别任务中的婚姻状况类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 98}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 已婚\n2. 未婚\n3. 离异\n4. 丧偶\n5. 分居\n6. 同居\n7. 单身\n8. 已订婚\n9. 未订婚\n10. 恋爱中\n11. 已离婚\n12. 已丧偶\n13. 已分居\n14. 已同居\n15. 未婚同居\n16. 已订婚单身\n17. 恋爱中单身\n18. 已离婚单身\n19. 已丧偶单身\n20. 已分居单身\n21. 已同居单身\n22. 已婚未育\n23. 已婚有子女\n24. 未婚育有子女\n25. 离异未再婚\n26. 离异再婚\n27. 丧偶未再婚\n28. 丧偶再婚\n29. 分居未离婚\n30. 分居已离婚", "role": "assistant"}}], "created": 1745144048, "id": "2025042018140317efac71cc5e4b71", "model": "glm-4-air-250414", "request_id": "2025042018140317efac71cc5e4b71", "usage": {"completion_tokens": 201, "prompt_tokens": 46, "total_tokens": 247}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成40个多样化的、可以归类为家庭成员的命名实体，每行一个。这些实体将用于命名实体识别任务中的家庭成员类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 112}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 父亲\n2. 母亲\n3. 儿子\n4. 女儿\n5. 丈夫\n6. 妻子\n7. 爷爷\n8. 奶奶\n9. 外公\n10. 外婆\n11. 孙子\n12. 孙女\n13. 伯父\n14. 叔父\n15. 姑母\n16. 舅父\n17. 姨母\n18. 侄子\n19. 侄女\n20. 甥子\n21. 甥女\n22. 哥哥\n23. 弟弟\n24. 姐姐\n25. 妹妹\n26. 祖父\n27. 祖母\n28. 曾祖父\n29. 曾祖母\n30. 继父\n31. 继母\n32. 继子\n33. 继女\n34. 养父\n35. 养母\n36. 养子\n37. 养女\n38. 夫妻\n39. 兄弟\n40. 姐妹", "role": "assistant"}}], "created": 1745144049, "id": "2025042018140327740c3d436c4912", "model": "glm-4-air-250414", "request_id": "2025042018140327740c3d436c4912", "usage": {"completion_tokens": 252, "prompt_tokens": 44, "total_tokens": 296}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成40个多样化的、可以归类为临床表现的命名实体，每行一个。这些实体将用于命名实体识别任务中的临床表现类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 175}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 胸痛\n2. 呼吸困难\n3. 发热\n4. 咳嗽\n5. 头痛\n6. 腹痛\n7. 恶心\n8. 呕吐\n9. 腰痛\n10. 关节痛\n11. 皮疹\n12. 瘙痒\n13. 眩晕\n14. 意识模糊\n15. 肌肉无力\n16. 抽搐\n17. 惊厥\n18. 昏迷\n19. 黄疸\n20. 水肿\n21. 尿频\n22. 尿急\n23. 尿痛\n24. 便秘\n25. 腹泻\n26. 便血\n27. 黑便\n28. 咯血\n29. 鼻衄\n30. 月经过多\n31. 月经不调\n32. 阴道分泌物异常\n33. 乳房肿块\n34. 视力模糊\n35. 听力下降\n36. 嗅觉丧失\n37. 味觉异常\n38. 肢体麻木\n39. 胸闷\n40. 多汗", "role": "assistant"}}], "created": 1745144049, "id": "20250420181403802f6a93592244c5", "model": "glm-4-air-250414", "request_id": "20250420181403802f6a93592244c5", "usage": {"completion_tokens": 268, "prompt_tokens": 44, "total_tokens": 312}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成50个多样化的、可以归类为年龄的命名实体，每行一个。这些实体将用于命名实体识别任务中的年龄类型。\n请生成多样化的年龄表达，包括具体数字和年龄段描述。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 49}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 23岁  \n2. 七十岁  \n3. 30岁  \n4. 中年  \n5. 18岁  \n6. 老年人  \n7. 12岁  \n8. 儿童期  \n9. 45岁  \n10. 青少年  \n11. 5岁  \n12. 60岁  \n13. 幼儿  \n14. 35岁  \n15. 80岁  \n16. 青年  \n17. 50岁  \n18. 老年  \n19. 28岁  \n20. 10岁  \n21. 不惑之年  \n22. 40岁  \n23. 少年  \n24. 65岁  \n25. 25岁  \n26. 90岁  \n27. 花甲之年  \n28. 33岁  \n29. 70多岁  \n30. 20岁  \n31. 少年时期  \n32. 55岁  \n33. 38岁  \n34. 75岁  \n35. 青壮年  \n36. 16岁  \n37. 85岁  \n38. 早期成年  \n39. 48岁  \n40. 晚年  \n41. 29岁  \n42. 62岁  \n43. 初生儿  \n44. 39岁  \n45. 72岁  \n46. 42岁  \n47. 57岁  \n48. 36岁  \n49. 64岁  \n50. 53岁", "role": "assistant"}}], "created": **********, "id": "202504201814026ae5015b90484c22", "model": "glm-4-air-250414", "request_id": "202504201814026ae5015b90484c22", "usage": {"completion_tokens": 313, "prompt_tokens": 57, "total_tokens": 370}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成40个多样化的、可以归类为疾病的命名实体，每行一个。这些实体将用于命名实体识别任务中的疾病类型。\n请生成多样化的疾病名称，包括常见病、慢性病、罕见病等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 161}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 糖尿病  \n2. 高血压  \n3. 冠心病  \n4. 肺炎  \n5. 流感  \n6. 艾滋病  \n7. 癌症  \n8. 肝硬化  \n9. 肾衰竭  \n10. 脑卒中  \n11. 多发性硬化  \n12. 系统性红斑狼疮  \n13. 帕金森病  \n14. 阿尔茨海默病  \n15. 克罗恩病  \n16. 胰腺炎  \n17. 病毒性肝炎  \n18. 结核病  \n19. 骨质疏松症  \n20. 风湿性关节炎  \n21. 强直性脊柱炎  \n22. 白血病  \n23. 地中海贫血  \n24. 囊性纤维化  \n25. 尿毒症  \n26. 脊髓灰质炎  \n27. 猩红热  \n28. 麻疹  \n29. 天花  \n30. 布鲁氏菌病  \n31. 莱姆病  \n32. 疟疾  \n33. 黄热病  \n34. 非洲昏睡病  \n35. 肠易激综合征  \n36. 睡眠呼吸暂停综合征  \n37. 乳糜泻  \n38. 肌萎缩侧索硬化症  \n39. 先天性心脏病  \n40. 神经纤维瘤病", "role": "assistant"}}], "created": **********, "id": "20250420181403d8a49a7da31e42a4", "model": "glm-4-air-250414", "request_id": "20250420181403d8a49a7da31e42a4", "usage": {"completion_tokens": 309, "prompt_tokens": 60, "total_tokens": 369}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成60个多样化的、可以归类为职业的命名实体，每行一个。这些实体将用于命名实体识别任务中的职业类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 70}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Software Engineer  \n2. Data Scientist  \n3. Financial Analyst  \n4. Marketing Manager  \n5. Human Resources Specialist  \n6. Project Manager  \n7. Accountant  \n8. Electrician  \n9. Chef  \n10. Teacher  \n11. Doctor  \n12. Nurse  \n13. Lawyer  \n14. Architect  \n15. Mechanical Engineer  \n16. Sales Representative  \n17. Customer Service Rep  \n18. Web Developer  \n19. Graphic Designer  \n20. Translator  \n21. Researcher  \n22. IT Consultant  \n23. Retail Associate  \n24. Pharmacist  \n25. Veterinarian  \n26. Journalist  \n27. Flight Attendant  \n28. Security Guard  \n29. Carpenter  \n30. Librarian  \n31. Psychologist  \n32. Actuary  \n33. Consultant  \n34. Truck Driver  \n35. Radiologist  \n36. Physiotherapist  \n37. Economist  \n38. Plumber  \n39. Surveyor  \n40. Paralegal  \n41. Biologist  \n42. Chemist  \n43. Astronomer  \n44. Historian  \n45. Fashion Designer  \n46. Real Estate Agent  \n47. Insurance Agent  \n48. Biomedical Engineer  \n49. Urban Planner  \n50. Environmental Scientist  \n51. Sound Engineer  \n52. Interior Designer  \n53. Flight Instructor  \n54. Agricultural Scientist  \n55. Mining Engineer  \n56. Diplomat  \n57. Flight Surgeon  \n58. Optometrist  \n59. Chiropractor  \n60. Social Worker", "role": "assistant"}}], "created": **********, "id": "20250420181403a08bc29360d64b3a", "model": "glm-4-air-250414", "request_id": "20250420181403a08bc29360d64b3a", "usage": {"completion_tokens": 304, "prompt_tokens": 44, "total_tokens": 348}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成60个多样化的、可以归类为信用记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的信用记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 140}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. CreditScore\n2. FICO_Score\n3. Equifax_Report\n4. Experian_Record\n5. TransUnion_Bureau\n6. DebtToIncomeRatio\n7. PaymentHistory\n8. OpenAccounts\n9. ClosedAccounts\n10. PublicRecords\n11. BankruptcyFilings\n12. ForeclosureStatus\n13. TaxLiens\n14. CivilJudgments\n15. CollectionsAccounts\n16. CreditInquiries\n17. HardInquiries\n18. SoftInquiries\n19. CreditLimit\n20. AvailableCredit\n21. CreditUtilization\n22. RevolvingCredit\n23. InstallmentLoans\n24. MortgageHistory\n25. AutoLoanRecords\n26. StudentLoanHistory\n27. PersonalLoanDetails\n28. CreditCardHistory\n29. OverduePayments\n30. LatePaymentFlags\n31. DelinquencyStatus\n32. DefaultRecords\n33. ChargeOffs\n34. WriteOffs\n35. CreditWorthiness\n36. CreditRating\n37. RiskAssessment\n38. CreditBureauData\n39. CreditMonitoringReport\n40. IdentityTheftFlags\n41. FraudAlerts\n42. CreditFreezeStatus\n43. CreditThawStatus\n44. CreditMonitoringService\n45. CreditScoreHistory\n46. CreditImprovementPlan\n47. CreditDisputeLog\n48. CreditCorrectionRequest\n49. CreditVerificationReport\n50. CreditProfileSummary\n51. CreditBehaviorMetrics\n52. CreditEligibility\n53. CreditApplicationHistory\n54. CreditApprovalStatus\n55. CreditDenialReasons\n56. CreditRecoveryPlan\n57. CreditEnhancementReport\n58. CreditAssessmentScore\n59. CreditHealthIndicator\n60. CreditComplianceReport", "role": "assistant"}}], "created": **********, "id": "20250420181402d85aa336d4d24ad4", "model": "glm-4-air-250414", "request_id": "20250420181402d85aa336d4d24ad4", "usage": {"completion_tokens": 370, "prompt_tokens": 46, "total_tokens": 416}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成60个多样化的、可以归类为实体资产的命名实体，每行一个。这些实体将用于命名实体识别任务中的实体资产类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 147}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 银行账户\n2. 信用卡\n3. 投资组合\n4. 股票\n5. 债券\n6. 基金份额\n7. 退休金账户\n8. 现金储备\n9. 股权\n10. 不动产\n11. 土地\n12. 住宅\n13. 商业地产\n14. 写字楼\n15. 零售店面\n16. 工业厂房\n17. 租赁物业\n18. 车辆\n19. 汽车\n20. 卡车\n21. 摩托车\n22. 船舶\n23. 飞机\n24. 设备\n25. 机器\n26. 工具\n27. 专利\n28. 商标\n29. 版权\n30. 商业秘密\n31. 知识产权\n32. 数据库\n33. 服务器\n34. 计算机硬件\n35. 网络设备\n36. 通信线路\n37. 软件\n38. 应用程序\n39. 电子书\n40. 音乐版权\n41. 电影版权\n42. 艺术品\n43. 古董\n44. 首饰\n45. 黄金\n46. 白银\n47. 贵金属\n48. 艺术收藏品\n49. 珍稀邮票\n50. 藏书\n51. 储蓄账户\n52. 保险单\n53. 信托基金\n54. 退休计划\n55. 养老金\n56. 期权\n57. 期货合约\n58. 互换协议\n59. 衍生品\n60. 金融衍生品", "role": "assistant"}}], "created": 1745144052, "id": "20250420181403062fec6161d1446f", "model": "glm-4-air-250414", "request_id": "20250420181403062fec6161d1446f", "usage": {"completion_tokens": 372, "prompt_tokens": 45, "total_tokens": 417}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成50个多样化的、可以归类为生育信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的生育信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 196}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 胎儿性别\n2. 孕周数\n3. 流产史\n4. 剖宫产记录\n5. 妊娠并发症\n6. 产前检查日期\n7. 分娩方式\n8. 婴儿出生体重\n9. 人工授精记录\n10. 辅助生殖技术\n11. 不孕不育诊断\n12. 产前筛查结果\n13. 孕期体重增长\n14. 哺乳期持续时间\n15. 子宫大小\n16. 卵巢功能评估\n17. 卵泡监测数据\n18. 促排卵治疗记录\n19. 试管婴儿周期\n20. 胎心监测数据\n21. 胎位信息\n22. 孕酮水平\n23. HCG检测值\n24. 胎儿发育评估\n25. 胎盘位置\n26. 羊水量\n27. 胎膜状态\n28. 宫颈长度\n29. 早产风险评分\n30. 胎儿畸形筛查\n31. 产前诊断报告\n32. 产后恢复计划\n33. 输卵管通畅性\n34. 子宫内膜厚度\n35. 卵巢储备功能\n36. 妊娠糖尿病史\n37. 妊娠高血压记录\n38. 双胞胎妊娠确认\n39. 多胎妊娠管理\n40. 胎儿遗传病筛查\n41. 产前基因检测\n42. 孕期用药记录\n43. 妊娠合并症列表\n44. 人工流产次数\n45. 自然流产次数\n46. 输卵管手术史\n47. 子宫手术史\n48. 卵巢手术史\n49. 产前心理咨询记录\n50. 生育能力评估报告", "role": "assistant"}}], "created": 1745144052, "id": "202504201814033045393204764609", "model": "glm-4-air-250414", "request_id": "202504201814033045393204764609", "usage": {"completion_tokens": 393, "prompt_tokens": 45, "total_tokens": 438}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成56个多样化的、可以归类为民族的命名实体，每行一个。这些实体将用于命名实体识别任务中的民族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 84}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 汉族\n2. 藏族\n3. 蒙古族\n4. 回族\n5. 维吾尔族\n6. 苗族\n7. 彝族\n8. 壮族\n9. 布依族\n10. 朝鲜族\n11. 满族\n12. 侗族\n13. 瑶族\n14. 白族\n15. 土家族\n16. 哈尼族\n17. 哈萨克族\n18. 黎族\n19. 傣族\n20. 畲族\n21. 高山族\n22. 拉祜族\n23. 水族\n24. 东乡族\n25. 纳西族\n26. 景颇族\n27. 柯尔克孜族\n28. 土族\n29. 达斡尔族\n30. 仫佬族\n31. 羌族\n32. 布朗族\n33. 撒拉族\n34. 毛南族\n35. 仡佬族\n36. 锡伯族\n37. 阿昌族\n38. 普米族\n39. 鄂温克族\n40. 德昂族\n41. 保安族\n42. 裕固族\n43. 京族\n44. 塔吉克族\n45. 独龙族\n46. 鄂伦春族\n47. 赫哲族\n48. 门巴族\n49. 珞巴族\n50. 基诺族\n51. 美洲原住民\n52. 非洲裔\n53. 欧洲白人\n54. 亚洲裔\n55. 澳大利亚原住民\n56. 爱斯基摩人", "role": "assistant"}}], "created": 1745144052, "id": "20250420181402a07f794ec6c04227", "model": "glm-4-air-250414", "request_id": "20250420181402a07f794ec6c04227", "usage": {"completion_tokens": 411, "prompt_tokens": 43, "total_tokens": 454}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成50个多样化的、可以归类为药物的命名实体，每行一个。这些实体将用于命名实体识别任务中的药物类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 168}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n以下是为命名实体识别任务生成的50个多样化的药物命名实体，每行一个：\n\n1. 阿司匹林\n2. 头孢克肟\n3. 布洛芬\n4. 甲硝唑\n5. 红霉素\n6. 地高辛\n7. 吲哚美辛\n8. 氟康唑\n9. 哌替啶\n10. 阿莫西林\n11. 硝酸甘油\n12. 左氧氟沙星\n13. 氯雷他定\n14. 青霉素V钾\n15. 阿托伐他汀\n16. 奥美拉唑\n17. 吡拉西坦\n18. 氨茶碱\n19. 环丙沙星\n20. 依那普利\n21. 格列美脲\n22. 酮康唑\n23. 羟苯磺酸钙\n24. 醛氢叶酸\n25. 奥氮平\n26. 丙戊酸钠\n27. 莫西沙星\n28. 阿立哌唑\n29. 氟哌啶醇\n30. 磺胺嘧啶\n31. 硝苯地平\n32. 葛根素\n33. 三七总皂苷\n34. 丹参酮\n35. 人参皂苷\n36. 黄芪多糖\n37. 苦参碱\n38. 茶碱缓释片\n39. 阿米卡星\n40. 伏立康唑\n41. 埃索美拉唑\n42. 奥沙西泮\n43. 苯妥英钠\n44. 卡马西平\n45. 拉莫三嗪\n46. 扑热息痛\n47. 双氯芬酸钠\n48. 甲氨蝶呤\n49. 氯吡格雷\n50. 替格瑞洛", "role": "assistant"}}], "created": 1745144053, "id": "2025042018140343b5308eb03e478e", "model": "glm-4-air-250414", "request_id": "2025042018140343b5308eb03e478e", "usage": {"completion_tokens": 436, "prompt_tokens": 43, "total_tokens": 479}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成50个多样化的、可以归类为过敏信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的过敏信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 189}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n以下是为命名实体识别任务生成的50个多样化的过敏信息命名实体，每行一个：\n\n1. 青霉素过敏\n2. 花生过敏\n3. 牛奶过敏\n4. 鸡蛋过敏\n5. 尼龙过敏\n6. 水杨酸过敏\n7. 氨基比林过敏\n8. 阿司匹林过敏\n9. 磺胺类药物过敏\n10. 贝类过敏\n11. 海鲜过敏\n12. 螃蟹过敏\n13. 芒果过敏\n14. 荔枝过敏\n15. 柑橘过敏\n16. 杏仁过敏\n17. 核桃过敏\n18. 小麦过敏\n19. 大豆过敏\n20. 芝麻过敏\n21. 麻黄素过敏\n22. 青霉素G过敏\n23. 青霉素V过敏\n24. 头孢菌素过敏\n25. 链霉素过敏\n26. 土霉素过敏\n27. 氯霉素过敏\n28. 红霉素过敏\n29. 磺胺甲噁唑过敏\n30. 甲氧苄啶过敏\n31. 阿莫西林过敏\n32. 氨苄西林过敏\n33. 头孢呋辛过敏\n34. 头孢曲松过敏\n35. 磺胺嘧啶过敏\n36. 氯喹过敏\n37. 奎宁过敏\n38. 水杨酸盐过敏\n39. 对乙酰氨基酚过敏\n40. 布洛芬过敏\n41. 双氯芬酸过敏\n42. 美洛昔康过敏\n43. 舒林酸过敏\n44. 吲哚美辛过敏\n45. 阿司匹林三联征\n46. 青霉素酶过敏\n47. 头孢克肟过敏\n48. 头孢氨苄过敏\n49. 青霉素V钾过敏\n50. 头孢他啶过敏", "role": "assistant"}}], "created": **********, "id": "20250420181403d53a881fc8634598", "model": "glm-4-air-250414", "request_id": "20250420181403d53a881fc8634598", "usage": {"completion_tokens": 451, "prompt_tokens": 45, "total_tokens": 496}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成60个多样化的、可以归类为教育背景的命名实体，每行一个。这些实体将用于命名实体识别任务中的教育背景类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 91}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Bachelor of Science in Computer Science\n2. Master of Business Administration\n3. Doctor of Philosophy in Economics\n4. Associate Degree in Nursing\n5. High School Diploma\n6. Bachelor of Arts in English Literature\n7. Master of Public Health\n8. Doctor of Medicine\n9. Bachelor of Engineering in Electrical Engineering\n10. Master of Fine Arts in Film Production\n11. Ph.D. in Psychology\n12. Associate of Applied Science in Automotive Technology\n13. High School Certificate\n14. Bachelor of Science in Biology\n15. Master of Education\n16. Juris Doctor\n17. Bachelor of Arts in History\n18. Master of Science in Data Science\n19. Doctor of Dental Surgery\n20. Associate Degree in Graphic Design\n21. Secondary School Leaving Certificate\n22. Bachelor of Science in Physics\n23. Master of Arts in Sociology\n24. Doctor of Pharmacy\n25. Bachelor of Arts in Political Science\n26. Master of Engineering Management\n27. Ph.D. in Computer Science\n28. Associate Degree in Information Technology\n29. General Certificate of Education\n30. Bachelor of Science in Chemistry\n31. Master of Criminal Justice\n32. Doctor of Veterinary Medicine\n33. Bachelor of Arts in Psychology\n34. Master of Science in Environmental Science\n35. Juris Master\n36. Associate Degree in Early Childhood Education\n37. High School Graduation Diploma\n38. Bachelor of Science in Mathematics\n39. Master of Library Science\n40. Doctor of Optometry\n41. Bachelor of Arts in Communications\n42. Master of Urban Planning\n43. Ph.D. in Chemistry\n44. Associate Degree in Respiratory Therapy\n45. Secondary School Certificate\n46. Bachelor of Science in Aerospace Engineering\n47. Master of Theology\n48. Doctor of Chiropractic\n49. Bachelor of Arts in Sociology\n50. Master of Accounting\n51. Ph.D. in Physics\n52. Associate Degree in Radiologic Technology\n53. High School Leaving Certificate\n54. Bachelor of Science in Environmental Engineering\n55. Master of Science in Nursing\n56. Doctor of Physical Therapy\n57. Bachelor of Arts in Economics\n58. Master of Technology Management\n59. Ph.D. in Mathematics\n60. Associate Degree in Web Development", "role": "assistant"}}], "created": **********, "id": "2025042018140267426be6c6274a46", "model": "glm-4-air-250414", "request_id": "2025042018140267426be6c6274a46", "usage": {"completion_tokens": 463, "prompt_tokens": 46, "total_tokens": 509}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成50个多样化的、可以归类为医疗程序的命名实体，每行一个。这些实体将用于命名实体识别任务中的医疗程序类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 182}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 心脏搭桥手术\n2. 关节置换术\n3. 胆囊切除术\n4. 胰腺移植手术\n5. 白内障手术\n6. 脑电图检查\n7. 核磁共振成像\n8. 骨密度检测\n9. 胃镜检查\n10. 肺功能测试\n11. 肾透析治疗\n12. 前列腺切除术\n13. 子宫肌瘤剔除术\n14. 颈椎病治疗\n15. 脊柱侧弯矫正\n16. 肝脏穿刺活检\n17. 肺癌根治术\n18. 腹腔镜手术\n19. 乳房切除术\n20. 结肠镜检查\n21. 静脉曲张治疗\n22. 尿液分析检测\n23. 心脏瓣膜置换\n24. 肾上腺切除术\n25. 鼻窦炎手术\n26. 腰椎间盘突出治疗\n27. 甲状腺功能检查\n28. 脑脊液检查\n29. 眼底检查\n30. 骨髓移植手术\n31. 人工关节置换\n32. 肝功能测试\n33. 胸腔镜手术\n34. 肾脏移植手术\n35. 子宫切除术\n36. 前列腺穿刺活检\n37. 肺癌化疗\n38. 胃癌根治术\n39. 颈动脉支架植入\n40. 脑部CT扫描\n41. 糖尿病足治疗\n42. 骨折内固定手术\n43. 肾功能测试\n44. 胆结石手术\n45. 肝癌射频消融\n46. 肾结石体外碎石\n47. 肺炎住院治疗\n48. 胰腺炎保守治疗\n49. 脑出血开颅手术\n50. 肾囊肿穿刺抽液", "role": "assistant"}}], "created": **********, "id": "202504201814032c6c04de207e40d4", "model": "glm-4-air-250414", "request_id": "202504201814032c6c04de207e40d4", "usage": {"completion_tokens": 428, "prompt_tokens": 45, "total_tokens": 473}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成70个多样化的、可以归类为交易信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的交易信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 154}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 交易ID\n2. 购买金额\n3. 支付日期\n4. 信用卡号\n5. CVV码\n6. 过期日期\n7. 发票号码\n8. 订单号\n9. 收据编号\n10. 支付方式\n11. 银行账号\n12. 支票号码\n13. 电子钱包ID\n14. 转账参考号\n15. 交易时间戳\n16. 商户ID\n17. 客户ID\n18. 账单地址\n19. 配送地址\n20. 商品代码\n21. 产品序列号\n22. SKU编号\n23. 库存单位\n24. 单价\n25. 数量\n26. 折扣代码\n27. 税额\n28. 运费\n29. 总金额\n30. 部分付款金额\n31. 余额\n32. 退款金额\n33. 交易状态\n34. 交易类型\n35. 汇率\n36. 货币代码\n37. 交易地点\n38. IP地址\n39. 设备ID\n40. 浏览器指纹\n41. 用户代理\n42. 会话ID\n43. 登录时间\n44. 登出时间\n45. 订单创建时间\n46. 支付确认时间\n47. 发货日期\n48. 到达日期\n49. 追踪号码\n50. 物流公司ID\n51. 订单备注\n52. 客户备注\n53. 商户备注\n54. 支付网关ID\n55. 批准代码\n56. 拒绝代码\n57. 争议编号\n58. 仲裁编号\n59. 退款原因\n60. 退款状态\n61. 交易发起人\n62. 交易接收人\n63. 转账备注\n64. 交易备注\n65. 账单周期\n66. 结算日期\n67. 结算金额\n68. 清算机构\n69. 交易验证码\n70. 安全令牌", "role": "assistant"}}], "created": **********, "id": "2025042018140309e663f8379f4946", "model": "glm-4-air-250414", "request_id": "2025042018140309e663f8379f4946", "usage": {"completion_tokens": 460, "prompt_tokens": 45, "total_tokens": 505}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成80个多样化的、可以归类为姓名的命名实体，每行一个。这些实体将用于命名实体识别任务中的姓名类型。\n请生成多样化的中文姓名，包括不同性别、年龄段和地区特色的姓名。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 42}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 张伟\n2. 王芳\n3. 李娜\n4. 刘洋\n5. 陈明\n6. 杨丽\n7. 赵强\n8. 黄静\n9. 周杰\n10. 吴敏\n11. 郑宇\n12. 朱婷\n13. 孙浩\n14. 徐静\n15. 马超\n16. 胡兰\n17. 林峰\n18. 高翔\n19. 郭静\n20. 何琳\n21. 邓超\n22. 梁静\n23. 宋宇\n24. 谢婷\n25. 唐杰\n26. 许静\n27. 曹宇\n28. 韩梅\n29. 彭强\n30. 吕静\n31. 苏浩\n32. 董静\n33. 丁宇\n34. 沈婷\n35. 韦强\n36. 龙静\n37. 叶峰\n38. 阮静\n39. 杜宇\n40. 毕婷\n41. 贺强\n42. 供静\n43. 程峰\n44. 曾婷\n45. 丁强\n46. 费静\n47. 骆宇\n48. 赖婷\n49. 夏强\n50. 廖静\n51. 武峰\n52. 谭婷\n53. 钦强\n54. 卢静\n55. 戴宇\n56. 谯婷\n57. 钟强\n58. 蒙静\n59. 贡峰\n60. 崔婷\n61. 廉强\n62. 霍静\n63. 廖峰\n64. 姜婷\n65. 屈强\n66. 靳静\n67. 樊峰\n68. 裘婷\n69. 谌强\n70. 屠静\n71. 路峰\n72. 严婷\n73. 荣强\n74. 岳静\n75. 和峰\n76. 芦婷\n77. 骆强\n78. 欧静\n79. 靳峰\n80. 佘婷", "role": "assistant"}}], "created": 1745144055, "id": "202504201814031089929a63cb49b0", "model": "glm-4-air-250414", "request_id": "202504201814031089929a63cb49b0", "usage": {"completion_tokens": 499, "prompt_tokens": 60, "total_tokens": 559}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成70个多样化的、可以归类为行程信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的行程信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 210}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 机票订单号\n2. 旅行路线\n3. 旅行套餐\n4. 旅行计划\n5. 旅行日程\n6. 旅行安排\n7. 旅行时间表\n8. 旅行日志\n9. 旅行日记\n10. 旅行笔记\n11. 旅行备忘录\n12. 旅行指南\n13. 旅行手册\n14. 旅行地图\n15. 旅行路线图\n16. 旅行行程单\n17. 旅行路线规划\n18. 旅行路线设计\n19. 旅行路线方案\n20. 旅行路线建议\n21. 旅行路线推荐\n22. 旅行路线查询\n23. 旅行路线预订\n24. 旅行路线变更\n25. 旅行路线取消\n26. 旅行路线确认\n27. 旅行路线修改\n28. 旅行路线查询结果\n29. 旅行路线预订记录\n30. 旅行路线变更记录\n31. 旅行路线取消记录\n32. 旅行路线确认记录\n33. 旅行路线修改记录\n34. 旅行路线查询历史\n35. 旅行路线预订历史\n36. 旅行路线变更历史\n37. 旅行路线取消历史\n38. 旅行路线确认历史\n39. 旅行路线修改历史\n40. 旅行路线查询日志\n41. 旅行路线预订日志\n42. 旅行路线变更日志\n43. 旅行路线取消日志\n44. 旅行路线确认日志\n45. 旅行路线修改日志\n46. 旅行路线查询数据\n47. 旅行路线预订数据\n48. 旅行路线变更数据\n49. 旅行路线取消数据\n50. 旅行路线确认数据\n51. 旅行路线修改数据\n52. 旅行路线查询信息\n53. 旅行路线预订信息\n54. 旅行路线变更信息\n55. 旅行路线取消信息\n56. 旅行路线确认信息\n57. 旅行路线修改信息\n58. 旅行路线查询详情\n59. 旅行路线预订详情\n60. 旅行路线变更详情\n61. 旅行路线取消详情\n62. 旅行路线确认详情\n63. 旅行路线修改详情\n64. 旅行路线查询备注\n65. 旅行路线预订备注\n66. 旅行路线变更备注\n67. 旅行路线取消备注\n68. 旅行路线确认备注\n69. 旅行路线修改备注\n70. 旅行路线查询状态", "role": "assistant"}}], "created": 1745144055, "id": "202504201814033543e57760954715", "model": "glm-4-air-250414", "request_id": "202504201814033543e57760954715", "usage": {"completion_tokens": 523, "prompt_tokens": 45, "total_tokens": 568}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成60个多样化的、可以归类为地理位置的命名实体，每行一个。这些实体将用于命名实体识别任务中的地理位置类型。\n请生成多样化的地理位置，包括省市区县、街道地址、建筑物名称等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 203}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 北京市\n2. 上海市\n3. 广州市\n4. 深圳市\n5. 天津市\n6. 重庆市\n7. 河北省石家庄市\n8. 江苏省南京市\n9. 浙江省杭州市\n10. 福建省福州市\n11. 山东省济南市\n12. 湖北省武汉市\n13. 湖南省长沙市\n14. 四川省成都市\n15. 陕西省西安市\n16. 云南省昆明市\n17. 广东省深圳市南山区\n18. 北京市海淀区中关村大街\n19. 上海市浦东新区陆家嘴\n20. 北京市朝阳区建国门外大街\n21. 广州市天河区珠江新城\n22. 深圳市福田区深南大道\n23. 重庆市渝中区解放碑\n24. 天津市和平区南京路\n25. 南京市鼓楼区中山北路\n26. 杭州市西湖区西湖文化广场\n27. 福州市鼓楼区五四路\n28. 济南市历下区泉城路\n29. 武汉市江汉区解放大道\n30. 长沙市芙蓉区五一大道\n31. 成都市锦江区春熙路\n32. 西安市雁塔区大雁塔\n33. 昆明市五华区翠湖\n34. 石家庄市长安区中山路\n35. 北京市东城区王府井\n36. 上海市静安区南京西路\n37. 广州市越秀区北京路\n38. 深圳市罗湖区深南东路\n39. 天津市河西区友谊路\n40. 重庆市沙坪坝区三峡广场\n41. 南京市玄武区太平北路\n42. 杭州市拱墅区拱宸桥\n43. 福州市台江区闽江路\n44. 济南市市中区经十路\n45. 武汉市洪山区光谷广场\n46. 长沙市天心区黄兴路\n47. 成都市武侯区锦里\n48. 西安市新城区解放路\n49. 昆明市盘龙区东风东路\n50. 石家庄市桥西区中华大街\n51. 北京市西城区西单\n52. 上海市徐汇区衡山路\n53. 广州市荔湾区上下九\n54. 深圳市宝安区宝安大道\n55. 天津市南开区南开三马路\n56. 重庆市江北区观音桥\n57. 南京市秦淮区夫子庙\n58. 杭州市上城区吴山广场\n59. 福州市仓山区仓前路\n60. 济南市槐荫区经一纬八路", "role": "assistant"}}], "created": 1745144056, "id": "202504201814024f6f72ee8c024745", "model": "glm-4-air-250414", "request_id": "202504201814024f6f72ee8c024745", "usage": {"completion_tokens": 546, "prompt_tokens": 61, "total_tokens": 607}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成70个多样化的、可以归类为投资产品的命名实体，每行一个。这些实体将用于命名实体识别任务中的投资产品类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 126}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 增利债券基金\n2. 稳健收益债券\n3. 高息定存计划\n4. 股票指数基金\n5. 优质蓝筹股票\n6. 活期理财通\n7. 稳盈货币基金\n8. 双币黄金ETF\n9. 新兴市场债券\n10. 科技成长基金\n11. 保守型年金保险\n12. 定投指数计划\n13. 全球债券组合\n14. 风险平衡组合\n15. 可转债基金\n16. 高收益债券信托\n17. 房地产投资信托\n18. 量化对冲基金\n19. 环保主题基金\n20. 退休储蓄计划\n21. 贵金属投资账户\n22. 互联网债券基金\n23. 医疗健康ETF\n24. 消费升级基金\n25. 短期国债组合\n26. 大盘蓝筹基金\n27. 跨境理财通\n28. 混合资产配置\n29. 稳健型养老金\n30. 农业主题投资\n31. 能源资源基金\n32. 智能投顾账户\n33. 税延养老保险\n34. 小盘成长基金\n35. 固定收益计划\n36. 汇率挂钩债券\n37. 数字货币基金\n38. 生命周期基金\n39. 房地产发展信托\n40. 跨境股票基金\n41. 价值投资组合\n42. 可再生能源基金\n43. 风险平准基金\n44. 通胀挂钩债券\n45. 主动管理型基金\n46. 消费品牌投资\n47. 证券借贷计划\n48. 债券增强基金\n49. 农产品期货基金\n50. 指数增强策略\n51. 退休目标日期基金\n52. 普惠金融债券\n53. 矿产资源信托\n54. 纯债基金\n55. 智能指数基金\n56. 银行理财宝\n57. 股债平衡计划\n58. 创业板基金\n59. 跨境ETF\n60. 环保产业基金\n61. 货币市场基金\n62. 稳健型债券组合\n63. 税收优化投资\n64. 生物科技基金\n65. 高收益信托计划\n66. 消费者信用债券\n67. 智能债券基金\n68. 股票增强基金\n69. 稳定收益计划\n70. 房地产增值基金", "role": "assistant"}}], "created": 1745144056, "id": "202504201814038b398875f3364e24", "model": "glm-4-air-250414", "request_id": "202504201814038b398875f3364e24", "usage": {"completion_tokens": 564, "prompt_tokens": 45, "total_tokens": 609}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成60个多样化的、可以归类为税务记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的税务记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 133}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 税务申报表\n2. 所得税表格\n3. 增值税发票\n4. 企业所得税报告\n5. 个人所得税记录\n6. 税务缴纳凭证\n7. 税务登记证\n8. 税务审计文件\n9. 税务缴纳通知\n10. 税务抵扣证明\n11. 税务减免申请\n12. 税务豁免文件\n13. 税务检查记录\n14. 税务咨询报告\n15. 税务合规文件\n16. 税务调整通知\n17. 税务评估报告\n18. 税务争议解决记录\n19. 税务行政复议文件\n20. 税务诉讼文件\n21. 税务代理合同\n22. 税务顾问记录\n23. 税务筹划方案\n24. 税务风险评估报告\n25. 税务审计报告\n26. 税务稽查文件\n27. 税务罚款通知\n28. 税务滞纳金记录\n29. 税务补缴通知\n30. 税务退税申请\n31. 税务发票管理记录\n32. 税务账簿记录\n33. 税务收据存根\n34. 税务缴纳清单\n35. 税务数据备份文件\n36. 税务电子档案\n37. 税务纸质档案\n38. 税务分类账\n39. 税务总账\n40. 税务明细账\n41. 税务往来凭证\n42. 税务银行对账单\n43. 税务现金流量表\n44. 税务资产负债表\n45. 税务利润表\n46. 税务成本核算表\n47. 税务费用明细表\n48. 税务销售收入表\n49. 税务资产清单\n50. 税务负债清单\n51. 税务股权结构表\n52. 税务关联交易记录\n53. 税务对外投资表\n54. 税务捐赠记录\n55. 税务公益支出表\n56. 税务福利支出记录\n57. 税务工资发放表\n58. 税务社保缴纳记录\n59. 税务公积金缴纳记录\n60. 税务其他收入记录", "role": "assistant"}}], "created": 1745144056, "id": "2025042018140374dd4299d1964247", "model": "glm-4-air-250414", "request_id": "2025042018140374dd4299d1964247", "usage": {"completion_tokens": 559, "prompt_tokens": 46, "total_tokens": 605}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成80个多样化的、可以归类为工资数额的命名实体，每行一个。这些实体将用于命名实体识别任务中的工资数额类型。\n请生成多样化的工资金额表达，包括月薪、年薪、时薪等不同计量方式。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 119}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 月薪10,000元  \n2. 年薪200,000元  \n3. 时薪50元  \n4. 月薪8,500美元  \n5. 年薪150,000美元  \n6. 时薪30美元  \n7. 月薪12,000欧元  \n8. 年薪300,000欧元  \n9. 时薪40欧元  \n10. 月薪9,000英镑  \n11. 年薪180,000英镑  \n12. 时薪35英镑  \n13. 月薪15,000元  \n14. 年薪250,000元  \n15. 时薪60元  \n16. 月薪7,000美元  \n17. 年薪120,000美元  \n18. 时薪25美元  \n19. 月薪11,000欧元  \n20. 年薪280,000欧元  \n21. 时薪45欧元  \n22. 月薪10,500英镑  \n23. 年薪200,000英镑  \n24. 时薪38英镑  \n25. 月薪13,000元  \n26. 年薪220,000元  \n27. 时薪55元  \n28. 月薪9,500美元  \n29. 年薪170,000美元  \n30. 时薪32美元  \n31. 月薪14,000欧元  \n32. 年薪320,000欧元  \n33. 时薪48欧元  \n34. 月薪8,000英镑  \n35. 年薪160,000英镑  \n36. 时薪36英镑  \n37. 月薪16,000元  \n38. 年薪270,000元  \n39. 时薪65元  \n40. 月薪7,500美元  \n41. 年薪130,000美元  \n42. 时薪28美元  \n43. 月薪12,500欧元  \n44. 年薪290,000欧元  \n45. 时薪42欧元  \n46. 月薪10,000英镑  \n47. 年薪190,000英镑  \n48. 时薪39英镑  \n49. 月薪18,000元  \n50. 年薪350,000元  \n51. 时薪70元  \n52. 月薪9,000美元  \n53. 年薪140,000美元  \n54. 时薪33美元  \n55. 月薪13,500欧元  \n56. 年薪310,000欧元  \n57. 时薪47欧元  \n58. 月薪8,500英镑  \n59. 年薪170,000英镑  \n60. 时薪37英镑  \n61. 月薪17,000元  \n62. 年薪260,000元  \n63. 时薪62元  \n64. 月薪8,200美元  \n65. 年薪135,000美元  \n66. 时薪27美元  \n67. 月薪11,800欧元  \n68. 年薪270,000欧元  \n69. 时薪43欧元  \n70. 月薪9,800英镑  \n71. 年薪185,000英镑  \n72. 时薪34英镑  \n73. 月薪15,500元  \n74. 年薪240,000元  \n75. 时薪58元  \n76. 月薪7,800美元  \n77. 年薪145,000美元  \n78. 时薪29美元  \n79. 月薪12,300欧元  \n80. 年薪230,000欧元", "role": "assistant"}}], "created": 1745144059, "id": "202504201814036e5433e32d6e4fdd", "model": "glm-4-air-250414", "request_id": "202504201814036e5433e32d6e4fdd", "usage": {"completion_tokens": 669, "prompt_tokens": 66, "total_tokens": 735}}]
