# quality_evaluation.py
# 生成数据质量检测模块：均衡性验证和多样性验证

import json
import os
import re
import math
import numpy as np
from collections import Counter, defaultdict
from typing import Dict, List, Tuple, Any
from datetime import datetime
import jieba
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score
import time
import requests
from pathlib import Path
from scipy.stats import entropy
from scipy.spatial.distance import jensenshannon
import spacy
try:
    nlp = spacy.load("zh_core_web_sm")
except:
    print("[警告] 未找到中文spacy模型，将使用简化的句法分析")
    nlp = None

def calculate_entropy(values: List[str]) -> float:
    """计算列表中值的熵"""
    if not values:
        return 0.0
    counter = Counter(values)
    probs = [count/len(values) for count in counter.values()]
    return entropy(probs)

def calculate_kl_divergence(p: Dict[str, int], q: Dict[str, int]) -> float:
    """计算两个分布的KL散度"""
    # 确保所有键都存在
    all_keys = set(p.keys()) | set(q.keys())
    p_sum = sum(p.values())
    q_sum = sum(q.values())
    
    # 计算概率分布
    p_dist = np.array([p.get(k, 0)/p_sum for k in all_keys])
    q_dist = np.array([q.get(k, 0)/q_sum for k in all_keys])
    
    # 使用Jensen-Shannon散度(对称且有界)
    return jensenshannon(p_dist, q_dist)

def calculate_ttr(tokens: List[str]) -> float:
    """计算Type-Token Ratio (TTR)"""
    if not tokens:
        return 0.0
    return len(set(tokens)) / len(tokens)

def get_dependency_patterns(text: str) -> List[str]:
    """获取依存句法模式"""
    if not nlp:
        # 简化版：仅返回句子长度
        return [str(len(text))]
    
    try:
        doc = nlp(text)
        patterns = []
        for token in doc:
            patterns.append(f"{token.dep_}-{token.head.pos_}")
        return patterns
    except Exception as e:
        print(f"[警告] 依存分析失败：{e}")
        return [str(len(text))]

# =====================
# 配置和常量
# =====================

# 评估阈值配置
EVALUATION_THRESHOLDS = {
    "balance": {
        "distribution_tolerance": 0.15,  # 分布差异容忍度
        "min_coverage_ratio": 0.8,       # 最小覆盖率
    },
    "diversity": {
        "vocabulary_diversity": 0.5,     # 词汇多样性阈值
        "syntactic_diversity": 0.4,      # 句法多样性阈值
        "semantic_diversity": 0.3,       # 语义多样性阈值
        "context_diversity": 0.4,        # 上下文多样性阈值
        "entity_diversity": 0.6,         # 实体多样性阈值
        "min_unique_entities": 0.3,      # 最小唯一实体比例
        "weights": {                     # 多样性指标权重
            "vocabulary": 0.2,
            "syntactic": 0.2,
            "semantic": 0.2,
            "context": 0.2,
            "entity": 0.2
        },
        "min_weighted_score": 0.8        # 加权得分最低要求（软条件）
    }
}

# 句法模式模板
SYNTACTIC_PATTERNS = [
    r'[^。！？]*[。！？]',  # 基本句子模式
    r'[^，；]*[，；][^，；]*[，；]',  # 包含逗号、分号的句子
    r'[^（）]*（[^）]*）[^（）]*',  # 包含括号的句子
    r'[^""]*"[^"]*"[^""]*',  # 包含引号的句子
    r'[^：]*：[^：]*',  # 包含冒号的句子
]

NATURALNESS_SCORE_THRESHOLD = 6.0  # 低于此分数视为不自然
NATURALNESS_LOW_RATE_THRESHOLD = 0.1  # 低分句子比例超过此阈值则警告

# =====================
# 数据加载函数
# =====================

def load_generated_dataset(dataset_path: str) -> List[Dict]:
    """加载生成的NER数据集"""
    with open(dataset_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def load_target_distribution(strategy_dir="reproduce") -> Dict[str, int]:
    """加载目标分布策略 - 从balance_config.json中读取"""
    # 优先从balance_config.json读取目标分布
    balance_config_file = Path("src/gen_strat/balance_config.json")
    if balance_config_file.exists():
        try:
            with open(balance_config_file, 'r', encoding='utf-8') as f:
                balance_config = json.load(f)
                target_distribution = balance_config.get("entity_type_targets", {})
                if target_distribution:
                    print(f"[✓] 从balance_config.json加载目标分布：{len(target_distribution)}个实体类型")
                    return target_distribution
        except Exception as e:
            print(f"[警告] 从balance_config.json加载目标分布失败：{e}")
    
    # 备用方案：从strategy_dir读取
    target_file = Path(strategy_dir) / "entity_target" / "privacy_bench_target.json"
    if target_file.exists():
        try:
            with open(target_file, 'r', encoding='utf-8') as f:
                result = json.load(f)
                print(f"[✓] 从{target_file}加载目标分布：{len(result)}个实体类型")
                return result
        except Exception as e:
            print(f"[警告] 从{target_file}加载目标分布失败：{e}")
    
    raise FileNotFoundError(f"未找到目标分布文件。尝试的路径：\n1. {balance_config_file}\n2. {target_file}")

def load_entity_schema() -> Dict[str, List[str]]:
    """加载实体模式定义"""
    with open('src/gen_strat/entity_schema.json', 'r', encoding='utf-8') as f:
        schema = json.load(f)
        return schema.get('categories', {})

def load_evaluation_config(config_path="src/synth_eval/evaluation_config.json"):
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

# =====================
# 均衡性验证函数
# =====================

def calculate_entity_distribution(dataset: List[Dict]) -> Dict[str, Dict[str, Any]]:
    """计算实体分布统计，包括实体内部分布分析"""
    entity_counts = Counter()  # 实体类型计数
    entity_texts = defaultdict(list)  # 每个类型的实体文本列表
    entity_value_counts = defaultdict(Counter)  # 每个类型中具体值的计数
    
    # 收集统计信息
    for item in dataset:
        for span in item["label"]:
            entity_type = span["type"]
            entity_text = span["entity"]
            entity_counts[entity_type] += 1
            entity_texts[entity_type].append(entity_text)
            entity_value_counts[entity_type][entity_text] += 1
    
    total_entities = sum(entity_counts.values())
    distribution = {}
    
    # 计算每个实体类型的详细统计
    for entity_type, count in entity_counts.items():
        unique_entities = set(entity_texts[entity_type])
        value_counts = entity_value_counts[entity_type]
        
        # 计算实体值分布的熵
        entity_entropy = calculate_entropy(entity_texts[entity_type])
        
        # 计算最常见值的比例
        most_common_value, most_common_count = value_counts.most_common(1)[0]
        most_common_ratio = most_common_count / count
        
        # 计算实体值的TTR
        entity_ttr = len(unique_entities) / count if count > 0 else 0
        
        distribution[entity_type] = {
            "count": count,
            "percentage": count / total_entities if total_entities > 0 else 0,
            "unique_entities": len(unique_entities),
            "total_occurrences": count,
            "value_distribution": dict(value_counts),
            "entropy": entity_entropy,
            "most_common_value": most_common_value,
            "most_common_ratio": most_common_ratio,
            "type_token_ratio": entity_ttr
        }
    
    return distribution

def calculate_target_distribution(target_counts: Dict[str, int]) -> Dict[str, float]:
    """计算目标分布比例"""
    total_target = sum(target_counts.values())
    target_distribution = {}
    
    for entity_type, count in target_counts.items():
        target_distribution[entity_type] = count / total_target if total_target > 0 else 0
    
    return target_distribution

def compare_distributions(
    actual_dist: Dict[str, Dict[str, Any]], 
    target_dist: Dict[str, float]
) -> Dict[str, Any]:
    """修正后的分布比较函数 - 基于绝对数量和容差评估
    
    优先使用balance_config.json中的绝对数量目标，只有在没有配置文件时才使用比例转换。
    
    Args:
        actual_dist: 实际分布，包含每个实体类型的统计信息
        target_dist: 目标分布（可能是比例或绝对数量）
    """
    
    # 优先从balance_config.json读取目标分布
    try:
        with open('src/gen_strat/balance_config.json', 'r', encoding='utf-8') as f:
            balance_config = json.load(f)
            target_counts = balance_config.get("entity_type_targets", {})
            tolerance = balance_config.get("distribution_tolerance", 0.1)
            
            # 如果balance_config.json中有目标数量，直接使用
            if target_counts:
                print(f"[✓] 使用balance_config.json中的绝对数量目标")
                print(f"    - 每个实体类型目标数量：{next(iter(target_counts.values()))}个")
                print(f"    - 容差范围：±{tolerance*100}%")
                # 不再进行比例转换
                is_proportion = False
            else:
                raise FileNotFoundError("balance_config.json中没有目标数量配置")
                
    except FileNotFoundError:
        print("[警告] 未找到balance_config.json或配置不完整，尝试使用比例转换")
        balance_config = {"distribution_tolerance": 0.1, "entity_type_targets": {}}
        tolerance = 0.1
        target_counts = {}
        # 检查target_dist是否为比例格式
        is_proportion = all(v <= 1.0 for v in target_dist.values() if v > 0)
    
    # 只有在没有balance_config.json的情况下才进行比例转换
    if not target_counts and is_proportion:
        print("[信息] 检测到目标分布为比例格式，进行转换")
        # 计算总实体数量，将比例转换为绝对数量
        total_actual = sum(actual_dist.get(et, {}).get("count", 0) for et in target_dist.keys())
        target_counts = {et: int(prop * total_actual) for et, prop in target_dist.items()}
        print(f"[信息] 转换后的目标数量：")
        for et, count in target_counts.items():
            print(f"    - {et}: {count}个")
    elif not target_counts:
        # 如果既没有配置文件也不是比例，直接使用target_dist
        target_counts = target_dist
    
    comparison = {
        "overall_score": 0.0,
        "entity_scores": {},
        "coverage_ratio": 0.0,
        "distribution_differences": {},
        "missing_entities": [],
        "excess_entities": [],
        "within_tolerance_count": 0,  # 在容差范围内的实体类型数量
        "tolerance_ratio": 0.0,       # 容差范围内的比例
        "tolerance_used": tolerance   # 实际使用的容差值
    }
    
    # 计算覆盖率
    covered_entities = set(actual_dist.keys())
    target_entities = set(target_dist.keys())
    comparison["coverage_ratio"] = len(covered_entities & target_entities) / len(target_entities)
    
    # 修正评分逻辑：基于绝对数量而非比例
    total_score = 0.0
    valid_comparisons = 0
    within_tolerance_count = 0
    
    for entity_type in target_entities:
        actual_count = actual_dist.get(entity_type, {}).get("count", 0)
        target_count = target_counts.get(entity_type, 0)
        
        # 计算容差范围
        tolerance_range = target_count * tolerance
        min_acceptable = max(0, target_count - tolerance_range)
        max_acceptable = target_count + tolerance_range
        
        # 判断是否在容差范围内
        within_tolerance = min_acceptable <= actual_count <= max_acceptable
        if within_tolerance:
            within_tolerance_count += 1
        
        # 计算得分：在容差范围内得满分，超出范围按距离扣分
        if within_tolerance:
            score = 1.0
        else:
            # 计算超出容差的程度
            if actual_count < min_acceptable:
                deviation = min_acceptable - actual_count
            else:  # actual_count > max_acceptable
                deviation = actual_count - max_acceptable
            
            # 根据偏差程度计算得分（偏差越大得分越低）
            max_penalty = target_count  # 最大惩罚
            score = max(0, 1 - deviation / max_penalty)
        
        comparison["entity_scores"][entity_type] = score
        comparison["distribution_differences"][entity_type] = {
            "actual": actual_count,
            "target": target_count,
            "min_acceptable": min_acceptable,
            "max_acceptable": max_acceptable,
            "within_tolerance": within_tolerance,
            "deviation": abs(actual_count - target_count),
            "deviation_ratio": abs(actual_count - target_count) / target_count if target_count > 0 else 0
        }
        
        total_score += score
        valid_comparisons += 1
    
    # 计算整体得分
    if valid_comparisons > 0:
        comparison["overall_score"] = total_score / valid_comparisons
    
    comparison["within_tolerance_count"] = within_tolerance_count
    comparison["tolerance_ratio"] = within_tolerance_count / len(target_entities) if target_entities else 0
    
    # 识别缺失和多余的实体类型
    comparison["missing_entities"] = list(target_entities - covered_entities)
    comparison["excess_entities"] = list(covered_entities - target_entities)
    
    return comparison

def evaluate_balance(dataset: List[Dict], target_counts: Dict[str, int]) -> Dict[str, Any]:
    """执行均衡性验证"""
    print("=== 执行均衡性验证 ===")
    
    # 计算实际分布
    actual_distribution = calculate_entity_distribution(dataset)
    print(f"实际实体分布：{len(actual_distribution)} 种实体类型")
    
    # 计算目标分布
    target_distribution = calculate_target_distribution(target_counts)
    print(f"目标实体分布：{len(target_distribution)} 种实体类型")
    
    # 比较分布
    comparison = compare_distributions(actual_distribution, target_distribution)
    
    # 评估结果 - 使用修正后的评估标准
    thresholds = EVALUATION_THRESHOLDS["balance"]
    
    # 修正通过标准：基于容差范围内的实体类型比例
    tolerance_threshold = 0.8  # 80%的实体类型在容差范围内即可通过
    balance_passed = (
        comparison["tolerance_ratio"] >= tolerance_threshold and
        comparison["coverage_ratio"] >= thresholds["min_coverage_ratio"]
    )
    
    evaluation_result = {
        "passed": balance_passed,
        "overall_score": comparison["overall_score"],
        "coverage_ratio": comparison["coverage_ratio"],
        "tolerance_ratio": comparison["tolerance_ratio"],
        "within_tolerance_count": comparison["within_tolerance_count"],
        "tolerance_used": comparison["tolerance_used"],
        "actual_distribution": actual_distribution,
        "target_distribution": target_distribution,
        "comparison": comparison,
        "thresholds": thresholds,
        "tolerance_threshold": tolerance_threshold
    }
    
    # 输出详细结果
    print(f"均衡性验证结果：{'通过' if balance_passed else '未通过'}")
    print(f"整体得分：{comparison['overall_score']:.3f}")
    print(f"覆盖率：{comparison['coverage_ratio']:.3f}")
    print(f"容差范围内实体类型：{comparison['within_tolerance_count']}/{len(target_distribution)}个 ({comparison['tolerance_ratio']:.1%})")
    print(f"使用容差：±{comparison['tolerance_used']:.1%}")
    
    # 输出未达标的实体类型详情
    failed_entities = []
    for entity_type, diff_info in comparison["distribution_differences"].items():
        if not diff_info["within_tolerance"]:
            failed_entities.append(f"{entity_type}: {diff_info['actual']}/{diff_info['target']} (偏差{diff_info['deviation']})")
    
    if failed_entities:
        print(f"未达标实体类型：{failed_entities}")
    
    if comparison["missing_entities"]:
        print(f"缺失实体类型：{comparison['missing_entities']}")
    if comparison["excess_entities"]:
        print(f"多余实体类型：{comparison['excess_entities']}")
    
    return evaluation_result

# =====================
# 多样性验证函数
# =====================

def calculate_vocabulary_diversity(texts: List[str]) -> Dict[str, float]:
    """计算词汇多样性，使用TTR和词频分布"""
    all_words = []
    for text in texts:
        words = jieba.lcut(text)
        all_words.extend(words)
    
    if not all_words:
        return {"ttr": 0.0, "entropy": 0.0}
    
    # 计算TTR
    ttr = calculate_ttr(all_words)
    
    # 计算词频分布熵
    word_entropy = calculate_entropy(all_words)
    
    return {
        "ttr": ttr,
        "entropy": word_entropy
    }

def calculate_syntactic_diversity(texts: List[str]) -> Dict[str, float]:
    """计算句法多样性，使用依存句法模式和句长分布"""
    if not texts:
        return {"pattern_diversity": 0.0, "length_entropy": 0.0}
    
    # 收集依存句法模式
    all_patterns = []
    lengths = []
    
    for text in texts:
        patterns = get_dependency_patterns(text)
        all_patterns.extend(patterns)
        lengths.append(len(text))
    
    # 计算模式多样性
    pattern_diversity = calculate_ttr(all_patterns)
    
    # 计算句长分布熵
    length_entropy = calculate_entropy([str(l) for l in lengths])
    
    return {
        "pattern_diversity": pattern_diversity,
        "length_entropy": length_entropy
    }

def calculate_semantic_diversity(texts: List[str]) -> Dict[str, float]:
    """计算语义多样性，使用Embedding聚类和轮廓系数"""
    if len(texts) < 2:
        return {"silhouette_score": 0.0, "cluster_entropy": 0.0}
    
    try:
        # 使用TF-IDF向量化
        vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words=None,
            ngram_range=(1, 2)
        )
        
        tfidf_matrix = vectorizer.fit_transform(texts)
        
        # 确定合适的聚类数
        n_clusters = min(int(len(texts) * 0.1) + 1, 10)  # 最多10个簇
        n_clusters = max(n_clusters, 2)  # 至少2个簇
        
        # K-means聚类
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        cluster_labels = kmeans.fit_predict(tfidf_matrix)
        
        # 计算轮廓系数
        sil_score = silhouette_score(tfidf_matrix, cluster_labels)
        
        # 计算聚类分布熵
        cluster_entropy = calculate_entropy([str(label) for label in cluster_labels])
        
        return {
            "silhouette_score": sil_score,
            "cluster_entropy": cluster_entropy
        }
        
    except Exception as e:
        print(f"[警告] 语义多样性计算失败：{e}")
        return {"silhouette_score": 0.0, "cluster_entropy": 0.0}

def calculate_context_diversity(dataset: List[Dict]) -> float:
    """计算上下文多样性"""
    context_patterns = defaultdict(int)
    total_items = len(dataset)
    
    if total_items == 0:
        return 0.0
    
    for item in dataset:
        text = item["text"]
        
        # 分析上下文模式
        if len(item["label"]) > 1:
            context_patterns["multi_entity"] += 1
        
        if re.search(r'[，；]', text):
            context_patterns["with_punctuation"] += 1
        
        if re.search(r'[（）]', text):
            context_patterns["with_parentheses"] += 1
        
        if re.search(r'[""]', text):
            context_patterns["with_quotes"] += 1
        
        if re.search(r'[：]', text):
            context_patterns["with_colon"] += 1
    
    # 计算模式多样性
    used_patterns = len([count for count in context_patterns.values() if count > 0])
    return used_patterns / len(context_patterns)

def calculate_entity_diversity(dataset: List[Dict]) -> Dict[str, float]:
    """计算实体多样性"""
    entity_stats = defaultdict(lambda: {"total": 0, "unique": set()})
    
    for item in dataset:
        for span in item["label"]:
            entity_type = span["type"]
            entity_text = span["entity"]
            entity_stats[entity_type]["total"] += 1
            entity_stats[entity_type]["unique"].add(entity_text)
    
    diversity_scores = {}
    for entity_type, stats in entity_stats.items():
        if stats["total"] > 0:
            diversity_scores[entity_type] = len(stats["unique"]) / stats["total"]
        else:
            diversity_scores[entity_type] = 0.0
    
    # 计算整体实体多样性
    if diversity_scores:
        overall_diversity = sum(diversity_scores.values()) / len(diversity_scores)
    else:
        overall_diversity = 0.0
    
    return {
        "overall": overall_diversity,
        "by_type": diversity_scores
    }

def evaluate_diversity(dataset: List[Dict]) -> Dict[str, Any]:
    """执行多样性验证 - 使用加权得分"""
    print("=== 执行多样性验证 ===")
    
    texts = [item["text"] for item in dataset]
    
    # 计算各项多样性指标
    vocab_diversity = calculate_vocabulary_diversity(texts)
    syntactic_diversity = calculate_syntactic_diversity(texts)
    semantic_diversity = calculate_semantic_diversity(texts)
    context_diversity = calculate_context_diversity(dataset)
    entity_diversity = calculate_entity_diversity(dataset)
    
    print(f"词汇多样性：{vocab_diversity:.3f}")
    print(f"句法多样性：{syntactic_diversity:.3f}")
    print(f"语义多样性：{semantic_diversity:.3f}")
    print(f"上下文多样性：{context_diversity:.3f}")
    print(f"实体多样性：{entity_diversity['overall']:.3f}")
    
    # 评估结果
    thresholds = EVALUATION_THRESHOLDS["diversity"]
    weights = thresholds["weights"]
    
    # 计算每个指标的达标率
    scores = {
        "vocabulary": vocab_diversity / thresholds["vocabulary_diversity"],
        "syntactic": syntactic_diversity / thresholds["syntactic_diversity"],
        "semantic": semantic_diversity / thresholds["semantic_diversity"],
        "context": context_diversity / thresholds["context_diversity"],
        "entity": entity_diversity["overall"] / thresholds["entity_diversity"]
    }
    
    # 计算加权得分
    weighted_score = sum(
        scores[metric] * weight 
        for metric, weight in weights.items()
    )
    
    # 软条件：加权得分达到最低要求
    diversity_passed = weighted_score >= thresholds["min_weighted_score"]
    
    evaluation_result = {
        "passed": diversity_passed,
        "vocabulary_diversity": vocab_diversity,
        "syntactic_diversity": syntactic_diversity,
        "semantic_diversity": semantic_diversity,
        "context_diversity": context_diversity,
        "entity_diversity": entity_diversity,
        "weighted_score": weighted_score,
        "metric_scores": scores,
        "thresholds": thresholds
    }
    
    print(f"多样性加权得分：{weighted_score:.3f}")
    print(f"多样性验证结果：{'通过' if diversity_passed else '未通过'}")
    
    return evaluation_result

# =====================
# 句子自然度检测函数
# =====================

def evaluate_sentence_naturalness(sentences, config=None, dataset_path=None, output_dir=None):
    """直接HTTP调用智谱glm-4.1v-thinking-flashx API对每个句子进行自然度打分，并保存详细评分结果"""
    if config is None:
        config = load_evaluation_config()
    nat_cfg = config.get("naturalness", {})
    api_url = nat_cfg.get("api_url")
    api_key_env = nat_cfg.get("api_key_env", "ZHIPUAI_API_KEY")
    api_key = os.getenv(api_key_env)
    max_retry = nat_cfg.get("max_retry", 3)
    sleep_time = nat_cfg.get("sleep_time", 1.5)
    score_threshold = nat_cfg.get("score_threshold", 6.0)
    
    if not api_url or not api_key:
        print("[警告] 未配置智谱API url或API key，跳过句子自然度检测。")
        return [10.0]*len(sentences), []
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 新增：创建详细评分结果列表
    detailed_scores = []
    scores = []
    low_score_examples = []
    
    for idx, sent in enumerate(sentences):
        score_detail = {
            "sentence_id": idx + 1,
            "sentence": sent,
            "score": 0.0,
            "evaluation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "status": "success",
            "metadata": {
                "length": len(sent),
                "has_punctuation": bool(re.search(r'[，。！？；：]', sent))
            }
        }
        
        prompt = f"请对下面这句话的自然度进行打分，0分表示极其生硬、完全不符合真实语言，10分表示非常自然、完全符合真实世界表达。只返回一个数字分数，不要解释。\n句子：{sent}"
        data = {
            "model": "glm-4.1v-thinking-flashx",
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.2
        }
        
        for attempt in range(max_retry):
            try:
                resp = requests.post(api_url, headers=headers, json=data, timeout=20)
                resp.raise_for_status()
                result = resp.json()
                content = result["choices"][0]["message"]["content"].strip()
                score = float(re.findall(r"\d+(?:\.\d+)?", content)[0])
                
                # 更新评分详情
                score_detail["score"] = score
                score_detail["attempt"] = attempt + 1
                
                scores.append(score)
                if score < score_threshold and len(low_score_examples) < 10:
                    low_score_examples.append({"sentence": sent, "score": score})
                break
                
            except Exception as e:
                score_detail["status"] = "failed"
                score_detail["error"] = str(e)
                if attempt == max_retry - 1:
                    print(f"[警告] 句子自然度打分失败：{sent}，错误：{e}")
                    scores.append(0.0)
                else:
                    time.sleep(sleep_time)
        
        detailed_scores.append(score_detail)
        
        # 每100个句子保存一次进度
        if (idx + 1) % 100 == 0:
            print(f"已完成 {idx + 1}/{len(sentences)} 个句子的评分")
    
    # 保存详细评分结果
    save_detailed_scores(detailed_scores, dataset_path, output_dir)
    
    return scores, low_score_examples

def save_detailed_scores(detailed_scores: List[Dict], dataset_path: str = None, output_dir: str = None):
    """保存详细的句子评分结果"""
    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 构建输出目录
    if output_dir:
        # 如果指定了输出目录，使用指定目录
        base_output_dir = Path(output_dir)
    else:
        # 否则使用默认的synth_dataset/evaluation_results结构
        base_output_dir = Path("synth_dataset/evaluation_results") / timestamp
    
    base_output_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取数据集名称（如果提供了数据集路径）
    dataset_name = "unknown_dataset"
    if dataset_path:
        dataset_name = os.path.splitext(os.path.basename(dataset_path))[0]
    
    # 构建完整的评分报告
    score_report = {
        "evaluation_time": timestamp,
        "dataset_name": dataset_name,
        "summary": {
            "total_sentences": len(detailed_scores),
            "average_score": sum(item["score"] for item in detailed_scores) / len(detailed_scores),
            "success_rate": sum(1 for item in detailed_scores if item["status"] == "success") / len(detailed_scores),
            "score_distribution": {
                "excellent": sum(1 for item in detailed_scores if item["score"] >= 8.0),
                "good": sum(1 for item in detailed_scores if 6.0 <= item["score"] < 8.0),
                "fair": sum(1 for item in detailed_scores if 4.0 <= item["score"] < 6.0),
                "poor": sum(1 for item in detailed_scores if item["score"] < 4.0)
            }
        },
        "detailed_scores": detailed_scores
    }
    
    # 保存详细报告
    detailed_report_path = base_output_dir / f"sentence_scores_detailed_{timestamp}.json"
    with open(detailed_report_path, 'w', encoding='utf-8') as f:
        json.dump(score_report, f, ensure_ascii=False, indent=2)
    
    # 保存简要统计报告（txt格式）
    summary_path = base_output_dir / f"evaluation_summary_{timestamp}.txt"
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write(f"句子自然度评估报告\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"评估时间：{timestamp}\n")
        f.write(f"数据集：{dataset_name}\n\n")
        f.write(f"总句子数：{score_report['summary']['total_sentences']}\n")
        f.write(f"平均分数：{score_report['summary']['average_score']:.2f}\n")
        f.write(f"评分成功率：{score_report['summary']['success_rate']:.2%}\n\n")
        f.write("分数分布：\n")
        f.write(f"- 优秀 (>=8.0)：{score_report['summary']['score_distribution']['excellent']} 句\n")
        f.write(f"- 良好 (6.0-7.9)：{score_report['summary']['score_distribution']['good']} 句\n")
        f.write(f"- 一般 (4.0-5.9)：{score_report['summary']['score_distribution']['fair']} 句\n")
        f.write(f"- 较差 (<4.0)：{score_report['summary']['score_distribution']['poor']} 句\n")
    
    print(f"评估报告已保存到目录：{base_output_dir}")
    print(f"- 详细报告：{detailed_report_path.name}")
    print(f"- 统计摘要：{summary_path.name}")

# =====================
# 主评估函数
# =====================

def calculate_distribution_difference(dist1: Dict[str, Dict], dist2: Dict[str, Dict]) -> Dict[str, Any]:
    """计算两个分布之间的差异"""
    differences = {
        "entity_types": {
            "added": list(set(dist1.keys()) - set(dist2.keys())),
            "removed": list(set(dist2.keys()) - set(dist1.keys())),
            "common": list(set(dist1.keys()) & set(dist2.keys()))
        },
        "counts": {},
        "percentages": {},
        "diversity": {}
    }
    
    # 计算共同实体类型的差异
    for entity_type in differences["entity_types"]["common"]:
        d1 = dist1[entity_type]
        d2 = dist2[entity_type]
        
        # 计数差异
        count_diff = d1["count"] - d2["count"]
        count_change = (count_diff / d2["count"]) * 100 if d2["count"] > 0 else float('inf')
        
        # 百分比差异
        pct_diff = d1["percentage"] - d2["percentage"]
        
        # 多样性指标差异
        diversity_diff = {
            "unique_ratio": (d1["unique_entities"] / d1["count"]) - (d2["unique_entities"] / d2["count"]) 
            if d1["count"] > 0 and d2["count"] > 0 else 0,
            "entropy_diff": d1.get("entropy", 0) - d2.get("entropy", 0),
            "ttr_diff": d1.get("type_token_ratio", 0) - d2.get("type_token_ratio", 0)
        }
        
        differences["counts"][entity_type] = {
            "absolute": count_diff,
            "percentage": count_change
        }
        differences["percentages"][entity_type] = pct_diff
        differences["diversity"][entity_type] = diversity_diff
    
    return differences

def evaluate_dataset_quality(
    dataset_path: str,
    output_dir: str = None,
    mode: str = "final",  # "iter" | "final" | "compare"
    ref_dataset_path: str = None,
    skip_plots: bool = False,
    skip_naturalness: bool = False
) -> Dict[str, Any]:
    """执行数据集质量评估
    
    Args:
        dataset_path: 要评估的数据集路径
        output_dir: 输出目录
        mode: 评估模式
            - "iter": 迭代评估(快速质量检查,仅JSON输出)
            - "final": 最终评估(完整报告+可视化)
            - "compare": 对比评估(需要参考数据集,强制差异分析)
        ref_dataset_path: 参考数据集路径(compare模式必需)
        skip_plots: 是否跳过图表生成
        skip_naturalness: 是否跳过自然度评估(用于加速迭代评估)
    """
    # 参数验证
    if mode == "compare" and not ref_dataset_path:
        raise ValueError("对比评估模式必须提供参考数据集路径(ref_dataset_path)")
    
    if mode == "iter":
        skip_plots = True  # 迭代模式强制跳过图表
        skip_naturalness = True  # 迭代模式跳过自然度评估
        
    # 加载参考数据集(对比模式)
    ref_dataset = None
    if mode == "compare":
        print(f"加载参考数据集：{ref_dataset_path}")
        ref_dataset = load_generated_dataset(ref_dataset_path)
    start_time = time.time()
    phase_times = {}
    
    config = load_evaluation_config()
    nat_cfg = config.get("naturalness", {})
    score_threshold = nat_cfg.get("score_threshold", 6.0)
    low_rate_threshold = nat_cfg.get("low_rate_threshold", 0.1)
    print("=== 开始数据集质量评估 ===")
    
    # 加载数据
    load_start = time.time()
    dataset = load_generated_dataset(dataset_path)
    target_counts = load_target_distribution()
    phase_times["data_loading"] = time.time() - load_start
    
    print(f"数据集大小：{len(dataset)} 条记录")
    print(f"目标实体类型：{len(target_counts)} 种")
    
    # 执行均衡性验证
    balance_start = time.time()
    balance_result = evaluate_balance(dataset, target_counts)
    phase_times["balance_evaluation"] = time.time() - balance_start
    
    # 执行多样性验证
    diversity_start = time.time()
    diversity_result = evaluate_diversity(dataset)
    phase_times["diversity_evaluation"] = time.time() - diversity_start
    
    # 句子自然度检测
    texts = [item["text"] for item in dataset]
    print("=== 检查句子自然度（大模型打分） ===")
    naturalness_start = time.time()
    naturalness_scores, low_score_examples = evaluate_sentence_naturalness(
        texts, 
        config,
        dataset_path,
        output_dir
    )
    phase_times["naturalness_evaluation"] = time.time() - naturalness_start
    
    avg_naturalness = sum(naturalness_scores) / len(naturalness_scores) if naturalness_scores else 10.0
    low_rate = sum(1 for s in naturalness_scores if s < score_threshold) / len(naturalness_scores) if naturalness_scores else 0.0
    print(f"句子自然度平均分：{avg_naturalness:.2f}，低分句子比例：{low_rate:.2%}")
    if low_rate > low_rate_threshold:
        print(f"[警告] 句子自然度低于{score_threshold}分的比例较高，请重点检查！")
    
    # 计算效率指标
    total_time = time.time() - start_time
    samples_per_second = len(dataset) / total_time
    entities_per_second = sum(len(item["label"]) for item in dataset) / total_time
    
    # 基础评估结果
    base_result = {
        "evaluation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "evaluation_mode": mode,
        "dataset_info": {
            "total_records": len(dataset),
            "total_entities": sum(len(item["label"]) for item in dataset),
            "entity_types": list(target_counts.keys()),
        },
        "balance_evaluation": balance_result,
        "diversity_evaluation": diversity_result
    }
    
    # 根据模式添加不同的评估结果
    if not skip_naturalness:
        base_result["naturalness_evaluation"] = {
            "avg_score": avg_naturalness,
            "low_score_rate": low_rate,
            "low_score_examples": low_score_examples,
            "threshold": score_threshold
        }
    
    # 对比模式：添加差异分析
    if mode == "compare" and ref_dataset:
        ref_distribution = calculate_entity_distribution(ref_dataset)
        distribution_diff = calculate_distribution_difference(
            actual_distribution,  # 当前数据集的分布
            ref_distribution     # 参考数据集的分布
        )
        
        # 计算整体改进情况
        improvements = {
            "balance": balance_result["overall_score"] - ref_balance_result["overall_score"],
            "diversity": diversity_result["weighted_score"] - ref_diversity_result["weighted_score"]
        }
        if not skip_naturalness:
            improvements["naturalness"] = avg_naturalness - ref_avg_naturalness
        
        base_result["comparison"] = {
            "distribution_differences": distribution_diff,
            "improvements": improvements,
            "ref_dataset_info": {
                "path": ref_dataset_path,
                "total_records": len(ref_dataset),
                "total_entities": sum(len(item["label"]) for item in ref_dataset)
            }
        }
    
    # 最终模式：添加完整指标
    if mode == "final":
        base_result["efficiency_metrics"] = {
            "total_evaluation_time": total_time,
            "samples_per_second": samples_per_second,
            "entities_per_second": entities_per_second,
            "phase_times": phase_times,
            "memory_usage": {
                "dataset_size_mb": sys.getsizeof(json.dumps(dataset)) / (1024 * 1024),
                "peak_memory_mb": None  # TODO: 添加内存使用监控
            }
        }
    
    # 计算整体评估结果
    base_result["overall_passed"] = (
        # 硬条件：必须通过均衡性验证
        balance_result["passed"] and
        # 软条件：多样性加权得分达标
        diversity_result["weighted_score"] >= EVALUATION_THRESHOLDS["diversity"]["min_weighted_score"] and
        # 自然度条件(如果未跳过)
        (skip_naturalness or low_rate <= low_rate_threshold)
    )
    
    # 根据模式打印不同的总结信息
    if mode == "iter":
        print("\n=== 迭代评估结果 ===")
        print(f"均衡性：{'通过' if balance_result['passed'] else '未通过'}")
        print(f"多样性：{diversity_result['weighted_score']:.3f}")
        print(f"整体结果：{'通过' if base_result['overall_passed'] else '未通过'}")
    
    elif mode == "final":
        print("\n=== 最终评估结果 ===")
        print(f"均衡性：{'通过' if balance_result['passed'] else '未通过'} (得分：{balance_result['overall_score']:.3f})")
        print(f"多样性：{diversity_result['weighted_score']:.3f}")
        if not skip_naturalness:
            print(f"自然度：{avg_naturalness:.2f} (低分比例：{low_rate:.2%})")
        print(f"整体结果：{'通过' if base_result['overall_passed'] else '未通过'}")
        
        # 打印效率指标
        print("\n=== 评估效率指标 ===")
        print(f"总评估时间：{total_time:.2f}秒")
        print(f"样本处理速度：{samples_per_second:.2f}条/秒")
        print(f"实体处理速度：{entities_per_second:.2f}个/秒")
        print("\n各阶段耗时：")
        for phase, t in phase_times.items():
            print(f"  - {phase}: {t:.2f}秒")
    
    elif mode == "compare":
        print("\n=== 对比评估结果 ===")
        print("实体类型变化：")
        print(f"  - 新增：{distribution_diff['entity_types']['added']}")
        print(f"  - 移除：{distribution_diff['entity_types']['removed']}")
        print("\n指标改进：")
        for metric, change in improvements.items():
            print(f"  - {metric}: {change:+.3f}")
        print(f"\n整体结果：{'通过' if base_result['overall_passed'] else '未通过'}")
    
    return base_result

def save_evaluation_report(evaluation_result: Dict[str, Any], output_path: str):
    """保存评估报告"""
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(evaluation_result, f, ensure_ascii=False, indent=2)
    print(f"评估报告已保存到：{output_path}")

# =====================
# 命令行接口
# =====================

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("用法：python quality_evaluation.py <dataset_path> [output_path]")
        sys.exit(1)
    
    dataset_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else "evaluation_report.json"
    
    try:
        # 执行评估
        evaluation_result = evaluate_dataset_quality(dataset_path)
        
        # 保存报告
        save_evaluation_report(evaluation_result, output_path)
        
    except Exception as e:
        print(f"评估失败：{e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 