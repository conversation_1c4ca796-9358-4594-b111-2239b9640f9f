{"sentence": "Violent protests erupt in Caracas following controversial election results.", "entity_names": ["Caracas"], "entity_types": ["location"]}
{"sentence": "Caracas Mayor calls for increased security measures in the city center.", "entity_names": ["Caracas"], "entity_types": ["location"]}
{"sentence": "Google for Education launches new online learning platform.", "entity_names": ["Google for Education"], "entity_types": ["organization"]}
{"sentence": "New York City schools partner with Google for Education for digital learning initiative.", "entity_names": ["New York City", "Google for Education"], "entity_types": ["location", "organization"]}
{"sentence": "Teachers across the country utilize Google for Education tools for virtual classrooms.", "entity_names": ["Google for Education"], "entity_types": ["organization"]}
{"sentence": "Pandora Media announces partnership with Sony Music Entertainment.", "entity_names": ["Pandora Media", "Sony Music Entertainment"], "entity_types": ["organization", "organization"]}
{"sentence": "Sony Music to release new album in collaboration with a popular artist.", "entity_names": ["Sony Music"], "entity_types": ["organization"]}
{"sentence": "Pandora Media's CEO discusses future plans for the company at annual conference.", "entity_names": ["Pandora Media"], "entity_types": ["organization"]}
{"sentence": "Private Michael Jenkins awarded Purple Heart for heroism in battle.", "entity_names": ["Private <PERSON>"], "entity_types": ["person"]}
{"sentence": "New research shows alarming increase in carbon emissions from China.", "entity_names": ["China"], "entity_types": ["location"]}
{"sentence": "Apple Inc. announces launch of new iPhone model.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Berlin police officer Samantha Brown honored for bravery.", "entity_names": ["Berlin", "Samantha Brown"], "entity_types": ["location", "person"]}
{"sentence": "Massive protest in Berlin against government's new immigration policy.", "entity_names": ["Berlin"], "entity_types": ["location"]}
{"sentence": "Officer Samantha Brown apprehends suspect in Berlin bank robbery.", "entity_names": ["Samantha Brown", "Berlin"], "entity_types": ["person", "location"]}
{"sentence": "The exhibition features a rare collection of Claude Monet's paintings.", "entity_names": ["Claude Monet"], "entity_types": ["person"]}
{"sentence": "The Claude Monet Museum in Giverny, France, reopens after renovations.", "entity_names": ["Claude Monet", "Giverny", "France"], "entity_types": ["person", "location", "location"]}
{"sentence": "Tech giant Apple launches new iPhone with improved camera and battery life.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "Stocks plunge as United Airlines announces 20,000 layoffs due to pandemic impact.", "entity_names": ["United Airlines"], "entity_types": ["organization"]}
{"sentence": "Famous actress Jennifer Lawrence to star in new Netflix thriller.", "entity_names": ["Jennifer Lawrence", "Netflix"], "entity_types": ["person", "organization"]}
{"sentence": "Kraft Heinz reports decrease in sales for the third quarter.", "entity_names": ["Kraft Heinz"], "entity_types": ["organization"]}
{"sentence": "Ina Garten's new cookbook becomes best-seller in the first week of release.", "entity_names": ["Ina Garten"], "entity_types": ["person"]}
{"sentence": "Stocks of Kraft Heinz surge after announcement of new CEO.", "entity_names": ["Kraft Heinz"], "entity_types": ["organization"]}
{"sentence": "The Climate Action Network demands stronger measures to combat climate change.", "entity_names": ["Climate Action Network"], "entity_types": ["organization"]}
{"sentence": "Prince Charles delivers a speech on environmental conservation at the United Nations.", "entity_names": ["Prince Charles", "United Nations"], "entity_types": ["person", "organization"]}
{"sentence": "Members of the Climate Action Network protest outside of the White House.", "entity_names": ["Climate Action Network", "White House"], "entity_types": ["organization", "location"]}
{"sentence": "Uber launches new feature to schedule rides in advance.", "entity_names": ["Uber"], "entity_types": ["organization"]}
{"sentence": "Uber driver charged with assault in New York City.", "entity_names": ["Uber", "New York City"], "entity_types": ["organization", "location"]}
{"sentence": "Uber CEO resigns amidst controversy.", "entity_names": ["Uber", "CEO"], "entity_types": ["organization", "person"]}
{"sentence": "Michelle Obama delivers powerful speech at United Nations conference.", "entity_names": ["Michelle Obama", "United Nations"], "entity_types": ["person", "organization"]}
{"sentence": "Former First Lady Michelle Obama launches new initiative to promote girls' education.", "entity_names": ["Michelle Obama"], "entity_types": ["person"]}
{"sentence": "Michelle Obama visits children's hospital in Chicago to meet with young patients.", "entity_names": ["Michelle Obama", "Chicago"], "entity_types": ["person", "location"]}
{"sentence": "BARCELONA - NEW MAYOR ELECTED AFTER CLOSE ELECTION.", "entity_names": ["Barcelona"], "entity_types": ["location"]}
{"sentence": "RALPH LAUREN ANNOUNCES PLAN TO LAUNCH SUSTAINABLE FASHION LINE.", "entity_names": ["RALPH LAUREN"], "entity_types": ["organization"]}
{"sentence": "Barcelona FC hires new head coach from Argentina.", "entity_names": ["Barcelona FC", "Argentina"], "entity_types": ["organization", "location"]}
{"sentence": "Oxfam International releases report on global hunger crisis.", "entity_names": ["Oxfam International"], "entity_types": ["organization"]}
{"sentence": "Aisha Khan appointed as ambassador to United Nations.", "entity_names": ["Aisha Khan", "United Nations"], "entity_types": ["person", "organization"]}
{"sentence": "New Oxfam International CEO vows to tackle inequality in third world countries.", "entity_names": ["Oxfam International"], "entity_types": ["organization"]}
{"sentence": "Samantha Miller appointed as the new spokesperson for the International Red Cross and Red Crescent Movement.", "entity_names": ["Samantha Miller", "International Red Cross and Red Crescent Movement"], "entity_types": ["person", "organization"]}
{"sentence": "The International Red Cross and Red Crescent Movement delivers aid to flood-affected areas in Southeast Asia.", "entity_names": ["International Red Cross and Red Crescent Movement", "Southeast Asia"], "entity_types": ["organization", "location"]}
{"sentence": "Investigative report uncovers corruption within the International Red Cross and Red Crescent Movement leadership.", "entity_names": ["International Red Cross and Red Crescent Movement"], "entity_types": ["organization"]}
{"sentence": "The Food and Agriculture Organization reports record crop yields in the Midwest.", "entity_names": ["Food and Agriculture Organization", "Midwest"], "entity_types": ["organization", "location"]}
{"sentence": "The CEO of Apple Inc. announces new product release at tech conference.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "China surpasses United States as world's largest importer of agricultural products, says Food and Agriculture Organization.", "entity_names": ["China", "United States", "Food and Agriculture Organization"], "entity_types": ["location", "location", "organization"]}
{"sentence": "Justin Bieber's new album breaks records.", "entity_names": ["Justin Bieber"], "entity_types": ["person"]}
{"sentence": "Sony Pictures announces plans for new blockbuster movie.", "entity_names": ["Sony Pictures"], "entity_types": ["organization"]}
{"sentence": "Justin Bieber's collaboration with Sony Pictures becomes a box office hit.", "entity_names": ["Justin Bieber", "Sony Pictures"], "entity_types": ["person", "organization"]}
{"sentence": "The Museum of Contemporary Art in Tokyo, Japan, is hosting a special exhibition on traditional Japanese pottery.", "entity_names": ["Museum of Contemporary Art", "Tokyo", "Japan"], "entity_types": ["organization", "location", "location"]}
{"sentence": "The Royal Academy of Arts in London is planning to showcase a collection of works by renowned British artists.", "entity_names": ["Royal Academy of Arts", "London"], "entity_types": ["organization", "location"]}
{"sentence": "Tokyo, Japan, will be the site of the upcoming international conference on climate change.", "entity_names": ["Tokyo", "Japan"], "entity_types": ["location", "location"]}
{"sentence": "Refugee Council USA advocates for more funding for resettlement programs.", "entity_names": ["Refugee Council USA"], "entity_types": ["organization"]}
{"sentence": "Natasha Petrovna elected as the new head of the International Red Cross.", "entity_names": ["Natasha Petrovna", "International Red Cross"], "entity_types": ["person", "organization"]}
{"sentence": "An earthquake hit the small town of Amalfi, Italy, causing widespread damage.", "entity_names": ["Amalfi", "Italy"], "entity_types": ["location", "location"]}
{"sentence": "Rescue team saves 10 climbers stranded in The Andes Mountains.", "entity_names": ["The Andes Mountains"], "entity_types": ["location"]}
{"sentence": "Cristiano Ronaldo scores a hat-trick in the semi-final match.", "entity_names": ["Cristiano Ronaldo"], "entity_types": ["person"]}
{"sentence": "The National Council of Teachers of English holds annual conference in Tokyo, Japan.", "entity_names": ["National Council of Teachers of English", "Tokyo", "Japan"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Tokyo, Japan to host 2020 Summer Olympics.", "entity_names": ["Tokyo", "Japan"], "entity_types": ["location", "location"]}
{"sentence": "The National Council of Teachers of English celebrates its 100th anniversary with a special event in Tokyo, Japan.", "entity_names": ["National Council of Teachers of English", "Tokyo", "Japan"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Local business owner, Anthony Nguyen, named Entrepreneur of the Year.", "entity_names": ["Anthony Nguyen"], "entity_types": ["person"]}
{"sentence": "Protests erupt in downtown Los Angeles over police shooting of Anthony Nguyen.", "entity_names": ["Los Angeles", "Anthony Nguyen"], "entity_types": ["location", "person"]}
{"sentence": "Anthony Nguyen Foundation donates $1 million to local hospital.", "entity_names": ["Anthony Nguyen"], "entity_types": ["person"]}
{"sentence": "Warren Buffett's Berkshire Hathaway invests in Starbucks Corporation.", "entity_names": ["Warren Buffett", "Berkshire Hathaway", "Starbucks Corporation"], "entity_types": ["person", "organization", "organization"]}
{"sentence": "Starbucks Corporation opens new store in Shanghai.", "entity_names": ["Starbucks Corporation", "Shanghai"], "entity_types": ["organization", "location"]}
{"sentence": "Warren Buffett predicts economic downturn in the coming year.", "entity_names": ["Warren Buffett"], "entity_types": ["person"]}
{"sentence": "Earthwatch Institute partners with local communities to study the effects of climate change.", "entity_names": ["Earthwatch Institute"], "entity_types": ["organization"]}
{"sentence": "The United Nations reports a 30% increase in deforestation in the Amazon rainforest.", "entity_names": ["United Nations", "Amazon rainforest"], "entity_types": ["organization", "location"]}
{"sentence": "Renowned physicist Stephen Hawking's theories on black holes continue to intrigue scientists around the world.", "entity_names": ["Stephen Hawking"], "entity_types": ["person"]}
{"sentence": "Google co-founder Larry Page steps down as CEO of Alphabet", "entity_names": ["Google", "Larry Page", "Alphabet"], "entity_types": ["organization", "person", "organization"]}
{"sentence": "Larry Page donates millions to wildlife conservation efforts", "entity_names": ["Larry Page"], "entity_types": ["person"]}
{"sentence": "Alphabet's Larry Page expresses support for climate change initiatives", "entity_names": ["Alphabet", "Larry Page"], "entity_types": ["organization", "person"]}
{"sentence": "Technology giant Apple announces record-breaking sales for the third quarter.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "The mayor of New York City vows to increase funding for public transportation infrastructure.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Renowned chef Jamie Oliver launches new cooking show on streaming platform.", "entity_names": ["Jamie Oliver"], "entity_types": ["person"]}
{"sentence": "Greenpeace protests the construction of a new oil pipeline in Alaska.", "entity_names": ["Greenpeace", "Alaska"], "entity_types": ["organization", "location"]}
{"sentence": "The United Way of Central Ohio raises over $1 million for local homeless shelters.", "entity_names": ["United Way of Central Ohio"], "entity_types": ["organization"]}
{"sentence": "Berlin experiences record-breaking heat wave, with temperatures reaching 40 degrees Celsius.", "entity_names": ["Berlin"], "entity_types": ["location"]}
{"sentence": "Tesla posts record-breaking profits in third quarter.", "entity_names": ["Tesla"], "entity_types": ["organization"]}
{"sentence": "New York City mayor announces plan to tackle homelessness crisis.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Emmy-winning actress to star in upcoming blockbuster film.", "entity_names": [], "entity_types": []}
{"sentence": "The European Union imposes new carbon emission regulations on automobile manufacturers.", "entity_names": ["European Union"], "entity_types": ["organization"]}
{"sentence": "Severe flooding in the Midwest prompts evacuation orders for thousands of residents.", "entity_names": ["Midwest"], "entity_types": ["location"]}
{"sentence": "TripAdvisor reveals the top 10 hotels in Europe for 2021.", "entity_names": ["TripAdvisor", "Europe"], "entity_types": ["organization", "location"]}
{"sentence": "Walt Disney World Resort announces new COVID-19 safety measures for guests.", "entity_names": ["Walt Disney World Resort"], "entity_types": ["organization"]}
{"sentence": "Jennifer Lopez to perform at the Super Bowl halftime show.", "entity_names": ["Jennifer Lopez", "Super Bowl"], "entity_types": ["person", "organization"]}
{"sentence": "Elon Musk's SpaceX successfully launches new satellite into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "New York City Mayor announces plans for citywide clean energy initiative.", "entity_names": ["New York City", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "Apple Inc. unveils latest iPhone model with enhanced features and capabilities.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Taylor Swift performs surprise show at The Bluebird Cafe.", "entity_names": ["Taylor Swift", "The Bluebird Cafe"], "entity_types": ["person", "location"]}
{"sentence": "The Bluebird Cafe in Nashville to host songwriter's night.", "entity_names": ["The Bluebird Cafe", "Nashville"], "entity_types": ["location", "location"]}
{"sentence": "New documentary explores the history of The Bluebird Cafe.", "entity_names": ["The Bluebird Cafe"], "entity_types": ["location"]}
{"sentence": "Bureau of Alcohol, Tobacco, Firearms and Explosives announces new initiative to combat gun trafficking.", "entity_names": ["Bureau of Alcohol, Tobacco, Firearms and Explosives"], "entity_types": ["organization"]}
{"sentence": "Cape Town experiences record-breaking heat wave, with temperatures exceeding 40 degrees Celsius.", "entity_names": ["Cape Town"], "entity_types": ["location"]}
{"sentence": "Local resident from Cape Town wins national award for community service.", "entity_names": ["Cape Town"], "entity_types": ["location"]}
{"sentence": "Pentagon announces new military strategy in Indo-Pacific region.", "entity_names": ["Pentagon"], "entity_types": ["organization"]}
{"sentence": "General James Mattis faces criticism over military budget allocation.", "entity_names": ["General James Mattis"], "entity_types": ["person"]}
{"sentence": "Local farmers at Smithfield Farmers' Market report record sales this season.", "entity_names": ["Smithfield Farmers' Market"], "entity_types": ["location"]}
{"sentence": "Smithfield Farmers' Market to host annual Fall Festival next weekend.", "entity_names": ["Smithfield Farmers' Market"], "entity_types": ["location"]}
{"sentence": "New organic produce vendor joins Smithfield Farmers' Market lineup.", "entity_names": ["Smithfield Farmers' Market"], "entity_types": ["organization"]}
{"sentence": "China National Space Administration launches new satellite to study climate change.", "entity_names": ["China National Space Administration"], "entity_types": ["organization"]}
{"sentence": "Sierra Nevada Corporation to supply NASA with cargo transportation services to the International Space Station.", "entity_names": ["Sierra Nevada Corporation", "NASA", "International Space Station"], "entity_types": ["organization", "organization", "location"]}
{"sentence": "Russian cosmonauts and Chinese astronauts conduct joint space mission.", "entity_names": ["Chinese"], "entity_types": ["organization"]}
{"sentence": "Tesla announces plans to open new manufacturing plant in Texas.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "Michelle Obama to launch new initiative to promote education for girls.", "entity_names": ["Michelle Obama"], "entity_types": ["person"]}
{"sentence": "European Union imposes sanctions on Russian officials over Navalny poisoning.", "entity_names": ["European Union", "Russian", "Navalny"], "entity_types": ["organization", "location", "person"]}
{"sentence": "The International Air Transport Association predicts a 26% drop in global passenger revenue.", "entity_names": ["International Air Transport Association"], "entity_types": ["organization"]}
{"sentence": "United Airlines announces the addition of new flights to Hawaii .", "entity_names": ["United Airlines", "Hawaii"], "entity_types": ["organization", "location"]}
{"sentence": "The CEO of Boeing resigned following the company's recent safety scandals.", "entity_names": ["Boeing"], "entity_types": ["organization"]}
{"sentence": "President Biden signs executive order to address climate change.", "entity_names": ["President Biden"], "entity_types": ["person"]}
{"sentence": "London Heathrow Airport sees 70% decrease in passenger numbers.", "entity_names": ["London Heathrow Airport"], "entity_types": ["location"]}
{"sentence": "Apple announces new iPhone release date.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "The CEO of Apple, Sarah Johnson, announces the launch of a new line of iPhones.", "entity_names": ["Apple", "Sarah Johnson"], "entity_types": ["organization", "person"]}
{"sentence": "The United Nations reports progress in peace negotiations in the Middle East, with Secretary-General Sarah Johnson overseeing the talks.", "entity_names": ["United Nations", "Middle East", "Sarah Johnson"], "entity_types": ["organization", "location", "person"]}
{"sentence": "Sarah Johnson, a renowned scientist, wins the Nobel Prize in Physics for her groundbreaking research on quantum mechanics.", "entity_names": ["Sarah Johnson"], "entity_types": ["person"]}
{"sentence": "Donatella Versace to open new flagship store in The Dubai Mall.", "entity_names": ["Donatella Versace", "The Dubai Mall"], "entity_types": ["person", "location"]}
{"sentence": "Annual fashion show to feature prominent designers including Donatella Versace.", "entity_names": ["Donatella Versace"], "entity_types": ["person"]}
{"sentence": "The Dubai Mall announces expansion plans to accommodate more luxury retailers.", "entity_names": ["The Dubai Mall"], "entity_types": ["location"]}
{"sentence": "The National Association for the Advancement of Colored People plans to hold a rally next week.", "entity_names": ["National Association for the Advancement of Colored People"], "entity_types": ["organization"]}
{"sentence": "Sarah Khan appointed as the new CEO of a major tech company.", "entity_names": ["Sarah Khan"], "entity_types": ["person"]}
{"sentence": "The annual conference of the National Association for the Advancement of Colored People will be held in Washington DC.", "entity_names": ["National Association for the Advancement of Colored People"], "entity_types": ["organization"]}
{"sentence": "Cape Town braces for severe drought as water levels continue to drop.", "entity_names": ["Cape Town"], "entity_types": ["location"]}
{"sentence": "UN Intergovernmental Panel on Climate Change releases new report on rising sea levels.", "entity_names": ["UN Intergovernmental Panel on Climate Change"], "entity_types": ["organization"]}
{"sentence": "Dr. David Lee appointed as head of department at prestigious medical research institute.", "entity_names": ["Dr. David Lee"], "entity_types": ["person"]}
{"sentence": "Tech giant Apple unveils new iPhone 13.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "Paris Hilton launches new fragrance line.", "entity_names": ["Paris Hilton"], "entity_types": ["person"]}
{"sentence": "Protesters gather outside United Nations headquarters in New York.", "entity_names": ["United Nations", "New York"], "entity_types": ["organization", "location"]}
{"sentence": "Pope Francis to visit the Church of the Holy Sepulchre in Jerusalem next week.", "entity_names": ["Pope Francis", "Church of the Holy Sepulchre", "Jerusalem"], "entity_types": ["person", "location", "location"]}
{"sentence": "United Nations Security Council to discuss ceasefire in Syria at emergency meeting.", "entity_names": ["United Nations Security Council", "Syria"], "entity_types": ["organization", "location"]}
{"sentence": "Tesla reports record-breaking sales in the second quarter.", "entity_names": ["Tesla"], "entity_types": ["organization"]}
{"sentence": "Tim Cook announces plans to expand Apple's presence in China.", "entity_names": ["Tim Cook", "Apple", "China"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Stocks on the Frankfurt Stock Exchange plunge after new trade tariffs are announced.", "entity_names": ["Frankfurt Stock Exchange"], "entity_types": ["organization"]}
{"sentence": "Frankfurt Stock Exchange hits record high as tech companies continue to thrive.", "entity_names": ["Frankfurt Stock Exchange"], "entity_types": ["organization"]}
{"sentence": "Starbucks partners with Rainforest Alliance to promote sustainable farming practices.", "entity_names": ["Starbucks", "Rainforest Alliance"], "entity_types": ["organization", "organization"]}
{"sentence": "Rainforest Alliance releases new report on deforestation in the Amazon.", "entity_names": ["Rainforest Alliance", "Amazon"], "entity_types": ["organization", "location"]}
{"sentence": "Climate change threatens the future of Rainforest Alliance certified coffee farms.", "entity_names": ["Rainforest Alliance"], "entity_types": ["organization"]}
{"sentence": "Tesla announces plans to open new manufacturing facility in Austin, Texas.", "entity_names": ["Tesla", "Austin", "Texas"], "entity_types": ["organization", "location", "location"]}
{"sentence": "United Nations reports increase in humanitarian aid to war-torn regions.", "entity_names": ["United Nations"], "entity_types": ["organization"]}
{"sentence": "Jerusalem's Old City sees increase in tourism after easing of COVID-19 restrictions.", "entity_names": ["Jerusalem"], "entity_types": ["location"]}
{"sentence": "Israel's Ministry of Health announces new vaccine mandate for all healthcare workers in Jerusalem.", "entity_names": ["Israel", "Ministry of Health", "Jerusalem"], "entity_types": ["location", "organization", "location"]}
{"sentence": "Apple Inc. announces plans to open new manufacturing plant in Texas.", "entity_names": ["Apple Inc.", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "CEO of SpaceX, Elon Musk, announces ambitious plan to colonize Mars within the next decade.", "entity_names": ["SpaceX", "Elon Musk", "Mars"], "entity_types": ["organization", "person", "location"]}
{"sentence": "Climate change activists protest outside United Nations headquarters in New York City.", "entity_names": ["United Nations", "New York City"], "entity_types": ["organization", "location"]}
{"sentence": "Immigrant Legal Resource Center files lawsuit against the Department of Homeland Security over new immigration policy.", "entity_names": ["Immigrant Legal Resource Center", "Department of Homeland Security"], "entity_types": ["organization", "organization"]}
{"sentence": "ICE raids target employers hiring unauthorized workers.", "entity_names": ["ICE"], "entity_types": ["organization"]}
{"sentence": "Renowned author and poet Maya Angelou to be honored with new stamp.", "entity_names": ["Maya Angelou"], "entity_types": ["person"]}
{"sentence": "Maya Angelou's autobiography 'I Know Why the Caged Bird Sings' to be adapted into a stage play.", "entity_names": ["Maya Angelou"], "entity_types": ["person"]}
{"sentence": "Maya Angelou's legacy celebrated on what would have been her 90th birthday.", "entity_names": ["Maya Angelou"], "entity_types": ["person"]}
{"sentence": "Patrick Mahomes signs 10-year contract extension with Kansas City Chiefs.", "entity_names": ["Patrick Mahomes", "Kansas City Chiefs"], "entity_types": ["person", "organization"]}
{"sentence": "Megan Rapinoe becomes first openly gay woman to appear in Sports Illustrated swimsuit issue.", "entity_names": ["Megan Rapinoe", "Sports Illustrated"], "entity_types": ["person", "organization"]}
{"sentence": "Patrick Mahomes leads Kansas City Chiefs to Super Bowl victory.", "entity_names": ["Patrick Mahomes", "Kansas City Chiefs"], "entity_types": ["person", "organization"]}
{"sentence": "Sydney experiences record-breaking heatwave.", "entity_names": ["Sydney"], "entity_types": ["location"]}
{"sentence": "New study shows Sydney's air pollution levels remain dangerously high.", "entity_names": ["Sydney"], "entity_types": ["location"]}
{"sentence": "Tesla surpasses expectations, reaching new record high in stock market.", "entity_names": ["Tesla"], "entity_types": ["organization"]}
{"sentence": "Tokyo Olympics officially postponed to 2021.", "entity_names": ["Tokyo"], "entity_types": ["location"]}
{"sentence": "Dr. Anthony Fauci warns of potential 'resurgence' of COVID-19 in the fall.", "entity_names": ["Dr. Anthony Fauci"], "entity_types": ["person"]}
{"sentence": "Amsterdam implements new bike lanes to promote safer commuting for cyclists.", "entity_names": ["Amsterdam"], "entity_types": ["location"]}
{"sentence": "New report from The Intergovernmental Panel on Climate Change warns of dire consequences if global temperatures continue to rise.", "entity_names": ["The Intergovernmental Panel on Climate Change"], "entity_types": ["organization"]}
{"sentence": "The United Nations Secretary-General praises The Intergovernmental Panel on Climate Change for its groundbreaking research on climate change.", "entity_names": ["United Nations Secretary-General", "The Intergovernmental Panel on Climate Change"], "entity_types": ["organization", "organization"]}
{"sentence": "The Intergovernmental Panel on Climate Change calls for immediate action to mitigate the impact of climate change on vulnerable communities.", "entity_names": ["The Intergovernmental Panel on Climate Change"], "entity_types": ["organization"]}
{"sentence": "Johnson Elementary School wins first place in national chess tournament.", "entity_names": ["Johnson Elementary School"], "entity_types": ["organization"]}
{"sentence": "Three students from Johnson Elementary School to represent their state in the National Science Fair.", "entity_names": ["Johnson Elementary School"], "entity_types": ["organization"]}
{"sentence": "Local community rallies behind Johnson Elementary School after devastating fire.", "entity_names": ["Johnson Elementary School"], "entity_types": ["organization"]}
{"sentence": "Record-breaking heatwave hits Los Angeles.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}
{"sentence": "Los Angeles Mayor announces new funding for homeless shelters.", "entity_names": ["Los Angeles", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "J.K. Rowling's new book release date revealed", "entity_names": ["J.K. Rowling"], "entity_types": ["person"]}
{"sentence": "Banksy's latest artwork discovered in London", "entity_names": ["Banksy", "London"], "entity_types": ["person", "location"]}
{"sentence": "Local school receives donation from J.K. Rowling", "entity_names": ["J.K. Rowling"], "entity_types": ["person"]}
{"sentence": "Mondelez International announces partnership with local farmers to promote sustainable cocoa production.", "entity_names": ["Mondelez International"], "entity_types": ["organization"]}
{"sentence": "Unilever acquires snack brands from Mondelez International for $2.7 billion.", "entity_names": ["Unilever", "Mondelez International"], "entity_types": ["organization", "organization"]}
{"sentence": "Kraft Heinz reports increase in sales, surpassing analyst expectations for the quarter.", "entity_names": ["Kraft Heinz"], "entity_types": ["organization"]}
{"sentence": "Pope Francis visits Vatican City to address climate change.", "entity_names": ["Pope Francis", "Vatican City"], "entity_types": ["person", "location"]}
{"sentence": "World Health Organization declares new COVID-19 variant a global concern.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}
{"sentence": "Firefighters battle massive wildfire in California.", "entity_names": ["California"], "entity_types": ["location"]}
{"sentence": "CDC warns of potential new COVID-19 variant.", "entity_names": ["CDC", "COVID-19"], "entity_types": ["organization", "organization"]}
{"sentence": "Celebrity chef Guy Fieri to open new restaurant in New York City.", "entity_names": ["Guy Fieri", "New York City"], "entity_types": ["person", "location"]}
{"sentence": "Food Network star Guy Fieri launches his own line of barbecue sauce.", "entity_names": ["Food Network", "Guy Fieri"], "entity_types": ["organization", "person"]}
{"sentence": "Guy Fieri's cooking show 'Diners, Drive-Ins and Dives' renewed for another season.", "entity_names": ["Guy Fieri"], "entity_types": ["person"]}
{"sentence": "Severe bushfires in Australia prompt warnings from the Australian Bureau of Meteorology.", "entity_names": ["Australia", "Australian Bureau of Meteorology"], "entity_types": ["location", "organization"]}
{"sentence": "Australian Bureau of Meteorology predicts record-breaking heatwave across the country.", "entity_names": ["Australian Bureau of Meteorology"], "entity_types": ["organization"]}
{"sentence": "Heatwave in Sydney reaches new highs, according to data from the Australian Bureau of Meteorology.", "entity_names": ["Sydney", "Australian Bureau of Meteorology"], "entity_types": ["location", "organization"]}
{"sentence": "Israel reopens its borders to vaccinated tourists.", "entity_names": ["Israel"], "entity_types": ["location"]}
{"sentence": "The Australian government announces new funding for Canberra infrastructure projects.", "entity_names": ["Australian government", "Canberra"], "entity_types": ["organization", "location"]}
{"sentence": "Coach Rachel Turner leads her team to victory in the championship game.", "entity_names": ["Coach Rachel Turner"], "entity_types": ["person"]}
{"sentence": "Pope Francis visits Mexico to address issues of poverty and immigration.", "entity_names": ["Pope Francis", "Mexico"], "entity_types": ["person", "location"]}
{"sentence": "Thousands flock to Niagara Falls for the annual fireworks display on July 4th.", "entity_names": ["Niagara Falls"], "entity_types": ["location"]}
{"sentence": "New species of endangered monkey discovered in Sundarbans National Park.", "entity_names": ["Sundarbans National Park"], "entity_types": ["location"]}
{"sentence": "Government allocates funding for infrastructure development in Sundarbans National Park.", "entity_names": ["Sundarbans National Park"], "entity_types": ["location"]}
{"sentence": "Environmental group protest proposed industrial development near Sundarbans National Park.", "entity_names": ["Sundarbans National Park"], "entity_types": ["location"]}
{"sentence": "Fashion icon Iris Apfel launches new line of eyewear.", "entity_names": ["Iris Apfel"], "entity_types": ["person"]}
{"sentence": "Janet Mock makes history as the first trans woman of color to direct a TV series.", "entity_names": ["Janet Mock"], "entity_types": ["person"]}
{"sentence": "Munroe Bergdorf appointed as the new diversity consultant for the fashion brand.", "entity_names": ["Munroe Bergdorf"], "entity_types": ["person"]}
{"sentence": "The annual LGBTQ+ parade in San Francisco draws thousands of participants.", "entity_names": ["San Francisco"], "entity_types": ["location"]}
{"sentence": "PFLAG releases new educational resources for LGBTQ+ youth and families.", "entity_names": ["PFLAG", "LGBTQ+"], "entity_types": ["organization", "organization"]}
{"sentence": "Local PFLAG chapter to host virtual support group for parents of transgender children.", "entity_names": ["PFLAG"], "entity_types": ["organization"]}
{"sentence": "PFLAG advocates for LGBTQ+ rights in upcoming city council meeting.", "entity_names": ["PFLAG", "LGBTQ+"], "entity_types": ["organization", "organization"]}
{"sentence": "German Chancellor Angela Merkel meets with French President Emmanuel Macron to discuss EU economic reforms.", "entity_names": ["Angela Merkel", "Emmanuel Macron"], "entity_types": ["person", "person"]}
{"sentence": "Angela Merkel announces plan to invest \u20ac130 billion in renewable energy infrastructure.", "entity_names": ["Angela Merkel"], "entity_types": ["person"]}
{"sentence": "Angela Merkel's conservative CDU party loses ground in local elections.", "entity_names": ["Angela Merkel", "CDU"], "entity_types": ["person", "organization"]}
{"sentence": "Atheist Alliance International condemns Archbishop Justin Welby's comments on atheism.", "entity_names": ["Atheist Alliance International", "Archbishop Justin Welby"], "entity_types": ["organization", "person"]}
{"sentence": "Archbishop Justin Welby meets with leaders of Atheist Alliance International to discuss religious tolerance.", "entity_names": ["Archbishop Justin Welby", "Atheist Alliance International"], "entity_types": ["person", "organization"]}
{"sentence": "Atheist Alliance International launches new campaign to promote secularism in schools with Archbishop Justin Welby's support.", "entity_names": ["Atheist Alliance International", "Archbishop Justin Welby"], "entity_types": ["organization", "person"]}
{"sentence": "Marissa Mayer appointed as the new CEO of a major tech company.", "entity_names": ["Marissa Mayer", "tech company"], "entity_types": ["person", "organization"]}
{"sentence": "Amazon.com, Inc. launches new subscription service for prime members.", "entity_names": ["Amazon.com, Inc."], "entity_types": ["organization"]}
{"sentence": "Elon Musk announces plans for new space exploration project.", "entity_names": ["Elon Musk"], "entity_types": ["person"]}
{"sentence": "Kim Kardashian launches new beauty line.", "entity_names": ["Kim Kardashian"], "entity_types": ["person"]}
{"sentence": "Major earthquake hits Los Angeles, causing widespread damage.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}
{"sentence": "Golden State Warriors defeat Houston Rockets in thrilling overtime game.", "entity_names": ["Golden State Warriors", "Houston Rockets"], "entity_types": ["organization", "organization"]}
{"sentence": "Emily Rodriguez appointed as new chief executive officer of Chicago-based tech company.", "entity_names": ["Emily Rodriguez", "Chicago"], "entity_types": ["person", "location"]}
{"sentence": "Chicago mayor announces plans to invest in infrastructure improvements.", "entity_names": ["Chicago"], "entity_types": ["location"]}
{"sentence": "Emily Rodriguez awarded prestigious business award for leadership in Chicago community.", "entity_names": ["Emily Rodriguez", "Chicago"], "entity_types": ["person", "location"]}
{"sentence": "Tom Cruise to receive special award from British Academy of Film and Television Arts (BAFTA) for outstanding contribution to film.", "entity_names": ["Tom Cruise", "British Academy of Film and Television Arts"], "entity_types": ["person", "organization"]}
{"sentence": "British Academy of Film and Television Arts (BAFTA) announces nominations for upcoming awards ceremony.", "entity_names": ["British Academy of Film and Television Arts"], "entity_types": ["organization"]}
{"sentence": "Tom Cruise's latest film tops box office over the weekend.", "entity_names": ["Tom Cruise"], "entity_types": ["person"]}
{"sentence": "Chaz Bono to star in new reality TV show.", "entity_names": ["Chaz Bono"], "entity_types": ["person"]}
{"sentence": "Rio de Janeiro celebrates Carnival with colorful parades and lively music.", "entity_names": ["Rio de Janeiro"], "entity_types": ["location"]}
{"sentence": "Brazil announces plan to combat deforestation in the Amazon rainforest.", "entity_names": ["Brazil"], "entity_types": ["location"]}
{"sentence": "Rio de Janeiro to host 2024 Summer Olympics.", "entity_names": ["Rio de Janeiro"], "entity_types": ["location"]}
{"sentence": "Red Rocks Amphitheatre to welcome famous music artists for summer concert series.", "entity_names": ["Red Rocks Amphitheatre"], "entity_types": ["location"]}
{"sentence": "Greta Thunberg announces new climate change initiative.", "entity_names": ["Greta Thunberg"], "entity_types": ["person"]}
{"sentence": "David Brown appointed as CEO of tech startup.", "entity_names": ["David Brown"], "entity_types": ["person"]}
{"sentence": "Boris Johnson announces new environmental regulations.", "entity_names": ["Boris Johnson"], "entity_types": ["person"]}
{"sentence": "Protests erupt in London following Boris Johnson's controversial remarks.", "entity_names": ["London", "Boris Johnson"], "entity_types": ["location", "person"]}
{"sentence": "Boris Johnson meets with European Union leaders to discuss trade agreements.", "entity_names": ["Boris Johnson", "European Union"], "entity_types": ["person", "organization"]}
{"sentence": "Lieutenant Colonel Mark Thompson resigns from his position as head of the military defense division.", "entity_names": ["Lieutenant Colonel Mark Thompson"], "entity_types": ["person"]}
{"sentence": "Investigations into the financial scandal at ABC Corporation reveal shocking details.", "entity_names": ["ABC Corporation"], "entity_types": ["organization"]}
{"sentence": "Protests erupt in downtown New York City over police brutality.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Sydney Opera House announces lineup for summer concert series.", "entity_names": ["Sydney Opera House"], "entity_types": ["location"]}
{"sentence": "Investigation reveals ties between mayor's office and local construction company.", "entity_names": ["mayor's office", "construction company"], "entity_types": ["organization", "organization"]}
{"sentence": "Teenage climate activist speaks at United Nations summit in New York.", "entity_names": ["United Nations", "New York"], "entity_types": ["organization", "location"]}
{"sentence": "Oxford University announces plans to open new research facility in London.", "entity_names": ["Oxford University", "London"], "entity_types": ["organization", "location"]}
{"sentence": "Jennifer Lopez to make guest appearance on popular TV show.", "entity_names": ["Jennifer Lopez"], "entity_types": ["person"]}
{"sentence": "The latest production of Shakespeare's Hamlet received rave reviews from theater critics.", "entity_names": ["Shakespeare"], "entity_types": ["person"]}
{"sentence": "The new Shakespeare exhibition at the British Museum is drawing large crowds of tourists.", "entity_names": ["Shakespeare", "British Museum"], "entity_types": ["person", "organization"]}
{"sentence": "Stratford-upon-Avon, the birthplace of Shakespeare, is a popular destination for literary enthusiasts.", "entity_names": ["Stratford-upon-Avon", "Shakespeare"], "entity_types": ["location", "person"]}
{"sentence": "Moscow announces plan to improve public transportation infrastructure.", "entity_names": ["Moscow"], "entity_types": ["location"]}
{"sentence": "Elizabeth Warren proposes new legislation to address student loan debt.", "entity_names": ["Elizabeth Warren"], "entity_types": ["person"]}
{"sentence": "Russia imposes sanctions on Western countries in response to diplomatic tensions.", "entity_names": ["Russia"], "entity_types": ["location"]}
{"sentence": "New discovery of ancient artifacts near Giza pyramid complex.", "entity_names": ["Giza"], "entity_types": ["location"]}
{"sentence": "Google launches new privacy feature for email users.", "entity_names": ["Google"], "entity_types": ["organization"]}
{"sentence": "Local residents protest proposed construction of new Google headquarters.", "entity_names": ["Google"], "entity_types": ["organization"]}
{"sentence": "Dwayne \"The Rock\" Johnson announces new partnership with fitness brand.", "entity_names": ["Dwayne \"The Rock\" Johnson", "fitness brand"], "entity_types": ["person", "organization"]}
{"sentence": "New York City reports record-high temperatures for the month of July.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Tesla's CEO Elon Musk unveils plans for new electric vehicle model.", "entity_names": ["Tesla", "Elon Musk"], "entity_types": ["organization", "person"]}
{"sentence": "Tech giant Apple Inc. launches new iPhone model.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Local artist John Smith to exhibit new work at gallery next month.", "entity_names": ["John Smith"], "entity_types": ["person"]}
{"sentence": "National Park Service to implement new conservation measures in Everglades National Park, USA.", "entity_names": ["National Park Service", "Everglades National Park", "USA"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Renowned environmentalist to speak at Everglades National Park, USA Earth Day event.", "entity_names": ["Everglades National Park", "USA"], "entity_types": ["location", "location"]}
{"sentence": "Federal funding approved for restoration project in Everglades National Park, USA.", "entity_names": ["Everglades National Park", "USA"], "entity_types": ["location", "location"]}
{"sentence": "Brie Larson wins Best Actress at the Atlanta Film Festival.", "entity_names": ["Brie Larson", "Atlanta"], "entity_types": ["person", "location"]}
{"sentence": "Berlin Marathon postponed due to COVID-19 pandemic.", "entity_names": ["Berlin"], "entity_types": ["location"]}
{"sentence": "Best-selling author Chinua Achebe passes away at 82.", "entity_names": ["Chinua Achebe"], "entity_types": ["person"]}
{"sentence": "J.K. Rowling's new book tops the bestseller list.", "entity_names": ["J.K. Rowling"], "entity_types": ["person"]}
{"sentence": "Philanthropist donates $1 million to local children's hospital.", "entity_names": [], "entity_types": []}
{"sentence": "United States Department of Defense announces new cybersecurity initiative.", "entity_names": ["United States Department of Defense"], "entity_types": ["organization"]}
{"sentence": "California wildfires force thousands to evacuate.", "entity_names": ["California"], "entity_types": ["location"]}
{"sentence": "Famous actor Tom Hanks to star in new World War II film.", "entity_names": ["Tom Hanks"], "entity_types": ["person"]}
{"sentence": "Los Angeles mayor announces new traffic control measures.", "entity_names": ["Los Angeles", "mayor"], "entity_types": ["location", "person"]}
{"sentence": "The Los Angeles Lakers will face the Golden State Warriors in their next game.", "entity_names": ["Los Angeles", "Lakers", "Golden State Warriors"], "entity_types": ["location", "organization", "organization"]}
{"sentence": "Heavy rain causes flooding in Los Angeles area.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}
{"sentence": "The United Nations releases new report on global climate change.", "entity_names": ["United Nations"], "entity_types": ["organization"]}
{"sentence": "Elon Musk's SpaceX plans to launch a new satellite into orbit next week.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "London Mayor announces plan to reduce air pollution in the city center.", "entity_names": ["London", "Mayor"], "entity_types": ["location", "organization"]}
{"sentence": "Moscow mayor announces new public transportation initiatives.", "entity_names": ["Moscow"], "entity_types": ["location"]}
{"sentence": "Top chef Fatima Ali launches new cooking show.", "entity_names": ["Fatima Ali"], "entity_types": ["person"]}
{"sentence": "Isabella Costa re-elected as president of the United Nations General Assembly.", "entity_names": ["Isabella Costa", "United Nations General Assembly"], "entity_types": ["person", "organization"]}
{"sentence": "Jews for Jesus celebrates 40 years of ministry.", "entity_names": ["Jews for Jesus"], "entity_types": ["organization"]}
{"sentence": "New York City to host annual Jews for Jesus conference.", "entity_names": ["New York City", "Jews for Jesus"], "entity_types": ["location", "organization"]}
{"sentence": "Former member of Jews for Jesus speaks out against the organization.", "entity_names": ["Jews for Jesus"], "entity_types": ["organization"]}
{"sentence": "Local high school to host It Gets Better Project assembly", "entity_names": ["high school", "It Gets Better Project"], "entity_types": ["organization", "organization"]}
{"sentence": "ISRO launches new satellite into orbit around Venus.", "entity_names": ["ISRO", "Venus"], "entity_types": ["organization", "location"]}
{"sentence": "Rocket Lab successfully tests new reusable rocket technology.", "entity_names": ["Rocket Lab"], "entity_types": ["organization"]}
{"sentence": "Scientists discover possible signs of life on Venus.", "entity_names": ["Venus"], "entity_types": ["location"]}
{"sentence": "Heavy rainfall causes flooding and road closures in Coventry, England.", "entity_names": ["Coventry", "England"], "entity_types": ["location", "location"]}
{"sentence": "Coventry City Council adopts new environmental sustainability initiative.", "entity_names": ["Coventry City Council"], "entity_types": ["organization"]}
{"sentence": "Local musician from Coventry, England, to perform at upcoming music festival.", "entity_names": ["Coventry", "England"], "entity_types": ["location", "location"]}
{"sentence": "Tech giant Apple announces the launch of new iPhone models.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "Senator Harris to visit Texas to discuss immigration reform.", "entity_names": ["Senator Harris", "Texas"], "entity_types": ["person", "location"]}
{"sentence": "EU imposes sanctions on Russian officials over human rights violations.", "entity_names": ["EU"], "entity_types": ["organization"]}
{"sentence": "The National Black Justice Coalition condemns the recent hate crime in Atlanta.", "entity_names": ["National Black Justice Coalition", "Atlanta"], "entity_types": ["organization", "location"]}
{"sentence": "The Association of LGBTQ Journalists announces new scholarship program for aspiring writers.", "entity_names": ["The Association of LGBTQ Journalists"], "entity_types": ["organization"]}
{"sentence": "Two transgender women elected to local government positions in California.", "entity_names": ["California"], "entity_types": ["location"]}
{"sentence": "Elon Musk's SpaceX Successfully Launches Crewed Mission to International Space Station.", "entity_names": ["Elon Musk", "SpaceX", "International Space Station"], "entity_types": ["person", "organization", "location"]}
{"sentence": "New York City to Invest $10 Billion in Infrastructure Projects Over Next 5 Years.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Apple Inc. Announces Plan to Open 20 New Retail Stores in China.", "entity_names": ["Apple Inc.", "China"], "entity_types": ["organization", "location"]}
{"sentence": "Elon Musk's SpaceX launches successful satellite into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "Forest fires rage through Northern California, causing widespread destruction.", "entity_names": ["Northern California"], "entity_types": ["location"]}
{"sentence": "Microsoft to acquire leading cybersecurity firm in multi-billion dollar deal.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}
{"sentence": "Royal Caribbean International offers 30% discount on select cruises.", "entity_names": ["Royal Caribbean International"], "entity_types": ["organization"]}
{"sentence": "New CEO appointed at Royal Caribbean International.", "entity_names": ["Royal Caribbean International"], "entity_types": ["organization"]}
{"sentence": "Royal Caribbean International announces plans for new cruise terminal in Miami.", "entity_names": ["Royal Caribbean International", "Miami"], "entity_types": ["organization", "location"]}
{"sentence": "The Green Climate Fund announces a $50 million investment in renewable energy projects in Africa.", "entity_names": ["Green Climate Fund", "Africa"], "entity_types": ["organization", "location"]}
{"sentence": "The Green Alliance calls for stricter regulations on industrial emissions to combat climate change.", "entity_names": ["Green Alliance"], "entity_types": ["organization"]}
{"sentence": "350.org launches global campaign to divest from fossil fuels.", "entity_names": ["350.org"], "entity_types": ["organization"]}
{"sentence": "McDonald's opens 50 new locations in China amid growing demand for fast food.", "entity_names": ["McDonald's", "China"], "entity_types": ["organization", "location"]}
{"sentence": "Berlin experiences a surge in tourism as travel restrictions ease.", "entity_names": ["Berlin"], "entity_types": ["location"]}
{"sentence": "Chef Giada De Laurentiis launches new line of pasta sauces in collaboration with Italian food company.", "entity_names": ["Giada De Laurentiis", "Italian food company"], "entity_types": ["person", "organization"]}
{"sentence": "Stella McCartney launches new sustainable fashion line.", "entity_names": ["Stella McCartney"], "entity_types": ["person"]}
{"sentence": "The fashion boutiques of New York City prepare for the upcoming holiday season.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Paris Fashion Week attracts top designers and fashion enthusiasts from around the world.", "entity_names": ["Paris Fashion Week"], "entity_types": ["organization"]}
{"sentence": "Donatella Versace launches new fashion line in The Soho District.", "entity_names": ["Donatella Versace", "The Soho District"], "entity_types": ["person", "location"]}
{"sentence": "The Soho District in New York City implements new safety measures to improve pedestrian traffic flow.", "entity_names": ["The Soho District", "New York City"], "entity_types": ["location", "location"]}
{"sentence": "Donatella Versace partners with a non-profit organization to support mental health awareness in The Soho District.", "entity_names": ["Donatella Versace", "The Soho District"], "entity_types": ["person", "location"]}
{"sentence": "New York City votes to increase minimum wage to $15 per hour by 2022.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Mayor de Blasio announces new initiative to combat homelessness in New York City.", "entity_names": ["de Blasio", "New York City"], "entity_types": ["person", "location"]}
{"sentence": "New York City Marathon attracts over 50,000 runners from around the world.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Celebrity chef Gordon Ramsay joins the Food Network as a host for a new cooking show.", "entity_names": ["Gordon Ramsay", "Food Network"], "entity_types": ["person", "organization"]}
{"sentence": "Food Network launches new streaming service for food enthusiasts.", "entity_names": ["Food Network"], "entity_types": ["organization"]}
{"sentence": "Food Network announces partnership with renowned pastry chef for new baking competition series.", "entity_names": ["Food Network"], "entity_types": ["organization"]}
{"sentence": "Microsoft's Terry Myerson Announces New Product Launch.", "entity_names": ["Microsoft", "Terry Myerson"], "entity_types": ["organization", "person"]}
{"sentence": "Terry Myerson to Step Down as Head of Windows at Microsoft.", "entity_names": ["Terry Myerson", "Windows", "Microsoft"], "entity_types": ["person", "organization", "organization"]}
{"sentence": "Terry Myerson joins startup as Chief Technology Officer.", "entity_names": ["Terry Myerson"], "entity_types": ["person"]}
{"sentence": "Rolling Stone magazine releases list of top 100 greatest drummers of all time.", "entity_names": ["Rolling Stone"], "entity_types": ["organization"]}
{"sentence": "New exhibit at the museum features iconic photographs from Rolling Stone.", "entity_names": ["Rolling Stone"], "entity_types": ["organization"]}
{"sentence": "Rolling Stone journalist interviews famous rock band for cover story.", "entity_names": ["Rolling Stone"], "entity_types": ["organization"]}
{"sentence": "Ford Motor Company to open new manufacturing plant in Shanghai.", "entity_names": ["Ford Motor Company", "Shanghai"], "entity_types": ["organization", "location"]}
{"sentence": "Shanghai experiences heavy rainfall, causing widespread flooding.", "entity_names": ["Shanghai"], "entity_types": ["location"]}
{"sentence": "Investigation reveals corruption scandal within Shanghai government officials.", "entity_names": ["Shanghai"], "entity_types": ["location"]}
{"sentence": "Chelsea Football Club 's new stadium plans face opposition from local residents.", "entity_names": ["Chelsea Football Club"], "entity_types": ["organization"]}
{"sentence": "Record-breaking goal scorer signs contract extension with Chelsea Football Club .", "entity_names": ["Chelsea Football Club"], "entity_types": ["organization"]}
{"sentence": "President Biden signs infrastructure bill into law.", "entity_names": ["Biden"], "entity_types": ["person"]}
{"sentence": "Wildfires continue to devastate California forests.", "entity_names": ["California"], "entity_types": ["location"]}
{"sentence": "Amnesty International reports human rights abuses in Myanmar.", "entity_names": ["Amnesty International", "Myanmar"], "entity_types": ["organization", "location"]}
{"sentence": "Protests erupt in response to controversial police shooting in New York City.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Amnesty International calls for investigation into alleged war crimes in Syria.", "entity_names": ["Amnesty International", "Syria"], "entity_types": ["organization", "location"]}
{"sentence": "UNICEF launches campaign to provide clean water to communities in Africa.", "entity_names": ["UNICEF", "Africa"], "entity_types": ["organization", "location"]}
{"sentence": "Young girl rescued by UNICEF from child labor in Southeast Asia.", "entity_names": ["UNICEF", "Southeast Asia"], "entity_types": ["organization", "location"]}
{"sentence": "UNICEF ambassador urges global leaders to prioritize education for children in conflict zones.", "entity_names": ["UNICEF"], "entity_types": ["organization"]}
{"sentence": "Malala Yousafzai receives Nobel Peace Prize for her advocacy for girls' education.", "entity_names": ["Malala Yousafzai"], "entity_types": ["person"]}
{"sentence": "Berlin Marathon attracts over 40,000 runners from around the world.", "entity_names": ["Berlin"], "entity_types": ["location"]}
{"sentence": "Organization for Economic Cooperation and Development reports economic growth in Europe.", "entity_names": ["Organization for Economic Cooperation and Development", "Europe"], "entity_types": ["organization", "location"]}
{"sentence": "Technology Coordinator Daniel Carter appointed as head of new cybersecurity task force.", "entity_names": ["Technology Coordinator Daniel Carter"], "entity_types": ["person"]}
{"sentence": "Local organization partners with Technology Coordinator Daniel Carter to provide free coding classes for underprivileged youth.", "entity_names": ["Technology Coordinator Daniel Carter"], "entity_types": ["person"]}
{"sentence": "Technology Coordinator Daniel Carter warns of potential data breach in upcoming software update.", "entity_names": ["Technology Coordinator Daniel Carter"], "entity_types": ["person"]}
{"sentence": "NASA scientists are designing a Mars Habitat to sustain human life on the red planet.", "entity_names": ["NASA", "Mars Habitat"], "entity_types": ["organization", "location"]}
{"sentence": "Elon Musk's company plans to establish the first Mars Colony by 2050.", "entity_names": ["Elon Musk", "Mars Colony"], "entity_types": ["person", "location"]}
{"sentence": "The astronauts at Johnson Space Center are undergoing rigorous training for the upcoming Mars mission.", "entity_names": ["Johnson Space Center"], "entity_types": ["organization"]}
{"sentence": "The Stonewall Inn designated as a National Historic Landmark.", "entity_names": ["Stonewall Inn"], "entity_types": ["location"]}
{"sentence": "New York City's famous Stonewall Inn celebrates 50th anniversary.", "entity_names": ["New York City", "Stonewall Inn"], "entity_types": ["location", "location"]}
{"sentence": "The Stonewall Inn, a symbol of LGBTQ+ rights, attracts visitors from around the world.", "entity_names": ["Stonewall Inn", "LGBTQ+"], "entity_types": ["location", "organization"]}
{"sentence": "Kobe Bryant leads the Lakers to victory in the NBA Finals.", "entity_names": ["Kobe Bryant", "Lakers", "NBA"], "entity_types": ["person", "organization", "organization"]}
{"sentence": "Tom Brady signs a record-breaking contract with the NFL team.", "entity_names": ["Tom Brady", "NFL"], "entity_types": ["person", "organization"]}
{"sentence": "Professional Golfers' Association announces new schedule for upcoming tournaments.", "entity_names": ["Professional Golfers' Association"], "entity_types": ["organization"]}
{"sentence": "Austin City Limits Live at The Moody Theater announces new concert lineup.", "entity_names": ["Austin City Limits Live at The Moody Theater"], "entity_types": ["organization"]}
{"sentence": "Country music star to perform at Austin City Limits Live at The Moody Theater.", "entity_names": ["Austin City Limits Live at The Moody Theater"], "entity_types": ["organization"]}
{"sentence": "European Union imposes new sanctions on Russia in response to cyber attacks.", "entity_names": ["European Union", "Russia"], "entity_types": ["organization", "location"]}
{"sentence": "Greta Thunberg speaks at United Nations climate conference.", "entity_names": ["Greta Thunberg", "United Nations"], "entity_types": ["person", "organization"]}
{"sentence": "Sydney Opera House to host international music festival next month.", "entity_names": ["Sydney", "Opera House"], "entity_types": ["location", "organization"]}
{"sentence": "The Food and Agriculture Organization warns of potential food shortage due to climate change.", "entity_names": ["Food and Agriculture Organization"], "entity_types": ["organization"]}
{"sentence": "Sydney-based company announces major expansion plans.", "entity_names": ["Sydney"], "entity_types": ["location"]}
{"sentence": "England's national rugby team defeats New Zealand in a stunning upset.", "entity_names": ["England", "New Zealand"], "entity_types": ["location", "location"]}
{"sentence": "Local activist Mandy Dean leads protest against deforestation in the Amazon.", "entity_names": ["Mandy Dean", "Amazon"], "entity_types": ["person", "location"]}
{"sentence": "Sam Williams appointed as the new CEO of XYZ Corporation.", "entity_names": ["Sam Williams", "XYZ Corporation"], "entity_types": ["person", "organization"]}
{"sentence": "Severe sandstorm sweeps across the Sahara Desert causing widespread damage.", "entity_names": ["Sahara Desert"], "entity_types": ["location"]}
{"sentence": "National Weather Service warns of potential flash floods in the Midwest region.", "entity_names": ["National Weather Service", "Midwest"], "entity_types": ["organization", "location"]}
{"sentence": "The CEO of SpaceX announces plans to launch a new satellite into orbit next month.", "entity_names": ["SpaceX"], "entity_types": ["organization"]}
{"sentence": "Dr. Jane Smith , a renowned climate scientist, warns of the potential impact of rising sea levels on coastal communities.", "entity_names": ["Dr. Jane Smith"], "entity_types": ["person"]}
{"sentence": "Protesters gather in Bangkok to demand political reform.", "entity_names": ["Bangkok"], "entity_types": ["location"]}
{"sentence": "Tech giant Apple announces new product launch event in Bangkok next week.", "entity_names": ["Apple", "Bangkok"], "entity_types": ["organization", "location"]}
{"sentence": "Famous chef Gordon Ramsay to open new restaurant in Bangkok.", "entity_names": ["Gordon Ramsay", "Bangkok"], "entity_types": ["person", "location"]}
{"sentence": "Medecins Sans Frontieres sends medical teams to assist in earthquake-affected regions.", "entity_names": ["Medecins Sans Frontieres"], "entity_types": ["organization"]}
{"sentence": "Johns Hopkins Medicine announces breakthrough in cancer research.", "entity_names": ["Johns Hopkins Medicine"], "entity_types": ["organization"]}
{"sentence": "March of Dimes launches campaign to raise awareness for premature birth prevention.", "entity_names": ["March of Dimes"], "entity_types": ["organization"]}
{"sentence": "Verizon Communications Inc. announces a new partnership with Amazon.com Inc. for cloud computing services.", "entity_names": ["Verizon Communications Inc.", "Amazon.com Inc."], "entity_types": ["organization", "organization"]}
{"sentence": "Tokyo reports a record number of COVID-19 cases in a single day.", "entity_names": ["Tokyo"], "entity_types": ["location"]}
{"sentence": "Shenzhen to invest $10 billion in renewable energy projects over the next five years.", "entity_names": ["Shenzhen"], "entity_types": ["location"]}
{"sentence": "General Motors announces plans to invest $1 billion in electric vehicle production.", "entity_names": ["General Motors"], "entity_types": ["organization"]}
{"sentence": "The CEO of General Motors, Mary Barra, will testify before Congress next week.", "entity_names": ["General Motors", "Mary Barra"], "entity_types": ["organization", "person"]}
{"sentence": "General Motors recalls 3 million vehicles due to faulty ignition switches.", "entity_names": ["General Motors"], "entity_types": ["organization"]}
{"sentence": "Leila Mahmood elected as the new mayor of Athens.", "entity_names": ["Leila Mahmood", "Athens"], "entity_types": ["person", "location"]}
{"sentence": "Greece announces new economic stimulus package to boost tourism.", "entity_names": ["Greece"], "entity_types": ["location"]}
{"sentence": "Athens Marathon cancelled due to COVID-19 pandemic.", "entity_names": ["Athens"], "entity_types": ["location"]}
{"sentence": "Oprah Winfrey launches new book club focusing on diverse authors.", "entity_names": ["Oprah Winfrey"], "entity_types": ["person"]}
{"sentence": "Local school receives generous donation from Oprah Winfrey.", "entity_names": ["Oprah Winfrey"], "entity_types": ["person"]}
{"sentence": "Oprah Winfrey to interview world leaders in new talk show series.", "entity_names": ["Oprah Winfrey"], "entity_types": ["person"]}
{"sentence": "Europa's new president vows to tackle climate change.", "entity_names": ["Europa"], "entity_types": ["organization"]}
{"sentence": "Renowned chef opens new restaurant in downtown Europa.", "entity_names": ["Europa"], "entity_types": ["location"]}
{"sentence": "Europa's trade agreement with Asia brings economic opportunities.", "entity_names": ["Europa", "Asia"], "entity_types": ["organization", "location"]}
{"sentence": "Ralph Lauren to release new fall collection at fashion week.", "entity_names": ["Ralph Lauren"], "entity_types": ["organization"]}
{"sentence": "Blake Lively spotted at premiere of new film.", "entity_names": ["Blake Lively"], "entity_types": ["person"]}
{"sentence": "Raf Simons named creative director of fashion house.", "entity_names": ["Raf Simons", "fashion house"], "entity_types": ["person", "organization"]}
{"sentence": "Brazilian President to visit Rio de Janeiro to address recent rise in crime rates.", "entity_names": ["Brazilian President", "Rio de Janeiro"], "entity_types": ["person", "location"]}
{"sentence": "Tourism in Rio de Janeiro takes hit as COVID-19 cases surge.", "entity_names": ["Rio de Janeiro"], "entity_types": ["location"]}
{"sentence": "Environmental organization launches campaign to protect endangered species in the Amazon rainforest.", "entity_names": ["organization", "Amazon rainforest"], "entity_types": ["organization", "location"]}
{"sentence": "APPLE ANNOUNCES LAUNCH OF NEW IPHONE MODEL.", "entity_names": ["APPLE"], "entity_types": ["organization"]}
{"sentence": "President Biden signs executive order on climate change.", "entity_names": ["Biden"], "entity_types": ["person"]}
{"sentence": "Tokyo Olympics to be held without spectators due to COVID-19 concerns.", "entity_names": ["Tokyo", "Olympics"], "entity_types": ["location", "organization"]}
{"sentence": "New study finds link between increased coffee consumption and decreased risk of Alzheimer's disease.", "entity_names": ["Alzheimer's disease"], "entity_types": ["organization"]}
{"sentence": "China imposes sanctions on United States after arms sale to Taiwan.", "entity_names": ["China", "United States", "Taiwan"], "entity_types": ["location", "location", "location"]}
{"sentence": "Famous actress opens animal rescue shelter in Los Angeles.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}
{"sentence": "The New York Times reports that the CEO of Apple Inc. will be stepping down.", "entity_names": ["The New York Times", "Apple Inc."], "entity_types": ["organization", "organization"]}
{"sentence": "In a recent interview, the mayor of London discussed plans for improving public transportation in the city.", "entity_names": ["London"], "entity_types": ["location"]}
{"sentence": "The New York Times released an article on the impact of climate change on small businesses in the Midwest.", "entity_names": ["The New York Times", "Midwest"], "entity_types": ["organization", "location"]}
{"sentence": "The new biography on Winston Churchill reveals his lesser-known political strategies.", "entity_names": ["Winston Churchill"], "entity_types": ["person"]}
{"sentence": "The summit between the United States and North Korea is set to take place in Vietnam next month.", "entity_names": ["United States", "North Korea", "Vietnam"], "entity_types": ["location", "location", "location"]}
{"sentence": "Apple Inc. unveils its latest line of products at the annual tech conference.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Marvel Studios announces new movie release date.", "entity_names": ["Marvel Studios"], "entity_types": ["organization"]}
{"sentence": "Actress Scarlett Johansson to star in new Marvel Studios film.", "entity_names": ["Scarlett Johansson", "Marvel Studios"], "entity_types": ["person", "organization"]}
{"sentence": "Marvel Studios surpasses box office records with latest superhero film.", "entity_names": ["Marvel Studios"], "entity_types": ["organization"]}
{"sentence": "Lake Victoria region faces severe drought crisis.", "entity_names": ["Lake Victoria"], "entity_types": ["location"]}
{"sentence": "Emma Watson appointed as UN Women Goodwill Ambassador.", "entity_names": ["Emma Watson", "UN Women"], "entity_types": ["person", "organization"]}
{"sentence": "Scientists discover new species of fish in Lake Victoria.", "entity_names": ["Lake Victoria"], "entity_types": ["location"]}
{"sentence": "Tokyo University researchers develop new cancer treatment.", "entity_names": ["Tokyo University"], "entity_types": ["organization"]}
{"sentence": "Barcelona to implement new urban transportation system.", "entity_names": ["Barcelona"], "entity_types": ["location"]}
{"sentence": "Spain announces plans to expand renewable energy production.", "entity_names": ["Spain"], "entity_types": ["location"]}
{"sentence": "The Smithsonian Institution celebrated its 175th anniversary with a special exhibit on American history.", "entity_names": ["The Smithsonian Institution"], "entity_types": ["organization"]}
{"sentence": "The Hermitage Museum in St. Petersburg, Russia, is undergoing extensive renovations to preserve its priceless art collection.", "entity_names": ["The Hermitage Museum", "St. Petersburg", "Russia"], "entity_types": ["organization", "location", "location"]}
{"sentence": "The Alliance of Independent Authors announced a new program to support self-published writers in reaching a wider audience.", "entity_names": ["The Alliance of Independent Authors"], "entity_types": ["organization"]}
{"sentence": "Megan Markle spotted vacationing in The Alps.", "entity_names": ["Megan Markle", "The Alps"], "entity_types": ["person", "location"]}
{"sentence": "Lake Titicaca in Bolivia experiences record-breaking water levels.", "entity_names": ["Lake Titicaca", "Bolivia"], "entity_types": ["location", "location"]}
{"sentence": "Rescue efforts underway in The Alps for stranded hikers.", "entity_names": ["The Alps"], "entity_types": ["location"]}
{"sentence": "Michelle Obama delivers keynote address at London climate summit.", "entity_names": ["Michelle Obama", "London"], "entity_types": ["person", "location"]}
{"sentence": "Nelson Mandela Foundation to open new center in England.", "entity_names": ["Nelson Mandela Foundation", "England"], "entity_types": ["organization", "location"]}
{"sentence": "London mayor announces new transportation initiative.", "entity_names": ["London"], "entity_types": ["location"]}
{"sentence": "Gordon Ramsay to open new restaurant in Mumbai.", "entity_names": ["Gordon Ramsay", "Mumbai"], "entity_types": ["person", "location"]}
{"sentence": "Mumbai experiences heavy rainfall, leading to waterlogging in several areas.", "entity_names": ["Mumbai"], "entity_types": ["location"]}
{"sentence": "Local chef partners with Gordon Ramsay for new cooking show.", "entity_names": ["Gordon Ramsay"], "entity_types": ["person"]}
{"sentence": "Germany announces new environmental protection laws.", "entity_names": ["Germany"], "entity_types": ["location"]}
{"sentence": "Renowned scientist from Germany awarded Nobel Prize for Physics.", "entity_names": ["scientist", "Germany"], "entity_types": ["person", "location"]}
{"sentence": "The Organization of American States calls for an emergency meeting to address the political crisis in Venezuela.", "entity_names": ["Organization of American States", "Venezuela"], "entity_types": ["organization", "location"]}
{"sentence": "African Union to deploy peacekeeping troops to the conflict-stricken region of South Sudan.", "entity_names": ["African Union", "South Sudan"], "entity_types": ["organization", "location"]}
{"sentence": "Canadian Prime Minister Justin Trudeau meets with European Union leaders to discuss trade agreements.", "entity_names": ["Justin Trudeau", "European Union"], "entity_types": ["person", "organization"]}
{"sentence": "Tech giant Apple launches new iPhone with enhanced camera features.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "President Biden announces plan to invest in infrastructure and create new jobs.", "entity_names": ["President Biden"], "entity_types": ["person"]}
{"sentence": "Wildfires continue to spread across California, prompting evacuations in several communities.", "entity_names": ["California"], "entity_types": ["location"]}
{"sentence": "The World Trade Organization announces new trade sanctions against China.", "entity_names": ["World Trade Organization", "China"], "entity_types": ["organization", "location"]}
{"sentence": "The National Health Service launches new campaign to promote healthy living.", "entity_names": ["National Health Service"], "entity_types": ["organization"]}
{"sentence": "Former President Obama delivers speech at United Nations General Assembly.", "entity_names": ["Obama", "United Nations General Assembly"], "entity_types": ["person", "organization"]}
{"sentence": "Jakarta hit by severe flooding after heavy rains.", "entity_names": ["Jakarta"], "entity_types": ["location"]}
{"sentence": "Justin Trudeau announces new climate change initiatives.", "entity_names": ["Justin Trudeau"], "entity_types": ["person"]}
{"sentence": "Local organization provides aid to Syrian refugees in Lebanon.", "entity_names": ["Lebanon"], "entity_types": ["location"]}
{"sentence": "NASA launches new Mars rover mission.", "entity_names": ["NASA", "Mars"], "entity_types": ["organization", "location"]}
{"sentence": "Kim Jong Un attends military parade in North Korea.", "entity_names": ["Kim Jong Un", "North Korea"], "entity_types": ["person", "location"]}
{"sentence": "NASA announces partnership with SpaceX for lunar exploration.", "entity_names": ["NASA", "SpaceX"], "entity_types": ["organization", "organization"]}
{"sentence": "Bindi Irwin announces new wildlife conservation initiative.", "entity_names": ["Bindi Irwin", "wildlife conservation initiative"], "entity_types": ["person", "organization"]}
{"sentence": "The Australian Zoo, founded by Bindi Irwin's family, celebrates its 50th anniversary.", "entity_names": ["Australian Zoo", "Bindi Irwin's family"], "entity_types": ["organization", "organization"]}
{"sentence": "Bindi Irwin's documentary about the Great Barrier Reef wins prestigious award at film festival.", "entity_names": ["Bindi Irwin", "Great Barrier Reef"], "entity_types": ["person", "location"]}
{"sentence": "Michelle Obama launches new initiative to promote girls' education.", "entity_names": ["Michelle Obama"], "entity_types": ["person"]}
{"sentence": "Former First Lady Michelle Obama visits schools to advocate for healthy eating.", "entity_names": ["Michelle Obama"], "entity_types": ["person"]}
{"sentence": "Michelle Obama Foundation partners with local organizations to support community health programs.", "entity_names": ["Michelle Obama"], "entity_types": ["person"]}
{"sentence": "The Red Cross provides aid to victims of the natural disaster.", "entity_names": ["Red Cross"], "entity_types": ["organization"]}
{"sentence": "World Trade Organization rules in favor of the United States in trade dispute with China.", "entity_names": ["World Trade Organization", "United States", "China"], "entity_types": ["organization", "location", "location"]}
{"sentence": "The CEO of the company faces allegations of fraud and embezzlement.", "entity_names": ["CEO"], "entity_types": ["person"]}
{"sentence": "The Indian Space Research Organisation successfully launches its lunar mission.", "entity_names": ["Indian Space Research Organisation"], "entity_types": ["organization"]}
{"sentence": "NASA announces partnership with Indian Space Research Organisation for Lunar Gateway project.", "entity_names": ["NASA", "Indian Space Research Organisation"], "entity_types": ["organization", "organization"]}
{"sentence": "Scientists at Indian Space Research Organisation make significant discovery on lunar surface.", "entity_names": ["Indian Space Research Organisation"], "entity_types": ["organization"]}
{"sentence": "The iconic Stonewall Inn in New York City celebrates 50 years since historic LGBTQ rights protests.", "entity_names": ["Stonewall Inn", "New York City"], "entity_types": ["location", "location"]}
{"sentence": "Chella Man discusses his role in the upcoming film with Jodie Foster.", "entity_names": ["Chella Man", "Jodie Foster"], "entity_types": ["person", "person"]}
{"sentence": "Ricky Martin set to headline charity concert for LGBTQ youth in New York City.", "entity_names": ["Ricky Martin", "New York City"], "entity_types": ["person", "location"]}
{"sentence": "The Coca-Cola Company announces partnership with Nestl\u00e9 S.A.", "entity_names": ["The Coca-Cola Company", "Nestl\u00e9 S.A."], "entity_types": ["organization", "organization"]}
{"sentence": "Nestl\u00e9 S.A. opens new production facility in Brazil.", "entity_names": ["Nestl\u00e9 S.A.", "Brazil"], "entity_types": ["organization", "location"]}
{"sentence": "The Coca-Cola Company appoints new CEO.", "entity_names": ["The Coca-Cola Company"], "entity_types": ["organization"]}
{"sentence": "Expedia Group reports increase in second quarter revenue.", "entity_names": ["Expedia Group"], "entity_types": ["organization"]}
{"sentence": "Major earthquake hits Japan, causing widespread damage.", "entity_names": ["Japan"], "entity_types": ["location"]}
{"sentence": "Expedia Group partners with major hotel chains to offer exclusive discounts.", "entity_names": ["Expedia Group"], "entity_types": ["organization"]}
{"sentence": "Officer Robert Jackson announced the launch of a new community policing initiative in downtown Los Angeles.", "entity_names": ["Officer Robert Jackson", "Los Angeles"], "entity_types": ["person", "location"]}
{"sentence": "Despite the rain, crowds gathered at the annual music festival headlined by Grammy-winning artist Alicia Keys.", "entity_names": ["Alicia Keys"], "entity_types": ["person"]}
{"sentence": "Stocks of tech giant Apple surged after the announcement of their latest iPhone model.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "The Organization of American States met to discuss human rights violations in Venezuela.", "entity_names": ["Organization of American States", "Venezuela"], "entity_types": ["organization", "location"]}
{"sentence": "National Geographic Society partners with the Organization of American States for environmental conservation efforts in the Amazon rainforest.", "entity_names": ["National Geographic Society", "Organization of American States", "Amazon rainforest"], "entity_types": ["organization", "organization", "location"]}
{"sentence": "High-ranking officials from the Organization of American States will visit Haiti to assess the current political situation.", "entity_names": ["Organization of American States", "Haiti"], "entity_types": ["organization", "location"]}
{"sentence": "The Humane Society International releases a report on animal welfare violations in factory farms.", "entity_names": ["Humane Society International"], "entity_types": ["organization"]}
{"sentence": "Local residents protest the construction of a new industrial plant in the neighborhood.", "entity_names": [], "entity_types": []}
{"sentence": "The CEO of a major tech company resigns amidst controversy over data privacy concerns.", "entity_names": ["CEO", "tech company"], "entity_types": ["organization", "organization"]}
{"sentence": "Elon Musk's SpaceX launches new satellite into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "New York City mayor announces plans for public transportation upgrade.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "WHO issues new guidelines for COVID-19 prevention.", "entity_names": ["WHO"], "entity_types": ["organization"]}
{"sentence": "President Bolsonaro visits Bras\u00edlia, Brazil for an emergency cabinet meeting.", "entity_names": ["Bolsonaro", "Bras\u00edlia", "Brazil", "cabinet"], "entity_types": ["person", "location", "location", "organization"]}
{"sentence": "Amazon rainforest fire in Brazil continues to devastate wildlife and indigenous communities.", "entity_names": ["Amazon", "Brazil"], "entity_types": ["organization", "location"]}
{"sentence": "S\u00e3o Paulo, Brazil to host international economic summit next month.", "entity_names": ["S\u00e3o Paulo", "Brazil"], "entity_types": ["location", "location"]}
{"sentence": "Microsoft acquires artificial intelligence startup for $1 billion.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}
{"sentence": "Mayor Johnson declares state of emergency in response to severe flooding.", "entity_names": ["Mayor Johnson"], "entity_types": ["person"]}
{"sentence": "New York City unveils plan to increase affordable housing options.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Meghan Markle launches new podcast series.", "entity_names": ["Meghan Markle"], "entity_types": ["person"]}
{"sentence": "Berlin experiences record-breaking heat wave.", "entity_names": ["Berlin"], "entity_types": ["location"]}
{"sentence": "Cairo signs agreement with European Union for infrastructure development.", "entity_names": ["Cairo", "European Union"], "entity_types": ["location", "organization"]}
{"sentence": "The Ganges River in India suffers from severe pollution, endangering both the environment and the local population.", "entity_names": ["The Ganges River", "India"], "entity_types": ["location", "location"]}
{"sentence": "Barack Obama delivers a powerful speech on climate change at the United Nations conference in Paris.", "entity_names": ["Barack Obama", "United Nations", "Paris"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Local organization organizes charity event to help clean up the polluted areas surrounding The Ganges River.", "entity_names": ["The Ganges River"], "entity_types": ["location"]}
{"sentence": "Alex Chavez wins reelection as Mayor of San Diego.", "entity_names": ["Alex Chavez", "San Diego"], "entity_types": ["person", "location"]}
{"sentence": "Global organization UNICEF donates millions to aid refugees in war-torn region.", "entity_names": ["UNICEF"], "entity_types": ["organization"]}
{"sentence": "Famous singer Alex Chavez announces new album release date.", "entity_names": ["Alex Chavez"], "entity_types": ["person"]}
{"sentence": "President Biden signs infrastructure bill into law", "entity_names": ["President Biden"], "entity_types": ["person"]}
{"sentence": "California wildfires force thousands to evacuate their homes", "entity_names": ["California"], "entity_types": ["location"]}
{"sentence": "Apple announces new iPhone release date", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "New York City ranks as the top destination for international tourists in the USA.", "entity_names": ["New York City", "USA"], "entity_types": ["location", "location"]}
{"sentence": "London experiences heavy rain and flooding, causing travel disruptions across England.", "entity_names": ["London", "England"], "entity_types": ["location", "location"]}
{"sentence": "Apple Inc. announces plans to open a new flagship store in New York City.", "entity_names": ["Apple Inc.", "New York City"], "entity_types": ["organization", "location"]}
{"sentence": "Local Youth Sports Organization receives $10,000 grant for new equipment.", "entity_names": ["Local Youth Sports Organization"], "entity_types": ["organization"]}
{"sentence": "Former professional athlete volunteers to coach for Youth Sports Organization.", "entity_names": ["Youth Sports Organization"], "entity_types": ["organization"]}
{"sentence": "Youth Sports Organization partners with local schools to promote physical activity among students.", "entity_names": ["Youth Sports Organization"], "entity_types": ["organization"]}
{"sentence": "Amazon announces plans to open new fulfillment center in Texas.", "entity_names": ["Amazon", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "Jeff Bezos steps down as CEO of Amazon.", "entity_names": ["Jeff Bezos", "Amazon"], "entity_types": ["person", "organization"]}
{"sentence": "Amazon invests $2 billion in affordable housing initiatives.", "entity_names": ["Amazon"], "entity_types": ["organization"]}
{"sentence": "Pope Francis visits Mexico to give blessing at the border.", "entity_names": ["Pope Francis", "Mexico"], "entity_types": ["person", "location"]}
{"sentence": "The United Nations and Pope Francis call for peace in war-torn region.", "entity_names": ["The United Nations", "Pope Francis"], "entity_types": ["organization", "person"]}
{"sentence": "Pope Francis delivers historic address to the United States Congress.", "entity_names": ["Pope Francis", "The United States Congress"], "entity_types": ["person", "organization"]}
{"sentence": "Walt Disney Parks and Resorts announces new expansion plans in Orlando.", "entity_names": ["Walt Disney Parks and Resorts", "Orlando"], "entity_types": ["organization", "location"]}
{"sentence": "The CEO of Tesla, Elon Musk, announced the company's plans to build a new Gigafactory in Texas.", "entity_names": ["Tesla", "Elon Musk", "Texas"], "entity_types": ["organization", "person", "location"]}
{"sentence": "Tokyo Olympics to ban all spectators due to rising COVID-19 cases.", "entity_names": ["Tokyo"], "entity_types": ["location"]}
{"sentence": "Former President Trump starts his own social media platform called 'Truth Social'.", "entity_names": ["Trump"], "entity_types": ["person"]}
{"sentence": "Uber Technologies Inc. announces partnership with local taxi companies to expand services in rural areas.", "entity_names": ["Uber Technologies Inc."], "entity_types": ["organization"]}
{"sentence": "The World Health Organization warns of a new strain of influenza spreading globally.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}
{"sentence": "United Nations releases report on climate change impact on small island nations.", "entity_names": ["United Nations"], "entity_types": ["organization"]}
{"sentence": "United Nations calls for urgent humanitarian aid to the Rohingya refugees in Bangladesh.", "entity_names": ["United Nations", "Bangladesh"], "entity_types": ["organization", "location"]}
{"sentence": "President Biden signs executive order to address climate change and promote renewable energy.", "entity_names": ["President Biden"], "entity_types": ["person"]}
{"sentence": "The Hershey's Company announces new partnership with local cocoa farmers in Ghana.", "entity_names": ["Hershey's", "Ghana"], "entity_types": ["organization", "location"]}
{"sentence": "Thousands of chocolate lovers gather at the annual Hershey's Chocolate Festival in Pennsylvania.", "entity_names": ["Hershey's", "Pennsylvania"], "entity_types": ["organization", "location"]}
{"sentence": "Hershey's CEO, John Smith, plans expansion of production facilities in Mexico.", "entity_names": ["Hershey's", "John Smith", "Mexico"], "entity_types": ["organization", "person", "location"]}
{"sentence": "World Health Organization warns of potential global pandemic.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}
{"sentence": "New York City Marathon organizers announce new safety measures for upcoming race.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "The Indian Film Industry is set to release a record number of movies in 2022.", "entity_names": ["Indian Film Industry"], "entity_types": ["organization"]}
{"sentence": "Sony Pictures Entertainment announces new partnership with Indian Film Industry.", "entity_names": ["Sony Pictures Entertainment", "Indian Film Industry"], "entity_types": ["organization", "organization"]}
{"sentence": "Colin Firth and Natalie Portman to star in upcoming film produced by Indian Film Industry.", "entity_names": ["Colin Firth", "Natalie Portman", "Indian Film Industry"], "entity_types": ["person", "person", "organization"]}
{"sentence": "New York City Mayor announced a new initiative to increase affordable housing options.", "entity_names": ["New York City", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "Dian Fossey's groundbreaking work with mountain gorillas continues to inspire conservation efforts in Rwanda.", "entity_names": ["Dian Fossey", "Rwanda"], "entity_types": ["person", "location"]}
{"sentence": "Apple Inc. unveils the latest version of its popular iPhone.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Seoul mayor found dead in apparent suicide.", "entity_names": ["Seoul"], "entity_types": ["location"]}
{"sentence": "Berlin-based startup secures $10 million in funding round.", "entity_names": ["Berlin"], "entity_types": ["location"]}
{"sentence": "Seoul and Berlin sign agreement to strengthen cultural exchange.", "entity_names": ["Seoul", "Berlin"], "entity_types": ["location", "location"]}
{"sentence": "The Society of Illustrators announces winners of annual competition.", "entity_names": ["Society of Illustrators"], "entity_types": ["organization"]}
{"sentence": "Renowned artist to speak at the Society of Illustrators event.", "entity_names": ["Society of Illustrators"], "entity_types": ["organization"]}
{"sentence": "Local art students honored at Society of Illustrators exhibition.", "entity_names": ["Society of Illustrators"], "entity_types": ["organization"]}
{"sentence": "Dubai International Airport ranked as the world's busiest airport for international travel in 2020.", "entity_names": ["Dubai International Airport"], "entity_types": ["location"]}
{"sentence": "United Arab Emirates to invest $10 billion in renewable energy sector.", "entity_names": ["United Arab Emirates"], "entity_types": ["location"]}
{"sentence": "Lima, Peru experiences record high temperatures during summer heatwave.", "entity_names": ["Lima", "Peru"], "entity_types": ["location", "location"]}
{"sentence": "The Council on American- Islamic Relations issues statement condemning recent hate crimes.", "entity_names": ["Council on American- Islamic Relations"], "entity_types": ["organization"]}
{"sentence": "New York City Mayor meets with representatives from the Council on American- Islamic Relations to discuss community outreach.", "entity_names": ["New York City", "Council on American- Islamic Relations"], "entity_types": ["location", "organization"]}
{"sentence": "Council on American- Islamic Relations launches initiative to promote interfaith dialogue.", "entity_names": ["Council on American- Islamic Relations"], "entity_types": ["organization"]}
{"sentence": "Mark Zuckerberg announces plans to invest in Montreal tech startups.", "entity_names": ["Mark Zuckerberg", "Montreal"], "entity_types": ["person", "location"]}
{"sentence": "Canada reports spike in COVID-19 cases in New York City travelers.", "entity_names": ["Canada", "New York City"], "entity_types": ["location", "location"]}
{"sentence": "New York City Marathon canceled for the second year in a row.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Megan Markle announces new initiative to support women's education in developing countries.", "entity_names": ["Megan Markle"], "entity_types": ["person"]}
{"sentence": "Human Rights Watch report highlights increased human rights violations in several conflict zones.", "entity_names": ["Human Rights Watch"], "entity_types": ["organization"]}
{"sentence": "The UN refugee agency calls for urgent support for displaced Rohingya in Bangladesh.", "entity_names": ["UN", "Bangladesh"], "entity_types": ["organization", "location"]}
{"sentence": "Indya Moore stars in groundbreaking new film about LGBTQ+ community.", "entity_names": ["Indya Moore"], "entity_types": ["person"]}
{"sentence": "Netflix announces new series featuring Indya Moore in lead role.", "entity_names": ["Netflix", "Indya Moore"], "entity_types": ["organization", "person"]}
{"sentence": "Indya Moore spotted at Paris Fashion Week, turning heads in stunning outfit.", "entity_names": ["Indya Moore", "Paris"], "entity_types": ["person", "location"]}
{"sentence": "Juan Rodriguez appointed as the new CEO of XYZ Corporation.", "entity_names": ["Juan Rodriguez", "XYZ Corporation"], "entity_types": ["person", "organization"]}
{"sentence": "Police arrested Juan Rodriguez in connection with the robbery at the bank.", "entity_names": ["Juan Rodriguez"], "entity_types": ["person"]}
{"sentence": "Local residents in the neighborhood of Juan Rodriguez express concern over recent crime surge.", "entity_names": ["Juan Rodriguez"], "entity_types": ["person"]}
{"sentence": "Kamala Harris visits NATO headquarters to discuss alliance's future.", "entity_names": ["Kamala Harris", "NATO"], "entity_types": ["person", "organization"]}
{"sentence": "Marie Curie's research on radioactivity led to her Nobel Prize in Physics and Chemistry.", "entity_names": ["Marie Curie"], "entity_types": ["person"]}
{"sentence": "Vladimir Putin denies allegations of election interference during press conference.", "entity_names": ["Vladimir Putin"], "entity_types": ["person"]}
{"sentence": "Kim Kardashian launches new skincare line.", "entity_names": ["Kim Kardashian"], "entity_types": ["person"]}
{"sentence": "Greta Thunberg addresses United Nations on climate change.", "entity_names": ["Greta Thunberg", "United Nations"], "entity_types": ["person", "organization"]}
{"sentence": "New York City announces plan to build affordable housing in downtown area.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "The Met Office issues severe weather warning for the south of England.", "entity_names": ["The Met Office", "England"], "entity_types": ["organization", "location"]}
{"sentence": "Weather Underground predicts record-breaking heatwave in the Midwest.", "entity_names": ["Weather Underground", "Midwest"], "entity_types": ["organization", "location"]}
{"sentence": "The Met Office partners with Weather Underground to improve global weather forecasting.", "entity_names": ["The Met Office", "Weather Underground"], "entity_types": ["organization", "organization"]}
{"sentence": "Tesla announces plans to open a new manufacturing plant in Germany.", "entity_names": ["Tesla", "Germany"], "entity_types": ["organization", "location"]}
{"sentence": "Apple CEO Tim Cook unveils new iPhone lineup at annual event.", "entity_names": ["Apple", "Tim Cook"], "entity_types": ["organization", "person"]}
{"sentence": "United Nations report warns of catastrophic consequences of climate change.", "entity_names": ["United Nations"], "entity_types": ["organization"]}
{"sentence": "Tesla to unveil new electric vehicle with longer battery life at upcoming auto show.", "entity_names": ["Tesla"], "entity_types": ["organization"]}
{"sentence": "President Biden appoints new chair of Federal Reserve.", "entity_names": ["President Biden", "Federal Reserve"], "entity_types": ["person", "organization"]}
{"sentence": "Tensions rise between Russia and Ukraine over border dispute.", "entity_names": ["Russia", "Ukraine"], "entity_types": ["location", "location"]}
{"sentence": "Jennifer Martinez appointed as the new CEO of XYZ Corporation.", "entity_names": ["Jennifer Martinez", "XYZ Corporation"], "entity_types": ["person", "organization"]}
{"sentence": "A massive wildfire continues to burn in the forests near Lake Tahoe, forcing thousands to evacuate.", "entity_names": ["Lake Tahoe"], "entity_types": ["location"]}
{"sentence": "New York City Mayor announces plans to increase funding for public transportation.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "New York City Mayor announces plan to increase affordable housing.", "entity_names": ["New York City", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "Apple Inc. releases new iPhone with advanced camera technology.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "United Nations calls for immediate ceasefire in war-torn region.", "entity_names": ["United Nations"], "entity_types": ["organization"]}
{"sentence": "The American Association of School Administrators announces new funding for educational programs.", "entity_names": ["American Association of School Administrators"], "entity_types": ["organization"]}
{"sentence": "California governor meets with representatives from the American Association of School Administrators to discuss education reform.", "entity_names": ["California", "American Association of School Administrators"], "entity_types": ["location", "organization"]}
{"sentence": "Members of the American Association of School Administrators gather for annual conference in New York City.", "entity_names": ["American Association of School Administrators", "New York City"], "entity_types": ["organization", "location"]}
{"sentence": "Paris to implement new traffic regulations to reduce air pollution.", "entity_names": ["Paris"], "entity_types": ["location"]}
{"sentence": "France announces plans to increase renewable energy production by 2030.", "entity_names": ["France"], "entity_types": ["location"]}
{"sentence": "World Health Organization warns of potential vaccine shortages in developing countries.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}
{"sentence": "Serena Williams wins her 7th grand slam title.", "entity_names": ["Serena Williams"], "entity_types": ["person"]}
{"sentence": "Greta Thunberg delivers passionate speech at climate change summit.", "entity_names": ["Greta Thunberg"], "entity_types": ["person"]}
{"sentence": "United Nations declares climate emergency.", "entity_names": ["United Nations"], "entity_types": ["organization"]}
{"sentence": "South Africa announces plan to enforce stricter regulations around Kruger National Park.", "entity_names": ["South Africa", "Kruger National Park"], "entity_types": ["location", "location"]}
{"sentence": "Renowned wildlife conservationist from Kruger National Park to speak at South Africa's upcoming environmental summit.", "entity_names": ["Kruger National Park", "South Africa"], "entity_types": ["location", "location"]}
{"sentence": "Human Rights Watch criticizes Chinese government's treatment of Uighur Muslims.", "entity_names": ["Human Rights Watch", "Chinese government"], "entity_types": ["organization", "organization"]}
{"sentence": "New York City Mayor announces plan to invest in affordable housing.", "entity_names": ["New York City", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "Protests erupt in Venezuela after controversial election results.", "entity_names": ["Venezuela"], "entity_types": ["location"]}
{"sentence": "Donald Trump delivers a speech on immigration reform at the rally.", "entity_names": ["Donald Trump"], "entity_types": ["person"]}
{"sentence": "The Red Cross launches a fundraising campaign to support relief efforts in the region.", "entity_names": ["Red Cross"], "entity_types": ["organization"]}
{"sentence": "IBM CEO Ginni Rometty steps down from role.", "entity_names": ["IBM", "Ginni Rometty"], "entity_types": ["organization", "person"]}
{"sentence": "Tesla opens new manufacturing plant in Shanghai, China.", "entity_names": ["Tesla", "Shanghai", "China"], "entity_types": ["organization", "location", "location"]}
{"sentence": "President Biden announces new infrastructure plan in speech to Congress.", "entity_names": ["Biden"], "entity_types": ["person"]}
{"sentence": "The National Coalition Against Domestic Violence reports a 20% increase in calls to their hotline during the pandemic.", "entity_names": ["National Coalition Against Domestic Violence"], "entity_types": ["organization"]}
{"sentence": "Alexandria Ocasio-Cortez introduces Green New Deal legislation to combat climate change.", "entity_names": ["Alexandria Ocasio-Cortez"], "entity_types": ["person"]}
{"sentence": "The annual conference hosted by the National Coalition Against Domestic Violence draws attention to the impact of domestic violence on marginalized communities.", "entity_names": ["National Coalition Against Domestic Violence"], "entity_types": ["organization"]}
{"sentence": "The Royal Shakespeare Company announces new production of Romeo and Juliet.", "entity_names": ["The Royal Shakespeare Company"], "entity_types": ["organization"]}
{"sentence": "Kamala Harris visits Rome to meet with Italian leaders.", "entity_names": ["Kamala Harris", "Rome"], "entity_types": ["person", "location"]}
{"sentence": "Rome celebrates 100th anniversary of historic landmark restoration.", "entity_names": ["Rome"], "entity_types": ["location"]}
{"sentence": "Geo Neptune, a Native American artist, creates stunning sculptures from soapstone.", "entity_names": ["Geo Neptune"], "entity_types": ["person"]}
{"sentence": "The Environmental Protection Agency announces new regulations to reduce carbon emissions.", "entity_names": ["Environmental Protection Agency"], "entity_types": ["organization"]}
{"sentence": "The city of Tokyo prepares for the upcoming Summer Olympics with extensive renovations to its sports facilities.", "entity_names": ["Tokyo", "Summer Olympics"], "entity_types": ["location", "organization"]}
{"sentence": "International Finance Corporation announces $1 billion investment in renewable energy projects.", "entity_names": ["International Finance Corporation"], "entity_types": ["organization"]}
{"sentence": "Google CEO Sundar Pichai testifies before Congress on antitrust concerns.", "entity_names": ["Google", "Sundar Pichai"], "entity_types": ["organization", "person"]}
{"sentence": "Stocks surge as International Finance Corporation predicts global economic recovery.", "entity_names": ["International Finance Corporation"], "entity_types": ["organization"]}
{"sentence": "President Trump signs executive order to increase tariffs on steel and aluminum imports.", "entity_names": ["Trump"], "entity_types": ["person"]}
{"sentence": "Severe weather warnings issued for southern California ahead of incoming storm.", "entity_names": ["southern California"], "entity_types": ["location"]}
{"sentence": "Apple Inc. announces launch of new iPhone model with advanced features.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Cristiano Ronaldo scores hat-trick in win over rival team.", "entity_names": ["Cristiano Ronaldo"], "entity_types": ["person"]}
{"sentence": "Dallas Cowboys sign new coach amidst team's losing streak.", "entity_names": ["Dallas Cowboys"], "entity_types": ["organization"]}
{"sentence": "United States Olympic & Paralympic Committee announces new guidelines for athlete training programs.", "entity_names": ["United States Olympic & Paralympic Committee"], "entity_types": ["organization"]}
{"sentence": "Machu Picchu reopens to tourists after pandemic closure.", "entity_names": ["Machu Picchu"], "entity_types": ["location"]}
{"sentence": "Kim Kardashian visits Machu Picchu during South American trip.", "entity_names": ["Kim Kardashian", "Machu Picchu"], "entity_types": ["person", "location"]}
{"sentence": "Tesla's CEO Elon Musk announces plans to build new Gigafactory in Texas.", "entity_names": ["Tesla", "Elon Musk", "Texas"], "entity_types": ["organization", "person", "location"]}
{"sentence": "President Biden meets with NATO leaders to discuss global security issues.", "entity_names": ["President Biden", "NATO"], "entity_types": ["person", "organization"]}
{"sentence": "New York City imposes new restrictions as COVID-19 cases surge.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "President Biden delivers State of the Union address to Congress.", "entity_names": ["President Biden", "Congress"], "entity_types": ["person", "organization"]}
{"sentence": "Police arrest suspect in downtown robbery.", "entity_names": ["Police"], "entity_types": ["organization"]}
{"sentence": "California declares drought emergency in Central Valley.", "entity_names": ["California", "Central Valley"], "entity_types": ["location", "location"]}
{"sentence": "Wallace County Public Library holds virtual story time event for children.", "entity_names": ["Wallace County Public Library"], "entity_types": ["organization"]}
{"sentence": "Local resident donates rare historical documents to the Wallace County Public Library.", "entity_names": ["Wallace County Public Library"], "entity_types": ["organization"]}
{"sentence": "New York Times best-selling author to give book signing at Wallace County Public Library.", "entity_names": ["New York Times", "Wallace County Public Library"], "entity_types": ["organization", "organization"]}
{"sentence": "Tokyo, Japan is set to host the upcoming Olympic Games.", "entity_names": ["Tokyo", "Japan"], "entity_types": ["location", "location"]}
{"sentence": "Tokyo, Japan - The Japanese government has approved a new stimulus package to boost the economy.", "entity_names": ["Tokyo", "Japan"], "entity_types": ["location", "location"]}
{"sentence": "A new documentary on the life of Ernest Hemingway is set to premiere next month.", "entity_names": ["Ernest Hemingway"], "entity_types": ["person"]}
{"sentence": "Aung San Suu Kyi wins Nobel Peace Prize.", "entity_names": ["Aung San Suu Kyi"], "entity_types": ["person"]}
{"sentence": "The meeting between Aung San Suu Kyi and the United Nations Secretary-General focused on human rights issues in Myanmar.", "entity_names": ["Aung San Suu Kyi", "United Nations", "Myanmar"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Aung San Suu Kyi urges international community to take action against human rights violations in Myanmar.", "entity_names": ["Aung San Suu Kyi", "Myanmar"], "entity_types": ["person", "location"]}
{"sentence": "Elon Musk's SpaceX successfully launches communication satellite.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "New York City announces plan to invest in renewable energy sources.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Microsoft unveils new line of Surface laptops.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}
{"sentence": "Tokyo reports surge in COVID-19 cases.", "entity_names": ["Tokyo"], "entity_types": ["location"]}
{"sentence": "Celebrity chef Rachael Ray to open new restaurant in New York City.", "entity_names": ["Rachael Ray", "New York City"], "entity_types": ["person", "location"]}
{"sentence": "Tokyo Olympics organizers announce new guidelines for spectators.", "entity_names": ["Tokyo", "Olympics"], "entity_types": ["location", "organization"]}
{"sentence": "Vladimir Putin signs new trade agreement with China.", "entity_names": ["Vladimir Putin", "China"], "entity_types": ["person", "location"]}
{"sentence": "George Floyd Memorial Foundation awarded grant for police reform efforts.", "entity_names": ["George Floyd", "Memorial Foundation"], "entity_types": ["person", "organization"]}
{"sentence": "Protests erupt after court decision in George Floyd case.", "entity_names": ["George Floyd"], "entity_types": ["person"]}
{"sentence": "Seoul, South Korea, reports record high temperatures for the summer.", "entity_names": ["Seoul", "South Korea"], "entity_types": ["location", "location"]}
{"sentence": "Renowned chef opens new restaurant in Seoul.", "entity_names": ["Seoul"], "entity_types": ["location"]}
{"sentence": "South Korea approves new trade deal with United States.", "entity_names": ["South Korea", "United States"], "entity_types": ["location", "location"]}
{"sentence": "John Tavares signs a 7-year contract extension with the Toronto Maple Leafs.", "entity_names": ["John Tavares", "Toronto Maple Leafs"], "entity_types": ["person", "organization"]}
{"sentence": "Tavares scores a hat-trick in the game against the Montreal Canadiens.", "entity_names": ["Tavares", "Montreal Canadiens"], "entity_types": ["person", "organization"]}
{"sentence": "The Tavares trade rumors have sparked speculation among hockey fans.", "entity_names": ["Tavares"], "entity_types": ["person"]}
{"sentence": "Rachael Ray's new cookbook tops the bestseller list.", "entity_names": ["Rachael Ray"], "entity_types": ["person"]}
{"sentence": "Seoul experiences record-breaking heatwave.", "entity_names": ["Seoul"], "entity_types": ["location"]}
{"sentence": "South Korea's Ministry of Health announces new COVID-19 guidelines.", "entity_names": ["South Korea", "Ministry of Health"], "entity_types": ["location", "organization"]}
{"sentence": "President Biden addresses the nation on new infrastructure plan.", "entity_names": ["President Biden"], "entity_types": ["person"]}
{"sentence": "San Francisco Giants defeat Los Angeles Dodgers in extra innings.", "entity_names": ["San Francisco Giants", "Los Angeles Dodgers"], "entity_types": ["organization", "organization"]}
{"sentence": "Hurricane Katrina devastates New Orleans, Louisiana.", "entity_names": ["New Orleans", "Louisiana"], "entity_types": ["location", "location"]}
{"sentence": "The Organization of American States calls for emergency meeting to address political crisis in Bolivia.", "entity_names": ["Organization of American States", "Bolivia"], "entity_types": ["organization", "location"]}
{"sentence": "Federal Reserve System announces interest rate cut to stimulate economy.", "entity_names": ["Federal Reserve System"], "entity_types": ["organization"]}
{"sentence": "Security Council calls for peace talks in war-torn region.", "entity_names": ["Security Council"], "entity_types": ["organization"]}
{"sentence": "The United Nations issues a new report on climate change.", "entity_names": ["United Nations"], "entity_types": ["organization"]}
{"sentence": "President Biden to address climate change in speech from Washington, D.C.", "entity_names": ["President Biden", "Washington, D.C."], "entity_types": ["person", "location"]}
{"sentence": "Amazon announces plans to open new headquarters in Arlington, Virginia, near Washington, D.C.", "entity_names": ["Amazon", "Arlington, Virginia", "Washington, D.C."], "entity_types": ["organization", "location", "location"]}
{"sentence": "Protesters gather outside the White House in Washington, D.C. to demand police reform.", "entity_names": ["White House", "Washington, D.C."], "entity_types": ["location", "location"]}
{"sentence": "The Wildlife Conservation Society reported a successful rescue and rehabilitation of an injured rhinoceros in Kruger National Park, South Africa.", "entity_names": ["Wildlife Conservation Society", "Kruger National Park", "South Africa"], "entity_types": ["organization", "location", "location"]}
{"sentence": "New study by the Wildlife Rescue and Rehabilitation organization shows an increase in poaching activities in Kruger National Park, South Africa.", "entity_names": ["Wildlife Rescue and Rehabilitation", "Kruger National Park", "South Africa"], "entity_types": ["organization", "location", "location"]}
{"sentence": "A team of researchers from the Wildlife Conservation Society discovered a new species of bird in the forests of Kruger National Park, South Africa.", "entity_names": ["Wildlife Conservation Society", "Kruger National Park", "South Africa"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Stockholm experiencing record-breaking temperatures reaching 35 degrees Celsius.", "entity_names": ["Stockholm"], "entity_types": ["location"]}
{"sentence": "Sweden announces plans to increase funding for renewable energy projects.", "entity_names": ["Sweden"], "entity_types": ["location"]}
{"sentence": "Student Council President Samantha Rodriguez of Trinity College Dublin wins prestigious leadership award.", "entity_names": ["Student Council President Samantha Rodriguez", "Trinity College Dublin"], "entity_types": ["person", "organization"]}
{"sentence": "Pop icon Selena Gomez to launch her new beauty line next week.", "entity_names": ["Selena Gomez"], "entity_types": ["person"]}
{"sentence": "Selena Gomez spotted filming her new music video in Los Angeles.", "entity_names": ["Selena Gomez", "Los Angeles"], "entity_types": ["person", "location"]}
{"sentence": "Fans wait in line for hours to meet Selena Gomez at her book signing event.", "entity_names": ["Selena Gomez"], "entity_types": ["person"]}
{"sentence": "China's President Xi Jinping meets with South Korean leader for trade talks.", "entity_names": ["China", "Xi Jinping", "South Korean"], "entity_types": ["location", "person", "organization"]}
{"sentence": "United Nations calls for ceasefire in Yemen conflict, as Xi Jinping urges diplomatic solution.", "entity_names": ["United Nations", "Yemen", "Xi Jinping"], "entity_types": ["organization", "location", "person"]}
{"sentence": "European Union leaders discuss trade deal with China during summit attended by Xi Jinping.", "entity_names": ["European Union", "China", "Xi Jinping"], "entity_types": ["organization", "location", "person"]}
{"sentence": "The Directors Guild of America has announced the winners of its annual awards ceremony in Los Angeles.", "entity_names": ["Directors Guild of America", "Los Angeles"], "entity_types": ["organization", "location"]}
{"sentence": "The film festival in Venice attracts top talent and cinephiles from around the world.", "entity_names": ["Venice"], "entity_types": ["location"]}
{"sentence": "Internationally acclaimed director from South Korea to present new project at the Directors Guild of America event in New York.", "entity_names": ["South Korea", "Directors Guild of America", "New York"], "entity_types": ["location", "organization", "location"]}
{"sentence": "Scarlett Johansson to star in new film based on the life of a famous scientist.", "entity_names": ["Scarlett Johansson"], "entity_types": ["person"]}
{"sentence": "Thousands gather at the Sydney Opera House to celebrate Australia Day.", "entity_names": ["Sydney Opera House"], "entity_types": ["location"]}
{"sentence": "Thousands gather at Venice Beach for annual music festival.", "entity_names": ["Venice Beach"], "entity_types": ["location"]}
{"sentence": "Visitors admire Botticelli's 'The Birth of Venus' at the Uffizi Gallery in Florence.", "entity_names": ["Botticelli", "Uffizi Gallery", "Florence"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Local artist unveils new mural at Venice Beach boardwalk.", "entity_names": ["Venice Beach"], "entity_types": ["location"]}
{"sentence": "Tesla announces plan to open new manufacturing plant in Germany.", "entity_names": ["Tesla", "Germany"], "entity_types": ["organization", "location"]}
{"sentence": "The CEO of Google, Sundar Pichai, will deliver the keynote speech at the upcoming tech conference.", "entity_names": ["Google", "Sundar Pichai"], "entity_types": ["organization", "person"]}
{"sentence": "Tokyo Olympics postponed until 2021 due to COVID-19 pandemic.", "entity_names": ["Tokyo"], "entity_types": ["location"]}
{"sentence": "NASA astronaut returns to International Space Station after 6 months.", "entity_names": ["NASA"], "entity_types": ["organization"]}
{"sentence": "European Space Agency collaborates with International Space Station for new research project.", "entity_names": ["European Space Agency", "International Space Station"], "entity_types": ["organization", "location"]}
{"sentence": "Russian cosmonauts conduct spacewalk outside International Space Station.", "entity_names": ["International Space Station"], "entity_types": ["location"]}
{"sentence": "Joe Biden signs executive order to address climate change.", "entity_names": ["Joe Biden"], "entity_types": ["person"]}
{"sentence": "Firefighters from across the country assist in battling wildfires in California.", "entity_names": ["California"], "entity_types": ["location"]}
{"sentence": "Apple Inc. announces plans to release new iPhone model next month.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Former Microsoft executive Terry Myerson joins Airbnb as a strategic advisor.", "entity_names": ["Terry Myerson", "Airbnb"], "entity_types": ["person", "organization"]}
{"sentence": "Tokyo, Japan sets new record for highest number of foreign tourists in a single year.", "entity_names": ["Tokyo", "Japan"], "entity_types": ["location", "location"]}
{"sentence": "Airbnb faces backlash in Tokyo, Japan over impact on local housing market.", "entity_names": ["Airbnb", "Tokyo", "Japan"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Tesla CEO Elon Musk announces plans to build new Gigafactory in Texas.", "entity_names": ["Tesla", "Elon Musk", "Texas"], "entity_types": ["organization", "person", "location"]}
{"sentence": "United Nations releases report on climate change impact in Pacific Islands.", "entity_names": ["United Nations", "Pacific Islands"], "entity_types": ["organization", "location"]}
{"sentence": "Apple Inc. to launch new product line next month.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Tom Cruise to star in new action film.", "entity_names": ["Tom Cruise"], "entity_types": ["person"]}
{"sentence": "Gal Gadot makes appearance at Sydney film festival.", "entity_names": ["Gal Gadot", "Sydney"], "entity_types": ["person", "location"]}
{"sentence": "Simone Biles wins gold in gymnastics at Tokyo Olympics.", "entity_names": ["Simone Biles", "Tokyo"], "entity_types": ["person", "location"]}
{"sentence": "United Nations reports record number of displaced persons in 2021.", "entity_names": ["United Nations"], "entity_types": ["organization"]}
{"sentence": "Red Cross volunteers provide aid to hurricane victims in devastated areas.", "entity_names": ["Red Cross"], "entity_types": ["organization"]}
{"sentence": "IPCC report warns of irreversible damage to the planet if drastic action is not taken soon.", "entity_names": ["IPCC"], "entity_types": ["organization"]}
{"sentence": "Experts from the IPCC suggest that urgent measures are needed to combat the effects of climate change.", "entity_names": ["IPCC"], "entity_types": ["organization"]}
{"sentence": "The latest IPCC findings indicate that the global temperature is on track to exceed the 1.5\u00b0C threshold within the next decade.", "entity_names": ["IPCC"], "entity_types": ["organization"]}
{"sentence": "Formula One team Ferrari N.V. wins the latest race with a stunning performance.", "entity_names": ["Ferrari N.V."], "entity_types": ["organization"]}
{"sentence": "The famous Italian car manufacturer Ferrari N.V. launches a new line of luxury vehicles.", "entity_names": ["Ferrari N.V."], "entity_types": ["organization"]}
{"sentence": "Sebastian Vettel signs a new contract with Ferrari N.V. to continue racing for the team.", "entity_names": ["Sebastian Vettel", "Ferrari N.V."], "entity_types": ["person", "organization"]}
{"sentence": "Tech giant Apple announces new iPhone release.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "New York City Mayor announces plan to improve subway system.", "entity_names": ["New York City", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "WHO warns of potential outbreak in South American countries.", "entity_names": ["WHO", "South American countries"], "entity_types": ["organization", "location"]}
{"sentence": "Istanbul, Turkey is set to host the next United Nations Climate Change Conference.", "entity_names": ["Istanbul", "Turkey", "United Nations"], "entity_types": ["location", "location", "organization"]}
{"sentence": "The CEO of Apple Inc. announces a new product launch event.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Flooding in southern India displaces thousands of people.", "entity_names": ["India"], "entity_types": ["location"]}
{"sentence": "Massive protest march scheduled to mark the anniversary of the Stonewall Riots.", "entity_names": ["Stonewall"], "entity_types": ["location"]}
{"sentence": "Cape Town reports the highest number of COVID-19 cases in South Africa.", "entity_names": ["Cape Town", "South Africa"], "entity_types": ["location", "location"]}
{"sentence": "World Health Organization urges increased vaccine distribution in South Africa to combat new COVID-19 variant.", "entity_names": ["World Health Organization", "South Africa"], "entity_types": ["organization", "location"]}
{"sentence": "Renowned physicist Stephen Hawking passes away at age 76.", "entity_names": ["Stephen Hawking"], "entity_types": ["person"]}
{"sentence": "Hawking's groundbreaking theories continue to influence the field of theoretical physics.", "entity_names": ["Hawking"], "entity_types": ["person"]}
{"sentence": "The Hawking Foundation pledges $1 million in support of scientific research.", "entity_names": ["Hawking Foundation"], "entity_types": ["organization"]}
{"sentence": "Golden Globes nominees announced for best picture and director categories.", "entity_names": ["Golden Globes"], "entity_types": ["organization"]}
{"sentence": "Hollywood celebrities dazzle at the Golden Globes red carpet event.", "entity_names": ["Golden Globes"], "entity_types": ["organization"]}
{"sentence": "Golden Globes ceremony to be held at the Beverly Hilton in Los Angeles.", "entity_names": ["Golden Globes", "Beverly Hilton", "Los Angeles"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Pope Francis addresses crowd at Vatican City.", "entity_names": ["Pope Francis", "Vatican City"], "entity_types": ["person", "location"]}
{"sentence": "European Union imposes sanctions on Russia for cyber attacks.", "entity_names": ["European Union", "Russia"], "entity_types": ["organization", "location"]}
{"sentence": "Apple Inc. unveils new iPhone model.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Samantha Roberts appointed as new CEO of XYZ Corporation.", "entity_names": ["Samantha Roberts", "XYZ Corporation"], "entity_types": ["person", "organization"]}
{"sentence": "Firefighters battle massive blaze in downtown Los Angeles.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}
{"sentence": "High school students protest against new school dress code.", "entity_names": [], "entity_types": []}
{"sentence": "Blue Origin announces successful test flight of New Shepard rocket.", "entity_names": ["Blue Origin"], "entity_types": ["organization"]}
{"sentence": "Elon Musk congratulates Blue Origin on their latest achievement.", "entity_names": ["Elon Musk", "Blue Origin"], "entity_types": ["person", "organization"]}
{"sentence": "Blue Origin plans to build new rocket manufacturing facility in Florida.", "entity_names": ["Blue Origin", "Florida"], "entity_types": ["organization", "location"]}
{"sentence": "Elon Musk's SpaceX launches 60 new Starlink satellites into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "New York City to invest $100 million in new public transportation initiatives.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Apple announces plans to open new research and development center in Beijing.", "entity_names": ["Apple", "Beijing"], "entity_types": ["organization", "location"]}
{"sentence": "Flooding in New Orleans prompts emergency evacuations as Hurricane Marco approaches the Gulf Coast.", "entity_names": ["New Orleans"], "entity_types": ["location"]}
{"sentence": "Apple unveils new iPhone with advanced facial recognition technology and improved camera features.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "Armani announces collaboration with famous designer for new fragrance line.", "entity_names": ["Armani"], "entity_types": ["organization"]}
{"sentence": "Milan Fashion Week to feature exclusive Armani runway show.", "entity_names": ["Milan", "Armani"], "entity_types": ["location", "organization"]}
{"sentence": "CEO of Armani steps down amid restructuring of company.", "entity_names": ["Armani"], "entity_types": ["organization"]}
{"sentence": "The Tokyo Auto Salon showcases the latest innovations in automotive technology.", "entity_names": ["Tokyo Auto Salon"], "entity_types": ["organization"]}
{"sentence": "Attendees from all over the world flock to Tokyo to attend the famous Auto Salon.", "entity_names": ["Tokyo", "Auto Salon"], "entity_types": ["location", "organization"]}
{"sentence": "The Tokyo Auto Salon, known for its extravagant displays, attracts top car manufacturers and enthusiasts alike.", "entity_names": ["Tokyo Auto Salon"], "entity_types": ["organization"]}
{"sentence": "The United States Marshals Service captures notorious fugitive in daring raid.", "entity_names": ["United States Marshals Service"], "entity_types": ["organization"]}
{"sentence": "The International Criminal Court announces investigation into alleged war crimes in conflict-torn region.", "entity_names": ["International Criminal Court"], "entity_types": ["organization"]}
{"sentence": "National Crime Agency warns of uptick in cybercrime during holiday season.", "entity_names": ["National Crime Agency"], "entity_types": ["organization"]}
{"sentence": "NATO leaders meet in Toronto for summit.", "entity_names": ["NATO", "Toronto"], "entity_types": ["organization", "location"]}
{"sentence": "Canada's Prime Minister meets with Queen Elizabeth II.", "entity_names": ["Canada", "Queen Elizabeth II"], "entity_types": ["location", "person"]}
{"sentence": "Toronto Raptors win NBA championship.", "entity_names": ["Toronto"], "entity_types": ["location"]}
{"sentence": "Renowned author James Baldwin was posthumously inducted into The American Academy of Arts and Letters.", "entity_names": ["James Baldwin", "The American Academy of Arts and Letters"], "entity_types": ["person", "organization"]}
{"sentence": "The American Academy of Arts and Letters announced the opening of a new exhibition featuring artwork from local artists.", "entity_names": ["The American Academy of Arts and Letters"], "entity_types": ["organization"]}
{"sentence": "James Baldwin's iconic novel 'Go Tell It on the Mountain' will be adapted into a stage production next year.", "entity_names": ["James Baldwin"], "entity_types": ["person"]}
{"sentence": "Elon Musk's SpaceX launches 60 Starlink satellites into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "The United Nations Security Council condemns human rights violations in Syria.", "entity_names": ["United Nations Security Council", "Syria"], "entity_types": ["organization", "location"]}
{"sentence": "The Securities and Exchange Commission announced new regulations for cryptocurrency trading.", "entity_names": ["Securities and Exchange Commission"], "entity_types": ["organization"]}
{"sentence": "Tech giant Apple faces investigation by the Securities and Exchange Commission over antitrust concerns.", "entity_names": ["Apple", "Securities and Exchange Commission"], "entity_types": ["organization", "organization"]}
{"sentence": "Investors are closely watching for any updates from the Securities and Exchange Commission regarding the new merger deal.", "entity_names": ["Securities and Exchange Commission"], "entity_types": ["organization"]}
{"sentence": "Getty Foundation awards $2 million grant to support conservation of ancient ruins in Greece.", "entity_names": ["Getty Foundation", "Greece"], "entity_types": ["organization", "location"]}
{"sentence": "Renowned architect unveils design for new museum funded by Getty Foundation.", "entity_names": ["Getty Foundation"], "entity_types": ["organization"]}
{"sentence": "Getty Foundation partners with local university to launch art preservation program.", "entity_names": ["Getty Foundation"], "entity_types": ["organization"]}
{"sentence": "Elton John to perform at the Staples Center in Los Angeles next week.", "entity_names": ["Elton John", "Los Angeles"], "entity_types": ["person", "location"]}
{"sentence": "California governor signs new bill to protect ocean wildlife.", "entity_names": ["California"], "entity_types": ["location"]}
{"sentence": "Los Angeles-based tech startup receives $10 million in funding.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}
{"sentence": "The CEO of Apple, Tim Cook, announces the launch of a new iPad.", "entity_names": ["Apple", "Tim Cook"], "entity_types": ["organization", "person"]}
{"sentence": "The devastating hurricane hit the coast of Florida, causing widespread destruction.", "entity_names": ["Florida"], "entity_types": ["location"]}
{"sentence": "The United Nations issues a statement condemning the recent terrorist attack in Europe.", "entity_names": ["United Nations", "Europe"], "entity_types": ["organization", "location"]}
{"sentence": "The United States Chamber of Commerce reports record high levels of business confidence.", "entity_names": ["United States Chamber of Commerce"], "entity_types": ["organization"]}
{"sentence": "International Finance Corporation partners with local banks to promote sustainable development in emerging markets.", "entity_names": ["International Finance Corporation"], "entity_types": ["organization"]}
{"sentence": "The United States Chamber of Commerce hosts annual summit in Washington, D.C.", "entity_names": ["United States Chamber of Commerce", "Washington, D.C."], "entity_types": ["organization", "location"]}
{"sentence": "The National Football League has announced a new policy regarding player conduct during the national anthem.", "entity_names": ["National Football League"], "entity_types": ["organization"]}
{"sentence": "Tom Brady, quarterback for the New England Patriots, is considering retirement after this season.", "entity_names": ["Tom Brady", "New England Patriots"], "entity_types": ["person", "organization"]}
{"sentence": "A new stadium is being built in Los Angeles for the National Football League team, the Rams.", "entity_names": ["Los Angeles", "National Football League", "Rams"], "entity_types": ["location", "organization", "organization"]}
{"sentence": "Yotam Ottolenghi releases new cookbook.", "entity_names": ["Yotam Ottolenghi"], "entity_types": ["person"]}
{"sentence": "Israeli chef Yotam Ottolenghi opens new restaurant in London.", "entity_names": ["Yotam Ottolenghi", "London"], "entity_types": ["person", "location"]}
{"sentence": "Yotam Ottolenghi's cooking show to air on Food Network.", "entity_names": ["Yotam Ottolenghi", "Food Network"], "entity_types": ["person", "organization"]}
{"sentence": "Mondelez International to acquire chocolate company in France.", "entity_names": ["Mondelez International", "France"], "entity_types": ["organization", "location"]}
{"sentence": "Paris prepares for annual fashion week event.", "entity_names": ["Paris"], "entity_types": ["location"]}
{"sentence": "Milan, Italy - Former prime minister to speak at economic forum.", "entity_names": ["Milan", "Italy"], "entity_types": ["location", "location"]}
{"sentence": "Olivia Morales appointed as the new CEO of XYZ Corporation.", "entity_names": ["Olivia Morales", "XYZ Corporation"], "entity_types": ["person", "organization"]}
{"sentence": "The annual conference will take place in Paris, France, with keynote speaker Olivia Morales.", "entity_names": ["Paris", "France", "Olivia Morales"], "entity_types": ["location", "location", "person"]}
{"sentence": "Olivia Morales, renowned environmental activist, awarded the Nobel Peace Prize.", "entity_names": ["Olivia Morales", "Nobel Peace Prize"], "entity_types": ["person", "organization"]}
{"sentence": "Princeton University receives $10 million donation for new research center.", "entity_names": ["Princeton University"], "entity_types": ["organization"]}
{"sentence": "National Education Association Foundation launches new initiative to improve teacher retention.", "entity_names": ["National Education Association Foundation"], "entity_types": ["organization"]}
{"sentence": "The partnership between Princeton University and National Education Association Foundation aims to address education disparities.", "entity_names": ["Princeton University", "National Education Association Foundation"], "entity_types": ["organization", "organization"]}
{"sentence": "Alexandria Ocasio-Cortez criticizes new immigration policy proposed by American Civil Liberties Union.", "entity_names": ["Alexandria Ocasio-Cortez", "American Civil Liberties Union"], "entity_types": ["person", "organization"]}
{"sentence": "American Civil Liberties Union files lawsuit against controversial new voting restrictions in Georgia.", "entity_names": ["American Civil Liberties Union", "Georgia"], "entity_types": ["organization", "location"]}
{"sentence": "Alexandria Ocasio-Cortez delivers passionate speech on healthcare reform in Congress.", "entity_names": ["Alexandria Ocasio-Cortez", "Congress"], "entity_types": ["person", "organization"]}
{"sentence": "The Royal Academy of Arts in London showcases works by renowned Brazilian artists from Rio de Janeiro.", "entity_names": ["The Royal Academy of Arts", "Rio de Janeiro"], "entity_types": ["organization", "location"]}
{"sentence": "Top chef from Rio de Janeiro opens new restaurant in New York City's trendy SoHo district.", "entity_names": ["Rio de Janeiro", "New York City"], "entity_types": ["location", "location"]}
{"sentence": "An exhibition featuring the works of famous Colombian painter opens at The Royal Academy of Arts in London.", "entity_names": ["The Royal Academy of Arts"], "entity_types": ["organization"]}
{"sentence": "Barack Obama delivers keynote speech at climate change conference.", "entity_names": ["Barack Obama"], "entity_types": ["person"]}
{"sentence": "Norwegian Cruise Line Holdings reports record-breaking profits for the third quarter.", "entity_names": ["Norwegian Cruise Line Holdings"], "entity_types": ["organization"]}
{"sentence": "Barack Obama meets with leaders of G7 countries to discuss global economic recovery.", "entity_names": ["Barack Obama"], "entity_types": ["person"]}
{"sentence": "Oracle announces partnership with Sony Corporation to develop new cloud computing solutions.", "entity_names": ["Oracle", "Sony Corporation"], "entity_types": ["organization", "organization"]}
{"sentence": "Sony Corporation unveils plans for new flagship store in New York City.", "entity_names": ["Sony Corporation"], "entity_types": ["organization"]}
{"sentence": "Oracle CEO to step down after 10 years at the helm.", "entity_names": ["Oracle"], "entity_types": ["organization"]}
{"sentence": "Tesla's CEO Elon Musk unveils new electric car model at annual conference.", "entity_names": ["Tesla", "Elon Musk"], "entity_types": ["organization", "person"]}
{"sentence": "Tropical storm hits Florida, causing widespread flooding and power outages.", "entity_names": ["Florida"], "entity_types": ["location"]}
{"sentence": "Apple announces partnership with major music label for exclusive album releases.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "USA imposes sanctions on foreign leaders for human rights violations.", "entity_names": ["USA"], "entity_types": ["location"]}
{"sentence": "South Korea's Seoul ranked as one of the most innovative cities in the world.", "entity_names": ["South Korea", "Seoul"], "entity_types": ["location", "location"]}
{"sentence": "Planned Parenthood faces opposition in new legislation.", "entity_names": ["Planned Parenthood"], "entity_types": ["organization"]}
{"sentence": "Funding for Planned Parenthood at risk in budget negotiations.", "entity_names": ["Planned Parenthood"], "entity_types": ["organization"]}
{"sentence": "CEO of Planned Parenthood responds to allegations of misconduct.", "entity_names": ["CEO", "Planned Parenthood"], "entity_types": ["person", "organization"]}
{"sentence": "The Sydney Opera House in Australia is set to host a retrospective exhibition of the works of Claude Monet and Edvard Munch.", "entity_names": ["Sydney Opera House", "Australia", "Claude Monet", "Edvard Munch"], "entity_types": ["location", "location", "person", "person"]}
{"sentence": "Australian artist creates a stunning replica of the Sydney Opera House using recycled materials.", "entity_names": ["Australian", "Sydney Opera House"], "entity_types": ["location", "location"]}
{"sentence": "The famous painting 'The Scream' by Edvard Munch will be exhibited at the art gallery in Sydney, Australia.", "entity_names": ["Edvard Munch", "Sydney", "Australia"], "entity_types": ["person", "location", "location"]}
{"sentence": "The Smithsonian Institution announces new exhibit on ancient Egyptian artifacts.", "entity_names": ["Smithsonian Institution"], "entity_types": ["organization"]}
{"sentence": "Renowned chef from Canada to open new restaurant in the heart of the USA.", "entity_names": ["Canada", "USA"], "entity_types": ["location", "location"]}
{"sentence": "Actor Zachary Quinto to star in new science fiction film.", "entity_names": ["Zachary Quinto"], "entity_types": ["person"]}
{"sentence": "Zachary Quinto's production company signs deal with major studio.", "entity_names": ["Zachary Quinto"], "entity_types": ["person"]}
{"sentence": "Zachary Quinto speaks out on climate change at United Nations conference.", "entity_names": ["Zachary Quinto", "United Nations"], "entity_types": ["person", "organization"]}
{"sentence": "The Federal Emergency Management Agency mobilizes resources for hurricane response.", "entity_names": ["Federal Emergency Management Agency"], "entity_types": ["organization"]}
{"sentence": "The Intergovernmental Panel on Climate Change report warns of the dire consequences of continued deforestation.", "entity_names": ["The Intergovernmental Panel on Climate Change"], "entity_types": ["organization"]}
{"sentence": "CEO of Microsoft, Satya Nadella, delivers keynote address at technology conference.", "entity_names": ["Microsoft", "Satya Nadella"], "entity_types": ["organization", "person"]}
{"sentence": "Transparency International releases annual corruption index report.", "entity_names": ["Transparency International"], "entity_types": ["organization"]}
{"sentence": "India releases new economic growth forecast for Mumbai region.", "entity_names": ["India", "Mumbai"], "entity_types": ["location", "location"]}
{"sentence": "Mumbai police arrest suspect in connection with counterfeit money scheme.", "entity_names": ["Mumbai"], "entity_types": ["location"]}
{"sentence": "London Mayor pledges to invest in affordable housing for low-income families.", "entity_names": ["London"], "entity_types": ["location"]}
{"sentence": "Apple's new iPhone release date pushed back due to supply chain issues.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "The United Nations calls for an immediate ceasefire in Gaza.", "entity_names": ["United Nations", "Gaza"], "entity_types": ["organization", "location"]}
{"sentence": "Microsoft to acquire video game publisher Activision Blizzard for $75 billion.", "entity_names": ["Microsoft", "Activision Blizzard"], "entity_types": ["organization", "organization"]}
{"sentence": "The University of Sydney announces new scholarship program for international students.", "entity_names": ["University of Sydney"], "entity_types": ["organization"]}
{"sentence": "TED conference to feature groundbreaking research on climate change.", "entity_names": ["TED"], "entity_types": ["organization"]}
{"sentence": "Former President Barack Obama to deliver keynote address at Oxford University.", "entity_names": ["President Barack Obama", "Oxford University"], "entity_types": ["person", "organization"]}
{"sentence": "National Oceanic and Atmospheric Administration predicts above-average hurricane season.", "entity_names": ["National Oceanic and Atmospheric Administration"], "entity_types": ["organization"]}
{"sentence": "U.S. President meets with National Oceanic and Atmospheric Administration officials to discuss climate change.", "entity_names": ["U.S. President", "National Oceanic and Atmospheric Administration"], "entity_types": ["person", "organization"]}
{"sentence": "National Oceanic and Atmospheric Administration issues warning for severe weather in the Midwest.", "entity_names": ["National Oceanic and Atmospheric Administration"], "entity_types": ["organization"]}
{"sentence": "Sylvia Earle appointed as the new president of Wildlife and Environmental Society of South Africa.", "entity_names": ["Sylvia Earle", "Wildlife and Environmental Society of South Africa"], "entity_types": ["person", "organization"]}
{"sentence": "R.D. Lawrence to receive conservation award from The Wildfowl & Wetlands Trust.", "entity_names": ["R.D. Lawrence", "The Wildfowl & Wetlands Trust"], "entity_types": ["person", "organization"]}
{"sentence": "The Wildlife and Environmental Society of South Africa launches new conservation initiative.", "entity_names": ["Wildlife and Environmental Society of South Africa"], "entity_types": ["organization"]}
{"sentence": "The International Committee of the Red Cross provides humanitarian aid to conflict-affected regions.", "entity_names": ["International Committee of the Red Cross"], "entity_types": ["organization"]}
{"sentence": "Dalai Lama calls for peace and compassion in his latest public address.", "entity_names": ["Dalai Lama"], "entity_types": ["person"]}
{"sentence": "The negotiations between the two countries are being mediated by the International Committee of the Red Cross.", "entity_names": ["International Committee of the Red Cross"], "entity_types": ["organization"]}
{"sentence": "Paris Hilton to open new fashion store in France.", "entity_names": ["Paris Hilton", "France"], "entity_types": ["person", "location"]}
{"sentence": "Ancient statue of Julius Caesar discovered in Paris museum.", "entity_names": ["Julius Caesar", "Paris"], "entity_types": ["person", "location"]}
{"sentence": "France Paris sees surge in COVID-19 cases.", "entity_names": ["France", "Paris"], "entity_types": ["location", "location"]}
{"sentence": "Mumbai experiences heavy rainfall, causing widespread flooding.", "entity_names": ["Mumbai"], "entity_types": ["location"]}
{"sentence": "Seoul to host international conference on climate change.", "entity_names": ["Seoul"], "entity_types": ["location"]}
{"sentence": "New York Yankees defeat Boston Red Sox in a closely contested game.", "entity_names": ["New York Yankees", "Boston Red Sox"], "entity_types": ["organization", "organization"]}
{"sentence": "The mayor of Los Angeles has announced a new initiative to combat homelessness in the city.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}
{"sentence": "The Los Angeles Lakers have signed a multi-year contract extension with their head coach.", "entity_names": ["Los Angeles", "Lakers"], "entity_types": ["location", "organization"]}
{"sentence": "Los Angeles police department implements new training program to address rising crime rates.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}
{"sentence": "Wangari Mathai, Nobel Peace Prize laureate, launches new environmental campaign.", "entity_names": ["Wangari Mathai"], "entity_types": ["person"]}
{"sentence": "Hundreds in New York march in support of Wangari Mathai's conservation efforts.", "entity_names": ["New York", "Wangari Mathai"], "entity_types": ["location", "person"]}
{"sentence": "Wangari Mathai Foundation partners with UNICEF to promote sustainable development in Africa.", "entity_names": ["Wangari Mathai Foundation", "UNICEF"], "entity_types": ["organization", "organization"]}
{"sentence": "SpaceX launches new satellite into orbit.", "entity_names": ["SpaceX"], "entity_types": ["organization"]}
{"sentence": "Tokyo prepares for 2020 Summer Olympics.", "entity_names": ["Tokyo"], "entity_types": ["location"]}
{"sentence": "Elon Musk unveils new SpaceX rocket design.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "Chuck Schumer opposes the new tax reform bill.", "entity_names": ["Chuck Schumer"], "entity_types": ["person"]}
{"sentence": "The European Union imposes new trade tariffs on steel imports.", "entity_names": ["The European Union"], "entity_types": ["organization"]}
{"sentence": "Chuck Schumer calls for increased funding for public transportation.", "entity_names": ["Chuck Schumer"], "entity_types": ["person"]}
{"sentence": "Senator Rand Paul calls for bipartisan effort to pass new infrastructure bill.", "entity_names": ["Senator Rand Paul"], "entity_types": ["person"]}
{"sentence": "Rand Paul's proposed tax reform sparks controversy among lawmakers.", "entity_names": ["Rand Paul"], "entity_types": ["person"]}
{"sentence": "Rand Paul meets with local business leaders to discuss economic development initiatives.", "entity_names": ["Rand Paul"], "entity_types": ["person"]}
{"sentence": "Beyonc\u00e9's new album debuts at number one on the Billboard charts.", "entity_names": ["Beyonc\u00e9", "Billboard"], "entity_types": ["person", "organization"]}
{"sentence": "Protests erupt in Bangkok following controversial government decision.", "entity_names": ["Bangkok"], "entity_types": ["location"]}
{"sentence": "SpaceX launches new satellite into orbit for telecommunications company.", "entity_names": ["SpaceX"], "entity_types": ["organization"]}
{"sentence": "The Recording Industry Association of America announces new streaming music certification guidelines.", "entity_names": ["Recording Industry Association of America"], "entity_types": ["organization"]}
{"sentence": "Pop singer Taylor Swift wins three awards at the Recording Industry Association of America's annual ceremony.", "entity_names": ["Taylor Swift", "Recording Industry Association of America"], "entity_types": ["person", "organization"]}
{"sentence": "Music piracy lawsuit filed by the Recording Industry Association of America leads to multi-million dollar settlement.", "entity_names": ["Recording Industry Association of America"], "entity_types": ["organization"]}
{"sentence": "Megan Rapinoe wins Golden Boot award in Women's World Cup.", "entity_names": ["Megan Rapinoe", "Women's World Cup"], "entity_types": ["person", "organization"]}
{"sentence": "Malala Yousafzai meets with world leaders at United Nations summit.", "entity_names": ["Malala Yousafzai", "United Nations"], "entity_types": ["person", "organization"]}
{"sentence": "Football player Megan Rapinoe advocates for gender equality in sports.", "entity_names": ["Megan Rapinoe"], "entity_types": ["person"]}
{"sentence": "Stephen King announces release date for new horror novel.", "entity_names": ["Stephen King"], "entity_types": ["person"]}
{"sentence": "Best-selling author Stephen King to appear at book signing in New York City.", "entity_names": ["Stephen King", "New York City"], "entity_types": ["person", "location"]}
{"sentence": "Film adaptation of Stephen King 's classic novel receives critical acclaim.", "entity_names": ["Stephen King"], "entity_types": ["person"]}
{"sentence": "Save the Children opens new education center in Moscow, Russia.", "entity_names": ["Save the Children", "Moscow", "Russia"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Save the Children partners with local organizations to provide aid in Russia.", "entity_names": ["Save the Children", "Russia"], "entity_types": ["organization", "location"]}
{"sentence": "Facebook, Inc. announces the launch of new virtual reality device.", "entity_names": ["Facebook, Inc."], "entity_types": ["organization"]}
{"sentence": "London mayor declares plan to improve public transportation.", "entity_names": ["London"], "entity_types": ["location"]}
{"sentence": "Police arrest suspect in connection with London bank robbery.", "entity_names": ["London"], "entity_types": ["location"]}
{"sentence": "National Network for Immigrant and Refugee Rights protests outside City Hall in Paris.", "entity_names": ["National Network for Immigrant and Refugee Rights", "Paris"], "entity_types": ["organization", "location"]}
{"sentence": "Paris Police Chief announces new strategy to combat rising crime rates in the city.", "entity_names": ["Paris"], "entity_types": ["location"]}
{"sentence": "The National Network for Immigrant and Refugee Rights calls for better legal protections for undocumented workers in Paris.", "entity_names": ["National Network for Immigrant and Refugee Rights", "Paris"], "entity_types": ["organization", "location"]}
{"sentence": "Barcelona's National Gallery of Art announces new exhibition featuring local artists.", "entity_names": ["Barcelona", "National Gallery of Art"], "entity_types": ["location", "organization"]}
{"sentence": "World-renowned chef opens new restaurant in Barcelona.", "entity_names": ["Barcelona"], "entity_types": ["location"]}
{"sentence": "National Gallery of Art to host virtual tour of its latest exhibit.", "entity_names": ["National Gallery of Art"], "entity_types": ["organization"]}
{"sentence": "Dubai opens new high-tech public transportation system", "entity_names": ["Dubai"], "entity_types": ["location"]}
{"sentence": "International conference on climate change held in Dubai", "entity_names": ["Dubai"], "entity_types": ["location"]}
{"sentence": "Dubai-based company launches new sustainability initiative", "entity_names": ["Dubai"], "entity_types": ["location"]}
{"sentence": "Greta Thunberg delivers powerful speech at climate conference in Mexico City.", "entity_names": ["Greta Thunberg", "Mexico City"], "entity_types": ["person", "location"]}
{"sentence": "Mexico announces new trade deal with European Union.", "entity_names": ["Mexico", "European Union"], "entity_types": ["location", "organization"]}
{"sentence": "Thousands gather in Mexico City to protest government corruption.", "entity_names": ["Mexico City"], "entity_types": ["location"]}
{"sentence": "Record-breaking snowfall at Lake Tahoe this winter.", "entity_names": ["Lake Tahoe"], "entity_types": ["location"]}
{"sentence": "Sydney Opera House to undergo major renovations.", "entity_names": ["Sydney Opera House"], "entity_types": ["location"]}
{"sentence": "New report shows Lake Tahoe water quality improving.", "entity_names": ["Lake Tahoe"], "entity_types": ["location"]}
{"sentence": "Beijing urges Seoul to remain calm amid escalating tensions with China.", "entity_names": ["Beijing", "Seoul", "China"], "entity_types": ["location", "location", "location"]}
{"sentence": "China's President visits Seoul for diplomatic talks.", "entity_names": ["China", "President", "Seoul"], "entity_types": ["location", "person", "location"]}
{"sentence": "Seoul and Beijing sign trade agreement to boost economic cooperation.", "entity_names": ["Seoul", "Beijing"], "entity_types": ["location", "location"]}
{"sentence": "Elon Musk's SpaceX to launch new satellite into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "New York City imposes 100% tax on sugary drinks.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Google announces partnership with UNICEF to provide internet access in developing countries.", "entity_names": ["Google", "UNICEF", "developing countries"], "entity_types": ["organization", "organization", "location"]}
{"sentence": "New study finds correlation between coffee consumption and decreased risk of heart disease.", "entity_names": [], "entity_types": []}
{"sentence": "Elon Musk's SpaceX successfully launches communication satellite into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "California wildfires continue to devastate thousands of acres of land.", "entity_names": ["California"], "entity_types": ["location"]}
{"sentence": "UNICEF launches campaign to provide clean water in drought-affected regions.", "entity_names": ["UNICEF"], "entity_types": ["organization"]}
{"sentence": "Renowned scientist Dr. Jane Goodman awarded Nobel Prize for groundbreaking research.", "entity_names": ["Dr. Jane Goodman"], "entity_types": ["person"]}
{"sentence": "Australia announces new policies to reduce carbon emissions by 2030.", "entity_names": ["Australia"], "entity_types": ["location"]}
{"sentence": "Tesla's CEO, Elon Musk, unveils the company's latest electric car model.", "entity_names": ["Tesla", "Elon Musk"], "entity_types": ["organization", "person"]}
{"sentence": "The United Nations condemns the violence in Myanmar.", "entity_names": ["United Nations", "Myanmar"], "entity_types": ["organization", "location"]}
{"sentence": "Apple's revenue reaches a record high in the fourth quarter.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "Celebrity chef Ree Drummond to launch new cooking show on Food Network.", "entity_names": ["Ree Drummond", "Food Network"], "entity_types": ["person", "organization"]}
{"sentence": "Ree Drummond's cookbook tops bestseller list for the third consecutive week.", "entity_names": ["Ree Drummond"], "entity_types": ["person"]}
{"sentence": "Pawhuska, the hometown of Ree Drummond, experiences a surge in tourism due to her popular TV show.", "entity_names": ["Pawhuska", "Ree Drummond"], "entity_types": ["location", "person"]}
{"sentence": "The American Civil Liberties Union files lawsuit against local school district.", "entity_names": ["The American Civil Liberties Union"], "entity_types": ["organization"]}
{"sentence": "Police arrest man for violating restraining order filed by The American Civil Liberties Union.", "entity_names": ["Police", "The American Civil Liberties Union"], "entity_types": ["organization", "organization"]}
{"sentence": "The American Civil Liberties Union advocates for the rights of individuals in the criminal justice system.", "entity_names": ["The American Civil Liberties Union"], "entity_types": ["organization"]}
{"sentence": "Protests erupted in India after the government's decision to send troops to the Golden Temple.", "entity_names": ["India", "Golden Temple"], "entity_types": ["location", "location"]}
{"sentence": "The controversial visit to the Golden Temple by the foreign diplomat caused a stir in diplomatic circles.", "entity_names": ["Golden Temple"], "entity_types": ["location"]}
{"sentence": "The restoration project of the Golden Temple was completed ahead of schedule, to the delight of local residents.", "entity_names": ["Golden Temple"], "entity_types": ["location"]}
{"sentence": "Tesla announces plans to open new Gigafactory in Texas.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "Angela Merkel re-elected as Chancellor of Germany for fourth term.", "entity_names": ["Angela Merkel", "Germany"], "entity_types": ["person", "location"]}
{"sentence": "Apple introduces new iPhone with advanced facial recognition technology.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "SpaceX successfully launches satellite from Vandenberg Air Force Base.", "entity_names": ["SpaceX", "Vandenberg Air Force Base"], "entity_types": ["organization", "location"]}
{"sentence": "New Vandenberg Air Force Base commander appointed.", "entity_names": ["Vandenberg Air Force Base"], "entity_types": ["location"]}
{"sentence": "Vandenberg Air Force Base to host missile defense test.", "entity_names": ["Vandenberg Air Force Base"], "entity_types": ["location"]}
{"sentence": "Pope Francis visits earthquake-stricken region in Italy.", "entity_names": ["Pope Francis", "Italy"], "entity_types": ["person", "location"]}
{"sentence": "Malala Yousafzai awarded Nobel Peace Prize for her advocacy for girls' education.", "entity_names": ["Malala Yousafzai", "Nobel Peace Prize"], "entity_types": ["person", "organization"]}
{"sentence": "CERN's Large Hadron Collider sets new record for particle collisions.", "entity_names": ["CERN's Large Hadron Collider"], "entity_types": ["organization"]}
{"sentence": "Fashion industry mourns the loss of designer Alexander McQueen.", "entity_names": ["Alexander McQueen"], "entity_types": ["person"]}
{"sentence": "Alexander McQueen's latest collection wows audience at Paris Fashion Week.", "entity_names": ["Alexander McQueen"], "entity_types": ["person"]}
{"sentence": "Alexander McQueen's eponymous fashion house continues to thrive after his passing.", "entity_names": ["Alexander McQueen"], "entity_types": ["organization"]}
{"sentence": "Universal Studios announces new theme park expansion in Orlando.", "entity_names": ["Universal Studios", "Orlando"], "entity_types": ["organization", "location"]}
{"sentence": "The new film from Universal Studios tops box office charts this weekend.", "entity_names": ["Universal Studios"], "entity_types": ["organization"]}
{"sentence": "Universal Studios to produce a new original series for streaming platform.", "entity_names": ["Universal Studios"], "entity_types": ["organization"]}
{"sentence": "Amnesty International criticizes government's human rights record.", "entity_names": ["Amnesty International"], "entity_types": ["organization"]}
{"sentence": "M\u00e9decins Sans Fronti\u00e8res provides medical aid in war-torn region.", "entity_names": ["M\u00e9decins Sans Fronti\u00e8res"], "entity_types": ["organization"]}
{"sentence": "Famous actress rumored to be dating billionaire entrepreneur.", "entity_names": [], "entity_types": []}
{"sentence": "Narendra Modi meets with Joe Biden at the United Nations General Assembly.", "entity_names": ["Narendra Modi", "Joe Biden", "United Nations General Assembly"], "entity_types": ["person", "person", "organization"]}
{"sentence": "New COVID-19 cases surge in India, prompting Narendra Modi to announce stricter lockdown measures.", "entity_names": ["India", "Narendra Modi"], "entity_types": ["location", "person"]}
{"sentence": "Joe Biden unveils infrastructure plan to address climate change and create jobs.", "entity_names": ["Joe Biden"], "entity_types": ["person"]}
{"sentence": "The Great Barrier Reef is facing unprecedented coral bleaching due to rising water temperatures.", "entity_names": ["The Great Barrier Reef"], "entity_types": ["location"]}
{"sentence": "Experts warn that The Great Barrier Reef is at risk of irreversible damage without immediate action.", "entity_names": ["The Great Barrier Reef"], "entity_types": ["location"]}
{"sentence": "The Great Barrier Reef Marine Park Authority has implemented new measures to protect the fragile ecosystem.", "entity_names": ["The Great Barrier Reef"], "entity_types": ["organization"]}
{"sentence": "The National Trust for Historic Preservation celebrates the reopening of a historic theater in downtown Chicago.", "entity_names": ["National Trust for Historic Preservation", "Chicago"], "entity_types": ["organization", "location"]}
{"sentence": "The National Trust for Historic Preservation announces the designation of a new cultural heritage site in New Orleans.", "entity_names": ["National Trust for Historic Preservation", "New Orleans"], "entity_types": ["organization", "location"]}
{"sentence": "Volunteers from the National Trust for Historic Preservation work to restore a historically significant lighthouse on the East Coast.", "entity_names": ["National Trust for Historic Preservation", "East Coast"], "entity_types": ["organization", "location"]}
{"sentence": "Tesla announces plans to build new gigafactory in Texas.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "Famous actor Tom Hanks donates $1 million to charity for COVID-19 relief.", "entity_names": ["Tom Hanks"], "entity_types": ["person"]}
{"sentence": "The United Nations releases report on climate change impact in the Arctic.", "entity_names": ["United Nations", "Arctic"], "entity_types": ["organization", "location"]}
{"sentence": "National Football League announces plans to expand into international markets.", "entity_names": ["National Football League"], "entity_types": ["organization"]}
{"sentence": "Jerusalem mayor pledges to improve public transportation in the city.", "entity_names": ["Jerusalem"], "entity_types": ["location"]}
{"sentence": "The National Football League has invested in infrastructure improvements in Jerusalem for upcoming games.", "entity_names": ["National Football League", "Jerusalem"], "entity_types": ["organization", "location"]}
{"sentence": "Tech giant Apple launches new line of iPhones, hoping to boost sales amid fierce competition.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "President Biden announces plan to invest $1 trillion in infrastructure, aiming to create jobs and improve the nation's roads and bridges.", "entity_names": ["President Biden"], "entity_types": ["person"]}
{"sentence": "Wildfires continue to spread in California, forcing thousands to evacuate their homes.", "entity_names": ["California"], "entity_types": ["location"]}
{"sentence": "Protesters demand justice for George Floyd in downtown Minneapolis.", "entity_names": ["George Floyd", "Minneapolis"], "entity_types": ["person", "location"]}
{"sentence": "Black Lives Matter Global Network launches new initiative to address systemic racism in education.", "entity_names": ["Black Lives Matter Global Network"], "entity_types": ["organization"]}
{"sentence": "Former police officer convicted in the killing of George Floyd sentenced to 22.5 years in prison.", "entity_names": ["George Floyd"], "entity_types": ["person"]}
{"sentence": "Google LLC unveils new smartphone at tech conference.", "entity_names": ["Google LLC"], "entity_types": ["organization"]}
{"sentence": "The United States Environmental Protection Agency announces new regulations for carbon emissions.", "entity_names": ["United States Environmental Protection Agency"], "entity_types": ["organization"]}
{"sentence": "Biography on the life of Steve Jobs to be released next month.", "entity_names": ["Steve Jobs"], "entity_types": ["person"]}
{"sentence": "President Biden announces new infrastructure plan.", "entity_names": ["President Biden"], "entity_types": ["person"]}
{"sentence": "New York City to invest $100 million in public transportation.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Apple Inc. reports record-breaking quarterly earnings.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Dubai launches new initiative to promote sustainable tourism.", "entity_names": ["Dubai"], "entity_types": ["location"]}
{"sentence": "United Arab Emirates signs trade agreement with China.", "entity_names": ["United Arab Emirates", "China"], "entity_types": ["location", "location"]}
{"sentence": "Moscow to host international conference on climate change.", "entity_names": ["Moscow"], "entity_types": ["location"]}
{"sentence": "Barcelona to implement new measures to combat air pollution.", "entity_names": ["Barcelona"], "entity_types": ["location"]}
{"sentence": "Spain sees surge in tourism after easing COVID-19 restrictions.", "entity_names": ["Spain"], "entity_types": ["location"]}
{"sentence": "Keurig Dr Pepper announces expansion of production facilities in Texas.", "entity_names": ["Keurig Dr Pepper", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "The annual Pride parade drew thousands of participants to Church-Wellesley Village.", "entity_names": ["Church-Wellesley Village"], "entity_types": ["location"]}
{"sentence": "New York City lawmakers approve plan to expand affordable housing in the city.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Stonewall riots in 1969 sparked the modern LGBTQ+ rights movement.", "entity_names": ["Stonewall"], "entity_types": ["location"]}
{"sentence": "Northrop Grumman wins contract to build new spacecraft for JAXA.", "entity_names": ["Northrop Grumman", "JAXA"], "entity_types": ["organization", "organization"]}
{"sentence": "Scientists unveil prototype for Mars Habitat, designed for sustainable living on the red planet.", "entity_names": [], "entity_types": []}
{"sentence": "CEO of Northrop Grumman announces partnership with JAXA to develop technology for future Mars missions.", "entity_names": ["Northrop Grumman", "JAXA"], "entity_types": ["organization", "organization"]}
{"sentence": "Lionel Messi scores hat-trick in Barcelona's victory.", "entity_names": ["Lionel Messi", "Barcelona"], "entity_types": ["person", "organization"]}
{"sentence": "Argentina national team coach praises Lionel Messi's leadership.", "entity_names": ["Argentina", "Lionel Messi"], "entity_types": ["location", "person"]}
{"sentence": "Paris Saint-Germain signs Lionel Messi to a two-year contract.", "entity_names": ["Paris Saint-Germain", "Lionel Messi"], "entity_types": ["organization", "person"]}
{"sentence": "Elon Musk's SpaceX launches another batch of Starlink satellites into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "Paris, France to implement new environmental regulations to reduce air pollution.", "entity_names": ["Paris", "France"], "entity_types": ["location", "location"]}
{"sentence": "Suspect Anthony Williams apprehended in connection with the bank robbery.", "entity_names": ["Suspect Anthony Williams"], "entity_types": ["person"]}
{"sentence": "Government announces new infrastructure project to improve transportation across the country.", "entity_names": ["Government"], "entity_types": ["organization"]}
{"sentence": "The annual charity gala will be held at the Four Seasons Hotel in New York City.", "entity_names": ["Four Seasons Hotel", "New York City"], "entity_types": ["organization", "location"]}
{"sentence": "Microsoft CEO Satya Nadella visits London to discuss tech innovations.", "entity_names": ["Satya Nadella", "London"], "entity_types": ["person", "location"]}
{"sentence": "New exhibition at the Tate Modern in London showcases contemporary artists from around the world.", "entity_names": ["Tate Modern", "London"], "entity_types": ["organization", "location"]}
{"sentence": "London's mayor announces new initiatives to tackle air pollution in the city.", "entity_names": ["London"], "entity_types": ["location"]}
{"sentence": "National Geographic Society announces new conservation initiatives in Africa.", "entity_names": ["National Geographic Society", "Africa"], "entity_types": ["organization", "location"]}
{"sentence": "Researchers from National Geographic Society discover new species of fish in the Amazon River.", "entity_names": ["National Geographic Society", "Amazon River"], "entity_types": ["organization", "location"]}
{"sentence": "National Geographic Society partners with local communities to protect endangered species in Southeast Asia.", "entity_names": ["National Geographic Society", "Southeast Asia"], "entity_types": ["organization", "location"]}
{"sentence": "National Geographic explores the mysteries of the Amazon rainforest.", "entity_names": ["National Geographic", "Amazon rainforest"], "entity_types": ["organization", "location"]}
{"sentence": "Lady Gaga releases new album, breaking streaming records.", "entity_names": ["Lady Gaga"], "entity_types": ["person"]}
{"sentence": "Sydney Opera House hosts world-renowned orchestra for one-night-only performance.", "entity_names": ["Sydney Opera House"], "entity_types": ["location"]}
{"sentence": "Thousands of tourists flock to Edinburgh for the start of the annual Edinburgh Fringe Festival.", "entity_names": ["Edinburgh", "Edinburgh Fringe Festival"], "entity_types": ["location", "organization"]}
{"sentence": "Local businesses benefit from the influx of visitors during the Edinburgh Fringe Festival.", "entity_names": ["Edinburgh Fringe Festival"], "entity_types": ["organization"]}
{"sentence": "Comedians from all over the world showcase their talents at the renowned Edinburgh Fringe Festival.", "entity_names": ["Edinburgh Fringe Festival"], "entity_types": ["organization"]}
{"sentence": "Beyonc\u00e9 releases new single and announces upcoming tour.", "entity_names": ["Beyonc\u00e9"], "entity_types": ["person"]}
{"sentence": "New York City to host Beyonc\u00e9 concert at Madison Square Garden.", "entity_names": ["New York City", "Beyonc\u00e9", "Madison Square Garden"], "entity_types": ["location", "person", "location"]}
{"sentence": "Beyonc\u00e9's foundation donates $1 million to local charity.", "entity_names": ["Beyonc\u00e9"], "entity_types": ["person"]}
{"sentence": "CELEBRITY COUPLE ANNOUNCES ENGAGEMENT.", "entity_names": ["CELEBRITY COUPLE"], "entity_types": ["person"]}
{"sentence": "NEW YORK CITY TO IMPLEMENT STRICTER COVID-19 RESTRICTIONS.", "entity_names": ["NEW YORK CITY"], "entity_types": ["location"]}
{"sentence": "APPLE REPORTS RECORD SALES FOR LATEST iPHONE MODEL.", "entity_names": ["APPLE"], "entity_types": ["organization"]}
{"sentence": "Microsoft announced that Satya Nadella will be stepping down as CEO.", "entity_names": ["Microsoft", "Satya Nadella"], "entity_types": ["organization", "person"]}
{"sentence": "Amazon unveils new delivery drone design.", "entity_names": ["Amazon"], "entity_types": ["organization"]}
{"sentence": "As the pandemic persists, Amazon continues to see record-breaking profits.", "entity_names": ["Amazon"], "entity_types": ["organization"]}
{"sentence": "Microsoft CEO Satya Nadella announces new technology initiatives.", "entity_names": ["Microsoft", "Satya Nadella"], "entity_types": ["organization", "person"]}
{"sentence": "Sydney, Australia experiences record-breaking heatwave.", "entity_names": ["Sydney", "Australia"], "entity_types": ["location", "location"]}
{"sentence": "Mark Zuckerberg's new charity project aims to address global health issues.", "entity_names": ["Mark Zuckerberg"], "entity_types": ["person"]}
{"sentence": "Elon Musk's SpaceX successfully launches 60 more Starlink satellites into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "The United Nations warns of escalating violence in the Democratic Republic of the Congo.", "entity_names": ["United Nations", "Democratic Republic of the Congo"], "entity_types": ["organization", "location"]}
{"sentence": "Apple announces plans to open a new flagship store in Tokyo next year.", "entity_names": ["Apple", "Tokyo"], "entity_types": ["organization", "location"]}
{"sentence": "George Soros donates $1 million to support refugee assistance in Europe.", "entity_names": ["George Soros"], "entity_types": ["person"]}
{"sentence": "Prominent philanthropist George Soros pledges to establish a new foundation dedicated to environmental conservation.", "entity_names": ["George Soros"], "entity_types": ["person"]}
{"sentence": "George Soros calls for increased transparency and accountability in global financial institutions.", "entity_names": ["George Soros"], "entity_types": ["person"]}
{"sentence": "Doctors Without Borders provides medical aid to refugees in war-torn region.", "entity_names": ["Doctors Without Borders"], "entity_types": ["organization"]}
{"sentence": "The new healthcare bill faces opposition from Doctors Without Borders.", "entity_names": ["Doctors Without Borders"], "entity_types": ["organization"]}
{"sentence": "Doctors Without Borders sends emergency response team to earthquake-affected area.", "entity_names": ["Doctors Without Borders"], "entity_types": ["organization"]}
{"sentence": "American Red Cross provides aid to victims of natural disaster.", "entity_names": ["American Red Cross"], "entity_types": ["organization"]}
{"sentence": "CEO of American Red Cross steps down amid controversy.", "entity_names": ["American Red Cross"], "entity_types": ["organization"]}
{"sentence": "American Red Cross volunteers distribute food and supplies to hurricane survivors.", "entity_names": ["American Red Cross"], "entity_types": ["organization"]}
{"sentence": "Tokyo Marathon canceled due to COVID-19 concerns.", "entity_names": ["Tokyo"], "entity_types": ["location"]}
{"sentence": "Japan's Prime Minister announces economic stimulus package.", "entity_names": ["Japan"], "entity_types": ["location"]}
{"sentence": "New study shows declining fish population in Lake Victoria.", "entity_names": ["Lake Victoria"], "entity_types": ["location"]}
{"sentence": "Walt Disney World announces new park expansion.", "entity_names": ["Walt Disney World"], "entity_types": ["organization"]}
{"sentence": "Visitors flock to Walt Disney World for reopening after pandemic shutdown.", "entity_names": ["Walt Disney World"], "entity_types": ["location"]}
{"sentence": "Walt Disney World to host annual holiday parade.", "entity_names": ["Walt Disney World"], "entity_types": ["organization"]}
{"sentence": "David Thompson appointed as CEO of Microsoft.", "entity_names": ["David Thompson", "Microsoft"], "entity_types": ["person", "organization"]}
{"sentence": "Anthony Nguyen wins the 2021 Nobel Peace Prize.", "entity_names": ["Anthony Nguyen"], "entity_types": ["person"]}
{"sentence": "New York City declares a state of emergency due to severe weather.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "The Kraft Heinz Company announced a new partnership with Nestl\u00e9 to develop a line of healthy snack options.", "entity_names": ["Kraft Heinz Company", "Nestl\u00e9"], "entity_types": ["organization", "organization"]}
{"sentence": "Nestl\u00e9 plans to invest $1 billion in sustainable packaging initiatives over the next five years.", "entity_names": ["Nestl\u00e9"], "entity_types": ["organization"]}
{"sentence": "The Kraft Heinz Company CEO resigns amid declining sales and profits.", "entity_names": ["Kraft Heinz Company"], "entity_types": ["organization"]}
{"sentence": "Hilton Worldwide announces plans for new hotel in downtown Miami.", "entity_names": ["Hilton Worldwide", "Miami"], "entity_types": ["organization", "location"]}
{"sentence": "Carlos Martinez signs multi-year contract with New York Mets.", "entity_names": ["Carlos Martinez", "New York Mets"], "entity_types": ["person", "organization"]}
{"sentence": "Major flooding reported in southern California after heavy rainfall.", "entity_names": ["southern California"], "entity_types": ["location"]}
{"sentence": "HP Inc. announces new line of laptops with enhanced security features.", "entity_names": ["HP Inc."], "entity_types": ["organization"]}
{"sentence": "Berlin, Germany reports a surge in tourism despite COVID-19 restrictions.", "entity_names": ["Berlin", "Germany"], "entity_types": ["location", "location"]}
{"sentence": "Twitter, Inc. unveils new policy to combat misinformation on its platform.", "entity_names": ["Twitter, Inc."], "entity_types": ["organization"]}
{"sentence": "The National Endowment for the Arts announced funding for 20 new arts initiatives.", "entity_names": ["National Endowment for the Arts"], "entity_types": ["organization"]}
{"sentence": "Renowned playwright John Smith to receive National Endowment for the Arts grant.", "entity_names": ["John Smith", "National Endowment for the Arts"], "entity_types": ["person", "organization"]}
{"sentence": "Local theater company awarded National Endowment for the Arts fellowship.", "entity_names": ["National Endowment for the Arts"], "entity_types": ["organization"]}
{"sentence": "Dubai announces plans for new sustainable energy project", "entity_names": ["Dubai"], "entity_types": ["location"]}
{"sentence": "Niagara Falls tourism sees significant increase in visitors", "entity_names": ["Niagara Falls"], "entity_types": ["location"]}
{"sentence": "Investigation reveals environmental violations by major oil company", "entity_names": ["oil company"], "entity_types": ["organization"]}
{"sentence": "Tesla announces plans to build new manufacturing plant in Texas by 2022.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "Prime Minister Boris Johnson visits India to discuss trade agreements.", "entity_names": ["Boris Johnson", "India"], "entity_types": ["person", "location"]}
{"sentence": "CDC reports spike in flu cases, recommends increased vaccination efforts.", "entity_names": ["CDC"], "entity_types": ["organization"]}
{"sentence": "Celebrities flock to Paris Fashion Week for the latest trends.", "entity_names": [], "entity_types": []}
{"sentence": "Fashion designers from around the world showcase their collections at Paris Fashion Week.", "entity_names": [], "entity_types": []}
{"sentence": "Top models and influencers gather in Paris for the highly-anticipated Paris Fashion Week event.", "entity_names": ["Paris Fashion Week"], "entity_types": ["organization"]}
{"sentence": "Protests erupt in Cape Town following controversial election results.", "entity_names": ["Cape Town"], "entity_types": ["location"]}
{"sentence": "Cape Town University announces new scholarship program for foreign students.", "entity_names": ["Cape Town University"], "entity_types": ["organization"]}
{"sentence": "Cape Town native, Nelson Mandela, honored with a statue in the city center.", "entity_names": ["Cape Town", "Nelson Mandela"], "entity_types": ["location", "person"]}
{"sentence": "Shawn Mendes to perform at annual music festival in Australia.", "entity_names": ["Shawn Mendes", "Australia"], "entity_types": ["person", "location"]}
{"sentence": "New Shawn Mendes album hits number one on the charts.", "entity_names": ["Shawn Mendes"], "entity_types": ["person"]}
{"sentence": "Shawn Mendes cancels upcoming tour dates due to illness.", "entity_names": ["Shawn Mendes"], "entity_types": ["person"]}
{"sentence": "The J. Paul Getty Trust donates $10 million to support arts education in underserved communities.", "entity_names": ["The J. Paul Getty Trust"], "entity_types": ["organization"]}
{"sentence": "Los Angeles County Museum of Art collaborates with The J. Paul Getty Trust to organize a special exhibition on ancient Egyptian artifacts.", "entity_names": ["Los Angeles County Museum of Art", "The J. Paul Getty Trust"], "entity_types": ["organization", "organization"]}
{"sentence": "The J. Paul Getty Trust announces the acquisition of a rare collection of Renaissance paintings from a private collector.", "entity_names": ["The J. Paul Getty Trust"], "entity_types": ["organization"]}
{"sentence": "German Chancellor Angela Merkel meets with French President Emmanuel Macron in Paris.", "entity_names": ["Angela Merkel", "Emmanuel Macron", "Paris"], "entity_types": ["person", "person", "location"]}
{"sentence": "Stocks fall as investors react to Angela Merkel's announcement of not seeking re-election.", "entity_names": ["Angela Merkel"], "entity_types": ["person"]}
{"sentence": "European Union leaders hold emergency summit to discuss Angela Merkel's proposal for migrant policy reform.", "entity_names": ["European Union", "Angela Merkel"], "entity_types": ["organization", "person"]}
{"sentence": "Renowned chef Mario Batali opens new restaurant in Istanbul.", "entity_names": ["Mario Batali", "Istanbul"], "entity_types": ["person", "location"]}
{"sentence": "Turkey's tourism industry sees a surge in visitors.", "entity_names": ["Turkey"], "entity_types": ["location"]}
{"sentence": "Istanbul airport ranked among the top in the world.", "entity_names": ["Istanbul"], "entity_types": ["location"]}
{"sentence": "A new exhibition featuring Salvador Dal\u00ed's surrealist masterpieces opens at The Prado Museum in Madrid.", "entity_names": ["Salvador Dal\u00ed", "The Prado Museum"], "entity_types": ["person", "location"]}
{"sentence": "The iconic painting 'The Persistence of Memory' by Salvador Dal\u00ed will be on display at The Prado Museum for a limited time.", "entity_names": ["The Persistence of Memory", "Salvador Dal\u00ed", "The Prado Museum"], "entity_types": ["organization", "person", "location"]}
{"sentence": "A rare collection of Salvador Dal\u00ed's works is set to be showcased at The Prado Museum in collaboration with the Dal\u00ed Foundation.", "entity_names": ["Salvador Dal\u00ed", "The Prado Museum", "Dal\u00ed Foundation"], "entity_types": ["person", "location", "organization"]}
{"sentence": "Tyson Foods announces new plant opening in Montreal, Canada.", "entity_names": ["Tyson Foods", "Montreal", "Canada"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Martha Stewart launches new line of home goods.", "entity_names": ["Martha Stewart"], "entity_types": ["person"]}
{"sentence": "Heavy snowfall leads to travel disruptions in Montreal, Canada.", "entity_names": ["Montreal", "Canada"], "entity_types": ["location", "location"]}
{"sentence": "The new research facility in Melbourne will focus on developing innovative treatments for rare diseases.", "entity_names": ["Melbourne"], "entity_types": ["location"]}
{"sentence": "The CEO of the Melbourne-based tech company announced a partnership with a leading international corporation.", "entity_names": ["Melbourne"], "entity_types": ["location"]}
{"sentence": "Melbourne hosts its annual music festival, featuring top artists from around the world.", "entity_names": ["Melbourne"], "entity_types": ["location"]}
{"sentence": "Celebrity chef Mario Batali to launch new cooking show on streaming platform.", "entity_names": ["Mario Batali"], "entity_types": ["person"]}
{"sentence": "Mario Batali to open new Italian restaurant in downtown Los Angeles.", "entity_names": ["Mario Batali", "Los Angeles"], "entity_types": ["person", "location"]}
{"sentence": "Allegations against Mario Batali prompt him to step away from his restaurant empire.", "entity_names": ["Mario Batali"], "entity_types": ["person"]}
{"sentence": "NASA administrator, Jim Bridenstine, announces new plans for space exploration.", "entity_names": ["NASA", "Jim Bridenstine"], "entity_types": ["organization", "person"]}
{"sentence": "International Space Station crew members conduct spacewalk under the guidance of Jim Bridenstine.", "entity_names": ["International Space Station", "Jim Bridenstine"], "entity_types": ["location", "person"]}
{"sentence": "Jim Bridenstine advocates for increased funding for future space missions.", "entity_names": ["Jim Bridenstine"], "entity_types": ["person"]}
{"sentence": "NASA's Europa Clipper mission set to explore Jupiter's moon, Europa.", "entity_names": ["NASA", "Europa", "Europa"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Europa League final match to be held in Gdansk, Poland.", "entity_names": ["Gdansk"], "entity_types": ["location"]}
{"sentence": "NASA's Europa mission discovers evidence of potential water plumes on Jupiter's moon.", "entity_names": ["NASA", "Europa"], "entity_types": ["organization", "location"]}
{"sentence": "Jim Fowler, famous animal expert and TV personality, speaks at the National Wildlife Federation event in Washington.", "entity_names": ["Jim Fowler", "National Wildlife Federation", "Washington"], "entity_types": ["person", "organization", "location"]}
{"sentence": "National Wildlife Federation reports a 10% decrease in wildlife habitats due to deforestation in the Amazon rainforest.", "entity_names": ["National Wildlife Federation", "Amazon rainforest"], "entity_types": ["organization", "location"]}
{"sentence": "Environmental activist and National Wildlife Federation member, Jane Smith, to speak at the upcoming climate change conference in New York.", "entity_names": ["National Wildlife Federation", "Jane Smith", "New York"], "entity_types": ["organization", "person", "location"]}
{"sentence": "Apple's new iPhone release date rumors spark excitement among tech enthusiasts.", "entity_names": ["Apple", "iPhone"], "entity_types": ["organization", "organization"]}
{"sentence": "The National Gallery of Art unveils a new exhibition featuring Banksy's latest works.", "entity_names": ["National Gallery of Art", "Banksy"], "entity_types": ["organization", "person"]}
{"sentence": "J.K. Rowling announces the release date for her upcoming fantasy novel series.", "entity_names": ["J.K. Rowling"], "entity_types": ["person"]}
{"sentence": "Tourists from around the world flock to the National Gallery of Art to see the latest exhibit.", "entity_names": ["National Gallery of Art"], "entity_types": ["organization"]}
{"sentence": "The Sierra Club launches new environmental initiative to protect wildlife habitats.", "entity_names": ["Sierra Club"], "entity_types": ["organization"]}
{"sentence": "Journalist Elizabeth Kolbert wins Pulitzer Prize for her coverage of climate change.", "entity_names": ["Elizabeth Kolbert"], "entity_types": ["person"]}
{"sentence": "Sierra Club partners with local community organizations to clean up polluted river.", "entity_names": ["Sierra Club"], "entity_types": ["organization"]}
{"sentence": "Melbourne named the most livable city for the 7th year in a row.", "entity_names": ["Melbourne"], "entity_types": ["location"]}
{"sentence": "Local organization hosts annual charity event in Melbourne.", "entity_names": ["Melbourne"], "entity_types": ["location"]}
{"sentence": "Famous chef opens new restaurant in Melbourne.", "entity_names": ["Melbourne"], "entity_types": ["location"]}
{"sentence": "The Louvre museum in Paris houses a priceless collection of Rembrandt's works.", "entity_names": ["Louvre", "Rembrandt"], "entity_types": ["location", "person"]}
{"sentence": "Claude Monet's famous water lilies will be displayed at the National Gallery in London next month.", "entity_names": ["Claude Monet", "National Gallery", "London"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Authorities have confirmed the authenticity of a newly discovered painting by Rembrandt.", "entity_names": ["Rembrandt"], "entity_types": ["person"]}
{"sentence": "French President Macron to visit Paris, France next week.", "entity_names": ["Macron", "Paris", "France"], "entity_types": ["person", "location", "location"]}
{"sentence": "Tesla announces plans to open new electric car factory in Paris, France.", "entity_names": ["Tesla", "Paris", "France"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Paris, France to host international climate change summit in 2023.", "entity_names": ["Paris", "France"], "entity_types": ["location", "location"]}
{"sentence": "Diego Fernandez appointed as the new CEO of Microsoft.", "entity_names": ["Diego Fernandez", "Microsoft"], "entity_types": ["person", "organization"]}
{"sentence": "The wildfire in California has forced thousands of residents to evacuate their homes.", "entity_names": ["California"], "entity_types": ["location"]}
{"sentence": "Diego Fernandez elected as the new president of the local Chamber of Commerce.", "entity_names": ["Diego Fernandez", "Chamber of Commerce"], "entity_types": ["person", "organization"]}
{"sentence": "The European Space Agency plans to launch a new satellite for climate research.", "entity_names": ["European Space Agency"], "entity_types": ["organization"]}
{"sentence": "Ada Lovelace, the first computer programmer, is celebrated on Ada Lovelace Day.", "entity_names": ["Ada Lovelace", "Ada Lovelace"], "entity_types": ["person", "person"]}
{"sentence": "Scientists are studying the effects of climate change on The Dead Sea's water levels.", "entity_names": ["The Dead Sea"], "entity_types": ["location"]}
{"sentence": "Greta Thunberg speaks at the United Nations Climate Action Summit.", "entity_names": ["Greta Thunberg", "United Nations"], "entity_types": ["person", "organization"]}
{"sentence": "Protesters gather outside the White House demanding action on climate change, while Greta Thunberg addresses the UN General Assembly.", "entity_names": ["White House", "Greta Thunberg", "UN General Assembly"], "entity_types": ["location", "person", "organization"]}
{"sentence": "Greta Thunberg's passionate speech at the Climate Action Summit goes viral on social media.", "entity_names": ["Greta Thunberg", "Climate Action Summit"], "entity_types": ["person", "organization"]}
{"sentence": "Ellen DeGeneres announces new stand-up comedy special.", "entity_names": ["Ellen DeGeneres"], "entity_types": ["person"]}
{"sentence": "Ellen DeGeneres wins award for humanitarian work.", "entity_names": ["Ellen DeGeneres"], "entity_types": ["person"]}
{"sentence": "Coronavirus cases surge in Los Angeles, prompting Ellen DeGeneres to film her talk show from home.", "entity_names": ["Los Angeles", "Ellen DeGeneres"], "entity_types": ["location", "person"]}
{"sentence": "Renowned chef Rick Bayless opens new restaurant in Chicago.", "entity_names": ["Rick Bayless", "Chicago"], "entity_types": ["person", "location"]}
{"sentence": "Rick Bayless partners with non-profit organization to provide meals for the homeless.", "entity_names": ["Rick Bayless"], "entity_types": ["person"]}
{"sentence": "Award-winning chef Rick Bayless announces expansion of his cooking show to new television network.", "entity_names": ["Rick Bayless"], "entity_types": ["person"]}
{"sentence": "Joe Biden delivers speech on climate change at the United Nations General Assembly.", "entity_names": ["Joe Biden", "United Nations General Assembly"], "entity_types": ["person", "organization"]}
{"sentence": "Paris mayor announces new initiatives to combat air pollution in the city.", "entity_names": ["Paris"], "entity_types": ["location"]}
{"sentence": "Shanghai surpasses Paris as the world's most-visited city in global tourism rankings.", "entity_names": ["Shanghai", "Paris"], "entity_types": ["location", "location"]}
{"sentence": "Elon Musk's SpaceX successfully launches satellite into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "New York City imposes indoor mask mandate to curb COVID-19 spread.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Johnson & Johnson recalls baby powder due to asbestos contamination.", "entity_names": ["Johnson & Johnson"], "entity_types": ["organization"]}
{"sentence": "Justin Bieber announces concert tour in London.", "entity_names": ["Justin Bieber", "London"], "entity_types": ["person", "location"]}
{"sentence": "London-based organization provides aid to refugee crisis.", "entity_names": ["London"], "entity_types": ["location"]}
{"sentence": "New study shows Justin Bieber's impact on music industry.", "entity_names": ["Justin Bieber"], "entity_types": ["person"]}
{"sentence": "The Nature Conservancy launches new initiative to protect endangered species in South America.", "entity_names": ["The Nature Conservancy", "South America"], "entity_types": ["organization", "location"]}
{"sentence": "Nature Conservancy's CEO announces partnership with local community to preserve natural habitats.", "entity_names": ["Nature Conservancy"], "entity_types": ["organization"]}
{"sentence": "Researchers from The Nature Conservancy discover new method for sustainable water management.", "entity_names": ["The Nature Conservancy"], "entity_types": ["organization"]}
{"sentence": "Elon Musk's SpaceX successfully launches 60 Starlink satellites into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "Severe storms cause flooding and damage in several states, including Texas and Louisiana.", "entity_names": ["Texas", "Louisiana"], "entity_types": ["location", "location"]}
{"sentence": "Apple to release new iPhone model with advanced camera features next month.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "Tesla announces new electric vehicle model.", "entity_names": ["Tesla"], "entity_types": ["organization"]}
{"sentence": "President Biden visits NATO headquarters in Belgium.", "entity_names": ["President Biden", "NATO", "Belgium"], "entity_types": ["person", "organization", "location"]}
{"sentence": "NASA's Mars rover discovers signs of ancient life.", "entity_names": ["NASA", "Mars"], "entity_types": ["organization", "location"]}
{"sentence": "Ratan Tata invests in a new tech startup.", "entity_names": ["Ratan Tata"], "entity_types": ["person"]}
{"sentence": "The new Ratan Tata Foundation to provide aid to communities in need.", "entity_names": ["Ratan Tata"], "entity_types": ["person"]}
{"sentence": "Ratan Tata appointed as chairman of the global conglomerate.", "entity_names": ["Ratan Tata"], "entity_types": ["person"]}
{"sentence": "Michelle Obama to speak at women's empowerment conference.", "entity_names": ["Michelle Obama"], "entity_types": ["person"]}
{"sentence": "Serena Williams wins Wimbledon for the eighth time.", "entity_names": ["Serena Williams"], "entity_types": ["person"]}
{"sentence": "Highly anticipated book release from Oprah's publishing company.", "entity_names": ["Oprah"], "entity_types": ["person"]}
{"sentence": "Emma Stone signs multi-film deal with New Line Cinema", "entity_names": ["Emma Stone", "New Line Cinema"], "entity_types": ["person", "organization"]}
{"sentence": "New Line Cinema announces partnership with Emma Stone for upcoming thriller", "entity_names": ["New Line Cinema", "Emma Stone"], "entity_types": ["organization", "person"]}
{"sentence": "Emma Stone to star in new romantic comedy produced by New Line Cinema", "entity_names": ["Emma Stone", "New Line Cinema"], "entity_types": ["person", "organization"]}
{"sentence": "London prepares for annual New Year's Eve fireworks display.", "entity_names": ["London"], "entity_types": ["location"]}
{"sentence": "British Prime Minister announces new economic stimulus package.", "entity_names": ["British Prime Minister"], "entity_types": ["organization"]}
{"sentence": "Dr. Terri Irwin appointed as new president of the Sierra Club.", "entity_names": ["Dr. Terri Irwin", "Sierra Club"], "entity_types": ["person", "organization"]}
{"sentence": "Sierra Club launches campaign to protect endangered species in the Amazon rainforest.", "entity_names": ["Sierra Club", "Amazon rainforest"], "entity_types": ["organization", "location"]}
{"sentence": "Dr. Terri Irwin delivers keynote speech on conservation at the Sierra Club annual conference.", "entity_names": ["Dr. Terri Irwin", "Sierra Club"], "entity_types": ["person", "organization"]}
{"sentence": "Prime Minister Trudeau meets with German Chancellor Merkel to discuss trade.", "entity_names": ["Trudeau", "German Chancellor Merkel"], "entity_types": ["person", "person"]}
{"sentence": "Massive wildfire threatens California wine country.", "entity_names": ["California"], "entity_types": ["location"]}
{"sentence": "Tokyo to host the 2020 Summer Olympics.", "entity_names": ["Tokyo"], "entity_types": ["location"]}
{"sentence": "Tesla surpasses $1 trillion market cap.", "entity_names": ["Tesla"], "entity_types": ["organization"]}
{"sentence": "London Mayor Sadiq Khan announces new public transportation initiative.", "entity_names": ["London", "Sadiq Khan"], "entity_types": ["location", "person"]}
{"sentence": "Facebook faces renewed scrutiny over data privacy concerns.", "entity_names": ["Facebook"], "entity_types": ["organization"]}
{"sentence": "Rosa Parks' legacy honored with a new museum in her hometown.", "entity_names": ["Rosa Parks"], "entity_types": ["person"]}
{"sentence": "Panama Canal expansion project to be completed ahead of schedule.", "entity_names": ["Panama Canal"], "entity_types": ["location"]}
{"sentence": "The controversial decision to privatize the Panama Canal faced opposition from various organizations.", "entity_names": ["Panama Canal"], "entity_types": ["location"]}
{"sentence": "Apple launches new iPhone models during an event in Shenzhen, China .", "entity_names": ["Apple", "Shenzhen", "China"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Environmental activists protest against the construction of a new factory in Shenzhen, China .", "entity_names": ["Shenzhen", "China"], "entity_types": ["location", "location"]}
{"sentence": "Renowned chef opens a new restaurant in Shenzhen, China .", "entity_names": ["Shenzhen", "China"], "entity_types": ["location", "location"]}
{"sentence": "Emma Watson announces new role in upcoming film.", "entity_names": ["Emma Watson"], "entity_types": ["person"]}
{"sentence": "Emma Watson spotted at charity event in London.", "entity_names": ["Emma Watson", "London"], "entity_types": ["person", "location"]}
{"sentence": "Emma Watson launches new sustainable fashion brand.", "entity_names": ["Emma Watson"], "entity_types": ["person"]}
{"sentence": "Sylvia Earle, an oceanographer, will be honored with an award for her groundbreaking research on marine life.", "entity_names": ["Sylvia Earle"], "entity_types": ["person"]}
{"sentence": "The conservation organization founded by Sylvia Earle is hosting a beach cleanup event next weekend.", "entity_names": ["Sylvia Earle"], "entity_types": ["person"]}
{"sentence": "A new marine sanctuary off the coast of California, supported by Sylvia Earle, will provide protection for endangered species.", "entity_names": ["California", "Sylvia Earle"], "entity_types": ["location", "person"]}
{"sentence": "Fires continue to devastate the Amazon Rainforest in Brazil.", "entity_names": ["Amazon Rainforest", "Brazil"], "entity_types": ["location", "location"]}
{"sentence": "Amazon announces new HQ location in Brazil.", "entity_names": ["Amazon", "Brazil"], "entity_types": ["organization", "location"]}
{"sentence": "Brazilian president vows to protect the Amazon Rainforest from deforestation.", "entity_names": ["Brazilian president", "Amazon Rainforest"], "entity_types": ["organization", "location"]}
{"sentence": "Angela Merkel reelected as German Chancellor for fourth term.", "entity_names": ["Angela Merkel"], "entity_types": ["person"]}
{"sentence": "Subaru Corporation announces plans for new electric vehicle model.", "entity_names": ["Subaru Corporation"], "entity_types": ["organization"]}
{"sentence": "Protests erupt in downtown Los Angeles after controversial police shooting.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}
{"sentence": "The United Nations reports a sharp increase in humanitarian aid needs in war-torn countries.", "entity_names": ["United Nations"], "entity_types": ["organization"]}
{"sentence": "CEO of Tesla, Elon Musk, reveals plans for a new electric vehicle model.", "entity_names": ["Tesla", "Elon Musk"], "entity_types": ["organization", "person"]}
{"sentence": "Wildfires rage across the Australian outback, prompting mass evacuations.", "entity_names": ["Australian"], "entity_types": ["location"]}
{"sentence": "After years of negotiations, the Panama Canal expansion project is finally complete.", "entity_names": ["Panama Canal"], "entity_types": ["location"]}
{"sentence": "Rome experiences record-breaking heatwave, with temperatures reaching 104 degrees Fahrenheit.", "entity_names": ["Rome"], "entity_types": ["location"]}
{"sentence": "The agreement between Italy and Panama for joint management of the Panama Canal has been finalized.", "entity_names": ["Italy", "Panama Canal"], "entity_types": ["location", "location"]}
{"sentence": "Chinese President Xi Jinping meets with Russian President Vladimir Putin in Moscow.", "entity_names": ["Xi Jinping", "Vladimir Putin", "Moscow"], "entity_types": ["person", "person", "location"]}
{"sentence": "The United Nations criticizes Chinese President Xi Jinping for human rights abuses.", "entity_names": ["United Nations", "Xi Jinping"], "entity_types": ["organization", "person"]}
{"sentence": "Xi Jinping discusses trade relations with European Union leaders in Brussels.", "entity_names": ["Xi Jinping", "European Union", "Brussels"], "entity_types": ["person", "organization", "location"]}
{"sentence": "International tourists flock to visit the Great Wall of China.", "entity_names": ["Great Wall of China"], "entity_types": ["location"]}
{"sentence": "Beijing announces new pollution control measures to improve air quality.", "entity_names": ["Beijing"], "entity_types": ["location"]}
{"sentence": "Chinese organization invests in a new tech startup in Beijing.", "entity_names": ["Beijing"], "entity_types": ["location"]}
{"sentence": "The Coca-Cola Company reports a 20% increase in quarterly profits.", "entity_names": ["Coca-Cola Company"], "entity_types": ["organization"]}
{"sentence": "CEO of the Coca-Cola Company visits new bottling plant in India.", "entity_names": ["Coca-Cola Company"], "entity_types": ["organization"]}
{"sentence": "Coca-Cola Company to launch new line of flavored water beverages.", "entity_names": ["Coca-Cola Company"], "entity_types": ["organization"]}
{"sentence": "The United Nations Climate Change Conference aims to address the global impact of rising sea levels and extreme weather events.", "entity_names": ["United Nations Climate Change Conference"], "entity_types": ["organization"]}
{"sentence": "Countries along the Nile River are working to manage water resources for agricultural and domestic use.", "entity_names": ["Nile River"], "entity_types": ["location"]}
{"sentence": "Leaders from various organizations attended the United Nations Climate Change Conference to discuss strategies for reducing greenhouse gas emissions.", "entity_names": ["United Nations Climate Change Conference"], "entity_types": ["organization"]}
{"sentence": "The CEO of Google announced the company's plan to expand into the renewable energy sector.", "entity_names": ["Google"], "entity_types": ["organization"]}
{"sentence": "The President of France will be meeting with the Chancellor of Germany to discuss trade agreements.", "entity_names": ["France", "Germany"], "entity_types": ["location", "location"]}
{"sentence": "The UN has declared a humanitarian crisis in the war-torn region of South Sudan.", "entity_names": ["UN", "South Sudan"], "entity_types": ["organization", "location"]}
{"sentence": "Disney announces new partnership with Lucasfilm Ltd.", "entity_names": ["Disney", "Lucasfilm Ltd."], "entity_types": ["organization", "organization"]}
{"sentence": "California offers tax incentives to lure Lucasfilm Ltd. headquarters to Los Angeles.", "entity_names": ["California", "Lucasfilm Ltd.", "Los Angeles"], "entity_types": ["location", "organization", "location"]}
{"sentence": "Director Jon Favreau to helm new Star Wars series for Lucasfilm Ltd.", "entity_names": ["Jon Favreau", "Star Wars", "Lucasfilm Ltd."], "entity_types": ["person", "organization", "organization"]}
{"sentence": "Sydney to host the 2023 FIFA Women's World Cup.", "entity_names": ["Sydney", "FIFA"], "entity_types": ["location", "organization"]}
{"sentence": "Australia's Prime Minister announces new climate change policies.", "entity_names": ["Australia", "Prime Minister"], "entity_types": ["location", "person"]}
{"sentence": "Rio de Janeiro Carnival cancelled due to COVID-19 concerns.", "entity_names": ["Rio de Janeiro", "COVID-19"], "entity_types": ["location", "organization"]}
{"sentence": "Subway announces plans to open 100 new locations in the next year.", "entity_names": ["Subway"], "entity_types": ["organization"]}
{"sentence": "World Health Organization reports significant increase in global vaccination rates.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}
{"sentence": "New study shows Subway sandwiches contain higher sodium levels than recommended by health experts.", "entity_names": ["Subway"], "entity_types": ["organization"]}
{"sentence": "Jennifer Lopez to headline the upcoming music festival in Miami.", "entity_names": ["Jennifer Lopez", "Miami"], "entity_types": ["person", "location"]}
{"sentence": "Taj Mahal named as one of the New Seven Wonders of the World.", "entity_names": ["Taj Mahal"], "entity_types": ["location"]}
{"sentence": "Local organization hosts fundraiser event to support cancer research.", "entity_names": [], "entity_types": []}
{"sentence": "The National Endowment for the Arts announces grant recipients for the upcoming fiscal year.", "entity_names": ["National Endowment for the Arts"], "entity_types": ["organization"]}
{"sentence": "Regional artist to showcase work at the National Endowment for the Arts gallery in Washington, D.C.", "entity_names": ["National Endowment for the Arts", "Washington, D.C."], "entity_types": ["organization", "location"]}
{"sentence": "Famous musician partners with the National Endowment for the Arts to promote arts education in public schools.", "entity_names": ["National Endowment for the Arts"], "entity_types": ["organization"]}
{"sentence": "Elon Musk's SpaceX announces plans for mission to Mars.", "entity_names": ["Elon Musk", "SpaceX", "Mars"], "entity_types": ["person", "organization", "location"]}
{"sentence": "United Nations releases report on climate change impact in Africa.", "entity_names": ["United Nations", "Africa"], "entity_types": ["organization", "location"]}
{"sentence": "Apple's new iPhone launch event draws massive crowd in New York City.", "entity_names": ["Apple", "New York City"], "entity_types": ["organization", "location"]}
{"sentence": "Cape Town, South Africa experiences record-breaking heatwave.", "entity_names": ["Cape Town", "South Africa"], "entity_types": ["location", "location"]}
{"sentence": "Vancouver, Canada-based tech company announces new partnership.", "entity_names": ["Vancouver", "Canada"], "entity_types": ["location", "location"]}
{"sentence": "South African president visits Cape Town for environmental summit.", "entity_names": ["Cape Town"], "entity_types": ["location"]}
{"sentence": "Tourists flock to the stunning natural beauty of the Gal\u00e1pagos Islands.", "entity_names": ["Gal\u00e1pagos Islands"], "entity_types": ["location"]}
{"sentence": "Research shows that climate change is having a significant impact on the wildlife of the Gal\u00e1pagos Islands.", "entity_names": ["Gal\u00e1pagos Islands"], "entity_types": ["location"]}
{"sentence": "Conservation efforts in the Gal\u00e1pagos Islands have been successful in preserving the unique ecosystem.", "entity_names": ["Gal\u00e1pagos Islands"], "entity_types": ["location"]}
{"sentence": "Scientists discover new species of penguin in Antarctica.", "entity_names": ["Antarctica"], "entity_types": ["location"]}
{"sentence": "Antarctica experiences record high temperatures, raising concerns about melting ice caps.", "entity_names": ["Antarctica"], "entity_types": ["location"]}
{"sentence": "International research team embarks on ambitious mission to study climate change impact on Antarctica.", "entity_names": ["Antarctica"], "entity_types": ["location"]}
{"sentence": "South Africa announces new COVID-19 restrictions in Cape Town.", "entity_names": ["South Africa", "Cape Town"], "entity_types": ["location", "location"]}
{"sentence": "Microsoft acquires cybersecurity firm based in Cape Town.", "entity_names": ["Microsoft", "Cape Town"], "entity_types": ["organization", "location"]}
{"sentence": "Local activist from Cape Town nominated for Nobel Peace Prize.", "entity_names": ["Cape Town"], "entity_types": ["location"]}
{"sentence": "Serena Williams advances to the final at Wimbledon.", "entity_names": ["Serena Williams", "Wimbledon"], "entity_types": ["person", "location"]}
{"sentence": "Investigation reveals ties between Russian oligarch and Serena Williams' tennis academy.", "entity_names": ["Russian", "Serena Williams"], "entity_types": ["location", "person"]}
{"sentence": "Serena Williams partners with UNICEF to promote girls' education in Africa.", "entity_names": ["Serena Williams", "UNICEF", "Africa"], "entity_types": ["person", "organization", "location"]}
{"sentence": "APPLE LAUNCHES NEW IPHONE 13 WITH ENHANCED CAMERA.", "entity_names": ["APPLE"], "entity_types": ["organization"]}
{"sentence": "TERRORIST ATTACK IN PARIS KILLS 12.", "entity_names": ["PARIS"], "entity_types": ["location"]}
{"sentence": "ELON MUSK'S SPACEX LAUNCHES FALCON 9 ROCKET INTO ORBIT.", "entity_names": ["ELON MUSK", "SPACEX", "FALCON 9"], "entity_types": ["person", "organization", "organization"]}
{"sentence": "Huawei Technologies Co., Ltd. faces lawsuit from Qualcomm over patent infringement.", "entity_names": ["Huawei Technologies Co., Ltd.", "Qualcomm"], "entity_types": ["organization", "organization"]}
{"sentence": "Qualcomm announces partnership with Huawei Technologies Co., Ltd. for development of new 5G technology.", "entity_names": ["Qualcomm", "Huawei Technologies Co., Ltd."], "entity_types": ["organization", "organization"]}
{"sentence": "Huawei Technologies Co., Ltd. CEO speaks out against trade restrictions imposed by the U.S. government.", "entity_names": ["Huawei Technologies Co., Ltd.", "U.S."], "entity_types": ["organization", "location"]}
{"sentence": "Scientists discover new species of amphibian in the Okefenokee Swamp.", "entity_names": ["Okefenokee Swamp"], "entity_types": ["location"]}
{"sentence": "Environmental group launches campaign to protect the Okefenokee Swamp from industrial development.", "entity_names": ["Okefenokee Swamp"], "entity_types": ["location"]}
{"sentence": "Tourists flock to the Okefenokee Swamp for wildlife viewing and kayaking.", "entity_names": ["Okefenokee Swamp"], "entity_types": ["location"]}
{"sentence": "United Nations reports increase in global hunger.", "entity_names": ["United Nations"], "entity_types": ["organization"]}
{"sentence": "Xi Jinping pledges to strengthen China's economy.", "entity_names": ["Xi Jinping"], "entity_types": ["person"]}
{"sentence": "Tensions rise between United Nations and North Korea.", "entity_names": ["United Nations", "North Korea"], "entity_types": ["organization", "location"]}
{"sentence": "Local Chamber of Commerce hosts annual Small Business Awards ceremony.", "entity_names": ["Local Chamber of Commerce"], "entity_types": ["organization"]}
{"sentence": "Hollywood actress donates $100,000 to local charity.", "entity_names": [], "entity_types": []}
{"sentence": "Local Chamber of Commerce partners with city government for job fair.", "entity_names": ["Local Chamber of Commerce"], "entity_types": ["organization"]}
{"sentence": "The National Gallery of Art unveils new exhibit on impressionist painters.", "entity_names": ["National Gallery of Art"], "entity_types": ["organization"]}
{"sentence": "Renowned artist to hold workshop at the National Gallery of Art.", "entity_names": ["National Gallery of Art"], "entity_types": ["organization"]}
{"sentence": "National Gallery of Art appoints new chief curator.", "entity_names": ["National Gallery of Art"], "entity_types": ["organization"]}
{"sentence": "The Earthwatch Institute is conducting a study on the wildlife in the Pantanal wetlands.", "entity_names": ["Earthwatch Institute", "Pantanal wetlands"], "entity_types": ["organization", "location"]}
{"sentence": "The Pantanal wetlands, located in Brazil, is home to a wide variety of endangered species.", "entity_names": ["Pantanal wetlands", "Brazil"], "entity_types": ["location", "location"]}
{"sentence": "Earthwatch Institute's scientists have discovered a new species of frog in the Pantanal wetlands.", "entity_names": ["Earthwatch Institute", "Pantanal wetlands"], "entity_types": ["organization", "location"]}
{"sentence": "Samin Nosrat to host cooking show in Dubai.", "entity_names": ["Samin Nosrat", "Dubai"], "entity_types": ["person", "location"]}
{"sentence": "Alton Brown announces live tour in United Arab Emirates.", "entity_names": ["Alton Brown", "United Arab Emirates"], "entity_types": ["person", "location"]}
{"sentence": "Dubai named host city for 2020 World Expo.", "entity_names": ["Dubai"], "entity_types": ["location"]}
{"sentence": "Lupita Nyong'o to star in new film set in Dubai.", "entity_names": ["Lupita Nyong'o", "Dubai"], "entity_types": ["person", "location"]}
{"sentence": "Dubai announces plans for sustainable city development.", "entity_names": ["Dubai"], "entity_types": ["location"]}
{"sentence": "Local organization in Dubai wins international award for environmental conservation efforts.", "entity_names": ["Dubai"], "entity_types": ["location"]}
{"sentence": "APPLE ANNOUNCES NEW IPHONE RELEASE DATE.", "entity_names": ["APPLE"], "entity_types": ["organization"]}
{"sentence": "TERRORIST ATTACK IN PARIS KILLS SEVERAL.", "entity_names": ["PARIS"], "entity_types": ["location"]}
{"sentence": "ELON MUSK LAUNCHES NEW SPACE EXPLORATION COMPANY.", "entity_names": ["ELON MUSK"], "entity_types": ["person"]}
{"sentence": "Pro-democracy activists in Hong Kong protest against new security law.", "entity_names": ["Hong Kong"], "entity_types": ["location"]}
{"sentence": "Apple Inc. announces plans to open a new store in Hong Kong .", "entity_names": ["Apple Inc.", "Hong Kong"], "entity_types": ["organization", "location"]}
{"sentence": "Tennis star Naomi Osaka to compete in tournament in Hong Kong .", "entity_names": ["Naomi Osaka", "Hong Kong"], "entity_types": ["person", "location"]}
{"sentence": "The mayor of Rio de Janeiro, Brazil, announced a new initiative to tackle crime in the city.", "entity_names": ["Rio de Janeiro", "Brazil"], "entity_types": ["location", "location"]}
{"sentence": "The company based in Rio de Janeiro, Brazil, is investing in renewable energy projects in the region.", "entity_names": ["Rio de Janeiro", "Brazil"], "entity_types": ["location", "location"]}
{"sentence": "Prominent singer from Rio de Janeiro, Brazil, to perform a charity concert for children in need.", "entity_names": ["Rio de Janeiro", "Brazil"], "entity_types": ["location", "location"]}
{"sentence": "St. Jude Children's Research Hospital announces breakthrough in childhood leukemia treatment.", "entity_names": ["St. Jude Children's Research Hospital"], "entity_types": ["organization"]}
{"sentence": "Researchers from St. Jude Children's Research Hospital collaborate with international team to study genetic causes of pediatric cancer.", "entity_names": ["St. Jude Children's Research Hospital"], "entity_types": ["organization"]}
{"sentence": "Fundraising event for St. Jude Children's Research Hospital raises over $1 million for childhood cancer research.", "entity_names": ["St. Jude Children's Research Hospital"], "entity_types": ["organization"]}
{"sentence": "The Sistine Chapel ceiling painted by Michelangelo Buonarroti has been restored to its former glory.", "entity_names": ["Sistine Chapel", "Michelangelo Buonarroti"], "entity_types": ["location", "person"]}
{"sentence": "The famous sculpture 'David' by Michelangelo Buonarroti is being moved to a new museum in Florence for display.", "entity_names": ["Michelangelo Buonarroti", "Florence"], "entity_types": ["person", "location"]}
{"sentence": "Michelangelo Buonarroti's masterpiece, the Pieta, attracts millions of visitors to Vatican City each year.", "entity_names": ["Michelangelo Buonarroti", "Pieta", "Vatican City"], "entity_types": ["person", "location", "location"]}
{"sentence": "Tesla announces plans to build new Gigafactory in Texas.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "Emma Watson appointed as ambassador for UN Women.", "entity_names": ["Emma Watson", "UN Women"], "entity_types": ["person", "organization"]}
{"sentence": "Japan to host 2020 Summer Olympics amid concerns over COVID-19 pandemic.", "entity_names": ["Japan", "2020 Summer Olympics"], "entity_types": ["location", "organization"]}
{"sentence": "Billie Eilish wins five Grammy Awards, including Album of the Year.", "entity_names": ["Billie Eilish"], "entity_types": ["person"]}
{"sentence": "Dolly Parton donates $1 million to fund COVID-19 vaccine research.", "entity_names": ["Dolly Parton"], "entity_types": ["person"]}
{"sentence": "New York City announces partnership with local organizations to provide free summer concerts in Central Park.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Elon Musk's SpaceX successfully launches latest batch of Starlink satellites.", "entity_names": ["Elon Musk", "SpaceX", "Starlink"], "entity_types": ["person", "organization", "organization"]}
{"sentence": "California wildfires rage on, forcing thousands to evacuate.", "entity_names": ["California"], "entity_types": ["location"]}
{"sentence": "Apple announces plans to release new iPhone model next month.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "Toronto, Canada, announces new restrictions amid rising COVID-19 cases.", "entity_names": ["Toronto", "Canada"], "entity_types": ["location", "location"]}
{"sentence": "Jacob Tobia signs book deal with major publishing house.", "entity_names": ["Jacob Tobia"], "entity_types": ["person"]}
{"sentence": "Canada reports record-breaking temperatures in Toronto.", "entity_names": ["Canada", "Toronto"], "entity_types": ["location", "location"]}
{"sentence": "The British Museum unveils new exhibit on ancient Mesopotamia.", "entity_names": ["British Museum"], "entity_types": ["organization"]}
{"sentence": "Famous British actor to donate personal art collection to the British Museum .", "entity_names": ["British Museum"], "entity_types": ["organization"]}
{"sentence": "Tourists flock to the British Museum to see famous Egyptian artifacts.", "entity_names": ["British Museum"], "entity_types": ["organization"]}
{"sentence": "English PEN launches campaign for freedom of expression.", "entity_names": ["English PEN"], "entity_types": ["organization"]}
{"sentence": "Human rights activist receives support from English PEN.", "entity_names": ["English PEN"], "entity_types": ["organization"]}
{"sentence": "English PEN's annual literary festival to feature renowned authors.", "entity_names": ["English PEN"], "entity_types": ["organization"]}
{"sentence": "Disney World announces plan to reopen with new safety measures in place.", "entity_names": ["Disney World"], "entity_types": ["organization"]}
{"sentence": "TED cancels 2021 in-person conference, moves to virtual format.", "entity_names": ["TED"], "entity_types": ["organization"]}
{"sentence": "Local family files lawsuit against Disney World for alleged negligence.", "entity_names": ["Disney World"], "entity_types": ["organization"]}
{"sentence": "Lockheed Martin wins $500 million contract to build new space shuttle.", "entity_names": ["Lockheed Martin"], "entity_types": ["organization"]}
{"sentence": "Yuri Gagarin, the first human to travel into space, made history in 1961.", "entity_names": ["Yuri Gagarin"], "entity_types": ["person"]}
{"sentence": "Investigation reveals new details about Lockheed Martin's involvement in government contracts.", "entity_names": ["Lockheed Martin"], "entity_types": ["organization"]}
{"sentence": "Tesla announces plans to build new manufacturing plant in Texas.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "Senator Harris to introduce bill addressing climate change and renewable energy.", "entity_names": ["Senator Harris"], "entity_types": ["person"]}
{"sentence": "Paris to implement new regulations on private car usage in city center.", "entity_names": ["Paris"], "entity_types": ["location"]}
{"sentence": "PepsiCo announces plans to acquire popular snack brand.", "entity_names": ["PepsiCo"], "entity_types": ["organization"]}
{"sentence": "General Mills sees increase in quarterly earnings.", "entity_names": ["General Mills"], "entity_types": ["organization"]}
{"sentence": "PepsiCo and General Mills team up for new joint marketing campaign.", "entity_names": ["PepsiCo", "General Mills"], "entity_types": ["organization", "organization"]}
{"sentence": "Larry Page steps down as CEO of Alphabet Inc.", "entity_names": ["Larry Page", "Alphabet Inc"], "entity_types": ["person", "organization"]}
{"sentence": "The United States Department of Defense announces new military contracts.", "entity_names": ["United States Department of Defense"], "entity_types": ["organization"]}
{"sentence": "Google co-founder Larry Page invests in new technology startup.", "entity_names": ["Google", "Larry Page"], "entity_types": ["organization", "person"]}
{"sentence": "Beyonc\u00e9 wins 4 Grammy Awards.", "entity_names": ["Beyonc\u00e9"], "entity_types": ["person"]}
{"sentence": "Greta Thunberg delivers speech at climate summit in New York.", "entity_names": ["Greta Thunberg", "New York"], "entity_types": ["person", "location"]}
{"sentence": "Moscow hosts international film festival.", "entity_names": ["Moscow"], "entity_types": ["location"]}
{"sentence": "Dr. Anthony Fauci warns of potential 'surge upon a surge' of COVID-19 cases.", "entity_names": ["Dr. Anthony Fauci"], "entity_types": ["person"]}
{"sentence": "Mumbai imposes strict lockdown measures in response to rising COVID-19 cases.", "entity_names": ["Mumbai"], "entity_types": ["location"]}
{"sentence": "Director of WHO commends Mumbai's efforts in combating COVID-19.", "entity_names": ["WHO", "Mumbai"], "entity_types": ["organization", "location"]}
{"sentence": "Carla Hall announced as the new CEO of XYZ Corporation.", "entity_names": ["Carla Hall", "XYZ Corporation"], "entity_types": ["person", "organization"]}
{"sentence": "\"Severe storm warning for the Midwest issued by the National Weather Service,\" explained meteorologist Carla Hall.", "entity_names": ["Midwest", "National Weather Service", "Carla Hall"], "entity_types": ["location", "organization", "person"]}
{"sentence": "Carla Hall, a renowned chef, to open a new restaurant in downtown Chicago.", "entity_names": ["Carla Hall", "Chicago"], "entity_types": ["person", "location"]}
{"sentence": "London, UK braces for heavy snowfall as winter storm approaches.", "entity_names": ["London, UK"], "entity_types": ["location"]}
{"sentence": "John Smith appointed as the new CEO of ABC Corporation.", "entity_names": ["John Smith", "ABC Corporation"], "entity_types": ["person", "organization"]}
{"sentence": "Thousands gather in Trafalgar Square to protest government's new policies.", "entity_names": ["Trafalgar Square"], "entity_types": ["location"]}
{"sentence": "The Uffizi Gallery in Florence is renowned for its impressive collection of Renaissance art.", "entity_names": ["Uffizi Gallery", "Florence"], "entity_types": ["location", "location"]}
{"sentence": "The Tate Modern in London will be hosting a special exhibition featuring contemporary African artists.", "entity_names": ["Tate Modern", "London"], "entity_types": ["organization", "location"]}
{"sentence": "Visitors can enjoy a virtual tour of The Uffizi Gallery from the comfort of their own homes.", "entity_names": ["Uffizi Gallery"], "entity_types": ["location"]}
{"sentence": "Tesla unveils new electric car model at annual conference.", "entity_names": ["Tesla"], "entity_types": ["organization"]}
{"sentence": "Prime Minister Johnson meets with President Macron to discuss trade agreements.", "entity_names": ["Prime Minister Johnson", "President Macron"], "entity_types": ["person", "person"]}
{"sentence": "Earthquake hits southern California, causing widespread damage.", "entity_names": ["southern California"], "entity_types": ["location"]}
{"sentence": "Andrea Agnelli named new chairman of European Club Association.", "entity_names": ["Andrea Agnelli", "European Club Association"], "entity_types": ["person", "organization"]}
{"sentence": "Investigation reveals corruption within FIFA led by Andrea Agnelli.", "entity_names": ["FIFA", "Andrea Agnelli"], "entity_types": ["organization", "person"]}
{"sentence": "Andrea Agnelli denies allegations of match-fixing in Serie A.", "entity_names": ["Andrea Agnelli", "Serie A"], "entity_types": ["person", "organization"]}
{"sentence": "The Dubai Mall announces a partnership with Zara for a new flagship store.", "entity_names": ["The Dubai Mall", "Zara"], "entity_types": ["location", "organization"]}
{"sentence": "Zara CEO set to visit The Dubai Mall for the launch event of their new collection.", "entity_names": ["Zara", "The Dubai Mall"], "entity_types": ["organization", "location"]}
{"sentence": "The Dubai Mall reports record-breaking sales following the opening of the new Zara store.", "entity_names": ["The Dubai Mall", "Zara"], "entity_types": ["location", "organization"]}
{"sentence": "Tesla unveils new electric pickup truck.", "entity_names": ["Tesla"], "entity_types": ["organization"]}
{"sentence": "Elon Musk announces plans for Mars colonization.", "entity_names": ["Elon Musk", "Mars"], "entity_types": ["person", "location"]}
{"sentence": "Elon Musk's SpaceX launches new batch of Starlink satellites.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "India reports record-breaking daily COVID-19 cases.", "entity_names": ["India"], "entity_types": ["location"]}
{"sentence": "Apple announces plans to build new manufacturing plant in Texas.", "entity_names": ["Apple", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "Kim Kardashian launches new fashion line.", "entity_names": ["Kim Kardashian"], "entity_types": ["person"]}
{"sentence": "Kim Kardashian donates to children's charity.", "entity_names": ["Kim Kardashian"], "entity_types": ["person"]}
{"sentence": "Kim Kardashian's reality show renewed for another season.", "entity_names": ["Kim Kardashian"], "entity_types": ["person"]}
{"sentence": "Lionel Messi scores hat-trick in victory over rival team.", "entity_names": ["Lionel Messi"], "entity_types": ["person"]}
{"sentence": "Tourists flock to Angkor Wat in Cambodia for breathtaking views of ancient temples.", "entity_names": ["Angkor Wat", "Cambodia"], "entity_types": ["location", "location"]}
{"sentence": "Tokyo hosts international summit with leaders from the US, China, and Japan in attendance.", "entity_names": ["Tokyo", "Japan"], "entity_types": ["location", "location"]}
{"sentence": "New York Times names new editor-in-chief.", "entity_names": ["New York Times"], "entity_types": ["organization"]}
{"sentence": "President Biden announces partnership with Pfizer for COVID-19 vaccine distribution.", "entity_names": ["President Biden", "Pfizer"], "entity_types": ["person", "organization"]}
{"sentence": "Wildfires continue to devastate parts of California.", "entity_names": ["California"], "entity_types": ["location"]}
{"sentence": "Aaron Rodgers leads the Packers to victory at Lambeau Field.", "entity_names": ["Aaron Rodgers", "Packers", "Lambeau Field"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Manchester United signs a new midfielder from Spain.", "entity_names": ["Manchester United", "Spain"], "entity_types": ["organization", "location"]}
{"sentence": "The mayor of Manchester delivers a speech at the town hall.", "entity_names": ["Manchester"], "entity_types": ["location"]}
{"sentence": "United Nations report highlights the urgent need for action on climate change.", "entity_names": ["United Nations"], "entity_types": ["organization"]}
{"sentence": "Paris Hilton's new documentary reveals untold stories from her personal life.", "entity_names": ["Paris Hilton"], "entity_types": ["person"]}
{"sentence": "The Walt Disney Company announces a new theme park opening in China.", "entity_names": ["Walt Disney Company", "China"], "entity_types": ["organization", "location"]}
{"sentence": "Lionsgate releases new blockbuster movie starring A-list actors.", "entity_names": ["Lionsgate"], "entity_types": ["organization"]}
{"sentence": "CEO of The Walt Disney Company steps down due to health reasons.", "entity_names": ["The Walt Disney Company"], "entity_types": ["organization"]}
{"sentence": "The International Criminal Court has announced the indictment of several war criminals in Eastern Europe.", "entity_names": ["International Criminal Court", "Eastern Europe"], "entity_types": ["organization", "location"]}
{"sentence": "The Organization for Security and Co-operation in Europe has deployed peacekeepers to the conflict zone in the Balkans.", "entity_names": ["Organization for Security and Co-operation in Europe", "Balkans"], "entity_types": ["organization", "location"]}
{"sentence": "Russia criticizes the International Criminal Court for its handling of a high-profile case involving a former military leader.", "entity_names": ["Russia", "International Criminal Court"], "entity_types": ["location", "organization"]}
{"sentence": "Vancouver, Canada, Prime Minister Trudeau announces new climate change initiative.", "entity_names": ["Vancouver", "Canada", "Trudeau"], "entity_types": ["location", "location", "person"]}
{"sentence": "Local organization hosts charity event in downtown Vancouver, Canada.", "entity_names": ["Vancouver", "Canada"], "entity_types": ["location", "location"]}
{"sentence": "Renowned chef opens new restaurant in Vancouver, Canada.", "entity_names": ["Vancouver", "Canada"], "entity_types": ["location", "location"]}
{"sentence": "Emma Watson appointed as ambassador for European Organization for Nuclear Research.", "entity_names": ["Emma Watson", "European Organization for Nuclear Research"], "entity_types": ["person", "organization"]}
{"sentence": "European Organization for Nuclear Research discovers new subatomic particle.", "entity_names": ["European Organization for Nuclear Research"], "entity_types": ["organization"]}
{"sentence": "Emma Watson to star in upcoming sci-fi film about particle physics.", "entity_names": ["Emma Watson"], "entity_types": ["person"]}
{"sentence": "Rio de Janeiro to host the 2023 World Youth Day.", "entity_names": ["Rio de Janeiro"], "entity_types": ["location"]}
{"sentence": "Brazil implements new environmental regulations to protect the Amazon rainforest.", "entity_names": ["Brazil", "Amazon rainforest"], "entity_types": ["location", "location"]}
{"sentence": "The International Labour Organization warns of a global economic downturn.", "entity_names": ["International Labour Organization"], "entity_types": ["organization"]}
{"sentence": "Serengeti National Park sees an increase in tourist visits.", "entity_names": ["Serengeti National Park"], "entity_types": ["location"]}
{"sentence": "Russian opposition leader Alexei Navalny released from prison.", "entity_names": ["Alexei Navalny"], "entity_types": ["person"]}
{"sentence": "New conservation efforts announced for Serengeti National Park.", "entity_names": ["Serengeti National Park"], "entity_types": ["location"]}
{"sentence": "General Mills announces the launch of a new line of organic cereals.", "entity_names": ["General Mills"], "entity_types": ["organization"]}
{"sentence": "The Coca-Cola Company reports a 10% increase in quarterly sales.", "entity_names": ["The Coca-Cola Company"], "entity_types": ["organization"]}
{"sentence": "A former executive at General Mills is appointed as the new CEO of a major food distribution company.", "entity_names": ["General Mills"], "entity_types": ["organization"]}
{"sentence": "New York City's mayor announces plan for large-scale public transportation improvements.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Apple Inc. unveils new line of iPhone models with advanced camera technology.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Amandla Stenberg to make a guest appearance at Castro District Pride event.", "entity_names": ["Amandla Stenberg", "Castro District"], "entity_types": ["person", "location"]}
{"sentence": "Protesters gather on Canal Street to demand justice for recent police brutality cases.", "entity_names": ["Canal Street"], "entity_types": ["location"]}
{"sentence": "Manchester United FC secures victory in the Champions League.", "entity_names": ["Manchester United FC", "Champions League"], "entity_types": ["organization", "organization"]}
{"sentence": "Officer Samantha Brown honored for her bravery in responding to the Chicago bank robbery.", "entity_names": ["Officer Samantha Brown", "Chicago"], "entity_types": ["person", "location"]}
{"sentence": "Director Steven Chen appointed as head of the cybersecurity task force.", "entity_names": ["Director Steven Chen"], "entity_types": ["person"]}
{"sentence": "Chicago Bulls defeat the Los Angeles Lakers in a thrilling overtime game.", "entity_names": ["Chicago", "Los Angeles Lakers"], "entity_types": ["location", "organization"]}
{"sentence": "Poetry Foundation announces new partnership with Moscow Institute of Literature.", "entity_names": ["Poetry Foundation", "Moscow Institute of Literature"], "entity_types": ["organization", "organization"]}
{"sentence": "Renowned poet from Moscow to visit Poetry Foundation for special reading event.", "entity_names": ["Moscow", "Poetry Foundation"], "entity_types": ["location", "organization"]}
{"sentence": "Moscow mayor attends opening ceremony of new Poetry Foundation branch in the city.", "entity_names": ["Moscow", "Poetry Foundation"], "entity_types": ["location", "organization"]}
{"sentence": "London Mayor announces new initiative to combat air pollution.", "entity_names": ["London", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "UK government announces plans to increase funding for mental health services.", "entity_names": ["UK"], "entity_types": ["location"]}
{"sentence": "New study finds that coffee consumption in London reaches all-time high.", "entity_names": ["London"], "entity_types": ["location"]}
{"sentence": "Mayo Clinic Health System expands its operations to Toronto, Canada.", "entity_names": ["Mayo Clinic Health System", "Toronto", "Canada"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Toronto, Canada implements new traffic regulations to reduce accidents.", "entity_names": ["Toronto", "Canada"], "entity_types": ["location", "location"]}
{"sentence": "Mayo Clinic Health System partners with leading hospitals in Toronto for cancer research.", "entity_names": ["Mayo Clinic Health System", "Toronto"], "entity_types": ["organization", "location"]}
{"sentence": "Meghan Markle launches her new charitable foundation, Archewell.", "entity_names": ["Meghan Markle", "Archewell"], "entity_types": ["person", "organization"]}
{"sentence": "Meghan Markle expresses her support for mental health awareness in an exclusive interview with Oprah Winfrey.", "entity_names": ["Meghan Markle", "Oprah Winfrey"], "entity_types": ["person", "person"]}
{"sentence": "The Duke and Duchess of Sussex, Prince Harry and Meghan Markle, attend the premiere of their new documentary film in London.", "entity_names": ["Duke and Duchess of Sussex", "Prince Harry", "Meghan Markle", "London"], "entity_types": ["person", "person", "person", "location"]}
{"sentence": "The Pacific Ring of Fire experiences a series of major earthquakes and volcanic eruptions.", "entity_names": ["The Pacific Ring of Fire"], "entity_types": ["location"]}
{"sentence": "Renowned astrophysicist Neil deGrasse Tyson makes a groundbreaking discovery in the search for extraterrestrial life.", "entity_names": ["Neil deGrasse Tyson"], "entity_types": ["person"]}
{"sentence": "Multiple countries in the Asia-Pacific region are affected by the ongoing seismic activities along The Pacific Ring of Fire.", "entity_names": ["The Pacific Ring of Fire"], "entity_types": ["location"]}
{"sentence": "Investment firm injects $500 million into Blue Origin for space tourism project.", "entity_names": ["Blue Origin"], "entity_types": ["organization"]}
{"sentence": "Blue Origin founder Jeff Bezos unveils plans for lunar lander.", "entity_names": ["Blue Origin", "Jeff Bezos"], "entity_types": ["organization", "person"]}
{"sentence": "Heavy rain and flooding cause damage in New York City.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Apple Inc. announces the release of its latest iPhone model.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "New luxury hotel planned for Dubai.", "entity_names": ["Dubai"], "entity_types": ["location"]}
{"sentence": "CEO of Dubai-based company arrested for fraud.", "entity_names": ["Dubai"], "entity_types": ["location"]}
{"sentence": "Dubai airport named busiest in the world.", "entity_names": ["Dubai"], "entity_types": ["location"]}
{"sentence": "The Tokyo Stock Exchange index reached a new high today.", "entity_names": ["Tokyo Stock Exchange"], "entity_types": ["organization"]}
{"sentence": "Investors on the Tokyo Stock Exchange were concerned about the impact of the trade war.", "entity_names": ["Tokyo Stock Exchange"], "entity_types": ["organization"]}
{"sentence": "Japanese Prime Minister visited the Tokyo Stock Exchange to discuss economic policies with investors.", "entity_names": ["Prime Minister", "Tokyo Stock Exchange"], "entity_types": ["person", "organization"]}
{"sentence": "The Venice Beach boardwalk is a popular tourist destination.", "entity_names": ["Venice Beach"], "entity_types": ["location"]}
{"sentence": "The Edinburgh Fringe Festival attracts artists and performers from around the world.", "entity_names": ["Edinburgh Fringe Festival"], "entity_types": ["organization"]}
{"sentence": "The Hollywood Foreign Press Association announced the nominees for this year's Golden Globe Awards.", "entity_names": ["Hollywood Foreign Press Association"], "entity_types": ["organization"]}
{"sentence": "The Coca-Cola Company reports record profits for the third quarter.", "entity_names": ["The Coca-Cola Company"], "entity_types": ["organization"]}
{"sentence": "The New York Times correspondent covers the latest developments in the Middle East.", "entity_names": ["The New York Times"], "entity_types": ["organization"]}
{"sentence": "Stocks plummet as The Coca-Cola Company announces a major restructuring plan.", "entity_names": ["The Coca-Cola Company"], "entity_types": ["organization"]}
{"sentence": "Prince Harry and Meghan Markle to step back as senior members of the royal family.", "entity_names": ["Prince Harry", "Meghan Markle", "royal family"], "entity_types": ["person", "person", "organization"]}
{"sentence": "Daniel Martinez elected as the new president of the student council.", "entity_names": ["Daniel Martinez", "student council"], "entity_types": ["person", "organization"]}
{"sentence": "New York City to implement new COVID-19 restrictions as cases surge.", "entity_names": ["New York City", "COVID-19"], "entity_types": ["location", "organization"]}
{"sentence": "J.K. Rowling announces release date for new fantasy novel.", "entity_names": ["J.K. Rowling"], "entity_types": ["person"]}
{"sentence": "Apple Inc. launches new line of smart home devices.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "John Smith elected as mayor of New York City.", "entity_names": ["John Smith", "New York City"], "entity_types": ["person", "location"]}
{"sentence": "Northrop Grumman wins $298 million deal for missile defense satellites.", "entity_names": ["Northrop Grumman"], "entity_types": ["organization"]}
{"sentence": "CNES partners with SpaceX for satellite launch.", "entity_names": ["CNES", "SpaceX"], "entity_types": ["organization", "organization"]}
{"sentence": "New CEO appointed at Northrop Grumman.", "entity_names": ["Northrop Grumman"], "entity_types": ["organization"]}
{"sentence": "Disneyland in Anaheim reopens with limited capacity after pandemic shutdown.", "entity_names": ["Disneyland", "Anaheim"], "entity_types": ["organization", "location"]}
{"sentence": "Dwayne \"The Rock\" Johnson to star in new action thriller for Netflix.", "entity_names": ["Dwayne \"The Rock\" Johnson", "Netflix"], "entity_types": ["person", "organization"]}
{"sentence": "Anaheim residents protest new zoning regulations for downtown area.", "entity_names": ["Anaheim"], "entity_types": ["location"]}
{"sentence": "The Environmental Working Group released a report on the impact of climate change on agriculture.", "entity_names": ["Environmental Working Group"], "entity_types": ["organization"]}
{"sentence": "Barack Obama delivered a speech on foreign policy at the United Nations General Assembly.", "entity_names": ["Barack Obama", "United Nations General Assembly"], "entity_types": ["person", "organization"]}
{"sentence": "Concerns about water contamination in Flint, Michigan continue to grow.", "entity_names": ["Flint, Michigan"], "entity_types": ["location"]}
{"sentence": "Dolce & Gabbana launches new fragrance line.", "entity_names": ["Dolce & Gabbana"], "entity_types": ["organization"]}
{"sentence": "Italian fashion house Dolce & Gabbana to open flagship store in New York City.", "entity_names": ["Dolce & Gabbana", "New York City"], "entity_types": ["organization", "location"]}
{"sentence": "Celebrities spotted wearing Dolce & Gabbana designs at the Oscars.", "entity_names": ["Dolce & Gabbana"], "entity_types": ["organization"]}
{"sentence": "Christian Meunier appointed as the new CEO of Ford.", "entity_names": ["Christian Meunier", "Ford"], "entity_types": ["person", "organization"]}
{"sentence": "The earthquake in California caused widespread damage.", "entity_names": ["California"], "entity_types": ["location"]}
{"sentence": "Microsoft announces plans to acquire a popular gaming company.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}
{"sentence": "Russian Ministry of Defence accuses United Kingdom of airspace violation.", "entity_names": ["Russian Ministry of Defence", "United Kingdom"], "entity_types": ["organization", "location"]}
{"sentence": "United Kingdom pledges humanitarian aid for Kabul reconstruction efforts.", "entity_names": ["United Kingdom", "Kabul"], "entity_types": ["location", "location"]}
{"sentence": "NASA's Lunar Gateway project moves forward with new partnership.", "entity_names": ["NASA", "Lunar Gateway"], "entity_types": ["organization", "organization"]}
{"sentence": "Elon Musk's SpaceX to collaborate with Mars Mission Control for upcoming mission.", "entity_names": ["Elon Musk", "SpaceX", "Mars Mission Control"], "entity_types": ["person", "organization", "organization"]}
{"sentence": "International Space Station crew prepares for transfer to Lunar Gateway.", "entity_names": ["International Space Station", "Lunar Gateway"], "entity_types": ["organization", "location"]}
{"sentence": "Ford Motor Company announces plans to open new production facility in Mexico.", "entity_names": ["Ford Motor Company", "Mexico"], "entity_types": ["organization", "location"]}
{"sentence": "The White House confirms President Biden's plan to increase infrastructure spending.", "entity_names": ["the White House", "President Biden"], "entity_types": ["organization", "person"]}
{"sentence": "Amazon.com, Inc. reports record-breaking sales during the holiday season.", "entity_names": ["Amazon.com, Inc.", "holiday season"], "entity_types": ["organization", "organization"]}
{"sentence": "The Sierra Club launches new campaign to protect endangered species in the Amazon.", "entity_names": ["Sierra Club"], "entity_types": ["organization"]}
{"sentence": "Southern Poverty Law Center files lawsuit against hate group for inciting violence.", "entity_names": ["Southern Poverty Law Center"], "entity_types": ["organization"]}
{"sentence": "Doctors Without Borders sends medical team to assist refugees in war-torn region.", "entity_names": ["Doctors Without Borders"], "entity_types": ["organization"]}
{"sentence": "The annual summit in Istanbul brings together world leaders and business executives.", "entity_names": ["Istanbul"], "entity_types": ["location"]}
{"sentence": "The mayor of Istanbul unveils plans for a new public transportation system.", "entity_names": ["Istanbul"], "entity_types": ["location"]}
{"sentence": "Several large corporations, including Microsoft and Google, are investing in the new tech hub in Istanbul .", "entity_names": ["Microsoft", "Google", "Istanbul"], "entity_types": ["organization", "organization", "location"]}
{"sentence": "Mayor Angela Martinez delivers opening speech at city council meeting.", "entity_names": ["Mayor Angela Martinez"], "entity_types": ["person"]}
{"sentence": "Rio de Janeiro welcomes tourists with new safety measures in place.", "entity_names": ["Rio de Janeiro"], "entity_types": ["location"]}
{"sentence": "Reykjavik experiences record-breaking temperatures in the midst of winter.", "entity_names": ["Reykjavik"], "entity_types": ["location"]}
{"sentence": "Global organization announces plans for climate change summit in Reykjavik.", "entity_names": ["global organization", "Reykjavik"], "entity_types": ["organization", "location"]}
{"sentence": "Tesla announces plans to build new production facility in Germany.", "entity_names": ["Tesla", "Germany"], "entity_types": ["organization", "location"]}
{"sentence": "Dr. Anthony Fauci warns of potential surge in COVID-19 cases.", "entity_names": ["Anthony Fauci"], "entity_types": ["person"]}
{"sentence": "The United Nations calls for ceasefire in conflict-torn region.", "entity_names": ["The United Nations"], "entity_types": ["organization"]}
{"sentence": "The trial of Joan of Arc began on February 21, 1431.", "entity_names": ["Joan of Arc"], "entity_types": ["person"]}
{"sentence": "The city of Orleans, where Joan of Arc achieved a significant victory, celebrates her legacy every year.", "entity_names": ["Orleans", "Joan of Arc"], "entity_types": ["location", "person"]}
{"sentence": "A new documentary explores the life and legacy of Joan of Arc, the famed French heroine.", "entity_names": ["Joan of Arc"], "entity_types": ["person"]}
{"sentence": "Atlanta-based company to expand its operations in Georgia.", "entity_names": ["Atlanta", "Georgia"], "entity_types": ["location", "location"]}
{"sentence": "Pan American Health Organization warns of potential outbreak in Atlanta.", "entity_names": ["Pan American Health Organization", "Atlanta"], "entity_types": ["organization", "location"]}
{"sentence": "Georgia native appointed as new CEO of multinational organization.", "entity_names": ["Georgia"], "entity_types": ["location"]}
{"sentence": "National Geographic Society plans expedition to Antarctica.", "entity_names": ["National Geographic Society", "Antarctica"], "entity_types": ["organization", "location"]}
{"sentence": "Pope Francis visits Mount Kilimanjaro in Tanzania.", "entity_names": ["Pope Francis", "Mount Kilimanjaro", "Tanzania"], "entity_types": ["person", "location", "location"]}
{"sentence": "Tanzanian government to launch conservation effort for Mount Kilimanjaro.", "entity_names": ["Mount Kilimanjaro"], "entity_types": ["location"]}
{"sentence": "Search and rescue teams continue efforts to locate missing hikers at the Grand Canyon.", "entity_names": ["Grand Canyon"], "entity_types": ["location"]}
{"sentence": "The famous musician from Chicago will be performing a live concert next week.", "entity_names": ["Chicago"], "entity_types": ["location"]}
{"sentence": "Tesla CEO Elon Musk announces new electric vehicle model.", "entity_names": ["Tesla", "Elon Musk"], "entity_types": ["organization", "person"]}
{"sentence": "New York City to implement new recycling program in all public parks.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Federal Bureau of Investigation announces new cybercrime task force in partnership with major tech companies.", "entity_names": ["Federal Bureau of Investigation"], "entity_types": ["organization"]}
{"sentence": "U.S. Senator Warren proposes bill to increase funding for rural infrastructure projects.", "entity_names": ["U.S. Senator Warren"], "entity_types": ["person"]}
{"sentence": "New York City to implement new recycling program in collaboration with environmental organizations.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Jennifer Lopez announces new world tour.", "entity_names": ["Jennifer Lopez"], "entity_types": ["person"]}
{"sentence": "Record-breaking attendance at Jennifer Lopez concert in New York City.", "entity_names": ["Jennifer Lopez", "New York City"], "entity_types": ["person", "location"]}
{"sentence": "Jennifer Lopez signs multi-million dollar deal with streaming platform.", "entity_names": ["Jennifer Lopez"], "entity_types": ["person"]}
{"sentence": "The Getty Center presents a new exhibition on Renaissance art.", "entity_names": ["The Getty Center"], "entity_types": ["organization"]}
{"sentence": "Visitors enjoy the scenic views and architecture of The Getty Center in Los Angeles.", "entity_names": ["The Getty Center"], "entity_types": ["location"]}
{"sentence": "The Getty Center announces a partnership with local schools to provide art education programs.", "entity_names": ["The Getty Center"], "entity_types": ["organization"]}
{"sentence": "World Health Organization warns of the potential risks of a new strain of the flu.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}
{"sentence": "Famous actor Tom Hanks donates to the World Health Organization's relief efforts in Africa.", "entity_names": ["Tom Hanks", "World Health Organization"], "entity_types": ["person", "organization"]}
{"sentence": "The United Nations and the World Health Organization collaborate on a global campaign for vaccinations.", "entity_names": ["United Nations", "World Health Organization"], "entity_types": ["organization", "organization"]}
{"sentence": "Serena Williams defeats opponent in straight sets to advance to the next round of the tournament.", "entity_names": ["Serena Williams"], "entity_types": ["person"]}
{"sentence": "Major earthquake hits California, causing widespread damage and power outages.", "entity_names": ["California"], "entity_types": ["location"]}
{"sentence": "Tesla Inc. announces plans to open a new manufacturing plant in Texas.", "entity_names": ["Tesla Inc.", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "Elon Musk unveils plans for new SpaceX mission to Mars", "entity_names": ["Elon Musk", "SpaceX", "Mars"], "entity_types": ["person", "organization", "location"]}
{"sentence": "New York City Mayor announces public transportation improvements", "entity_names": ["New York City", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "Apple Inc. reports record-breaking quarterly earnings", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Student Emma Williams wins prestigious scholarship.", "entity_names": ["Emma Williams"], "entity_types": ["person"]}
{"sentence": "Martin Luther King Jr. memorial unveiled in Washington, D.C.", "entity_names": ["Martin Luther King Jr.", "Washington, D.C."], "entity_types": ["person", "location"]}
{"sentence": "Amazon announces record-breaking profits for the quarter.", "entity_names": ["Amazon"], "entity_types": ["organization"]}
{"sentence": "Tech giant Apple Inc. is set to release its latest iPhone model next month.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "President Biden announces plans to increase funding for infrastructure projects.", "entity_names": ["President Biden"], "entity_types": ["person"]}
{"sentence": "Wildfires continue to ravage parts of California, forcing thousands to evacuate their homes.", "entity_names": ["California"], "entity_types": ["location"]}
{"sentence": "Officer William Brown awarded Medal of Valor for heroic act.", "entity_names": ["Officer William Brown"], "entity_types": ["person"]}
{"sentence": "New York City to invest $10 million in affordable housing initiative.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Tech giant Apple announces record-breaking profits for the quarter.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "Tesla to open new Gigafactory in India.", "entity_names": ["Tesla", "India"], "entity_types": ["organization", "location"]}
{"sentence": "Elon Musk announces plans for SpaceX lunar mission.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "European Union leaders plan summit on climate change.", "entity_names": ["European Union"], "entity_types": ["organization"]}
{"sentence": "The National Association for the Advancement of Colored People holds virtual event to celebrate Black History Month.", "entity_names": ["National Association for the Advancement of Colored People"], "entity_types": ["organization"]}
{"sentence": "Walt Disney Studios announces plans for new streaming service featuring original content.", "entity_names": ["Walt Disney Studios"], "entity_types": ["organization"]}
{"sentence": "Celebrity chef Gordon Ramsay opens new restaurant in downtown Los Angeles.", "entity_names": ["Gordon Ramsay", "Los Angeles"], "entity_types": ["person", "location"]}
{"sentence": "New York City Mayor announces new initiative to combat homelessness.", "entity_names": ["New York City", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "United States Senate passes new infrastructure bill.", "entity_names": ["United States", "Senate"], "entity_types": ["location", "organization"]}
{"sentence": "New York City Marathon organizers unveil new route for upcoming race.", "entity_names": ["New York City", "Marathon organizers"], "entity_types": ["location", "organization"]}
{"sentence": "Johnson & Johnson faces lawsuits over baby powder", "entity_names": ["Johnson & Johnson"], "entity_types": ["organization"]}
{"sentence": "Investigation reveals Johnson & Johnson knew about asbestos in talcum powder", "entity_names": ["Johnson & Johnson"], "entity_types": ["organization"]}
{"sentence": "Johnson & Johnson announces new CEO", "entity_names": ["Johnson & Johnson", "CEO"], "entity_types": ["organization", "person"]}
{"sentence": "The Metropolitan Museum of Art announces new exhibit featuring works by Vincent van Gogh.", "entity_names": ["The Metropolitan Museum of Art", "Vincent van Gogh"], "entity_types": ["organization", "person"]}
{"sentence": "Thousands protest in front of The Metropolitan Museum of Art over controversial exhibit.", "entity_names": ["The Metropolitan Museum of Art"], "entity_types": ["organization"]}
{"sentence": "The Metropolitan Museum of Art to open new branch in Paris next year.", "entity_names": ["The Metropolitan Museum of Art", "Paris"], "entity_types": ["organization", "location"]}
{"sentence": "The Food and Drug Administration approved a new drug for the treatment of diabetes.", "entity_names": ["Food and Drug Administration"], "entity_types": ["organization"]}
{"sentence": "The Food and Drug Administration is investigating cases of salmonella outbreaks in several states.", "entity_names": ["Food and Drug Administration"], "entity_types": ["organization"]}
{"sentence": "The Food and Drug Administration issued a warning about the potential side effects of a popular weight loss supplement.", "entity_names": ["Food and Drug Administration"], "entity_types": ["organization"]}
{"sentence": "Cape Town experiences a surge in tourism as travel restrictions are lifted.", "entity_names": ["Cape Town"], "entity_types": ["location"]}
{"sentence": "Celebrity chef Nigella Lawson launches new cooking show on streaming platform.", "entity_names": ["Nigella Lawson"], "entity_types": ["person"]}
{"sentence": "Local organization in Cape Town hosts charity fundraiser for homeless population.", "entity_names": ["Cape Town"], "entity_types": ["location"]}
{"sentence": "On Wednesday, Sony Corporation announced a new partnership with a South Korean technology company.", "entity_names": ["Sony Corporation", "South Korean"], "entity_types": ["organization", "location"]}
{"sentence": "The CEO of Sony Corporation, John Smith, spoke at the annual technology conference in Las Vegas.", "entity_names": ["Sony Corporation", "John Smith", "Las Vegas"], "entity_types": ["organization", "person", "location"]}
{"sentence": "Major retailers across the country reported a surge in sales of the latest Sony Corporation gaming console.", "entity_names": ["Sony Corporation"], "entity_types": ["organization"]}
{"sentence": "NASA successfully launches new satellite into orbit.", "entity_names": ["NASA"], "entity_types": ["organization"]}
{"sentence": "Amazon.com, Inc. announces plans for new headquarters in Rome.", "entity_names": ["Amazon.com, Inc.", "Rome"], "entity_types": ["organization", "location"]}
{"sentence": "Rome mayor meets with local business leaders to discuss economic recovery.", "entity_names": ["Rome"], "entity_types": ["location"]}
{"sentence": "Captain Sarah Wilson awarded Medal of Honor for bravery in combat.", "entity_names": ["Captain Sarah Wilson"], "entity_types": ["person"]}
{"sentence": "New York City marathon canceled due to COVID-19 concerns.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "World Health Organization declares global pandemic for COVID-19.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}
{"sentence": "Olivia Morales has been appointed as the new CEO of a leading tech company.", "entity_names": ["Olivia Morales"], "entity_types": ["person"]}
{"sentence": "The city of Boston is preparing for a major snowstorm this weekend, with up to 12 inches of snow expected.", "entity_names": ["Boston"], "entity_types": ["location"]}
{"sentence": "Olivia Morales , the renowned environmental activist, was awarded the Nobel Peace Prize for her contributions to sustainability efforts.", "entity_names": ["Olivia Morales"], "entity_types": ["person"]}
{"sentence": "The Nobel Foundation announces 2021 Peace Prize winner.", "entity_names": ["The Nobel Foundation"], "entity_types": ["organization"]}
{"sentence": "The Nobel Foundation ceremony to be held in Stockholm.", "entity_names": ["The Nobel Foundation", "Stockholm"], "entity_types": ["organization", "location"]}
{"sentence": "Former US President Obama awarded The Nobel Foundation Peace Prize.", "entity_names": ["Obama", "The Nobel Foundation"], "entity_types": ["person", "organization"]}
{"sentence": "Mexico City hosts international technology conference.", "entity_names": ["Mexico City"], "entity_types": ["location"]}
{"sentence": "President of Mexico visits China for trade talks.", "entity_names": ["Mexico", "China"], "entity_types": ["location", "location"]}
{"sentence": "Major earthquake strikes central Mexico, causing widespread damage.", "entity_names": ["Mexico"], "entity_types": ["location"]}
{"sentence": "Tensions rise as refugees continue to pour into Jerusalem from neighboring countries.", "entity_names": ["Jerusalem"], "entity_types": ["location"]}
{"sentence": "CEO of tech giant announces plans for new headquarters in downtown Jerusalem .", "entity_names": ["Jerusalem"], "entity_types": ["location"]}
{"sentence": "Local Jerusalem artist wins prestigious award for innovative sculptures.", "entity_names": ["Jerusalem"], "entity_types": ["location"]}
{"sentence": "The Intergovernmental Panel on Climate Change releases a report warning of dire consequences if global temperatures continue to rise.", "entity_names": ["The Intergovernmental Panel on Climate Change"], "entity_types": ["organization"]}
{"sentence": "Cape Town, South Africa experiences severe drought, leading to water shortages and strict usage restrictions.", "entity_names": ["Cape Town", "South Africa"], "entity_types": ["location", "location"]}
{"sentence": "The mayor of Cape Town, South Africa announces plans for a new public transportation initiative to reduce traffic congestion in the city center.", "entity_names": ["Cape Town", "South Africa"], "entity_types": ["location", "location"]}
{"sentence": "Los Angeles Mayor announces new affordable housing initiative.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}
{"sentence": "Sundance Institute partners with Royal Academy of Arts for new film program.", "entity_names": ["Sundance Institute", "Royal Academy of Arts"], "entity_types": ["organization", "organization"]}
{"sentence": "Historical Preservation Society raises funds for new exhibit.", "entity_names": ["Historical Preservation Society"], "entity_types": ["organization"]}
{"sentence": "St. Mary's Hospital announces plan to expand emergency department.", "entity_names": ["St. Mary's Hospital"], "entity_types": ["organization"]}
{"sentence": "Emily Rodriguez elected as president of local charity organization.", "entity_names": ["Emily Rodriguez"], "entity_types": ["person"]}
{"sentence": "Scientists discover new species in The Coral Triangle.", "entity_names": ["The Coral Triangle"], "entity_types": ["location"]}
{"sentence": "Antarctica experiences record high temperatures.", "entity_names": ["Antarctica"], "entity_types": ["location"]}
{"sentence": "Beyonc\u00e9 releases new single, breaking streaming records.", "entity_names": ["Beyonc\u00e9"], "entity_types": ["person"]}
{"sentence": "Vladimir Putin meets with world leaders at the G20 summit.", "entity_names": ["Vladimir Putin", "G20 summit"], "entity_types": ["person", "organization"]}
{"sentence": "Beyonc\u00e9 to headline major music festival in New York City.", "entity_names": ["Beyonc\u00e9", "New York City"], "entity_types": ["person", "location"]}
{"sentence": "Starbucks Corporation announces plan to open 500 new locations in China.", "entity_names": ["Starbucks Corporation", "China"], "entity_types": ["organization", "location"]}
{"sentence": "Beyonc\u00e9's new album tops the charts for the sixth consecutive week.", "entity_names": ["Beyonc\u00e9"], "entity_types": ["person"]}
{"sentence": "Investors react positively to the news of Starbucks Corporation's expansion into India.", "entity_names": ["Starbucks Corporation", "India"], "entity_types": ["organization", "location"]}
{"sentence": "National Immigration Forum calls for reform in immigration policies.", "entity_names": ["National Immigration Forum"], "entity_types": ["organization"]}
{"sentence": "Athens, Greece experiences record high temperatures this summer.", "entity_names": ["Athens", "Greece"], "entity_types": ["location", "location"]}
{"sentence": "New York City Mayor announces plans for new affordable housing initiative.", "entity_names": ["New York City", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "New York City Marathon attracts over 50,000 runners.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Amazon announces plans to open new headquarters in Texas.", "entity_names": ["Amazon", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "Malala Yousafzai appointed as the youngest-ever UN Messenger of Peace.", "entity_names": ["Malala Yousafzai", "UN"], "entity_types": ["person", "organization"]}
{"sentence": "World Health Organization reports significant decrease in global malaria cases.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}
{"sentence": "Greta Thunberg to speak at United Nations Climate Action Summit.", "entity_names": ["Greta Thunberg", "United Nations"], "entity_types": ["person", "organization"]}
{"sentence": "The World Health Organization issues new guidelines on COVID-19 prevention.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}
{"sentence": "Dr. Tedros Adhanom Ghebreyesus urges global cooperation in vaccine distribution.", "entity_names": ["Dr. Tedros Adhanom Ghebreyesus"], "entity_types": ["person"]}
{"sentence": "Africa sees increase in cases of malaria, warns World Health Organization.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}
{"sentence": "LeBron James signs a four-year contract with the Los Angeles Lakers.", "entity_names": ["LeBron James", "Los Angeles Lakers"], "entity_types": ["person", "organization"]}
{"sentence": "Pope Francis meets with world leaders to discuss climate change and global poverty.", "entity_names": ["Pope Francis"], "entity_types": ["person"]}
{"sentence": "The new film starring LeBron James as a basketball player breaks box office records.", "entity_names": ["LeBron James"], "entity_types": ["person"]}
{"sentence": "Major League Soccer announces expansion team in Las Vegas.", "entity_names": ["Major League Soccer", "Las Vegas"], "entity_types": ["organization", "location"]}
{"sentence": "Swimmer Katie Ledecky sets new world record in 1500m freestyle.", "entity_names": ["Katie Ledecky"], "entity_types": ["person"]}
{"sentence": "New study shows benefits of exercise on mental health.", "entity_names": [], "entity_types": []}
{"sentence": "Taylor Johnson wins gold medal in the 100m sprint at the Olympics.", "entity_names": ["Taylor Johnson"], "entity_types": ["person"]}
{"sentence": "Tourists stranded in the Grand Canyon due to severe weather conditions.", "entity_names": ["Grand Canyon"], "entity_types": ["location"]}
{"sentence": "Environmental organization launches campaign to preserve the Grand Canyon's natural beauty.", "entity_names": ["organization", "Grand Canyon"], "entity_types": ["organization", "location"]}
{"sentence": "Kia Corporation launches new electric vehicle model in European market.", "entity_names": ["Kia Corporation", "European"], "entity_types": ["organization", "location"]}
{"sentence": "Stocks of Kia Corporation surge after announcement of record-breaking sales.", "entity_names": ["Kia Corporation"], "entity_types": ["organization"]}
{"sentence": "Kia Corporation CEO predicts strong revenue growth for the next fiscal year.", "entity_names": ["Kia Corporation", "CEO"], "entity_types": ["organization", "person"]}
{"sentence": "The Salvation Army plans to expand its homeless shelter services in downtown Chicago.", "entity_names": ["Salvation Army", "Chicago"], "entity_types": ["organization", "location"]}
{"sentence": "The local Salvation Army chapter will hold a fundraiser to support those affected by the recent natural disaster.", "entity_names": ["Salvation Army"], "entity_types": ["organization"]}
{"sentence": "Former NBA player volunteers at Salvation Army soup kitchen.", "entity_names": ["NBA", "Salvation Army"], "entity_types": ["organization", "organization"]}
{"sentence": "Israel announces plans to build 2,000 new settlement units in the West Bank.", "entity_names": ["Israel"], "entity_types": ["location"]}
{"sentence": "Jerusalem celebrates annual Festival of Light with stunning light displays.", "entity_names": ["Jerusalem"], "entity_types": ["location"]}
{"sentence": "Israeli Prime Minister meets with UN Secretary-General to discuss peace talks.", "entity_names": ["Israeli Prime Minister", "UN"], "entity_types": ["person", "organization"]}
{"sentence": "Wildfires continue to ravage Northern California, forcing thousands to evacuate.", "entity_names": ["Northern California"], "entity_types": ["location"]}
{"sentence": "Apple unveils new iPhone with advanced facial recognition technology.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "The National Queer Asian Pacific Islander Alliance advocates for LGBTQ rights.", "entity_names": ["National Queer Asian Pacific Islander Alliance"], "entity_types": ["organization"]}
{"sentence": "Equality Federation launches new initiative to promote equality in the workplace.", "entity_names": ["Equality Federation"], "entity_types": ["organization"]}
{"sentence": "The National Queer Asian Pacific Islander Alliance and Equality Federation partner for Pride Month events.", "entity_names": ["National Queer Asian Pacific Islander Alliance", "Equality Federation"], "entity_types": ["organization", "organization"]}
{"sentence": "Nestle reports record-breaking quarterly profits, exceeding analyst expectations.", "entity_names": ["Nestle"], "entity_types": ["organization"]}
{"sentence": "The CEO of Nestle, Mark Schneider, announces a new sustainability initiative aimed at reducing plastic waste.", "entity_names": ["Nestle", "Mark Schneider"], "entity_types": ["organization", "person"]}
{"sentence": "Nestle invests $100 million in new manufacturing facility in South Carolina, creating 300 new jobs.", "entity_names": ["Nestle", "South Carolina"], "entity_types": ["organization", "location"]}
{"sentence": "Turkey opens new international airport in Istanbul.", "entity_names": ["Turkey", "Istanbul"], "entity_types": ["location", "location"]}
{"sentence": "President Erdogan announces new economic reforms to boost growth in Istanbul.", "entity_names": ["President Erdogan", "Istanbul"], "entity_types": ["person", "location"]}
{"sentence": "Istanbul-based company secures major contract for infrastructure development in the city.", "entity_names": ["Istanbul"], "entity_types": ["location"]}
{"sentence": "German Chancellor Angela Merkel meets with French President Emmanuel Macron to discuss economic recovery plan.", "entity_names": ["Angela Merkel", "Emmanuel Macron"], "entity_types": ["person", "person"]}
{"sentence": "Angela Merkel delivers speech at United Nations General Assembly.", "entity_names": ["Angela Merkel", "United Nations General Assembly"], "entity_types": ["person", "organization"]}
{"sentence": "European Union leaders agree on climate action plan during Angela Merkel's tenure as Council President.", "entity_names": ["European Union", "Angela Merkel"], "entity_types": ["organization", "person"]}
{"sentence": "The RSPB celebrates the success of their conservation project in protecting endangered bird species.", "entity_names": ["RSPB"], "entity_types": ["organization"]}
{"sentence": "Defenders of Wildlife files a lawsuit against the government over the protection of endangered species habitats.", "entity_names": ["Defenders of Wildlife"], "entity_types": ["organization"]}
{"sentence": "The partnership between RSPB and local communities leads to the restoration of wildlife habitats in the area.", "entity_names": ["RSPB"], "entity_types": ["organization"]}
{"sentence": "Emma Watson appointed as goodwill ambassador for UN Women.", "entity_names": ["Emma Watson", "UN Women"], "entity_types": ["person", "organization"]}
{"sentence": "Emma Watson's new movie Breaks Box Office Records.", "entity_names": ["Emma Watson"], "entity_types": ["person"]}
{"sentence": "Emma Watson spotted at charity event in Los Angeles.", "entity_names": ["Emma Watson", "Los Angeles"], "entity_types": ["person", "location"]}
{"sentence": "Gigi Hadid walks the runway for top designer in Paris Fashion Week.", "entity_names": ["Gigi Hadid", "Paris"], "entity_types": ["person", "location"]}
{"sentence": "Gigi Hadid announces partnership with UNICEF to support children's education.", "entity_names": ["Gigi Hadid", "UNICEF"], "entity_types": ["person", "organization"]}
{"sentence": "Gigi Hadid spotted at Los Angeles airport with rumored new boyfriend.", "entity_names": ["Gigi Hadid", "Los Angeles"], "entity_types": ["person", "location"]}
{"sentence": "Anthony Nguyen appointed as CEO of tech company.", "entity_names": ["Anthony Nguyen"], "entity_types": ["person"]}
{"sentence": "Paris, France announces new environmental initiatives.", "entity_names": ["Paris", "France"], "entity_types": ["location", "location"]}
{"sentence": "Alzheimer's Association advocates for increased funding for research.", "entity_names": ["Alzheimer's Association"], "entity_types": ["organization"]}
{"sentence": "Brazilian President to visit United States next week for trade talks.", "entity_names": ["Brazilian", "United States"], "entity_types": ["organization", "location"]}
{"sentence": "Study finds link between climate change and increased natural disasters.", "entity_names": [], "entity_types": []}
{"sentence": "Apple CEO Tim Cook unveils new iPhone model.", "entity_names": ["Apple", "Tim Cook"], "entity_types": ["organization", "person"]}
{"sentence": "Wildfires spread across California, forcing thousands to evacuate.", "entity_names": ["California"], "entity_types": ["location"]}
{"sentence": "United Nations condemns human rights violations in Syria.", "entity_names": ["United Nations", "Syria"], "entity_types": ["organization", "location"]}
{"sentence": "Carlos Ghosn, former Nissan chairman, flees to Lebanon to escape justice.", "entity_names": ["Carlos Ghosn", "Nissan", "Lebanon"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Investigation reveals evidence of financial misconduct by Carlos Ghosn during his time at Nissan.", "entity_names": ["Carlos Ghosn", "Nissan"], "entity_types": ["person", "organization"]}
{"sentence": "Carlos Ghosn denies allegations of financial wrongdoing in press conference.", "entity_names": ["Carlos Ghosn"], "entity_types": ["person"]}
{"sentence": "Red Cross provides aid to earthquake-stricken areas.", "entity_names": ["Red Cross"], "entity_types": ["organization"]}
{"sentence": "Joan of Arc statue unveiled in Paris.", "entity_names": ["Joan of Arc", "Paris"], "entity_types": ["person", "location"]}
{"sentence": "New director appointed for Red Cross humanitarian efforts.", "entity_names": ["Red Cross"], "entity_types": ["organization"]}
{"sentence": "Marine Sergeant Adam Carter receives Medal of Honor for valor in combat.", "entity_names": ["Marine Sergeant Adam Carter"], "entity_types": ["person"]}
{"sentence": "International Rescue Committee provides aid to refugees in war-torn region.", "entity_names": ["International Rescue Committee"], "entity_types": ["organization"]}
{"sentence": "New York City Marathon attracts runners from around the world.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Germany's Chancellor Merkel addresses economic policy in Berlin.", "entity_names": ["Germany", "Merkel", "Berlin"], "entity_types": ["location", "person", "location"]}
{"sentence": "Berlin-based startup secures $10 million in funding.", "entity_names": ["Berlin"], "entity_types": ["location"]}
{"sentence": "Protests erupt in Berlin over new immigration laws.", "entity_names": ["Berlin"], "entity_types": ["location"]}
{"sentence": "The White House plans to unveil new infrastructure proposal next week.", "entity_names": ["the White House"], "entity_types": ["organization"]}
{"sentence": "Environmentalists urge action to protect the Amazon Rainforest from deforestation.", "entity_names": ["Amazon Rainforest"], "entity_types": ["location"]}
{"sentence": "Apple Inc. announces new product launch event in California.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "Islamic scholar Mufti Menk delivers lecture at Wittenberg University.", "entity_names": ["Mufti Menk", "Wittenberg University"], "entity_types": ["person", "organization"]}
{"sentence": "Wittenberg, Germany celebrates 500th anniversary of Reformation.", "entity_names": ["Wittenberg", "Germany"], "entity_types": ["location", "location"]}
{"sentence": "Mufti Menk's charity organization donates to orphanage in Wittenberg.", "entity_names": ["Mufti Menk", "Wittenberg"], "entity_types": ["person", "location"]}
{"sentence": "The National Wildlife Federation announces the launch of a new conservation program in the Midwest.", "entity_names": ["National Wildlife Federation", "Midwest"], "entity_types": ["organization", "location"]}
{"sentence": "Wildlife Trust of India receives international recognition for its efforts in protecting endangered species.", "entity_names": ["Wildlife Trust of India"], "entity_types": ["organization"]}
{"sentence": "The National Wildlife Federation partners with local governments to establish new wildlife sanctuaries in the Pacific Northwest.", "entity_names": ["National Wildlife Federation", "Pacific Northwest"], "entity_types": ["organization", "location"]}
{"sentence": "Prince Harry and Meghan Markle make surprise visit to Tokyo.", "entity_names": ["Prince Harry", "Tokyo"], "entity_types": ["person", "location"]}
{"sentence": "Tokyo Olympics organizers announce new COVID-19 safety measures for athletes.", "entity_names": ["Tokyo"], "entity_types": ["location"]}
{"sentence": "Dr. Martin Luther King Jr. memorial unveiled in Washington, D.C.", "entity_names": ["Dr. Martin Luther King Jr.", "Washington, D.C."], "entity_types": ["person", "location"]}
{"sentence": "Islamic Circle of North America hosts annual convention in Houston.", "entity_names": ["Islamic Circle of North America", "Houston"], "entity_types": ["organization", "location"]}
{"sentence": "New study shows the impact of Dr. Martin Luther King Jr.'s speeches on civil rights movement.", "entity_names": ["Dr. Martin Luther King Jr."], "entity_types": ["person"]}
{"sentence": "Elon Musk's SpaceX successfully launches 60 more satellites into orbit.", "entity_names": ["Elon Musk", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "Heavy rain causes severe flooding in parts of New York City.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Apple Inc. announces record-breaking quarterly earnings.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}
{"sentence": "The Organization of Islamic Cooperation condemns the recent terrorist attack.", "entity_names": ["Organization of Islamic Cooperation"], "entity_types": ["organization"]}
{"sentence": "The International Red Cross sends humanitarian aid to the war-torn region.", "entity_names": ["International Red Cross"], "entity_types": ["organization"]}
{"sentence": "A. Smith appointed as the new head of the World Health Organization.", "entity_names": ["A. Smith", "World Health Organization"], "entity_types": ["person", "organization"]}
{"sentence": "After a series of earthquakes, residents in Los Angeles are on edge.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}
{"sentence": "Golden Gate Bridge to undergo major renovation to improve safety.", "entity_names": ["Golden Gate Bridge"], "entity_types": ["location"]}
{"sentence": "Sarah Gonzalez appointed as the new CEO of the tech company.", "entity_names": ["Sarah Gonzalez"], "entity_types": ["person"]}
{"sentence": "Chef Michael Adams opens new restaurant in downtown Chicago.", "entity_names": ["Chef Michael Adams", "Chicago"], "entity_types": ["person", "location"]}
{"sentence": "Chef Michael Adams partners with renowned winery for exclusive dining experience.", "entity_names": ["Chef Michael Adams"], "entity_types": ["person"]}
{"sentence": "Renowned chef Chef Michael Adams wins prestigious culinary award.", "entity_names": ["Chef Michael Adams"], "entity_types": ["person"]}
{"sentence": "India records highest daily COVID-19 cases since start of pandemic.", "entity_names": ["India"], "entity_types": ["location"]}
{"sentence": "United States Department of Defense announces new military budget for the upcoming fiscal year.", "entity_names": ["United States Department of Defense"], "entity_types": ["organization"]}
{"sentence": "Beijing imposes new restrictions on internet usage in the city.", "entity_names": ["Beijing"], "entity_types": ["location"]}
{"sentence": "Moscow prepares for a major economic summit next month.", "entity_names": ["Moscow"], "entity_types": ["location"]}
{"sentence": "John Glenn, the first American to orbit the Earth, passes away at the age of 95.", "entity_names": ["John Glenn"], "entity_types": ["person"]}
{"sentence": "The Indian Space Research Organisation successfully launches its latest communication satellite into orbit.", "entity_names": ["Indian Space Research Organisation"], "entity_types": ["organization"]}
{"sentence": "Remembering the legacy of Kalpana Chawla, the first woman of Indian origin to go to space.", "entity_names": ["Kalpana Chawla", "Indian"], "entity_types": ["person", "location"]}
{"sentence": "Angela Merkel meets with Shinzo Abe to discuss trade agreements.", "entity_names": ["Angela Merkel", "Shinzo Abe"], "entity_types": ["person", "person"]}
{"sentence": "The G7 summit was held in Biarritz, France.", "entity_names": ["G7", "Biarritz", "France"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Tesla announces plans to build new Gigafactory in Berlin.", "entity_names": ["Tesla", "Berlin"], "entity_types": ["organization", "location"]}
{"sentence": "Tesla announces plans to build new production facility in China.", "entity_names": ["Tesla", "China"], "entity_types": ["organization", "location"]}
{"sentence": "Angela Merkel wins re-election as Chancellor of Germany.", "entity_names": ["Angela Merkel", "Germany"], "entity_types": ["person", "location"]}
{"sentence": "Apple introduces new iPhone with enhanced camera features.", "entity_names": ["Apple"], "entity_types": ["organization"]}
{"sentence": "The National Organization for Women is advocating for equal pay for women in the workplace.", "entity_names": ["National Organization for Women"], "entity_types": ["organization"]}
{"sentence": "The National Organization for Women held a rally in Washington D.C. to protest gender-based violence.", "entity_names": ["National Organization for Women", "Washington D.C."], "entity_types": ["organization", "location"]}
{"sentence": "The National Organization for Women honored Gloria Steinem for her contributions to the feminist movement.", "entity_names": ["National Organization for Women", "Gloria Steinem"], "entity_types": ["organization", "person"]}
{"sentence": "German Chancellor Angela Merkel meets with French President Macron to discuss EU trade agreements.", "entity_names": ["Angela Merkel", "Macron", "EU"], "entity_types": ["person", "person", "organization"]}
{"sentence": "Angela Merkel announces plan to increase funding for renewable energy in Germany.", "entity_names": ["Angela Merkel", "Germany"], "entity_types": ["person", "location"]}
{"sentence": "Angela Merkel calls for unity among European leaders in addressing the refugee crisis.", "entity_names": ["Angela Merkel"], "entity_types": ["person"]}
{"sentence": "New York City set to build new affordable housing units.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "The New York Yankees sign a new pitcher for the upcoming season.", "entity_names": ["New York Yankees"], "entity_types": ["organization"]}
{"sentence": "Snowstorm hits New York City, causing traffic disruptions and school closures.", "entity_names": ["New York City"], "entity_types": ["location"]}
{"sentence": "Tesla announces plans to build new factory in Texas.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}
{"sentence": "President Biden delivers speech on infrastructure plan in Pittsburgh.", "entity_names": ["Biden", "Pittsburgh"], "entity_types": ["person", "location"]}
{"sentence": "Facebook faces legal action over data privacy violations.", "entity_names": ["Facebook"], "entity_types": ["organization"]}
{"sentence": "Adam Schiff calls for investigation into alleged cyberattack on Democratic National Committee.", "entity_names": ["Adam Schiff", "Democratic National Committee"], "entity_types": ["person", "organization"]}
{"sentence": "Democratic National Committee announces plans for nationwide voter registration drive.", "entity_names": ["Democratic National Committee"], "entity_types": ["organization"]}
{"sentence": "Adam Schiff accuses government officials of obstructing investigation into Russian interference.", "entity_names": ["Adam Schiff"], "entity_types": ["person"]}
{"sentence": "Archaeologists discover more about the legacy of Genghis Khan.", "entity_names": ["Genghis Khan"], "entity_types": ["person"]}
{"sentence": "Climbers attempt to reach the summit of Mount Everest.", "entity_names": ["Mount Everest"], "entity_types": ["location"]}
{"sentence": "The Great Wall of China undergoes major restoration work.", "entity_names": ["Great Wall of China"], "entity_types": ["location"]}
{"sentence": "Microsoft Corporation announces plans to open new headquarters in Sydney, Australia.", "entity_names": ["Microsoft Corporation", "Sydney", "Australia"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Sydney, Australia experiences record-breaking heatwave, with temperatures reaching 45 degrees Celsius.", "entity_names": ["Sydney", "Australia"], "entity_types": ["location", "location"]}
{"sentence": "Microsoft Corporation unveils new AI technology at Sydney, Australia conference.", "entity_names": ["Microsoft Corporation", "Sydney", "Australia"], "entity_types": ["organization", "location", "location"]}
{"sentence": "Maria Rodriguez appointed as new CEO of Techtronics Inc.", "entity_names": ["Maria Rodriguez", "Techtronics Inc."], "entity_types": ["person", "organization"]}
{"sentence": "Taylor Williams elected as mayor of Greenville.", "entity_names": ["Taylor Williams", "Greenville"], "entity_types": ["person", "location"]}
{"sentence": "Local nonprofit organization, HopeWorks, to receive $1 million grant for community programs.", "entity_names": ["HopeWorks"], "entity_types": ["organization"]}
{"sentence": "Vladimir Putin meets with Angela Merkel to discuss trade relations and international cooperation.", "entity_names": ["Vladimir Putin", "Angela Merkel"], "entity_types": ["person", "person"]}
{"sentence": "European Union imposes sanctions on Belarus, draws strong reaction from Vladimir Putin.", "entity_names": ["European Union", "Belarus", "Vladimir Putin"], "entity_types": ["organization", "location", "person"]}
{"sentence": "Vladimir Putin delivers annual address to the nation, outlining key policies for economic growth and social development.", "entity_names": ["Vladimir Putin"], "entity_types": ["person"]}
{"sentence": "ECONOMY - US Federal Reserve announces interest rate hike.", "entity_names": ["US Federal Reserve"], "entity_types": ["organization"]}
{"sentence": "Tokyo Olympics postponed due to COVID-19 pandemic.", "entity_names": ["Tokyo"], "entity_types": ["location"]}
{"sentence": "Martha Stewart's company reports strong third quarter earnings.", "entity_names": ["Martha Stewart"], "entity_types": ["person"]}
{"sentence": "A new book about Martha Stewart's life hits the shelves next week.", "entity_names": ["Martha Stewart"], "entity_types": ["person"]}
{"sentence": "Martha Stewart to step down as CEO of her media empire.", "entity_names": ["Martha Stewart"], "entity_types": ["person"]}
{"sentence": "The Georgia O'Keeffe Museum in Santa Fe showcases the artist's iconic works.", "entity_names": ["Georgia O'Keeffe", "Santa Fe"], "entity_types": ["person", "location"]}
{"sentence": "Ai Weiwei's exhibition at the Tate Modern in London drew large crowds.", "entity_names": ["Ai Weiwei", "Tate Modern", "London"], "entity_types": ["person", "organization", "location"]}
{"sentence": "Leonardo da Vinci's masterpiece 'Mona Lisa' attracts millions of visitors to the Louvre Museum in Paris each year.", "entity_names": ["Leonardo da Vinci", "Louvre Museum", "Paris"], "entity_types": ["person", "organization", "location"]}
{"sentence": "South Korea and Japan sign trade agreement amidst tensions.", "entity_names": ["South Korea", "Japan"], "entity_types": ["location", "location"]}
{"sentence": "Stocks rally as European Union announces economic stimulus package.", "entity_names": ["European Union"], "entity_types": ["organization"]}
{"sentence": "The Association for Supervision and Curriculum Development announces new leadership team.", "entity_names": ["Association for Supervision and Curriculum Development"], "entity_types": ["organization"]}
{"sentence": "Renowned author Maya Angelou to speak at the Association for Supervision and Curriculum Development conference.", "entity_names": ["Maya Angelou", "Association for Supervision and Curriculum Development"], "entity_types": ["person", "organization"]}
{"sentence": "The Association for Supervision and Curriculum Development expands its reach with new international partnerships.", "entity_names": ["Association for Supervision and Curriculum Development"], "entity_types": ["organization"]}
{"sentence": "Fashion icon Donatella Versace launches new clothing line under the Christian Dior brand.", "entity_names": ["Donatella Versace", "Christian Dior"], "entity_types": ["person", "organization"]}
{"sentence": "Christian Dior announces Donatella Versace as the new creative director for their upcoming couture collection.", "entity_names": ["Christian Dior", "Donatella Versace"], "entity_types": ["organization", "person"]}
{"sentence": "Donatella Versace partners with Christian Dior for a charity fashion show to support children's education.", "entity_names": ["Donatella Versace", "Christian Dior"], "entity_types": ["person", "organization"]}
{"sentence": "Art Basel cancels its upcoming exhibition due to the ongoing pandemic.", "entity_names": ["Art Basel"], "entity_types": ["organization"]}
{"sentence": "Salvador Dali's iconic painting, The Persistence of Memory, sells for a record-breaking price at auction.", "entity_names": ["Salvador Dali"], "entity_types": ["person"]}
{"sentence": "Renowned artist Ai Weiwei's latest installation sparks controversy and conversation at the gallery opening.", "entity_names": ["Ai Weiwei"], "entity_types": ["person"]}
{"sentence": "Tesla unveils new electric car model.", "entity_names": ["Tesla"], "entity_types": ["organization"]}
{"sentence": "New York City Mayor announces plan to build affordable housing.", "entity_names": ["New York City", "Mayor"], "entity_types": ["location", "person"]}
{"sentence": "WHO declares global health emergency in response to new virus outbreak.", "entity_names": ["WHO"], "entity_types": ["organization"]}
{"sentence": "Jeff Corwin to host new documentary series on wildlife conservation.", "entity_names": ["Jeff Corwin"], "entity_types": ["person"]}
{"sentence": "Environmental organization partners with Jeff Corwin for ocean cleanup campaign.", "entity_names": ["Jeff Corwin"], "entity_types": ["person"]}
{"sentence": "Jeff Corwin's latest book on endangered species hits bestseller list.", "entity_names": ["Jeff Corwin"], "entity_types": ["person"]}
{"sentence": "NASA Administrator Jim Bridenstine announces new mission to explore Europa.", "entity_names": ["NASA", "Jim Bridenstine", "Europa"], "entity_types": ["organization", "person", "location"]}
{"sentence": "Jim Bridenstine visits SpaceX headquarters to discuss upcoming collaboration.", "entity_names": ["Jim Bridenstine", "SpaceX"], "entity_types": ["person", "organization"]}
{"sentence": "Jim Bridenstine's resignation from NASA sparks speculation about potential replacements.", "entity_names": ["Jim Bridenstine", "NASA"], "entity_types": ["person", "organization"]}
{"sentence": "The World Health Organization warns of a potential new wave of COVID-19.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}
{"sentence": "Prime Minister Johnson announces new infrastructure plan for Northern England.", "entity_names": ["Prime Minister Johnson", "Northern England"], "entity_types": ["person", "location"]}
{"sentence": "The stock market experienced a sharp decline following the Federal Reserve's interest rate decision.", "entity_names": ["Federal Reserve"], "entity_types": ["organization"]}
{"sentence": "Greta Gerwig to direct new film for Walt Disney Pictures.", "entity_names": ["Greta Gerwig", "Walt Disney Pictures"], "entity_types": ["person", "organization"]}
{"sentence": "American Film Institute announces new scholarship program in New York City.", "entity_names": ["American Film Institute", "New York City"], "entity_types": ["organization", "location"]}
{"sentence": "Walt Disney Pictures to release new animated feature in 2022.", "entity_names": ["Walt Disney Pictures"], "entity_types": ["organization"]}
{"sentence": "United Nations Educational, Scientific and Cultural Organization launches new global initiative to protect cultural heritage sites from destruction.", "entity_names": ["United Nations Educational, Scientific and Cultural Organization"], "entity_types": ["organization"]}
{"sentence": "Famous chef Gordon Ramsay opens new restaurant in downtown Los Angeles.", "entity_names": ["Gordon Ramsay", "Los Angeles"], "entity_types": ["person", "location"]}
{"sentence": "United Nations Educational, Scientific and Cultural Organization to host international conference on climate change and its impact on cultural preservation.", "entity_names": ["United Nations Educational, Scientific and Cultural Organization"], "entity_types": ["organization"]}
{"sentence": "Ferrari unveils new supercar in Maranello", "entity_names": ["Ferrari", "Maranello"], "entity_types": ["organization", "location"]}
{"sentence": "Maranello-based racing team wins championship", "entity_names": ["Maranello"], "entity_types": ["location"]}
{"sentence": "Maranello mayor announces new infrastructure plan", "entity_names": ["Maranello"], "entity_types": ["location"]}
{"sentence": "Vincent van Gogh's famous painting 'Starry Night' sells for millions at auction.", "entity_names": ["Vincent van Gogh"], "entity_types": ["person"]}
{"sentence": "Tourists flock to the Acropolis in Athens to admire the ancient ruins.", "entity_names": ["Acropolis", "Athens"], "entity_types": ["location", "location"]}
{"sentence": "The Athens Symphony Orchestra, founded in 1949, celebrates its 70th anniversary with a special performance.", "entity_names": ["Athens Symphony Orchestra"], "entity_types": ["organization"]}
{"sentence": "Jorge Sanchez, a renowned economist, predicts a 3% increase in the stock market for the upcoming fiscal year.", "entity_names": ["Jorge Sanchez"], "entity_types": ["person"]}
{"sentence": "Cairo experiences a surge in tourism as travel restrictions are lifted.", "entity_names": ["Cairo"], "entity_types": ["location"]}
{"sentence": "The organization 'Food for All' launches a new campaign to fight hunger in underprivileged communities.", "entity_names": ["Food for All"], "entity_types": ["organization"]}
{"sentence": "Mount Everest summit reached by New Zealand explorer, following in the footsteps of Edmund Hillary.", "entity_names": ["Mount Everest", "New Zealand", "Edmund Hillary"], "entity_types": ["location", "location", "person"]}
{"sentence": "Edmund Hillary Foundation announces scholarship program for aspiring mountain climbers.", "entity_names": ["Edmund Hillary Foundation"], "entity_types": ["organization"]}
{"sentence": "Exhibit featuring artifacts from Edmund Hillary's historic Everest expedition opens at National Geographic museum.", "entity_names": ["Edmund Hillary", "National Geographic"], "entity_types": ["person", "organization"]}
{"sentence": "Jacinda Ardern addresses the nation on climate change.", "entity_names": ["Jacinda Ardern"], "entity_types": ["person"]}
{"sentence": "New Zealand Prime Minister Jacinda Ardern meets with European Union leaders in Brussels.", "entity_names": ["Jacinda Ardern", "European Union"], "entity_types": ["person", "organization"]}
{"sentence": "Jacinda Ardern announces new funding for mental health programs.", "entity_names": ["Jacinda Ardern"], "entity_types": ["person"]}
