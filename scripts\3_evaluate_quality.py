#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据质量检测脚本
输入：合成数据集文件路径
输出：质量评估报告和可视化图表

使用方法：
python scripts/3_evaluate_quality.py --dataset path/to/synthetic_dataset.json
"""

import os
import sys
import json
import argparse
from datetime import datetime
from pathlib import Path
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def setup_environment():
    """设置环境变量和路径"""
    # 确保必要的目录存在
    os.makedirs("reproduce/evaluation_results", exist_ok=True)
    os.makedirs("reproduce/visualization", exist_ok=True)
    
    print("[✓] 环境设置完成")

def validate_dataset(dataset_path: str):
    """验证数据集文件"""
    if not os.path.exists(dataset_path):
        print(f"[错误] 数据集文件不存在：{dataset_path}")
        return False
    
    try:
        with open(dataset_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list):
            print("[错误] 数据集格式错误：应为JSON数组")
            return False
        
        if len(data) == 0:
            print("[警告] 数据集为空")
            return False
        
        print(f"[✓] 数据集验证通过，包含 {len(data)} 个样本")
        return True
        
    except json.JSONDecodeError as e:
        print(f"[错误] 数据集JSON格式错误：{e}")
        return False
    except Exception as e:
        print(f"[错误] 数据集验证失败：{e}")
        return False

def update_evaluation_config(dataset_path: str):
    """更新evaluation_config.json中的数据集路径"""
    eval_config_path = "src/synth_eval/evaluation_config.json"
    
    try:
        # 读取当前配置
        with open(eval_config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 更新数据集路径
        old_dataset = config.get("dataset_path", "")
        config["dataset_path"] = dataset_path
        
        # 保存更新后的配置
        with open(eval_config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"[✓] 已更新evaluation_config.json")
        print(f"    dataset_path: {old_dataset} -> {dataset_path}")
        
    except Exception as e:
        print(f"[错误] 更新evaluation_config.json失败：{e}")
        raise

def update_evaluation_dataset_path(new_path, config_path="src/synth_eval/evaluation_config.json"):
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    config["dataset_path"] = new_path
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)

def run_quality_evaluation(is_final=True, generate_plots=True, output_dir=None, original_dataset_path=None):
    """运行质量评估"""
    print("\n=== 执行质量评估 ===")
    try:
        sys.path.insert(0, str(project_root / "src" / "synth_eval"))
        from main_evaluation import run_comprehensive_evaluation
        eval_config_path = "src/synth_eval/evaluation_config.json"
        with open(eval_config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        dataset_path = config.get("dataset_path", "")
        if not dataset_path:
            raise Exception("配置文件中未找到dataset_path")
        # 运行评估，传递所有参数包括原数据集路径
        evaluation_result = run_comprehensive_evaluation(
            dataset_path=dataset_path,
            output_dir=output_dir,
            generate_plots=generate_plots,
            generate_html=True,
            is_final=is_final,
            original_dataset_path=original_dataset_path
        )
        print("[✓] 质量评估完成")
    except ImportError as e:
        print(f"[警告] 无法导入评估模块，尝试直接运行脚本：{e}")
        script_path = "src/synth_eval/main_evaluation.py"
        result = os.system(f"python {script_path}")
        if result == 0:
            print("[✓] 质量评估完成")
        else:
            raise Exception(f"脚本执行失败，返回码：{result}")
    except Exception as e:
        print(f"[错误] 质量评估失败：{e}")
        raise

def get_latest_evaluation_results():
    """获取最新的评估结果文件"""
    results = {}
    
    # 获取评估结果目录
    eval_dir = "reproduce/evaluation_results"
    if os.path.exists(eval_dir):
        eval_files = [f for f in os.listdir(eval_dir) if f.endswith('.json')]
        if eval_files:
            latest_eval = sorted(eval_files)[-1]
            results['evaluation'] = os.path.join(eval_dir, latest_eval)
    
    # 获取可视化图表目录
    viz_dir = "reproduce/visualization"
    if os.path.exists(viz_dir):
        viz_files = [f for f in os.listdir(viz_dir) if f.endswith(('.png', '.jpg', '.pdf'))]
        if viz_files:
            results['visualization'] = [os.path.join(viz_dir, f) for f in viz_files]
    
    return results

def create_summary_report(dataset_path: str, evaluation_results: dict, output_dir: str = None):
    """创建执行总结报告"""
    # 使用运行ID而不是当前时间戳，避免时间不一致问题
    if output_dir:
        # 从输出目录路径中提取运行ID
        output_path = Path(output_dir)
        run_id = None
        
        # 查找包含运行ID的路径部分（格式：YYYYMMDD_HHMMSS）
        for part in output_path.parts:
            if len(part) == 15 and '_' in part and part.replace('_', '').isdigit():
                run_id = part
                break
        
        # 如果找到运行ID，使用它；否则使用当前时间戳
        if run_id:
            report_filename = f"quality_evaluation_report_{run_id}.txt"
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_filename = f"quality_evaluation_report_{timestamp}.txt"
        
        report_path = str(output_path / report_filename)
    else:
        # 如果没有指定输出目录，使用当前目录和当前时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = f"quality_evaluation_report_{timestamp}.txt"
    
    report_content = f"""
数据质量检测报告
==================

检测时间：{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

输入文件：
- 合成数据集：{dataset_path}

输出文件：
"""
    
    if 'evaluation' in evaluation_results:
        report_content += f"- 评估结果：{evaluation_results['evaluation']}\n"
    
    if 'visualization' in evaluation_results:
        report_content += "- 可视化图表：\n"
        for viz_file in evaluation_results['visualization']:
            report_content += f"  * {viz_file}\n"
    
    report_content += """
评估内容：
- 实体分布均衡性验证
- 词汇多样性验证
- 句法多样性验证
- 语义多样性验证
- 上下文多样性验证
- 实体多样性验证

下一步：
查看评估结果和可视化图表，分析数据质量
"""
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"\n[✓] 执行报告已保存：{report_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="数据质量检测脚本")
    parser.add_argument("--dataset", help="合成数据集文件路径（优先级最高）")
    parser.add_argument("--original-dataset", help="原数据集文件路径（用于对比）")
    parser.add_argument("--skip-visualization", action="store_true", help="跳过可视化生成")
    parser.add_argument("--is-final", action="store_true", help="是否为最终评估（仅最后一轮输出图表和详细报告）")
    parser.add_argument("--output-dir", default=None, help="评估输出目录（默认自动生成evaluation/时间戳/）")
    args = parser.parse_args()
    print("=== 开始数据质量检测 ===")
    # 优先使用命令行参数，否则读取config
    dataset_path = args.dataset
    if not dataset_path:
        with open("src/synth_eval/evaluation_config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
        dataset_path = config.get("dataset_path", "")
    print(f"合成数据集：{dataset_path}")
    if args.original_dataset:
        print(f"原数据集：{args.original_dataset}")
    try:
        if not validate_dataset(dataset_path):
            sys.exit(1)
        setup_environment()
        # 每轮评估前自动更新config中的dataset_path
        update_evaluation_dataset_path(dataset_path)
        run_quality_evaluation(
            is_final=args.is_final,
            generate_plots=not args.skip_visualization,
            output_dir=args.output_dir,
            original_dataset_path=args.original_dataset
        )
        evaluation_results = get_latest_evaluation_results()
        create_summary_report(dataset_path, evaluation_results, args.output_dir)
        print(f"\n=== 数据质量检测完成 ===")
        if 'evaluation' in evaluation_results:
            print(f"评估结果：{evaluation_results['evaluation']}")
        if 'visualization' in evaluation_results:
            print(f"可视化图表：{len(evaluation_results['visualization'])} 个文件")
            for viz_file in evaluation_results['visualization']:
                print(f"  - {viz_file}")
        print("\n质量检测完成，请查看评估结果和可视化图表")
    except Exception as e:
        print(f"\n[错误] 数据质量检测失败：{e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 