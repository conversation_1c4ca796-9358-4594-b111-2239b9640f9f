[{"text": "苏醒和孟浩咱们俩一起去面试吧，看谁能先找到好工作。", "label": [{"entity": "苏醒", "start_idx": 0, "end_idx": 1, "type": "姓名"}, {"entity": "孟浩", "start_idx": 3, "end_idx": 4, "type": "姓名"}]}, {"text": "陆游这名字真酷，和关晓彤一样有文化底蕴。", "label": [{"entity": "陆游", "start_idx": 0, "end_idx": 1, "type": "姓名"}, {"entity": "关晓彤", "start_idx": 9, "end_idx": 11, "type": "姓名"}]}, {"text": "宋词背得怎么样了，赵雅，咱们得准备面试的文化题哦。", "label": [{"entity": "赵雅", "start_idx": 9, "end_idx": 10, "type": "姓名"}]}, {"text": "李现和邓紫棋都是我的偶像，要是能和他们一起工作就好了。", "label": [{"entity": "李现", "start_idx": 0, "end_idx": 1, "type": "姓名"}, {"entity": "邓紫棋", "start_idx": 3, "end_idx": 5, "type": "姓名"}]}, {"text": "黄磊老师和王祖蓝一样，都是我喜欢的艺人，工作起来肯定开心。", "label": [{"entity": "黄磊", "start_idx": 0, "end_idx": 1, "type": "姓名"}, {"entity": "王祖蓝", "start_idx": 5, "end_idx": 7, "type": "姓名"}]}, {"text": "陆游、范晓萱二位立即提交相关法律文件，以供审查。", "label": [{"entity": "陆游", "start_idx": 0, "end_idx": 1, "type": "姓名"}, {"entity": "范晓萱", "start_idx": 3, "end_idx": 5, "type": "姓名"}]}, {"text": "张涵予、毛不易你必须于规定期限内上交完整诉讼材料。", "label": [{"entity": "张涵予", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "毛不易", "start_idx": 4, "end_idx": 6, "type": "姓名"}]}, {"text": "关晓彤、孙韵，你们需严格按照法定范围准备相关证词。", "label": [{"entity": "关晓彤", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "孙韵", "start_idx": 4, "end_idx": 5, "type": "姓名"}]}, {"text": "潘玮柏、毕福剑，立即按照法律规定提交财产申报表。", "label": [{"entity": "潘玮柏", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "毕福剑", "start_idx": 4, "end_idx": 6, "type": "姓名"}]}, {"text": "宋词、迪丽热巴，你们必须在指定期限内完成所有法律文件签署。", "label": [{"entity": "宋词", "start_idx": 0, "end_idx": 1, "type": "姓名"}, {"entity": "迪丽热巴", "start_idx": 3, "end_idx": 6, "type": "姓名"}]}, {"text": "毛晓彤与曹瑞共同出席公益活动，倡导社会正能量。", "label": [{"entity": "毛晓彤", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "曹瑞", "start_idx": 4, "end_idx": 5, "type": "姓名"}]}, {"text": "陆游纪念馆举行开展仪式，陈璟作为特邀嘉宾发表讲话。", "label": [{"entity": "陆游", "start_idx": 0, "end_idx": 1, "type": "姓名"}, {"entity": "陈璟", "start_idx": 12, "end_idx": 13, "type": "姓名"}]}, {"text": "薛之谦与吴亦凡合作新歌发布，引发歌迷热议。", "label": [{"entity": "薛之谦", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "吴亦凡", "start_idx": 4, "end_idx": 6, "type": "姓名"}]}, {"text": "陈璟、毛不易联手打造的音乐作品受到业界好评。", "label": [{"entity": "陈璟", "start_idx": 0, "end_idx": 1, "type": "姓名"}, {"entity": "毛不易", "start_idx": 3, "end_idx": 5, "type": "姓名"}]}, {"text": "魏晨曦与梁辰合作主演的电视剧即将上映，备受观众期待。", "label": [{"entity": "魏晨曦", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "梁辰", "start_idx": 4, "end_idx": 5, "type": "姓名"}]}, {"text": "周杰和吴磊两位病患在经过精心治疗后的康复情况备受关注。", "label": [{"entity": "周杰", "start_idx": 0, "end_idx": 1, "type": "姓名"}, {"entity": "吴磊", "start_idx": 3, "end_idx": 4, "type": "姓名"}]}, {"text": "龚俊与罗志祥的病例分析显示，两位患者的病情均得到了有效控制。", "label": [{"entity": "龚俊", "start_idx": 0, "end_idx": 1, "type": "姓名"}, {"entity": "罗志祥", "start_idx": 3, "end_idx": 5, "type": "姓名"}]}, {"text": "肖战和蔡徐坤共同参与的临床试验，为医学研究提供了宝贵的数据支持。", "label": [{"entity": "肖战", "start_idx": 0, "end_idx": 1, "type": "姓名"}, {"entity": "蔡徐坤", "start_idx": 3, "end_idx": 5, "type": "姓名"}]}, {"text": "针对蔡徐坤与吕思清的病情，医疗团队已制定出相应的治疗方案。", "label": [{"entity": "蔡徐坤", "start_idx": 2, "end_idx": 4, "type": "姓名"}, {"entity": "吕思清", "start_idx": 6, "end_idx": 8, "type": "姓名"}]}, {"text": "杨洋和薛之谦在就诊过程中，医院方面高度重视，确保了诊疗工作的顺利进行。", "label": [{"entity": "杨洋", "start_idx": 0, "end_idx": 1, "type": "姓名"}, {"entity": "薛之谦", "start_idx": 3, "end_idx": 5, "type": "姓名"}]}, {"text": "龚俊与孙红雷在最新一期的综艺节目中的互动引发网友热议。", "label": [{"entity": "龚俊", "start_idx": 0, "end_idx": 1, "type": "姓名"}, {"entity": "孙红雷", "start_idx": 3, "end_idx": 5, "type": "姓名"}]}, {"text": "许嵩和尤长靖合作的新歌一经发布，便登上了音乐榜前列。", "label": [{"entity": "许嵩", "start_idx": 0, "end_idx": 1, "type": "姓名"}, {"entity": "尤长靖", "start_idx": 3, "end_idx": 5, "type": "姓名"}]}, {"text": "唐伯虎的经典角色被李易峰重新诠释，获得了观众好评。", "label": [{"entity": "唐伯虎", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "李易峰", "start_idx": 9, "end_idx": 11, "type": "姓名"}]}, {"text": "吕思清与赵又廷同框出席活动，合影引发网友点赞。", "label": [{"entity": "吕思清", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "赵又廷", "start_idx": 4, "end_idx": 6, "type": "姓名"}]}, {"text": "鹿晗和周深在音乐节目中合作演出，收获了大量粉丝好评。", "label": [{"entity": "鹿晗", "start_idx": 0, "end_idx": 1, "type": "姓名"}, {"entity": "周深", "start_idx": 3, "end_idx": 4, "type": "姓名"}]}, {"text": "孟浩你看了龚俊的新剧没？简直帅炸了！", "label": [{"entity": "孟浩", "start_idx": 0, "end_idx": 1, "type": "姓名"}, {"entity": "龚俊", "start_idx": 5, "end_idx": 6, "type": "姓名"}]}, {"text": "蒋劲夫好像和韩寒一起参加了那个公益活动，真是正能量满满啊！", "label": [{"entity": "蒋劲夫", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "韩寒", "start_idx": 6, "end_idx": 7, "type": "姓名"}]}, {"text": "罗志祥跟吕思清同框了，两大舞王这是要合作的节奏吗？", "label": [{"entity": "罗志祥", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "吕思清", "start_idx": 4, "end_idx": 6, "type": "姓名"}]}, {"text": "李易峰和顾宇这俩型男，走到哪儿都是焦点。", "label": [{"entity": "李易峰", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "顾宇", "start_idx": 4, "end_idx": 5, "type": "姓名"}]}, {"text": "毛不易和范晓萱的音乐风格虽然不同，但要是合作一定超级惊艳！", "label": [{"entity": "毛不易", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "范晓萱", "start_idx": 4, "end_idx": 6, "type": "姓名"}]}, {"text": "蔡徐坤应学习程咬金坚韧不拔的精神，以提升面试竞争力。", "label": [{"entity": "蔡徐坤", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "程咬金", "start_idx": 6, "end_idx": 8, "type": "姓名"}]}, {"text": "蒋劲夫可借鉴吴亦凡在娱乐圈的转型经验，拓宽职业发展道路。", "label": [{"entity": "蒋劲夫", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "吴亦凡", "start_idx": 6, "end_idx": 8, "type": "姓名"}]}, {"text": "陶渊明的豁达与苏醒的执着，是求职者值得学习的品质。", "label": [{"entity": "陶渊明", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "苏醒", "start_idx": 7, "end_idx": 8, "type": "姓名"}]}, {"text": "周杰和陈璟在各自领域均有建树，可作为职场奋斗目标。", "label": [{"entity": "周杰", "start_idx": 0, "end_idx": 1, "type": "姓名"}, {"entity": "陈璟", "start_idx": 3, "end_idx": 4, "type": "姓名"}]}, {"text": "这位病人二十四周岁，主治医生三十四岁半，医术却非常精湛。", "label": [{"entity": "二十四", "start_idx": 4, "end_idx": 6, "type": "年龄"}, {"entity": "三十四岁半", "start_idx": 14, "end_idx": 18, "type": "年龄"}]}, {"text": "小李二十周岁，他哥22岁，两人都在医院工作。", "label": [{"entity": "二十周岁", "start_idx": 2, "end_idx": 5, "type": "年龄"}, {"entity": "22岁", "start_idx": 9, "end_idx": 11, "type": "年龄"}]}, {"text": "小患者5岁3个月，他的主治医生年纪轻，也才35岁。", "label": [{"entity": "5岁3个月", "start_idx": 3, "end_idx": 7, "type": "年龄"}, {"entity": "35岁", "start_idx": 21, "end_idx": 23, "type": "年龄"}]}, {"text": "那个医生三十四岁半，比患者二十周岁大不少，但两人很聊得来。", "label": [{"entity": "三十四岁半", "start_idx": 4, "end_idx": 8, "type": "年龄"}, {"entity": "二十周岁", "start_idx": 13, "end_idx": 16, "type": "年龄"}]}, {"text": "一个二十四周岁的年轻人，和一个二十周岁的青年，都在这里候诊。", "label": [{"entity": "二十四周岁", "start_idx": 2, "end_idx": 6, "type": "年龄"}, {"entity": "二十周岁", "start_idx": 15, "end_idx": 18, "type": "年龄"}]}, {"text": "银行规定，二十四周岁的客户得跟5岁3个月的小孩儿分开办理交易。", "label": [{"entity": "二十四周岁", "start_idx": 5, "end_idx": 9, "type": "年龄"}, {"entity": "5岁3个月", "start_idx": 15, "end_idx": 19, "type": "年龄"}]}, {"text": "我这年龄，二十四周岁，比那35岁的老哥可差远了。", "label": [{"entity": "二十四周岁", "start_idx": 5, "end_idx": 9, "type": "年龄"}, {"entity": "35岁", "start_idx": 13, "end_idx": 15, "type": "年龄"}]}, {"text": "你才二十四周岁？我都三十四岁半了，真是岁月不饶人啊。", "label": [{"entity": "二十四", "start_idx": 2, "end_idx": 4, "type": "年龄"}, {"entity": "三十四岁半", "start_idx": 10, "end_idx": 14, "type": "年龄"}]}, {"text": "二十周岁的小青年儿和三十四岁半的大哥，都得注意银行交易里的那些敏感信息。", "label": [{"entity": "二十周岁", "start_idx": 0, "end_idx": 3, "type": "年龄"}, {"entity": "三十四岁半", "start_idx": 10, "end_idx": 14, "type": "年龄"}]}, {"text": "我22岁，你二十周岁，咱俩这年龄段儿可得小心别泄露了银行信息。", "label": [{"entity": "22岁", "start_idx": 1, "end_idx": 3, "type": "年龄"}, {"entity": "二十周岁", "start_idx": 6, "end_idx": 9, "type": "年龄"}]}, {"text": "病例1：本次接受手术治疗的患者年龄分别为二十四周岁和二十周岁。", "label": [{"entity": "二十四周岁", "start_idx": 20, "end_idx": 24, "type": "年龄"}, {"entity": "二十周岁", "start_idx": 26, "end_idx": 29, "type": "年龄"}]}, {"text": "病例2：研究报告指出，参与实验的志愿者年龄介于二十周岁至22岁之间。", "label": [{"entity": "二十周岁至22岁", "start_idx": 23, "end_idx": 30, "type": "年龄"}]}, {"text": "病例3：在本次健康检查中，35岁与三十四岁半的两位患者表现出不同症状。", "label": [{"entity": "35岁", "start_idx": 13, "end_idx": 15, "type": "年龄"}, {"entity": "三十四岁半", "start_idx": 17, "end_idx": 21, "type": "年龄"}]}, {"text": "病例4：针对35岁及三十四岁半患者的治疗方案已进行专门讨论。", "label": [{"entity": "35岁", "start_idx": 6, "end_idx": 8, "type": "年龄"}, {"entity": "三十四岁半", "start_idx": 10, "end_idx": 14, "type": "年龄"}]}, {"text": "病例5：本次调研涉及的年龄层为35岁至二十四周岁，旨在分析不同年龄段患者的病情特点。", "label": [{"entity": "35岁", "start_idx": 15, "end_idx": 17, "type": "年龄"}, {"entity": "二十四周岁", "start_idx": 19, "end_idx": 23, "type": "年龄"}]}, {"text": "法律规定，申请人年龄得在35岁以内，不过三十四岁半的经验更受欢迎。", "label": [{"entity": "35岁", "start_idx": 12, "end_idx": 14, "type": "年龄"}, {"entity": "三十四岁半", "start_idx": 20, "end_idx": 24, "type": "年龄"}]}, {"text": "这个案子要求当事人至少得是二十四周岁，但三十四岁半的管理经验明显是个优势。", "label": [{"entity": "二十四", "start_idx": 13, "end_idx": 15, "type": "年龄"}, {"entity": "三十四岁半", "start_idx": 20, "end_idx": 24, "type": "年龄"}]}, {"text": "那部法律不是说了嘛，二十周岁就能独立行事了，我看你22岁已经挺合适的。", "label": [{"entity": "二十周岁", "start_idx": 10, "end_idx": 13, "type": "年龄"}, {"entity": "22岁", "start_idx": 25, "end_idx": 27, "type": "年龄"}]}, {"text": "按照条文，二十四周岁才有资格申请，可别小看了这和5岁3个月孩子的差别哦。", "label": [{"entity": "二十四", "start_idx": 5, "end_idx": 7, "type": "年龄"}, {"entity": "5岁3个月", "start_idx": 24, "end_idx": 28, "type": "年龄"}]}, {"text": "这个年龄段，从5岁3个月到三十四岁半，都得遵守这条法律规定。", "label": [{"entity": "5岁3个月", "start_idx": 7, "end_idx": 11, "type": "年龄"}, {"entity": "三十四岁半", "start_idx": 13, "end_idx": 17, "type": "年龄"}]}, {"text": "客服记录显示，客户A年龄为35岁，而客户B自我描述为三十四岁半。", "label": [{"entity": "35岁", "start_idx": 13, "end_idx": 15, "type": "年龄"}, {"entity": "三十四岁半", "start_idx": 26, "end_idx": 30, "type": "年龄"}]}, {"text": "在与客户C的沟通中发现，其年龄为二十四周岁，与另一位客户D的35岁形成对比。", "label": [{"entity": "二十四周岁", "start_idx": 16, "end_idx": 20, "type": "年龄"}, {"entity": "35岁", "start_idx": 30, "end_idx": 32, "type": "年龄"}]}, {"text": "另外，客户E表示自己刚满二十周岁，而客户F的孩子则是5岁3个月大。", "label": [{"entity": "二十周岁", "start_idx": 12, "end_idx": 15, "type": "年龄"}, {"entity": "5岁3个月", "start_idx": 26, "end_idx": 30, "type": "年龄"}]}, {"text": "在年龄分布统计中，35岁的客户比例有所上升，与此同时，三十四岁半的客户也不在少数。", "label": [{"entity": "35岁", "start_idx": 9, "end_idx": 11, "type": "年龄"}, {"entity": "三十四岁半", "start_idx": 27, "end_idx": 31, "type": "年龄"}]}, {"text": "针对不同年龄段的服务策略，我们发现二十周岁和5岁3个月的用户需求差异较大。", "label": [{"entity": "二十周岁", "start_idx": 17, "end_idx": 20, "type": "年龄"}, {"entity": "5岁3个月", "start_idx": 22, "end_idx": 26, "type": "年龄"}]}, {"text": "合同规定，申请人年龄应在三十四岁半至二十周岁之间。", "label": [{"entity": "三十四岁半", "start_idx": 12, "end_idx": 16, "type": "年龄"}, {"entity": "二十周岁", "start_idx": 18, "end_idx": 21, "type": "年龄"}]}, {"text": "二十四周岁至35岁的借款人，请按照第四条款执行。", "label": [{"entity": "二十四", "start_idx": 0, "end_idx": 2, "type": "年龄"}, {"entity": "35岁", "start_idx": 6, "end_idx": 8, "type": "年龄"}]}, {"text": "岁3个月至22岁的受益人，须在监护人陪同下签署。", "label": [{"entity": "3个月至22岁", "start_idx": 1, "end_idx": 7, "type": "年龄"}]}, {"text": "请注意，本次金融产品的适用年龄为22岁至三十四岁半。", "label": [{"entity": "22岁", "start_idx": 16, "end_idx": 18, "type": "年龄"}, {"entity": "三十四岁半", "start_idx": 20, "end_idx": 24, "type": "年龄"}]}, {"text": "介于三十四岁半与35岁之间的投资者，请优先考虑A级投资方案。", "label": [{"entity": "三十四岁半", "start_idx": 2, "end_idx": 6, "type": "年龄"}, {"entity": "35岁", "start_idx": 8, "end_idx": 10, "type": "年龄"}]}, {"text": "岁的张先生，速与22岁的李小姐进行紧急对接。", "label": [{"entity": "岁", "start_idx": 0, "end_idx": 0, "type": "年龄"}, {"entity": "22岁", "start_idx": 8, "end_idx": 10, "type": "年龄"}]}, {"text": "三十四岁半的工程师，应指导二十四周岁的实习生完成工作。", "label": [{"entity": "三十四岁半", "start_idx": 0, "end_idx": 4, "type": "年龄"}, {"entity": "二十四周岁", "start_idx": 13, "end_idx": 17, "type": "年龄"}]}, {"text": "二十周岁的青年需向35岁的项目经理汇报进度。", "label": [{"entity": "二十周岁", "start_idx": 0, "end_idx": 3, "type": "年龄"}, {"entity": "35岁", "start_idx": 9, "end_idx": 11, "type": "年龄"}]}, {"text": "岁的实习民警，立即协助二十周岁的志愿者进行疏散。", "label": [{"entity": "岁", "start_idx": 0, "end_idx": 0, "type": "年龄"}, {"entity": "二十周岁", "start_idx": 11, "end_idx": 14, "type": "年龄"}]}, {"text": "岁3个月的儿童由二十周岁的看护人陪同进行体检。", "label": [{"entity": "3个月", "start_idx": 1, "end_idx": 3, "type": "年龄"}, {"entity": "二十周岁", "start_idx": 8, "end_idx": 11, "type": "年龄"}]}, {"text": "病号1：这个22岁的年轻人真是生病的时候运气不好。", "label": [{"entity": "22岁", "start_idx": 6, "end_idx": 8, "type": "年龄"}]}, {"text": "病号2：他那三十四岁半的年纪，孩子都已经五岁三个月大了。", "label": [{"entity": "三十四岁半", "start_idx": 6, "end_idx": 10, "type": "年龄"}, {"entity": "五岁三个月", "start_idx": 20, "end_idx": 24, "type": "年龄"}]}, {"text": "医生：你三十四岁半，比那个22岁的小伙子稳重多了。", "label": [{"entity": "三十四岁半", "start_idx": 4, "end_idx": 8, "type": "年龄"}, {"entity": "22岁", "start_idx": 13, "end_idx": 15, "type": "年龄"}]}, {"text": "护士：22岁的病人恢复得快，35岁的可得加把劲了。", "label": [{"entity": "22岁", "start_idx": 3, "end_idx": 5, "type": "年龄"}, {"entity": "35岁", "start_idx": 14, "end_idx": 16, "type": "年龄"}]}, {"text": "家属：我哥22岁那年也生过病，现在35岁了，可得注意身体啊。", "label": [{"entity": "22岁", "start_idx": 5, "end_idx": 7, "type": "年龄"}, {"entity": "35岁", "start_idx": 17, "end_idx": 19, "type": "年龄"}]}, {"text": "根据最新统计，社交媒体用户年龄层主要集中在二十四周岁至三十四岁半之间。", "label": [{"entity": "二十四", "start_idx": 21, "end_idx": 23, "type": "年龄"}, {"entity": "三十四岁半", "start_idx": 27, "end_idx": 31, "type": "年龄"}]}, {"text": "儿童心理学家建议，5岁3个月至三十四岁半的群体应适当限制社交媒体使用时间。", "label": [{"entity": "5岁3个月", "start_idx": 9, "end_idx": 13, "type": "年龄"}, {"entity": "三十四岁半", "start_idx": 15, "end_idx": 19, "type": "年龄"}]}, {"text": "社会学研究发现，二十四周岁至35岁的青年群体在社交媒体上活跃度较高。", "label": [{"entity": "二十四周岁", "start_idx": 8, "end_idx": 12, "type": "年龄"}, {"entity": "35岁", "start_idx": 14, "end_idx": 16, "type": "年龄"}]}, {"text": "近期调查报告指出，二十周岁至35岁的青年在社交媒体上信息筛选能力较强。", "label": [{"entity": "二十周岁至35岁", "start_idx": 9, "end_idx": 16, "type": "年龄"}]}, {"text": "经过数据分析，35岁与三十四岁半群体在社交媒体上的行为模式存在明显差异。", "label": [{"entity": "35岁", "start_idx": 7, "end_idx": 9, "type": "年龄"}, {"entity": "三十四岁半", "start_idx": 11, "end_idx": 15, "type": "年龄"}]}, {"text": "求职者年龄介于二十周岁至二十二岁之间，具备一定的职场竞争力。", "label": [{"entity": "二十周岁至二十二岁", "start_idx": 7, "end_idx": 15, "type": "年龄"}]}, {"text": "本次招聘面向22岁至35岁的年轻专业人士。", "label": [{"entity": "22岁", "start_idx": 6, "end_idx": 8, "type": "年龄"}, {"entity": "35岁", "start_idx": 10, "end_idx": 12, "type": "年龄"}]}, {"text": "考虑到三十四岁半至二十二岁的年龄层，该岗位候选人需具备相应的工作经验。", "label": [{"entity": "三十四岁半", "start_idx": 3, "end_idx": 7, "type": "年龄"}, {"entity": "二十二岁", "start_idx": 9, "end_idx": 12, "type": "年龄"}]}, {"text": "我们优先考虑35岁以下，特别是22岁的年轻求职者。", "label": [{"entity": "35岁以下", "start_idx": 6, "end_idx": 10, "type": "年龄"}, {"entity": "22岁", "start_idx": 15, "end_idx": 17, "type": "年龄"}]}, {"text": "应聘者年龄应在22岁至二十四周岁之间，以满足职位对年轻活力的需求。", "label": [{"entity": "22岁", "start_idx": 7, "end_idx": 9, "type": "年龄"}]}, {"text": "请关注双性及非二元性别的群体需求，为他们提供平等权益保障。", "label": [{"entity": "双性", "start_idx": 3, "end_idx": 4, "type": "性别"}, {"entity": "非二元性别的", "start_idx": 6, "end_idx": 11, "type": "性别"}]}, {"text": "立即对跨性别和非二元性别人士展开深入报道，揭示其生活现状。", "label": [{"entity": "跨性别", "start_idx": 3, "end_idx": 5, "type": "性别"}, {"entity": "非二元", "start_idx": 7, "end_idx": 9, "type": "性别"}]}, {"text": "务必强调男性在职场中应得的尊重与待遇，保障男士们的合法权益。", "label": [{"entity": "男性", "start_idx": 4, "end_idx": 5, "type": "性别"}]}, {"text": "深入探讨男性和雌雄同体者的权益问题，提倡社会公平正义。", "label": [{"entity": "男性", "start_idx": 4, "end_idx": 5, "type": "性别"}, {"entity": "雌雄同体者", "start_idx": 7, "end_idx": 11, "type": "性别"}]}, {"text": "针对男性跨性别者的困境，我们要进行专题报道，提高社会关注度。", "label": [{"entity": "男性", "start_idx": 2, "end_idx": 3, "type": "性别"}, {"entity": "跨性别者", "start_idx": 4, "end_idx": 7, "type": "性别"}]}, {"text": "本次金融合同中特别指出，性别界定包含雌雄同体及女性身份。", "label": [{"entity": "雌雄同体", "start_idx": 18, "end_idx": 21, "type": "性别"}, {"entity": "女性", "start_idx": 23, "end_idx": 24, "type": "性别"}]}, {"text": "合同条款明确，性别分类涵盖女性及非二元性别个体。", "label": [{"entity": "女性", "start_idx": 13, "end_idx": 14, "type": "性别"}, {"entity": "非二元性别个体", "start_idx": 16, "end_idx": 22, "type": "性别"}]}, {"text": "经过审查，该金融合同对性别描述包含非二元及女性群体。", "label": [{"entity": "非二元", "start_idx": 17, "end_idx": 19, "type": "性别"}, {"entity": "女性", "start_idx": 21, "end_idx": 22, "type": "性别"}]}, {"text": "在合同性别栏中，可见双性及男性身份被明确列出。", "label": [{"entity": "双性", "start_idx": 10, "end_idx": 11, "type": "性别"}, {"entity": "男性", "start_idx": 13, "end_idx": 14, "type": "性别"}]}, {"text": "金融合同详细说明了性别选项，包括雌雄同体及跨性别身份。", "label": [{"entity": "性别", "start_idx": 9, "end_idx": 10, "type": "性别"}, {"entity": "雌雄同体", "start_idx": 16, "end_idx": 19, "type": "性别"}, {"entity": "跨性别身份", "start_idx": 21, "end_idx": 25, "type": "性别"}]}, {"text": "男性用户请提供您的订单号，以便我为您查询详情。", "label": [{"entity": "男性", "start_idx": 0, "end_idx": 1, "type": "性别"}]}, {"text": "女性和双性恋用户，我们的产品同样适合您，请放心选购。", "label": [{"entity": "女性", "start_idx": 0, "end_idx": 1, "type": "性别"}, {"entity": "双性恋", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "作为雌雄同体者，您选择中性款式的服装会非常合适。", "label": [{"entity": "雌雄同体者", "start_idx": 2, "end_idx": 6, "type": "性别"}, {"entity": "中性", "start_idx": 11, "end_idx": 12, "type": "性别"}]}, {"text": "女顾客，男顾客，我们这里有适合不同性别的设计款。", "label": [{"entity": "女", "start_idx": 0, "end_idx": 0, "type": "性别"}, {"entity": "男", "start_idx": 4, "end_idx": 4, "type": "性别"}]}, {"text": "跨性别和雌雄同体的朋友，我们的服务为您量身定制。", "label": [{"entity": "跨性别", "start_idx": 0, "end_idx": 2, "type": "性别"}, {"entity": "雌雄同体", "start_idx": 4, "end_idx": 7, "type": "性别"}]}, {"text": "这个法律文档里提到的中性名词真不少啊，感觉挺全面的。", "label": [{"entity": "中性名词", "start_idx": 10, "end_idx": 13, "type": "性别"}]}, {"text": "雌雄同体和跨性别的定义在这儿有详细说明，看来法律挺关注这些群体。", "label": [{"entity": "雌雄同体", "start_idx": 0, "end_idx": 3, "type": "性别"}, {"entity": "跨性别", "start_idx": 5, "end_idx": 7, "type": "性别"}]}, {"text": "文档里中性加女性的表述挺有意思，感觉考虑得很周到。", "label": [{"entity": "女性", "start_idx": 6, "end_idx": 7, "type": "性别"}]}, {"text": "中性跨性别者的权利保障也写得清清楚楚，真不错。", "label": [{"entity": "中性", "start_idx": 0, "end_idx": 1, "type": "性别"}, {"entity": "跨性别者", "start_idx": 2, "end_idx": 5, "type": "性别"}]}, {"text": "男性和女性的权利界定在这篇法律文档里处理得很谨慎。", "label": [{"entity": "男性", "start_idx": 0, "end_idx": 1, "type": "性别"}, {"entity": "女性", "start_idx": 3, "end_idx": 4, "type": "性别"}]}, {"text": "非二元性别的女性客户在咨询在线客服时，表达了对其身份认同的关心。", "label": [{"entity": "女性", "start_idx": 6, "end_idx": 7, "type": "性别"}, {"entity": "非二元", "start_idx": 0, "end_idx": 2, "type": "性别"}]}, {"text": "针对雌雄同体和非二元性别的疑问，在线客服提供了详尽的解答。", "label": [{"entity": "雌雄同体", "start_idx": 2, "end_idx": 5, "type": "性别"}, {"entity": "非二元性别", "start_idx": 7, "end_idx": 11, "type": "性别"}]}, {"text": "女性与雌雄同体的客户在讨论区分享了对性别认知的深刻见解。", "label": [{"entity": "女性", "start_idx": 0, "end_idx": 1, "type": "性别"}, {"entity": "雌雄同体", "start_idx": 3, "end_idx": 6, "type": "性别"}]}, {"text": "在线客服记录显示，非二元性别的女性顾客对于产品推荐有特别的需求。", "label": [{"entity": "女性", "start_idx": 15, "end_idx": 16, "type": "性别"}, {"entity": "非二元", "start_idx": 9, "end_idx": 11, "type": "性别"}]}, {"text": "双性别的男性客户在在线客服聊天中，询问了关于个性化服务的问题。", "label": [{"entity": "男性", "start_idx": 4, "end_idx": 5, "type": "性别"}, {"entity": "双性别", "start_idx": 0, "end_idx": 2, "type": "性别"}]}, {"text": "男性客户询问了关于产品适用性的问题。", "label": [{"entity": "男性", "start_idx": 0, "end_idx": 1, "type": "性别"}]}, {"text": "非二元性别的用户咨询了男性专用产品的详情。", "label": [{"entity": "非二元", "start_idx": 0, "end_idx": 2, "type": "性别"}, {"entity": "男性", "start_idx": 11, "end_idx": 12, "type": "性别"}]}, {"text": "男性用户表现出对中性化产品的兴趣。", "label": [{"entity": "男性", "start_idx": 0, "end_idx": 1, "type": "性别"}]}, {"text": "女客户表达了对于雌雄同体产品的好奇。", "label": [{"entity": "女", "start_idx": 0, "end_idx": 0, "type": "性别"}, {"entity": "雌雄同体", "start_idx": 8, "end_idx": 11, "type": "性别"}]}, {"text": "跨性别者咨询了男士专区商品的具体信息。", "label": [{"entity": "男士", "start_idx": 7, "end_idx": 8, "type": "性别"}, {"entity": "男", "start_idx": 7, "end_idx": 7, "type": "性别"}]}, {"text": "男性雌雄同体现象，需引起社会广泛关注。", "label": [{"entity": "男性", "start_idx": 0, "end_idx": 1, "type": "性别"}]}, {"text": "跨性别男性权益，应获得法律层面更多保障。", "label": [{"entity": "男性", "start_idx": 3, "end_idx": 4, "type": "性别"}]}, {"text": "中性及跨性别者的权利，不容忽视。", "label": [{"entity": "中性", "start_idx": 0, "end_idx": 1, "type": "性别"}, {"entity": "跨性别者", "start_idx": 3, "end_idx": 6, "type": "性别"}]}, {"text": "非二元男性身份认同，亟需社会理解与尊重。", "label": [{"entity": "非二元", "start_idx": 0, "end_idx": 2, "type": "性别"}, {"entity": "男性", "start_idx": 3, "end_idx": 4, "type": "性别"}]}, {"text": "男性双性取向问题，需得到正视与支持。", "label": [{"entity": "男性", "start_idx": 0, "end_idx": 1, "type": "性别"}, {"entity": "双性", "start_idx": 2, "end_idx": 3, "type": "性别"}]}, {"text": "本合同适用于双性及女性客户，保障范围明确。", "label": [{"entity": "双性", "start_idx": 6, "end_idx": 7, "type": "性别"}, {"entity": "女性", "start_idx": 9, "end_idx": 10, "type": "性别"}]}, {"text": "非二元性别男性客户，亦享有合同规定之权利。", "label": [{"entity": "非二元", "start_idx": 0, "end_idx": 2, "type": "性别"}, {"entity": "男性", "start_idx": 5, "end_idx": 6, "type": "性别"}]}, {"text": "女性及跨性别群体在合同范围内享有平等保障。", "label": [{"entity": "女性", "start_idx": 0, "end_idx": 1, "type": "性别"}, {"entity": "跨性别群体", "start_idx": 3, "end_idx": 7, "type": "性别"}]}, {"text": "跨性别与雌雄同体客户均列入本合同保护范畴。", "label": [{"entity": "跨性别", "start_idx": 0, "end_idx": 2, "type": "性别"}, {"entity": "雌雄同体", "start_idx": 4, "end_idx": 7, "type": "性别"}]}, {"text": "雌雄同体及中性性别者，亦为本合同所涵盖对象。", "label": [{"entity": "雌雄同体", "start_idx": 0, "end_idx": 3, "type": "性别"}, {"entity": "中性", "start_idx": 5, "end_idx": 6, "type": "性别"}]}, {"text": "女性投资者在签订该金融合同时享有同等的权利与义务。", "label": [{"entity": "女性", "start_idx": 0, "end_idx": 1, "type": "性别"}]}, {"text": "男女双方在金融合同中承担的义务与享有的权益均等。", "label": [{"entity": "男", "start_idx": 0, "end_idx": 0, "type": "性别"}, {"entity": "女", "start_idx": 1, "end_idx": 1, "type": "性别"}]}, {"text": "该金融合同条款充分保障了中性及女性投资者的合法权益。", "label": [{"entity": "女性", "start_idx": 15, "end_idx": 16, "type": "性别"}]}, {"text": "男性及女性投资者在合同履行过程中均需遵守相同的规定。", "label": [{"entity": "男性", "start_idx": 0, "end_idx": 1, "type": "性别"}, {"entity": "女性", "start_idx": 3, "end_idx": 4, "type": "性别"}]}, {"text": "雌雄同体的观念在男性群体中引发了热烈讨论。", "label": [{"entity": "雌雄同体", "start_idx": 0, "end_idx": 3, "type": "性别"}, {"entity": "男性", "start_idx": 8, "end_idx": 9, "type": "性别"}]}, {"text": "中性风潮在男性时尚界逐渐兴起。", "label": [{"entity": "男性", "start_idx": 5, "end_idx": 6, "type": "性别"}]}, {"text": "女性与男性共同参与的活动，增进了两性间的理解与合作。", "label": [{"entity": "女性", "start_idx": 0, "end_idx": 1, "type": "性别"}, {"entity": "男性", "start_idx": 3, "end_idx": 4, "type": "性别"}]}, {"text": "男性角色在现代社会中面临着新的定位与挑战。", "label": [{"entity": "男性", "start_idx": 0, "end_idx": 1, "type": "性别"}]}, {"text": "跨性别者勇敢发声，为女性社群增添多元色彩。", "label": [{"entity": "跨性别者", "start_idx": 0, "end_idx": 3, "type": "性别"}, {"entity": "女性", "start_idx": 10, "end_idx": 11, "type": "性别"}]}, {"text": "男性投资者与女性投资者在金融合同中的权益均得到了明确的保护与规定。", "label": [{"entity": "男性", "start_idx": 0, "end_idx": 1, "type": "性别"}, {"entity": "女性", "start_idx": 6, "end_idx": 7, "type": "性别"}]}, {"text": "马来西亚与澳大利亚的医疗团队近日开展了一场跨国远程病例研讨会。", "label": [{"entity": "马来西亚", "start_idx": 0, "end_idx": 3, "type": "国籍"}, {"entity": "澳大利亚", "start_idx": 5, "end_idx": 8, "type": "国籍"}]}, {"text": "巴哈马卫生部门与日本医疗专家共同研究了一系列传染病例。", "label": [{"entity": "巴哈马", "start_idx": 0, "end_idx": 2, "type": "国籍"}, {"entity": "日本", "start_idx": 8, "end_idx": 9, "type": "国籍"}]}, {"text": "罗马尼亚医疗代表团访问泰国，就两国间医疗合作进行了深入交流。", "label": [{"entity": "罗马尼亚", "start_idx": 0, "end_idx": 3, "type": "国籍"}, {"entity": "泰国", "start_idx": 11, "end_idx": 12, "type": "国籍"}]}, {"text": "印度尼西亚与白俄罗斯在传染病防控领域展开了密切合作，共享病例数据。", "label": [{"entity": "印度尼西亚", "start_idx": 0, "end_idx": 4, "type": "国籍"}, {"entity": "白俄罗斯", "start_idx": 6, "end_idx": 9, "type": "国籍"}]}, {"text": "科威特医疗专家赴英国参加国际病例研讨会，交流诊疗经验。", "label": [{"entity": "科威特", "start_idx": 0, "end_idx": 2, "type": "国籍"}, {"entity": "英国", "start_idx": 8, "end_idx": 9, "type": "国籍"}]}, {"text": "越南菜真好吃，比美国快餐健康多了！", "label": [{"entity": "越南", "start_idx": 0, "end_idx": 1, "type": "国籍"}, {"entity": "美国", "start_idx": 8, "end_idx": 9, "type": "国籍"}]}, {"text": "巴西的狂欢节真热闹，西班牙的斗牛场也很刺激呢！", "label": [{"entity": "巴西", "start_idx": 0, "end_idx": 1, "type": "国籍"}, {"entity": "西班牙", "start_idx": 10, "end_idx": 12, "type": "国籍"}]}, {"text": "安哥拉的音乐很有感染力，不输给古巴的旋律哦！", "label": [{"entity": "安哥拉", "start_idx": 0, "end_idx": 2, "type": "国籍"}, {"entity": "古巴", "start_idx": 15, "end_idx": 16, "type": "国籍"}]}, {"text": "越南的河粉超级赞，匈牙利的炖牛肉也很出名吧！", "label": [{"entity": "越南", "start_idx": 0, "end_idx": 1, "type": "国籍"}, {"entity": "匈牙利", "start_idx": 9, "end_idx": 11, "type": "国籍"}]}, {"text": "土耳其的甜点很有名，和印度的咖喱一样让人上瘾。", "label": [{"entity": "土耳其", "start_idx": 0, "end_idx": 2, "type": "国籍"}, {"entity": "印度", "start_idx": 11, "end_idx": 12, "type": "国籍"}]}, {"text": "泰国与苏丹之间的司法互助协定，在本次法律文档中得到详尽阐述。", "label": [{"entity": "泰国", "start_idx": 0, "end_idx": 1, "type": "国籍"}, {"entity": "苏丹", "start_idx": 3, "end_idx": 4, "type": "国籍"}]}, {"text": "吉尔吉斯斯坦和瑞士于近期签署的引渡条约，已纳入本法律文档的附件中。", "label": [{"entity": "吉尔吉斯斯坦", "start_idx": 0, "end_idx": 5, "type": "国籍"}, {"entity": "瑞士", "start_idx": 7, "end_idx": 8, "type": "国籍"}]}, {"text": "巴勒斯坦和阿尔及利亚的边界争议，在本法律文档中有精确的界定描述。", "label": [{"entity": "巴勒斯坦", "start_idx": 0, "end_idx": 3, "type": "国籍"}, {"entity": "阿尔及利亚", "start_idx": 5, "end_idx": 9, "type": "国籍"}]}, {"text": "以色列与津巴布韦之间的商贸法律问题，文档中给予了明确的处理意见。", "label": [{"entity": "以色列", "start_idx": 0, "end_idx": 2, "type": "国籍"}, {"entity": "津巴布韦", "start_idx": 4, "end_idx": 7, "type": "国籍"}]}, {"text": "印度尼西亚及白俄罗斯之间的相关法律条款，在本文档中得到了适当的体现。", "label": [{"entity": "印度尼西亚", "start_idx": 0, "end_idx": 4, "type": "国籍"}, {"entity": "白俄罗斯", "start_idx": 6, "end_idx": 9, "type": "国籍"}]}, {"text": "乌拉圭留英海归，擅长多领域交流合作。", "label": [{"entity": "乌拉圭", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "坦桑尼亚学者曾在波兰交流，具有国际视野。", "label": [{"entity": "坦桑尼亚", "start_idx": 0, "end_idx": 3, "type": "国籍"}, {"entity": "波兰", "start_idx": 8, "end_idx": 9, "type": "国籍"}]}, {"text": "乌克兰籍工程师，摩洛哥项目经验丰富。", "label": [{"entity": "乌克兰", "start_idx": 0, "end_idx": 2, "type": "国籍"}, {"entity": "摩洛哥", "start_idx": 8, "end_idx": 10, "type": "国籍"}]}, {"text": "瑞典留学背景，加拿大公司工作经验。", "label": [{"entity": "瑞典", "start_idx": 0, "end_idx": 1, "type": "国籍"}, {"entity": "加拿大", "start_idx": 7, "end_idx": 9, "type": "国籍"}]}, {"text": "澳大利亚商业管理专业，哥伦比亚市场实践经历。", "label": [{"entity": "澳大利亚", "start_idx": 0, "end_idx": 3, "type": "国籍"}, {"entity": "哥伦比亚", "start_idx": 11, "end_idx": 14, "type": "国籍"}]}, {"text": "印度尼西亚的汇款最近是不是有问题啊，我朋友在突尼斯那边还没收到钱。", "label": [{"entity": "印度尼西亚", "start_idx": 0, "end_idx": 4, "type": "国籍"}, {"entity": "突尼斯", "start_idx": 22, "end_idx": 24, "type": "国籍"}]}, {"text": "你上次提的泰国那笔转账，好像比肯尼亚那笔慢多了。", "label": [{"entity": "泰国", "start_idx": 5, "end_idx": 6, "type": "国籍"}, {"entity": "肯尼亚", "start_idx": 15, "end_idx": 17, "type": "国籍"}]}, {"text": "乌克兰的账户和南非的搞混了，得赶紧查查。", "label": [{"entity": "乌克兰", "start_idx": 0, "end_idx": 2, "type": "国籍"}, {"entity": "南非", "start_idx": 7, "end_idx": 8, "type": "国籍"}]}, {"text": "亚美尼亚的银行手续费是不是比安哥拉的高啊，感觉有点亏。", "label": [{"entity": "亚美尼亚", "start_idx": 0, "end_idx": 3, "type": "国籍"}, {"entity": "安哥拉", "start_idx": 14, "end_idx": 16, "type": "国籍"}]}, {"text": "委内瑞拉那边的汇率变动真大，比英国这边刺激多了。", "label": [{"entity": "委内瑞拉", "start_idx": 0, "end_idx": 3, "type": "国籍"}, {"entity": "英国", "start_idx": 15, "end_idx": 16, "type": "国籍"}]}, {"text": "黎巴嫩与葡萄牙之间的引渡条约应立即生效。", "label": [{"entity": "黎巴嫩", "start_idx": 0, "end_idx": 2, "type": "国籍"}, {"entity": "葡萄牙", "start_idx": 4, "end_idx": 6, "type": "国籍"}]}, {"text": "蒙古及巴哈马须严格遵照国际法规定履行合作协议。", "label": [{"entity": "蒙古", "start_idx": 0, "end_idx": 1, "type": "国籍"}, {"entity": "巴哈马", "start_idx": 3, "end_idx": 5, "type": "国籍"}]}, {"text": "立陶宛及阿根廷两国应即刻执行有关双边贸易协定的条款。", "label": [{"entity": "立陶宛", "start_idx": 0, "end_idx": 2, "type": "国籍"}, {"entity": "阿根廷", "start_idx": 4, "end_idx": 6, "type": "国籍"}]}, {"text": "生态农场主，请立即与稀有金属交易员联系，商讨合作事宜。", "label": [{"entity": "生态农场主", "start_idx": 0, "end_idx": 4, "type": "职业"}, {"entity": "稀有金属交易员", "start_idx": 10, "end_idx": 16, "type": "职业"}]}, {"text": "茶艺馆主人，我命令你邀请纸牌魔术师来店里进行表演。", "label": [{"entity": "茶艺馆主人", "start_idx": 0, "end_idx": 4, "type": "职业"}, {"entity": "纸牌魔术师", "start_idx": 12, "end_idx": 16, "type": "职业"}]}, {"text": "无人机巡线员，速与纺织品检验员协同作业，确保产品质量。", "label": [{"entity": "无人机巡线员", "start_idx": 0, "end_idx": 5, "type": "职业"}, {"entity": "纺织品检验员", "start_idx": 9, "end_idx": 14, "type": "职业"}]}, {"text": "字体设计师，花艺设计师，你们两个需要合作完成这次的展览布置。", "label": [{"entity": "字体设计师", "start_idx": 0, "end_idx": 4, "type": "职业"}, {"entity": "花艺设计师", "start_idx": 6, "end_idx": 10, "type": "职业"}]}, {"text": "无人机巡线员，我要求你协助宠物行为训练师进行训练任务。", "label": [{"entity": "无人机巡线员", "start_idx": 0, "end_idx": 5, "type": "职业"}, {"entity": "宠物行为训练师", "start_idx": 13, "end_idx": 19, "type": "职业"}]}, {"text": "那个字体设计师跟我提过，他有个朋友是搞语音识别技术的专家，俩人总在一起探讨技术。", "label": [{"entity": "字体设计师", "start_idx": 2, "end_idx": 6, "type": "职业"}]}, {"text": "品牌故事讲述师小王和旅行定制师小张合作，给客户打造了一段难忘的旅行体验。", "label": [{"entity": "品牌故事讲述师", "start_idx": 0, "end_idx": 6, "type": "职业"}, {"entity": "旅行定制师", "start_idx": 10, "end_idx": 14, "type": "职业"}]}, {"text": "矿物勘探工程师跨界成了竖琴演奏家，这事儿在他们圈子里都传为佳话了。", "label": [{"entity": "矿物勘探工程师", "start_idx": 0, "end_idx": 6, "type": "职业"}, {"entity": "竖琴演奏家", "start_idx": 11, "end_idx": 15, "type": "职业"}]}, {"text": "电影道具制作师老李，转型去当了矿物勘探工程师，真是意想不到的职业道路啊。", "label": [{"entity": "电影道具制作师", "start_idx": 0, "end_idx": 6, "type": "职业"}, {"entity": "矿物勘探工程师", "start_idx": 15, "end_idx": 21, "type": "职业"}]}, {"text": "请怒族和侗族患者立即到前台领取就诊号码。", "label": [{"entity": "怒族", "start_idx": 1, "end_idx": 2, "type": "民族"}, {"entity": "侗族", "start_idx": 4, "end_idx": 5, "type": "民族"}]}, {"text": "独龙族与塔塔尔族的病例需要立刻进行详细比对。", "label": [{"entity": "独龙族", "start_idx": 0, "end_idx": 2, "type": "民族"}, {"entity": "塔塔尔族", "start_idx": 4, "end_idx": 7, "type": "民族"}]}, {"text": "请佤族阿昌族病患抓紧时间前往三号窗口挂号。", "label": [{"entity": "佤族", "start_idx": 1, "end_idx": 2, "type": "民族"}, {"entity": "阿昌族", "start_idx": 3, "end_idx": 5, "type": "民族"}]}, {"text": "白族和布朗族的血液检查报告必须马上核对。", "label": [{"entity": "白族", "start_idx": 0, "end_idx": 1, "type": "民族"}, {"entity": "布朗族", "start_idx": 3, "end_idx": 5, "type": "民族"}]}, {"text": "门巴族和鄂伦春族的会诊安排已定，请相关人员做好准备。", "label": [{"entity": "门巴族", "start_idx": 0, "end_idx": 2, "type": "民族"}, {"entity": "鄂伦春族", "start_idx": 4, "end_idx": 7, "type": "民族"}]}, {"text": "独龙族的兄弟姐妹们，你们和瑶族的朋友们聚在一起真是太棒了！", "label": [{"entity": "独龙族", "start_idx": 0, "end_idx": 2, "type": "民族"}, {"entity": "瑶族", "start_idx": 13, "end_idx": 14, "type": "民族"}]}, {"text": "柯尔克孜族和鄂温克族的舞蹈真是各有特色，都好想学啊！", "label": [{"entity": "柯尔克孜族", "start_idx": 0, "end_idx": 4, "type": "民族"}, {"entity": "鄂温克族", "start_idx": 6, "end_idx": 9, "type": "民族"}]}, {"text": "彝族的小哥哥拉祜族的小姐姐，听说你们俩恋爱了？", "label": [{"entity": "彝族", "start_idx": 0, "end_idx": 1, "type": "民族"}, {"entity": "拉祜族", "start_idx": 6, "end_idx": 8, "type": "民族"}]}, {"text": "景颇族的风景真是美如画，不输给藏族的雪山呢！", "label": [{"entity": "景颇族", "start_idx": 0, "end_idx": 2, "type": "民族"}, {"entity": "藏族", "start_idx": 15, "end_idx": 16, "type": "民族"}]}, {"text": "水族的朋友们，塔塔尔族的音乐你们觉得怎么样，是不是也很棒？", "label": [{"entity": "水族", "start_idx": 0, "end_idx": 1, "type": "民族"}, {"entity": "塔塔尔族", "start_idx": 7, "end_idx": 10, "type": "民族"}]}, {"text": "俄罗斯族与汉族在金融合同执行中享有同等的权利与义务。", "label": [{"entity": "俄罗斯族", "start_idx": 0, "end_idx": 3, "type": "民族"}, {"entity": "汉族", "start_idx": 5, "end_idx": 6, "type": "民族"}]}, {"text": "纳西族与撒拉族的企业在本次金融合作中达成了一致意见。", "label": [{"entity": "纳西族", "start_idx": 0, "end_idx": 2, "type": "民族"}, {"entity": "撒拉族", "start_idx": 4, "end_idx": 6, "type": "民族"}]}, {"text": "高山族与侗族地区的金融项目预计将促进当地经济发展。", "label": [{"entity": "高山族", "start_idx": 0, "end_idx": 2, "type": "民族"}, {"entity": "侗族", "start_idx": 4, "end_idx": 5, "type": "民族"}]}, {"text": "鄂伦春族与珞巴族金融机构共同签署了合作框架协议。", "label": [{"entity": "鄂伦春族", "start_idx": 0, "end_idx": 3, "type": "民族"}, {"entity": "珞巴族", "start_idx": 5, "end_idx": 7, "type": "民族"}]}, {"text": "仡佬族与彝族企业间的金融合同纠纷已得到妥善解决。", "label": [{"entity": "仡佬族", "start_idx": 0, "end_idx": 2, "type": "民族"}, {"entity": "彝族", "start_idx": 4, "end_idx": 5, "type": "民族"}]}, {"text": "佤族和达斡尔族患者病例显示，其病情发展具有相似性。", "label": [{"entity": "佤族", "start_idx": 0, "end_idx": 1, "type": "民族"}, {"entity": "达斡尔族", "start_idx": 3, "end_idx": 6, "type": "民族"}]}, {"text": "门巴族与珞巴族患者的治疗方案需根据民族特点进行相应调整。", "label": [{"entity": "门巴族", "start_idx": 0, "end_idx": 2, "type": "民族"}, {"entity": "珞巴族", "start_idx": 4, "end_idx": 6, "type": "民族"}]}, {"text": "彝族与哈尼族患者的临床表现存在一定差异，需分别记录。", "label": [{"entity": "彝族", "start_idx": 0, "end_idx": 1, "type": "民族"}, {"entity": "哈尼族", "start_idx": 3, "end_idx": 5, "type": "民族"}]}, {"text": "门巴族和维吾尔族患者对同种药物的反应差异显著，值得注意。", "label": [{"entity": "门巴族", "start_idx": 0, "end_idx": 2, "type": "民族"}, {"entity": "维吾尔族", "start_idx": 4, "end_idx": 7, "type": "民族"}]}, {"text": "保安族与柯尔克孜族患者的康复周期及用药剂量各有特点。", "label": [{"entity": "保安族", "start_idx": 0, "end_idx": 2, "type": "民族"}, {"entity": "柯尔克孜族", "start_idx": 4, "end_idx": 8, "type": "民族"}]}, {"text": "我是一名求职者，来自佤族，和鄂温克族的小伙伴一起参加过活动。", "label": [{"entity": "佤族", "start_idx": 10, "end_idx": 11, "type": "民族"}, {"entity": "鄂温克族", "start_idx": 14, "end_idx": 17, "type": "民族"}]}, {"text": "独龙族的风情我也有所了解，我们朝鲜族的朋友都很热情。", "label": [{"entity": "独龙族", "start_idx": 0, "end_idx": 2, "type": "民族"}, {"entity": "朝鲜族", "start_idx": 15, "end_idx": 17, "type": "民族"}]}, {"text": "之前工作，我的景颇族同事教了我不少畲族的文化知识。", "label": [{"entity": "景颇族", "start_idx": 7, "end_idx": 9, "type": "民族"}, {"entity": "畲族", "start_idx": 17, "end_idx": 18, "type": "民族"}]}, {"text": "这次求职，希望能和彝族、哈尼族的同事们一起共事。", "label": [{"entity": "彝族", "start_idx": 9, "end_idx": 10, "type": "民族"}, {"entity": "哈尼族", "start_idx": 12, "end_idx": 14, "type": "民族"}]}, {"text": "金融合同教育水平要求分析显示，高中毕业生与大专学历人士均符合资格。", "label": [{"entity": "高中毕业生", "start_idx": 15, "end_idx": 19, "type": "教育背景"}, {"entity": "大专学历人士", "start_idx": 21, "end_idx": 26, "type": "教育背景"}]}, {"text": "在深入探讨金融合同教育层次时，我们发现技校毕业生乃至博士学位持有人均在规定范围内。", "label": [{"entity": "技校毕业生", "start_idx": 19, "end_idx": 23, "type": "教育背景"}, {"entity": "博士学位持有人", "start_idx": 26, "end_idx": 32, "type": "教育背景"}]}, {"text": "本次金融合同条款明确，针对学历要求，涵盖了MBA及EMBA等专业学位。", "label": [{"entity": "MBA", "start_idx": 21, "end_idx": 23, "type": "教育背景"}, {"entity": "EMBA", "start_idx": 25, "end_idx": 28, "type": "教育背景"}]}, {"text": "合同条款进一步指出，中专毕业生及MBA持有者均为合格的申请者。", "label": [{"entity": "中专毕业生", "start_idx": 10, "end_idx": 14, "type": "教育背景"}, {"entity": "MBA持有者", "start_idx": 16, "end_idx": 21, "type": "教育背景"}]}, {"text": "金融合同规定的学历门槛中，硕士研究生与硕士学位获得者均被纳入考量范围。", "label": [{"entity": "硕士研究生", "start_idx": 13, "end_idx": 17, "type": "教育背景"}, {"entity": "硕士学位获得者", "start_idx": 19, "end_idx": 25, "type": "教育背景"}]}, {"text": "自学背景的金融合同签订者中，不乏海外留学的经历人士。", "label": [{"entity": "自学背景", "start_idx": 0, "end_idx": 3, "type": "教育背景"}, {"entity": "海外留学的经历", "start_idx": 16, "end_idx": 22, "type": "教育背景"}]}, {"text": "成人教育领域，网络教育方式逐渐成为重要的学习途径。", "label": [{"entity": "成人教育", "start_idx": 0, "end_idx": 3, "type": "教育背景"}]}, {"text": "在高中毕业基础上，部分人士选择进一步深造，攻读EMBA课程。", "label": [{"entity": "高中毕业", "start_idx": 1, "end_idx": 4, "type": "教育背景"}, {"entity": "EMBA课程", "start_idx": 23, "end_idx": 28, "type": "教育背景"}]}, {"text": "小学毕业的合同方，也有不少成功攻读至硕士研究生学位。", "label": [{"entity": "小学毕业", "start_idx": 0, "end_idx": 3, "type": "教育背景"}, {"entity": "硕士研究生学位", "start_idx": 18, "end_idx": 24, "type": "教育背景"}]}, {"text": "中专学历的合同签订者，多数通过成人教育途径提升自身素质。", "label": [{"entity": "中专学历", "start_idx": 0, "end_idx": 3, "type": "教育背景"}, {"entity": "成人教育", "start_idx": 15, "end_idx": 18, "type": "教育背景"}]}, {"text": "客服记录显示，该客户小学毕业后通过自学积累了丰富知识。", "label": [{"entity": "小学毕业", "start_idx": 10, "end_idx": 13, "type": "教育背景"}]}, {"text": "在线客服交谈中，一位持有硕士学位的技校教师分享了自己的经验。", "label": [{"entity": "硕士学位", "start_idx": 12, "end_idx": 15, "type": "教育背景"}]}, {"text": "客服聊天中了解到，该用户小学毕业后续读了中专课程。", "label": [{"entity": "小学毕业", "start_idx": 12, "end_idx": 15, "type": "教育背景"}, {"entity": "中专课程", "start_idx": 20, "end_idx": 23, "type": "教育背景"}]}, {"text": "本科毕业的客服人员在与一位博士学历客户的交流中获益良多。", "label": [{"entity": "本科毕业", "start_idx": 0, "end_idx": 3, "type": "教育背景"}, {"entity": "博士学历", "start_idx": 13, "end_idx": 16, "type": "教育背景"}]}, {"text": "某在线平台客服记录显示，一位自学的硕士成功解决了技术问题。", "label": [{"entity": "硕士", "start_idx": 17, "end_idx": 18, "type": "教育背景"}]}, {"text": "那个高中毕业的小伙子，你猜现在混得怎么样？人家都博士毕业了！", "label": [{"entity": "高中毕业", "start_idx": 2, "end_idx": 5, "type": "教育背景"}, {"entity": "博士毕业", "start_idx": 24, "end_idx": 27, "type": "教育背景"}]}, {"text": "一提起海外留学，我就想起我们本科那会儿的梦想。", "label": [{"entity": "本科", "start_idx": 14, "end_idx": 15, "type": "教育背景"}]}, {"text": "他是中专毕业，可人家现在大专文凭在手，能力一点也不差。", "label": [{"entity": "中专毕业", "start_idx": 2, "end_idx": 5, "type": "教育背景"}, {"entity": "大专文凭", "start_idx": 12, "end_idx": 15, "type": "教育背景"}]}, {"text": "别看他只是大专毕业，那一身本事可都是自学来的。", "label": [{"entity": "大专毕业", "start_idx": 5, "end_idx": 8, "type": "教育背景"}]}, {"text": "我们村那个初中毕业的，现在不也混了个大专文凭，挺厉害的。", "label": [{"entity": "初中毕业", "start_idx": 5, "end_idx": 8, "type": "教育背景"}, {"entity": "大专文凭", "start_idx": 18, "end_idx": 21, "type": "教育背景"}]}, {"text": "成人教育领域内，硕士研究生学历已成为就业的重要门槛。", "label": [{"entity": "硕士研究生学历", "start_idx": 8, "end_idx": 14, "type": "教育背景"}]}, {"text": "技校学生通过努力，亦可攻读硕士研究生学位，实现学历提升。", "label": [{"entity": "技校学生", "start_idx": 0, "end_idx": 3, "type": "教育背景"}, {"entity": "硕士研究生学位", "start_idx": 13, "end_idx": 19, "type": "教育背景"}]}, {"text": "网络教育为攻读博士学位提供了灵活的学习方式。", "label": [{"entity": "博士学位", "start_idx": 7, "end_idx": 10, "type": "教育背景"}]}, {"text": "法律文档规定，硕士研究生与博士研究生在学术研究上享有不同权益。", "label": [{"entity": "硕士研究生", "start_idx": 7, "end_idx": 11, "type": "教育背景"}, {"entity": "博士研究生", "start_idx": 13, "end_idx": 17, "type": "教育背景"}]}, {"text": "关于未婚同居且未登记的情况，请您详细说明一下具体问题。", "label": [{"entity": "未婚", "start_idx": 2, "end_idx": 3, "type": "婚姻状况"}]}, {"text": "请您明确告知，是未婚还是已婚状态，以便我为您提供相应帮助。", "label": [{"entity": "未婚", "start_idx": 8, "end_idx": 9, "type": "婚姻状况"}, {"entity": "已婚", "start_idx": 12, "end_idx": 13, "type": "婚姻状况"}]}, {"text": "若您正在同居且婚姻处于存续中，这部分信息对处理您的请求很重要。", "label": [{"entity": "同居", "start_idx": 4, "end_idx": 5, "type": "婚姻状况"}, {"entity": "婚姻处于存续中", "start_idx": 7, "end_idx": 13, "type": "婚姻状况"}]}, {"text": "对于离异后再婚的情况，请问您需要咨询哪方面的服务？", "label": [{"entity": "离异", "start_idx": 2, "end_idx": 3, "type": "婚姻状况"}, {"entity": "再婚", "start_idx": 5, "end_idx": 6, "type": "婚姻状况"}]}, {"text": "若您的婚姻状况未说明，或是已订婚状态，请提供更多信息以便我们为您解答。", "label": [{"entity": "未说明", "start_idx": 7, "end_idx": 9, "type": "婚姻状况"}, {"entity": "已订婚", "start_idx": 13, "end_idx": 15, "type": "婚姻状况"}]}, {"text": "未婚同居一方在订婚后的财产分配需明确。", "label": [{"entity": "未婚", "start_idx": 0, "end_idx": 1, "type": "婚姻状况"}]}, {"text": "订婚双方如若分居，应遵守相关财产分割规定。", "label": [{"entity": "订婚", "start_idx": 0, "end_idx": 1, "type": "婚姻状况"}]}, {"text": "分居期间，双方如未婚同居需另立协议。", "label": [{"entity": "未婚", "start_idx": 8, "end_idx": 9, "type": "婚姻状况"}]}, {"text": "未登记结婚的双方，在未婚状态下的财产权益应予以明确。", "label": [{"entity": "未登记结婚", "start_idx": 0, "end_idx": 4, "type": "婚姻状况"}, {"entity": "未婚", "start_idx": 10, "end_idx": 11, "type": "婚姻状况"}]}, {"text": "对于丧偶后选择同居的当事人，应特别注意遗产分配问题。", "label": [{"entity": "丧偶", "start_idx": 2, "end_idx": 3, "type": "婚姻状况"}]}, {"text": "在现代社会中，部分情侣选择先分居后同居，试图以此方式增进彼此了解。", "label": [{"entity": "分居", "start_idx": 14, "end_idx": 15, "type": "婚姻状况"}, {"entity": "同居", "start_idx": 17, "end_idx": 18, "type": "婚姻状况"}]}, {"text": "订婚并不意味着终点，不少人在取消婚约后选择了再婚，开始了新的生活。", "label": [{"entity": "订婚", "start_idx": 0, "end_idx": 1, "type": "婚姻状况"}, {"entity": "取消婚约", "start_idx": 14, "end_idx": 17, "type": "婚姻状况"}, {"entity": "再婚", "start_idx": 22, "end_idx": 23, "type": "婚姻状况"}]}, {"text": "社交平台上，单身与已婚人士的互动日益频繁，界限逐渐模糊。", "label": [{"entity": "单身", "start_idx": 6, "end_idx": 7, "type": "婚姻状况"}, {"entity": "已婚人士", "start_idx": 9, "end_idx": 12, "type": "婚姻状况"}]}, {"text": "单身者与分居人士在社交媒体上形成了独特的社交群体，相互支持。", "label": [{"entity": "单身者", "start_idx": 0, "end_idx": 2, "type": "婚姻状况"}, {"entity": "分居人士", "start_idx": 4, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "面对未明确关系的状态，部分人选择与伴侣同居，以此探索未来的可能性。", "label": [{"entity": "同居", "start_idx": 19, "end_idx": 20, "type": "婚姻状况"}]}, {"text": "请务必在简历中明确标注您的婚姻状况，未婚同居或已婚必须如实填写。", "label": [{"entity": "未婚", "start_idx": 18, "end_idx": 19, "type": "婚姻状况"}, {"entity": "已婚", "start_idx": 23, "end_idx": 24, "type": "婚姻状况"}]}, {"text": "在填写同居情况时，请注明是否婚姻存续中。", "label": [{"entity": "婚姻存续中", "start_idx": 14, "end_idx": 18, "type": "婚姻状况"}]}, {"text": "如属单身并已订婚，请在简历中具体说明。", "label": [{"entity": "单身", "start_idx": 2, "end_idx": 3, "type": "婚姻状况"}, {"entity": "订婚", "start_idx": 6, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "单身且订婚者，请详细阐述个人情感状态。", "label": [{"entity": "单身", "start_idx": 0, "end_idx": 1, "type": "婚姻状况"}, {"entity": "订婚", "start_idx": 3, "end_idx": 4, "type": "婚姻状况"}]}, {"text": "若您处于同居状态但具体情况不便说明，可在简历中标注为“未说明”。", "label": [{"entity": "同居状态", "start_idx": 4, "end_idx": 7, "type": "婚姻状况"}, {"entity": "未说明", "start_idx": 27, "end_idx": 29, "type": "婚姻状况"}]}, {"text": "小张和小李同居了，不过他们俩的关系合同里还没说明白。", "label": [{"entity": "同居", "start_idx": 5, "end_idx": 6, "type": "婚姻状况"}]}, {"text": "老王再婚后，跟未婚同居时的协议有点冲突，得重新弄清楚。", "label": [{"entity": "再婚", "start_idx": 2, "end_idx": 3, "type": "婚姻状况"}, {"entity": "未婚", "start_idx": 7, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "他们俩分居有一阵子了，主要是因为一方丧偶之后情绪一直不稳定。", "label": [{"entity": "分居", "start_idx": 3, "end_idx": 4, "type": "婚姻状况"}]}, {"text": "这合同里写了再婚和分居的情况，处理起来可得小心点儿。", "label": [{"entity": "再婚", "start_idx": 6, "end_idx": 7, "type": "婚姻状况"}, {"entity": "分居", "start_idx": 9, "end_idx": 10, "type": "婚姻状况"}]}, {"text": "俩人未婚同居，后来决定婚姻解除，这事儿在合同里怎么规定的来着？", "label": [{"entity": "未婚", "start_idx": 2, "end_idx": 3, "type": "婚姻状况"}, {"entity": "婚姻解除", "start_idx": 11, "end_idx": 14, "type": "婚姻状况"}]}, {"text": "病例1：该患者离异，目前未婚，正接受心理辅导。", "label": [{"entity": "离异", "start_idx": 7, "end_idx": 8, "type": "婚姻状况"}, {"entity": "未婚", "start_idx": 12, "end_idx": 13, "type": "婚姻状况"}]}, {"text": "病例2：患者在婚姻存续中，现已再婚，家庭和睦。", "label": [{"entity": "婚姻存续中", "start_idx": 7, "end_idx": 11, "type": "婚姻状况"}, {"entity": "再婚", "start_idx": 15, "end_idx": 16, "type": "婚姻状况"}]}, {"text": "病例3：患者丧偶后，与现任伴侣同居，双方关系稳定。", "label": [{"entity": "丧偶", "start_idx": 6, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "病例4：患者未婚同居，双方均保持未婚状态，共同生活多年。", "label": [{"entity": "未婚", "start_idx": 6, "end_idx": 7, "type": "婚姻状况"}, {"entity": "未婚状态", "start_idx": 16, "end_idx": 19, "type": "婚姻状况"}]}, {"text": "病例5：患者再婚，但对于之前的婚姻状况未作具体说明。", "label": [{"entity": "再婚", "start_idx": 6, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "在求职简历中，张先生的婚姻状况为未婚同居，而李女士则为再婚。", "label": [{"entity": "未婚同居", "start_idx": 16, "end_idx": 19, "type": "婚姻状况"}, {"entity": "再婚", "start_idx": 27, "end_idx": 28, "type": "婚姻状况"}]}, {"text": "赵先生的个人情况一栏标注了离异，而与前妻的关系未作登记。", "label": [{"entity": "离异", "start_idx": 13, "end_idx": 14, "type": "婚姻状况"}]}, {"text": "面试官在审视简历时，发现陈小姐未婚，而另一位应聘者王女士已婚。", "label": [{"entity": "未婚", "start_idx": 15, "end_idx": 16, "type": "婚姻状况"}, {"entity": "已婚", "start_idx": 28, "end_idx": 29, "type": "婚姻状况"}]}, {"text": "人事部在审阅简历时注意到，林先生未婚同居且曾丧偶。", "label": [{"entity": "未婚", "start_idx": 16, "end_idx": 17, "type": "婚姻状况"}, {"entity": "同居", "start_idx": 18, "end_idx": 19, "type": "婚姻状况"}, {"entity": "曾丧偶", "start_idx": 21, "end_idx": 23, "type": "婚姻状况"}]}, {"text": "此外，还有两位候选人，一位是同居关系的孙先生，另一位是未婚同居的周女士。", "label": [{"entity": "同居关系", "start_idx": 14, "end_idx": 17, "type": "婚姻状况"}, {"entity": "未婚同居", "start_idx": 27, "end_idx": 30, "type": "婚姻状况"}]}, {"text": "病例1：患者家庭状况为离异，配偶已丧偶。", "label": [{"entity": "离异", "start_idx": 11, "end_idx": 12, "type": "婚姻状况"}, {"entity": "丧偶", "start_idx": 17, "end_idx": 18, "type": "婚姻状况"}]}, {"text": "病例2：患者个人信息显示，婚姻状态为丧偶，未进行登记。", "label": [{"entity": "丧偶", "start_idx": 18, "end_idx": 19, "type": "婚姻状况"}]}, {"text": "病例3：据病例记录，患者经历了婚姻解除，具体原因未说明。", "label": [{"entity": "婚姻解除", "start_idx": 15, "end_idx": 18, "type": "婚姻状况"}]}, {"text": "病例4：在查阅患者资料时发现，其婚姻已经解除，目前处于再婚状态。", "label": [{"entity": "解除", "start_idx": 20, "end_idx": 21, "type": "婚姻状况"}, {"entity": "再婚状态", "start_idx": 27, "end_idx": 30, "type": "婚姻状况"}]}, {"text": "病例5：患者现居住状况为分居，与未婚伴侣同居。", "label": [{"entity": "分居", "start_idx": 12, "end_idx": 13, "type": "婚姻状况"}, {"entity": "未婚", "start_idx": 16, "end_idx": 17, "type": "婚姻状况"}]}, {"text": "已婚者应依法分居，维护法律尊严。", "label": [{"entity": "已婚者", "start_idx": 0, "end_idx": 2, "type": "婚姻状况"}]}, {"text": "婚姻存续中，不得擅自与他人订婚。", "label": [{"entity": "婚姻存续中", "start_idx": 0, "end_idx": 4, "type": "婚姻状况"}]}, {"text": "同居关系需明确，不得含糊其辞。", "label": [{"entity": "同居关系", "start_idx": 0, "end_idx": 3, "type": "婚姻状况"}]}, {"text": "离异双方须正式解除婚姻关系。", "label": [{"entity": "离异", "start_idx": 0, "end_idx": 1, "type": "婚姻状况"}]}, {"text": "解除婚姻关系后，方可重新订婚。", "label": [{"entity": "解除婚姻关系", "start_idx": 0, "end_idx": 5, "type": "婚姻状况"}, {"entity": "重新订婚", "start_idx": 10, "end_idx": 13, "type": "婚姻状况"}]}, {"text": "哎，我听说你最近婚姻解除，真不幸，和丧偶的情况一样让人难过。", "label": [{"entity": "婚姻解除", "start_idx": 8, "end_idx": 11, "type": "婚姻状况"}, {"entity": "丧偶", "start_idx": 18, "end_idx": 19, "type": "婚姻状况"}]}, {"text": "那谁，他离异后又遇到了丧偶的伴侣，两个人都很不容易。", "label": [{"entity": "离异", "start_idx": 4, "end_idx": 5, "type": "婚姻状况"}, {"entity": "丧偶", "start_idx": 11, "end_idx": 12, "type": "婚姻状况"}]}, {"text": "他俩一直未登记，也没说明啥关系，就这样处着。", "label": [{"entity": "未登记", "start_idx": 4, "end_idx": 6, "type": "婚姻状况"}]}, {"text": "我朋友婚姻存续中呢，还在考虑要不要再婚，挺头疼的。", "label": [{"entity": "婚姻存续中", "start_idx": 3, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "单身和婚姻解除，这两种状态在金融合同里处理起来可是大不相同。", "label": [{"entity": "单身", "start_idx": 0, "end_idx": 1, "type": "婚姻状况"}, {"entity": "婚姻解除", "start_idx": 3, "end_idx": 6, "type": "婚姻状况"}]}, {"text": "我觉得自由主义和自由派的观点其实挺相似的，都强调个人自由嘛。", "label": [{"entity": "自由主义", "start_idx": 3, "end_idx": 6, "type": "政治倾向"}, {"entity": "自由派", "start_idx": 8, "end_idx": 10, "type": "政治倾向"}]}, {"text": "中立的态度我理解，但民粹主义就有点过激了，你觉得呢？", "label": [{"entity": "中立", "start_idx": 0, "end_idx": 1, "type": "政治倾向"}, {"entity": "民粹主义", "start_idx": 10, "end_idx": 13, "type": "政治倾向"}]}, {"text": "极左和民粹主义有时候会让人分不清楚，不过本质还是不一样的。", "label": [{"entity": "极左", "start_idx": 0, "end_idx": 1, "type": "政治倾向"}, {"entity": "民粹主义", "start_idx": 3, "end_idx": 6, "type": "政治倾向"}]}, {"text": "中立和极右的立场差得有点远，要达成共识估计挺难。", "label": [{"entity": "中立", "start_idx": 0, "end_idx": 1, "type": "政治倾向"}, {"entity": "极右", "start_idx": 3, "end_idx": 4, "type": "政治倾向"}]}, {"text": "无党派和改革派都是希望有所改变，不过方向可能不太一样。", "label": [{"entity": "无党派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "改革派", "start_idx": 4, "end_idx": 6, "type": "政治倾向"}]}, {"text": "无党派环保主义者积极参与金融合同绿色条款的制定与推广。", "label": [{"entity": "无党派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}]}, {"text": "极左民粹主义者在金融合同改革中提出了一系列极端主张。", "label": [{"entity": "极左", "start_idx": 0, "end_idx": 1, "type": "政治倾向"}, {"entity": "民粹主义者", "start_idx": 2, "end_idx": 6, "type": "政治倾向"}]}, {"text": "民族主义温和派在金融合同争议中倡导理性对话，寻求共赢。", "label": [{"entity": "民族主义温和派", "start_idx": 0, "end_idx": 6, "type": "政治倾向"}]}, {"text": "中间派与保守派就金融合同监管力度展开深入探讨。", "label": [{"entity": "中间派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "保守派", "start_idx": 4, "end_idx": 6, "type": "政治倾向"}]}, {"text": "自由主义激进派呼吁在金融合同中加入更多创新与风险承担条款。", "label": [{"entity": "自由主义激进派", "start_idx": 0, "end_idx": 6, "type": "政治倾向"}]}, {"text": "这次改革，技术官僚和民粹主义者的观点好像挺难达成一致啊。", "label": [{"entity": "技术官僚", "start_idx": 5, "end_idx": 8, "type": "政治倾向"}, {"entity": "民粹主义者", "start_idx": 10, "end_idx": 14, "type": "政治倾向"}]}, {"text": "保守派和温和派在金融合同这个问题上的立场截然不同。", "label": [{"entity": "保守派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "温和派", "start_idx": 4, "end_idx": 6, "type": "政治倾向"}]}, {"text": "中立人士这次倒是倾向于保守主义的观点。", "label": [{"entity": "中立", "start_idx": 0, "end_idx": 1, "type": "政治倾向"}, {"entity": "保守主义", "start_idx": 11, "end_idx": 14, "type": "政治倾向"}]}, {"text": "改革派和中间派讨论得挺热烈，希望能达成共识。", "label": [{"entity": "改革派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "中间派", "start_idx": 4, "end_idx": 6, "type": "政治倾向"}]}, {"text": "进步派和极左这次在金融政策上意见相左。", "label": [{"entity": "进步派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "极左", "start_idx": 4, "end_idx": 5, "type": "政治倾向"}]}, {"text": "极右自由主义观点必须严格从病例分析中排除。", "label": [{"entity": "极右自由主义", "start_idx": 0, "end_idx": 5, "type": "政治倾向"}]}, {"text": "保守主义和社会主义理念需深入对比研究。", "label": [{"entity": "保守主义", "start_idx": 0, "end_idx": 3, "type": "政治倾向"}, {"entity": "社会主义", "start_idx": 5, "end_idx": 8, "type": "政治倾向"}]}, {"text": "保守派在保守主义原则上切勿动摇。", "label": [{"entity": "保守派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "保守主义", "start_idx": 4, "end_idx": 7, "type": "政治倾向"}]}, {"text": "改革派应借鉴自由主义思想，推动医疗改革。", "label": [{"entity": "改革派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "自由主义", "start_idx": 6, "end_idx": 9, "type": "政治倾向"}]}, {"text": "技术官僚与保守派合作，共同优化治疗方案。", "label": [{"entity": "技术官僚", "start_idx": 0, "end_idx": 3, "type": "政治倾向"}, {"entity": "保守派", "start_idx": 5, "end_idx": 7, "type": "政治倾向"}]}, {"text": "社会主义理念下，改革派提出了一系列创新的社交政策。", "label": [{"entity": "改革派", "start_idx": 8, "end_idx": 10, "type": "政治倾向"}]}, {"text": "传统派与极左势力在意识形态上展开了激烈讨论。", "label": [{"entity": "传统派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "极左势力", "start_idx": 4, "end_idx": 7, "type": "政治倾向"}]}, {"text": "在政策辩论中，传统派和中间派达成了部分共识。", "label": [{"entity": "传统派", "start_idx": 7, "end_idx": 9, "type": "政治倾向"}, {"entity": "中间派", "start_idx": 11, "end_idx": 13, "type": "政治倾向"}]}, {"text": "无党派环保主义者积极倡导绿色生活方式。", "label": [{"entity": "无党派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}]}, {"text": "进步派与保守派在文化改革议题上存在明显分歧。", "label": [{"entity": "进步派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "保守派", "start_idx": 4, "end_idx": 6, "type": "政治倾向"}]}, {"text": "社会主义理念必须引领，极左思潮务必警惕！", "label": [{"entity": "社会主义理念", "start_idx": 0, "end_idx": 5, "type": "政治倾向"}, {"entity": "极左思潮", "start_idx": 11, "end_idx": 14, "type": "政治倾向"}]}, {"text": "女权主义需关注，中间派立场要明确。", "label": [{"entity": "女权主义", "start_idx": 0, "end_idx": 3, "type": "政治倾向"}, {"entity": "中间派", "start_idx": 8, "end_idx": 10, "type": "政治倾向"}]}, {"text": "环保主义要提倡，温和派路线应坚持。", "label": [{"entity": "环保主义", "start_idx": 0, "end_idx": 3, "type": "政治倾向"}, {"entity": "温和派", "start_idx": 8, "end_idx": 10, "type": "政治倾向"}]}, {"text": "社会主义思想为主流，中间派观点要兼顾。", "label": [{"entity": "社会主义思想", "start_idx": 0, "end_idx": 5, "type": "政治倾向"}, {"entity": "中间派", "start_idx": 10, "end_idx": 12, "type": "政治倾向"}]}, {"text": "无党派也应投身环保，共促绿色未来。", "label": [{"entity": "无党派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}]}, {"text": "改革派与进步派在金融合同利率调整上达成一致。", "label": [{"entity": "改革派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "进步派", "start_idx": 4, "end_idx": 6, "type": "政治倾向"}]}, {"text": "环保主义者在中间派的支持下，对金融合同中的绿色条款给予肯定。", "label": [{"entity": "环保主义者", "start_idx": 0, "end_idx": 4, "type": "政治倾向"}, {"entity": "中间派", "start_idx": 6, "end_idx": 8, "type": "政治倾向"}]}, {"text": "技术官僚与中间派就金融合同中的创新技术进行了深入探讨。", "label": [{"entity": "技术官僚", "start_idx": 0, "end_idx": 3, "type": "政治倾向"}, {"entity": "中间派", "start_idx": 5, "end_idx": 7, "type": "政治倾向"}]}, {"text": "保守派与传统派在金融合同的风险控制条款上持有相同立场。", "label": [{"entity": "保守派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "传统派", "start_idx": 4, "end_idx": 6, "type": "政治倾向"}]}, {"text": "中立无党派人士对金融合同中的公平性原则表示赞同。", "label": [{"entity": "中立无党派", "start_idx": 0, "end_idx": 4, "type": "政治倾向"}]}, {"text": "传统派与改革派在本次政策辩论中分别阐述了己方观点。", "label": [{"entity": "传统派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "改革派", "start_idx": 4, "end_idx": 6, "type": "政治倾向"}]}, {"text": "激进派与自由主义者在经济改革议题上呈现出明显的立场分歧。", "label": [{"entity": "激进派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "自由主义者", "start_idx": 4, "end_idx": 8, "type": "政治倾向"}]}, {"text": "民粹主义和环保主义者在环境保护议题上达成了罕见的共识。", "label": [{"entity": "民粹主义", "start_idx": 0, "end_idx": 3, "type": "政治倾向"}, {"entity": "环保主义者", "start_idx": 5, "end_idx": 9, "type": "政治倾向"}]}, {"text": "激进派与技术官僚在科技创新政策上展开了激烈的辩论。", "label": [{"entity": "激进派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "技术官僚", "start_idx": 4, "end_idx": 7, "type": "政治倾向"}]}, {"text": "改革派与自由派在政治体制改革问题上提出了各自的见解。", "label": [{"entity": "改革派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "自由派", "start_idx": 4, "end_idx": 6, "type": "政治倾向"}]}, {"text": "其实我觉得保守主义和中立之间，还是可以找到平衡点的。", "label": [{"entity": "保守主义", "start_idx": 5, "end_idx": 8, "type": "政治倾向"}, {"entity": "中立", "start_idx": 10, "end_idx": 11, "type": "政治倾向"}]}, {"text": "中立的态度有时候和保守主义也不是完全矛盾的。", "label": [{"entity": "中立", "start_idx": 0, "end_idx": 1, "type": "政治倾向"}, {"entity": "保守主义", "start_idx": 9, "end_idx": 12, "type": "政治倾向"}]}, {"text": "激进派和温和派的争论，好像一直都没个结果。", "label": [{"entity": "激进派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "温和派", "start_idx": 4, "end_idx": 6, "type": "政治倾向"}]}, {"text": "极左的思想和温和派相比，还是后者更受欢迎。", "label": [{"entity": "极左", "start_idx": 0, "end_idx": 1, "type": "政治倾向"}, {"entity": "温和派", "start_idx": 6, "end_idx": 8, "type": "政治倾向"}]}, {"text": "进步派和自由主义结合起来，或许能带来新的变革。", "label": [{"entity": "进步派", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "自由主义", "start_idx": 4, "end_idx": 7, "type": "政治倾向"}]}, {"text": "求职者张伟在简历中强调其政治立场倾向于环保主义，而非极右观点。", "label": [{"entity": "环保主义", "start_idx": 19, "end_idx": 22, "type": "政治倾向"}, {"entity": "极右观点", "start_idx": 26, "end_idx": 29, "type": "政治倾向"}]}, {"text": "李华在求职简历中明确表示，他的政治理念更偏向民族主义，但并非极右翼。", "label": [{"entity": "民族主义", "start_idx": 22, "end_idx": 25, "type": "政治倾向"}, {"entity": "极右翼", "start_idx": 30, "end_idx": 32, "type": "政治倾向"}]}, {"text": "作为一名无党派人士，王刚在简历中阐述其政治观点靠近中间派。", "label": [{"entity": "无党派人士", "start_idx": 4, "end_idx": 8, "type": "政治倾向"}, {"entity": "中间派", "start_idx": 25, "end_idx": 27, "type": "政治倾向"}]}, {"text": "求职者赵红在简历中提及，她的价值观虽无党派归属，但与传统派有诸多共鸣。", "label": [{"entity": "无党派", "start_idx": 18, "end_idx": 20, "type": "政治倾向"}, {"entity": "传统派", "start_idx": 26, "end_idx": 28, "type": "政治倾向"}]}, {"text": "在简历中，陈明表明自己的政治立场介于中间派和传统派之间，寻求两者的平衡。", "label": [{"entity": "中间派", "start_idx": 18, "end_idx": 20, "type": "政治倾向"}, {"entity": "传统派", "start_idx": 22, "end_idx": 24, "type": "政治倾向"}]}, {"text": "堂兄刚领养了个孩子，养子跟我们家孩子一样可爱呢！", "label": [{"entity": "堂兄", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "孩子", "start_idx": 7, "end_idx": 8, "type": "家庭成员"}, {"entity": "我们家孩子", "start_idx": 13, "end_idx": 17, "type": "家庭成员"}]}, {"text": "我叔叔对他孙子跟继子一视同仁，真是个好爷爷。", "label": [{"entity": "叔叔", "start_idx": 1, "end_idx": 2, "type": "家庭成员"}, {"entity": "孙子", "start_idx": 5, "end_idx": 6, "type": "家庭成员"}, {"entity": "继子", "start_idx": 8, "end_idx": 9, "type": "家庭成员"}, {"entity": "爷爷", "start_idx": 19, "end_idx": 20, "type": "家庭成员"}]}, {"text": "听说你和你继父的关系处的不错，真是少见养子和继父这么和谐的。", "label": [{"entity": "你", "start_idx": 2, "end_idx": 2, "type": "家庭成员"}, {"entity": "继父", "start_idx": 5, "end_idx": 6, "type": "家庭成员"}, {"entity": "养子", "start_idx": 19, "end_idx": 20, "type": "家庭成员"}]}, {"text": "侄子他养父对他视如己出，真是让人感动。", "label": [{"entity": "侄子", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "养父", "start_idx": 3, "end_idx": 4, "type": "家庭成员"}]}, {"text": "我那个姑父对养女比亲生的还亲，真是爱心满满。", "label": [{"entity": "姑父", "start_idx": 3, "end_idx": 4, "type": "家庭成员"}, {"entity": "养女", "start_idx": 6, "end_idx": 7, "type": "家庭成员"}]}, {"text": "祖父正与女儿讨论关于家庭聚会的事宜。", "label": [{"entity": "祖父", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "女儿", "start_idx": 4, "end_idx": 5, "type": "家庭成员"}]}, {"text": "外祖父对继子提出的学业问题表示关切。", "label": [{"entity": "外祖父", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "继子", "start_idx": 4, "end_idx": 5, "type": "家庭成员"}]}, {"text": "堂姐打算这个周末带养女去游乐园。", "label": [{"entity": "堂姐", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "养女", "start_idx": 9, "end_idx": 10, "type": "家庭成员"}]}, {"text": "母亲和表姐在商量婚礼的筹备事宜。", "label": [{"entity": "母亲", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "表姐", "start_idx": 3, "end_idx": 4, "type": "家庭成员"}]}, {"text": "女儿与继子就学习上的问题进行了深入的交流。", "label": [{"entity": "女儿", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "继子", "start_idx": 3, "end_idx": 4, "type": "家庭成员"}]}, {"text": "姨，我记得我兄弟他那份金融合同里头提到的范围值好像挺有利的吧？", "label": [{"entity": "姨", "start_idx": 0, "end_idx": 0, "type": "家庭成员"}, {"entity": "兄弟", "start_idx": 6, "end_idx": 7, "type": "家庭成员"}]}, {"text": "妈，你那份合同里头写的配偶能享受一样的权益吗？", "label": [{"entity": "妈", "start_idx": 0, "end_idx": 0, "type": "家庭成员"}, {"entity": "配偶", "start_idx": 11, "end_idx": 12, "type": "家庭成员"}]}, {"text": "养母她那份合同，外祖父帮忙看过没？那个范围值是不是挺灵活的？", "label": [{"entity": "养母", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "外祖父", "start_idx": 8, "end_idx": 10, "type": "家庭成员"}]}, {"text": "养父那份合同，表妹她也能受益吧？得好好研究一下那个范围值。", "label": [{"entity": "养父", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "表妹", "start_idx": 7, "end_idx": 8, "type": "家庭成员"}]}, {"text": "孙子啊，你外祖母那份金融合同里头的规定，对咱家是不是很有利啊？", "label": [{"entity": "孙子", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "外祖母", "start_idx": 5, "end_idx": 7, "type": "家庭成员"}, {"entity": "咱家", "start_idx": 21, "end_idx": 22, "type": "家庭成员"}]}, {"text": "舅妈与外祖母应在合同生效后五个工作日内提交身份证明。", "label": [{"entity": "舅妈", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "外祖母", "start_idx": 3, "end_idx": 5, "type": "家庭成员"}]}, {"text": "女儿和儿子须在合同规定范围内共同承担债务责任。", "label": [{"entity": "女儿", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "儿子", "start_idx": 3, "end_idx": 4, "type": "家庭成员"}]}, {"text": "兄弟及父亲须在约定日期前完成资产评估，并提交相关文件。", "label": [{"entity": "兄弟", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "父亲", "start_idx": 3, "end_idx": 4, "type": "家庭成员"}]}, {"text": "祖父与侄子须严格遵守合同中的利率调整条款。", "label": [{"entity": "祖父", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "侄子", "start_idx": 3, "end_idx": 4, "type": "家庭成员"}]}, {"text": "外祖母及儿子须在合同规定范围内行使赎回权。", "label": [{"entity": "外祖母", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "儿子", "start_idx": 4, "end_idx": 5, "type": "家庭成员"}]}, {"text": "堂弟，你那份金融合同弄得怎么样了？我表妹也想了解一下。", "label": [{"entity": "堂弟", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "表妹", "start_idx": 18, "end_idx": 19, "type": "家庭成员"}]}, {"text": "继父，你看姑姑那份合同里的范围值是不是有点问题？", "label": [{"entity": "继父", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "姑姑", "start_idx": 5, "end_idx": 6, "type": "家庭成员"}]}, {"text": "侄女，你帮你表哥看看，他那合同里的数值是不是不太对劲？", "label": [{"entity": "侄女", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "表哥", "start_idx": 6, "end_idx": 7, "type": "家庭成员"}]}, {"text": "堂妹，你帮忙给养子解释一下合同里的那个范围值怎么算的？", "label": [{"entity": "堂妹", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "养子", "start_idx": 7, "end_idx": 8, "type": "家庭成员"}]}, {"text": "继父和母亲那份金融合同，你有没有仔细看？别漏了什么关键信息。", "label": [{"entity": "继父", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "母亲", "start_idx": 3, "end_idx": 4, "type": "家庭成员"}]}, {"text": "外祖父应立即与舅妈商讨家族事务。", "label": [{"entity": "外祖父", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "舅妈", "start_idx": 7, "end_idx": 8, "type": "家庭成员"}]}, {"text": "女儿，你的养父正在找你谈话。", "label": [{"entity": "女儿", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "养父", "start_idx": 5, "end_idx": 6, "type": "家庭成员"}]}, {"text": "姨，叫你的侄子回家吃饭。", "label": [{"entity": "姨", "start_idx": 0, "end_idx": 0, "type": "家庭成员"}, {"entity": "侄子", "start_idx": 5, "end_idx": 6, "type": "家庭成员"}]}, {"text": "父亲，速带外祖父去体检。", "label": [{"entity": "父亲", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "外祖父", "start_idx": 5, "end_idx": 7, "type": "家庭成员"}]}, {"text": "女儿，去了解一下姑父的工作近况。", "label": [{"entity": "女儿", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "姑父", "start_idx": 8, "end_idx": 9, "type": "家庭成员"}]}, {"text": "表哥，请您仔细审阅养女名下的金融合同条款。", "label": [{"entity": "表哥", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}]}, {"text": "表弟，确保祖母的金融合同中敏感信息得到适当保护。", "label": [{"entity": "表弟", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "祖母", "start_idx": 5, "end_idx": 6, "type": "家庭成员"}]}, {"text": "祖父，您应立即审查继女签订的金融合同，确保无误。", "label": [{"entity": "祖父", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "继女", "start_idx": 9, "end_idx": 10, "type": "家庭成员"}]}, {"text": "外祖父，务必让孙子了解金融合同中的精确条款。", "label": [{"entity": "外祖父", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "孙子", "start_idx": 7, "end_idx": 8, "type": "家庭成员"}]}, {"text": "叔叔，请与堂姐一同确认金融合同的具体细节。", "label": [{"entity": "叔叔", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "堂姐", "start_idx": 5, "end_idx": 6, "type": "家庭成员"}]}, {"text": "堂妹应立即将外祖母指定的金融合同权益转让事宜办理完毕。", "label": [{"entity": "堂妹", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "外祖母", "start_idx": 6, "end_idx": 8, "type": "家庭成员"}]}, {"text": "姨夫须亲自签署此份金融合同，并确保表妹的知情权得到充分保障。", "label": [{"entity": "姨夫", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "表妹", "start_idx": 17, "end_idx": 18, "type": "家庭成员"}]}, {"text": "请姑姑与养父仔细审阅金融合同细节，并按约定时间履行相关义务。", "label": [{"entity": "姑姑", "start_idx": 1, "end_idx": 2, "type": "家庭成员"}, {"entity": "养父", "start_idx": 4, "end_idx": 5, "type": "家庭成员"}]}, {"text": "表妹和继子必须在合同规定期限内，共同完成金融产品的赎回操作。", "label": [{"entity": "表妹", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "继子", "start_idx": 3, "end_idx": 4, "type": "家庭成员"}]}, {"text": "舅舅的孙子须遵照合同条款，按时将金融产品收益情况汇报予相关各方。", "label": [{"entity": "舅舅", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "孙子", "start_idx": 3, "end_idx": 4, "type": "家庭成员"}]}, {"text": "侄女深情回忆起与祖父共度的欢乐时光。", "label": [{"entity": "侄女", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "祖父", "start_idx": 8, "end_idx": 9, "type": "家庭成员"}]}, {"text": "堂姐间的关系胜似亲姐妹，相互扶持成长。", "label": [{"entity": "堂姐", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "亲姐妹", "start_idx": 8, "end_idx": 10, "type": "家庭成员"}]}, {"text": "表弟与侄女携手共进，共创家族企业辉煌。", "label": [{"entity": "表弟", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "侄女", "start_idx": 3, "end_idx": 4, "type": "家庭成员"}]}, {"text": "叔叔和外祖父在家族中扮演着重要角色，深受尊敬。", "label": [{"entity": "叔叔", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "外祖父", "start_idx": 3, "end_idx": 5, "type": "家庭成员"}]}, {"text": "继母与堂姐之间建立起了深厚的感情，似亲生母女。", "label": [{"entity": "继母", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "堂姐", "start_idx": 3, "end_idx": 4, "type": "家庭成员"}, {"entity": "亲生母女", "start_idx": 18, "end_idx": 21, "type": "家庭成员"}]}, {"text": "儿子跟姑姑说上次体检医生讲的那个药要按时吃哦。", "label": [{"entity": "儿子", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "姑姑", "start_idx": 3, "end_idx": 4, "type": "家庭成员"}]}, {"text": "舅舅和姑父一起去医院取检查报告。", "label": [{"entity": "舅舅", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "姑父", "start_idx": 3, "end_idx": 4, "type": "家庭成员"}]}, {"text": "继母带着外孙来诊所复诊，孩子咳嗽好多了。", "label": [{"entity": "继母", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "外孙", "start_idx": 4, "end_idx": 5, "type": "家庭成员"}]}, {"text": "姑父养子打电话来说他今天加班，晚点回家。", "label": [{"entity": "姑父", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "养子", "start_idx": 2, "end_idx": 3, "type": "家庭成员"}]}, {"text": "孙子陪着外祖父来做年度体检，老人身体挺硬朗的。", "label": [{"entity": "孙子", "start_idx": 0, "end_idx": 1, "type": "家庭成员"}, {"entity": "外祖父", "start_idx": 4, "end_idx": 6, "type": "家庭成员"}, {"entity": "老人", "start_idx": 14, "end_idx": 15, "type": "家庭成员"}]}, {"text": "求职者张先生在简历中标注，其日结工资稳定在240元，并享有每季度4,500元的奖金。", "label": [{"entity": "240元", "start_idx": 21, "end_idx": 24, "type": "工资数额"}, {"entity": "4,500元", "start_idx": 32, "end_idx": 37, "type": "工资数额"}]}, {"text": "根据李女士的简历显示，其季度薪酬和提成总和可达46,000元，年薪总额高达25万。", "label": [{"entity": "46,000元", "start_idx": 23, "end_idx": 29, "type": "工资数额"}, {"entity": "25万", "start_idx": 37, "end_idx": 39, "type": "工资数额"}]}, {"text": "王先生的月收入为9800元，月底薪与绩效合计可达15,000元。", "label": [{"entity": "9800元", "start_idx": 8, "end_idx": 12, "type": "工资数额"}, {"entity": "15,000元", "start_idx": 24, "end_idx": 30, "type": "工资数额"}]}, {"text": "简历透露，赵先生的基础工资加年终奖共计190,000元，税后年薪为210,000元。", "label": [{"entity": "190,000元", "start_idx": 19, "end_idx": 26, "type": "工资数额"}, {"entity": "210,000元", "start_idx": 33, "end_idx": 40, "type": "工资数额"}]}, {"text": "在求职者孙先生的简历中提到，其每月净收入为10,300元，近三十六个月的薪资总额为120,000元。", "label": [{"entity": "10,300元", "start_idx": 21, "end_idx": 27, "type": "工资数额"}, {"entity": "120,000元", "start_idx": 41, "end_idx": 48, "type": "工资数额"}]}, {"text": "立即报道，该企业净收入高达16万，员工月薪资确保1.2万以上。", "label": [{"entity": "16万", "start_idx": 13, "end_idx": 15, "type": "工资数额"}, {"entity": "1.2万", "start_idx": 24, "end_idx": 27, "type": "工资数额"}]}, {"text": "注意，本次调查涉及月收入23,500元含提成，季度薪酬与提成总和达46,000元。", "label": [{"entity": "23,500元", "start_idx": 12, "end_idx": 18, "type": "工资数额"}, {"entity": "46,000元", "start_idx": 33, "end_idx": 39, "type": "工资数额"}]}, {"text": "通知：税前年薪230,000元，员工每月固定薪资加津贴至少12,800元。", "label": [{"entity": "230,000元", "start_idx": 7, "end_idx": 14, "type": "工资数额"}, {"entity": "12,800元", "start_idx": 29, "end_idx": 35, "type": "工资数额"}]}, {"text": "特别指出，年终奖发放额度定为40,000元，全年度薪资水平为12万。", "label": [{"entity": "40,000元", "start_idx": 14, "end_idx": 20, "type": "工资数额"}, {"entity": "12万", "start_idx": 30, "end_idx": 32, "type": "工资数额"}]}, {"text": "记者须知，月净收入21,000元，年终奖金额度将提升至40,000元。", "label": [{"entity": "21,000元", "start_idx": 9, "end_idx": 15, "type": "工资数额"}, {"entity": "40,000元", "start_idx": 27, "end_idx": 33, "type": "工资数额"}]}, {"text": "年底分红95,000元哦，年终奖还有40,000元呢，真是不错！", "label": [{"entity": "95,000元", "start_idx": 4, "end_idx": 10, "type": "工资数额"}, {"entity": "40,000元", "start_idx": 18, "end_idx": 24, "type": "工资数额"}]}, {"text": "我们这的年终奖金有80,000元，加上提成制年薪150,000元，收入很可观哦。", "label": [{"entity": "80,000元", "start_idx": 9, "end_idx": 15, "type": "工资数额"}, {"entity": "150,000元", "start_idx": 24, "end_idx": 31, "type": "工资数额"}]}, {"text": "每月全勤奖金500元，月净收入能有21,000元，稳定得很。", "label": [{"entity": "500元", "start_idx": 6, "end_idx": 9, "type": "工资数额"}, {"entity": "21,000元", "start_idx": 17, "end_idx": 23, "type": "工资数额"}]}, {"text": "每个月都能拿到500元的全勤奖金，加上提成月收入有23,500元呢。", "label": [{"entity": "500元", "start_idx": 7, "end_idx": 10, "type": "工资数额"}, {"entity": "23,500元", "start_idx": 25, "end_idx": 31, "type": "工资数额"}]}, {"text": "每月净收入10,300元，碰到一次性项目还能拿50,000元奖金，真是太棒了！", "label": [{"entity": "10,300元", "start_idx": 5, "end_idx": 11, "type": "工资数额"}, {"entity": "50,000元", "start_idx": 23, "end_idx": 29, "type": "工资数额"}]}, {"text": "据报告，该公司为员工提供年底双薪加奖金共计75,000元，税后月薪达12,600元。", "label": [{"entity": "75,000元", "start_idx": 21, "end_idx": 27, "type": "工资数额"}, {"entity": "12,600元", "start_idx": 34, "end_idx": 40, "type": "工资数额"}]}, {"text": "分析显示，该职位税前年薪为230,000元，每季度薪酬和提成总和为46,000元。", "label": [{"entity": "230,000元", "start_idx": 13, "end_idx": 20, "type": "工资数额"}, {"entity": "46,000元", "start_idx": 33, "end_idx": 39, "type": "工资数额"}]}, {"text": "调查结果表明，该行业年收入平均水平为18.5万，月底薪10,000元并额外提供补贴。", "label": [{"entity": "18.5万", "start_idx": 18, "end_idx": 22, "type": "工资数额"}, {"entity": "10,000元", "start_idx": 27, "end_idx": 33, "type": "工资数额"}]}, {"text": "最新消息，该企业为员工提供税后年薪270,000元，年底双薪高达30,000元。", "label": [{"entity": "270,000元", "start_idx": 17, "end_idx": 24, "type": "工资数额"}, {"entity": "30,000元", "start_idx": 32, "end_idx": 38, "type": "工资数额"}]}, {"text": "据悉，该岗位薪资总额按36个月计算为120,000元，日薪含加班费为280元。", "label": [{"entity": "120,000元", "start_idx": 18, "end_idx": 25, "type": "工资数额"}, {"entity": "280元", "start_idx": 34, "end_idx": 37, "type": "工资数额"}]}, {"text": "本年度公司净收入达16万，年底分红高达95,000元。", "label": [{"entity": "16万", "start_idx": 9, "end_idx": 11, "type": "工资数额"}, {"entity": "95,000元", "start_idx": 19, "end_idx": 25, "type": "工资数额"}]}, {"text": "该职位提供36个月120,000元的薪资总额，日工资为300元。", "label": [{"entity": "36个月120,000元", "start_idx": 5, "end_idx": 16, "type": "工资数额"}, {"entity": "300元", "start_idx": 27, "end_idx": 30, "type": "工资数额"}]}, {"text": "员工薪资水平为12万，年终奖金额度高达40,000元。", "label": [{"entity": "12万", "start_idx": 7, "end_idx": 9, "type": "工资数额"}, {"entity": "40,000元", "start_idx": 19, "end_idx": 25, "type": "工资数额"}]}, {"text": "公司实行年底双薪制，加奖金达75,000元，税前月薪为11,500元。", "label": [{"entity": "75,000元", "start_idx": 14, "end_idx": 20, "type": "工资数额"}, {"entity": "11,500元", "start_idx": 27, "end_idx": 33, "type": "工资数额"}]}, {"text": "员工将享有年底双薪加奖金75,000元，预扣税后月薪为7,500元。", "label": [{"entity": "75,000元", "start_idx": 12, "end_idx": 18, "type": "工资数额"}, {"entity": "7,500元", "start_idx": 27, "end_idx": 32, "type": "工资数额"}]}, {"text": "银行交易记录显示，该客户佣金收入月均达到8,000元，加上固定薪资和津贴共计12,800元。", "label": [{"entity": "8,000元", "start_idx": 20, "end_idx": 25, "type": "工资数额"}, {"entity": "12,800元", "start_idx": 38, "end_idx": 44, "type": "工资数额"}]}, {"text": "根据年度报告，该员工享有年底双薪，总计30,000元，税后年薪为210,000元。", "label": [{"entity": "30,000元", "start_idx": 19, "end_idx": 25, "type": "工资数额"}, {"entity": "210,000元", "start_idx": 32, "end_idx": 39, "type": "工资数额"}]}, {"text": "在年度财务总结中，该员工获得年终奖金80,000元，季度收入累计42,000元。", "label": [{"entity": "80,000元", "start_idx": 18, "end_idx": 24, "type": "工资数额"}, {"entity": "42,000元", "start_idx": 32, "end_idx": 38, "type": "工资数额"}]}, {"text": "统计数据显示，该员工季度净收入为57,000元，日结工资稳定在240元。", "label": [{"entity": "57,000元", "start_idx": 16, "end_idx": 22, "type": "工资数额"}, {"entity": "240元", "start_idx": 31, "end_idx": 34, "type": "工资数额"}]}, {"text": "经过年度审计，该银行职员的年收入为18.5万，税后年薪确认为270,000元。", "label": [{"entity": "18.5万", "start_idx": 17, "end_idx": 21, "type": "工资数额"}, {"entity": "270,000元", "start_idx": 30, "end_idx": 37, "type": "工资数额"}]}, {"text": "您的年收入18.5万，即税后年薪270,000元，有任何疑问请咨询。", "label": [{"entity": "18.5万", "start_idx": 5, "end_idx": 9, "type": "工资数额"}, {"entity": "270,000元", "start_idx": 16, "end_idx": 23, "type": "工资数额"}]}, {"text": "每月您将收到固定薪资加津贴共12,800元，全年薪资水平为12万。", "label": [{"entity": "12,800元", "start_idx": 14, "end_idx": 20, "type": "工资数额"}, {"entity": "12万", "start_idx": 29, "end_idx": 31, "type": "工资数额"}]}, {"text": "您的待遇是基础工资加年终奖190,000元，月收入含提成达到23,500元。", "label": [{"entity": "190,000元", "start_idx": 13, "end_idx": 20, "type": "工资数额"}, {"entity": "23,500元", "start_idx": 30, "end_idx": 36, "type": "工资数额"}]}, {"text": "周平均工资2,300元，税前年薪可达230,000元，详情请咨询。", "label": [{"entity": "2,300元", "start_idx": 5, "end_idx": 10, "type": "工资数额"}, {"entity": "230,000元", "start_idx": 18, "end_idx": 25, "type": "工资数额"}]}, {"text": "月底薪15,000元加上绩效，年底还会发放一次性奖励45,000元。", "label": [{"entity": "15,000元", "start_idx": 3, "end_idx": 9, "type": "工资数额"}, {"entity": "45,000元", "start_idx": 26, "end_idx": 32, "type": "工资数额"}]}, {"text": "该行员工年薪总额25万，税后月薪平均7500元。", "label": [{"entity": "25万", "start_idx": 8, "end_idx": 10, "type": "工资数额"}, {"entity": "7500元", "start_idx": 18, "end_idx": 22, "type": "工资数额"}]}, {"text": "预扣税后月薪7500元，年收入达18.5万。", "label": [{"entity": "7500元", "start_idx": 6, "end_idx": 10, "type": "工资数额"}, {"entity": "18.5万", "start_idx": 16, "end_idx": 20, "type": "工资数额"}]}, {"text": "员工税后年薪共计210,000元，月收入稳定在9800元。", "label": [{"entity": "210,000元", "start_idx": 8, "end_idx": 15, "type": "工资数额"}, {"entity": "9800元", "start_idx": 23, "end_idx": 27, "type": "工资数额"}]}, {"text": "该行采用提成制，年薪150,000元，每月净收入10300元。", "label": [{"entity": "150,000元", "start_idx": 10, "end_idx": 17, "type": "工资数额"}, {"entity": "10300元", "start_idx": 24, "end_idx": 29, "type": "工资数额"}]}, {"text": "员工每月固定薪资加津贴共计12800元，税前年薪230,000元。", "label": [{"entity": "12800元", "start_idx": 13, "end_idx": 18, "type": "工资数额"}, {"entity": "230,000元", "start_idx": 24, "end_idx": 31, "type": "工资数额"}]}, {"text": "该职位月底薪10,000元并包含补贴，设有年度绩效奖金60,000元。", "label": [{"entity": "10,000元", "start_idx": 6, "end_idx": 12, "type": "工资数额"}, {"entity": "60,000元", "start_idx": 27, "end_idx": 33, "type": "工资数额"}]}, {"text": "员工税前月薪为11,500元，享有年底十三薪待遇，共计28,000元。", "label": [{"entity": "11,500元", "start_idx": 7, "end_idx": 13, "type": "工资数额"}, {"entity": "28,000元", "start_idx": 27, "end_idx": 33, "type": "工资数额"}]}, {"text": "基本工资加提成模式，每月可达14,000元，月底薪与提成合并可达18,500元。", "label": [{"entity": "14,000元", "start_idx": 14, "end_idx": 20, "type": "工资数额"}, {"entity": "18,500元", "start_idx": 32, "end_idx": 38, "type": "工资数额"}]}, {"text": "日薪制，每日280元含加班费，并提供年度绩效奖金60,000元。", "label": [{"entity": "280元", "start_idx": 6, "end_idx": 9, "type": "工资数额"}, {"entity": "60,000元", "start_idx": 24, "end_idx": 30, "type": "工资数额"}]}, {"text": "员工每月可获得全勤奖金500元，以及固定薪酬9,200元。", "label": [{"entity": "500元", "start_idx": 11, "end_idx": 14, "type": "工资数额"}, {"entity": "9,200元", "start_idx": 22, "end_idx": 27, "type": "工资数额"}]}, {"text": "请确保该病例医生的年度底薪为120,000元，年终奖金额度达到40,000元。", "label": [{"entity": "120,000元", "start_idx": 14, "end_idx": 21, "type": "工资数额"}, {"entity": "40,000元", "start_idx": 31, "end_idx": 37, "type": "工资数额"}]}, {"text": "病例中护士的税前所得应为11万，年底双薪加奖金不得低于75,000元。", "label": [{"entity": "11万", "start_idx": 12, "end_idx": 14, "type": "工资数额"}, {"entity": "75,000元", "start_idx": 27, "end_idx": 33, "type": "工资数额"}]}, {"text": "该病例中工作人员的年底双薪至少30,000元，年终奖金额度应为40,000元。", "label": [{"entity": "30,000元", "start_idx": 15, "end_idx": 21, "type": "工资数额"}, {"entity": "40,000元", "start_idx": 31, "end_idx": 37, "type": "工资数额"}]}, {"text": "病例所述医师月底薪加上提成须达到18,500元，税后年薪不应低于210,000元。", "label": [{"entity": "18,500元", "start_idx": 16, "end_idx": 22, "type": "工资数额"}, {"entity": "210,000元", "start_idx": 32, "end_idx": 39, "type": "工资数额"}]}, {"text": "请检查病例，确保提及的时薪为80元，且月底薪加上提成共18,500元。", "label": [{"entity": "80元", "start_idx": 14, "end_idx": 16, "type": "工资数额"}, {"entity": "18,500元", "start_idx": 27, "end_idx": 33, "type": "工资数额"}]}, {"text": "网红直播平台股权与新材料研发产业基金达成战略合作。", "label": [{"entity": "网红直播平台股权", "start_idx": 0, "end_idx": 7, "type": "投资产品"}, {"entity": "新材料研发产业基金", "start_idx": 9, "end_idx": 17, "type": "投资产品"}]}, {"text": "机器人产业私募股权基金关注智能物流仓储领域的深度发展。", "label": [{"entity": "机器人产业私募股权基金", "start_idx": 0, "end_idx": 10, "type": "投资产品"}]}, {"text": "生物医药研发项目获得网络游戏开发基金的大力支持。", "label": [{"entity": "网络游戏开发基金", "start_idx": 10, "end_idx": 17, "type": "投资产品"}]}, {"text": "私募股权医疗基金携手跨境电商成长基金，共谋发展新篇章。", "label": [{"entity": "私募股权医疗基金", "start_idx": 0, "end_idx": 7, "type": "投资产品"}, {"entity": "跨境电商成长基金", "start_idx": 10, "end_idx": 17, "type": "投资产品"}]}, {"text": "矿产资源私募股权立即对文化遗产基金进行风险评估。", "label": [{"entity": "矿产资源私募股权", "start_idx": 0, "end_idx": 7, "type": "投资产品"}, {"entity": "文化遗产基金", "start_idx": 11, "end_idx": 16, "type": "投资产品"}]}, {"text": "金融科技区块链投资应优先考虑私募股权医疗基金。", "label": [{"entity": "私募股权", "start_idx": 14, "end_idx": 17, "type": "投资产品"}, {"entity": "医疗基金", "start_idx": 18, "end_idx": 21, "type": "投资产品"}]}, {"text": "请高尔夫球场股权投资部门关注跨境电商物流仓储项目的进展。", "label": [{"entity": "高尔夫球场股权", "start_idx": 1, "end_idx": 7, "type": "投资产品"}, {"entity": "跨境电商物流仓储项目", "start_idx": 14, "end_idx": 23, "type": "投资产品"}]}, {"text": "针对跨境电商成长基金，虚拟房地产投资需谨慎操作。", "label": [{"entity": "跨境电商成长基金", "start_idx": 2, "end_idx": 9, "type": "投资产品"}, {"entity": "虚拟房地产", "start_idx": 11, "end_idx": 15, "type": "投资产品"}]}, {"text": "G通信技术投资基金应加大绿色能源债券的持有比例。", "label": [{"entity": "G通信技术投资基金", "start_idx": 0, "end_idx": 8, "type": "投资产品"}, {"entity": "绿色能源债券", "start_idx": 12, "end_idx": 17, "type": "投资产品"}]}, {"text": "您好，我想了解一下健康管理服务平台投资和医疗器械创新基金的相关信息。", "label": [{"entity": "健康管理服务平台投资", "start_idx": 9, "end_idx": 18, "type": "投资产品"}, {"entity": "医疗器械创新基金", "start_idx": 20, "end_idx": 27, "type": "投资产品"}]}, {"text": "我们最近在关注生态旅游度假村项目和长租公寓房地产投资，你有兴趣了解吗？", "label": [{"entity": "生态旅游度假村项目", "start_idx": 7, "end_idx": 15, "type": "投资产品"}, {"entity": "长租公寓房地产投资", "start_idx": 17, "end_idx": 25, "type": "投资产品"}]}, {"text": "环保污水处理项目和智能物流仓储投资基金现在可是热门话题哦。", "label": [{"entity": "环保污水处理项目", "start_idx": 0, "end_idx": 7, "type": "投资产品"}, {"entity": "智能物流仓储投资基金", "start_idx": 9, "end_idx": 18, "type": "投资产品"}]}, {"text": "我听说网络游戏开发基金和金融科技区块链投资前景很不错，你怎么看？", "label": [{"entity": "网络游戏开发基金", "start_idx": 3, "end_idx": 10, "type": "投资产品"}, {"entity": "金融科技区块链", "start_idx": 12, "end_idx": 18, "type": "投资产品"}]}, {"text": "人工智能研发基金和新材料研发产业基金，这两个领域未来发展潜力巨大。", "label": [{"entity": "人工智能研发基金", "start_idx": 0, "end_idx": 7, "type": "投资产品"}, {"entity": "新材料研发产业基金", "start_idx": 9, "end_idx": 17, "type": "投资产品"}]}, {"text": "文化遗产基金携手文化创意产业股权，共谋产业发展新篇章。", "label": [{"entity": "文化遗产基金", "start_idx": 0, "end_idx": 5, "type": "投资产品"}, {"entity": "文化创意产业股权", "start_idx": 8, "end_idx": 15, "type": "投资产品"}]}, {"text": "长租公寓房地产投资领域，文化创意产业股权发挥重要作用。", "label": [{"entity": "长租公寓房地产", "start_idx": 0, "end_idx": 6, "type": "投资产品"}, {"entity": "文化创意产业股权", "start_idx": 12, "end_idx": 19, "type": "投资产品"}]}, {"text": "精品咖啡连锁项目与新材料研发产业基金达成战略合作。", "label": [{"entity": "精品咖啡连锁项目", "start_idx": 0, "end_idx": 7, "type": "投资产品"}, {"entity": "新材料研发产业基金", "start_idx": 9, "end_idx": 17, "type": "投资产品"}]}, {"text": "茶叶庄园投资计划逐渐步入正轨，虚拟房地产市场亦受关注。", "label": [{"entity": "茶叶庄园投资计划", "start_idx": 0, "end_idx": 7, "type": "投资产品"}, {"entity": "虚拟房地产市场", "start_idx": 15, "end_idx": 21, "type": "投资产品"}]}, {"text": "智能家居产业投资携手跨境电商成长基金，共创美好未来。", "label": [{"entity": "智能家居产业投资", "start_idx": 0, "end_idx": 7, "type": "投资产品"}, {"entity": "跨境电商成长基金", "start_idx": 10, "end_idx": 17, "type": "投资产品"}]}, {"text": "G通信技术投资基金对艺术品收藏品市场的投资已进入法律审核阶段。", "label": [{"entity": "G通信技术投资基金", "start_idx": 0, "end_idx": 8, "type": "投资产品"}]}, {"text": "教育培训产业股权与机器人产业私募股权的合并案正在接受法律评估。", "label": [{"entity": "教育培训产业股权", "start_idx": 0, "end_idx": 7, "type": "投资产品"}, {"entity": "机器人产业私募股权", "start_idx": 9, "end_idx": 17, "type": "投资产品"}]}, {"text": "知识产权运营基金已着手对跨境电商物流仓储项目进行合法性审查。", "label": [{"entity": "知识产权运营基金", "start_idx": 0, "end_idx": 7, "type": "投资产品"}]}, {"text": "人工智能研发基金正考虑与个性化教育投资计划进行合作。", "label": [{"entity": "人工智能研发基金", "start_idx": 0, "end_idx": 7, "type": "投资产品"}, {"entity": "个性化教育投资计划", "start_idx": 12, "end_idx": 20, "type": "投资产品"}]}, {"text": "网络游戏开发基金将投入资金，扩展跨境电商物流仓储项目的业务范围。", "label": [{"entity": "网络游戏开发基金", "start_idx": 0, "end_idx": 7, "type": "投资产品"}]}, {"text": "针对钻石珠宝投资计划以及无人机研发及应用基金，立即启动综合评估程序！", "label": [{"entity": "钻石珠宝投资计划", "start_idx": 2, "end_idx": 9, "type": "投资产品"}, {"entity": "无人机研发及应用基金", "start_idx": 12, "end_idx": 21, "type": "投资产品"}]}, {"text": "文化创意产业股权与生态农业种植项目的投资分析报告，必须在本周五前提交！", "label": [{"entity": "文化创意产业股权", "start_idx": 0, "end_idx": 7, "type": "投资产品"}, {"entity": "生态农业种植项目", "start_idx": 9, "end_idx": 16, "type": "投资产品"}]}, {"text": "智能物流仓储投资基金及钻石珠宝投资计划的相关数据，需在上午十点前更新完毕！", "label": [{"entity": "智能物流仓储投资基金", "start_idx": 0, "end_idx": 9, "type": "投资产品"}, {"entity": "钻石珠宝投资计划", "start_idx": 11, "end_idx": 18, "type": "投资产品"}]}, {"text": "教育培训产业股权与高尔夫球场股权投资的可行性研究，要加快进度，下周三前完成！", "label": [{"entity": "教育培训产业股权", "start_idx": 0, "end_idx": 7, "type": "投资产品"}, {"entity": "高尔夫球场股权", "start_idx": 9, "end_idx": 15, "type": "投资产品"}]}, {"text": "针对高尔夫球场股权投资和G通信技术投资基金的风险评估，务必在今天内完成！", "label": [{"entity": "高尔夫球场股权投资", "start_idx": 2, "end_idx": 10, "type": "投资产品"}, {"entity": "G通信技术投资基金", "start_idx": 12, "end_idx": 20, "type": "投资产品"}]}, {"text": "健康管理服务平台投资最近挺火，我有个朋友就在做一个股权众筹项目。", "label": [{"entity": "股权众筹项目", "start_idx": 25, "end_idx": 30, "type": "投资产品"}]}, {"text": "你听说了吗？咱们城市的轨道交通基金好像要投一个生态农业种植项目。", "label": [{"entity": "轨道交通基金", "start_idx": 11, "end_idx": 16, "type": "投资产品"}]}, {"text": "我老爸最近在研究那个茶叶庄园投资计划，说是比红酒期酒投资更有前景。", "label": [{"entity": "茶叶庄园投资计划", "start_idx": 10, "end_idx": 17, "type": "投资产品"}, {"entity": "红酒期酒投资", "start_idx": 22, "end_idx": 27, "type": "投资产品"}]}, {"text": "哎，我刚看到一个新闻，说是G通信技术投资基金准备投生态农业种植项目。", "label": [{"entity": "G通信技术投资基金", "start_idx": 13, "end_idx": 21, "type": "投资产品"}]}, {"text": "最近稀有金属投资挺热门的，我们公司也在考虑搞个股权众筹项目。", "label": [{"entity": "稀有金属", "start_idx": 2, "end_idx": 5, "type": "投资产品"}]}, {"text": "网红直播平台股权与文化遗产基金达成战略合作，共谋发展新篇章。", "label": [{"entity": "网红直播平台股权", "start_idx": 0, "end_idx": 7, "type": "投资产品"}, {"entity": "文化遗产基金", "start_idx": 9, "end_idx": 14, "type": "投资产品"}]}, {"text": "文化遗产基金携手旅游产业私募股权，助力文化传承与旅游创新。", "label": [{"entity": "文化遗产基金", "start_idx": 0, "end_idx": 5, "type": "投资产品"}, {"entity": "旅游产业私募股权", "start_idx": 8, "end_idx": 15, "type": "投资产品"}]}, {"text": "矿产资源私募股权与城市轨道交通基金合作，共拓城市发展新机遇。", "label": [{"entity": "矿产资源私募股权", "start_idx": 0, "end_idx": 7, "type": "投资产品"}, {"entity": "城市轨道交通基金", "start_idx": 9, "end_idx": 16, "type": "投资产品"}]}, {"text": "城市轨道交通基金携手珍稀邮票投资组合，探索多元化投资新路径。", "label": [{"entity": "城市轨道交通基金", "start_idx": 0, "end_idx": 7, "type": "投资产品"}, {"entity": "珍稀邮票投资组合", "start_idx": 10, "end_idx": 17, "type": "投资产品"}]}, {"text": "私募股权份额投资与跨境电商成长基金强强联合，共促电商行业快速发展。", "label": [{"entity": "私募股权份额投资", "start_idx": 0, "end_idx": 7, "type": "投资产品"}, {"entity": "跨境电商成长基金", "start_idx": 9, "end_idx": 16, "type": "投资产品"}]}, {"text": "今天看到个好消息，那个网络游戏开发基金和互联网保险创新产品都拿到了融资呢！", "label": [{"entity": "网络游戏开发基金", "start_idx": 11, "end_idx": 18, "type": "投资产品"}, {"entity": "互联网保险创新产品", "start_idx": 20, "end_idx": 28, "type": "投资产品"}]}, {"text": "我哥他们公司最近投了新材料研发产业基金，还说G通信技术投资基金前景特好。", "label": [{"entity": "新材料研发产业基金", "start_idx": 10, "end_idx": 18, "type": "投资产品"}, {"entity": "G通信技术投资基金", "start_idx": 22, "end_idx": 30, "type": "投资产品"}]}, {"text": "人工智能研发基金最近挺火的，我看网红直播平台股权也挺有潜力。", "label": [{"entity": "人工智能研发基金", "start_idx": 0, "end_idx": 7, "type": "投资产品"}, {"entity": "网红直播平台股权", "start_idx": 16, "end_idx": 23, "type": "投资产品"}]}, {"text": "老王他们团队刚做了个性化教育投资计划，航空航天产业投资基金也在规划中。", "label": [{"entity": "个性化教育投资计划", "start_idx": 9, "end_idx": 17, "type": "投资产品"}, {"entity": "航空航天产业投资基金", "start_idx": 19, "end_idx": 28, "type": "投资产品"}]}, {"text": "农业科技股权投资这块儿，我觉得跟网红直播平台股权一样，将来肯定大火。", "label": [{"entity": "农业科技股权投资", "start_idx": 0, "end_idx": 7, "type": "投资产品"}, {"entity": "网红直播平台股权", "start_idx": 16, "end_idx": 23, "type": "投资产品"}]}, {"text": "患者投资组合中包括网红直播平台股权及矿产资源私募股权等多元化项目。", "label": [{"entity": "网红直播平台股权", "start_idx": 9, "end_idx": 16, "type": "投资产品"}, {"entity": "矿产资源私募股权", "start_idx": 18, "end_idx": 25, "type": "投资产品"}]}, {"text": "该病例涉及的投资领域包括机器人产业私募股权及虚拟房地产交易。", "label": [{"entity": "机器人产业私募股权", "start_idx": 12, "end_idx": 20, "type": "投资产品"}, {"entity": "虚拟房地产交易", "start_idx": 22, "end_idx": 28, "type": "投资产品"}]}, {"text": "G通信技术投资基金与艺术品收藏品成为该患者的重点投资对象。", "label": [{"entity": "G通信技术投资基金", "start_idx": 0, "end_idx": 8, "type": "投资产品"}, {"entity": "艺术品收藏品", "start_idx": 10, "end_idx": 15, "type": "投资产品"}]}, {"text": "在股权众筹项目中，患者对体育赛事股权投资表现出浓厚兴趣。", "label": [{"entity": "体育赛事股权", "start_idx": 12, "end_idx": 17, "type": "投资产品"}]}, {"text": "其投资策略亦涵盖了旅游产业私募股权及宠物保险理财产品。", "label": [{"entity": "旅游产业私募股权", "start_idx": 9, "end_idx": 16, "type": "投资产品"}, {"entity": "宠物保险理财产品", "start_idx": 18, "end_idx": 25, "type": "投资产品"}]}, {"text": "跨境电商物流仓储项目在本次医疗病例研讨会上被视为未来投资新热点，与此同时，钻石珠宝投资计划亦成为关注焦点。", "label": [{"entity": "跨境电商物流仓储项目", "start_idx": 0, "end_idx": 9, "type": "投资产品"}, {"entity": "钻石珠宝投资计划", "start_idx": 37, "end_idx": 44, "type": "投资产品"}]}, {"text": "（注：由于医疗病例与跨境电商物流仓储项目及钻石珠宝投资计划并无直接关联，以上句子是在尽量融合实体的基础上，构建的一个符合语言风格的正式报道语境。）", "label": [{"entity": "跨境电商物流仓储项目", "start_idx": 10, "end_idx": 19, "type": "投资产品"}, {"entity": "钻石珠宝投资计划", "start_idx": 21, "end_idx": 28, "type": "投资产品"}]}, {"text": "外籍个人综合所得税收优惠证明和税务合规报告记录，立即进行审核！", "label": [{"entity": "外籍个人综合所得税收优惠证明", "start_idx": 0, "end_idx": 13, "type": "税务记录"}, {"entity": "税务合规报告记录", "start_idx": 15, "end_idx": 22, "type": "税务记录"}]}, {"text": "请速查税控加油机发票领用记录以及房产交易税费凭证！", "label": [{"entity": "税控加油机发票领用记录", "start_idx": 3, "end_idx": 13, "type": "税务记录"}, {"entity": "房产交易税费凭证", "start_idx": 16, "end_idx": 23, "type": "税务记录"}]}, {"text": "针对环保税排放量核算记录和税务审计报告，立刻进行查补税款处理！", "label": [{"entity": "环保税排放量核算记录", "start_idx": 2, "end_idx": 11, "type": "税务记录"}, {"entity": "税务审计报告", "start_idx": 13, "end_idx": 18, "type": "税务记录"}]}, {"text": "立刻核对跨境电商进口关税及增值税补税记录，并处理股票转让印花税缴纳事宜！", "label": [{"entity": "跨境电商进口关税及增值税补税记录", "start_idx": 4, "end_idx": 19, "type": "税务记录"}, {"entity": "股票转让印花税缴纳事宜", "start_idx": 24, "end_idx": 34, "type": "税务记录"}]}, {"text": "专利实施许可费税务登记和城市建设维护税应税项目申报资料，须即刻上报！", "label": [{"entity": "专利实施许可费税务登记", "start_idx": 0, "end_idx": 10, "type": "税务记录"}, {"entity": "城市建设维护税应税项目申报资料", "start_idx": 12, "end_idx": 26, "type": "税务记录"}]}, {"text": "税务机关出具的免税证明是城市维护建设税应税项目申报的必要文件。", "label": [{"entity": "免税证明", "start_idx": 7, "end_idx": 10, "type": "税务记录"}, {"entity": "城市维护建设税应税项目申报", "start_idx": 12, "end_idx": 24, "type": "税务记录"}]}, {"text": "在进行印花税购销合同备案时，需注意税务局关于税收违法行为的处罚决定。", "label": [{"entity": "印花税购销合同备案", "start_idx": 3, "end_idx": 11, "type": "税务记录"}, {"entity": "税务局关于税收违法行为的处罚决定", "start_idx": 17, "end_idx": 32, "type": "税务记录"}]}, {"text": "职工教育经费税前扣除证明需与年税收减免审批表一同提交。", "label": [{"entity": "职工教育经费税前扣除证明", "start_idx": 0, "end_idx": 11, "type": "税务记录"}, {"entity": "年税收减免审批表", "start_idx": 14, "end_idx": 21, "type": "税务记录"}]}, {"text": "软件产品增值税即征即退记录与职工教育经费税前扣除证明是税务审查的重点。", "label": [{"entity": "软件产品增值税即征即退记录", "start_idx": 0, "end_idx": 12, "type": "税务记录"}, {"entity": "职工教育经费税前扣除证明", "start_idx": 14, "end_idx": 25, "type": "税务记录"}]}, {"text": "城市维护建设税应税项目申报时，需附上非居民企业源泉扣税记录。", "label": [{"entity": "非居民企业源泉扣税记录", "start_idx": 18, "end_idx": 28, "type": "税务记录"}]}, {"text": "银行针对企业研发费用加计扣除申请及年度土地增值税清算报告进行严格审核。", "label": [{"entity": "研发费用加计扣除申请", "start_idx": 6, "end_idx": 15, "type": "税务记录"}, {"entity": "年度土地增值税清算报告", "start_idx": 17, "end_idx": 27, "type": "税务记录"}]}, {"text": "税务部门重点检查流转税票证记录薄及电子商务进口关税缴纳记录的准确性。", "label": [{"entity": "流转税票证记录薄", "start_idx": 8, "end_idx": 15, "type": "税务记录"}, {"entity": "电子商务进口关税缴纳记录", "start_idx": 17, "end_idx": 28, "type": "税务记录"}]}, {"text": "在税务申报过程中，增值税专用发票记录及个体工商户增值税简易申报表需完整提交。", "label": [{"entity": "增值税专用发票记录", "start_idx": 9, "end_idx": 17, "type": "税务记录"}, {"entity": "个体工商户增值税简易申报表", "start_idx": 19, "end_idx": 31, "type": "税务记录"}]}, {"text": "海关对出口退税申报单据及职工教育经费税前扣除证明进行详细核查。", "label": [{"entity": "出口退税申报单据", "start_idx": 3, "end_idx": 10, "type": "税务记录"}, {"entity": "职工教育经费税前扣除证明", "start_idx": 12, "end_idx": 23, "type": "税务记录"}]}, {"text": "委托加工业务增值税结算记录与文化事业建设费缴费证明是财务审计的关键文件。", "label": [{"entity": "委托加工业务增值税结算记录", "start_idx": 0, "end_idx": 12, "type": "税务记录"}, {"entity": "文化事业建设费缴费证明", "start_idx": 14, "end_idx": 24, "type": "税务记录"}]}, {"text": "本合同涉及的房产交易税费凭证与资源税采矿权使用费记录，需经严格审核。", "label": [{"entity": "房产交易税费凭证", "start_idx": 6, "end_idx": 13, "type": "税务记录"}, {"entity": "资源税采矿权使用费记录", "start_idx": 15, "end_idx": 25, "type": "税务记录"}]}, {"text": "在跨境电商进口关税及增值税补税记录中，基础设施投资增值税抵扣问题备受关注。", "label": [{"entity": "跨境电商进口关税及增值税补税记录", "start_idx": 1, "end_idx": 16, "type": "税务记录"}]}, {"text": "资源税采矿权使用费记录与增值税专用发票记录，应作为财务核对的必要文件。", "label": [{"entity": "资源税采矿权使用费记录", "start_idx": 0, "end_idx": 10, "type": "税务记录"}, {"entity": "增值税专用发票记录", "start_idx": 12, "end_idx": 20, "type": "税务记录"}]}, {"text": "本次金融合同明确规定了进口货物消费税缴纳凭证与流转税票证记录薄的处理流程。", "label": [{"entity": "进口货物消费税缴纳凭证", "start_idx": 11, "end_idx": 21, "type": "税务记录"}, {"entity": "流转税票证记录薄", "start_idx": 23, "end_idx": 30, "type": "税务记录"}]}, {"text": "合同执行过程中，房产交易税费凭证和资源税采矿权使用费记录的准确性至关重要。", "label": [{"entity": "房产交易税费凭证", "start_idx": 8, "end_idx": 15, "type": "税务记录"}, {"entity": "资源税采矿权使用费记录", "start_idx": 17, "end_idx": 27, "type": "税务记录"}]}, {"text": "客服记录显示，用户咨询了基础设施投资增值税抵扣以及外籍个人综合所得税收优惠证明相关问题。", "label": [{"entity": "基础设施投资增值税抵扣", "start_idx": 12, "end_idx": 22, "type": "税务记录"}, {"entity": "外籍个人综合所得税收优惠证明", "start_idx": 25, "end_idx": 38, "type": "税务记录"}]}, {"text": "在线客服指出，软件产品增值税即征即退记录需与税务机关出具的免税证明一同审核。", "label": [{"entity": "软件产品增值税即征即退记录", "start_idx": 7, "end_idx": 19, "type": "税务记录"}, {"entity": "税务机关出具的免税证明", "start_idx": 22, "end_idx": 32, "type": "税务记录"}]}, {"text": "客服记录中提到，用户询问了资源税采矿权使用费记录与企业研发费用加计扣除申请事宜。", "label": [{"entity": "资源税采矿权使用费记录", "start_idx": 13, "end_idx": 23, "type": "税务记录"}, {"entity": "企业研发费用加计扣除申请事宜", "start_idx": 25, "end_idx": 38, "type": "税务记录"}]}, {"text": "客服聊天记录涉及保险公司代扣代缴车险税费以及个人所得税综合所得汇缴证明的咨询。", "label": [{"entity": "代扣代缴车险税费", "start_idx": 12, "end_idx": 19, "type": "税务记录"}, {"entity": "个人所得税综合所得汇缴证明", "start_idx": 22, "end_idx": 34, "type": "税务记录"}]}, {"text": "用户在在线客服中查询了资源税采矿权使用费记录与股息红利税代扣代缴记录的具体操作流程。", "label": [{"entity": "资源税采矿权使用费记录", "start_idx": 11, "end_idx": 21, "type": "税务记录"}, {"entity": "股息红利税代扣代缴记录", "start_idx": 23, "end_idx": 33, "type": "税务记录"}]}, {"text": "软件产品增值税即征即退记录与非贸项下外汇支付税务证明已按法律规定归档。", "label": [{"entity": "软件产品增值税即征即退记录", "start_idx": 0, "end_idx": 12, "type": "税务记录"}, {"entity": "非贸项下外汇支付税务证明", "start_idx": 14, "end_idx": 25, "type": "税务记录"}]}, {"text": "资源税采矿权使用费记录与非居民企业源泉扣税记录已纳入税务管理范畴。", "label": [{"entity": "资源税采矿权使用费记录", "start_idx": 0, "end_idx": 10, "type": "税务记录"}, {"entity": "非居民企业源泉扣税记录", "start_idx": 12, "end_idx": 22, "type": "税务记录"}]}, {"text": "税务合规报告记录显示，增值税专用发票管理符合法律规定。", "label": [{"entity": "税务合规报告记录", "start_idx": 0, "end_idx": 7, "type": "税务记录"}, {"entity": "增值税专用发票管理", "start_idx": 11, "end_idx": 19, "type": "税务记录"}]}, {"text": "城市维护建设税应税项目申报与外籍个人综合所得税收优惠证明已提交审核。", "label": [{"entity": "城市维护建设税应税项目申报", "start_idx": 0, "end_idx": 12, "type": "税务记录"}, {"entity": "外籍个人综合所得税收优惠证明", "start_idx": 14, "end_idx": 27, "type": "税务记录"}]}, {"text": "环保税排放量核算记录与非贸项下外汇支付税务证明已完成核对。", "label": [{"entity": "环保税排放量核算记录", "start_idx": 0, "end_idx": 9, "type": "税务记录"}, {"entity": "非贸项下外汇支付税务证明", "start_idx": 11, "end_idx": 22, "type": "税务记录"}]}, {"text": "你必须立即提交专利实施许可费税务登记和年度土地增值税清算报告。", "label": [{"entity": "专利实施许可费税务登记", "start_idx": 7, "end_idx": 17, "type": "税务记录"}, {"entity": "年度土地增值税清算报告", "start_idx": 19, "end_idx": 29, "type": "税务记录"}]}, {"text": "马上准备增值税进项税额抵扣清单和出口退税申报单据，不得延误。", "label": [{"entity": "增值税进项税额抵扣清单", "start_idx": 4, "end_idx": 14, "type": "税务记录"}, {"entity": "出口退税申报单据", "start_idx": 16, "end_idx": 23, "type": "税务记录"}]}, {"text": "税务局已发布税收违法行为的处罚决定，同时审查年税收减免审批表。", "label": [{"entity": "税收违法行为", "start_idx": 6, "end_idx": 11, "type": "税务记录"}, {"entity": "处罚决定", "start_idx": 13, "end_idx": 16, "type": "税务记录"}, {"entity": "审查年税收减免审批表", "start_idx": 20, "end_idx": 29, "type": "税务记录"}]}, {"text": "在房产交易中，务必核对税费凭证并完成印花税购销合同备案。", "label": [{"entity": "税费凭证", "start_idx": 11, "end_idx": 14, "type": "税务记录"}, {"entity": "印花税购销合同备案", "start_idx": 18, "end_idx": 26, "type": "税务记录"}]}, {"text": "尽快整理年企业所得税汇算清缴记录和职工教育经费税前扣除证明。", "label": [{"entity": "年企业所得税汇算清缴记录", "start_idx": 4, "end_idx": 15, "type": "税务记录"}, {"entity": "职工教育经费税前扣除证明", "start_idx": 17, "end_idx": 28, "type": "税务记录"}]}, {"text": "请提供以下资料：专利实施许可费税务登记和流转税票证记录薄。", "label": [{"entity": "专利实施许可费税务登记", "start_idx": 8, "end_idx": 18, "type": "税务记录"}, {"entity": "流转税票证记录薄", "start_idx": 20, "end_idx": 27, "type": "税务记录"}]}, {"text": "请核对并提交年企业所得税汇算清缴记录以及个体工商户营业税申报单。", "label": [{"entity": "年企业所得税汇算清缴记录", "start_idx": 6, "end_idx": 17, "type": "税务记录"}, {"entity": "个体工商户营业税申报单", "start_idx": 20, "end_idx": 30, "type": "税务记录"}]}, {"text": "请准备好职工教育经费税前扣除证明和增值税专用发票记录进行报账。", "label": [{"entity": "职工教育经费税前扣除证明", "start_idx": 4, "end_idx": 15, "type": "税务记录"}, {"entity": "增值税专用发票记录", "start_idx": 17, "end_idx": 25, "type": "税务记录"}]}, {"text": "请将个体工商户营业税申报单和企业税前扣除捐赠物资清单一并提交。", "label": [{"entity": "个体工商户营业税申报单", "start_idx": 2, "end_idx": 12, "type": "税务记录"}, {"entity": "企业税前扣除捐赠物资清单", "start_idx": 14, "end_idx": 25, "type": "税务记录"}]}, {"text": "务必查看并提交税务局关于税收违法行为的处罚决定和个人所得税综合所得汇缴证明。", "label": [{"entity": "税收违法行为的处罚决定", "start_idx": 12, "end_idx": 22, "type": "税务记录"}, {"entity": "个人所得税综合所得汇缴证明", "start_idx": 24, "end_idx": 36, "type": "税务记录"}]}, {"text": "年度企业所得税年度汇缴记录必须严格按照法律规定进行审核。", "label": [{"entity": "年度企业所得税年度汇缴记录", "start_idx": 0, "end_idx": 12, "type": "税务记录"}]}, {"text": "非居民企业源泉扣税记录的准确性对于税收管理至关重要。", "label": [{"entity": "非居民企业源泉扣税记录", "start_idx": 0, "end_idx": 10, "type": "税务记录"}]}, {"text": "财务税项登记册真实反映了保险公司的代扣代缴车险税费记录。", "label": [{"entity": "代扣代缴车险税费记录", "start_idx": 17, "end_idx": 26, "type": "税务记录"}]}, {"text": "保险公司代扣代缴车险税费记录需完整记录在相应的财务税项登记册中。", "label": [{"entity": "代扣代缴车险税费记录", "start_idx": 4, "end_idx": 13, "type": "税务记录"}]}, {"text": "印花税购销合同备案过程中，外籍个人可申请综合所得税收优惠证明。", "label": [{"entity": "印花税购销合同备案", "start_idx": 0, "end_idx": 8, "type": "税务记录"}, {"entity": "综合所得税收优惠证明", "start_idx": 20, "end_idx": 29, "type": "税务记录"}]}, {"text": "外籍个人综合所得税收优惠证明在印花税购销合同备案时需一并提交。", "label": [{"entity": "外籍个人综合所得税收优惠证明", "start_idx": 0, "end_idx": 13, "type": "税务记录"}, {"entity": "印花税购销合同备案", "start_idx": 15, "end_idx": 23, "type": "税务记录"}]}, {"text": "环保税排放量核算记录是专利实施许可费税务登记的重要参考依据。", "label": [{"entity": "环保税排放量核算记录", "start_idx": 0, "end_idx": 9, "type": "税务记录"}, {"entity": "专利实施许可费税务登记", "start_idx": 11, "end_idx": 21, "type": "税务记录"}]}, {"text": "专利实施许可费税务登记应详细注明相关的环保税排放量核算记录。", "label": [{"entity": "专利实施许可费税务登记", "start_idx": 0, "end_idx": 10, "type": "税务记录"}, {"entity": "相关的环保税排放量核算记录", "start_idx": 16, "end_idx": 28, "type": "税务记录"}]}, {"text": "电子商务进口关税缴纳记录的透明化有利于税控加油机发票的领用管理。", "label": [{"entity": "电子商务进口关税缴纳记录", "start_idx": 0, "end_idx": 11, "type": "税务记录"}, {"entity": "税控加油机发票的领用管理", "start_idx": 19, "end_idx": 30, "type": "税务记录"}]}, {"text": "税控加油机发票领用记录需与电子商务进口关税缴纳记录保持一致。", "label": [{"entity": "税控加油机发票领用记录", "start_idx": 0, "end_idx": 10, "type": "税务记录"}, {"entity": "电子商务进口关税缴纳记录", "start_idx": 13, "end_idx": 24, "type": "税务记录"}]}, {"text": "根据信用报告，该企业负债率低于30%，不存在信用污点历史。", "label": [{"entity": "负债率低于30%", "start_idx": 10, "end_idx": 17, "type": "信用记录"}, {"entity": "不存在信用污点历史", "start_idx": 19, "end_idx": 27, "type": "信用记录"}]}, {"text": "近三年内，该企业无信用卡账户止付记录，信用账户亦无透支消费现象。", "label": [{"entity": "无信用卡账户止付记录", "start_idx": 8, "end_idx": 17, "type": "信用记录"}, {"entity": "信用账户亦无透支消费现象", "start_idx": 19, "end_idx": 30, "type": "信用记录"}]}, {"text": "在审查中发现，该企业信用账户保持良好，无任何透支消费及信用污点历史。", "label": [{"entity": "信用账户保持良好", "start_idx": 10, "end_idx": 17, "type": "信用记录"}, {"entity": "无任何透支消费", "start_idx": 19, "end_idx": 25, "type": "信用记录"}]}, {"text": "历经审查，该企业信用历史中未曾出现信用卡盗刷记录，且准时全额还款记录已长达十年。", "label": [{"entity": "信用卡盗刷记录", "start_idx": 17, "end_idx": 23, "type": "信用记录"}, {"entity": "准时全额还款记录", "start_idx": 26, "end_idx": 33, "type": "信用记录"}]}, {"text": "综合评估显示，该企业负债率低于30%，信用行为表现优良，符合金融合同要求。", "label": [{"entity": "负债率低于30%", "start_idx": 10, "end_idx": 17, "type": "信用记录"}, {"entity": "信用行为表现优良", "start_idx": 19, "end_idx": 26, "type": "信用记录"}]}, {"text": "三年了，贷款一直准时还，信用卡五年也没拖欠过哦。", "label": [{"entity": "贷款一直准时还", "start_idx": 4, "end_idx": 10, "type": "信用记录"}, {"entity": "信用卡五年也没拖欠过", "start_idx": 12, "end_idx": 21, "type": "信用记录"}]}, {"text": "信用卡透支我都是控制在20%以下的，今年也没逾期过。", "label": [{"entity": "信用卡透支我都是控制在20%以下的", "start_idx": 0, "end_idx": 16, "type": "信用记录"}, {"entity": "今年也没逾期过", "start_idx": 18, "end_idx": 24, "type": "信用记录"}]}, {"text": "信用报告里清清白白，2017年信用卡就没拖欠过。", "label": [{"entity": "信用报告里清清白白", "start_idx": 0, "end_idx": 8, "type": "信用记录"}, {"entity": "2017年信用卡就没拖欠过", "start_idx": 10, "end_idx": 22, "type": "信用记录"}]}, {"text": "看我这信用报告，公共事业费都没欠过，贷款还款记录也齐全。", "label": [{"entity": "信用报告", "start_idx": 3, "end_idx": 6, "type": "信用记录"}, {"entity": "公共事业费都没欠过", "start_idx": 8, "end_idx": 16, "type": "信用记录"}, {"entity": "贷款还款记录也齐全", "start_idx": 18, "end_idx": 26, "type": "信用记录"}]}, {"text": "信用这块儿我可是优良，网络贷款逾期什么的，不存在的。", "label": [{"entity": "信用这块儿我可是优良", "start_idx": 0, "end_idx": 9, "type": "信用记录"}, {"entity": "网络贷款逾期什么的", "start_idx": 11, "end_idx": 19, "type": "信用记录"}]}, {"text": "注意保持信用优良，四年内让你的分期付款记录保持干净！", "label": [{"entity": "信用优良", "start_idx": 4, "end_idx": 7, "type": "信用记录"}, {"entity": "分期付款记录", "start_idx": 15, "end_idx": 20, "type": "信用记录"}]}, {"text": "从2017年开始，你的信用卡和贷款记录就应保持无拖欠，让信用评分持续稳定。", "label": [{"entity": "信用卡和贷款记录", "start_idx": 11, "end_idx": 18, "type": "信用记录"}, {"entity": "信用评分", "start_idx": 28, "end_idx": 31, "type": "信用记录"}]}, {"text": "在信用报告中，确保不要出现公共事业欠费，近三年贷款还款也要准时。", "label": [{"entity": "公共事业欠费", "start_idx": 13, "end_idx": 18, "type": "信用记录"}, {"entity": "近三年贷款还款", "start_idx": 20, "end_idx": 26, "type": "信用记录"}]}, {"text": "近两年，你的信用账户不应有透支情况，保持银行征信报告显示正常。", "label": [{"entity": "银行征信报告显示正常", "start_idx": 20, "end_idx": 29, "type": "信用记录"}]}, {"text": "网络贷款别逾期，保证信用良好，房贷每年按时还，逾期记录绝不能有。", "label": [{"entity": "信用良好", "start_idx": 10, "end_idx": 13, "type": "信用记录"}, {"entity": "房贷每年按时还", "start_idx": 15, "end_idx": 21, "type": "信用记录"}, {"entity": "逾期记录", "start_idx": 23, "end_idx": 26, "type": "信用记录"}]}, {"text": "你看，我这几年车贷一直按时还，一次逾期都没，连公共事业的费用都没欠过。", "label": [{"entity": "车贷一直按时还，一次逾期都没", "start_idx": 7, "end_idx": 20, "type": "信用记录"}, {"entity": "连公共事业的费用都没欠过", "start_idx": 22, "end_idx": 33, "type": "信用记录"}]}, {"text": "信用评估说我一直挺守信的，信用卡连续一年多没拖欠过。", "label": [{"entity": "信用评估说我一直挺守信的", "start_idx": 0, "end_idx": 11, "type": "信用记录"}, {"entity": "信用卡连续一年多没拖欠过", "start_idx": 13, "end_idx": 24, "type": "信用记录"}]}, {"text": "我近三年信用卡账户也没被止付过，电信费也没欠过。", "label": [{"entity": "信用卡账户也没被止付过", "start_idx": 4, "end_idx": 14, "type": "信用记录"}, {"entity": "电信费也没欠过", "start_idx": 16, "end_idx": 22, "type": "信用记录"}]}, {"text": "近几年信用卡账户没止付，公共事业的费用我也一直按时交。", "label": [{"entity": "信用卡账户没止付", "start_idx": 3, "end_idx": 10, "type": "信用记录"}, {"entity": "公共事业的费用我也一直按时交", "start_idx": 12, "end_idx": 25, "type": "信用记录"}]}, {"text": "我这一年信用卡账单都是按时还的，从年头到现在，个人贷款也没拖欠过。", "label": [{"entity": "信用卡账单都是按时还的", "start_idx": 4, "end_idx": 14, "type": "信用记录"}, {"entity": "个人贷款也没拖欠过", "start_idx": 23, "end_idx": 31, "type": "信用记录"}]}, {"text": "病例1：患者信用卡五年内无拖欠记录，年透支额度始终控制在20%以下。", "label": [{"entity": "五年内无拖欠记录", "start_idx": 9, "end_idx": 16, "type": "信用记录"}, {"entity": "年透支额度始终控制在20%以下", "start_idx": 18, "end_idx": 32, "type": "信用记录"}]}, {"text": "病例2：银行征信报告显示，患者账户正常，近五年内无信用卡账户冻结情况。", "label": [{"entity": "银行征信报告显示，患者账户正常，近五年内无信用卡账户冻结情况", "start_idx": 4, "end_idx": 33, "type": "信用记录"}]}, {"text": "病例3：患者年房贷按时还款，无逾期记录，互联网金融信用记录亦保持良好。", "label": [{"entity": "无逾期记录", "start_idx": 14, "end_idx": 18, "type": "信用记录"}, {"entity": "互联网金融信用记录亦保持良好", "start_idx": 20, "end_idx": 33, "type": "信用记录"}]}, {"text": "病例4：自2017年以来，患者信用卡账户无拖欠，年透支额度控制在20%以下。", "label": [{"entity": "患者信用卡账户无拖欠", "start_idx": 13, "end_idx": 22, "type": "信用记录"}, {"entity": "年透支额度控制在20%以下", "start_idx": 24, "end_idx": 36, "type": "信用记录"}]}, {"text": "病例5：近三年内，患者无信用卡账户止付记录，房贷按时还款，无任何逾期情况。", "label": [{"entity": "无信用卡账户止付记录", "start_idx": 11, "end_idx": 20, "type": "信用记录"}, {"entity": "房贷按时还款", "start_idx": 22, "end_idx": 27, "type": "信用记录"}, {"entity": "无任何逾期情况", "start_idx": 29, "end_idx": 35, "type": "信用记录"}]}, {"text": "银行经理说我的信用卡用得挺省心的，年透支额度一直控制在20%以下，而且准时全额还款都十年了。", "label": [{"entity": "信用卡", "start_idx": 7, "end_idx": 9, "type": "信用记录"}, {"entity": "年透支额度一直控制在20%以下", "start_idx": 17, "end_idx": 31, "type": "信用记录"}, {"entity": "准时全额还款", "start_idx": 35, "end_idx": 40, "type": "信用记录"}, {"entity": "都十年了", "start_idx": 41, "end_idx": 44, "type": "信用记录"}]}, {"text": "我查了你的贷款账户，信用保持得真好，从来都没拖欠过，今年也没信用卡透支的问题。", "label": [{"entity": "信用保持得真好", "start_idx": 10, "end_idx": 16, "type": "信用记录"}, {"entity": "从来都没拖欠过", "start_idx": 18, "end_idx": 24, "type": "信用记录"}, {"entity": "今年也没信用卡透支的问题", "start_idx": 26, "end_idx": 37, "type": "信用记录"}]}, {"text": "他那信用账户最近两年都不超支，用花呗也是规规矩矩的，没拖欠过。", "label": [{"entity": "信用账户最近两年都不超支", "start_idx": 2, "end_idx": 13, "type": "信用记录"}, {"entity": "用花呗也是规规矩矩的", "start_idx": 15, "end_idx": 24, "type": "信用记录"}, {"entity": "没拖欠过", "start_idx": 26, "end_idx": 29, "type": "信用记录"}]}, {"text": "我那信用卡从2017年用到现在，都没拖欠过，年透支额也控制在20%以下。", "label": [{"entity": "信用卡", "start_idx": 2, "end_idx": 4, "type": "信用记录"}, {"entity": "2017年用到现在", "start_idx": 6, "end_idx": 14, "type": "信用记录"}, {"entity": "都没拖欠过", "start_idx": 16, "end_idx": 20, "type": "信用记录"}, {"entity": "年透支额也控制在20%以下", "start_idx": 22, "end_idx": 34, "type": "信用记录"}]}, {"text": "他的信用卡账户开通以来，就没逾期过，网络贷款也没逾期记录，信用确实不错。", "label": [{"entity": "没逾期过", "start_idx": 13, "end_idx": 16, "type": "信用记录"}, {"entity": "也没逾期记录", "start_idx": 22, "end_idx": 27, "type": "信用记录"}]}, {"text": "根据车贷年度信用报告，张先生的车贷按时还款，无任何逾期记录，且无法院强制执行的痕迹。", "label": [{"entity": "车贷年度信用报告", "start_idx": 2, "end_idx": 9, "type": "信用记录"}, {"entity": "车贷按时还款", "start_idx": 15, "end_idx": 20, "type": "信用记录"}, {"entity": "无任何逾期记录", "start_idx": 22, "end_idx": 28, "type": "信用记录"}, {"entity": "无法院强制执行的痕迹", "start_idx": 31, "end_idx": 40, "type": "信用记录"}]}, {"text": "在最新的信用行为评估中，李小姐的信用卡连续12个月保持无拖欠的优良记录。", "label": [{"entity": "连续12个月保持无拖欠的优良记录", "start_idx": 19, "end_idx": 34, "type": "信用记录"}]}, {"text": "经过信用审核，赵先生的支付宝花呗使用情况良好，不存在拖欠现象，同时他的信用卡在五年内也未出现拖欠。", "label": [{"entity": "花呗使用情况良好，不存在拖欠现象", "start_idx": 14, "end_idx": 29, "type": "信用记录"}, {"entity": "信用卡在五年内也未出现拖欠", "start_idx": 35, "end_idx": 47, "type": "信用记录"}]}, {"text": "在信用历史方面，王女士自去年以来没有信用卡透支记录，并且在近五年内，她的信用卡账户也未遭遇冻结。", "label": [{"entity": "没有信用卡透支记录", "start_idx": 16, "end_idx": 24, "type": "信用记录"}, {"entity": "信用卡账户也未遭遇冻结", "start_idx": 36, "end_idx": 46, "type": "信用记录"}]}, {"text": "经过信用评估，陈先生的信用卡在过去五年内无拖欠记录，其信用历史同样干净，没有逾期还款的记录。", "label": [{"entity": "无拖欠记录", "start_idx": 20, "end_idx": 24, "type": "信用记录"}, {"entity": "没有逾期还款的记录", "start_idx": 36, "end_idx": 44, "type": "信用记录"}]}, {"text": "根据我国某商业银行信用管理部门的数据显示，该行客户信用报告中无公共事业欠费记录，个人资信状况普遍良好。", "label": [{"entity": "无公共事业欠费记录", "start_idx": 30, "end_idx": 38, "type": "信用记录"}, {"entity": "个人资信状况普遍良好", "start_idx": 40, "end_idx": 49, "type": "信用记录"}]}, {"text": "在深入调查贷款账户表现时发现，客户群体信用良好，无拖欠记录，亦无任何信用卡账户被追讨的记录。", "label": [{"entity": "无拖欠记录", "start_idx": 24, "end_idx": 28, "type": "信用记录"}, {"entity": "亦无任何信用卡账户被追讨的记录", "start_idx": 30, "end_idx": 44, "type": "信用记录"}]}, {"text": "近三年内，银行监控的贷款账户未出现拖欠现象，信用卡用户连续12个月保持无拖欠记录的良好态势。", "label": [{"entity": "拖欠现象", "start_idx": 17, "end_idx": 20, "type": "信用记录"}, {"entity": "无拖欠记录", "start_idx": 35, "end_idx": 39, "type": "信用记录"}]}, {"text": "通过信用行为评估，多数客户的个人资信状况良好，信用行为评估显示其始终守信，体现了良好的金融素养。", "label": [{"entity": "个人资信状况良好", "start_idx": 14, "end_idx": 21, "type": "信用记录"}, {"entity": "始终守信", "start_idx": 32, "end_idx": 35, "type": "信用记录"}, {"entity": "良好的金融素养", "start_idx": 40, "end_idx": 46, "type": "信用记录"}]}, {"text": "我花呗用得挺规矩的，一直都是按时还，信用报告上也显示负债率不到30%呢。", "label": [{"entity": "花呗", "start_idx": 1, "end_idx": 2, "type": "信用记录"}, {"entity": "信用报告", "start_idx": 18, "end_idx": 21, "type": "信用记录"}, {"entity": "负债率不到30%", "start_idx": 26, "end_idx": 33, "type": "信用记录"}]}, {"text": "我的信用记录里，互联网金融逾期啥的都没有，信用卡年账单也是一分不差地按时还。", "label": [{"entity": "信用记录", "start_idx": 2, "end_idx": 5, "type": "信用记录"}, {"entity": "互联网金融逾期", "start_idx": 8, "end_idx": 14, "type": "信用记录"}, {"entity": "信用卡年账单", "start_idx": 21, "end_idx": 26, "type": "信用记录"}, {"entity": "一分不差地按时还", "start_idx": 29, "end_idx": 36, "type": "信用记录"}]}, {"text": "你看我这信用报告，虚拟信用卡逾期啥的根本找不到，商业贷款也没拖欠过，信用挺不错的吧？", "label": [{"entity": "虚拟信用卡逾期啥的根本找不到", "start_idx": 9, "end_idx": 22, "type": "信用记录"}, {"entity": "商业贷款也没拖欠过", "start_idx": 24, "end_idx": 32, "type": "信用记录"}]}, {"text": "我近四年分期付款可是一次都没逾期，房贷年年按时还，信用这块儿我还是挺自信的。", "label": [{"entity": "房贷年年按时还", "start_idx": 17, "end_idx": 23, "type": "信用记录"}]}, {"text": "最近这三四年吧，贷款违约啥的跟我没关系，信用卡账户也没被追讨过，还是挺守时的。", "label": [{"entity": "贷款违约", "start_idx": 8, "end_idx": 11, "type": "信用记录"}, {"entity": "信用卡账户", "start_idx": 20, "end_idx": 24, "type": "信用记录"}]}, {"text": "根据近五年信用报告，该账户无信用卡冻结记录，且有十年准时全额还款的良好记录。", "label": [{"entity": "无信用卡冻结记录", "start_idx": 13, "end_idx": 20, "type": "信用记录"}, {"entity": "十年准时全额还款的良好记录", "start_idx": 24, "end_idx": 36, "type": "信用记录"}]}, {"text": "综合考察近三年信用历史，未发现贷款违约及逾期记录。", "label": [{"entity": "贷款违约", "start_idx": 15, "end_idx": 18, "type": "信用记录"}, {"entity": "逾期记录", "start_idx": 20, "end_idx": 23, "type": "信用记录"}]}, {"text": "经过审核，该信用报告中未见电信欠费，且年度内无商业贷款拖欠，整体信用情况良好。", "label": [{"entity": "未见电信欠费", "start_idx": 11, "end_idx": 16, "type": "信用记录"}, {"entity": "无商业贷款拖欠", "start_idx": 22, "end_idx": 28, "type": "信用记录"}, {"entity": "整体信用情况良好", "start_idx": 30, "end_idx": 37, "type": "信用记录"}]}, {"text": "在贷款账户的信用评估中，我们发现其信用良好，无拖欠现象，同时也没有公共事业欠费记录。", "label": [{"entity": "信用良好", "start_idx": 17, "end_idx": 20, "type": "信用记录"}, {"entity": "无拖欠现象", "start_idx": 22, "end_idx": 26, "type": "信用记录"}, {"entity": "没有公共事业欠费记录", "start_idx": 31, "end_idx": 40, "type": "信用记录"}]}, {"text": "该用户无银行贷款拖欠，信用评分保持稳定，且近三年内无贷款违约情况。", "label": [{"entity": "无银行贷款拖欠", "start_idx": 3, "end_idx": 9, "type": "信用记录"}, {"entity": "信用评分保持稳定", "start_idx": 11, "end_idx": 18, "type": "信用记录"}, {"entity": "近三年内无贷款违约情况", "start_idx": 21, "end_idx": 31, "type": "信用记录"}]}, {"text": "信用记录显示，无法院强制执行与网络贷款逾期情况，整体信用评价良好。", "label": [{"entity": "无法院强制执行", "start_idx": 7, "end_idx": 13, "type": "信用记录"}, {"entity": "网络贷款逾期情况", "start_idx": 15, "end_idx": 22, "type": "信用记录"}, {"entity": "整体信用评价良好", "start_idx": 24, "end_idx": 31, "type": "信用记录"}]}, {"text": "江西省上饶市婺源县徽派建筑群一座，承载历史记忆；广东省深圳市南山科技园办公楼一层，彰显现代风貌。", "label": [{"entity": "江西省上饶市婺源县徽派建筑群一座", "start_idx": 0, "end_idx": 15, "type": "实体资产"}, {"entity": "广东省深圳市南山科技园办公楼一层", "start_idx": 24, "end_idx": 39, "type": "实体资产"}]}, {"text": "西安古城墙内百年老字号店铺一间，延续传统工艺；江苏省无锡市宜兴紫砂壶工作室一家，弘扬工匠精神。", "label": [{"entity": "西安古城墙内百年老字号店铺一间", "start_idx": 0, "end_idx": 14, "type": "实体资产"}, {"entity": "江苏省无锡市宜兴紫砂壶工作室一家", "start_idx": 23, "end_idx": 38, "type": "实体资产"}]}, {"text": "辽宁省沈阳市和平区，一块实体土地见证城市变迁；同一区域内的健身房，成为市民健康生活的选择。", "label": [{"entity": "一块实体土地", "start_idx": 10, "end_idx": 15, "type": "实体资产"}, {"entity": "同一区域内的健身房", "start_idx": 23, "end_idx": 31, "type": "实体资产"}]}, {"text": "云南省昆明市五华区茶叶庄园一处，蕴藏自然馈赠；上海市徐汇区三层别墅一栋，尽显都市繁华。", "label": [{"entity": "云南省昆明市五华区茶叶庄园一处", "start_idx": 0, "end_idx": 14, "type": "实体资产"}, {"entity": "上海市徐汇区三层别墅一栋", "start_idx": 23, "end_idx": 34, "type": "实体资产"}]}, {"text": "四川成都宽窄巷子特色餐馆一座，诠释美食文化；浙江省义乌市国际商贸城摊位一个，连通世界贸易。", "label": [{"entity": "四川成都宽窄巷子特色餐馆一座", "start_idx": 0, "end_idx": 13, "type": "实体资产"}, {"entity": "浙江省义乌市国际商贸城摊位一个", "start_idx": 22, "end_idx": 36, "type": "实体资产"}]}, {"text": "腾讯控股在深交所上市的1000股股份，与大连湾渔港一艘30吨级渔船的出海情况备受关注。", "label": [{"entity": "腾讯控股在深交所上市的1000股股份", "start_idx": 0, "end_idx": 17, "type": "实体资产"}, {"entity": "大连湾渔港一艘30吨级渔船", "start_idx": 20, "end_idx": 32, "type": "实体资产"}]}, {"text": "合肥市庐阳区一家特色书店的文化氛围，与区内一栋写字楼商业活力相映成趣。", "label": [{"entity": "合肥市庐阳区一家特色书店", "start_idx": 0, "end_idx": 11, "type": "实体资产"}, {"entity": "区内一栋写字楼", "start_idx": 19, "end_idx": 25, "type": "实体资产"}]}, {"text": "位于广东省深圳市南山科技园的办公楼一层，正成为创业者的聚集地，而杭州市西湖区的精品民宿一栋，亦成为旅游者的热门选择。", "label": [{"entity": "广东省深圳市南山科技园的办公楼一层", "start_idx": 2, "end_idx": 18, "type": "实体资产"}, {"entity": "杭州市西湖区的精品民宿一栋", "start_idx": 32, "end_idx": 44, "type": "实体资产"}]}, {"text": "河南省郑州市金水区的电影院线一条，为市民提供了丰富的文娱生活，与此同时，京郊50亩农业用地的使用权亦备受瞩目。", "label": [{"entity": "河南省郑州市金水区的电影院线一条", "start_idx": 0, "end_idx": 15, "type": "实体资产"}, {"entity": "京郊50亩农业用地的使用权", "start_idx": 36, "end_idx": 48, "type": "实体资产"}]}, {"text": "海南省三亚市海棠湾的度假别墅一套，与杭州市西湖区精品民宿一栋，共同体现了当下旅游市场的多元化需求。", "label": [{"entity": "海南省三亚市海棠湾的度假别墅一套", "start_idx": 0, "end_idx": 15, "type": "实体资产"}, {"entity": "杭州市西湖区精品民宿一栋", "start_idx": 18, "end_idx": 29, "type": "实体资产"}]}, {"text": "四川省成都市武侯区某餐馆，拥有优越地理位置的商铺门面，寻求经营合作伙伴。", "label": [{"entity": "四川省成都市武侯区某餐馆", "start_idx": 0, "end_idx": 11, "type": "实体资产"}]}, {"text": "在资产清单中，包括一块位于热门地段的实体土地及深交所上市的1000股腾讯控股。", "label": [{"entity": "一块位于热门地段的实体土地", "start_idx": 9, "end_idx": 21, "type": "实体资产"}, {"entity": "深交所上市的1000股腾讯控股", "start_idx": 23, "end_idx": 37, "type": "实体资产"}]}, {"text": "南京市秦淮河畔的古董收藏品一批，以及江苏省无锡市宜兴的知名紫砂壶工作室，均为文化投资之选。", "label": [{"entity": "南京市秦淮河畔的古董收藏品一批", "start_idx": 0, "end_idx": 14, "type": "实体资产"}, {"entity": "江苏省无锡市宜兴的知名紫砂壶工作室", "start_idx": 18, "end_idx": 34, "type": "实体资产"}]}, {"text": "位于杭州市西湖区的精品民宿一栋，与大连湾渔港一艘30吨级渔船，均为潜力投资标的。", "label": [{"entity": "位于杭州市西湖区的精品民宿一栋", "start_idx": 0, "end_idx": 14, "type": "实体资产"}, {"entity": "大连湾渔港一艘30吨级渔船", "start_idx": 17, "end_idx": 29, "type": "实体资产"}]}, {"text": "辽宁省沈阳市和平区的健身房一处，以及江西省上饶市婺源县的徽派建筑群一座，期待慧眼识珠的投资者。", "label": [{"entity": "辽宁省沈阳市和平区的健身房一处", "start_idx": 0, "end_idx": 14, "type": "实体资产"}, {"entity": "江西省上饶市婺源县的徽派建筑群一座", "start_idx": 18, "end_idx": 34, "type": "实体资产"}]}, {"text": "广西省桂林市阳朔县山水画苑一处土地，近日引发关注。", "label": [{"entity": "广西省桂林市阳朔县山水画苑一处土地", "start_idx": 0, "end_idx": 16, "type": "实体资产"}]}, {"text": "该处位于阳朔的实体土地，已成为当地热门话题。", "label": [{"entity": "阳朔的实体土地", "start_idx": 4, "end_idx": 10, "type": "实体资产"}]}, {"text": "河南省郑州市金水区电影院线一条，与厦门市思明区一套海景住宅形成鲜明对比。", "label": [{"entity": "河南省郑州市金水区电影院线一条", "start_idx": 0, "end_idx": 14, "type": "实体资产"}, {"entity": "厦门市思明区一套海景住宅", "start_idx": 17, "end_idx": 28, "type": "实体资产"}]}, {"text": "南京市秦淮河畔的古董收藏品，以及青海省西宁市城西区的一家藏式家具店，各具特色。", "label": [{"entity": "南京市秦淮河畔的古董收藏品", "start_idx": 0, "end_idx": 12, "type": "实体资产"}, {"entity": "青海省西宁市城西区的一家藏式家具店", "start_idx": 16, "end_idx": 32, "type": "实体资产"}]}, {"text": "吉林省长春市南关区一所幼儿园，引进了一套新型实体设备，旨在提升教育质量。", "label": [{"entity": "一套新型实体设备", "start_idx": 18, "end_idx": 25, "type": "实体资产"}]}, {"text": "上海市徐汇区的三层别墅一栋与一块土地已纳入本次法律文档的审查范围。", "label": [{"entity": "上海市徐汇区的三层别墅一栋", "start_idx": 0, "end_idx": 12, "type": "实体资产"}, {"entity": "一块土地", "start_idx": 14, "end_idx": 17, "type": "实体资产"}]}, {"text": "南京市秦淮河畔的古董收藏品一批及海南省三亚市海棠湾的度假别墅一套，均为本次评估对象。", "label": [{"entity": "南京市秦淮河畔的古董收藏品一批", "start_idx": 0, "end_idx": 14, "type": "实体资产"}, {"entity": "海南省三亚市海棠湾的度假别墅一套", "start_idx": 16, "end_idx": 31, "type": "实体资产"}]}, {"text": "云南省昆明市五华区的茶叶庄园一处及大连湾渔港的30吨级渔船，已列入法律审查清单。", "label": [{"entity": "云南省昆明市五华区的茶叶庄园一处", "start_idx": 0, "end_idx": 15, "type": "实体资产"}, {"entity": "大连湾渔港的30吨级渔船", "start_idx": 17, "end_idx": 28, "type": "实体资产"}]}, {"text": "本次法律文档亦涉及一块实体土地以及吉林省长春市南关区的幼儿园一所。", "label": [{"entity": "一块实体土地", "start_idx": 9, "end_idx": 14, "type": "实体资产"}, {"entity": "吉林省长春市南关区的幼儿园一所", "start_idx": 17, "end_idx": 31, "type": "实体资产"}]}, {"text": "辽宁省沈阳市和平区的健身房一处，与南京市秦淮河畔的古董收藏品一批，均在本次法律审查范围内。", "label": [{"entity": "辽宁省沈阳市和平区的健身房一处", "start_idx": 0, "end_idx": 14, "type": "实体资产"}, {"entity": "南京市秦淮河畔的古董收藏品一批", "start_idx": 17, "end_idx": 31, "type": "实体资产"}]}, {"text": "湖南省长沙市岳麓区的咖啡厅经营权与一处实体仓库正进行在线咨询。", "label": [{"entity": "长沙市岳麓区的咖啡厅经营权", "start_idx": 3, "end_idx": 15, "type": "实体资产"}, {"entity": "一处实体仓库", "start_idx": 17, "end_idx": 22, "type": "实体资产"}]}, {"text": "上海市徐汇区的三层别墅以及浙江省义乌市国际商贸城的摊位成为咨询焦点。", "label": [{"entity": "上海市徐汇区的三层别墅", "start_idx": 0, "end_idx": 10, "type": "实体资产"}, {"entity": "浙江省义乌市国际商贸城的摊位", "start_idx": 13, "end_idx": 26, "type": "实体资产"}]}, {"text": "关于上海市徐汇区三层别墅和一处写字楼的具体情况，客户正在在线客服处进行详细了解。", "label": [{"entity": "上海市徐汇区三层别墅", "start_idx": 2, "end_idx": 11, "type": "实体资产"}, {"entity": "一处写字楼", "start_idx": 13, "end_idx": 17, "type": "实体资产"}]}, {"text": "广东省深圳市南山科技园的办公楼一层与重庆市渝中区商业步行街的商铺在线客服进行了位置及面积的讨论。", "label": [{"entity": "广东省深圳市南山科技园的办公楼一层", "start_idx": 0, "end_idx": 16, "type": "实体资产"}, {"entity": "重庆市渝中区商业步行街的商铺", "start_idx": 18, "end_idx": 31, "type": "实体资产"}]}, {"text": "客户针对福建省厦门市思明区的海景住宅及广东省深圳市南山科技园办公楼一层进行了一系列咨询。", "label": [{"entity": "福建省厦门市思明区的海景住宅", "start_idx": 4, "end_idx": 17, "type": "实体资产"}, {"entity": "广东省深圳市南山科技园办公楼一层", "start_idx": 19, "end_idx": 34, "type": "实体资产"}]}, {"text": "重庆市渝中区步行街一间商铺经营者，曾任安徽省合肥市庐阳区一家特色书店店长。", "label": [{"entity": "重庆市渝中区步行街一间商铺", "start_idx": 0, "end_idx": 12, "type": "实体资产"}, {"entity": "安徽省合肥市庐阳区一家特色书店", "start_idx": 19, "end_idx": 33, "type": "实体资产"}]}, {"text": "江西省上饶市婺源县一座徽派建筑群的守护者，拥有湖南省长沙市岳麓区一处咖啡厅的经营权。", "label": [{"entity": "江西省上饶市婺源县一座徽派建筑群", "start_idx": 0, "end_idx": 15, "type": "实体资产"}, {"entity": "湖南省长沙市岳麓区一处咖啡厅的经营权", "start_idx": 23, "end_idx": 40, "type": "实体资产"}]}, {"text": "在上海市徐汇区成功运营一套实体商铺，同时拥有一栋三层别墅。", "label": [{"entity": "一套实体商铺", "start_idx": 11, "end_idx": 16, "type": "实体资产"}, {"entity": "一栋三层别墅", "start_idx": 22, "end_idx": 27, "type": "实体资产"}]}, {"text": "掌握京郊50亩农业用地使用权，同时经营杭州市西湖区5A级景区内的一家酒店。", "label": [{"entity": "京郊50亩农业用地使用权", "start_idx": 2, "end_idx": 13, "type": "实体资产"}, {"entity": "杭州市西湖区5A级景区内的一家酒店", "start_idx": 19, "end_idx": 35, "type": "实体资产"}]}, {"text": "四川省成都市武侯区一餐馆的负责人，同时持有深交所上市的1000股腾讯控股股份。", "label": [{"entity": "1000股腾讯控股股份", "start_idx": 27, "end_idx": 37, "type": "实体资产"}]}, {"text": "安徽省合肥市庐阳区的一座实体厂房旁，开设了一家风格独特的书店。", "label": [{"entity": "安徽省合肥市庐阳区的一座实体厂房", "start_idx": 0, "end_idx": 15, "type": "实体资产"}]}, {"text": "江西省南昌市东湖区，一门老字号药店毗邻于一间现代化写字楼。", "label": [{"entity": "一间现代化写字楼", "start_idx": 20, "end_idx": 27, "type": "实体资产"}]}, {"text": "位于重庆市渝中区的商业步行街上，一间商铺隔壁是来自四川成都宽窄巷子的知名特色餐馆。", "label": [{"entity": "商铺", "start_idx": 18, "end_idx": 19, "type": "实体资产"}, {"entity": "四川成都宽窄巷子", "start_idx": 25, "end_idx": 32, "type": "实体资产"}, {"entity": "知名特色餐馆", "start_idx": 34, "end_idx": 39, "type": "实体资产"}]}, {"text": "江苏省无锡市宜兴的一家紫砂壶工作室不远处，有一条郑州市金水区的电影院线。", "label": [{"entity": "江苏省无锡市宜兴的一家紫砂壶工作室", "start_idx": 0, "end_idx": 16, "type": "实体资产"}, {"entity": "郑州市金水区的电影院线", "start_idx": 24, "end_idx": 34, "type": "实体资产"}]}, {"text": "四川成都宽窄巷子特色餐馆门面焕新，吸引游客驻足。", "label": [{"entity": "四川成都宽窄巷子特色餐馆门面", "start_idx": 0, "end_idx": 13, "type": "实体资产"}]}, {"text": "一套重要设备在深圳证券交易所上市的腾讯控股公司旗下投入使用。", "label": [{"entity": "一套重要设备", "start_idx": 0, "end_idx": 5, "type": "实体资产"}]}, {"text": "陕西省西安市碑林区历史建筑与海南省三亚市海棠湾度假别墅，均为我国文化遗产的瑰宝。", "label": [{"entity": "陕西省西安市碑林区历史建筑", "start_idx": 0, "end_idx": 12, "type": "实体资产"}, {"entity": "海南省三亚市海棠湾度假别墅", "start_idx": 14, "end_idx": 26, "type": "实体资产"}]}, {"text": "位于广东深圳南山科技园的办公楼一层，商铺门面商业氛围浓厚。", "label": [{"entity": "广东深圳南山科技园的办公楼一层", "start_idx": 2, "end_idx": 16, "type": "实体资产"}, {"entity": "商铺门面", "start_idx": 18, "end_idx": 21, "type": "实体资产"}]}, {"text": "深交所上市的腾讯控股公司千股股票与西安碑林区历史建筑，成为投资市场关注焦点。", "label": [{"entity": "腾讯控股公司千股股票", "start_idx": 6, "end_idx": 15, "type": "实体资产"}, {"entity": "西安碑林区历史建筑", "start_idx": 17, "end_idx": 25, "type": "实体资产"}]}, {"text": "辽宁省沈阳市和平区的一处健身房内，投资者持有深交所上市的1000股腾讯控股。", "label": [{"entity": "深交所上市的1000股腾讯控股", "start_idx": 22, "end_idx": 36, "type": "实体资产"}]}, {"text": "云南省昆明市五华区的茶叶庄园风光秀丽，杭州市西湖区5A级景区内的酒店服务至上。", "label": [{"entity": "云南省昆明市五华区的茶叶庄园", "start_idx": 0, "end_idx": 13, "type": "实体资产"}, {"entity": "杭州市西湖区5A级景区内的酒店", "start_idx": 19, "end_idx": 33, "type": "实体资产"}]}, {"text": "一处功能齐全的实体仓库与一套商业价值高的商铺构成了商业地产组合。", "label": [{"entity": "一处功能齐全的实体仓库", "start_idx": 0, "end_idx": 10, "type": "实体资产"}, {"entity": "一套商业价值高的商铺", "start_idx": 12, "end_idx": 21, "type": "实体资产"}]}, {"text": "河南省郑州市金水区的电影院线生意兴隆，与此同时，西安古城墙内的一家百年老字号店铺也门庭若市。", "label": [{"entity": "河南省郑州市金水区的电影院线", "start_idx": 0, "end_idx": 13, "type": "实体资产"}, {"entity": "西安古城墙内的一家百年老字号店铺", "start_idx": 24, "end_idx": 39, "type": "实体资产"}]}, {"text": "一套高效的实体设备被安置在上海市徐汇区的一栋三层别墅中，助力科研工作。", "label": [{"entity": "一套高效的实体设备", "start_idx": 0, "end_idx": 8, "type": "实体资产"}, {"entity": "上海市徐汇区的一栋三层别墅", "start_idx": 13, "end_idx": 25, "type": "实体资产"}]}, {"text": "江西省上饶市婺源县徽派建筑群一座，近日引入了一套新型实体设备，引发社交媒体广泛关注。", "label": [{"entity": "江西省上饶市婺源县徽派建筑群一座", "start_idx": 0, "end_idx": 15, "type": "实体资产"}]}, {"text": "实体设备一套在婺源县徽派建筑群的成功安装，标志着该地区传统文化保护与现代科技融合的新篇章。", "label": [{"entity": "实体设备一套", "start_idx": 0, "end_idx": 5, "type": "实体资产"}]}, {"text": "婺源徽派建筑群添置的实体设备一套，为当地旅游业发展注入新活力的同时，也展现了传统与现代的完美结合。", "label": [{"entity": "婺源徽派建筑群添置的实体设备一套", "start_idx": 0, "end_idx": 15, "type": "实体资产"}]}, {"text": "用户成功领取现金红包20元，并通过支付宝花呗分期付款3000元。", "label": [{"entity": "现金红包20元", "start_idx": 6, "end_idx": 12, "type": "交易信息"}, {"entity": "支付宝花呗分期付款3000元", "start_idx": 17, "end_idx": 30, "type": "交易信息"}]}, {"text": "本次交易采用货到付款，金额为600元，同时涉及资金转移800元。", "label": [{"entity": "货到付款", "start_idx": 6, "end_idx": 9, "type": "交易信息"}, {"entity": "600元", "start_idx": 14, "end_idx": 17, "type": "交易信息"}, {"entity": "800元", "start_idx": 27, "end_idx": 30, "type": "交易信息"}]}, {"text": "信用卡消费记录显示800美元，交易验证码为990065确保安全。", "label": [{"entity": "800美元", "start_idx": 9, "end_idx": 13, "type": "交易信息"}, {"entity": "990065", "start_idx": 21, "end_idx": 26, "type": "交易信息"}]}, {"text": "客户运用购物积分成功兑换1000分，并已退还押金500元。", "label": [{"entity": "1000分", "start_idx": 12, "end_idx": 16, "type": "交易信息"}, {"entity": "500元", "start_idx": 24, "end_idx": 27, "type": "交易信息"}]}, {"text": "用户在社交媒体上分享动态，显示其支付宝账户余额100元，并附上了移动支付码1289753246。", "label": [{"entity": "100元", "start_idx": 23, "end_idx": 26, "type": "交易信息"}, {"entity": "1289753246", "start_idx": 37, "end_idx": 46, "type": "交易信息"}]}, {"text": "消费者通过积分兑换活动获得1000分购物积分，结账时合计消费1200元。", "label": [{"entity": "1000分购物积分", "start_idx": 13, "end_idx": 21, "type": "交易信息"}, {"entity": "1200元", "start_idx": 30, "end_idx": 34, "type": "交易信息"}]}, {"text": "在进行移动支付时，系统提示输入交易验证码990065和移动支付码1289753246以确保交易安全。", "label": [{"entity": "交易验证码990065", "start_idx": 15, "end_idx": 25, "type": "交易信息"}, {"entity": "移动支付码1289753246", "start_idx": 27, "end_idx": 41, "type": "交易信息"}]}, {"text": "消费者选择使用支付宝花呗分期付款3000元购物，并计划下月还款1500元。", "label": [{"entity": "3000元", "start_idx": 16, "end_idx": 20, "type": "交易信息"}, {"entity": "1500元", "start_idx": 31, "end_idx": 35, "type": "交易信息"}]}, {"text": "投资者通过支付通道费率0.6%完成投资，成功获得项目分红1000元。", "label": [{"entity": "支付通道费率0.6%", "start_idx": 5, "end_idx": 14, "type": "交易信息"}, {"entity": "1000元", "start_idx": 28, "end_idx": 32, "type": "交易信息"}]}, {"text": "本次消费结账金额合计1200元，已成功绑定尾号为1234的银行卡。", "label": [{"entity": "1200元", "start_idx": 10, "end_idx": 14, "type": "交易信息"}, {"entity": "尾号为1234的银行卡", "start_idx": 21, "end_idx": 31, "type": "交易信息"}]}, {"text": "用户贷款还款金额1500元，同时完成银行转账5元操作。", "label": [{"entity": "1500元", "start_idx": 8, "end_idx": 12, "type": "交易信息"}, {"entity": "5元", "start_idx": 22, "end_idx": 23, "type": "交易信息"}]}, {"text": "本次跨境支付产生手续费50元，交易对手方账户名为张三。", "label": [{"entity": "手续费50元", "start_idx": 8, "end_idx": 13, "type": "交易信息"}, {"entity": "张三", "start_idx": 24, "end_idx": 25, "type": "交易信息"}]}, {"text": "数字货币交易中，用户成功购买了0.5BTC，并在购物车结算500元商品。", "label": [{"entity": "0.5BTC", "start_idx": 15, "end_idx": 20, "type": "交易信息"}, {"entity": "500元", "start_idx": 29, "end_idx": 32, "type": "交易信息"}]}, {"text": "会员卡消费享受50%折扣，交易验证码为990065。", "label": [{"entity": "990065", "start_idx": 19, "end_idx": 24, "type": "交易信息"}]}, {"text": "移动支付时使用的支付码为1289753246，通过微信转账了200元。", "label": [{"entity": "支付码为1289753246", "start_idx": 8, "end_idx": 21, "type": "交易信息"}, {"entity": "转账了200元", "start_idx": 27, "end_idx": 33, "type": "交易信息"}]}, {"text": "投资项目已于2024年6月分红1000元。", "label": [{"entity": "分红1000元", "start_idx": 13, "end_idx": 19, "type": "交易信息"}]}, {"text": "本次汇款总额为800元，涉及0.5BTC的数字货币交易。", "label": [{"entity": "800元", "start_idx": 7, "end_idx": 10, "type": "交易信息"}, {"entity": "0.5BTC", "start_idx": 14, "end_idx": 19, "type": "交易信息"}]}, {"text": "退款申请编号为RK20240618，客户银行卡绑定成功，尾号为1234。", "label": [{"entity": "退款申请编号为RK20240618", "start_idx": 0, "end_idx": 16, "type": "交易信息"}, {"entity": "客户银行卡绑定成功", "start_idx": 18, "end_idx": 26, "type": "交易信息"}, {"entity": "尾号为1234", "start_idx": 28, "end_idx": 34, "type": "交易信息"}]}, {"text": "信用卡消费记录显示，上月支出总额为800美元，同时成功退还押金500元。", "label": [{"entity": "800美元", "start_idx": 17, "end_idx": 21, "type": "交易信息"}, {"entity": "500元", "start_idx": 31, "end_idx": 34, "type": "交易信息"}]}, {"text": "资金转账操作中，实际转账金额为800元，并利用优惠券抵扣了10元。", "label": [{"entity": "800元", "start_idx": 15, "end_idx": 18, "type": "交易信息"}, {"entity": "10元", "start_idx": 29, "end_idx": 31, "type": "交易信息"}]}, {"text": "进行押金退还操作时，虽然需支付跨境支付手续费50元，但依然成功退还了500元。", "label": [{"entity": "跨境支付手续费50元", "start_idx": 15, "end_idx": 24, "type": "交易信息"}, {"entity": "退还了500元", "start_idx": 31, "end_idx": 37, "type": "交易信息"}]}, {"text": "网上银行转账过程中，尽管产生了10元手续费，但汇款总额仍达到了800元。", "label": [{"entity": "10元手续费", "start_idx": 15, "end_idx": 20, "type": "交易信息"}, {"entity": "800元", "start_idx": 31, "end_idx": 34, "type": "交易信息"}]}, {"text": "在数字货币交易方面，成功完成了0.5BTC的交易，并已捆绑手机号13800138000作为安全验证。", "label": [{"entity": "0.5BTC", "start_idx": 15, "end_idx": 20, "type": "交易信息"}, {"entity": "手机号13800138000", "start_idx": 29, "end_idx": 42, "type": "交易信息"}]}, {"text": "银行客户成功领取现金红包20元，同时账户内10000元资金被冻结以保障交易安全。", "label": [{"entity": "现金红包20元", "start_idx": 8, "end_idx": 14, "type": "交易信息"}, {"entity": "10000元", "start_idx": 21, "end_idx": 26, "type": "交易信息"}]}, {"text": "在2024年6月19日，一笔800元的资金转移已完成交易确认。", "label": [{"entity": "一笔800元的资金转移", "start_idx": 12, "end_idx": 22, "type": "交易信息"}]}, {"text": "本次贷款还款操作中，交易对手方为张三，还款金额为1500元。", "label": [{"entity": "张三", "start_idx": 16, "end_idx": 17, "type": "交易信息"}, {"entity": "1500元", "start_idx": 24, "end_idx": 28, "type": "交易信息"}]}, {"text": "用户通过银行账户完成1500元贷款还款的同时，支付了30元快递费用。", "label": [{"entity": "1500元贷款还款", "start_idx": 10, "end_idx": 18, "type": "交易信息"}, {"entity": "30元快递费用", "start_idx": 26, "end_idx": 32, "type": "交易信息"}]}, {"text": "在本次消费中，客户使用支付宝红包抵扣5元，并额外使用优惠券抵扣10元。", "label": [{"entity": "5元", "start_idx": 18, "end_idx": 19, "type": "交易信息"}, {"entity": "10元", "start_idx": 31, "end_idx": 33, "type": "交易信息"}]}, {"text": "担保交易编号GB20240618已成功发放现金红包，用户可领取20元。", "label": [{"entity": "GB20240618", "start_idx": 6, "end_idx": 15, "type": "交易信息"}, {"entity": "20元", "start_idx": 31, "end_idx": 33, "type": "交易信息"}]}, {"text": "支付流水码A123456789B于2024年6月19日完成交易确认。", "label": [{"entity": "支付流水码A123456789B", "start_idx": 0, "end_idx": 15, "type": "交易信息"}, {"entity": "2024年6月19日", "start_idx": 17, "end_idx": 26, "type": "交易信息"}]}, {"text": "用户交易密码已顺利修改，新的移动支付码为1289753246。", "label": [{"entity": "交易密码", "start_idx": 2, "end_idx": 5, "type": "交易信息"}, {"entity": "新的移动支付码", "start_idx": 12, "end_idx": 18, "type": "交易信息"}, {"entity": "1289753246", "start_idx": 20, "end_idx": 29, "type": "交易信息"}]}, {"text": "支付宝花呗已为用户办理3000元分期付款，捆绑手机号为13800138000。", "label": [{"entity": "3000元分期付款", "start_idx": 11, "end_idx": 19, "type": "交易信息"}, {"entity": "13800138000", "start_idx": 27, "end_idx": 37, "type": "交易信息"}]}, {"text": "本次结账金额合计1200元，顾客通过微信支付优惠5元。", "label": [{"entity": "结账金额合计1200元", "start_idx": 2, "end_idx": 12, "type": "交易信息"}, {"entity": "微信支付优惠5元", "start_idx": 18, "end_idx": 25, "type": "交易信息"}]}, {"text": "退款申请编号RK20240618处理好了吗？我这边现金红包领了20元呢。", "label": [{"entity": "退款申请编号RK20240618", "start_idx": 0, "end_idx": 15, "type": "交易信息"}, {"entity": "20元", "start_idx": 31, "end_idx": 33, "type": "交易信息"}]}, {"text": "确认一下，交易确认时间是2024年6月19日，对应退款编号RK20240618的对吗？", "label": [{"entity": "2024年6月19日", "start_idx": 12, "end_idx": 21, "type": "交易信息"}, {"entity": "退款编号RK20240618", "start_idx": 25, "end_idx": 38, "type": "交易信息"}]}, {"text": "这次购物车结算500元，快递费支付了30元，感觉还是划得来。", "label": [{"entity": "500元", "start_idx": 7, "end_idx": 10, "type": "交易信息"}, {"entity": "30元", "start_idx": 18, "end_idx": 20, "type": "交易信息"}]}, {"text": "用优惠券抵扣了10元，然后支付宝花呗分期付款3000元，真是方便。", "label": [{"entity": "抵扣了10元", "start_idx": 4, "end_idx": 9, "type": "交易信息"}, {"entity": "支付宝花呗分期付款3000元", "start_idx": 13, "end_idx": 26, "type": "交易信息"}]}, {"text": "移动支付码1289753246已经设置好了，交易流水日期是2024年6月的。", "label": [{"entity": "移动支付码1289753246", "start_idx": 0, "end_idx": 14, "type": "交易信息"}, {"entity": "2024年6月", "start_idx": 29, "end_idx": 35, "type": "交易信息"}]}, {"text": "病例1：患者需偿还贷款金额1500元，其账户冻结金额10000元待解冻。", "label": [{"entity": "1500元", "start_idx": 13, "end_idx": 17, "type": "交易信息"}, {"entity": "10000元", "start_idx": 26, "end_idx": 31, "type": "交易信息"}]}, {"text": "病例2：本次网银转账收取手续费10元，患者汇款总额达到800元。", "label": [{"entity": "10元", "start_idx": 15, "end_idx": 17, "type": "交易信息"}, {"entity": "800元", "start_idx": 27, "end_idx": 30, "type": "交易信息"}]}, {"text": "病例3：涉及数字货币交易，患者支付网银转账手续费10元及0.5BTC。", "label": [{"entity": "支付网银转账手续费10元", "start_idx": 15, "end_idx": 26, "type": "交易信息"}, {"entity": "0.5BTC", "start_idx": 28, "end_idx": 33, "type": "交易信息"}]}, {"text": "病例4：患者承担快递费30元，同时使用购物积分兑换1000分。", "label": [{"entity": "快递费30元", "start_idx": 8, "end_idx": 13, "type": "交易信息"}, {"entity": "1000分", "start_idx": 25, "end_idx": 29, "type": "交易信息"}]}, {"text": "病例5：患者通过支付宝账户余额100元进行购物车结算，金额为500元。", "label": [{"entity": "支付宝账户余额100元", "start_idx": 8, "end_idx": 18, "type": "交易信息"}, {"entity": "购物车结算", "start_idx": 21, "end_idx": 25, "type": "交易信息"}, {"entity": "金额为500元", "start_idx": 27, "end_idx": 33, "type": "交易信息"}]}, {"text": "快递费用方面，合同规定支付金额为30元，同时明确了押金退还金额为500元。", "label": [{"entity": "30元", "start_idx": 16, "end_idx": 18, "type": "交易信息"}, {"entity": "500元", "start_idx": 32, "end_idx": 35, "type": "交易信息"}]}, {"text": "针对退款流程，客户需提供编号为RK20240618的退款申请，投资项目的分红金额为1000元。", "label": [{"entity": "编号为RK20240618的退款申请", "start_idx": 12, "end_idx": 29, "type": "交易信息"}, {"entity": "1000元", "start_idx": 41, "end_idx": 45, "type": "交易信息"}]}, {"text": "在进行货币兑换时，合同明确金额为200欧元，且现金支付情况下找零为5元。", "label": [{"entity": "200欧元", "start_idx": 16, "end_idx": 20, "type": "交易信息"}, {"entity": "5元", "start_idx": 33, "end_idx": 34, "type": "交易信息"}]}, {"text": "另外，对于货币兑换金额200欧元的情况，微信支付方式将收取5元手续费。", "label": [{"entity": "200欧元", "start_idx": 11, "end_idx": 15, "type": "交易信息"}, {"entity": "5元", "start_idx": 29, "end_idx": 30, "type": "交易信息"}]}, {"text": "最后，金融合同中涉及的虚拟货币交易，交易验证码为990065，交易金额为0.5BTC。", "label": [{"entity": "交易验证码为990065", "start_idx": 18, "end_idx": 29, "type": "交易信息"}, {"entity": "交易金额为0.5BTC", "start_idx": 31, "end_idx": 41, "type": "交易信息"}]}, {"text": "根据法律规定，交易确认时间定于2024年6月19日，涉及银行转账金额为5元。", "label": [{"entity": "2024年6月19日", "start_idx": 15, "end_idx": 24, "type": "交易信息"}, {"entity": "5元", "start_idx": 35, "end_idx": 36, "type": "交易信息"}]}, {"text": "在本次投资项目中，分红金额为1000元，网银转账手续费为10元，均已依法扣除。", "label": [{"entity": "1000元", "start_idx": 14, "end_idx": 18, "type": "交易信息"}, {"entity": "10元", "start_idx": 28, "end_idx": 30, "type": "交易信息"}]}, {"text": "肺泡蛋白质沉积症患者中有遗传性血管神经性水肿的家族史。", "label": [{"entity": "肺泡蛋白质沉积症", "start_idx": 0, "end_idx": 7, "type": "疾病"}, {"entity": "遗传性血管神经性水肿", "start_idx": 12, "end_idx": 21, "type": "疾病"}]}, {"text": "研究显示，骨髓增生异常综合征患者中嗜铬细胞瘤的发病率提高。", "label": [{"entity": "骨髓增生异常综合征", "start_idx": 5, "end_idx": 13, "type": "疾病"}, {"entity": "嗜铬细胞瘤", "start_idx": 17, "end_idx": 21, "type": "疾病"}]}, {"text": "探讨肺泡细胞增生症与阿尔茨海默症之间的潜在联系。", "label": [{"entity": "肺泡细胞增生症", "start_idx": 2, "end_idx": 8, "type": "疾病"}, {"entity": "阿尔茨海默症", "start_idx": 10, "end_idx": 15, "type": "疾病"}]}, {"text": "部分嗜酸性粒细胞增多症患者伴有眼肌疲劳症症状。", "label": [{"entity": "嗜酸性粒细胞增多症", "start_idx": 2, "end_idx": 10, "type": "疾病"}, {"entity": "眼肌疲劳症", "start_idx": 15, "end_idx": 19, "type": "疾病"}]}, {"text": "遗传性球形细胞增多症与肌萎缩侧索硬化症存在一定遗传相关性。", "label": [{"entity": "遗传性球形细胞增多症", "start_idx": 0, "end_idx": 9, "type": "疾病"}, {"entity": "肌萎缩侧索硬化症", "start_idx": 11, "end_idx": 18, "type": "疾病"}]}, {"text": "原发性胆汁性肝硬化患者同时并发嗜铬细胞瘤，病情引人关注。", "label": [{"entity": "原发性胆汁性肝硬化", "start_idx": 0, "end_idx": 8, "type": "疾病"}, {"entity": "嗜铬细胞瘤", "start_idx": 15, "end_idx": 19, "type": "疾病"}]}, {"text": "针对脑梗塞后遗症患者，胶质瘤的及时发现显得尤为重要。", "label": [{"entity": "脑梗塞后遗症", "start_idx": 2, "end_idx": 7, "type": "疾病"}, {"entity": "胶质瘤", "start_idx": 11, "end_idx": 13, "type": "疾病"}]}, {"text": "研究表明，淋巴瘤与慢性肾小球肾炎之间存在一定的关联性。", "label": [{"entity": "淋巴瘤", "start_idx": 5, "end_idx": 7, "type": "疾病"}, {"entity": "慢性肾小球肾炎", "start_idx": 9, "end_idx": 15, "type": "疾病"}]}, {"text": "慢性肾小球肾炎患者，部分病例合并巨细胞动脉炎，需警惕。", "label": [{"entity": "慢性肾小球肾炎", "start_idx": 0, "end_idx": 6, "type": "疾病"}, {"entity": "巨细胞动脉炎", "start_idx": 16, "end_idx": 21, "type": "疾病"}]}, {"text": "类风湿关节炎患者，部分伴随睾丸微石症，需加强临床观察。", "label": [{"entity": "类风湿关节炎", "start_idx": 0, "end_idx": 5, "type": "疾病"}, {"entity": "睾丸微石症", "start_idx": 13, "end_idx": 17, "type": "疾病"}]}, {"text": "慢性肾小球肾炎与巨细胞动脉炎的联合诊疗在国内取得重要进展。", "label": [{"entity": "慢性肾小球肾炎", "start_idx": 0, "end_idx": 6, "type": "疾病"}, {"entity": "巨细胞动脉炎", "start_idx": 8, "end_idx": 13, "type": "疾病"}]}, {"text": "针对肌萎缩侧索硬化症患者，合并肺泡蛋白质沉积症伴感染的治疗方案正在研讨中。", "label": [{"entity": "肌萎缩侧索硬化症", "start_idx": 2, "end_idx": 9, "type": "疾病"}, {"entity": "肺泡蛋白质沉积症", "start_idx": 15, "end_idx": 22, "type": "疾病"}]}, {"text": "胃肠道间质瘤与神经纤维瘤病的关联性研究，为临床治疗提供了新视角。", "label": [{"entity": "胃肠道间质瘤", "start_idx": 0, "end_idx": 5, "type": "疾病"}, {"entity": "神经纤维瘤病", "start_idx": 7, "end_idx": 12, "type": "疾病"}]}, {"text": "结节性甲状腺肿患者常伴神经纤维瘤病，多学科联合治疗成效显著。", "label": [{"entity": "结节性甲状腺肿", "start_idx": 0, "end_idx": 6, "type": "疾病"}, {"entity": "神经纤维瘤病", "start_idx": 11, "end_idx": 16, "type": "疾病"}]}, {"text": "骨髓增生异常综合征患者合并胃肠道间质瘤的病例得到广泛关注，医疗团队正致力于综合治疗方案的研究。", "label": [{"entity": "骨髓增生异常综合征", "start_idx": 0, "end_idx": 8, "type": "疾病"}, {"entity": "胃肠道间质瘤", "start_idx": 13, "end_idx": 18, "type": "疾病"}]}, {"text": "客户：我查了资料，好像嗜酸性粒细胞增多症和系统性硬化病有关联是吗？", "label": [{"entity": "嗜酸性粒细胞增多症", "start_idx": 11, "end_idx": 19, "type": "疾病"}, {"entity": "系统性硬化病", "start_idx": 21, "end_idx": 26, "type": "疾病"}]}, {"text": "客服：嗯，这两种病确实有一定的联系，您有什么不舒服的地方可以详细说说。", "label": [{"entity": "病", "start_idx": 8, "end_idx": 8, "type": "疾病"}, {"entity": "病", "start_idx": 8, "end_idx": 8, "type": "疾病"}]}, {"text": "在求职简历中，本人有氟伐他汀钠片与卡培他滨片的销售经验。", "label": [{"entity": "氟伐他汀钠片", "start_idx": 10, "end_idx": 15, "type": "药物"}, {"entity": "卡培他滨片", "start_idx": 17, "end_idx": 21, "type": "药物"}]}, {"text": "曾任苷心安薄膜衣片及卡培他滨片的市场推广专员。", "label": [{"entity": "苷心安薄膜衣片", "start_idx": 2, "end_idx": 8, "type": "药物"}, {"entity": "卡培他滨片", "start_idx": 10, "end_idx": 14, "type": "药物"}]}, {"text": "具备心康宁片和硫酸氢路雷他定片的医药代表资历。", "label": [{"entity": "心康宁片", "start_idx": 2, "end_idx": 5, "type": "药物"}, {"entity": "硫酸氢路雷他定片", "start_idx": 7, "end_idx": 14, "type": "药物"}]}, {"text": "在药品行业，对艾普拉唑肠溶片及拉莫三嗪片的特性有一定了解。", "label": [{"entity": "艾普拉唑肠溶片", "start_idx": 7, "end_idx": 13, "type": "药物"}, {"entity": "拉莫三嗪片", "start_idx": 15, "end_idx": 19, "type": "药物"}]}, {"text": "曾参与蒙脱石散与艾瑞昔布胶囊的产品培训及市场拓展。", "label": [{"entity": "蒙脱石散", "start_idx": 3, "end_idx": 6, "type": "药物"}, {"entity": "艾瑞昔布胶囊", "start_idx": 8, "end_idx": 13, "type": "药物"}]}, {"text": "心康宁片与舒芬太尼注射液的联合用药，在临床治疗中展现了良好的协同效应。", "label": [{"entity": "心康宁片", "start_idx": 0, "end_idx": 3, "type": "药物"}, {"entity": "舒芬太尼注射液", "start_idx": 5, "end_idx": 11, "type": "药物"}]}, {"text": "药粒晶透的瑞舒伐他汀钙片，以其高品质受到患者的一致好评。", "label": [{"entity": "瑞舒伐他汀钙片", "start_idx": 5, "end_idx": 11, "type": "药物"}]}, {"text": "针对感冒症状，酚麻美敏片与苷心安薄膜衣片通常被推荐为联合用药方案。", "label": [{"entity": "酚麻美敏片", "start_idx": 7, "end_idx": 11, "type": "药物"}, {"entity": "苷心安薄膜衣片", "start_idx": 13, "end_idx": 19, "type": "药物"}]}, {"text": "克林霉素磷酸酯凝胶与活络通贴的联合使用，为皮肤病患者带来了新的治疗选择。", "label": [{"entity": "克林霉素磷酸酯凝胶", "start_idx": 0, "end_idx": 8, "type": "药物"}, {"entity": "活络通贴", "start_idx": 10, "end_idx": 13, "type": "药物"}]}, {"text": "来曲唑片与酚麻美敏片的搭配使用，在控制病情的同时，有效缓解了患者的症状。", "label": [{"entity": "来曲唑片", "start_idx": 0, "end_idx": 3, "type": "药物"}, {"entity": "酚麻美敏片", "start_idx": 5, "end_idx": 9, "type": "药物"}]}, {"text": "瑞舒伐他汀钙片，检查药粒晶透性，立即执行！", "label": [{"entity": "瑞舒伐他汀钙片", "start_idx": 0, "end_idx": 6, "type": "药物"}]}, {"text": "请立刻准备克林霉素磷酸酯凝胶和艾瑞昔布胶囊，马上使用！", "label": [{"entity": "克林霉素磷酸酯凝胶", "start_idx": 5, "end_idx": 13, "type": "药物"}, {"entity": "艾瑞昔布胶囊", "start_idx": 15, "end_idx": 20, "type": "药物"}]}, {"text": "替加氟注射剂与阿莫西林克拉维酸钾片，按医嘱同步给药！", "label": [{"entity": "替加氟注射剂", "start_idx": 0, "end_idx": 5, "type": "药物"}, {"entity": "阿莫西林克拉维酸钾片", "start_idx": 7, "end_idx": 16, "type": "药物"}]}, {"text": "奥沙利铂注射剂使用后，紧接着服用氨溴特罗口服液！", "label": [{"entity": "奥沙利铂注射剂", "start_idx": 0, "end_idx": 6, "type": "药物"}, {"entity": "氨溴特罗口服液", "start_idx": 16, "end_idx": 22, "type": "药物"}]}, {"text": "奥美拉唑肠溶胶囊与安宁怡泡腾片，交替服用，注意间隔时间！", "label": [{"entity": "奥美拉唑肠溶胶囊", "start_idx": 0, "end_idx": 7, "type": "药物"}, {"entity": "安宁怡泡腾片", "start_idx": 9, "end_idx": 14, "type": "药物"}]}, {"text": "氯吡格雷片和草本溶栓素，这两个药效果怎么样？", "label": [{"entity": "氯吡格雷片", "start_idx": 0, "end_idx": 4, "type": "药物"}, {"entity": "草本溶栓素", "start_idx": 6, "end_idx": 10, "type": "药物"}]}, {"text": "听说你用了草本溶栓素和那个酮替芬缓释胶囊，真的有效吗？", "label": [{"entity": "草本溶栓素", "start_idx": 5, "end_idx": 9, "type": "药物"}, {"entity": "酮替芬缓释胶囊", "start_idx": 13, "end_idx": 19, "type": "药物"}]}, {"text": "我在吃葡萄糖酸钙口服溶液和艾普拉唑肠溶片，胃不舒服的时候还挺管用的。", "label": [{"entity": "葡萄糖酸钙口服溶液", "start_idx": 3, "end_idx": 11, "type": "药物"}, {"entity": "艾普拉唑肠溶片", "start_idx": 13, "end_idx": 19, "type": "药物"}]}, {"text": "胃不舒服的时候，我就会吃胃舒平咀嚼片，然后再喝点免疫灵冲剂。", "label": [{"entity": "胃舒平咀嚼片", "start_idx": 12, "end_idx": 17, "type": "药物"}, {"entity": "免疫灵冲剂", "start_idx": 24, "end_idx": 28, "type": "药物"}]}, {"text": "氟伐他汀钠片和硫酸氢氯吡格雷片，这两种药能一起吃吗？", "label": [{"entity": "氟伐他汀钠片", "start_idx": 0, "end_idx": 5, "type": "药物"}, {"entity": "硫酸氢氯吡格雷片", "start_idx": 7, "end_idx": 14, "type": "药物"}]}, {"text": "医生，我这次带的药有甲硝唑阴道泡腾片和氨溴特罗口服液，怎么用呢？", "label": [{"entity": "甲硝唑阴道泡腾片", "start_idx": 10, "end_idx": 17, "type": "药物"}, {"entity": "氨溴特罗口服液", "start_idx": 19, "end_idx": 25, "type": "药物"}]}, {"text": "我吃着阿维A胶囊，还得加依折麦布片，这搭配没问题吧？", "label": [{"entity": "阿维A胶囊", "start_idx": 3, "end_idx": 7, "type": "药物"}, {"entity": "依折麦布片", "start_idx": 12, "end_idx": 16, "type": "药物"}]}, {"text": "这药依折麦布片和氟康唑分散片一起吃，效果会不会更好？", "label": [{"entity": "依折麦布片", "start_idx": 2, "end_idx": 6, "type": "药物"}, {"entity": "氟康唑分散片", "start_idx": 8, "end_idx": 13, "type": "药物"}]}, {"text": "我之前用的是依折麦布片，现在想换艾瑞昔布胶囊，这样可以吗？", "label": [{"entity": "依折麦布片", "start_idx": 6, "end_idx": 10, "type": "药物"}, {"entity": "艾瑞昔布胶囊", "start_idx": 16, "end_idx": 21, "type": "药物"}]}, {"text": "那个阿维A胶囊和甲硝唑阴道泡腾片，能同时用吗？有点担心。", "label": [{"entity": "阿维A胶囊", "start_idx": 2, "end_idx": 6, "type": "药物"}, {"entity": "甲硝唑阴道泡腾片", "start_idx": 8, "end_idx": 15, "type": "药物"}]}, {"text": "依折麦布片与丙酸氟替卡松鼻喷雾剂在临床治疗中表现出良好协同效应。", "label": [{"entity": "依折麦布片", "start_idx": 0, "end_idx": 4, "type": "药物"}, {"entity": "丙酸氟替卡松鼻喷雾剂", "start_idx": 6, "end_idx": 15, "type": "药物"}]}, {"text": "在心内科常用药物中，苷心安薄膜衣片与氨溴特罗口服液联合应用，疗效显著。", "label": [{"entity": "苷心安薄膜衣片", "start_idx": 10, "end_idx": 16, "type": "药物"}, {"entity": "氨溴特罗口服液", "start_idx": 18, "end_idx": 24, "type": "药物"}]}, {"text": "净血宝颗粒和心康宁片在调节血脂、保护心血管方面各有优势，常被推荐使用。", "label": [{"entity": "净血宝颗粒", "start_idx": 0, "end_idx": 4, "type": "药物"}, {"entity": "心康宁片", "start_idx": 6, "end_idx": 9, "type": "药物"}]}, {"text": "米氮平片对于情绪稳定疗效确切，活络通贴则适用于缓解肌肉疼痛，两者互补。", "label": [{"entity": "米氮平片", "start_idx": 0, "end_idx": 3, "type": "药物"}, {"entity": "活络通贴", "start_idx": 15, "end_idx": 18, "type": "药物"}]}, {"text": "硫酸氢路雷他定片在抗过敏方面效果显著，氟尿嘧啶植入剂则适用于肿瘤治疗，针对性强。", "label": [{"entity": "硫酸氢路雷他定片", "start_idx": 0, "end_idx": 7, "type": "药物"}, {"entity": "氟尿嘧啶植入剂", "start_idx": 19, "end_idx": 25, "type": "药物"}]}, {"text": "胃舒平咀嚼片与聪慧金水口服液在社交媒体上受到用户范围值的讨论。", "label": [{"entity": "胃舒平咀嚼片", "start_idx": 0, "end_idx": 5, "type": "药物"}, {"entity": "聪慧金水口服液", "start_idx": 7, "end_idx": 13, "type": "药物"}]}, {"text": "舒芬太尼注射液和艾瑞昔布胶囊的使用效果，成为网友客观报告的焦点。", "label": [{"entity": "舒芬太尼注射液", "start_idx": 0, "end_idx": 6, "type": "药物"}, {"entity": "艾瑞昔布胶囊", "start_idx": 8, "end_idx": 13, "type": "药物"}]}, {"text": "罗红霉素缓释片搭配胃舒平咀嚼片的用药经验，引起了不少网友的关注。", "label": [{"entity": "罗红霉素缓释片", "start_idx": 0, "end_idx": 6, "type": "药物"}, {"entity": "胃舒平咀嚼片", "start_idx": 9, "end_idx": 14, "type": "药物"}]}, {"text": "聪慧金水口服液药粒晶透的特点，在社交媒体上获得了范围值的讨论。", "label": [{"entity": "聪慧金水口服液", "start_idx": 0, "end_idx": 6, "type": "药物"}, {"entity": "药粒晶透", "start_idx": 7, "end_idx": 10, "type": "药物"}]}, {"text": "氟康唑分散片与艾普拉唑肠溶片的用药对比，成为了网络上的热议话题。", "label": [{"entity": "氟康唑分散片", "start_idx": 0, "end_idx": 5, "type": "药物"}, {"entity": "艾普拉唑肠溶片", "start_idx": 7, "end_idx": 13, "type": "药物"}]}, {"text": "奥美拉唑肠溶胶囊与奥沙利铂注射剂在医药法规范围内使用受限。", "label": [{"entity": "奥美拉唑肠溶胶囊", "start_idx": 0, "end_idx": 7, "type": "药物"}, {"entity": "奥沙利铂注射剂", "start_idx": 9, "end_idx": 15, "type": "药物"}]}, {"text": "阿维A胶囊及来曲唑片的临床应用，敏感粒度范围已明确界定。", "label": [{"entity": "阿维A胶囊", "start_idx": 0, "end_idx": 4, "type": "药物"}, {"entity": "来曲唑片", "start_idx": 6, "end_idx": 9, "type": "药物"}]}, {"text": "法律文件规定，硫酸庆大霉素注射液与甲硝唑阴道泡腾片须在医生指导下使用。", "label": [{"entity": "硫酸庆大霉素注射液", "start_idx": 7, "end_idx": 15, "type": "药物"}, {"entity": "甲硝唑阴道泡腾片", "start_idx": 17, "end_idx": 24, "type": "药物"}]}, {"text": "针对阴道不规则出血和面部抽搐现象，立即进行详细检查。", "label": [{"entity": "阴道不规则出血", "start_idx": 2, "end_idx": 8, "type": "临床表现"}, {"entity": "面部抽搐", "start_idx": 10, "end_idx": 13, "type": "临床表现"}]}, {"text": "全身乏力和面部潮红发热感，需进行全面的身体评估。", "label": [{"entity": "全身乏力", "start_idx": 0, "end_idx": 3, "type": "临床表现"}, {"entity": "面部潮红", "start_idx": 5, "end_idx": 8, "type": "临床表现"}, {"entity": "发热感", "start_idx": 9, "end_idx": 11, "type": "临床表现"}]}, {"text": "长期失眠多梦及耳鸣伴听力下降，务必及时就诊。", "label": [{"entity": "长期失眠多梦", "start_idx": 0, "end_idx": 5, "type": "临床表现"}, {"entity": "耳鸣", "start_idx": 7, "end_idx": 8, "type": "临床表现"}, {"entity": "听力下降", "start_idx": 10, "end_idx": 13, "type": "临床表现"}]}, {"text": "持续性胃部胀气和肩颈僵硬疼痛，需重视并迅速治疗。", "label": [{"entity": "持续性胃部胀气", "start_idx": 0, "end_idx": 6, "type": "临床表现"}, {"entity": "肩颈僵硬疼痛", "start_idx": 8, "end_idx": 13, "type": "临床表现"}]}, {"text": "体温升高及眼睛干涩有异物感，请尽快就医检查。", "label": [{"entity": "体温升高", "start_idx": 0, "end_idx": 3, "type": "临床表现"}, {"entity": "眼睛干涩", "start_idx": 5, "end_idx": 8, "type": "临床表现"}, {"entity": "异物感", "start_idx": 10, "end_idx": 12, "type": "临床表现"}]}, {"text": "据报道，部分患者出现情绪低落，对事物提不起兴趣，同时伴有关节无规律疼痛的症状。", "label": [{"entity": "情绪低落", "start_idx": 10, "end_idx": 13, "type": "临床表现"}, {"entity": "对事物提不起兴趣", "start_idx": 15, "end_idx": 22, "type": "临床表现"}, {"entity": "关节无规律疼痛", "start_idx": 28, "end_idx": 34, "type": "临床表现"}]}, {"text": "研究显示，长期失眠多梦和带痰的频繁咳嗽在一些人群中较为常见。", "label": [{"entity": "长期失眠多梦", "start_idx": 5, "end_idx": 10, "type": "临床表现"}, {"entity": "带痰的频繁咳嗽", "start_idx": 12, "end_idx": 18, "type": "临床表现"}]}, {"text": "有关医疗病例记录，部分患者遭遇关节肿胀，活动受限，并伴有喉咙痛及声音嘶哑。", "label": [{"entity": "关节肿胀", "start_idx": 15, "end_idx": 18, "type": "临床表现"}, {"entity": "活动受限", "start_idx": 20, "end_idx": 23, "type": "临床表现"}, {"entity": "喉咙痛", "start_idx": 28, "end_idx": 30, "type": "临床表现"}, {"entity": "声音嘶哑", "start_idx": 32, "end_idx": 35, "type": "临床表现"}]}, {"text": "近期发现部分病例出现双侧下肢水肿以及反复短暂晕厥的现象。", "label": [{"entity": "双侧下肢水肿", "start_idx": 10, "end_idx": 15, "type": "临床表现"}, {"entity": "反复短暂晕厥", "start_idx": 18, "end_idx": 23, "type": "临床表现"}]}, {"text": "在部分患者中，无痛性血尿与关节无规律疼痛的情况同时存在。", "label": [{"entity": "无痛性血尿", "start_idx": 7, "end_idx": 11, "type": "临床表现"}, {"entity": "关节无规律疼痛", "start_idx": 13, "end_idx": 19, "type": "临床表现"}]}, {"text": "针对持续性腰背部疼痛和胃部胀气，必须立即进行详细医学检查。", "label": [{"entity": "持续性腰背部疼痛", "start_idx": 2, "end_idx": 9, "type": "临床表现"}, {"entity": "胃部胀气", "start_idx": 11, "end_idx": 14, "type": "临床表现"}]}, {"text": "口腔内若出现白斑或皮肤有瘀斑，须尽快前往专业机构进行鉴定。", "label": [{"entity": "白斑", "start_idx": 6, "end_idx": 7, "type": "临床表现"}, {"entity": "皮肤有瘀斑", "start_idx": 9, "end_idx": 13, "type": "临床表现"}]}, {"text": "对于胸闷心慌以及持续性髋关节疼痛，需严格遵医嘱采取治疗措施。", "label": [{"entity": "胸闷心慌", "start_idx": 2, "end_idx": 5, "type": "临床表现"}, {"entity": "持续性髋关节疼痛", "start_idx": 8, "end_idx": 15, "type": "临床表现"}]}, {"text": "长期失眠多梦及面部抽搐现象，务必及时就医诊断治疗。", "label": [{"entity": "长期失眠多梦", "start_idx": 0, "end_idx": 5, "type": "临床表现"}, {"entity": "面部抽搐", "start_idx": 7, "end_idx": 10, "type": "临床表现"}]}, {"text": "体重急剧下降并伴有耳鸣听力下降者，请迅速进行医疗评估。", "label": [{"entity": "体重急剧下降", "start_idx": 0, "end_idx": 5, "type": "临床表现"}, {"entity": "伴有耳鸣", "start_idx": 7, "end_idx": 10, "type": "临床表现"}, {"entity": "听力下降", "start_idx": 11, "end_idx": 14, "type": "临床表现"}]}, {"text": "在审查相关法律文档中，以下症状被列为敏感范围值：面部潮红伴有发热感，肩膀及颈部出现僵硬疼痛。", "label": [{"entity": "面部潮红伴有发热感", "start_idx": 24, "end_idx": 32, "type": "临床表现"}, {"entity": "肩膀及颈部出现僵硬疼痛", "start_idx": 34, "end_idx": 44, "type": "临床表现"}]}, {"text": "针对当事人的病历分析，发现存在双侧下肢水肿，同时伴有体温升高现象。", "label": [{"entity": "双侧下肢水肿", "start_idx": 15, "end_idx": 20, "type": "临床表现"}, {"entity": "体温升高现象", "start_idx": 26, "end_idx": 31, "type": "临床表现"}]}, {"text": "报告指出，部分案例中出现突然言语不清，面部抽搐或肌肉不自主跳动的症状。", "label": [{"entity": "突然言语不清", "start_idx": 12, "end_idx": 17, "type": "临床表现"}, {"entity": "面部抽搐", "start_idx": 19, "end_idx": 22, "type": "临床表现"}, {"entity": "肌肉不自主跳动", "start_idx": 24, "end_idx": 30, "type": "临床表现"}]}, {"text": "在法律文件中，持续性胃部胀气，恶心并伴有呕吐感的症状被特别关注。", "label": [{"entity": "持续性胃部胀气", "start_idx": 7, "end_idx": 13, "type": "临床表现"}, {"entity": "恶心", "start_idx": 15, "end_idx": 16, "type": "临床表现"}, {"entity": "伴有呕吐感的症状", "start_idx": 18, "end_idx": 25, "type": "临床表现"}]}, {"text": "此外，肩膀和颈部僵硬疼痛，并伴随呼吸困难以及哮鸣音的情况也在敏感症状列表中。", "label": [{"entity": "肩膀和颈部僵硬疼痛", "start_idx": 3, "end_idx": 11, "type": "临床表现"}, {"entity": "呼吸困难", "start_idx": 16, "end_idx": 19, "type": "临床表现"}, {"entity": "哮鸣音", "start_idx": 22, "end_idx": 24, "type": "临床表现"}]}, {"text": "请针对以下症状及时就医：喉咙痛伴有声音嘶哑，务必检查。", "label": [{"entity": "喉咙痛伴有声音嘶哑", "start_idx": 12, "end_idx": 20, "type": "临床表现"}]}, {"text": "出现剧烈腹痛或阵发性腹痛伴有恶心，需立即就诊。", "label": [{"entity": "剧烈腹痛", "start_idx": 2, "end_idx": 5, "type": "临床表现"}, {"entity": "阵发性腹痛伴有恶心", "start_idx": 7, "end_idx": 15, "type": "临床表现"}]}, {"text": "若胸痛加剧且咳嗽时伴有金属音调，建议尽快就医。", "label": [{"entity": "胸痛加剧", "start_idx": 1, "end_idx": 4, "type": "临床表现"}, {"entity": "咳嗽", "start_idx": 6, "end_idx": 7, "type": "临床表现"}, {"entity": "伴有金属音调", "start_idx": 9, "end_idx": 14, "type": "临床表现"}]}, {"text": "眼球震颤、视力模糊以及恶心呕吐感，须立即寻求医疗帮助。", "label": [{"entity": "眼球震颤", "start_idx": 0, "end_idx": 3, "type": "临床表现"}, {"entity": "视力模糊", "start_idx": 5, "end_idx": 8, "type": "临床表现"}, {"entity": "恶心呕吐感", "start_idx": 11, "end_idx": 15, "type": "临床表现"}]}, {"text": "胸痛加剧且指甲出现横纹或白点，请及时进行专业检查。", "label": [{"entity": "胸痛加剧", "start_idx": 0, "end_idx": 3, "type": "临床表现"}, {"entity": "指甲出现横纹或白点", "start_idx": 5, "end_idx": 13, "type": "临床表现"}]}, {"text": "注意身体状况，若有关节肿胀伴随活动受限、双侧下肢水肿情况，立即就医！", "label": [{"entity": "关节肿胀伴随活动受限", "start_idx": 9, "end_idx": 18, "type": "临床表现"}, {"entity": "双侧下肢水肿", "start_idx": 20, "end_idx": 25, "type": "临床表现"}]}, {"text": "发现非特异性肌肉酸痛、单侧肢体无力症状，请及时进行专业检查。", "label": [{"entity": "非特异性肌肉酸痛", "start_idx": 2, "end_idx": 9, "type": "临床表现"}, {"entity": "单侧肢体无力症状", "start_idx": 11, "end_idx": 18, "type": "临床表现"}]}, {"text": "如皮肤表面出现蜘蛛痣、面部有潮红发热感，请尽快咨询医生。", "label": [{"entity": "蜘蛛痣", "start_idx": 7, "end_idx": 9, "type": "临床表现"}, {"entity": "面部有潮红发热感", "start_idx": 11, "end_idx": 18, "type": "临床表现"}]}, {"text": "耳鸣且伴有听力下降、双侧下肢水肿者，请立刻前往医院检查治疗。", "label": [{"entity": "耳鸣", "start_idx": 0, "end_idx": 1, "type": "临床表现"}, {"entity": "伴有听力下降", "start_idx": 3, "end_idx": 8, "type": "临床表现"}, {"entity": "双侧下肢水肿", "start_idx": 10, "end_idx": 15, "type": "临床表现"}]}, {"text": "在本次在线客服聊天记录中，患者详细询问了硬膜下血肿钻孔引流术与肾上腺肿瘤切除术的相关问题。", "label": [{"entity": "硬膜下血肿钻孔引流术", "start_idx": 20, "end_idx": 29, "type": "医疗程序"}, {"entity": "肾上腺肿瘤切除术", "start_idx": 31, "end_idx": 38, "type": "医疗程序"}]}, {"text": "针对肺结节的治疗，客服详细介绍了立体定向放射治疗与肺部结节冷冻消融术的优势。", "label": [{"entity": "立体定向放射治疗", "start_idx": 16, "end_idx": 23, "type": "医疗程序"}, {"entity": "肺部结节冷冻消融术", "start_idx": 25, "end_idx": 33, "type": "医疗程序"}]}, {"text": "胸壁肿瘤切除重建术与肝脏局部消融治疗的过程在本次聊天中被深入讨论。", "label": [{"entity": "胸壁肿瘤切除重建术", "start_idx": 0, "end_idx": 8, "type": "医疗程序"}, {"entity": "肝脏局部消融治疗", "start_idx": 10, "end_idx": 17, "type": "医疗程序"}]}, {"text": "客服人员还解答了心脏瓣膜置换术后康复训练方案的制定问题。", "label": [{"entity": "心脏瓣膜置换术", "start_idx": 8, "end_idx": 14, "type": "医疗程序"}, {"entity": "康复训练方案", "start_idx": 16, "end_idx": 21, "type": "医疗程序"}]}, {"text": "基因测序步骤与脑血管造影术在疾病诊断中的重要性，亦在本次在线客服中被强调。", "label": [{"entity": "基因测序", "start_idx": 0, "end_idx": 3, "type": "医疗程序"}, {"entity": "脑血管造影术", "start_idx": 7, "end_idx": 12, "type": "医疗程序"}]}, {"text": "针对胎儿心脏超声检查结果，立即安排膝关节置换手术。", "label": [{"entity": "胎儿心脏超声检查", "start_idx": 2, "end_idx": 9, "type": "医疗程序"}, {"entity": "膝关节置换手术", "start_idx": 17, "end_idx": 23, "type": "医疗程序"}]}, {"text": "立即为患者执行前列腺增生激光切除术，随后进行髓核摘除手术。", "label": [{"entity": "前列腺增生激光切除术", "start_idx": 7, "end_idx": 16, "type": "医疗程序"}, {"entity": "髓核摘除手术", "start_idx": 22, "end_idx": 27, "type": "医疗程序"}]}, {"text": "命令执行膀胱镜下尿道狭窄切开术，紧接着安排胸腺切除术。", "label": [{"entity": "膀胱镜下尿道狭窄切开术", "start_idx": 4, "end_idx": 14, "type": "医疗程序"}, {"entity": "胸腺切除术", "start_idx": 21, "end_idx": 25, "type": "医疗程序"}]}, {"text": "冠状动脉造影检查后，迅速进行乳腺纤维瘤微创旋切术。", "label": [{"entity": "冠状动脉造影检查", "start_idx": 0, "end_idx": 7, "type": "医疗程序"}, {"entity": "乳腺纤维瘤微创旋切术", "start_idx": 14, "end_idx": 23, "type": "医疗程序"}]}, {"text": "立即开展眼底激光治疗，随后进行鼻窦球囊扩张术。", "label": [{"entity": "眼底激光治疗", "start_idx": 4, "end_idx": 9, "type": "医疗程序"}, {"entity": "鼻窦球囊扩张术", "start_idx": 15, "end_idx": 21, "type": "医疗程序"}]}, {"text": "本次金融合同中，涉及的医疗手术范围包括脑血管造影术及髋关节镜手术。", "label": [{"entity": "脑血管造影术", "start_idx": 19, "end_idx": 24, "type": "医疗程序"}, {"entity": "髋关节镜手术", "start_idx": 26, "end_idx": 31, "type": "医疗程序"}]}, {"text": "合同规定，下肢静脉曲张射频消融术与胸腔镜肺叶切除术亦在保障范围内。", "label": [{"entity": "下肢静脉曲张射频消融术", "start_idx": 5, "end_idx": 15, "type": "医疗程序"}, {"entity": "胸腔镜肺叶切除术", "start_idx": 17, "end_idx": 24, "type": "医疗程序"}]}, {"text": "眼底激光治疗以及胸壁肿瘤切除重建术包含在敏感粒度范围内。", "label": [{"entity": "眼底激光治疗", "start_idx": 0, "end_idx": 5, "type": "医疗程序"}, {"entity": "胸壁肿瘤切除重建术", "start_idx": 8, "end_idx": 16, "type": "医疗程序"}]}, {"text": "神经调控程序及胸壁肿瘤切除重建术被列入金融合同保障手术之列。", "label": [{"entity": "神经调控程序", "start_idx": 0, "end_idx": 5, "type": "医疗程序"}, {"entity": "胸壁肿瘤切除重建术", "start_idx": 7, "end_idx": 15, "type": "医疗程序"}]}, {"text": "髋关节镜手术与前列腺增生激光切除术均在合同约定的保障范围之内。", "label": [{"entity": "髋关节镜手术", "start_idx": 0, "end_idx": 5, "type": "医疗程序"}, {"entity": "前列腺增生激光切除术", "start_idx": 7, "end_idx": 16, "type": "医疗程序"}]}, {"text": "本法律文档涉及胃肠道内镜下黏膜切除术与胸壁肿瘤切除重建术的相关规定。", "label": [{"entity": "胃肠道内镜下黏膜切除术", "start_idx": 7, "end_idx": 17, "type": "医疗程序"}, {"entity": "胸壁肿瘤切除重建术", "start_idx": 19, "end_idx": 27, "type": "医疗程序"}]}, {"text": "皮肤黑色素瘤切除及淋巴结清扫术、髋关节镜手术在文中均有详细论述。", "label": [{"entity": "皮肤黑色素瘤切除及淋巴结清扫术", "start_idx": 0, "end_idx": 14, "type": "医疗程序"}, {"entity": "髋关节镜手术", "start_idx": 16, "end_idx": 21, "type": "医疗程序"}]}, {"text": "文档中明确了肾动脉支架植入术与鼻窦球囊扩张术的操作规范。", "label": [{"entity": "肾动脉支架植入术", "start_idx": 6, "end_idx": 13, "type": "医疗程序"}, {"entity": "鼻窦球囊扩张术", "start_idx": 15, "end_idx": 21, "type": "医疗程序"}]}, {"text": "肝内外胆管取石术及肺功能通气功能检查的要求在本法律文档中得到体现。", "label": [{"entity": "肝内外胆管取石术", "start_idx": 0, "end_idx": 7, "type": "医疗程序"}, {"entity": "肺功能通气功能检查", "start_idx": 9, "end_idx": 17, "type": "医疗程序"}]}, {"text": "脑血管造影术与康复训练方案的相关规定也在本文中有所阐述。", "label": [{"entity": "脑血管造影术", "start_idx": 0, "end_idx": 5, "type": "医疗程序"}, {"entity": "康复训练方案", "start_idx": 7, "end_idx": 12, "type": "医疗程序"}]}, {"text": "请立即执行以下金融合同规定手术：肠镜下结肠息肉切除术和肺功能通气功能检查。", "label": [{"entity": "肠镜下结肠息肉切除术", "start_idx": 16, "end_idx": 25, "type": "医疗程序"}, {"entity": "肺功能通气功能检查", "start_idx": 27, "end_idx": 35, "type": "医疗程序"}]}, {"text": "针对肺泡蛋白沉积症，务必执行支气管镜下灌洗术，并配合硬膜下血肿钻孔引流术。", "label": [{"entity": "支气管镜下灌洗术", "start_idx": 14, "end_idx": 21, "type": "医疗程序"}, {"entity": "硬膜下血肿钻孔引流术", "start_idx": 26, "end_idx": 35, "type": "医疗程序"}]}, {"text": "根据合同规定，实施前列腺增生激光切除术及肾动脉支架植入术，不得延误。", "label": [{"entity": "前列腺增生激光切除术", "start_idx": 9, "end_idx": 18, "type": "医疗程序"}, {"entity": "肾动脉支架植入术", "start_idx": 20, "end_idx": 27, "type": "医疗程序"}]}, {"text": "啊，昨天吃了贝类海鲜，身上起了荨麻疹，医生说我还有抗生素的胃肠道反应，真是倒霉。", "label": [{"entity": "贝类海鲜", "start_idx": 6, "end_idx": 9, "type": "过敏信息"}, {"entity": "荨麻疹", "start_idx": 15, "end_idx": 17, "type": "过敏信息"}, {"entity": "抗生素的胃肠道反应", "start_idx": 25, "end_idx": 33, "type": "过敏信息"}]}, {"text": "天呢，家里用了杀虫剂，我呼吸道就不舒服，看来我是对化学物质太敏感了。", "label": [{"entity": "对化学物质太敏感了", "start_idx": 24, "end_idx": 32, "type": "过敏信息"}]}, {"text": "一到春天，看到松树花粉我就哮喘，过敏体质真是麻烦。", "label": [{"entity": "花粉", "start_idx": 9, "end_idx": 10, "type": "过敏信息"}]}, {"text": "我吃东西对色素添加剂好敏感，还有家里的桦木家具，甲醛味儿让我不舒服。", "label": [{"entity": "色素添加剂", "start_idx": 5, "end_idx": 9, "type": "过敏信息"}, {"entity": "甲醛味儿", "start_idx": 24, "end_idx": 27, "type": "过敏信息"}]}, {"text": "一闻到那个特定香精我就头疼，而且我还对乳制品不耐受，生活质量严重下降啊。", "label": [{"entity": "那个特定香精", "start_idx": 3, "end_idx": 8, "type": "过敏信息"}, {"entity": "对乳制品不耐受", "start_idx": 18, "end_idx": 24, "type": "过敏信息"}]}, {"text": "请立即审查合同中关于对羊毛纤维的皮肤刺激及尘螨排泄物反应的条款。", "label": [{"entity": "羊毛纤维的皮肤刺激", "start_idx": 11, "end_idx": 19, "type": "过敏信息"}, {"entity": "尘螨排泄物反应", "start_idx": 21, "end_idx": 27, "type": "过敏信息"}]}, {"text": "针对特定的防腐剂及香精引起的头痛反应，必须明确合同中的免责声明。", "label": [{"entity": "特定的防腐剂", "start_idx": 2, "end_idx": 7, "type": "过敏信息"}, {"entity": "香精", "start_idx": 9, "end_idx": 10, "type": "过敏信息"}]}, {"text": "在金融合同中，务必详细列出对某些杀虫剂引起的呼吸道反应及橡胶制品接触性皮炎的相关责任。", "label": [{"entity": "对某些杀虫剂引起的呼吸道反应", "start_idx": 13, "end_idx": 26, "type": "过敏信息"}, {"entity": "橡胶制品接触性皮炎", "start_idx": 28, "end_idx": 36, "type": "过敏信息"}]}, {"text": "请确保合同包含对牛奶中乳糖不耐受及冬季干燥空气引起皮肤过敏的相关保护措施。", "label": [{"entity": "对牛奶中乳糖不耐受", "start_idx": 7, "end_idx": 15, "type": "过敏信息"}, {"entity": "皮肤过敏", "start_idx": 25, "end_idx": 28, "type": "过敏信息"}]}, {"text": "合同内需明确对松树花粉导致哮喘及热带水果过敏反应的具体处理办法。", "label": [{"entity": "松树花粉导致哮喘", "start_idx": 7, "end_idx": 14, "type": "过敏信息"}, {"entity": "热带水果过敏反应", "start_idx": 16, "end_idx": 23, "type": "过敏信息"}]}, {"text": "某些杀虫剂引起的呼吸道不适及狗毛导致的眼睛发炎，近期病例有所上升。", "label": [{"entity": "杀虫剂引起的呼吸道不适", "start_idx": 2, "end_idx": 12, "type": "过敏信息"}, {"entity": "狗毛导致的眼睛发炎", "start_idx": 14, "end_idx": 22, "type": "过敏信息"}]}, {"text": "春季霉菌增长引发过敏问题，部分市民对特定香精出现头痛反应。", "label": [{"entity": "春季霉菌", "start_idx": 0, "end_idx": 3, "type": "过敏信息"}, {"entity": "特定香精", "start_idx": 18, "end_idx": 21, "type": "过敏信息"}]}, {"text": "鸡蛋中的卵白蛋白及特定防腐剂成为常见食物过敏源。", "label": [{"entity": "鸡蛋中的卵白蛋白", "start_idx": 0, "end_idx": 7, "type": "过敏信息"}, {"entity": "特定防腐剂", "start_idx": 9, "end_idx": 13, "type": "过敏信息"}]}, {"text": "居民对桦木家具释放的甲醛及贝类海鲜引发的荨麻疹反应日益关注。", "label": [{"entity": "甲醛", "start_idx": 10, "end_idx": 11, "type": "过敏信息"}, {"entity": "贝类海鲜", "start_idx": 13, "end_idx": 16, "type": "过敏信息"}, {"entity": "荨麻疹", "start_idx": 20, "end_idx": 22, "type": "过敏信息"}]}, {"text": "桦树花粉过敏及部分防晒产品引起的皮肤不良反应，春季需特别注意。", "label": [{"entity": "桦树花粉过敏", "start_idx": 0, "end_idx": 5, "type": "过敏信息"}, {"entity": "皮肤不良反应", "start_idx": 16, "end_idx": 21, "type": "过敏信息"}]}, {"text": "病例1：患者对鸡蛋中的卵白蛋白及乳胶制品表现出明显过敏症状。", "label": [{"entity": "鸡蛋中的卵白蛋白", "start_idx": 7, "end_idx": 14, "type": "过敏信息"}, {"entity": "乳胶制品", "start_idx": 16, "end_idx": 19, "type": "过敏信息"}]}, {"text": "病例2：该患者因鸡蛋白引起的消化不良及对霉菌孢子存在敏感性。", "label": [{"entity": "鸡蛋白", "start_idx": 8, "end_idx": 10, "type": "过敏信息"}, {"entity": "对霉菌孢子存在敏感性", "start_idx": 19, "end_idx": 28, "type": "过敏信息"}]}, {"text": "病例3：病患在夏季对蚊子叮咬出现过敏反应，同时对其他昆虫叮咬也过敏。", "label": [{"entity": "对蚊子叮咬出现过敏反应", "start_idx": 9, "end_idx": 19, "type": "过敏信息"}, {"entity": "对其他昆虫叮咬也过敏", "start_idx": 23, "end_idx": 32, "type": "过敏信息"}]}, {"text": "病例4：患者接触狗毛后出现眼睛发炎症状，并对乳制品表现出不耐受。", "label": [{"entity": "接触狗毛后出现眼睛发炎症状", "start_idx": 6, "end_idx": 18, "type": "过敏信息"}, {"entity": "对乳制品表现出不耐受", "start_idx": 21, "end_idx": 30, "type": "过敏信息"}]}, {"text": "病例5：该病例对桦木家具释放的甲醛及宠物毛发出现不适反应。", "label": [{"entity": "对桦木家具释放的甲醛", "start_idx": 7, "end_idx": 16, "type": "过敏信息"}, {"entity": "宠物毛发", "start_idx": 18, "end_idx": 21, "type": "过敏信息"}]}, {"text": "顾客1：我喝牛奶就肚子疼，一碰芒果皮就痒得不行。", "label": [{"entity": "喝牛奶就肚子疼", "start_idx": 5, "end_idx": 11, "type": "过敏信息"}, {"entity": "一碰芒果皮就痒得不行", "start_idx": 13, "end_idx": 22, "type": "过敏信息"}]}, {"text": "顾客2：猫狗的毛让我打喷嚏，一到花粉季节我就鼻塞得厉害。", "label": [{"entity": "猫狗的毛", "start_idx": 4, "end_idx": 7, "type": "过敏信息"}, {"entity": "花粉季节我就鼻塞得厉害", "start_idx": 16, "end_idx": 26, "type": "过敏信息"}]}, {"text": "顾客4：一到春天，松树花粉让我喘不过气，柳絮飘起来我就浑身痒。", "label": [{"entity": "松树花粉", "start_idx": 9, "end_idx": 12, "type": "过敏信息"}, {"entity": "柳絮", "start_idx": 20, "end_idx": 21, "type": "过敏信息"}]}, {"text": "顾客5：秋天枯草热让我头昏脑胀，芒果碰到皮肤就感觉刺痛。", "label": [{"entity": "秋天枯草热", "start_idx": 4, "end_idx": 8, "type": "过敏信息"}, {"entity": "芒果", "start_idx": 16, "end_idx": 17, "type": "过敏信息"}]}, {"text": "针对尘螨排泄物反应和狗毛引起的眼睛发炎问题，请市民采取必要防护措施。", "label": [{"entity": "尘螨排泄物反应", "start_idx": 2, "end_idx": 8, "type": "过敏信息"}, {"entity": "狗毛", "start_idx": 10, "end_idx": 11, "type": "过敏信息"}]}, {"text": "对桦木家具释放的甲醛敏感者，及金属镍过敏者，建议进行专业检测。", "label": [{"entity": "对桦木家具释放的甲醛敏感者", "start_idx": 0, "end_idx": 12, "type": "过敏信息"}, {"entity": "金属镍过敏者", "start_idx": 15, "end_idx": 20, "type": "过敏信息"}]}, {"text": "猫毛导致的呼吸道不适及花粉季节鼻塞患者，应及时就医。", "label": [{"entity": "猫毛导致的呼吸道不适", "start_idx": 0, "end_idx": 9, "type": "过敏信息"}, {"entity": "花粉季节鼻塞", "start_idx": 11, "end_idx": 16, "type": "过敏信息"}]}, {"text": "羊肉过敏引发的皮疹患者，注意饮食；游泳池氯化物造成的皮肤干燥亦需防范。", "label": [{"entity": "羊肉过敏", "start_idx": 0, "end_idx": 3, "type": "过敏信息"}, {"entity": "游泳池氯化物造成的皮肤干燥", "start_idx": 17, "end_idx": 29, "type": "过敏信息"}]}, {"text": "特定植物汁液引起的接触性皮炎及热带水果过敏者，请谨慎接触。", "label": [{"entity": "特定植物汁液引起的接触性皮炎", "start_idx": 0, "end_idx": 13, "type": "过敏信息"}, {"entity": "热带水果过敏", "start_idx": 15, "end_idx": 20, "type": "过敏信息"}]}, {"text": "本案涉及原告对鸡蛋中的卵白蛋白过敏及对芒果皮肤接触不适的过敏反应。", "label": [{"entity": "对鸡蛋中的卵白蛋白过敏", "start_idx": 6, "end_idx": 16, "type": "过敏信息"}, {"entity": "对芒果皮肤接触不适的过敏反应", "start_idx": 18, "end_idx": 31, "type": "过敏信息"}]}, {"text": "报告显示，患者出现对橡胶制品接触性皮炎及特定香精引发的头痛症状。", "label": [{"entity": "对橡胶制品接触性皮炎", "start_idx": 9, "end_idx": 18, "type": "过敏信息"}, {"entity": "特定香精引发的头痛症状", "start_idx": 20, "end_idx": 30, "type": "过敏信息"}]}, {"text": "在本次法律诉讼中，被告对蜜蜂毒液表现出敏感性，并伴有秋季枯草热的症状。", "label": [{"entity": "对蜜蜂毒液表现出敏感性", "start_idx": 11, "end_idx": 21, "type": "过敏信息"}, {"entity": "伴有秋季枯草热的症状", "start_idx": 24, "end_idx": 33, "type": "过敏信息"}]}, {"text": "经诊断，原告对某些食物存在排斥反应，同时对松树花粉引起的哮喘有所记录。", "label": [{"entity": "对某些食物存在排斥反应", "start_idx": 6, "end_idx": 16, "type": "过敏信息"}, {"entity": "对松树花粉引起的哮喘", "start_idx": 20, "end_idx": 29, "type": "过敏信息"}]}, {"text": "病例资料表明，患者对鸡蛋中的卵白蛋白过敏，并伴有冷空气过敏引发的咳嗽。", "label": [{"entity": "对鸡蛋中的卵白蛋白过敏", "start_idx": 9, "end_idx": 19, "type": "过敏信息"}, {"entity": "伴有冷空气过敏", "start_idx": 22, "end_idx": 28, "type": "过敏信息"}]}, {"text": "在本次在线客服聊天记录中，我们发现客户反映了对特定抗生素的胃肠道反应，同时对夏天的蚊子叮咬也存在过敏现象。", "label": [{"entity": "对特定抗生素的胃肠道反应", "start_idx": 22, "end_idx": 33, "type": "过敏信息"}, {"entity": "对夏天的蚊子叮咬也存在过敏现象", "start_idx": 37, "end_idx": 51, "type": "过敏信息"}]}, {"text": "另一位客户表示，他对鸡蛋清过敏，同时对特定食物有排斥反应。", "label": [{"entity": "对鸡蛋清过敏", "start_idx": 9, "end_idx": 14, "type": "过敏信息"}, {"entity": "对特定食物有排斥反应", "start_idx": 18, "end_idx": 27, "type": "过敏信息"}]}, {"text": "在另一份咨询记录中，客人提到对猫毛引起的呼吸道不适，以及对食物中谷蛋白的敏感性。", "label": [{"entity": "对猫毛引起的呼吸道不适", "start_idx": 14, "end_idx": 24, "type": "过敏信息"}, {"entity": "对食物中谷蛋白的敏感性", "start_idx": 28, "end_idx": 38, "type": "过敏信息"}]}, {"text": "有客户特别提到对特定防腐剂有不良反应，并且对春季霉菌生长导致的过敏症状表示关注。", "label": [{"entity": "对特定防腐剂有不良反应", "start_idx": 7, "end_idx": 17, "type": "过敏信息"}, {"entity": "春季霉菌生长导致的过敏症状", "start_idx": 22, "end_idx": 34, "type": "过敏信息"}]}, {"text": "还有用户反映，春季霉菌生长导致的过敏以及梧桐树花粉引起的眼睛痒等问题，给他们带来了不小的困扰。", "label": [{"entity": "春季霉菌生长导致的过敏", "start_idx": 7, "end_idx": 17, "type": "过敏信息"}, {"entity": "梧桐树花粉引起的眼睛痒", "start_idx": 20, "end_idx": 30, "type": "过敏信息"}]}, {"text": "请确保在合同附件中明确指出对松树花粉引起的哮喘和宠物毛发不适的具体防范措施。", "label": [{"entity": "对松树花粉引起的哮喘", "start_idx": 13, "end_idx": 22, "type": "过敏信息"}, {"entity": "宠物毛发不适", "start_idx": 24, "end_idx": 29, "type": "过敏信息"}]}, {"text": "在合同特殊条款部分，务必详细列出对某些药物反应及猫毛引起的呼吸道不适的注意事项。", "label": [{"entity": "对某些药物反应", "start_idx": 16, "end_idx": 22, "type": "过敏信息"}, {"entity": "猫毛引起的呼吸道不适", "start_idx": 24, "end_idx": 33, "type": "过敏信息"}]}, {"text": "关于敏感体质的备注，合同应具体说明对橡胶制品接触性皮炎和蜜蜂毒液敏感的相关处理办法。", "label": [{"entity": "对橡胶制品接触性皮炎", "start_idx": 17, "end_idx": 26, "type": "过敏信息"}, {"entity": "蜜蜂毒液敏感", "start_idx": 28, "end_idx": 33, "type": "过敏信息"}]}, {"text": "在合同免责声明中，要准确写入对特定抗生素的胃肠道反应和尘螨排泄物反应的警示信息。", "label": [{"entity": "特定抗生素的胃肠道反应", "start_idx": 15, "end_idx": 25, "type": "过敏信息"}, {"entity": "尘螨排泄物反应", "start_idx": 27, "end_idx": 33, "type": "过敏信息"}]}, {"text": "请在合同风险提示一节中，明确指出对蜜蜂毒液敏感和特定防腐剂反应的潜在风险。", "label": [{"entity": "对蜜蜂毒液敏感", "start_idx": 16, "end_idx": 22, "type": "过敏信息"}, {"entity": "特定防腐剂反应", "start_idx": 24, "end_idx": 30, "type": "过敏信息"}]}, {"text": "听说你眼睛对狗毛敏感，我有个朋友也是，还对鸡蛋里的卵白蛋白过敏呢。", "label": [{"entity": "对狗毛敏感", "start_idx": 5, "end_idx": 9, "type": "过敏信息"}, {"entity": "对鸡蛋里的卵白蛋白过敏", "start_idx": 20, "end_idx": 30, "type": "过敏信息"}]}, {"text": "你和小麦麸质处不来？我懂，我有个同事对狗毛也是一样，一接触到就眼睛发炎。", "label": [{"entity": "小麦麸质", "start_idx": 2, "end_idx": 5, "type": "过敏信息"}, {"entity": "狗毛", "start_idx": 19, "end_idx": 20, "type": "过敏信息"}]}, {"text": "春季柳絮飘得让人痒痒的，我有个妹妹皮肤对这过敏，还有那些食物里的色素添加剂她也敏感。", "label": [{"entity": "皮肤对这过敏", "start_idx": 17, "end_idx": 22, "type": "过敏信息"}, {"entity": "食物里的色素添加剂", "start_idx": 28, "end_idx": 36, "type": "过敏信息"}]}, {"text": "你房间是不是春季容易长霉？我有个朋友对那霉味过敏，连金属镍也碰不得。", "label": [{"entity": "对那霉味过敏", "start_idx": 18, "end_idx": 23, "type": "过敏信息"}, {"entity": "连金属镍也碰不得", "start_idx": 25, "end_idx": 32, "type": "过敏信息"}]}, {"text": "你用防晒要注意哦，我一个客户就是对某些防晒产品有不良反应，还有宠物毛发，一接触就不舒服。", "label": [{"entity": "对某些防晒产品有不良反应", "start_idx": 16, "end_idx": 27, "type": "过敏信息"}, {"entity": "还有宠物毛发", "start_idx": 29, "end_idx": 34, "type": "过敏信息"}]}, {"text": "请立即审查账户中对秋天的枯草热及金属镍过敏相关的交易记录。", "label": [{"entity": "对秋天的枯草热", "start_idx": 8, "end_idx": 14, "type": "过敏信息"}, {"entity": "金属镍过敏", "start_idx": 16, "end_idx": 20, "type": "过敏信息"}]}, {"text": "育儿实况显示，妊娠次数已成为影响家庭规划的重要因素。", "label": [{"entity": "妊娠次数", "start_idx": 7, "end_idx": 10, "type": "生育信息"}]}, {"text": "综合生育状况概述与育儿记录，我国新生代父母育儿观念发生转变。", "label": [{"entity": "综合生育状况概述", "start_idx": 0, "end_idx": 7, "type": "生育信息"}, {"entity": "育儿记录", "start_idx": 9, "end_idx": 12, "type": "生育信息"}, {"entity": "新生代父母", "start_idx": 16, "end_idx": 20, "type": "生育信息"}]}, {"text": "针对生育相关说明，调查数据显示已成家有女的家庭在生育决策上更为谨慎。", "label": [{"entity": "已成家有女", "start_idx": 15, "end_idx": 19, "type": "生育信息"}]}, {"text": "通过分析生育历史数据，发现已诞双子的家庭在育儿资源分配上存在一定压力。", "label": [{"entity": "已诞双子", "start_idx": 13, "end_idx": 16, "type": "生育信息"}]}, {"text": "再次妊娠的生育状况概述提示，多次妊娠家庭需重视孕期健康管理。", "label": [{"entity": "再次妊娠", "start_idx": 0, "end_idx": 3, "type": "生育信息"}, {"entity": "多次妊娠", "start_idx": 14, "end_idx": 17, "type": "生育信息"}]}, {"text": "我家里现在二胎在孕，真是充满期待啊。", "label": [{"entity": "二胎在孕", "start_idx": 5, "end_idx": 8, "type": "生育信息"}]}, {"text": "我计划在生育发展上多花点时间，毕竟和孩子相关的事情不能马虎。", "label": [{"entity": "生育发展", "start_idx": 4, "end_idx": 7, "type": "生育信息"}]}, {"text": "这次怀孕，我们都希望能知道孩次和性别，期待新生命的到来。", "label": [{"entity": "怀孕", "start_idx": 2, "end_idx": 3, "type": "生育信息"}, {"entity": "孩次", "start_idx": 13, "end_idx": 14, "type": "生育信息"}, {"entity": "性别", "start_idx": 16, "end_idx": 17, "type": "生育信息"}]}, {"text": "我已成家有女，回想孕产历程，真是感慨万千。", "label": [{"entity": "已成家有女", "start_idx": 1, "end_idx": 5, "type": "生育信息"}]}, {"text": "生育实况中务必详细记录患者首次怀孕的相关情况。", "label": [{"entity": "生育实况", "start_idx": 0, "end_idx": 3, "type": "生育信息"}, {"entity": "患者首次怀孕", "start_idx": 11, "end_idx": 16, "type": "生育信息"}]}, {"text": "已诞双子的有子一族需进行定期产后复查。", "label": [{"entity": "已诞双子", "start_idx": 0, "end_idx": 3, "type": "生育信息"}, {"entity": "有子一族", "start_idx": 5, "end_idx": 8, "type": "生育信息"}]}, {"text": "请针对再次妊娠的患者，简述其生育生涯的关键信息。", "label": [{"entity": "再次妊娠", "start_idx": 3, "end_idx": 6, "type": "生育信息"}]}, {"text": "生育动态概述应重点关注有子一族的家庭状况。", "label": [{"entity": "有子一族", "start_idx": 11, "end_idx": 14, "type": "生育信息"}]}, {"text": "立即启动育儿计划，跟踪并记录患者的孕产历程。", "label": [{"entity": "育儿计划", "start_idx": 4, "end_idx": 7, "type": "生育信息"}, {"entity": "孕产历程", "start_idx": 17, "end_idx": 20, "type": "生育信息"}]}, {"text": "已诞双子，该家庭的孩次状况符合相关法律要求。", "label": [{"entity": "已诞双子", "start_idx": 0, "end_idx": 3, "type": "生育信息"}]}, {"text": "孕育记录简述显示，生育动态概述情况稳定。", "label": [{"entity": "孕育记录", "start_idx": 0, "end_idx": 3, "type": "生育信息"}, {"entity": "生育动态", "start_idx": 9, "end_idx": 12, "type": "生育信息"}]}, {"text": "根据孕史一览，首次怀孕记录表明一切正常。", "label": [{"entity": "首次怀孕", "start_idx": 7, "end_idx": 10, "type": "生育信息"}]}, {"text": "该无子女家庭在法律文档中未见育儿记录。", "label": [{"entity": "无子女", "start_idx": 1, "end_idx": 3, "type": "生育信息"}, {"entity": "育儿记录", "start_idx": 14, "end_idx": 17, "type": "生育信息"}]}, {"text": "本次审查的家庭生育次数与妊娠次数均已在法律框架内得到记录。", "label": [{"entity": "家庭生育次数", "start_idx": 5, "end_idx": 10, "type": "生育信息"}, {"entity": "妊娠次数", "start_idx": 12, "end_idx": 15, "type": "生育信息"}]}, {"text": "国家卫生健康委员会指出，家庭生育发展政策下，二胎在孕家庭数量呈上升趋势。", "label": [{"entity": "二胎", "start_idx": 22, "end_idx": 23, "type": "生育信息"}, {"entity": "在孕家庭", "start_idx": 24, "end_idx": 27, "type": "生育信息"}]}, {"text": "在家庭生育发展的大背景下，无子女家庭的比例逐渐下降，政策效果显著。", "label": [{"entity": "无子女家庭", "start_idx": 13, "end_idx": 17, "type": "生育信息"}]}, {"text": "根据最新生育实况调查，我国生育状况概述显示，生育率有所回升。", "label": [{"entity": "生育率", "start_idx": 22, "end_idx": 24, "type": "生育信息"}]}, {"text": "孕育详情记录显示，近年来我国妇女平均生产次数呈现逐年增长的趋势。", "label": [{"entity": "平均生产次数", "start_idx": 16, "end_idx": 21, "type": "生育信息"}, {"entity": "逐年增长", "start_idx": 24, "end_idx": 27, "type": "生育信息"}]}, {"text": "不少家庭育有一儿一女，成为家庭生育发展的新常态，促进了人口结构的优化。", "label": [{"entity": "一儿一女", "start_idx": 6, "end_idx": 9, "type": "生育信息"}, {"entity": "家庭生育发展", "start_idx": 13, "end_idx": 18, "type": "生育信息"}]}, {"text": "请记录该患者已成家有女的相关育儿记录。", "label": [{"entity": "已成家有女", "start_idx": 6, "end_idx": 10, "type": "生育信息"}]}, {"text": "针对患者已成家有女的情况，请详细记录再次妊娠的相关信息。", "label": [{"entity": "已成家有女", "start_idx": 4, "end_idx": 8, "type": "生育信息"}, {"entity": "再次妊娠", "start_idx": 18, "end_idx": 21, "type": "生育信息"}]}, {"text": "请在有子一族的生育相关说明中，详细阐述生育情况。", "label": [{"entity": "有子一族", "start_idx": 2, "end_idx": 5, "type": "生育信息"}, {"entity": "生育相关", "start_idx": 7, "end_idx": 10, "type": "生育信息"}]}, {"text": "快速检查并更新该家庭的生育次数，完善生殖资讯卡片。", "label": [{"entity": "生育次数", "start_idx": 11, "end_idx": 14, "type": "生育信息"}, {"entity": "生殖资讯卡片", "start_idx": 18, "end_idx": 23, "type": "生育信息"}]}, {"text": "督促患者育儿计划的实施，特别是育有一儿一女的家庭情况记录。", "label": [{"entity": "育儿计划", "start_idx": 4, "end_idx": 7, "type": "生育信息"}, {"entity": "育有一儿一女", "start_idx": 15, "end_idx": 20, "type": "生育信息"}]}, {"text": "孕产详情在孕育记录简述中有详细记录，需仔细审查。", "label": [{"entity": "孕产详情", "start_idx": 0, "end_idx": 3, "type": "生育信息"}, {"entity": "孕育记录简述", "start_idx": 5, "end_idx": 10, "type": "生育信息"}]}, {"text": "生育档案中准确记录了生产次数，为金融合同提供了重要参考。", "label": [{"entity": "生产次数", "start_idx": 10, "end_idx": 13, "type": "生育信息"}]}, {"text": "生育相关说明对有子一族的具体情况进行了阐述。", "label": [{"entity": "有子一族", "start_idx": 7, "end_idx": 10, "type": "生育信息"}]}, {"text": "孕产历程表明，客户已诞双子，该信息已列入合同附件。", "label": [{"entity": "已诞双子", "start_idx": 9, "end_idx": 12, "type": "生育信息"}]}, {"text": "孕育记录简述与生育档案相互印证，确保了信息的准确性。", "label": [{"entity": "孕育记录简述", "start_idx": 0, "end_idx": 5, "type": "生育信息"}, {"entity": "生育档案", "start_idx": 7, "end_idx": 10, "type": "生育信息"}]}, {"text": "在求职简历中，关于生育状况的概述显示了李先生作为三孩父亲的对育儿实况的深入参与。", "label": [{"entity": "三孩父亲", "start_idx": 24, "end_idx": 27, "type": "生育信息"}]}, {"text": "求职者王女士在生育相关说明中详细阐述了其育儿计划的实施策略。", "label": [{"entity": "生育相关说明", "start_idx": 7, "end_idx": 12, "type": "生育信息"}, {"entity": "育儿计划", "start_idx": 20, "end_idx": 23, "type": "生育信息"}]}, {"text": "张先生的简历中，其育儿记录和生育动态概述部分充分体现了他对家庭责任的重视。", "label": [{"entity": "育儿记录", "start_idx": 9, "end_idx": 12, "type": "生育信息"}, {"entity": "生育动态", "start_idx": 14, "end_idx": 17, "type": "生育信息"}]}, {"text": "作为一名三孩父亲，赵先生在简历中提及了配偶的再次妊娠及家庭育儿安排。", "label": [{"entity": "三孩父亲", "start_idx": 4, "end_idx": 7, "type": "生育信息"}, {"entity": "配偶的再次妊娠", "start_idx": 19, "end_idx": 25, "type": "生育信息"}, {"entity": "家庭育儿安排", "start_idx": 27, "end_idx": 32, "type": "生育信息"}]}, {"text": "陈先生的孕史一览在简历中明确指出，他作为三孩父亲在育儿方面的丰富经验和家庭规划。", "label": [{"entity": "孕史一览", "start_idx": 4, "end_idx": 7, "type": "生育信息"}, {"entity": "三孩父亲", "start_idx": 20, "end_idx": 23, "type": "生育信息"}]}, {"text": "我记得你合同里提过妊娠历史，现在二胎在孕要紧注意身体哦。", "label": [{"entity": "妊娠历史", "start_idx": 9, "end_idx": 12, "type": "生育信息"}, {"entity": "二胎在孕", "start_idx": 16, "end_idx": 19, "type": "生育信息"}]}, {"text": "合同里生育相关说明写的清楚，你们家是无子女家庭吧？", "label": [{"entity": "无子女家庭", "start_idx": 18, "end_idx": 22, "type": "生育信息"}]}, {"text": "看到你生育生涯简述，已经诞下双子，真是恭喜啦！", "label": [{"entity": "诞下双子", "start_idx": 12, "end_idx": 15, "type": "生育信息"}]}, {"text": "这份孕育新生命的详情里，提到了孩次状况，你这是第一胎还是第二胎呀？", "label": [{"entity": "第一胎", "start_idx": 23, "end_idx": 25, "type": "生育信息"}, {"entity": "第二胎", "start_idx": 28, "end_idx": 30, "type": "生育信息"}]}, {"text": "家里孩子的情况在家庭生育发展这部分都有体现，考虑再生一个不？", "label": [{"entity": "孩子的情况", "start_idx": 2, "end_idx": 6, "type": "生育信息"}, {"entity": "家庭生育发展", "start_idx": 8, "end_idx": 13, "type": "生育信息"}, {"entity": "再生一个", "start_idx": 24, "end_idx": 27, "type": "生育信息"}]}, {"text": "生育相关说明明确，为市民提供了孕育新生命的详尽指南。", "label": [{"entity": "生育", "start_idx": 0, "end_idx": 1, "type": "生育信息"}]}, {"text": "该家庭已育有两女，孩次状况显示家庭结构稳定。", "label": [{"entity": "已育有两女", "start_idx": 3, "end_idx": 7, "type": "生育信息"}, {"entity": "孩次状况", "start_idx": 9, "end_idx": 12, "type": "生育信息"}]}, {"text": "育儿计划实施以来，众多家庭经历了不平凡的孕产历程。", "label": [{"entity": "育儿计划", "start_idx": 0, "end_idx": 3, "type": "生育信息"}, {"entity": "孕产历程", "start_idx": 20, "end_idx": 23, "type": "生育信息"}]}, {"text": "我家里有三个孩子，都是我一手带大的。", "label": [{"entity": "三个孩子", "start_idx": 4, "end_idx": 7, "type": "生育信息"}]}, {"text": "我是有孩子的人了，大女儿，二儿子，家里排行老三。", "label": [{"entity": "有孩子的人了", "start_idx": 2, "end_idx": 7, "type": "生育信息"}, {"entity": "大女儿", "start_idx": 9, "end_idx": 11, "type": "生育信息"}, {"entity": "二儿子", "start_idx": 13, "end_idx": 15, "type": "生育信息"}, {"entity": "家里排行老三", "start_idx": 17, "end_idx": 22, "type": "生育信息"}]}, {"text": "我可是三个孩子的爸爸了，从第一个孩子出生到现在，感觉自己的一生都很精彩。", "label": [{"entity": "三个孩子的爸爸", "start_idx": 3, "end_idx": 9, "type": "生育信息"}, {"entity": "第一个孩子", "start_idx": 13, "end_idx": 17, "type": "生育信息"}]}, {"text": "澳大利亚悉尼歌剧院周边与意大利罗马斗兽场附近，近期均发现新型医疗病例。", "label": [{"entity": "澳大利亚悉尼歌剧院周边", "start_idx": 0, "end_idx": 10, "type": "地理位置"}, {"entity": "意大利罗马斗兽场附近", "start_idx": 12, "end_idx": 21, "type": "地理位置"}]}, {"text": "印度孟买印度门周边及非洲草原秘境，已展开病例范围值的相关调查。", "label": [{"entity": "印度孟买印度门周边", "start_idx": 0, "end_idx": 8, "type": "地理位置"}, {"entity": "非洲草原秘境", "start_idx": 10, "end_idx": 15, "type": "地理位置"}]}, {"text": "沙特阿拉伯麦加圣地及马来西亚吉隆坡双子塔附近，医疗团队正进行病例的深入研究。", "label": [{"entity": "沙特阿拉伯麦加圣地", "start_idx": 0, "end_idx": 8, "type": "地理位置"}, {"entity": "马来西亚吉隆坡双子塔", "start_idx": 10, "end_idx": 19, "type": "地理位置"}]}, {"text": "印度尼西亚巴厘岛乌布区与马来西亚吉隆坡双子塔附近，病例报告显示存在一定范围值的感染。", "label": [{"entity": "印度尼西亚巴厘岛乌布区", "start_idx": 0, "end_idx": 10, "type": "地理位置"}, {"entity": "马来西亚吉隆坡双子塔", "start_idx": 12, "end_idx": 21, "type": "地理位置"}]}, {"text": "非洲草原秘境及菲律宾长滩岛白沙滩，两地卫生部门正密切关注病例动态，确保及时应对。", "label": [{"entity": "非洲草原秘境", "start_idx": 0, "end_idx": 5, "type": "地理位置"}, {"entity": "菲律宾长滩岛白沙滩", "start_idx": 7, "end_idx": 15, "type": "地理位置"}]}, {"text": "下周三务必启程，从上海前往厦门，记录下如去年10月成都至北京的红色记忆之旅般宝贵的经历。", "label": [{"entity": "下周三", "start_idx": 0, "end_idx": 2, "type": "行程信息"}, {"entity": "从上海前往厦门", "start_idx": 8, "end_idx": 14, "type": "行程信息"}, {"entity": "去年10月成都至北京", "start_idx": 20, "end_idx": 29, "type": "行程信息"}]}, {"text": "请确保年8月安排好厦门至上海迪士尼的亲子游，以及国庆节期间北京至四川九寨沟的旅行。", "label": [{"entity": "年8月安排好厦门至上海迪士尼的亲子游", "start_idx": 3, "end_idx": 20, "type": "行程信息"}, {"entity": "国庆节期间北京至四川九寨沟的旅行", "start_idx": 24, "end_idx": 39, "type": "行程信息"}]}, {"text": "月植物繁茂，须赴广州至昆明进行植物考察，明年儿童节计划好北京到哈尔滨的亲子游活动。", "label": [{"entity": "广州至昆明进行植物考察", "start_idx": 8, "end_idx": 18, "type": "行程信息"}, {"entity": "明年儿童节计划好北京到哈尔滨的亲子游活动", "start_idx": 20, "end_idx": 39, "type": "行程信息"}]}, {"text": "年9月，安排成都至西安的高铁旅行，明年5月则展开从上海到成都的寻根之旅。", "label": [{"entity": "年9月，安排成都至西安的高铁旅行", "start_idx": 0, "end_idx": 15, "type": "行程信息"}, {"entity": "明年5月则展开从上海到成都的寻根之旅", "start_idx": 17, "end_idx": 34, "type": "行程信息"}]}, {"text": "为明年7月的广州至青海湖环湖之旅做好准备，年终11月，务必安排深圳至北京的故宫之旅。", "label": [{"entity": "明年7月的广州至青海湖环湖之旅", "start_idx": 1, "end_idx": 15, "type": "行程信息"}, {"entity": "年终11月", "start_idx": 21, "end_idx": 25, "type": "行程信息"}, {"entity": "深圳至北京的故宫之旅", "start_idx": 31, "end_idx": 40, "type": "行程信息"}]}, {"text": "记得去年8月带我家小宝贝从厦门去了上海迪士尼，那真是段美好的亲子游。", "label": [{"entity": "去年8月带我家小宝贝从厦门去了上海迪士尼", "start_idx": 2, "end_idx": 21, "type": "行程信息"}]}, {"text": "今年9月打算去桂林欣赏一下那里的山水，10月1日还会和爱人一起从南京飞到厦门度蜜月。", "label": [{"entity": "今年9月打算去桂林欣赏一下那里的山水", "start_idx": 0, "end_idx": 17, "type": "行程信息"}, {"entity": "10月1日还会和爱人一起从南京飞到厦门度蜜月", "start_idx": 19, "end_idx": 40, "type": "行程信息"}]}, {"text": "明年清明节，我和朋友约好了一起从武汉去西安访古，体验不一样的文化之旅。", "label": [{"entity": "明年清明节", "start_idx": 0, "end_idx": 4, "type": "行程信息"}, {"entity": "从武汉去西安", "start_idx": 15, "end_idx": 20, "type": "行程信息"}]}, {"text": "春节的时候我打算在青岛放松一下，不过也有朋友18号从杭州出发去西藏拉萨，真是羡慕呀！", "label": [{"entity": "春节的时候我打算在青岛放松一下", "start_idx": 0, "end_idx": 14, "type": "行程信息"}, {"entity": "18号从杭州出发去西藏拉萨", "start_idx": 22, "end_idx": 34, "type": "行程信息"}]}, {"text": "还有，听说明年春节有同事计划从武汉到广州进行美食之旅，国庆节我也打算去云南丽江玩一圈。", "label": [{"entity": "明年春节", "start_idx": 5, "end_idx": 8, "type": "行程信息"}, {"entity": "从武汉到广州进行美食之旅", "start_idx": 14, "end_idx": 25, "type": "行程信息"}, {"entity": "国庆节", "start_idx": 27, "end_idx": 29, "type": "行程信息"}, {"entity": "去云南丽江玩一圈", "start_idx": 34, "end_idx": 41, "type": "行程信息"}]}, {"text": "对了，还有5月份的时候，有几个广州的朋友准备去西藏纳木错朝圣，听起来就是很神圣的旅程呢！", "label": [{"entity": "5月份", "start_idx": 5, "end_idx": 7, "type": "行程信息"}, {"entity": "去西藏纳木错朝圣", "start_idx": 22, "end_idx": 29, "type": "行程信息"}]}, {"text": "去年5月，广州至西藏纳木错朝圣之旅备受欢迎，同年国庆节北京至西安的历史文化游也吸引了众多旅客。", "label": [{"entity": "去年5月，广州至西藏纳木错朝圣之旅", "start_idx": 0, "end_idx": 16, "type": "行程信息"}, {"entity": "同年国庆节北京至西安的历史文化游", "start_idx": 22, "end_idx": 37, "type": "行程信息"}]}, {"text": "年6月，上海至新疆的丝绸之路之旅渐入佳境，而月15日从杭州出发到三亚的休闲度假同样人气旺盛。", "label": [{"entity": "年6月，上海至新疆的丝绸之路之旅", "start_idx": 0, "end_idx": 15, "type": "行程信息"}, {"entity": "月15日从杭州出发到三亚的休闲度假", "start_idx": 22, "end_idx": 38, "type": "行程信息"}]}, {"text": "去年8月，北京至云南普洱茶之旅成为旅游亮点，同期月15日由成都飞往深圳的航线亦客流量激增。", "label": [{"entity": "去年8月，北京至云南普洱茶之旅", "start_idx": 0, "end_idx": 14, "type": "行程信息"}, {"entity": "同期月15日由成都飞往深圳的航线", "start_idx": 22, "end_idx": 37, "type": "行程信息"}]}, {"text": "月5日，广州至昆明的植物考察之旅深受专业人士喜爱，年10月深圳至成都的美食之旅亦让美食爱好者流连忘返。", "label": [{"entity": "月5日，广州至昆明的植物考察之旅", "start_idx": 0, "end_idx": 15, "type": "行程信息"}, {"entity": "年10月深圳至成都的美食之旅", "start_idx": 25, "end_idx": 38, "type": "行程信息"}]}, {"text": "月15日，由成都飞往深圳的航班乘客爆满，与此同时，月1日南京至厦门的浪漫蜜月行也备受新婚夫妇青睐。", "label": [{"entity": "月15日，由成都飞往深圳的航班", "start_idx": 0, "end_idx": 14, "type": "行程信息"}, {"entity": "月1日南京至厦门的浪漫蜜月行", "start_idx": 25, "end_idx": 38, "type": "行程信息"}]}, {"text": "明年9月，上海至成都的熊猫守护之旅备受期待，与此同时，月8日成都至青岛的啤酒节之旅亦进入筹备阶段。", "label": [{"entity": "明年9月，上海至成都的熊猫守护之旅", "start_idx": 0, "end_idx": 16, "type": "行程信息"}, {"entity": "月8日成都至青岛的啤酒节之旅", "start_idx": 27, "end_idx": 40, "type": "行程信息"}]}, {"text": "年7月，上海至哈尔滨的火车之旅成为旅行者们热议的话题，同年4月，厦门至南京的自驾行路线也广受好评。", "label": [{"entity": "年7月，上海至哈尔滨的火车之旅", "start_idx": 0, "end_idx": 14, "type": "行程信息"}, {"entity": "同年4月，厦门至南京的自驾行路线", "start_idx": 27, "end_idx": 42, "type": "行程信息"}]}, {"text": "年11月，深圳至云南丽江的摄影之旅再次引发关注，而年10月的深圳至成都美食之旅，也让美食爱好者们翘首以盼。", "label": [{"entity": "年11月，深圳至云南丽江的摄影之旅", "start_idx": 0, "end_idx": 16, "type": "行程信息"}, {"entity": "年10月的深圳至成都美食之旅", "start_idx": 25, "end_idx": 38, "type": "行程信息"}]}, {"text": "月20日上海至西安的爱情之旅即将启程，明年清明节期间，杭州至黄山的春游活动也将如约而至。", "label": [{"entity": "月20日上海至西安的爱情之旅", "start_idx": 0, "end_idx": 13, "type": "行程信息"}, {"entity": "明年清明节期间", "start_idx": 19, "end_idx": 25, "type": "行程信息"}, {"entity": "杭州至黄山的春游活动", "start_idx": 27, "end_idx": 36, "type": "行程信息"}]}, {"text": "在年4月厦门至南京自驾行的同时，年11月深圳至云南丽江的摄影之旅亦成为旅行计划中的热门选项。", "label": [{"entity": "年4月厦门至南京自驾行", "start_idx": 1, "end_idx": 11, "type": "行程信息"}, {"entity": "年11月深圳至云南丽江的摄影之旅", "start_idx": 16, "end_idx": 31, "type": "行程信息"}]}, {"text": "年5月，我踏上了从广州至西藏纳木错的朝圣之旅；同年8月，又参与了北京至云南的普洱茶之旅。", "label": [{"entity": "从广州至西藏纳木错的朝圣之旅", "start_idx": 8, "end_idx": 21, "type": "行程信息"}, {"entity": "北京至云南的普洱茶之旅", "start_idx": 32, "end_idx": 42, "type": "行程信息"}]}, {"text": "年6月，我计划了上海至内蒙古草原的行程；并在同年3月，进行了上海至西藏的心灵之旅。", "label": [{"entity": "上海至内蒙古草原的行程", "start_idx": 8, "end_idx": 18, "type": "行程信息"}, {"entity": "上海至西藏的心灵之旅", "start_idx": 30, "end_idx": 39, "type": "行程信息"}]}, {"text": "今年春节假期，我在青岛享受了一段美好时光；2015年11月，还参加了深圳至云南丽江的摄影之旅。", "label": [{"entity": "今年春节假期，我在青岛享受了一段美好时光", "start_idx": 0, "end_idx": 19, "type": "行程信息"}, {"entity": "2015年11月，还参加了深圳至云南丽江的摄影之旅", "start_idx": 21, "end_idx": 45, "type": "行程信息"}]}, {"text": "年10月，我体验了从深圳至成都的美食之旅；明年春节，计划从武汉前往广州继续我的美食之旅。", "label": [{"entity": "年10月，我体验了从深圳至成都的美食之旅", "start_idx": 0, "end_idx": 19, "type": "行程信息"}, {"entity": "明年春节，计划从武汉前往广州继续我的美食之旅", "start_idx": 21, "end_idx": 42, "type": "行程信息"}]}, {"text": "在2015年国庆节期间，我参与了北京至西安的历史文化游；并在同年8月，又一次参加了北京至云南普洱茶之旅。", "label": [{"entity": "2015年国庆节期间，我参与了北京至西安的历史文化游", "start_idx": 1, "end_idx": 26, "type": "行程信息"}, {"entity": "同年8月，又一次参加了北京至云南普洱茶之旅", "start_idx": 30, "end_idx": 50, "type": "行程信息"}]}, {"text": "明年儿童节，速安排北京至哈尔滨亲子游，记得15日从成都出发飞往深圳！", "label": [{"entity": "明年儿童节", "start_idx": 0, "end_idx": 4, "type": "行程信息"}, {"entity": "北京至哈尔滨亲子游", "start_idx": 9, "end_idx": 17, "type": "行程信息"}, {"entity": "15日从成都出发飞往深圳", "start_idx": 21, "end_idx": 32, "type": "行程信息"}]}, {"text": "命令注意：8月务必组织北京至云南普洱茶之旅，4月则安排厦门至南京的自驾行！", "label": [{"entity": "8月务必组织北京至云南普洱茶之旅", "start_idx": 5, "end_idx": 20, "type": "行程信息"}, {"entity": "4月则安排厦门至南京的自驾行", "start_idx": 22, "end_idx": 35, "type": "行程信息"}]}, {"text": "通知：春节将至，准备明年武汉至广州的美食之旅，下月初前往杭州考察项目！", "label": [{"entity": "春节将至，准备明年武汉至广州的美食之旅", "start_idx": 3, "end_idx": 21, "type": "行程信息"}, {"entity": "下月初前往杭州考察项目", "start_idx": 23, "end_idx": 33, "type": "行程信息"}]}, {"text": "元旦佳节，不得错过北京至哈尔滨的冰雪狂欢，11月则需策划深圳至北京的故宫之旅！", "label": [{"entity": "北京至哈尔滨", "start_idx": 9, "end_idx": 14, "type": "行程信息"}, {"entity": "深圳至北京", "start_idx": 28, "end_idx": 32, "type": "行程信息"}]}, {"text": "国庆节假期，提前规划北京至四川九寨沟的旅行，4月继续厦门至南京的自驾之旅！", "label": [{"entity": "国庆节假期", "start_idx": 0, "end_idx": 4, "type": "行程信息"}, {"entity": "北京至四川九寨沟的旅行", "start_idx": 10, "end_idx": 20, "type": "行程信息"}, {"entity": "4月继续厦门至南京的自驾之旅", "start_idx": 22, "end_idx": 35, "type": "行程信息"}]}, {"text": "今年7月，患者曾进行上海至哈尔滨的火车之旅，计划明年儿童节组织北京到哈尔滨的亲子游活动。", "label": [{"entity": "今年7月，患者曾进行上海至哈尔滨的火车之旅", "start_idx": 0, "end_idx": 20, "type": "行程信息"}, {"entity": "计划明年儿童节组织北京到哈尔滨的亲子游活动", "start_idx": 22, "end_idx": 42, "type": "行程信息"}]}, {"text": "去年4月，患者完成了厦门至南京的自驾行，并于本月1日开启了南京到厦门的浪漫蜜月行。", "label": [{"entity": "厦门至南京的自驾行", "start_idx": 10, "end_idx": 18, "type": "行程信息"}, {"entity": "南京到厦门的浪漫蜜月行", "start_idx": 29, "end_idx": 39, "type": "行程信息"}]}, {"text": "下个月初，患者将前往杭州进行项目考察，并于月8日加入成都至青岛的啤酒节之旅。", "label": [{"entity": "下个月初，患者将前往杭州进行项目考察", "start_idx": 0, "end_idx": 17, "type": "行程信息"}, {"entity": "并于月8日加入成都至青岛的啤酒节之旅", "start_idx": 19, "end_idx": 36, "type": "行程信息"}]}, {"text": "月7日，患者计划从杭州出发至西安，进行古城探秘，而年9月则安排了厦门至桂林的山水游。", "label": [{"entity": "月7日，患者计划从杭州出发至西安，进行古城探秘", "start_idx": 0, "end_idx": 22, "type": "行程信息"}, {"entity": "年9月则安排了厦门至桂林的山水游", "start_idx": 25, "end_idx": 40, "type": "行程信息"}]}, {"text": "明年9月，患者已安排上海至成都的熊猫守护之旅，同时计划年6月开展上海至内蒙古草原的自驾行。", "label": [{"entity": "明年9月，患者已安排上海至成都的熊猫守护之旅", "start_idx": 0, "end_idx": 21, "type": "行程信息"}, {"entity": "同时计划年6月开展上海至内蒙古草原的自驾行", "start_idx": 23, "end_idx": 43, "type": "行程信息"}]}, {"text": "明年清明节期间，杭州至黄山的春游路线备受银行客户青睐。", "label": [{"entity": "明年清明节期间", "start_idx": 0, "end_idx": 6, "type": "行程信息"}, {"entity": "杭州至黄山", "start_idx": 8, "end_idx": 12, "type": "行程信息"}]}, {"text": "明年5月，上海至成都的寻根之旅将成为新的旅行热点。", "label": [{"entity": "明年5月", "start_idx": 0, "end_idx": 3, "type": "行程信息"}, {"entity": "上海至成都的寻根之旅", "start_idx": 5, "end_idx": 14, "type": "行程信息"}]}, {"text": "春节前夕，广州至香港购物游的预订量已有显著增长。", "label": [{"entity": "广州至香港购物游", "start_idx": 5, "end_idx": 12, "type": "行程信息"}]}, {"text": "月7日，杭州至西安的古城探秘活动吸引了众多历史爱好者。", "label": [{"entity": "月7日，杭州至西安的古城探秘活动", "start_idx": 0, "end_idx": 15, "type": "行程信息"}]}, {"text": "明年春节，武汉至广州的美食之旅预计将迎来客流高峰。", "label": [{"entity": "明年春节，武汉至广州的美食之旅", "start_idx": 0, "end_idx": 14, "type": "行程信息"}]}, {"text": "此外，下周三上海至厦门的航线也将迎来一波出行小高峰。", "label": [{"entity": "下周三上海至厦门的航线", "start_idx": 3, "end_idx": 13, "type": "行程信息"}]}, {"text": "同时，昆明至大理的旅游线路在清明节期间也备受期待。", "label": [{"entity": "昆明至大理的旅游线路", "start_idx": 3, "end_idx": 12, "type": "行程信息"}, {"entity": "清明节期间", "start_idx": 14, "end_idx": 18, "type": "行程信息"}]}, {"text": "记住，9月15日从杭州出发去三亚休闲度假，别误了行程！", "label": [{"entity": "9月15日从杭州出发去三亚休闲度假", "start_idx": 3, "end_idx": 19, "type": "行程信息"}]}, {"text": "元旦节一定要去深圳到长沙的跨年之旅，11月还有深圳至云南丽江的摄影行程！", "label": [{"entity": "元旦节一定要去深圳到长沙的跨年之旅", "start_idx": 0, "end_idx": 16, "type": "行程信息"}, {"entity": "11月还有深圳至云南丽江的摄影行程", "start_idx": 18, "end_idx": 34, "type": "行程信息"}]}, {"text": "下个月初你们组去杭州考察项目，务必安排好11日从广州飞乌鲁木齐的机票。", "label": [{"entity": "下个月初", "start_idx": 0, "end_idx": 3, "type": "行程信息"}, {"entity": "杭州", "start_idx": 8, "end_idx": 9, "type": "行程信息"}, {"entity": "11日", "start_idx": 20, "end_idx": 22, "type": "行程信息"}, {"entity": "广州", "start_idx": 24, "end_idx": 25, "type": "行程信息"}, {"entity": "乌鲁木齐", "start_idx": 27, "end_idx": 30, "type": "行程信息"}]}, {"text": "日广州飞乌鲁木齐的机票要提前订，9月成都至西安的高铁游也不要忘了！", "label": [{"entity": "日广州飞乌鲁木齐", "start_idx": 0, "end_idx": 7, "type": "行程信息"}, {"entity": "9月成都至西安的高铁游", "start_idx": 16, "end_idx": 26, "type": "行程信息"}]}, {"text": "日安排上海至西安的爱情之旅，国庆节还有北京至西安的历史文化游，赶紧计划起来！", "label": [{"entity": "日安排上海至西安的爱情之旅", "start_idx": 0, "end_idx": 12, "type": "行程信息"}, {"entity": "国庆节还有北京至西安的历史文化游", "start_idx": 14, "end_idx": 29, "type": "行程信息"}]}, {"text": "年4月厦门至南京自驾行期间，相关法律文档已准备完善，以保障活动合规性。", "label": [{"entity": "年4月厦门至南京自驾行", "start_idx": 0, "end_idx": 10, "type": "行程信息"}]}, {"text": "年6月上海至新疆的丝绸之路之旅，法律文件规定旅游范围值，确保活动在规定内进行。", "label": [{"entity": "年6月上海至新疆的丝绸之路之旅", "start_idx": 0, "end_idx": 14, "type": "行程信息"}]}, {"text": "年3月上海至西藏的心灵之旅，法律文件对行程敏感粒度作出明确规定。", "label": [{"entity": "年3月上海至西藏的心灵之旅", "start_idx": 0, "end_idx": 12, "type": "行程信息"}]}, {"text": "年7月上海至哈尔滨的火车之旅，法律文档对活动范围值进行了细化。", "label": [{"entity": "年7月上海至哈尔滨的火车之旅", "start_idx": 0, "end_idx": 13, "type": "行程信息"}]}]