{"prompts": ["Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Character.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type character\n- (B). The span contains a named character entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not character\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, other].\n\nA named character entity must be the name of a character.\nCharacter qualifiers such as \"main antagonist\", \"villain character\", \"strong female lead character\" are not relevant named entities. Ambiguous identifiers such as \"character\", \"main character\" and \"lead role\" are also not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"who is the actor in the {{lead role}} in forrest gump\"\nText Span: \"lead role\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Who is the {{main character}} in the Star Wars series\"\nText Span: \"main character\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"Could you recommend a family-friendly movie with a {{strong female character}} as the lead?\"\nText Span: \"strong female character\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n4. Query: \"Tell me about the actress who played {{Hermione Granger}} in the Harry Potter series.\"\nText Span: \"Hermione Granger\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Who directed the film released in 1972 starring {{Finn}}?\"\nText Span: \"Finn\"\n\n2. Query: \"are there any GP-rated movies featuring a {{fearless female protagonist}}\"\nText Span: \"fearless female protagonist\"\n\n3. Query: \"did {{michael myers}} star in any horror movies directed by stephen daldry\"\nText Span: \"michael myers\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Character.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type character\n- (B). The span contains a named character entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not character\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, other].\n\nA named character entity must be the name of a character.\nCharacter qualifiers such as \"main antagonist\", \"villain character\", \"strong female lead character\" are not relevant named entities. Ambiguous identifiers such as \"character\", \"main character\" and \"lead role\" are also not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"who is the actor in the {{lead role}} in forrest gump\"\nText Span: \"lead role\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Could you recommend a family-friendly movie with a {{strong female character}} as the lead?\"\nText Span: \"strong female character\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n3. Query: \"Tell me about the actress who played {{Hermione Granger}} in the Harry Potter series.\"\nText Span: \"Hermione Granger\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"Who is the {{main character}} in the Star Wars series\"\nText Span: \"main character\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What year did the movie The Hunger Games come out, and who is the {{main character}}?\"\nText Span: \"main character\"\n\n2. Query: \"Who directed the movie about a {{young ballerina}} looking for her big break in the world of dance?\"\nText Span: \"young ballerina\"\n\n3. Query: \"Is there a movie from 1961 with {{compelling characters}} that I should watch?\"\nText Span: \"compelling characters\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Character.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type character\n- (B). The span contains a named character entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not character\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, other].\n\nA named character entity must be the name of a character.\nCharacter qualifiers such as \"main antagonist\", \"villain character\", \"strong female lead character\" are not relevant named entities. Ambiguous identifiers such as \"character\", \"main character\" and \"lead role\" are also not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Who is the {{main character}} in the Star Wars series\"\nText Span: \"main character\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Could you recommend a family-friendly movie with a {{strong female character}} as the lead?\"\nText Span: \"strong female character\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n3. Query: \"Tell me about the actress who played {{Hermione Granger}} in the Harry <PERSON> series.\"\nText Span: \"Hermione Granger\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"who is the actor in the {{lead role}} in forrest gump\"\nText Span: \"lead role\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Could you show me a movie clip of an action film with {{Dynamic performances}}?\"\nText Span: \"Dynamic performances\"\n\n2. Query: \"Show me the trailer for the Avengers movie featuring {{Mr. Incredible}} as a character.\"\nText Span: \"Mr. Incredible\"\n\n3. Query: \"Is there a {{fierce}} action movie with a high viewers' rating?\"\nText Span: \"fierce\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Character.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type character\n- (B). The span contains a named character entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not character\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, other].\n\nA named character entity must be the name of a character.\nCharacter qualifiers such as \"main antagonist\", \"villain character\", \"strong female lead character\" are not relevant named entities. Ambiguous identifiers such as \"character\", \"main character\" and \"lead role\" are also not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Who is the {{main character}} in the Star Wars series\"\nText Span: \"main character\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Tell me about the actress who played {{<PERSON><PERSON><PERSON>}} in the Harry Potter series.\"\nText Span: \"Hermione Granger\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"who is the actor in the {{lead role}} in forrest gump\"\nText Span: \"lead role\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"Could you recommend a family-friendly movie with a {{strong female character}} as the lead?\"\nText Span: \"strong female character\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the plot of the movie Mulan that features the character {{Mushu}}?\"\nText Span: \"Mushu\"\n\n2. Query: \"What is a movie that is not too predictable and has {{unconvincing characters}}?\"\nText Span: \"unconvincing characters\"\n\n3. Query: \"Show me a preview of the film with the character {{Jafar}} in it\"\nText Span: \"Jafar\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Character.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type character\n- (B). The span contains a named character entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not character\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, other].\n\nA named character entity must be the name of a character.\nCharacter qualifiers such as \"main antagonist\", \"villain character\", \"strong female lead character\" are not relevant named entities. Ambiguous identifiers such as \"character\", \"main character\" and \"lead role\" are also not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Tell me about the actress who played {{Her<PERSON><PERSON>}} in the Harry Potter series.\"\nText Span: \"Hermione Granger\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Who is the {{main character}} in the Star Wars series\"\nText Span: \"main character\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"Could you recommend a family-friendly movie with a {{strong female character}} as the lead?\"\nText Span: \"strong female character\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n4. Query: \"who is the actor in the {{lead role}} in forrest gump\"\nText Span: \"lead role\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Show me the trailer for a movie about a Battle for survival starring {{Katniss Everdeen}}.\"\nText Span: \"Katniss Everdeen\"\n\n2. Query: \"What is the highest-rated comedy film featuring {{Ron Weasley}} as a character?\"\nText Span: \"Ron Weasley\"\n\n3. Query: \"Can you provide me with the plot of the movie that has the {{character Neo}} in it\"\nText Span: \"character Neo\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Character.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type character\n- (B). The span contains a named character entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not character\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, other].\n\nA named character entity must be the name of a character.\nCharacter qualifiers such as \"main antagonist\", \"villain character\", \"strong female lead character\" are not relevant named entities. Ambiguous identifiers such as \"character\", \"main character\" and \"lead role\" are also not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"who is the actor in the {{lead role}} in forrest gump\"\nText Span: \"lead role\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Could you recommend a family-friendly movie with a {{strong female character}} as the lead?\"\nText Span: \"strong female character\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n3. Query: \"Tell me about the actress who played {{Hermione Granger}} in the Harry Potter series.\"\nText Span: \"Hermione Granger\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"Who is the {{main character}} in the Star Wars series\"\nText Span: \"main character\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Which movie features a character similar to {{Jon Snow}} in Game of Thrones?\"\nText Span: \"Jon Snow\"\n\n2. Query: \"What year did Bong Joon-ho direct a film with a {{strong female lead}} and a gripping plot?\"\nText Span: \"strong female lead\"\n\n3. Query: \"Can you recommend a movie directed by Niki Caro that has a {{strong female lead}}?\"\nText Span: \"strong female lead\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Character.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type character\n- (B). The span contains a named character entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not character\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, other].\n\nA named character entity must be the name of a character.\nCharacter qualifiers such as \"main antagonist\", \"villain character\", \"strong female lead character\" are not relevant named entities. Ambiguous identifiers such as \"character\", \"main character\" and \"lead role\" are also not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Could you recommend a family-friendly movie with a {{strong female character}} as the lead?\"\nText Span: \"strong female character\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n2. Query: \"who is the actor in the {{lead role}} in forrest gump\"\nText Span: \"lead role\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"Tell me about the actress who played {{Hermione Granger}} in the Harry Potter series.\"\nText Span: \"Hermione Granger\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"Who is the {{main character}} in the Star Wars series\"\nText Span: \"main character\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What movies featuring {{ghosts}} were released in the 1990s?\"\nText Span: \"ghosts\"\n\n2. Query: \"Is {{Kylo Ren}} a character in any of the Star Wars movies?\"\nText Span: \"Kylo Ren\"\n\n3. Query: \"can you show me a character teaser of the upcoming {{James Bond}} film No Time to Die\"\nText Span: \"James Bond\""]}