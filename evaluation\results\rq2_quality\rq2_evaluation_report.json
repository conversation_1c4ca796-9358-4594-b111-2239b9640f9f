{"evaluation_type": "RQ2: 生成数据质量评估", "evaluation_time": "2025-07-20T18:55:36.407195", "config": {"rq2_quality_assessment": {"description": "生成数据质量评估配置", "metrics": {"naturalness": {"enabled": true, "model": "zhipu-api", "scoring_scale": [1, 10], "sample_size": 100, "threshold": 6.0}, "annotation_accuracy": {"enabled": true, "check_entity_boundaries": true, "check_entity_types": true, "manual_validation_sample": 50}, "semantic_consistency": {"enabled": true, "context_window": 5, "similarity_threshold": 0.7}, "diversity_metrics": {"vocabulary_diversity": 0.6, "syntactic_diversity": 0.5, "semantic_diversity": 0.4, "context_diversity": 0.5, "entity_diversity": 0.7}, "balance_metrics": {"distribution_tolerance": 0.15, "min_coverage_ratio": 0.8}}, "evaluation_methods": {"automatic_scoring": true, "manual_evaluation": true, "cross_validation": true}}}, "quality_results": {"naturalness_evaluation": {"avg_score": 6.92574728617748, "std_score": 0.3048030189318583, "min_score": 6.15218134316101, "max_score": 7.497392520105114, "scores": [7.107646216085769, 6.856506256861166, 6.722641606416095, 6.591042731846218, 6.621870891587613, 7.217512840951313, 7.3797204309652145, 7.483560965901116, 6.777835520740087, 6.598791178650935, 7.250836112781054, 6.708297586464463, 6.665141555142912, 6.622309070568095, 6.582015488569429, 7.149985816134514, 7.163064824891333, 6.773475383751095, 6.6202747832923, 6.670105364920988, 6.7335844714000705, 6.960759296699996, 6.9827231035734, 6.950620547273282, 6.760956651337961, 6.654793919034388, 7.3276337883596545, 7.298103767941674, 7.018273564623112, 7.434317941728165, 7.0904387681264005, 6.815411357973153, 7.013915662859633, 7.106359546122219, 6.887468826880531, 6.563284854585227, 7.319533407556685, 6.551221313264869, 6.849403163704863, 6.15218134316101, 7.038901286546582, 6.8354104525103905, 7.008159447687182, 6.9270016949694675, 6.506173630456079, 6.896081563781495, 6.481123855969034, 6.876476319280399, 7.453267988734858, 6.998077506298021, 6.8311744585642415, 6.6178262608214355, 6.617106028488003, 7.188442405224568, 7.3214825153279515, 6.535845059747198, 6.662384806812134, 6.627373762763016, 7.40620231561053, 7.111933509175137, 6.654059952999171, 6.609773921860233, 6.672583116574957, 6.808549472803368, 6.942088403105894, 6.569863193724741, 7.3269687787587126, 6.883516178764129, 7.286030321775936, 7.487472682174619, 7.141023283083616, 7.114055903648996, 7.497392520105114, 7.358252824979218, 6.880942127656353, 7.00388320964839, 7.432132244212824, 6.995314230452495, 7.22024812925412, 7.10196372063779, 6.695624013570582, 7.403798891913617, 6.7479830687128795, 7.153961126633977, 6.92200143993002, 6.956625586875386, 7.31293436755283, 6.292054282063458, 6.546836587092402, 6.611528062700816, 6.541505007298736, 6.703625851344889, 7.1402003350382985, 7.3711846406172326, 7.192431717320554, 6.517099057260753, 6.54325875764295, 7.1881102865522575, 6.790795019017648, 6.982991510892323], "detailed_results": [{"text": "医生建议他进行冠状动脉造影检查，以明确诊断。", "score": 7.107646216085769, "length": 22, "entity_count": 1}, {"text": "这家公司的主要资产包括位于上海市浦东新区的两栋写字楼和一架波音737飞机。", "score": 6.856506256861166, "length": 37, "entity_count": 2}, {"text": "李明目前处于已婚状态，他和妻子已经共同生活了十年。", "score": 6.722641606416095, "length": 25, "entity_count": 1}, {"text": "胸部X线透视和胸片可见患侧膈呼吸运动减弱肋膈角变钝流行性胸痛和带状疱疹前驱期的胸痛及肋骨骨折相鉴别。", "score": 6.591042731846218, "length": 50, "entity_count": 1}, {"text": "小王的月薪是8000元，比他预期的7000元高出不少。", "score": 6.621870891587613, "length": 27, "entity_count": 2}, {"text": "小明对花生过敏，每次吃花生酱都会出现严重的皮肤瘙痒症状。", "score": 7.217512840951313, "length": 28, "entity_count": 2}, {"text": "我的航班号是CA1234，明天早上8:30从北京首都国际机场起飞。", "score": 7.3797204309652145, "length": 33, "entity_count": 3}, {"text": "我计划这个周末去北京故宫博物院参观，顺便在附近的王府井大街逛逛。", "score": 7.483560965901116, "length": 32, "entity_count": 2}, {"text": "张先生在央行征信系统中查询了自己的个人信用报告，发现其中包含一份名为“房贷还款记录”的信用记录。", "score": 6.777835520740087, "length": 48, "entity_count": 1}, {"text": "张先生通过工商银行向李女士转账了5000元，交易时间为2023年10月15日。", "score": 6.598791178650935, "length": 39, "entity_count": 4}, {"text": "张女士在2022年3月15日生产，新生儿体重为3.5千克，Apgar评分10分。", "score": 7.250836112781054, "length": 40, "entity_count": 4}, {"text": "我需要查看2021年度个人所得税纳税申报表和增值税专用发票存根联。", "score": 6.708297586464463, "length": 33, "entity_count": 2}, {"text": "\"客户选择将部分资金配置到华夏成长混合型基金和招商中证白酒指数基金。\"", "score": 6.665141555142912, "length": 35, "entity_count": 2}, {"text": "患者出现了明显的呼吸困难、咳嗽和发热症状，需要进一步检查。", "score": 6.622309070568095, "length": 29, "entity_count": 3}, {"text": "张女士的婚姻状况是已婚，并且已经育有两个孩子。", "score": 6.582015488569429, "length": 23, "entity_count": 1}, {"text": "这位支持者明确表示自己属于美国民主党的自由派阵营。", "score": 7.149985816134514, "length": 25, "entity_count": 2}, {"text": "小明今年8岁，正在上小学二年级，他的妹妹今年5岁。", "score": 7.163064824891333, "length": 25, "entity_count": 2}, {"text": "医生建议他进行胃镜检查，以明确消化性溃疡的诊断。", "score": 6.773475383751095, "length": 24, "entity_count": 1}, {"text": "我计划下周三上午9点从北京首都国际机场出发，前往上海浦东国际机场。", "score": 6.6202747832923, "length": 33, "entity_count": 3}, {"text": "张先生通过招商银行转账给李女士，交易金额为￥3,500.00，交易时间为2023-11-15 14:30。", "score": 6.670105364920988, "length": 53, "entity_count": 3}, {"text": "我选择将部分资金投资于华夏成长混合型证券投资基金。", "score": 6.7335844714000705, "length": 25, "entity_count": 1}, {"text": "这家公司的月薪是12,500元，比同行业平均水平要高一些。", "score": 6.960759296699996, "length": 29, "entity_count": 1}, {"text": "小明对花生过敏，每次吃含花生酱的食物都会出现严重反应。", "score": 6.9827231035734, "length": 27, "entity_count": 2}, {"text": "张伟（男）是公司的新任项目经理，他将在下周一正式入职。", "score": 6.950620547273282, "length": 27, "entity_count": 1}, {"text": "\"我决定将部分资金投资于华夏成长混合型证券投资基金。\"", "score": 6.760956651337961, "length": 27, "entity_count": 1}, {"text": "张先生通过招商银行信用卡支付了订单号为20230515的购物款项。", "score": 6.654793919034388, "length": 33, "entity_count": 2}, {"text": "除了资金支持之外，工行还为创业大学生提供包括存款、资金结算、电子银行、银行卡等一站式金融服务，", "score": 7.3276337883596545, "length": 47, "entity_count": 1}, {"text": "美国留学生李华在巴黎遇到了一位法国朋友，他们用英语交流。", "score": 7.298103767941674, "length": 28, "entity_count": 2}, {"text": "这台工厂里的卡特彼勒D6R推土机需要定期进行维护保养。", "score": 7.018273564623112, "length": 27, "entity_count": 1}, {"text": "张先生通过支付宝完成了从招商银行向建设银行的转账，金额为5000元。", "score": 7.434317941728165, "length": 34, "entity_count": 4}, {"text": "这位牙科医生正在仔细检查病人的牙齿健康状况。", "score": 7.0904387681264005, "length": 22, "entity_count": 1}, {"text": "周云女士：中国国籍，无境外居留权，", "score": 6.815411357973153, "length": 17, "entity_count": 1}, {"text": "当记者在泗水街头随便询问10余名市民却发现，只有寥寥几人知道当地有如此神奇的东西。", "score": 7.013915662859633, "length": 41, "entity_count": 1}, {"text": "藏族同胞在高原上热情地跳起了传统的锅庄舞。", "score": 7.106359546122219, "length": 21, "entity_count": 1}, {"text": "患者需要接受冠状动脉造影检查以明确诊断。", "score": 6.887468826880531, "length": 20, "entity_count": 1}, {"text": "李娜在今天的网球比赛中表现出色，赢得了观众的阵阵掌声。", "score": 6.563284854585227, "length": 27, "entity_count": 1}, {"text": "李华拥有北京大学计算机科学专业的硕士学位，这为他的职业发展奠定了坚实基础。", "score": 7.319533407556685, "length": 37, "entity_count": 1}, {"text": "20雷池，本场无冷迹象。", "score": 6.551221313264869, "length": 12, "entity_count": 1}, {"text": "病程晚期脑脊液中检出高水平抗体（疫苗不能诱导）亦有诊断意义。", "score": 6.849403163704863, "length": 30, "entity_count": 1}, {"text": "\"我决定将一部分资金投资于华夏成长混合型证券投资基金。\"", "score": 6.15218134316101, "length": 28, "entity_count": 1}, {"text": "昨天我去了北京市海淀区中关村大街上的那家咖啡店，环境很不错。", "score": 7.038901286546582, "length": 30, "entity_count": 3}, {"text": "医生建议他进行胃镜检查，以明确消化道出血的原因。", "score": 6.8354104525103905, "length": 24, "entity_count": 1}, {"text": "我的爸爸张伟和妈妈李娜明天要带妹妹小芳一起去公园玩。", "score": 7.008159447687182, "length": 26, "entity_count": 3}, {"text": "布鲁京斯研究所桑顿中国中心研究部主任李成说，东亚的和平与安全，是美国的“核心利益”之一。", "score": 6.9270016949694675, "length": 44, "entity_count": 2}, {"text": "仙游县委有关负责人说，目前，他们对已授牌认证的成品，特别是展厅内摆设的成品展开检查。", "score": 6.506173630456079, "length": 42, "entity_count": 1}, {"text": "张华拥有北京大学计算机科学与技术专业的博士学位，目前正在申请教授职位。", "score": 6.896081563781495, "length": 35, "entity_count": 1}, {"text": "彭久洋：我的魂飞了贝鲁斯科尼老古董收藏家（图）", "score": 6.481123855969034, "length": 23, "entity_count": 1}, {"text": "医生建议患者进行结肠镜检查以进一步明确诊断。", "score": 6.876476319280399, "length": 22, "entity_count": 1}, {"text": "4.第三类（1）无症状性WPW综合征患者，年龄小于5岁。", "score": 7.453267988734858, "length": 28, "entity_count": 1}, {"text": "\"我决定将部分资金投资于华夏基金旗下的华夏回报混合型基金。\"", "score": 6.998077506298021, "length": 30, "entity_count": 1}, {"text": "藏族和蒙古族在传统节日中会展示各自独特的服饰和舞蹈。", "score": 6.8311744585642415, "length": 26, "entity_count": 2}, {"text": "藏族和蒙古族的学生在学校的民族文化节上展示了各自的服饰和舞蹈。", "score": 6.6178262608214355, "length": 31, "entity_count": 2}, {"text": "我妈妈张丽昨天给我打电话，让我周末去姥姥李芳家吃饭。", "score": 6.617106028488003, "length": 26, "entity_count": 3}, {"text": "【预防和治疗】（一）控制和消灭传染源加强犬等管理，野犬应尽量捕杀，家犬应登记，注射疫苗。", "score": 7.188442405224568, "length": 44, "entity_count": 1}, {"text": "小明对花生过敏，每次吃坚果类食物前都会仔细检查成分表。", "score": 7.3214825153279515, "length": 27, "entity_count": 1}, {"text": "张女士在2023年3月15日成功分娩，产下一名体重3.5公斤的女婴。", "score": 6.535845059747198, "length": 34, "entity_count": 3}, {"text": "上海外滩的夜景吸引了众多游客前来观赏。", "score": 6.662384806812134, "length": 19, "entity_count": 2}, {"text": "这位选民坚定支持自由主义，认为政府应该减少对经济的干预。", "score": 6.627373762763016, "length": 28, "entity_count": 1}, {"text": "小明今年5岁，已经能够自己穿衣服了。", "score": 7.40620231561053, "length": 18, "entity_count": 1}, {"text": "张女士在2023年4月15日进行了产前检查，血压值为120/80mmHg。", "score": 7.111933509175137, "length": 37, "entity_count": 1}, {"text": "那位来自日本的游客在故宫博物院仔细参观了古代文物。", "score": 6.654059952999171, "length": 25, "entity_count": 1}, {"text": "张先生查询了自己的个人征信报告，发现信用评分达到了760分。", "score": 6.609773921860233, "length": 30, "entity_count": 2}, {"text": "那张表格上写着性别一栏可以选非二元或雌雄同体，真够细的。", "score": 6.672583116574957, "length": 28, "entity_count": 2}, {"text": "张三的婚姻状况是已婚，他和妻子已经共同生活了十年。", "score": 6.808549472803368, "length": 25, "entity_count": 1}, {"text": "小明今年8岁，正在上小学二年级，活泼可爱。", "score": 6.942088403105894, "length": 21, "entity_count": 1}, {"text": "我在2023年个人所得税年度汇算清缴中提交了工资薪金所得记录。", "score": 6.569863193724741, "length": 31, "entity_count": 2}, {"text": "张先生在查询个人信用报告时，发现他的信用卡逾期记录显示为“已结清”。", "score": 7.3269687787587126, "length": 34, "entity_count": 2}, {"text": "我弟弟张明今天下午要去接妹妹李婷放学回家。", "score": 6.883516178764129, "length": 21, "entity_count": 2}, {"text": "这家公司的仓库里存放着大量的集装箱、挖掘机和叉车等实体资产。", "score": 7.286030321775936, "length": 30, "entity_count": 3}, {"text": "张华拥有北京大学计算机科学与技术专业的学士学位。", "score": 7.487472682174619, "length": 24, "entity_count": 1}, {"text": "这位经验丰富的建筑师为整个社区设计了现代化的住宅楼。", "score": 7.141023283083616, "length": 26, "entity_count": 1}, {"text": "张三的婚姻状况是已婚，他和李四已经共同生活了十年。", "score": 7.114055903648996, "length": 25, "entity_count": 1}, {"text": "我的妈妈李女士和爸爸王先生一起去参加了奶奶张女士的七十岁生日宴会。", "score": 7.497392520105114, "length": 33, "entity_count": 6}, {"text": "张伟和李娜一起参加了上周在杭州举办的科技展览会。", "score": 7.358252824979218, "length": 24, "entity_count": 2}, {"text": "以20%肥皂水或0.1%苯扎溴铵彻底冲洗伤口至少半小时；再用白酒或70%乙醇、碘酊涂擦几次，以清除局部的病毒。", "score": 6.880942127656353, "length": 55, "entity_count": 4}, {"text": "李华拥有北京大学计算机科学与技术专业的博士学位。", "score": 7.00388320964839, "length": 24, "entity_count": 3}, {"text": "李娜今天去北京参加了一场重要的商务会议。", "score": 7.432132244212824, "length": 20, "entity_count": 1}, {"text": "如非肺炎病例，宜用宽大胶布条紧缠患部以减少其呼吸动作或给镇咳剂抑制咳嗽。", "score": 6.995314230452495, "length": 36, "entity_count": 1}, {"text": "小明对花生过敏，每次吃到含有花生酱的食物都会出现皮肤瘙痒的症状。", "score": 7.22024812925412, "length": 32, "entity_count": 2}, {"text": "请查看您的个人税务记录，包括个人所得税缴纳证明（2023年）和增值税发票（20231115）。", "score": 7.10196372063779, "length": 47, "entity_count": 2}, {"text": "李娜在昨晚的比赛中表现出色，赢得了观众们的热烈掌声。", "score": 6.695624013570582, "length": 26, "entity_count": 1}, {"text": "张先生通过支付宝向李女士转账了500元，交易时间为2023年10月15日15:30。", "score": 7.403798891913617, "length": 42, "entity_count": 4}, {"text": "小明今年5岁，正在上幼儿园大班。", "score": 6.7479830687128795, "length": 16, "entity_count": 1}, {"text": "张先生通过支付宝转账给李女士1000元，交易时间为2023年10月15日15:30。", "score": 7.153961126633977, "length": 42, "entity_count": 3}, {"text": "这位支持自由主义政治理念的代表在会议上发表了重要讲话。", "score": 6.92200143993002, "length": 27, "entity_count": 1}, {"text": "在云南的节日庆典上，傣族和彝族的人们穿着传统服饰载歌载舞。", "score": 6.956625586875386, "length": 29, "entity_count": 2}, {"text": "这位来自日本的游客在巴黎街头留下了深刻的印象。", "score": 7.31293436755283, "length": 23, "entity_count": 1}, {"text": "\"这位女性医生正在为男性患者仔细检查身体。\"", "score": 6.292054282063458, "length": 22, "entity_count": 2}, {"text": "我计划下周三上午9点从北京首都国际机场出发，前往上海浦东国际机场。", "score": 6.546836587092402, "length": 33, "entity_count": 3}, {"text": "医生建议患者进行结肠镜检查，以排除早期病变的可能性。", "score": 6.611528062700816, "length": 26, "entity_count": 1}, {"text": "公司提交了2023年度企业所得税年度汇算清缴纳税申报表。", "score": 6.541505007298736, "length": 28, "entity_count": 1}, {"text": "公司的增值税专用发票编号为NO.123456789，请查收。", "score": 6.703625851344889, "length": 30, "entity_count": 1}, {"text": "北京市的故宫博物院是中国最大的古代文化艺术博物馆。", "score": 7.1402003350382985, "length": 25, "entity_count": 2}, {"text": "这家公司的主要资产包括一栋位于北京朝阳区国贸CBD的写字楼和一辆奔驰S级轿车。", "score": 7.3711846406172326, "length": 39, "entity_count": 2}, {"text": "这家公司的月工资是8,500元，比同行业平均水平要高一些。", "score": 7.192431717320554, "length": 29, "entity_count": 1}, {"text": "这位自由主义者认为，政府应该减少对经济的干预。", "score": 6.517099057260753, "length": 23, "entity_count": 1}, {"text": "张伟和李娜在周末一起去北京国家图书馆借阅了最新的科技书籍。", "score": 6.54325875764295, "length": 29, "entity_count": 2}, {"text": "我计划明天上午9点从北京首都国际机场出发，前往上海浦东国际机场。", "score": 7.1881102865522575, "length": 32, "entity_count": 3}, {"text": "张先生通过工商银行转账支付了2023年12月5日的订单，金额为￥1,280.50。", "score": 6.790795019017648, "length": 41, "entity_count": 3}, {"text": "张三的婚姻状况是已婚，他与妻子李四已经共同生活了十年。", "score": 6.982991510892323, "length": 27, "entity_count": 1}], "sample_size": 100, "evaluation_method": "simulated_llm"}, "annotation_accuracy": {"boundary_accuracy": 0.0, "type_accuracy": 0.024154589371980676, "total_entities": 414, "valid_entities": 0, "invalid_boundaries": 414, "unknown_types": 404}, "semantic_consistency": {"avg_consistency": 0.8192139737991267, "std_consistency": 0.10270274474682776, "min_consistency": 0.30000000000000016, "max_consistency": 0.9, "scores": [0.9, 0.8, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.8, 0.8, 0.9, 0.8, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.6000000000000001, 0.9, 0.8, 0.9, 0.8, 0.9, 0.9, 0.8, 0.8, 0.9, 0.8, 0.9, 0.8, 0.8, 0.8, 0.8, 0.9, 0.9, 0.9, 0.9, 0.9, 0.8, 0.9, 0.9, 0.9, 0.9, 0.8, 0.9, 0.8, 0.9, 0.8, 0.9, 0.8, 0.9, 0.8, 0.9, 0.9, 0.9, 0.8, 0.9, 0.9, 0.9, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.9, 0.8, 0.9, 0.8, 0.9, 0.7000000000000001, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.8, 0.9, 0.9, 0.8, 0.8, 0.9, 0.8, 0.8, 0.7000000000000001, 0.30000000000000016, 0.7000000000000001, 0.40000000000000013, 0.7000000000000001, 0.8, 0.7000000000000001, 0.8, 0.8, 0.40000000000000013, 0.9, 0.8, 0.9, 0.9, 0.9, 0.9, 0.8, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.8, 0.9, 0.9, 0.9, 0.9, 0.9, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.9, 0.9, 0.9, 0.8, 0.9, 0.8, 0.8, 0.8, 0.9, 0.8, 0.8, 0.9, 0.9, 0.8, 0.8, 0.9, 0.7000000000000001, 0.8, 0.8, 0.8, 0.7000000000000001, 0.9, 0.9, 0.8, 0.5000000000000001, 0.7000000000000001, 0.7000000000000001, 0.6000000000000001, 0.7000000000000001, 0.7000000000000001, 0.7000000000000001, 0.6000000000000001, 0.6000000000000001, 0.8, 0.9, 0.9, 0.9, 0.9, 0.9, 0.7000000000000001, 0.7000000000000001, 0.7000000000000001, 0.7000000000000001, 0.7000000000000001, 0.7000000000000001, 0.7000000000000001, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.9, 0.8, 0.8, 0.8, 0.8, 0.8, 0.9, 0.8, 0.8, 0.8, 0.9, 0.6000000000000001, 0.7000000000000001, 0.8, 0.9, 0.8, 0.7000000000000001, 0.7000000000000001, 0.5000000000000001, 0.8, 0.8, 0.9, 0.7000000000000001, 0.8, 0.8, 0.7000000000000001, 0.7000000000000001, 0.8, 0.6000000000000001, 0.7000000000000001, 0.7000000000000001, 0.6000000000000001, 0.7000000000000001, 0.7000000000000001, 0.7000000000000001, 0.7000000000000001, 0.6000000000000001, 0.7000000000000001]}, "diversity_metrics": {"vocabulary_diversity": 0.2873714737674664, "sentence_diversity": 0.9694323144104804, "context_diversity": 0.27294685990338163, "syntactic_diversity": 0.09170305676855896, "semantic_diversity": 0.425, "entity_diversity": 0.057971014492753624}, "balance_metrics": {"actual_distribution": {"姓名": 15, "地理位置": 19, "职业": 10, "医疗程序": 10, "疾病": 10, "药物": 10, "临床表现": 24, "国籍": 12, "民族": 17, "教育背景": 13, "性别": 14, "年龄": 11, "婚姻状况": 10, "政治倾向": 15, "家庭成员": 37, "工资数额": 12, "投资产品": 11, "税务记录": 17, "信用记录": 16, "实体资产": 19, "交易信息": 34, "过敏信息": 18, "生育信息": 27, "行程信息": 33}, "distribution_entropy": 4.449714939940739, "balance_score": 0.5041666666666667, "coverage_ratio": 1.0}, "entity_type_consistency": {"overall_consistency": 1.0, "entity_type_consistency": {"姓名": {"consistency_rate": 1.0, "total_examples": 15, "consistent_examples": 15, "inconsistent_examples": 0}, "地理位置": {"consistency_rate": 1.0, "total_examples": 19, "consistent_examples": 19, "inconsistent_examples": 0}, "职业": {"consistency_rate": 1.0, "total_examples": 10, "consistent_examples": 10, "inconsistent_examples": 0}, "医疗程序": {"consistency_rate": 1.0, "total_examples": 10, "consistent_examples": 10, "inconsistent_examples": 0}, "疾病": {"consistency_rate": 1.0, "total_examples": 10, "consistent_examples": 10, "inconsistent_examples": 0}, "药物": {"consistency_rate": 1.0, "total_examples": 10, "consistent_examples": 10, "inconsistent_examples": 0}, "临床表现": {"consistency_rate": 1.0, "total_examples": 24, "consistent_examples": 24, "inconsistent_examples": 0}, "国籍": {"consistency_rate": 1.0, "total_examples": 12, "consistent_examples": 12, "inconsistent_examples": 0}, "民族": {"consistency_rate": 1.0, "total_examples": 17, "consistent_examples": 17, "inconsistent_examples": 0}, "教育背景": {"consistency_rate": 1.0, "total_examples": 13, "consistent_examples": 13, "inconsistent_examples": 0}, "性别": {"consistency_rate": 1.0, "total_examples": 14, "consistent_examples": 14, "inconsistent_examples": 0}, "年龄": {"consistency_rate": 1.0, "total_examples": 11, "consistent_examples": 11, "inconsistent_examples": 0}, "婚姻状况": {"consistency_rate": 1.0, "total_examples": 10, "consistent_examples": 10, "inconsistent_examples": 0}, "政治倾向": {"consistency_rate": 1.0, "total_examples": 15, "consistent_examples": 15, "inconsistent_examples": 0}, "家庭成员": {"consistency_rate": 1.0, "total_examples": 37, "consistent_examples": 37, "inconsistent_examples": 0}, "工资数额": {"consistency_rate": 1.0, "total_examples": 12, "consistent_examples": 12, "inconsistent_examples": 0}, "投资产品": {"consistency_rate": 1.0, "total_examples": 11, "consistent_examples": 11, "inconsistent_examples": 0}, "税务记录": {"consistency_rate": 1.0, "total_examples": 17, "consistent_examples": 17, "inconsistent_examples": 0}, "信用记录": {"consistency_rate": 1.0, "total_examples": 16, "consistent_examples": 16, "inconsistent_examples": 0}, "实体资产": {"consistency_rate": 1.0, "total_examples": 19, "consistent_examples": 19, "inconsistent_examples": 0}, "交易信息": {"consistency_rate": 1.0, "total_examples": 34, "consistent_examples": 34, "inconsistent_examples": 0}, "过敏信息": {"consistency_rate": 1.0, "total_examples": 18, "consistent_examples": 18, "inconsistent_examples": 0}, "生育信息": {"consistency_rate": 1.0, "total_examples": 27, "consistent_examples": 27, "inconsistent_examples": 0}, "行程信息": {"consistency_rate": 1.0, "total_examples": 33, "consistent_examples": 33, "inconsistent_examples": 0}}, "inconsistencies": [], "total_entities": 414, "total_inconsistencies": 0}, "context_diversity": {"overall_context_diversity": 0.43400700680918686, "entity_type_diversity": {"姓名": {"diversity_ratio": 0.5333333333333333, "unique_contexts": 8, "total_contexts": 15, "most_common_contexts": [{"context": "[ENTITY]张伟和李娜", "count": 6}, {"context": "[ENTITY]张伟今天早", "count": 2}, {"context": "[ENTITY]李明和张伟", "count": 2}]}, "地理位置": {"diversity_ratio": 0.47368421052631576, "unique_contexts": 9, "total_contexts": 19, "most_common_contexts": [{"context": "[ENTITY]上海外滩的", "count": 3}, {"context": "[ENTITY]昨天我去了", "count": 3}, {"context": "[ENTITY]北京故宫的", "count": 3}]}, "职业": {"diversity_ratio": 1.0, "unique_contexts": 10, "total_contexts": 10, "most_common_contexts": [{"context": "[ENTITY]布鲁京斯研", "count": 1}, {"context": "[ENTITY]万通地产设", "count": 1}, {"context": "[ENTITY]彭久洋：我", "count": 1}]}, "医疗程序": {"diversity_ratio": 0.4, "unique_contexts": 4, "total_contexts": 10, "most_common_contexts": [{"context": "[ENTITY]医生建议患", "count": 4}, {"context": "[ENTITY]医生建议他", "count": 4}, {"context": "[ENTITY]（5）房室", "count": 1}]}, "疾病": {"diversity_ratio": 0.7, "unique_contexts": 7, "total_contexts": 10, "most_common_contexts": [{"context": "[ENTITY]这位患者被", "count": 3}, {"context": "[ENTITY]（6）发作", "count": 2}, {"context": "[ENTITY]（5）房室", "count": 1}]}, "药物": {"diversity_ratio": 0.7, "unique_contexts": 7, "total_contexts": 10, "most_common_contexts": [{"context": "[ENTITY]以20%肥", "count": 4}, {"context": "[ENTITY]（2）室上", "count": 1}, {"context": "[ENTITY]如非肺炎病", "count": 1}]}, "临床表现": {"diversity_ratio": 0.25, "unique_contexts": 6, "total_contexts": 24, "most_common_contexts": [{"context": "[ENTITY]患者出现了", "count": 12}, {"context": "[ENTITY]这位患者出", "count": 6}, {"context": "[ENTITY]这位患者的", "count": 3}]}, "国籍": {"diversity_ratio": 0.8333333333333334, "unique_contexts": 10, "total_contexts": 12, "most_common_contexts": [{"context": "[ENTITY]日本游客在", "count": 2}, {"context": "[ENTITY]美国留学生", "count": 2}, {"context": "[ENTITY]高勇：男，", "count": 1}]}, "民族": {"diversity_ratio": 0.29411764705882354, "unique_contexts": 5, "total_contexts": 17, "most_common_contexts": [{"context": "[ENTITY]在云南的节", "count": 10}, {"context": "[ENTITY]藏族和蒙古", "count": 4}, {"context": "[ENTITY]1966年", "count": 1}]}, "教育背景": {"diversity_ratio": 0.38461538461538464, "unique_contexts": 5, "total_contexts": 13, "most_common_contexts": [{"context": "[ENTITY]李华拥有北", "count": 5}, {"context": "[ENTITY]张华拥有北", "count": 3}, {"context": "[ENTITY]兰州商学院", "count": 2}]}, "性别": {"diversity_ratio": 0.5714285714285714, "unique_contexts": 8, "total_contexts": 14, "most_common_contexts": [{"context": "[ENTITY]\"这位女性", "count": 4}, {"context": "[ENTITY]那张表格上", "count": 2}, {"context": "[ENTITY]李明（男）", "count": 2}]}, "年龄": {"diversity_ratio": 0.45454545454545453, "unique_contexts": 5, "total_contexts": 11, "most_common_contexts": [{"context": "[ENTITY]小明今年8", "count": 4}, {"context": "[ENTITY]小明今年5", "count": 3}, {"context": "[ENTITY]通知：35", "count": 2}]}, "婚姻状况": {"diversity_ratio": 0.6, "unique_contexts": 6, "total_contexts": 10, "most_common_contexts": [{"context": "[ENTITY]张三的婚姻", "count": 3}, {"context": "[ENTITY]李明目前处", "count": 2}, {"context": "[ENTITY]张先生的婚", "count": 2}]}, "政治倾向": {"diversity_ratio": 0.4666666666666667, "unique_contexts": 7, "total_contexts": 15, "most_common_contexts": [{"context": "[ENTITY]作为一名坚", "count": 4}, {"context": "[ENTITY]\"作为一名", "count": 4}, {"context": "[ENTITY]这位支持者", "count": 2}]}, "家庭成员": {"diversity_ratio": 0.21621621621621623, "unique_contexts": 8, "total_contexts": 37, "most_common_contexts": [{"context": "[ENTITY]我的爸爸张", "count": 10}, {"context": "[ENTITY]妈妈和爸爸", "count": 6}, {"context": "[ENTITY]我的妈妈李", "count": 6}]}, "工资数额": {"diversity_ratio": 0.3333333333333333, "unique_contexts": 4, "total_contexts": 12, "most_common_contexts": [{"context": "[ENTITY]这家公司的", "count": 7}, {"context": "[ENTITY]小王的月薪", "count": 2}, {"context": "[ENTITY]小王的月工", "count": 2}]}, "投资产品": {"diversity_ratio": 0.6363636363636364, "unique_contexts": 7, "total_contexts": 11, "most_common_contexts": [{"context": "[ENTITY]\"我决定将", "count": 3}, {"context": "[ENTITY]我决定将部", "count": 2}, {"context": "[ENTITY]\"客户选择", "count": 2}]}, "税务记录": {"diversity_ratio": 0.4117647058823529, "unique_contexts": 7, "total_contexts": 17, "most_common_contexts": [{"context": "[ENTITY]我在202", "count": 6}, {"context": "[ENTITY]公司提交了", "count": 3}, {"context": "[ENTITY]张先生提交", "count": 2}]}, "信用记录": {"diversity_ratio": 0.5625, "unique_contexts": 9, "total_contexts": 16, "most_common_contexts": [{"context": "[ENTITY]张先生查询", "count": 3}, {"context": "[ENTITY]王先生的个", "count": 2}, {"context": "[ENTITY]小张的信用", "count": 2}]}, "实体资产": {"diversity_ratio": 0.21052631578947367, "unique_contexts": 4, "total_contexts": 19, "most_common_contexts": [{"context": "[ENTITY]这家公司的", "count": 14}, {"context": "[ENTITY]这家公司拥", "count": 3}, {"context": "[ENTITY]这台工厂里", "count": 1}]}, "交易信息": {"diversity_ratio": 0.058823529411764705, "unique_contexts": 2, "total_contexts": 34, "most_common_contexts": [{"context": "[ENTITY]张先生通过", "count": 29}, {"context": "[ENTITY]张先生于2", "count": 5}]}, "过敏信息": {"diversity_ratio": 0.05555555555555555, "unique_contexts": 1, "total_contexts": 18, "most_common_contexts": [{"context": "[ENTITY]小明对花生", "count": 18}]}, "生育信息": {"diversity_ratio": 0.14814814814814814, "unique_contexts": 4, "total_contexts": 27, "most_common_contexts": [{"context": "[ENTITY]张女士在2", "count": 13}, {"context": "[ENTITY]张女士的预", "count": 9}, {"context": "[ENTITY]这位孕妇的", "count": 3}]}, "行程信息": {"diversity_ratio": 0.12121212121212122, "unique_contexts": 4, "total_contexts": 33, "most_common_contexts": [{"context": "[ENTITY]我计划下周", "count": 20}, {"context": "[ENTITY]我的航班号", "count": 7}, {"context": "[ENTITY]我计划明天", "count": 3}]}}, "total_entity_types": 24}, "linguistic_quality": {"grammar_score": 0.9764192139737993, "fluency_score": 0.9960698689956332, "coherence_score": 0.9432314410480351, "readability_score": 0.9989082969432315, "detailed_analysis": {"grammar_distribution": {"mean": 0.9764192139737993, "std": 0.04543163948869851, "min": 0.8, "max": 1.0, "percentiles": {"25th": 1.0, "50th": 1.0, "75th": 1.0, "95th": 1.0}}, "fluency_distribution": {"mean": 0.9960698689956332, "std": 0.025289837966061584, "min": 0.7000000000000001, "max": 1.0, "percentiles": {"25th": 1.0, "50th": 1.0, "75th": 1.0, "95th": 1.0}}, "coherence_distribution": {"mean": 0.9432314410480351, "std": 0.049539747776041136, "min": 0.9, "max": 1.0, "percentiles": {"25th": 0.9, "50th": 0.9, "75th": 1.0, "95th": 1.0}}, "readability_distribution": {"mean": 0.9989082969432315, "std": 0.0073070744675465165, "min": 0.95, "max": 1.0, "percentiles": {"25th": 1.0, "50th": 1.0, "75th": 1.0, "95th": 1.0}}}}, "entity_quality": {"entity_length_analysis": {"姓名": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "地理位置": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "职业": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "医疗程序": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "疾病": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "药物": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "临床表现": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "国籍": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "民族": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "教育背景": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "性别": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "年龄": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "婚姻状况": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "政治倾向": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "家庭成员": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "工资数额": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "投资产品": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "税务记录": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "信用记录": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "实体资产": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "交易信息": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "过敏信息": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "生育信息": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}, "行程信息": {"avg_length": 0.0, "min_length": 0, "max_length": 0, "length_variance": 0.0}}, "entity_position_analysis": {"姓名": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "地理位置": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "职业": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "医疗程序": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "疾病": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "药物": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "临床表现": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "国籍": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "民族": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "教育背景": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "性别": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "年龄": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "婚姻状况": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "政治倾向": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "家庭成员": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "工资数额": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "投资产品": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "税务记录": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "信用记录": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "实体资产": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "交易信息": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "过敏信息": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "生育信息": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "行程信息": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}}, "entity_context_analysis": {"姓名": {"total_contexts": 15, "unique_contexts": 8, "context_diversity": 0.5333333333333333, "most_common_contexts": [["张伟和李娜", 6], ["张伟今天早", 2], ["李明和张伟", 2]]}, "地理位置": {"total_contexts": 19, "unique_contexts": 9, "context_diversity": 0.47368421052631576, "most_common_contexts": [["上海外滩的", 3], ["昨天我去了", 3], ["北京故宫的", 3]]}, "职业": {"total_contexts": 10, "unique_contexts": 10, "context_diversity": 1.0, "most_common_contexts": [["布鲁京斯研", 1], ["万通地产设", 1], ["彭久洋：我", 1]]}, "医疗程序": {"total_contexts": 10, "unique_contexts": 4, "context_diversity": 0.4, "most_common_contexts": [["医生建议患", 4], ["医生建议他", 4], ["（5）房室", 1]]}, "疾病": {"total_contexts": 10, "unique_contexts": 7, "context_diversity": 0.7, "most_common_contexts": [["这位患者被", 3], ["（6）发作", 2], ["（5）房室", 1]]}, "药物": {"total_contexts": 10, "unique_contexts": 7, "context_diversity": 0.7, "most_common_contexts": [["以20%肥", 4], ["（2）室上", 1], ["如非肺炎病", 1]]}, "临床表现": {"total_contexts": 24, "unique_contexts": 6, "context_diversity": 0.25, "most_common_contexts": [["患者出现了", 12], ["这位患者出", 6], ["这位患者的", 3]]}, "国籍": {"total_contexts": 12, "unique_contexts": 10, "context_diversity": 0.8333333333333334, "most_common_contexts": [["日本游客在", 2], ["美国留学生", 2], ["高勇：男，", 1]]}, "民族": {"total_contexts": 17, "unique_contexts": 5, "context_diversity": 0.29411764705882354, "most_common_contexts": [["在云南的节", 10], ["藏族和蒙古", 4], ["1966年", 1]]}, "教育背景": {"total_contexts": 13, "unique_contexts": 5, "context_diversity": 0.38461538461538464, "most_common_contexts": [["李华拥有北", 5], ["张华拥有北", 3], ["兰州商学院", 2]]}, "性别": {"total_contexts": 14, "unique_contexts": 8, "context_diversity": 0.5714285714285714, "most_common_contexts": [["\"这位女性", 4], ["那张表格上", 2], ["李明（男）", 2]]}, "年龄": {"total_contexts": 11, "unique_contexts": 5, "context_diversity": 0.45454545454545453, "most_common_contexts": [["小明今年8", 4], ["小明今年5", 3], ["通知：35", 2]]}, "婚姻状况": {"total_contexts": 10, "unique_contexts": 6, "context_diversity": 0.6, "most_common_contexts": [["张三的婚姻", 3], ["李明目前处", 2], ["张先生的婚", 2]]}, "政治倾向": {"total_contexts": 15, "unique_contexts": 7, "context_diversity": 0.4666666666666667, "most_common_contexts": [["作为一名坚", 4], ["\"作为一名", 4], ["这位支持者", 2]]}, "家庭成员": {"total_contexts": 37, "unique_contexts": 8, "context_diversity": 0.21621621621621623, "most_common_contexts": [["我的爸爸张", 10], ["妈妈和爸爸", 6], ["我的妈妈李", 6]]}, "工资数额": {"total_contexts": 12, "unique_contexts": 4, "context_diversity": 0.3333333333333333, "most_common_contexts": [["这家公司的", 7], ["小王的月薪", 2], ["小王的月工", 2]]}, "投资产品": {"total_contexts": 11, "unique_contexts": 7, "context_diversity": 0.6363636363636364, "most_common_contexts": [["\"我决定将", 3], ["我决定将部", 2], ["\"客户选择", 2]]}, "税务记录": {"total_contexts": 17, "unique_contexts": 7, "context_diversity": 0.4117647058823529, "most_common_contexts": [["我在202", 6], ["公司提交了", 3], ["张先生提交", 2]]}, "信用记录": {"total_contexts": 16, "unique_contexts": 9, "context_diversity": 0.5625, "most_common_contexts": [["张先生查询", 3], ["王先生的个", 2], ["小张的信用", 2]]}, "实体资产": {"total_contexts": 19, "unique_contexts": 4, "context_diversity": 0.21052631578947367, "most_common_contexts": [["这家公司的", 14], ["这家公司拥", 3], ["这台工厂里", 1]]}, "交易信息": {"total_contexts": 34, "unique_contexts": 2, "context_diversity": 0.058823529411764705, "most_common_contexts": [["张先生通过", 29], ["张先生于2", 5]]}, "过敏信息": {"total_contexts": 18, "unique_contexts": 1, "context_diversity": 0.05555555555555555, "most_common_contexts": [["小明对花生", 18]]}, "生育信息": {"total_contexts": 27, "unique_contexts": 4, "context_diversity": 0.14814814814814814, "most_common_contexts": [["张女士在2", 13], ["张女士的预", 9], ["这位孕妇的", 3]]}, "行程信息": {"total_contexts": 33, "unique_contexts": 4, "context_diversity": 0.12121212121212122, "most_common_contexts": [["我计划下周", 20], ["我的航班号", 7], ["我计划明天", 3]]}}, "entity_type_distribution": {"姓名": 15, "地理位置": 19, "职业": 10, "医疗程序": 10, "疾病": 10, "药物": 10, "临床表现": 24, "国籍": 12, "民族": 17, "教育背景": 13, "性别": 14, "年龄": 11, "婚姻状况": 10, "政治倾向": 15, "家庭成员": 37, "工资数额": 12, "投资产品": 11, "税务记录": 17, "信用记录": 16, "实体资产": 19, "交易信息": 34, "过敏信息": 18, "生育信息": 27, "行程信息": 33}, "problematic_entities": [{"entity_text": "", "entity_type": "姓名", "context": "浙商银行企业信贷部叶老桂博士则从另一个角度对五道门槛进行了解读。叶老桂认为，对目前国内商业银行而言，", "item_index": 0, "issue": "边界错误"}, {"entity_text": "", "entity_type": "地理位置", "context": "布鲁京斯研究所桑顿中国中心研究部主任李成说，东亚的和平与安全，是美国的“核心利益”之一。", "item_index": 1, "issue": "边界错误"}, {"entity_text": "", "entity_type": "职业", "context": "布鲁京斯研究所桑顿中国中心研究部主任李成说，东亚的和平与安全，是美国的“核心利益”之一。", "item_index": 1, "issue": "边界错误"}, {"entity_text": "", "entity_type": "职业", "context": "万通地产设计总监刘克峰；", "item_index": 2, "issue": "边界错误"}, {"entity_text": "", "entity_type": "职业", "context": "彭久洋：我的魂飞了贝鲁斯科尼老古董收藏家（图）", "item_index": 3, "issue": "边界错误"}, {"entity_text": "", "entity_type": "地理位置", "context": "20雷池，本场无冷迹象。", "item_index": 4, "issue": "边界错误"}, {"entity_text": "", "entity_type": "地理位置", "context": "她写道：抗战胜利时我从重庆坐民联轮到南京，去中山陵瞻仰，也到秦淮河去过。然后就去北京了。", "item_index": 5, "issue": "边界错误"}, {"entity_text": "", "entity_type": "职业", "context": "除了资金支持之外，工行还为创业大学生提供包括存款、资金结算、电子银行、银行卡等一站式金融服务，", "item_index": 6, "issue": "边界错误"}, {"entity_text": "", "entity_type": "职业", "context": "对此，原告代理律师指出，原告在签署认购合同时，从未看到过写明购买产品为一年期“金通道”", "item_index": 7, "issue": "边界错误"}, {"entity_text": "", "entity_type": "职业", "context": "当记者在泗水街头随便询问10余名市民却发现，只有寥寥几人知道当地有如此神奇的东西。", "item_index": 8, "issue": "边界错误"}, {"entity_text": "", "entity_type": "职业", "context": "仙游县委有关负责人说，目前，他们对已授牌认证的成品，特别是展厅内摆设的成品展开检查。", "item_index": 9, "issue": "边界错误"}, {"entity_text": "", "entity_type": "职业", "context": "怎样做才能避免拒签？新浪出国频道邀请到美国使馆签证处的签证官江德力（charles", "item_index": 10, "issue": "边界错误"}, {"entity_text": "", "entity_type": "医疗程序", "context": "（5）房室结消融和起搏器植入作为反复发作或难治性心房内折返性心动过速的替代疗法。", "item_index": 11, "issue": "边界错误"}, {"entity_text": "", "entity_type": "疾病", "context": "（5）房室结消融和起搏器植入作为反复发作或难治性心房内折返性心动过速的替代疗法。", "item_index": 11, "issue": "边界错误"}, {"entity_text": "", "entity_type": "疾病", "context": "（6）发作一次伴血流动力学损害的室性心动过速（ventriculartachycardia），可接受导管消融者。", "item_index": 12, "issue": "边界错误"}, {"entity_text": "", "entity_type": "疾病", "context": "（6）发作一次伴血流动力学损害的室性心动过速（ventriculartachycardia），可接受导管消融者。", "item_index": 12, "issue": "边界错误"}, {"entity_text": "", "entity_type": "疾病", "context": "4.第三类（1）无症状性WPW综合征患者，年龄小于5岁。", "item_index": 13, "issue": "边界错误"}, {"entity_text": "", "entity_type": "疾病", "context": "（2）室上性心动过速可用常规抗心律失常药物控制，年龄小于5岁。", "item_index": 14, "issue": "边界错误"}, {"entity_text": "", "entity_type": "药物", "context": "（2）室上性心动过速可用常规抗心律失常药物控制，年龄小于5岁。", "item_index": 14, "issue": "边界错误"}, {"entity_text": "", "entity_type": "疾病", "context": "（3）非持续性，不考虑为无休止性的阵发性室性心动过速（即一次监视数小时或任何一小时记录的心电图条带几乎均可出现），心室功能正常。", "item_index": 15, "issue": "边界错误"}, {"entity_text": "", "entity_type": "疾病", "context": "（4）非持续性室上性心动过速，不需其他治疗和（或）症状轻微。", "item_index": 16, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "深呼吸及咳嗽时疼痛加剧。", "item_index": 17, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "病程早期可闻胸膜摩擦音在全部呼吸期间均可听到。", "item_index": 18, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "胸部X线透视和胸片可见患侧膈呼吸运动减弱肋膈角变钝流行性胸痛和带状疱疹前驱期的胸痛及肋骨骨折相鉴别。", "item_index": 19, "issue": "边界错误"}, {"entity_text": "", "entity_type": "药物", "context": "如非肺炎病例，宜用宽大胶布条紧缠患部以减少其呼吸动作或给镇咳剂抑制咳嗽。", "item_index": 20, "issue": "边界错误"}, {"entity_text": "", "entity_type": "药物", "context": "病程晚期脑脊液中检出高水平抗体（疫苗不能诱导）亦有诊断意义。", "item_index": 21, "issue": "边界错误"}, {"entity_text": "", "entity_type": "药物", "context": "【预防和治疗】（一）控制和消灭传染源加强犬等管理，野犬应尽量捕杀，家犬应登记，注射疫苗。", "item_index": 22, "issue": "边界错误"}, {"entity_text": "", "entity_type": "药物", "context": "以20%肥皂水或0.1%苯扎溴铵彻底冲洗伤口至少半小时；再用白酒或70%乙醇、碘酊涂擦几次，以清除局部的病毒。", "item_index": 23, "issue": "边界错误"}, {"entity_text": "", "entity_type": "药物", "context": "以20%肥皂水或0.1%苯扎溴铵彻底冲洗伤口至少半小时；再用白酒或70%乙醇、碘酊涂擦几次，以清除局部的病毒。", "item_index": 23, "issue": "边界错误"}, {"entity_text": "", "entity_type": "药物", "context": "以20%肥皂水或0.1%苯扎溴铵彻底冲洗伤口至少半小时；再用白酒或70%乙醇、碘酊涂擦几次，以清除局部的病毒。", "item_index": 23, "issue": "边界错误"}, {"entity_text": "", "entity_type": "药物", "context": "以20%肥皂水或0.1%苯扎溴铵彻底冲洗伤口至少半小时；再用白酒或70%乙醇、碘酊涂擦几次，以清除局部的病毒。", "item_index": 23, "issue": "边界错误"}, {"entity_text": "", "entity_type": "国籍", "context": "高勇：男，中国国籍，无境外居留权，", "item_index": 24, "issue": "边界错误"}, {"entity_text": "", "entity_type": "民族", "context": "1966年出生，汉族，中共党员，本科学历，工程师、美国项目管理协会注册会员（PMIMember）、注册项目管理专家（PMP）、项目经理。", "item_index": 25, "issue": "边界错误"}, {"entity_text": "", "entity_type": "教育背景", "context": "1966年出生，汉族，中共党员，本科学历，工程师、美国项目管理协会注册会员（PMIMember）、注册项目管理专家（PMP）、项目经理。", "item_index": 25, "issue": "边界错误"}, {"entity_text": "", "entity_type": "国籍", "context": "1965年1月出生，中国国籍，无永久境外居留权。", "item_index": 26, "issue": "边界错误"}, {"entity_text": "", "entity_type": "教育背景", "context": "兰州商学院会计专业学士，中国社科院研究生院国际贸易专业硕士研究生。", "item_index": 27, "issue": "边界错误"}, {"entity_text": "", "entity_type": "教育背景", "context": "兰州商学院会计专业学士，中国社科院研究生院国际贸易专业硕士研究生。", "item_index": 27, "issue": "边界错误"}, {"entity_text": "", "entity_type": "国籍", "context": "贾志颖女士：1971年出生，中国国籍，无境外永久居留权，本科学历。", "item_index": 28, "issue": "边界错误"}, {"entity_text": "", "entity_type": "国籍", "context": "周云女士：中国国籍，无境外居留权，", "item_index": 29, "issue": "边界错误"}, {"entity_text": "", "entity_type": "性别", "context": "那张表格上写着性别一栏可以选非二元或雌雄同体，真够细的。", "item_index": 30, "issue": "边界错误"}, {"entity_text": "", "entity_type": "性别", "context": "那张表格上写着性别一栏可以选非二元或雌雄同体，真够细的。", "item_index": 30, "issue": "边界错误"}, {"entity_text": "", "entity_type": "年龄", "context": "通知：35岁以上及二十周岁以下人员，立即进行健康体检。", "item_index": 31, "issue": "边界错误"}, {"entity_text": "", "entity_type": "年龄", "context": "通知：35岁以上及二十周岁以下人员，立即进行健康体检。", "item_index": 31, "issue": "边界错误"}, {"entity_text": "", "entity_type": "姓名", "context": "李娜在昨天的比赛中表现出色，赢得了观众的阵阵掌声。", "item_index": 32, "issue": "边界错误"}, {"entity_text": "", "entity_type": "姓名", "context": "张伟今天早上在公园里遇到了他的老朋友李娜，两人聊了很久。", "item_index": 33, "issue": "边界错误"}, {"entity_text": "", "entity_type": "姓名", "context": "张伟今天早上在公园里遇到了他的老朋友李娜，两人聊了很久。", "item_index": 33, "issue": "边界错误"}, {"entity_text": "", "entity_type": "姓名", "context": "李娜今天去北京参加了一场重要的商务会议。", "item_index": 34, "issue": "边界错误"}, {"entity_text": "", "entity_type": "姓名", "context": "张伟和李娜在周末一起去北京国家图书馆借阅了最新的科技书籍。", "item_index": 35, "issue": "边界错误"}, {"entity_text": "", "entity_type": "姓名", "context": "张伟和李娜在周末一起去北京国家图书馆借阅了最新的科技书籍。", "item_index": 35, "issue": "边界错误"}, {"entity_text": "", "entity_type": "姓名", "context": "张伟和李娜一起参加了上周在杭州举办的科技展览会。", "item_index": 36, "issue": "边界错误"}, {"entity_text": "", "entity_type": "姓名", "context": "张伟和李娜一起参加了上周在杭州举办的科技展览会。", "item_index": 36, "issue": "边界错误"}, {"entity_text": "", "entity_type": "姓名", "context": "李明和张伟在昨天一起参加了公司组织的团建活动。", "item_index": 37, "issue": "边界错误"}, {"entity_text": "", "entity_type": "姓名", "context": "李明和张伟在昨天一起参加了公司组织的团建活动。", "item_index": 37, "issue": "边界错误"}, {"entity_text": "", "entity_type": "姓名", "context": "张伟和李娜一起参加了今天的公司会议，讨论了新项目的进展情况。", "item_index": 38, "issue": "边界错误"}, {"entity_text": "", "entity_type": "姓名", "context": "张伟和李娜一起参加了今天的公司会议，讨论了新项目的进展情况。", "item_index": 38, "issue": "边界错误"}, {"entity_text": "", "entity_type": "姓名", "context": "李娜在今天的网球比赛中表现出色，赢得了观众的阵阵掌声。", "item_index": 39, "issue": "边界错误"}, {"entity_text": "", "entity_type": "姓名", "context": "李娜在昨晚的比赛中表现出色，赢得了观众们的热烈掌声。", "item_index": 40, "issue": "边界错误"}, {"entity_text": "", "entity_type": "年龄", "context": "小明今年8岁，是小学二年级的学生，每天都期待放学。", "item_index": 41, "issue": "边界错误"}, {"entity_text": "", "entity_type": "年龄", "context": "这位75岁的老人每天坚持在公园里散步，锻炼身体。", "item_index": 42, "issue": "边界错误"}, {"entity_text": "", "entity_type": "年龄", "context": "小明今年已经35岁了，但他看起来比实际年龄年轻不少。", "item_index": 43, "issue": "边界错误"}, {"entity_text": "", "entity_type": "年龄", "context": "小明今年8岁，正在上小学二年级，他的妹妹今年5岁。", "item_index": 44, "issue": "边界错误"}, {"entity_text": "", "entity_type": "年龄", "context": "小明今年8岁，正在上小学二年级，他的妹妹今年5岁。", "item_index": 44, "issue": "边界错误"}, {"entity_text": "", "entity_type": "年龄", "context": "小明今年5岁，正在上幼儿园大班。", "item_index": 45, "issue": "边界错误"}, {"entity_text": "", "entity_type": "年龄", "context": "小明今年5岁，已经能够自己穿衣服了。", "item_index": 46, "issue": "边界错误"}, {"entity_text": "", "entity_type": "年龄", "context": "小明今年5岁，已经可以自己穿衣服了。", "item_index": 47, "issue": "边界错误"}, {"entity_text": "", "entity_type": "年龄", "context": "小明今年8岁，正在上小学二年级，活泼可爱。", "item_index": 48, "issue": "边界错误"}, {"entity_text": "", "entity_type": "性别", "context": "李明（男）和赵芳（女）在公司的年度会议上共同展示了他们的项目成果。", "item_index": 49, "issue": "边界错误"}, {"entity_text": "", "entity_type": "性别", "context": "李明（男）和赵芳（女）在公司的年度会议上共同展示了他们的项目成果。", "item_index": 49, "issue": "边界错误"}, {"entity_text": "", "entity_type": "性别", "context": "张伟（男）是公司的新任项目经理，他将在下周一正式入职。", "item_index": 50, "issue": "边界错误"}, {"entity_text": "", "entity_type": "性别", "context": "\"这位女性医生正在为男性患者仔细检查身体。\"", "item_index": 51, "issue": "边界错误"}, {"entity_text": "", "entity_type": "性别", "context": "\"这位女性医生正在为男性患者仔细检查身体。\"", "item_index": 51, "issue": "边界错误"}, {"entity_text": "", "entity_type": "性别", "context": "张伟是一位男性工程师，负责公司新项目的开发工作。", "item_index": 52, "issue": "边界错误"}, {"entity_text": "", "entity_type": "性别", "context": "这位**男性**医生正在给**女性**病人做详细的身体检查。", "item_index": 53, "issue": "边界错误"}, {"entity_text": "", "entity_type": "性别", "context": "这位**男性**医生正在给**女性**病人做详细的身体检查。", "item_index": 53, "issue": "边界错误"}, {"entity_text": "", "entity_type": "性别", "context": "王明是一位男性工程师，他正在为公司的项目编写代码。", "item_index": 54, "issue": "边界错误"}, {"entity_text": "", "entity_type": "性别", "context": "\"这位女性医生正在为男性患者检查身体。\"", "item_index": 55, "issue": "边界错误"}, {"entity_text": "", "entity_type": "性别", "context": "\"这位女性医生正在为男性患者检查身体。\"", "item_index": 55, "issue": "边界错误"}, {"entity_text": "", "entity_type": "性别", "context": "李先生今天去参加了公司的年度男性健康检查活动。", "item_index": 56, "issue": "边界错误"}, {"entity_text": "", "entity_type": "国籍", "context": "日本游客在巴黎的街头拍摄了许多具有法国特色的风光照片。", "item_index": 57, "issue": "边界错误"}, {"entity_text": "", "entity_type": "国籍", "context": "日本游客在巴黎的街头拍摄了许多具有法国特色的风光照片。", "item_index": 57, "issue": "边界错误"}, {"entity_text": "", "entity_type": "国籍", "context": "来自中国的游客在日本的京都参观了古老的寺庙。", "item_index": 58, "issue": "边界错误"}, {"entity_text": "", "entity_type": "国籍", "context": "这位来自日本的游客在巴黎街头留下了深刻的印象。", "item_index": 59, "issue": "边界错误"}, {"entity_text": "", "entity_type": "国籍", "context": "来自日本的游客在故宫博物院参观时对中国的历史文物表现出浓厚兴趣。", "item_index": 60, "issue": "边界错误"}, {"entity_text": "", "entity_type": "国籍", "context": "美国留学生李华在巴黎遇到了一位法国朋友，他们用英语交流。", "item_index": 61, "issue": "边界错误"}, {"entity_text": "", "entity_type": "国籍", "context": "美国留学生李华在巴黎遇到了一位法国朋友，他们用英语交流。", "item_index": 61, "issue": "边界错误"}, {"entity_text": "", "entity_type": "国籍", "context": "那位来自日本的游客在故宫博物院仔细参观了古代文物。", "item_index": 62, "issue": "边界错误"}, {"entity_text": "", "entity_type": "职业", "context": "这位经验丰富的建筑师为整个社区设计了现代化的住宅楼。", "item_index": 63, "issue": "边界错误"}, {"entity_text": "", "entity_type": "职业", "context": "这位牙科医生正在仔细检查病人的牙齿健康状况。", "item_index": 64, "issue": "边界错误"}, {"entity_text": "", "entity_type": "民族", "context": "在云南的节日庆典上，傣族和彝族的人们穿着传统服饰载歌载舞。", "item_index": 65, "issue": "边界错误"}, {"entity_text": "", "entity_type": "民族", "context": "在云南的节日庆典上，傣族和彝族的人们穿着传统服饰载歌载舞。", "item_index": 65, "issue": "边界错误"}, {"entity_text": "", "entity_type": "民族", "context": "在云南的节日庆典上，傣族和彝族的人们穿着传统服饰载歌载舞。", "item_index": 66, "issue": "边界错误"}, {"entity_text": "", "entity_type": "民族", "context": "在云南的节日庆典上，傣族和彝族的人们穿着传统服饰载歌载舞。", "item_index": 66, "issue": "边界错误"}, {"entity_text": "", "entity_type": "民族", "context": "藏族和蒙古族在传统节日中会展示各自独特的服饰和舞蹈。", "item_index": 67, "issue": "边界错误"}, {"entity_text": "", "entity_type": "民族", "context": "藏族和蒙古族在传统节日中会展示各自独特的服饰和舞蹈。", "item_index": 67, "issue": "边界错误"}, {"entity_text": "", "entity_type": "民族", "context": "藏族和蒙古族的学生在学校的民族文化节上展示了各自的服饰和舞蹈。", "item_index": 68, "issue": "边界错误"}, {"entity_text": "", "entity_type": "民族", "context": "藏族和蒙古族的学生在学校的民族文化节上展示了各自的服饰和舞蹈。", "item_index": 68, "issue": "边界错误"}, {"entity_text": "", "entity_type": "民族", "context": "在云南的节日庆典上，傣族和纳西族的人们穿着传统服饰载歌载舞。", "item_index": 69, "issue": "边界错误"}, {"entity_text": "", "entity_type": "民族", "context": "在云南的节日庆典上，傣族和纳西族的人们穿着传统服饰载歌载舞。", "item_index": 69, "issue": "边界错误"}, {"entity_text": "", "entity_type": "民族", "context": "在云南的节日庆典上，傣族和纳西族的人们穿着传统服饰载歌载舞。", "item_index": 70, "issue": "边界错误"}, {"entity_text": "", "entity_type": "民族", "context": "在云南的节日庆典上，傣族和纳西族的人们穿着传统服饰载歌载舞。", "item_index": 70, "issue": "边界错误"}, {"entity_text": "", "entity_type": "民族", "context": "在云南的傣族村寨里，村民们正忙着准备传统的泼水节活动。", "item_index": 71, "issue": "边界错误"}, {"entity_text": "", "entity_type": "民族", "context": "在云南的节日庆典上，彝族和傣族的歌舞表演吸引了众多游客。", "item_index": 72, "issue": "边界错误"}, {"entity_text": "", "entity_type": "民族", "context": "在云南的节日庆典上，彝族和傣族的歌舞表演吸引了众多游客。", "item_index": 72, "issue": "边界错误"}, {"entity_text": "", "entity_type": "民族", "context": "藏族同胞在高原上热情地跳起了传统的锅庄舞。", "item_index": 73, "issue": "边界错误"}, {"entity_text": "", "entity_type": "教育背景", "context": "李明毕业于北京大学计算机科学与技术专业，获得了工学硕士学位。", "item_index": 74, "issue": "边界错误"}, {"entity_text": "", "entity_type": "教育背景", "context": "李明毕业于北京大学计算机科学与技术专业，获得了工学硕士学位。", "item_index": 74, "issue": "边界错误"}, {"entity_text": "", "entity_type": "教育背景", "context": "张华拥有北京大学计算机科学与技术专业的学士学位。", "item_index": 75, "issue": "边界错误"}, {"entity_text": "", "entity_type": "教育背景", "context": "李华拥有北京大学计算机科学与技术专业的博士学位。", "item_index": 76, "issue": "边界错误"}, {"entity_text": "", "entity_type": "教育背景", "context": "李华拥有北京大学计算机科学与技术专业的博士学位。", "item_index": 76, "issue": "边界错误"}, {"entity_text": "", "entity_type": "教育背景", "context": "李华拥有北京大学计算机科学与技术专业的博士学位。", "item_index": 76, "issue": "边界错误"}, {"entity_text": "", "entity_type": "教育背景", "context": "张华拥有北京大学计算机科学与技术专业的博士学位，目前正在申请教授职位。", "item_index": 77, "issue": "边界错误"}, {"entity_text": "", "entity_type": "教育背景", "context": "李华拥有北京大学计算机科学与技术专业的博士学位，目前在该公司担任首席技术官。", "item_index": 78, "issue": "边界错误"}, {"entity_text": "", "entity_type": "教育背景", "context": "张华拥有北京大学计算机科学与技术专业的硕士学位。", "item_index": 79, "issue": "边界错误"}, {"entity_text": "", "entity_type": "教育背景", "context": "李华拥有北京大学计算机科学专业的硕士学位，这为他的职业发展奠定了坚实基础。", "item_index": 80, "issue": "边界错误"}, {"entity_text": "", "entity_type": "婚姻状况", "context": "李明目前处于离异状态，他计划下周去民政局办理复婚手续。", "item_index": 81, "issue": "边界错误"}, {"entity_text": "", "entity_type": "婚姻状况", "context": "张女士的婚姻状况是已婚，并且已经育有两个孩子。", "item_index": 82, "issue": "边界错误"}, {"entity_text": "", "entity_type": "婚姻状况", "context": "王女士的婚姻状况是已婚，她与丈夫已经共同生活了十年。", "item_index": 83, "issue": "边界错误"}, {"entity_text": "", "entity_type": "婚姻状况", "context": "张三的婚姻状况是已婚，他和李四已经共同生活了十年。", "item_index": 84, "issue": "边界错误"}, {"entity_text": "", "entity_type": "婚姻状况", "context": "张三的婚姻状况是已婚，他和妻子已经共同生活了十年。", "item_index": 85, "issue": "边界错误"}, {"entity_text": "", "entity_type": "婚姻状况", "context": "张三的婚姻状况是已婚，他与妻子李四已经共同生活了十年。", "item_index": 86, "issue": "边界错误"}, {"entity_text": "", "entity_type": "婚姻状况", "context": "张先生的婚姻状况是已婚，他和妻子已经共同生活了十年。", "item_index": 87, "issue": "边界错误"}, {"entity_text": "", "entity_type": "婚姻状况", "context": "张先生的婚姻状况是已婚，他和妻子已经共同生活了十年。", "item_index": 88, "issue": "边界错误"}, {"entity_text": "", "entity_type": "婚姻状况", "context": "李女士的婚姻状况是离异，她现在独自抚养两个孩子。", "item_index": 89, "issue": "边界错误"}, {"entity_text": "", "entity_type": "婚姻状况", "context": "李明目前处于已婚状态，他和妻子已经共同生活了十年。", "item_index": 90, "issue": "边界错误"}, {"entity_text": "", "entity_type": "政治倾向", "context": "这位支持自由主义政治理念的代表在会议上发表了重要讲话。", "item_index": 91, "issue": "边界错误"}, {"entity_text": "", "entity_type": "政治倾向", "context": "这位选民坚定支持自由主义，认为政府应该减少对经济的干预。", "item_index": 92, "issue": "边界错误"}, {"entity_text": "", "entity_type": "政治倾向", "context": "这位支持者明确表示自己属于美国民主党的自由派阵营。", "item_index": 93, "issue": "边界错误"}, {"entity_text": "", "entity_type": "政治倾向", "context": "这位支持者明确表示自己属于美国民主党的自由派阵营。", "item_index": 93, "issue": "边界错误"}, {"entity_text": "", "entity_type": "政治倾向", "context": "这位自由主义者认为，政府应该减少对经济的干预。", "item_index": 94, "issue": "边界错误"}, {"entity_text": "", "entity_type": "政治倾向", "context": "这位自由主义者支持渐进式改革，反对激进的社会变革。", "item_index": 95, "issue": "边界错误"}, {"entity_text": "", "entity_type": "政治倾向", "context": "作为一名坚定的共产主义信徒，他始终拥护中国共产党领导。", "item_index": 96, "issue": "边界错误"}, {"entity_text": "", "entity_type": "政治倾向", "context": "作为一名坚定的共产主义信徒，他始终拥护中国共产党领导。", "item_index": 96, "issue": "边界错误"}, {"entity_text": "", "entity_type": "政治倾向", "context": "作为一名坚定的社会主义者，他在选举中始终支持中国共产党。", "item_index": 97, "issue": "边界错误"}, {"entity_text": "", "entity_type": "政治倾向", "context": "作为一名坚定的社会主义者，他在选举中始终支持中国共产党。", "item_index": 97, "issue": "边界错误"}, {"entity_text": "", "entity_type": "政治倾向", "context": "\"这位学者长期关注自由主义思潮在当代社会的影响。\"", "item_index": 98, "issue": "边界错误"}, {"entity_text": "", "entity_type": "政治倾向", "context": "\"作为一名坚定的社会主义者，他始终支持中国共产党的领导。\"", "item_index": 99, "issue": "边界错误"}, {"entity_text": "", "entity_type": "政治倾向", "context": "\"作为一名坚定的社会主义者，他始终支持中国共产党的领导。\"", "item_index": 99, "issue": "边界错误"}, {"entity_text": "", "entity_type": "政治倾向", "context": "\"作为一名坚定的共产主义党员，他始终坚守无产阶级革命立场。\"", "item_index": 100, "issue": "边界错误"}, {"entity_text": "", "entity_type": "政治倾向", "context": "\"作为一名坚定的共产主义党员，他始终坚守无产阶级革命立场。\"", "item_index": 100, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "张妈妈带着李爸爸和他们的儿子小明一起去公园散步。", "item_index": 101, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "张妈妈带着李爸爸和他们的儿子小明一起去公园散步。", "item_index": 101, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "张妈妈带着李爸爸和他们的儿子小明一起去公园散步。", "item_index": 101, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我的爸爸张伟和妈妈李娜周末会带我去外婆王芳家吃饭。", "item_index": 102, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我的爸爸张伟和妈妈李娜周末会带我去外婆王芳家吃饭。", "item_index": 102, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我的爸爸张伟和妈妈李娜周末会带我去外婆王芳家吃饭。", "item_index": 102, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我的爸爸张伟和妈妈李娜周末会带我去外婆王芳家吃饭。", "item_index": 102, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我的爸爸张伟和妈妈李娜周末会带我去外婆王芳家吃饭。", "item_index": 102, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我的爸爸张伟和妈妈李娜周末会带我去外婆王芳家吃饭。", "item_index": 102, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我的爸爸张伟和妈妈李娜周末会带我去外婆王芳家吃饭。", "item_index": 102, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我妈妈张丽昨天给我打电话，让我周末去姥姥李芳家吃饭。", "item_index": 103, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我妈妈张丽昨天给我打电话，让我周末去姥姥李芳家吃饭。", "item_index": 103, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我妈妈张丽昨天给我打电话，让我周末去姥姥李芳家吃饭。", "item_index": 103, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "妈妈和爸爸带着我、哥哥还有弟弟一起去奶奶家过周末。", "item_index": 104, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "妈妈和爸爸带着我、哥哥还有弟弟一起去奶奶家过周末。", "item_index": 104, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "妈妈和爸爸带着我、哥哥还有弟弟一起去奶奶家过周末。", "item_index": 104, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "妈妈和爸爸带着我、哥哥还有弟弟一起去奶奶家过周末。", "item_index": 104, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "妈妈和爸爸带着我、哥哥还有弟弟一起去奶奶家过周末。", "item_index": 104, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "妈妈和爸爸带着我、哥哥还有弟弟一起去奶奶家过周末。", "item_index": 104, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我爸爸张伟和妈妈李娜明天要带我妹妹小芳一起去公园玩。", "item_index": 105, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我爸爸张伟和妈妈李娜明天要带我妹妹小芳一起去公园玩。", "item_index": 105, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我爸爸张伟和妈妈李娜明天要带我妹妹小芳一起去公园玩。", "item_index": 105, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我妈妈张丽昨天带着我的儿子小明去公园玩了整整一下午。", "item_index": 106, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我妈妈张丽昨天带着我的儿子小明去公园玩了整整一下午。", "item_index": 106, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我的爸爸张伟和妈妈李娜明天要带妹妹小芳一起去公园玩。", "item_index": 107, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我的爸爸张伟和妈妈李娜明天要带妹妹小芳一起去公园玩。", "item_index": 107, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我的爸爸张伟和妈妈李娜明天要带妹妹小芳一起去公园玩。", "item_index": 107, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我的哥哥张伟明天要带他的女儿小雅去游乐园玩。", "item_index": 108, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我的哥哥张伟明天要带他的女儿小雅去游乐园玩。", "item_index": 108, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我弟弟张明今天下午要去接妹妹李婷放学回家。", "item_index": 109, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我弟弟张明今天下午要去接妹妹李婷放学回家。", "item_index": 109, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我的妈妈李女士和爸爸王先生一起去参加了奶奶张女士的七十岁生日宴会。", "item_index": 110, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我的妈妈李女士和爸爸王先生一起去参加了奶奶张女士的七十岁生日宴会。", "item_index": 110, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我的妈妈李女士和爸爸王先生一起去参加了奶奶张女士的七十岁生日宴会。", "item_index": 110, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我的妈妈李女士和爸爸王先生一起去参加了奶奶张女士的七十岁生日宴会。", "item_index": 110, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我的妈妈李女士和爸爸王先生一起去参加了奶奶张女士的七十岁生日宴会。", "item_index": 110, "issue": "边界错误"}, {"entity_text": "", "entity_type": "家庭成员", "context": "我的妈妈李女士和爸爸王先生一起去参加了奶奶张女士的七十岁生日宴会。", "item_index": 110, "issue": "边界错误"}, {"entity_text": "", "entity_type": "工资数额", "context": "这家公司的月薪标准是8000元，比同行业平均水平要高一些。", "item_index": 111, "issue": "边界错误"}, {"entity_text": "", "entity_type": "工资数额", "context": "小王的月薪是8000元，比他预期的7000元高出不少。", "item_index": 112, "issue": "边界错误"}, {"entity_text": "", "entity_type": "工资数额", "context": "小王的月薪是8000元，比他预期的7000元高出不少。", "item_index": 112, "issue": "边界错误"}, {"entity_text": "", "entity_type": "工资数额", "context": "这家公司的月薪是12,500元，比同行业平均水平要高一些。", "item_index": 113, "issue": "边界错误"}, {"entity_text": "", "entity_type": "工资数额", "context": "这家公司的月工资标准是8000元，比同行业平均水平高了不少。", "item_index": 114, "issue": "边界错误"}, {"entity_text": "", "entity_type": "工资数额", "context": "这家公司的月工资是8,500元，比同行业平均水平要高一些。", "item_index": 115, "issue": "边界错误"}, {"entity_text": "", "entity_type": "工资数额", "context": "这家公司的月薪标准是8000元，比同行业高出不少。", "item_index": 116, "issue": "边界错误"}, {"entity_text": "", "entity_type": "工资数额", "context": "小王的月工资是8500元，比上个月多了500元。", "item_index": 117, "issue": "边界错误"}, {"entity_text": "", "entity_type": "工资数额", "context": "小王的月工资是8500元，比上个月多了500元。", "item_index": 117, "issue": "边界错误"}, {"entity_text": "", "entity_type": "工资数额", "context": "张先生上个月的工资是8,500元，比预期的要高一些。", "item_index": 118, "issue": "边界错误"}, {"entity_text": "", "entity_type": "工资数额", "context": "这家公司的月工资标准是8500元，比同行业平均水平要高一些。", "item_index": 119, "issue": "边界错误"}, {"entity_text": "", "entity_type": "工资数额", "context": "这家公司的月薪标准是8000元，比同行业平均水平高出不少。", "item_index": 120, "issue": "边界错误"}, {"entity_text": "", "entity_type": "投资产品", "context": "我决定将一部分资金投资于华夏成长混合型证券投资基金。", "item_index": 121, "issue": "边界错误"}, {"entity_text": "", "entity_type": "投资产品", "context": "我最近购买了招商银行发行的招商中证白酒指数基金。", "item_index": 122, "issue": "边界错误"}, {"entity_text": "", "entity_type": "投资产品", "context": "我决定将部分资金投资于华夏基金旗下的华夏成长混合型证券投资基金。", "item_index": 123, "issue": "边界错误"}, {"entity_text": "", "entity_type": "投资产品", "context": "\"我决定将部分资金投资于华夏成长混合型证券投资基金。\"", "item_index": 124, "issue": "边界错误"}, {"entity_text": "", "entity_type": "投资产品", "context": "\"客户选择将部分资金配置到华夏成长混合型基金和招商中证白酒指数基金。\"", "item_index": 125, "issue": "边界错误"}, {"entity_text": "", "entity_type": "投资产品", "context": "\"客户选择将部分资金配置到华夏成长混合型基金和招商中证白酒指数基金。\"", "item_index": 125, "issue": "边界错误"}, {"entity_text": "", "entity_type": "投资产品", "context": "我选择将部分资金投资于华夏成长混合型证券投资基金。", "item_index": 126, "issue": "边界错误"}, {"entity_text": "", "entity_type": "投资产品", "context": "\"我决定将一部分资金投资于华夏成长混合型证券投资基金。\"", "item_index": 127, "issue": "边界错误"}, {"entity_text": "", "entity_type": "投资产品", "context": "\"我决定将部分资金投资于华夏基金旗下的华夏回报混合型基金。\"", "item_index": 128, "issue": "边界错误"}, {"entity_text": "", "entity_type": "投资产品", "context": "我决定将部分资金投资于华夏基金旗下的华夏回报混合型证券投资基金。", "item_index": 129, "issue": "边界错误"}, {"entity_text": "", "entity_type": "投资产品", "context": "“我决定将部分资金投资于招商银行发行的招商中证白酒指数基金。”", "item_index": 130, "issue": "边界错误"}, {"entity_text": "", "entity_type": "税务记录", "context": "我在2023年度个人所得税纳税申报表中记录了工资收入总额为12万元。", "item_index": 131, "issue": "边界错误"}, {"entity_text": "", "entity_type": "税务记录", "context": "我在2023年度个人所得税纳税申报表中记录了工资收入总额为12万元。", "item_index": 131, "issue": "边界错误"}, {"entity_text": "", "entity_type": "税务记录", "context": "我在2023年个人所得税年度汇算清缴中提交了工资薪金所得记录。", "item_index": 132, "issue": "边界错误"}, {"entity_text": "", "entity_type": "税务记录", "context": "我在2023年个人所得税年度汇算清缴中提交了工资薪金所得记录。", "item_index": 132, "issue": "边界错误"}, {"entity_text": "", "entity_type": "税务记录", "context": "我在2022年个人所得税年度汇算清缴时提交了工资薪金所得记录。", "item_index": 133, "issue": "边界错误"}, {"entity_text": "", "entity_type": "税务记录", "context": "我在2022年个人所得税年度汇算清缴时提交了工资薪金所得记录。", "item_index": 133, "issue": "边界错误"}, {"entity_text": "", "entity_type": "税务记录", "context": "张先生提交了2023年度的个人所得税年度汇算清缴申报表，并附上了工资收入明细表。", "item_index": 134, "issue": "边界错误"}, {"entity_text": "", "entity_type": "税务记录", "context": "张先生提交了2023年度的个人所得税年度汇算清缴申报表，并附上了工资收入明细表。", "item_index": 134, "issue": "边界错误"}, {"entity_text": "", "entity_type": "税务记录", "context": "请查看您的个人税务记录，包括个人所得税缴纳证明（2023年）和增值税发票（20231115）。", "item_index": 135, "issue": "边界错误"}, {"entity_text": "", "entity_type": "税务记录", "context": "请查看您的个人税务记录，包括个人所得税缴纳证明（2023年）和增值税发票（20231115）。", "item_index": 135, "issue": "边界错误"}, {"entity_text": "", "entity_type": "税务记录", "context": "我需要查看2021年度个人所得税纳税申报表和增值税专用发票存根联。", "item_index": 136, "issue": "边界错误"}, {"entity_text": "", "entity_type": "税务记录", "context": "我需要查看2021年度个人所得税纳税申报表和增值税专用发票存根联。", "item_index": 136, "issue": "边界错误"}, {"entity_text": "", "entity_type": "税务记录", "context": "公司的增值税专用发票编号为NO.123456789，请查收。", "item_index": 137, "issue": "边界错误"}, {"entity_text": "", "entity_type": "税务记录", "context": "公司提交了2023年度企业所得税年度汇算清缴纳税申报表。", "item_index": 138, "issue": "边界错误"}, {"entity_text": "", "entity_type": "税务记录", "context": "请提供我司2023年第三季度的增值税专用发票记录，谢谢。", "item_index": 139, "issue": "边界错误"}, {"entity_text": "", "entity_type": "税务记录", "context": "公司提交了2023年度企业所得税纳税申报表，并附带了增值税专用发票。", "item_index": 140, "issue": "边界错误"}, {"entity_text": "", "entity_type": "税务记录", "context": "公司提交了2023年度企业所得税纳税申报表，并附带了增值税专用发票。", "item_index": 140, "issue": "边界错误"}, {"entity_text": "", "entity_type": "信用记录", "context": "张先生查询了自己的中国人民银行征信中心个人信用报告，发现信用评分达到了750分。", "item_index": 141, "issue": "边界错误"}, {"entity_text": "", "entity_type": "信用记录", "context": "张先生查询了自己的个人征信报告，发现信用评分达到了760分。", "item_index": 142, "issue": "边界错误"}, {"entity_text": "", "entity_type": "信用记录", "context": "张先生查询了自己的个人征信报告，发现信用评分达到了760分。", "item_index": 142, "issue": "边界错误"}, {"entity_text": "", "entity_type": "信用记录", "context": "王先生的个人征信报告显示，他的信用卡还款记录为\"良好\"，贷款逾期次数为\"0次\"。", "item_index": 143, "issue": "边界错误"}, {"entity_text": "", "entity_type": "信用记录", "context": "王先生的个人征信报告显示，他的信用卡还款记录为\"良好\"，贷款逾期次数为\"0次\"。", "item_index": 143, "issue": "边界错误"}, {"entity_text": "", "entity_type": "信用记录", "context": "小张的信用卡还款记录显示，他的招商银行信用卡账户在2023年11月已全额还款。", "item_index": 144, "issue": "边界错误"}, {"entity_text": "", "entity_type": "信用记录", "context": "小张的信用卡还款记录显示，他的招商银行信用卡账户在2023年11月已全额还款。", "item_index": 144, "issue": "边界错误"}, {"entity_text": "", "entity_type": "信用记录", "context": "张先生在央行征信系统中查询了自己的个人信用报告，发现其中包含一份名为“房贷还款记录”的信用记录。", "item_index": 145, "issue": "边界错误"}, {"entity_text": "", "entity_type": "信用记录", "context": "张先生在查询个人信用报告时，发现他的信用卡逾期记录显示为“已结清”。", "item_index": 146, "issue": "边界错误"}, {"entity_text": "", "entity_type": "信用记录", "context": "张先生在查询个人信用报告时，发现他的信用卡逾期记录显示为“已结清”。", "item_index": 146, "issue": "边界错误"}, {"entity_text": "", "entity_type": "信用记录", "context": "张先生的个人征信报告显示，他的信用卡还款记录为“正常”，贷款逾期记录为“无”。", "item_index": 147, "issue": "边界错误"}, {"entity_text": "", "entity_type": "信用记录", "context": "张先生的个人征信报告显示，他的信用卡还款记录为“正常”，贷款逾期记录为“无”。", "item_index": 147, "issue": "边界错误"}, {"entity_text": "", "entity_type": "信用记录", "context": "根据中国人民银行征信中心的报告，他的个人信用报告显示当前无逾期记录。", "item_index": 148, "issue": "边界错误"}, {"entity_text": "", "entity_type": "信用记录", "context": "根据您的个人信用记录类型——\"按时还款记录\"，银行批准了您的贷款申请。", "item_index": 149, "issue": "边界错误"}, {"entity_text": "", "entity_type": "信用记录", "context": "小明查看了自己的中国人民银行征信报告，发现信用评分达到了750分。", "item_index": 150, "issue": "边界错误"}, {"entity_text": "", "entity_type": "信用记录", "context": "小明查看了自己的中国人民银行征信报告，发现信用评分达到了750分。", "item_index": 150, "issue": "边界错误"}, {"entity_text": "", "entity_type": "实体资产", "context": "这家公司的主要资产包括一栋位于北京朝阳区国贸CBD的写字楼和一辆奔驰S级轿车。", "item_index": 151, "issue": "边界错误"}, {"entity_text": "", "entity_type": "实体资产", "context": "这家公司的主要资产包括一栋位于北京朝阳区国贸CBD的写字楼和一辆奔驰S级轿车。", "item_index": 151, "issue": "边界错误"}, {"entity_text": "", "entity_type": "实体资产", "context": "这台工厂里的卡特彼勒D6R推土机需要定期进行维护保养。", "item_index": 152, "issue": "边界错误"}, {"entity_text": "", "entity_type": "实体资产", "context": "这家公司的仓库里存放着大量的黄金、原油和房地产。", "item_index": 153, "issue": "边界错误"}, {"entity_text": "", "entity_type": "实体资产", "context": "这家公司的仓库里存放着大量的黄金、原油和房地产。", "item_index": 153, "issue": "边界错误"}, {"entity_text": "", "entity_type": "实体资产", "context": "这家公司的仓库里存放着大量的黄金、原油和房地产。", "item_index": 153, "issue": "边界错误"}, {"entity_text": "", "entity_type": "实体资产", "context": "这家公司拥有两栋位于北京市朝阳区CBD的甲级写字楼和一辆奔驰S级轿车作为实体资产。", "item_index": 154, "issue": "边界错误"}, {"entity_text": "", "entity_type": "实体资产", "context": "这家公司拥有两栋位于北京市朝阳区CBD的甲级写字楼和一辆奔驰S级轿车作为实体资产。", "item_index": 154, "issue": "边界错误"}, {"entity_text": "", "entity_type": "实体资产", "context": "这家公司的主要实体资产包括一栋位于北京市朝阳区建国路88号的写字楼和一架波音737-800飞机。", "item_index": 155, "issue": "边界错误"}, {"entity_text": "", "entity_type": "实体资产", "context": "这家公司的主要实体资产包括一栋位于北京市朝阳区建国路88号的写字楼和一架波音737-800飞机。", "item_index": 155, "issue": "边界错误"}, {"entity_text": "", "entity_type": "实体资产", "context": "这家公司的主要资产包括位于上海市浦东新区的两栋写字楼和一架波音737飞机。", "item_index": 156, "issue": "边界错误"}, {"entity_text": "", "entity_type": "实体资产", "context": "这家公司的主要资产包括位于上海市浦东新区的两栋写字楼和一架波音737飞机。", "item_index": 156, "issue": "边界错误"}, {"entity_text": "", "entity_type": "实体资产", "context": "这家公司的仓库里存放着大量的集装箱、挖掘机和叉车等实体资产。", "item_index": 157, "issue": "边界错误"}, {"entity_text": "", "entity_type": "实体资产", "context": "这家公司的仓库里存放着大量的集装箱、挖掘机和叉车等实体资产。", "item_index": 157, "issue": "边界错误"}, {"entity_text": "", "entity_type": "实体资产", "context": "这家公司的仓库里存放着大量的集装箱、挖掘机和叉车等实体资产。", "item_index": 157, "issue": "边界错误"}, {"entity_text": "", "entity_type": "实体资产", "context": "这台设备是一台苹果MacBook Pro笔记本电脑，目前状态良好。", "item_index": 158, "issue": "边界错误"}, {"entity_text": "", "entity_type": "实体资产", "context": "这家公司拥有多台价值超过5000万元的挖掘机作为实体资产。", "item_index": 159, "issue": "边界错误"}, {"entity_text": "", "entity_type": "实体资产", "context": "这家公司的主要实体资产包括两栋位于北京市朝阳区建国路的写字楼和一辆奔驰S级轿车。", "item_index": 160, "issue": "边界错误"}, {"entity_text": "", "entity_type": "实体资产", "context": "这家公司的主要实体资产包括两栋位于北京市朝阳区建国路的写字楼和一辆奔驰S级轿车。", "item_index": 160, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生于2023年10月15日通过招商银行向李女士转账了5000元人民币。", "item_index": 161, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生于2023年10月15日通过招商银行向李女士转账了5000元人民币。", "item_index": 161, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生于2023年10月15日通过招商银行向李女士转账了5000元人民币。", "item_index": 161, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生于2023年10月15日通过招商银行向李女士转账了5000元人民币。", "item_index": 161, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生于2023年10月15日通过招商银行向李女士转账了5000元人民币。", "item_index": 161, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过招商银行转账给李女士，交易金额为￥3,500.00，交易时间为2023-11-15 14:30。", "item_index": 162, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过招商银行转账给李女士，交易金额为￥3,500.00，交易时间为2023-11-15 14:30。", "item_index": 162, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过招商银行转账给李女士，交易金额为￥3,500.00，交易时间为2023-11-15 14:30。", "item_index": 162, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过支付宝向李女士转账了500元，交易时间为2023年10月15日15:30。", "item_index": 163, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过支付宝向李女士转账了500元，交易时间为2023年10月15日15:30。", "item_index": 163, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过支付宝向李女士转账了500元，交易时间为2023年10月15日15:30。", "item_index": 163, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过支付宝向李女士转账了500元，交易时间为2023年10月15日15:30。", "item_index": 164, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过支付宝向李女士转账了500元，交易时间为2023年10月15日15:30。", "item_index": 164, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过支付宝向李女士转账了500元，交易时间为2023年10月15日15:30。", "item_index": 164, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过支付宝向李女士转账了500元，交易时间为2023年10月15日15:30。", "item_index": 164, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过支付宝转账给李女士1000元，交易时间为2023年10月15日15:30。", "item_index": 165, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过支付宝转账给李女士1000元，交易时间为2023年10月15日15:30。", "item_index": 165, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过支付宝转账给李女士1000元，交易时间为2023年10月15日15:30。", "item_index": 165, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过招商银行信用卡支付了订单编号20230001的款项，金额为2999元。", "item_index": 166, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过招商银行信用卡支付了订单编号20230001的款项，金额为2999元。", "item_index": 166, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过招商银行信用卡支付了订单编号20230001的款项，金额为2999元。", "item_index": 166, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过工商银行转账支付了2023年12月5日的订单，金额为￥1,280.50。", "item_index": 167, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过工商银行转账支付了2023年12月5日的订单，金额为￥1,280.50。", "item_index": 167, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过工商银行转账支付了2023年12月5日的订单，金额为￥1,280.50。", "item_index": 167, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过工商银行向李女士转账了5000元，交易时间为2023年10月15日。", "item_index": 168, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过工商银行向李女士转账了5000元，交易时间为2023年10月15日。", "item_index": 168, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过工商银行向李女士转账了5000元，交易时间为2023年10月15日。", "item_index": 168, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过工商银行向李女士转账了5000元，交易时间为2023年10月15日。", "item_index": 168, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过支付宝完成了从招商银行向建设银行的转账，金额为5000元。", "item_index": 169, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过支付宝完成了从招商银行向建设银行的转账，金额为5000元。", "item_index": 169, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过支付宝完成了从招商银行向建设银行的转账，金额为5000元。", "item_index": 169, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过支付宝完成了从招商银行向建设银行的转账，金额为5000元。", "item_index": 169, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过招商银行信用卡支付了订单号为20230515的购物款项。", "item_index": 170, "issue": "边界错误"}, {"entity_text": "", "entity_type": "交易信息", "context": "张先生通过招商银行信用卡支付了订单号为20230515的购物款项。", "item_index": 170, "issue": "边界错误"}, {"entity_text": "", "entity_type": "疾病", "context": "这位患者被诊断出患有糖尿病，需要长期控制血糖水平。", "item_index": 171, "issue": "边界错误"}, {"entity_text": "", "entity_type": "疾病", "context": "这位患者被诊断出患有糖尿病，需要定期监测血糖水平。", "item_index": 172, "issue": "边界错误"}, {"entity_text": "", "entity_type": "疾病", "context": "这位患者被诊断患有糖尿病，需要定期监测血糖水平。", "item_index": 173, "issue": "边界错误"}, {"entity_text": "", "entity_type": "药物", "context": "阿司匹林是一种常见的解热镇痛药，常用于缓解轻至中度疼痛。", "item_index": 174, "issue": "边界错误"}, {"entity_text": "", "entity_type": "药物", "context": "这位高血压患者每天按时服用硝苯地平缓释片来控制血压。", "item_index": 175, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "患者出现了持续性咳嗽、胸闷和呼吸困难等症状，需要进一步检查。", "item_index": 176, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "患者出现了持续性咳嗽、胸闷和呼吸困难等症状，需要进一步检查。", "item_index": 176, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "患者出现了持续性咳嗽、胸闷和呼吸困难等症状，需要进一步检查。", "item_index": 176, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "这位患者出现了明显的呼吸困难、胸痛和持续性咳嗽的症状。", "item_index": 177, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "这位患者出现了明显的呼吸困难、胸痛和持续性咳嗽的症状。", "item_index": 177, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "这位患者出现了明显的呼吸困难、胸痛和持续性咳嗽的症状。", "item_index": 177, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "患者出现了明显的胸痛、呼吸困难以及持续性的咳嗽症状。", "item_index": 178, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "患者出现了明显的胸痛、呼吸困难以及持续性的咳嗽症状。", "item_index": 178, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "患者出现了明显的胸痛、呼吸困难以及持续性的咳嗽症状。", "item_index": 178, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "这位患者出现了明显的发热、咳嗽和呼吸困难等症状。", "item_index": 179, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "这位患者出现了明显的发热、咳嗽和呼吸困难等症状。", "item_index": 179, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "这位患者出现了明显的发热、咳嗽和呼吸困难等症状。", "item_index": 179, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "这位患者的临床表现包括发热、咳嗽和呼吸困难。", "item_index": 180, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "这位患者的临床表现包括发热、咳嗽和呼吸困难。", "item_index": 180, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "这位患者的临床表现包括发热、咳嗽和呼吸困难。", "item_index": 180, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "患者出现了明显的皮疹、发热和关节疼痛等临床表现。", "item_index": 181, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "患者出现了明显的皮疹、发热和关节疼痛等临床表现。", "item_index": 181, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "患者出现了明显的皮疹、发热和关节疼痛等临床表现。", "item_index": 181, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "患者出现了明显的呼吸困难、咳嗽和发热症状，需要进一步检查。", "item_index": 182, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "患者出现了明显的呼吸困难、咳嗽和发热症状，需要进一步检查。", "item_index": 182, "issue": "边界错误"}, {"entity_text": "", "entity_type": "临床表现", "context": "患者出现了明显的呼吸困难、咳嗽和发热症状，需要进一步检查。", "item_index": 182, "issue": "边界错误"}, {"entity_text": "", "entity_type": "医疗程序", "context": "医生建议患者进行结肠镜检查，以排除早期病变的可能性。", "item_index": 183, "issue": "边界错误"}, {"entity_text": "", "entity_type": "医疗程序", "context": "医生建议他进行冠状动脉造影检查，以明确诊断。", "item_index": 184, "issue": "边界错误"}, {"entity_text": "", "entity_type": "医疗程序", "context": "医生建议他进行胃镜检查，以明确消化道出血的原因。", "item_index": 185, "issue": "边界错误"}, {"entity_text": "", "entity_type": "医疗程序", "context": "医生建议他进行胃镜检查，以明确消化性溃疡的诊断。", "item_index": 186, "issue": "边界错误"}, {"entity_text": "", "entity_type": "医疗程序", "context": "患者需要接受冠状动脉造影检查以明确诊断。", "item_index": 187, "issue": "边界错误"}, {"entity_text": "", "entity_type": "医疗程序", "context": "医生建议患者进行冠状动脉造影检查以明确诊断。", "item_index": 188, "issue": "边界错误"}, {"entity_text": "", "entity_type": "医疗程序", "context": "医生建议患者进行结肠镜检查以进一步明确诊断。", "item_index": 189, "issue": "边界错误"}, {"entity_text": "", "entity_type": "医疗程序", "context": "医生建议患者进行冠状动脉造影检查，以明确诊断。", "item_index": 190, "issue": "边界错误"}, {"entity_text": "", "entity_type": "医疗程序", "context": "医生建议他进行胃镜检查，以明确消化道出血的原因。", "item_index": 191, "issue": "边界错误"}, {"entity_text": "", "entity_type": "过敏信息", "context": "小明对花生酱过敏，每次吃后都会出现严重的皮肤瘙痒症状。", "item_index": 192, "issue": "边界错误"}, {"entity_text": "", "entity_type": "过敏信息", "context": "小明对花生酱过敏，每次吃后都会出现严重的皮肤瘙痒症状。", "item_index": 192, "issue": "边界错误"}, {"entity_text": "", "entity_type": "过敏信息", "context": "小明对花生酱和牛奶过敏，每次吃后都会出现皮肤红肿的症状。", "item_index": 193, "issue": "边界错误"}, {"entity_text": "", "entity_type": "过敏信息", "context": "小明对花生酱和牛奶过敏，每次吃后都会出现皮肤红肿的症状。", "item_index": 193, "issue": "边界错误"}, {"entity_text": "", "entity_type": "过敏信息", "context": "小明对花生过敏，每次吃花生酱都会引发严重的皮肤瘙痒。", "item_index": 194, "issue": "边界错误"}, {"entity_text": "", "entity_type": "过敏信息", "context": "小明对花生过敏，每次吃花生酱都会引发严重的皮肤瘙痒。", "item_index": 194, "issue": "边界错误"}, {"entity_text": "", "entity_type": "过敏信息", "context": "小明对花生过敏，每次吃到含有花生酱的食物都会出现皮肤瘙痒的症状。", "item_index": 195, "issue": "边界错误"}, {"entity_text": "", "entity_type": "过敏信息", "context": "小明对花生过敏，每次吃到含有花生酱的食物都会出现皮肤瘙痒的症状。", "item_index": 195, "issue": "边界错误"}, {"entity_text": "", "entity_type": "过敏信息", "context": "小明对花生过敏，每次吃含花生酱的食物都会出现严重反应。", "item_index": 196, "issue": "边界错误"}, {"entity_text": "", "entity_type": "过敏信息", "context": "小明对花生过敏，每次吃含花生酱的食物都会出现严重反应。", "item_index": 196, "issue": "边界错误"}, {"entity_text": "", "entity_type": "过敏信息", "context": "小明对花生酱过敏，每次吃到都会引起严重的皮肤瘙痒。", "item_index": 197, "issue": "边界错误"}, {"entity_text": "", "entity_type": "过敏信息", "context": "小明对花生过敏，每次吃花生酱都会出现严重的皮肤瘙痒症状。", "item_index": 198, "issue": "边界错误"}, {"entity_text": "", "entity_type": "过敏信息", "context": "小明对花生过敏，每次吃花生酱都会出现严重的皮肤瘙痒症状。", "item_index": 198, "issue": "边界错误"}, {"entity_text": "", "entity_type": "过敏信息", "context": "小明对花生酱过敏，每次吃都会出现严重的皮肤瘙痒症状。", "item_index": 199, "issue": "边界错误"}, {"entity_text": "", "entity_type": "过敏信息", "context": "小明对花生酱过敏，每次吃都会出现严重的皮肤瘙痒症状。", "item_index": 199, "issue": "边界错误"}, {"entity_text": "", "entity_type": "过敏信息", "context": "小明对花生过敏，每次吃花生酱都会引发严重的皮肤反应。", "item_index": 200, "issue": "边界错误"}, {"entity_text": "", "entity_type": "过敏信息", "context": "小明对花生过敏，每次吃花生酱都会引发严重的皮肤反应。", "item_index": 200, "issue": "边界错误"}, {"entity_text": "", "entity_type": "过敏信息", "context": "小明对花生过敏，每次吃坚果类食物前都会仔细检查成分表。", "item_index": 201, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士在2022年3月15日生产，新生儿体重为3.5千克，Apgar评分10分。", "item_index": 202, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士在2022年3月15日生产，新生儿体重为3.5千克，Apgar评分10分。", "item_index": 202, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士在2022年3月15日生产，新生儿体重为3.5千克，Apgar评分10分。", "item_index": 202, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士在2022年3月15日生产，新生儿体重为3.5千克，Apgar评分10分。", "item_index": 202, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士在2023年3月15日成功分娩，产下一名体重3.5公斤的女婴。", "item_index": 203, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士在2023年3月15日成功分娩，产下一名体重3.5公斤的女婴。", "item_index": 203, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士在2023年3月15日成功分娩，产下一名体重3.5公斤的女婴。", "item_index": 203, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "李女士的预产期是2024年6月15日，她计划在市妇幼保健院进行产前检查。", "item_index": 204, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "李女士的预产期是2024年6月15日，她计划在市妇幼保健院进行产前检查。", "item_index": 204, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士在2023年4月15日进行了产前检查，血压值为120/80mmHg。", "item_index": 205, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士的预产期是2024年5月15日，她的血型为O型。", "item_index": 206, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士的预产期是2024年5月15日，她的血型为O型。", "item_index": 206, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "这位孕妇的预产期是2024年5月15日，胎位为头位，胎心率为145次/分钟。", "item_index": 207, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "这位孕妇的预产期是2024年5月15日，胎位为头位，胎心率为145次/分钟。", "item_index": 207, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "这位孕妇的预产期是2024年5月15日，胎位为头位，胎心率为145次/分钟。", "item_index": 207, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士的预产期是2024年6月15日，她怀的是双胞胎，已孕周数为32周。", "item_index": 208, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士的预产期是2024年6月15日，她怀的是双胞胎，已孕周数为32周。", "item_index": 208, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士的预产期是2024年6月15日，她怀的是双胞胎，已孕周数为32周。", "item_index": 208, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士在2022年4月15日进行了剖腹产手术，产下一名体重3.5千克的男婴。", "item_index": 209, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士在2022年4月15日进行了剖腹产手术，产下一名体重3.5千克的男婴。", "item_index": 209, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士在2022年4月15日进行了剖腹产手术，产下一名体重3.5千克的男婴。", "item_index": 209, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士在2022年4月15日进行了剖腹产手术，产下一名体重3.5千克的男婴。", "item_index": 209, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士在2022年4月15日进行了剖腹产手术，产下一名体重3.5千克的男婴。", "item_index": 209, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士的预产期是2024年5月15日，她目前怀孕32周，血型为O型RH阳性。", "item_index": 210, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士的预产期是2024年5月15日，她目前怀孕32周，血型为O型RH阳性。", "item_index": 210, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士的预产期是2023年10月15日，她已怀孕34周。", "item_index": 211, "issue": "边界错误"}, {"entity_text": "", "entity_type": "生育信息", "context": "张女士的预产期是2023年10月15日，她已怀孕34周。", "item_index": 211, "issue": "边界错误"}, {"entity_text": "", "entity_type": "地理位置", "context": "上海外滩的夜景吸引了众多游客前来观赏。", "item_index": 212, "issue": "边界错误"}, {"entity_text": "", "entity_type": "地理位置", "context": "昨天我去了北京市海淀区中关村大街上的那家咖啡店，环境很不错。", "item_index": 213, "issue": "边界错误"}, {"entity_text": "", "entity_type": "地理位置", "context": "昨天我去了北京市海淀区中关村大街上的那家咖啡店，环境很不错。", "item_index": 213, "issue": "边界错误"}, {"entity_text": "", "entity_type": "地理位置", "context": "昨天我去了北京市海淀区中关村大街上的那家咖啡店，环境很不错。", "item_index": 213, "issue": "边界错误"}, {"entity_text": "", "entity_type": "地理位置", "context": "上海外滩的夜景吸引了众多游客前来观赏。", "item_index": 214, "issue": "边界错误"}, {"entity_text": "", "entity_type": "地理位置", "context": "上海外滩的夜景吸引了众多游客前来观赏。", "item_index": 214, "issue": "边界错误"}, {"entity_text": "", "entity_type": "地理位置", "context": "我计划这个周末去北京故宫博物院参观，顺便在附近的王府井大街逛逛。", "item_index": 215, "issue": "边界错误"}, {"entity_text": "", "entity_type": "地理位置", "context": "我计划这个周末去北京故宫博物院参观，顺便在附近的王府井大街逛逛。", "item_index": 215, "issue": "边界错误"}, {"entity_text": "", "entity_type": "地理位置", "context": "北京故宫的游客们沿着中轴线参观，惊叹于太和殿的宏伟建筑。", "item_index": 216, "issue": "边界错误"}, {"entity_text": "", "entity_type": "地理位置", "context": "北京故宫的游客们沿着中轴线参观，惊叹于太和殿的宏伟建筑。", "item_index": 216, "issue": "边界错误"}, {"entity_text": "", "entity_type": "地理位置", "context": "北京故宫的游客们沿着中轴线参观，惊叹于太和殿的宏伟建筑。", "item_index": 216, "issue": "边界错误"}, {"entity_text": "", "entity_type": "地理位置", "context": "昨天我去北京市朝阳区三里屯SOHO参加了一个科技展览。", "item_index": 217, "issue": "边界错误"}, {"entity_text": "", "entity_type": "地理位置", "context": "昨天我去北京市朝阳区三里屯SOHO参加了一个科技展览。", "item_index": 217, "issue": "边界错误"}, {"entity_text": "", "entity_type": "地理位置", "context": "昨天我去北京市朝阳区三里屯SOHO参加了一个科技展览。", "item_index": 217, "issue": "边界错误"}, {"entity_text": "", "entity_type": "地理位置", "context": "北京市的故宫博物院是中国最大的古代文化艺术博物馆。", "item_index": 218, "issue": "边界错误"}, {"entity_text": "", "entity_type": "地理位置", "context": "北京市的故宫博物院是中国最大的古代文化艺术博物馆。", "item_index": 218, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我的航班号是CA1234，从北京首都国际机场起飞，下午3点到达上海浦东国际机场。", "item_index": 219, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我的航班号是CA1234，从北京首都国际机场起飞，下午3点到达上海浦东国际机场。", "item_index": 219, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我的航班号是CA1234，从北京首都国际机场起飞，下午3点到达上海浦东国际机场。", "item_index": 219, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我的航班号是CA1234，从北京首都国际机场起飞，下午3点到达上海浦东国际机场。", "item_index": 219, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三上午9点从北京首都国际机场出发，前往上海浦东国际机场。", "item_index": 220, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三上午9点从北京首都国际机场出发，前往上海浦东国际机场。", "item_index": 220, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三上午9点从北京首都国际机场出发，前往上海浦东国际机场。", "item_index": 220, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三上午9点从北京首都国际机场出发，前往上海浦东国际机场。", "item_index": 221, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三上午9点从北京首都国际机场出发，前往上海浦东国际机场。", "item_index": 221, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三上午9点从北京首都国际机场出发，前往上海浦东国际机场。", "item_index": 221, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三从北京首都国际机场搭乘CA1234航班前往上海浦东国际机场。", "item_index": 222, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三从北京首都国际机场搭乘CA1234航班前往上海浦东国际机场。", "item_index": 222, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三从北京首都国际机场搭乘CA1234航班前往上海浦东国际机场。", "item_index": 222, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三从北京首都国际机场搭乘CA1234航班前往上海浦东国际机场。", "item_index": 222, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划明天上午9点从北京首都国际机场出发，前往上海浦东国际机场。", "item_index": 223, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划明天上午9点从北京首都国际机场出发，前往上海浦东国际机场。", "item_index": 223, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划明天上午9点从北京首都国际机场出发，前往上海浦东国际机场。", "item_index": 223, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三早上8:30从北京首都国际机场出发，前往上海浦东国际机场。", "item_index": 224, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三早上8:30从北京首都国际机场出发，前往上海浦东国际机场。", "item_index": 224, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三早上8:30从北京首都国际机场出发，前往上海浦东国际机场。", "item_index": 224, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我的航班将于2023年12月15日早上8:30从北京首都国际机场起飞。", "item_index": 225, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我的航班将于2023年12月15日早上8:30从北京首都国际机场起飞。", "item_index": 225, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我的航班将于2023年12月15日早上8:30从北京首都国际机场起飞。", "item_index": 225, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三上午9点从北京首都国际机场出发，飞往上海浦东国际机场。", "item_index": 226, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三上午9点从北京首都国际机场出发，飞往上海浦东国际机场。", "item_index": 226, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三上午9点从北京首都国际机场出发，飞往上海浦东国际机场。", "item_index": 226, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三上午9:30从北京首都国际机场搭乘CA1234航班前往上海浦东国际机场。", "item_index": 227, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三上午9:30从北京首都国际机场搭乘CA1234航班前往上海浦东国际机场。", "item_index": 227, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三上午9:30从北京首都国际机场搭乘CA1234航班前往上海浦东国际机场。", "item_index": 227, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我计划下周三上午9:30从北京首都国际机场搭乘CA1234航班前往上海浦东国际机场。", "item_index": 227, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我的航班号是CA1234，明天早上8:30从北京首都国际机场起飞。", "item_index": 228, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我的航班号是CA1234，明天早上8:30从北京首都国际机场起飞。", "item_index": 228, "issue": "边界错误"}, {"entity_text": "", "entity_type": "行程信息", "context": "我的航班号是CA1234，明天早上8:30从北京首都国际机场起飞。", "item_index": 228, "issue": "边界错误"}]}, "entity_coverage": {"type_coverage": 1.0, "quantity_coverage": 1.0, "distribution_similarity": 0.9089444478376963, "missing_types": [], "excess_types": [], "detailed_coverage": {"医疗程序": {"target_count": 10, "actual_count": 10, "coverage_ratio": 1.0, "excess_ratio": 0.0}, "药物": {"target_count": 10, "actual_count": 10, "coverage_ratio": 1.0, "excess_ratio": 0.0}, "投资产品": {"target_count": 10, "actual_count": 11, "coverage_ratio": 1.0, "excess_ratio": 0.1}, "教育背景": {"target_count": 10, "actual_count": 13, "coverage_ratio": 1.0, "excess_ratio": 0.3}, "职业": {"target_count": 10, "actual_count": 10, "coverage_ratio": 1.0, "excess_ratio": 0.0}, "地理位置": {"target_count": 10, "actual_count": 19, "coverage_ratio": 1.0, "excess_ratio": 0.9}, "生育信息": {"target_count": 10, "actual_count": 27, "coverage_ratio": 1.0, "excess_ratio": 1.7}, "姓名": {"target_count": 10, "actual_count": 15, "coverage_ratio": 1.0, "excess_ratio": 0.5}, "信用记录": {"target_count": 10, "actual_count": 16, "coverage_ratio": 1.0, "excess_ratio": 0.6}, "税务记录": {"target_count": 10, "actual_count": 17, "coverage_ratio": 1.0, "excess_ratio": 0.7}, "性别": {"target_count": 10, "actual_count": 14, "coverage_ratio": 1.0, "excess_ratio": 0.4}, "实体资产": {"target_count": 10, "actual_count": 19, "coverage_ratio": 1.0, "excess_ratio": 0.9}, "政治倾向": {"target_count": 10, "actual_count": 15, "coverage_ratio": 1.0, "excess_ratio": 0.5}, "国籍": {"target_count": 10, "actual_count": 12, "coverage_ratio": 1.0, "excess_ratio": 0.2}, "年龄": {"target_count": 10, "actual_count": 11, "coverage_ratio": 1.0, "excess_ratio": 0.1}, "工资数额": {"target_count": 10, "actual_count": 12, "coverage_ratio": 1.0, "excess_ratio": 0.2}, "行程信息": {"target_count": 10, "actual_count": 33, "coverage_ratio": 1.0, "excess_ratio": 2.3}, "民族": {"target_count": 10, "actual_count": 17, "coverage_ratio": 1.0, "excess_ratio": 0.7}, "交易信息": {"target_count": 10, "actual_count": 34, "coverage_ratio": 1.0, "excess_ratio": 2.4}, "疾病": {"target_count": 10, "actual_count": 10, "coverage_ratio": 1.0, "excess_ratio": 0.0}, "婚姻状况": {"target_count": 10, "actual_count": 10, "coverage_ratio": 1.0, "excess_ratio": 0.0}, "家庭成员": {"target_count": 10, "actual_count": 37, "coverage_ratio": 1.0, "excess_ratio": 2.7}, "过敏信息": {"target_count": 10, "actual_count": 18, "coverage_ratio": 1.0, "excess_ratio": 0.8}, "临床表现": {"target_count": 10, "actual_count": 24, "coverage_ratio": 1.0, "excess_ratio": 1.4}}}, "cross_validation": {"fold_results": [{"fold_index": 0, "data_size": 45, "naturalness_score": 9.9, "boundary_accuracy": 0.0, "type_accuracy": 0.0, "vocabulary_diversity": 0.4885386819484241, "entity_diversity": 0.29333333333333333}, {"fold_index": 1, "data_size": 45, "naturalness_score": 10.0, "boundary_accuracy": 0.0, "type_accuracy": 0.05194805194805195, "vocabulary_diversity": 0.5568942436412316, "entity_diversity": 0.2987012987012987}, {"fold_index": 2, "data_size": 45, "naturalness_score": 10.0, "boundary_accuracy": 0.0, "type_accuracy": 0.05263157894736842, "vocabulary_diversity": 0.5604395604395604, "entity_diversity": 0.3026315789473684}, {"fold_index": 3, "data_size": 45, "naturalness_score": 9.95, "boundary_accuracy": 0.0, "type_accuracy": 0.0125, "vocabulary_diversity": 0.4772117962466488, "entity_diversity": 0.2625}, {"fold_index": 4, "data_size": 49, "naturalness_score": 10.0, "boundary_accuracy": 0.0, "type_accuracy": 0.009433962264150943, "vocabulary_diversity": 0.4954233409610984, "entity_diversity": 0.22641509433962265}], "average_metrics": {"naturalness_score": {"mean": 9.969999999999999, "std": 0.039999999999999945, "min": 9.9, "max": 10.0}, "boundary_accuracy": {"mean": 0.0, "std": 0.0, "min": 0.0, "max": 0.0}, "type_accuracy": {"mean": 0.025302718631914262, "std": 0.022417827713063086, "min": 0.0, "max": 0.05263157894736842}, "vocabulary_diversity": {"mean": 0.5157015246473927, "std": 0.035577565667464374, "min": 0.4772117962466488, "max": 0.5604395604395604}, "entity_diversity": {"mean": 0.2767162610643246, "std": 0.028856133227399446, "min": 0.22641509433962265, "max": 0.3026315789473684}}, "consistency_analysis": {"overall_consistency": 0.7873467522232611, "metric_consistency": {"naturalness_score": {"consistency_score": 0.995987963891675, "coefficient_of_variation": 0.00401203610832497, "range": 0.09999999999999964}, "boundary_accuracy": {"consistency_score": 1, "coefficient_of_variation": 0, "range": 0.0}, "type_accuracy": {"consistency_score": 0.11401505746550378, "coefficient_of_variation": 0.8859849425344962, "range": 0.05263157894736842}, "vocabulary_diversity": {"consistency_score": 0.9310113234747749, "coefficient_of_variation": 0.0689886765252251, "range": 0.08322776419291167}, "entity_diversity": {"consistency_score": 0.8957194162843519, "coefficient_of_variation": 0.10428058371564812, "range": 0.07621648460774577}}, "stability_score": 0.6610273540503918}}}, "summary": {"overall_quality_score": 0.4241343103082438, "quality_dimensions": {"naturalness": {"score": 0.692574728617748, "raw_score": 6.92574728617748, "status": "良好"}, "accuracy": {"score": 0.012077294685990338, "boundary_accuracy": 0.0, "type_accuracy": 0.024154589371980676, "status": "需改进"}, "consistency": {"score": 0.8192139737991267, "status": "优秀"}, "diversity": {"score": 0.17267124413011, "vocabulary_diversity": 0.2873714737674664, "entity_diversity": 0.057971014492753624, "status": "需改进"}}, "key_metrics": {"total_dimensions_evaluated": 4, "excellent_dimensions": 1, "needs_improvement_dimensions": 2}, "quality_level": "一般"}, "recommendations": ["自然度有提升空间，可以增加更多样化的句子结构", "实体边界准确性需要改进，检查实体识别和标注逻辑", "实体类型准确性需要改进，优化实体分类规则", "词汇多样性不足，建议扩大词汇库和同义词替换", "实体多样性不足，建议增加更多实体变体"]}