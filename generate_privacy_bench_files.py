"""
为 privacy_bench 数据集在 reproduce\diversify-x 目录下生成相关文件
"""

import os
import json
import shutil
from datetime import datetime

# 设置基本路径和时间戳
base_path = "reproduce\\diversify-x"
timestamp = datetime.now().strftime('%y-%m-%d')
dataset_name = "privacy_bench"

# 从 generated_data 目录读取生成的结果
def read_generated_data():
    # 查找最新的生成目录
    diverse_context_dir = f"generated_data\\{dataset_name}\\diverse-context"
    if not os.path.exists(diverse_context_dir):
        print(f"错误: {diverse_context_dir} 目录不存在")
        return None

    # 获取所有子目录并按时间排序
    subdirs = [d for d in os.listdir(diverse_context_dir) if os.path.isdir(os.path.join(diverse_context_dir, d))]
    if not subdirs:
        print(f"错误: {diverse_context_dir} 目录下没有子目录")
        return None

    # 选择最新的目录
    latest_dir = sorted(subdirs)[-1]
    latest_path = os.path.join(diverse_context_dir, latest_dir)
    print(f"使用最新的生成目录: {latest_path}")

    # 读取生成的属性和类别
    attribute2values = {}
    for attr_dir in os.listdir(latest_path):
        attr_path = os.path.join(latest_path, attr_dir)
        if os.path.isdir(attr_path):
            # 查找 completion 文件
            completion_files = [f for f in os.listdir(attr_path) if f.startswith("completion-")]
            if completion_files:
                with open(os.path.join(attr_path, completion_files[0]), "r", encoding="utf-8") as f:
                    content = f.read()

                # 解析类别
                categories = []
                for line in content.split("\n"):
                    # 匹配形如 "1. 类别名" 的行
                    if line.strip() and line[0].isdigit() and ". " in line:
                        category = line.split(". ", 1)[1].strip()
                        if category:
                            categories.append(category)

                attribute2values[attr_dir] = categories

    return {
        "latest_dir": latest_dir,
        "latest_path": latest_path,
        "attribute2values": attribute2values
    }

# 创建目录
def create_directories():
    directories = [
        os.path.join(base_path, "config"),
        os.path.join(base_path, "gen-attr-dim", f"{timestamp}_{dataset_name}"),
        os.path.join(base_path, "gen-attr-val", f"{timestamp}_{dataset_name}")
    ]

    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"创建目录: {directory}")

# 从 privacy_bench_a2c.py 文件中提取属性信息
def extract_attributes_from_a2c():
    # 尝试导入 privacy_bench_a2c
    try:
        # 添加项目根目录到 sys.path
        import sys
        sys.path.insert(0, os.getcwd())

        # 导入 privacy_bench_a2c
        from src.generate.diversify.attr2cats_dicts.privacy_bench_a2c import privacy_bench_a2c
        from src.generate.diversify.util import ENTITY_KEY, ENTITY_KEY_SEEDED

        # 提取属性信息
        attributes = []
        for attr, info in privacy_bench_a2c.items():
            if attr not in [ENTITY_KEY, ENTITY_KEY_SEEDED]:
                attributes.append(attr)

        return {
            "privacy_bench_a2c": privacy_bench_a2c,
            "attributes": attributes
        }
    except Exception as e:
        print(f"导入 privacy_bench_a2c 失败: {e}")
        # 使用硬编码的属性列表
        return {
            "privacy_bench_a2c": None,
            "attributes": ["data-domain", "data-source", "data-sensitivity", "data-purpose"]
        }

# 生成 config.json 文件
def generate_config_json(attribute2values):
    # 从 privacy_bench_a2c.py 文件中提取属性信息
    a2c_data = extract_attributes_from_a2c()
    attributes = a2c_data["attributes"]

    # 参考 mit_restaurant.json 格式生成 privacy_bench.json
    config_path = os.path.join(base_path, "config", f"{dataset_name}.json")

    # 确保属性值存在
    filtered_attributes = [attr for attr in attributes if attr in attribute2values]
    filtered_attribute2values = {attr: attribute2values.get(attr, []) for attr in filtered_attributes}

    config = {
        "meta": {
            "dataset_name": dataset_name,
            "diverse_context": True,
            "diverse_entity": False,
            "attributes": filtered_attributes
        },
        "attribute2values": filtered_attribute2values
    }

    with open(config_path, "w", encoding="utf-8") as f:
        json.dump(config, f, ensure_ascii=False, indent=4)
    print(f"生成文件: {config_path}")

# 生成 prompt.txt 和 response1.txt 文件
def generate_prompt_response_files():
    prompt = (
        "Creating diverse examples of sensitive data for privacy protection and data desensitization "
        "involves considering a variety of attributes. "
        "Please list and describe key attributes to consider when generating diverse sensitive data examples, "
        "along with examples in each category. "
        "Focus on the following categories: personal PII, financial information, medical data, and location information."
    )

    response = """
Creating diverse examples of sensitive data for privacy protection and data desensitization involves considering a variety of attributes. Here are key attributes to consider when generating diverse sensitive data examples:

1. Data Domain
   - Personal PII: Information that can identify an individual
   - Financial Information: Data related to financial transactions and status
   - Medical Data: Health-related information
   - Location Information: Geographic and movement data
   - Educational Records: Academic history and performance
   - Employment Data: Work history and professional information
   - Social Media Content: Posts, connections, and online behavior
   - Government Records: Official documentation and identifiers

2. Data Source
   - Medical Records: "Patient exhibited symptoms of hypertension during the examination."
   - Financial Transactions: "Monthly transfer of ¥5,000 to savings account initiated on January 15th."
   - Government Databases: "Passport #G12345678 issued on March 10, 2020, expires March 9, 2030."
   - Social Media: "Posted vacation photos from Shanghai, tagged with family members."
   - Corporate Records: "Employee performance review indicates exceeding targets by 15%."
   - Educational Institutions: "Transcript shows GPA of 3.8 in Computer Science program."
   - Online Forms: "Submitted application with home address and contact information."
   - Email Communications: "Please find attached my bank account details for the refund."

3. Data Sensitivity
   - Public Information: "Zhang Wei works at ABC Technology Company."
   - Personal Identifiable Information: "ID number 110101199001011234 belongs to Li Ming."
   - Sensitive Personal Information: "Monthly salary of ¥25,000 deposited to Bank of China account."
   - Highly Sensitive Information: "HIV test results came back positive on June 3rd."
   - Confidential Information: "Access code to the company's financial database is 7H9K2L."

4. Data Format
   - Structured Data: Database entries with clearly defined fields
   - Semi-structured Data: JSON or XML files with nested information
   - Unstructured Text: "The patient reported chest pain and was prescribed nitroglycerin."
   - Conversational Format: "Hi Dr. Chen, I've been experiencing severe headaches and dizziness since Monday."
   - Tabular Data: Spreadsheets containing personal and financial information
   - List Format: Itemized medical procedures with associated costs and dates

5. Data Purpose
   - Research Analysis: Anonymized patient data used for clinical studies
   - Commercial Decision-making: Credit scores and financial history for loan approval
   - Personalized Services: User preferences and behavior for recommendation systems
   - Risk Assessment: Medical history for insurance premium calculation
   - Compliance Auditing: Financial transactions for tax reporting
   - User Verification: Identity documents for account creation

6. Data Volume
   - Low Density: "Wang Jie, 35 years old, lives in Beijing."
   - Medium Density: "Patient: Zhang Min, Female, 42, Blood Type: A+, Allergies: Penicillin, Current Medications: Lisinopril 10mg daily."
   - High Density: Comprehensive medical record with full history, diagnoses, treatments, lab results, and personal information.

7. Temporal Aspects
   - Historical Data: "Underwent appendectomy in 2005 at Beijing United Hospital."
   - Current Information: "Current balance in savings account: ¥45,678.90 as of April 15, 2023."
   - Predictive Data: "Based on spending patterns, likely to make a major purchase in next 30 days."
   - Periodic Data: "Monthly income of ¥15,000 from primary employer."

8. Demographic Factors
   - Age Groups: Children, teenagers, adults, seniors
   - Geographic Regions: Urban centers, rural areas, specific provinces
   - Socioeconomic Status: Income levels, education, occupation
   - Cultural Background: Different ethnic groups and cultural practices

Examples for each main category:

Personal PII Examples:
- "Liu Wei, born on January 15, 1985, resides at 123 Zhongshan Road, Shanghai."
- "Chinese passport number E12345678 issued to Chen Mei, expires December 2027."
- "Employee ID 12345, Han ethnicity, married with two children."
- "WeChat ID: happydragon88, linked to phone number 138-1234-5678."
- "Bachelor's degree in Economics from Peking University, graduated 2008."

Financial Information Examples:
- "Credit card ending in 4567 with ¥50,000 limit, current balance ¥12,345."
- "Monthly mortgage payment of ¥8,500 for property at 45 Nanjing East Road."
- "Investment portfolio includes 200 shares of Alibaba purchased at ¥180 per share."
- "Annual tax filing shows total income of ¥320,000 with ¥42,000 in deductions."
- "Insurance policy #ABC123456 with monthly premium of ¥1,200 covering family of four."

Medical Data Examples:
- "Diagnosed with Type 2 diabetes in 2018, current HbA1c level 7.2%."
- "Prescribed Lipitor 20mg daily for hypercholesterolemia, started March 2022."
- "Surgical history includes appendectomy (2010) and cesarean section (2015)."
- "Allergic to penicillin, exhibits rash and difficulty breathing upon exposure."
- "Family history of hypertension and coronary artery disease on paternal side."

Location Information Examples:
- "Commutes daily from Haidian District to Financial Street, Beijing, departure time 7:30 AM."
- "Checked in at Shanghai Pudong International Airport, Terminal 2, Gate 15."
- "Frequent visits to Harmony Fitness Center at 78 Guangzhou Road, typically Tuesday and Thursday evenings."
- "Location history shows regular travel between Beijing and Tianjin on weekends."
- "Home address: Building 3, Unit 2, Apartment 1502, Golden River Community, Chaoyang District, Beijing."

By considering these attributes and their combinations, you can generate diverse and realistic sensitive data examples that cover various domains, formats, and sensitivity levels for privacy protection and data desensitization tasks.
"""

    prompt_path = os.path.join(base_path, "gen-attr-dim", f"{timestamp}_{dataset_name}", "prompt.txt")
    response_path = os.path.join(base_path, "gen-attr-dim", f"{timestamp}_{dataset_name}", "response1.txt")

    with open(prompt_path, "w", encoding="utf-8") as f:
        f.write(prompt)
    print(f"生成文件: {prompt_path}")

    with open(response_path, "w", encoding="utf-8") as f:
        f.write(response)
    print(f"生成文件: {response_path}")

# 复制原始生成文件到 gen-attr-val 目录
def copy_original_files_to_gen_attr_val(latest_path):
    # 创建目标目录
    target_dir = os.path.join(base_path, "gen-attr-val", f"{timestamp}_{dataset_name}")
    os.makedirs(target_dir, exist_ok=True)

    # 遍历原始目录中的所有子目录
    for attr_dir in os.listdir(latest_path):
        source_dir = os.path.join(latest_path, attr_dir)
        if os.path.isdir(source_dir):
            # 复制所有文件，除了 prompts.log 和 write-completion.log
            for file_name in os.listdir(source_dir):
                if file_name not in ["prompts.log", "write-completion.log"]:
                    source_file = os.path.join(source_dir, file_name)
                    if os.path.isfile(source_file):
                        # 如果是 completion 文件，重命名为 attr.json
                        if file_name.startswith("completion-"):
                            target_file = os.path.join(target_dir, f"{attr_dir}.json")
                            # 读取内容并解析类别
                            with open(source_file, "r", encoding="utf-8") as f:
                                content = f.read()

                            # 解析类别
                            categories = []
                            for line in content.split("\n"):
                                # 匹配形如 "1. 类别名" 的行
                                if line.strip() and line[0].isdigit() and ". " in line:
                                    category = line.split(". ", 1)[1].strip()
                                    if category:
                                        categories.append(category)

                            # 保存为 JSON 文件
                            with open(target_file, "w", encoding="utf-8") as f:
                                json.dump(categories, f, ensure_ascii=False, indent=4)
                        else:
                            # 其他文件直接复制
                            target_file = os.path.join(target_dir, file_name)
                            shutil.copy2(source_file, target_file)

                        print(f"复制文件: {source_file} -> {target_file}")

# 主函数
def main():
    # 读取生成的数据
    data = read_generated_data()
    if not data:
        # 如果没有找到生成的数据，使用默认值
        data = {
            "latest_dir": "default",
            "latest_path": "generated_data\\privacy_bench\\diverse-context\\default",
            "attribute2values": {
                "data-domain": [
                    "医疗健康",
                    "金融财务",
                    "个人身份",
                    "地理位置",
                    "教育背景",
                    "社交网络",
                    "政府公共服务",
                    "混合领域"
                ],
                "data-source": [
                    "医疗记录",
                    "金融交易",
                    "社交媒体",
                    "政府档案",
                    "企业内部数据",
                    "在线表单",
                    "电子邮件",
                    "聊天记录"
                ],
                "data-sensitivity": [
                    "公开信息",
                    "个人可识别信息",
                    "敏感个人信息",
                    "高度敏感信息",
                    "机密信息"
                ],
                "data-format": [
                    "结构化数据",
                    "半结构化数据",
                    "非结构化文本",
                    "对话形式",
                    "表格数据",
                    "列表数据"
                ],
                "data-purpose": [
                    "研究分析",
                    "商业决策",
                    "个性化服务",
                    "风险评估",
                    "合规审计",
                    "用户验证"
                ],
                "data-volume": [
                    "低密度",
                    "中等密度",
                    "高密度"
                ]
            }
        }
        print("未找到生成的数据，使用默认值")

    # 创建目录
    create_directories()

    # 生成文件
    generate_config_json(data["attribute2values"])
    generate_prompt_response_files()

    # 复制原始生成文件到 gen-attr-val 目录
    copy_original_files_to_gen_attr_val(data["latest_path"])

    print("所有文件生成完成！")

if __name__ == "__main__":
    main()
