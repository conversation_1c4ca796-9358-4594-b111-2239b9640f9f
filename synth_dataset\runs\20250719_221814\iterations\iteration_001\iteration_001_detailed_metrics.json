{"basic_statistics": {"total_samples": 229, "text_length_stats": {"mean": 30.21834061135371, "median": 28.0, "std": 9.180167290536549, "min": 12, "max": 86, "percentiles": {"25": 24.0, "75": 35.0, "90": 40.20000000000002, "95": 44.599999999999994}}, "entity_count_stats": {"mean": 1.7292576419213974, "median": 1.0, "std": 0.904172699137273, "min": 1, "max": 5}, "label_distribution": {"姓名": 18, "地理位置": 15, "职业": 10, "医疗程序": 10, "疾病": 10, "药物": 10, "临床表现": 24, "国籍": 11, "民族": 15, "教育背景": 16, "性别": 16, "年龄": 10, "婚姻状况": 10, "政治倾向": 12, "家庭成员": 31, "工资数额": 13, "投资产品": 10, "税务记录": 14, "信用记录": 13, "实体资产": 19, "交易信息": 32, "过敏信息": 18, "生育信息": 26, "行程信息": 33}}, "entity_analysis": {"entity_type_distribution": {"姓名": 18, "地理位置": 15, "职业": 10, "医疗程序": 10, "疾病": 10, "药物": 10, "临床表现": 24, "国籍": 11, "民族": 15, "教育背景": 16, "性别": 16, "年龄": 10, "婚姻状况": 10, "政治倾向": 12, "家庭成员": 31, "工资数额": 13, "投资产品": 10, "税务记录": 14, "信用记录": 13, "实体资产": 19, "交易信息": 32, "过敏信息": 18, "生育信息": 26, "行程信息": 33}, "entity_length_analysis": {"姓名": {"count": 18, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "地理位置": {"count": 15, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "职业": {"count": 10, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "医疗程序": {"count": 10, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "疾病": {"count": 10, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "药物": {"count": 10, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "临床表现": {"count": 24, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "国籍": {"count": 11, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "民族": {"count": 15, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "教育背景": {"count": 16, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "性别": {"count": 16, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "年龄": {"count": 10, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "婚姻状况": {"count": 10, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "政治倾向": {"count": 12, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "家庭成员": {"count": 31, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "工资数额": {"count": 13, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "投资产品": {"count": 10, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "税务记录": {"count": 14, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "信用记录": {"count": 13, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "实体资产": {"count": 19, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "交易信息": {"count": 32, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "过敏信息": {"count": 18, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "生育信息": {"count": 26, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}, "行程信息": {"count": 33, "mean_length": 0.0, "median_length": 0.0, "std_length": 0.0, "min_length": 0, "max_length": 0}}, "entity_position_analysis": {"姓名": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "地理位置": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "职业": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "医疗程序": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "疾病": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "药物": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "临床表现": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "国籍": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "民族": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "教育背景": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "性别": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "年龄": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "婚姻状况": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "政治倾向": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "家庭成员": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "工资数额": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "投资产品": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "税务记录": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "信用记录": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "实体资产": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "交易信息": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "过敏信息": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "生育信息": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "行程信息": {"mean_position": 0.0, "std_position": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}}, "entity_density_analysis": {"mean_density": 0.05962198965254488, "median_density": 0.047619047619047616, "std_density": 0.0318360345010345, "max_density": 0.23529411764705882}, "entity_overlap_analysis": {"total_overlaps": 0, "overlap_rate": 0.0, "overlap_details": []}}, "linguistic_analysis": {"vocabulary_analysis": {"total_words": 3785, "unique_words": 1101, "vocabulary_diversity": 0.29088507265521796, "total_chars": 6920, "unique_chars": 960, "most_common_words": [["。", 224], ["的", 192], ["，", 170], ["了", 81], ["在", 50], ["和", 43], ["为", 35], ["是", 34], ["我", 34], ["他", 33], ["年", 23], ["元", 23], ["这位", 22], ["患者", 21], ["进行", 20], ["月", 20], ["公司", 20], ["小明", 19], ["医生", 18], ["日", 17]], "word_length_distribution": {"mean_word_length": 1.8289264939185617, "median_word_length": 2.0, "std_word_length": 1.0389449049325352, "word_length_distribution": {"single_char": 0.41433104177683766, "two_chars": 0.45293495505023795, "three_chars": 0.06398730830248546, "four_plus_chars": 0.06874669487043893}}}, "sentence_structure_analysis": {"mean_sentence_length": 16.528384279475983, "median_sentence_length": 16.0, "std_sentence_length": 5.125953102208513, "sentence_length_distribution": {"short": 0.017467248908296942, "medium": 0.8034934497816594, "long": 0.17903930131004367}}, "punctuation_analysis": {"total_punctuation": 462, "punctuation_diversity": 11, "punctuation_distribution": {"。": 224, "，": 170, "；": 2, "：": 7, "（": 15, "）": 14, "、": 15, "？": 1, "【": 1, "】": 1, "\"": 12}}, "word_frequency_analysis": {}}, "diversity_analysis": {"lexical_diversity": {"type_token_ratio": 0.29088507265521796, "hapax_legomena_ratio": 0.5767484105358764, "vocabulary_richness": 349.38879129533797}, "syntactic_diversity": {"unique_patterns": 9, "pattern_diversity": 0.039301310043668124, "most_common_patterns": [["LONG_COMMA_END_PUNCT", 64], ["MEDIUM_COMMA_END_PUNCT", 64], ["MEDIUM_END_PUNCT", 63], ["LONG_END_PUNCT", 27], ["LONG_COMMA_END_PUNCT_MID_PUNCT", 4], ["MEDIUM_MID_PUNCT", 2], ["LONG_COMMA", 2], ["MEDIUM_COMMA_MID_PUNCT", 2], ["MEDIUM_COMMA_END_PUNCT_MID_PUNCT", 1]]}, "semantic_diversity": {}, "entity_context_diversity": {"姓名": {"total_contexts": 18, "unique_contexts": 6, "context_diversity": 0.3333333333333333}, "地理位置": {"total_contexts": 15, "unique_contexts": 6, "context_diversity": 0.4}, "职业": {"total_contexts": 10, "unique_contexts": 10, "context_diversity": 1.0}, "医疗程序": {"total_contexts": 10, "unique_contexts": 3, "context_diversity": 0.3}, "疾病": {"total_contexts": 10, "unique_contexts": 7, "context_diversity": 0.7}, "药物": {"total_contexts": 10, "unique_contexts": 7, "context_diversity": 0.7}, "临床表现": {"total_contexts": 24, "unique_contexts": 6, "context_diversity": 0.25}, "国籍": {"total_contexts": 11, "unique_contexts": 9, "context_diversity": 0.8181818181818182}, "民族": {"total_contexts": 15, "unique_contexts": 6, "context_diversity": 0.4}, "教育背景": {"total_contexts": 16, "unique_contexts": 7, "context_diversity": 0.4375}, "性别": {"total_contexts": 16, "unique_contexts": 6, "context_diversity": 0.375}, "年龄": {"total_contexts": 10, "unique_contexts": 6, "context_diversity": 0.6}, "婚姻状况": {"total_contexts": 10, "unique_contexts": 6, "context_diversity": 0.6}, "政治倾向": {"total_contexts": 12, "unique_contexts": 7, "context_diversity": 0.5833333333333334}, "家庭成员": {"total_contexts": 31, "unique_contexts": 8, "context_diversity": 0.25806451612903225}, "工资数额": {"total_contexts": 13, "unique_contexts": 6, "context_diversity": 0.46153846153846156}, "投资产品": {"total_contexts": 10, "unique_contexts": 8, "context_diversity": 0.8}, "税务记录": {"total_contexts": 14, "unique_contexts": 7, "context_diversity": 0.5}, "信用记录": {"total_contexts": 13, "unique_contexts": 9, "context_diversity": 0.6923076923076923}, "实体资产": {"total_contexts": 19, "unique_contexts": 2, "context_diversity": 0.10526315789473684}, "交易信息": {"total_contexts": 32, "unique_contexts": 5, "context_diversity": 0.15625}, "过敏信息": {"total_contexts": 18, "unique_contexts": 2, "context_diversity": 0.1111111111111111}, "生育信息": {"total_contexts": 26, "unique_contexts": 5, "context_diversity": 0.19230769230769232}, "行程信息": {"total_contexts": 33, "unique_contexts": 3, "context_diversity": 0.09090909090909091}}}, "quality_indicators": {"annotation_quality": {"total_annotations": 396, "valid_annotations": 0, "boundary_accuracy": 0.0, "boundary_errors": 396, "type_consistency_errors": 0, "type_consistency_rate": 1.0}, "text_quality": {"mean_quality_score": 0.9812227074235809, "median_quality_score": 1.0, "std_quality_score": 0.04329535216749497, "quality_distribution": {"high": 1.0, "medium": 0.0, "low": 0.0}}, "consistency_indicators": {}}}