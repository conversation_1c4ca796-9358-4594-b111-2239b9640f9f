{"resume-category": ["经验简历：重点突出个人过往的工作经验、成就和技能，适合有多年工作经验的求职者。", "学生简历：面向在校学生，重点展示学业成绩、校园活动、社团经历等，可能较少工作经验。", "求职简历：通用型简历，用于求职，涵盖教育背景、技能、工作经验等基本信息，适合多种岗位申请。", "应届生简历：专门为即将毕业或刚毕业的学生设计，重点突出实习经历、校园实践、个人技能等。", "实习简历：适用于寻找实习机会的学生或初入职场者，着重展示实习经验、学习能力和职业规划。", "技能导向简历：重点突出个人的专业技能和专长，适合技术类岗位或需要特定技能的职位。", "创意简历：设计独特、具有创意的简历，适合设计、艺术、广告等创意行业，通过视觉效果吸引招聘者注意。", "项目经验简历：重点展示个人参与的项目经历、项目成果和贡献，适合项目导向型工作。", "管理经验简历：突出个人的管理经验、领导能力和团队管理能力，适合管理类岗位。", "学术简历：适用于学术研究、教育领域，重点展示教育背景、研究成果、发表论文、学术会议等。"], "education-level": ["高中", "中专", "大专", "本科", "硕士", "博士", "博士后", "研究生"], "industry": ["医疗", "金融", "制造业", "教育", "IT", "零售", "房地产", "能源", "咨询", "文化传媒"], "experience-level": ["应届生/实习生", "1年以下", "1", "3", "5", "8", "10年以上", "资深专家/管理层"], "language-style": ["创新风格：强调独特性和创造力，使用富有想象力的词汇和句式。", "简洁风格：言简意赅，直截了当，避免冗余和复杂的表达。", "正式风格：使用规范的、符合礼仪的词汇和句式，显得庄重和严谨。", "专业风格：使用行业术语和专业技术词汇，体现专业性和深度。", "详细风格：提供丰富的细节和背景信息，使读者对内容有全面的了解。", "亲和风格：使用友好、亲切的语言，拉近与读者的距离。", "动态风格：使用生动、有活力的词汇和句式，展示个性和热情。", "精炼风格：在简洁的基础上，进一步提炼语言，使其更加精炼和有力。"]}