{"dataset_name": "conll2003-no-misc", "entity_types": ["person", "location", "organization"], "examples": [{"sentence": "The European Union is considering imposing tariffs on American products.", "entity_names": ["European Union"], "entity_types": ["organization"]}, {"sentence": "Elon Musk's SpaceX successfully launches another batch of Starlink satellites.", "entity_names": ["<PERSON><PERSON>", "SpaceX"], "entity_types": ["person", "organization"]}, {"sentence": "The protests in Hong Kong continue to escalate as tensions rise between the government and demonstrators.", "entity_names": ["Hong Kong"], "entity_types": ["location"]}, {"sentence": "The World Health Organization warns of a potential Ebola outbreak in the Democratic Republic of Congo.", "entity_names": ["World Health Organization", "Democratic Republic of Congo"], "entity_types": ["organization", "location"]}, {"sentence": "Amazon announces plans to open a new fulfillment center, creating 2,000 jobs in the local community.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "<PERSON> releases her highly anticipated album, \"Fearless (<PERSON>'s Version).\"", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "The United Nations urges immediate action to address the humanitarian crisis in Yemen.", "entity_names": ["United Nations", "Yemen"], "entity_types": ["organization", "location"]}, {"sentence": "The Los Angeles Lakers defeat the Brooklyn Nets in a thrilling overtime game.", "entity_names": ["Los Angeles Lakers", "Brooklyn Nets"], "entity_types": ["organization", "organization"]}, {"sentence": "President <PERSON> signs an executive order to address climate change and promote clean energy.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "The Tokyo Olympics are facing uncertainty as concerns over the COVID-19 pandemic persist.", "entity_names": ["Tokyo", "Olympics"], "entity_types": ["location", "organization"]}, {"sentence": "TikTok faces scrutiny over data privacy concerns and potential national security risks.", "entity_names": ["TikTok"], "entity_types": ["organization"]}, {"sentence": "<PERSON><PERSON> and <PERSON> announce their decision to step back from their royal duties.", "entity_names": ["<PERSON><PERSON>", "<PERSON>"], "entity_types": ["person", "person"]}, {"sentence": "The United States imposes sanctions on Russia following the suspected poisoning of <PERSON>.", "entity_names": ["United States", "Russia", "<PERSON>"], "entity_types": ["location", "location", "person"]}, {"sentence": "Tesla's stock prices surge as the company reports record-breaking sales and profits.", "entity_names": ["Tesla"], "entity_types": ["organization"]}, {"sentence": "The United Nations Security Council condemns the recent terrorist attacks in Nigeria.", "entity_names": ["United Nations Security Council", "Nigeria"], "entity_types": ["organization", "location"]}, {"sentence": "<PERSON>, the Chancellor of Germany, announces her plans to retire from politics.", "entity_names": ["<PERSON>", "Germany"], "entity_types": ["person", "location"]}, {"sentence": "Tesla CEO <PERSON><PERSON> announces plans to build a new gigafactory in Texas.", "entity_names": ["Tesla", "<PERSON><PERSON>", "Texas"], "entity_types": ["organization", "person", "location"]}, {"sentence": "The World Bank approves a $500 million loan to support education reform in India.", "entity_names": ["World Bank", "India"], "entity_types": ["organization", "location"]}, {"sentence": "South Korea hosts a virtual summit with leaders from ASEAN countries to strengthen economic cooperation.", "entity_names": ["South Korea", "ASEAN"], "entity_types": ["location", "organization"]}, {"sentence": "The European Central Bank announces an expansion of its stimulus program to support the economy.", "entity_names": ["European Central Bank"], "entity_types": ["organization"]}, {"sentence": "New study shows link between social media use and mental health issues.", "entity_names": ["social media"], "entity_types": ["organization"]}, {"sentence": "Wildfires continue to devastate parts of California.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Pfizer announces plans for booster shots in response to new COVID variants.", "entity_names": ["Pfizer"], "entity_types": ["organization"]}, {"sentence": "<PERSON> withdraws from Wimbledon due to injury.", "entity_names": ["<PERSON>", "Wimbledon"], "entity_types": ["person", "location"]}, {"sentence": "United Nations report shows increase in global poverty rates.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "Tech giant Apple introduces new line of products.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "Prime Minister <PERSON><PERSON><PERSON> visits Indigenous community in northern Canada.", "entity_names": ["<PERSON><PERSON><PERSON>", "Indigenous", "Canada"], "entity_types": ["person", "location", "location"]}, {"sentence": "SpaceX successfully launches new satellite into orbit.", "entity_names": ["SpaceX"], "entity_types": ["organization"]}, {"sentence": "Record-breaking heatwave hits Western Europe.", "entity_names": ["Western Europe"], "entity_types": ["location"]}, {"sentence": "Apple Inc. unveils latest iPhone model.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}, {"sentence": "Tropical storm makes landfall in Florida.", "entity_names": ["Florida"], "entity_types": ["location"]}, {"sentence": "Elon Musk's SpaceX launches satellite into orbit.", "entity_names": ["<PERSON><PERSON>", "SpaceX"], "entity_types": ["person", "organization"]}, {"sentence": "New study shows link between coffee consumption and reduced risk of heart disease.", "entity_names": [], "entity_types": []}, {"sentence": "United Nations issues statement on human rights violations in Myanmar.", "entity_names": ["United Nations", "Myanmar"], "entity_types": ["organization", "location"]}, {"sentence": "<PERSON> wins gold in gymnastics competition.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Google announces partnership with local schools to promote computer science education.", "entity_names": ["Google"], "entity_types": ["organization"]}, {"sentence": "Former President <PERSON> visits Kenya for charity work.", "entity_names": ["<PERSON>", "Kenya"], "entity_types": ["person", "location"]}, {"sentence": "Wildfires continue to ravage California forests.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Amazon to open new distribution center in Texas.", "entity_names": ["Amazon", "Texas"], "entity_types": ["organization", "location"]}, {"sentence": "New study finds potential link between obesity and increased risk of certain cancers.", "entity_names": [], "entity_types": []}, {"sentence": "<PERSON> releases new album.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "European Union imposes sanctions on Russia for human rights abuses.", "entity_names": ["European Union", "Russia"], "entity_types": ["organization", "location"]}, {"sentence": "New York City to implement new public transportation system.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "<PERSON><PERSON> speaks at climate change conference in Switzerland.", "entity_names": ["<PERSON><PERSON>", "Switzerland"], "entity_types": ["person", "location"]}, {"sentence": "Toyota announces recall of over 1 million vehicles.", "entity_names": ["Toyota"], "entity_types": ["organization"]}, {"sentence": "Hurricane hits coastal town in Louisiana.", "entity_names": ["Louisiana"], "entity_types": ["location"]}, {"sentence": "Researchers discover new species of dinosaur in South America.", "entity_names": ["South America"], "entity_types": ["location"]}, {"sentence": "<PERSON> performs at sold-out concert in London.", "entity_names": ["<PERSON>", "London"], "entity_types": ["person", "location"]}, {"sentence": "Microsoft CEO steps down from position.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "Australia experiences record-breaking heatwave.", "entity_names": ["Australia"], "entity_types": ["location"]}, {"sentence": "<PERSON><PERSON><PERSON> signs multi-million dollar contract with new team.", "entity_names": ["<PERSON><PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Alibaba founder faces investigation for alleged financial misconduct.", "entity_names": ["Alibaba"], "entity_types": ["organization"]}, {"sentence": "Mount Everest climbers stranded in severe snowstorm.", "entity_names": ["Mount Everest"], "entity_types": ["location"]}, {"sentence": "<PERSON> named head coach of NBA team.", "entity_names": ["<PERSON>", "NBA"], "entity_types": ["person", "organization"]}, {"sentence": "New study suggests link between social media use and increased anxiety.", "entity_names": [], "entity_types": []}, {"sentence": "Thousands protest for democracy in Hong Kong.", "entity_names": ["Hong Kong"], "entity_types": ["location"]}, {"sentence": "<PERSON> donates $1 billion to charity.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Ford to invest in new electric vehicle technology.", "entity_names": ["Ford"], "entity_types": ["organization"]}, {"sentence": "Severe drought threatens crops in Midwest.", "entity_names": ["Midwest"], "entity_types": ["location"]}, {"sentence": "<PERSON> to star in upcoming film.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "World Health Organization issues warning about new strain of flu virus.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "New York Yankees win World Series.", "entity_names": ["New York Yankees"], "entity_types": ["organization"]}, {"sentence": "<PERSON> of Cambridge visits children's hospital in London.", "entity_names": ["Duchess of Cambridge", "London"], "entity_types": ["person", "location"]}, {"sentence": "Uber to launch new food delivery service.", "entity_names": ["Uber"], "entity_types": ["organization"]}, {"sentence": "Scientists confirm presence of water on Mars.", "entity_names": ["Mars"], "entity_types": ["location"]}, {"sentence": "Singer <PERSON><PERSON><PERSON> starts new fashion line.", "entity_names": ["<PERSON><PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "United Airlines to add new routes to Europe.", "entity_names": ["United Airlines", "Europe"], "entity_types": ["organization", "location"]}, {"sentence": "FBI warns of potential cyberattack on government agencies.", "entity_names": ["FBI"], "entity_types": ["organization"]}, {"sentence": "Italian restaurant chain announces expansion to Asia.", "entity_names": ["Asia"], "entity_types": ["location"]}, {"sentence": "New study shows link between air pollution and increased risk of respiratory illnesses.", "entity_names": [], "entity_types": []}, {"sentence": "<PERSON> and <PERSON><PERSON> visit Africa for charity work.", "entity_names": ["<PERSON>", "<PERSON><PERSON>", "Africa"], "entity_types": ["person", "person", "location"]}, {"sentence": "Facebook launches new initiative to combat misinformation.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "Iran announces plan to increase uranium enrichment.", "entity_names": ["Iran"], "entity_types": ["location"]}, {"sentence": "Nobel Peace Prize awarded to humanitarian organization for work in conflict zones.", "entity_names": ["Nobel Peace Prize"], "entity_types": ["organization"]}, {"sentence": "Severe flooding hits coastal town in Japan.", "entity_names": ["Japan"], "entity_types": ["location"]}, {"sentence": "Apple announces new iPhone 13 launch date.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "Prime Minister <PERSON> meets with French President <PERSON><PERSON>.", "entity_names": ["<PERSON>", "<PERSON><PERSON>"], "entity_types": ["person", "person"]}, {"sentence": "Hurricane hits Florida, causing widespread damage.", "entity_names": ["Florida"], "entity_types": ["location"]}, {"sentence": "Amazon faces lawsuit over worker conditions.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "Elon Musk plans to visit SpaceX headquarters.", "entity_names": ["<PERSON><PERSON>", "SpaceX"], "entity_types": ["person", "organization"]}, {"sentence": "New York City imposes vaccine mandate for indoor activities.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "President <PERSON><PERSON> unveils new infrastructure plan.", "entity_names": ["Biden"], "entity_types": ["person"]}, {"sentence": "COVID-19 cases surge in California.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Facebook to launch new virtual reality headset.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "<PERSON> returns to golf after injury.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "United Nations adopts resolution on climate change.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "Wildfires continue to ravage Australian countryside.", "entity_names": ["Australian"], "entity_types": ["location"]}, {"sentence": "CEO of Microsoft steps down after 20 years.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "German Chancellor <PERSON><PERSON><PERSON> wins reelection.", "entity_names": ["<PERSON><PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Tokyo Olympics conclude after two-week event.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "Tesla announces record-breaking quarterly profits.", "entity_names": ["Tesla"], "entity_types": ["organization"]}, {"sentence": "Los Angeles Lakers sign new star player.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}, {"sentence": "Russian President <PERSON> meets with Chinese leader <PERSON>.", "entity_names": ["<PERSON>", "Xi"], "entity_types": ["person", "person"]}, {"sentence": "NASA launches new rover to explore Mars.", "entity_names": ["NASA", "Mars"], "entity_types": ["organization", "location"]}, {"sentence": "Pfizer seeks approval for COVID-19 vaccine booster.", "entity_names": ["Pfizer"], "entity_types": ["organization"]}, {"sentence": "London experiences heavy flooding after rainstorm.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "Goldman Sachs CEO testifies before Congress.", "entity_names": ["Goldman Sachs"], "entity_types": ["organization"]}, {"sentence": "Syrian refugees stranded at border.", "entity_names": ["Syrian"], "entity_types": ["location"]}, {"sentence": "Famous chef opens new restaurant in Paris.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "Federal Reserve announces interest rate hike.", "entity_names": ["Federal Reserve"], "entity_types": ["organization"]}, {"sentence": "Canadian Prime Minister <PERSON><PERSON><PERSON> faces backlash over pipeline decision.", "entity_names": ["Canadian", "<PERSON><PERSON><PERSON>"], "entity_types": ["location", "person"]}, {"sentence": "Oil prices soar amid global supply chain issues.", "entity_names": [], "entity_types": []}, {"sentence": "Mumbai hit by massive power outage.", "entity_names": ["Mumbai"], "entity_types": ["location"]}, {"sentence": "Anti-government protests erupt in Hong Kong.", "entity_names": ["Hong Kong"], "entity_types": ["location"]}, {"sentence": "CEO of Amazon donates millions to charity.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "Violent clashes between police and protesters in Barcelona.", "entity_names": ["Barcelona"], "entity_types": ["location"]}, {"sentence": "New study shows link between air pollution and health problems.", "entity_names": [], "entity_types": []}, {"sentence": "United Nations peacekeepers deployed to conflict zone in Africa.", "entity_names": ["United Nations", "Africa"], "entity_types": ["organization", "location"]}, {"sentence": "Egyptian archaeologists discover ancient tombs.", "entity_names": ["Egyptian"], "entity_types": ["location"]}, {"sentence": "Google unveils new AI technology for healthcare.", "entity_names": ["Google"], "entity_types": ["organization"]}, {"sentence": "FBI warns of potential cyberattack on U.S. infrastructure.", "entity_names": ["FBI", "U.S."], "entity_types": ["organization", "location"]}, {"sentence": "Italy imposes new COVID-19 restrictions.", "entity_names": ["Italy"], "entity_types": ["location"]}, {"sentence": "Starbucks announces plan to phase out plastic straws.", "entity_names": ["Starbucks"], "entity_types": ["organization"]}, {"sentence": "Violence erupts in Ukraine-Russia border region.", "entity_names": ["Ukraine", "Russia"], "entity_types": ["location", "location"]}, {"sentence": "Japan to host 2021 Summer Olympics.", "entity_names": ["Japan"], "entity_types": ["location"]}, {"sentence": "New study finds link between sugary drinks and obesity.", "entity_names": [], "entity_types": []}, {"sentence": "Hollywood actress files lawsuit against tabloid magazine.", "entity_names": ["Hollywood"], "entity_types": ["location"]}, {"sentence": "U.N. Secretary-General calls for global ceasefire.", "entity_names": ["U.N. Secretary-General"], "entity_types": ["organization"]}, {"sentence": "Thousands evacuated as volcano erupts in Indonesia.", "entity_names": ["Indonesia"], "entity_types": ["location"]}, {"sentence": "Twitter suspends accounts linked to misinformation.", "entity_names": ["Twitter"], "entity_types": ["organization"]}, {"sentence": "Germany announces new climate action plan.", "entity_names": ["Germany"], "entity_types": ["location"]}, {"sentence": "<PERSON> donates billions to charity.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "South Korea reports record high daily COVID-19 cases.", "entity_names": ["South Korea"], "entity_types": ["location"]}, {"sentence": "Paris Agreement anniversary marked with climate rallies worldwide.", "entity_names": ["Paris Agreement"], "entity_types": ["organization"]}, {"sentence": "Apple announces launch of new iPhone.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "Earthquake hits Los Angeles, causing minor damages.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}, {"sentence": "Facebook to acquire virtual reality company for $2 billion.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "Elon Musk's SpaceX to send astronauts to Mars by 2024.", "entity_names": ["<PERSON><PERSON>", "SpaceX"], "entity_types": ["person", "organization"]}, {"sentence": "Tropical storm warning in effect for Florida coast.", "entity_names": ["Florida"], "entity_types": ["location"]}, {"sentence": "Amazon faces antitrust investigation in Europe.", "entity_names": ["Amazon", "Europe"], "entity_types": ["organization", "location"]}, {"sentence": "New York City to implement vaccine mandate for indoor activities.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "CEO of Microsoft steps down amid controversy.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "Wildfire destroys homes in California.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "<PERSON>'s Nobel Prize medal sells for record-breaking price.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Tokyo Olympics committee announces new COVID-19 protocols.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "United Nations to hold emergency meeting on humanitarian crisis in Yemen.", "entity_names": ["United Nations", "Yemen"], "entity_types": ["organization", "location"]}, {"sentence": "Johnson & Johnson faces lawsuit over opioid crisis.", "entity_names": ["Johnson & Johnson"], "entity_types": ["organization"]}, {"sentence": "Hollywood actress <PERSON> to star in new film.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "European Union introduces new regulations for plastic waste.", "entity_names": ["European Union"], "entity_types": ["organization"]}, {"sentence": "Hurricane warning issued for Gulf Coast states.", "entity_names": ["Gulf Coast"], "entity_types": ["location"]}, {"sentence": "Nobel Peace Prize awarded to climate activist <PERSON><PERSON>.", "entity_names": ["Nobel Peace Prize", "<PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "SpaceX rocket launch successful, delivers supplies to International Space Station.", "entity_names": ["SpaceX", "International Space Station"], "entity_types": ["organization", "location"]}, {"sentence": "New York City Mayor announces new policy on recycling.", "entity_names": ["New York City", "Mayor"], "entity_types": ["location", "person"]}, {"sentence": "Apple Inc. unveils the new iPhone 13.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}, {"sentence": "Global warming Summit to be held in Paris.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "President <PERSON><PERSON> to meet with G7 leaders next week.", "entity_names": ["Biden", "G7"], "entity_types": ["person", "organization"]}, {"sentence": "Massive wildfire destroys homes in California.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "SpaceX launches new satellite into orbit.", "entity_names": ["SpaceX"], "entity_types": ["organization"]}, {"sentence": "World Health Organization issues new guidelines for COVID-19 vaccinations.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "Paris Fashion Week announces new designers.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "United Nations releases report on global hunger crisis.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "Celebrity couple files for divorce.", "entity_names": [], "entity_types": []}, {"sentence": "Google to expand its headquarters in Silicon Valley.", "entity_names": ["Google", "Silicon Valley"], "entity_types": ["organization", "location"]}, {"sentence": "Hurricane hits Florida coast.", "entity_names": ["Florida"], "entity_types": ["location"]}, {"sentence": "<PERSON> and <PERSON><PERSON> welcome their second child.", "entity_names": ["<PERSON>", "<PERSON><PERSON>"], "entity_types": ["person", "person"]}, {"sentence": "New study shows the benefits of meditation for mental health.", "entity_names": [], "entity_types": []}, {"sentence": "Sydney Opera House to host international music festival.", "entity_names": ["Sydney Opera House"], "entity_types": ["location"]}, {"sentence": "Amazon CEO <PERSON> steps down from his position.", "entity_names": ["Amazon", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Delta variant of COVID-19 spreads rapidly in Texas.", "entity_names": ["Texas"], "entity_types": ["location"]}, {"sentence": "Famous chef opens new restaurant in London.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "Pfizer and BioNTech develop new vaccine for coronavirus.", "entity_names": ["Pfizer", "BioNTech"], "entity_types": ["organization", "organization"]}, {"sentence": "Ohio State University to require COVID-19 vaccinations for all students.", "entity_names": ["Ohio State University"], "entity_types": ["organization"]}, {"sentence": "Tokyo Olympics announce new safety protocols for athletes.", "entity_names": ["Tokyo", "Olympics"], "entity_types": ["location", "organization"]}, {"sentence": "Soccer star <PERSON><PERSON><PERSON> signs new contract with Manchester United.", "entity_names": ["<PERSON><PERSON><PERSON>", "Manchester United"], "entity_types": ["person", "organization"]}, {"sentence": "Wild bison herd sighted in Yellowstone National Park.", "entity_names": ["Yellowstone National Park"], "entity_types": ["location"]}, {"sentence": "<PERSON> donates $1 million to charity.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "New York Stock Exchange experiences record-breaking gains.", "entity_names": ["New York Stock Exchange"], "entity_types": ["organization"]}, {"sentence": "Paris Hilton launches new fashion line.", "entity_names": ["Paris Hilton"], "entity_types": ["person"]}, {"sentence": "United States imposes new sanctions on Russia.", "entity_names": ["United States", "Russia"], "entity_types": ["location", "location"]}, {"sentence": "Pop singer <PERSON> releases new album.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Microsoft to acquire gaming company Bethesda.", "entity_names": ["Microsoft", "Bethesda"], "entity_types": ["organization", "organization"]}, {"sentence": "Former President <PERSON> to write a memoir.", "entity_names": ["President <PERSON>"], "entity_types": ["person"]}, {"sentence": "London Marathon raises millions for charity.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "In-N-Out Burger opens new location in Arizona.", "entity_names": ["In-N-Out Burger", "Arizona"], "entity_types": ["organization", "location"]}, {"sentence": "High-speed rail project approved for construction in California.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Fashion designer <PERSON> passes away at 85.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "American actress <PERSON> visits Syrian refugee camp.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "SpaceX founder <PERSON><PERSON> plans trip to space.", "entity_names": ["SpaceX", "<PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Apple Store robbed in downtown Chicago.", "entity_names": ["Apple Store", "Chicago"], "entity_types": ["organization", "location"]}, {"sentence": "World Bank warns of economic downturn in developing countries.", "entity_names": ["World Bank"], "entity_types": ["organization"]}, {"sentence": "New York Yankees defeat Boston Red Sox in extra innings.", "entity_names": ["New York Yankees", "Boston Red Sox"], "entity_types": ["organization", "organization"]}, {"sentence": "South Africa imposes strict lockdown measures to curb COVID-19 spread.", "entity_names": ["South Africa"], "entity_types": ["location"]}, {"sentence": "Australian airline Qantas to resume international flights.", "entity_names": ["Qantas"], "entity_types": ["organization"]}, {"sentence": "Technology giant IBM announces new sustainability initiative.", "entity_names": ["IBM"], "entity_types": ["organization"]}, {"sentence": "Instagram influencer faces backlash over controversial post.", "entity_names": ["Instagram"], "entity_types": ["organization"]}, {"sentence": "United Nations appoints new envoy for climate change.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "New study shows the benefits of exercise for heart health.", "entity_names": [], "entity_types": []}, {"sentence": "Microsoft CEO <PERSON><PERSON><PERSON> speaks at technology conference.", "entity_names": ["Microsoft", "<PERSON><PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Los Angeles Lakers star <PERSON><PERSON><PERSON> sidelined with ankle injury.", "entity_names": ["Los Angeles Lakers", "<PERSON><PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "The CEO of Apple announced a new product launch.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "London's mayor declares a state of emergency due to extreme weather conditions.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "The United Nations has issued a statement condemning the recent acts of violence in the region.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "Famous actress <PERSON> will star in a new film adaptation of a classic novel.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "The European Union has implemented new trade tariffs on imported goods.", "entity_names": ["European Union"], "entity_types": ["organization"]}, {"sentence": "New York City plans to invest millions of dollars in upgrading its public transportation system.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "The former president of the United States will be delivering a keynote speech at the upcoming conference.", "entity_names": ["United States"], "entity_types": ["location"]}, {"sentence": "Facebook is facing scrutiny over its data privacy policies.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "Thousands of protesters gathered outside the headquarters of the multinational corporation to demand better working conditions.", "entity_names": [], "entity_types": []}, {"sentence": "Australian Prime Minister announces a new policy to combat climate change.", "entity_names": ["Australian Prime Minister"], "entity_types": ["organization"]}, {"sentence": "The latest study conducted by the World Health Organization reveals alarming statistics about global obesity rates.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "The CEO of Tesla unveils plans for a revolutionary new electric vehicle.", "entity_names": ["Tesla"], "entity_types": ["organization"]}, {"sentence": "Tokyo Olympics organizers reveal new safety measures for the upcoming games.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "The famous singer <PERSON> announces a world tour.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "The World Bank has approved a loan for infrastructure development in developing countries.", "entity_names": ["World Bank"], "entity_types": ["organization"]}, {"sentence": "NASA launches a new satellite into orbit to study climate patterns.", "entity_names": ["NASA"], "entity_types": ["organization"]}, {"sentence": "The Italian government is facing backlash over its immigration policies.", "entity_names": ["Italian"], "entity_types": ["organization"]}, {"sentence": "Pop star <PERSON> cancels her upcoming concert due to illness.", "entity_names": ["<PERSON> <PERSON>"], "entity_types": ["person"]}, {"sentence": "The United Nations Security Council condemns the recent terrorist attacks in the Middle East.", "entity_names": ["United Nations Security Council"], "entity_types": ["organization"]}, {"sentence": "The city of Paris is implementing new measures to reduce air pollution.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "The CEO of Amazon announces plans for expansion into new markets.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "Indian Prime Minister addresses the nation on Independence Day.", "entity_names": ["Indian Prime Minister"], "entity_types": ["person"]}, {"sentence": "The International Monetary Fund warns of a global economic slowdown.", "entity_names": ["International Monetary Fund"], "entity_types": ["organization"]}, {"sentence": "The Russian government denies allegations of election interference.", "entity_names": [], "entity_types": []}, {"sentence": "Renowned chef <PERSON> opens a new restaurant in Los Angeles.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "The World Trade Organization reports a significant increase in global trade tensions.", "entity_names": ["World Trade Organization"], "entity_types": ["organization"]}, {"sentence": "London Symphony Orchestra announces a series of upcoming performances.", "entity_names": ["London Symphony Orchestra"], "entity_types": ["organization"]}, {"sentence": "The British monarchy faces criticism over its handling of recent scandals.", "entity_names": ["British"], "entity_types": ["organization"]}, {"sentence": "Microsoft unveils a new line of products at its annual conference.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "French President <PERSON><PERSON> delivers a speech on national security.", "entity_names": ["President <PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "The World Wildlife Fund is raising awareness about endangered species in Africa.", "entity_names": ["World Wildlife Fund"], "entity_types": ["organization"]}, {"sentence": "The Chinese government announces a crackdown on corruption within the Communist Party.", "entity_names": [], "entity_types": []}, {"sentence": "Hollywood actor <PERSON> to star in a new action thriller film.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "The International Red Cross issues a statement on the humanitarian crisis in Syria.", "entity_names": ["International Red Cross"], "entity_types": ["organization"]}, {"sentence": "Tokyo Electric Power Company faces lawsuits over the Fukushima nuclear disaster.", "entity_names": ["Tokyo Electric Power Company"], "entity_types": ["organization"]}, {"sentence": "The German Chancellor meets with world leaders to discuss trade agreements.", "entity_names": ["German Chancellor"], "entity_types": ["person"]}, {"sentence": "The American Red Cross provides aid to victims of a natural disaster in the Midwest.", "entity_names": ["American Red Cross"], "entity_types": ["organization"]}, {"sentence": "South Korean pop sensation BTS breaks records with their latest album release.", "entity_names": [], "entity_types": []}, {"sentence": "The European Central Bank announces an interest rate hike.", "entity_names": ["European Central Bank"], "entity_types": ["organization"]}, {"sentence": "New Zealand Prime Minister addresses climate change at the United Nations summit.", "entity_names": ["New Zealand Prime Minister"], "entity_types": ["person"]}, {"sentence": "The World Economic Forum releases its annual report on global competitiveness.", "entity_names": ["World Economic Forum"], "entity_types": ["organization"]}, {"sentence": "Australian wildfires devastate wildlife and natural habitats.", "entity_names": ["Australian"], "entity_types": ["location"]}, {"sentence": "The Gates Foundation pledges millions of dollars to combat infectious diseases in developing countries.", "entity_names": ["Gates Foundation"], "entity_types": ["organization"]}, {"sentence": "The Filipino government faces criticism over human rights abuses.", "entity_names": [], "entity_types": []}, {"sentence": "The National Aeronautics and Space Administration (NASA) confirms the presence of water on the moon.", "entity_names": ["National Aeronautics and Space Administration"], "entity_types": ["organization"]}, {"sentence": "The Swedish pop group ABBA announces a reunion tour.", "entity_names": ["Swedish"], "entity_types": ["location"]}, {"sentence": "Harvard University researchers make a breakthrough in cancer treatment.", "entity_names": ["Harvard University"], "entity_types": ["organization"]}, {"sentence": "South African President delivers a speech on economic reforms.", "entity_names": ["South African President"], "entity_types": ["person"]}, {"sentence": "The World Health Organization warns of a new strain of flu virus in Southeast Asia.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "The United Nations Educational, Scientific and Cultural Organization (UNESCO) designates a new World Heritage Site.", "entity_names": ["United Nations Educational, Scientific and Cultural Organization"], "entity_types": ["organization"]}, {"sentence": "New study shows that eating fruits and vegetables can lower the risk of heart disease.", "entity_names": [], "entity_types": []}, {"sentence": "President <PERSON><PERSON> announces new infrastructure plan to improve roads and bridges.", "entity_names": ["Biden"], "entity_types": ["person"]}, {"sentence": "Tesla CEO <PERSON><PERSON> predicts that self-driving cars will be on the road by next year.", "entity_names": ["Tesla", "<PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Japan to host 2021 Summer Olympics despite concerns over COVID-19.", "entity_names": ["Japan"], "entity_types": ["location"]}, {"sentence": "Apple unveils new iPhone with advanced features and expanded battery life.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "Rising star actress <PERSON> takes a break from acting to focus on activism.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Wildfires continue to ravage California, forcing thousands to evacuate their homes.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Amazon founder <PERSON> announces plans for space tourism company.", "entity_names": ["Amazon", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Study finds that regular exercise can improve mental health and cognitive function.", "entity_names": [], "entity_types": []}, {"sentence": "European Union leaders meet to discuss climate change and environmental policies.", "entity_names": ["European Union"], "entity_types": ["organization"]}, {"sentence": "The United Nations convened a special session to discuss climate change.", "entity_names": ["United Nations", "climate change"], "entity_types": ["organization", "organization"]}, {"sentence": "President <PERSON><PERSON> announced new policies on immigration reform.", "entity_names": ["President <PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "The city of London broke ground on a new public transportation project.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "Amazon employees protest for better working conditions.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "Elon Musk launches new space exploration company.", "entity_names": ["<PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "The New York Stock Exchange experienced a record high trading day.", "entity_names": ["New York Stock Exchange"], "entity_types": ["organization"]}, {"sentence": "Thousands of people gathered in Paris to protest vaccine mandates.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "Apple releases the latest iPhone model.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "Spanish soccer star <PERSON> signs with Paris Saint-Germain.", "entity_names": ["<PERSON>", "Paris Saint-Germain"], "entity_types": ["person", "organization"]}, {"sentence": "The European Union announces new trade agreements with China.", "entity_names": ["European Union", "China"], "entity_types": ["organization", "location"]}, {"sentence": "CEO of Tesla, <PERSON><PERSON>, becomes the world's richest person.", "entity_names": ["Tesla", "<PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Hurricane season devastates coastal communities in the Caribbean.", "entity_names": ["Caribbean"], "entity_types": ["location"]}, {"sentence": "Australian government invests in renewable energy projects.", "entity_names": ["Australian government"], "entity_types": ["organization"]}, {"sentence": "Grammy award-winning artist <PERSON> releases new album.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Russian President <PERSON> meets with German Chancellor <PERSON>.", "entity_names": ["<PERSON>", "German Chancellor <PERSON>"], "entity_types": ["person", "person"]}, {"sentence": "Tokyo to host 2021 Summer Olympics.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "Facebook under fire for privacy breaches.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "The World Health Organization warns of a new strain of flu virus.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "South African activist <PERSON> awarded Nobel Peace Prize.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Amazon announces plans to open new headquarters in Atlanta.", "entity_names": ["Amazon", "Atlanta"], "entity_types": ["organization", "location"]}, {"sentence": "Scientists discover new species of dinosaur in Patagonia.", "entity_names": ["Patagonia"], "entity_types": ["location"]}, {"sentence": "Apple releases new iPhone with advanced camera features.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "New study shows correlation between social media use and mental health issues.", "entity_names": [], "entity_types": []}, {"sentence": "European Union imposes sanctions on Russia.", "entity_names": ["European Union", "Russia"], "entity_types": ["organization", "location"]}, {"sentence": "Actress <PERSON> speaks out against gender inequality in Hollywood.", "entity_names": ["<PERSON>", "Hollywood"], "entity_types": ["person", "location"]}, {"sentence": "Ford recalls over 100,000 vehicles due to safety concerns.", "entity_names": ["Ford"], "entity_types": ["organization"]}, {"sentence": "United Nations announces humanitarian aid for refugees in Syria.", "entity_names": ["United Nations", "Syria"], "entity_types": ["organization", "location"]}, {"sentence": "CEO <PERSON><PERSON> steps down from Tesla.", "entity_names": ["<PERSON><PERSON>", "Tesla"], "entity_types": ["person", "organization"]}, {"sentence": "New York City implements new COVID-19 restrictions.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "Former President <PERSON> to release memoir next year.", "entity_names": ["President <PERSON>"], "entity_types": ["person"]}, {"sentence": "Starbucks to expand its presence in China.", "entity_names": ["Starbucks", "China"], "entity_types": ["organization", "location"]}, {"sentence": "Tennis player <PERSON> advances to the semifinals.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Google faces antitrust lawsuit from the Department of Justice.", "entity_names": ["Google", "Department of Justice"], "entity_types": ["organization", "organization"]}, {"sentence": "Hurricane season expected to be particularly active this year.", "entity_names": [], "entity_types": []}, {"sentence": "Microsoft acquires artificial intelligence startup.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "French government proposes new tax reforms.", "entity_names": ["French government"], "entity_types": ["organization"]}, {"sentence": "Pilot reported seeing unidentified flying object over Arizona.", "entity_names": ["Arizona"], "entity_types": ["location"]}, {"sentence": "Legendary musician <PERSON> to perform at charity concert.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Walmart to launch same-day delivery service.", "entity_names": ["Walmart"], "entity_types": ["organization"]}, {"sentence": "California wildfires continue to devastate communities.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "CEO <PERSON> unveils new line of products at Apple event.", "entity_names": ["<PERSON>", "Apple"], "entity_types": ["person", "organization"]}, {"sentence": "Renowned chef <PERSON> opens new restaurant in Las Vegas.", "entity_names": ["<PERSON>", "Las Vegas"], "entity_types": ["person", "location"]}, {"sentence": "South Korea imposes strict lockdown measures amid surge in COVID-19 cases.", "entity_names": ["South Korea"], "entity_types": ["location"]}, {"sentence": "Bitcoin reaches new all-time high in trading value.", "entity_names": [], "entity_types": []}, {"sentence": "Amazon founder <PERSON> steps down as CEO.", "entity_names": ["Amazon", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Broadway musical Hamilton to return to theaters next year.", "entity_names": ["<PERSON>"], "entity_types": ["organization"]}, {"sentence": "AstraZeneca vaccine found to be highly effective in new study.", "entity_names": ["AstraZeneca"], "entity_types": ["organization"]}, {"sentence": "Manchester United signs new sponsorship deal with major tech company.", "entity_names": ["Manchester United"], "entity_types": ["organization"]}, {"sentence": "McDonald's introduces new plant-based burger to its menu.", "entity_names": ["McDonald's"], "entity_types": ["organization"]}, {"sentence": "Swiss government announces plans to reduce carbon emissions.", "entity_names": ["Swiss government"], "entity_types": ["organization"]}, {"sentence": "Actress <PERSON> appointed as special envoy for the United Nations High Commissioner for Refugees.", "entity_names": ["<PERSON>", "United Nations High Commissioner for Refugees"], "entity_types": ["person", "organization"]}, {"sentence": "Violent protests erupt in downtown Los Angeles.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}, {"sentence": "CEO <PERSON> faces congressional hearing on privacy issues.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Tokyo Olympics organizers announce new COVID-19 protocols for athletes.", "entity_names": ["Tokyo", "Olympics"], "entity_types": ["location", "organization"]}, {"sentence": "Starbucks CEO <PERSON> to step down from his position.", "entity_names": ["Starbucks", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "New Zealand experiences 6.5 magnitude earthquake.", "entity_names": ["New Zealand"], "entity_types": ["location"]}, {"sentence": "Grammy-winning artist <PERSON> to headline music festival in Chicago.", "entity_names": ["<PERSON>", "Chicago"], "entity_types": ["person", "location"]}, {"sentence": "Tesla announces plans to build new Gigafactory in Texas.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}, {"sentence": "Germany approves new climate change legislation.", "entity_names": ["Germany"], "entity_types": ["location"]}, {"sentence": "Legendary director <PERSON> to produce new film.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "SpaceX CEO <PERSON><PERSON> announces plans for manned mission to Mars.", "entity_names": ["SpaceX", "<PERSON><PERSON>", "Mars"], "entity_types": ["organization", "person", "location"]}, {"sentence": "Australian government launches inquiry into banking industry practices.", "entity_names": ["Australian government"], "entity_types": ["organization"]}, {"sentence": "Major snowstorm expected to hit northeastern United States.", "entity_names": ["United States"], "entity_types": ["location"]}, {"sentence": "Instagram introduces new feature to combat cyberbullying.", "entity_names": ["Instagram"], "entity_types": ["organization"]}, {"sentence": "New York City mayor announces new infrastructure plan.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "CEO of Apple speaks at annual tech conference.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "Protests erupt in Paris over new labor laws.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "Famous actor arrested for drunk driving.", "entity_names": [], "entity_types": []}, {"sentence": "United Nations report highlights climate change concerns.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "Prime Minister of Japan meets with President of the United States.", "entity_names": ["Japan", "President of the United States"], "entity_types": ["location", "person"]}, {"sentence": "Major earthquake hits southern California.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Olympic athlete breaks world record in 100m dash.", "entity_names": [], "entity_types": []}, {"sentence": "New study shows link between diet and heart disease.", "entity_names": [], "entity_types": []}, {"sentence": "Hurricane warning issued for Florida coast.", "entity_names": ["Florida"], "entity_types": ["location"]}, {"sentence": "Apple announces new iPhone models.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "President <PERSON><PERSON> visits troops in Afghanistan.", "entity_names": ["President <PERSON><PERSON>", "Afghanistan"], "entity_types": ["person", "location"]}, {"sentence": "Google CEO resigns amid controversy.", "entity_names": ["Google"], "entity_types": ["organization"]}, {"sentence": "California wildfires rage on, threatening homes.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Tesla stock reaches all-time high.", "entity_names": ["Tesla"], "entity_types": ["organization"]}, {"sentence": "Dr. <PERSON> warns of potential COVID-19 surge.", "entity_names": ["Dr. <PERSON>"], "entity_types": ["person"]}, {"sentence": "United Nations calls for ceasefire in Syria.", "entity_names": ["United Nations", "Syria"], "entity_types": ["organization", "location"]}, {"sentence": "Amazon Prime Day sales exceed expectations.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "New York City imposes vaccine mandate for indoor dining.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "Facebook faces backlash over privacy concerns.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "<PERSON> elected as President of the European Union.", "entity_names": ["<PERSON>", "European Union"], "entity_types": ["person", "organization"]}, {"sentence": "Tokyo Olympics postponed due to pandemic.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "International Monetary Fund projects global economic slowdown.", "entity_names": ["International Monetary Fund"], "entity_types": ["organization"]}, {"sentence": "FDA approves new cancer treatment.", "entity_names": ["FDA"], "entity_types": ["organization"]}, {"sentence": "World Health Organization declares polio eradicated in Africa.", "entity_names": ["World Health Organization", "Africa"], "entity_types": ["organization", "location"]}, {"sentence": "Uber partners with local restaurant to offer food delivery.", "entity_names": ["Uber"], "entity_types": ["organization"]}, {"sentence": "Apple plans to open a new store in downtown Miami.", "entity_names": ["Apple", "Miami"], "entity_types": ["organization", "location"]}, {"sentence": "The CEO of Tesla announced a new electric car model at the conference.", "entity_names": ["Tesla"], "entity_types": ["organization"]}, {"sentence": "Mexico surpasses 100,000 COVID-19 deaths.", "entity_names": ["Mexico"], "entity_types": ["location"]}, {"sentence": "Japanese Prime Minister to visit South Korea next week.", "entity_names": ["Japanese Prime Minister", "South Korea"], "entity_types": ["person", "location"]}, {"sentence": "Facebook acquires a leading artificial intelligence startup.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "The famous actor, <PERSON>, to star in a new war film.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Germany imposes strict lockdown measures in response to rising COVID-19 cases.", "entity_names": ["Germany"], "entity_types": ["location"]}, {"sentence": "The United Nations peacekeeping mission in the Middle East extends for another year.", "entity_names": ["United Nations", "Middle East"], "entity_types": ["organization", "location"]}, {"sentence": "New York City to invest $1 billion in affordable housing initiatives.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "The renowned scientist <PERSON> awarded the Nobel Prize for her groundbreaking research.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Samsung to launch its latest smartphone model next month.", "entity_names": ["Samsung"], "entity_types": ["organization"]}, {"sentence": "The president of France addresses the nation in a televised speech.", "entity_names": ["France"], "entity_types": ["location"]}, {"sentence": "Harvard University announces new financial aid initiatives for low-income students.", "entity_names": ["Harvard University"], "entity_types": ["organization"]}, {"sentence": "Hurricane Maria causes widespread devastation in Puerto Rico.", "entity_names": ["Hurricane Maria", "Puerto Rico"], "entity_types": ["organization", "location"]}, {"sentence": "The popular singer <PERSON> releases a new album.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Google's parent company, Alphabet, faces antitrust investigation by the European Union.", "entity_names": ["Google", "Alphabet", "European Union"], "entity_types": ["organization", "organization", "location"]}, {"sentence": "The mayor of London introduces new cycling infrastructure to reduce traffic congestion.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "Microsoft announces a partnership with a leading tech startup to develop innovative software solutions.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "The renowned chef <PERSON>'s cookbook sells for a record price at auction.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Brazil's president signs an executive order to address deforestation in the Amazon rainforest.", "entity_names": ["Brazil", "Amazon rainforest"], "entity_types": ["location", "location"]}, {"sentence": "SpaceX launches a new satellite into orbit.", "entity_names": ["SpaceX"], "entity_types": ["organization"]}, {"sentence": "The famous basketball player <PERSON> to invest in a new sports team.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "The European Central Bank announces new stimulus measures to support the economy.", "entity_names": ["European Central Bank"], "entity_types": ["organization"]}, {"sentence": "The iconic landmark, the Eiffel Tower, reopens to tourists after a lengthy renovation.", "entity_names": ["Eiffel Tower"], "entity_types": ["location"]}, {"sentence": "The United States announces new trade tariffs on Chinese goods.", "entity_names": ["United States", "Chinese"], "entity_types": ["location", "organization"]}, {"sentence": "<PERSON> appointed as new CEO of ABC Corporation.", "entity_names": ["<PERSON>", "ABC Corporation"], "entity_types": ["person", "organization"]}, {"sentence": "Firefighters battle massive wildfire in California.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Researchers discover potential new treatment for Alzheimer's disease.", "entity_names": [], "entity_types": []}, {"sentence": "President <PERSON> delivers annual State of the Union address.", "entity_names": ["President <PERSON>"], "entity_types": ["person"]}, {"sentence": "NASA launches new Mars rover mission.", "entity_names": ["NASA", "Mars"], "entity_types": ["organization", "location"]}, {"sentence": "Prime Minister <PERSON> visits South Korea to discuss trade agreements.", "entity_names": ["Prime Minister <PERSON>", "South Korea"], "entity_types": ["person", "location"]}, {"sentence": "Tennis star <PERSON> wins Wimbledon championship.", "entity_names": ["<PERSON>", "Wimbledon"], "entity_types": ["person", "location"]}, {"sentence": "A new study reveals the impact of climate change on polar bears in the Arctic.", "entity_names": ["Arctic"], "entity_types": ["location"]}, {"sentence": "OPEC members meet to discuss oil production quotas.", "entity_names": ["OPEC"], "entity_types": ["organization"]}, {"sentence": "Environmental activist <PERSON><PERSON> speaks at United Nations climate summit.", "entity_names": ["<PERSON><PERSON>", "United Nations"], "entity_types": ["person", "organization"]}, {"sentence": "Japan's economy enters recession due to pandemic-related lockdowns.", "entity_names": ["Japan"], "entity_types": ["location"]}, {"sentence": "SpaceX successfully launches satellite into orbit.", "entity_names": ["SpaceX"], "entity_types": ["organization"]}, {"sentence": "Local brewery wins top prize at international beer competition.", "entity_names": [], "entity_types": []}, {"sentence": "Famous actor <PERSON> stars in new blockbuster film.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "The World Health Organization declares global pandemic.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "Nature conservation group protests logging in Amazon rainforest.", "entity_names": ["Amazon rainforest"], "entity_types": ["location"]}, {"sentence": "UK Parliament votes to approve new Brexit deal.", "entity_names": ["UK Parliament", "Brexit"], "entity_types": ["organization", "organization"]}, {"sentence": "Microsoft acquires AI startup for $1 billion.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "Egyptian archaeologists uncover ancient tomb in Luxor.", "entity_names": ["Egyptian", "<PERSON><PERSON><PERSON>"], "entity_types": ["location", "location"]}, {"sentence": "Famous chef <PERSON> opens new restaurant in London.", "entity_names": ["<PERSON>", "London"], "entity_types": ["person", "location"]}, {"sentence": "WHO warns of potential vaccine shortages in developing countries.", "entity_names": ["WHO"], "entity_types": ["organization"]}, {"sentence": "<PERSON><PERSON> driver charged with assault in Los Angeles.", "entity_names": ["Uber", "Los Angeles"], "entity_types": ["organization", "location"]}, {"sentence": "Facebook faces antitrust investigation by US government.", "entity_names": ["Facebook", "US"], "entity_types": ["organization", "location"]}, {"sentence": "Activist <PERSON><PERSON> awarded Nobel Peace Prize.", "entity_names": ["Malala You<PERSON>fzai", "Nobel Peace Prize"], "entity_types": ["person", "organization"]}, {"sentence": "Severe weather warnings issued for Midwest region.", "entity_names": ["Midwest"], "entity_types": ["location"]}, {"sentence": "Harvard University announces new scholarship program for low-income students.", "entity_names": ["Harvard University"], "entity_types": ["organization"]}, {"sentence": "Fashion designer <PERSON> launches sustainable clothing line.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Oil spill contaminates coastal waters in Nigeria.", "entity_names": ["Nigeria"], "entity_types": ["location"]}, {"sentence": "Former president <PERSON> releases memoir.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Volunteers clean up litter on beach in Sydney.", "entity_names": ["Sydney"], "entity_types": ["location"]}, {"sentence": "Japanese automaker Toyota recalls millions of vehicles.", "entity_names": ["Japanese", "Toyota"], "entity_types": ["organization", "organization"]}, {"sentence": "UNICEF provides humanitarian aid to refugees in war-torn region.", "entity_names": ["UNICEF"], "entity_types": ["organization"]}, {"sentence": "Olympic champion <PERSON><PERSON> announces retirement from track and field.", "entity_names": ["<PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Earthquake rocks major city in Indonesia.", "entity_names": ["Indonesia"], "entity_types": ["location"]}, {"sentence": "Pfizer and Moderna report positive results from COVID-19 vaccine trials.", "entity_names": ["Pfizer", "Moderna"], "entity_types": ["organization", "organization"]}, {"sentence": "Climate activist groups organize protest outside government headquarters.", "entity_names": [], "entity_types": []}, {"sentence": "Renowned author <PERSON><PERSON><PERSON><PERSON> publishes new book.", "entity_names": ["<PERSON><PERSON><PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Italian government launches investigation into corruption allegations.", "entity_names": ["Italian"], "entity_types": ["organization"]}, {"sentence": "Thousands attend protest against police brutality in Chicago.", "entity_names": ["Chicago"], "entity_types": ["location"]}, {"sentence": "SpaceX founder <PERSON><PERSON> announces plans for Mars colonization.", "entity_names": ["SpaceX", "<PERSON><PERSON>", "Mars"], "entity_types": ["organization", "person", "location"]}, {"sentence": "World Bank approves funding for infrastructure project in Africa.", "entity_names": ["World Bank", "Africa"], "entity_types": ["organization", "location"]}, {"sentence": "Soccer star <PERSON> signs record-breaking contract with Paris Saint-Germain.", "entity_names": ["<PERSON>", "Paris Saint-Germain"], "entity_types": ["person", "organization"]}, {"sentence": "Philadelphia Eagles quarterback suffers season-ending injury.", "entity_names": ["Philadelphia Eagles"], "entity_types": ["organization"]}, {"sentence": "British Prime Minister <PERSON> declares national emergency.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Australian scientists make breakthrough in cancer research.", "entity_names": ["Australian"], "entity_types": ["organization"]}, {"sentence": "The United Nations reports a surge in violence in the Middle East.", "entity_names": ["United Nations", "Middle East"], "entity_types": ["organization", "location"]}, {"sentence": "Apple announces new iPhone release date.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "German Chancellor <PERSON> meets with French President <PERSON>.", "entity_names": ["<PERSON>", "<PERSON>"], "entity_types": ["person", "person"]}, {"sentence": "Tokyo Olympics to proceed without international spectators.", "entity_names": ["Tokyo", "Olympics"], "entity_types": ["location", "organization"]}, {"sentence": "Tesla founder <PERSON><PERSON> unveils plans for Mars colonization.", "entity_names": ["Tesla", "<PERSON><PERSON>", "Mars"], "entity_types": ["organization", "person", "location"]}, {"sentence": "NATO allies meet to discuss global security concerns.", "entity_names": ["NATO"], "entity_types": ["organization"]}, {"sentence": "Hurricane <PERSON> batters the U.S. East Coast.", "entity_names": ["Hurricane Florence", "U.S. East Coast"], "entity_types": ["location", "location"]}, {"sentence": "Amazon employees plan to unionize for better working conditions.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "Barcelona FC appoints new head coach.", "entity_names": ["Barcelona FC"], "entity_types": ["organization"]}, {"sentence": "Apple Inc. announces new iPhone release.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}, {"sentence": "President <PERSON><PERSON> visits the White House.", "entity_names": ["President <PERSON><PERSON>", "White House"], "entity_types": ["person", "location"]}, {"sentence": "Tokyo Olympics to be held without spectators.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "Amazon launches new delivery drone program.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "CEO <PERSON><PERSON> unveils plans for Mars colonization.", "entity_names": ["<PERSON><PERSON>", "Mars"], "entity_types": ["person", "location"]}, {"sentence": "United Nations calls for ceasefire in the Middle East.", "entity_names": ["United Nations", "Middle East"], "entity_types": ["organization", "location"]}, {"sentence": "Xerox Corporation reports record profits for the quarter.", "entity_names": ["Xerox Corporation"], "entity_types": ["organization"]}, {"sentence": "British Prime Minister <PERSON> tests positive for COVID-19.", "entity_names": ["Prime Minister <PERSON>"], "entity_types": ["person"]}, {"sentence": "Google announces expansion of data center in Texas.", "entity_names": ["Google", "Texas"], "entity_types": ["organization", "location"]}, {"sentence": "European Union approves new trade agreement with Canada.", "entity_names": ["European Union", "Canada"], "entity_types": ["organization", "location"]}, {"sentence": "SpaceX successfully launches communication satellite into orbit.", "entity_names": ["SpaceX"], "entity_types": ["organization"]}, {"sentence": "London Stock Exchange experiences record trading volume.", "entity_names": ["London Stock Exchange"], "entity_types": ["organization"]}, {"sentence": "Microsoft acquires artificial intelligence startup for $1 billion.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "Florida Governor <PERSON><PERSON><PERSON><PERSON> signs controversial voting rights bill.", "entity_names": ["Florida", "Governor <PERSON><PERSON><PERSON><PERSON>"], "entity_types": ["location", "person"]}, {"sentence": "CEO of Coca-Cola announces retirement after 30 years with the company.", "entity_names": ["Coca-Cola"], "entity_types": ["organization"]}, {"sentence": "Australian Prime Minister <PERSON> addresses climate change at G7 summit.", "entity_names": ["Australian Prime Minister <PERSON>", "G7"], "entity_types": ["person", "organization"]}, {"sentence": "Bank of America introduces new sustainable investing initiative.", "entity_names": ["Bank of America"], "entity_types": ["organization"]}, {"sentence": "Los Angeles Lakers sign top draft pick to multi-year contract.", "entity_names": ["Los Angeles Lakers"], "entity_types": ["organization"]}, {"sentence": "WHO declares global pandemic due to new virus strain.", "entity_names": ["WHO"], "entity_types": ["organization"]}, {"sentence": "CEO <PERSON> testifies before Congress on data privacy.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "New Zealand Prime Minister <PERSON><PERSON><PERSON> announces economic stimulus package.", "entity_names": ["New Zealand", "<PERSON><PERSON>rn"], "entity_types": ["location", "person"]}, {"sentence": "Reuters to lay off 10% of workforce in cost-cutting measure.", "entity_names": ["Reuters"], "entity_types": ["organization"]}, {"sentence": "NBA Finals to be held in Phoenix for the first time.", "entity_names": ["NBA", "Phoenix"], "entity_types": ["organization", "location"]}, {"sentence": "South Korean President <PERSON>in meets with North Korean leader <PERSON>.", "entity_names": ["South Korean President <PERSON>", "North Korean leader", "<PERSON>"], "entity_types": ["person", "location", "person"]}, {"sentence": "Goldman Sachs predicts 3% GDP growth for the next fiscal year.", "entity_names": ["Goldman Sachs"], "entity_types": ["organization"]}, {"sentence": "Spotify reaches 200 million paid subscribers milestone.", "entity_names": ["Spotify"], "entity_types": ["organization"]}, {"sentence": "Seattle Seahawks quarterback <PERSON> signs record-breaking contract extension.", "entity_names": ["Seattle Seahawks", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Food and Drug Administration approves new cancer treatment drug.", "entity_names": ["Food and Drug Administration"], "entity_types": ["organization"]}, {"sentence": "Brazilian President <PERSON><PERSON><PERSON> faces impeachment proceedings.", "entity_names": ["Brazilian President <PERSON><PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "World Health Organization issues travel advisory for Southeast Asia.", "entity_names": ["World Health Organization", "Southeast Asia"], "entity_types": ["organization", "location"]}, {"sentence": "JP Morgan Chase CEO <PERSON> undergoes emergency surgery.", "entity_names": ["JP <PERSON> Chase", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "G20 Summit concludes with new global economic recovery plan.", "entity_names": ["G20"], "entity_types": ["organization"]}, {"sentence": "Hong Kong protests escalate as China imposes new security law.", "entity_names": ["Hong Kong", "China"], "entity_types": ["location", "location"]}, {"sentence": "IBM launches quantum computing research center in Switzerland.", "entity_names": ["IBM", "Switzerland"], "entity_types": ["organization", "location"]}, {"sentence": "Uber to expand services to rural communities in the United States.", "entity_names": ["Uber", "United States"], "entity_types": ["organization", "location"]}, {"sentence": "Australian Open tennis tournament to allow limited fan attendance.", "entity_names": ["Australian Open"], "entity_types": ["organization"]}, {"sentence": "San Francisco-based tech company raises $100 million in new funding round.", "entity_names": ["San Francisco"], "entity_types": ["location"]}, {"sentence": "<PERSON> visits Iraq in historic trip.", "entity_names": ["<PERSON>", "Iraq"], "entity_types": ["person", "location"]}, {"sentence": "Toyota recalls over 1 million vehicles for safety issue.", "entity_names": ["Toyota"], "entity_types": ["organization"]}, {"sentence": "Denmark to build new offshore wind farm to increase renewable energy production.", "entity_names": ["Denmark"], "entity_types": ["location"]}, {"sentence": "NASA launches new satellite to monitor climate change effects.", "entity_names": ["NASA"], "entity_types": ["organization"]}, {"sentence": "Atlanta Braves win World Series title for the first time in 26 years.", "entity_names": ["Atlanta Braves"], "entity_types": ["organization"]}, {"sentence": "British Airways cancels flights due to ongoing labor strike.", "entity_names": ["British Airways"], "entity_types": ["organization"]}, {"sentence": "Indonesian President <PERSON><PERSON><PERSON> unveils economic reform plan.", "entity_names": ["Indonesian President <PERSON><PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "U.S. Supreme Court justices hear landmark case on gun rights.", "entity_names": ["U.S. Supreme Court"], "entity_types": ["organization"]}, {"sentence": "New York Times Pulitzer Prize-winning journalist writes investigative report on government corruption.", "entity_names": ["New York Times", "Pulitzer Prize-winning journalist"], "entity_types": ["organization", "organization"]}, {"sentence": "New York City announces new public transportation initiative.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "Amazon to acquire MGM Studios for $8.45 billion.", "entity_names": ["Amazon", "MGM Studios"], "entity_types": ["organization", "organization"]}, {"sentence": "Apple unveils new iPhone with advanced camera technology.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "Elon Musk's SpaceX launches new satellite into orbit.", "entity_names": ["<PERSON><PERSON>", "SpaceX"], "entity_types": ["person", "organization"]}, {"sentence": "Brazil surpasses 500,000 COVID-19 deaths.", "entity_names": ["Brazil"], "entity_types": ["location"]}, {"sentence": "Microsoft introduces new cybersecurity measures.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "Famous actor <PERSON> involved in charity fundraiser.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "European Union imposes sanctions on Belarus.", "entity_names": ["European Union", "Belarus"], "entity_types": ["organization", "location"]}, {"sentence": "World Bank approves $1 billion loan for sustainable energy projects.", "entity_names": ["World Bank"], "entity_types": ["organization"]}, {"sentence": "<PERSON> advances to Wimbledon final.", "entity_names": ["<PERSON>", "Wimbledon"], "entity_types": ["person", "location"]}, {"sentence": "California wildfires continue to rage, destroying thousands of acres.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Facebook reveals plan to introduce new virtual reality technology.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "<PERSON> elected as new head of European Central Bank.", "entity_names": ["<PERSON>", "European Central Bank"], "entity_types": ["person", "organization"]}, {"sentence": "SpaceX founder <PERSON><PERSON> becomes world's wealthiest person.", "entity_names": ["SpaceX", "<PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "South Korea reports surge in COVID-19 cases.", "entity_names": ["South Korea"], "entity_types": ["location"]}, {"sentence": "United Nations announces initiative to combat global poverty.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "<PERSON> releases highly-anticipated album.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Flooding in India displaces thousands of residents.", "entity_names": ["India"], "entity_types": ["location"]}, {"sentence": "Google faces antitrust lawsuit from European Union.", "entity_names": ["Google", "European Union"], "entity_types": ["organization", "organization"]}, {"sentence": "<PERSON> and <PERSON><PERSON> launch new charitable foundation.", "entity_names": ["<PERSON>", "<PERSON><PERSON>"], "entity_types": ["person", "person"]}, {"sentence": "London Stock Exchange experiences record gains.", "entity_names": ["London", "Stock Exchange"], "entity_types": ["location", "organization"]}, {"sentence": "Australian government pledges support for renewable energy initiatives.", "entity_names": [], "entity_types": []}, {"sentence": "NBA superstar <PERSON><PERSON><PERSON> signs new endorsement deal.", "entity_names": ["NBA", "<PERSON><PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Hurricane devastates coastal communities in the Caribbean.", "entity_names": ["Caribbean"], "entity_types": ["location"]}, {"sentence": "Uber partners with local charities to provide free rides for healthcare workers.", "entity_names": ["Uber"], "entity_types": ["organization"]}, {"sentence": "<PERSON><PERSON>'s new single reaches number one on the charts.", "entity_names": ["Cardi B"], "entity_types": ["person"]}, {"sentence": "Italy imposes new restrictions to curb rising COVID-19 infections.", "entity_names": ["Italy"], "entity_types": ["location"]}, {"sentence": "Twitter bans political advertisements ahead of upcoming elections.", "entity_names": ["Twitter"], "entity_types": ["organization"]}, {"sentence": "<PERSON> warns of potential surge in COVID-19 cases.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Russia sends aid to war-torn region of Ukraine.", "entity_names": ["Russia", "Ukraine"], "entity_types": ["location", "location"]}, {"sentence": "TaylorMade Golf Company to release new line of clubs.", "entity_names": ["TaylorMade Golf Company"], "entity_types": ["organization"]}, {"sentence": "Kenyan marathon runner sets new world record.", "entity_names": ["Kenyan"], "entity_types": ["location"]}, {"sentence": "Walmart introduces new sustainability initiative.", "entity_names": ["Walmart"], "entity_types": ["organization"]}, {"sentence": "Paris Fashion Week sees return of in-person runway shows.", "entity_names": ["Paris", "Fashion Week"], "entity_types": ["location", "organization"]}, {"sentence": "SpaceX's Starship prototype successfully completes test flight.", "entity_names": ["SpaceX"], "entity_types": ["organization"]}, {"sentence": "Egypt and Sudan agree to joint infrastructure development project.", "entity_names": ["Egypt", "Sudan"], "entity_types": ["location", "location"]}, {"sentence": "Starbucks to open 500 new locations in China.", "entity_names": ["Starbucks", "China"], "entity_types": ["organization", "location"]}, {"sentence": "<PERSON>'s new album breaks streaming records.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "British Royal Family announces new charity initiative.", "entity_names": ["British Royal Family"], "entity_types": ["organization"]}, {"sentence": "<PERSON> wins top honors at Grammy Awards.", "entity_names": ["<PERSON>", "Grammy Awards"], "entity_types": ["person", "organization"]}, {"sentence": "Spain celebrates national soccer team's victory in championship.", "entity_names": ["Spain"], "entity_types": ["location"]}, {"sentence": "Twitter CEO <PERSON> steps down from position.", "entity_names": ["Twitter", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Germany experiences surge in renewable energy production.", "entity_names": ["Germany"], "entity_types": ["location"]}, {"sentence": "Apple Music to feature exclusive concert from popular R&B artist.", "entity_names": ["Apple Music"], "entity_types": ["organization"]}, {"sentence": "United Nations refugee agency provides aid to displaced families in Syria.", "entity_names": ["United Nations", "Syria"], "entity_types": ["organization", "location"]}, {"sentence": "Apple releases new iPhone model.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "California experiences record heatwave.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Tokyo Olympics to begin next month.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "Amazon CEO <PERSON> steps down.", "entity_names": ["Amazon", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Hurricane <PERSON> devastates Louisiana coast.", "entity_names": ["Hurricane Laura", "Louisiana"], "entity_types": ["location", "location"]}, {"sentence": "Pfizer announces new COVID-19 vaccine trial results.", "entity_names": ["Pfizer"], "entity_types": ["organization"]}, {"sentence": "<PERSON><PERSON> speaks at climate change conference.", "entity_names": ["<PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "New York City to implement vaccine passport system.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "Wildfires rage in California, forcing evacuations.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Former President <PERSON> to speak at climate change conference.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "United Nations calls for ceasefire in war-torn region.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "Pfizer vaccine approved for children ages 5-11.", "entity_names": ["Pfizer"], "entity_types": ["organization"]}, {"sentence": "Hurricane devastates communities along the Gulf Coast.", "entity_names": ["Gulf Coast"], "entity_types": ["location"]}, {"sentence": "Facebook faces allegations of antitrust violations.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "London Stock Exchange experiences record-breaking gains.", "entity_names": ["London Stock Exchange"], "entity_types": ["organization"]}, {"sentence": "Germany imposes new restrictions to curb COVID-19 spread.", "entity_names": ["Germany"], "entity_types": ["location"]}, {"sentence": "Actress <PERSON> announces new film project.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Amazon plans to open new fulfillment center in Texas.", "entity_names": ["Amazon", "Texas"], "entity_types": ["organization", "location"]}, {"sentence": "New York City mayor proposes budget cuts to address fiscal crisis.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "CEO of Tesla predicts surge in electric vehicle sales.", "entity_names": ["Tesla"], "entity_types": ["organization"]}, {"sentence": "NASA unveils plans for lunar exploration mission.", "entity_names": ["NASA"], "entity_types": ["organization"]}, {"sentence": "Argentina seeks IMF assistance to address economic challenges.", "entity_names": ["Argentina", "IMF"], "entity_types": ["location", "organization"]}, {"sentence": "Pop star <PERSON> surprises fans with new album release.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Hong Kong protests erupt over proposed extradition law.", "entity_names": ["Hong Kong"], "entity_types": ["location"]}, {"sentence": "Google faces lawsuit over alleged privacy violations.", "entity_names": ["Google"], "entity_types": ["organization"]}, {"sentence": "President <PERSON><PERSON> addresses nation in televised speech.", "entity_names": ["President <PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Chicago police department under investigation for misconduct.", "entity_names": ["Chicago"], "entity_types": ["location"]}, {"sentence": "Ferrari unveils new lineup of luxury sports cars.", "entity_names": ["Ferrari"], "entity_types": ["organization"]}, {"sentence": "<PERSON> donates $1 million to humanitarian aid efforts.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "World Health Organization issues global health alert.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "Australian Open tennis tournament postponed due to COVID-19 concerns.", "entity_names": ["Australian Open"], "entity_types": ["organization"]}, {"sentence": "Bank of America reports record profits for the quarter.", "entity_names": ["Bank of America"], "entity_types": ["organization"]}, {"sentence": "<PERSON><PERSON><PERSON> launches new fashion collection.", "entity_names": ["<PERSON><PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Paris climate agreement gains support from major nations.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "Starbucks unveils plans for sustainable packaging initiative.", "entity_names": ["Starbucks"], "entity_types": ["organization"]}, {"sentence": "<PERSON><PERSON><PERSON><PERSON> faces backlash over controversial statements.", "entity_names": ["<PERSON><PERSON><PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "South Africa announces new measures to combat rising crime rates.", "entity_names": ["South Africa"], "entity_types": ["location"]}, {"sentence": "<PERSON> set to headline music festival in New Orleans.", "entity_names": ["<PERSON>", "New Orleans"], "entity_types": ["person", "location"]}, {"sentence": "<PERSON> steps down as CEO of Amazon.", "entity_names": ["<PERSON>", "Amazon"], "entity_types": ["person", "organization"]}, {"sentence": "Philippine president visits China to discuss trade agreements.", "entity_names": ["Philippine", "China"], "entity_types": ["location", "location"]}, {"sentence": "Sony announces release date for new PlayStation console.", "entity_names": ["Sony"], "entity_types": ["organization"]}, {"sentence": "Major earthquake strikes off the coast of Japan.", "entity_names": ["Japan"], "entity_types": ["location"]}, {"sentence": "<PERSON> Buffet donates $1 billion to charity.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "WHO declares global pandemic due to novel coronavirus.", "entity_names": ["WHO"], "entity_types": ["organization"]}, {"sentence": "<PERSON>'s new album breaks streaming records.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Golden State Warriors to face off against the Brooklyn Nets in the NBA Finals.", "entity_names": ["Golden State Warriors", "Brooklyn Nets"], "entity_types": ["organization", "organization"]}, {"sentence": "Federal Reserve raises interest rates in response to inflation concerns.", "entity_names": ["Federal Reserve"], "entity_types": ["organization"]}, {"sentence": "Reddit co-founder resigns from company board.", "entity_names": ["Reddit"], "entity_types": ["organization"]}, {"sentence": "International Space Station conducts groundbreaking experiments in zero gravity.", "entity_names": ["International Space Station"], "entity_types": ["organization"]}, {"sentence": "New Zealand Prime Minister announces new policies to address climate change.", "entity_names": ["New Zealand", "Prime Minister"], "entity_types": ["location", "person"]}, {"sentence": "Newly elected president pledges to address climate change.", "entity_names": [], "entity_types": []}, {"sentence": "Los Angeles Dodgers clinch playoff spot.", "entity_names": ["Los Angeles Dodgers"], "entity_types": ["organization"]}, {"sentence": "Paris, France experiences record high temperatures.", "entity_names": ["Paris", "France"], "entity_types": ["location", "location"]}, {"sentence": "CEO of Apple Inc. announces new product launch.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}, {"sentence": "United Nations calls for peace negotiations in the Middle East.", "entity_names": ["United Nations", "Middle East"], "entity_types": ["organization", "location"]}, {"sentence": "British Prime Minister visits India for trade talks.", "entity_names": ["British Prime Minister", "India"], "entity_types": ["organization", "location"]}, {"sentence": "Major earthquake hits Tokyo, Japan.", "entity_names": ["Tokyo", "Japan"], "entity_types": ["location", "location"]}, {"sentence": "World Health Organization declares global pandemic.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "President <PERSON><PERSON> visits the United Kingdom for G7 summit.", "entity_names": ["President <PERSON><PERSON>", "United Kingdom", "G7"], "entity_types": ["person", "location", "organization"]}, {"sentence": "Apple Inc. releases new iPhone model.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}, {"sentence": "Famous actress <PERSON> adopts a child from Ethiopia.", "entity_names": ["<PERSON>", "Ethiopia"], "entity_types": ["person", "location"]}, {"sentence": "The European Union imposes sanctions on Russia.", "entity_names": ["European Union", "Russia"], "entity_types": ["organization", "location"]}, {"sentence": "New York City to host Olympic Games in 2032.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "<PERSON> donates $1 billion to combat climate change.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "The World Health Organization declares a global pandemic.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "<PERSON> wins the Masters Golf Tournament.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Amazon to open new headquarters in Tokyo.", "entity_names": ["Amazon", "Tokyo"], "entity_types": ["organization", "location"]}, {"sentence": "German Chancellor <PERSON> meets with French President <PERSON><PERSON>.", "entity_names": ["<PERSON>", "President <PERSON><PERSON>"], "entity_types": ["person", "person"]}, {"sentence": "Microsoft acquires cybersecurity firm for $10 billion.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "<PERSON> launches new beauty line.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Mount Everest climbers stranded after avalanche.", "entity_names": ["Mount Everest"], "entity_types": ["location"]}, {"sentence": "The United Nations warns of humanitarian crisis in Syria.", "entity_names": ["United Nations", "Syria"], "entity_types": ["organization", "location"]}, {"sentence": "<PERSON><PERSON><PERSON> signs $100 million contract with LA Lakers.", "entity_names": ["<PERSON><PERSON><PERSON>", "LA Lakers"], "entity_types": ["person", "organization"]}, {"sentence": "Facebook to invest in renewable energy projects.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "<PERSON> announces world tour dates.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "United Airlines expands routes to Latin America.", "entity_names": ["United Airlines", "Latin America"], "entity_types": ["organization", "location"]}, {"sentence": "German scientists make breakthrough in cancer research.", "entity_names": ["German"], "entity_types": ["location"]}, {"sentence": "<PERSON> to star in new Mission Impossible movie.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "European Central Bank announces interest rate hike.", "entity_names": ["European Central Bank"], "entity_types": ["organization"]}, {"sentence": "<PERSON> to receive humanitarian award at the MTV Music Awards.", "entity_names": ["<PERSON>", "MTV Music Awards"], "entity_types": ["person", "organization"]}, {"sentence": "Oil prices surge after Saudi Arabia drone attack.", "entity_names": ["Saudi Arabia"], "entity_types": ["location"]}, {"sentence": "SpaceX founder <PERSON><PERSON> plans mission to Mars.", "entity_names": ["SpaceX", "<PERSON><PERSON>", "Mars"], "entity_types": ["organization", "person", "location"]}, {"sentence": "Federal Reserve raises key interest rates.", "entity_names": ["Federal Reserve"], "entity_types": ["organization"]}, {"sentence": "<PERSON> divorces <PERSON> after 5 years of marriage.", "entity_names": ["<PERSON>", "<PERSON>"], "entity_types": ["person", "person"]}, {"sentence": "California wildfires destroy thousands of homes.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Paris Fashion Week showcases latest designer collections.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "Google announces new artificial intelligence research center in Beijing.", "entity_names": ["Google", "Beijing"], "entity_types": ["organization", "location"]}, {"sentence": "<PERSON> visits Iraq for historic peace mission.", "entity_names": ["<PERSON>", "Iraq"], "entity_types": ["person", "location"]}, {"sentence": "Australian Prime Minister announces new climate change policy.", "entity_names": [], "entity_types": []}, {"sentence": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> donate $1 million to charity.", "entity_names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "entity_types": ["person", "person"]}, {"sentence": "Italy declares state of emergency after earthquake.", "entity_names": ["Italy"], "entity_types": ["location"]}, {"sentence": "Spotify surpasses 1 billion active users.", "entity_names": ["Spotify"], "entity_types": ["organization"]}, {"sentence": "London Stock Exchange experiences record highs.", "entity_names": ["London", "Stock Exchange"], "entity_types": ["location", "organization"]}, {"sentence": "<PERSON> to star in new sci-fi thriller.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Iranian President <PERSON><PERSON><PERSON> addresses United Nations General Assembly.", "entity_names": ["Iranian President <PERSON><PERSON><PERSON>", "United Nations General Assembly"], "entity_types": ["person", "organization"]}, {"sentence": "Starbucks to open 1,000 new stores in China.", "entity_names": ["Starbucks", "China"], "entity_types": ["organization", "location"]}, {"sentence": "<PERSON> and <PERSON><PERSON><PERSON> to headline music festival.", "entity_names": ["<PERSON>", "Kanye West"], "entity_types": ["person", "person"]}, {"sentence": "New Zealand Prime Minister announces COVID-19 vaccination mandate.", "entity_names": ["New Zealand", "Prime Minister"], "entity_types": ["location", "person"]}, {"sentence": "The International Monetary Fund releases global economic forecast.", "entity_names": ["International Monetary Fund"], "entity_types": ["organization"]}, {"sentence": "Disney to launch new streaming service in Europe.", "entity_names": ["Disney", "Europe"], "entity_types": ["organization", "location"]}, {"sentence": "SpaceX unveils plans for manned mission to Mars.", "entity_names": ["SpaceX", "Mars"], "entity_types": ["organization", "location"]}, {"sentence": "Japan to invest $2 billion in renewable energy projects.", "entity_names": ["Japan"], "entity_types": ["location"]}, {"sentence": "U.S. government sanctions North Korean officials.", "entity_names": ["U.S.", "North Korean"], "entity_types": ["location", "location"]}, {"sentence": "Apple's CEO <PERSON> announces new product launch.", "entity_names": ["Apple", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "The United Nations reports increase in global poverty rates.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "President <PERSON><PERSON> signs new infrastructure bill into law.", "entity_names": ["Biden"], "entity_types": ["person"]}, {"sentence": "SpaceX plans to launch new satellite into orbit next week.", "entity_names": ["SpaceX"], "entity_types": ["organization"]}, {"sentence": "Thousands gather in Times Square to celebrate New Year's Eve.", "entity_names": ["Times Square"], "entity_types": ["location"]}, {"sentence": "Walmart announces partnership with local food banks to fight hunger.", "entity_names": ["Walmart"], "entity_types": ["organization"]}, {"sentence": "London police arrest suspect in connection to recent bank robbery.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "Amazon's stock price reaches all-time high after strong earnings report.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "Famous singer <PERSON> to release new album next month.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "European Union leaders meet to discuss climate change initiatives.", "entity_names": ["European Union"], "entity_types": ["organization"]}, {"sentence": "Microsoft CEO <PERSON><PERSON><PERSON> speaks at tech industry conference.", "entity_names": ["Microsoft", "<PERSON><PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "New York City announces plan to build affordable housing units.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "WHO issues global health warning for new strain of influenza.", "entity_names": ["WHO"], "entity_types": ["organization"]}, {"sentence": "Tokyo Olympics committee reveals new event schedule for 2024 games.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "Tesla's <PERSON><PERSON> breaks ground on new Gigafactory in Texas.", "entity_names": ["Tesla", "<PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "<PERSON> condemns violence in Middle East conflict.", "entity_names": ["<PERSON>", "Middle East"], "entity_types": ["person", "location"]}, {"sentence": "Google announces expansion of data center in Midwest region.", "entity_names": ["Google", "Midwest"], "entity_types": ["organization", "location"]}, {"sentence": "NBA player <PERSON><PERSON><PERSON> signs multi-million dollar endorsement deal.", "entity_names": ["NBA", "<PERSON><PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Paris fashion week kicks off with star-studded runway show.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "IMF warns of economic downturn in developing nations.", "entity_names": ["IMF"], "entity_types": ["organization"]}, {"sentence": "Facebook introduces new privacy features to protect user data.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "Former president <PERSON> to release memoir next year.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "London mayor announces plans to improve public transportation system.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "ExxonMobil faces lawsuit over environmental pollution.", "entity_names": ["ExxonMobil"], "entity_types": ["organization"]}, {"sentence": "Australian Open tennis tournament attracts top players from around the world.", "entity_names": ["Australian Open"], "entity_types": ["organization"]}, {"sentence": "CEO of Coca-Cola steps down after 10 years in the role.", "entity_names": ["Coca-Cola"], "entity_types": ["organization"]}, {"sentence": "Berlin to host international film festival next month.", "entity_names": ["Berlin"], "entity_types": ["location"]}, {"sentence": "National Weather Service issues tornado warnings for several states.", "entity_names": ["National Weather Service"], "entity_types": ["organization"]}, {"sentence": "NBA legend <PERSON> to invest in new sports complex.", "entity_names": ["NBA", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Sony introduces new line of virtual reality gaming consoles.", "entity_names": ["Sony"], "entity_types": ["organization"]}, {"sentence": "London-based charity raises funds for humanitarian aid in war-torn region.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "Twitter CEO <PERSON> resigns, citing personal reasons.", "entity_names": ["Twitter", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Italian government announces plan to invest in renewable energy.", "entity_names": ["Italian government"], "entity_types": ["organization"]}, {"sentence": "Las Vegas casino workers vote to authorize strike.", "entity_names": ["Las Vegas"], "entity_types": ["location"]}, {"sentence": "International Monetary Fund predicts global economic recovery.", "entity_names": ["International Monetary Fund"], "entity_types": ["organization"]}, {"sentence": "Grammy-winning artist <PERSON> to headline music festival.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Chicago police arrest suspect in connection to recent string of burglaries.", "entity_names": ["Chicago"], "entity_types": ["location"]}, {"sentence": "Pfizer and Moderna CEOs testify before Senate committee on vaccine distribution.", "entity_names": ["Pfizer", "Moderna", "Senate committee"], "entity_types": ["organization", "organization", "organization"]}, {"sentence": "Paris agreement on climate change receives support from over 180 countries.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "Goldman Sachs reports record profits in third quarter.", "entity_names": ["Goldman Sachs"], "entity_types": ["organization"]}, {"sentence": "Los Angeles mayor unveils plan to tackle homelessness crisis.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}, {"sentence": "World Health Organization declares new strain of virus a global health emergency.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "Academy Award-winning actress <PERSON><PERSON> to star in new film adaptation.", "entity_names": ["<PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Tokyo prepares to host Summer Olympics amid ongoing pandemic.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "Starbucks introduces new line of sustainable coffee products.", "entity_names": ["Starbucks"], "entity_types": ["organization"]}, {"sentence": "University of Oxford researchers make breakthrough in cancer treatment.", "entity_names": ["University of Oxford"], "entity_types": ["organization"]}, {"sentence": "Riots erupt in major cities following controversial court ruling.", "entity_names": [], "entity_types": []}, {"sentence": "World Bank approves loan to fund infrastructure projects in developing countries.", "entity_names": ["World Bank"], "entity_types": ["organization"]}, {"sentence": "Real Madrid's <PERSON><PERSON><PERSON> breaks goal-scoring record.", "entity_names": ["Real Madrid", "<PERSON><PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "London Stock Exchange experiences sharp decline amid global market volatility.", "entity_names": ["London Stock Exchange"], "entity_types": ["organization"]}, {"sentence": "Tokyo Olympics officially postponed until 2021.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "Facebook unveils new privacy features.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "Amazon workers protest working conditions.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "<PERSON> reelected as German Chancellor.", "entity_names": ["<PERSON>", "German"], "entity_types": ["person", "location"]}, {"sentence": "New York City bans plastic straws.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "Microsoft acquires cybersecurity firm.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "Elon Musk announces plans for Mars colonization.", "entity_names": ["<PERSON><PERSON>", "Mars"], "entity_types": ["person", "location"]}, {"sentence": "Florida declares state of emergency.", "entity_names": ["Florida"], "entity_types": ["location"]}, {"sentence": "Google faces antitrust investigation.", "entity_names": ["Google"], "entity_types": ["organization"]}, {"sentence": "<PERSON><PERSON><PERSON> scores hat-trick in soccer match.", "entity_names": ["<PERSON><PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Paris agrees to new climate change initiatives.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "IMF projects global economic slowdown.", "entity_names": ["IMF"], "entity_types": ["organization"]}, {"sentence": "<PERSON> steps down as Amazon CEO.", "entity_names": ["<PERSON>", "Amazon"], "entity_types": ["person", "organization"]}, {"sentence": "London experiences record high temperatures.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "WHO issues new guidelines for pandemic response.", "entity_names": ["WHO"], "entity_types": ["organization"]}, {"sentence": "<PERSON> wins Wimbledon.", "entity_names": ["<PERSON>", "Wimbledon"], "entity_types": ["person", "location"]}, {"sentence": "Australia to host international summit.", "entity_names": ["Australia"], "entity_types": ["location"]}, {"sentence": "TikTok under fire for privacy concerns.", "entity_names": ["TikTok"], "entity_types": ["organization"]}, {"sentence": "<PERSON> signs exclusive podcast deal.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "California wildfires rage on.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Pfizer vaccine approved for emergency use.", "entity_names": ["Pfizer"], "entity_types": ["organization"]}, {"sentence": "London Fashion Week kicks off with new designers.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "United Nations calls for ceasefire in conflict.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "<PERSON> announces new album release date.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Tokyo to host 2032 Summer Olympics.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "Uber introduces new driver safety measures.", "entity_names": ["Uber"], "entity_types": ["organization"]}, {"sentence": "Las Vegas reopens casinos amidst pandemic.", "entity_names": ["Las Vegas"], "entity_types": ["location"]}, {"sentence": "NOAA issues hurricane warning for East Coast.", "entity_names": ["NOAA", "East Coast"], "entity_types": ["organization", "location"]}, {"sentence": "<PERSON> named as new UNICEF ambassador.", "entity_names": ["<PERSON>", "UNICEF"], "entity_types": ["person", "organization"]}, {"sentence": "Paris to implement new bike sharing program.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "ISIS claims responsibility for latest terrorist attack.", "entity_names": ["ISIS"], "entity_types": ["organization"]}, {"sentence": "<PERSON> releases new single.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Los Angeles to invest in affordable housing.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}, {"sentence": "Johnson & Johnson vaccine shows promising results.", "entity_names": ["Johnson & Johnson"], "entity_types": ["organization"]}, {"sentence": "Chicago sees spike in violent crime.", "entity_names": ["Chicago"], "entity_types": ["location"]}, {"sentence": "NASA to launch new satellite for climate research.", "entity_names": ["NASA"], "entity_types": ["organization"]}, {"sentence": "<PERSON><PERSON> and <PERSON> welcome baby girl.", "entity_names": ["<PERSON><PERSON>", "<PERSON>"], "entity_types": ["person", "person"]}, {"sentence": "Miami to implement new public transportation system.", "entity_names": ["Miami"], "entity_types": ["location"]}, {"sentence": "Twitter introduces new anti-harassment tools.", "entity_names": ["Twitter"], "entity_types": ["organization"]}, {"sentence": "Sydney Opera House to hold virtual concerts.", "entity_names": ["Sydney Opera House"], "entity_types": ["location"]}, {"sentence": "Al-Qaeda leader killed in drone strike.", "entity_names": ["Al-Qaeda"], "entity_types": ["organization"]}, {"sentence": "<PERSON> to headline music festival.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "San Francisco to ban single-use plastics.", "entity_names": ["San Francisco"], "entity_types": ["location"]}, {"sentence": "Amazon founder <PERSON> goes to space.", "entity_names": ["Amazon", "<PERSON>", "Space"], "entity_types": ["organization", "person", "location"]}, {"sentence": "The United States announced a new trade deal with Mexico and Canada.", "entity_names": ["The United States", "Mexico", "Canada"], "entity_types": ["organization", "location", "location"]}, {"sentence": "Professor <PERSON> published a groundbreaking study on climate change.", "entity_names": ["Professor <PERSON>"], "entity_types": ["person"]}, {"sentence": "The European Union imposed sanctions on Russia.", "entity_names": ["The European Union", "Russia"], "entity_types": ["organization", "location"]}, {"sentence": "The stock market reached record highs today.", "entity_names": [], "entity_types": []}, {"sentence": "Mayor <PERSON> unveiled a new plan for city infrastructure improvements.", "entity_names": ["Mayor <PERSON>"], "entity_types": ["person"]}, {"sentence": "The World Health Organization declared a global health emergency.", "entity_names": ["The World Health Organization"], "entity_types": ["organization"]}, {"sentence": "British singer <PERSON> released a new album.", "entity_names": ["British", "<PERSON>"], "entity_types": ["location", "person"]}, {"sentence": "SpaceX successfully launched a new satellite into orbit.", "entity_names": ["SpaceX"], "entity_types": ["organization"]}, {"sentence": "Wildfires continue to spread across Australia.", "entity_names": ["Australia"], "entity_types": ["location"]}, {"sentence": "New York City declares state of emergency due to extreme weather conditions.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "Tesla unveils new electric pickup truck.", "entity_names": ["Tesla"], "entity_types": ["organization"]}, {"sentence": "Soccer player <PERSON> signs $500 million contract with Paris Saint-Germain.", "entity_names": ["<PERSON>", "Paris Saint-Germain"], "entity_types": ["person", "organization"]}, {"sentence": "India surpasses China as world's most populous country.", "entity_names": ["India", "China"], "entity_types": ["location", "location"]}, {"sentence": "President <PERSON><PERSON> announces new infrastructure plan to rebuild roads and bridges.", "entity_names": ["President <PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Google faces antitrust lawsuit in European Union.", "entity_names": ["Google", "European Union"], "entity_types": ["organization", "organization"]}, {"sentence": "Senator <PERSON> proposes legislation to cancel student loan debt.", "entity_names": ["Senator <PERSON>"], "entity_types": ["person"]}, {"sentence": "SpaceX launches new mission to International Space Station.", "entity_names": ["SpaceX", "International Space Station"], "entity_types": ["organization", "location"]}, {"sentence": "Amazon CEO <PERSON> steps down, handing over reins to <PERSON>.", "entity_names": ["Amazon", "<PERSON>", "<PERSON>"], "entity_types": ["organization", "person", "person"]}, {"sentence": "Brazil experiences record-breaking heatwave, prompting health warnings.", "entity_names": ["Brazil"], "entity_types": ["location"]}, {"sentence": "Olympic gold medalist <PERSON> announces retirement from gymnastics.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "UNICEF provides aid to children affected by conflict in Syria.", "entity_names": ["UNICEF", "Syria"], "entity_types": ["organization", "location"]}, {"sentence": "Apple introduces new iPhone with advanced camera features.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "Former President <PERSON> launches new social media platform.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Wildfires continue to ravage California, prompting evacuations.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "President <PERSON><PERSON> to meet with G7 leaders in June.", "entity_names": ["Biden", "G7"], "entity_types": ["person", "organization"]}, {"sentence": "Wildfires ravage California, thousands evacuated.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Major earthquake hits Japan, tsunami warning issued.", "entity_names": ["Japan"], "entity_types": ["location"]}, {"sentence": "Amazon faces backlash over labor practices.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "New study finds link between processed meat and cancer risk.", "entity_names": [], "entity_types": []}, {"sentence": "Hurricane season expected to be particularly severe this year.", "entity_names": [], "entity_types": []}, {"sentence": "Facebook under investigation for antitrust violations.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "Prime Minister <PERSON><PERSON><PERSON> visits India to discuss trade agreements.", "entity_names": ["<PERSON><PERSON><PERSON>", "India"], "entity_types": ["person", "location"]}, {"sentence": "Hollywood actress arrested for DUI.", "entity_names": [], "entity_types": []}, {"sentence": "Google introduces new privacy features for users.", "entity_names": ["Google"], "entity_types": ["organization"]}, {"sentence": "Former President <PERSON> to release memoir.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "South Africa imposes new COVID-19 lockdown measures.", "entity_names": ["South Africa"], "entity_types": ["location"]}, {"sentence": "Pfizer vaccine found to be highly effective in children.", "entity_names": ["Pfizer"], "entity_types": ["organization"]}, {"sentence": "United Nations calls for ceasefire in Yemen.", "entity_names": ["United Nations", "Yemen"], "entity_types": ["organization", "location"]}, {"sentence": "Major data breach at Microsoft exposes millions of users' information.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "<PERSON> condemns violence in the Middle East.", "entity_names": ["<PERSON>", "Middle East"], "entity_types": ["person", "location"]}, {"sentence": "New York City to invest in affordable housing initiatives.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "CEO of Netflix steps down amid controversy.", "entity_names": ["Netflix"], "entity_types": ["organization"]}, {"sentence": "Russia announces plans for manned mission to Mars.", "entity_names": ["Russia", "Mars"], "entity_types": ["location", "location"]}, {"sentence": "<PERSON> and <PERSON><PERSON> welcome baby daughter.", "entity_names": ["<PERSON>", "<PERSON><PERSON>"], "entity_types": ["person", "person"]}, {"sentence": "Australian wildfires destroy acres of land.", "entity_names": ["Australian"], "entity_types": ["location"]}, {"sentence": "CEO of Disney resigns following accusations of misconduct.", "entity_names": ["Disney"], "entity_types": ["organization"]}, {"sentence": "Italy extends COVID-19 lockdown restrictions.", "entity_names": ["Italy"], "entity_types": ["location"]}, {"sentence": "<PERSON> and <PERSON> spotted together in public.", "entity_names": ["<PERSON>", "<PERSON>"], "entity_types": ["person", "person"]}, {"sentence": "Japan's economy shows signs of recovery after pandemic.", "entity_names": ["Japan"], "entity_types": ["location"]}, {"sentence": "Former CEO of Amazon launches new space exploration company.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "Paris Hilton launches new clothing line.", "entity_names": ["Paris Hilton"], "entity_types": ["person"]}, {"sentence": "NASA's Perseverance rover discovers evidence of ancient life on Mars.", "entity_names": ["NASA", "Perseverance", "Mars"], "entity_types": ["organization", "organization", "location"]}, {"sentence": "Greece declares state of emergency as wildfires rage.", "entity_names": ["Greece"], "entity_types": ["location"]}, {"sentence": "<PERSON><PERSON><PERSON> to star in new basketball documentary.", "entity_names": ["<PERSON><PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Chinese government cracks down on cryptocurrency trading.", "entity_names": [], "entity_types": []}, {"sentence": "<PERSON><PERSON> launches new podcast series.", "entity_names": ["<PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "South Korea to host international peace summit.", "entity_names": ["South Korea"], "entity_types": ["location"]}, {"sentence": "<PERSON> releases new album.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Amazon to invest billions in renewable energy projects.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "Canada announces plans for nationwide infrastructure upgrades.", "entity_names": ["Canada"], "entity_types": ["location"]}, {"sentence": "Elon Musk's SpaceX launches astronauts to International Space Station.", "entity_names": ["<PERSON><PERSON>", "SpaceX", "International Space Station"], "entity_types": ["person", "organization", "location"]}, {"sentence": "UK imposes new travel restrictions amid COVID-19 concerns.", "entity_names": ["UK"], "entity_types": ["location"]}, {"sentence": "<PERSON> releases surprise album.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Germany to phase out coal power by 2038.", "entity_names": ["Germany"], "entity_types": ["location"]}, {"sentence": "<PERSON> launches new charity initiative.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "World Health Organization urges global cooperation in pandemic response.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "Tesla announces plans to build new gigafactory in Texas.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}, {"sentence": "Senator <PERSON><PERSON> to visit small businesses in Ohio.", "entity_names": ["<PERSON><PERSON>", "Ohio"], "entity_types": ["person", "location"]}, {"sentence": "Apple releases new iPhone model with advanced camera technology.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "Famous chef <PERSON> launches new cooking show on Netflix.", "entity_names": ["<PERSON>", "Netflix"], "entity_types": ["person", "organization"]}, {"sentence": "Massive wildfire destroys thousands of acres in California.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "World Health Organization warns of new COVID-19 variant.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "Olympic gold medalist <PERSON> discusses mental health struggles.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "New York City to invest $1 billion in public transportation.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "Former president <PERSON> speaks at climate change conference.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Google announces partnership with leading research institution.", "entity_names": ["Google"], "entity_types": ["organization"]}, {"sentence": "Wild weather causes power outages across the Midwest.", "entity_names": ["Midwest"], "entity_types": ["location"]}, {"sentence": "Amazon founder <PERSON> sets new record for space travel.", "entity_names": ["Amazon", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "European Union imposes sanctions on Russian officials.", "entity_names": ["European Union", "Russian"], "entity_types": ["organization", "organization"]}, {"sentence": "Celebrated author <PERSON><PERSON><PERSON><PERSON> releases new book.", "entity_names": ["<PERSON><PERSON><PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "UNICEF delivers aid to refugee camps in the Middle East.", "entity_names": ["UNICEF", "Middle East"], "entity_types": ["organization", "location"]}, {"sentence": "Australian Open tennis tournament to allow limited spectators.", "entity_names": ["Australian Open"], "entity_types": ["organization"]}, {"sentence": "Los Angeles Lakers trade star player to New York Knicks.", "entity_names": ["Los Angeles", "Lakers", "New York Knicks"], "entity_types": ["location", "organization", "organization"]}, {"sentence": "Environmental activist <PERSON><PERSON> speaks at climate rally.", "entity_names": ["<PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Paris Fashion Week attracts top designers from around the world.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "Uber and Lyft reach settlement in driver classification lawsuit.", "entity_names": ["Uber", "Lyft"], "entity_types": ["organization", "organization"]}, {"sentence": "Surge in COVID-19 cases overwhelms hospitals in the South.", "entity_names": ["South"], "entity_types": ["location"]}, {"sentence": "Facebook unveils new initiative to combat misinformation.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "Football legend <PERSON> announces retirement.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "National Hurricane Center issues warning for Gulf Coast.", "entity_names": ["National Hurricane Center", "Gulf Coast"], "entity_types": ["organization", "location"]}, {"sentence": "Black Lives Matter protests erupt in major cities across the country.", "entity_names": ["Black Lives Matter"], "entity_types": ["organization"]}, {"sentence": "SpaceX CEO <PERSON><PERSON> plans mission to Mars.", "entity_names": ["SpaceX", "<PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "National Park Service celebrates 150 years of preservation.", "entity_names": ["National Park Service"], "entity_types": ["organization"]}, {"sentence": "Golden State Warriors star <PERSON> scores 50 points in win.", "entity_names": ["Golden State Warriors", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "United Nations General Assembly convenes in New York City.", "entity_names": ["United Nations", "New York City"], "entity_types": ["organization", "location"]}, {"sentence": "World Bank approves loan for rural development in Africa.", "entity_names": ["World Bank", "Africa"], "entity_types": ["organization", "location"]}, {"sentence": "Major earthquake rattles residents in Japan.", "entity_names": ["Japan"], "entity_types": ["location"]}, {"sentence": "Grammy-winning artist <PERSON> releases surprise album.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "European Central Bank announces stimulus package.", "entity_names": ["European Central Bank"], "entity_types": ["organization"]}, {"sentence": "Pro-democracy protests continue in Hong Kong.", "entity_names": ["Hong Kong"], "entity_types": ["location"]}, {"sentence": "Ford Motor Company announces plans for electric vehicle production.", "entity_names": ["Ford Motor Company"], "entity_types": ["organization"]}, {"sentence": "Major flooding displaces thousands in Southeast Asia.", "entity_names": ["Southeast Asia"], "entity_types": ["location"]}, {"sentence": "Pop icon <PERSON> celebrates 40 years in music industry.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "International Monetary Fund projects global economic recovery.", "entity_names": ["International Monetary Fund"], "entity_types": ["organization"]}, {"sentence": "Florida Governor <PERSON> signs controversial bill into law.", "entity_names": ["Florida", "<PERSON>"], "entity_types": ["location", "person"]}, {"sentence": "Social media giant Twitter announces new privacy features.", "entity_names": ["Twitter"], "entity_types": ["organization"]}, {"sentence": "Severe drought impacts agriculture in the Midwest.", "entity_names": ["Midwest"], "entity_types": ["location"]}, {"sentence": "Hollywood actress <PERSON> stars in new film.", "entity_names": ["Hollywood", "<PERSON>"], "entity_types": ["location", "person"]}, {"sentence": "New study reveals connection between coffee consumption and heart health.", "entity_names": [], "entity_types": []}, {"sentence": "Amazon to open new headquarters in Austin, Texas.", "entity_names": ["Amazon", "Austin", "Texas"], "entity_types": ["organization", "location", "location"]}, {"sentence": "Researchers discover new species of dinosaur in Patagonia.", "entity_names": ["Patagonia"], "entity_types": ["location"]}, {"sentence": "Apple launches new iPhone with advanced camera technology.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "Report shows increase in air pollution levels in Los Angeles.", "entity_names": ["Los Angeles"], "entity_types": ["location"]}, {"sentence": "Soccer star <PERSON> signs with Paris Saint-Germain.", "entity_names": ["<PERSON>", "Paris Saint-Germain"], "entity_types": ["person", "organization"]}, {"sentence": "Study finds correlation between screen time and anxiety in teenagers.", "entity_names": [], "entity_types": []}, {"sentence": "New report ranks Tokyo as the most expensive city in the world.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "Wildfires continue to ravage Northern California.", "entity_names": ["Northern California"], "entity_types": ["location"]}, {"sentence": "Study suggests link between processed meat consumption and cancer risk.", "entity_names": [], "entity_types": []}, {"sentence": "Facebook faces criticism over data privacy concerns.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "Olympic swimmer <PERSON> breaks world record.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "New York City announces plan to combat rising crime rates.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "Scientists discover potential new treatment for Alzheimer's disease.", "entity_names": [], "entity_types": []}, {"sentence": "Rising sea levels pose threat to coastal communities.", "entity_names": [], "entity_types": []}, {"sentence": "Study shows correlation between exercise and mental health.", "entity_names": [], "entity_types": []}, {"sentence": "NASA's Perseverance rover discovers ancient river delta on Mars.", "entity_names": ["NASA", "Mars"], "entity_types": ["organization", "location"]}, {"sentence": "Bitcoin reaches new all-time high.", "entity_names": ["Bitcoin"], "entity_types": ["organization"]}, {"sentence": "United Nations issues statement on global food insecurity.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "Researchers develop new vaccine for malaria.", "entity_names": [], "entity_types": []}, {"sentence": "Mayor <PERSON><PERSON><PERSON><PERSON> proposes new housing plan for Los Angeles.", "entity_names": ["<PERSON><PERSON><PERSON><PERSON>", "Los Angeles"], "entity_types": ["person", "location"]}, {"sentence": "Study reveals link between social media use and loneliness.", "entity_names": [], "entity_types": []}, {"sentence": "Amazon founder <PERSON> becomes the richest person in the world.", "entity_names": ["Amazon", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "London named as the host city for 2022 World Athletics Championships.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "Climate change activists protest outside the headquarters of ExxonMobil.", "entity_names": ["ExxonMobil"], "entity_types": ["organization"]}, {"sentence": "New data shows increase in unemployment rates across the country.", "entity_names": [], "entity_types": []}, {"sentence": "CEO <PERSON> announces new product lineup for Apple.", "entity_names": ["<PERSON>", "Apple"], "entity_types": ["person", "organization"]}, {"sentence": "Study finds link between air pollution and respiratory illnesses.", "entity_names": [], "entity_types": []}, {"sentence": "Facebook introduces new feature to combat misinformation.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "President <PERSON><PERSON> addresses nation on new COVID-19 restrictions.", "entity_names": ["<PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Tokyo Olympics to proceed without spectators.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "Researchers discover ancient Mayan city hidden in the jungle.", "entity_names": [], "entity_types": []}, {"sentence": "New study highlights impact of climate change on polar bear populations.", "entity_names": [], "entity_types": []}, {"sentence": "SpaceX founder <PERSON><PERSON> announces plan for mission to Mars.", "entity_names": ["SpaceX", "<PERSON><PERSON>", "Mars"], "entity_types": ["organization", "person", "location"]}, {"sentence": "Global survey ranks Paris as the best city for student life.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "Study finds link between fast food consumption and obesity rates.", "entity_names": [], "entity_types": []}, {"sentence": "New York City mayor announces plan to address homelessness crisis.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "Researchers identify new strain of flu virus with pandemic potential.", "entity_names": [], "entity_types": []}, {"sentence": "Amazon faces antitrust scrutiny over its e-commerce dominance.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "CEO <PERSON> testifies before Congress on data privacy issues.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "World Health Organization issues warning about new Covid variant.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "Scientists discover new species of deep-sea fish in the Pacific Ocean.", "entity_names": ["Pacific Ocean"], "entity_types": ["location"]}, {"sentence": "Study shows impact of deforestation on biodiversity in the Amazon rainforest.", "entity_names": ["Amazon"], "entity_types": ["location"]}, {"sentence": "Apple CEO <PERSON> announces plans for sustainable manufacturing.", "entity_names": ["Apple", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Researcher <PERSON> honored for groundbreaking medical discoveries.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Apple unveils new iPhone model.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "President <PERSON><PERSON> delivers State of the Union address.", "entity_names": ["President <PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Powerful earthquake hits Japan.", "entity_names": ["Japan"], "entity_types": ["location"]}, {"sentence": "Amazon workers protest for higher wages.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "CEO of Microsoft steps down.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "Wildfires continue to ravage California.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "NASA announces new space mission.", "entity_names": ["NASA"], "entity_types": ["organization"]}, {"sentence": "New study shows link between obesity and heart disease.", "entity_names": [], "entity_types": []}, {"sentence": "Famous actor opens restaurant in New York City.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "United Nations report highlights climate change impact.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "<PERSON> donates millions to charity.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Pandemic causes economic downturn in Italy.", "entity_names": ["Italy"], "entity_types": ["location"]}, {"sentence": "Facebook data breach affects millions of users.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "Olympic Games to be held in Paris.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "CEO of Tesla under investigation for fraud.", "entity_names": ["Tesla"], "entity_types": ["organization"]}, {"sentence": "Protests in Hong Kong demand democratic reforms.", "entity_names": ["Hong Kong"], "entity_types": ["location"]}, {"sentence": "Federal Reserve raises interest rates.", "entity_names": ["Federal Reserve"], "entity_types": ["organization"]}, {"sentence": "Famous singer cancels world tour.", "entity_names": [], "entity_types": []}, {"sentence": "Wildfires in Australia destroy homes.", "entity_names": ["Australia"], "entity_types": ["location"]}, {"sentence": "CEO of Walmart steps down.", "entity_names": ["Walmart"], "entity_types": ["organization"]}, {"sentence": "New study shows benefits of exercise.", "entity_names": [], "entity_types": []}, {"sentence": "United Nations peacekeepers sent to conflict zone.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "Oil spill devastates marine life in the Gulf of Mexico.", "entity_names": ["Gulf of Mexico"], "entity_types": ["location"]}, {"sentence": "Google faces antitrust lawsuit.", "entity_names": ["Google"], "entity_types": ["organization"]}, {"sentence": "South Korea announces new trade deal with Japan.", "entity_names": ["South Korea", "Japan"], "entity_types": ["location", "location"]}, {"sentence": "Famous author releases new book.", "entity_names": [], "entity_types": []}, {"sentence": "United Nations humanitarian aid reaches war-torn region.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "German Chancellor addresses immigration crisis.", "entity_names": ["German Chancellor"], "entity_types": ["person"]}, {"sentence": "Protesters clash with police in London.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "Reuters wins Pulitzer Prize for investigative journalism.", "entity_names": ["Reuters"], "entity_types": ["organization"]}, {"sentence": "New study shows potential link between cell phone use and cancer.", "entity_names": [], "entity_types": []}, {"sentence": "European Union leaders meet to discuss economic recovery.", "entity_names": ["European Union"], "entity_types": ["organization"]}, {"sentence": "CEO of Amazon under scrutiny for labor practices.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "Heavy rainfall causes flooding in India.", "entity_names": ["India"], "entity_types": ["location"]}, {"sentence": "Apple CEO announces new product lineup.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "New study shows impact of air pollution on respiratory health.", "entity_names": [], "entity_types": []}, {"sentence": "President <PERSON><PERSON> signs new environmental legislation.", "entity_names": ["President <PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Violence erupts in the Middle East.", "entity_names": ["Middle East"], "entity_types": ["location"]}, {"sentence": "Walmart employees demand better working conditions.", "entity_names": ["Walmart"], "entity_types": ["organization"]}, {"sentence": "Study finds potential breakthrough in cancer research.", "entity_names": [], "entity_types": []}, {"sentence": "UNICEF reports alarming rise in child poverty.", "entity_names": ["UNICEF"], "entity_types": ["organization"]}, {"sentence": "CEO of Twitter resigns amid controversy.", "entity_names": ["Twitter"], "entity_types": ["organization"]}, {"sentence": "Famous athlete announces retirement.", "entity_names": [], "entity_types": []}, {"sentence": "High-speed train derails in Germany.", "entity_names": ["Germany"], "entity_types": ["location"]}, {"sentence": "CEO of Google testifies before Congress.", "entity_names": ["Google"], "entity_types": ["organization"]}, {"sentence": "New study shows positive effects of meditation on stress levels.", "entity_names": [], "entity_types": []}, {"sentence": "<PERSON> releases new album", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "SpaceX successfully launches mission to Mars", "entity_names": ["SpaceX", "Mars"], "entity_types": ["organization", "location"]}, {"sentence": "President <PERSON><PERSON> announces new infrastructure plan", "entity_names": ["President <PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Amazon opens new headquarters in New York City", "entity_names": ["Amazon", "New York City"], "entity_types": ["organization", "location"]}, {"sentence": "El Salvador becomes first country to adopt Bitcoin as legal tender", "entity_names": ["El Salvador"], "entity_types": ["location"]}, {"sentence": "<PERSON> wins Wimbledon championship", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "European Union imposes sanctions on Russia", "entity_names": ["European Union", "Russia"], "entity_types": ["organization", "location"]}, {"sentence": "<PERSON> steps down as CEO of Amazon", "entity_names": ["<PERSON>", "Amazon"], "entity_types": ["person", "organization"]}, {"sentence": "California wildfires force thousands to evacuate", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Pfizer announces new COVID-19 vaccine booster shot", "entity_names": ["Pfizer"], "entity_types": ["organization"]}, {"sentence": "Tokyo Olympics opening ceremony dazzles viewers", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "Elon Musk's SpaceX plans mission to colonize Mars", "entity_names": ["<PERSON><PERSON>", "SpaceX", "Mars"], "entity_types": ["person", "organization", "location"]}, {"sentence": "Google faces antitrust investigation by EU", "entity_names": ["Google", "EU"], "entity_types": ["organization", "organization"]}, {"sentence": "Hurricane Irma devastates Caribbean islands", "entity_names": ["Caribbean"], "entity_types": ["location"]}, {"sentence": "<PERSON><PERSON><PERSON> signs with Manchester United", "entity_names": ["<PERSON><PERSON><PERSON>", "Manchester United"], "entity_types": ["person", "organization"]}, {"sentence": "New York City mayor announces new affordable housing initiative", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "<PERSON> wins re-election in Germany", "entity_names": ["<PERSON>", "Germany"], "entity_types": ["person", "location"]}, {"sentence": "Facebook under fire for privacy violations", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "Miami Dolphins sign new quarterback", "entity_names": ["Miami Dolphins"], "entity_types": ["organization"]}, {"sentence": "Indonesian volcano eruption leads to evacuation of nearby villages", "entity_names": ["Indonesian"], "entity_types": ["location"]}, {"sentence": "<PERSON> breaks record for most listened-to album in a day", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Apple unveils new iPhone with advanced features", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "Greece struggles with economic crisis", "entity_names": ["Greece"], "entity_types": ["location"]}, {"sentence": "<PERSON> donates $1 million to charity", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Tesla surpasses $1 trillion market value", "entity_names": ["Tesla"], "entity_types": ["organization"]}, {"sentence": "Paris Hilton launches new fashion line", "entity_names": ["Paris Hilton"], "entity_types": ["person"]}, {"sentence": "United Nations issues global warning on climate change", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "California governor signs new climate change legislation", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "<PERSON> announces new world tour dates", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Boeing announces plans for new supersonic jet", "entity_names": ["Boeing"], "entity_types": ["organization"]}, {"sentence": "Portugal national soccer team wins European championship", "entity_names": ["Portugal"], "entity_types": ["location"]}, {"sentence": "<PERSON> donates billions to combat malaria", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Volkswagen recalls millions of vehicles for safety issues", "entity_names": ["Volkswagen"], "entity_types": ["organization"]}, {"sentence": "London experiences record-breaking heatwave", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "<PERSON> wins Grammy for Album of the Year", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Microsoft acquires video game company for $7 billion", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "<PERSON>'s new album breaks streaming records", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Amazon founder <PERSON> launches into space", "entity_names": ["Amazon", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "New Zealand implements strict COVID-19 restrictions", "entity_names": ["New Zealand"], "entity_types": ["location"]}, {"sentence": "<PERSON> to headline Super Bowl halftime show", "entity_names": ["<PERSON> <PERSON>", "Super Bowl"], "entity_types": ["person", "organization"]}, {"sentence": "International Space Station celebrates 20th anniversary", "entity_names": ["International Space Station"], "entity_types": ["location"]}, {"sentence": "Taylor Swift donates $1 million to tornado relief efforts", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "South Korea imposes strict lockdown measures", "entity_names": ["South Korea"], "entity_types": ["location"]}, {"sentence": "<PERSON> surprises fans with impromptu concert", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Apple faces lawsuit over alleged patent infringement", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "New York City subway system faces significant delays", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "<PERSON> to receive prestigious award for humanitarian work", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Russia launches new space mission to Mars", "entity_names": ["Russia", "Mars"], "entity_types": ["location", "location"]}, {"sentence": "<PERSON><PERSON><PERSON> announces new album release", "entity_names": ["Kanye West"], "entity_types": ["person"]}, {"sentence": "The CEO of Apple, <PERSON>, announced a new product launch.", "entity_names": ["Apple", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Thousands of people were evacuated from the flooded city of Houston.", "entity_names": ["Houston"], "entity_types": ["location"]}, {"sentence": "The United Nations issued a statement condemning the recent terrorist attacks.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "The famous actor, <PERSON>, will star in a new biopic about a World War II hero.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Amazon reported a record-breaking quarter with a 200% increase in profits.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "The president of France, <PERSON>, met with world leaders to discuss climate change.", "entity_names": ["France", "<PERSON>"], "entity_types": ["location", "person"]}, {"sentence": "Hurricane Florence wreaks havoc in the Carolinas.", "entity_names": ["Carolinas"], "entity_types": ["location"]}, {"sentence": "NASA to launch a new space probe to explore the outer planets.", "entity_names": ["NASA"], "entity_types": ["organization"]}, {"sentence": "The UK Prime Minister, <PERSON>, faces criticism over his handling of the pandemic.", "entity_names": ["UK", "<PERSON>"], "entity_types": ["location", "person"]}, {"sentence": "Tesla's CEO, <PERSON><PERSON>, unveils plans for a new electric car model.", "entity_names": ["Tesla", "<PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "The city of Tokyo gears up for the upcoming Olympics.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "The World Health Organization warns of a new virus spreading rapidly in Africa.", "entity_names": ["World Health Organization", "Africa"], "entity_types": ["organization", "location"]}, {"sentence": "10 killed in a terrorist attack in Berlin.", "entity_names": ["Berlin"], "entity_types": ["location"]}, {"sentence": "The legendary musician, <PERSON>, releases a new album.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Facebook under fire for data privacy breach.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "Scientists discover a new species of lizard in the Amazon rainforest.", "entity_names": ["Amazon"], "entity_types": ["location"]}, {"sentence": "Former President <PERSON> to write a memoir about his time in office.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Google launches a new initiative to support small businesses.", "entity_names": ["Google"], "entity_types": ["organization"]}, {"sentence": "The Prime Minister of Japan, <PERSON><PERSON>, resigns due to health reasons.", "entity_names": ["Japan", "<PERSON><PERSON>"], "entity_types": ["location", "person"]}, {"sentence": "Wildfires rage through the state of California, destroying thousands of homes.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Apple announces new iPhone release.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "London mayor approves new public transportation plan.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "CEO of Google steps down.", "entity_names": ["Google"], "entity_types": ["organization"]}, {"sentence": "United Nations report warns of climate change crisis.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "New study shows link between coffee consumption and lower risk of heart disease.", "entity_names": [], "entity_types": []}, {"sentence": "President of France visits Canada.", "entity_names": ["France", "Canada"], "entity_types": ["location", "location"]}, {"sentence": "Supreme Court rules in favor of same-sex marriage.", "entity_names": ["Supreme Court"], "entity_types": ["organization"]}, {"sentence": "Famous actor announces retirement from Hollywood.", "entity_names": [], "entity_types": []}, {"sentence": "CEO of Microsoft unveils new product line.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "Paris Fashion Week attracts international designers.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "British Prime Minister calls for national healthcare reform.", "entity_names": ["British"], "entity_types": ["location"]}, {"sentence": "Starbucks introduces new eco-friendly coffee cup.", "entity_names": ["Starbucks"], "entity_types": ["organization"]}, {"sentence": "California wildfires leave thousands homeless.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Australian scientist makes breakthrough in cancer research.", "entity_names": ["Australian"], "entity_types": ["location"]}, {"sentence": "World Health Organization declares pandemic.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "Facebook founder testifies before Congress.", "entity_names": ["Facebook", "Congress"], "entity_types": ["organization", "organization"]}, {"sentence": "German chancellor announces economic stimulus plan.", "entity_names": ["German"], "entity_types": ["location"]}, {"sentence": "New regulations proposed for cryptocurrency trading.", "entity_names": [], "entity_types": []}, {"sentence": "NBA player breaks record for most points in a single game.", "entity_names": ["NBA"], "entity_types": ["organization"]}, {"sentence": "Italian government unveils plan to reduce carbon emissions.", "entity_names": ["Italian"], "entity_types": ["location"]}, {"sentence": "London Stock Exchange experiences record high trading volume.", "entity_names": ["London Stock Exchange"], "entity_types": ["organization"]}, {"sentence": "Russia accuses United States of election interference.", "entity_names": ["Russia", "United States"], "entity_types": ["location", "location"]}, {"sentence": "Famous singer cancels world tour due to illness.", "entity_names": [], "entity_types": []}, {"sentence": "Japan launches new satellite into space.", "entity_names": ["Japan"], "entity_types": ["location"]}, {"sentence": "British royal family welcomes new addition.", "entity_names": ["British"], "entity_types": ["location"]}, {"sentence": "Volkswagen recalls millions of vehicles over safety concerns.", "entity_names": ["Volkswagen"], "entity_types": ["organization"]}, {"sentence": "Australian Open tennis tournament kicks off.", "entity_names": ["Australian Open"], "entity_types": ["organization"]}, {"sentence": "New report shows rising sea levels in the Maldives.", "entity_names": ["Maldives"], "entity_types": ["location"]}, {"sentence": "Major earthquake strikes California.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Spanish government announces new immigration policy.", "entity_names": ["Spanish"], "entity_types": ["location"]}, {"sentence": "Intel reveals new chip technology.", "entity_names": ["Intel"], "entity_types": ["organization"]}, {"sentence": "South Korean president meets with North Korean leader for peace talks.", "entity_names": ["South Korean", "North Korean"], "entity_types": ["location", "location"]}, {"sentence": "World Bank approves loan for infrastructure development in Africa.", "entity_names": ["World Bank", "Africa"], "entity_types": ["organization", "location"]}, {"sentence": "British Prime Minister resigns amid political scandal.", "entity_names": ["British"], "entity_types": ["location"]}, {"sentence": "New study shows link between air pollution and increased respiratory illnesses.", "entity_names": [], "entity_types": []}, {"sentence": "NASA announces plans for manned mission to Mars.", "entity_names": ["NASA", "Mars"], "entity_types": ["organization", "location"]}, {"sentence": "New York City to invest billions in public housing renovations.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "Brazilian president faces impeachment proceedings.", "entity_names": ["Brazilian"], "entity_types": ["location"]}, {"sentence": "Tesla stocks surge after strong earnings report.", "entity_names": ["Tesla"], "entity_types": ["organization"]}, {"sentence": "Hong Kong protests escalate as government crackdown intensifies.", "entity_names": ["Hong Kong"], "entity_types": ["location"]}, {"sentence": "Famous author wins Nobel Prize for Literature.", "entity_names": ["Nobel Prize"], "entity_types": ["organization"]}, {"sentence": "Paris to implement new measures to combat air pollution.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "World Economic Forum to be held in Davos.", "entity_names": ["World Economic Forum", "Davos"], "entity_types": ["organization", "location"]}, {"sentence": "Elon Musk's SpaceX launches 60 more Starlink satellites.", "entity_names": ["<PERSON><PERSON>", "SpaceX"], "entity_types": ["person", "organization"]}, {"sentence": "New York City announces plans to build new affordable housing.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "The World Health Organization declares a global health emergency.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "Apple unveils new iPhone with 5G capabilities.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "Several European countries impose new travel restrictions.", "entity_names": [], "entity_types": []}, {"sentence": "Amazon expands its warehouse network in Texas.", "entity_names": ["Amazon", "Texas"], "entity_types": ["organization", "location"]}, {"sentence": "Olympic swimmer <PERSON> sets new world record.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "The United Nations calls for ceasefire in ongoing conflict.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "California wildfires continue to threaten homes and wildlife.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Facebook introduces new privacy features for users.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "CEO of Microsoft announces plans for expansion in Asia.", "entity_names": ["Microsoft", "Asia"], "entity_types": ["organization", "location"]}, {"sentence": "Former President <PERSON> to release new memoir.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Paris fashion week goes virtual amid pandemic.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "WHO investigates new strain of avian flu in China.", "entity_names": ["WHO", "China"], "entity_types": ["organization", "location"]}, {"sentence": "SpaceX founder <PERSON><PERSON> launches new electric car company.", "entity_names": ["SpaceX", "<PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "London mayor announces new transportation initiatives.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "Google faces antitrust investigation from European Union.", "entity_names": ["Google", "European Union"], "entity_types": ["organization", "organization"]}, {"sentence": "<PERSON> meets with leaders from various religious organizations.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Music festival in New Orleans canceled due to COVID-19 concerns.", "entity_names": ["New Orleans"], "entity_types": ["location"]}, {"sentence": "Tesla CEO <PERSON><PERSON> becomes world's richest person.", "entity_names": ["Tesla", "<PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "South Africa imposes new lockdown measures to curb COVID-19 cases.", "entity_names": ["South Africa"], "entity_types": ["location"]}, {"sentence": "<PERSON> donates millions to refugee relief efforts.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "London-based pharmaceutical company announces breakthrough in cancer research.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "Japan to host 2021 Summer Olympics despite pandemic concerns.", "entity_names": ["Japan"], "entity_types": ["location"]}, {"sentence": "CEO of Amazon <PERSON> steps down from position.", "entity_names": ["Amazon", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Wildfires in Australia lead to unprecedented destruction.", "entity_names": ["Australia"], "entity_types": ["location"]}, {"sentence": "New York City to invest in infrastructure improvements.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "SpaceX successfully lands rocket on drone ship.", "entity_names": ["SpaceX"], "entity_types": ["organization"]}, {"sentence": "Elon Musk's Tesla announces plans for new electric pickup truck.", "entity_names": ["<PERSON><PERSON>", "Tesla"], "entity_types": ["person", "organization"]}, {"sentence": "France to increase police presence in response to recent terrorist attacks.", "entity_names": ["France"], "entity_types": ["location"]}, {"sentence": "CEO of Apple Tim <PERSON> unveils new line of products.", "entity_names": ["Apple", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "London prepares for Brexit transition period to end.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "Google's parent company Alphabet faces legal challenges.", "entity_names": ["Google", "Alphabet"], "entity_types": ["organization", "organization"]}, {"sentence": "Rising sea levels threaten coastal communities worldwide.", "entity_names": [], "entity_types": []}, {"sentence": "South Korean president addresses nation in New Year's speech.", "entity_names": [], "entity_types": []}, {"sentence": "Ford CEO announces plans for increased investment in electric vehicles.", "entity_names": ["Ford"], "entity_types": ["organization"]}, {"sentence": "Germany reports record number of COVID-19 cases.", "entity_names": ["Germany"], "entity_types": ["location"]}, {"sentence": "Microsoft co-founder <PERSON> launches new initiative to combat climate change.", "entity_names": ["Microsoft", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "World Bank provides financial assistance to developing countries.", "entity_names": ["World Bank"], "entity_types": ["organization"]}, {"sentence": "Australian Open tennis tournament to proceed with limited audience.", "entity_names": ["Australian Open"], "entity_types": ["organization"]}, {"sentence": "Hong Kong police arrest pro-democracy activists.", "entity_names": ["Hong Kong"], "entity_types": ["location"]}, {"sentence": "<PERSON><PERSON> faces legal challenge over driver classification.", "entity_names": ["Uber"], "entity_types": ["organization"]}, {"sentence": "Apple unveils new iPhone 12 models.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "President <PERSON><PERSON> delivers inaugural address.", "entity_names": ["Biden"], "entity_types": ["person"]}, {"sentence": "England imposes new lockdown measures.", "entity_names": ["England"], "entity_types": ["location"]}, {"sentence": "Tokyo Olympics postponed due to COVID-19.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "Prime Minister <PERSON><PERSON><PERSON> announces new climate initiative.", "entity_names": ["<PERSON><PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Germany extends lockdown restrictions.", "entity_names": ["Germany"], "entity_types": ["location"]}, {"sentence": "Amazon launches new delivery drone service.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "CEO of Twitter resigns.", "entity_names": ["Twitter"], "entity_types": ["organization"]}, {"sentence": "Wildfires sweep through California.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "President <PERSON><PERSON> tests positive for COVID-19.", "entity_names": ["<PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "New York City to mandate COVID-19 vaccinations for indoor activities.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "United Nations issues statement on refugee crisis.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "California governor faces recall election.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Microsoft acquires gaming company for $7.5 billion.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "Deadly earthquake strikes Haiti.", "entity_names": ["Haiti"], "entity_types": ["location"]}, {"sentence": "Olympic gold medalist <PERSON> withdraws from competition.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "WHO declares COVID-19 a global pandemic.", "entity_names": ["WHO"], "entity_types": ["organization"]}, {"sentence": "New vaccine mandates issued for federal employees.", "entity_names": [], "entity_types": []}, {"sentence": "Facebook whistleblower reveals internal documents.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "Hurricane causes widespread damage in Louisiana.", "entity_names": ["Louisiana"], "entity_types": ["location"]}, {"sentence": "CEO of Amazon announces retirement.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "Voter turnout reaches record high in presidential election.", "entity_names": [], "entity_types": []}, {"sentence": "Canadian province Manitoba declares state of emergency.", "entity_names": ["Manitoba"], "entity_types": ["location"]}, {"sentence": "Google faces antitrust lawsuit in Europe.", "entity_names": ["Google"], "entity_types": ["organization"]}, {"sentence": "Severe flooding in India displaces thousands.", "entity_names": ["India"], "entity_types": ["location"]}, {"sentence": "Senator <PERSON> proposes wealth tax.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Major cybersecurity breach affects government agencies.", "entity_names": [], "entity_types": []}, {"sentence": "Wildfires force evacuations in Oregon.", "entity_names": ["Oregon"], "entity_types": ["location"]}, {"sentence": "Apple announces new privacy features for iPhone users.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "Pfizer seeks approval for COVID-19 vaccine booster shot.", "entity_names": ["Pfizer"], "entity_types": ["organization"]}, {"sentence": "Florida governor signs controversial new voting bill into law.", "entity_names": ["Florida"], "entity_types": ["location"]}, {"sentence": "CEO of Walmart announces ambitious sustainability goals.", "entity_names": ["Walmart"], "entity_types": ["organization"]}, {"sentence": "Death toll rises in European flooding disaster.", "entity_names": [], "entity_types": []}, {"sentence": "<PERSON><PERSON> launches new initiative to support women in workforce.", "entity_names": ["<PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "New study shows link between air pollution and increased risk of dementia.", "entity_names": [], "entity_types": []}, {"sentence": "Chicago mayor announces plan to tackle city's violence epidemic.", "entity_names": ["Chicago"], "entity_types": ["location"]}, {"sentence": "NASA announces new mission to study Europa's ocean.", "entity_names": ["NASA", "Europa"], "entity_types": ["organization", "location"]}, {"sentence": "Apple to release new iPhone model next month.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "Former President <PERSON> endorses candidate for Senate race.", "entity_names": ["Former President <PERSON>"], "entity_types": ["person"]}, {"sentence": "Wildfire in California destroys thousands of acres.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "WHO issues new guidelines for COVID-19 prevention.", "entity_names": ["WHO"], "entity_types": ["organization"]}, {"sentence": "New study finds link between air pollution and respiratory illnesses.", "entity_names": [], "entity_types": []}, {"sentence": "SpaceX launches new satellites into orbit.", "entity_names": ["SpaceX"], "entity_types": ["organization"]}, {"sentence": "High school student awarded scholarship for academic achievement.", "entity_names": [], "entity_types": []}, {"sentence": "Supreme Court justice announces retirement.", "entity_names": ["Supreme Court"], "entity_types": ["organization"]}, {"sentence": "Tornado causes widespread damage in Texas.", "entity_names": ["Texas"], "entity_types": ["location"]}, {"sentence": "International Red Cross delivers aid to refugees in Ukraine.", "entity_names": ["International Red Cross", "Ukraine"], "entity_types": ["organization", "location"]}, {"sentence": "Chinese president visits European leaders.", "entity_names": ["Chinese president"], "entity_types": ["person"]}, {"sentence": "New research from Harvard University identifies potential treatment for Alzheimer's disease.", "entity_names": ["Harvard University"], "entity_types": ["organization"]}, {"sentence": "Police arrest suspect in connection with bank robbery.", "entity_names": [], "entity_types": []}, {"sentence": "African Union condemns human rights abuses in South Sudan.", "entity_names": ["African Union", "South Sudan"], "entity_types": ["organization", "location"]}, {"sentence": "Famous actor to star in upcoming movie.", "entity_names": [], "entity_types": []}, {"sentence": "Twitter bans accounts spreading misinformation about COVID-19 vaccines.", "entity_names": ["Twitter"], "entity_types": ["organization"]}, {"sentence": "Canadian Prime Minister promises new initiatives for climate change.", "entity_names": ["Canadian Prime Minister"], "entity_types": ["person"]}, {"sentence": "US Treasury Department to provide aid to small businesses affected by pandemic.", "entity_names": ["US Treasury Department"], "entity_types": ["organization"]}, {"sentence": "Doctor warns of potential outbreak of rare virus in Africa.", "entity_names": ["Africa"], "entity_types": ["location"]}, {"sentence": "Ford Motor Company recalls thousands of vehicles for safety issues.", "entity_names": ["Ford Motor Company"], "entity_types": ["organization"]}, {"sentence": "Japanese scientist wins Nobel Prize for groundbreaking research.", "entity_names": [], "entity_types": []}, {"sentence": "UNICEF launches campaign to provide clean water to communities in need.", "entity_names": ["UNICEF"], "entity_types": ["organization"]}, {"sentence": "Amazon founder and CEO becomes world's richest person.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "Italian government introduces new measures to combat organized crime.", "entity_names": ["Italian government"], "entity_types": ["organization"]}, {"sentence": "Firefighters battle massive blaze in Australia.", "entity_names": ["Australia"], "entity_types": ["location"]}, {"sentence": "Olympic Games to be held in Paris in 2024.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "California governor signs bill to address homelessness crisis.", "entity_names": [], "entity_types": []}, {"sentence": "Researchers develop promising vaccine for Malaria.", "entity_names": [], "entity_types": []}, {"sentence": "Bank of America to expand investment in renewable energy projects.", "entity_names": ["Bank of America"], "entity_types": ["organization"]}, {"sentence": "Russian president meets with leaders from Middle East countries.", "entity_names": ["Russian president", "Middle East"], "entity_types": ["person", "location"]}, {"sentence": "Royal Caribbean Cruises announces new ship for luxury travel.", "entity_names": ["Royal Caribbean Cruises"], "entity_types": ["organization"]}, {"sentence": "University of Oxford study shows potential link between diet and mental health.", "entity_names": ["University of Oxford"], "entity_types": ["organization"]}, {"sentence": "Apple CEO unveils new product at tech conference.", "entity_names": ["Apple", "CEO"], "entity_types": ["organization", "person"]}, {"sentence": "New round of peace talks to begin in Middle East conflict.", "entity_names": ["Middle East"], "entity_types": ["location"]}, {"sentence": "British actress wins award for performance in new film.", "entity_names": ["British actress"], "entity_types": ["person"]}, {"sentence": "Volkswagen to invest billions in electric vehicle production.", "entity_names": ["Volkswagen"], "entity_types": ["organization"]}, {"sentence": "Turkish government announces plans for infrastructure improvements.", "entity_names": ["Turkish government"], "entity_types": ["organization"]}, {"sentence": "Former CEO of Google to testify before Congress.", "entity_names": ["CEO of Google", "Congress"], "entity_types": ["person", "organization"]}, {"sentence": "Solar storm could disrupt communication systems, warns scientist.", "entity_names": [], "entity_types": []}, {"sentence": "Yelp to add new features for restaurant reviews.", "entity_names": ["Yelp"], "entity_types": ["organization"]}, {"sentence": "Philippines president signs law to combat online child exploitation.", "entity_names": ["Philippines president"], "entity_types": ["person"]}, {"sentence": "New study finds correlation between social media use and depression.", "entity_names": [], "entity_types": []}, {"sentence": "IMF forecasts global economic growth in the upcoming year.", "entity_names": ["IMF"], "entity_types": ["organization"]}, {"sentence": "Apple unveils new iPhone 13 models.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "Wildfires ravage through California.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Amazon to hire 100,000 new employees.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "European Union imposes new tariffs on Chinese imports.", "entity_names": ["European Union"], "entity_types": ["organization"]}, {"sentence": "UN report highlights climate change crisis.", "entity_names": ["UN"], "entity_types": ["organization"]}, {"sentence": "New York City to invest in affordable housing projects.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "Researchers discover new species in the Amazon rainforest.", "entity_names": ["Amazon"], "entity_types": ["location"]}, {"sentence": "Tokyo Olympics postponed due to COVID-19 outbreak.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "Walmart to raise minimum wage for employees.", "entity_names": ["Walmart"], "entity_types": ["organization"]}, {"sentence": "<PERSON> wins reelection as German Chancellor.", "entity_names": ["<PERSON>", "German Chancellor"], "entity_types": ["person", "organization"]}, {"sentence": "United Nations calls for ceasefire in Middle East conflict.", "entity_names": ["United Nations", "Middle East"], "entity_types": ["organization", "location"]}, {"sentence": "California governor signs new climate change legislation.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Twitter faces backlash over handling of misinformation.", "entity_names": ["Twitter"], "entity_types": ["organization"]}, {"sentence": "British royal family celebrates the Queen's birthday.", "entity_names": ["Queen"], "entity_types": ["person"]}, {"sentence": "NASA's Mars rover captures new images of the Martian landscape.", "entity_names": ["NASA", "Mars"], "entity_types": ["organization", "location"]}, {"sentence": "Volkswagen announces plans to go all-electric by 2035.", "entity_names": ["Volkswagen"], "entity_types": ["organization"]}, {"sentence": "Australian Prime Minister visits wildfire affected areas.", "entity_names": ["Australian", "Prime Minister"], "entity_types": ["location", "person"]}, {"sentence": "Facebook launches new feature to combat cyberbullying.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "Paris fashion week showcases latest designer collections.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "CEO of Tesla tweets controversial statement.", "entity_names": ["Tesla"], "entity_types": ["organization"]}, {"sentence": "Russian President <PERSON> meets with Chinese leader <PERSON>.", "entity_names": ["<PERSON>", "Xi <PERSON>ping"], "entity_types": ["person", "person"]}, {"sentence": "Australian Open tennis tournament cancelled due to COVID-19.", "entity_names": ["Australian Open"], "entity_types": ["organization"]}, {"sentence": "<PERSON> donates millions to combat malaria in Africa.", "entity_names": ["<PERSON>", "Africa"], "entity_types": ["person", "location"]}, {"sentence": "Starbucks to introduce new environmentally friendly cup design.", "entity_names": ["Starbucks"], "entity_types": ["organization"]}, {"sentence": "Harvard University announces new scholarship program.", "entity_names": ["Harvard University"], "entity_types": ["organization"]}, {"sentence": "New Orleans prepares for hurricane season.", "entity_names": ["New Orleans"], "entity_types": ["location"]}, {"sentence": "South Korean K-pop group BTS breaks new record with latest album.", "entity_names": ["South Korean", "BTS"], "entity_types": ["location", "organization"]}, {"sentence": "London mayor announces plans for public transportation expansion.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "Canadian Prime Minister <PERSON> visits Indigenous communities.", "entity_names": ["Canadian", "<PERSON>", "Indigenous"], "entity_types": ["location", "person", "organization"]}, {"sentence": "SpaceX to launch new satellite internet service.", "entity_names": ["SpaceX"], "entity_types": ["organization"]}, {"sentence": "World Economic Forum held in Davos, Switzerland.", "entity_names": ["World Economic Forum", "Davos", "Switzerland"], "entity_types": ["organization", "location", "location"]}, {"sentence": "<PERSON> visits refugee camp in Greece.", "entity_names": ["<PERSON>", "Greece"], "entity_types": ["person", "location"]}, {"sentence": "BMW recalls thousands of vehicles for safety issues.", "entity_names": ["BMW"], "entity_types": ["organization"]}, {"sentence": "New study reveals alarming decline in bee populations.", "entity_names": [], "entity_types": []}, {"sentence": "Indian government announces new COVID-19 vaccine distribution plan.", "entity_names": ["Indian"], "entity_types": ["location"]}, {"sentence": "Microsoft acquires gaming company for $7 billion.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "Sydney Opera House reopens to the public.", "entity_names": ["Sydney Opera House"], "entity_types": ["location"]}, {"sentence": "Indonesian president calls for increased cooperation on climate change.", "entity_names": ["president"], "entity_types": ["person"]}, {"sentence": "Starbucks to close hundreds of stores in the United States.", "entity_names": ["Starbucks", "United States"], "entity_types": ["organization", "location"]}, {"sentence": "Nigerian author wins Nobel Prize for literature.", "entity_names": ["Nigerian", "Nobel Prize"], "entity_types": ["location", "organization"]}, {"sentence": "New York Mets sign top free agent pitcher.", "entity_names": ["New York Mets"], "entity_types": ["organization"]}, {"sentence": "Google's parent company Alphabet announces quarterly earnings.", "entity_names": ["Google", "Alphabet"], "entity_types": ["organization", "organization"]}, {"sentence": "The United States reported a record number of daily COVID-19 cases.", "entity_names": ["United States"], "entity_types": ["location"]}, {"sentence": "Apple Inc. announced a new line of products at their annual conference.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}, {"sentence": "Actress <PERSON> is rumored to be dating a tech entrepreneur.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "The European Union is considering new sanctions against Russia.", "entity_names": ["European Union", "Russia"], "entity_types": ["organization", "location"]}, {"sentence": "Tesla CEO <PERSON><PERSON> made headlines with his latest controversial tweet.", "entity_names": ["Tesla", "<PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Japan will host the 2021 Olympics despite concerns over the COVID-19 pandemic.", "entity_names": ["Japan"], "entity_types": ["location"]}, {"sentence": "The World Health Organization issued a warning about a new strain of the flu virus.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "President <PERSON> signed a new executive order aimed at addressing climate change.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Pop star <PERSON> released a new album, causing a frenzy among fans.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "The United Nations is urging for immediate humanitarian aid to be sent to the war-torn region.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "Microsoft announced a major security breach that affected millions of users.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "South Africa is experiencing a severe drought, leading to food shortages.", "entity_names": ["South Africa"], "entity_types": ["location"]}, {"sentence": "Olympic swimmer <PERSON> broke another world record in the 100m butterfly event.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Facebook is facing criticism for its handling of misinformation on the platform.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "The Amazon rainforest is at risk of widespread deforestation due to illegal logging.", "entity_names": ["Amazon"], "entity_types": ["location"]}, {"sentence": "The United States imposes new sanctions on Russia.", "entity_names": ["United States", "Russia"], "entity_types": ["organization", "location"]}, {"sentence": "CEO of Apple, <PERSON>, announces new product launch.", "entity_names": ["Apple", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "China surpasses the United States in renewable energy production.", "entity_names": ["China", "United States"], "entity_types": ["location", "location"]}, {"sentence": "Famous actress <PERSON> visits refugee camp in Syria.", "entity_names": ["<PERSON>", "Syria"], "entity_types": ["person", "location"]}, {"sentence": "The European Union issues a statement condemning human rights abuses in Myanmar.", "entity_names": ["European Union", "Myanmar"], "entity_types": ["organization", "location"]}, {"sentence": "Scientist <PERSON>'s research on black holes creates a breakthrough in astrophysics.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Indian Prime Minister <PERSON><PERSON><PERSON> proposes new trade agreement with Japan.", "entity_names": ["<PERSON><PERSON><PERSON>", "Japan"], "entity_types": ["person", "location"]}, {"sentence": "Germany experiences record-breaking heatwave.", "entity_names": ["Germany"], "entity_types": ["location"]}, {"sentence": "A new study by the World Health Organization reveals the impact of air pollution on public health.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "The United Nations launches investigation into war crimes in Yemen.", "entity_names": ["United Nations", "Yemen"], "entity_types": ["organization", "location"]}, {"sentence": "Amazon CEO <PERSON> becomes the world's richest person.", "entity_names": ["Amazon", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "South Africa undergoes political turmoil as corruption scandal unfolds.", "entity_names": ["South Africa"], "entity_types": ["location"]}, {"sentence": "The G7 summit ends in a stalemate over trade negotiations.", "entity_names": ["G7"], "entity_types": ["organization"]}, {"sentence": "British author <PERSON><PERSON><PERSON><PERSON> announces new book release.", "entity_names": ["British", "<PERSON><PERSON><PERSON><PERSON>"], "entity_types": ["location", "person"]}, {"sentence": "The World Bank commits $1 billion in aid to developing countries.", "entity_names": ["World Bank"], "entity_types": ["organization"]}, {"sentence": "French President <PERSON> proposes new climate change legislation.", "entity_names": ["French", "<PERSON>"], "entity_types": ["location", "person"]}, {"sentence": "Irish rock band U2 announces farewell tour.", "entity_names": ["Irish", "U2"], "entity_types": ["location", "organization"]}, {"sentence": "The International Monetary Fund warns of global economic slowdown.", "entity_names": ["International Monetary Fund"], "entity_types": ["organization"]}, {"sentence": "Canadian singer <PERSON><PERSON> cancels world tour due to health issues.", "entity_names": ["Canadian", "<PERSON><PERSON>"], "entity_types": ["location", "person"]}, {"sentence": "The Central Intelligence Agency unveils new counter-terrorism initiatives.", "entity_names": ["Central Intelligence Agency"], "entity_types": ["organization"]}, {"sentence": "Italian fashion house Gucci releases new designer collection.", "entity_names": ["Italian", "<PERSON><PERSON>"], "entity_types": ["location", "organization"]}, {"sentence": "The International Olympic Committee announces new host city for the 2032 Summer Olympics.", "entity_names": ["International Olympic Committee"], "entity_types": ["organization"]}, {"sentence": "Swiss tennis player <PERSON> wins Wimbledon championship.", "entity_names": ["Swiss", "<PERSON>"], "entity_types": ["location", "person"]}, {"sentence": "The World Trade Organization reports record-breaking trade deficit.", "entity_names": ["World Trade Organization"], "entity_types": ["organization"]}, {"sentence": "Brazilian President <PERSON><PERSON> faces criticism over environmental policies.", "entity_names": ["Brazilian", "<PERSON><PERSON>"], "entity_types": ["location", "person"]}, {"sentence": "The United Nations Children's Fund provides aid to refugee children in Syria.", "entity_names": ["United Nations Children's Fund", "Syria"], "entity_types": ["organization", "location"]}, {"sentence": "American tech giant Microsoft reveals new cybersecurity strategy.", "entity_names": ["American", "Microsoft"], "entity_types": ["location", "organization"]}, {"sentence": "Japanese automaker Toyota announces recall of over 1 million vehicles.", "entity_names": ["Japanese", "Toyota"], "entity_types": ["location", "organization"]}, {"sentence": "The World Economic Forum publishes global competitiveness report.", "entity_names": ["World Economic Forum"], "entity_types": ["organization"]}, {"sentence": "British Prime Minister <PERSON> faces criticism over handling of COVID-19 pandemic.", "entity_names": ["British", "<PERSON>"], "entity_types": ["location", "person"]}, {"sentence": "The European Space Agency launches new Mars rover mission.", "entity_names": ["European Space Agency", "Mars"], "entity_types": ["organization", "location"]}, {"sentence": "Chinese e-commerce company Alibaba reports significant profit growth.", "entity_names": ["Chinese", "Alibaba"], "entity_types": ["location", "organization"]}, {"sentence": "The International Criminal Court issues arrest warrant for war crimes suspect in Sudan.", "entity_names": ["International Criminal Court", "Sudan"], "entity_types": ["organization", "location"]}, {"sentence": "American basketball player <PERSON><PERSON><PERSON> signs record-breaking contract with Los Angeles Lakers.", "entity_names": ["American", "<PERSON><PERSON><PERSON>", "Los Angeles Lakers"], "entity_types": ["location", "person", "organization"]}, {"sentence": "Indian pharmaceutical company Cipla receives approval for new drug in the US.", "entity_names": ["Indian", "Cipla", "US"], "entity_types": ["location", "organization", "location"]}, {"sentence": "The Economic Community of West African States imposes sanctions on Mali.", "entity_names": ["Economic Community of West African States", "Mali"], "entity_types": ["organization", "location"]}, {"sentence": "Russian President <PERSON> holds summit with North Korean leader <PERSON>.", "entity_names": ["<PERSON>", "North Korean", "<PERSON>"], "entity_types": ["person", "location", "person"]}, {"sentence": "Microsoft co-founder <PERSON> donates $1 billion to global health initiatives.", "entity_names": ["Microsoft", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "The International Labor Organization advocates for workers' rights in the gig economy.", "entity_names": ["International Labor Organization"], "entity_types": ["organization"]}, {"sentence": "American chef <PERSON> opens new restaurant in London.", "entity_names": ["American", "<PERSON>", "London"], "entity_types": ["location", "person", "location"]}, {"sentence": "The World Food Programme delivers aid to famine-stricken regions in Ethiopia.", "entity_names": ["World Food Programme", "Ethiopia"], "entity_types": ["organization", "location"]}, {"sentence": "South Korean boy band BTS breaks streaming record with new album release.", "entity_names": ["BTS"], "entity_types": ["organization"]}, {"sentence": "The United Nations High Commissioner for Refugees provides emergency assistance to Rohingya refugees in Bangladesh.", "entity_names": ["United Nations High Commissioner for Refugees", "Rohingya", "Bangladesh"], "entity_types": ["organization", "organization", "location"]}, {"sentence": "Amazon founder <PERSON> launches space exploration company Blue Origin.", "entity_names": ["Amazon", "<PERSON>", "Blue Origin"], "entity_types": ["organization", "person", "organization"]}, {"sentence": "German Chancellor <PERSON> meets with French President <PERSON> to discuss economic policies.", "entity_names": ["<PERSON>", "French", "<PERSON>"], "entity_types": ["person", "location", "person"]}, {"sentence": "The World Health Organization declares new strain of flu virus a global health emergency.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "American singer <PERSON> donates $1 million to tornado relief efforts in Tennessee.", "entity_names": ["American", "<PERSON>", "Tennessee"], "entity_types": ["location", "person", "location"]}, {"sentence": "The European Union Parliament votes against proposed trade agreement with China.", "entity_names": ["European Union", "China"], "entity_types": ["organization", "location"]}, {"sentence": "New Study Shows Link Between Coffee and Longevity", "entity_names": [], "entity_types": []}, {"sentence": "Apple Announces Release of New iPhone Model", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "President <PERSON><PERSON> Signs Executive Order on Climate Change", "entity_names": ["President <PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Wildfires Threaten Homes in California", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "WHO Warns of Potential COVID-19 Surge in Europe", "entity_names": ["WHO", "Europe"], "entity_types": ["organization", "location"]}, {"sentence": "Elon Musk's SpaceX Launches New Satellite into Orbit", "entity_names": ["<PERSON><PERSON>", "SpaceX"], "entity_types": ["person", "organization"]}, {"sentence": "<PERSON> Releases New Skincare Line", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Florida Governor Signs Controversial Voting Bill into Law", "entity_names": ["Florida"], "entity_types": ["location"]}, {"sentence": "Tokyo Olympics to Proceed Without Spectators", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "Microsoft Reports Record Profits in Q2", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "New York City Prepares for Influx of Tourists", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "Study Finds Link Between Sleep and Mental Health", "entity_names": [], "entity_types": []}, {"sentence": "United Nations issues statement on humanitarian crisis in Yemen", "entity_names": ["United Nations", "Yemen"], "entity_types": ["organization", "location"]}, {"sentence": "Amazon Announces Plan to Build New Headquarters", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "Biden Administration Announces Infrastructure Investment Plan", "entity_names": ["Biden"], "entity_types": ["person"]}, {"sentence": "Hurricane Florence Causes Devastation in North Carolina", "entity_names": ["Hurricane Florence", "North Carolina"], "entity_types": ["organization", "location"]}, {"sentence": "French Open Champion Advances to Quarterfinals", "entity_names": ["French Open"], "entity_types": ["organization"]}, {"sentence": "Google Faces Antitrust Lawsuit from EU", "entity_names": ["Google", "EU"], "entity_types": ["organization", "organization"]}, {"sentence": "Germany to Host G7 Summit Next Year", "entity_names": ["Germany", "G7"], "entity_types": ["location", "organization"]}, {"sentence": "CEO of Tesla Elon Musk Denies Securities Fraud Allegations", "entity_names": ["CEO", "Tesla", "<PERSON><PERSON>"], "entity_types": ["organization", "organization", "person"]}, {"sentence": "<PERSON> Announces Stadium Tour Dates", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Political Unrest in Venezuela Escalates", "entity_names": ["Venezuela"], "entity_types": ["location"]}, {"sentence": "NASA Prepares to Launch New Mars Rover", "entity_names": ["NASA", "Mars"], "entity_types": ["organization", "location"]}, {"sentence": "Starbucks to Open 200 New Stores in China", "entity_names": ["Starbucks", "China"], "entity_types": ["organization", "location"]}, {"sentence": "Canadian Prime Minister <PERSON> Ann<PERSON>nces New Housing Plan", "entity_names": ["Canadian", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Typhoon Hits Philippines, Leaving Thousands Displaced", "entity_names": ["Philippines"], "entity_types": ["location"]}, {"sentence": "Facebook Faces Scrutiny Over Data Privacy Issues", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "Actress <PERSON> Welcomes Baby Girl", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "World Bank Releases Report on Global Poverty Rates", "entity_names": ["World Bank"], "entity_types": ["organization"]}, {"sentence": "5.8 Magnitude Earthquake Strikes Japan", "entity_names": ["Japan"], "entity_types": ["location"]}, {"sentence": "New Research Indicates Potential Breakthrough in Cancer Treatment", "entity_names": [], "entity_types": []}, {"sentence": "AstraZeneca Announces Plans for COVID-19 Vaccine Distribution", "entity_names": ["AstraZeneca"], "entity_types": ["organization"]}, {"sentence": "Storm Causes Flooding in Australia's East Coast", "entity_names": ["Australia"], "entity_types": ["location"]}, {"sentence": "Major League Baseball Commissioner Addresses Player Strikes", "entity_names": ["Major League Baseball"], "entity_types": ["organization"]}, {"sentence": "Pop Star <PERSON><PERSON><PERSON> Launches New Fashion Line", "entity_names": ["<PERSON><PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "World Health Organization Issues Warning on Antibiotic Resistance", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "Texas Governor Signs Controversial Abortion Bill into Law", "entity_names": ["Texas"], "entity_types": ["location"]}, {"sentence": "Kentucky Derby Winner <PERSON> Positive for Banned Substance", "entity_names": ["Kentucky"], "entity_types": ["location"]}, {"sentence": "<PERSON> as CEO of Amazon", "entity_names": ["<PERSON>", "Amazon"], "entity_types": ["person", "organization"]}, {"sentence": "European Union Approves New Climate Change Regulations", "entity_names": ["European Union"], "entity_types": ["organization"]}, {"sentence": "South African President <PERSON> Addresses Economic Challenges", "entity_names": ["South African", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "New Zealand to Implement Stricter Gun Control Measures", "entity_names": ["New Zealand"], "entity_types": ["location"]}, {"sentence": "McDonald's Announces Plant-Based Burger Option", "entity_names": ["McDonald's"], "entity_types": ["organization"]}, {"sentence": "Nigerian President <PERSON><PERSON> Meets with U.S. Secretary of State", "entity_names": ["Nigerian", "<PERSON><PERSON>", "U.S."], "entity_types": ["organization", "person", "location"]}, {"sentence": "<PERSON> Wins Album of the Year at Grammys", "entity_names": ["<PERSON>", "Grammys"], "entity_types": ["person", "organization"]}, {"sentence": "FBI Investigates Cyberattack on Major U.S. Corporation", "entity_names": ["FBI", "U.S."], "entity_types": ["organization", "location"]}, {"sentence": "Italy Faces Political Turmoil Amidst Government Resignations", "entity_names": ["Italy"], "entity_types": ["location"]}, {"sentence": "Zara Unveils Sustainable Fashion Collection", "entity_names": ["<PERSON><PERSON>"], "entity_types": ["organization"]}, {"sentence": "Microsoft CEO <PERSON><PERSON><PERSON> Discusses Future of Technology at Summit", "entity_names": ["Microsoft", "<PERSON><PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Apple launches new iPhone 12.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "Tokyo Olympics postponed until 2021 due to COVID-19.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "Amazon announces plans to open new headquarters in Texas.", "entity_names": ["Amazon", "Texas"], "entity_types": ["organization", "location"]}, {"sentence": "Elon Musk's SpaceX successfully launches new satellite into space.", "entity_names": ["<PERSON><PERSON>", "SpaceX"], "entity_types": ["person", "organization"]}, {"sentence": "California wildfires continue to threaten homes.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Pfizer and Moderna announce progress on COVID-19 vaccine.", "entity_names": ["Pfizer", "Moderna"], "entity_types": ["organization", "organization"]}, {"sentence": "New York City to invest billions in renewable energy projects.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "<PERSON> wins gold at gymnastics world championships.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Tesla to build new gigafactory in Germany.", "entity_names": ["Tesla", "Germany"], "entity_types": ["organization", "location"]}, {"sentence": "South Africa to implement new COVID-19 restrictions.", "entity_names": ["South Africa"], "entity_types": ["location"]}, {"sentence": "The United Nations has declared a humanitarian crisis in the war-torn country.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "The CEO of Google, <PERSON><PERSON>, announced their plans to invest in renewable energy.", "entity_names": ["Google", "<PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "The annual meeting of the World Health Organization will take place in Geneva next month.", "entity_names": ["World Health Organization", "Geneva"], "entity_types": ["organization", "location"]}, {"sentence": "The Prime Minister of Canada, <PERSON>, unveiled a new economic stimulus package.", "entity_names": ["Canada", "<PERSON>"], "entity_types": ["location", "person"]}, {"sentence": "The United States experienced a record-breaking heatwave last week, with temperatures reaching over 100 degrees in some areas.", "entity_names": ["United States"], "entity_types": ["location"]}, {"sentence": "The European Union is implementing new regulations to improve data privacy for its citizens.", "entity_names": ["European Union"], "entity_types": ["organization"]}, {"sentence": "The Mayor of London, <PERSON><PERSON>, announced plans to expand the city's public transportation system.", "entity_names": ["London", "<PERSON><PERSON>"], "entity_types": ["location", "person"]}, {"sentence": "NASA is preparing for a historic mission to Mars, with plans to send humans to the red planet by 2030.", "entity_names": ["NASA", "Mars"], "entity_types": ["organization", "location"]}, {"sentence": "The CEO of Microsoft, <PERSON><PERSON><PERSON>, revealed the company's plans for expansion into the healthcare industry.", "entity_names": ["Microsoft", "<PERSON><PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "The United Nations Climate Change Conference will be held in Glasgow later this year.", "entity_names": ["United Nations", "Glasgow"], "entity_types": ["organization", "location"]}, {"sentence": "President <PERSON><PERSON> signs executive order to boost cybersecurity.", "entity_names": ["President <PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "New York City announces plan to reopen all schools in September.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "Apple unveils new iPhone with upgraded camera and battery life.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "Scientists discover new species of butterfly in the Amazon rainforest.", "entity_names": ["Amazon"], "entity_types": ["location"]}, {"sentence": "Tesla CEO <PERSON><PERSON> becomes the world's richest person.", "entity_names": ["Tesla", "<PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "United Nations reports record-breaking temperatures in Antarctica.", "entity_names": ["United Nations", "Antarctica"], "entity_types": ["organization", "location"]}, {"sentence": "Former President <PERSON> to release memoir this fall.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Hurricane warning issued for coastal areas of Florida.", "entity_names": ["Florida"], "entity_types": ["location"]}, {"sentence": "Google to invest $1 billion in renewable energy projects.", "entity_names": ["Google"], "entity_types": ["organization"]}, {"sentence": "Legendary musician <PERSON> announces new album.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "The U.S. House of Representatives passed the bill with a vote of 220-212.", "entity_names": ["U.S. House of Representatives"], "entity_types": ["organization"]}, {"sentence": "President <PERSON><PERSON> issued a statement on the ongoing conflict in the Middle East.", "entity_names": ["President <PERSON><PERSON>", "Middle East"], "entity_types": ["person", "location"]}, {"sentence": "The World Health Organization declared the Ebola outbreak a public health emergency.", "entity_names": ["World Health Organization", "Ebola"], "entity_types": ["organization", "organization"]}, {"sentence": "Several residents in New York City reported sightings of a large black bear in the area.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "Elon Musk's SpaceX successfully launched another batch of Starlink satellites.", "entity_names": ["<PERSON><PERSON>", "SpaceX", "Starlink"], "entity_types": ["person", "organization", "organization"]}, {"sentence": "The European Union imposed sanctions on Belarus for human rights abuses.", "entity_names": ["European Union", "Belarus"], "entity_types": ["organization", "location"]}, {"sentence": "The singer <PERSON> released a new album, gaining widespread acclaim from fans.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "California Governor <PERSON> signed a bill to address climate change in the state.", "entity_names": ["California", "<PERSON>"], "entity_types": ["location", "person"]}, {"sentence": "The United Nations announced a new initiative to combat global poverty and hunger.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "Thousands of protesters gathered in Hong Kong to demand democratic reforms.", "entity_names": ["Hong Kong"], "entity_types": ["location"]}, {"sentence": "<PERSON><PERSON> reveals plans for a manned mission to Mars.", "entity_names": ["<PERSON><PERSON>", "Mars"], "entity_types": ["person", "location"]}, {"sentence": "Facebook faces backlash over data privacy concerns.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "President <PERSON><PERSON> announces new infrastructure proposal.", "entity_names": ["President <PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Tokyo Olympics set to begin next week.", "entity_names": ["Tokyo", "Olympics"], "entity_types": ["location", "organization"]}, {"sentence": "Researchers discover new species of deep-sea jellyfish.", "entity_names": [], "entity_types": []}, {"sentence": "Europe experiences record-breaking heatwave.", "entity_names": ["Europe"], "entity_types": ["location"]}, {"sentence": "Stock market hits record high as tech companies soar.", "entity_names": [], "entity_types": []}, {"sentence": "New York City subway system faces budget crisis.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "The United Nations condemns human rights abuses in Myanmar.", "entity_names": ["United Nations", "Myanmar"], "entity_types": ["organization", "location"]}, {"sentence": "Apple unveils latest iPhone model.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "China surpasses United States in renewable energy production.", "entity_names": ["China", "United States"], "entity_types": ["location", "location"]}, {"sentence": "Prime Minister <PERSON><PERSON> meets with Russian President <PERSON>.", "entity_names": ["Prime Minister <PERSON><PERSON>", "Russian President <PERSON>"], "entity_types": ["person", "person"]}, {"sentence": "Pfizer announces new vaccine efficacy study results.", "entity_names": ["Pfizer"], "entity_types": ["organization"]}, {"sentence": "Hurricane Florence devastates coastal communities.", "entity_names": ["Hurricane Florence"], "entity_types": ["location"]}, {"sentence": "Tesla reports record profits for the quarter.", "entity_names": ["Tesla"], "entity_types": ["organization"]}, {"sentence": "Google faces antitrust probe from European Union.", "entity_names": ["Google", "European Union"], "entity_types": ["organization", "organization"]}, {"sentence": "CEO of Amazon steps down from role.", "entity_names": ["CEO of Amazon"], "entity_types": ["organization"]}, {"sentence": "Massive wildfire threatens California town.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "The United States announces new sanctions on Russia.", "entity_names": ["United States", "Russia"], "entity_types": ["location", "location"]}, {"sentence": "CEO of Apple Inc. steps down from his position.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}, {"sentence": "Global warming continues to be a concern for scientists.", "entity_names": [], "entity_types": []}, {"sentence": "President of France visits Germany for bilateral talks.", "entity_names": ["France", "Germany"], "entity_types": ["location", "location"]}, {"sentence": "New research shows that coffee consumption may have health benefits.", "entity_names": [], "entity_types": []}, {"sentence": "Severe flooding reported in several areas of India.", "entity_names": ["India"], "entity_types": ["location"]}, {"sentence": "Tesla announces plans to build new factory in Texas.", "entity_names": ["Tesla", "Texas"], "entity_types": ["organization", "location"]}, {"sentence": "Hollywood actress wins Oscar for Best Actress.", "entity_names": [], "entity_types": []}, {"sentence": "United Nations calls for immediate ceasefire in war-torn region.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "The president of the United States issued a statement regarding the recent weather disasters.", "entity_names": ["United States"], "entity_types": ["location"]}, {"sentence": "Sales of the new iPhone model exceeded expectations in the first quarter.", "entity_names": [], "entity_types": []}, {"sentence": "The CEO of Apple Inc. announced a new sustainability initiative.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}, {"sentence": "Tensions continue to rise between North Korea and South Korea.", "entity_names": ["North Korea", "South Korea"], "entity_types": ["location", "location"]}, {"sentence": "<PERSON> wins Grammy for Album of the Year.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "The United Nations declared a humanitarian crisis in the war-torn region.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "Thousands gather in Times Square to celebrate the New Year.", "entity_names": ["Times Square"], "entity_types": ["location"]}, {"sentence": "The Prime Minister of Canada addressed the nation about the new policies.", "entity_names": ["Canada"], "entity_types": ["location"]}, {"sentence": "Tesla Motors unveils plans for a new electric vehicle.", "entity_names": ["Tesla Motors"], "entity_types": ["organization"]}, {"sentence": "The famous chef opened a new restaurant in downtown Manhattan.", "entity_names": [], "entity_types": []}, {"sentence": "China launches new satellite into orbit.", "entity_names": ["China"], "entity_types": ["location"]}, {"sentence": "The outbreak of a new virus threatens public health.", "entity_names": [], "entity_types": []}, {"sentence": "Amazon.com announces record-breaking sales during the holiday season.", "entity_names": ["Amazon.com"], "entity_types": ["organization"]}, {"sentence": "The European Union criticizes the new trade agreement.", "entity_names": ["European Union"], "entity_types": ["organization"]}, {"sentence": "The Duchess of Cambridge visits a children's hospital in London.", "entity_names": ["Duchess of Cambridge", "London"], "entity_types": ["person", "location"]}, {"sentence": "CEO of Microsoft announces new strategic partnership.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "The United Nations condemns the recent human rights violations in Syria.", "entity_names": ["United Nations", "Syria"], "entity_types": ["organization", "location"]}, {"sentence": "Famous actor <PERSON> is set to star in a new blockbuster film.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "The World Health Organization reports an increase in cases of malaria in Africa.", "entity_names": ["World Health Organization", "Africa"], "entity_types": ["organization", "location"]}, {"sentence": "President of France announces new environmental initiatives.", "entity_names": ["France"], "entity_types": ["location"]}, {"sentence": "Thousands gather in protest against government corruption in Brazil.", "entity_names": ["Brazil"], "entity_types": ["location"]}, {"sentence": "New study shows the impact of climate change on polar bear populations.", "entity_names": [], "entity_types": []}, {"sentence": "The European Union imposes sanctions on Belarus for human rights abuses.", "entity_names": ["European Union", "Belarus"], "entity_types": ["organization", "location"]}, {"sentence": "Apple Inc. reveals plans for a new line of products.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}, {"sentence": "Apple launches new iPhone model.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "Tokyo experiences record-breaking heatwave.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "Famous singer <PERSON> announces new album release.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "United Nations passes resolution on climate change.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "Los Angeles Dodgers win World Series.", "entity_names": ["Los Angeles Dodgers"], "entity_types": ["organization"]}, {"sentence": "President of the United States signs new trade agreement.", "entity_names": ["President of the United States"], "entity_types": ["person"]}, {"sentence": "London sees surge in COVID-19 cases.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "Amazon hires 100,000 new employees.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "<PERSON><PERSON><PERSON> signs record-breaking contract with new team.", "entity_names": ["<PERSON><PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Elon Musk's SpaceX successfully launches 60 Starlink satellites into orbit.", "entity_names": ["<PERSON><PERSON>", "SpaceX"], "entity_types": ["person", "organization"]}, {"sentence": "New York City Mayor announces plan to improve public transportation system.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "Apple Inc. unveils new iPhone with groundbreaking features.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}, {"sentence": "Famous actor faces backlash for controversial social media post.", "entity_names": ["actor"], "entity_types": ["person"]}, {"sentence": "California experiences record-breaking heatwave.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "United Nations report highlights worsening global hunger crisis.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "Miami Beach imposes new restrictions to control spring break crowds.", "entity_names": ["Miami Beach"], "entity_types": ["location"]}, {"sentence": "World Health Organization declares Ebola outbreak a global health emergency.", "entity_names": ["World Health Organization", "Ebola"], "entity_types": ["organization", "organization"]}, {"sentence": "The CEO of Apple Inc. announced a new product launch.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}, {"sentence": "The mayor of New York City revealed plans for a new subway system.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "The United Nations released a report on climate change.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "A local charity organization raised $1 million for a new shelter.", "entity_names": [], "entity_types": []}, {"sentence": "The prime minister of Japan visited the United States for trade talks.", "entity_names": ["Japan", "United States"], "entity_types": ["location", "location"]}, {"sentence": "Google's parent company, Alphabet Inc., reported record profits.", "entity_names": ["Google", "Alphabet Inc."], "entity_types": ["organization", "organization"]}, {"sentence": "The earthquake in California caused widespread damage.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "The European Union imposed new tariffs on imported goods.", "entity_names": ["European Union"], "entity_types": ["organization"]}, {"sentence": "The president of France signed a new trade agreement with Germany.", "entity_names": ["France", "Germany"], "entity_types": ["location", "location"]}, {"sentence": "NASA launched a new satellite into orbit.", "entity_names": ["NASA"], "entity_types": ["organization"]}, {"sentence": "The United Nations Secretary-General visited conflict zones in Africa.", "entity_names": ["United Nations", "Africa"], "entity_types": ["organization", "location"]}, {"sentence": "The CEO of Tesla unveiled a new electric car model.", "entity_names": ["Tesla"], "entity_types": ["organization"]}, {"sentence": "The governor of Texas announced a state of emergency due to severe weather.", "entity_names": ["Texas"], "entity_types": ["location"]}, {"sentence": "The World Health Organization released a report on global vaccination rates.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "Apple's new headquarters in Cupertino, California, opened to the public.", "entity_names": ["Apple", "Cupertino", "California"], "entity_types": ["organization", "location", "location"]}, {"sentence": "The United Nations Security Council held an emergency meeting on the crisis in the Middle East.", "entity_names": ["United Nations Security Council", "Middle East"], "entity_types": ["organization", "location"]}, {"sentence": "The prime minister of Canada addressed the nation in a televised speech.", "entity_names": ["Canada"], "entity_types": ["location"]}, {"sentence": "Amazon Inc. faced criticism for its labor practices.", "entity_names": ["Amazon Inc."], "entity_types": ["organization"]}, {"sentence": "The mayor of London announced a new initiative to reduce air pollution.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "The International Monetary Fund warned of a global economic downturn.", "entity_names": ["International Monetary Fund"], "entity_types": ["organization"]}, {"sentence": "Facebook's CEO testified before Congress on data privacy issues.", "entity_names": ["Facebook", "Congress"], "entity_types": ["organization", "organization"]}, {"sentence": "The president of Russia met with the leaders of China and India to discuss trade agreements.", "entity_names": ["Russia", "China", "India"], "entity_types": ["location", "location", "location"]}, {"sentence": "The United Nations Human Rights Council condemned human rights abuses in North Korea.", "entity_names": ["United Nations Human Rights Council", "North Korea"], "entity_types": ["organization", "location"]}, {"sentence": "The CEO of Microsoft announced a major reorganization of the company.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "California's governor signed a new law to combat climate change.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "The United Nations Refugee Agency provided aid to displaced families in Syria.", "entity_names": ["United Nations Refugee Agency", "Syria"], "entity_types": ["organization", "location"]}, {"sentence": "The CEO of Walmart unveiled a new sustainability initiative.", "entity_names": ["Walmart"], "entity_types": ["organization"]}, {"sentence": "The mayor of Paris announced plans to improve public transportation.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "The World Trade Organization issued a statement on global trade tensions.", "entity_names": ["World Trade Organization"], "entity_types": ["organization"]}, {"sentence": "Google's CEO testified before the Senate on antitrust concerns.", "entity_names": ["Google", "Senate"], "entity_types": ["organization", "organization"]}, {"sentence": "The governor of Florida declared a state of emergency ahead of a hurricane.", "entity_names": ["Florida"], "entity_types": ["location"]}, {"sentence": "The International Atomic Energy Agency reported on nuclear non-proliferation efforts.", "entity_names": ["International Atomic Energy Agency"], "entity_types": ["organization"]}, {"sentence": "The CEO of Amazon Inc. announced a new initiative to combat climate change.", "entity_names": ["Amazon Inc."], "entity_types": ["organization"]}, {"sentence": "The mayor of Tokyo unveiled a plan to host the Olympic Games.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "The European Central Bank announced new monetary policy measures.", "entity_names": ["European Central Bank"], "entity_types": ["organization"]}, {"sentence": "The president of Brazil spoke at the United Nations General Assembly.", "entity_names": ["Brazil", "United Nations General Assembly"], "entity_types": ["location", "organization"]}, {"sentence": "NASA's Mars rover discovered evidence of ancient microbial life.", "entity_names": ["NASA"], "entity_types": ["organization"]}, {"sentence": "The mayor of Berlin announced a new initiative to support local businesses.", "entity_names": ["Berlin"], "entity_types": ["location"]}, {"sentence": "The World Health Organization declared a global pandemic.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "Apple Inc. faced criticism for its supply chain practices.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}, {"sentence": "The governor of Michigan signed a new education bill into law.", "entity_names": ["Michigan"], "entity_types": ["location"]}, {"sentence": "The International Monetary Fund warned of a recession.", "entity_names": ["International Monetary Fund"], "entity_types": ["organization"]}, {"sentence": "Google's parent company, Alphabet Inc., reported a data breach.", "entity_names": ["Google", "Alphabet Inc."], "entity_types": ["organization", "organization"]}, {"sentence": "The mayor of Rome announced plans to overhaul the city's infrastructure.", "entity_names": ["Rome"], "entity_types": ["location"]}, {"sentence": "The World Trade Organization issued a report on global trade trends.", "entity_names": ["World Trade Organization"], "entity_types": ["organization"]}, {"sentence": "Facebook's CEO faced questioning from Congress on misinformation.", "entity_names": ["Facebook", "Congress"], "entity_types": ["organization", "organization"]}, {"sentence": "The president of Indonesia addressed the nation in a televised speech.", "entity_names": ["Indonesia"], "entity_types": ["location"]}, {"sentence": "The World Food Programme provided aid to famine-stricken regions in Africa.", "entity_names": ["World Food Programme", "Africa"], "entity_types": ["organization", "location"]}, {"sentence": "The CEO of Tesla faced legal challenges over labor practices.", "entity_names": ["Tesla"], "entity_types": ["organization"]}, {"sentence": "The mayor of Chicago announced a new program to address gun violence.", "entity_names": ["Chicago"], "entity_types": ["location"]}, {"sentence": "The CEO of Apple is expected to announce a new product next week.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "London police arrest suspect in connection with the recent bank robbery.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "The famous actress will receive a lifetime achievement award at the upcoming film festival.", "entity_names": [], "entity_types": []}, {"sentence": "The United Nations issued a statement condemning the recent acts of aggression in the region.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "Russia launches new initiative to combat climate change.", "entity_names": ["Russia"], "entity_types": ["location"]}, {"sentence": "The renowned chef plans to open a new restaurant in Paris next year.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "The Prime Minister of Japan meets with world leaders to discuss economic cooperation.", "entity_names": ["Japan"], "entity_types": ["location"]}, {"sentence": "NASA announces plans for a manned mission to Mars in the next decade.", "entity_names": ["NASA", "Mars"], "entity_types": ["organization", "location"]}, {"sentence": "Facebook CEO testifies before Congress about the company's data privacy practices.", "entity_names": ["Facebook", "Congress"], "entity_types": ["organization", "organization"]}, {"sentence": "The mayor of New York City unveils a new plan to improve public transportation.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "The United Nations issues a statement condemning the recent terrorist attacks in Europe.", "entity_names": ["The United Nations", "Europe"], "entity_types": ["organization", "location"]}, {"sentence": "<PERSON> appointed as the new CEO of Johnson & Johnson.", "entity_names": ["<PERSON>", "Johnson & Johnson"], "entity_types": ["person", "organization"]}, {"sentence": "NASA announces plans for a manned mission to Mars by 2030.", "entity_names": ["NASA", "Mars"], "entity_types": ["organization", "location"]}, {"sentence": "Italy imposes new restrictions to combat the spread of COVID-19.", "entity_names": ["Italy", "COVID-19"], "entity_types": ["location", "organization"]}, {"sentence": "Amazon to open a new fulfillment center in Texas, creating thousands of jobs.", "entity_names": ["Amazon", "Texas"], "entity_types": ["organization", "location"]}, {"sentence": "President <PERSON><PERSON> signs an executive order to address climate change.", "entity_names": ["President <PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "The World Health Organization warns of a potential new strain of influenza.", "entity_names": ["The World Health Organization"], "entity_types": ["organization"]}, {"sentence": "Russia accuses the United States of espionage activities near its border.", "entity_names": ["Russia", "the United States"], "entity_types": ["location", "location"]}, {"sentence": "<PERSON> wins her 23rd Grand Slam title at the Australian Open.", "entity_names": ["<PERSON>", "Australian Open"], "entity_types": ["person", "organization"]}, {"sentence": "Apple announces record-breaking sales for its latest iPhone model.", "entity_names": ["Apple", "iPhone"], "entity_types": ["organization", "organization"]}, {"sentence": "Elon Musk plans to send more astronauts to the International Space Station.", "entity_names": ["<PERSON><PERSON>", "International Space Station"], "entity_types": ["person", "location"]}, {"sentence": "The European Union imposes new tariffs on steel imports from China.", "entity_names": ["European Union", "China"], "entity_types": ["organization", "location"]}, {"sentence": "<PERSON><PERSON> and <PERSON> welcome their second child.", "entity_names": ["<PERSON><PERSON>", "<PERSON>"], "entity_types": ["person", "person"]}, {"sentence": "Pfizer announces new vaccine efficacy data against COVID-19 variants.", "entity_names": ["Pfizer"], "entity_types": ["organization"]}, {"sentence": "India reports a surge in COVID-19 cases.", "entity_names": ["India", "COVID-19"], "entity_types": ["location", "organization"]}, {"sentence": "Facebook faces antitrust probe from the Federal Trade Commission.", "entity_names": ["Facebook", "Federal Trade Commission"], "entity_types": ["organization", "organization"]}, {"sentence": "SpaceX launches a new batch of Starlink satellites into orbit.", "entity_names": ["SpaceX", "Starlink"], "entity_types": ["organization", "organization"]}, {"sentence": "<PERSON> visits the United States for diplomatic talks.", "entity_names": ["<PERSON>", "United States"], "entity_types": ["person", "location"]}, {"sentence": "An earthquake hits Tokyo, causing widespread damage.", "entity_names": ["Tokyo"], "entity_types": ["location"]}, {"sentence": "Apple unveils the latest iPhone model at a product launch event.", "entity_names": ["Apple"], "entity_types": ["organization"]}, {"sentence": "<PERSON><PERSON><PERSON> signs a new contract with Juventus.", "entity_names": ["<PERSON><PERSON><PERSON>", "Juventus"], "entity_types": ["person", "organization"]}, {"sentence": "The World Health Organization warns of a new variant of the Ebola virus.", "entity_names": ["World Health Organization", "Ebola"], "entity_types": ["organization", "organization"]}, {"sentence": "The United Kingdom imposes stricter travel restrictions due to COVID-19.", "entity_names": ["United Kingdom"], "entity_types": ["location"]}, {"sentence": "Amazon to open new headquarters in Texas.", "entity_names": ["Amazon", "Texas"], "entity_types": ["organization", "location"]}, {"sentence": "Hurricane leaves trail of destruction in Florida.", "entity_names": ["Florida"], "entity_types": ["location"]}, {"sentence": "University of California experiences budget cuts.", "entity_names": ["University of California"], "entity_types": ["organization"]}, {"sentence": "Tesla announces record-breaking sales.", "entity_names": ["Tesla"], "entity_types": ["organization"]}, {"sentence": "New York City imposes new COVID-19 restrictions.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "WHO issues new guidelines for vaccine distribution.", "entity_names": ["WHO"], "entity_types": ["organization"]}, {"sentence": "Google acquires new tech startup.", "entity_names": ["Google"], "entity_types": ["organization"]}, {"sentence": "Olympic athlete tests positive for banned substance.", "entity_names": ["Olympic athlete"], "entity_types": ["person"]}, {"sentence": "Paris Fashion Week kicks off.", "entity_names": ["Paris"], "entity_types": ["location"]}, {"sentence": "New study reveals link between diet and heart health.", "entity_names": [], "entity_types": []}, {"sentence": "Microsoft introduces new gaming console.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "Delta variant spreads rapidly across Europe.", "entity_names": ["Delta variant", "Europe"], "entity_types": ["organization", "location"]}, {"sentence": "Apple and Google announce partnership for new app development.", "entity_names": ["Apple", "Google"], "entity_types": ["organization", "organization"]}, {"sentence": "Protests erupt in Hong Kong over new extradition law.", "entity_names": ["Hong Kong"], "entity_types": ["location"]}, {"sentence": "Facebook under fire for privacy violations.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "<PERSON> elected as new mayor of Smalltown, USA.", "entity_names": ["<PERSON>", "Smalltown", "USA"], "entity_types": ["person", "location", "location"]}, {"sentence": "Wells Fargo to close multiple branches.", "entity_names": ["Wells Fargo"], "entity_types": ["organization"]}, {"sentence": "California governor declares state of emergency after earthquake.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Miami Heat signs star rookie to multi-million dollar contract.", "entity_names": ["Miami Heat"], "entity_types": ["organization"]}, {"sentence": "WHO declares global health emergency due to new virus.", "entity_names": ["WHO"], "entity_types": ["organization"]}, {"sentence": "Hurricane hits Caribbean islands, leaving widespread destruction.", "entity_names": ["Caribbean islands"], "entity_types": ["location"]}, {"sentence": "Amazon workers go on strike for better working conditions.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "Washington Post publishes article on climate change.", "entity_names": ["Washington Post"], "entity_types": ["organization"]}, {"sentence": "College students protest rising tuition costs.", "entity_names": [], "entity_types": []}, {"sentence": "United Nations to send aid to conflict-affected region.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "Tokyo Olympics faces new challenges amid pandemic.", "entity_names": ["Tokyo Olympics"], "entity_types": ["organization"]}, {"sentence": "Miami Beach imposes new restrictions on alcohol sales.", "entity_names": ["Miami Beach"], "entity_types": ["location"]}, {"sentence": "Secretary of State visits Middle East for peace negotiations.", "entity_names": ["Secretary of State", "Middle East"], "entity_types": ["person", "location"]}, {"sentence": "Starbucks introduces new plant-based menu options.", "entity_names": ["Starbucks"], "entity_types": ["organization"]}, {"sentence": "New study shows alarming increase in childhood obesity rates.", "entity_names": [], "entity_types": []}, {"sentence": "CEO of Facebook grilled by lawmakers in Congressional hearing.", "entity_names": ["CEO of Facebook", "Congressional"], "entity_types": ["person", "organization"]}, {"sentence": "Wildfire season in Australia poses new challenges for firefighters.", "entity_names": ["Australia"], "entity_types": ["location"]}, {"sentence": "Former president announces bid for reelection.", "entity_names": ["Former president"], "entity_types": ["person"]}, {"sentence": "Election results show <PERSON> in the lead.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Earthquake hits northern California.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "President <PERSON> announces new economic policy.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Wildfires continue to ravage Australia.", "entity_names": ["Australia"], "entity_types": ["location"]}, {"sentence": "Famous actress arrested for DUI.", "entity_names": ["actress"], "entity_types": ["person"]}, {"sentence": "Amazon plans to open new distribution center.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "Mayor of New York City proposes new tax law.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "Cyclone causes devastation in Bangladesh.", "entity_names": ["Bangladesh"], "entity_types": ["location"]}, {"sentence": "CEO of Tesla predicts strong quarter earnings.", "entity_names": ["Tesla"], "entity_types": ["organization"]}, {"sentence": "Famous singer announces world tour.", "entity_names": ["singer"], "entity_types": ["person"]}, {"sentence": "Flooding in Japan displaces thousands.", "entity_names": ["Japan"], "entity_types": ["location"]}, {"sentence": "Microsoft acquires new start-up company.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "<PERSON><PERSON><PERSON> not out 49", "entity_names": ["<PERSON><PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Arizona senator calls for immigration reform.", "entity_names": ["Arizona"], "entity_types": ["location"]}, {"sentence": "NBA player <PERSON><PERSON><PERSON> signs new contract.", "entity_names": ["<PERSON><PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "New COVID-19 variant discovered in South Africa.", "entity_names": ["South Africa"], "entity_types": ["location"]}, {"sentence": "SpaceX CEO <PERSON><PERSON> to resign.", "entity_names": ["SpaceX", "<PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Prime Minister <PERSON><PERSON><PERSON> promises infrastructure funding.", "entity_names": ["<PERSON><PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "Oil prices surge amid Middle East tensions.", "entity_names": ["Middle East"], "entity_types": ["location"]}, {"sentence": "Activist <PERSON><PERSON> speaks at climate change summit.", "entity_names": ["<PERSON><PERSON>"], "entity_types": ["person"]}, {"sentence": "California governor signs new gun control bill.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Boxer <PERSON> wins championship fight.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "U.S. President <PERSON><PERSON> to visit Mexico next week.", "entity_names": ["Biden", "Mexico"], "entity_types": ["person", "location"]}, {"sentence": "CEO of Amazon <PERSON> steps down.", "entity_names": ["Amazon", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Queens Park Rangers win championship title.", "entity_names": ["Queens Park Rangers"], "entity_types": ["organization"]}, {"sentence": "Yellowstone National Park experiences record number of visitors.", "entity_names": ["Yellowstone National Park"], "entity_types": ["location"]}, {"sentence": "Singer <PERSON> to release new album.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Starbucks launches new line of sustainable products.", "entity_names": ["Starbucks"], "entity_types": ["organization"]}, {"sentence": "<PERSON> wins re-election as Chancellor of Germany.", "entity_names": ["<PERSON>", "Germany"], "entity_types": ["person", "location"]}, {"sentence": "Wildfires in Oregon force evacuations.", "entity_names": ["Oregon"], "entity_types": ["location"]}, {"sentence": "CEO of Microsoft <PERSON><PERSON><PERSON> to step down.", "entity_names": ["Microsoft", "<PERSON><PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Famous chef <PERSON> opens new restaurant.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Google CEO <PERSON><PERSON> announces new AI initiative.", "entity_names": ["Google", "<PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "New study shows link between climate change and extreme weather events.", "entity_names": [], "entity_types": []}, {"sentence": "Coinbase goes public on stock exchange.", "entity_names": ["Coinbase"], "entity_types": ["organization"]}, {"sentence": "<PERSON> wins Wimbledon title.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Notre Dame Cathedral undergoes reconstruction.", "entity_names": ["Notre Dame Cathedral"], "entity_types": ["location"]}, {"sentence": "SpaceX launches manned mission to International Space Station.", "entity_names": ["SpaceX", "International Space Station"], "entity_types": ["organization", "location"]}, {"sentence": "New York Yankees acquire top pitcher in trade.", "entity_names": ["New York Yankees"], "entity_types": ["organization"]}, {"sentence": "Facebook whistleblower exposes internal corruption.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "Hurricane causes widespread destruction in Caribbean islands.", "entity_names": ["Caribbean"], "entity_types": ["location"]}, {"sentence": "CEO of Twitter <PERSON> steps down.", "entity_names": ["Twitter", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "The United Nations issued a report today on the impact of climate change.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "NASA plans to launch a new satellite into orbit next week.", "entity_names": ["NASA"], "entity_types": ["organization"]}, {"sentence": "The CEO of Apple, <PERSON>, made a statement regarding the company's latest product release.", "entity_names": ["Apple", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "The Prime Minister of Canada visited a local school in Toronto.", "entity_names": ["Prime Minister of Canada", "Toronto"], "entity_types": ["organization", "location"]}, {"sentence": "Facebook announced a new initiative to combat misinformation on its platform.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "The mayor of New York City unveiled a new plan for affordable housing.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "The United States Department of Defense confirmed a successful military operation in the Middle East.", "entity_names": ["United States Department of Defense", "Middle East"], "entity_types": ["organization", "location"]}, {"sentence": "The singer <PERSON> announced a world tour starting next year.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "South Korea and North Korea signed a historic peace agreement last week.", "entity_names": ["South Korea", "North Korea"], "entity_types": ["location", "location"]}, {"sentence": "The CEO of Tesla, <PERSON><PERSON>, made headlines with a controversial tweet.", "entity_names": ["Tesla", "<PERSON><PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "The United Nations Security Council passed a resolution condemning the recent acts of aggression.", "entity_names": ["United Nations Security Council"], "entity_types": ["organization"]}, {"sentence": "United States imposes sanctions on Russia.", "entity_names": ["United States", "Russia"], "entity_types": ["location", "location"]}, {"sentence": "Tennis star <PERSON> announces retirement.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "European Union leaders discuss immigration policy.", "entity_names": ["European Union"], "entity_types": ["organization"]}, {"sentence": "CEO of Amazon, <PERSON>, steps down.", "entity_names": ["Amazon", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Wildfire destroys thousands of acres in California.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "Pfizer seeks approval for Covid-19 vaccine booster shots.", "entity_names": ["Pfizer"], "entity_types": ["organization"]}, {"sentence": "Former president <PERSON> to speak at climate summit.", "entity_names": ["<PERSON>"], "entity_types": ["person"]}, {"sentence": "Teen climate activist <PERSON><PERSON> nominated for Nobel Peace Prize.", "entity_names": ["<PERSON><PERSON>", "Nobel Peace Prize"], "entity_types": ["person", "organization"]}, {"sentence": "Flooding in India displaces thousands.", "entity_names": ["India"], "entity_types": ["location"]}, {"sentence": "World Health Organization declares new Covid-19 variant a \"variant of concern.\"", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "NASA rover discovers evidence of ancient life on Mars.", "entity_names": ["NASA", "Mars"], "entity_types": ["organization", "location"]}, {"sentence": "Facebook announces new measures to combat fake news.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "Australian government introduces new climate change legislation.", "entity_names": ["Australian"], "entity_types": ["organization"]}, {"sentence": "Alibaba founder <PERSON> reemerges after period of absence.", "entity_names": ["Alibaba", "<PERSON>"], "entity_types": ["organization", "person"]}, {"sentence": "Oil spill contaminates coastline in Louisiana.", "entity_names": ["Louisiana"], "entity_types": ["location"]}, {"sentence": "Juventus signs soccer prodigy from Argentina.", "entity_names": ["Juventus", "Argentina"], "entity_types": ["organization", "location"]}, {"sentence": "The United Nations has announced a new peacekeeping mission in the Middle East.", "entity_names": ["United Nations", "Middle East"], "entity_types": ["organization", "location"]}, {"sentence": "The CEO of Apple Inc. is set to unveil the latest iPhone model at the upcoming tech conference.", "entity_names": ["Apple Inc."], "entity_types": ["organization"]}, {"sentence": "The Prime Minister of Canada is facing criticism for the handling of the recent economic crisis.", "entity_names": ["Prime Minister of Canada"], "entity_types": ["person"]}, {"sentence": "Russia has announced plans to increase military presence along its western border.", "entity_names": ["Russia"], "entity_types": ["location"]}, {"sentence": "The European Union has imposed sanctions on several individuals with ties to the regime in Belarus.", "entity_names": ["European Union", "Belarus"], "entity_types": ["organization", "location"]}, {"sentence": "The mayor of New York City has declared a state of emergency due to the recent surge in COVID-19 cases.", "entity_names": ["New York City"], "entity_types": ["location"]}, {"sentence": "NASA plans to launch a new satellite into orbit next month.", "entity_names": ["NASA"], "entity_types": ["organization"]}, {"sentence": "The World Health Organization has issued a warning about the spread of a new variant of the coronavirus.", "entity_names": ["World Health Organization"], "entity_types": ["organization"]}, {"sentence": "The President of France is scheduled to meet with leaders from neighboring countries to discuss trade agreements.", "entity_names": ["President of France"], "entity_types": ["person"]}, {"sentence": "The United Nations Security Council has voted to impose sanctions on North Korea.", "entity_names": ["United Nations Security Council", "North Korea"], "entity_types": ["organization", "location"]}, {"sentence": "The CEO of Amazon has announced plans to invest in renewable energy projects.", "entity_names": ["Amazon"], "entity_types": ["organization"]}, {"sentence": "The earthquake in Japan has caused widespread destruction and loss of life.", "entity_names": ["Japan"], "entity_types": ["location"]}, {"sentence": "The Secretary-General of the United Nations is calling for an immediate ceasefire in the region.", "entity_names": ["Secretary-General of the United Nations"], "entity_types": ["organization"]}, {"sentence": "The European Central Bank has announced plans to increase interest rates in response to inflation concerns.", "entity_names": ["European Central Bank"], "entity_types": ["organization"]}, {"sentence": "The governor of Texas has declared a state of emergency following severe flooding in the region.", "entity_names": ["Texas"], "entity_types": ["location"]}, {"sentence": "The IMF has approved a new aid package for Argentina to help stabilize its economy.", "entity_names": ["IMF", "Argentina"], "entity_types": ["organization", "location"]}, {"sentence": "The President of the United States has signed a new trade agreement with Mexico and Canada.", "entity_names": ["President of the United States", "Mexico", "Canada"], "entity_types": ["person", "location", "location"]}, {"sentence": "The WHO has declared a global health emergency in response to the rapid spread of a new virus.", "entity_names": ["WHO"], "entity_types": ["organization"]}, {"sentence": "The prime minister of Italy has resigned amid political turmoil.", "entity_names": ["prime minister of Italy"], "entity_types": ["organization"]}, {"sentence": "The UNHCR is assisting refugees from war-torn countries in finding safe havens.", "entity_names": ["UNHCR"], "entity_types": ["organization"]}, {"sentence": "The mayor of London has announced plans to address the city's housing crisis.", "entity_names": ["London"], "entity_types": ["location"]}, {"sentence": "The South Korean government has unveiled a new plan to boost the economy through infrastructure investments.", "entity_names": ["South Korean"], "entity_types": ["location"]}, {"sentence": "The CEO of Tesla has announced a partnership with a Chinese technology company to build a new electric vehicle factory.", "entity_names": ["Tesla"], "entity_types": ["organization"]}, {"sentence": "The WHO has warned of a potential humanitarian crisis in a conflict-affected region.", "entity_names": ["WHO"], "entity_types": ["organization"]}, {"sentence": "The governor of California has declared a state of emergency due to the ongoing wildfires.", "entity_names": ["California"], "entity_types": ["location"]}, {"sentence": "The IMF has approved a new financial assistance program for Ukraine.", "entity_names": ["IMF", "Ukraine"], "entity_types": ["organization", "location"]}, {"sentence": "The CEO of Facebook has testified before Congress about data privacy concerns.", "entity_names": ["Facebook"], "entity_types": ["organization"]}, {"sentence": "The President of Brazil has announced plans to address deforestation in the Amazon rainforest.", "entity_names": ["President of Brazil", "Amazon"], "entity_types": ["person", "location"]}, {"sentence": "The European Union has imposed tariffs on imports from the United States in a trade dispute.", "entity_names": ["European Union", "United States"], "entity_types": ["organization", "location"]}, {"sentence": "The CEO of Microsoft has announced a major restructuring of the company's leadership team.", "entity_names": ["Microsoft"], "entity_types": ["organization"]}, {"sentence": "The Prime Minister of Japan has pledged support for developing countries in combating climate change.", "entity_names": ["Prime Minister of Japan"], "entity_types": ["person"]}, {"sentence": "The UNHCR is calling for increased humanitarian aid for refugees in the region.", "entity_names": ["UNHCR"], "entity_types": ["organization"]}, {"sentence": "The governor of Florida has issued a state of emergency in preparation for an incoming hurricane.", "entity_names": ["Florida"], "entity_types": ["location"]}, {"sentence": "The IMF has revised its global economic growth forecast for the upcoming year.", "entity_names": ["IMF"], "entity_types": ["organization"]}, {"sentence": "The CEO of Google has announced plans to expand the company's presence in the Middle East market.", "entity_names": ["Google", "Middle East"], "entity_types": ["organization", "location"]}, {"sentence": "The President of South Africa has called for unity and reconciliation in the wake of recent political unrest.", "entity_names": ["President of South Africa"], "entity_types": ["person"]}, {"sentence": "The mayor of Chicago has unveiled a new plan to combat rising crime rates in the city.", "entity_names": ["Chicago"], "entity_types": ["location"]}, {"sentence": "The European Commission has proposed new regulations to curb greenhouse gas emissions.", "entity_names": ["European Commission"], "entity_types": ["organization"]}, {"sentence": "The Prime Minister of Australia has announced measures to address the country's housing affordability crisis.", "entity_names": ["Prime Minister of Australia"], "entity_types": ["person"]}, {"sentence": "The United Nations has launched a new initiative to promote gender equality in the workplace.", "entity_names": ["United Nations"], "entity_types": ["organization"]}, {"sentence": "The CEO of Netflix has announced plans to invest in original content production in India.", "entity_names": ["Netflix", "India"], "entity_types": ["organization", "location"]}, {"sentence": "The WHO has warned of a potential public health crisis in a region with limited access to medical care.", "entity_names": ["WHO"], "entity_types": ["organization"]}, {"sentence": "The governor of Michigan has declared a state of emergency in response to a water contamination crisis.", "entity_names": ["Michigan"], "entity_types": ["location"]}, {"sentence": "The Secretary-General of the United Nations has called for urgent action on climate change.", "entity_names": ["Secretary-General of the United Nations"], "entity_types": ["person"]}, {"sentence": "The IMF has approved a new loan program for a struggling African country.", "entity_names": ["IMF", "African"], "entity_types": ["organization", "location"]}, {"sentence": "The CEO of Twitter has announced new measures to combat misinformation on the platform.", "entity_names": ["Twitter"], "entity_types": ["organization"]}, {"sentence": "The President of Argentina has pledged support for farmers affected by drought in the region.", "entity_names": ["President of Argentina"], "entity_types": ["person"]}, {"sentence": "The European Union has allocated funds for the development of renewable energy projects in member countries.", "entity_names": ["European Union"], "entity_types": ["organization"]}, {"sentence": "The Prime Minister of India has announced plans to invest in infrastructure to boost economic growth.", "entity_names": ["Prime Minister of India"], "entity_types": ["person"]}, {"sentence": "The UNHCR is providing aid to refugees fleeing conflict in the region.", "entity_names": ["UNHCR"], "entity_types": ["organization"]}]}