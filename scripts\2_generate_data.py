#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成合成数据脚本
输入：目标分布、句子多样化、实体多样化、示例的文件路径
输出：合成NER数据集

使用方法：
python scripts/2_generate_data.py --target path/to/target.json --sentence-diversity path/to/sentence_diversity.json --entity-diversity path/to/entity_diversity/ --examples path/to/examples.json
可选参数：
--disable-sentence-diversity: 禁用句子多样化
--disable-entity-diversity: 禁用实体多样化
--disable-examples: 禁用示例句子
"""

import os
import sys
import json
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def setup_environment():
    """设置环境变量和路径"""
    # 确保必要的目录存在
    os.makedirs("reproduce/synthetic_data", exist_ok=True)
    
    print("[✓] 环境设置完成")

def update_ner_config(target_path: str, sentence_diversity_path: str, entity_diversity_path: str, examples_path: str, generation_features: Dict = None):
    """更新ner_config.json中的文件路径和生成特征配置"""
    ner_config_path = "src/synth_data/ner_config.json"
    
    try:
        # 读取当前配置
        with open(ner_config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 更新文件路径
        old_target = config.get("target_file", "")
        old_sentence = config.get("sentence_diversity_file", "")
        old_entity = config.get("entity_diversity_dir", "")
        old_examples = config.get("example_sentences_file", "")
        
        config.update({
            "target_file": target_path,
            "sentence_diversity_file": sentence_diversity_path,
            "entity_diversity_dir": entity_diversity_path,
            "example_sentences_file": examples_path
        })
        
        # 更新生成特征配置
        if generation_features is None:
            generation_features = {
                "use_sentence_diversity": True,
                "use_entity_diversity": True,
                "use_example_sentences": True
            }
        config["generation_features"] = generation_features
        
        # 保存更新后的配置
        with open(ner_config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"[✓] 已更新ner_config.json")
        print(f"    target_file: {old_target} -> {target_path}")
        print(f"    sentence_diversity_file: {old_sentence} -> {sentence_diversity_path}")
        print(f"    entity_diversity_dir: {old_entity} -> {entity_diversity_path}")
        print(f"    example_sentences_file: {old_examples} -> {examples_path}")
        print(f"    generation_features: {json.dumps(generation_features, ensure_ascii=False)}")
        
    except Exception as e:
        print(f"[错误] 更新ner_config.json失败：{e}")
        raise

def validate_input_files(target_path: str, sentence_diversity_path: str, entity_diversity_path: str, examples_path: str):
    """验证输入文件是否存在"""
    files_to_check = [
        ("目标分布文件", target_path),
        ("句子多样化文件", sentence_diversity_path),
        ("实体多样化目录", entity_diversity_path),
        ("示例数据文件", examples_path)
    ]
    
    missing_files = []
    for name, path in files_to_check:
        if not os.path.exists(path):
            missing_files.append(f"{name}: {path}")
    
    if missing_files:
        print("[错误] 以下文件不存在：")
        for missing in missing_files:
            print(f"  - {missing}")
        return False
    
    print("[✓] 所有输入文件验证通过")
    return True

def run_ner_data_generation():
    """运行NER数据生成"""
    print("\n=== 生成合成NER数据 ===")
    
    try:
        # 导入并运行NER数据生成模块
        sys.path.insert(0, str(project_root / "src" / "synth_data"))
        
        # 导入数据生成模块
        from ner_data_generation import main as ner_main
        
        # 运行数据生成
        ner_main()
        
        print("[✓] 合成NER数据生成完成")
        
    except ImportError as e:
        print(f"[警告] 无法导入NER数据生成模块，尝试直接运行脚本：{e}")
        # 直接运行脚本
        script_path = "src/synth_data/ner_data_generation.py"
        result = os.system(f"python {script_path}")
        
        if result == 0:
            print("[✓] 合成NER数据生成完成")
        else:
            raise Exception(f"脚本执行失败，返回码：{result}")
        
    except Exception as e:
        print(f"[错误] 合成NER数据生成失败：{e}")
        raise

def get_latest_synthetic_data():
    """获取最新生成的合成数据文件"""
    synthetic_dir = "reproduce/synthetic_data"
    if os.path.exists(synthetic_dir):
        data_files = [f for f in os.listdir(synthetic_dir) if f.endswith('.json')]
        if data_files:
            latest_data = sorted(data_files)[-1]
            return os.path.join(synthetic_dir, latest_data)
    return None

def create_summary_report(target_path: str, sentence_diversity_path: str, entity_diversity_path: str, examples_path: str, synthetic_data_path: str, generation_features: Dict):
    """创建执行总结报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_path = f"data_generation_report_{timestamp}.txt"
    
    report_content = f"""
合成数据生成报告
==================

生成时间：{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

输入文件：
- 目标分布文件：{target_path}
- 句子多样化文件：{sentence_diversity_path}
- 实体多样化目录：{entity_diversity_path}
- 示例数据文件：{examples_path}

生成特征配置：
- 使用句子多样化：{"是" if generation_features["use_sentence_diversity"] else "否"}
- 使用实体多样化：{"是" if generation_features["use_entity_diversity"] else "否"}
- 使用示例句子：{"是" if generation_features["use_example_sentences"] else "否"}

输出文件：
- 合成NER数据：{synthetic_data_path if synthetic_data_path else "未找到"}

下一步：
运行 scripts/3_evaluate_quality.py 进行数据质量检测
"""
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"\n[✓] 执行报告已保存：{report_path}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="生成合成数据脚本")
    parser.add_argument("--target", required=True, help="目标分布文件路径")
    parser.add_argument("--sentence-diversity", required=True, help="句子多样化文件路径")
    parser.add_argument("--entity-diversity", required=True, help="实体多样化目录路径")
    parser.add_argument("--examples", required=True, help="示例数据文件路径")
    # 添加生成特征控制参数
    parser.add_argument("--disable-sentence-diversity", action="store_true", help="禁用句子多样化")
    parser.add_argument("--disable-entity-diversity", action="store_true", help="禁用实体多样化")
    parser.add_argument("--disable-examples", action="store_true", help="禁用示例句子")
    
    args = parser.parse_args()
    
    # 构建生成特征配置
    generation_features = {
        "use_sentence_diversity": not args.disable_sentence_diversity,
        "use_entity_diversity": not args.disable_entity_diversity,
        "use_example_sentences": not args.disable_examples
    }
    
    print("=== 开始生成合成数据 ===")
    print(f"目标分布文件：{args.target}")
    print(f"句子多样化文件：{args.sentence_diversity}")
    print(f"实体多样化目录：{args.entity_diversity}")
    print(f"示例数据文件：{args.examples}")
    print(f"生成特征配置：{json.dumps(generation_features, ensure_ascii=False)}")
    
    try:
        # 1. 验证输入文件
        if not validate_input_files(args.target, args.sentence_diversity, args.entity_diversity, args.examples):
            sys.exit(1)
        
        # 2. 设置环境
        setup_environment()
        
        # 3. 更新配置
        update_ner_config(args.target, args.sentence_diversity, args.entity_diversity, args.examples, generation_features)
        
        # 4. 生成合成数据
        run_ner_data_generation()
        
        # 5. 获取生成的文件
        synthetic_data_path = get_latest_synthetic_data()
        
        # 6. 创建总结报告
        create_summary_report(args.target, args.sentence_diversity, args.entity_diversity, args.examples, synthetic_data_path, generation_features)
        
        print(f"\n=== 合成数据生成完成 ===")
        if synthetic_data_path:
            print(f"生成的合成数据：{synthetic_data_path}")
        else:
            print("警告：未找到生成的合成数据文件")
        print("\n下一步：运行 scripts/3_evaluate_quality.py 进行数据质量检测")
        
    except Exception as e:
        print(f"\n[错误] 合成数据生成失败：{e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 