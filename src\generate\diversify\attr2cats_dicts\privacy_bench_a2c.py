"""
Privacy Bench 数据集的属性配置
"""

from typing import Dict, Any
from src.generate.diversify.util import ENTITY_KEY, ENTITY_KEY_SEEDED, ENTITY_TYPE_DEFAULT

__all__ = ['privacy_bench_a2c']

privacy_bench_a2c = {
    ENTITY_KEY: dict(
        short='ent',
        seed_category=None,
        categories='default',
        presets={
            'default': 'privacy-bench-entities'
        }
    ),
    ENTITY_KEY_SEEDED: dict(
        short='ent-s',
        seed_category='sensitivity-level',
        categories='default',
        presets={
            'default': 'privacy-bench-entities-seeded'
        }
    ),
    'entity-type': dict(
        short='et',
        name='实体类型',
        attribute_names=['实体类型'],
        desc='不同类型的敏感实体',
        examples=['个人信息类', '财务信息类', '医疗信息类', '位置信息类'],
        categories='default',
        presets={
            'default': 'privacy-bench-entity-types'
        }
    ),
    'context-type': dict(
        short='ctx',
        name='上下文类型',
        attribute_names=['上下文类型'],
        desc='不同类型的文本数据上下文',
        examples=['医疗记录', '金融交易', '社交媒体', '政府档案', '企业内部数据'],
        categories='default',
        presets={
            'default': 'privacy-bench-context-types'
        }
    ),
    'sensitivity-level': dict(
        short='sens',
        name='敏感级别',
        attribute_names=['敏感级别'],
        desc='不同的数据敏感级别',
        examples=['直接敏感信息', '间接敏感信息'],
        categories='default',
        presets={
            'default': 'privacy-bench-sensitivity-levels'
        }
    ),
    'tone': dict(
        short='tone',
        name='语气',
        attribute_names=['语气'],
        desc='不同的语言语气',
        examples=['正式', '非正式', '专业', '日常'],
        categories='default',
        presets={
            'default': 'privacy-bench-tones'
        }
    ),
    # 'demographic': dict(
    #     short='demo',
    #     name='人口特征',
    #     attribute_names=['人口特征'],
    #     desc='不同的人口特征',
    #     examples=['年龄段', '职业', '教育水平', '地域'],
    #     categories='default',
    #     presets={
    #         'default': 'privacy-bench-demographics'
    #     }
    # ),
    'intent': dict(
        short='int',
        name='使用意图',
        attribute_names=['使用意图'],
        desc='不同的文本数据使用意图',
        examples=['研究分析', '商业决策', '个性化服务', '风险评估', '合规审计'],
        categories='default',
        presets={
            'default': 'privacy-bench-intents'
        }
    )
}

for attr, d in privacy_bench_a2c.items():
    if attr not in [ENTITY_KEY, ENTITY_KEY_SEEDED]:
        d.update(kind='categorical', seed_category=None, from_file=True)
