#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修改entity_diversity.json格式的脚本
将vanilla数组中的每个值变成单独的一行
"""

import json
import os

def reformat_entity_diversity_json():
    """重新格式化entity_diversity.json文件，将vanilla数组中的每个值变成单独的一行"""
    
    # 文件路径
    json_file = "reproduce/entity_diversity/entity_diversity_20250705_165355/entity_diversity.json"
    
    # 检查文件是否存在
    if not os.path.exists(json_file):
        print(f"错误：文件不存在 - {json_file}")
        return
    
    try:
        # 读取JSON文件
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"正在重新格式化文件：{json_file}")
        
        # 重新写入文件，使用indent=2确保每个值都在单独的行
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print("文件格式化完成！")
        print("vanilla数组中的每个值现在都在单独的一行")
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误：{e}")
    except Exception as e:
        print(f"处理文件时出错：{e}")

if __name__ == "__main__":
    reformat_entity_diversity_json() 