{"prompts": ["Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Song.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type song\n- (B). The span contains a named song entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not song\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Review, Character, other].\n\nA named song entity must be the name of a song.\nSong attributes such as \"iconic song,\" \"love song,\" \"famous song,\" and \"original song\" are not relevant named entities. Names of Artists such as \"<PERSON><PERSON><PERSON>\" are also not relevant named entities. Musical descriptors such as \"song\", \"theme song\" and \"soundtrack\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Is {{Every Breath You Take}} featured in any popular movie soundtracks?\"\nText Span: \"Every Breath You Take\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: 'What is the {{theme song}} of the James Bond movie \"Skyfall\"?'\nText Span: \"theme song\"\nLabel: (D). Not a Named Entity.\n\n3. Query: 'Can you play a {{song}} from the movie \"The Sound of Music\"?'\nText Span: \"song\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"Can you suggest a movie with a {{famous soundtrack}}?\"\nText Span: \"famous soundtrack\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a movie featuring Kate Winslet with the song '{{Smells Like Teen Spirit}}' in its soundtrack?\"\nText Span: \"Smells Like Teen Spirit\"\n\n2. Query: \"Can you recommend a classic movie with the song {{Livin' on a Prayer}}?\"\nText Span: \"Livin' on a Prayer\"\n\n3. Query: \"Are there any coming-of-age movies with the song '{{Lucy in the Sky with Diamonds}}'?\"\nText Span: \"Lucy in the Sky with Diamonds\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Song.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type song\n- (B). The span contains a named song entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not song\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Review, Character, other].\n\nA named song entity must be the name of a song.\nSong attributes such as \"iconic song,\" \"love song,\" \"famous song,\" and \"original song\" are not relevant named entities. Names of Artists such as \"<PERSON><PERSON><PERSON>\" are also not relevant named entities. Musical descriptors such as \"song\", \"theme song\" and \"soundtrack\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Is {{Every Breath You Take}} featured in any popular movie soundtracks?\"\nText Span: \"Every Breath You Take\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: 'Can you play a {{song}} from the movie \"The Sound of Music\"?'\nText Span: \"song\"\nLabel: (D). Not a Named Entity.\n\n3. Query: 'What is the {{theme song}} of the James Bond movie \"Skyfall\"?'\nText Span: \"theme song\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"Can you suggest a movie with a {{famous soundtrack}}?\"\nText Span: \"famous soundtrack\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a movie with the song '{{Happy Birthday}}' in it?\"\nText Span: \"Happy Birthday\"\n\n2. Query: \"Show me a Colin Firth film from the 2010s with a great {{soundtrack}}\"\nText Span: \"soundtrack\"\n\n3. Query: \"I'm looking for a comedy movie from 2030 featuring the song '{{Don't Stop Believin'}}.\"\nText Span: \"Don't Stop Believin'\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Song.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type song\n- (B). The span contains a named song entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not song\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Review, Character, other].\n\nA named song entity must be the name of a song.\nSong attributes such as \"iconic song,\" \"love song,\" \"famous song,\" and \"original song\" are not relevant named entities. Names of Artists such as \"<PERSON><PERSON><PERSON>\" are also not relevant named entities. Musical descriptors such as \"song\", \"theme song\" and \"soundtrack\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Is {{Every Breath You Take}} featured in any popular movie soundtracks?\"\nText Span: \"Every Breath You Take\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you suggest a movie with a {{famous soundtrack}}?\"\nText Span: \"famous soundtrack\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n3. Query: 'What is the {{theme song}} of the James Bond movie \"Skyfall\"?'\nText Span: \"theme song\"\nLabel: (D). Not a Named Entity.\n\n4. Query: 'Can you play a {{song}} from the movie \"The Sound of Music\"?'\nText Span: \"song\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a movie with spectacular visual effects and a powerful soundtrack, such as the song {{Reflection}}?\"\nText Span: \"Reflection\"\n\n2. Query: \"Show me the trailer for Apocalypse, the movie with the song from {{Dirty Dancing}}.\"\nText Span: \"Dirty Dancing\"\n\n3. Query: \"Show me a trailer for the movie that has a song performed by {{Elton John}}.\"\nText Span: \"Elton John\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Song.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type song\n- (B). The span contains a named song entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not song\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Review, Character, other].\n\nA named song entity must be the name of a song.\nSong attributes such as \"iconic song,\" \"love song,\" \"famous song,\" and \"original song\" are not relevant named entities. Names of Artists such as \"<PERSON><PERSON><PERSON>\" are also not relevant named entities. Musical descriptors such as \"song\", \"theme song\" and \"soundtrack\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Can you play a {{song}} from the movie \"The Sound of Music\"?'\nText Span: \"song\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Is {{Every Breath You Take}} featured in any popular movie soundtracks?\"\nText Span: \"Every Breath You Take\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: 'What is the {{theme song}} of the James Bond movie \"Skyfall\"?'\nText Span: \"theme song\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"Can you suggest a movie with a {{famous soundtrack}}?\"\nText Span: \"famous soundtrack\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a movie similar to Yellow Submarine in terms of its {{song}} and characters?\"\nText Span: \"song\"\n\n2. Query: \"I'm looking for a comedy film from 1990 with John Candy and a trailer featuring the song '{{Ghostbusters}}.'\"\nText Span: \"Ghostbusters\"\n\n3. Query: \"Is there a favorite 80s movie with a {{memorable soundtrack}} and a 5.5-star rating?\"\nText Span: \"memorable soundtrack\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Song.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type song\n- (B). The span contains a named song entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not song\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Review, Character, other].\n\nA named song entity must be the name of a song.\nSong attributes such as \"iconic song,\" \"love song,\" \"famous song,\" and \"original song\" are not relevant named entities. Names of Artists such as \"<PERSON><PERSON><PERSON>\" are also not relevant named entities. Musical descriptors such as \"song\", \"theme song\" and \"soundtrack\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Can you play a {{song}} from the movie \"The Sound of Music\"?'\nText Span: \"song\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Is {{Every Breath You Take}} featured in any popular movie soundtracks?\"\nText Span: \"Every Breath You Take\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Can you suggest a movie with a {{famous soundtrack}}?\"\nText Span: \"famous soundtrack\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n4. Query: 'What is the {{theme song}} of the James Bond movie \"Skyfall\"?'\nText Span: \"theme song\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can I watch a movie with an unimpressive rating but an {{outstanding soundtrack}}?\"\nText Span: \"outstanding soundtrack\"\n\n2. Query: \"I'm looking for a film approved for all audiences that features the song '{{Circle of Life}}'\"\nText Span: \"Circle of Life\"\n\n3. Query: \"Show me a trailer for a 1998 movie featuring the song '{{My Heart Will Go On}}.'\"\nText Span: \"My Heart Will Go On\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Song.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type song\n- (B). The span contains a named song entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not song\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Review, Character, other].\n\nA named song entity must be the name of a song.\nSong attributes such as \"iconic song,\" \"love song,\" \"famous song,\" and \"original song\" are not relevant named entities. Names of Artists such as \"<PERSON><PERSON><PERSON>\" are also not relevant named entities. Musical descriptors such as \"song\", \"theme song\" and \"soundtrack\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you suggest a movie with a {{famous soundtrack}}?\"\nText Span: \"famous soundtrack\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n2. Query: \"Is {{Every Breath You Take}} featured in any popular movie soundtracks?\"\nText Span: \"Every Breath You Take\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: 'What is the {{theme song}} of the James Bond movie \"Skyfall\"?'\nText Span: \"theme song\"\nLabel: (D). Not a Named Entity.\n\n4. Query: 'Can you play a {{song}} from the movie \"The Sound of Music\"?'\nText Span: \"song\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a Wes Anderson film that features a song by {{Tony Montana}}?\"\nText Span: \"Tony Montana\"\n\n2. Query: \"Show me an exploitation film with a {{song}} that is considered the best of all time.\"\nText Span: \"song\"\n\n3. Query: '\"What is the highest viewers' rating for a movie featuring the song '{{Livin' on a Prayer}}'?'\nText Span: \"Livin' on a Prayer\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Song.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type song\n- (B). The span contains a named song entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not song\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Review, Character, other].\n\nA named song entity must be the name of a song.\nSong attributes such as \"iconic song,\" \"love song,\" \"famous song,\" and \"original song\" are not relevant named entities. Names of Artists such as \"<PERSON><PERSON><PERSON>\" are also not relevant named entities. Musical descriptors such as \"song\", \"theme song\" and \"soundtrack\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'Can you play a {{song}} from the movie \"The Sound of Music\"?'\nText Span: \"song\"\nLabel: (D). Not a Named Entity.\n\n2. Query: 'What is the {{theme song}} of the James Bond movie \"Skyfall\"?'\nText Span: \"theme song\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"Can you suggest a movie with a {{famous soundtrack}}?\"\nText Span: \"famous soundtrack\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n4. Query: \"Is {{Every Breath You Take}} featured in any popular movie soundtracks?\"\nText Span: \"Every Breath You Take\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a movie with a {{great soundtrack}} from the 80s?\"\nText Span: \"great soundtrack\"\n\n2. Query: \"Show me a movie with a {{memorable song}} like Billie Jean\"\nText Span: \"memorable song\"\n\n3. Query: \"Show me a movie with a memorable song like {{Billie Jean}}\"\nText Span: \"Billie Jean\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Song.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type song\n- (B). The span contains a named song entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not song\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Review, Character, other].\n\nA named song entity must be the name of a song.\nSong attributes such as \"iconic song,\" \"love song,\" \"famous song,\" and \"original song\" are not relevant named entities. Names of Artists such as \"<PERSON><PERSON><PERSON>\" are also not relevant named entities. Musical descriptors such as \"song\", \"theme song\" and \"soundtrack\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: 'What is the {{theme song}} of the James Bond movie \"Skyfall\"?'\nText Span: \"theme song\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Can you suggest a movie with a {{famous soundtrack}}?\"\nText Span: \"famous soundtrack\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n3. Query: \"Is {{Every Breath You Take}} featured in any popular movie soundtracks?\"\nText Span: \"Every Breath You Take\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: 'Can you play a {{song}} from the movie \"The Sound of Music\"?'\nText Span: \"song\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Who directed the movie from 1987 with Michelle Pfeiffer and the song {{Don't Stop Believin'}}?\"\nText Span: \"Don't Stop Believin'\"\n\n2. Query: \"can you recommend a movie from 1989 that is directed by tim burton and features {{a song by prince}}\"\nText Span: \"a song by prince\"\n\n3. Query: \"I want to watch a movie with a song by {{Adele}}. Show me the options.\"\nText Span: \"Adele\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Song.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type song\n- (B). The span contains a named song entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not song\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Review, Character, other].\n\nA named song entity must be the name of a song.\nSong attributes such as \"iconic song,\" \"love song,\" \"famous song,\" and \"original song\" are not relevant named entities. Names of Artists such as \"<PERSON><PERSON><PERSON>\" are also not relevant named entities. Musical descriptors such as \"song\", \"theme song\" and \"soundtrack\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you suggest a movie with a {{famous soundtrack}}?\"\nText Span: \"famous soundtrack\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n2. Query: 'What is the {{theme song}} of the James Bond movie \"Skyfall\"?'\nText Span: \"theme song\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"Is {{Every Breath You Take}} featured in any popular movie soundtracks?\"\nText Span: \"Every Breath You Take\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: 'Can you play a {{song}} from the movie \"The Sound of Music\"?'\nText Span: \"song\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Who directed the science fiction film with the special song '{{I Will Always Love You}}'?\"\nText Span: \"I Will Always Love You\"\n\n2. Query: \"What year did the movie 'Pride and Prejudice' with a romantic plot and a {{timeless}} song come out?\"\nText Span: \"timeless\"\n\n3. Query: \"What reflective movie directed by Christopher Nolan has the song {{Time}} by Hans Zimmer?\"\nText Span: \"Time\"", "Here is a spoken query to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Song.\nIn particular, for the given query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type song\n- (B). The span contains a named song entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not song\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Review, Character, other].\n\nA named song entity must be the name of a song.\nSong attributes such as \"iconic song,\" \"love song,\" \"famous song,\" and \"original song\" are not relevant named entities. Names of Artists such as \"<PERSON><PERSON><PERSON>\" are also not relevant named entities. Musical descriptors such as \"song\", \"theme song\" and \"soundtrack\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Is {{Every Breath You Take}} featured in any popular movie soundtracks?\"\nText Span: \"Every Breath You Take\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you suggest a movie with a {{famous soundtrack}}?\"\nText Span: \"famous soundtrack\"\nLabel: (C). Wrong Type. The correct entity type is other.\n\n3. Query: 'What is the {{theme song}} of the James Bond movie \"Skyfall\"?'\nText Span: \"theme song\"\nLabel: (D). Not a Named Entity.\n\n4. Query: 'Can you play a {{song}} from the movie \"The Sound of Music\"?'\nText Span: \"song\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following span of text:\n\nQuery: \"Did the movie Born to Run, featuring {{the song of the same name}}, receive positive viewers' ratings?\"\nText Span: \"the song of the same name\""]}