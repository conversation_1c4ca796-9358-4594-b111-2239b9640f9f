{"attribute2categories": {"meal-category": ["Chinese", "Indian", "Mexican", "Thai", "Japanese", "Italian", "Mediterranean", "Vegan/Vegetarian", "Seafood", "BBQ", "French", "Greek", "Korean", "<PERSON><PERSON>", "Caribbean", "Brunch", "Vegan", "Korean BBQ", "<PERSON><PERSON>", "<PERSON><PERSON>", "Tapas", "Farm-to-table", "Street food", "Steakhouse", "Middle Eastern", "Comfort food", "Fusion", "Food truck", "Barbecue", "Brazilian", "Ethiopian"], "demographic": ["25-year-old female student", "35-year-old male construction worker", "40-year-old female accountant", "55-year-old male retiree", "30-year-old female stay-at-home parent", "45-year-old male business executive", "20-year-old female retail worker", "50-year-old male doctor", "28-year-old female software developer", "65-year-old male teacher", "Young adult, female, student", "Middle-aged, male, accountant", "Senior citizen, female, retired", "30s, male, doctor", "Teenager, female, high school student", "40s, female, lawyer", "20s, male, programmer", "50s, male, business owner", "25, female, nurse", "35, male, teacher"], "ambiance": ["Casual", "Outdoor seating", "Family-friendly", "Romantic", "Formal", "<PERSON><PERSON><PERSON>", "Rustic", "Intimate", "Fine dining", "Lounge/Bar", "Fast-casual", "Waterfront", "Urban/Chic", "Cozy and intimate", "Upscale and elegant", "Hip and trendy", "Rustic and charming", "Lively and energetic", "Quiet and peaceful", "Beachfront or waterfront", "Quirky and eccentric"], "price": ["Fine dining", "Upper mid-range", "Mid-range", "Budget-friendly", "Affordable", "Luxury dining", "Expensive", "Cheap", "Moderately priced", "Higher-end"], "dietary": ["Vegetarian", "Vegan", "Gluten-free", "Dairy-free", "Nut-free", "<PERSON><PERSON>", "Low-carb", "Pescatarian", "<PERSON><PERSON>", "<PERSON><PERSON>"], "special": ["Gluten-free options", "Vegan or vegetarian-friendly menu", "Private dining rooms or event spaces", "Bottomless brunch or mimosa specials", "Food and wine pairing events", "Chef's table experiences", "Rooftop or terrace dining", "Cooking classes or demonstrations", "Food delivery or takeout options", "Outdoor grilling or BBQ nights", "Romantic dining packages for couples", "Themed dining nights or special holiday menus", "Vegetarian and vegan options", "Gluten-free and allergen-friendly menu options", "Private dining or event space", "Farm-to-table or locally sourced ingredients", "Celebrity chef or unique culinary experience", "Wine or cocktail pairing recommendations", "Food and drink delivery services", "Cooking classes or workshops", "Themed or pop-up dining experiences", "Unique or specialty cuisines (e.g. fusion, experimental, international)", "Chef's table or tasting menu options", "Food festivals or special culinary events hosted at the restaurant", "Dietary restrictions accommodations (e.g. gluten-free, vegan, etc.)", "Romantic setting for date nights", "Private dining options for events or special occasions", "Michelin-starred or celebrity chef-owned establishments", "Farm-to-table or locally-sourced ingredients", "Unique tasting menu or chef's table experience", "Wine or cocktail pairing options", "Interactive or experiential dining experiences (e.g. themed dinners, immersive experiences)", "Open kitchen concept for watching chefs at work", "Fusion or international cuisine options", "Eco-friendly or sustainable practices", "Culinary classes or workshops offered onsite."]}, "meta": {"dataset-name": "mit-restaurant", "attributes": ["meal-category", "demographic", "ambiance", "price", "dietary", "special"], "entity-sizes": {"meal-category": 31, "demographic": 20, "ambiance": 21, "price": 10, "dietary": 10, "special": 36}}}