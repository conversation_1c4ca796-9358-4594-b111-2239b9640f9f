{"meta": {"dataset_name": "privacy_bench", "source_dataset_dir_name": "25-04-24_20-48-57_<PERSON><PERSON>-Res_{#l=1,dc=T,lc=T}", "diversity_variant": "Diverse-Y-seeded"}, "entity_type2correction_info": {"姓名": {"defn": "姓名实体是指人的名字，包括中文姓名、英文姓名、网名或昵称等。姓名实体应该是具体的人名，而不是泛指的'某人'、'此人'等。", "name": "姓名", "name_full": "姓名实体", "demos": [{"sentence": "刘强，男，汉族，1985年生，本科学历，现居住在南京市鼓楼区，患有轻度高血压。", "entity_span": "刘强", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "布鲁京斯研究所桑顿中国中心研究部主任李成说，东亚的和平与安全，是美国的“核心利益”之一。", "entity_span": "李成", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "家住宝城的梁京去年办了张信用卡，有三次未及时还款，前不久他计划买住房时，去售楼部打听得知。", "entity_span": "某人", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "地理位置": {"defn": "地理位置实体是指具体的地点，包括国家、省份、城市、区县、街道、小区等。", "name": "地理位置", "name_full": "地理位置实体", "demos": [{"sentence": "刘强，男，汉族，1985年生，本科学历，现居住在南京市鼓楼区，患有轻度高血压。", "entity_span": "南京市鼓楼区", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "深圳市人大常委会批准的58个项目中,有2个保障房项目,分别是松坪村三期和深康村。", "entity_span": "松坪村", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "我们将前往目的地。", "entity_span": "目的地", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "职业": {"defn": "职业实体是指人的工作或职务，如医生、教师、工程师等。", "name": "职业", "name_full": "职业实体", "demos": [{"sentence": "万通地产设计总监刘克峰；", "entity_span": "设计总监", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "方传柳实习生王梦菲", "entity_span": "实习生", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "他从事这个行业已经十年了。", "entity_span": "行业", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "教育背景": {"defn": "教育背景实体是指人的学历、学位、专业、毕业院校等教育相关信息。", "name": "教育背景", "name_full": "教育背景实体", "demos": [{"sentence": "刘强，男，汉族，1985年生，本科学历，现居住在南京市鼓楼区，患有轻度高血压。", "entity_span": "本科学历", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "张三毕业于北京大学计算机科学与技术专业，获得硕士学位。", "entity_span": "北京大学计算机科学与技术专业", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "他正在攻读学位。", "entity_span": "学位", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "婚姻状况": {"defn": "婚姻状况实体是指人的婚姻状态，如已婚、未婚、离异、丧偶等。", "name": "婚姻状况", "name_full": "婚姻状况实体", "demos": [{"sentence": "李明，男，已婚，现居住在北京市海淀区。", "entity_span": "已婚", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "请填写您的婚姻状况。", "entity_span": "婚姻状况", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "政治倾向": {"defn": "政治倾向实体是指人的政治立场或观点，如自由主义、保守主义、社会主义等。", "name": "政治倾向", "name_full": "政治倾向实体", "demos": [{"sentence": "张三是一个自由主义者，支持市场经济。", "entity_span": "自由主义者", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "请说明您的政治立场。", "entity_span": "政治立场", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "家庭成员": {"defn": "家庭成员实体是指人的家庭关系，如父母、子女、配偶、兄弟姐妹等。", "name": "家庭成员", "name_full": "家庭成员实体", "demos": [{"sentence": "李明的父亲是一名医生，母亲是一名教师。", "entity_span": "父亲", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "请填写您的家庭成员信息。", "entity_span": "家庭成员", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "工资数额": {"defn": "工资数额实体是指人的工资收入，如月薪、年薪、时薪等。", "name": "工资数额", "name_full": "工资数额实体", "demos": [{"sentence": "张三的月薪是10000元，年薪是12万元。", "entity_span": "10000元", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "请填写您的工资信息。", "entity_span": "工资信息", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "投资产品": {"defn": "投资产品实体是指人所持有的投资产品，如股票、基金、债券等。", "name": "投资产品", "name_full": "投资产品实体", "demos": [{"sentence": "李明持有阿里巴巴股票和国债。", "entity_span": "阿里巴巴股票", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "请填写您的投资信息。", "entity_span": "投资信息", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "税务记录": {"defn": "税务记录实体是指人的税收相关信息，如税号、税率、税收金额等。", "name": "税务记录", "name_full": "税务记录实体", "demos": [{"sentence": "张三的个人所得税税率是20%，去年税后收入是10万元。", "entity_span": "20%", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "请填写您的税务信息。", "entity_span": "税务信息", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "信用记录": {"defn": "信用记录实体是指人的信用相关信息，如信用分数、信用卡还款记录、贷款记录等。", "name": "信用记录", "name_full": "信用记录实体", "demos": [{"sentence": "李明的信用分数是750分，属于优秀级别。", "entity_span": "750分", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "请查询您的信用记录。", "entity_span": "信用记录", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "实体资产": {"defn": "实体资产实体是指人所拥有的实物资产，如房产、车辆、珠宝等。", "name": "实体资产", "name_full": "实体资产实体", "demos": [{"sentence": "张三在北京海淀区有一套三居室的房产，价值500万元。", "entity_span": "三居室的房产", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "请填写您的资产信息。", "entity_span": "资产信息", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "交易信息": {"defn": "交易信息实体是指人的交易相关信息，如交易金额、交易时间、交易对象等。", "name": "交易信息", "name_full": "交易信息实体", "demos": [{"sentence": "李明昨天在淘宝上消费了5000元。", "entity_span": "5000元", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "请查询您的交易记录。", "entity_span": "交易记录", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "药物": {"defn": "药物实体是指医疗用药的名称，如阿司匹林、布洛芬、胶囊等。", "name": "药物", "name_full": "药物实体", "demos": [{"sentence": "张三每天需要服用阿司匹林和降压药。", "entity_span": "阿司匹林", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "请记录您的用药情况。", "entity_span": "用药情况", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "临床表现": {"defn": "临床表现实体是指疾病的症状或表现，如发热、头痛、咽痛等。", "name": "临床表现", "name_full": "临床表现实体", "demos": [{"sentence": "李明昨天开始发热和咽痛，可能是感冒了。", "entity_span": "发热", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "请描述您的症状。", "entity_span": "症状", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "生育信息": {"defn": "生育信息实体是指人的生育相关信息，如生育能力、生育计划、生育历史等。", "name": "生育信息", "name_full": "生育信息实体", "demos": [{"sentence": "张三和妻子计划生两个孩子。", "entity_span": "生两个孩子", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "请填写您的生育情况。", "entity_span": "生育情况", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "行程信息": {"defn": "行程信息实体是指人的出行相关信息，如出行时间、出行目的地、交通工具等。", "name": "行程信息", "name_full": "行程信息实体", "demos": [{"sentence": "李明计划下周乘坐高铁前往上海旅游。", "entity_span": "下周", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "请填写您的行程安排。", "entity_span": "行程安排", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "性别": {"defn": "性别实体是指人的性别，如男、女等。", "name": "性别", "name_full": "性别实体", "demos": [{"sentence": "刘强，男，汉族，1985年生，本科学历，现居住在南京市鼓楼区，患有轻度高血压。", "entity_span": "男", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "这位女士是我们公司的首席执行官。", "entity_span": "女士", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}]}, "民族": {"defn": "民族实体是指人的民族身份，如汉族、蒙古族、藏族等。", "name": "民族", "name_full": "民族实体", "demos": [{"sentence": "刘强，男，汉族，1985年生，本科学历，现居住在南京市鼓楼区，患有轻度高血压。", "entity_span": "汉族", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "这是一个多民族的国家。", "entity_span": "多民族", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "国籍": {"defn": "国籍实体是指人的国家身份，如中国、美国、日本等。", "name": "国籍", "name_full": "国籍实体", "demos": [{"sentence": "张三是中国籍，现居住在北京市海淀区。", "entity_span": "中国", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "请填写您的国籍信息。", "entity_span": "国籍", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "种族": {"defn": "种族实体是指人的种族身份，如黑人、白人、亚裔人等。", "name": "种族", "name_full": "种族实体", "demos": [{"sentence": "他是一个亚裔人，来自日本。", "entity_span": "亚裔人", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "这个国家有多种种族。", "entity_span": "多种种族", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "年龄": {"defn": "年龄实体是指人的年龄或出生年份。", "name": "年龄", "name_full": "年龄实体", "demos": [{"sentence": "刘强，男，汉族，1985年生，本科学历，现居住在南京市鼓楼区，患有轻度高血压。", "entity_span": "1985年", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "这位老人今年已经85岁了。", "entity_span": "85岁", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}]}, "疾病": {"defn": "疾病实体是指具体的疾病名称，如高血压、糖尿病等。", "name": "疾病", "name_full": "疾病实体", "demos": [{"sentence": "刘强，男，汉族，1985年生，本科学历，现居住在南京市鼓楼区，患有轻度高血压。", "entity_span": "高血压", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "张伟今年35岁，因为对青霉素过敏，在北京协和医院接受了心脏搭桥手术。", "entity_span": "青霉素过敏", "entity_type": null, "label": "WRONG_TYPE", "correct_span": null, "correct_type": "过敏信息", "reason": null}]}, "过敏信息": {"defn": "过敏信息实体是指人对某种物质的过敏反应，如对青霉素过敏、对花粉过敏等。", "name": "过敏信息", "name_full": "过敏信息实体", "demos": [{"sentence": "张伟今年35岁，因为对青霉素过敏，在北京协和医院接受了心脏搭桥手术。", "entity_span": "青霉素", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "这种药物可能会引起过敏反应。", "entity_span": "过敏反应", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}, "医疗程序": {"defn": "医疗程序实体是指具体的医疗操作或手术，如心脏搭桥手术、胃镜检查等。", "name": "医疗程序", "name_full": "医疗程序实体", "demos": [{"sentence": "张伟今年35岁，因为对青霉素过敏，在北京协和医院接受了心脏搭桥手术。", "entity_span": "心脏搭桥手术", "entity_type": null, "label": "CORRECT", "correct_span": null, "correct_type": null, "reason": null}, {"sentence": "他需要进行一系列的检查。", "entity_span": "检查", "entity_type": null, "label": "NA", "correct_span": null, "correct_type": null, "reason": null}]}}}