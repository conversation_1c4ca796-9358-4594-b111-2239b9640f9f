{"sentence": "Where can I find tickets for the new James Bond movie in theaters", "entity_names": ["James Bond"], "entity_types": ["Title"]}
{"sentence": "I need to know the showtimes for the latest Avengers film right now", "entity_names": ["Avengers"], "entity_types": ["Title"]}
{"sentence": "What's the fastest way to get tickets for the upcoming Star Wars movie", "entity_names": ["Star Wars"], "entity_types": ["Title"]}
{"sentence": "Can you tell me the plot of the latest Cinderella movie?", "entity_names": ["Cinderella"], "entity_types": ["Title"]}
{"sentence": "Is there a movie directed by <PERSON><PERSON> with a heartwarming storyline?", "entity_names": ["Greta Gerwig", "heartwarming"], "entity_types": ["Director", "Plot"]}
{"sentence": "Which film featuring <PERSON><PERSON><PERSON> has the most romantic plot?", "entity_names": ["Zendaya", "romantic"], "entity_types": ["Actor", "Plot"]}
{"sentence": "Can I buy tickets for the latest James Bond movie?", "entity_names": ["James Bond"], "entity_types": ["Title"]}
{"sentence": "Is there a sci-fi movie with a soundtrack by <PERSON> playing at the theater today?", "entity_names": ["sci-fi", "<PERSON>"], "entity_types": ["Genre", "Song"]}
{"sentence": "I'm looking for a movie directed by <PERSON> Nolan and rated R. Do you have any suggestions?", "entity_names": ["Christopher Nolan", "R"], "entity_types": ["Director", "MPAA Rating"]}
{"sentence": "Can you recommend a film with an iconic soundtrack from the 80s?", "entity_names": ["iconic", "80s"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "I'm looking for a movie with a character who performs a memorable musical number. Can you suggest one?", "entity_names": ["musical number"], "entity_types": ["Character"]}
{"sentence": "Can you provide me with the director of the documentary on the making of The Dark Knight?", "entity_names": ["documentary on the making of The Dark Knight"], "entity_types": ["Title"]}
{"sentence": "I'm interested in a film about the history of visual effects in cinema, what options do you have?", "entity_names": ["film", "history of visual effects in cinema"], "entity_types": ["Title", "Plot"]}
{"sentence": "Show me a movie featuring a detailed behind-the-scenes look at the special effects in the Lord of the Rings trilogy.", "entity_names": ["behind-the-scenes look", "special effects", "Lord of the Rings trilogy"], "entity_types": ["Plot", "Plot", "Title"]}
{"sentence": "Can you tell me about the director of the movie Inception?", "entity_names": ["director", "Inception"], "entity_types": ["Director", "Title"]}
{"sentence": "Show me a movie with a really intense fight scene.", "entity_names": ["intense fight scene"], "entity_types": ["Plot"]}
{"sentence": "Can you tell me about the special effects in the latest Avengers movie?", "entity_names": ["Avengers"], "entity_types": ["Title"]}
{"sentence": "Who directed the Transformers movie that came out in 2007?", "entity_names": ["directed", "Transformers", "2007"], "entity_types": ["Director", "Title", "Year"]}
{"sentence": "Show me a trailer for the new Spider-Man movie with Tom Holland.", "entity_names": ["trailer", "Spider-Man", "Tom Holland"], "entity_types": ["Trailer", "Title", "Actor"]}
{"sentence": "Can you tell me about the director of the recent coming-of-age romantic comedy film?", "entity_names": ["coming-of-age", "romantic comedy"], "entity_types": ["Genre", "Genre"]}
{"sentence": "Are there any action movies with a female lead that came out last year?", "entity_names": ["action", "female lead", "last year"], "entity_types": ["Genre", "Character", "Year"]}
{"sentence": "I'm interested in learning more about the special effects in the new science fiction movie, can you share some details?", "entity_names": ["special effects", "science fiction"], "entity_types": ["Plot", "Genre"]}
{"sentence": "I want to see a trailer for a thriller film directed by Christopher Nolan", "entity_names": ["trailer", "thriller", "Christopher Nolan"], "entity_types": ["Trailer", "Genre", "Director"]}
{"sentence": "Can you recommend a movie teaser with a strong female lead and a song by Beyonc\u00e9", "entity_names": ["teaser", "strong female lead", "Beyonc\u00e9"], "entity_types": ["Trailer", "Character", "Song"]}
{"sentence": "Show me a preview of a sci-fi movie from the 90s that has a cult following", "entity_names": ["preview", "sci-fi", "90s", "cult following"], "entity_types": ["Trailer", "Genre", "Year", "Genre"]}
{"sentence": "Can you tell me about a movie with a famous song in it?", "entity_names": ["famous song"], "entity_types": ["Song"]}
{"sentence": "Hey there! Can you show me a trailer for the new James Bond movie?", "entity_names": ["trailer", "James Bond"], "entity_types": ["Trailer", "Title"]}
{"sentence": "Hi! I'm in the mood for some action. Can you recommend a movie teaser with lots of explosions?", "entity_names": ["action", "movie teaser", "explosions"], "entity_types": ["Genre", "Trailer", "Plot"]}
{"sentence": "Hello! I'm looking for a romantic comedy. Could you show me a teaser for a popular one?", "entity_names": ["romantic comedy", "teaser", "popular"], "entity_types": ["Genre", "Trailer", "Viewers' Rating"]}
{"sentence": "Can you provide me with behind-the-scenes footage of the Avengers: Endgame movie", "entity_names": ["behind-the-scenes footage", "Avengers: Endgame"], "entity_types": ["Plot", "Title"]}
{"sentence": "Who directed the film Inception and can you show me some behind-the-scenes clips", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "Can you suggest me a sci-fi movie that includes time travel?", "entity_names": ["sci-fi", "time travel"], "entity_types": ["Genre", "Plot"]}
{"sentence": "What action movies with a superhero theme have been popular lately?", "entity_names": ["action", "superhero"], "entity_types": ["Genre", "Plot"]}
{"sentence": "I'm looking for a horror film with zombies. Can you recommend any?", "entity_names": ["horror", "zombies"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Can you recommend a movie with a behind-the-scenes featurette about its special effects and stunts?", "entity_names": ["behind-the-scenes featurette", "special effects and stunts"], "entity_types": ["Plot", "Genre"]}
{"sentence": "Is there a sci-fi film from the 90s with a bonus feature showing the actors' training for their roles?", "entity_names": ["sci-fi film", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "I'm curious to know more about the storyline of a recent action movie, could you provide some details?", "entity_names": ["storyline", "action movie"], "entity_types": ["Plot", "Genre"]}
{"sentence": "I'm looking for a film that has a really intriguing plot, do you have any recommendations?", "entity_names": ["intriguing plot"], "entity_types": ["Plot"]}
{"sentence": "I'm interested in finding out more about a classic movie that has a compelling storyline, can you help me with that?", "entity_names": ["classic movie", "compelling storyline"], "entity_types": ["Title", "Plot"]}
{"sentence": "Can you show me a trailer for the latest action movie directed by Michael Bay?", "entity_names": ["trailer", "action", "Michael Bay"], "entity_types": ["Trailer", "Genre", "Director"]}
{"sentence": "What movie released in 1994 has the song 'I Will Always Love You' in it?", "entity_names": ["1994", "I Will Always Love You"], "entity_types": ["Year", "Song"]}
{"sentence": "Is there a movie with the character Jack Sparrow that has received a high viewers' rating?", "entity_names": ["Jack Sparrow", "high viewers' rating"], "entity_types": ["Character", "Viewers' Rating"]}
{"sentence": "Hey, can you tell me about a movie directed by Quentin Tarantino that gives a behind-the-scenes look at filmmaking?", "entity_names": ["Quentin Tarantino", "behind-the-scenes"], "entity_types": ["Director", "Plot"]}
{"sentence": "I'm so pumped to learn about a classic action movie from the 80s with a killer soundtrack. Can you show me a behind-the-scenes clip?", "entity_names": ["classic action", "80s", "killer soundtrack", "behind-the-scenes"], "entity_types": ["Genre", "Year", "Song", "Trailer"]}
{"sentence": "I'm dying to know about a suspenseful thriller directed by Alfred Hitchcock. Can you please share some behind-the-scenes secrets with me?", "entity_names": ["suspenseful thriller", "Alfred Hitchcock", "behind-the-scenes"], "entity_types": ["Genre", "Director", "Plot"]}
{"sentence": "Is the movie Inception available to stream?", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "What are the top-rated 80s action movies available to stream?", "entity_names": ["80s", "action"], "entity_types": ["Year", "Genre"]}
{"sentence": "Could you provide me with the trailer for the movie The Dark Knight", "entity_names": ["trailer", "The Dark Knight"], "entity_types": ["Trailer", "Title"]}
{"sentence": "I am interested in watching a movie directed by Steven Spielberg. Can you recommend a film with a captivating trailer?", "entity_names": ["Steven Spielberg", "trailer"], "entity_types": ["Director", "Trailer"]}
{"sentence": "What is the viewers' rating for the movie Inception", "entity_names": ["viewers' rating", "Inception"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "What are the showtimes for the new Marvel movie?", "entity_names": ["Marvel"], "entity_types": ["Genre"]}
{"sentence": "Do you have tickets available for the re-release of The Lion King?", "entity_names": ["The Lion King"], "entity_types": ["Title"]}
{"sentence": "Can you tell me about the director of the latest Marvel movie and any special features on the Blu-ray edition?", "entity_names": ["Marvel movie", "special features"], "entity_types": ["Title", "Plot"]}
{"sentence": "I'm looking for a movie from the 90s with a popular soundtrack and some bloopers from the filming.", "entity_names": ["90s", "popular soundtrack", "bloopers"], "entity_types": ["Year", "Song", "Plot"]}
{"sentence": "Are there any recent action movies with an audio commentary by the director or main cast members?", "entity_names": ["action movies", "audio commentary", "director", "main cast members"], "entity_types": ["Genre", "Plot", "Director", "Actor"]}
{"sentence": "Can you recommend a movie with Tom Hanks and Meg Ryan?", "entity_names": ["Tom Hanks", "Meg Ryan"], "entity_types": ["Actor", "Actor"]}
{"sentence": "Can you recommend a movie with a really intense trailer?", "entity_names": [], "entity_types": []}
{"sentence": "What are some popular movies with amazing teasers?", "entity_names": ["popular", "amazing", "teasers"], "entity_types": ["Viewers' Rating", "Review", "Trailer"]}
{"sentence": "Which movie has the most intriguing teaser of all time?", "entity_names": ["intriguing", "teaser"], "entity_types": ["Review", "Trailer"]}
{"sentence": "Can you recommend any movies with the song 'Unchained Melody'?", "entity_names": ["Unchained Melody"], "entity_types": ["Song"]}
{"sentence": "What movie from the 1950s has the best soundtrack?", "entity_names": ["1950s", "best soundtrack"], "entity_types": ["Year", "Review"]}
{"sentence": "Are there any old movies with a great love song?", "entity_names": ["great love song"], "entity_types": ["Review"]}
{"sentence": "Can I watch the movie Sleepless in Seattle on any streaming platforms?", "entity_names": ["Sleepless in Seattle"], "entity_types": ["Title"]}
{"sentence": "Is the movie When Harry Met Sally available to stream anywhere?", "entity_names": ["When Harry Met Sally"], "entity_types": ["Title"]}
{"sentence": "Are there any classic romantic comedies like Pretty Woman that I can watch on a streaming service?", "entity_names": ["romantic comedies", "Pretty Woman"], "entity_types": ["Genre", "Title"]}
{"sentence": "Can you recommend a movie with an amazing soundtrack?", "entity_names": ["amazing soundtrack"], "entity_types": ["Review"]}
{"sentence": "Who composed the music for the movie Inception?", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "What are some of the best musical movies of all time?", "entity_names": ["best musical"], "entity_types": ["Viewers' Rating"]}
{"sentence": "Are there any G-rated animated movies playing today?", "entity_names": ["G-rated", "animated"], "entity_types": ["MPAA Rating", "Genre"]}
{"sentence": "What family-friendly movies are showing this weekend?", "entity_names": ["family-friendly"], "entity_types": ["Viewers' Rating"]}
{"sentence": "I'm looking for showtimes and tickets for the movie Inception directed by Christopher Nolan", "entity_names": ["Inception", "Christopher Nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "Can you find me tickets for the latest Marvel movie, with a high viewers' rating?", "entity_names": ["Marvel", "high viewers' rating"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "I want to watch a movie released in 2020 with a compelling plot and a great soundtrack.", "entity_names": ["2020", "compelling plot", "great soundtrack"], "entity_types": ["Year", "Plot", "Song"]}
{"sentence": "Hey, what's the name of the movie with the cool song 'Eye of the Tiger' in it?", "entity_names": ["Eye of the Tiger"], "entity_types": ["Song"]}
{"sentence": "Do you know any good movies with awesome music like 'Bohemian Rhapsody' in them?", "entity_names": ["Bohemian Rhapsody"], "entity_types": ["Song"]}
{"sentence": "I'm looking for a film with a killer soundtrack, like 'Purple Rain' or 'Footloose'. Any recommendations?", "entity_names": ["Purple Rain", "Footloose"], "entity_types": ["Song", "Song"]}
{"sentence": "can you give me a brief summary of the film Inception", "entity_names": ["summary", "Inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "what is the storyline for the movie The Shawshank Redemption", "entity_names": ["storyline", "The Shawshank Redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "tell me about the plot of the action movie Mad Max: Fury Road", "entity_names": ["plot", "Mad Max: Fury Road"], "entity_types": ["Plot", "Title"]}
{"sentence": "Are there any streaming platforms where I can watch the movie Inception?", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "Can you recommend a movie from the 1980s that has a great soundtrack?", "entity_names": ["1980s", "great soundtrack"], "entity_types": ["Year", "Review"]}
{"sentence": "I'm looking for a comedy film directed by Christopher Guest, do you have any suggestions?", "entity_names": ["comedy", "Christopher Guest"], "entity_types": ["Genre", "Director"]}
{"sentence": "Can you recommend a highly-rated action movie from the 1990s?", "entity_names": ["highly-rated", "action movie", "1990s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "Show me a film with a compelling plot and strong performances from Meryl Streep.", "entity_names": ["Meryl Streep"], "entity_types": ["Actor"]}
{"sentence": "Who's in that new action movie where stuff explodes?", "entity_names": ["new action movie"], "entity_types": ["Genre"]}
{"sentence": "What's that movie where Tom Hanks plays a character stranded on an island?", "entity_names": ["Tom Hanks"], "entity_types": ["Actor"]}
{"sentence": "Do you know who directed the latest comedy film with Jennifer Lawrence?", "entity_names": ["latest comedy film", "Jennifer Lawrence"], "entity_types": ["Genre", "Actor"]}
{"sentence": "Can you tell me what awards and nominations the Shawshank Redemption received?", "entity_names": ["awards and nominations", "Shawshank Redemption"], "entity_types": ["Review", "Title"]}
{"sentence": "I'm looking for an action movie from the 2000s with a high viewers' rating. Any suggestions?", "entity_names": ["action", "2000s", "high viewers' rating"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "Can you tell me the showtimes for the Avengers: Endgame?", "entity_names": ["Avengers: Endgame"], "entity_types": ["Title"]}
{"sentence": "Are there any available tickets for the latest Fast and Furious movie?", "entity_names": ["Fast and Furious"], "entity_types": ["Title"]}
{"sentence": "What is the genre of the latest James Bond film?", "entity_names": ["James Bond"], "entity_types": ["Character"]}
{"sentence": "What's the highest rated movie of 2021 so far?", "entity_names": ["highest rated", "2021"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "Give me the best action film of all time now!", "entity_names": ["best", "action", "all time"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "Could you show me the trailer for the latest James Bond film?", "entity_names": ["trailer", "James Bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "I'm in the mood for something exciting! Can you recommend a movie with an action-packed teaser?", "entity_names": ["exciting", "action-packed", "teaser"], "entity_types": ["Viewers' Rating", "Genre", "Trailer"]}
{"sentence": "I'm feeling really curious about the new animated film. Can you play a teaser for it?", "entity_names": ["curious", "animated", "teaser"], "entity_types": ["Viewers' Rating", "Genre", "Trailer"]}
{"sentence": "I'm not sure what to watch tonight, can you give me a brief overview of a movie with a thrilling plot?", "entity_names": ["thrilling"], "entity_types": ["Plot"]}
{"sentence": "I can't decide what movie to watch, do you have any recommendations for a film with a suspenseful storyline?", "entity_names": ["recommendations", "suspenseful"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "I'm in the mood for a movie with an engaging storyline, can you tell me about a film that has a captivating plot?", "entity_names": ["engaging"], "entity_types": ["Genre"]}
{"sentence": "I'm looking for a movie with a captivating and unpredictable plot", "entity_names": ["captivating and unpredictable"], "entity_types": ["Plot"]}
{"sentence": "Can you recommend a film with an intriguing storyline and unexpected twists?", "entity_names": ["intriguing storyline and unexpected twists"], "entity_types": ["Plot"]}
{"sentence": "I want to watch a movie with a gripping and mind-bending plot", "entity_names": ["gripping and mind-bending"], "entity_types": ["Plot"]}
{"sentence": "What movie features the song 'Let It Go'?", "entity_names": ["Let It Go"], "entity_types": ["Song"]}
{"sentence": "I want to watch a film with a romantic soundtrack, can you suggest one?", "entity_names": ["romantic", "soundtrack"], "entity_types": ["Genre", "Plot"]}
{"sentence": "What children's movies are in the fantasy genre?", "entity_names": ["children's movies", "fantasy"], "entity_types": ["Genre", "Genre"]}
{"sentence": "Can you recommend a family-friendly movie in the animated musical genre?", "entity_names": ["family-friendly", "animated musical"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "What animated films can you suggest that fall under the adventure genre?", "entity_names": ["animated films", "adventure"], "entity_types": ["Genre", "Genre"]}
{"sentence": "Can you tell me about the making-of the newest Star Wars movie?", "entity_names": ["making-of", "newest Star Wars movie"], "entity_types": ["Plot", "Title"]}
{"sentence": "Is there a documentary about the special effects in the Marvel superhero movies?", "entity_names": ["documentary", "special effects", "Marvel superhero movies"], "entity_types": ["Genre", "Plot", "Title"]}
{"sentence": "I'm looking for a film that shows the director's cut and deleted scenes from The Dark Knight.", "entity_names": ["director's cut", "deleted scenes", "The Dark Knight"], "entity_types": ["Plot", "Plot", "Title"]}
{"sentence": "Can you tell me when and where I can see the new Spider-Man movie?", "entity_names": ["Spider-Man"], "entity_types": ["Title"]}
{"sentence": "I'm really looking forward to watching a romantic comedy with Julia Roberts, can you suggest one for me?", "entity_names": ["romantic comedy", "Julia Roberts"], "entity_types": ["Genre", "Actor"]}
{"sentence": "I heard great things about the new James Bond movie, can you help me find tickets for it?", "entity_names": ["James Bond"], "entity_types": ["Title"]}
{"sentence": "Can you tell me about a recent action movie with a thrilling story?", "entity_names": ["action movie", "thrilling story"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Who directed the romantic comedy that everyone loved last year?", "entity_names": ["romantic comedy", "last year"], "entity_types": ["Genre", "Year"]}
{"sentence": "I'm interested in a movie with a powerful soundtrack. Can you recommend one?", "entity_names": ["powerful soundtrack"], "entity_types": ["Song"]}
{"sentence": "Can you recommend a highly rated comedy from the 2000s?", "entity_names": ["highly rated", "comedy", "2000s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "Who are the main actors in the latest sci-fi movie directed by Christopher Nolan?", "entity_names": ["sci-fi", "Christopher Nolan"], "entity_types": ["Genre", "Director"]}
{"sentence": "I'm looking for a popular action movie with great special effects. Can you suggest one?", "entity_names": ["popular", "action", "great special effects"], "entity_types": ["Viewers' Rating", "Genre", "Review"]}
{"sentence": "Can you tell me the plot of the movie Inception directed by Christopher Nolan?", "entity_names": ["Plot", "Inception", "Christopher Nolan"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "I'm looking for a movie with an exciting and mysterious plot. Can you recommend a film for me?", "entity_names": [], "entity_types": []}
{"sentence": "I want to watch a movie that has a thrilling and engaging storyline. Can you suggest one?", "entity_names": ["thrilling and engaging"], "entity_types": ["Plot"]}
{"sentence": "Can you show me the trailer for the new James Bond movie?", "entity_names": ["trailer", "James Bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "I'm looking for a movie teaser with an epic battle scene. Can you recommend one?", "entity_names": ["teaser", "epic battle scene"], "entity_types": ["Trailer", "Plot"]}
{"sentence": "Could you play a sneak peek of the upcoming Marvel movie?", "entity_names": ["sneak peek", "Marvel"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "Can you give me a brief overview of the plot for the movie Inception?", "entity_names": ["plot", "Inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "I'm looking for a movie directed by Christopher Nolan with a mind-bending plot and a thrilling soundtrack.", "entity_names": ["Christopher Nolan", "mind-bending plot", "thrilling soundtrack"], "entity_types": ["Director", "Plot", "Song"]}
{"sentence": "Show me a film featuring an ensemble cast, a gripping plot, and a high viewers' rating.", "entity_names": ["ensemble cast", "gripping plot", "high viewers' rating"], "entity_types": ["Character", "Plot", "Viewers' Rating"]}
{"sentence": "What's that movie with the really cool fight scenes and is considered a classic martial arts film?", "entity_names": ["really cool fight scenes", "classic", "martial arts"], "entity_types": ["Plot", "Viewers' Rating", "Genre"]}
{"sentence": "Who directed that awesome horror movie with a killer clown that scared the pants off everyone?", "entity_names": ["horror", "killer clown", "scared the pants off everyone"], "entity_types": ["Genre", "Character", "Review"]}
{"sentence": "Is there a romantic comedy with a really catchy theme song that came out in the 90s?", "entity_names": ["romantic comedy", "catchy theme song", "90s"], "entity_types": ["Genre", "Song", "Year"]}
{"sentence": "Can you find me a movie teaser that reveals just enough, but not too much?", "entity_names": ["movie teaser"], "entity_types": ["Trailer"]}
{"sentence": "Is there a film out there with a sneak peek that's worth watching?", "entity_names": ["sneak peek"], "entity_types": ["Trailer"]}
{"sentence": "Could you show me a preview for a new release that leaves something to the imagination?", "entity_names": ["preview"], "entity_types": ["Trailer"]}
{"sentence": "Can I stream the movie Arrival on any platform?", "entity_names": ["Arrival"], "entity_types": ["Title"]}
{"sentence": "Is there a streaming service where I can watch the movie Titanic?", "entity_names": ["streaming service", "Titanic"], "entity_types": ["Plot", "Title"]}
{"sentence": "Which platform has the movie The Dark Knight available for streaming?", "entity_names": ["platform", "The Dark Knight"], "entity_types": ["Plot", "Title"]}
{"sentence": "I'm so bored, can you recommend a thriller movie directed by Alfred Hitchcock?", "entity_names": ["thriller", "Alfred Hitchcock"], "entity_types": ["Genre", "Director"]}
{"sentence": "I need something to watch, how about a comedy film from the 1990s?", "entity_names": ["comedy", "1990s"], "entity_types": ["Genre", "Year"]}
{"sentence": "I'm bored out of my mind, show me a classic romance movie featuring Audrey Hepburn.", "entity_names": ["romance", "Audrey Hepburn"], "entity_types": ["Genre", "Actor"]}
{"sentence": "I'm looking for a feel-good movie that has won multiple awards, any recommendations?", "entity_names": ["feel-good", "multiple awards"], "entity_types": ["Genre", "Review"]}
{"sentence": "Can you tell me about the making-of the movie Interstellar?", "entity_names": ["Interstellar"], "entity_types": ["Title"]}
{"sentence": "Who directed the behind-the-scenes documentary for the movie Jurassic Park?", "entity_names": ["Jurassic Park"], "entity_types": ["Title"]}
{"sentence": "Show me some exclusive interviews with the cast of the movie Titanic.", "entity_names": ["cast", "Titanic"], "entity_types": ["Character", "Title"]}
{"sentence": "Who directed the action thriller film with the highest viewers' rating?", "entity_names": ["action thriller", "highest viewers' rating"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "Can you tell me which movie from the 90s has Denzel Washington as the lead actor?", "entity_names": ["90s", "Denzel Washington"], "entity_types": ["Year", "Actor"]}
{"sentence": "I'm looking for a fantasy movie directed by a female director, can you recommend one?", "entity_names": ["fantasy", "female director"], "entity_types": ["Genre", "Director"]}
{"sentence": "Can you recommend a heartwarming romance movie from the 90s with a strong female lead", "entity_names": ["romance", "90s", "strong female lead"], "entity_types": ["Genre", "Year", "Character"]}
{"sentence": "I'm looking for a feel-good drama film from the 80s that showcases family values", "entity_names": ["feel-good", "drama", "80s", "family values"], "entity_types": ["Genre", "Genre", "Year", "Plot"]}
{"sentence": "What comedy movie directed by Nancy Meyers would you suggest for a girls' night out", "entity_names": ["comedy", "Nancy Meyers", "girls' night out"], "entity_types": ["Genre", "Director", "Viewers' Rating"]}
{"sentence": "Can I see the trailer for the new high school musical movie?", "entity_names": ["trailer", "the new high school musical movie"], "entity_types": ["Trailer", "Title"]}
{"sentence": "What's a good coming-of-age film that came out this year that I can watch?", "entity_names": ["coming-of-age", "this year"], "entity_types": ["Genre", "Year"]}
{"sentence": "What is the viewers' rating of the film The Shawshank Redemption?", "entity_names": ["viewers' rating", "The Shawshank Redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "Could you recommend a movie with Meryl Streep as the lead actress?", "entity_names": ["Meryl Streep"], "entity_types": ["Actor"]}
{"sentence": "Can you tell me about the director of the movie Lawrence of Arabia?", "entity_names": ["director", "Lawrence of Arabia"], "entity_types": ["Director", "Title"]}
{"sentence": "I'm a fan of Robert De Niro, can you recommend a movie he starred in?", "entity_names": ["Robert De Niro"], "entity_types": ["Actor"]}
{"sentence": "What is the viewers' rating for the film The Godfather?", "entity_names": ["viewers' rating", "The Godfather"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "Can you tell me about the making-of featurette for the movie Inception?", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "Who was the director of the behind-the-scenes documentary for the film Jurassic Park?", "entity_names": ["Jurassic Park"], "entity_types": ["Title"]}
{"sentence": "I'm curious about the special effects in the movie Avatar, can you show me a behind-the-scenes clip?", "entity_names": ["Avatar"], "entity_types": ["Title"]}
{"sentence": "What kind of movie is The Dark Knight? Like, is it action or thriller or what?", "entity_names": ["The Dark Knight"], "entity_types": ["Title"]}
{"sentence": "Can you recommend a rom-com from the 90s? You know, something lighthearted and funny?", "entity_names": ["rom-com", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "Who directed that sci-fi movie about time travel and stuff? I think it's called Inception or something like that.", "entity_names": ["sci-fi", "time travel", "Inception"], "entity_types": ["Genre", "Plot", "Title"]}
{"sentence": "Can you tell me about any award-winning movies from the 1990s", "entity_names": ["award-winning", "1990s"], "entity_types": ["Review", "Year"]}
{"sentence": "I'm interested in a critically acclaimed film from the 2010s", "entity_names": ["critically acclaimed", "2010s"], "entity_types": ["Review", "Year"]}
{"sentence": "Could you recommend a movie that has received multiple nominations?", "entity_names": ["multiple nominations"], "entity_types": ["Review"]}
{"sentence": "Who directed the fantasy film with the best visual effects of all time?", "entity_names": ["fantasy", "visual effects"], "entity_types": ["Genre", "Genre"]}
{"sentence": "Can you recommend a movie directed by Martin Scorsese that features Leonardo DiCaprio?", "entity_names": ["Martin Scorsese", "Leonardo DiCaprio"], "entity_types": ["Director", "Actor"]}
{"sentence": "What is the highest-rated comedy film that stars Jim Carrey?", "entity_names": ["highest-rated", "comedy", "Jim Carrey"], "entity_types": ["Viewers' Rating", "Genre", "Actor"]}
{"sentence": "I am interested in learning more about the filming locations of the movie The Godfather. Can you assist me with that?", "entity_names": ["filming locations", "The Godfather"], "entity_types": ["Plot", "Title"]}
{"sentence": "What are some highly acclaimed science fiction movies from the 1980s?", "entity_names": ["science fiction", "1980s"], "entity_types": ["Genre", "Year"]}
{"sentence": "What is the title of the movie featuring the song 'My Heart Will Go On'?", "entity_names": ["My Heart Will Go On"], "entity_types": ["Song"]}
{"sentence": "Can you tell me the name of a movie with a great soundtrack?", "entity_names": ["great soundtrack"], "entity_types": ["Review"]}
{"sentence": "Do you have any information on a movie with a famous song by Elvis Presley?", "entity_names": ["famous song by Elvis Presley"], "entity_types": ["Song"]}
{"sentence": "I'm curious, is there a trailer for the new Martin Scorsese movie about organized crime?", "entity_names": ["trailer", "Martin Scorsese", "organized crime"], "entity_types": ["Trailer", "Director", "Plot"]}
{"sentence": "What's the behind-the-scenes story of the latest Star Wars movie?", "entity_names": ["behind-the-scenes story", "Star Wars"], "entity_types": ["Plot", "Title"]}
{"sentence": "Yo, who's the director of that new action flick with all the explosions and stuff?", "entity_names": ["action flick", "explosions"], "entity_types": ["Genre", "Plot"]}
{"sentence": "What's the deal with that movie where the dude from Avengers plays a spy or something?", "entity_names": ["spy"], "entity_types": ["Genre"]}
{"sentence": "I heard there's a film coming out soon with that badass actress from the last sci-fi movie. What's it called?", "entity_names": ["badass actress", "sci-fi movie"], "entity_types": ["Actor", "Genre"]}
{"sentence": "Can you play the trailer for the new animated movie featuring talking animals?", "entity_names": ["trailer", "new", "animated", "talking animals"], "entity_types": ["Trailer", "Year", "Genre", "Plot"]}
{"sentence": "What family-friendly movie with a catchy theme song was released in the past year?", "entity_names": ["family-friendly", "catchy theme song", "past year"], "entity_types": ["Genre", "Song", "Year"]}
{"sentence": "Who directed the latest fantasy adventure film for kids?", "entity_names": ["fantasy adventure", "kids"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "I need a quick rundown of the plot for the latest James Bond film", "entity_names": ["James Bond"], "entity_types": ["Character"]}
{"sentence": "Can you tell me what the new Marvel superhero movie is all about?", "entity_names": ["Marvel", "superhero"], "entity_types": ["Genre", "Genre"]}
{"sentence": "I'm in a rush, but I need to know the storyline of that award-winning romantic movie from last year", "entity_names": ["romantic", "last year"], "entity_types": ["Genre", "Year"]}
{"sentence": "Can you recommend a movie with an iconic soundtrack that is perfect for road trips?", "entity_names": ["iconic soundtrack", "road trips"], "entity_types": ["Song", "Genre"]}
{"sentence": "Show me a movie with a thrilling action scene accompanied by an exciting musical score.", "entity_names": ["thrilling action scene", "exciting musical score"], "entity_types": ["Plot", "Song"]}
{"sentence": "I'm looking for a film with an unforgettable song that perfectly captures the adventurous spirit of the characters.", "entity_names": ["unforgettable song", "adventurous spirit"], "entity_types": ["Song", "Character"]}
{"sentence": "What is the viewers' rating for the movie Inception directed by Christopher Nolan", "entity_names": ["viewers' rating", "Inception", "Christopher Nolan"], "entity_types": ["Viewers' Rating", "Title", "Director"]}
{"sentence": "Could you provide the plot summary for the film Shawshank Redemption", "entity_names": ["plot summary", "Shawshank Redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "Show me the trailer for the latest James Bond movie", "entity_names": ["trailer", "latest", "James Bond"], "entity_types": ["Trailer", "Year", "Character"]}
{"sentence": "Can you tell me which movies feature the song 'Bohemian Rhapsody' by Queen?", "entity_names": ["Bohemian Rhapsody"], "entity_types": ["Song"]}
{"sentence": "I'm looking for a movie with an epic original soundtrack that will blow my mind. Can you suggest one?", "entity_names": ["epic original soundtrack"], "entity_types": ["Song"]}
{"sentence": "What film has the most iconic music of all time?", "entity_names": ["most iconic music"], "entity_types": ["Review"]}
{"sentence": "What awards did the movie The Shape of Water win?", "entity_names": ["The Shape of Water"], "entity_types": ["Title"]}
{"sentence": "Has the director of Parasite received any nominations for the film?", "entity_names": ["Parasite"], "entity_types": ["Title"]}
{"sentence": "What are some action movies with an epic soundtrack?", "entity_names": ["action movies", "epic soundtrack"], "entity_types": ["Genre", "Song"]}
{"sentence": "Show me a movie from the 90s with a killer soundtrack.", "entity_names": ["90s", "killer soundtrack"], "entity_types": ["Year", "Song"]}
{"sentence": "Can you recommend a movie with an awesome theme song?", "entity_names": ["awesome theme song"], "entity_types": ["Song"]}
{"sentence": "What is the specific subgenre of horror does the movie Hereditary fall under", "entity_names": ["specific subgenre", "horror", "Hereditary"], "entity_types": ["Review", "Genre", "Title"]}
{"sentence": "Can you recommend a crime movie directed by Quentin Tarantino that has a neo-noir style", "entity_names": ["crime", "Quentin Tarantino", "neo-noir"], "entity_types": ["Genre", "Director", "Genre"]}
{"sentence": "Which science fiction film from the 1980s directed by James Cameron is known for its groundbreaking special effects", "entity_names": ["science fiction", "1980s", "James Cameron", "groundbreaking special effects"], "entity_types": ["Genre", "Year", "Director", "Review"]}
{"sentence": "Can I watch the movie The Shawshank Redemption on any streaming platform?", "entity_names": ["The Shawshank Redemption"], "entity_types": ["Title"]}
{"sentence": "Is the movie Inception available for streaming anywhere?", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "I'm in the mood for a classic movie. Where can I watch Casablanca?", "entity_names": ["Casablanca"], "entity_types": ["Title"]}
{"sentence": "Can you recommend a must-see action movie from the 90s directed by Michael Bay?", "entity_names": ["must-see", "action", "90s", "Michael Bay"], "entity_types": ["Viewers' Rating", "Genre", "Year", "Director"]}
{"sentence": "I'm looking for a critically acclaimed drama film from the 2000s with a strong performance by Meryl Streep.", "entity_names": ["critically acclaimed", "drama", "2000s", "Meryl Streep"], "entity_types": ["Review", "Genre", "Year", "Actor"]}
{"sentence": "Can you tell me about the plot of the movie Blade Runner 2049 directed by Denis Villeneuve?", "entity_names": ["Blade Runner 2049", "Denis Villeneuve"], "entity_types": ["Title", "Director"]}
{"sentence": "What is the storyline of the romantic movie La La Land?", "entity_names": ["romantic", "La La Land"], "entity_types": ["Genre", "Title"]}
{"sentence": "Could you provide a summary of the plot for the film Inception directed by Christopher Nolan?", "entity_names": ["plot", "Inception", "Christopher Nolan"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "Can you tell me what the latest Liam Neeson movie is about?", "entity_names": ["Liam Neeson"], "entity_types": ["Actor"]}
{"sentence": "Hey, what's the deal with that new horror flick with the haunted house?", "entity_names": ["horror", "haunted house"], "entity_types": ["Genre", "Plot"]}
{"sentence": "What's the story behind that Tom Hanks movie where he gets stranded on a desert island?", "entity_names": ["Tom Hanks", "desert island"], "entity_types": ["Actor", "Plot"]}
{"sentence": "Who directed the movie Titanic and what is the viewers' rating for it?", "entity_names": ["directed", "Titanic", "viewers' rating"], "entity_types": ["Director", "Title", "Viewers' Rating"]}
{"sentence": "Can you recommend me a movie with Meryl Streep as the lead actor?", "entity_names": ["Meryl Streep", "lead actor"], "entity_types": ["Actor", "Character"]}
{"sentence": "What is the plot of the film Inception directed by Christopher Nolan?", "entity_names": ["plot", "Inception", "Christopher Nolan"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "Hey, what kind of movie is The Avengers? Like, is it an action movie or something else?", "entity_names": ["The Avengers", "action"], "entity_types": ["Title", "Genre"]}
{"sentence": "I'm in the mood for a scary movie. Can you recommend a good horror film from the 80s or 90s?", "entity_names": ["scary movie", "horror film", "80s or 90s"], "entity_types": ["Genre", "Genre", "Year"]}
{"sentence": "Do you know if there are any good romantic comedy movies coming out soon? I could use a good laugh and some lovey-dovey stuff.", "entity_names": ["romantic comedy"], "entity_types": ["Genre"]}
{"sentence": "Can you recommend a good thriller from the 90s?", "entity_names": ["recommend", "thriller", "90s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "Can you tell me who directed the movie 'The Goonies', and if it's as good as I remember?", "entity_names": ["directed", "The Goonies"], "entity_types": ["Director", "Title"]}
{"sentence": "I remember a movie with Tom Hanks and Meg Ryan, can you remind me of the title and the year it came out?", "entity_names": ["Tom Hanks", "Meg Ryan", "title", "year"], "entity_types": ["Actor", "Actor", "Title", "Year"]}
{"sentence": "I loved the soundtrack in 'Dirty Dancing', can you tell me the name of the song that played during the final dance scene?", "entity_names": ["soundtrack", "Dirty Dancing"], "entity_types": ["Song", "Title"]}
{"sentence": "Who directed the movie The Shawshank Redemption?", "entity_names": ["The Shawshank Redemption"], "entity_types": ["Title"]}
{"sentence": "Show me a movie starring Meryl Streep.", "entity_names": ["Meryl Streep"], "entity_types": ["Actor"]}
{"sentence": "Can you recommend a movie with a top-notch plot and a gripping review?", "entity_names": ["top-notch", "gripping"], "entity_types": ["Viewers' Rating", "Review"]}
{"sentence": "What movie from the 90s has an exceptional rating and a memorable song in its soundtrack?", "entity_names": ["90s", "exceptional", "memorable"], "entity_types": ["Year", "Viewers' Rating", "Song"]}
{"sentence": "I'm looking for a film that received rave reviews and features an outstanding performance by a renowned actor, can you suggest one?", "entity_names": ["rave reviews", "outstanding", "renowned actor"], "entity_types": ["Review", "Viewers' Rating", "Actor"]}
{"sentence": "Can you recommend a movie with a heartbreaking plot that received high viewers' ratings?", "entity_names": ["heartbreaking", "high viewers' ratings"], "entity_types": ["Plot", "Viewers' Rating"]}
{"sentence": "I'm feeling down. Can you suggest a movie with a touching storyline and a beautiful song?", "entity_names": ["touching storyline", "beautiful song"], "entity_types": ["Plot", "Song"]}
{"sentence": "I need a movie that will make me cry. Can you find a title with a heartwarming plot and a highly rated director?", "entity_names": ["make me cry", "heartwarming plot", "highly rated director"], "entity_types": ["Review", "Plot", "Director"]}
{"sentence": "What's the storyline for the highest rated animated movie of all time?", "entity_names": ["storyline", "highest rated", "animated"], "entity_types": ["Plot", "Viewers' Rating", "Genre"]}
{"sentence": "I'm curious, could you tell me the basic premise of the most recent romantic comedy?", "entity_names": ["basic premise", "most recent", "romantic comedy"], "entity_types": ["Plot", "Year", "Genre"]}
{"sentence": "can you tell me the plot of the movie inception", "entity_names": ["plot", "inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "what is the storyline of the film interstellar", "entity_names": ["storyline", "interstellar"], "entity_types": ["Plot", "Title"]}
{"sentence": "describe the synopsis of the movie the shawshank redemption", "entity_names": ["synopsis", "the shawshank redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "I'm not sure what type of movie I'm in the mood for, maybe something thrilling or suspenseful?", "entity_names": ["thrilling", "suspenseful"], "entity_types": ["Genre", "Genre"]}
{"sentence": "I'm trying to decide on a movie to watch tonight, should I go for something classic or a more modern film?", "entity_names": ["classic", "modern"], "entity_types": ["Genre", "Genre"]}
{"sentence": "I can't make up my mind if I want to watch a comedy or a romantic comedy, what do you suggest?", "entity_names": ["comedy", "romantic comedy"], "entity_types": ["Genre", "Genre"]}
{"sentence": "I don't have much time, just tell me the storyline for the film The Shawshank Redemption", "entity_names": ["storyline", "The Shawshank Redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "I need a brief synopsis for the movie Pulp Fiction, hurry up!", "entity_names": ["brief synopsis", "Pulp Fiction"], "entity_types": ["Plot", "Title"]}
{"sentence": "Who directed the science fiction film from 1997 that received the highest rating from viewers?", "entity_names": ["science fiction film", "1997", "highest rating"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "Can you tell me about the making of the film 'Inception' directed by Christopher Nolan?", "entity_names": ["Inception", "Christopher Nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "What are some iconic historical films directed by Steven Spielberg that I should watch?", "entity_names": ["historical films", "Steven Spielberg", "should watch"], "entity_types": ["Genre", "Director", "Viewers' Rating"]}
{"sentence": "What is the primary genre and subgenre of the film Blade Runner 2049?", "entity_names": ["Blade Runner 2049"], "entity_types": ["Title"]}
{"sentence": "Can you recommend a movie with a complex plot that falls under the science fiction genre?", "entity_names": ["complex plot", "science fiction"], "entity_types": ["Plot", "Genre"]}
{"sentence": "Which director is known for creating movies in the psychological thriller subgenre?", "entity_names": ["psychological thriller"], "entity_types": ["Genre"]}
{"sentence": "I'm not sure where to watch the movie The Shawshank Redemption, can you tell me?", "entity_names": ["The Shawshank Redemption"], "entity_types": ["Title"]}
{"sentence": "I'm not sure if I should watch a comedy or a thriller, do you have any recommendations of movies available on streaming platforms?", "entity_names": ["comedy", "thriller"], "entity_types": ["Genre", "Genre"]}
{"sentence": "I can't decide what to watch tonight, is there a popular movie from the 90s available for streaming?", "entity_names": ["90s"], "entity_types": ["Year"]}
{"sentence": "Retrieve the trailer for the 2020 science fiction film directed by Christopher Nolan.", "entity_names": ["2020", "science fiction", "Christopher Nolan"], "entity_types": ["Year", "Genre", "Director"]}
{"sentence": "Find me the teaser for the latest installment of the James Bond series.", "entity_names": ["James Bond"], "entity_types": ["Character"]}
{"sentence": "Show me the sneak peek of the upcoming Marvel superhero movie scheduled for release next year.", "entity_names": ["Marvel", "next year"], "entity_types": ["Genre", "Year"]}
{"sentence": "Can I stream the movie Goodfellas?", "entity_names": ["Goodfellas"], "entity_types": ["Title"]}
{"sentence": "Is there a streaming platform that has The Godfather?", "entity_names": ["The Godfather"], "entity_types": ["Title"]}
{"sentence": "Where can I watch The Shawshank Redemption online?", "entity_names": ["The Shawshank Redemption"], "entity_types": ["Title"]}
{"sentence": "Hey, what's the latest comedy that's playing in theaters right now?", "entity_names": ["latest comedy"], "entity_types": ["Genre"]}
{"sentence": "Can you tell me if there are any action movies from the 90s showing at the theater nearby?", "entity_names": ["action movies", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "Is there a drama film directed by Christopher Nolan that I can watch this weekend?", "entity_names": ["drama", "Christopher Nolan", "this weekend"], "entity_types": ["Genre", "Director", "Viewers' Rating"]}
{"sentence": "I want to know about the making of the movie Inception", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "Show me a documentary about the filming process of The Dark Knight", "entity_names": ["The Dark Knight"], "entity_types": ["Title"]}
{"sentence": "What insider details can you tell me about the production of The Shawshank Redemption", "entity_names": ["The Shawshank Redemption"], "entity_types": ["Title"]}
{"sentence": "I'm curious to learn about the creative process behind a classic movie that was quite influential. Can you provide insight into the making of this film?", "entity_names": ["classic movie", "influential"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "I'm interested in finding out more about a film that has an intriguing backstory in terms of its production. Can you share some details about this?", "entity_names": ["intriguing backstory", "production"], "entity_types": ["Plot", "Genre"]}
{"sentence": "I'd like to know about a movie that had a famous director known for their unique approach to filmmaking. Can you tell me more about this film and its behind-the-scenes dynamics?", "entity_names": ["famous director", "unique approach"], "entity_types": ["Director", "Director"]}
{"sentence": "Who directed the movie 'Jurassic Park'?", "entity_names": ["Jurassic Park"], "entity_types": ["Title"]}
{"sentence": "What year did the movie 'The Avengers' come out?", "entity_names": ["The Avengers"], "entity_types": ["Title"]}
{"sentence": "Can you show me a trailer for the movie 'Star Wars'?", "entity_names": ["trailer", "Star Wars"], "entity_types": ["Trailer", "Title"]}
{"sentence": "Has that movie with the intense car chase scene won any awards?", "entity_names": ["intense car chase scene"], "entity_types": ["Plot"]}
{"sentence": "Did the film about the family road trip get any nominations?", "entity_names": ["family road trip"], "entity_types": ["Plot"]}
{"sentence": "Hey, has the movie where the guy saves the world from aliens received any accolades?", "entity_names": ["saves the world from aliens"], "entity_types": ["Plot"]}
{"sentence": "Hey, can you show me a trailer for the upcoming James Bond movie?", "entity_names": ["trailer", "James Bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "I heard there's a new superhero movie coming out soon, can you give me a sneak peek?", "entity_names": ["superhero", "sneak peek"], "entity_types": ["Genre", "Trailer"]}
{"sentence": "Is there a teaser for the new animated Disney movie with the song 'Let It Go'?", "entity_names": ["teaser", "Let It Go"], "entity_types": ["Trailer", "Song"]}
{"sentence": "yo, what's the name of that movie with that dope song 'Eye of the Tiger'?", "entity_names": ["Eye of the Tiger"], "entity_types": ["Song"]}
{"sentence": "hey, can you tell me a movie with that lit song 'Old Town Road' in it?", "entity_names": ["Old Town Road"], "entity_types": ["Song"]}
{"sentence": "yo, which movie has that fire track 'Can't Stop the Feeling' by Justin Timberlake?", "entity_names": ["Can't Stop the Feeling"], "entity_types": ["Song"]}
{"sentence": "What is the making-of documentary for the movie The Lord of the Rings: The Fellowship of the Ring", "entity_names": ["The Lord of the Rings: The Fellowship of the Ring"], "entity_types": ["Title"]}
{"sentence": "Can you tell me about the special features of the film Inception? I'm bored.", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "Show me interviews with the director and cast members of The Dark Knight. I need something to do.", "entity_names": ["director", "The Dark Knight"], "entity_types": ["Director", "Title"]}
{"sentence": "Who directed the making-of documentary for the movie Inception?", "entity_names": ["directed", "making-of", "Inception"], "entity_types": ["Director", "Genre", "Title"]}
{"sentence": "What is the song featured in the end credits of the latest James Bond movie?", "entity_names": ["end credits", "latest", "James Bond"], "entity_types": ["Plot", "Year", "Character"]}
{"sentence": "Show me a trailer for the new Quentin Tarantino film, I can't wait any longer!", "entity_names": ["trailer", "new", "Quentin Tarantino"], "entity_types": ["Trailer", "Year", "Director"]}
{"sentence": "can you tell me the showtimes for the latest James Bond movie?", "entity_names": ["James Bond"], "entity_types": ["Title"]}
{"sentence": "is there a movie theater nearby playing the new action film?", "entity_names": ["action film"], "entity_types": ["Genre"]}
{"sentence": "Can you check out the trailer for that new action flick starring Keanu Reeves?", "entity_names": ["trailer", "action", "Keanu Reeves"], "entity_types": ["Trailer", "Genre", "Actor"]}
{"sentence": "Hey, could you recommend a cool movie preview to watch later?", "entity_names": ["movie preview"], "entity_types": ["Trailer"]}
{"sentence": "Do you have any teasers for the upcoming Marvel superhero film?", "entity_names": ["teasers", "Marvel"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "Can you recommend a detective movie where the main character is a retired cop hunting down a serial killer", "entity_names": ["detective movie", "retired cop", "serial killer"], "entity_types": ["Genre", "Character", "Plot"]}
{"sentence": "Who directed the movie Shawshank Redemption?", "entity_names": ["Shawshank Redemption"], "entity_types": ["Title"]}
{"sentence": "What are the top-rated movies by Steven Spielberg?", "entity_names": ["Steven Spielberg"], "entity_types": ["Director"]}
{"sentence": "Can you recommend a classic film featuring Audrey Hepburn?", "entity_names": ["classic", "Audrey Hepburn"], "entity_types": ["Viewers' Rating", "Actor"]}
{"sentence": "Can you show me a preview of the latest action movie?", "entity_names": ["preview", "latest action movie"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "Is there a movie with an iconic song that I can watch a teaser for?", "entity_names": ["iconic song", "teaser"], "entity_types": ["Song", "Trailer"]}
{"sentence": "Which director has released a new film with a thrilling plot that I can get a sneak peek of?", "entity_names": ["new film", "sneak peek"], "entity_types": ["Year", "Trailer"]}
{"sentence": "I'm in the mood for an action movie with a strong female lead. Can you recommend a recent film with that kind of plot?", "entity_names": ["action", "strong female lead"], "entity_types": ["Genre", "Character"]}
{"sentence": "I'm in the mood for a classic romantic comedy. Can you suggest a movie that will make me laugh and feel good?", "entity_names": ["romantic comedy"], "entity_types": ["Genre"]}
{"sentence": "I'm looking for a thought-provoking science fiction movie with a unique storyline. Do you have any recommendations?", "entity_names": ["thought-provoking", "science fiction"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "I'm feeling really down. Can you recommend a drama movie that will make me cry?", "entity_names": ["drama"], "entity_types": ["Genre"]}
{"sentence": "I'm in the mood for a tearjerker. Can you suggest a romance movie with a tragic plot?", "entity_names": ["romance", "tragic"], "entity_types": ["Genre", "Plot"]}
{"sentence": "I need a movie to watch that will tug at my heartstrings. Do you have any recommendations for a coming-of-age film?", "entity_names": ["coming-of-age"], "entity_types": ["Genre"]}
{"sentence": "Can you tell me about the director and the filming process of the movie The Sound of Music?", "entity_names": ["director", "The Sound of Music"], "entity_types": ["Director", "Title"]}
{"sentence": "Do you have any documentaries about the making of classic Hollywood movies from the 1950s?", "entity_names": ["documentaries", "classic Hollywood movies from the 1950s"], "entity_types": ["Genre", "Genre"]}
{"sentence": "Can you recommend a movie that showcases the behind-the-scenes work of a famous director like Alfred Hitchcock?", "entity_names": ["behind-the-scenes work", "Alfred Hitchcock"], "entity_types": ["Plot", "Director"]}
{"sentence": "I need to find a movie with the best original song from the 80s, can you help?", "entity_names": ["best original song", "80s"], "entity_types": ["Song", "Year"]}
{"sentence": "I'm in a rush to find a movie with a killer soundtrack, any recommendations?", "entity_names": [], "entity_types": []}
{"sentence": "I'm dying to watch a movie with the most iconic theme music of all time, what do you suggest?", "entity_names": ["most iconic theme music"], "entity_types": ["Song"]}
{"sentence": "Can you tell me the director of the movie Inception?", "entity_names": ["director", "Inception"], "entity_types": ["Director", "Title"]}
{"sentence": "What is the viewers' rating for the film The Shawshank Redemption?", "entity_names": ["viewers' rating", "The Shawshank Redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "Which actor played the lead role in the film The Godfather?", "entity_names": ["lead role", "The Godfather"], "entity_types": ["Character", "Title"]}
{"sentence": "What is the viewers' rating for the movie The Shawshank Redemption directed by Frank Darabont?", "entity_names": ["The Shawshank Redemption", "Frank Darabont"], "entity_types": ["Title", "Director"]}
{"sentence": "I need to watch a movie tonight, but I don't know what's playing. Can you tell me the showtimes for 'Inception'?", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "I'm running late and I don't want to miss the start of the movie. Please show me the nearest theater with tickets available for 'The Lion King'?", "entity_names": ["The Lion King"], "entity_types": ["Title"]}
{"sentence": "I have been looking forward to this movie all week, but I can't find any tickets anywhere. Can you help me find tickets for 'Avengers: Endgame'?", "entity_names": ["Avengers: Endgame"], "entity_types": ["Title"]}
{"sentence": "What's the best romantic comedy film from the 2000s that features a love triangle", "entity_names": ["romantic comedy", "2000s", "love triangle"], "entity_types": ["Genre", "Year", "Plot"]}
{"sentence": "Can you recommend a fantasy movie for kids with dragons and magic spells", "entity_names": ["fantasy", "kids", "dragons and magic spells"], "entity_types": ["Genre", "Viewers' Rating", "Plot"]}
{"sentence": "Who directed the animated film about a brave princess who saves her kingdom from an evil sorcerer", "entity_names": ["animated", "princess", "evil sorcerer"], "entity_types": ["Genre", "Character", "Character"]}
{"sentence": "Can I watch any good action movies on streaming platforms?", "entity_names": ["action movies"], "entity_types": ["Genre"]}
{"sentence": "Is there a classic romantic film available for streaming?", "entity_names": ["classic romantic film"], "entity_types": ["Genre"]}
{"sentence": "Are there any popular movies from the 90s that I can watch online?", "entity_names": ["popular movies", "90s"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "Can you tell me about the making-of process for the latest Mission: Impossible movie?", "entity_names": ["making-of process", "Mission: Impossible"], "entity_types": ["Plot", "Title"]}
{"sentence": "Show me a movie with the behind-the-scenes footage of a famous action sequence.", "entity_names": ["behind-the-scenes footage", "action sequence"], "entity_types": ["Plot", "Plot"]}
{"sentence": "I want to learn more about the special effects in a recent fantasy film.", "entity_names": ["special effects", "fantasy"], "entity_types": ["Plot", "Genre"]}
{"sentence": "Can I get tickets for the new James Bond movie?", "entity_names": ["James Bond"], "entity_types": ["Title"]}
{"sentence": "Are there any theaters playing the new Marvel movie tonight?", "entity_names": ["new Marvel"], "entity_types": ["Genre"]}
{"sentence": "Can I watch the movie Inception on any streaming platform?", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "Is the director's cut of Blade Runner available for streaming anywhere?", "entity_names": ["director's cut", "Blade Runner"], "entity_types": ["Plot", "Title"]}
{"sentence": "Do you really think the new Tarantino movie lives up to the hype? Some people say it's overrated.", "entity_names": ["new", "Tarantino", "overrated"], "entity_types": ["Year", "Director", "Review"]}
{"sentence": "I heard that the latest action film starring Tom Hardy got mixed reviews. What's the story about anyway?", "entity_names": ["action film", "Tom Hardy", "mixed reviews", "story"], "entity_types": ["Genre", "Actor", "Review", "Plot"]}
{"sentence": "what's the deal with that new movie everyone's talking about?", "entity_names": ["new movie", "everyone's talking about"], "entity_types": ["Title", "Review"]}
{"sentence": "who's the actor in that action movie with all the explosions?", "entity_names": ["actor", "action movie", "explosions"], "entity_types": ["Actor", "Genre", "Plot"]}
{"sentence": "is there a funny movie from the 90s that's suitable for kids?", "entity_names": ["funny movie", "90s", "suitable for kids"], "entity_types": ["Review", "Year", "MPAA Rating"]}
{"sentence": "Can you recommend a good sci-fi comedy movie?", "entity_names": ["sci-fi comedy"], "entity_types": ["Genre"]}
{"sentence": "I'm in the mood for a classic romance film. Do you have any recommendations?", "entity_names": ["classic romance"], "entity_types": ["Genre"]}
{"sentence": "I'm looking for a thriller with elements of mystery. Any suggestions?", "entity_names": ["thriller", "mystery"], "entity_types": ["Genre", "Genre"]}
{"sentence": "I'm curious about a movie with a memorable song, maybe something romantic, like 'My Heart Will Go On'. Any recommendations?", "entity_names": ["My Heart Will Go On"], "entity_types": ["Song"]}
{"sentence": "I heard a fantastic movie with an amazing soundtrack and it's set in the 80s. Do you know which one I'm talking about?", "entity_names": ["80s"], "entity_types": ["Year"]}
{"sentence": "I'm trying to find a film with an iconic song that's been featured in many other movies. Any ideas?", "entity_names": ["iconic song"], "entity_types": ["Song"]}
{"sentence": "Can you tell me the highest-rated animated movies of all time?", "entity_names": ["highest-rated", "animated"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "What's the plot of the new Disney princess movie that came out this year?", "entity_names": ["new Disney princess movie"], "entity_types": ["Genre"]}
{"sentence": "I'm looking for a romantic comedy with strong female characters. Can you recommend one?", "entity_names": ["romantic comedy", "strong female characters"], "entity_types": ["Genre", "Character"]}
{"sentence": "Can you just give me a quick summary of the movie 'Inception'?", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "I need to know the plot of 'The Godfather' right now.", "entity_names": ["The Godfather"], "entity_types": ["Title"]}
{"sentence": "I don't have all day, just tell me about the story of 'The Shawshank Redemption'.", "entity_names": ["The Shawshank Redemption"], "entity_types": ["Title"]}
{"sentence": "Can you tell me about the director of the movie 'The Dark Knight' and any behind-the-scenes stories?", "entity_names": ["director", "The Dark Knight", "behind-the-scenes stories"], "entity_types": ["Director", "Title", "Plot"]}
{"sentence": "I'm interested in knowing the song used in the movie 'Grease' during the behind-the-scenes footage. Can you show me a clip?", "entity_names": ["song", "Grease", "behind-the-scenes footage", "clip"], "entity_types": ["Song", "Title", "Plot", "Trailer"]}
{"sentence": "What behind-the-scenes information can you provide about the making of the movie 'Jurassic Park' directed by Steven Spielberg?", "entity_names": ["making of", "Jurassic Park", "Steven Spielberg"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "What is the name of the movie with the iconic song 'My Heart Will Go On'?", "entity_names": ["My Heart Will Go On"], "entity_types": ["Song"]}
{"sentence": "Can you recommend a film with an amazing soundtrack that I won't be able to stop listening to?", "entity_names": ["amazing soundtrack"], "entity_types": ["Review"]}
{"sentence": "I'm looking for a movie that has an unforgettable musical number. Can you help me find one?", "entity_names": ["unforgettable musical number"], "entity_types": ["Review"]}
{"sentence": "Can you recommend a movie featuring Leonardo DiCaprio and directed by Christopher Nolan?", "entity_names": ["Leonardo DiCaprio", "Christopher Nolan"], "entity_types": ["Actor", "Director"]}
{"sentence": "What are some popular action movies from the 90s?", "entity_names": ["action", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "I'm looking for a comedy film starring Emma Stone and Ryan Reynolds, can you help me find one?", "entity_names": ["comedy", "Emma Stone", "Ryan Reynolds"], "entity_types": ["Genre", "Actor", "Actor"]}
{"sentence": "Can you tell me who directed the film Inception?", "entity_names": ["directed", "Inception"], "entity_types": ["Director", "Title"]}
{"sentence": "What is the viewers' rating for The Shawshank Redemption?", "entity_names": ["viewers' rating", "The Shawshank Redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "Show me a movie with Meryl Streep as the lead character.", "entity_names": ["Meryl Streep"], "entity_types": ["Actor"]}
{"sentence": "Can you tell me about the reviews for the movie Inception?", "entity_names": ["reviews", "Inception"], "entity_types": ["Review", "Title"]}
{"sentence": "I'm not sure if the movie The Godfather is as good as they say. Can you provide its viewers' rating?", "entity_names": ["The Godfather", "viewers' rating"], "entity_types": ["Title", "Viewers' Rating"]}
{"sentence": "I don't trust the director of the movie Avatar. What can you tell me about James Cameron's work on it?", "entity_names": ["Avatar", "James Cameron"], "entity_types": ["Title", "Director"]}
{"sentence": "What songs were featured in the musical film The Sound of Music?", "entity_names": ["songs", "musical", "The Sound of Music"], "entity_types": ["Song", "Genre", "Title"]}
{"sentence": "Can you tell me about the actress who played Dorothy in The Wizard of Oz and any interesting behind-the-scenes facts?", "entity_names": ["actress", "Dorothy", "The Wizard of Oz", "behind-the-scenes"], "entity_types": ["Actor", "Character", "Title", "Plot"]}
{"sentence": "Hey, what's the name of that movie with the awesome soundtrack that has a bunch of 80s hits?", "entity_names": ["80s hits"], "entity_types": ["Genre"]}
{"sentence": "Can you recommend a good movie with a killer score, like something with a really epic theme song?", "entity_names": ["killer score", "epic theme song"], "entity_types": ["Review", "Song"]}
{"sentence": "I heard there's a new movie coming out with a killer soundtrack, do you know the name of it?", "entity_names": ["new movie", "killer soundtrack"], "entity_types": ["Title", "Song"]}
{"sentence": "What movie from the 1990s is a must-see action film?", "entity_names": ["1990s", "must-see", "action"], "entity_types": ["Year", "Viewers' Rating", "Genre"]}
{"sentence": "Who directed the science fiction movie with a PG-13 rating that came out last year?", "entity_names": ["science fiction", "PG-13", "last year"], "entity_types": ["Genre", "MPAA Rating", "Year"]}
{"sentence": "What are some highly-rated action movies from the 1990s?", "entity_names": ["highly-rated", "action", "1990s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "I'm looking for a romantic comedy with a catchy soundtrack. Can you recommend one?", "entity_names": ["romantic comedy", "catchy soundtrack"], "entity_types": ["Genre", "Song"]}
{"sentence": "What is the highest-rated musical film of all time?", "entity_names": ["highest-rated", "musical"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "can you give me a summary of the plot for the movie Sleepless in Seattle", "entity_names": ["Sleepless in Seattle"], "entity_types": ["Title"]}
{"sentence": "what is the storyline for the movie Steel Magnolias", "entity_names": ["Steel Magnolias"], "entity_types": ["Title"]}
{"sentence": "please tell me about the plot of The Bridges of Madison County", "entity_names": ["The Bridges of Madison County"], "entity_types": ["Title"]}
{"sentence": "What movie has a catchy song that kids would love?", "entity_names": ["catchy song", "kids"], "entity_types": ["Song", "Viewers' Rating"]}
{"sentence": "Could you recommend a family-friendly movie with a strong female character as the lead?", "entity_names": ["family-friendly", "lead"], "entity_types": ["Viewers' Rating", "Character"]}
{"sentence": "Is the movie 'La La Land' available for streaming anywhere?", "entity_names": ["La La Land"], "entity_types": ["Title"]}
{"sentence": "Can I watch 'The Devil Wears Prada' on any streaming platform?", "entity_names": ["The Devil Wears Prada"], "entity_types": ["Title"]}
{"sentence": "Is 'Inception' available to stream?", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "Can you recommend any action movies with a strong female lead from the 2000s?", "entity_names": ["action", "strong female lead", "2000s"], "entity_types": ["Genre", "Character", "Year"]}
{"sentence": "What's a good coming-of-age film that deals with LGBTQ+ themes?", "entity_names": ["coming-of-age", "LGBTQ+ themes"], "entity_types": ["Genre", "Genre"]}
{"sentence": "I'm looking for a romantic comedy with a diverse cast, preferably released in the last five years.", "entity_names": ["romantic comedy", "diverse cast", "last five years"], "entity_types": ["Genre", "Character", "Year"]}
{"sentence": "Can you tell me about the director of the movie Casablanca and some interesting behind-the-scenes stories?", "entity_names": ["director", "Casablanca"], "entity_types": ["Director", "Title"]}
{"sentence": "What are some highly rated classic musical films from the 1950s that I should watch?", "entity_names": ["highly rated", "musical", "1950s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "I'm interested in learning about the making of the movie The Godfather and the impact it had on the crime genre.", "entity_names": ["making of", "The Godfather", "crime"], "entity_types": ["Plot", "Title", "Genre"]}
{"sentence": "Can I find the movie 'Casablanca' on any streaming platforms?", "entity_names": ["Casablanca"], "entity_types": ["Title"]}
{"sentence": "What are some classic western movies available for streaming?", "entity_names": ["classic western movies"], "entity_types": ["Genre"]}
{"sentence": "Is the director's cut of 'Blade Runner' available on any streaming services?", "entity_names": ["director's cut", "Blade Runner"], "entity_types": ["Plot", "Title"]}
{"sentence": "I need to watch a comedy movie right now, is 'The Hangover' available on any streaming platform?", "entity_names": ["comedy", "The Hangover"], "entity_types": ["Genre", "Title"]}
{"sentence": "I can't find 'Inception' anywhere, is it on any streaming platform?", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "I'm in the mood for a classic, is 'Casablanca' streaming anywhere?", "entity_names": ["classic", "Casablanca"], "entity_types": ["Genre", "Title"]}
{"sentence": "Can you tell me the plot of the movie Sleepless in Seattle from 1993?", "entity_names": ["Sleepless in Seattle", "1993"], "entity_types": ["Title", "Year"]}
{"sentence": "I'm looking for a movie with a romantic plot that is set in the 1950s", "entity_names": ["romantic", "1950s"], "entity_types": ["Genre", "Year"]}
{"sentence": "What film has a plot about a woman who travels back in time to the 19th century?", "entity_names": ["19th century"], "entity_types": ["Year"]}
{"sentence": "Can you provide me with the director and main actors of the movie The Social Network?", "entity_names": ["director", "main actors", "The Social Network"], "entity_types": ["Director", "Actor", "Title"]}
{"sentence": "I am interested in learning about the soundtrack and filming locations of the movie La La Land", "entity_names": ["soundtrack", "filming locations", "La La Land"], "entity_types": ["Song", "Plot", "Title"]}
{"sentence": "What's the buzz on the new thriller film that just came out?", "entity_names": ["thriller film", "just came out"], "entity_types": ["Genre", "Year"]}
{"sentence": "Can you recommend a movie with a really high viewers' rating?", "entity_names": ["really high viewers' rating"], "entity_types": ["Viewers' Rating"]}
{"sentence": "I'm looking for a classic film directed by a renowned filmmaker.", "entity_names": ["classic film", "renowned filmmaker"], "entity_types": ["Genre", "Director"]}
{"sentence": "What movie did Meryl Streep star in with the song 'Dancing Queen'?", "entity_names": ["Meryl Streep", "Dancing Queen"], "entity_types": ["Actor", "Song"]}
{"sentence": "Show me a film with a high viewers' rating that was directed by Christopher Nolan", "entity_names": ["high viewers' rating", "Christopher Nolan"], "entity_types": ["Viewers' Rating", "Director"]}
{"sentence": "Who directed the sci-fi film Interstellar, and what is its viewers' rating?", "entity_names": ["sci-fi", "Interstellar", "viewers' rating"], "entity_types": ["Genre", "Title", "Viewers' Rating"]}
{"sentence": "Can you provide me with the plot of the movie Inception and the name of the actor who played the lead role?", "entity_names": ["plot", "Inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "Show me the trailer for the film Titanic and tell me the name of the main character.", "entity_names": ["trailer", "Titanic", "main character"], "entity_types": ["Trailer", "Title", "Character"]}
{"sentence": "Can you tell me which movie won the most awards in the 1990s?", "entity_names": ["most awards", "1990s"], "entity_types": ["Review", "Year"]}
{"sentence": "I'm looking for a critically acclaimed film with the highest number of nominations for Best Picture.", "entity_names": ["critically acclaimed", "highest number of nominations for Best Picture"], "entity_types": ["Review", "Review"]}
{"sentence": "Is there a movie with Zac Efron and Vanessa Hudgens from the musical genre?", "entity_names": ["Zac Efron", "Vanessa Hudgens", "musical"], "entity_types": ["Actor", "Actor", "Genre"]}
{"sentence": "Can you tell me the names of all the actors in the movie 'Frozen' and show me its trailer?", "entity_names": ["actors", "Frozen", "trailer"], "entity_types": ["Actor", "Title", "Trailer"]}
{"sentence": "Yo, what kind of movie is The Matrix? Like, is it sci-fi or what?", "entity_names": ["The Matrix", "sci-fi"], "entity_types": ["Title", "Genre"]}
{"sentence": "I'm lookin' for a movie that's like, action but also crime, you know what I mean?", "entity_names": ["action", "crime"], "entity_types": ["Genre", "Genre"]}
{"sentence": "What's that movie with Will Ferrell that's both comedy and sports at the same time?", "entity_names": ["Will Ferrell", "comedy", "sports"], "entity_types": ["Actor", "Genre", "Genre"]}
{"sentence": "Can I watch the latest Marvel movie at the theater tonight?", "entity_names": ["Marvel"], "entity_types": ["Genre"]}
{"sentence": "Is there a showing of the new Christopher Nolan film at the cinema near me?", "entity_names": ["Christopher Nolan"], "entity_types": ["Director"]}
{"sentence": "What are the showtimes for the action movie with Tom Cruise in it?", "entity_names": ["action", "Tom Cruise"], "entity_types": ["Genre", "Actor"]}
{"sentence": "What was the last film directed by Quentin Tarantino and featuring Leonardo DiCaprio?", "entity_names": ["Quentin Tarantino", "Leonardo DiCaprio"], "entity_types": ["Director", "Actor"]}
{"sentence": "Can you recommend a sci-fi movie with Sigourney Weaver in the cast?", "entity_names": ["sci-fi", "Sigourney Weaver"], "entity_types": ["Genre", "Actor"]}
{"sentence": "I'm looking for a film with Tom Hanks in a leading role, directed by Steven Spielberg and released in the 1990s", "entity_names": ["Tom Hanks", "Steven Spielberg", "1990s"], "entity_types": ["Actor", "Director", "Year"]}
{"sentence": "What are the most popular action movie trailers of 2021?", "entity_names": ["popular", "action", "trailers", "2021"], "entity_types": ["Viewers' Rating", "Genre", "Trailer", "Year"]}
{"sentence": "Who directed the film with the most intriguing teaser?", "entity_names": ["intriguing", "teaser"], "entity_types": ["Review", "Trailer"]}
{"sentence": "Can you recommend a highly-rated movie from the 1990s?", "entity_names": ["highly-rated", "1990s"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "Who directed the top action movie of all time?", "entity_names": ["top action movie"], "entity_types": ["Review"]}
{"sentence": "Show me trailers for science fiction films with female lead characters.", "entity_names": ["science fiction", "female lead characters"], "entity_types": ["Genre", "Character"]}
{"sentence": "Can you suggest a family-friendly film with a lot of nominations for major awards?", "entity_names": ["family-friendly", "nominations for major awards"], "entity_types": ["Viewers' Rating", "Review"]}
{"sentence": "I need to watch a movie with an intense plot and a twist ending, do you have any recommendations?", "entity_names": ["intense plot", "twist ending"], "entity_types": ["Plot", "Plot"]}
{"sentence": "Show me a trailer for a classic film that was both critically acclaimed and commercially successful.", "entity_names": ["trailer", "classic film", "critically acclaimed"], "entity_types": ["Trailer", "Title", "Review"]}
{"sentence": "Could you please provide me with the trailer for the latest James Bond film?", "entity_names": ["trailer", "James Bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "I would like to see a teaser for the upcoming Marvel superhero movie, if possible.", "entity_names": ["teaser", "Marvel"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "Do you have the trailer for the new Steven Spielberg film?", "entity_names": ["trailer", "Steven Spielberg"], "entity_types": ["Trailer", "Director"]}
{"sentence": "Hey, can you tell me if the movie 'The Shawshank Redemption' won any awards?", "entity_names": ["The Shawshank Redemption"], "entity_types": ["Title"]}
{"sentence": "I'm curious, did 'La La Land' get any nominations for its music?", "entity_names": ["La La Land", "nominations", "music"], "entity_types": ["Title", "Title", "Song"]}
{"sentence": "Do you know if 'Forrest Gump' received any honors for its special effects?", "entity_names": ["Forrest Gump", "special effects"], "entity_types": ["Title", "Genre"]}
{"sentence": "Can you recommend a highly-rated movie with a gripping plot and stunning visuals?", "entity_names": ["highly-rated", "gripping plot", "stunning visuals"], "entity_types": ["Viewers' Rating", "Plot", "Review"]}
{"sentence": "What is the best movie of the year so far, according to the critics?", "entity_names": ["best movie", "year so far", "critics"], "entity_types": ["Review", "Year", "Viewers' Rating"]}
{"sentence": "I'm looking for an action-packed movie with a top-notch performance by the lead actor. Any recommendations?", "entity_names": ["action-packed", "top-notch performance", "lead actor"], "entity_types": ["Genre", "Review", "Actor"]}
{"sentence": "Hey, can you tell me who directed that awesome sci-fi movie with the killer soundtrack?", "entity_names": ["sci-fi", "killer soundtrack"], "entity_types": ["Genre", "Song"]}
{"sentence": "I heard there's a new action movie coming out soon, who's starring in it and what's the plot like?", "entity_names": ["action"], "entity_types": ["Genre"]}
{"sentence": "I need some recommendations for feel-good movies from the 90s, any ideas?", "entity_names": ["feel-good", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "Can you recommend a movie with an iconic soundtrack featuring the song 'Eye of the Tiger'?", "entity_names": ["Eye of the Tiger"], "entity_types": ["Song"]}
{"sentence": "What movie from the 80s has a memorable soundtrack with the song 'Take My Breath Away'?", "entity_names": ["80s", "Take My Breath Away"], "entity_types": ["Year", "Song"]}
{"sentence": "I'm in the mood for a movie with a captivating soundtrack like 'I Will Always Love You.' Any suggestions?", "entity_names": ["I Will Always Love You"], "entity_types": ["Song"]}
{"sentence": "Can you recommend a feel-good movie with a strong female lead and a great soundtrack?", "entity_names": ["feel-good", "strong female lead"], "entity_types": ["Viewers' Rating", "Character"]}
{"sentence": "What are some highly-rated movies from the 80s that are suitable for family viewing?", "entity_names": ["highly-rated", "80s", "family viewing"], "entity_types": ["Viewers' Rating", "Year", "MPAA Rating"]}
{"sentence": "Who directed the top-rated romantic comedy of the past decade?", "entity_names": ["top-rated", "romantic comedy", "past decade"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "What movie from the 90s has the best soundtrack of all time?", "entity_names": ["90s", "best soundtrack of all time"], "entity_types": ["Year", "Review"]}
{"sentence": "Can you recommend a movie with an iconic song from the 80s?", "entity_names": ["iconic song", "80s"], "entity_types": ["Song", "Year"]}
{"sentence": "Do you know any recent movies with a score by a female composer?", "entity_names": ["recent movies"], "entity_types": ["Genre"]}
{"sentence": "I'm in a rush, so tell me who directed the highest-rated action film of last year", "entity_names": ["highest-rated", "action", "last year"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "I need to know right now, which actor starred in the romantic comedy with the catchy theme song from the 90s", "entity_names": ["actor", "romantic comedy", "catchy theme song", "90s"], "entity_types": ["Actor", "Genre", "Song", "Year"]}
{"sentence": "Can you show me the trailer for a heartwarming movie to lift my spirits?", "entity_names": ["trailer", "heartwarming"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "I'm feeling down, can you recommend a movie with a touching plot that will cheer me up?", "entity_names": ["touching plot"], "entity_types": ["Plot"]}
{"sentence": "What film has a beautiful and emotional song in it?", "entity_names": ["beautiful and emotional song"], "entity_types": ["Song"]}
{"sentence": "Hey, can I get a peek at the trailer for the new James Bond movie?", "entity_names": ["trailer", "James Bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "Do you know if there's a trailer out for the upcoming superhero movie?", "entity_names": ["trailer", "upcoming superhero movie"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "What's the latest teaser for the animated film about the magical creatures?", "entity_names": ["teaser", "animated film", "magical creatures"], "entity_types": ["Trailer", "Genre", "Plot"]}
{"sentence": "Can you recommend a family-friendly movie with a high viewers' rating?", "entity_names": ["family-friendly", "high viewers' rating"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "What is the plot of the latest action movie directed by Christopher Nolan?", "entity_names": ["latest action movie", "Christopher Nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "I'm looking for a classic film with Audrey Hepburn as the lead actress, can you suggest one?", "entity_names": ["classic film", "Audrey Hepburn"], "entity_types": ["Genre", "Actor"]}
{"sentence": "Are there any showtimes available for the latest Marvel movie?", "entity_names": ["Marvel"], "entity_types": ["Genre"]}
{"sentence": "Can I buy tickets for the new James Bond film?", "entity_names": ["James Bond"], "entity_types": ["Character"]}
{"sentence": "What time is the next screening for the movie directed by Christopher Nolan?", "entity_names": ["Christopher Nolan"], "entity_types": ["Director"]}
{"sentence": "What are people saying about the new Marvel movie directed by Jon Watts?", "entity_names": ["Marvel movie", "Jon Watts"], "entity_types": ["Title", "Director"]}
{"sentence": "Show me a movie from the 90s with a high viewers' rating.", "entity_names": ["90s", "high viewers' rating"], "entity_types": ["Year", "Viewers' Rating"]}
{"sentence": "Can you recommend a family-friendly animated movie with a catchy soundtrack?", "entity_names": ["family-friendly", "animated", "catchy soundtrack"], "entity_types": ["Genre", "Genre", "Song"]}
{"sentence": "I'm curious about the director behind the scenes of the classic film Casablanca, what can you tell me about the person who made it?", "entity_names": ["director", "Casablanca"], "entity_types": ["Director", "Title"]}
{"sentence": "I'm interested in the making-of details of the movie Psycho, could you provide some insights into the person responsible for its creation?", "entity_names": ["making-of details", "Psycho"], "entity_types": ["Plot", "Title"]}
{"sentence": "I'd like to know more about the creative mind behind the making of The Godfather, what can you tell me about the person who directed it?", "entity_names": ["creative mind", "The Godfather"], "entity_types": ["Director", "Title"]}
{"sentence": "Please provide me with the plot summary of the movie Inception.", "entity_names": ["plot summary", "Inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "Can you give me a synopsis of the film The Shawshank Redemption?", "entity_names": ["synopsis", "The Shawshank Redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "I would like to hear the plot summary of the science fiction movie Blade Runner 2049.", "entity_names": ["plot summary", "science fiction", "Blade Runner 2049"], "entity_types": ["Plot", "Genre", "Title"]}
{"sentence": "Can you tell me who directed the making of the movie 'Inception'?", "entity_names": ["directed", "Inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "What new sci-fi movie has an interesting behind-the-scenes featurette?", "entity_names": ["sci-fi"], "entity_types": ["Genre"]}
{"sentence": "I heard there's a documentary about the making of a classic horror film, can you provide more information?", "entity_names": ["documentary", "classic horror film"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Can you really find me a movie with an iconic soundtrack that is worth listening to?", "entity_names": ["iconic soundtrack"], "entity_types": ["Song"]}
{"sentence": "I'm not sure if there are any good movies out there with a memorable song. Can you prove me wrong?", "entity_names": ["memorable song"], "entity_types": ["Song"]}
{"sentence": "Is there any movie with a soundtrack that truly stands out, or am I just setting myself up for disappointment?", "entity_names": ["soundtrack that truly stands out"], "entity_types": ["Song"]}
{"sentence": "Can you play a sneak peek of the new Spider-Man movie?", "entity_names": ["sneak peek", "Spider-Man"], "entity_types": ["Trailer", "Title"]}
{"sentence": "I'm dying to see a preview of the next James Bond film. Can you show me a bit?", "entity_names": ["preview", "James Bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "I need to get a glimpse of the upcoming Star Wars movie. Do you have the trailer?", "entity_names": ["glimpse", "Star Wars"], "entity_types": ["Trailer", "Title"]}
{"sentence": "Can you provide me with a brief plot synopsis of the movie The Shawshank Redemption?", "entity_names": ["plot synopsis", "The Shawshank Redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "What is the storyline of the film Inception directed by Christopher Nolan?", "entity_names": ["storyline", "Inception", "Christopher Nolan"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "Tell me about the plot of the sci-fi movie Blade Runner 2049 released in 2017.", "entity_names": ["plot", "sci-fi", "Blade Runner 2049", "2017"], "entity_types": ["Plot", "Genre", "Title", "Year"]}
{"sentence": "Can you recommend a movie with a thrilling plot and a touch of mystery?", "entity_names": ["thrilling", "mystery"], "entity_types": ["Genre", "Genre"]}
{"sentence": "I'm looking for a film that has a mix of action and romance, any suggestions?", "entity_names": ["action", "romance"], "entity_types": ["Genre", "Genre"]}
{"sentence": "Do you know of any movies that fall into the fantasy genre, maybe with some elements of adventure?", "entity_names": ["fantasy", "adventure"], "entity_types": ["Genre", "Genre"]}
{"sentence": "Can you tell me about the awards and nominations for the animated movie Frozen?", "entity_names": ["Frozen"], "entity_types": ["Title"]}
{"sentence": "Is the movie Finding Nemo appropriate for kids, and has it won any awards?", "entity_names": ["Finding Nemo", "kids"], "entity_types": ["Title", "Viewers' Rating"]}
{"sentence": "I'm looking for a family-friendly movie that has won multiple awards, can you recommend one?", "entity_names": ["family-friendly"], "entity_types": ["Viewers' Rating"]}
{"sentence": "Can you show me the trailer for the latest Marvel movie?", "entity_names": ["trailer", "Marvel"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "What's an action movie with a great song in it?", "entity_names": ["action", "great song"], "entity_types": ["Genre", "Song"]}
{"sentence": "Who directed the highest-rated horror movie of the year?", "entity_names": ["highest-rated", "horror", "the year"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "Who directed the making-of documentary for the Lord of the Rings trilogy?", "entity_names": ["Lord of the Rings"], "entity_types": ["Title"]}
{"sentence": "Are there any good behind-the-scenes featurettes for the Harry Potter movies?", "entity_names": ["behind-the-scenes featurettes", "Harry Potter"], "entity_types": ["Plot", "Title"]}
{"sentence": "Can you recommend a film about the history and production of Disney animated classics?", "entity_names": ["Disney animated classics"], "entity_types": ["Genre"]}
{"sentence": "Can you tell me if the movie Mean Girls won any awards?", "entity_names": ["Mean Girls", "awards"], "entity_types": ["Title", "Viewers' Rating"]}
{"sentence": "I'm curious if the film To All the Boys I've Loved Before received any nominations?", "entity_names": ["To All the Boys I've Loved Before", "nominations"], "entity_types": ["Title", "Review"]}
{"sentence": "Which movie from the 1990s has the most insightful director commentary?", "entity_names": ["1990s", "insightful", "director commentary"], "entity_types": ["Year", "Review", "Plot"]}
{"sentence": "I'm interested in a film with a compelling featurette about its special effects. Can you recommend one?", "entity_names": ["compelling", "featurette", "special effects"], "entity_types": ["Review", "Plot", "Plot"]}
{"sentence": "Is there a movie that has a really cool adventure plot?", "entity_names": ["adventure plot"], "entity_types": ["Plot"]}
{"sentence": "What's the best horror movie of the 2010s?", "entity_names": ["horror movie", "2010s"], "entity_types": ["Genre", "Year"]}
{"sentence": "Who directed the classic romantic comedy with the song 'You've Got a Friend in Me'?", "entity_names": ["romantic comedy", "You've Got a Friend in Me"], "entity_types": ["Genre", "Song"]}
{"sentence": "Can you recommend a recent action film with a strong female lead?", "entity_names": ["action film", "strong female lead"], "entity_types": ["Genre", "Character"]}
{"sentence": "What movie features the song 'My Heart Will Go On'?", "entity_names": ["My Heart Will Go On"], "entity_types": ["Song"]}
{"sentence": "Can you recommend a film with an amazing soundtrack?", "entity_names": ["amazing soundtrack"], "entity_types": ["Review"]}
{"sentence": "What is the title of the movie with the best music of all time?", "entity_names": ["best music of all time"], "entity_types": ["Review"]}
{"sentence": "What's the deal with that new Tarantino movie, is it any good?", "entity_names": ["Tarantino movie", "any good"], "entity_types": ["Title", "Review"]}
{"sentence": "Hey, have you heard about that horror movie with the killer clown? What's the viewers' rating on that one?", "entity_names": ["horror movie", "killer clown", "viewers' rating"], "entity_types": ["Genre", "Character", "Viewers' Rating"]}
{"sentence": "Can you tell me a cool action movie from the 90s, something like Die Hard or Terminator?", "entity_names": ["action movie", "90s", "Die Hard", "Terminator"], "entity_types": ["Genre", "Year", "Title", "Title"]}
{"sentence": "Can you recommend a highly-rated action movie from the 2000s?", "entity_names": ["highly-rated", "action movie", "2000s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "Who is the director of the top-rated science fiction film of all time?", "entity_names": ["top-rated", "science fiction film", "all time"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "I'm looking for a critically acclaimed drama with Meryl Streep, any suggestions?", "entity_names": ["critically acclaimed", "drama", "Meryl Streep"], "entity_types": ["Review", "Genre", "Actor"]}
{"sentence": "Can you recommend a movie with a great romantic song that came out in the 1980s?", "entity_names": ["romantic song", "1980s"], "entity_types": ["Song", "Year"]}
{"sentence": "I'm looking for a feel-good movie with a catchy soundtrack. Any suggestions?", "entity_names": ["feel-good", "catchy soundtrack"], "entity_types": ["Genre", "Review"]}
{"sentence": "Who composed the music for the classic film Casablanca?", "entity_names": ["composed the music", "Casablanca"], "entity_types": ["Director", "Title"]}
{"sentence": "What's the viewers' rating for the new Quentin Tarantino movie?", "entity_names": ["Quentin Tarantino"], "entity_types": ["Director"]}
{"sentence": "Show me a movie with a high IMDb rating and a plot twist", "entity_names": ["high IMDb rating", "plot twist"], "entity_types": ["Viewers' Rating", "Plot"]}
{"sentence": "Can you recommend a recent action film with a strong female lead and a catchy soundtrack?", "entity_names": ["action film", "strong female lead", "catchy soundtrack"], "entity_types": ["Genre", "Character", "Song"]}
{"sentence": "Can I see the trailer for the new James Bond movie?", "entity_names": ["trailer", "James Bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "Which movie has the most exciting teaser of all time?", "entity_names": ["most exciting"], "entity_types": ["Review"]}
{"sentence": "Are there any must-see films with intense action movie trailers?", "entity_names": ["must-see", "action", "movie trailers"], "entity_types": ["Viewers' Rating", "Genre", "Trailer"]}
{"sentence": "Can you recommend a horror movie with an atmospheric setting and a gripping plot?", "entity_names": ["horror", "atmospheric setting"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Show me a romantic comedy with a heartwarming storyline and great chemistry between the lead actors", "entity_names": ["romantic comedy", "heartwarming storyline"], "entity_types": ["Genre", "Plot"]}
{"sentence": "What is the most iconic science fiction film with a dystopian future setting?", "entity_names": ["science fiction", "dystopian future setting"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Can you recommend a good action movie that came out in the 1990s", "entity_names": ["action movie", "1990s"], "entity_types": ["Genre", "Year"]}
{"sentence": "I'm in the mood for a suspense thriller with a strong female lead, can you suggest one for me", "entity_names": ["suspense thriller", "strong female lead"], "entity_types": ["Genre", "Character"]}
{"sentence": "What are some classic romantic comedies from the 1980s", "entity_names": ["romantic comedies", "1980s"], "entity_types": ["Genre", "Year"]}
{"sentence": "Can you show me a trailer for the latest romantic comedy directed by Nancy Meyers?", "entity_names": ["romantic comedy", "Nancy Meyers"], "entity_types": ["Genre", "Director"]}
{"sentence": "What are the top-rated dramas from the 1980s that I should definitely watch?", "entity_names": ["top-rated", "dramas", "1980s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "I'd like to see a teaser for the new action movie starring Tom Hardy.", "entity_names": ["teaser", "action", "Tom Hardy"], "entity_types": ["Trailer", "Genre", "Actor"]}
{"sentence": "I'm bored, can you show me a teaser for the latest James Bond movie?", "entity_names": ["teaser", "James Bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "I need something to watch, how about a movie with an epic battle scene?", "entity_names": ["epic battle scene"], "entity_types": ["Plot"]}
{"sentence": "I'm feeling uninspired, could you recommend a film directed by Christopher Nolan?", "entity_names": ["Christopher Nolan"], "entity_types": ["Director"]}
{"sentence": "Can I find any showtimes for a movie with a superhero character?", "entity_names": ["superhero character"], "entity_types": ["Character"]}
{"sentence": "Is there a movie playing with a song by Michael Jackson?", "entity_names": ["Michael Jackson"], "entity_types": ["Actor"]}
{"sentence": "Are there any highly-rated movies from the 1980s?", "entity_names": ["highly-rated", "1980s"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "Who directed the action movie starring Matt Damon and Julia Stiles with a PG-13 rating", "entity_names": ["action movie", "Matt Damon", "Julia Stiles", "PG-13"], "entity_types": ["Genre", "Actor", "Actor", "MPAA Rating"]}
{"sentence": "What film features Meryl Streep and is known for its captivating storyline and a high viewers' rating", "entity_names": ["Meryl Streep", "captivating storyline", "high viewers' rating"], "entity_types": ["Actor", "Plot", "Viewers' Rating"]}
{"sentence": "Can you provide me with the name of the director of the science fiction movie released in 2010", "entity_names": ["science fiction movie", "2010"], "entity_types": ["Genre", "Year"]}
{"sentence": "Can I watch Casablanca on any streaming platforms?", "entity_names": ["Casablanca"], "entity_types": ["Title"]}
{"sentence": "Are there any classic musicals available for streaming?", "entity_names": ["classic musicals"], "entity_types": ["Genre"]}
{"sentence": "Which streaming service has the movie starring Audrey Hepburn and directed by Billy Wilder?", "entity_names": ["Audrey Hepburn", "Billy Wilder"], "entity_types": ["Actor", "Director"]}
{"sentence": "Who directed the movie Inception and who were the main actors?", "entity_names": ["directed", "Inception", "main actors"], "entity_types": ["Director", "Title", "Actor"]}
{"sentence": "I need to know the year the film Pulp Fiction was released and the genre of the movie.", "entity_names": ["year", "Pulp Fiction", "genre"], "entity_types": ["Year", "Title", "Genre"]}
{"sentence": "Give me the name of the actor who starred in The Godfather and the viewers' rating for the film now.", "entity_names": ["actor", "The Godfather", "viewers' rating"], "entity_types": ["Actor", "Title", "Viewers' Rating"]}
{"sentence": "who directed the making-of documentary for the movie Titanic", "entity_names": ["directed", "making-of documentary", "Titanic"], "entity_types": ["Director", "Plot", "Title"]}
{"sentence": "can you recommend a behind-the-scenes film about the Marvel Cinematic Universe", "entity_names": ["behind-the-scenes film", "Marvel Cinematic Universe"], "entity_types": ["Plot", "Title"]}
{"sentence": "show me a documentary about the special effects in the Star Wars movies", "entity_names": ["documentary", "special effects", "Star Wars"], "entity_types": ["Plot", "Plot", "Title"]}
{"sentence": "What kind of movie is To All the Boys I've Loved Before?", "entity_names": ["To All the Boys I've Loved Before"], "entity_types": ["Title"]}
{"sentence": "Can you recommend a romantic comedy for me to watch with my friends?", "entity_names": ["romantic comedy"], "entity_types": ["Genre"]}
{"sentence": "I heard about a movie called Crazy Rich Asians, what's it about?", "entity_names": ["Crazy Rich Asians"], "entity_types": ["Title"]}
{"sentence": "Can you provide information on the making-of documentary for the film The Lord of the Rings: The Fellowship of the Ring?", "entity_names": ["making-of documentary", "The Lord of the Rings: The Fellowship of the Ring"], "entity_types": ["Plot", "Title"]}
{"sentence": "I'm interested in learning about the director's commentary for the movie Inception. Where can I find it?", "entity_names": ["director's commentary", "Inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "Are there any special features or bonus content available for the Blu-ray edition of the film Jurassic Park?", "entity_names": ["bonus content", "Jurassic Park"], "entity_types": ["Plot", "Title"]}
{"sentence": "I'm looking for a drama film with Hugh Jackman. Do you have any recommendations?", "entity_names": ["drama", "Hugh Jackman"], "entity_types": ["Genre", "Actor"]}
{"sentence": "What are the top-rated action films from the 90s?", "entity_names": ["top-rated", "action films", "90s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "Can you recommend a movie with a memorable soundtrack?", "entity_names": [], "entity_types": []}
{"sentence": "What's a movie with a great score from the 80s?", "entity_names": ["great score", "80s"], "entity_types": ["Song", "Year"]}
{"sentence": "Show me a film with an iconic theme song.", "entity_names": ["iconic theme song"], "entity_types": ["Song"]}
{"sentence": "I'm excited about watching a movie tonight. Can you tell me if there are any showings for the film 'Inception' at the nearby theaters?", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "I'm really looking forward to seeing a new action movie. What are the showtimes for 'Mad Max: Fury Road' in 3D?", "entity_names": ["Mad Max: Fury Road"], "entity_types": ["Title"]}
{"sentence": "I'm in the mood for a comedy this weekend. Are there any theaters playing 'Bridesmaids'?", "entity_names": ["comedy", "Bridesmaids"], "entity_types": ["Genre", "Title"]}
{"sentence": "Can you recommend a movie with high viewers' rating and a thrilling plot for young adults?", "entity_names": ["high viewers' rating", "thrilling"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "Who directed the highest rated movie for young adults in the past 5 years?", "entity_names": ["highest rated", "young adults", "past 5 years"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "I'm looking for a movie with a strong female lead and positive reviews from the past decade.", "entity_names": ["strong female lead", "positive reviews", "past decade"], "entity_types": ["Character", "Review", "Year"]}
{"sentence": "Can you tell me which movies Tom Hanks starred in during the 1990s?", "entity_names": ["Tom Hanks", "1990s"], "entity_types": ["Actor", "Year"]}
{"sentence": "What is the viewer rating for the movie The Shawshank Redemption?", "entity_names": ["viewer rating", "The Shawshank Redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "What is the name of the actor who plays Iron Man in the Marvel movies?", "entity_names": ["actor", "Iron Man", "Marvel movies"], "entity_types": ["Actor", "Character", "Genre"]}
{"sentence": "Can you tell me the genre of the movie The Matrix?", "entity_names": ["genre", "The Matrix"], "entity_types": ["Genre", "Title"]}
{"sentence": "can you tell me about the highest-rated film in terms of viewers' ratings", "entity_names": ["highest-rated"], "entity_types": ["Viewers' Rating"]}
{"sentence": "What is the plot of the movie Inception", "entity_names": ["plot", "Inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "Can you give me a synopsis of the film The Shawshank Redemption", "entity_names": ["synopsis", "The Shawshank Redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "Tell me about the story of the movie The Godfather", "entity_names": ["story", "The Godfather"], "entity_types": ["Plot", "Title"]}
{"sentence": "Can you tell me the plot of the classic film The Sound of Music?", "entity_names": ["plot", "The Sound of Music"], "entity_types": ["Plot", "Title"]}
{"sentence": "I'm feeling nostalgic for a 90s movie with a compelling storyline, could you recommend one?", "entity_names": ["90s", "compelling storyline"], "entity_types": ["Year", "Plot"]}
{"sentence": "What's the synopsis of the Steven Spielberg-directed adventure movie Jurassic Park?", "entity_names": ["synopsis", "Steven Spielberg", "Jurassic Park"], "entity_types": ["Plot", "Director", "Title"]}
{"sentence": "Can you show me some teaser for a new action movie?", "entity_names": ["teaser", "action movie"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "What's the latest movie trailer available for viewing?", "entity_names": ["latest", "movie trailer"], "entity_types": ["Year", "Trailer"]}
{"sentence": "Do you have any sneak peeks of upcoming mystery movies?", "entity_names": ["sneak peeks", "mystery movies"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "Can you provide information about the director of the movie The Social Network?", "entity_names": ["director", "The Social Network"], "entity_types": ["Director", "Title"]}
{"sentence": "I am looking for a film with a poignant soundtrack. Can you recommend one?", "entity_names": ["poignant soundtrack"], "entity_types": ["Song"]}
{"sentence": "Which movie from the 90s has the most authentic portrayal of a historical event?", "entity_names": ["90s", "historical event"], "entity_types": ["Year", "Plot"]}
{"sentence": "Can you tell me which movie starred both Leonardo DiCaprio and Kate Winslet?", "entity_names": ["Leonardo DiCaprio", "Kate Winslet"], "entity_types": ["Actor", "Actor"]}
{"sentence": "I'm looking for a classic film directed by Alfred Hitchcock, with a suspenseful plot and a high viewers' rating.", "entity_names": ["classic", "Alfred Hitchcock", "suspenseful", "high viewers' rating"], "entity_types": ["Genre", "Director", "Plot", "Viewers' Rating"]}
{"sentence": "Show me a movie featuring Meryl Streep, directed by Steven Spielberg, and released in the 1980s.", "entity_names": ["Meryl Streep", "Steven Spielberg", "1980s"], "entity_types": ["Actor", "Director", "Year"]}
{"sentence": "Can you provide the showtimes for the new James Bond film and let me know if tickets are available?", "entity_names": ["James Bond"], "entity_types": ["Title"]}
{"sentence": "I'm interested in seeing a movie from the thriller genre this weekend. Can you tell me the showtimes for any recent releases?", "entity_names": ["thriller"], "entity_types": ["Genre"]}
{"sentence": "I need to book tickets for a movie directed by Christopher Nolan. What are my options?", "entity_names": ["Christopher Nolan"], "entity_types": ["Director"]}
{"sentence": "I'm looking for a film with a famous actor and a plot full of suspense.", "entity_names": ["famous actor", "plot full of suspense"], "entity_types": ["Actor", "Plot"]}
{"sentence": "Can you recommend a movie from the 90s with a lot of action?", "entity_names": ["90s", "a lot of action"], "entity_types": ["Year", "Genre"]}
{"sentence": "Can you recommend a movie with a beautiful love song in it?", "entity_names": ["beautiful love song"], "entity_types": ["Song"]}
{"sentence": "I'm looking for a film from the 1950s with a classic jazz soundtrack, do you have any suggestions?", "entity_names": ["1950s", "classic jazz"], "entity_types": ["Year", "Genre"]}
{"sentence": "What movie features the song 'Unchained Melody' and was directed by a female filmmaker?", "entity_names": ["Unchained Melody", "female filmmaker"], "entity_types": ["Song", "Director"]}
{"sentence": "On which streaming platform can I watch the movie The Shawshank Redemption directed by Frank Darabont?", "entity_names": ["The Shawshank Redemption", "Frank Darabont"], "entity_types": ["Title", "Director"]}
{"sentence": "Is the movie Inception with Leonardo DiCaprio available for streaming anywhere?", "entity_names": ["Inception", "Leonardo DiCaprio"], "entity_types": ["Title", "Actor"]}
{"sentence": "What is the viewers' rating for the film The Dark Knight released in 2008 on any streaming platform?", "entity_names": ["viewers' rating", "The Dark Knight", "2008"], "entity_types": ["Viewers' Rating", "Title", "Year"]}
{"sentence": "What is the viewers' rating for the movie Inception?", "entity_names": ["viewers' rating", "Inception"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "Can you tell me the plot of the movie The Shawshank Redemption?", "entity_names": ["plot", "The Shawshank Redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "What are the showtimes for the newest superhero movie in theaters right now?", "entity_names": ["newest superhero movie"], "entity_types": ["Genre"]}
{"sentence": "Can I buy tickets for the romantic comedy movie that's getting great reviews?", "entity_names": ["romantic comedy movie", "great reviews"], "entity_types": ["Genre", "Review"]}
{"sentence": "Are there any action movies directed by Christopher Nolan playing this weekend?", "entity_names": ["action movies", "Christopher Nolan", "this weekend"], "entity_types": ["Genre", "Director", "Year"]}
{"sentence": "Hey, when is the next showing for the latest Fast & Furious movie?", "entity_names": ["Fast & Furious"], "entity_types": ["Title"]}
{"sentence": "Do you know if there are any good action movies playing this weekend?", "entity_names": ["action movies"], "entity_types": ["Genre"]}
{"sentence": "Can you find me tickets for the new Spider-Man movie?", "entity_names": ["Spider-Man"], "entity_types": ["Title"]}
{"sentence": "Can you tell me about the behind-the-scenes details of the movie Jaws, directed by Steven Spielberg?", "entity_names": ["behind-the-scenes details", "Jaws", "Steven Spielberg"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "I'm curious about the making-of process of The Godfather. Can you provide some insights?", "entity_names": ["making-of process", "The Godfather"], "entity_types": ["Plot", "Title"]}
{"sentence": "Can you recommend a highly-rated movie directed by Christopher Nolan?", "entity_names": ["highly-rated", "Christopher Nolan"], "entity_types": ["Viewers' Rating", "Director"]}
{"sentence": "What is the plot of the movie Inception?", "entity_names": ["the plot", "Inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "Show me a trailer for any action movie released in the past year.", "entity_names": ["trailer", "action", "past year"], "entity_types": ["Trailer", "Genre", "Year"]}
{"sentence": "What's a highly-rated movie with an intense plot that will keep me on the edge of my seat?", "entity_names": ["highly-rated", "intense plot"], "entity_types": ["Viewers' Rating", "Plot"]}
{"sentence": "Can you recommend a movie with a mind-blowing twist ending that has received great reviews?", "entity_names": ["mind-blowing twist ending", "great reviews"], "entity_types": ["Plot", "Review"]}
{"sentence": "I need a feel-good movie with a heartwarming plot and positive viewers' ratings to lift my spirits.", "entity_names": ["feel-good", "heartwarming plot", "positive viewers' ratings"], "entity_types": ["Genre", "Plot", "Viewers' Rating"]}
{"sentence": "Hey, can you tell me the plot of the new James Bond movie? I'm so excited to hear all about it!", "entity_names": ["plot", "James Bond"], "entity_types": ["Plot", "Character"]}
{"sentence": "What's the storyline of the latest romantic comedy that everyone's raving about? I can't wait to hear the plot!", "entity_names": ["storyline", "romantic comedy"], "entity_types": ["Plot", "Genre"]}
{"sentence": "I'm dying to know the synopsis of the upcoming action-packed superhero film. Could you give me some details about the plot, please?", "entity_names": ["synopsis", "action-packed", "superhero film"], "entity_types": ["Plot", "Genre", "Genre"]}
{"sentence": "Can you give me a brief synopsis of the movie Casablanca?", "entity_names": ["Casablanca"], "entity_types": ["Title"]}
{"sentence": "Who directed the movie Gone with the Wind and what's it about?", "entity_names": ["directed", "Gone with the Wind"], "entity_types": ["Director", "Title"]}
{"sentence": "I'm interested in a movie from the 1950s with a romantic plot - do you have any suggestions?", "entity_names": ["1950s", "romantic"], "entity_types": ["Year", "Genre"]}
{"sentence": "Can I stream the movie 'The Shawshank Redemption' anywhere?", "entity_names": ["The Shawshank Redemption"], "entity_types": ["Title"]}
{"sentence": "Is there a way to watch 'Inception' online?", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "Where can I find 'The Godfather' to stream?", "entity_names": ["The Godfather"], "entity_types": ["Title"]}
{"sentence": "I can't wait to learn more about the director's cut of Blade Runner, when will it be released?", "entity_names": ["director's cut", "Blade Runner", "released"], "entity_types": ["Plot", "Title", "Year"]}
{"sentence": "I'm really eager to watch a film that dives into the special effects of Jurassic Park, do you have any recommendations?", "entity_names": ["special effects", "Jurassic Park"], "entity_types": ["Plot", "Title"]}
{"sentence": "Yo, who starred in that new superhero flick?", "entity_names": ["superhero flick"], "entity_types": ["Genre"]}
{"sentence": "Who directed that crazy action movie with all the explosions?", "entity_names": ["crazy action movie", "explosions"], "entity_types": ["Genre", "Plot"]}
{"sentence": "What's the deal with that rom-com that everyone's talking about? Who's in it?", "entity_names": ["rom-com"], "entity_types": ["Genre"]}
{"sentence": "Show me a film with a diverse cast that received critical acclaim", "entity_names": ["critical acclaim"], "entity_types": ["Review"]}
{"sentence": "What time is the next showing of the movie Dirty Dancing?", "entity_names": ["Dirty Dancing"], "entity_types": ["Title"]}
{"sentence": "Can I still get tickets to see the film Steel Magnolias this weekend?", "entity_names": ["Steel Magnolias"], "entity_types": ["Title"]}
{"sentence": "Is the movie Fried Green Tomatoes playing at any theaters nearby?", "entity_names": ["Fried Green Tomatoes"], "entity_types": ["Title"]}
{"sentence": "Show me a list of films starring Meryl Streep.", "entity_names": ["Meryl Streep"], "entity_types": ["Actor"]}
{"sentence": "What's the viewers' rating for the movie The Shawshank Redemption?", "entity_names": ["viewers' rating", "The Shawshank Redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "Can you tell me which movies featured Leonardo DiCaprio and Kate Winslet as co-stars?", "entity_names": ["Leonardo DiCaprio", "Kate Winslet"], "entity_types": ["Actor", "Actor"]}
{"sentence": "Can you tell me about any acclaimed movies in terms of recognition?", "entity_names": ["acclaimed movies", "recognition"], "entity_types": ["Genre", "Review"]}
{"sentence": "What are some highly-decorated movies recognized in the film industry?", "entity_names": ["highly-decorated movies", "recognized"], "entity_types": ["Title", "Review"]}
{"sentence": "I'm looking for a movie from the 90s that was nominated for multiple Oscars, can you help me find one?", "entity_names": ["90s", "nominated for multiple Oscars"], "entity_types": ["Year", "Review"]}
{"sentence": "What movie features the song 'Can't Help Falling in Love' by Elvis Presley?", "entity_names": ["Can't Help Falling in Love"], "entity_types": ["Song"]}
{"sentence": "I'm in the mood for a movie with a great soundtrack. Can you recommend a film with iconic songs from the 80s?", "entity_names": ["great soundtrack", "iconic songs", "80s"], "entity_types": ["Review", "Song", "Year"]}
{"sentence": "I need a feel-good movie with an uplifting soundtrack. Can you suggest a film with classic hits from the 70s?", "entity_names": ["feel-good", "uplifting soundtrack", "classic hits", "70s"], "entity_types": ["Review", "Plot", "Song", "Year"]}
{"sentence": "What are the showtimes for the latest action movies", "entity_names": ["latest action movies"], "entity_types": ["Genre"]}
{"sentence": "I need to buy tickets for the top-rated comedy film that just came out", "entity_names": ["comedy"], "entity_types": ["Genre"]}
{"sentence": "Can I still get tickets for the thriller movie that everyone is talking about", "entity_names": ["thriller movie"], "entity_types": ["Genre"]}
{"sentence": "Could you provide information on the making-of the movie Inception directed by Christopher Nolan?", "entity_names": ["Inception", "Christopher Nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "I would like to know more about the behind-the-scenes of the film La La Land starring Ryan Gosling and Emma Stone.", "entity_names": ["La La Land", "Ryan Gosling", "Emma Stone"], "entity_types": ["Title", "Actor", "Actor"]}
{"sentence": "Please show me a documentary about the making of the classic film Citizen Kane by Orson Welles.", "entity_names": ["Citizen Kane", "Orson Welles"], "entity_types": ["Title", "Director"]}
{"sentence": "Can you tell me the plot for the movie Black Panther directed by Ryan Coogler?", "entity_names": ["Black Panther", "Ryan Coogler"], "entity_types": ["Title", "Director"]}
{"sentence": "What songs are featured in the film A Star is Born?", "entity_names": ["songs", "A Star is Born"], "entity_types": ["Song", "Title"]}
{"sentence": "I'm looking for a new animated film to watch, can you give me the plot of the movie Luca?", "entity_names": ["animated", "Luca"], "entity_types": ["Genre", "Title"]}
{"sentence": "Can you tell me a movie starring Meryl Streep and directed by Steven Spielberg?", "entity_names": ["Meryl Streep", "Steven Spielberg"], "entity_types": ["Actor", "Director"]}
{"sentence": "What movie features both Leonardo DiCaprio and Kate Winslet as the main characters?", "entity_names": ["Leonardo DiCaprio", "Kate Winslet"], "entity_types": ["Actor", "Actor"]}
{"sentence": "Can you show me the trailer for the movie Inception?", "entity_names": ["trailer", "Inception"], "entity_types": ["Trailer", "Title"]}
{"sentence": "What are some highly-rated movies with strong female leads?", "entity_names": ["highly-rated", "strong female leads"], "entity_types": ["Viewers' Rating", "Character"]}
{"sentence": "Who directed the classic film Casablanca?", "entity_names": ["classic", "Casablanca"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "Hey, who directed that awesome action movie with Tom Hardy and Charlize Theron?", "entity_names": ["action", "Tom Hardy", "Charlize Theron"], "entity_types": ["Genre", "Actor", "Actor"]}
{"sentence": "What's the name of that one movie where Leonardo DiCaprio gets mauled by a bear?", "entity_names": ["Leonardo DiCaprio"], "entity_types": ["Actor"]}
{"sentence": "Can you recommend any good sci-fi movies with Sigourney Weaver?", "entity_names": ["sci-fi", "Sigourney Weaver"], "entity_types": ["Genre", "Actor"]}
{"sentence": "Can you show me the trailer for the film Inception?", "entity_names": ["trailer", "Inception"], "entity_types": ["Trailer", "Title"]}
{"sentence": "What type of movie genre is the trailer for the new Spider-Man movie?", "entity_names": ["trailer", "Spider-Man"], "entity_types": ["Trailer", "Title"]}
{"sentence": "Is there a teaser for the upcoming James Bond movie?", "entity_names": ["teaser", "James Bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "who directed the movie Inception", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "what is the rating for the movie The Shawshank Redemption", "entity_names": ["The Shawshank Redemption"], "entity_types": ["Title"]}
{"sentence": "show me a trailer for the latest movie directed by Christopher Nolan", "entity_names": ["Christopher Nolan"], "entity_types": ["Director"]}
{"sentence": "Are there any streaming platforms that have the movie 'The Shawshank Redemption'?", "entity_names": ["The Shawshank Redemption"], "entity_types": ["Title"]}
{"sentence": "Is there a way to watch 'Forrest Gump' on any streaming services?", "entity_names": ["Forrest Gump"], "entity_types": ["Title"]}
{"sentence": "Can I find 'The Matrix' on any streaming platform?", "entity_names": ["The Matrix"], "entity_types": ["Title"]}
{"sentence": "Can you tell me what awards and nominations the movie Titanic received?", "entity_names": ["Titanic"], "entity_types": ["Title"]}
{"sentence": "Which film directed by Steven Spielberg won the most awards in the 1990s?", "entity_names": ["Steven Spielberg", "1990s"], "entity_types": ["Director", "Year"]}
{"sentence": "Can you play a movie with the song 'My Heart Will Go On'?", "entity_names": ["My Heart Will Go On"], "entity_types": ["Song"]}
{"sentence": "I'm in the mood for a movie with a great soundtrack. Can you recommend one?", "entity_names": ["great soundtrack"], "entity_types": ["Review"]}
{"sentence": "Is there a film with a memorable original song that I can watch?", "entity_names": ["memorable original song"], "entity_types": ["Song"]}
{"sentence": "Is the movie The Shawshank Redemption really as good as people say it is", "entity_names": ["The Shawshank Redemption", "good as people say"], "entity_types": ["Title", "Review"]}
{"sentence": "I'm not sure if I believe the hype, but can you tell me about the storyline for the movie The Godfather", "entity_names": ["storyline", "The Godfather"], "entity_types": ["Plot", "Title"]}
{"sentence": "Can you recommend a high-rated action movie from the 1990s?", "entity_names": ["high-rated", "action movie", "1990s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "What is the viewer rating for the latest James Cameron film?", "entity_names": ["viewer rating", "latest", "James Cameron"], "entity_types": ["Viewers' Rating", "Year", "Director"]}
{"sentence": "Tell me about the sci-fi movie Interstellar directed by Christopher Nolan.", "entity_names": ["sci-fi movie", "Interstellar", "Christopher Nolan"], "entity_types": ["Genre", "Title", "Director"]}
{"sentence": "Can you provide me with the director of the new Disney movie about mermaids?", "entity_names": ["Disney", "mermaids"], "entity_types": ["Genre", "Plot"]}
{"sentence": "I want to watch a movie from the 1980s that shows how they made special effects for space adventure films", "entity_names": ["1980s", "special effects", "space adventure"], "entity_types": ["Year", "Plot", "Genre"]}
{"sentence": "I'm looking for a film where I can see how they created the magical costumes and props for a fantasy world", "entity_names": ["fantasy"], "entity_types": ["Genre"]}
{"sentence": "Can you recommend a movie with a compelling plot and a strong female lead?", "entity_names": ["compelling plot", "strong female lead"], "entity_types": ["Plot", "Character"]}
{"sentence": "What movie from the 1990s has the highest viewers' rating?", "entity_names": ["1990s", "highest viewers' rating"], "entity_types": ["Year", "Viewers' Rating"]}
{"sentence": "Who directed the romantic comedy film that came out in 2005 and received a PG-13 rating?", "entity_names": ["romantic comedy", "2005", "PG-13"], "entity_types": ["Genre", "Year", "MPAA Rating"]}
{"sentence": "Show me a movie with Tom Hanks as the lead actor.", "entity_names": ["Tom Hanks"], "entity_types": ["Actor"]}
{"sentence": "Who stars in that new action movie with the superheroes?", "entity_names": ["action movie", "superheroes"], "entity_types": ["Genre", "Character"]}
{"sentence": "What's the name of the director for the new horror flick with the haunted house?", "entity_names": ["horror flick", "haunted house"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Can you tell me the year when that comedy with the talking animals came out?", "entity_names": ["comedy", "talking animals"], "entity_types": ["Genre", "Plot"]}
{"sentence": "I'm looking for showtimes and tickets for the latest Marvel movie", "entity_names": ["Marvel"], "entity_types": ["Title"]}
{"sentence": "Can I see a list of theaters showing the new Christopher Nolan film?", "entity_names": ["Christopher Nolan"], "entity_types": ["Director"]}
{"sentence": "I want to buy tickets for a well-rated horror movie that came out last year", "entity_names": ["well-rated horror movie", "last year"], "entity_types": ["Genre", "Year"]}
{"sentence": "Who directed the sci-fi thriller film titled Interstellar and what technologies were used in its visual effects?", "entity_names": ["directed", "sci-fi thriller", "Interstellar", "visual effects"], "entity_types": ["Director", "Genre", "Title", "Plot"]}
{"sentence": "Can you recommend a documentary about the production of the film Inception and its impact on the science fiction genre?", "entity_names": ["documentary", "production", "Inception", "science fiction"], "entity_types": ["Genre", "Plot", "Title", "Genre"]}
{"sentence": "I'm interested in learning about the cinematography techniques used in the classic movie The Godfather, could you provide some insights?", "entity_names": ["cinematography techniques", "classic", "The Godfather"], "entity_types": ["Plot", "Genre", "Title"]}
{"sentence": "Could you provide me with the soundtrack of the movie Inception directed by Christopher Nolan?", "entity_names": ["Soundtrack", "Inception", "Christopher Nolan"], "entity_types": ["Song", "Title", "Director"]}
{"sentence": "What is the viewers' rating for the movie La La Land, known for its iconic song City of Stars?", "entity_names": ["viewers' rating", "La La Land", "City of Stars"], "entity_types": ["Viewers' Rating", "Title", "Song"]}
{"sentence": "I would like to know the year of release for the movie A Star is Born featuring the song Shallow by Lady Gaga and Bradley Cooper.", "entity_names": ["year of release", "A Star is Born", "Shallow", "Lady Gaga", "Bradley Cooper"], "entity_types": ["Year", "Title", "Song", "Actor", "Actor"]}
{"sentence": "Who directed the action film with a high viewers' rating and Sharon Stone as the lead actress?", "entity_names": ["action film", "high viewers' rating", "Sharon Stone"], "entity_types": ["Genre", "Viewers' Rating", "Actor"]}
{"sentence": "Can you recommend a comedy film from the 90s with Jim Carrey and Cameron Diaz in the cast?", "entity_names": ["comedy film", "90s", "Jim Carrey", "Cameron Diaz"], "entity_types": ["Genre", "Year", "Actor", "Actor"]}
{"sentence": "Can you recommend a classic western movie with lots of action?", "entity_names": ["classic western", "lots of action"], "entity_types": ["Genre", "Plot"]}
{"sentence": "What is the best romantic comedy film from the 1950s?", "entity_names": ["romantic comedy", "1950s"], "entity_types": ["Genre", "Year"]}
{"sentence": "Are there any family-friendly animated movies with talking animals?", "entity_names": ["family-friendly", "animated", "talking animals"], "entity_types": ["Viewers' Rating", "Genre", "Plot"]}
{"sentence": "Can you provide a review for the film The Shawshank Redemption?", "entity_names": ["review", "The Shawshank Redemption"], "entity_types": ["Review", "Title"]}
{"sentence": "Show me the trailer for the latest Quentin Tarantino movie.", "entity_names": ["trailer", "latest Quentin Tarantino movie"], "entity_types": ["Trailer", "Title"]}
{"sentence": "Can I find tickets for the new James Bond movie?", "entity_names": ["James Bond"], "entity_types": ["Title"]}
{"sentence": "What are the showtimes for the classic musical movie Singin' in the Rain?", "entity_names": ["classic musical", "Singin' in the Rain"], "entity_types": ["Genre", "Title"]}
{"sentence": "Is there a movie theater near me showing the award-winning film Casablanca?", "entity_names": ["award-winning", "Casablanca"], "entity_types": ["Review", "Title"]}
{"sentence": "What are some good action movies from the 1980s?", "entity_names": ["action movies", "1980s"], "entity_types": ["Genre", "Year"]}
{"sentence": "Can you recommend a classic western film from the 1960s?", "entity_names": ["classic western film", "1960s"], "entity_types": ["Genre", "Year"]}
{"sentence": "I'm in the mood for a suspense thriller. Any recommendations?", "entity_names": ["suspense thriller"], "entity_types": ["Genre"]}
{"sentence": "Can you recommend a good movie with a high viewers' rating?", "entity_names": ["good"], "entity_types": ["Viewers' Rating"]}
{"sentence": "Who directed the highest-rated movie of 2021?", "entity_names": ["highest-rated", "2021"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "What is the plot of the latest action movie with a female lead?", "entity_names": ["latest", "action", "female lead"], "entity_types": ["Year", "Genre", "Character"]}
{"sentence": "Can you tell me the director of the movie The Shawshank Redemption?", "entity_names": ["director", "The Shawshank Redemption"], "entity_types": ["Director", "Title"]}
{"sentence": "What year did Clint Eastwood star in a western film?", "entity_names": ["Clint Eastwood", "western"], "entity_types": ["Actor", "Genre"]}
{"sentence": "I'm looking for a comedy movie with Steve Martin, can you recommend one?", "entity_names": ["comedy", "Steve Martin"], "entity_types": ["Genre", "Actor"]}
{"sentence": "Can you recommend a good action film from the 1980s featuring Arnold Schwarzenegger?", "entity_names": ["action", "1980s", "Arnold Schwarzenegger"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "I'm a big fan of Quentin Tarantino's work. Can you tell me the plot of his latest movie and if there's a trailer available?", "entity_names": ["Quentin Tarantino", "latest movie", "trailer"], "entity_types": ["Director", "Title", "Trailer"]}
{"sentence": "Can you tell me about the director of that one movie with lots of action scenes?", "entity_names": ["action scenes"], "entity_types": ["Plot"]}
{"sentence": "I'm looking for a film that has a famous actor and a great soundtrack. Do you have any suggestions?", "entity_names": ["famous actor", "great soundtrack"], "entity_types": ["Actor", "Song"]}
{"sentence": "What's the deal with that classic movie everyone talks about? Who directed it?", "entity_names": ["classic movie"], "entity_types": ["Title"]}
{"sentence": "Could you provide me with a brief synopsis of the movie Inception directed by Christopher Nolan?", "entity_names": ["Inception", "Christopher Nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "Can you tell me the general plot of the film The Shawshank Redemption?", "entity_names": ["plot", "The Shawshank Redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "I am interested in learning more about the film The Matrix. Could you please give me a synopsis?", "entity_names": ["The Matrix", "synopsis"], "entity_types": ["Title", "Plot"]}
{"sentence": "I'm interested in a movie with behind-the-scenes info about war strategies. What do you recommend?", "entity_names": ["war strategies"], "entity_types": ["Plot"]}
{"sentence": "Show me a film directed by Kathryn Bigelow that reveals the backstory of its main character.", "entity_names": ["Kathryn Bigelow", "backstory", "main character"], "entity_types": ["Director", "Plot", "Character"]}
{"sentence": "Can you provide a brief plot summary of the movie Inception directed by Christopher Nolan?", "entity_names": ["Plot summary", "Inception", "Christopher Nolan"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "What is the synopsis of the film Titanic which was released in 1997?", "entity_names": ["Synopsis", "Titanic", "1997"], "entity_types": ["Plot", "Title", "Year"]}
{"sentence": "Could you give me an overview of the storyline for the movie The Shawshank Redemption?", "entity_names": ["Storyline", "The Shawshank Redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "I'm looking for a comedy movie from the 1980s with a high viewers' rating. Any suggestions?", "entity_names": ["comedy", "1980s", "high viewers' rating"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "Hey, what's a really good movie to watch this weekend?", "entity_names": [], "entity_types": []}
{"sentence": "Can you find me a movie with a great soundtrack playing nearby?", "entity_names": [], "entity_types": []}
{"sentence": "I'd like to know more about the actors in the film The Shawshank Redemption", "entity_names": ["actors", "The Shawshank Redemption"], "entity_types": ["Actor", "Title"]}
{"sentence": "Show me a preview of the new Marvel movie directed by Taika Waititi", "entity_names": ["preview", "Marvel movie", "Taika Waititi"], "entity_types": ["Trailer", "Genre", "Director"]}
{"sentence": "Can you tell me who directed the movie Top Gun?", "entity_names": ["directed", "Top Gun"], "entity_types": ["Director", "Title"]}
{"sentence": "Show me a film starring Humphrey Bogart that is set in the 1940s", "entity_names": ["Humphrey Bogart", "1940s"], "entity_types": ["Actor", "Year"]}
{"sentence": "What is the viewers' rating for the movie Shawshank Redemption?", "entity_names": ["viewers' rating", "Shawshank Redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "What is the genre of the film The Godfather?", "entity_names": ["The Godfather"], "entity_types": ["Title"]}
{"sentence": "Can you tell me which movies Tom Hanks starred in?", "entity_names": ["Tom Hanks"], "entity_types": ["Actor"]}
{"sentence": "can I see a trailer for the movie Inception", "entity_names": ["trailer", "Inception"], "entity_types": ["Trailer", "Title"]}
{"sentence": "What is the viewers' rating for the movie The Shawshank Redemption", "entity_names": ["viewers' rating", "The Shawshank Redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "Who directed the 2017 movie Dunkirk", "entity_names": ["directed", "2017", "Dunkirk"], "entity_types": ["Director", "Year", "Title"]}
{"sentence": "Who directed the making-of documentary for the movie Inception", "entity_names": ["directed", "Inception"], "entity_types": ["Director", "Title"]}
{"sentence": "What are some popular behind-the-scenes features for fantasy movies from the 2010s", "entity_names": ["fantasy movies", "2010s"], "entity_types": ["Genre", "Year"]}
{"sentence": "Can you recommend a movie with a director's commentary by a non-binary filmmaker", "entity_names": ["director's commentary"], "entity_types": ["Plot"]}
{"sentence": "What are some good horror movies with a supernatural plot?", "entity_names": ["horror", "supernatural"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Can you recommend any action-packed spy movies from the 2000s?", "entity_names": ["action-packed", "spy", "2000s"], "entity_types": ["Genre", "Plot", "Year"]}
{"sentence": "Who directed the classic comedy film about a dysfunctional family?", "entity_names": ["classic comedy", "dysfunctional family"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Can you recommend a classic suspense movie from the 1950s directed by Alfred Hitchcock?", "entity_names": ["classic suspense", "1950s", "Alfred Hitchcock"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "I'm in the mood for a light-hearted romantic comedy with a happy ending. Do you have any suggestions?", "entity_names": ["light-hearted romantic comedy", "happy ending"], "entity_types": ["Genre", "Plot"]}
{"sentence": "I'm interested in exploring some independent drama films that have received critical acclaim. Any recommendations?", "entity_names": ["independent drama films", "critical acclaim"], "entity_types": ["Genre", "Review"]}
{"sentence": "What are the different subgenres of action movies?", "entity_names": ["action movies"], "entity_types": ["Genre"]}
{"sentence": "Can you recommend a classic romance movie from the 1950s?", "entity_names": ["romance movie", "1950s"], "entity_types": ["Genre", "Year"]}
{"sentence": "Who directed the popular science fiction film from the 1970s?", "entity_names": ["science fiction film", "1970s"], "entity_types": ["Genre", "Year"]}
{"sentence": "I heard there's a popular movie with a great soundtrack, is it available to stream anywhere?", "entity_names": ["popular movie", "great soundtrack"], "entity_types": ["Title", "Song"]}
{"sentence": "I'm interested in a classic film from the 1950s, are there any well-rated options to watch online?", "entity_names": ["classic film", "1950s", "well-rated options", "watch online"], "entity_types": ["Genre", "Year", "Viewers' Rating", "Review"]}
{"sentence": "I'm looking for a movie with a compelling plot and strong performances, is it possible to find such a film on a streaming platform?", "entity_names": ["compelling plot", "strong performances", "streaming platform"], "entity_types": ["Plot", "Review", "Genre"]}
{"sentence": "Which action movie from the 2000s has the best special effects and stunts, and who was the stunt coordinator for that film?", "entity_names": ["action movie", "2000s", "special effects", "stunts"], "entity_types": ["Genre", "Year", "Plot", "Plot"]}
{"sentence": "What is the most intense horror movie to watch with friends and what was the inspiration behind the set design and overall atmosphere?", "entity_names": ["horror movie", "friends", "inspiration", "set design", "atmosphere"], "entity_types": ["Genre", "Viewers' Rating", "Plot", "Plot", "Plot"]}
{"sentence": "I'm bored, can you show me a trailer for the latest action movie?", "entity_names": ["trailer", "action movie"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "I have nothing to do, can you recommend a movie with a catchy theme song?", "entity_names": ["catchy theme song"], "entity_types": ["Song"]}
{"sentence": "I need something to entertain me, show me a movie with a mind-bending plot twist.", "entity_names": ["mind-bending plot twist"], "entity_types": ["Plot"]}
{"sentence": "Hey, who's the director of the new action movie starring Tom Hardy?", "entity_names": ["action movie", "Tom Hardy"], "entity_types": ["Genre", "Actor"]}
{"sentence": "Show me a movie with Keanu Reeves and Sandra Bullock together. It has to be a rom-com or a drama.", "entity_names": ["Keanu Reeves", "Sandra Bullock", "rom-com", "drama"], "entity_types": ["Actor", "Actor", "Genre", "Genre"]}
{"sentence": "What's that movie where Leonardo DiCaprio plays a con artist in the 90s?", "entity_names": ["Leonardo DiCaprio", "con artist", "90s"], "entity_types": ["Actor", "Character", "Year"]}
{"sentence": "Can you show me a movie trailer for the latest horror film directed by Jordan Peele", "entity_names": ["horror film", "Jordan Peele"], "entity_types": ["Genre", "Director"]}
{"sentence": "What movie from the 90s has the best soundtrack", "entity_names": ["90s", "best soundtrack"], "entity_types": ["Year", "Review"]}
{"sentence": "Can you tell me the leading actor in The Shawshank Redemption?", "entity_names": ["The Shawshank Redemption"], "entity_types": ["Title"]}
{"sentence": "What is the genre of the movie The Matrix?", "entity_names": ["The Matrix"], "entity_types": ["Title"]}
{"sentence": "who directed the making of the movie avatar", "entity_names": ["directed", "making of", "avatar"], "entity_types": ["Director", "Plot", "Title"]}
{"sentence": "can you recommend a film with a behind-the-scenes look at the special effects", "entity_names": ["behind-the-scenes", "special effects"], "entity_types": ["Plot", "Plot"]}
{"sentence": "show me a documentary about the production of the lord of the rings trilogy", "entity_names": ["production", "lord of the rings", "trilogy"], "entity_types": ["Plot", "Title", "Plot"]}
{"sentence": "I'm looking for a romantic comedy from the 2000s with a diverse cast and strong LGBTQ+ representation. Any suggestions?", "entity_names": ["romantic comedy", "2000s", "diverse cast", "LGBTQ+ representation"], "entity_types": ["Genre", "Year", "Character", "Plot"]}
{"sentence": "Who directed the making-of documentary for the Titanic movie", "entity_names": ["directed", "making-of documentary", "Titanic"], "entity_types": ["Director", "Plot", "Title"]}
{"sentence": "Can you recommend a movie with a behind-the-scenes look at the special effects used in action films", "entity_names": ["action films"], "entity_types": ["Genre"]}
{"sentence": "Is there a documentary about the history of animation films and the filmmakers behind them", "entity_names": ["documentary", "history of animation films", "filmmakers"], "entity_types": ["Genre", "Plot", "Director"]}
{"sentence": "What is the name of the movie where the song 'My Heart Will Go On' is featured?", "entity_names": ["My Heart Will Go On"], "entity_types": ["Song"]}
{"sentence": "Can you recommend a movie with a great soundtrack from the 80s?", "entity_names": ["80s"], "entity_types": ["Year"]}
{"sentence": "I'm looking for a film with a famous actor that has a jazz music theme, can you suggest one?", "entity_names": ["jazz music"], "entity_types": ["Genre"]}
{"sentence": "can you tell me about a movie with a really interesting storyline", "entity_names": ["really interesting", "storyline"], "entity_types": ["Viewers' Rating", "Plot"]}
{"sentence": "is there a film with a surprising twist ending", "entity_names": ["surprising twist ending"], "entity_types": ["Plot"]}
{"sentence": "I'm in a hurry, can you tell me the genre of the movie Forest Gump?", "entity_names": ["genre", "Forest Gump"], "entity_types": ["Genre", "Title"]}
{"sentence": "I need to know the subgenre of the film The Matrix right now", "entity_names": ["subgenre", "The Matrix"], "entity_types": ["Genre", "Title"]}
{"sentence": "What's the genre of the classic movie Casablanca? Hurry up!", "entity_names": ["genre", "Casablanca"], "entity_types": ["Genre", "Title"]}
{"sentence": "Can you recommend a movie with an amazing soundtrack that features the song 'Bohemian Rhapsody'?", "entity_names": ["Bohemian Rhapsody"], "entity_types": ["Song"]}
{"sentence": "I'm looking for a movie from the 80s with a fantastic soundtrack that has a high viewers' rating.", "entity_names": ["80s", "high viewers' rating"], "entity_types": ["Year", "Viewers' Rating"]}
{"sentence": "Who directed the film with the best original song in the past decade?", "entity_names": ["best original song", "past decade"], "entity_types": ["Song", "Year"]}
{"sentence": "What is the viewers' rating for the movie 'Inception' directed by Christopher Nolan?", "entity_names": ["viewers' rating", "Inception", "Christopher Nolan"], "entity_types": ["Viewers' Rating", "Title", "Director"]}
{"sentence": "Can you provide the plot summary for the film 'The Shawshank Redemption' released in 1994?", "entity_names": ["plot summary", "The Shawshank Redemption", "1994"], "entity_types": ["Plot", "Title", "Year"]}
{"sentence": "I am looking for a thriller movie directed by David Fincher, could you suggest a title?", "entity_names": ["thriller", "David Fincher"], "entity_types": ["Genre", "Director"]}
{"sentence": "What movie has the best soundtrack of all time?", "entity_names": ["best"], "entity_types": ["Review"]}
{"sentence": "Can you recommend a movie with iconic music from the 80s?", "entity_names": ["iconic music", "80s"], "entity_types": ["Song", "Year"]}
{"sentence": "Which movie features the song 'My Heart Will Go On' by Celine Dion?", "entity_names": ["My Heart Will Go On", "Celine Dion"], "entity_types": ["Song", "Actor"]}
{"sentence": "Can you show me a trailer for a movie about love and loss, something that will make me cry?", "entity_names": ["love and loss"], "entity_types": ["Plot"]}
{"sentence": "I need to watch something that will tug at my heartstrings. Show me a movie trailer that's emotional and touching.", "entity_names": ["emotional and touching"], "entity_types": ["Review"]}
{"sentence": "I'm feeling down and could use something to uplift my spirits. Can you play a teaser for a movie with a bittersweet storyline?", "entity_names": ["bittersweet"], "entity_types": ["Plot"]}
{"sentence": "What are some highly-rated romantic movies from the 1950s?", "entity_names": ["highly-rated", "romantic", "1950s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "Can you recommend a comedy film directed by Nancy Meyers?", "entity_names": ["comedy", "Nancy Meyers"], "entity_types": ["Genre", "Director"]}
{"sentence": "Show me the trailer for the latest Meryl Streep movie", "entity_names": ["trailer", "latest", "Meryl Streep"], "entity_types": ["Trailer", "Year", "Actor"]}
{"sentence": "Can you recommend a highly-rated thriller directed by David Fincher?", "entity_names": ["highly-rated", "thriller", "David Fincher"], "entity_types": ["Viewers' Rating", "Genre", "Director"]}
{"sentence": "Could you provide me with a review of the latest Quentin Tarantino movie?", "entity_names": ["review", "Quentin Tarantino"], "entity_types": ["Review", "Director"]}
{"sentence": "Show me a trailer for a top-rated animated film from the 2010s.", "entity_names": ["trailer", "top-rated", "animated", "2010s"], "entity_types": ["Trailer", "Viewers' Rating", "Genre", "Year"]}
{"sentence": "Can I buy tickets for the new James Bond movie today?", "entity_names": ["James Bond", "today"], "entity_types": ["Character", "Year"]}
{"sentence": "What are the showtimes for the latest Tarantino film?", "entity_names": ["showtimes", "Tarantino"], "entity_types": ["Review", "Director"]}
{"sentence": "I'm looking for a good action movie to watch this weekend. Any recommendations?", "entity_names": ["action movie", "weekend"], "entity_types": ["Genre", "Year"]}
{"sentence": "What are the showtimes for the latest Marvel movie?", "entity_names": ["Marvel movie"], "entity_types": ["Genre"]}
{"sentence": "Can I buy tickets for the new horror film directed by Jordan Peele?", "entity_names": ["horror film", "Jordan Peele"], "entity_types": ["Genre", "Director"]}
{"sentence": "Is there a romantic comedy playing at the theater this weekend?", "entity_names": ["romantic comedy"], "entity_types": ["Genre"]}
{"sentence": "Yo, where can I peep the trailer for that new superhero flick with the kickass special effects?", "entity_names": ["trailer", "superhero flick"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "Hey, what's the deal with that movie everyone's gossiping about? Is it worth checking out?", "entity_names": ["movie", "everyone's gossiping about"], "entity_types": ["Title", "Viewers' Rating"]}
{"sentence": "Yo, who's the mastermind behind that dope action movie from last year with the insane stunts?", "entity_names": ["mastermind", "action movie", "last year", "insane stunts"], "entity_types": ["Director", "Genre", "Year", "Plot"]}
{"sentence": "I'm curious about the director behind the scenes of that action-packed film with the intense fight scenes. Can you tell me who directed it?", "entity_names": ["action-packed", "intense fight scenes"], "entity_types": ["Genre", "Plot"]}
{"sentence": "I heard there was a must-see movie from the 90s that had a really interesting making-of story. Do you know which one I'm talking about?", "entity_names": ["must-see", "90s", "making-of story"], "entity_types": ["Viewers' Rating", "Year", "Plot"]}
{"sentence": "I'm looking for a film with a legendary actor known for their dedication to perfecting their roles, like in those behind-the-scenes documentaries. Can you recommend one?", "entity_names": ["legendary actor", "dedication to perfecting their roles"], "entity_types": ["Actor", "Actor"]}
{"sentence": "Are there any good movies playing at the theater right now?", "entity_names": [], "entity_types": []}
{"sentence": "What's the latest movie that's out in theaters?", "entity_names": [], "entity_types": []}
{"sentence": "Can you recommend a movie to watch this weekend?", "entity_names": [], "entity_types": []}
{"sentence": "Can I stream any movies directed by Christopher Nolan?", "entity_names": ["Christopher Nolan"], "entity_types": ["Director"]}
{"sentence": "Are there any thriller movies from the 90s that I can watch?", "entity_names": ["thriller", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "Which film has been highly praised by critics and viewers alike?", "entity_names": ["highly praised"], "entity_types": ["Review"]}
{"sentence": "Can you recommend a recent movie with a stellar cast and a compelling plot?", "entity_names": ["stellar cast", "compelling plot"], "entity_types": ["Actor", "Plot"]}
{"sentence": "I'm interested in a movie that is known for its incredible soundtrack, any suggestions?", "entity_names": ["incredible soundtrack"], "entity_types": ["Song"]}
{"sentence": "Are you sure the movie 'The Lion King' has the best soundtrack of all time?", "entity_names": ["The Lion King", "best soundtrack of all time"], "entity_types": ["Title", "Review"]}
{"sentence": "Can you really find the song 'I Will Always Love You' in the movie 'The Bodyguard'?", "entity_names": ["I Will Always Love You", "The Bodyguard"], "entity_types": ["Song", "Title"]}
{"sentence": "Do you really think the movie 'A Star is Born' has a soundtrack worth listening to?", "entity_names": ["A Star is Born", "soundtrack worth listening to"], "entity_types": ["Title", "Review"]}
{"sentence": "Can you recommend a movie with a great soundtrack featuring love songs from the 80s?", "entity_names": ["love songs from the 80s"], "entity_types": ["Genre"]}
{"sentence": "I'm looking for a movie with a beautiful original score composed by a female artist. Any recommendations?", "entity_names": ["original score", "female artist"], "entity_types": ["Song", "Actor"]}
{"sentence": "Could you suggest a romantic film with a memorable theme song that became popular in the 90s?", "entity_names": ["romantic film", "theme song", "popular in the 90s"], "entity_types": ["Genre", "Song", "Year"]}
{"sentence": "Are there any streaming platforms where I can watch the movie Titanic?", "entity_names": ["Titanic"], "entity_types": ["Title"]}
{"sentence": "Can I find the movie Inception available for streaming anywhere?", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "Can I watch the trailer for the new James Bond movie No Time to Die?", "entity_names": ["trailer", "James Bond", "No Time to Die"], "entity_types": ["Trailer", "Character", "Title"]}
{"sentence": "What is the viewers' rating for the movie Parasite directed by Bong Joon-ho?", "entity_names": ["viewers' rating", "Parasite", "Bong Joon-ho"], "entity_types": ["Viewers' Rating", "Title", "Director"]}
{"sentence": "Is there a comedy movie from the 80s directed by John Hughes?", "entity_names": ["comedy", "80s", "John Hughes"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "hey, can you play the trailer for the new Fast & Furious movie?", "entity_names": ["trailer", "Fast & Furious"], "entity_types": ["Trailer", "Title"]}
{"sentence": "I need to see a sneak peek of the upcoming horror movie with Jamie Lee Curtis", "entity_names": ["sneak peek", "horror", "Jamie Lee Curtis"], "entity_types": ["Trailer", "Genre", "Actor"]}
{"sentence": "yo, show me a preview of that sci-fi flick directed by Steven Spielberg", "entity_names": ["preview", "sci-fi", "Steven Spielberg"], "entity_types": ["Trailer", "Genre", "Director"]}
{"sentence": "Can you show me the trailer for the latest romantic comedy film?", "entity_names": ["trailer", "romantic comedy"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "What movie has a heartwarming song that I can listen to?", "entity_names": ["heartwarming", "song"], "entity_types": ["Review", "Song"]}
{"sentence": "Can you tell me about the awards and nominations received by the movie The Shawshank Redemption", "entity_names": ["The Shawshank Redemption"], "entity_types": ["Title"]}
{"sentence": "I'm curious to know the accolades for the film La La Land", "entity_names": ["La La Land"], "entity_types": ["Title"]}
{"sentence": "What awards did the movie Parasite win, and what nominations did it receive?", "entity_names": ["Parasite"], "entity_types": ["Title"]}
{"sentence": "Can you recommend a movie with an epic soundtrack and action-packed scenes?", "entity_names": ["epic soundtrack", "action-packed scenes"], "entity_types": ["Song", "Plot"]}
{"sentence": "Is there a movie with a heavy metal song in its soundtrack that has intense fight scenes?", "entity_names": ["heavy metal song", "intense fight scenes"], "entity_types": ["Song", "Plot"]}
{"sentence": "What movie has the best rock music and high-energy concert scenes?", "entity_names": ["rock music", "high-energy concert scenes"], "entity_types": ["Song", "Plot"]}
{"sentence": "Can you find any teasers for the upcoming Marvel movie", "entity_names": ["teasers", "Marvel"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "I'm not sure if I want to watch a scary movie trailer or a romantic one, can you help me decide?", "entity_names": ["scary", "romantic"], "entity_types": ["Genre", "Genre"]}
{"sentence": "I'm not sure if I want to watch a classic film trailer or something more modern, can you recommend anything?", "entity_names": ["classic", "modern"], "entity_types": ["Genre", "Genre"]}
{"sentence": "Do you know if there's a behind-the-scenes featurette for the latest Star Wars movie? I'd love to see how they created those special effects.", "entity_names": ["Star Wars", "special effects"], "entity_types": ["Title", "Plot"]}
{"sentence": "Can you provide me with the viewers' rating for the movie The Shawshank Redemption?", "entity_names": ["viewers' rating", "The Shawshank Redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "Please recommend a highly-rated romantic comedy film from the 1990s.", "entity_names": ["highly-rated", "romantic comedy", "1990s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "Who directed the film Inception and what is the plot of the movie?", "entity_names": ["directed", "Inception", "plot"], "entity_types": ["Director", "Title", "Plot"]}
{"sentence": "What movie from the 2010s has won the most awards?", "entity_names": ["2010s", "won the most awards"], "entity_types": ["Year", "Review"]}
{"sentence": "Which movie directed by Christopher Nolan has received the most nominations?", "entity_names": ["Christopher Nolan", "received the most nominations"], "entity_types": ["Director", "Review"]}
{"sentence": "Can you tell me the year in which the film Inception starring Leonardo DiCaprio was released?", "entity_names": ["Inception", "Leonardo DiCaprio"], "entity_types": ["Title", "Actor"]}
{"sentence": "I'm looking for the genre of the movie Pulp Fiction directed by Quentin Tarantino.", "entity_names": ["Pulp Fiction", "Quentin Tarantino"], "entity_types": ["Title", "Director"]}
{"sentence": "Can you recommend me some action thriller movies with a female lead?", "entity_names": ["action thriller", "female lead"], "entity_types": ["Genre", "Character"]}
{"sentence": "I'm in the mood for a mind-bending science fiction film, can you suggest one from the 90s?", "entity_names": ["mind-bending", "science fiction", "90s"], "entity_types": ["Plot", "Genre", "Year"]}
{"sentence": "Can you tell me about any awards or nominations that The Shawshank Redemption received?", "entity_names": ["awards or nominations", "The Shawshank Redemption"], "entity_types": ["Review", "Title"]}
{"sentence": "I'm open to watching a movie directed by a female filmmaker that received critical acclaim. Do you have any recommendations?", "entity_names": ["critical acclaim"], "entity_types": ["Review"]}
{"sentence": "What film did Quentin Tarantino direct that has a very high viewers' rating?", "entity_names": ["Quentin Tarantino", "viewers' rating"], "entity_types": ["Director", "Viewers' Rating"]}
{"sentence": "I'm interested in a romantic comedy released in the 1990s, do you have any recommendations?", "entity_names": ["romantic comedy", "1990s"], "entity_types": ["Genre", "Year"]}
{"sentence": "Can you provide me with some insights into the making of the film Inception?", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "I'm interested in learning about the director of the movie Titanic.", "entity_names": ["Titanic"], "entity_types": ["Title"]}
{"sentence": "Could you show me a documentary about the creation of the Lord of the Rings series?", "entity_names": ["Lord of the Rings"], "entity_types": ["Title"]}
{"sentence": "Hey, I'm dying to see the teaser for the new Christopher Nolan movie, could you please show me a snippet?", "entity_names": ["teaser", "Christopher Nolan"], "entity_types": ["Trailer", "Director"]}
{"sentence": "I'm so pumped for the upcoming Marvel movie, can you hook me up with a glimpse of the trailer?", "entity_names": ["Marvel", "trailer"], "entity_types": ["Genre", "Trailer"]}
{"sentence": "I'm really hyped for the next Star Wars film, can you show me a sneak peek of the teaser?", "entity_names": ["Star Wars", "sneak peek"], "entity_types": ["Title", "Trailer"]}
{"sentence": "Can you recommend a movie with an amazing soundtrack that will make me want to go on an adventure?", "entity_names": ["amazing soundtrack", "adventure"], "entity_types": ["Song", "Genre"]}
{"sentence": "I'm looking for a film with a catchy theme song that will get me in the mood for an exciting journey. Do you have any recommendations?", "entity_names": ["catchy theme song", "exciting journey"], "entity_types": ["Song", "Plot"]}
{"sentence": "I'm interested in a movie that features an epic soundtrack, preferably in the adventure or fantasy genre. Any suggestions?", "entity_names": ["epic soundtrack", "adventure", "fantasy"], "entity_types": ["Song", "Genre", "Genre"]}
{"sentence": "Can you tell me about the director of the film The Revenant and any behind-the-scenes details?", "entity_names": ["director", "The Revenant"], "entity_types": ["Director", "Title"]}
{"sentence": "I'm interested in a movie with an amazing soundtrack. Do you have any recommendations and can you provide some behind-the-scenes information about the music production?", "entity_names": ["amazing soundtrack", "behind-the-scenes information"], "entity_types": ["Song", "Plot"]}
{"sentence": "Show me a classic movie directed by Alfred Hitchcock and share some behind-the-scenes stories about the making of the film.", "entity_names": ["Alfred Hitchcock"], "entity_types": ["Director"]}
{"sentence": "Who directed the movie that reveals the making of the Titanic film", "entity_names": ["making of", "Titanic"], "entity_types": ["Plot", "Title"]}
{"sentence": "What are some must-see films that explore the behind-the-scenes of famous Hollywood blockbusters", "entity_names": ["behind-the-scenes", "Hollywood blockbusters"], "entity_types": ["Plot", "Genre"]}
{"sentence": "Show me a film that delves into the production process of a classic western movie", "entity_names": ["classic western"], "entity_types": ["Genre"]}
{"sentence": "Can you tell me which movie features the song 'My Heart Will Go On'?", "entity_names": ["My Heart Will Go On"], "entity_types": ["Song"]}
{"sentence": "Show me a movie with a famous theme song that became a chart-topping hit", "entity_names": [], "entity_types": []}
{"sentence": "What film is known for its iconic soundtrack with songs by The Beatles?", "entity_names": [], "entity_types": []}
{"sentence": "Can you recommend a movie starring Leonardo DiCaprio?", "entity_names": ["Leonardo DiCaprio"], "entity_types": ["Actor"]}
{"sentence": "What's the best comedy film from the 2000s?", "entity_names": ["best", "comedy film", "2000s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "What is the name of the movie that features the song 'My Heart Will Go On'?", "entity_names": ["My Heart Will Go On"], "entity_types": ["Song"]}
{"sentence": "Show me a movie where the main character is a famous DJ", "entity_names": ["famous DJ"], "entity_types": ["Character"]}
{"sentence": "Can you tell me the plot of the movie Inception?", "entity_names": ["plot", "Inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "What are some highly-rated science fiction movies from the 1990s?", "entity_names": ["highly-rated", "science fiction", "1990s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "Who are the main characters in the film The Shawshank Redemption?", "entity_names": ["main characters", "The Shawshank Redemption"], "entity_types": ["Character", "Title"]}
{"sentence": "Can you tell me which film has received the highest viewers' rating recently?", "entity_names": ["highest viewers' rating"], "entity_types": ["Viewers' Rating"]}
{"sentence": "I'm curious about the type of movie that Quentin Tarantino directed in 2019, can you provide some information?", "entity_names": ["Quentin Tarantino", "2019"], "entity_types": ["Director", "Year"]}
{"sentence": "I'm looking for a classic movie that has an iconic song in it, any recommendations?", "entity_names": [], "entity_types": []}
{"sentence": "What is the storyline of the film The Shawshank Redemption?", "entity_names": ["storyline", "The Shawshank Redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "Could you give me a brief summary of the movie The Godfather?", "entity_names": ["summary", "The Godfather"], "entity_types": ["Plot", "Title"]}
{"sentence": "what movie features the song my heart will go on", "entity_names": ["my heart will go on"], "entity_types": ["Song"]}
{"sentence": "can you recommend a film with an iconic soundtrack", "entity_names": ["iconic"], "entity_types": ["Song"]}
{"sentence": "who directed the movie that features the song eye of the tiger", "entity_names": ["eye of the tiger"], "entity_types": ["Song"]}
{"sentence": "I'm not sure which movie to watch tonight, can you recommend a film with a high viewers' rating and a compelling plot?", "entity_names": ["high viewers' rating", "compelling plot"], "entity_types": ["Viewers' Rating", "Plot"]}
{"sentence": "I can't decide on a movie, do you have any suggestions for a classic film directed by Alfred Hitchcock?", "entity_names": ["classic film", "Alfred Hitchcock"], "entity_types": ["Title", "Director"]}
{"sentence": "I'm looking for a movie with a strong female lead and a great soundtrack, any recommendations?", "entity_names": ["strong female lead", "great soundtrack"], "entity_types": ["Character", "Song"]}
{"sentence": "Can you give me a detailed summary of the plot for the movie Inception?", "entity_names": ["detailed summary", "Inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "I'm looking for a movie with a mind-bending plot twist. What would you recommend?", "entity_names": ["mind-bending plot twist"], "entity_types": ["Plot"]}
{"sentence": "I want to watch a movie with an intense and gripping storyline. Any suggestions?", "entity_names": ["intense and gripping storyline"], "entity_types": ["Plot"]}
{"sentence": "Can you recommend a romantic comedy movie from the 1990s with a high viewers' rating?", "entity_names": ["romantic comedy", "1990s", "high"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "I'm looking for a drama film directed by a female director that received positive reviews.", "entity_names": ["drama", "female director", "positive reviews"], "entity_types": ["Genre", "Director", "Review"]}
{"sentence": "Which movie starring Tom Hanks has the highest viewers' rating?", "entity_names": ["Tom Hanks", "highest"], "entity_types": ["Actor", "Viewers' Rating"]}
{"sentence": "What movie falls under the category of science fiction and has a strong emphasis on time travel?", "entity_names": ["science fiction", "time travel"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Show me a movie that belongs to the horror genre and features a haunted house as the main setting.", "entity_names": ["horror", "haunted house"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Hey, can I see a sneak peek of the new James Bond movie?", "entity_names": ["sneak peek", "James Bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "What's the deal with that new horror movie? Is it really as scary as they say?", "entity_names": ["horror"], "entity_types": ["Genre"]}
{"sentence": "Do you have any trailers for those blockbuster action movies coming out next year?", "entity_names": ["trailers", "action", "next year"], "entity_types": ["Trailer", "Genre", "Year"]}
{"sentence": "can you give me a summary of the movie inception", "entity_names": ["inception"], "entity_types": ["Title"]}
{"sentence": "what is the plot of the film the shawshank redemption", "entity_names": ["the shawshank redemption"], "entity_types": ["Title"]}
{"sentence": "tell me about the storyline of the matrix trilogy", "entity_names": ["the matrix"], "entity_types": ["Title"]}
{"sentence": "Yo, did that movie with Leo DiCaprio win any sick awards or get any nominations?", "entity_names": ["Leo DiCaprio", "nominations"], "entity_types": ["Actor", "Review"]}
{"sentence": "Hey, has any movie from the 90s got mad recognition and accolades?", "entity_names": ["90s", "recognition", "accolades"], "entity_types": ["Year", "Review", "Viewers' Rating"]}
{"sentence": "Yo, which flick got the most love from the critics and the audience?", "entity_names": ["love from the critics", "audience"], "entity_types": ["Review", "Viewers' Rating"]}
{"sentence": "Can you tell me the name of the movie with the song 'Let It Go'?", "entity_names": ["Let It Go"], "entity_types": ["Song"]}
{"sentence": "I want to watch a film with the best soundtrack, what do you recommend?", "entity_names": ["best soundtrack"], "entity_types": ["Review"]}
{"sentence": "Could you show me a movie with a great love song in it?", "entity_names": ["love song"], "entity_types": ["Song"]}
{"sentence": "Who directed the film Titanic?", "entity_names": ["directed", "Titanic"], "entity_types": ["Director", "Title"]}
{"sentence": "Can you recommend a thriller movie from the 1990s?", "entity_names": ["thriller", "1990s"], "entity_types": ["Genre", "Year"]}
{"sentence": "Can you tell me about a movie with a really catchy song?", "entity_names": ["catchy song"], "entity_types": ["Song"]}
{"sentence": "Do you have any recommendations for movies with great music?", "entity_names": ["great music"], "entity_types": ["Song"]}
{"sentence": "What's a movie that has a famous song in it?", "entity_names": ["famous song"], "entity_types": ["Song"]}
{"sentence": "Can you recommend a movie with a great soundtrack?", "entity_names": ["great soundtrack"], "entity_types": ["Review"]}
{"sentence": "Show me a movie with an iconic song from the 80s", "entity_names": ["iconic song", "80s"], "entity_types": ["Song", "Year"]}
{"sentence": "Is there a movie with a famous song by Queen in it?", "entity_names": [], "entity_types": []}
{"sentence": "Yo, where can I stream that new Spider-Man flick?", "entity_names": ["Spider-Man"], "entity_types": ["Title"]}
{"sentence": "Can I find any 80s action movies starring Arnold Schwarzenegger on any streaming service?", "entity_names": ["80s action", "Arnold Schwarzenegger"], "entity_types": ["Genre", "Actor"]}
{"sentence": "Yo, what's the deal with that horror movie from 2019 with the killer clown? Can I watch it anywhere?", "entity_names": ["horror movie", "2019", "killer clown"], "entity_types": ["Genre", "Year", "Character"]}
{"sentence": "Can you tell me the showtimes for the movie A Star is Born?", "entity_names": ["A Star is Born"], "entity_types": ["Title"]}
{"sentence": "Is there a movie theater nearby showing the film The Bridges of Madison County?", "entity_names": ["The Bridges of Madison County"], "entity_types": ["Title"]}
{"sentence": "Are there any romance movies playing this weekend that are rated PG-13?", "entity_names": ["romance", "PG-13"], "entity_types": ["Genre", "MPAA Rating"]}
{"sentence": "What is the story of the film The Shawshank Redemption?", "entity_names": ["story", "The Shawshank Redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "Could you give me a summary of the movie The Matrix?", "entity_names": ["summary", "The Matrix"], "entity_types": ["Plot", "Title"]}
{"sentence": "Can you tell me about the director and some interesting behind-the-scenes facts of the movie The Notebook?", "entity_names": ["director", "The Notebook"], "entity_types": ["Director", "Title"]}
{"sentence": "I'm looking for a movie with a captivating plot and the song 'My Heart Will Go On'. Can you suggest one?", "entity_names": ["my heart will go on"], "entity_types": ["Song"]}
{"sentence": "I'm interested in a film from the 1980s with a strong female lead. Can you recommend one with some details about the making-of?", "entity_names": ["1980s", "strong female lead"], "entity_types": ["Year", "Character"]}
{"sentence": "Can you find a movie with a soundtrack featuring the song 'My Heart Will Go On'?", "entity_names": ["My Heart Will Go On"], "entity_types": ["Song"]}
{"sentence": "Which movie directed by Christopher Nolan has the best soundtrack?", "entity_names": ["Christopher Nolan"], "entity_types": ["Director"]}
{"sentence": "What are some classic sci-fi films from the 80s directed by Steven Spielberg", "entity_names": ["sci-fi", "80s", "Steven Spielberg"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "Can you recommend a crime thriller movie with a high viewers' rating?", "entity_names": ["crime thriller", "high viewers' rating"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "I'm looking for a romantic comedy with a catchy soundtrack, any recommendations?", "entity_names": ["romantic comedy", "catchy soundtrack"], "entity_types": ["Genre", "Song"]}
{"sentence": "Can you give me a brief summary of the latest romantic comedy film?", "entity_names": ["romantic comedy"], "entity_types": ["Genre"]}
{"sentence": "I'm not sure what type of movie I want to watch. Can you tell me about a thriller from the 90s?", "entity_names": ["thriller", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "I'm indecisive about what to watch tonight. Can you tell me about a classic drama from the 1950s?", "entity_names": ["classic drama", "1950s"], "entity_types": ["Genre", "Year"]}
{"sentence": "What is the genre of the movie Inception directed by Christopher Nolan?", "entity_names": ["Inception", "Christopher Nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "Can you recommend a classic comedy film from the 1980s?", "entity_names": ["classic comedy", "1980s"], "entity_types": ["Genre", "Year"]}
{"sentence": "Show me a thriller movie with Leonardo DiCaprio in the lead role", "entity_names": ["thriller", "Leonardo DiCaprio"], "entity_types": ["Genre", "Actor"]}
{"sentence": "Can you show me a teaser for the new James Bond movie?", "entity_names": ["teaser", "James Bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "I want to see a trailer for a horror movie that came out this year.", "entity_names": ["trailer", "horror", "this year"], "entity_types": ["Trailer", "Genre", "Year"]}
{"sentence": "Are there any teasers for upcoming animated movies?", "entity_names": ["teasers", "upcoming", "animated"], "entity_types": ["Trailer", "Year", "Genre"]}
{"sentence": "Yo, what's the deal with that new flick with the crazy plot twist at the end?", "entity_names": ["plot twist"], "entity_types": ["Plot"]}
{"sentence": "Can you give me the lowdown on that movie where the guy goes all vigilante on the bad guys?", "entity_names": ["vigilante"], "entity_types": ["Plot"]}
{"sentence": "Tell me about that film where the girl kicks butt and takes down the corrupt government, you know what I'm sayin'?", "entity_names": ["kicks butt", "corrupt government"], "entity_types": ["Plot", "Plot"]}
{"sentence": "Can you tell me the story of the movie 'Jurassic Park'?", "entity_names": ["Jurassic Park"], "entity_types": ["Title"]}
{"sentence": "Is there a movie from the 2000s with really cool action scenes?", "entity_names": ["2000s", "action"], "entity_types": ["Year", "Genre"]}
{"sentence": "I want to watch a film about a superhero who can fly - do you know any?", "entity_names": ["superhero", "fly"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Can I find the movie The Godfather on any streaming platforms?", "entity_names": ["The Godfather"], "entity_types": ["Title"]}
{"sentence": "Is there a movie with an amazing soundtrack available on any streaming platform?", "entity_names": ["amazing soundtrack"], "entity_types": ["Review"]}
{"sentence": "Can you recommend any classic romance movies from the 1950s?", "entity_names": ["classic romance", "1950s"], "entity_types": ["Genre", "Year"]}
{"sentence": "I'm in the mood for a lighthearted comedy set in the 1920s. Any suggestions?", "entity_names": ["lighthearted comedy", "1920s"], "entity_types": ["Genre", "Year"]}
{"sentence": "What type of films fall under the crime genre, particularly those involving heists or capers?", "entity_names": ["crime"], "entity_types": ["Genre"]}
{"sentence": "Can I watch the latest horror movie tonight?", "entity_names": ["latest horror movie"], "entity_types": ["Genre"]}
{"sentence": "Is there a romantic comedy playing this weekend?", "entity_names": ["romantic comedy"], "entity_types": ["Genre"]}
{"sentence": "Do you have any action films with Tom Cruise in the lead role?", "entity_names": ["action films", "Tom Cruise"], "entity_types": ["Genre", "Actor"]}
{"sentence": "Can you actually show me a trailer for the new James Bond movie?", "entity_names": ["trailer", "James Bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "Are there any teasers for the upcoming horror film directed by Jordan Peele?", "entity_names": ["teasers", "horror", "Jordan Peele"], "entity_types": ["Trailer", "Genre", "Director"]}
{"sentence": "I'm not sure if it's real, but can you play a snippet of the trailer for the next Marvel superhero movie?", "entity_names": ["snippet", "Marvel", "superhero"], "entity_types": ["Trailer", "Genre", "Genre"]}
{"sentence": "Can you tell me the plot of a thriller movie directed by Alfred Hitchcock?", "entity_names": ["thriller", "Alfred Hitchcock"], "entity_types": ["Genre", "Director"]}
{"sentence": "I want to watch a film with a captivating storyline. Can you recommend a movie released in the 1990s with a strong female lead?", "entity_names": ["1990s", "strong female lead"], "entity_types": ["Year", "Character"]}
{"sentence": "I'm looking for a movie with a complex narrative. Could you suggest a film featuring time travel or alternate realities?", "entity_names": ["time travel"], "entity_types": ["Plot"]}
{"sentence": "I heard that some of the best action movies belong to a specific subgenre. Can you tell me what kind of subgenre 'Die Hard' falls under?", "entity_names": ["Die Hard"], "entity_types": ["Title"]}
{"sentence": "I'm looking for a movie that has a really unique plot. Can you recommend a film that falls under the science fiction genre?", "entity_names": ["science fiction"], "entity_types": ["Genre"]}
{"sentence": "I'm interested in watching a movie with a really intense soundtrack. Do you have any recommendations for films that fall under the thriller genre?", "entity_names": ["thriller"], "entity_types": ["Genre"]}
{"sentence": "Could you tell me the name of the director for the film Inception?", "entity_names": ["director", "Inception"], "entity_types": ["Director", "Title"]}
{"sentence": "I'm looking for a movie with Meryl Streep in it. Can you recommend one with a high viewers' rating?", "entity_names": ["Meryl Streep", "high viewers' rating"], "entity_types": ["Actor", "Viewers' Rating"]}
{"sentence": "Show me a movie directed by Christopher Nolan that has a trailer available.", "entity_names": ["Christopher Nolan", "trailer"], "entity_types": ["Director", "Trailer"]}
{"sentence": "Can you recommend a comedy movie with a strong female lead that is set in the 1990s", "entity_names": ["comedy", "strong female lead", "1990s"], "entity_types": ["Genre", "Character", "Year"]}
{"sentence": "I am looking for a thriller movie with espionage as the main plot in the style of the Bourne series", "entity_names": ["thriller", "espionage", "the Bourne series"], "entity_types": ["Genre", "Plot", "Title"]}
{"sentence": "Do you have any recommendations for science fiction movies that are centered around time travel and have a mind-bending plot twist", "entity_names": ["science fiction", "time travel", "mind-bending plot twist"], "entity_types": ["Genre", "Plot", "Plot"]}
{"sentence": "Can you show me a teaser for the upcoming Avengers movie", "entity_names": ["teaser", "Avengers"], "entity_types": ["Trailer", "Title"]}
{"sentence": "I'm excited about the new Star Wars film, could you show me a sneak peek", "entity_names": ["Star Wars", "sneak peek"], "entity_types": ["Title", "Trailer"]}
{"sentence": "I want to see a preview of the new Spider-Man movie, please", "entity_names": ["preview", "Spider-Man"], "entity_types": ["Trailer", "Title"]}
{"sentence": "I need to see a preview of the new Spider-Man movie right now", "entity_names": ["preview", "Spider-Man"], "entity_types": ["Trailer", "Title"]}
{"sentence": "show me a teaser for the upcoming James Bond film immediately", "entity_names": ["teaser", "James Bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "I don't have time to waste, I want to watch a sneak peek of the new Jurassic Park movie", "entity_names": ["sneak peek", "Jurassic Park"], "entity_types": ["Trailer", "Title"]}
{"sentence": "Hey, can you give me a preview of the latest Fast and Furious film with Vin Diesel in it?", "entity_names": ["Fast and Furious", "Vin Diesel"], "entity_types": ["Title", "Actor"]}
{"sentence": "I heard there's a new Pixar animated movie out, can you show me a sneak peek or something?", "entity_names": ["animated"], "entity_types": ["Genre"]}
{"sentence": "Who directed the 1994 film Forrest Gump?", "entity_names": ["directed", "1994", "Forrest Gump"], "entity_types": ["Director", "Year", "Title"]}
{"sentence": "Show me a trailer for the film The Matrix.", "entity_names": ["trailer", "The Matrix"], "entity_types": ["Trailer", "Title"]}
{"sentence": "Hey, can I watch The Matrix on any streaming platforms?", "entity_names": ["The Matrix"], "entity_types": ["Title"]}
{"sentence": "Is there a new action movie out this year that's worth watching on any streaming services?", "entity_names": ["action movie", "worth watching", "streaming services"], "entity_types": ["Genre", "Viewers' Rating", "Genre"]}
{"sentence": "Hey, what are some comedy movies from the 90s that I can stream right now?", "entity_names": ["comedy movies", "90s", "stream right now"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "Can you please show me a trailer for the new James Bond film?", "entity_names": ["trailer", "James Bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "I'm dying to see a thrilling movie with an amazing plot. What do you recommend?", "entity_names": ["thrilling", "amazing plot"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Do you have any suggestions for must-see films with great soundtracks?", "entity_names": ["must-see", "great soundtracks"], "entity_types": ["Viewers' Rating", "Song"]}
{"sentence": "Can I watch the trailer for the latest James Bond movie?", "entity_names": ["trailer", "James Bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "What movie from the 80s has the best soundtrack?", "entity_names": ["80s", "best soundtrack"], "entity_types": ["Year", "Review"]}
{"sentence": "Who directed the science fiction film that won best picture in the 90s?", "entity_names": ["science fiction", "best picture", "90s"], "entity_types": ["Genre", "Review", "Year"]}
{"sentence": "Can you give me some behind-the-scenes insights into the making of the latest sci-fi movie about time travel?", "entity_names": ["sci-fi", "time travel"], "entity_types": ["Genre", "Plot"]}
{"sentence": "I'm really curious about the director's vision for the upcoming crime thriller. Could you share some behind-the-scenes details about it?", "entity_names": ["crime thriller"], "entity_types": ["Genre"]}
{"sentence": "I'm excited about the new fantasy film coming out. Can you provide some behind-the-scenes information about the special effects and CGI used in it?", "entity_names": ["fantasy", "special effects", "CGI"], "entity_types": ["Genre", "Plot", "Plot"]}
{"sentence": "Can you tell me about the making of the movie Inception?", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "Who directed the western movie Tombstone?", "entity_names": ["Tombstone"], "entity_types": ["Title"]}
{"sentence": "I want to see some behind-the-scenes footage of the 1980s movie Top Gun", "entity_names": ["1980s", "Top Gun"], "entity_types": ["Year", "Title"]}
{"sentence": "What are some movies with classic 1950s songs in their soundtracks?", "entity_names": ["classic 1950s songs"], "entity_types": ["Song"]}
{"sentence": "Could you recommend a movie with a beautiful instrumental soundtrack for relaxation?", "entity_names": ["relaxation"], "entity_types": ["Viewers' Rating"]}
{"sentence": "Which movie features the song 'My Heart Will Go On' by Celine Dion in its soundtrack?", "entity_names": ["My Heart Will Go On", "Celine Dion"], "entity_types": ["Song", "Actor"]}
{"sentence": "who directed the movie Titanic?", "entity_names": ["Titanic"], "entity_types": ["Title"]}
{"sentence": "show me a movie with Tom Hanks as the lead actor", "entity_names": ["Tom Hanks"], "entity_types": ["Actor"]}
{"sentence": "Can you tell me the plot of the latest James Bond movie?", "entity_names": ["James Bond"], "entity_types": ["Character"]}
{"sentence": "I need to know the storyline of the highest-rated horror film of 2021 ASAP", "entity_names": ["horror film", "2021"], "entity_types": ["Genre", "Year"]}
{"sentence": "What's the premise of the upcoming Christopher Nolan movie?", "entity_names": ["Christopher Nolan"], "entity_types": ["Director"]}
{"sentence": "Has the movie La La Land won any awards?", "entity_names": ["La La Land"], "entity_types": ["Title"]}
{"sentence": "What nominations did the movie The Shape of Water receive?", "entity_names": ["nominations", "The Shape of Water"], "entity_types": ["Plot", "Title"]}
{"sentence": "Can you tell me about the director of the movie Titanic and how the special effects were created?", "entity_names": ["director", "Titanic", "special effects"], "entity_types": ["Director", "Title", "Plot"]}
{"sentence": "I'm so bored, show me some interviews with the cast of The Avengers film.", "entity_names": ["interviews", "The Avengers"], "entity_types": ["Plot", "Title"]}
{"sentence": "What are some documentaries about the making of classic Hitchcock films?", "entity_names": ["documentaries", "making of", "Hitchcock"], "entity_types": ["Plot", "Plot", "Director"]}
{"sentence": "can you recommend any classic western films directed by John Ford", "entity_names": ["classic western films", "John Ford"], "entity_types": ["Genre", "Director"]}
{"sentence": "what is the highest rated sci-fi movie from the 1990s", "entity_names": ["highest rated", "sci-fi", "1990s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "show me a thriller film from the 2000s with a plot twist", "entity_names": ["thriller", "2000s", "plot twist"], "entity_types": ["Genre", "Year", "Plot"]}
{"sentence": "Can you tell me about the director of the movie The Aviator?", "entity_names": ["director", "The Aviator"], "entity_types": ["Director", "Title"]}
{"sentence": "What was the process of creating the special effects for the movie Jurassic Park?", "entity_names": ["special effects", "Jurassic Park"], "entity_types": ["Plot", "Title"]}
{"sentence": "I'm interested in knowing about the actors who performed their own stunts in the movie Mission: Impossible. Can you provide some details?", "entity_names": ["actors", "stunts", "Mission: Impossible"], "entity_types": ["Actor", "Plot", "Title"]}
{"sentence": "Can you tell me about the awards and nominations received by the movie The Dark Knight?", "entity_names": ["The Dark Knight"], "entity_types": ["Title"]}
{"sentence": "Which movie won the most awards in 2020?", "entity_names": ["2020"], "entity_types": ["Year"]}
{"sentence": "Can you just tell me the plot of the Shawshank Redemption already?", "entity_names": ["plot", "the Shawshank Redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "I don't have time, just give me a quick summary of the movie Inception.", "entity_names": ["summary", "Inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "I need to know what the movie Interstellar is about, can you give me a brief synopsis?", "entity_names": ["Interstellar", "synopsis"], "entity_types": ["Title", "Plot"]}
{"sentence": "Can you recommend a heart-wrenching movie with a high viewers' rating?", "entity_names": ["heart-wrenching", "high viewers' rating"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "I need a movie that will make me cry. Who directed it?", "entity_names": ["make me cry", "directed"], "entity_types": ["Review", "Director"]}
{"sentence": "What's a movie with a tragic plot that received rave reviews?", "entity_names": ["tragic plot", "rave reviews"], "entity_types": ["Plot", "Review"]}
{"sentence": "Can you show me the trailer for the classic movie 'The Lion King'?", "entity_names": ["trailer", "classic", "The Lion King"], "entity_types": ["Trailer", "Viewers' Rating", "Title"]}
{"sentence": "I remember a movie with a catchy song, can you show me a teaser for the film with the song 'My Heart Will Go On'?", "entity_names": ["teaser", "My Heart Will Go On"], "entity_types": ["Trailer", "Song"]}
{"sentence": "I'm feeling nostalgic for an old western movie, can you show me a preview for the film 'The Good, the Bad and the Ugly'?", "entity_names": ["old", "western", "preview", "The Good, the Bad and the Ugly"], "entity_types": ["Year", "Genre", "Trailer", "Title"]}
{"sentence": "Can you recommend a movie with a memorable soundtrack that is worth listening to?", "entity_names": ["memorable soundtrack"], "entity_types": ["Review"]}
{"sentence": "Which movie from the 80s is known for its iconic song that became a hit?", "entity_names": ["80s", "iconic song"], "entity_types": ["Year", "Song"]}
{"sentence": "Could you suggest a film with a beautiful theme song that complements the storyline?", "entity_names": ["storyline"], "entity_types": ["Plot"]}
{"sentence": "Hey, what's that super scary horror movie with the killer clown that came out in 2017?", "entity_names": ["horror", "killer clown", "2017"], "entity_types": ["Genre", "Character", "Year"]}
{"sentence": "Can you recommend a good action movie from the 90s with Bruce Willis in it?", "entity_names": ["action", "90s", "Bruce Willis"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "I'm in the mood for a romantic comedy, can you suggest one that's really heartwarming?", "entity_names": ["romantic comedy", "heartwarming"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "I'm looking for a film that has a memorable soundtrack.", "entity_names": ["memorable soundtrack"], "entity_types": ["Song"]}
{"sentence": "Do you know of any movies with a great music score?", "entity_names": ["great music score"], "entity_types": ["Song"]}
{"sentence": "Can you tell me about the making of the movie Inception directed by Christopher Nolan?", "entity_names": ["Inception", "Christopher Nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "What behind-the-scenes footage is available for the movie Avatar by James Cameron?", "entity_names": ["Avatar", "James Cameron"], "entity_types": ["Title", "Director"]}
{"sentence": "I'm interested in learning more about the production process for the movie The Matrix directed by the Wachowskis. Can you provide any details?", "entity_names": ["The Matrix", "Wachowskis"], "entity_types": ["Title", "Director"]}
{"sentence": "Where can I find the movie Interstellar to watch?", "entity_names": ["Interstellar"], "entity_types": ["Title"]}
{"sentence": "Has the movie 'The Shawshank Redemption' won any awards or nominations?", "entity_names": ["The Shawshank Redemption", "awards or nominations"], "entity_types": ["Title", "Review"]}
{"sentence": "What awards did 'Parasite' win in the year it was released?", "entity_names": ["awards", "Parasite"], "entity_types": ["Review", "Title"]}
{"sentence": "I need to know if 'La La Land' received any nominations for its music and songs in the film", "entity_names": ["La La Land", "nominations", "music and songs"], "entity_types": ["Title", "Review", "Song"]}
{"sentence": "What are some popular action-adventure films from the 1990s?", "entity_names": ["action-adventure", "1990s"], "entity_types": ["Genre", "Year"]}
{"sentence": "I'm looking for a romantic comedy with a unique storyline and a happy ending.", "entity_names": ["romantic comedy", "unique storyline", "happy ending"], "entity_types": ["Genre", "Plot", "Plot"]}
{"sentence": "Hey, are there any showtimes for the new James Bond movie?", "entity_names": ["James Bond"], "entity_types": ["Title"]}
{"sentence": "Do you know if the new horror movie is playing tonight?", "entity_names": ["horror"], "entity_types": ["Genre"]}
{"sentence": "Can I get tickets for the new Marvel movie at the theater near me?", "entity_names": ["Marvel"], "entity_types": ["Title"]}
{"sentence": "what are some classic adventure movies like Indiana Jones", "entity_names": ["classic adventure", "Indiana Jones"], "entity_types": ["Genre", "Title"]}
{"sentence": "can you recommend a romantic comedy similar to When Harry Met Sally", "entity_names": ["romantic comedy", "When Harry Met Sally"], "entity_types": ["Genre", "Title"]}
{"sentence": "which crime thriller movies are worth watching", "entity_names": ["crime thriller"], "entity_types": ["Genre"]}
{"sentence": "What is the main genre of the movie The Dark Knight and what specific subgenre does it belong to?", "entity_names": ["main genre", "The Dark Knight", "specific subgenre"], "entity_types": ["Genre", "Title", "Genre"]}
{"sentence": "Can you recommend a highly-rated comedy film from the 1980s that has an outstanding plot?", "entity_names": ["comedy film", "1980s", "outstanding plot"], "entity_types": ["Genre", "Year", "Plot"]}
{"sentence": "I'm interested in watching a science fiction movie with a focus on time travel. Are there any recent ones with good viewer ratings?", "entity_names": ["science fiction movie", "time travel", "recent ones", "good viewer ratings"], "entity_types": ["Genre", "Plot", "Genre", "Viewers' Rating"]}
{"sentence": "Yo, can you hook me up with a sneak peek of that new action flick with Dwayne Johnson?", "entity_names": ["sneak peek", "action", "Dwayne Johnson"], "entity_types": ["Trailer", "Genre", "Actor"]}
{"sentence": "Hey, what's the deal with that horror movie trailer everyone's talking about?", "entity_names": ["horror", "trailer"], "entity_types": ["Genre", "Trailer"]}
{"sentence": "I heard there's a new rom-com dropping soon, can I get a taste of the trailer?", "entity_names": ["rom-com", "taste"], "entity_types": ["Genre", "Trailer"]}
{"sentence": "what are the showtimes for the movie inception directed by christopher nolan", "entity_names": ["inception", "christopher nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "where can I buy tickets for the film wonder woman 1984", "entity_names": ["wonder woman 1984"], "entity_types": ["Title"]}
{"sentence": "what is the viewers' rating for the movie the shawshank redemption", "entity_names": ["viewers' rating", "the shawshank redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "Can you recommend a movie with an iconic 80s soundtrack?", "entity_names": ["iconic", "80s", "soundtrack"], "entity_types": ["Review", "Year", "Song"]}
{"sentence": "I miss classic musicals with great songs. Can you suggest one for me?", "entity_names": ["classic musicals", "great songs"], "entity_types": ["Genre", "Song"]}
{"sentence": "Can you recommend any recent sci-fi movies with a strong female lead?", "entity_names": ["recent", "sci-fi", "strong female lead"], "entity_types": ["Year", "Genre", "Character"]}
{"sentence": "What's a good comedy movie that also includes elements of romance and adventure?", "entity_names": ["comedy", "romance", "adventure"], "entity_types": ["Genre", "Genre", "Genre"]}
{"sentence": "I'm in the mood for some historical drama set in ancient times. Can you suggest any titles?", "entity_names": ["historical drama", "ancient times"], "entity_types": ["Genre", "Year"]}
{"sentence": "Can you tell me what movies Emma Stone has starred in?", "entity_names": ["Emma Stone"], "entity_types": ["Actor"]}
{"sentence": "What is the viewer's rating for the film The Shawshank Redemption?", "entity_names": ["viewer's rating", "The Shawshank Redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "I heard that there is a new movie out with a title track that everyone is talking about. Can you tell me which streaming platform has it?", "entity_names": ["title track"], "entity_types": ["Song"]}
{"sentence": "I'm looking for a film from the 90s with a very intense plot. Do you know if it's available on any streaming platforms?", "entity_names": ["90s", "intense plot"], "entity_types": ["Year", "Plot"]}
{"sentence": "I'm interested in a classic movie directed by a well-known director. Can you check if it's on any streaming platforms?", "entity_names": ["classic movie", "well-known director"], "entity_types": ["Genre", "Director"]}
{"sentence": "Yo, what's the deal with that sci-fi flick about time travel and stuff?", "entity_names": ["sci-fi", "time travel"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Hey, I'm looking for a movie with some crazy action scenes, like, fast and furious style.", "entity_names": ["action", "fast and furious style"], "entity_types": ["Genre", "Genre"]}
{"sentence": "Can you recommend a romantic comedy that's not super cheesy, but still makes you feel all warm and fuzzy inside?", "entity_names": ["romantic comedy", "warm and fuzzy inside"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "I'd like to see the new Marvel superhero movie, can you tell me the showtimes and ticket availability?", "entity_names": ["Marvel superhero"], "entity_types": ["Genre"]}
{"sentence": "What is the best-rated action movie of the year and when can I see it?", "entity_names": ["best-rated", "action", "the year"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "I'm looking for a comedy film that's suitable for families, can I get the showtimes and buy tickets?", "entity_names": ["comedy", "families"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "Which movie did Steven Spielberg direct in 1993?", "entity_names": ["Steven Spielberg", "1993"], "entity_types": ["Director", "Year"]}
{"sentence": "What are some films starring Tom Hanks and Meg Ryan?", "entity_names": ["Tom Hanks", "Meg Ryan"], "entity_types": ["Actor", "Actor"]}
{"sentence": "Can you recommend a thriller directed by Christopher Nolan?", "entity_names": ["thriller", "Christopher Nolan"], "entity_types": ["Genre", "Director"]}
{"sentence": "Can you tell me the storyline of the latest Marvel movie?", "entity_names": ["Marvel movie"], "entity_types": ["Title"]}
{"sentence": "Do you have any recommendations for a family-friendly film with a heartwarming plot?", "entity_names": ["family-friendly", "heartwarming"], "entity_types": ["MPAA Rating", "Plot"]}
{"sentence": "Can I watch Inception on any streaming platforms right now?", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "Is there any Netflix original movie with Tom Hardy in it?", "entity_names": ["Tom Hardy"], "entity_types": ["Actor"]}
{"sentence": "What action-packed movie from the 90s can I stream with my friends this weekend?", "entity_names": ["action-packed", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "Can you recommend a movie with a soundtrack featuring the song 'I Will Always Love You'?", "entity_names": ["I Will Always Love You"], "entity_types": ["Song"]}
{"sentence": "What's a movie from the 90s with a popular love song in its soundtrack?", "entity_names": ["90s", "popular love song"], "entity_types": ["Year", "Song"]}
{"sentence": "I'm looking for a film with a soundtrack filled with classic rock hits, can you suggest one?", "entity_names": ["classic rock hits"], "entity_types": ["Song"]}
{"sentence": "What is the overall rating for the movie To All the Boys I've Loved Before", "entity_names": ["To All the Boys I've Loved Before"], "entity_types": ["Title"]}
{"sentence": "Can you recommend a coming-of-age movie with a strong female lead", "entity_names": ["coming-of-age", "strong female lead"], "entity_types": ["Genre", "Character"]}
{"sentence": "Who directed the romantic comedy film with a high viewers' rating that came out last year", "entity_names": ["romantic comedy", "high viewers' rating", "last year"], "entity_types": ["Genre", "Viewers' Rating", "Year"]}
{"sentence": "Can you play the trailer for the classic movie Ghostbusters?", "entity_names": ["classic movie Ghostbusters"], "entity_types": ["Title"]}
{"sentence": "I remember a movie with an iconic soundtrack - can you show me a snippet of the trailer for The Bodyguard?", "entity_names": ["iconic soundtrack", "The Bodyguard"], "entity_types": ["Song", "Title"]}
{"sentence": "I'm feeling nostalgic for the 80s - can you play the teaser for the film E.T. the Extra-Terrestrial?", "entity_names": ["nostalgic", "80s", "E.T. the Extra-Terrestrial"], "entity_types": ["Genre", "Year", "Title"]}
{"sentence": "Hey, what's the plot of the new action movie with Tom Cruise?", "entity_names": ["action", "Tom Cruise"], "entity_types": ["Genre", "Actor"]}
{"sentence": "I heard about a new thriller film, can you tell me the plot?", "entity_names": ["thriller"], "entity_types": ["Genre"]}
{"sentence": "I can't wait to see a romantic comedy this weekend, can you give me a summary of a good one?", "entity_names": ["romantic comedy"], "entity_types": ["Genre"]}
{"sentence": "Can you recommend any romantic comedy movies with a strong female lead?", "entity_names": ["romantic comedy", "strong female lead"], "entity_types": ["Genre", "Character"]}
{"sentence": "I'm in the mood for a classic musical, preferably from the 1950s or 1960s.", "entity_names": ["classic musical", "1950s or 1960s"], "entity_types": ["Genre", "Year"]}
{"sentence": "I'm looking for a heartfelt drama set in a small town, something like Steel Magnolias.", "entity_names": ["heartfelt drama", "small town", "Steel Magnolias"], "entity_types": ["Genre", "Plot", "Title"]}
{"sentence": "I'm not sure which movie has the best soundtrack, can you recommend one with a great song that sets the mood?", "entity_names": ["best soundtrack", "great song"], "entity_types": ["Review", "Song"]}
{"sentence": "I'm not sure which movie to watch, can you suggest one with a memorable theme song that's worth listening to?", "entity_names": ["worth listening to"], "entity_types": ["Viewers' Rating"]}
{"sentence": "Can you provide me with a trailer for the latest James Cameron film?", "entity_names": ["trailer", "James Cameron"], "entity_types": ["Trailer", "Director"]}
{"sentence": "What is the plot of the highest-rated comedy movie from the 1990s?", "entity_names": ["highest-rated", "comedy", "1990s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "Yo, where can I watch a flick with Chris Hemsworth kickin' butt? Any idea?", "entity_names": ["Chris Hemsworth"], "entity_types": ["Actor"]}
{"sentence": "what is the showtime for the movie 'Inception' at the nearest theater", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "are there any available tickets for the new James Bond movie", "entity_names": ["James Bond"], "entity_types": ["Character"]}
{"sentence": "can I see the trailer for the latest Marvel movie", "entity_names": ["trailer", "Marvel"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "Can you play the trailer for the newest animated movie for kids?", "entity_names": ["newest animated movie"], "entity_types": ["Genre"]}
{"sentence": "Show me the teaser for the family-friendly movie with the talking animals", "entity_names": ["family-friendly", "talking animals"], "entity_types": ["Viewers' Rating", "Plot"]}
{"sentence": "What movie from last year has a fun and exciting preview that kids will love?", "entity_names": ["last year", "preview", "kids"], "entity_types": ["Year", "Trailer", "Viewers' Rating"]}
{"sentence": "Yo, where can I catch the latest Avengers flick?", "entity_names": ["Avengers"], "entity_types": ["Title"]}
{"sentence": "Hey, is there a dope action movie playing at the theaters this weekend?", "entity_names": ["action"], "entity_types": ["Genre"]}
{"sentence": "Yo, I'm tryna see a scary movie tonight, what's playing?", "entity_names": ["scary"], "entity_types": ["Genre"]}
{"sentence": "What's the highest rated action movie of all time?", "entity_names": ["highest rated", "action movie"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "Can you recommend a comedy movie from the 90s with a great plot?", "entity_names": ["comedy movie", "90s", "great plot"], "entity_types": ["Genre", "Year", "Plot"]}
{"sentence": "Who directed the best sci-fi movie in recent years?", "entity_names": ["best", "sci-fi movie", "recent years"], "entity_types": ["Review", "Genre", "Year"]}
{"sentence": "Can you please show me a teaser for the new James Bond movie?", "entity_names": ["teaser", "James Bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "I'm dying to see a preview for the highly anticipated Marvel movie releasing next year. Can you assist with that?", "entity_names": ["preview", "Marvel", "next year"], "entity_types": ["Trailer", "Genre", "Year"]}
{"sentence": "I heard there's a thrilling new trailer for the upcoming sci-fi film. I cannot wait to watch it!", "entity_names": ["thrilling", "trailer", "sci-fi"], "entity_types": ["Viewers' Rating", "Trailer", "Genre"]}
{"sentence": "I need to find a movie with an epic soundtrack, filled with songs that give me chills and make my heart race", "entity_names": ["epic soundtrack"], "entity_types": ["Song"]}
{"sentence": "What's the latest movie with an amazing original song? I'm in the mood to discover some new music that will blow my mind!", "entity_names": [], "entity_types": []}
{"sentence": "Tell me about a movie with a soundtrack that will have me dancing in my seat! I need to feel the rhythm and groove along with the music", "entity_names": ["soundtrack"], "entity_types": ["Song"]}
{"sentence": "Can you tell me the name of the movie directed by Christopher Nolan that has a surprise ending?", "entity_names": ["Christopher Nolan", "surprise ending"], "entity_types": ["Director", "Plot"]}
{"sentence": "I'm interested in a film starring Meryl Streep and Tom Hanks, can you recommend one?", "entity_names": ["Meryl Streep", "Tom Hanks"], "entity_types": ["Actor", "Actor"]}
{"sentence": "What is the highest-rated movie of Quentin Tarantino?", "entity_names": ["highest-rated", "Quentin Tarantino"], "entity_types": ["Viewers' Rating", "Director"]}
{"sentence": "Can you show me the trailer for the new romantic comedy directed by Nancy Meyers?", "entity_names": ["romantic comedy", "Nancy Meyers"], "entity_types": ["Genre", "Director"]}
{"sentence": "I'm looking for a movie with a great soundtrack. Can you recommend a film with a memorable song performed by Whitney Houston?", "entity_names": ["great soundtrack", "memorable song", "Whitney Houston"], "entity_types": ["Review", "Song", "Actor"]}
{"sentence": "I heard there's a new historical drama featuring Meryl Streep coming out soon. Can I see a teaser for it?", "entity_names": ["historical drama", "Meryl Streep", "teaser"], "entity_types": ["Genre", "Actor", "Trailer"]}
{"sentence": "What are some mind-bending sci-fi movies that will leave me on the edge of my seat?", "entity_names": ["mind-bending", "sci-fi"], "entity_types": ["Genre", "Genre"]}
{"sentence": "Can you recommend some classic western films with intense gunfights and gripping storylines?", "entity_names": ["classic", "western", "intense gunfights"], "entity_types": ["Viewers' Rating", "Genre", "Plot"]}
{"sentence": "I'm craving a horror movie that will scare the daylights out of me. What's a truly terrifying supernatural thriller worth watching?", "entity_names": ["horror", "terrifying", "supernatural"], "entity_types": ["Genre", "Viewers' Rating", "Plot"]}
{"sentence": "Can you provide a summary of the movie Inception directed by Christopher Nolan", "entity_names": ["summary", "Inception", "Christopher Nolan"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "What are the top-rated sci-fi movies released in the past 5 years", "entity_names": ["top-rated", "sci-fi", "5 years"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "Could you recommend a movie with a strong female lead character and a compelling plot", "entity_names": ["strong female lead"], "entity_types": ["Character"]}
{"sentence": "Are there any action movies from the 1990s with a high viewers' rating and can you show me the making-of of the best one?", "entity_names": ["action", "1990s", "high viewers' rating", "making-of"], "entity_types": ["Genre", "Year", "Viewers' Rating", "Plot"]}
{"sentence": "I want to watch a comedy film from the 2000s, can you recommend one with funny bloopers and outtakes?", "entity_names": ["comedy", "2000s", "funny bloopers and outtakes"], "entity_types": ["Genre", "Year", "Plot"]}
{"sentence": "What is the plot of the film Interstellar directed by Christopher Nolan?", "entity_names": ["plot", "Interstellar", "Christopher Nolan"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "Could you tell me the storyline of the movie The Shawshank Redemption?", "entity_names": ["storyline", "The Shawshank Redemption"], "entity_types": ["Plot", "Title"]}
{"sentence": "Who directed the movie Titanic and who starred in it? I need to know now!", "entity_names": ["directed", "Titanic", "starred"], "entity_types": ["Director", "Title", "Actor"]}
{"sentence": "What's the highest rated action movie from the 90s? I really need to find something exciting to watch!", "entity_names": ["highest rated", "action", "90s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "Can you tell me the plot of the latest James Bond film and who's playing the villain? I can't wait to find out!", "entity_names": ["plot", "latest", "James Bond", "playing the villain"], "entity_types": ["Plot", "Year", "Character", "Actor"]}
{"sentence": "Can I buy tickets for the new Spider-Man movie directed by Jon Watts?", "entity_names": ["Spider-Man", "Jon Watts"], "entity_types": ["Title", "Director"]}
{"sentence": "What's the showtime for the action movie with a high viewers' rating?", "entity_names": ["action", "high viewers' rating"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "Do you have any tickets available for the musical film directed by Damien Chazelle?", "entity_names": ["musical", "Damien Chazelle"], "entity_types": ["Genre", "Director"]}
{"sentence": "what movie has the best soundtrack of all time", "entity_names": ["best soundtrack of all time"], "entity_types": ["Review"]}
{"sentence": "can you recommend a movie with a great song by beyonce", "entity_names": ["great song by beyonce"], "entity_types": ["Song"]}
{"sentence": "show me a movie with an iconic musical number", "entity_names": [], "entity_types": []}
{"sentence": "I heard that the director of The Dark Knight also made a behind-the-scenes documentary. Can you tell me more about it?", "entity_names": ["director", "The Dark Knight", "behind-the-scenes documentary"], "entity_types": ["Director", "Title", "Genre"]}
{"sentence": "I'm interested in learning about the making-of process of an iconic 90s film. Do you have any recommendations?", "entity_names": ["making-of process", "90s"], "entity_types": ["Plot", "Year"]}
{"sentence": "I'm curious about a movie that showcases the development of special effects in the film industry. Any suggestions?", "entity_names": ["special effects"], "entity_types": ["Plot"]}
{"sentence": "Yo, who's the boss director of that new action flick with the sick car chase scene?", "entity_names": ["director", "action flick", "car chase scene"], "entity_types": ["Director", "Genre", "Plot"]}
{"sentence": "Hey, what's the deal with that movie that has that girl from the horror flick last year, you know what I'm talking about?", "entity_names": ["horror flick", "last year"], "entity_types": ["Genre", "Year"]}
{"sentence": "Yo, who's the dude that played the funny sidekick in that superhero movie with the dope soundtrack?", "entity_names": ["dude", "funny sidekick", "superhero movie", "dope soundtrack"], "entity_types": ["Actor", "Character", "Genre", "Song"]}
{"sentence": "What is the main genre and subgenre of the movie The Dark Knight?", "entity_names": ["The Dark Knight"], "entity_types": ["Title"]}
{"sentence": "Can you recommend a movie with a mix of both romance and comedy as the main and subgenres?", "entity_names": ["romance", "comedy"], "entity_types": ["Genre", "Genre"]}
{"sentence": "Is there a film that falls into the horror genre with a subgenre of psychological thriller?", "entity_names": ["horror", "psychological thriller"], "entity_types": ["Genre", "Genre"]}
{"sentence": "What's the buzz on the latest action flick?", "entity_names": [], "entity_types": []}
{"sentence": "Can you recommend a highly-rated mystery movie from the 1980s?", "entity_names": ["highly-rated", "mystery", "1980s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "Who played the lead in that classic romance film?", "entity_names": ["classic romance"], "entity_types": ["Genre"]}
{"sentence": "Can you play the trailer for the latest James Bond film?", "entity_names": ["trailer", "James Bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "What movie has the most intense trailer of all time?", "entity_names": ["intense", "trailer"], "entity_types": ["Review", "Trailer"]}
{"sentence": "Show me a teaser for the upcoming Marvel movie.", "entity_names": ["teaser", "Marvel"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "What's the plot of the movie Inception?", "entity_names": ["plot", "Inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "Can you give me a summary of the movie Goodfellas directed by Martin Scorsese?", "entity_names": ["summary", "Goodfellas", "Martin Scorsese"], "entity_types": ["Plot", "Title", "Director"]}
{"sentence": "I'm looking for a movie with a mysterious plot twist, can you recommend one?", "entity_names": ["mysterious plot twist"], "entity_types": ["Plot"]}
{"sentence": "What movie has the best trailer of all time?", "entity_names": ["best", "trailer"], "entity_types": ["Review", "Trailer"]}
{"sentence": "Can you recommend any classic movies with a catchy song in the teaser?", "entity_names": ["classic", "catchy", "song", "teaser"], "entity_types": ["Genre", "Song", "Song", "Trailer"]}
{"sentence": "Which director is known for creating amazing movie teasers?", "entity_names": ["amazing"], "entity_types": ["Review"]}
{"sentence": "I'm in the mood for a musical! Can you recommend a movie with a catchy soundtrack from the 1980s?", "entity_names": ["musical", "catchy soundtrack", "1980s"], "entity_types": ["Genre", "Song", "Year"]}
{"sentence": "I'm looking for a feel-good movie with an uplifting song that will brighten my day. Can you suggest one that's family-friendly?", "entity_names": ["feel-good", "uplifting song", "family-friendly"], "entity_types": ["Song", "Song", "MPAA Rating"]}
{"sentence": "I'm hosting a movie night and want to feature a film with a memorable love theme. Can you recommend a romantic movie from the 1990s?", "entity_names": ["romantic", "1990s"], "entity_types": ["Genre", "Year"]}
{"sentence": "Show me a movie starring Tom Hanks and Meg Ryan.", "entity_names": ["Tom Hanks", "Meg Ryan"], "entity_types": ["Actor", "Actor"]}
{"sentence": "What is the viewers' rating for the film La La Land?", "entity_names": ["viewers' rating", "La La Land"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "Can I find the movie Inception with Leonardo DiCaprio available on any streaming platform?", "entity_names": ["Inception", "Leonardo DiCaprio"], "entity_types": ["Title", "Actor"]}
{"sentence": "I'm looking for a comedy from the 1990s that has a viewers' rating of at least 4 stars. Is there any streaming platform offering such a movie?", "entity_names": ["comedy", "1990s", "4 stars"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "Is there a movie directed by Quentin Tarantino with Samuel L. Jackson that is available for streaming?", "entity_names": ["Quentin Tarantino", "Samuel L. Jackson"], "entity_types": ["Director", "Actor"]}
{"sentence": "Yo, who's the director of that new action flick with The Rock?", "entity_names": ["action flick", "The Rock"], "entity_types": ["Genre", "Actor"]}
{"sentence": "What's the deal with that movie starring Taraji P. Henson and Octavia Spencer?", "entity_names": ["Taraji P. Henson", "Octavia Spencer"], "entity_types": ["Actor", "Actor"]}
{"sentence": "Tell me about that film where Keanu Reeves plays a badass assassin.", "entity_names": ["Keanu Reeves"], "entity_types": ["Actor"]}
{"sentence": "Can you provide me with a documentary on the production process of the movie Inception directed by Christopher Nolan?", "entity_names": ["Inception", "Christopher Nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "I'm interested in learning about the special effects used in the film Avatar. Could you recommend any behind-the-scenes featurettes or interviews with the visual effects team?", "entity_names": ["Avatar"], "entity_types": ["Title"]}
{"sentence": "I'm looking for a movie that delves into the cinematography of Lawrence of Arabia and its impact on the filmmaking industry.", "entity_names": ["Lawrence of Arabia"], "entity_types": ["Title"]}
{"sentence": "Can you tell me the showtimes for the new James Bond movie?", "entity_names": ["showtimes", "James Bond"], "entity_types": ["Plot", "Character"]}
{"sentence": "Which theater is playing the latest Marvel movie and what time does it start?", "entity_names": ["Marvel"], "entity_types": ["Title"]}
{"sentence": "Are there any science fiction films with high viewers' ratings currently showing?", "entity_names": ["science fiction", "high viewers' ratings"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "Can you tell me the plot of the latest Spider-Man movie?", "entity_names": ["Spider-Man"], "entity_types": ["Title"]}
{"sentence": "What's the story of the sci-fi movie released in 2020 called Tenet?", "entity_names": ["sci-fi", "2020", "Tenet"], "entity_types": ["Genre", "Year", "Title"]}
{"sentence": "I want to know the plot of the action film directed by Christopher Nolan and rated PG-13", "entity_names": ["Christopher Nolan", "PG-13"], "entity_types": ["Director", "MPAA Rating"]}
{"sentence": "Yo, who starred in that heist movie with the badass car chases?", "entity_names": ["heist movie", "car chases"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Hey, what's the deal with that flick from the 90s with the killer soundtrack?", "entity_names": ["90s", "killer soundtrack"], "entity_types": ["Year", "Song"]}
{"sentence": "Can you hook me up with the deets on the director of that sci-fi flick with the epic special effects?", "entity_names": ["director", "sci-fi flick", "epic special effects"], "entity_types": ["Director", "Genre", "Plot"]}
{"sentence": "Do you have any showtimes for the new animated movie 'Sing 2'?", "entity_names": ["Sing 2"], "entity_types": ["Title"]}
{"sentence": "Is the movie 'Encanto' suitable for young kids to watch?", "entity_names": ["Encanto"], "entity_types": ["Title"]}
{"sentence": "Could you recommend a family-friendly movie that came out this year?", "entity_names": ["family-friendly", "this year"], "entity_types": ["Genre", "Year"]}
{"sentence": "What is the genre and subgenre of the film 'The Shawshank Redemption' directed by Frank Darabont?", "entity_names": ["The Shawshank Redemption", "Frank Darabont"], "entity_types": ["Title", "Director"]}
{"sentence": "Can you provide me with the genre and subgenre of the movie 'Inception' from 2010?", "entity_names": ["Inception", "2010"], "entity_types": ["Title", "Year"]}
{"sentence": "I would like to know the genre and subgenre of the film 'The Godfather' directed by Francis Ford Coppola.", "entity_names": ["The Godfather", "Francis Ford Coppola"], "entity_types": ["Title", "Director"]}
{"sentence": "\"Can I watch Titanic on any streaming platforms?", "entity_names": ["Titanic"], "entity_types": ["Title"]}
{"sentence": "Which streaming platform has the highest rated action movie from the 1990s?", "entity_names": ["streaming platform", "highest rated", "action movie", "1990s"], "entity_types": ["Genre", "Viewers' Rating", "Genre", "Year"]}
{"sentence": "What is the main genre and subgenre of the movie The Shawshank Redemption?", "entity_names": ["The Shawshank Redemption"], "entity_types": ["Title"]}
{"sentence": "Can you provide the genre and subgenre details for the film Inception directed by Christopher Nolan?", "entity_names": ["Inception", "Christopher Nolan"], "entity_types": ["Title", "Director"]}
{"sentence": "I am interested in knowing the genre and subgenre classifications for the movie The Dark Knight Rises.", "entity_names": ["The Dark Knight Rises"], "entity_types": ["Title"]}
{"sentence": "Can you recommend a film from the 80s that falls into the horror genre?", "entity_names": ["80s", "horror"], "entity_types": ["Year", "Genre"]}
{"sentence": "What's a good mystery movie with a unique plot twist?", "entity_names": ["mystery", "unique plot twist"], "entity_types": ["Genre", "Plot"]}
{"sentence": "I'm looking for a movie that blends romance with comedy. Any suggestions?", "entity_names": ["romance", "comedy"], "entity_types": ["Genre", "Genre"]}
{"sentence": "Hey, what's that movie where Tom Hanks plays a cop in a big city?", "entity_names": ["Tom Hanks", "cop in a big city"], "entity_types": ["Actor", "Plot"]}
{"sentence": "Can you recommend a horror movie with some supernatural elements?", "entity_names": ["horror", "supernatural elements"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Yo, who directed that awesome sci-fi flick with the time-travel storyline?", "entity_names": ["sci-fi", "time-travel storyline"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Are there any action movies playing tonight that were directed by James Cameron", "entity_names": ["action", "James Cameron"], "entity_types": ["Genre", "Director"]}
{"sentence": "Can I buy tickets for a sci-fi film that was released in the 1990s and has a high viewers' rating?", "entity_names": ["sci-fi", "1990s", "high"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "What are the showtimes for the latest thriller movie starring Denzel Washington", "entity_names": ["thriller", "Denzel Washington"], "entity_types": ["Genre", "Actor"]}
{"sentence": "What's the rating for the latest Marvel movie?", "entity_names": ["Marvel movie"], "entity_types": ["Title"]}
{"sentence": "Who directed the highest-grossing film of last year?", "entity_names": ["directed", "highest-grossing film", "last year"], "entity_types": ["Director", "Title", "Year"]}
{"sentence": "Can you recommend a thriller movie with a high IMDb rating?", "entity_names": ["thriller movie", "high IMDb rating"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "I'm thinking of going to see a popular movie tonight, any suggestions?", "entity_names": ["popular movie"], "entity_types": ["Viewers' Rating"]}
{"sentence": "I heard there's a new film out that everyone's talking about, are there any showtimes available?", "entity_names": ["new film"], "entity_types": ["Title"]}
{"sentence": "I'm trying to find a good movie to watch this weekend, any recommendations?", "entity_names": ["good movie"], "entity_types": ["Viewers' Rating"]}
{"sentence": "Can you recommend a highly-rated movie from the 1990s with a thought-provoking plot?", "entity_names": ["highly-rated", "1990s", "thought-provoking"], "entity_types": ["Viewers' Rating", "Year", "Plot"]}
{"sentence": "Who directed the classic film with a stellar performance by Meryl Streep?", "entity_names": ["classic film", "Meryl Streep"], "entity_types": ["Genre", "Actor"]}
{"sentence": "Could you suggest a family-friendly movie with a catchy soundtrack?", "entity_names": ["family-friendly"], "entity_types": ["MPAA Rating"]}
{"sentence": "What is the main genre of the movie The Godfather and who directed it?", "entity_names": ["main genre", "The Godfather", "directed"], "entity_types": ["Genre", "Title", "Director"]}
{"sentence": "Can you recommend a good action movie from the 80s with a strong female lead?", "entity_names": ["action movie", "80s", "strong female lead"], "entity_types": ["Genre", "Year", "Character"]}
{"sentence": "I'm looking for a thriller movie directed by Alfred Hitchcock, can you suggest one?", "entity_names": ["thriller movie", "Alfred Hitchcock"], "entity_types": ["Genre", "Director"]}
{"sentence": "Can you list the nominations for the movie Inception?", "entity_names": ["nominations", "Inception"], "entity_types": ["Review", "Title"]}
{"sentence": "I'm curious about the director behind the camera for the movie Inception. Can you tell me some interesting details about the making of that film?", "entity_names": ["director", "Inception", "making of"], "entity_types": ["Director", "Title", "Plot"]}
{"sentence": "I wonder if there are any famous actors who have starred in classic musical films. Can you give me some insights about the production of those movies?", "entity_names": ["musical"], "entity_types": ["Genre"]}
{"sentence": "I'm interested in learning about the history behind the making of the sci-fi movie Blade Runner. Can you provide me with some details about the creative process involved in its production?", "entity_names": ["sci-fi", "Blade Runner", "production"], "entity_types": ["Genre", "Title", "Plot"]}
{"sentence": "Can you recommend a movie with a high Viewers' Rating that came out in the last year?", "entity_names": ["high Viewers' Rating", "last year"], "entity_types": ["Viewers' Rating", "Year"]}
{"sentence": "Who directed the latest Disney princess movie with a catchy song?", "entity_names": ["latest Disney princess movie", "catchy song"], "entity_types": ["Title", "Song"]}
{"sentence": "I want to watch a movie that is suitable for my age, can you suggest a film with a PG rating?", "entity_names": ["suitable for my age"], "entity_types": ["MPAA Rating"]}
{"sentence": "Hey, what's the coolest action movie of all time?", "entity_names": ["coolest", "action"], "entity_types": ["Viewers' Rating", "Genre"]}
{"sentence": "Tell me about that new superhero movie with the highest ratings.", "entity_names": ["superhero", "highest ratings"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "Who directed the animated film with the talking animals that everyone's been talking about?", "entity_names": ["animated", "talking animals"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Can you tell me a movie with a great soundtrack?", "entity_names": ["great soundtrack"], "entity_types": ["Song"]}
{"sentence": "I'm looking for a movie with memorable music, do you have any recommendations?", "entity_names": ["memorable music"], "entity_types": ["Song"]}
{"sentence": "I'm searching for a film that has a popular song in it, can you help me find one?", "entity_names": ["popular song"], "entity_types": ["Song"]}
{"sentence": "What's a good sci-fi movie to watch?", "entity_names": ["sci-fi"], "entity_types": ["Genre"]}
{"sentence": "Who starred in the top-rated action movie of the year?", "entity_names": ["top-rated", "action", "the year"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "Tell me about the new comedy with the catchy theme song.", "entity_names": ["comedy", "catchy theme song"], "entity_types": ["Genre", "Song"]}
{"sentence": "Hey, have you heard about that new thriller movie with the crazy plot? What's the viewer rating for it?", "entity_names": ["thriller", "crazy plot", "viewer rating"], "entity_types": ["Genre", "Plot", "Viewers' Rating"]}
{"sentence": "I need a recommendation for a really good comedy film from the 90s, you got any in mind?", "entity_names": ["comedy", "90s"], "entity_types": ["Genre", "Year"]}
{"sentence": "Who starred in that horror movie directed by James Wan?", "entity_names": ["horror", "James Wan"], "entity_types": ["Genre", "Director"]}
{"sentence": "Can I find The Dark Knight on any streaming platforms?", "entity_names": ["The Dark Knight"], "entity_types": ["Title"]}
{"sentence": "Is there a movie with Tom Hanks on Netflix?", "entity_names": ["Tom Hanks"], "entity_types": ["Actor"]}
{"sentence": "What are the showtimes for the movie The Shawshank Redemption?", "entity_names": ["The Shawshank Redemption"], "entity_types": ["Title"]}
{"sentence": "Is the movie Casablanca playing in theaters near me?", "entity_names": ["Casablanca"], "entity_types": ["Title"]}
{"sentence": "Could you tell me if there are any available tickets for the movie Lawrence of Arabia?", "entity_names": ["Lawrence of Arabia"], "entity_types": ["Title"]}
{"sentence": "Can you recommend a movie with a strong female lead that has a high Viewers' Rating?", "entity_names": ["strong female lead", "high"], "entity_types": ["Character", "Viewers' Rating"]}
{"sentence": "Who directed the top-rated romantic comedy of the year 2020?", "entity_names": ["romantic comedy", "2020"], "entity_types": ["Genre", "Year"]}
{"sentence": "I'm looking for a movie with an amazing soundtrack, particularly one with a popular pop song. Can you suggest one?", "entity_names": [], "entity_types": []}
{"sentence": "Could you recommend a movie with a memorable soundtrack that features the song 'My Heart Will Go On'?", "entity_names": ["My Heart Will Go On"], "entity_types": ["Song"]}
{"sentence": "I am looking for a movie with a powerful theme song that perfectly complements the storyline. Do you have any suggestions?", "entity_names": ["powerful theme song"], "entity_types": ["Song"]}
{"sentence": "Can you tell me about the awards and nominations received by the film The Shape of Water?", "entity_names": ["The Shape of Water"], "entity_types": ["Title"]}
{"sentence": "Can you tell me the plot of the movie Sleepless in Seattle?", "entity_names": ["plot", "Sleepless in Seattle"], "entity_types": ["Plot", "Title"]}
{"sentence": "What movie with romance as the genre has a captivating and heartwarming plot?", "entity_names": ["romance", "heartwarming"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Could you recommend a movie directed by Nora Ephron with a plot that involves a love story?", "entity_names": ["Nora Ephron", "love story"], "entity_types": ["Director", "Plot"]}
{"sentence": "What is the name of the action-adventure film directed by Steven Spielberg in the 1980s?", "entity_names": ["action-adventure", "Steven Spielberg", "1980s"], "entity_types": ["Genre", "Director", "Year"]}
{"sentence": "Can you tell me about the director of the movie 'The Sound of Music' and any interesting behind-the-scenes stories about its filming?", "entity_names": ["director", "The Sound of Music", "behind-the-scenes stories"], "entity_types": ["Director", "Title", "Plot"]}
{"sentence": "I'm interested in finding a classic movie from the 1950s known for its breathtaking scenery. Do you have any recommendations?", "entity_names": ["1950s", "breathtaking scenery"], "entity_types": ["Year", "Plot"]}
{"sentence": "Can you give me a brief summary of the plot for the movie Inception?", "entity_names": ["Inception"], "entity_types": ["Title"]}
{"sentence": "What is the storyline for the film Shawshank Redemption?", "entity_names": ["Shawshank Redemption"], "entity_types": ["Title"]}
{"sentence": "Could you provide a synopsis for the movie The Godfather?", "entity_names": ["The Godfather"], "entity_types": ["Title"]}
{"sentence": "What's the highest rated movie of all time?", "entity_names": ["highest rated", "movie of all time"], "entity_types": ["Viewers' Rating", "Review"]}
{"sentence": "Who directed the most recent superhero movie with great special effects?", "entity_names": ["superhero movie", "great special effects"], "entity_types": ["Genre", "Review"]}
{"sentence": "What is the plot of the latest romantic comedy directed by Nancy Meyers?", "entity_names": ["romantic comedy", "Nancy Meyers"], "entity_types": ["Genre", "Director"]}
{"sentence": "Can you tell me about the storyline of the highest-rated drama starring Meryl Streep?", "entity_names": ["highest-rated", "drama", "Meryl Streep"], "entity_types": ["Viewers' Rating", "Genre", "Actor"]}
{"sentence": "I'm looking for a movie with a thrilling plot directed by Kathryn Bigelow. Any suggestions?", "entity_names": ["thrilling plot", "Kathryn Bigelow"], "entity_types": ["Plot", "Director"]}
{"sentence": "I'm really curious about the new James Bond movie, I wonder if there's a preview or teaser available?", "entity_names": ["James Bond", "preview or teaser"], "entity_types": ["Character", "Trailer"]}
{"sentence": "I'm looking for a film with a really catchy theme song, any suggestions?", "entity_names": [], "entity_types": []}
{"sentence": "I heard there's a highly acclaimed director working on a new science fiction movie, do you know anything about it?", "entity_names": ["highly acclaimed director", "science fiction"], "entity_types": ["Director", "Genre"]}
{"sentence": "Can you tell me the plot of the movie Frozen?", "entity_names": ["Plot", "Frozen"], "entity_types": ["Genre", "Title"]}
{"sentence": "What children's movie from the 90s has a princess as the main character?", "entity_names": ["children's", "90s", "princess"], "entity_types": ["Genre", "Year", "Character"]}
{"sentence": "Is there a family-friendly movie with a talking animal as the main character?", "entity_names": ["family-friendly", "talking animal"], "entity_types": ["Viewers' Rating", "Character"]}
{"sentence": "Can you tell me the director of the movie Moonlight?", "entity_names": ["Moonlight"], "entity_types": ["Title"]}
{"sentence": "I'm looking for a film with Emma Stone and Ryan Gosling in it, can you recommend something?", "entity_names": ["Emma Stone", "Ryan Gosling"], "entity_types": ["Actor", "Actor"]}
{"sentence": "What genre of movies does Taika Waititi typically direct?", "entity_names": ["Taika Waititi"], "entity_types": ["Director"]}
{"sentence": "Who directed the science fiction movie with a high viewers' rating", "entity_names": ["directed", "science fiction", "viewers' rating"], "entity_types": ["Director", "Genre", "Viewers' Rating"]}
{"sentence": "Which movie features the song 'My Heart Will Go On' and who are the main actors", "entity_names": ["My Heart Will Go On", "main actors"], "entity_types": ["Song", "Actor"]}
{"sentence": "Recommend a thriller film released in the 90s with an R rating and tell me about the plot", "entity_names": ["thriller", "90s", "R rating", "plot"], "entity_types": ["Genre", "Year", "MPAA Rating", "Plot"]}
{"sentence": "Can I see the new Spider-Man film tonight at the nearest theater?", "entity_names": ["Spider-Man"], "entity_types": ["Title"]}
{"sentence": "Can you provide me with the viewers' rating for the film Fargo?", "entity_names": ["viewers' rating", "Fargo"], "entity_types": ["Review", "Title"]}
{"sentence": "What year was the movie Inception released, and who directed it?", "entity_names": ["Inception", "directed"], "entity_types": ["Title", "Director"]}
{"sentence": "I'm interested in seeing a gripping thriller. Could you recommend a movie directed by Alfred Hitchcock?", "entity_names": ["thriller", "Alfred Hitchcock"], "entity_types": ["Genre", "Director"]}
{"sentence": "Has the movie The Shawshank Redemption won any awards?", "entity_names": ["The Shawshank Redemption", "awards"], "entity_types": ["Title", "Genre"]}
{"sentence": "Which movie from 2019 received the most nominations?", "entity_names": ["2019", "most nominations"], "entity_types": ["Year", "Review"]}
{"sentence": "Can you tell me if the film Titanic received any accolades?", "entity_names": ["Titanic", "accolades"], "entity_types": ["Title", "Review"]}
{"sentence": "Can you recommend a romantic comedy film with a high viewers' rating?", "entity_names": ["romantic comedy", "high"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "I'm looking for a sci-fi movie that was released in the 1980s directed by James Cameron", "entity_names": ["sci-fi", "1980s", "James Cameron"], "entity_types": ["Genre", "Year", "Director"]}
{"sentence": "Show me a classic action movie with a lead role by Arnold Schwarzenegger and a song by Queen", "entity_names": ["classic action", "Arnold Schwarzenegger", "Queen"], "entity_types": ["Genre", "Actor", "Song"]}
{"sentence": "Are there any interviews with the cast and crew of the film Titanic that I can watch?", "entity_names": ["Titanic"], "entity_types": ["Title"]}
{"sentence": "I'm not sure if I believe the story behind the film The Social Network, can you give me more information about it?", "entity_names": ["story", "The Social Network"], "entity_types": ["Plot", "Title"]}
{"sentence": "Are there any good action movies playing today?", "entity_names": ["action movies"], "entity_types": ["Genre"]}
{"sentence": "Can I trust the ratings for the new horror film?", "entity_names": ["ratings", "new horror film"], "entity_types": ["Review", "Genre"]}
{"sentence": "Is the director of the romantic comedy film known for making successful movies?", "entity_names": ["director", "romantic comedy film"], "entity_types": ["Director", "Genre"]}
{"sentence": "Can you recommend a horror film with a supernatural plot?", "entity_names": ["horror", "supernatural"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Who directed the latest science fiction movie set in space?", "entity_names": ["latest", "science fiction", "set in space"], "entity_types": ["Year", "Genre", "Plot"]}
{"sentence": "Can you tell me the plot of the latest Avengers movie?", "entity_names": ["Plot", "Avengers"], "entity_types": ["Plot", "Title"]}
{"sentence": "I'm in the mood for a heartwarming movie, can you recommend one with a great plot?", "entity_names": ["heartwarming", "great plot"], "entity_types": ["Genre", "Review"]}
{"sentence": "Which movie directed by Christopher Nolan has the most intriguing plot?", "entity_names": ["Christopher Nolan", "intriguing plot"], "entity_types": ["Director", "Plot"]}
{"sentence": "Can you tell me the name of the actor who played the main character in the movie Inception?", "entity_names": ["actor", "main character", "Inception"], "entity_types": ["Actor", "Character", "Title"]}
{"sentence": "I'm looking for a movie directed by Quentin Tarantino that has received a high viewers' rating.", "entity_names": ["Quentin Tarantino", "high viewers' rating"], "entity_types": ["Director", "Viewers' Rating"]}
{"sentence": "Show me a thriller film from the 1990s with a soundtrack by Hans Zimmer.", "entity_names": ["thriller", "1990s", "Hans Zimmer"], "entity_types": ["Genre", "Year", "Song"]}
{"sentence": "What famous movies feature the song 'Let It Go'?", "entity_names": ["Let It Go"], "entity_types": ["Song"]}
{"sentence": "Can you recommend a movie with an awesome soundtrack for a girls' night in?", "entity_names": ["awesome soundtrack", "girls' night"], "entity_types": ["Review", "Viewers' Rating"]}
{"sentence": "Who directed the movie with the best 80s music soundtrack?", "entity_names": [], "entity_types": []}
{"sentence": "Hey, can you tell me a movie with an awesome soundtrack?", "entity_names": ["awesome soundtrack"], "entity_types": ["Review"]}
{"sentence": "What are some movies with iconic songs in them?", "entity_names": ["iconic songs"], "entity_types": ["Song"]}
{"sentence": "Do you know any films with killer background music?", "entity_names": ["killer background music"], "entity_types": ["Review"]}
{"sentence": "Can you show me the trailer for the latest Star Wars movie?", "entity_names": ["trailer", "Star Wars"], "entity_types": ["Trailer", "Title"]}
{"sentence": "Which movie directed by Christopher Nolan has the highest viewers' rating?", "entity_names": ["Christopher Nolan", "viewers' rating"], "entity_types": ["Director", "Viewers' Rating"]}
{"sentence": "What are some must-see movies with Tom Hanks as the lead actor?", "entity_names": ["must-see", "Tom Hanks"], "entity_types": ["Viewers' Rating", "Actor"]}
{"sentence": "Who directed the science fiction film with time travel that came out in 2014?", "entity_names": ["science fiction", "time travel", "2014"], "entity_types": ["Genre", "Plot", "Year"]}
{"sentence": "Show me a movie with a plot involving a heist and a high viewers' rating", "entity_names": ["heist", "high viewers' rating"], "entity_types": ["Plot", "Viewers' Rating"]}
{"sentence": "I need a movie that has a thrilling plot with a unique and unexpected twist, something in the suspense or psychological thriller genre.", "entity_names": ["thrilling plot", "suspense or psychological thriller"], "entity_types": ["Plot", "Genre"]}
{"sentence": "I'm feeling really tense right now and need to watch a light-hearted comedy from the 1980s with a feel-good love story.", "entity_names": ["light-hearted comedy", "1980s", "feel-good love story"], "entity_types": ["Genre", "Year", "Plot"]}
{"sentence": "Can you tell me what the heck happens in the movie Inception?", "entity_names": ["what the heck happens", "Inception"], "entity_types": ["Plot", "Title"]}
{"sentence": "I need to know the deal with the movie Pulp Fiction, can you give me a quick rundown?", "entity_names": ["the deal", "Pulp Fiction", "quick rundown"], "entity_types": ["Plot", "Title", "Review"]}
{"sentence": "Hey, what's the scoop on The Shawshank Redemption? I want to know what it's all about.", "entity_names": ["the scoop", "The Shawshank Redemption", "what it's all about"], "entity_types": ["Plot", "Title", "Review"]}
{"sentence": "Can you tell me about the awards and nominations received by the movie La La Land?", "entity_names": ["awards and nominations", "La La Land"], "entity_types": ["Review", "Title"]}
{"sentence": "Which movie, directed by Steven Spielberg, has the most nominations for the Academy Awards?", "entity_names": ["Steven Spielberg", "most nominations"], "entity_types": ["Director", "Review"]}
{"sentence": "I'm curious about the Golden Globe Awards won by the movie The Shape of Water. Can you provide me with some information?", "entity_names": ["Golden Globe Awards", "The Shape of Water"], "entity_types": ["Review", "Title"]}
{"sentence": "Can you play the trailer for the movie Inception?", "entity_names": ["trailer", "Inception"], "entity_types": ["Trailer", "Title"]}
{"sentence": "What movie directed by Christopher Nolan has the most thrilling trailer?", "entity_names": ["Christopher Nolan", "thrilling", "trailer"], "entity_types": ["Director", "Review", "Trailer"]}
{"sentence": "Show me a movie teaser with an intense action sequence.", "entity_names": ["teaser", "intense action sequence"], "entity_types": ["Trailer", "Plot"]}
{"sentence": "Can you play the trailer for the latest Marvel movie for me?", "entity_names": ["trailer", "Marvel movie"], "entity_types": ["Trailer", "Title"]}
{"sentence": "What are the top-rated horror movies from the 2010s?", "entity_names": ["top-rated", "horror movies", "2010s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "Who directed the action movie with Tom Cruise that came out last year?", "entity_names": ["directed", "action movie", "Tom Cruise", "last year"], "entity_types": ["Director", "Genre", "Actor", "Year"]}
{"sentence": "Are there any tickets available for the new James Bond movie?", "entity_names": ["James Bond"], "entity_types": ["Title"]}
{"sentence": "Can I really trust that the action movie has good reviews?", "entity_names": ["action", "good reviews"], "entity_types": ["Genre", "Review"]}
{"sentence": "Is the theater showing the romantic comedy film this weekend?", "entity_names": ["romantic comedy"], "entity_types": ["Genre"]}
{"sentence": "I want to know the plot of the film The Shawshank Redemption.", "entity_names": ["The Shawshank Redemption"], "entity_types": ["Title"]}
{"sentence": "Tell me about the storyline of the classic movie Casablanca.", "entity_names": ["Casablanca"], "entity_types": ["Title"]}
{"sentence": "Can you hurry up and show me a trailer for the next James Bond movie already?", "entity_names": ["trailer", "James Bond"], "entity_types": ["Trailer", "Character"]}
{"sentence": "I need to see a teaser for the upcoming Marvel movie right now!", "entity_names": ["teaser", "Marvel"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "I don't have all day, show me the preview for the new action movie with Tom Cruise in it.", "entity_names": ["preview", "action", "Tom Cruise"], "entity_types": ["Trailer", "Genre", "Actor"]}
{"sentence": "Can you tell me which director is known for creating intense psychological thrillers that keep you on the edge of your seat?", "entity_names": ["intense psychological thrillers", "edge of your seat"], "entity_types": ["Genre", "Viewers' Rating"]}
{"sentence": "I'm interested in a movie that showcases the evolution of special effects in the sci-fi genre, can you recommend one?", "entity_names": ["special effects", "sci-fi"], "entity_types": ["Plot", "Genre"]}
{"sentence": "I'm curious to know which actor has worked on the most iconic romantic films throughout their career.", "entity_names": ["iconic romantic films", "career"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Can you recommend a movie with a romantic plot and a song by Whitney Houston?", "entity_names": ["romantic", "Whitney Houston"], "entity_types": ["Genre", "Song"]}
{"sentence": "What is the best-rated movie directed by Nora Ephron that was released in the 1990s?", "entity_names": ["best-rated", "Nora Ephron", "1990s"], "entity_types": ["Viewers' Rating", "Director", "Year"]}
{"sentence": "I'm looking for a classic movie with Audrey Hepburn in the lead role, can you suggest one?", "entity_names": ["classic", "Audrey Hepburn"], "entity_types": ["Genre", "Actor"]}
{"sentence": "Hey, what kind of movies does Quentin Tarantino usually direct? I'm looking for something with lots of action and maybe a bit of dark humor.", "entity_names": ["Quentin Tarantino", "action", "dark humor"], "entity_types": ["Director", "Genre", "Genre"]}
{"sentence": "I'm in the mood for a romantic comedy, but I want something specific. Do you know any good movies set in the 1940s, starring Audrey Hepburn?", "entity_names": ["romantic comedy", "1940s", "Audrey Hepburn"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "I need a recommendation for a scary movie, but nothing too intense. Can you suggest something with a supernatural thriller vibe and maybe a strong female lead?", "entity_names": ["scary movie", "supernatural thriller", "strong female lead"], "entity_types": ["Genre", "Genre", "Character"]}
{"sentence": "What is the viewers' rating for the movie \"The Shawshank Redemption\"?", "entity_names": ["viewers' rating", "The Shawshank Redemption"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "Who directed the classic film \"Casablanca\"?", "entity_names": ["directed", "classic", "Casablanca"], "entity_types": ["Director", "Genre", "Title"]}
{"sentence": "Can you tell me about the making-of documentary for the Lord of the Rings movies?", "entity_names": ["making-of documentary", "Lord of the Rings"], "entity_types": ["Plot", "Title"]}
{"sentence": "I'm looking for a movie that showcases the director's commentary and deleted scenes. Any recommendations?", "entity_names": ["director's commentary", "deleted scenes"], "entity_types": ["Plot", "Plot"]}
{"sentence": "I want to watch a film with a featurette on the special effects. What do you suggest?", "entity_names": ["featurette", "special effects"], "entity_types": ["Plot", "Plot"]}
{"sentence": "Can you tell me about the director and the behind-the-scenes secrets of the movie Inception?", "entity_names": ["director", "behind-the-scenes secrets", "Inception"], "entity_types": ["Director", "Plot", "Title"]}
{"sentence": "What are the best rated movies of 2020, and can you share any interesting making-of stories about them?", "entity_names": ["best rated movies", "2020", "making-of stories"], "entity_types": ["Viewers' Rating", "Year", "Plot"]}
{"sentence": "I'm in the mood for a classic film. Could you recommend a highly acclaimed black and white movie from the 1950s and share some trivia about the director?", "entity_names": ["highly acclaimed", "black and white", "1950s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "Can you provide me with the director of the making-of documentary for the movie Inception?", "entity_names": ["making-of documentary", "Inception"], "entity_types": ["Genre", "Title"]}
{"sentence": "I'm looking for a movie directed by Christopher Nolan with a complex and mind-bending storyline", "entity_names": ["Christopher Nolan", "storyline"], "entity_types": ["Director", "Plot"]}
{"sentence": "I want to watch a drama film from the 90s with a strong female lead and a compelling storyline", "entity_names": ["drama", "90s", "strong female lead", "compelling storyline"], "entity_types": ["Genre", "Year", "Character", "Plot"]}
{"sentence": "I need to know if the film 'Titanic' won any awards or nominations", "entity_names": ["Titanic", "awards or nominations"], "entity_types": ["Title", "Review"]}
{"sentence": "Can you tell me if the movie 'The Shawshank Redemption' received any accolades?", "entity_names": ["The Shawshank Redemption", "accolades"], "entity_types": ["Title", "Review"]}
{"sentence": "I'm so curious to find out if 'The Lord of the Rings: The Return of the King' won any Oscars", "entity_names": ["The Lord of the Rings: The Return of the King", "Oscars"], "entity_types": ["Title", "Review"]}
{"sentence": "I'm not sure if the trailer for the new James Bond film truly represents the storyline. Can you provide a summary of the plot?", "entity_names": ["trailer", "new James Bond film"], "entity_types": ["Trailer", "Title"]}
{"sentence": "Can you recommend any comedy movies that are feel-good and family-friendly?", "entity_names": ["comedy", "feel-good", "family-friendly"], "entity_types": ["Genre", "Viewers' Rating", "Viewers' Rating"]}
{"sentence": "What are some classic action films from the 1980s that are considered iconic?", "entity_names": ["action", "1980s", "iconic"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "Who directed the thriller movie with a post-apocalyptic plot that was released in 2015?", "entity_names": ["thriller", "post-apocalyptic", "2015"], "entity_types": ["Genre", "Plot", "Year"]}
{"sentence": "Can you recommend a coming-of-age movie in the drama genre?", "entity_names": ["coming-of-age", "drama"], "entity_types": ["Genre", "Genre"]}
{"sentence": "What is the best sci-fi film from the 2010s with a strong female lead?", "entity_names": ["sci-fi", "2010s"], "entity_types": ["Genre", "Year"]}
{"sentence": "I'm looking for a feel-good romantic comedy set in a small town. Do you have any recommendations?", "entity_names": ["romantic comedy", "small town"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Can you find me a movie featuring Tom Hanks and Meg Ryan together?", "entity_names": ["Tom Hanks", "Meg Ryan"], "entity_types": ["Actor", "Actor"]}
{"sentence": "Show me a film where Meryl Streep played a historical character.", "entity_names": ["Meryl Streep", "historical character"], "entity_types": ["Actor", "Character"]}
{"sentence": "Who directed the movie Titanic?", "entity_names": ["directed", "Titanic"], "entity_types": ["Director", "Title"]}
{"sentence": "Can you tell me if Meryl Streep starred in any romantic comedies?", "entity_names": ["Meryl Streep", "romantic comedies"], "entity_types": ["Actor", "Genre"]}
{"sentence": "Show me the trailer for the latest Tom Hanks film", "entity_names": ["trailer", "Tom Hanks"], "entity_types": ["Trailer", "Actor"]}
{"sentence": "Are there any showtimes for the new James Bond film, No Time to Die, tonight?", "entity_names": ["No Time to Die"], "entity_types": ["Title"]}
{"sentence": "I'm not sure if I can trust the ratings, but can you tell me the viewers' rating for the movie Dune?", "entity_names": ["Dune"], "entity_types": ["Title"]}
{"sentence": "I'm not convinced that the genre matches my preference, but could you recommend a comedy film directed by Quentin Tarantino?", "entity_names": ["comedy", "Quentin Tarantino"], "entity_types": ["Genre", "Director"]}
{"sentence": "I'm looking for a movie with a mysterious and twisty plot. Any suggestions?", "entity_names": ["mysterious", "twisty"], "entity_types": ["Genre", "Plot"]}
{"sentence": "Who directed the movie The Shawshank Redemption, and what's the plot?", "entity_names": ["directed", "The Shawshank Redemption", "plot"], "entity_types": ["Director", "Title", "Plot"]}
{"sentence": "I'm so bored, is there a movie with the song 'My Heart Will Go On' available on any streaming platform?", "entity_names": ["My Heart Will Go On"], "entity_types": ["Song"]}
{"sentence": "I need something to watch, is there a comedy movie from the 1990s available to stream?", "entity_names": ["comedy", "1990s"], "entity_types": ["Genre", "Year"]}
{"sentence": "I'm bored out of my mind, can I find any action movies directed by James Cameron on any streaming platform?", "entity_names": ["action", "James Cameron"], "entity_types": ["Genre", "Director"]}
{"sentence": "Can you recommend a suspenseful thriller from the 90s with a high Viewers' Rating?", "entity_names": ["suspenseful thriller", "90s", "high"], "entity_types": ["Genre", "Year", "Viewers' Rating"]}
{"sentence": "Who directed the action-packed blockbuster released in 2018 with a thrilling Plot?", "entity_names": ["action-packed blockbuster", "2018", "thrilling"], "entity_types": ["Genre", "Year", "Plot"]}
{"sentence": "Show me a romantic movie from the 2000s with an iconic Song.", "entity_names": ["romantic", "2000s", "iconic"], "entity_types": ["Genre", "Year", "Song"]}
{"sentence": "Hey, what's showing at the theater tonight with Tom Hanks as the main character?", "entity_names": ["Tom Hanks"], "entity_types": ["Actor"]}
{"sentence": "Can you recommend a good action movie playing right now?", "entity_names": ["action"], "entity_types": ["Genre"]}
{"sentence": "Can you tell me the name of the movie that has a behind-the-scenes documentary about its special effects?", "entity_names": ["behind-the-scenes documentary", "special effects"], "entity_types": ["Plot", "Plot"]}
{"sentence": "I'm looking for a movie from the 90s with a director's commentary featuring behind-the-scenes stories.", "entity_names": ["90s", "director's commentary", "behind-the-scenes stories"], "entity_types": ["Year", "Plot", "Plot"]}
{"sentence": "Are there any romantic comedies with a making-of featurette about the chemistry between the lead actors?", "entity_names": ["romantic comedies", "making-of featurette", "chemistry between the lead actors"], "entity_types": ["Genre", "Plot", "Plot"]}
{"sentence": "can you recommend a movie with a memorable soundtrack featuring the song 'Eye of the Tiger'?", "entity_names": ["Eye of the Tiger"], "entity_types": ["Song"]}
{"sentence": "show me a movie with an iconic theme song similar to 'The Imperial March' from Star Wars", "entity_names": ["The Imperial March", "Star Wars"], "entity_types": ["Song", "Title"]}
{"sentence": "which movie has a famous theme song like 'My Heart Will Go On' by Celine Dion from Titanic?", "entity_names": ["'My Heart Will Go On' by Celine Dion", "Titanic"], "entity_types": ["Song", "Title"]}
{"sentence": "\"Can you recommend a movie with a memorable soundtrack featuring the song \"Eye of the Tiger\"?", "entity_names": ["Eye of the Tiger"], "entity_types": ["Song"]}
{"sentence": "Show me a list of movies from the 80s with iconic song soundtracks.", "entity_names": ["80s", "iconic song soundtracks"], "entity_types": ["Year", "Song"]}
{"sentence": "Are there any must-see movies known for their famous musical scores or theme songs?", "entity_names": ["must-see"], "entity_types": ["Viewers' Rating"]}
{"sentence": "Can you show me a teaser for that new action movie?", "entity_names": ["teaser", "action"], "entity_types": ["Trailer", "Genre"]}
{"sentence": "What's the latest movie that everyone's talking about?", "entity_names": [], "entity_types": []}
{"sentence": "Tell me about a classic film with a great soundtrack.", "entity_names": ["classic"], "entity_types": ["Viewers' Rating"]}
{"sentence": "Can you recommend a highly rated comedy from the 1990s?", "entity_names": ["highly rated", "comedy", "1990s"], "entity_types": ["Viewers' Rating", "Genre", "Year"]}
{"sentence": "Who directed the action film with a great soundtrack that came out in 2015?", "entity_names": ["action film", "great soundtrack", "2015"], "entity_types": ["Genre", "Song", "Year"]}
{"sentence": "I heard there's a new thriller movie out. What's the plot and who stars in it?", "entity_names": ["thriller", "plot", "stars"], "entity_types": ["Genre", "Plot", "Actor"]}
{"sentence": "Show me a film starring Leonardo DiCaprio and Tom Hanks.", "entity_names": ["Leonardo DiCaprio", "Tom Hanks"], "entity_types": ["Actor", "Actor"]}
{"sentence": "What is the viewers' rating for the film Inception?", "entity_names": ["viewers' rating", "Inception"], "entity_types": ["Viewers' Rating", "Title"]}
{"sentence": "What is the viewers' rating for the movie Inception? I also want to know the name of the song played in the movie. Hurry up, please!", "entity_names": ["viewers' rating", "Inception", "song"], "entity_types": ["Viewers' Rating", "Title", "Song"]}
{"sentence": "I'm running late for a movie trivia contest! Can you tell me the release year and the plot of the film Titanic? And who was the main actor? I need this information now!", "entity_names": ["release year", "plot", "Titanic", "main actor"], "entity_types": ["Year", "Plot", "Title", "Actor"]}
{"sentence": "I'm looking for showtimes and tickets to a 90s action movie directed by James Cameron", "entity_names": ["90s", "action", "James Cameron"], "entity_types": ["Year", "Genre", "Director"]}
{"sentence": "I want to see a critically acclaimed romantic comedy from the 2000s with Hugh Grant in it, can you help me find showtimes?", "entity_names": ["romantic comedy", "2000s", "Hugh Grant"], "entity_types": ["Genre", "Year", "Actor"]}
{"sentence": "Can you recommend a movie from the 80s with an iconic soundtrack?", "entity_names": ["80s", "iconic soundtrack"], "entity_types": ["Year", "Song"]}
{"sentence": "I'm feeling nostalgic for some classic musicals. Can you suggest a movie with a standout song and dance number?", "entity_names": ["nostalgic", "classic musicals", "standout song and dance number"], "entity_types": ["Viewers' Rating", "Genre", "Song"]}
{"sentence": "I miss the old romantic comedies. Can you tell me a movie with a memorable love song in its soundtrack?", "entity_names": ["old romantic comedies", "memorable love song"], "entity_types": ["Genre", "Song"]}
{"sentence": "Can I find showtimes for the latest romantic comedy with Hugh Grant?", "entity_names": ["romantic comedy", "Hugh Grant"], "entity_types": ["Genre", "Actor"]}
{"sentence": "Is there a theater near me showing a drama film based on a true story?", "entity_names": ["drama", "true story"], "entity_types": ["Genre", "Plot"]}
{"sentence": "I'm looking for a family-friendly movie directed by Steven Spielberg, is there one playing this weekend?", "entity_names": ["family-friendly", "Steven Spielberg"], "entity_types": ["Viewers' Rating", "Director"]}
{"sentence": "Who directed the movie Inception", "entity_names": ["directed", "Inception"], "entity_types": ["Director", "Title"]}
{"sentence": "Can you recommend a film starring Tom Hanks", "entity_names": ["Tom Hanks"], "entity_types": ["Actor"]}
{"sentence": "What is the viewers' rating for The Shawshank Redemption", "entity_names": ["viewers' rating", "The Shawshank Redemption"], "entity_types": ["Viewers' Rating", "Title"]}
