{"sentence": "it was later explained that the song was about Cannabis ('can of this' sounding like Cannabis when said faster) it is uncertain if they were told to change the lyric like they did on P.O.P and HUMANITY.", "tokens": ["it", "was", "later", "explained", "that", "the", "song", "was", "about", "Cannabis", "(", "'", "can", "of", "this", "'", "sounding", "like", "Cannabis", "when", "said", "faster", ")", "it", "is", "uncertain", "if", "they", "were", "told", "to", "change", "the", "lyric", "like", "they", "did", "on", "P.O.P", "and", "HUMANITY", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "B-MISC", "O"]}
{"sentence": "The composition of this brigade remained unchanged from this point until the war's end and included the 62d NY, 93d PVI, 98th PVI and the 102d PVI.", "tokens": ["The", "composition", "of", "this", "brigade", "remained", "unchanged", "from", "this", "point", "until", "the", "war", "'s", "end", "and", "included", "the", "62d", "NY", ",", "93d", "PVI", ",", "98th", "PVI", "and", "the", "102d", "PVI", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "B-ORG", "I-ORG", "O", "B-ORG", "I-ORG", "O", "O", "B-ORG", "I-ORG", "O"]}
{"sentence": "Five months later, however, it did participate in the 2nd Battle of Fredericksburg.", "tokens": ["Five", "months", "later", ",", "however", ",", "it", "did", "participate", "in", "the", "2nd", "Battle", "of", "Fredericksburg", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "O"]}
{"sentence": "By December 1864, they were back in the siege lines of Petersburg.", "tokens": ["By", "December", "1864", ",", "they", "were", "back", "in", "the", "siege", "lines", "of", "Petersburg", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "A train consisting of seven cars left Atlantic City over the West Jersey Railroad bearing a special excursion of Red Men and their friends from Bridgeton and Salem, New Jersey, and had reached the crossing of the Reading Railroad when it was struck by the 5:40 down express train from Philadelphia.", "tokens": ["A", "train", "consisting", "of", "seven", "cars", "left", "Atlantic", "City", "over", "the", "West", "Jersey", "Railroad", "bearing", "a", "special", "excursion", "of", "Red", "Men", "and", "their", "friends", "from", "Bridgeton", "and", "Salem", ",", "New", "Jersey", ",", "and", "had", "reached", "the", "crossing", "of", "the", "Reading", "Railroad", "when", "it", "was", "struck", "by", "the", "5:40", "down", "express", "train", "from", "Philadelphia", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "B-LOC", "I-LOC", "I-LOC", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "His engine had barely cleared the crossing when the locomotive of the Reading train, which left Philadelphia at 5:40 pm, struck the first car full in the centre, throwing it far off the track in a nearby ditch, and completely submerging it.", "tokens": ["His", "engine", "had", "barely", "cleared", "the", "crossing", "when", "the", "locomotive", "of", "the", "Reading", "train", ",", "which", "left", "Philadelphia", "at", "5:40", "pm", ",", "struck", "the", "first", "car", "full", "in", "the", "centre", ",", "throwing", "it", "far", "off", "the", "track", "in", "a", "nearby", "ditch", ",", "and", "completely", "submerging", "it", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Darkness fell quickly, and the work of rescuing the injured and recovering the bodies of the dead was carried out under the glare of huge bonfires.", "tokens": ["Darkness", "fell", "quickly", ",", "and", "the", "work", "of", "rescuing", "the", "injured", "and", "recovering", "the", "bodies", "of", "the", "dead", "was", "carried", "out", "under", "the", "glare", "of", "huge", "bonfires", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Bridgeton and Salem excursionists who escaped injury were brought back to Atlantic City and sent home on a special train several hours later in the evening.", "tokens": ["The", "Bridgeton", "and", "Salem", "excursionists", "who", "escaped", "injury", "were", "brought", "back", "to", "Atlantic", "City", "and", "sent", "home", "on", "a", "special", "train", "several", "hours", "later", "in", "the", "evening", "."], "ner_tags": ["O", "B-LOC", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "County Coroner William McLaughlin, immediately upon hearing of the accident, went to the scene.", "tokens": ["County", "Coroner", "William", "McLaughlin", ",", "immediately", "upon", "hearing", "of", "the", "accident", ",", "went", "to", "the", "scene", "."], "ner_tags": ["O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "When the operator set the \"clear\" signal on the Pennsylvania track, this would automatically set the \"danger\" signal on the Reading track, and vice versa.)", "tokens": ["When", "the", "operator", "set", "the", "\"", "clear", "\"", "signal", "on", "the", "Pennsylvania", "track", ",", "this", "would", "automatically", "set", "the", "\"", "danger", "\"", "signal", "on", "the", "Reading", "track", ",", "and", "vice", "versa", ".", ")"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "His action in running at a speed of forty-five miles an hour past a danger signal seemed inexplicable to those present at the inquest.", "tokens": ["His", "action", "in", "running", "at", "a", "speed", "of", "forty-five", "miles", "an", "hour", "past", "a", "danger", "signal", "seemed", "inexplicable", "to", "those", "present", "at", "the", "inquest", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Farr of the Atlantic City Railroad failed for have his engine under proper control on approaching [the] crossing, and that Tower Man George F. Hauser, in giving the excursion train of the West Jersey Railroad the right of way over a fast express used bad judgment... [and] that Engineer John Greiner of said excursion train erred in not exercising greater care on crossing ahead of said fast express. \"", "tokens": ["Farr", "of", "the", "Atlantic", "City", "Railroad", "failed", "for", "have", "his", "engine", "under", "proper", "control", "on", "approaching", "[", "the", "]", "crossing", ",", "and", "that", "Tower", "Man", "George", "F.", "Hauser", ",", "in", "giving", "the", "excursion", "train", "of", "the", "West", "Jersey", "Railroad", "the", "right", "of", "way", "over", "a", "fast", "express", "used", "bad", "judgment.", "..", "[", "and", "]", "that", "Engineer", "John", "Greiner", "of", "said", "excursion", "train", "erred", "in", "not", "exercising", "greater", "care", "on", "crossing", "ahead", "of", "said", "fast", "express", ".", "\""], "ner_tags": ["B-PER", "O", "O", "B-LOC", "I-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Bowling Green finished the season 8-5 overall and has finished 4-2 in the MAC East.", "tokens": ["Bowling", "Green", "finished", "the", "season", "8-5", "overall", "and", "has", "finished", "4-2", "in", "the", "MAC", "East", "."], "ner_tags": ["B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O"]}
{"sentence": "Returning starters from the 2006 team are six offensive starters and eight defensive starters.", "tokens": ["Returning", "starters", "from", "the", "2006", "team", "are", "six", "offensive", "starters", "and", "eight", "defensive", "starters", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "30 Seconds to Mars' first, self-titled album, produced by Bob Ezrin, was released in 2002 to mixed reviews and achieved sales of just over 100,000.", "tokens": ["30", "Seconds", "to", "Mars", "'", "first", ",", "self-titled", "album", ",", "produced", "by", "Bob", "Ezrin", ",", "was", "released", "in", "2002", "to", "mixed", "reviews", "and", "achieved", "sales", "of", "just", "over", "100,000", "."], "ner_tags": ["B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "On August 31, 2006, the band won the MTV2 Award for \"The Kill\" at the MTV Video Music Awards, one of their two nominations.", "tokens": ["On", "August", "31", ",", "2006", ",", "the", "band", "won", "the", "MTV2", "Award", "for", "\"", "The", "Kill", "\"", "at", "the", "MTV", "Video", "Music", "Awards", ",", "one", "of", "their", "two", "nominations", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The tour was \"environmentally sound\" according to a 2006 interview with then-bassist Matt Wachter.", "tokens": ["The", "tour", "was", "\"", "environmentally", "sound", "\"", "according", "to", "a", "2006", "interview", "with", "then-bassist", "Matt", "Wachter", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O"]}
{"sentence": "As of Spring 2007, the band is supporting The Used as a part of the \"Taste of Chaos\" tour and have scheduled a string of dates in Europe supporting Linkin Park.", "tokens": ["As", "of", "Spring", "2007", ",", "the", "band", "is", "supporting", "The", "Used", "as", "a", "part", "of", "the", "\"", "Taste", "of", "Chaos", "\"", "tour", "and", "have", "scheduled", "a", "string", "of", "dates", "in", "Europe", "supporting", "Linkin", "Park", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "B-ORG", "I-ORG", "O"]}
{"sentence": "On May 2, 2007, the band announced that \"A Beautiful Lie\" had won and would be the next single.", "tokens": ["On", "May", "2", ",", "2007", ",", "the", "band", "announced", "that", "\"", "A", "Beautiful", "Lie", "\"", "had", "won", "and", "would", "be", "the", "next", "single", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "On December 21st, 2007, 30 Seconds to Mars won Fuse's Best of 2007 Award.", "tokens": ["On", "December", "21st", ",", "2007", ",", "30", "Seconds", "to", "Mars", "won", "Fuse", "'s", "Best", "of", "2007", "Award", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "B-ORG", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "O"]}
{"sentence": "The Echelon is a publicity street team for the band 30 Seconds to Mars, which helps in bringing friends to the shows, phoning local radio stations to request the band's songs, putting up posters, posting to band forums or related bulletin boards online, and maintaining magazines or websites dedicated to the band.", "tokens": ["The", "Echelon", "is", "a", "publicity", "street", "team", "for", "the", "band", "30", "Seconds", "to", "Mars", ",", "which", "helps", "in", "bringing", "friends", "to", "the", "shows", ",", "phoning", "local", "radio", "stations", "to", "request", "the", "band", "'s", "songs", ",", "putting", "up", "posters", ",", "posting", "to", "band", "forums", "or", "related", "bulletin", "boards", "online", ",", "and", "maintaining", "magazines", "or", "websites", "dedicated", "to", "the", "band", "."], "ner_tags": ["O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "\"Phase 1: Fortification\" was released on an overseas single for \"Capricorn (A Brand New Name)\".", "tokens": ["\"", "Phase", "1", ":", "Fortification", "\"", "was", "released", "on", "an", "overseas", "single", "for", "\"", "Capricorn", "(", "A", "Brand", "New", "Name", ")", "\"", "."], "ner_tags": ["O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O"]}
{"sentence": "For example, \"Fallen\" was previously called \"Jupiter\", and \"Year Zero\" was previously called \"Hero\".", "tokens": ["For", "example", ",", "\"", "Fallen", "\"", "was", "previously", "called", "\"", "Jupiter", "\"", ",", "and", "\"", "Year", "Zero", "\"", "was", "previously", "called", "\"", "Hero", "\"", "."], "ner_tags": ["O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "B-MISC", "O", "O"]}
{"sentence": "The winners are indicated in bold.", "tokens": ["The", "winners", "are", "indicated", "in", "bold", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "During \"Big Week\" (February 1944), the 65th participated in the assaults against the German Air Force and the German aircraft industry.", "tokens": ["During", "\"", "Big", "Week", "\"", "(", "February", "1944", ")", ",", "the", "65th", "participated", "in", "the", "assaults", "against", "the", "German", "Air", "Force", "and", "the", "German", "aircraft", "industry", "."], "ner_tags": ["O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "B-MISC", "O", "O", "O"]}
{"sentence": "The 65th also controlled the operations of attached tactical fighter squadrons deployed to Spain for temporary duty.", "tokens": ["The", "65th", "also", "controlled", "the", "operations", "of", "attached", "tactical", "fighter", "squadrons", "deployed", "to", "Spain", "for", "temporary", "duty", "."], "ner_tags": ["O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O"]}
{"sentence": "Inactivated on 21 November 1945.", "tokens": ["Inactivated", "on", "21", "November", "1945", "."], "ner_tags": ["O", "O", "O", "O", "O", "O"]}
{"sentence": "Redesignated 65 Air Division and activated on 1 June 1985.", "tokens": ["Redesignated", "65", "Air", "Division", "and", "activated", "on", "1", "June", "1985", "."], "ner_tags": ["O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Fourth Air Force, 27 March 1943; Army Service Forces, 6 May 1943; Eighth Air Force, 2 June 1943; VIII Fighter Command, 4 June 1943; 2 Bombardment (later, 2 Air) Division, 15 September 1944; 3 Air Division, 1 June 1945 -- 21 November 1945.", "tokens": ["Fourth", "Air", "Force", ",", "27", "March", "1943", ";", "Army", "Service", "Forces", ",", "6", "May", "1943", ";", "Eighth", "Air", "Force", ",", "2", "June", "1943", ";", "VIII", "Fighter", "Command", ",", "4", "June", "1943", ";", "2", "Bombardment", "(", "later", ",", "2", "Air", ")", "Division", ",", "15", "September", "1944", ";", "3", "Air", "Division", ",", "1", "June", "1945", "--", "21", "November", "1945", "."], "ner_tags": ["B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Madrid, Spain, 8 April 1957; Torrejon Air Base, Spain, 1 October 1957 -- 1 January 1965.", "tokens": ["Madrid", ",", "Spain", ",", "8", "April", "1957", ";", "Torrejon", "Air", "Base", ",", "Spain", ",", "1", "October", "1957", "--", "1", "January", "1965", "."], "ner_tags": ["B-LOC", "O", "B-LOC", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Unknown, 27 March 1943; Colonel Jesse Auton, 10 April 1943; Brigadier General Ross G. Hoyt, 4 June 1943; Brigadier General Jesse Auton, 6 September 1943; Colonel William L.", "tokens": ["Unknown", ",", "27", "March", "1943", ";", "Colonel", "Jesse", "Auton", ",", "10", "April", "1943", ";", "Brigadier", "General", "Ross", "G.", "Hoyt", ",", "4", "June", "1943", ";", "Brigadier", "General", "Jesse", "Auton", ",", "6", "September", "1943", ";", "Colonel", "William", "L", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "I-PER"]}
{"sentence": "Unknown, 1 June 1985 -- 14 July 1985; Major General John C. Scheidt Jr., 15 July 1985; Brigadier General Philip M. Drew, 12 August 1986; Brigadier General John A. Corder, 3 August 1987; Brigadier General Gerald A. Daniel, 22 April 1988; Brigadier General Glenn A. Profitt II, 24 January 1989 -- unkn.", "tokens": ["Unknown", ",", "1", "June", "1985", "--", "14", "July", "1985", ";", "Major", "General", "John", "C.", "Scheidt", "Jr.", ",", "15", "July", "1985", ";", "Brigadier", "General", "Philip", "M.", "Drew", ",", "12", "August", "1986", ";", "Brigadier", "General", "John", "A.", "Corder", ",", "3", "August", "1987", ";", "Brigadier", "General", "Gerald", "A.", "Daniel", ",", "22", "April", "1988", ";", "Brigadier", "General", "Glenn", "A.", "Profitt", "II", ",", "24", "January", "1989", "--", "unkn", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "I-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "I-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "6PR was founded by electrical company Nicholson's Limited, which ran the station from a studio above its showroom in Barrack Street, Perth.", "tokens": ["6PR", "was", "founded", "by", "electrical", "company", "Nicholson", "'s", "Limited", ",", "which", "ran", "the", "station", "from", "a", "studio", "above", "its", "showroom", "in", "Barrack", "Street", ",", "Perth", "."], "ner_tags": ["B-ORG", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "B-LOC", "O"]}
{"sentence": "In the station's early days there were only four people on the payroll now there are around one hundred.", "tokens": ["In", "the", "station", "'s", "early", "days", "there", "were", "only", "four", "people", "on", "the", "payroll", "now", "there", "are", "around", "one", "hundred", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "In the early eighties the station commenced a more comprehensive news and current affairs format with the introduction of regular programs by Howard Sattler, Bob Maumill and Graham Mabury, which proved extremely popular with listeners.", "tokens": ["In", "the", "early", "eighties", "the", "station", "commenced", "a", "more", "comprehensive", "news", "and", "current", "affairs", "format", "with", "the", "introduction", "of", "regular", "programs", "by", "Howard", "Sattler", ",", "Bob", "Maumill", "and", "Graham", "Mabury", ",", "which", "proved", "extremely", "popular", "with", "listeners", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "During the nineties 6PR continued its dominance in the talk format arena with personalities such as David Christison & Lee Tate, Howard Sattler, Jenny Seaton & Gary Carvolth, Liam Bartlett, Harvey Deegan and Graham Mabury.", "tokens": ["During", "the", "nineties", "6PR", "continued", "its", "dominance", "in", "the", "talk", "format", "arena", "with", "personalities", "such", "as", "David", "Christison", "&", "Lee", "Tate", ",", "Howard", "Sattler", ",", "Jenny", "Seaton", "&", "Gary", "Carvolth", ",", "Liam", "Bartlett", ",", "Harvey", "Deegan", "and", "Graham", "Mabury", "."], "ner_tags": ["O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O"]}
{"sentence": "The local West Australian Football League is also broadcast via a program on 6PR called Bouncing Around.", "tokens": ["The", "local", "West", "Australian", "Football", "League", "is", "also", "broadcast", "via", "a", "program", "on", "6PR", "called", "Bouncing", "Around", "."], "ner_tags": ["O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "B-MISC", "I-MISC", "O"]}
{"sentence": "6PR stopped broadcasting Perth Glory matches after the 2005/2006 A-League season.", "tokens": ["6PR", "stopped", "broadcasting", "Perth", "Glory", "matches", "after", "the", "2005/2006", "A-League", "season", "."], "ner_tags": ["B-ORG", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "B-ORG", "O", "O"]}
{"sentence": "The weekday morning drive time show is also hosted in both English and Chinese.", "tokens": ["The", "weekday", "morning", "drive", "time", "show", "is", "also", "hosted", "in", "both", "English", "and", "Chinese", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "B-MISC", "O"]}
{"sentence": "Aabenraa County corresponded geographically to the former Prussian Kreis Apenrade combined with the larger part of Bov Parish and Fr\u00f8slev municipalit (Handewit / Hanved Parish).", "tokens": ["Aabenraa", "County", "corresponded", "geographically", "to", "the", "former", "Prussian", "Kreis", "Apenrade", "combined", "with", "the", "larger", "part", "of", "Bov", "Parish", "and", "Fr\u00f8slev", "municipalit", "(", "Handewit", "/", "Hanved", "Parish", ")", "."], "ner_tags": ["B-LOC", "I-LOC", "O", "O", "O", "O", "O", "B-MISC", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "B-LOC", "O", "B-LOC", "O", "B-LOC", "I-LOC", "O", "O"]}
{"sentence": "1300) was a French minstrel or trouv\u00e8re, also known as Ro Adam, Li Rois Adenes, Adan le Menestrel, and Adam Rex Menestrallus.", "tokens": ["1300", ")", "was", "a", "French", "minstrel", "or", "trouv\u00e8re", ",", "also", "known", "as", "Ro", "Adam", ",", "Li", "Rois", "Adenes", ",", "Adan", "le", "Menestrel", ",", "and", "Adam", "Rex", "Menestrallus", "."], "ner_tags": ["O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "I-PER", "O", "B-PER", "I-PER", "I-PER", "O", "O", "B-PER", "I-PER", "I-PER", "O"]}
{"sentence": "He went on to play 26 games for Lausanne before leaving for FC Basel in 2007 on a free transfer, signing a 3 year deal.", "tokens": ["He", "went", "on", "to", "play", "26", "games", "for", "Lausanne", "before", "leaving", "for", "FC", "Basel", "in", "2007", "on", "a", "free", "transfer", ",", "signing", "a", "3", "year", "deal", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Agnieszka Lipska Baranowska (1819 - 1890) was a Polish playwright and poet.", "tokens": ["Agnieszka", "Lipska", "Baranowska", "(", "1819", "-", "1890", ")", "was", "a", "Polish", "playwright", "and", "poet", "."], "ner_tags": ["B-PER", "I-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O"]}
{"sentence": "They included Karol Bali\u0144ski, Teofil Lenartowicz, Franciszek Mickiewicz and Eweryst Estkowski.", "tokens": ["They", "included", "Karol", "Bali\u0144ski", ",", "Teofil", "Lenartowicz", ",", "Franciszek", "Mickiewicz", "and", "Eweryst", "Estkowski", "."], "ner_tags": ["O", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O"]}
{"sentence": "She died on December 15, 1890 in Pozna\u0144.", "tokens": ["She", "died", "on", "December", "15", ",", "1890", "in", "Pozna\u0144", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "They were shunned and hated; were allotted separate quarters in towns, called cagoteries, and lived in wretched huts in the country distinct from the villages.", "tokens": ["They", "were", "shunned", "and", "hated", ";", "were", "allotted", "separate", "quarters", "in", "towns", ",", "called", "cagoteries", ",", "and", "lived", "in", "wretched", "huts", "in", "the", "country", "distinct", "from", "the", "villages", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The origin of Agotes (or Cagots) is uncertain.", "tokens": ["The", "origin", "of", "Agotes", "(", "or", "Cagots", ")", "is", "uncertain", "."], "ner_tags": ["O", "O", "O", "B-MISC", "O", "O", "B-MISC", "O", "O", "O", "O"]}
{"sentence": "Thus would arise the confusion between Christians and Cretins.", "tokens": ["Thus", "would", "arise", "the", "confusion", "between", "Christians", "and", "Cretins", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-MISC", "O", "B-MISC", "O"]}
{"sentence": "The team did n't have a main sponsor.", "tokens": ["The", "team", "did", "n't", "have", "a", "main", "sponsor", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Aisby can be spelt as Azeby or even Hazeby; Heydour seems to be currently preferred, but other versions are Haydor, Haydour, and Heydor.", "tokens": ["Aisby", "can", "be", "spelt", "as", "Azeby", "or", "even", "Hazeby", ";", "Heydour", "seems", "to", "be", "currently", "preferred", ",", "but", "other", "versions", "are", "Haydor", ",", "Haydour", ",", "and", "Heydor", "."], "ner_tags": ["B-LOC", "O", "O", "O", "O", "B-LOC", "O", "O", "B-LOC", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "O", "O", "B-LOC", "O"]}
{"sentence": "Wren sat with the Liberal caucus and ran twice in Ontario Liberal Party leadership conventions coming in second place in 1954 with 162 votes when he lost to Farquhar Oliver (himself a former United Farmers of Ontario MPP), and in last place with only seven votes in 1957 losing to John Wintermeyer.", "tokens": ["Wren", "sat", "with", "the", "Liberal", "caucus", "and", "ran", "twice", "in", "Ontario", "Liberal", "Party", "leadership", "conventions", "coming", "in", "second", "place", "in", "1954", "with", "162", "votes", "when", "he", "lost", "to", "Farquhar", "Oliver", "(", "himself", "a", "former", "United", "Farmers", "of", "Ontario", "MPP", ")", ",", "and", "in", "last", "place", "with", "only", "seven", "votes", "in", "1957", "losing", "to", "John", "Wintermeyer", "."], "ner_tags": ["B-PER", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O"]}
{"sentence": "He has proposed that this song be made the Russian national anthem.", "tokens": ["He", "has", "proposed", "that", "this", "song", "be", "made", "the", "Russian", "national", "anthem", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O"]}
{"sentence": "He is known to have a huge vocal range of more than 3 octavs.", "tokens": ["He", "is", "known", "to", "have", "a", "huge", "vocal", "range", "of", "more", "than", "3", "octavs", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "of various hockey clubs met at the Victoria Skating Rink in Montreal at the instigation of the Victoria Hockey Club of Montreal.", "tokens": ["of", "various", "hockey", "clubs", "met", "at", "the", "Victoria", "Skating", "Rink", "in", "Montreal", "at", "the", "instigation", "of", "the", "Victoria", "Hockey", "Club", "of", "Montreal", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "I-LOC", "O", "B-LOC", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "B-LOC", "O"]}
{"sentence": "The left wing, centre and right wing were the forwards, like today.", "tokens": ["The", "left", "wing", ",", "centre", "and", "right", "wing", "were", "the", "forwards", ",", "like", "today", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "An umpire would judge the legality of each score.", "tokens": ["An", "umpire", "would", "judge", "the", "legality", "of", "each", "score", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "With the exception of 1888, the challenge system was exclusively used in the AHAC before the advent of the Stanley Cup, while the series system became the norm in 1893, the first year the Cup was contested.", "tokens": ["With", "the", "exception", "of", "1888", ",", "the", "challenge", "system", "was", "exclusively", "used", "in", "the", "AHAC", "before", "the", "advent", "of", "the", "Stanley", "Cup", ",", "while", "the", "series", "system", "became", "the", "norm", "in", "1893", ",", "the", "first", "year", "the", "Cup", "was", "contested", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O"]}
{"sentence": "However, teams from outside Montreal incurred huge travelling expenses, which led the AHAC to revert back to the challenge system.", "tokens": ["However", ",", "teams", "from", "outside", "Montreal", "incurred", "huge", "travelling", "expenses", ",", "which", "led", "the", "AHAC", "to", "revert", "back", "to", "the", "challenge", "system", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "After accepting the trophy, the hockey club remained adamant about returning the trophy that was presented to them.", "tokens": ["After", "accepting", "the", "trophy", ",", "the", "hockey", "club", "remained", "adamant", "about", "returning", "the", "trophy", "that", "was", "presented", "to", "them", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The issue would be finally resolved in later years, after various attempts at reconciliation.", "tokens": ["The", "issue", "would", "be", "finally", "resolved", "in", "later", "years", ",", "after", "various", "attempts", "at", "reconciliation", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The most recent of his ten books of poems are Tremors - New & Selected Poems (Black Pepper, Melbourne, Australia, 2004) and Speed & Other Liberties (Salt Publishing, Cambridge, UK, 2008).", "tokens": ["The", "most", "recent", "of", "his", "ten", "books", "of", "poems", "are", "Tremors", "-", "New", "&", "Selected", "Poems", "(", "Black", "Pepper", ",", "Melbourne", ",", "Australia", ",", "2004", ")", "and", "Speed", "&", "Other", "Liberties", "(", "Salt", "Publishing", ",", "Cambridge", ",", "UK", ",", "2008", ")", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "B-ORG", "I-ORG", "O", "B-LOC", "O", "B-LOC", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "B-ORG", "I-ORG", "O", "B-LOC", "O", "B-LOC", "O", "O", "O", "O"]}
{"sentence": "She survived, and after years of chemotherapy and radiation therapy was declared to be in remission.", "tokens": ["She", "survived", ",", "and", "after", "years", "of", "chemotherapy", "and", "radiation", "therapy", "was", "declared", "to", "be", "in", "remission", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Whatever her choice, the administrators of George Washington University Hospital -- who were also the liability risk managers -- were disturbed to hear that she had not elected to have an immediate C-section procedure.", "tokens": ["Whatever", "her", "choice", ",", "the", "administrators", "of", "George", "Washington", "University", "Hospital", "--", "who", "were", "also", "the", "liability", "risk", "managers", "--", "were", "disturbed", "to", "hear", "that", "she", "had", "not", "elected", "to", "have", "an", "immediate", "C-section", "procedure", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Nonetheless, and despite medical testimony that such a procedure would probably end Carder's life, an order was issued authorizing the hospital to perform an immediate C-section.", "tokens": ["Nonetheless", ",", "and", "despite", "medical", "testimony", "that", "such", "a", "procedure", "would", "probably", "end", "Carder", "'s", "life", ",", "an", "order", "was", "issued", "authorizing", "the", "hospital", "to", "perform", "an", "immediate", "C-section", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Angela Carder survived her surgery by two days.", "tokens": ["Angela", "Carder", "survived", "her", "surgery", "by", "two", "days", "."], "ner_tags": ["B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "In November of 1990, days before the scheduled trial was to begin, the hospital settled out of court for an undisclosed amount of money and a promise of new hospital policies protecting the rights of pregnant women.", "tokens": ["In", "November", "of", "1990", ",", "days", "before", "the", "scheduled", "trial", "was", "to", "begin", ",", "the", "hospital", "settled", "out", "of", "court", "for", "an", "undisclosed", "amount", "of", "money", "and", "a", "promise", "of", "new", "hospital", "policies", "protecting", "the", "rights", "of", "pregnant", "women", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "With not much to do those days most people resorted to having many house parties, recalls Angelo.", "tokens": ["With", "not", "much", "to", "do", "those", "days", "most", "people", "resorted", "to", "having", "many", "house", "parties", ",", "recalls", "Angelo", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O"]}
{"sentence": "As the nightlife began to grow he began to produce events in famed 1235 (now Mansion), Les Bains (later Chaos), Le Loft & Dunes (later Liquid, now Suite), Cameo Theatre (now Crobar), Penrods (now Nikki Beach / Pearl) and many more.", "tokens": ["As", "the", "nightlife", "began", "to", "grow", "he", "began", "to", "produce", "events", "in", "famed", "1235", "(", "now", "Mansion", ")", ",", "Les", "Bains", "(", "later", "Chaos", ")", ",", "Le", "Loft", "&", "Dunes", "(", "later", "Liquid", ",", "now", "Suite", ")", ",", "Cameo", "Theatre", "(", "now", "Crobar", ")", ",", "Penrods", "(", "now", "Nikki", "Beach", "/", "Pearl", ")", "and", "many", "more", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "B-ORG", "O", "O", "B-ORG", "I-ORG", "O", "O", "B-ORG", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "B-ORG", "O", "O", "B-ORG", "O", "O", "B-ORG", "I-ORG", "O", "O", "B-ORG", "O", "O", "B-ORG", "O", "O", "B-ORG", "I-ORG", "O", "B-ORG", "O", "O", "O", "O", "O"]}
{"sentence": "He has accomplished a repertoire for having the best events & collaborating on some of the most distinguished & successful events in Miami.", "tokens": ["He", "has", "accomplished", "a", "repertoire", "for", "having", "the", "best", "events", "&", "collaborating", "on", "some", "of", "the", "most", "distinguished", "&", "successful", "events", "in", "Miami", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "The list is rather large but to name a few of the events Angelo helped produce include a very bright roster of who's-who, Britney Spears, Jamie Foxx, Paris Hilton, Shaquille O'Neal, Jonathan Vilma, Bryant Mckinnie, Kanye West, P. Diddy, Cameron Diaz, Oliver Stone, Steven Tyler, Neve Campbell, Dwayne The Rock Johnson, Andy Garcia, Shakira, Ricky Martin, Hootie & the Blowfish, Michael Jordan, Roberto Cavalli with DJ's like DJ AM, DJ IRIE, DJ KALED, DJ TIESTO, and many more, creating Super Bowl events, CD release parties, track shows, special performances, birthday soir\u00e9e's, Film release parties, corporate events, Fashion shows, magazine cover release, after award show private events and many more.", "tokens": ["The", "list", "is", "rather", "large", "but", "to", "name", "a", "few", "of", "the", "events", "Angelo", "helped", "produce", "include", "a", "very", "bright", "roster", "of", "who's-who", ",", "Britney", "Spears", ",", "Jamie", "Foxx", ",", "Paris", "Hilton", ",", "Shaquille", "O'Neal", ",", "Jonathan", "Vilma", ",", "Bryant", "Mckinnie", ",", "Kanye", "West", ",", "P.", "Diddy", ",", "Cameron", "Diaz", ",", "Oliver", "Stone", ",", "Steven", "Tyler", ",", "Neve", "Campbell", ",", "Dwayne", "The", "Rock", "Johnson", ",", "Andy", "Garcia", ",", "Shakira", ",", "Ricky", "Martin", ",", "Hootie", "&", "the", "Blowfish", ",", "Michael", "Jordan", ",", "Roberto", "Cavalli", "with", "DJ", "'s", "like", "DJ", "AM", ",", "DJ", "IRIE", ",", "DJ", "KALED", ",", "DJ", "TIESTO", ",", "and", "many", "more", ",", "creating", "Super", "Bowl", "events", ",", "CD", "release", "parties", ",", "track", "shows", ",", "special", "performances", ",", "birthday", "soir\u00e9e's", ",", "Film", "release", "parties", ",", "corporate", "events", ",", "Fashion", "shows", ",", "magazine", "cover", "release", ",", "after", "award", "show", "private", "events", "and", "many", "more", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "I-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "O", "B-PER", "I-PER", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "O", "O", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "He enjoys spending time with his children, teaching, life coaching & mentoring others, although single & not married, having overcome much adversity he continues to fulfill his purpose in life; to change & impact lives as he leads & directs them to their destiny!", "tokens": ["He", "enjoys", "spending", "time", "with", "his", "children", ",", "teaching", ",", "life", "coaching", "&", "mentoring", "others", ",", "although", "single", "&", "not", "married", ",", "having", "overcome", "much", "adversity", "he", "continues", "to", "fulfill", "his", "purpose", "in", "life", ";", "to", "change", "&", "impact", "lives", "as", "he", "leads", "&", "directs", "them", "to", "their", "destiny", "!"], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "If not, much more?", "tokens": ["If", "not", ",", "much", "more", "?"], "ner_tags": ["O", "O", "O", "O", "O", "O"]}
{"sentence": "A Portuguesa is the national anthem of Portugal.", "tokens": ["A", "Portuguesa", "is", "the", "national", "anthem", "of", "Portugal", "."], "ner_tags": ["B-MISC", "I-MISC", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "The words were set to a melody by composer Alfredo Keil and the song soon became popular among people unhappy with what they considered a submissive and humiliating attitude by the Portuguese authorities.", "tokens": ["The", "words", "were", "set", "to", "a", "melody", "by", "composer", "Alfredo", "Keil", "and", "the", "song", "soon", "became", "popular", "among", "people", "unhappy", "with", "what", "they", "considered", "a", "submissive", "and", "humiliating", "attitude", "by", "the", "Portuguese", "authorities", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O"]}
{"sentence": "The second and third verses below were part of the poem, but are not included in the anthem (and are indeed unknown to even exist by a large majority of Portuguese).", "tokens": ["The", "second", "and", "third", "verses", "below", "were", "part", "of", "the", "poem", ",", "but", "are", "not", "included", "in", "the", "anthem", "(", "and", "are", "indeed", "unknown", "to", "even", "exist", "by", "a", "large", "majority", "of", "Portuguese", ")", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O"]}
{"sentence": "The date was a national holiday in many of the former allied nations to allow people to commemorate those members of the armed forces who were killed during war.", "tokens": ["The", "date", "was", "a", "national", "holiday", "in", "many", "of", "the", "former", "allied", "nations", "to", "allow", "people", "to", "commemorate", "those", "members", "of", "the", "armed", "forces", "who", "were", "killed", "during", "war", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Since the 1990s a growing number of people have observed a two-minute silence on 11 November, resulting in both Armistice Day and Remembrance Sunday being commemorated formally in the UK (although in 2007 they fell on the same day).", "tokens": ["Since", "the", "1990s", "a", "growing", "number", "of", "people", "have", "observed", "a", "two-minute", "silence", "on", "11", "November", ",", "resulting", "in", "both", "Armistice", "Day", "and", "Remembrance", "Sunday", "being", "commemorated", "formally", "in", "the", "UK", "(", "although", "in", "2007", "they", "fell", "on", "the", "same", "day", ")", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Mass Transit System of Athens, Greece is the largest mass transit system in all of Greece.", "tokens": ["The", "Mass", "Transit", "System", "of", "Athens", ",", "Greece", "is", "the", "largest", "mass", "transit", "system", "in", "all", "of", "Greece", "."], "ner_tags": ["O", "B-LOC", "I-LOC", "I-LOC", "O", "B-LOC", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "The civil parish is formed by the village of Atwick and the hamlet of Skirlington.", "tokens": ["The", "civil", "parish", "is", "formed", "by", "the", "village", "of", "Atwick", "and", "the", "hamlet", "of", "Skirlington", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "It describes itself as upholding the virtues of freedom.", "tokens": ["It", "describes", "itself", "as", "upholding", "the", "virtues", "of", "freedom", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "They believe that the world is run by a secret society of Jews.", "tokens": ["They", "believe", "that", "the", "world", "is", "run", "by", "a", "secret", "society", "of", "Jews", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O"]}
{"sentence": "The League has been accused of being associated with the now-defunct Australian National Socialist Party.", "tokens": ["The", "League", "has", "been", "accused", "of", "being", "associated", "with", "the", "now-defunct", "Australian", "National", "Socialist", "Party", "."], "ner_tags": ["O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O"]}
{"sentence": "ALOR is also allegedly associated with National Action", "tokens": ["ALOR", "is", "also", "allegedly", "associated", "with", "National", "Action"], "ner_tags": ["B-ORG", "O", "O", "O", "O", "O", "B-ORG", "I-ORG"]}
{"sentence": "It has been associated with the now-defunct New Zealand League of Rights, the Canadian League of Rights and through the British League of Rights also the John Birch Society in the United States.", "tokens": ["It", "has", "been", "associated", "with", "the", "now-defunct", "New", "Zealand", "League", "of", "Rights", ",", "the", "Canadian", "League", "of", "Rights", "and", "through", "the", "British", "League", "of", "Rights", "also", "the", "John", "Birch", "Society", "in", "the", "United", "States", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "B-LOC", "I-LOC", "O"]}
{"sentence": "According to Professor Amos Frumkin of the Hebrew University, the cave is unique in that a thick layer of chalk left it impermeable to water.", "tokens": ["According", "to", "Professor", "Amos", "Frumkin", "of", "the", "Hebrew", "University", ",", "the", "cave", "is", "unique", "in", "that", "a", "thick", "layer", "of", "chalk", "left", "it", "impermeable", "to", "water", "."], "ner_tags": ["O", "O", "O", "B-PER", "I-PER", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The BWF bestows special honours onto players, umpires, sponsors and other individuals for their achievement in badminton or for their contributions to badminton.", "tokens": ["The", "BWF", "bestows", "special", "honours", "onto", "players", ",", "umpires", ",", "sponsors", "and", "other", "individuals", "for", "their", "achievement", "in", "badminton", "or", "for", "their", "contributions", "to", "badminton", "."], "ner_tags": ["O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "There are some places of historic significance around Bandarlapalle like Sitamma Konda and Ramakuppam.", "tokens": ["There", "are", "some", "places", "of", "historic", "significance", "around", "Bandarlapalle", "like", "Sitamma", "Konda", "and", "Ramakuppam", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "B-ORG", "I-ORG", "O", "B-ORG", "O"]}
{"sentence": "Bandarlapalle was very backward and under developed until recently.", "tokens": ["Bandarlapalle", "was", "very", "backward", "and", "under", "developed", "until", "recently", "."], "ner_tags": ["B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Scouting was founded in Bangladesh as part of the British Indian branch of The Scout Association, and continued as part of the Pakistan Boy Scouts Association until the country's divided sections split in 1971 during the Bangladesh Liberation War.", "tokens": ["Scouting", "was", "founded", "in", "Bangladesh", "as", "part", "of", "the", "British", "Indian", "branch", "of", "The", "Scout", "Association", ",", "and", "continued", "as", "part", "of", "the", "Pakistan", "Boy", "Scouts", "Association", "until", "the", "country", "'s", "divided", "sections", "split", "in", "1971", "during", "the", "Bangladesh", "Liberation", "War", "."], "ner_tags": ["O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "O"]}
{"sentence": "During national disasters, such as the many floods that strike Bangladesh, Scouts are called to help with flood control, relocation of citizens and organizing shelters.", "tokens": ["During", "national", "disasters", ",", "such", "as", "the", "many", "floods", "that", "strike", "Bangladesh", ",", "Scouts", "are", "called", "to", "help", "with", "flood", "control", ",", "relocation", "of", "citizens", "and", "organizing", "shelters", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "4.", "tokens": ["4", "."], "ner_tags": ["O", "O"]}
{"sentence": "It includes a Scout Museum inaugurated in November 2007.", "tokens": ["It", "includes", "a", "Scout", "Museum", "inaugurated", "in", "November", "2007", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Battle of Hulao (\u864e\u7262\u4e4b\u6230) of 28 May 621, located just east of Luoyang, was a decisive victory for Li Shimin, through which he was able to subdue two warlords, Dou Jiande and Wang Shichong.", "tokens": ["The", "Battle", "of", "Hulao", "(", "\u864e\u7262\u4e4b\u6230", ")", "of", "28", "May", "621", ",", "located", "just", "east", "of", "Luoyang", ",", "was", "a", "decisive", "victory", "for", "Li", "Shimin", ",", "through", "which", "he", "was", "able", "to", "subdue", "two", "warlords", ",", "Dou", "Jiande", "and", "Wang", "Shichong", "."], "ner_tags": ["O", "B-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O"]}
{"sentence": "Paul Joalland called \"one of the hottest moment of the campaign\".", "tokens": ["Paul", "Joalland", "called", "\"", "one", "of", "the", "hottest", "moment", "of", "the", "campaign", "\"", "."], "ner_tags": ["B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Knowing that the realist army did not have enough mules to move its artillery and provisions, Belgrano planned to use a pincer movement to attack, confidently believing that Pezuela would not provide much resistance.", "tokens": ["Knowing", "that", "the", "realist", "army", "did", "not", "have", "enough", "mules", "to", "move", "its", "artillery", "and", "provisions", ",", "Belgrano", "planned", "to", "use", "a", "pincer", "movement", "to", "attack", ",", "confidently", "believing", "that", "Pezuela", "would", "not", "provide", "much", "resistance", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The realist army reorganized itself and appropriated all of its artillery, continuously shelling the few soldiers left in Belgrano's encampment.", "tokens": ["The", "realist", "army", "reorganized", "itself", "and", "appropriated", "all", "of", "its", "artillery", ",", "continuously", "shelling", "the", "few", "soldiers", "left", "in", "Belgrano", "'s", "encampment", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O"]}
{"sentence": "It is held over Memorial Day weekend in San Jose, California.", "tokens": ["It", "is", "held", "over", "Memorial", "Day", "weekend", "in", "San", "Jose", ",", "California", "."], "ner_tags": ["O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "B-LOC", "I-LOC", "O", "B-LOC", "O"]}
{"sentence": "Co-founded by John McLaughlin and Randall Cooper, the first BayCon in its current existence as \"The San Francisco Bay Area Regional Science Fiction and Fantasy Convention\" was held over Thanksgiving weekend in 1982.", "tokens": ["Co-founded", "by", "John", "McLaughlin", "and", "Randall", "Cooper", ",", "the", "first", "BayCon", "in", "its", "current", "existence", "as", "\"", "The", "San", "Francisco", "Bay", "Area", "Regional", "Science", "Fiction", "and", "Fantasy", "Convention", "\"", "was", "held", "over", "Thanksgiving", "weekend", "in", "1982", "."], "ner_tags": ["O", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O"]}
{"sentence": "From 2008-2010, it will be held at the Hyatt Regency Santa Clara and Santa Clara Convention Center in Santa Clara, California.", "tokens": ["From", "2008-2010", ",", "it", "will", "be", "held", "at", "the", "Hyatt", "Regency", "Santa", "Clara", "and", "Santa", "Clara", "Convention", "Center", "in", "Santa", "Clara", ",", "California", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "B-LOC", "I-LOC", "O", "B-LOC", "I-LOC", "I-LOC", "I-LOC", "O", "B-LOC", "I-LOC", "O", "B-LOC", "O"]}
{"sentence": "In addition to meeting a set of criteria demanding a company be \"representative of the Belgian equity market\", at least 15% of its shares must be considered free float in order to qualify for the index.", "tokens": ["In", "addition", "to", "meeting", "a", "set", "of", "criteria", "demanding", "a", "company", "be", "\"", "representative", "of", "the", "Belgian", "equity", "market", "\"", ",", "at", "least", "15%", "of", "its", "shares", "must", "be", "considered", "free", "float", "in", "order", "to", "qualify", "for", "the", "index", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Benjamin Stoloff (6 October 1895 - 8 September 1960), best known as Ben Stoloff, was an American film director and producer.", "tokens": ["Benjamin", "Stoloff", "(", "6", "October", "1895", "-", "8", "September", "1960", ")", ",", "best", "known", "as", "Ben", "Stoloff", ",", "was", "an", "American", "film", "director", "and", "producer", "."], "ner_tags": ["B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O"]}
{"sentence": "He was elected to the College Football Hall of Fame in 1962.", "tokens": ["He", "was", "elected", "to", "the", "College", "Football", "Hall", "of", "Fame", "in", "1962", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O"]}
{"sentence": "He has finished # 2 overall on the world bodyboarding tour several times.", "tokens": ["He", "has", "finished", "#", "2", "overall", "on", "the", "world", "bodyboarding", "tour", "several", "times", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "\"I just thought, 'You fools, this is what we do',\" says Ben Player, one of only two pro riders who voted to compete.", "tokens": ["\"", "I", "just", "thought", ",", "'", "You", "fools", ",", "this", "is", "what", "we", "do", "'", ",", "\"", "says", "Ben", "Player", ",", "one", "of", "only", "two", "pro", "riders", "who", "voted", "to", "compete", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Six months later he was in Hawaii in January 06 for the final stop on the world tour.", "tokens": ["Six", "months", "later", "he", "was", "in", "Hawaii", "in", "January", "06", "for", "the", "final", "stop", "on", "the", "world", "tour", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Not just the master of Pipeline, but champion of the world...", "tokens": ["Not", "just", "the", "master", "of", "Pipeline", ",", "but", "champion", "of", "the", "world", "..."], "ner_tags": ["O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "This month Player returns to Hawaii to defend his title.", "tokens": ["This", "month", "Player", "returns", "to", "Hawaii", "to", "defend", "his", "title", "."], "ner_tags": ["O", "O", "B-PER", "O", "O", "B-LOC", "O", "O", "O", "O", "O"]}
{"sentence": "A great surfer is a great surfer, no matter what they ride. \"", "tokens": ["A", "great", "surfer", "is", "a", "great", "surfer", ",", "no", "matter", "what", "they", "ride", ".", "\""], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "In fact Ben Player's biography reflects the twists and turns of the phenomenal growth of this comparatively new sport.", "tokens": ["In", "fact", "Ben", "Player", "'s", "biography", "reflects", "the", "twists", "and", "turns", "of", "the", "phenomenal", "growth", "of", "this", "comparatively", "new", "sport", "."], "ner_tags": ["O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "[Surfwear label] Quiksilver totally pulled out of bodyboarding and the company distributing Morey boards went bankrupt, so of course my contract went bust.", "tokens": ["[", "Surfwear", "label", "]", "Quiksilver", "totally", "pulled", "out", "of", "bodyboarding", "and", "the", "company", "distributing", "Morey", "boards", "went", "bankrupt", ",", "so", "of", "course", "my", "contract", "went", "bust", "."], "ner_tags": ["O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "\"The sport is in a much better position now,\" Player will tell you.", "tokens": ["\"", "The", "sport", "is", "in", "a", "much", "better", "position", "now", ",", "\"", "Player", "will", "tell", "you", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O"]}
{"sentence": "I wanted to make something I could show people and say, 'Hey, this is what I do.' \"", "tokens": ["I", "wanted", "to", "make", "something", "I", "could", "show", "people", "and", "say", ",", "'", "Hey", ",", "this", "is", "what", "I", "do", ".", "'", "\""], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "As the street cred of bodyboarding evolved, so did the dynamics of the sport.", "tokens": ["As", "the", "street", "cred", "of", "bodyboarding", "evolved", ",", "so", "did", "the", "dynamics", "of", "the", "sport", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "When he finally came up for air, he was bleeding from both ears from the sheer pressure exerted by the imploding wave.", "tokens": ["When", "he", "finally", "came", "up", "for", "air", ",", "he", "was", "bleeding", "from", "both", "ears", "from", "the", "sheer", "pressure", "exerted", "by", "the", "imploding", "wave", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Then there is Hawaii's Pipeline, the ideal of perfection whether you're standing up or lying down.", "tokens": ["Then", "there", "is", "Hawaii", "'s", "Pipeline", ",", "the", "ideal", "of", "perfection", "whether", "you", "'re", "standing", "up", "or", "lying", "down", "."], "ner_tags": ["O", "O", "O", "B-LOC", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "If the waves stay away and the top pros had to vote their champion in, you could count on Player power winning the day.", "tokens": ["If", "the", "waves", "stay", "away", "and", "the", "top", "pros", "had", "to", "vote", "their", "champion", "in", ",", "you", "could", "count", "on", "Player", "power", "winning", "the", "day", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O"]}
{"sentence": "The neighbouring village of Mece is the centre of one of the oldest industrial-agricultural cooperative farms in Europe Belje.", "tokens": ["The", "neighbouring", "village", "of", "Mece", "is", "the", "centre", "of", "one", "of", "the", "oldest", "industrial-agricultural", "cooperative", "farms", "in", "Europe", "Belje", "."], "ner_tags": ["O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O"]}
{"sentence": "The chain was founded by Herman Salling, but is now a part of Dansk Supermarked.", "tokens": ["The", "chain", "was", "founded", "by", "Herman", "Salling", ",", "but", "is", "now", "a", "part", "of", "Dansk", "Supermarked", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O"]}
{"sentence": "He currently resides in Phoenix, Arizona.", "tokens": ["He", "currently", "resides", "in", "Phoenix", ",", "Arizona", "."], "ner_tags": ["O", "O", "O", "O", "B-LOC", "O", "B-LOC", "O"]}
{"sentence": "In 1996 was named as one of the best lineman in the history of Texas high school football, ranking second on the Houston Chronicle list.", "tokens": ["In", "1996", "was", "named", "as", "one", "of", "the", "best", "lineman", "in", "the", "history", "of", "Texas", "high", "school", "football", ",", "ranking", "second", "on", "the", "Houston", "Chronicle", "list", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "O"]}
{"sentence": "Consensus All-SWC choice in 1969 and 1970 Also was a finalist for Outland Trophy and finished fifth in the voting for the UPI Lineman of the Year, both in 1970.", "tokens": ["Consensus", "All-SWC", "choice", "in", "1969", "and", "1970", "Also", "was", "a", "finalist", "for", "Outland", "Trophy", "and", "finished", "fifth", "in", "the", "voting", "for", "the", "UPI", "Lineman", "of", "the", "Year", ",", "both", "in", "1970", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O"]}
{"sentence": "Played in what has been called \"The Game of the Century\" between # 1 Texas and # 2 Arkansas on December 6, 1970, with the Longhorns winning 15-14.", "tokens": ["Played", "in", "what", "has", "been", "called", "\"", "The", "Game", "of", "the", "Century", "\"", "between", "#", "1", "Texas", "and", "#", "2", "Arkansas", "on", "December", "6", ",", "1970", ",", "with", "the", "Longhorns", "winning", "15-14", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O"]}
{"sentence": "Played defensive end for the Patriots in 1971.", "tokens": ["Played", "defensive", "end", "for", "the", "Patriots", "in", "1971", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-ORG", "O", "O", "O"]}
{"sentence": "NFL saga of an All-American", "tokens": ["NFL", "saga", "of", "an", "All-American"], "ner_tags": ["B-ORG", "O", "O", "O", "O"]}
{"sentence": "While World War II ravaged the Bruins' powerful roster thereafter -- Boston would not win another Cup during his career -- Cowley was the team's sole remaining star.", "tokens": ["While", "World", "War", "II", "ravaged", "the", "Bruins", "'", "powerful", "roster", "thereafter", "--", "Boston", "would", "not", "win", "another", "Cup", "during", "his", "career", "--", "Cowley", "was", "the", "team", "'s", "sole", "remaining", "star", "."], "ner_tags": ["O", "B-MISC", "I-MISC", "I-MISC", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "He was inducted into the Hockey Hall of Fame in 1968.", "tokens": ["He", "was", "inducted", "into", "the", "Hockey", "Hall", "of", "Fame", "in", "1968", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O"]}
{"sentence": "The school was opened in 1959 to serve as a regional high school for 5 parishes.", "tokens": ["The", "school", "was", "opened", "in", "1959", "to", "serve", "as", "a", "regional", "high", "school", "for", "5", "parishes", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Boilerhouse Boys is the stage name of the duo Ben Wolff and Andy Dean, British record producers, remixers, DJs, songwriters, and soundtrack composers.", "tokens": ["The", "Boilerhouse", "Boys", "is", "the", "stage", "name", "of", "the", "duo", "Ben", "Wolff", "and", "Andy", "Dean", ",", "British", "record", "producers", ",", "remixers", ",", "DJs", ",", "songwriters", ",", "and", "soundtrack", "composers", "."], "ner_tags": ["O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Boultham is a village in Lincolnshire, England.", "tokens": ["Boultham", "is", "a", "village", "in", "Lincolnshire", ",", "England", "."], "ner_tags": ["B-LOC", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "O"]}
{"sentence": "Brandhorst is a municipality in the southeast area of the district Wittenberg in Saxony-Anhalt, Germany.", "tokens": ["Brandhorst", "is", "a", "municipality", "in", "the", "southeast", "area", "of", "the", "district", "Wittenberg", "in", "Saxony-Anhalt", ",", "Germany", "."], "ner_tags": ["B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "O", "B-LOC", "O"]}
{"sentence": "B&SR design was based on experience of the Sandy River Railroad.", "tokens": ["B&SR", "design", "was", "based", "on", "experience", "of", "the", "Sandy", "River", "Railroad", "."], "ner_tags": ["B-LOC", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "I-LOC", "O"]}
{"sentence": "Trestles on the Harrison extension had been replaced by earthen fills and plate girder bridges by 1906.", "tokens": ["Trestles", "on", "the", "Harrison", "extension", "had", "been", "replaced", "by", "earthen", "fills", "and", "plate", "girder", "bridges", "by", "1906", "."], "ner_tags": ["O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Two passenger train sets were required for this service.", "tokens": ["Two", "passenger", "train", "sets", "were", "required", "for", "this", "service", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The B&SR was reorganized as the Bridgton and Harrison; but the extension to Harrison was dismantled after locomotive # 8 tipped over when the 35 # rails sagged in 1930.", "tokens": ["The", "B&SR", "was", "reorganized", "as", "the", "Bridgton", "and", "Harrison", ";", "but", "the", "extension", "to", "Harrison", "was", "dismantled", "after", "locomotive", "#", "8", "tipped", "over", "when", "the", "35", "#", "rails", "sagged", "in", "1930", "."], "ner_tags": ["O", "B-LOC", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "There are still signs of the B&SR evident in a few places if one searches carefully for them.", "tokens": ["There", "are", "still", "signs", "of", "the", "B&SR", "evident", "in", "a", "few", "places", "if", "one", "searches", "carefully", "for", "them", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Milepost 0.8: Scribner's - southbound spur.", "tokens": ["Milepost", "0.8", ":", "Scribner", "'s", "-", "southbound", "spur", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Milepost 4: Fill over the north end of Barker pond with granite masonry abutments for a short timber stringer span on the boundary between Hiram and the town of Sebago.", "tokens": ["Milepost", "4", ":", "Fill", "over", "the", "north", "end", "of", "Barker", "pond", "with", "granite", "masonry", "abutments", "for", "a", "short", "timber", "stringer", "span", "on", "the", "boundary", "between", "Hiram", "and", "the", "town", "of", "Sebago", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "The main line ran between Hancock Pond and B&SR superintendent Joseph Bennett's lakeside cottage a short distance south of the covered water tank.", "tokens": ["The", "main", "line", "ran", "between", "Hancock", "Pond", "and", "B&SR", "superintendent", "Joseph", "Bennett", "'s", "lakeside", "cottage", "a", "short", "distance", "south", "of", "the", "covered", "water", "tank", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "B-LOC", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Milepost 13.5: Sandy Creek - agent's station with passing siding serving a sawmill.", "tokens": ["Milepost", "13.5", ":", "Sandy", "Creek", "-", "agent", "'s", "station", "with", "passing", "siding", "serving", "a", "sawmill", "."], "ner_tags": ["O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Milepost 19.5: North Bridgton - agent's station with passing siding serving a separate freight house.", "tokens": ["Milepost", "19.5", ":", "North", "Bridgton", "-", "agent", "'s", "station", "with", "passing", "siding", "serving", "a", "separate", "freight", "house", "."], "ner_tags": ["O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The larger smoking compartment was rebuilt to carry express with a single baggage door on only one side of the car; and the smaller RPO compartment was rebuilt with a few seats to carry passengers.", "tokens": ["The", "larger", "smoking", "compartment", "was", "rebuilt", "to", "carry", "express", "with", "a", "single", "baggage", "door", "on", "only", "one", "side", "of", "the", "car", ";", "and", "the", "smaller", "RPO", "compartment", "was", "rebuilt", "with", "a", "few", "seats", "to", "carry", "passengers", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Flat car # 22 was fitted with a 2,500 gallon oil tank for the Standard Oil Company in 1901.", "tokens": ["Flat", "car", "#", "22", "was", "fitted", "with", "a", "2,500", "gallon", "oil", "tank", "for", "the", "Standard", "Oil", "Company", "in", "1901", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O"]}
{"sentence": "Box cars # 71-73 were the largest box cars on any 2-foot gauge railway in Maine.", "tokens": ["Box", "cars", "#", "71-73", "were", "the", "largest", "box", "cars", "on", "any", "2-foot", "gauge", "railway", "in", "Maine", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "In addition to the main Burnaby campus, it has a campus in downtown Vancouver, a marine campus in North Vancouver, an aerospace and technology campus in Richmond, and a number of small satellite campuses.", "tokens": ["In", "addition", "to", "the", "main", "Burnaby", "campus", ",", "it", "has", "a", "campus", "in", "downtown", "Vancouver", ",", "a", "marine", "campus", "in", "North", "Vancouver", ",", "an", "aerospace", "and", "technology", "campus", "in", "Richmond", ",", "and", "a", "number", "of", "small", "satellite", "campuses", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "BCIT also has a Technology Centre which undertakes and coordinates applied research, and provides technology transfer and commercialization assistance.", "tokens": ["BCIT", "also", "has", "a", "Technology", "Centre", "which", "undertakes", "and", "coordinates", "applied", "research", ",", "and", "provides", "technology", "transfer", "and", "commercialization", "assistance", "."], "ner_tags": ["B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "His lifetime ERA stands at 0.00.", "tokens": ["His", "lifetime", "ERA", "stands", "at", "0.00", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "At the CIA fair, Hayley gets into an argument with Director Avery Bullock.", "tokens": ["At", "the", "CIA", "fair", ",", "Hayley", "gets", "into", "an", "argument", "with", "Director", "Avery", "Bullock", "."], "ner_tags": ["O", "O", "B-ORG", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O"]}
{"sentence": "Bullock then tells Stan to kill Jeff, and when they go to the woods to do it, Jeff gets away with Stan's SUV.", "tokens": ["Bullock", "then", "tells", "Stan", "to", "kill", "Jeff", ",", "and", "when", "they", "go", "to", "the", "woods", "to", "do", "it", ",", "Jeff", "gets", "away", "with", "Stan", "'s", "SUV", "."], "ner_tags": ["B-PER", "O", "O", "B-PER", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "B-PER", "O", "O", "O"]}
{"sentence": "However, Seth MacFarlane and crew believe Picard was a far better captain of the starship Enterprise.", "tokens": ["However", ",", "Seth", "MacFarlane", "and", "crew", "believe", "Picard", "was", "a", "far", "better", "captain", "of", "the", "starship", "Enterprise", "."], "ner_tags": ["O", "O", "B-PER", "I-PER", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O"]}
{"sentence": "Lincoln Reservoir was begun in 1889 and completed in 1901.", "tokens": ["Lincoln", "Reservoir", "was", "begun", "in", "1889", "and", "completed", "in", "1901", "."], "ner_tags": ["B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Anderson had died in 1995 of AIDS.", "tokens": ["Anderson", "had", "died", "in", "1995", "of", "AIDS", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "In the 1960s, Caloi achieved some popularity producing folding bicycles.", "tokens": ["In", "the", "1960s", ",", "Caloi", "achieved", "some", "popularity", "producing", "folding", "bicycles", "."], "ner_tags": ["O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "In 1948, the company changed its name to Ind\u00fastria e Com\u00e9rcio de Bicicletas Caloi S.", "tokens": ["In", "1948", ",", "the", "company", "changed", "its", "name", "to", "Ind\u00fastria", "e", "Com\u00e9rcio", "de", "Bicicletas", "Caloi", "S", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O"]}
{"sentence": "\"Cancer for the Cure\" is a single released by American rock band, Eels.", "tokens": ["\"", "Cancer", "for", "the", "Cure", "\"", "is", "a", "single", "released", "by", "American", "rock", "band", ",", "Eels", "."], "ner_tags": ["O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "B-ORG", "O"]}
{"sentence": "A frequent contributor numerous periodicals, including Canadian Geographic, a selection of her magazine articles was colleted in Curious by Nature (2005).", "tokens": ["A", "frequent", "contributor", "numerous", "periodicals", ",", "including", "Canadian", "Geographic", ",", "a", "selection", "of", "her", "magazine", "articles", "was", "colleted", "in", "Curious", "by", "Nature", "(", "2005", ")", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O"]}
{"sentence": "Soul singers Minnie Riperton and Roberta Flack are among her influences.", "tokens": ["Soul", "singers", "Minnie", "Riperton", "and", "Roberta", "Flack", "are", "among", "her", "influences", "."], "ner_tags": ["O", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O"]}
{"sentence": "On impact with the animal the inertial momentum of the balls injects the drug into the animal, causing torpor and prostration within minutes.", "tokens": ["On", "impact", "with", "the", "animal", "the", "inertial", "momentum", "of", "the", "balls", "injects", "the", "drug", "into", "the", "animal", ",", "causing", "torpor", "and", "prostration", "within", "minutes", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Several models are available.", "tokens": ["Several", "models", "are", "available", "."], "ner_tags": ["O", "O", "O", "O", "O"]}
{"sentence": "He found this in poker and began playing tournaments at the Stanley Casino in Newcastle.", "tokens": ["He", "found", "this", "in", "poker", "and", "began", "playing", "tournaments", "at", "the", "Stanley", "Casino", "in", "Newcastle", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "B-LOC", "O"]}
{"sentence": "Citrone however, refused to go on record and implicate the poker player in question who was a father and a friend.", "tokens": ["Citrone", "however", ",", "refused", "to", "go", "on", "record", "and", "implicate", "the", "poker", "player", "in", "question", "who", "was", "a", "father", "and", "a", "friend", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Currently he is an analyst on Sky Vegas Poker (channel 846)", "tokens": ["Currently", "he", "is", "an", "analyst", "on", "Sky", "Vegas", "Poker", "(", "channel", "846", ")"], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O"]}
{"sentence": "In 1933, Cavallaro joined the jazz band of Al Kavelin, where he quickly became the featured soloist.", "tokens": ["In", "1933", ",", "Cavallaro", "joined", "the", "jazz", "band", "of", "Al", "Kavelin", ",", "where", "he", "quickly", "became", "the", "featured", "soloist", "."], "ner_tags": ["O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "In 1963, he had a million seller hit with the song Sukiyaki.", "tokens": ["In", "1963", ",", "he", "had", "a", "million", "seller", "hit", "with", "the", "song", "Sukiyaki", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O"]}
{"sentence": "Cavallaro also became famous through the medium of radio and film, firstly with his regular program on NBC during the 1940's, The Schaeffer Parade, of which he was the host and later in films where he played himself, starting with Hollywood Canteen (1944), then Diamond Horseshoe (1945) and Out of This World (1945).", "tokens": ["Cavallaro", "also", "became", "famous", "through", "the", "medium", "of", "radio", "and", "film", ",", "firstly", "with", "his", "regular", "program", "on", "NBC", "during", "the", "1940", "'s", ",", "The", "Schaeffer", "Parade", ",", "of", "which", "he", "was", "the", "host", "and", "later", "in", "films", "where", "he", "played", "himself", ",", "starting", "with", "Hollywood", "Canteen", "(", "1944", ")", ",", "then", "Diamond", "Horseshoe", "(", "1945", ")", "and", "Out", "of", "This", "World", "(", "1945", ")", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O"]}
{"sentence": "Beauford was exposed to sandbox and drumming at the age of three.", "tokens": ["Beauford", "was", "exposed", "to", "sandbox", "and", "drumming", "at", "the", "age", "of", "three", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Other members included saxophonist LeRoi Moore, trumpeter John D'Earth, vocalist Dawn Thompson, keyboardist Butch Taylor, and guitarist Tim Reynolds.", "tokens": ["Other", "members", "included", "saxophonist", "LeRoi", "Moore", ",", "trumpeter", "John", "D'Earth", ",", "vocalist", "Dawn", "Thompson", ",", "keyboardist", "Butch", "Taylor", ",", "and", "guitarist", "Tim", "Reynolds", "."], "ner_tags": ["O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "B-PER", "I-PER", "O", "O", "B-PER", "I-PER", "O", "O", "B-PER", "I-PER", "O", "O", "O", "B-PER", "I-PER", "O"]}
{"sentence": "Has a daughter named Breana Symone from his first marriage.", "tokens": ["Has", "a", "daughter", "named", "Breana", "Symone", "from", "his", "first", "marriage", "."], "ner_tags": ["O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O"]}
{"sentence": "Cary Village Mall opened in 1979 with Ivey's and Hudson Belk as anchors.", "tokens": ["Cary", "Village", "Mall", "opened", "in", "1979", "with", "Ivey", "'s", "and", "Hudson", "Belk", "as", "anchors", "."], "ner_tags": ["B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "B-ORG", "I-ORG", "O", "O", "O"]}
{"sentence": "It is also referred to by its former name, Cary Village Mall.", "tokens": ["It", "is", "also", "referred", "to", "by", "its", "former", "name", ",", "Cary", "Village", "Mall", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O"]}
{"sentence": "Nevertheless the network struggled, and ultimately failed, largely because of the reluctance of many cable systems across the United States to give it carriage, limiting severely its ability to attract both viewers and advertisers for its costly lineup of programming.", "tokens": ["Nevertheless", "the", "network", "struggled", ",", "and", "ultimately", "failed", ",", "largely", "because", "of", "the", "reluctance", "of", "many", "cable", "systems", "across", "the", "United", "States", "to", "give", "it", "carriage", ",", "limiting", "severely", "its", "ability", "to", "attract", "both", "viewers", "and", "advertisers", "for", "its", "costly", "lineup", "of", "programming", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Centennial Convention Hall is located in Hayward, California and is part of the Hayward City Center district of Hayward.", "tokens": ["The", "Centennial", "Convention", "Hall", "is", "located", "in", "Hayward", ",", "California", "and", "is", "part", "of", "the", "Hayward", "City", "Center", "district", "of", "Hayward", "."], "ner_tags": ["O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "B-LOC", "O", "B-LOC", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "I-LOC", "O", "O", "B-LOC", "O"]}
{"sentence": "There are also eight meeting rooms totaling 11,448 square feet and a 1,280-square-foot patio.", "tokens": ["There", "are", "also", "eight", "meeting", "rooms", "totaling", "11,448", "square", "feet", "and", "a", "1,280-square-foot", "patio", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Major clients include Sub-Zero, Herman Miller, KaVo / Gendex, SHURE and LifeFitness.", "tokens": ["Major", "clients", "include", "Sub-Zero", ",", "Herman", "Miller", ",", "KaVo", "/", "Gendex", ",", "SHURE", "and", "LifeFitness", "."], "ner_tags": ["O", "O", "O", "B-ORG", "O", "B-ORG", "I-ORG", "O", "B-ORG", "I-ORG", "I-ORG", "O", "B-ORG", "O", "B-ORG", "O"]}
{"sentence": "He signed on August 23, 2001 and did not play professionally until 2002.", "tokens": ["He", "signed", "on", "August", "23", ",", "2001", "and", "did", "not", "play", "professionally", "until", "2002", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "After posting a very respectable 2-0 record and 3.60 ERA in 15 appearances (including 3 starts) in 2003 for the Devil Rays, he went 1-2 with a 4.85 ERA in 26 appearances (4 starts) in 2004 while splitting time with AAA Durham.", "tokens": ["After", "posting", "a", "very", "respectable", "2-0", "record", "and", "3.60", "ERA", "in", "15", "appearances", "(", "including", "3", "starts", ")", "in", "2003", "for", "the", "Devil", "Rays", ",", "he", "went", "1-2", "with", "a", "4.85", "ERA", "in", "26", "appearances", "(", "4", "starts", ")", "in", "2004", "while", "splitting", "time", "with", "AAA", "Durham", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O"]}
{"sentence": "He was recalled by the Athletics after posting an 0.37 ERA in 4 starts for the River Cats and pitched in relief for the rest of the season for the big league club.", "tokens": ["He", "was", "recalled", "by", "the", "Athletics", "after", "posting", "an", "0.37", "ERA", "in", "4", "starts", "for", "the", "River", "Cats", "and", "pitched", "in", "relief", "for", "the", "rest", "of", "the", "season", "for", "the", "big", "league", "club", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Chamicuro is a critically endangered indigenous American language spoken by just a pair of aboriginal people in South America.", "tokens": ["Chamicuro", "is", "a", "critically", "endangered", "indigenous", "American", "language", "spoken", "by", "just", "a", "pair", "of", "aboriginal", "people", "in", "South", "America", "."], "ner_tags": ["B-MISC", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O"]}
{"sentence": "Although he was unsuccessful, he managed to increase NFP's share of the votes by 50%.", "tokens": ["Although", "he", "was", "unsuccessful", ",", "he", "managed", "to", "increase", "NFP", "'s", "share", "of", "the", "votes", "by", "50%", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "He played with the club for four seasons before moving to the VFA where he signed with Coburg.", "tokens": ["He", "played", "with", "the", "club", "for", "four", "seasons", "before", "moving", "to", "the", "VFA", "where", "he", "signed", "with", "Coburg", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O", "O", "B-ORG", "O"]}
{"sentence": "Famous detainees at the prison include Adolphe Feder, Kurt Gerstein, Henri Honor\u00e9 d'Estienn d'Orves and Alfred Dreyfus.", "tokens": ["Famous", "detainees", "at", "the", "prison", "include", "Adolphe", "Feder", ",", "Kurt", "Gerstein", ",", "Henri", "Honor\u00e9", "d'Estienn", "d'Orves", "and", "Alfred", "Dreyfus", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "I-PER", "I-PER", "O", "B-PER", "I-PER", "O"]}
{"sentence": "New script editor Eric Saward requested that Bailey devise another story idea, but neither the submitted outlines for May Time (later renamed Manwatch) nor The Children of Seth were taken further.", "tokens": ["New", "script", "editor", "Eric", "Saward", "requested", "that", "Bailey", "devise", "another", "story", "idea", ",", "but", "neither", "the", "submitted", "outlines", "for", "May", "Time", "(", "later", "renamed", "Manwatch", ")", "nor", "The", "Children", "of", "Seth", "were", "taken", "further", "."], "ner_tags": ["O", "O", "O", "B-PER", "I-PER", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "B-MISC", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O"]}
{"sentence": "SWAT arrived on the scene shortly thereafter, while a negotiator attempted to initiate contact with Penley.", "tokens": ["SWAT", "arrived", "on", "the", "scene", "shortly", "thereafter", ",", "while", "a", "negotiator", "attempted", "to", "initiate", "contact", "with", "Penley", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O"]}
{"sentence": "Police have been criticized for initiating action instead of waiting for Penley's father to arrive so that his father might talk him out of the situation.", "tokens": ["Police", "have", "been", "criticized", "for", "initiating", "action", "instead", "of", "waiting", "for", "Penley", "'s", "father", "to", "arrive", "so", "that", "his", "father", "might", "talk", "him", "out", "of", "the", "situation", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The youth center the Adolescent Life Coaching Center (adolescentlifecoaching.com) opened in honor of Christopher Penley's memory and to give a place for the voice of youth to be heard.", "tokens": ["The", "youth", "center", "the", "Adolescent", "Life", "Coaching", "Center", "(", "adolescentlifecoaching.com", ")", "opened", "in", "honor", "of", "Christopher", "Penley", "'s", "memory", "and", "to", "give", "a", "place", "for", "the", "voice", "of", "youth", "to", "be", "heard", "."], "ner_tags": ["O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Next year, in 1949 the circuit hosted the Zandvoort Grand Prix.", "tokens": ["Next", "year", ",", "in", "1949", "the", "circuit", "hosted", "the", "Zandvoort", "Grand", "Prix", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "O"]}
{"sentence": "One of the major events that is currently held at the circuit, along with DTM and A1GP, is the BP Ultimate Masters of Formula 3, where Formula 3 cars of several national racing series compete with each other (originally called Marlboro Masters, before tobacco advertising ban).", "tokens": ["One", "of", "the", "major", "events", "that", "is", "currently", "held", "at", "the", "circuit", ",", "along", "with", "DTM", "and", "A1GP", ",", "is", "the", "BP", "Ultimate", "Masters", "of", "Formula", "3", ",", "where", "Formula", "3", "cars", "of", "several", "national", "racing", "series", "compete", "with", "each", "other", "(", "originally", "called", "Marlboro", "Masters", ",", "before", "tobacco", "advertising", "ban", ")", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "B-MISC", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The older 'Classic' Zandvoort circuit layout from 1967 is modeled in detail and can be driven in the Grand Prix Legends racing simulation for X86-based pc's.", "tokens": ["The", "older", "'", "Classic", "'", "Zandvoort", "circuit", "layout", "from", "1967", "is", "modeled", "in", "detail", "and", "can", "be", "driven", "in", "the", "Grand", "Prix", "Legends", "racing", "simulation", "for", "X86-based", "pc's", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O"]}
{"sentence": "She had a relationship with Fr\u00e9d\u00e9ric Diefenthal, a French actor.", "tokens": ["She", "had", "a", "relationship", "with", "Fr\u00e9d\u00e9ric", "Diefenthal", ",", "a", "French", "actor", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "B-MISC", "O", "O"]}
{"sentence": "In 1921, the station was reorganized as the Carnegie Institution Department of Genetics.", "tokens": ["In", "1921", ",", "the", "station", "was", "reorganized", "as", "the", "Carnegie", "Institution", "Department", "of", "Genetics", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O"]}
{"sentence": "In October 2007 Watson resigned as a result of controversial remarks about race made to The Sunday Times in the U.K.", "tokens": ["In", "October", "2007", "Watson", "resigned", "as", "a", "result", "of", "controversial", "remarks", "about", "race", "made", "to", "The", "Sunday", "Times", "in", "the", "U.K", "."], "ner_tags": ["O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "B-LOC", "O"]}
{"sentence": "However, this closure came 15 years after its findings were incorporated into the National Origins Act (Immigration Act of 1924), which severely reduced the number of immigrants to America from southern and eastern Europe who, Harry Laughlin testified, were racially inferior than the Nordic immigrants from England and Germany.", "tokens": ["However", ",", "this", "closure", "came", "15", "years", "after", "its", "findings", "were", "incorporated", "into", "the", "National", "Origins", "Act", "(", "Immigration", "Act", "of", "1924", ")", ",", "which", "severely", "reduced", "the", "number", "of", "immigrants", "to", "America", "from", "southern", "and", "eastern", "Europe", "who", ",", "Harry", "Laughlin", "testified", ",", "were", "racially", "inferior", "than", "the", "Nordic", "immigrants", "from", "England", "and", "Germany", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "B-LOC", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "B-LOC", "O", "B-LOC", "O"]}
{"sentence": "These elite meetings cover often controversial topics in molecular biology and neuroscience.", "tokens": ["These", "elite", "meetings", "cover", "often", "controversial", "topics", "in", "molecular", "biology", "and", "neuroscience", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United States Penitentiary II in Coleman is a high security facility in the state of Florida housing male inmates.", "tokens": ["The", "United", "States", "Penitentiary", "II", "in", "Coleman", "is", "a", "high", "security", "facility", "in", "the", "state", "of", "Florida", "housing", "male", "inmates", "."], "ner_tags": ["O", "B-LOC", "I-LOC", "I-LOC", "I-LOC", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O"]}
{"sentence": "The 2007 event is being held from September 10 till September 16.", "tokens": ["The", "2007", "event", "is", "being", "held", "from", "September", "10", "till", "September", "16", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The earliest version of the game began in 1983 as a pick-6-of-36.", "tokens": ["The", "earliest", "version", "of", "the", "game", "began", "in", "1983", "as", "a", "pick-6-of-36", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The jackpot is won if all six match.", "tokens": ["The", "jackpot", "is", "won", "if", "all", "six", "match", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Winners of Classic Lotto (jackpot or otherwise) have one year to claim their prizes.", "tokens": ["Winners", "of", "Classic", "Lotto", "(", "jackpot", "or", "otherwise", ")", "have", "one", "year", "to", "claim", "their", "prizes", "."], "ner_tags": ["O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Connecticut Lottery", "tokens": ["Connecticut", "Lottery"], "ner_tags": ["B-ORG", "I-ORG"]}
{"sentence": "It specializes in the works of Maxfield Parrish, and houses the collection of Alma Gilbert.", "tokens": ["It", "specializes", "in", "the", "works", "of", "Maxfield", "Parrish", ",", "and", "houses", "the", "collection", "of", "Alma", "Gilbert", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O"]}
{"sentence": "Opened in 1847 by the London and South Western Railway (LSWR), it is located on the West Coastway Line which runs between Brighton and Southampton.", "tokens": ["Opened", "in", "1847", "by", "the", "London", "and", "South", "Western", "Railway", "(", "LSWR", ")", ",", "it", "is", "located", "on", "the", "West", "Coastway", "Line", "which", "runs", "between", "Brighton", "and", "Southampton", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "I-LOC", "O", "O", "O", "B-LOC", "O", "B-LOC", "O"]}
{"sentence": "For the rest of the year they toured extensively, supporting The Cult, then Julian Cope, and also played the Glastonbury Festival.", "tokens": ["For", "the", "rest", "of", "the", "year", "they", "toured", "extensively", ",", "supporting", "The", "Cult", ",", "then", "Julian", "Cope", ",", "and", "also", "played", "the", "Glastonbury", "Festival", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O"]}
{"sentence": "At the behest of the British Council, they played an international music festival in Moscow, USSR, and in early 1990, along with Skin Games and Jesus Jones were one the first western bands to tour post-Ceau\u015fescu Romania.", "tokens": ["At", "the", "behest", "of", "the", "British", "Council", ",", "they", "played", "an", "international", "music", "festival", "in", "Moscow", ",", "USSR", ",", "and", "in", "early", "1990", ",", "along", "with", "Skin", "Games", "and", "Jesus", "Jones", "were", "one", "the", "first", "western", "bands", "to", "tour", "post-Ceau\u015fescu", "Romania", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "They again toured the UK, supporting The Mission on a number of UK dates.", "tokens": ["They", "again", "toured", "the", "UK", ",", "supporting", "The", "Mission", "on", "a", "number", "of", "UK", "dates", "."], "ner_tags": ["O", "O", "O", "O", "B-LOC", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "B-LOC", "O", "O"]}
{"sentence": "It also held several sawmills (including one in Gilchrist, Oregon), a wood chip plant, and lumberyards in the Pacific Northwest, with wholesale marketing and sales office in states such as California, Utah, and Arizona.", "tokens": ["It", "also", "held", "several", "sawmills", "(", "including", "one", "in", "Gilchrist", ",", "Oregon", ")", ",", "a", "wood", "chip", "plant", ",", "and", "lumberyards", "in", "the", "Pacific", "Northwest", ",", "with", "wholesale", "marketing", "and", "sales", "office", "in", "states", "such", "as", "California", ",", "Utah", ",", "and", "Arizona", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "O", "O", "B-LOC", "O"]}
{"sentence": "Radio Disney added DaHv to the Radio Disney IncubaTor", "tokens": ["Radio", "Disney", "added", "DaHv", "to", "the", "Radio", "Disney", "IncubaTor"], "ner_tags": ["B-ORG", "I-ORG", "O", "B-PER", "O", "O", "B-MISC", "I-MISC", "I-MISC"]}
{"sentence": "He represented Welland in the Legislative Assembly of Ontario from 1879 to 1883 as a Liberal member.", "tokens": ["He", "represented", "Welland", "in", "the", "Legislative", "Assembly", "of", "Ontario", "from", "1879", "to", "1883", "as", "a", "Liberal", "member", "."], "ner_tags": ["O", "O", "B-LOC", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "B-ORG", "O", "O"]}
{"sentence": "He was born in Satu Mare and debuted in Divizia A with Steaua Bucure\u015fti in 1992.", "tokens": ["He", "was", "born", "in", "Satu", "Mare", "and", "debuted", "in", "Divizia", "A", "with", "Steaua", "Bucure\u015fti", "in", "1992", "."], "ner_tags": ["O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "B-ORG", "I-ORG", "O", "B-ORG", "I-ORG", "O", "O", "O"]}
{"sentence": "Daniel Segal is a British mathematician, currently a Professor of Mathematics at the University of Oxford.", "tokens": ["Daniel", "Segal", "is", "a", "British", "mathematician", ",", "currently", "a", "Professor", "of", "Mathematics", "at", "the", "University", "of", "Oxford", "."], "ner_tags": ["B-PER", "I-PER", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O"]}
{"sentence": "Darkness Death Doom is the sixth album by Runemagick.", "tokens": ["Darkness", "Death", "Doom", "is", "the", "sixth", "album", "by", "Runemagick", "."], "ner_tags": ["B-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "B-ORG", "O"]}
{"sentence": "Later members Arran Murphy is from Wicklow, while newest recruit, Camera Shanahan, comes from Dublin.", "tokens": ["Later", "members", "Arran", "Murphy", "is", "from", "Wicklow", ",", "while", "newest", "recruit", ",", "Camera", "Shanahan", ",", "comes", "from", "Dublin", "."], "ner_tags": ["O", "O", "B-PER", "I-PER", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "With the release of their debut EP, Dead Start Program on October 12 2007, the band embarked on a nationwide tour which began with gigs at Cyprus Avenue in Cork on October 9, Spirit Store in Dundalk on October 10, Tower Records (2pm) / Crawdaddy in Dublin on Saturday October 13 and Roisin Dubh in Galway on Thursday October 18.", "tokens": ["With", "the", "release", "of", "their", "debut", "EP", ",", "Dead", "Start", "Program", "on", "October", "12", "2007", ",", "the", "band", "embarked", "on", "a", "nationwide", "tour", "which", "began", "with", "gigs", "at", "Cyprus", "Avenue", "in", "Cork", "on", "October", "9", ",", "Spirit", "Store", "in", "Dundalk", "on", "October", "10", ",", "Tower", "Records", "(", "2pm", ")", "/", "Crawdaddy", "in", "Dublin", "on", "Saturday", "October", "13", "and", "Roisin", "Dubh", "in", "Galway", "on", "Thursday", "October", "18", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "B-LOC", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "B-LOC", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "B-ORG", "O", "B-LOC", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "B-LOC", "O", "O", "O", "O", "O"]}
{"sentence": "DATECS Ltd., was founded in 1990 by a group of research fellows from the Institute of Applied Cybernetics at the Bulgarian Academy of Sciences.", "tokens": ["DATECS", "Ltd.", ",", "was", "founded", "in", "1990", "by", "a", "group", "of", "research", "fellows", "from", "the", "Institute", "of", "Applied", "Cybernetics", "at", "the", "Bulgarian", "Academy", "of", "Sciences", "."], "ner_tags": ["B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O"]}
{"sentence": "The company produced its first electronic cash register - the Datecs MP500 - in 1996, followed by the model with the thermal printer, the MP500T, a year later.", "tokens": ["The", "company", "produced", "its", "first", "electronic", "cash", "register", "-", "the", "Datecs", "MP500", "-", "in", "1996", ",", "followed", "by", "the", "model", "with", "the", "thermal", "printer", ",", "the", "MP500T", ",", "a", "year", "later", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O"]}
{"sentence": "In 2004 he co-wrote a controversial paper with prominent intelligent design proponent Michael Behe.", "tokens": ["In", "2004", "he", "co-wrote", "a", "controversial", "paper", "with", "prominent", "intelligent", "design", "proponent", "Michael", "Behe", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O"]}
{"sentence": "Snoke was elected a Fellow of the American Scientific Affiliation in 2006.", "tokens": ["Snoke", "was", "elected", "a", "Fellow", "of", "the", "American", "Scientific", "Affiliation", "in", "2006", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O"]}
{"sentence": "At the Kitzmiller v. Dover Area School District trial later that year it was the one article referenced by both Behe and Scott Minnich as supporting intelligent design.", "tokens": ["At", "the", "Kitzmiller", "v.", "Dover", "Area", "School", "District", "trial", "later", "that", "year", "it", "was", "the", "one", "article", "referenced", "by", "both", "Behe", "and", "Scott", "Minnich", "as", "supporting", "intelligent", "design", "."], "ner_tags": ["O", "O", "B-PER", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O"]}
{"sentence": "It hosted the Patriot League men's basketball tournament title game in 1993.", "tokens": ["It", "hosted", "the", "Patriot", "League", "men", "'s", "basketball", "tournament", "title", "game", "in", "1993", "."], "ner_tags": ["O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Defamation Action League's strategy involves \"protesting\" the companies which host ripoffreport.com and badbusinessbureau.com by setting up \"protest sites\" and engaging in annoyance strategies such as placing classified ads with the companies' telephone numbers for products they are n't selling or sending mass emails to other customers claiming that the hosting companies support child pornography and are actively engaged in pedophilia.", "tokens": ["The", "Defamation", "Action", "League", "'s", "strategy", "involves", "\"", "protesting", "\"", "the", "companies", "which", "host", "ripoffreport.com", "and", "badbusinessbureau.com", "by", "setting", "up", "\"", "protest", "sites", "\"", "and", "engaging", "in", "annoyance", "strategies", "such", "as", "placing", "classified", "ads", "with", "the", "companies", "'", "telephone", "numbers", "for", "products", "they", "are", "n't", "selling", "or", "sending", "mass", "emails", "to", "other", "customers", "claiming", "that", "the", "hosting", "companies", "support", "child", "pornography", "and", "are", "actively", "engaged", "in", "pedophilia", "."], "ner_tags": ["O", "B-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "He handcrafts everything from reversible dualette guitars to harp guitars.", "tokens": ["He", "handcrafts", "everything", "from", "reversible", "dualette", "guitars", "to", "harp", "guitars", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "In 1971, he began to design and build his own guitars, originally bearing the name Del's.", "tokens": ["In", "1971", ",", "he", "began", "to", "design", "and", "build", "his", "own", "guitars", ",", "originally", "bearing", "the", "name", "Del's", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O"]}
{"sentence": "At one point, so many orders had been called in that there was a four-year waiting list for a Langejans guitar.", "tokens": ["At", "one", "point", ",", "so", "many", "orders", "had", "been", "called", "in", "that", "there", "was", "a", "four-year", "waiting", "list", "for", "a", "Langejans", "guitar", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O"]}
{"sentence": "His ability to combine jazz and rock has put him among the top Indonesian guitarists.", "tokens": ["His", "ability", "to", "combine", "jazz", "and", "rock", "has", "put", "him", "among", "the", "top", "Indonesian", "guitarists", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O"]}
{"sentence": "While there, he came across Jack Lesmana, a jazz maestro and father of jazz musician Indra Lesmana, who taught Budjana the philosophy of jazz.", "tokens": ["While", "there", ",", "he", "came", "across", "Jack", "Lesmana", ",", "a", "jazz", "maestro", "and", "father", "of", "jazz", "musician", "Indra", "Lesmana", ",", "who", "taught", "Budjana", "the", "philosophy", "of", "jazz", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O"]}
{"sentence": "Though many contestants participated, it was Budjana and his fellow bandmates who outperformed the competition and won.", "tokens": ["Though", "many", "contestants", "participated", ",", "it", "was", "Budjana", "and", "his", "fellow", "bandmates", "who", "outperformed", "the", "competition", "and", "won", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "He also sat in with many other bands --- including the Jimmy Manopo Band, Erwin Gutawa, Elfa's Big Band and Twilite Orchestra --- and participated in the North Sea Jazz Festival, a world jazz convention in Den Haag, the Netherlands.", "tokens": ["He", "also", "sat", "in", "with", "many", "other", "bands", "---", "including", "the", "Jimmy", "Manopo", "Band", ",", "Erwin", "Gutawa", ",", "Elfa", "'s", "Big", "Band", "and", "Twilite", "Orchestra", "---", "and", "participated", "in", "the", "North", "Sea", "Jazz", "Festival", ",", "a", "world", "jazz", "convention", "in", "Den", "Haag", ",", "the", "Netherlands", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O", "B-ORG", "I-ORG", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "B-LOC", "O"]}
{"sentence": "For his rig, Dewa Budjana mainly uses Mesa Boogie rectifier and Carvin Legacy amplifier that are connected to the Line 6 Ax2 212.", "tokens": ["For", "his", "rig", ",", "Dewa", "Budjana", "mainly", "uses", "Mesa", "Boogie", "rectifier", "and", "Carvin", "Legacy", "amplifier", "that", "are", "connected", "to", "the", "Line", "6", "Ax2", "212", "."], "ner_tags": ["O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "B-ORG", "I-ORG", "O", "O", "B-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Bright, passionate and fluent in Spanish, he ferried correspondence from the Ilocos to Manila, journeys that gave him his first glimpse of colonial injustice and that planted the seeds of rebellion.", "tokens": ["Bright", ",", "passionate", "and", "fluent", "in", "Spanish", ",", "he", "ferried", "correspondence", "from", "the", "Ilocos", "to", "Manila", ",", "journeys", "that", "gave", "him", "his", "first", "glimpse", "of", "colonial", "injustice", "and", "that", "planted", "the", "seeds", "of", "rebellion", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "He then began an association with the British who appointed him governor of the Ilocos on their behalf and promised him military reinforcement.", "tokens": ["He", "then", "began", "an", "association", "with", "the", "British", "who", "appointed", "him", "governor", "of", "the", "Ilocos", "on", "their", "behalf", "and", "promised", "him", "military", "reinforcement", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Riding a fast horse, Gabriela led her troops towards Vigan, but she was driven back.", "tokens": ["Riding", "a", "fast", "horse", ",", "Gabriela", "led", "her", "troops", "towards", "Vigan", ",", "but", "she", "was", "driven", "back", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Since indeterminacy as a compositional parameter has been a constant in our work, this often produces what we regard as interesting new developments.", "tokens": ["Since", "indeterminacy", "as", "a", "compositional", "parameter", "has", "been", "a", "constant", "in", "our", "work", ",", "this", "often", "produces", "what", "we", "regard", "as", "interesting", "new", "developments", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amber, from the CD version of the album, was used as the soundtrack to The Shop Floor, a video production by the artist Francis Gomila (UK, 2003).", "tokens": ["Amber", ",", "from", "the", "CD", "version", "of", "the", "album", ",", "was", "used", "as", "the", "soundtrack", "to", "The", "Shop", "Floor", ",", "a", "video", "production", "by", "the", "artist", "Francis", "Gomila", "(", "UK", ",", "2003", ")", "."], "ner_tags": ["B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "B-LOC", "O", "O", "O", "O"]}
{"sentence": "The drive to the village is scenic, with paddy fields and wooded hills lacing the roadway, very typical of the Goan countryside.", "tokens": ["The", "drive", "to", "the", "village", "is", "scenic", ",", "with", "paddy", "fields", "and", "wooded", "hills", "lacing", "the", "roadway", ",", "very", "typical", "of", "the", "Goan", "countryside", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O"]}
{"sentence": "Shree Saptakoteshwar was the patron Deity of the Kadambas.", "tokens": ["Shree", "Saptakoteshwar", "was", "the", "patron", "Deity", "of", "the", "Kadambas", "."], "ner_tags": ["B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "B-PER", "O"]}
{"sentence": "Fr. Sousa testifies that \"Divar was as much venerated by the Hindu Brahmins as the Holy land by us, on account of a temple of many indulgences and pilgrimages...\" Rui Gomez Pereira details further stating, \"The linga of the temple, made of five metals - gold, silver, copper, iron and bronze\" was later relocated in Bicholim.", "tokens": ["Fr.", "Sousa", "testifies", "that", "\"", "Divar", "was", "as", "much", "venerated", "by", "the", "Hindu", "Brahmins", "as", "the", "Holy", "land", "by", "us", ",", "on", "account", "of", "a", "temple", "of", "many", "indulgences", "and", "pilgrimages.", "..", "\"", "Rui", "Gomez", "Pereira", "details", "further", "stating", ",", "\"", "The", "linga", "of", "the", "temple", ",", "made", "of", "five", "metals", "-", "gold", ",", "silver", ",", "copper", ",", "iron", "and", "bronze", "\"", "was", "later", "relocated", "in", "Bicholim", "."], "ner_tags": ["O", "B-PER", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "The famous Bonderam festival is celebrated in Divar on the fourth Saturday of August during the monsoon with great fanfare and attended by thousands of tourists and locals.", "tokens": ["The", "famous", "Bonderam", "festival", "is", "celebrated", "in", "Divar", "on", "the", "fourth", "Saturday", "of", "August", "during", "the", "monsoon", "with", "great", "fanfare", "and", "attended", "by", "thousands", "of", "tourists", "and", "locals", "."], "ner_tags": ["O", "O", "B-MISC", "I-MISC", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "On this day a carnival ambience is created.", "tokens": ["On", "this", "day", "a", "carnival", "ambience", "is", "created", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "In keeping with his promise, he donated the bell to the church of Divar, which is located on a high hillock.", "tokens": ["In", "keeping", "with", "his", "promise", ",", "he", "donated", "the", "bell", "to", "the", "church", "of", "Divar", ",", "which", "is", "located", "on", "a", "high", "hillock", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "She married the merchant and banker Simon Veit in 1783.", "tokens": ["She", "married", "the", "merchant", "and", "banker", "Simon", "Veit", "in", "1783", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O"]}
{"sentence": "In 1801 her novel \"Florentin\" was published anonymously by Schlegel.", "tokens": ["In", "1801", "her", "novel", "\"", "Florentin", "\"", "was", "published", "anonymously", "by", "Schlegel", "."], "ner_tags": ["O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "B-PER", "O"]}
{"sentence": "Friedrich died in 1829, after which she moved to Frankfurt am Main.", "tokens": ["Friedrich", "died", "in", "1829", ",", "after", "which", "she", "moved", "to", "Frankfurt", "am", "Main", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "I-LOC", "O"]}
{"sentence": "Dorothea's brother, Joseph, was a friend and sponsor of Alexander von Humboldt, the great naturalist and ethnologist.", "tokens": ["Dorothea", "'s", "brother", ",", "Joseph", ",", "was", "a", "friend", "and", "sponsor", "of", "Alexander", "von", "Humboldt", ",", "the", "great", "naturalist", "and", "ethnologist", "."], "ner_tags": ["B-PER", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The emancipation of European Jewry, in which she and her family played the greatest imaginable role, became the main target of the Third Reich and its Nuremberg Laws.", "tokens": ["The", "emancipation", "of", "European", "Jewry", ",", "in", "which", "she", "and", "her", "family", "played", "the", "greatest", "imaginable", "role", ",", "became", "the", "main", "target", "of", "the", "Third", "Reich", "and", "its", "Nuremberg", "Laws", "."], "ner_tags": ["O", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "B-MISC", "I-MISC", "O"]}
{"sentence": "It was probably through de Sta\u00eb's husband, a Swedish Count, that the Schlegel's were granted a title of nobility in the Swedish court.", "tokens": ["It", "was", "probably", "through", "de", "Sta\u00eb", "'s", "husband", ",", "a", "Swedish", "Count", ",", "that", "the", "Schlegel", "'s", "were", "granted", "a", "title", "of", "nobility", "in", "the", "Swedish", "court", "."], "ner_tags": ["O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O"]}
{"sentence": "Dreams From A Dirt Nap is a compilation album by the band The Newlydeads containing 18 songs recorded between the years 1997 and 2001.", "tokens": ["Dreams", "From", "A", "Dirt", "Nap", "is", "a", "compilation", "album", "by", "the", "band", "The", "Newlydeads", "containing", "18", "songs", "recorded", "between", "the", "years", "1997", "and", "2001", "."], "ner_tags": ["B-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Bobick's younger brother Rodney Bobick was also a heavyweight boxer, though less successful, and died in a single car crash in 1977.", "tokens": ["Bobick", "'s", "younger", "brother", "Rodney", "Bobick", "was", "also", "a", "heavyweight", "boxer", ",", "though", "less", "successful", ",", "and", "died", "in", "a", "single", "car", "crash", "in", "1977", "."], "ner_tags": ["B-PER", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Bobick trained hard to start his pro career, which did not begin until the following spring, 1973.", "tokens": ["Bobick", "trained", "hard", "to", "start", "his", "pro", "career", ",", "which", "did", "not", "begin", "until", "the", "following", "spring", ",", "1973", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "He had 10 more fights in 1974, winning them all again, eight by KO.", "tokens": ["He", "had", "10", "more", "fights", "in", "1974", ",", "winning", "them", "all", "again", ",", "eight", "by", "KO", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Bobick gained top-10 ranking in 1975 with eight more fights and wins, all again by KO.", "tokens": ["Bobick", "gained", "top-10", "ranking", "in", "1975", "with", "eight", "more", "fights", "and", "wins", ",", "all", "again", "by", "KO", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The two punchers went right after each other.", "tokens": ["The", "two", "punchers", "went", "right", "after", "each", "other", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Despite that, he was back in the ring two months later, winning a rematch with Scott LeDoux.", "tokens": ["Despite", "that", ",", "he", "was", "back", "in", "the", "ring", "two", "months", "later", ",", "winning", "a", "rematch", "with", "Scott", "LeDoux", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O"]}
{"sentence": "His pro record was 48-4 with 42 KOs with wins over several notable contenders.", "tokens": ["His", "pro", "record", "was", "48-4", "with", "42", "KOs", "with", "wins", "over", "several", "notable", "contenders", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "His brother Rodney also was a professional boxer with a 37-7 record.", "tokens": ["His", "brother", "Rodney", "also", "was", "a", "professional", "boxer", "with", "a", "37-7", "record", "."], "ner_tags": ["O", "O", "B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Eleven Free Dutch Submarines operated out of Fremantle after the invasion of Java, the joint No. 18 (Netherlands East Indies) Squadron RAAF, established in 1942 and No. 120 formed at Canberra, was a combined Dutch and Australian Squadron with dual command, it used B-25 Mitchell bombers, paid for by the Dutch Government before the war.", "tokens": ["Eleven", "Free", "Dutch", "Submarines", "operated", "out", "of", "Fremantle", "after", "the", "invasion", "of", "Java", ",", "the", "joint", "No.", "18", "(", "Netherlands", "East", "Indies", ")", "Squadron", "RAAF", ",", "established", "in", "1942", "and", "No.", "120", "formed", "at", "Canberra", ",", "was", "a", "combined", "Dutch", "and", "Australian", "Squadron", "with", "dual", "command", ",", "it", "used", "B-25", "Mitchell", "bombers", ",", "paid", "for", "by", "the", "Dutch", "Government", "before", "the", "war", "."], "ner_tags": ["O", "O", "B-MISC", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "B-ORG", "I-ORG", "O", "B-ORG", "I-ORG", "I-ORG", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "B-LOC", "O", "O", "O", "O", "B-MISC", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O"]}
{"sentence": "The East Germany national handball team was the national handball team of the East Germany.", "tokens": ["The", "East", "Germany", "national", "handball", "team", "was", "the", "national", "handball", "team", "of", "the", "East", "Germany", "."], "ner_tags": ["O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O"]}
{"sentence": "Currently, Eberhard Faber also produces FIMO, Efaplast and Aquasoft modelling clay, which are products used in the Hobby & Crafts market, in kindergartens, schools and by artists.", "tokens": ["Currently", ",", "Eberhard", "Faber", "also", "produces", "FIMO", ",", "Efaplast", "and", "Aquasoft", "modelling", "clay", ",", "which", "are", "products", "used", "in", "the", "Hobby", "&", "Crafts", "market", ",", "in", "kindergartens", ",", "schools", "and", "by", "artists", "."], "ner_tags": ["O", "O", "B-ORG", "I-ORG", "O", "O", "B-MISC", "O", "B-MISC", "O", "B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The VI was born 9/23/06.", "tokens": ["The", "VI", "was", "born", "9/23/06", "."], "ner_tags": ["O", "B-PER", "O", "O", "O", "O"]}
{"sentence": "In 1875 he became director of the Burgh\u00f6lzli asylum, as well as professor of psychiatry at the University of Zurich.", "tokens": ["In", "1875", "he", "became", "director", "of", "the", "Burgh\u00f6lzli", "asylum", ",", "as", "well", "as", "professor", "of", "psychiatry", "at", "the", "University", "of", "Zurich", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "O"]}
{"sentence": "They identified the brains' \"motor strip\" which is a vertical strip of brain tissue on the cerebrum in the back of the frontal lobe which controls different muscles in the body.", "tokens": ["They", "identified", "the", "brains", "'", "\"", "motor", "strip", "\"", "which", "is", "a", "vertical", "strip", "of", "brain", "tissue", "on", "the", "cerebrum", "in", "the", "back", "of", "the", "frontal", "lobe", "which", "controls", "different", "muscles", "in", "the", "body", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Google Ask.com", "tokens": ["Google", "Ask.com"], "ner_tags": ["B-ORG", "I-ORG"]}
{"sentence": "Starring are Joachim Kr\u00f3l (L\u00e1szl\u00f3, Jewish restaurant owner), Erika Marozs\u00e1n (Ilona, waitress and L\u00e1szl\u00f3's lover), Stefano Dionisi (Andr\u00e1s, pianist who creates Gloomy Sunday) and Ben Becker (Hans Wieck, a German business man who becomes an SS officer).", "tokens": ["Starring", "are", "Joachim", "Kr\u00f3l", "(", "L\u00e1szl\u00f3", ",", "Jewish", "restaurant", "owner", ")", ",", "Erika", "Marozs\u00e1n", "(", "Ilona", ",", "waitress", "and", "L\u00e1szl\u00f3", "'s", "lover", ")", ",", "Stefano", "Dionisi", "(", "Andr\u00e1s", ",", "pianist", "who", "creates", "Gloomy", "Sunday", ")", "and", "Ben", "Becker", "(", "Hans", "Wieck", ",", "a", "German", "business", "man", "who", "becomes", "an", "SS", "officer", ")", "."], "ner_tags": ["O", "O", "B-PER", "I-PER", "O", "B-PER", "O", "B-MISC", "O", "O", "O", "O", "B-PER", "I-PER", "O", "B-PER", "O", "O", "O", "B-PER", "O", "O", "O", "O", "B-PER", "I-PER", "O", "B-PER", "O", "O", "O", "O", "B-MISC", "I-MISC", "O", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "O", "B-MISC", "O", "O", "O", "O", "O", "B-ORG", "O", "O", "O"]}
{"sentence": "He tries to commit suicide by jumping into a river, but L\u00e1szl\u00f3 saves him and Hans returns to Germany.", "tokens": ["He", "tries", "to", "commit", "suicide", "by", "jumping", "into", "a", "river", ",", "but", "L\u00e1szl\u00f3", "saves", "him", "and", "Hans", "returns", "to", "Germany", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "O", "O", "O", "B-PER", "O", "O", "B-LOC", "O"]}
{"sentence": "Electric Avenue in Brixton, London, gets its name from being the first electrified shopping area in London.", "tokens": ["Electric", "Avenue", "in", "Brixton", ",", "London", ",", "gets", "its", "name", "from", "being", "the", "first", "electrified", "shopping", "area", "in", "London", "."], "ner_tags": ["B-LOC", "I-LOC", "O", "B-LOC", "O", "B-LOC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "O"]}
{"sentence": "The bomb was one of three detonated by far-right extremist David Copeland in attacks aimed at London's black, Asian, and gay communities.", "tokens": ["The", "bomb", "was", "one", "of", "three", "detonated", "by", "far-right", "extremist", "David", "Copeland", "in", "attacks", "aimed", "at", "London", "'s", "black", ",", "Asian", ",", "and", "gay", "communities", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "O", "O", "O", "B-LOC", "O", "O", "O", "B-MISC", "O", "O", "O", "O", "O"]}
{"sentence": "Howell's son, James Bruen Howell, was a U.S. Senator from Iowa.", "tokens": ["Howell", "'s", "son", ",", "James", "Bruen", "Howell", ",", "was", "a", "U.S.", "Senator", "from", "Iowa", "."], "ner_tags": ["B-PER", "O", "O", "O", "B-PER", "I-PER", "I-PER", "O", "O", "O", "B-LOC", "O", "O", "B-LOC", "O"]}
{"sentence": "Ultimate production is expected to exceed 270,000,000 barrels, with some estimates as high as 500,000,000 barrels.", "tokens": ["Ultimate", "production", "is", "expected", "to", "exceed", "270,000,000", "barrels", ",", "with", "some", "estimates", "as", "high", "as", "500,000,000", "barrels", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Touma, like others who remained, such as Emil Habibi and Jabra Nicola, was a communist from the Haifa region.", "tokens": ["Touma", ",", "like", "others", "who", "remained", ",", "such", "as", "Emil", "Habibi", "and", "Jabra", "Nicola", ",", "was", "a", "communist", "from", "the", "Haifa", "region", "."], "ner_tags": ["B-PER", "O", "O", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "B-LOC", "O", "O"]}
{"sentence": "Following the retirement of the great German-American mathematician Hans Rademacher, he was appointed to the Thomas A.", "tokens": ["Following", "the", "retirement", "of", "the", "great", "German-American", "mathematician", "Hans", "Rademacher", ",", "he", "was", "appointed", "to", "the", "Thomas", "A", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-MISC", "O", "B-PER", "I-PER", "O", "O", "O", "O", "O", "O", "B-PER", "I-PER", "O"]}
{"sentence": "Express Nakhonphink is a Special Express Train that is operated by the State Railway of Thailand.", "tokens": ["Express", "Nakhonphink", "is", "a", "Special", "Express", "Train", "that", "is", "operated", "by", "the", "State", "Railway", "of", "Thailand", "."], "ner_tags": ["B-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "O"]}
{"sentence": "Ez2DJ Dance Edition Vol.1 (2000): Cancelled at Developing rate 80%.", "tokens": ["Ez2DJ", "Dance", "Edition", "Vol.1", "(", "2000", ")", ":", "Cancelled", "at", "Developing", "rate", "80%", "."], "ner_tags": ["B-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Ez2DJ 7th TraX - Resistance - (March, 2007)", "tokens": ["Ez2DJ", "7th", "TraX", "-", "Resistance", "-", "(", "March", ",", "2007", ")"], "ner_tags": ["B-MISC", "I-MISC", "I-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O"]}
{"sentence": "EZ2DJ cabinet features four red effector buttons, located at the top of the controller part.", "tokens": ["EZ2DJ", "cabinet", "features", "four", "red", "effector", "buttons", ",", "located", "at", "the", "top", "of", "the", "controller", "part", "."], "ner_tags": ["B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "EZ2DJ's passing system is known as the \"survival gauge\"; one needs to keep the gauge above 0% until the end of song to pass, similar to systems used in many dance-simulation games.", "tokens": ["EZ2DJ", "'s", "passing", "system", "is", "known", "as", "the", "\"", "survival", "gauge", "\"", ";", "one", "needs", "to", "keep", "the", "gauge", "above", "0%", "until", "the", "end", "of", "song", "to", "pass", ",", "similar", "to", "systems", "used", "in", "many", "dance-simulation", "games", "."], "ner_tags": ["B-MISC", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "',' Mystic Dream 9903 'are just playable remixed version.", "tokens": ["'", ",", "'", "Mystic", "Dream", "9903", "'", "are", "just", "playable", "remixed", "version", "."], "ner_tags": ["O", "O", "O", "B-MISC", "I-MISC", "I-MISC", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Confete is composed by Ruby Tuesday (< - Carlos, Carlos is singer).", "tokens": ["Confete", "is", "composed", "by", "Ruby", "Tuesday", "(", "<", "-", "Carlos", ",", "Carlos", "is", "singer", ")", "."], "ner_tags": ["B-MISC", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "B-PER", "O", "B-PER", "O", "O", "O", "O"]}
{"sentence": "Songs just in OST", "tokens": ["Songs", "just", "in", "OST"], "ner_tags": ["O", "O", "O", "O"]}
{"sentence": "Konami had originally filed a lawsuit against Amuse World in 2001 over the issue, and it has long since believed that they settled out of court due to the continued release of the title.", "tokens": ["Konami", "had", "originally", "filed", "a", "lawsuit", "against", "Amuse", "World", "in", "2001", "over", "the", "issue", ",", "and", "it", "has", "long", "since", "believed", "that", "they", "settled", "out", "of", "court", "due", "to", "the", "continued", "release", "of", "the", "title", "."], "ner_tags": ["B-ORG", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The original routes connected the southern part of the county (near the Mount Vernon Estate) to the Huntington Metrorail Station which borders Alexandria.", "tokens": ["The", "original", "routes", "connected", "the", "southern", "part", "of", "the", "county", "(", "near", "the", "Mount", "Vernon", "Estate", ")", "to", "the", "Huntington", "Metrorail", "Station", "which", "borders", "Alexandria", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "I-LOC", "O", "O", "O", "B-LOC", "I-LOC", "I-LOC", "O", "O", "B-LOC", "O"]}
{"sentence": "Fairfax Connector deployed SmarTrip readers on its buses in May 2007, extending the use of this Washington Metro fare-payment system to Fairfax County.", "tokens": ["Fairfax", "Connector", "deployed", "SmarTrip", "readers", "on", "its", "buses", "in", "May", "2007", ",", "extending", "the", "use", "of", "this", "Washington", "Metro", "fare-payment", "system", "to", "Fairfax", "County", "."], "ner_tags": ["B-ORG", "I-ORG", "O", "B-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-LOC", "I-LOC", "O", "O", "O", "B-LOC", "I-LOC", "O"]}
{"sentence": "This became independently accredited as the Fairmont State Community and Technical College in 2003 and was merged into the university in 2006 and was renamed Pierpont Community & Technical College.", "tokens": ["This", "became", "independently", "accredited", "as", "the", "Fairmont", "State", "Community", "and", "Technical", "College", "in", "2003", "and", "was", "merged", "into", "the", "university", "in", "2006", "and", "was", "renamed", "Pierpont", "Community", "&", "Technical", "College", "."], "ner_tags": ["O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O"]}
{"sentence": "The Robert C. Byrd National Aerospace Education Center located in nearby Bridgeport, West Virginia, offers multiple programs in aviation.", "tokens": ["The", "Robert", "C.", "Byrd", "National", "Aerospace", "Education", "Center", "located", "in", "nearby", "Bridgeport", ",", "West", "Virginia", ",", "offers", "multiple", "programs", "in", "aviation", "."], "ner_tags": ["O", "B-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "I-ORG", "O", "O", "O", "B-LOC", "O", "B-LOC", "I-LOC", "O", "O", "O", "O", "O", "O", "O"]}
