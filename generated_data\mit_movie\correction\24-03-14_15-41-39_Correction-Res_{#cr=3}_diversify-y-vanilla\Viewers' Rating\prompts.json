{"prompts": ["Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n3. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Is The 40-Year-Old Virgin considered a {{must-see}} comedy film?\"\nText Span: \"must-see\"\n\n2. Query: \"What is the {{viewers' rating}} for the dystopian society movie 'In Time'?\"\nText Span: \"viewers' rating\"\n\n3. Query: \"What is the {{viewers' rating}} for the famous action movie starring Tom Cruise and directed by James Cameron\"\nText Span: \"viewers' rating\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Is the Avengers movie considered a {{must-see}} action film?\"\nText Span: \"must-see\"\n\n2. Query: \"Is there a {{hauntingly beautiful}} film with a TV-Y7 rating\"\nText Span: \"hauntingly beautiful\"\n\n3. Query: \"show me a trailer for a {{classic}} film directed by alfred hitchcock Music \"\nText Span: \"classic\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n3. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What are some top-rated movies from the {{Enchanting}} year of 1999?\"\nText Span: \"Enchanting\"\n\n2. Query: \"Which {{exceptional}} movie from E would you suggest for me to watch?\"\nText Span: \"exceptional\"\n\n3. Query: \"What year did the movie 'Gone with the Wind' come out, and is it considered a {{classic}}?\"\nText Span: \"classic\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n2. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"I'm looking for a comedy film from the 1990s with a {{high viewers' rating}} and starring Jim Carrey.\"\nText Span: \"high viewers' rating\"\n\n2. Query: \"Is there a movie directed by Edgar Wright with a high {{Viewers' Rating}}?\"\nText Span: \"Viewers' Rating\"\n\n3. Query: \"What are some overrated {{highlights}} of Joffrey Baratheon's acting career?\"\nText Span: \"highlights\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Who are the actors in the latest {{blockbuster}} action film set in space?\"\nText Span: \"blockbuster\"\n\n2. Query: \"What movie did Francis Ford Coppola direct in the 1970s that received a {{high viewers' rating}}?\"\nText Span: \"high viewers' rating\"\n\n3. Query: \"Show me a film with Tom Hanks that is considered a {{classic romance}}\"\nText Span: \"classic romance\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What's your {{favorite}} war movie that was released before 1960?\"\nText Span: \"favorite\"\n\n2. Query: \"I'm looking for a {{Must-Watch}} movie, can you suggest one?\"\nText Span: \"Must-Watch\"\n\n3. Query: \"Is there a Kevin Hart movie that is a {{must-see}}?\"\nText Span: \"must-see\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n4. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Show me the trailer for My Girl, a movie from the 90s with a {{high viewers' rating}}\"\nText Span: \"high viewers' rating\"\n\n2. Query: \"What is the {{viewers' rating}} of the historical film directed by Roman Polanski?\"\nText Span: \"viewers' rating\"\n\n3. Query: \"Is there a film with an E rating that would be suitable for {{children}}?\"\nText Span: \"children\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n4. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is a {{cult classic}} film about terminal illness or a coming-of-age story?\"\nText Span: \"cult classic\"\n\n2. Query: \"Is there a movie from the 1980s with a high {{viewers' rating}} and a memorable character?\"\nText Span: \"viewers' rating\"\n\n3. Query: \"What is the {{viewers' rating}} for Goodfellas?\"\nText Span: \"viewers' rating\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n4. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Show me a movie with an {{exceptional}} viewers' rating, like Winnie the Pooh.\"\nText Span: \"exceptional\"\n\n2. Query: \"What is the {{best-rated}} movie released in the year 2010 about a crime thriller?\"\nText Span: \"best-rated\"\n\n3. Query: \"Which Harry Potter movie has the {{highest viewers' rating}}?\"\nText Span: \"highest viewers' rating\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n2. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What film starring Keanu Reeves is considered the most action-packed and {{entertaining}}?\"\nText Span: \"entertaining\"\n\n2. Query: \"What 2022 movie starring Gerard Butler has a {{cult}} following\"\nText Span: \"cult\"\n\n3. Query: \"Can I watch a {{stale}}, one-minute trailer of the classic film Casablanca\"\nText Span: \"stale\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n3. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What {{hilarious}} exploitation films were released in 1962?\"\nText Span: \"hilarious\"\n\n2. Query: \"Can you recommend an art house film that is a {{standout}} in terms of its alluring plot and features Al Pacino?\"\nText Span: \"standout\"\n\n3. Query: \"Can you recommend a {{nostalgic}} movie featuring Tom Cruise?\"\nText Span: \"nostalgic\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n2. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Not recommended movie with a {{high viewers' rating}} from the 1990s\"\nText Span: \"high viewers' rating\"\n\n2. Query: \"Can you recommend an {{enriching}} and engrossing movie for me to watch?\"\nText Span: \"enriching\"\n\n3. Query: \"Can you recommend a refreshing movie with an {{A- rating}}?\"\nText Span: \"A- rating\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the {{viewers' rating}} for Raging Bull?\"\nText Span: \"viewers' rating\"\n\n2. Query: \"can you recommend a Children's movie with a high {{Viewers' Rating}}?\"\nText Span: \"Viewers' Rating\"\n\n3. Query: \"Can you recommend a movie with an adventurous plot and a {{high viewers' rating}}?\"\nText Span: \"high viewers' rating\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n3. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"who was the director of the film adaptation of the novel 'To Kill a Mockingbird' and what is the {{viewers' rating}}\"\nText Span: \"viewers' rating\"\n\n2. Query: \"What movie starring Ryan Reynolds has been described as a {{standout}} in the action-comedy genre?\"\nText Span: \"standout\"\n\n3. Query: \"What is the 1/10 rating movie that I should {{avoid}} at all costs?\"\nText Span: \"avoid\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n3. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"what movies from the 2010s have received a rating of {{B}}\"\nText Span: \"B\"\n\n2. Query: \"Can you recommend A {{captivating}} movie preview from 1975 with brilliant performances?\"\nText Span: \"captivating\"\n\n3. Query: \"Is Mufasa in any {{films rated}} AO?\"\nText Span: \"films rated\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n3. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a {{classic}} movie with an iconic character and a thrilling storyline?\"\nText Span: \"classic\"\n\n2. Query: \"what are the {{top-rated}} horror movies directed by Wes Craven\"\nText Span: \"top-rated\"\n\n3. Query: \"What are some {{highly-rated}} political corruption movies from 1982?\"\nText Span: \"highly-rated\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Is there a Mockumentary film from 1999 that is {{highly acclaimed}}?\"\nText Span: \"highly acclaimed\"\n\n2. Query: \"Could you recommend an {{engaging}} black comedy film directed by Rob Reiner and starring Anne Bancroft?\"\nText Span: \"engaging\"\n\n3. Query: \"Which movie directed by Ridley Scott has the {{highest viewers' rating}}?\"\nText Span: \"highest viewers' rating\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n3. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What are some {{electrifying}} Coming-of-Age movies released in the past decade?\"\nText Span: \"electrifying\"\n\n2. Query: \"What {{inspiring}} movie did Paul Greengrass direct?\"\nText Span: \"inspiring\"\n\n3. Query: \"Who are the actors in the {{timeless classic}} movie The Godfather?\"\nText Span: \"timeless classic\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n3. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"I'm looking for a {{timeless classic}} film from the 1950s, preferably a drama.\"\nText Span: \"timeless classic\"\n\n2. Query: \"Can you show me the theatrical trailer for the 2008 film that is considered {{addictive}} to watch?\"\nText Span: \"addictive\"\n\n3. Query: \"Can you recommend any mind-bending films from the 2010s with a {{high}} viewers' rating?\"\nText Span: \"high\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n2. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Are there any {{five-star rated}} romantic comedies from the 1990s that I should watch?\"\nText Span: \"five-star rated\"\n\n2. Query: \"Is there a {{five-star}} rating for The Exorcist?\"\nText Span: \"five-star\"\n\n3. Query: \"Please show me a trailer for the movie directed by Christopher Nolan that has received a {{five-star rating}} from viewers.\"\nText Span: \"five-star rating\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n4. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Is there an {{Unwatchable}} movie from the 1980s\"\nText Span: \"Unwatchable\"\n\n2. Query: \"Show me a survival in the wilderness movie similar to Van Helsing with a {{high viewers' rating}}.\"\nText Span: \"high viewers' rating\"\n\n3. Query: \"What is the {{viewers' rating}} for the international trailer of the movie starring Emma Stone\"\nText Span: \"viewers' rating\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n3. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Who directed the action movie with intense fighting scenes and is {{highly rated}} by viewers\"\nText Span: \"highly rated\"\n\n2. Query: \"Show me a Trinity-related movie with {{high viewers' rating}}\"\nText Span: \"high viewers' rating\"\n\n3. Query: \"Is the new Harry Potter film {{worth watching}}?\"\nText Span: \"worth watching\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n2. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the {{viewers' rating}} of the movie that features an apocalyptic event and was released in 1984?\"\nText Span: \"viewers' rating\"\n\n2. Query: \"Are there any historical films from the 1990s that are considered a {{must-see}}\"\nText Span: \"must-see\"\n\n3. Query: \"Can you recommend an {{Excellent}} movie with a 3.5-star rating?\"\nText Span: \"Excellent\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n2. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What are some {{Mediocre}} movies with immersive world-building?\"\nText Span: \"Mediocre\"\n\n2. Query: \"what is the {{viewers' rating}} for the movie about organized crime with Al Pacino\"\nText Span: \"viewers' rating\"\n\n3. Query: \"Can you recommend a {{Superb}} science fiction movie from 1976?\"\nText Span: \"Superb\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n3. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a movie directed by Mark Joffe that is suitable for {{kids}}\"\nText Span: \"kids\"\n\n2. Query: \"can you recommend a movie with a {{captivating}} A plot\"\nText Span: \"captivating\"\n\n3. Query: \"I'm looking for a {{dreadful}} horror movie from the 1980s. Any suggestions?\"\nText Span: \"dreadful\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n3. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the {{viewers' rating}} for Million Dollar Baby?\"\nText Span: \"viewers' rating\"\n\n2. Query: \"Can you recommend a movie featuring the character Hulk with a {{high viewers' rating}}?\"\nText Span: \"high viewers' rating\"\n\n3. Query: \"Who directed the movie that Michael Bay acted in, and what is the {{viewers' rating}} for it?\"\nText Span: \"viewers' rating\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n3. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What's your {{favorite}} movie with the song Sound of Silence in it?\"\nText Span: \"favorite\"\n\n2. Query: \"Can you recommend a post-apocalyptic world movie with a {{3.7-star rating}}?\"\nText Span: \"3.7-star rating\"\n\n3. Query: \"Is Anthony Hopkins in the movie The Terminator, and how would you {{rate}} it?\"\nText Span: \"rate\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n3. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Show me a movie with Idris Elba that has a {{high viewers' rating}}.\"\nText Span: \"high viewers' rating\"\n\n2. Query: \"Can you recommend a {{family-friendly}} movie with a Parental Guidance rating?\"\nText Span: \"family-friendly\"\n\n3. Query: \"Can you recommend a movie from 2005 that is an {{Awe-inspiring}} time loop film\"\nText Span: \"Awe-inspiring\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n3. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"what is the {{top-rated}} action movie of the 1990s Music \"\nText Span: \"top-rated\"\n\n2. Query: \"Can you recommend a movie with {{A- list}} actors and a high Viewers' Rating?\"\nText Span: \"A- list\"\n\n3. Query: \"Can you recommend a movie that involves family secrets and features an {{unforgettable performance}} by Viggo Mortensen?\"\nText Span: \"unforgettable performance\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a {{cheesy}} coming-of-age movie with iconic scenes?\"\nText Span: \"cheesy\"\n\n2. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\n\n3. Query: \"What {{top-notch}} movies from the 1980s should I watch?\"\nText Span: \"top-notch\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n3. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n4. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the best action movie of the 1990s with a {{killer soundtrack}}?\"\nText Span: \"killer soundtrack\"\n\n2. Query: \"What are the {{top 5}} highest rated movies of all time\"\nText Span: \"top 5\"\n\n3. Query: \"I'm looking for an {{Immersive}} dystopian film released in 1978, can you help me find one?\"\nText Span: \"Immersive\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n2. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"what are the {{most popular}} horror movies from the 1980s?\"\nText Span: \"most popular\"\n\n2. Query: \"Which movie features John Watson and is rated as a {{must-see}}?\"\nText Span: \"must-see\"\n\n3. Query: \"What are some of the best movies starring Johnny Depp that are {{epic in scale}}?\"\nText Span: \"epic in scale\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n2. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n4. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Who directed the {{top-rated}} movies of 2023?\"\nText Span: \"top-rated\"\n\n2. Query: \"Which movie from the 90s has the song 'My Heart Will Go On' and is {{highly rated by viewers}}?\"\nText Span: \"highly rated by viewers\"\n\n3. Query: \"Show me a Michael Mann film with a high {{viewers' rating}}.\"\nText Span: \"viewers' rating\"", "Here is a spoken query to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Viewers' Rating.\nIn particular, for the given query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type viewers' rating\n- (B). The span contains a named viewers' rating entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not viewers' rating\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Review, Character, other].\n\nA named viewers' rating entity can be a rating score such as \"9 out of 10\". Endorsement phrases such as \"must see\" and \"highly recommended\" are also named viewers' rating entities. Evaluative descriptors such as \"best\", \"good\", \"popular\", \"famous\" and \"favorite\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nYou should capture the complete descriptive phrase such as \"high viewers' rating\" as opposed to \"high\".\nAmbiguous identifiers such as \"rating\" and \"viewers' rating\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"What is the {{best}} superhero movie of all time?\"\nText Span: \"best\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"I want to watch a horror movie with a {{high Rotten Tomatoes rating}}.\"\nText Span: \"high Rotten Tomatoes rating\"\nLabel: (A). Correct Entity Annotation.\n\n3. Query: \"What is the {{viewers' rating}} for the movie Sleeping Beauty?\"\nText Span: \"viewers' rating\"\nLabel: (D). Not a Named Entity.\n\n4. Query: \"Show me a Marielle Heller film released in 2019 with a {{high}} Viewers' Rating\"\nText Span: \"high\"\nLabel: (B). Wrong Boundary. The correct span boundary is \"high Viewers' Rating\".\n\n\n---\n\n\nPlease classify the following span of text:\n\nQuery: \"Can you recommend a movie directed by Damien Chazelle that has a {{great}} preview?\"\nText Span: \"great\""]}