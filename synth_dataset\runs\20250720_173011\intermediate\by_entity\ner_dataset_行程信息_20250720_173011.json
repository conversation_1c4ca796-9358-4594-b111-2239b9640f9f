[{"text": "我计划下周三早上8:30从北京首都国际机场出发，前往上海浦东国际机场。", "label": [{"entity": "下周三早上8:30", "start_idx": 3, "end_idx": 9, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 13, "end_idx": 21, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 25, "end_idx": 33, "type": "行程信息"}]}, {"text": "我计划明天上午10点从北京首都国际机场出发，前往上海浦东国际机场。", "label": [{"entity": "明天上午10点", "start_idx": 3, "end_idx": 8, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 10, "end_idx": 21, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 25, "end_idx": 36, "type": "行程信息"}]}, {"text": "今天下午3点，我需要从北京首都国际机场T3航站楼搭乘CA1234航班前往上海浦东国际机场。", "label": [{"entity": "今天下午3点", "start_idx": 0, "end_idx": 6, "type": "行程信息"}, {"entity": "北京首都国际机场T3航站楼", "start_idx": 7, "end_idx": 23, "type": "行程信息"}, {"entity": "CA1234航班", "start_idx": 24, "end_idx": 32, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 36, "end_idx": 50, "type": "行程信息"}]}, {"text": "我计划下周三上午9点从北京首都国际机场出发，前往上海浦东国际机场。", "label": [{"entity": "下周三上午9点", "start_idx": 3, "end_idx": 11, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 15, "end_idx": 24, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 29, "end_idx": 38, "type": "行程信息"}]}, {"text": "我计划明天早上8:30从北京首都国际机场出发，前往上海浦东国际机场。", "label": [{"entity": "明天早上8:30", "start_idx": 3, "end_idx": 10, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 12, "end_idx": 24, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 29, "end_idx": 41, "type": "行程信息"}]}, {"text": "今天下午3点，我从北京首都国际机场出发，前往上海浦东国际机场。", "label": [{"entity": "今天下午3点", "start_idx": 0, "end_idx": 6, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 9, "end_idx": 21, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 27, "end_idx": 39, "type": "行程信息"}]}, {"text": "我计划下周三早上8:30从北京首都国际机场出发，前往上海浦东国际机场。", "label": [{"entity": "下周三", "start_idx": 3, "end_idx": 5, "type": "行程信息"}, {"entity": "早上8:30", "start_idx": 6, "end_idx": 10, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 12, "end_idx": 19, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 23, "end_idx": 30, "type": "行程信息"}]}, {"text": "我计划明天上午9点从北京首都国际机场搭乘CA1234航班前往上海浦东国际机场。", "label": [{"entity": "明天上午9点", "start_idx": 3, "end_idx": 8, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 12, "end_idx": 23, "type": "行程信息"}, {"entity": "CA1234航班", "start_idx": 24, "end_idx": 31, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 36, "end_idx": 47, "type": "行程信息"}]}, {"text": "我计划下周三上午9:30从北京首都国际机场出发，飞往上海浦东国际机场。", "label": [{"entity": "下周三上午9:30", "start_idx": 3, "end_idx": 10, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 14, "end_idx": 24, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 27, "end_idx": 37, "type": "行程信息"}]}, {"text": "我计划明天上午9点从北京首都国际机场出发，前往上海浦东国际机场。", "label": [{"entity": "明天上午9点", "start_idx": 3, "end_idx": 8, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 10, "end_idx": 22, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 27, "end_idx": 39, "type": "行程信息"}]}]