{"sentence": "The European Union is considering imposing tariffs on American products.", "tokens": ["The", "European", "Union", "is", "considering", "imposing", "tariffs", "on", "American", "products", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Elon Musk's SpaceX successfully launches another batch of Starlink satellites.", "tokens": ["Elon", "Musk", "'", "s", "SpaceX", "successfully", "launches", "another", "batch", "of", "Starlink", "satellites", "."], "labels": ["B-person", "I-person", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The protests in Hong Kong continue to escalate as tensions rise between the government and demonstrators.", "tokens": ["The", "protests", "in", "Hong", "Kong", "continue", "to", "escalate", "as", "tensions", "rise", "between", "the", "government", "and", "demonstrators", "."], "labels": ["O", "O", "O", "B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Health Organization warns of a potential Ebola outbreak in the Democratic Republic of Congo.", "tokens": ["The", "World", "Health", "Organization", "warns", "of", "a", "potential", "Ebola", "outbreak", "in", "the", "Democratic", "Republic", "of", "Congo", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "I-location", "I-location", "O"]}
{"sentence": "Amazon announces plans to open a new fulfillment center, creating 2,000 jobs in the local community.", "tokens": ["Amazon", "announces", "plans", "to", "open", "a", "new", "fulfillment", "center", ",", "creating", "2", ",", "000", "jobs", "in", "the", "local", "community", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Taylor Swift releases her highly anticipated album, \"Fearless (Taylor's Version).\"", "tokens": ["Taylor", "Swift", "releases", "her", "highly", "anticipated", "album", ",", "\"", "Fearless", "(", "Taylor", "'", "s", "Version", ")", ".", "\""], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations urges immediate action to address the humanitarian crisis in Yemen.", "tokens": ["The", "United", "Nations", "urges", "immediate", "action", "to", "address", "the", "humanitarian", "crisis", "in", "Yemen", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "The Los Angeles Lakers defeat the Brooklyn Nets in a thrilling overtime game.", "tokens": ["The", "Los", "Angeles", "Lakers", "defeat", "the", "Brooklyn", "Nets", "in", "a", "thrilling", "overtime", "game", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "President Joe Biden signs an executive order to address climate change and promote clean energy.", "tokens": ["President", "Joe", "Biden", "signs", "an", "executive", "order", "to", "address", "climate", "change", "and", "promote", "clean", "energy", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Tokyo Olympics are facing uncertainty as concerns over the COVID-19 pandemic persist.", "tokens": ["The", "Tokyo", "Olympics", "are", "facing", "uncertainty", "as", "concerns", "over", "the", "COVID", "-", "19", "pandemic", "persist", "."], "labels": ["O", "B-location", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "TikTok faces scrutiny over data privacy concerns and potential national security risks.", "tokens": ["TikTok", "faces", "scrutiny", "over", "data", "privacy", "concerns", "and", "potential", "national", "security", "risks", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Meghan Markle and Prince Harry announce their decision to step back from their royal duties.", "tokens": ["Meghan", "Markle", "and", "Prince", "Harry", "announce", "their", "decision", "to", "step", "back", "from", "their", "royal", "duties", "."], "labels": ["B-person", "I-person", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United States imposes sanctions on Russia following the suspected poisoning of Alexei Navalny.", "tokens": ["The", "United", "States", "imposes", "sanctions", "on", "Russia", "following", "the", "suspected", "poisoning", "of", "Alexei", "Navalny", "."], "labels": ["O", "B-location", "I-location", "O", "O", "O", "B-location", "O", "O", "O", "O", "O", "B-person", "I-person", "O"]}
{"sentence": "Tesla's stock prices surge as the company reports record-breaking sales and profits.", "tokens": ["Tesla", "'", "s", "stock", "prices", "surge", "as", "the", "company", "reports", "record", "-", "breaking", "sales", "and", "profits", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations Security Council condemns the recent terrorist attacks in Nigeria.", "tokens": ["The", "United", "Nations", "Security", "Council", "condemns", "the", "recent", "terrorist", "attacks", "in", "Nigeria", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Angela Merkel, the Chancellor of Germany, announces her plans to retire from politics.", "tokens": ["Angela", "Merkel", ",", "the", "Chancellor", "of", "Germany", ",", "announces", "her", "plans", "to", "retire", "from", "politics", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tesla CEO Elon Musk announces plans to build a new gigafactory in Texas.", "tokens": ["Tesla", "CEO", "Elon", "Musk", "announces", "plans", "to", "build", "a", "new", "gigafactory", "in", "Texas", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "The World Bank approves a $500 million loan to support education reform in India.", "tokens": ["The", "World", "Bank", "approves", "a", "$", "500", "million", "loan", "to", "support", "education", "reform", "in", "India", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "South Korea hosts a virtual summit with leaders from ASEAN countries to strengthen economic cooperation.", "tokens": ["South", "Korea", "hosts", "a", "virtual", "summit", "with", "leaders", "from", "ASEAN", "countries", "to", "strengthen", "economic", "cooperation", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The European Central Bank announces an expansion of its stimulus program to support the economy.", "tokens": ["The", "European", "Central", "Bank", "announces", "an", "expansion", "of", "its", "stimulus", "program", "to", "support", "the", "economy", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New study shows link between social media use and mental health issues.", "tokens": ["New", "study", "shows", "link", "between", "social", "media", "use", "and", "mental", "health", "issues", "."], "labels": ["O", "O", "O", "O", "O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Wildfires continue to devastate parts of California.", "tokens": ["Wildfires", "continue", "to", "devastate", "parts", "of", "California", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Pfizer announces plans for booster shots in response to new COVID variants.", "tokens": ["Pfizer", "announces", "plans", "for", "booster", "shots", "in", "response", "to", "new", "COVID", "variants", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Serena Williams withdraws from Wimbledon due to injury.", "tokens": ["Serena", "Williams", "withdraws", "from", "Wimbledon", "due", "to", "injury", "."], "labels": ["B-person", "I-person", "O", "O", "B-location", "O", "O", "O", "O"]}
{"sentence": "United Nations report shows increase in global poverty rates.", "tokens": ["United", "Nations", "report", "shows", "increase", "in", "global", "poverty", "rates", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tech giant Apple introduces new line of products.", "tokens": ["Tech", "giant", "Apple", "introduces", "new", "line", "of", "products", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Prime Minister Trudeau visits Indigenous community in northern Canada.", "tokens": ["Prime", "Minister", "Trudeau", "visits", "Indigenous", "community", "in", "northern", "Canada", "."], "labels": ["O", "O", "B-person", "O", "B-location", "O", "O", "O", "B-location", "O"]}
{"sentence": "SpaceX successfully launches new satellite into orbit.", "tokens": ["SpaceX", "successfully", "launches", "new", "satellite", "into", "orbit", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Record-breaking heatwave hits Western Europe.", "tokens": ["Record", "-", "breaking", "heatwave", "hits", "Western", "Europe", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Apple Inc. unveils latest iPhone model.", "tokens": ["Apple", "Inc", ".", "unveils", "latest", "iPhone", "model", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Tropical storm makes landfall in Florida.", "tokens": ["Tropical", "storm", "makes", "landfall", "in", "Florida", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Elon Musk's SpaceX launches satellite into orbit.", "tokens": ["Elon", "Musk", "'", "s", "SpaceX", "launches", "satellite", "into", "orbit", "."], "labels": ["B-person", "I-person", "O", "O", "B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "New study shows link between coffee consumption and reduced risk of heart disease.", "tokens": ["New", "study", "shows", "link", "between", "coffee", "consumption", "and", "reduced", "risk", "of", "heart", "disease", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations issues statement on human rights violations in Myanmar.", "tokens": ["United", "Nations", "issues", "statement", "on", "human", "rights", "violations", "in", "Myanmar", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Simone Biles wins gold in gymnastics competition.", "tokens": ["Simone", "Biles", "wins", "gold", "in", "gymnastics", "competition", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Google announces partnership with local schools to promote computer science education.", "tokens": ["Google", "announces", "partnership", "with", "local", "schools", "to", "promote", "computer", "science", "education", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Former President Obama visits Kenya for charity work.", "tokens": ["Former", "President", "Obama", "visits", "Kenya", "for", "charity", "work", "."], "labels": ["O", "O", "B-person", "O", "B-location", "O", "O", "O", "O"]}
{"sentence": "Wildfires continue to ravage California forests.", "tokens": ["Wildfires", "continue", "to", "ravage", "California", "forests", "."], "labels": ["O", "O", "O", "O", "B-location", "O", "O"]}
{"sentence": "Amazon to open new distribution center in Texas.", "tokens": ["Amazon", "to", "open", "new", "distribution", "center", "in", "Texas", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "New study finds potential link between obesity and increased risk of certain cancers.", "tokens": ["New", "study", "finds", "potential", "link", "between", "obesity", "and", "increased", "risk", "of", "certain", "cancers", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Beyonc\u00e9 releases new album.", "tokens": ["Beyonc\u00e9", "releases", "new", "album", "."], "labels": ["B-person", "O", "O", "O", "O"]}
{"sentence": "European Union imposes sanctions on Russia for human rights abuses.", "tokens": ["European", "Union", "imposes", "sanctions", "on", "Russia", "for", "human", "rights", "abuses", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "New York City to implement new public transportation system.", "tokens": ["New", "York", "City", "to", "implement", "new", "public", "transportation", "system", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Greta Thunberg speaks at climate change conference in Switzerland.", "tokens": ["Greta", "Thunberg", "speaks", "at", "climate", "change", "conference", "in", "Switzerland", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Toyota announces recall of over 1 million vehicles.", "tokens": ["Toyota", "announces", "recall", "of", "over", "1", "million", "vehicles", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Hurricane hits coastal town in Louisiana.", "tokens": ["Hurricane", "hits", "coastal", "town", "in", "Louisiana", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Researchers discover new species of dinosaur in South America.", "tokens": ["Researchers", "discover", "new", "species", "of", "dinosaur", "in", "South", "America", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Taylor Swift performs at sold-out concert in London.", "tokens": ["Taylor", "Swift", "performs", "at", "sold", "-", "out", "concert", "in", "London", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Microsoft CEO steps down from position.", "tokens": ["Microsoft", "CEO", "steps", "down", "from", "position", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Australia experiences record-breaking heatwave.", "tokens": ["Australia", "experiences", "record", "-", "breaking", "heatwave", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "LeBron James signs multi-million dollar contract with new team.", "tokens": ["LeBron", "James", "signs", "multi", "-", "million", "dollar", "contract", "with", "new", "team", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Alibaba founder faces investigation for alleged financial misconduct.", "tokens": ["Alibaba", "founder", "faces", "investigation", "for", "alleged", "financial", "misconduct", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Mount Everest climbers stranded in severe snowstorm.", "tokens": ["Mount", "Everest", "climbers", "stranded", "in", "severe", "snowstorm", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Taylor Jenkins named head coach of NBA team.", "tokens": ["Taylor", "Jenkins", "named", "head", "coach", "of", "NBA", "team", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "B-organization", "O", "O"]}
{"sentence": "New study suggests link between social media use and increased anxiety.", "tokens": ["New", "study", "suggests", "link", "between", "social", "media", "use", "and", "increased", "anxiety", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Thousands protest for democracy in Hong Kong.", "tokens": ["Thousands", "protest", "for", "democracy", "in", "Hong", "Kong", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Warren Buffett donates $1 billion to charity.", "tokens": ["Warren", "Buffett", "donates", "$", "1", "billion", "to", "charity", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Ford to invest in new electric vehicle technology.", "tokens": ["Ford", "to", "invest", "in", "new", "electric", "vehicle", "technology", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Severe drought threatens crops in Midwest.", "tokens": ["Severe", "drought", "threatens", "crops", "in", "Midwest", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Jennifer Lawrence to star in upcoming film.", "tokens": ["Jennifer", "Lawrence", "to", "star", "in", "upcoming", "film", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "World Health Organization issues warning about new strain of flu virus.", "tokens": ["World", "Health", "Organization", "issues", "warning", "about", "new", "strain", "of", "flu", "virus", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York Yankees win World Series.", "tokens": ["New", "York", "Yankees", "win", "World", "Series", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O"]}
{"sentence": "Duchess of Cambridge visits children's hospital in London.", "tokens": ["Duchess", "of", "Cambridge", "visits", "children", "'", "s", "hospital", "in", "London", "."], "labels": ["B-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Uber to launch new food delivery service.", "tokens": ["Uber", "to", "launch", "new", "food", "delivery", "service", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Scientists confirm presence of water on Mars.", "tokens": ["Scientists", "confirm", "presence", "of", "water", "on", "Mars", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Singer Rihanna starts new fashion line.", "tokens": ["Singer", "Rihanna", "starts", "new", "fashion", "line", "."], "labels": ["O", "B-person", "O", "O", "O", "O", "O"]}
{"sentence": "United Airlines to add new routes to Europe.", "tokens": ["United", "Airlines", "to", "add", "new", "routes", "to", "Europe", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "FBI warns of potential cyberattack on government agencies.", "tokens": ["FBI", "warns", "of", "potential", "cyberattack", "on", "government", "agencies", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Italian restaurant chain announces expansion to Asia.", "tokens": ["Italian", "restaurant", "chain", "announces", "expansion", "to", "Asia", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "New study shows link between air pollution and increased risk of respiratory illnesses.", "tokens": ["New", "study", "shows", "link", "between", "air", "pollution", "and", "increased", "risk", "of", "respiratory", "illnesses", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Prince Harry and Meghan Markle visit Africa for charity work.", "tokens": ["Prince", "Harry", "and", "Meghan", "Markle", "visit", "Africa", "for", "charity", "work", "."], "labels": ["B-person", "I-person", "O", "B-person", "I-person", "O", "B-location", "O", "O", "O", "O"]}
{"sentence": "Facebook launches new initiative to combat misinformation.", "tokens": ["Facebook", "launches", "new", "initiative", "to", "combat", "misinformation", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Iran announces plan to increase uranium enrichment.", "tokens": ["Iran", "announces", "plan", "to", "increase", "uranium", "enrichment", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Nobel Peace Prize awarded to humanitarian organization for work in conflict zones.", "tokens": ["Nobel", "Peace", "Prize", "awarded", "to", "humanitarian", "organization", "for", "work", "in", "conflict", "zones", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Severe flooding hits coastal town in Japan.", "tokens": ["Severe", "flooding", "hits", "coastal", "town", "in", "Japan", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Apple announces new iPhone 13 launch date.", "tokens": ["Apple", "announces", "new", "iPhone", "13", "launch", "date", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Prime Minister Johnson meets with French President Macron.", "tokens": ["Prime", "Minister", "Johnson", "meets", "with", "French", "President", "Macron", "."], "labels": ["O", "O", "B-person", "O", "O", "O", "O", "B-person", "O"]}
{"sentence": "Hurricane hits Florida, causing widespread damage.", "tokens": ["Hurricane", "hits", "Florida", ",", "causing", "widespread", "damage", "."], "labels": ["O", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon faces lawsuit over worker conditions.", "tokens": ["Amazon", "faces", "lawsuit", "over", "worker", "conditions", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Elon Musk plans to visit SpaceX headquarters.", "tokens": ["Elon", "Musk", "plans", "to", "visit", "SpaceX", "headquarters", "."], "labels": ["B-person", "I-person", "O", "O", "O", "B-organization", "O", "O"]}
{"sentence": "New York City imposes vaccine mandate for indoor activities.", "tokens": ["New", "York", "City", "imposes", "vaccine", "mandate", "for", "indoor", "activities", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "President Biden unveils new infrastructure plan.", "tokens": ["President", "Biden", "unveils", "new", "infrastructure", "plan", "."], "labels": ["O", "B-person", "O", "O", "O", "O", "O"]}
{"sentence": "COVID-19 cases surge in California.", "tokens": ["COVID", "-", "19", "cases", "surge", "in", "California", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Facebook to launch new virtual reality headset.", "tokens": ["Facebook", "to", "launch", "new", "virtual", "reality", "headset", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tiger Woods returns to golf after injury.", "tokens": ["Tiger", "Woods", "returns", "to", "golf", "after", "injury", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations adopts resolution on climate change.", "tokens": ["United", "Nations", "adopts", "resolution", "on", "climate", "change", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Wildfires continue to ravage Australian countryside.", "tokens": ["Wildfires", "continue", "to", "ravage", "Australian", "countryside", "."], "labels": ["O", "O", "O", "O", "B-location", "O", "O"]}
{"sentence": "CEO of Microsoft steps down after 20 years.", "tokens": ["CEO", "of", "Microsoft", "steps", "down", "after", "20", "years", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "German Chancellor Merkel wins reelection.", "tokens": ["German", "Chancellor", "Merkel", "wins", "reelection", "."], "labels": ["O", "O", "B-person", "O", "O", "O"]}
{"sentence": "Tokyo Olympics conclude after two-week event.", "tokens": ["Tokyo", "Olympics", "conclude", "after", "two", "-", "week", "event", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tesla announces record-breaking quarterly profits.", "tokens": ["Tesla", "announces", "record", "-", "breaking", "quarterly", "profits", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Los Angeles Lakers sign new star player.", "tokens": ["Los", "Angeles", "Lakers", "sign", "new", "star", "player", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Russian President Putin meets with Chinese leader Xi.", "tokens": ["Russian", "President", "Putin", "meets", "with", "Chinese", "leader", "Xi", "."], "labels": ["O", "O", "B-person", "O", "O", "O", "O", "B-person", "O"]}
{"sentence": "NASA launches new rover to explore Mars.", "tokens": ["NASA", "launches", "new", "rover", "to", "explore", "Mars", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Pfizer seeks approval for COVID-19 vaccine booster.", "tokens": ["Pfizer", "seeks", "approval", "for", "COVID", "-", "19", "vaccine", "booster", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "London experiences heavy flooding after rainstorm.", "tokens": ["London", "experiences", "heavy", "flooding", "after", "rainstorm", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Goldman Sachs CEO testifies before Congress.", "tokens": ["Goldman", "Sachs", "CEO", "testifies", "before", "Congress", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Syrian refugees stranded at border.", "tokens": ["Syrian", "refugees", "stranded", "at", "border", "."], "labels": ["B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Famous chef opens new restaurant in Paris.", "tokens": ["Famous", "chef", "opens", "new", "restaurant", "in", "Paris", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Federal Reserve announces interest rate hike.", "tokens": ["Federal", "Reserve", "announces", "interest", "rate", "hike", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Canadian Prime Minister Trudeau faces backlash over pipeline decision.", "tokens": ["Canadian", "Prime", "Minister", "Trudeau", "faces", "backlash", "over", "pipeline", "decision", "."], "labels": ["B-location", "O", "O", "B-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Oil prices soar amid global supply chain issues.", "tokens": ["Oil", "prices", "soar", "amid", "global", "supply", "chain", "issues", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Mumbai hit by massive power outage.", "tokens": ["Mumbai", "hit", "by", "massive", "power", "outage", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Anti-government protests erupt in Hong Kong.", "tokens": ["Anti", "-", "government", "protests", "erupt", "in", "Hong", "Kong", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "CEO of Amazon donates millions to charity.", "tokens": ["CEO", "of", "Amazon", "donates", "millions", "to", "charity", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Violent clashes between police and protesters in Barcelona.", "tokens": ["Violent", "clashes", "between", "police", "and", "protesters", "in", "Barcelona", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "New study shows link between air pollution and health problems.", "tokens": ["New", "study", "shows", "link", "between", "air", "pollution", "and", "health", "problems", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations peacekeepers deployed to conflict zone in Africa.", "tokens": ["United", "Nations", "peacekeepers", "deployed", "to", "conflict", "zone", "in", "Africa", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Egyptian archaeologists discover ancient tombs.", "tokens": ["Egyptian", "archaeologists", "discover", "ancient", "tombs", "."], "labels": ["B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Google unveils new AI technology for healthcare.", "tokens": ["Google", "unveils", "new", "AI", "technology", "for", "healthcare", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "FBI warns of potential cyberattack on U.S. infrastructure.", "tokens": ["FBI", "warns", "of", "potential", "cyberattack", "on", "U", ".", "S", ".", "infrastructure", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-location", "I-location", "I-location", "I-location", "O", "O"]}
{"sentence": "Italy imposes new COVID-19 restrictions.", "tokens": ["Italy", "imposes", "new", "COVID", "-", "19", "restrictions", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Starbucks announces plan to phase out plastic straws.", "tokens": ["Starbucks", "announces", "plan", "to", "phase", "out", "plastic", "straws", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Violence erupts in Ukraine-Russia border region.", "tokens": ["Violence", "erupts", "in", "Ukraine", "-", "Russia", "border", "region", "."], "labels": ["O", "O", "O", "B-location", "O", "B-location", "O", "O", "O"]}
{"sentence": "Japan to host 2021 Summer Olympics.", "tokens": ["Japan", "to", "host", "2021", "Summer", "Olympics", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New study finds link between sugary drinks and obesity.", "tokens": ["New", "study", "finds", "link", "between", "sugary", "drinks", "and", "obesity", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Hollywood actress files lawsuit against tabloid magazine.", "tokens": ["Hollywood", "actress", "files", "lawsuit", "against", "tabloid", "magazine", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "U.N. Secretary-General calls for global ceasefire.", "tokens": ["U", ".", "N", ".", "Secretary", "-", "General", "calls", "for", "global", "ceasefire", "."], "labels": ["B-organization", "I-organization", "I-organization", "I-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Thousands evacuated as volcano erupts in Indonesia.", "tokens": ["Thousands", "evacuated", "as", "volcano", "erupts", "in", "Indonesia", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Twitter suspends accounts linked to misinformation.", "tokens": ["Twitter", "suspends", "accounts", "linked", "to", "misinformation", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Germany announces new climate action plan.", "tokens": ["Germany", "announces", "new", "climate", "action", "plan", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Warren Buffett donates billions to charity.", "tokens": ["Warren", "Buffett", "donates", "billions", "to", "charity", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "South Korea reports record high daily COVID-19 cases.", "tokens": ["South", "Korea", "reports", "record", "high", "daily", "COVID", "-", "19", "cases", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Paris Agreement anniversary marked with climate rallies worldwide.", "tokens": ["Paris", "Agreement", "anniversary", "marked", "with", "climate", "rallies", "worldwide", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple announces launch of new iPhone.", "tokens": ["Apple", "announces", "launch", "of", "new", "iPhone", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Earthquake hits Los Angeles, causing minor damages.", "tokens": ["Earthquake", "hits", "Los", "Angeles", ",", "causing", "minor", "damages", "."], "labels": ["O", "O", "B-location", "I-location", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook to acquire virtual reality company for $2 billion.", "tokens": ["Facebook", "to", "acquire", "virtual", "reality", "company", "for", "$", "2", "billion", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Elon Musk's SpaceX to send astronauts to Mars by 2024.", "tokens": ["Elon", "Musk", "'", "s", "SpaceX", "to", "send", "astronauts", "to", "Mars", "by", "2024", "."], "labels": ["B-person", "I-person", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tropical storm warning in effect for Florida coast.", "tokens": ["Tropical", "storm", "warning", "in", "effect", "for", "Florida", "coast", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O", "O"]}
{"sentence": "Amazon faces antitrust investigation in Europe.", "tokens": ["Amazon", "faces", "antitrust", "investigation", "in", "Europe", "."], "labels": ["B-organization", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "New York City to implement vaccine mandate for indoor activities.", "tokens": ["New", "York", "City", "to", "implement", "vaccine", "mandate", "for", "indoor", "activities", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Microsoft steps down amid controversy.", "tokens": ["CEO", "of", "Microsoft", "steps", "down", "amid", "controversy", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Wildfire destroys homes in California.", "tokens": ["Wildfire", "destroys", "homes", "in", "California", "."], "labels": ["O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Marie Curie's Nobel Prize medal sells for record-breaking price.", "tokens": ["Marie", "Curie", "'", "s", "Nobel", "Prize", "medal", "sells", "for", "record", "-", "breaking", "price", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tokyo Olympics committee announces new COVID-19 protocols.", "tokens": ["Tokyo", "Olympics", "committee", "announces", "new", "COVID", "-", "19", "protocols", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations to hold emergency meeting on humanitarian crisis in Yemen.", "tokens": ["United", "Nations", "to", "hold", "emergency", "meeting", "on", "humanitarian", "crisis", "in", "Yemen", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Johnson & Johnson faces lawsuit over opioid crisis.", "tokens": ["Johnson", "&", "Johnson", "faces", "lawsuit", "over", "opioid", "crisis", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Hollywood actress Jennifer Lawrence to star in new film.", "tokens": ["Hollywood", "actress", "Jennifer", "Lawrence", "to", "star", "in", "new", "film", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "European Union introduces new regulations for plastic waste.", "tokens": ["European", "Union", "introduces", "new", "regulations", "for", "plastic", "waste", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Hurricane warning issued for Gulf Coast states.", "tokens": ["Hurricane", "warning", "issued", "for", "Gulf", "Coast", "states", "."], "labels": ["O", "O", "O", "O", "B-location", "I-location", "O", "O"]}
{"sentence": "Nobel Peace Prize awarded to climate activist Greta Thunberg.", "tokens": ["Nobel", "Peace", "Prize", "awarded", "to", "climate", "activist", "Greta", "Thunberg", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "B-person", "I-person", "O"]}
{"sentence": "SpaceX rocket launch successful, delivers supplies to International Space Station.", "tokens": ["SpaceX", "rocket", "launch", "successful", ",", "delivers", "supplies", "to", "International", "Space", "Station", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "I-location", "O"]}
{"sentence": "New York City Mayor announces new policy on recycling.", "tokens": ["New", "York", "City", "Mayor", "announces", "new", "policy", "on", "recycling", "."], "labels": ["B-location", "I-location", "I-location", "B-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple Inc. unveils the new iPhone 13.", "tokens": ["Apple", "Inc", ".", "unveils", "the", "new", "iPhone", "13", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Global warming Summit to be held in Paris.", "tokens": ["Global", "warming", "Summit", "to", "be", "held", "in", "Paris", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "President Biden to meet with G7 leaders next week.", "tokens": ["President", "Biden", "to", "meet", "with", "G7", "leaders", "next", "week", "."], "labels": ["O", "B-person", "O", "O", "O", "B-organization", "O", "O", "O", "O"]}
{"sentence": "Massive wildfire destroys homes in California.", "tokens": ["Massive", "wildfire", "destroys", "homes", "in", "California", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "SpaceX launches new satellite into orbit.", "tokens": ["SpaceX", "launches", "new", "satellite", "into", "orbit", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "World Health Organization issues new guidelines for COVID-19 vaccinations.", "tokens": ["World", "Health", "Organization", "issues", "new", "guidelines", "for", "COVID", "-", "19", "vaccinations", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Paris Fashion Week announces new designers.", "tokens": ["Paris", "Fashion", "Week", "announces", "new", "designers", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations releases report on global hunger crisis.", "tokens": ["United", "Nations", "releases", "report", "on", "global", "hunger", "crisis", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Celebrity couple files for divorce.", "tokens": ["Celebrity", "couple", "files", "for", "divorce", "."], "labels": ["O", "O", "O", "O", "O", "O"]}
{"sentence": "Google to expand its headquarters in Silicon Valley.", "tokens": ["Google", "to", "expand", "its", "headquarters", "in", "Silicon", "Valley", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Hurricane hits Florida coast.", "tokens": ["Hurricane", "hits", "Florida", "coast", "."], "labels": ["O", "O", "B-location", "O", "O"]}
{"sentence": "Prince Harry and Meghan Markle welcome their second child.", "tokens": ["Prince", "Harry", "and", "Meghan", "Markle", "welcome", "their", "second", "child", "."], "labels": ["B-person", "I-person", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "New study shows the benefits of meditation for mental health.", "tokens": ["New", "study", "shows", "the", "benefits", "of", "meditation", "for", "mental", "health", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Sydney Opera House to host international music festival.", "tokens": ["Sydney", "Opera", "House", "to", "host", "international", "music", "festival", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon CEO Jeff Bezos steps down from his position.", "tokens": ["Amazon", "CEO", "Jeff", "Bezos", "steps", "down", "from", "his", "position", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Delta variant of COVID-19 spreads rapidly in Texas.", "tokens": ["Delta", "variant", "of", "COVID", "-", "19", "spreads", "rapidly", "in", "Texas", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Famous chef opens new restaurant in London.", "tokens": ["Famous", "chef", "opens", "new", "restaurant", "in", "London", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Pfizer and BioNTech develop new vaccine for coronavirus.", "tokens": ["Pfizer", "and", "BioNTech", "develop", "new", "vaccine", "for", "coronavirus", "."], "labels": ["B-organization", "O", "B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Ohio State University to require COVID-19 vaccinations for all students.", "tokens": ["Ohio", "State", "University", "to", "require", "COVID", "-", "19", "vaccinations", "for", "all", "students", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tokyo Olympics announce new safety protocols for athletes.", "tokens": ["Tokyo", "Olympics", "announce", "new", "safety", "protocols", "for", "athletes", "."], "labels": ["B-location", "B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Soccer star Cristiano Ronaldo signs new contract with Manchester United.", "tokens": ["Soccer", "star", "Cristiano", "Ronaldo", "signs", "new", "contract", "with", "Manchester", "United", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "B-organization", "I-organization", "O"]}
{"sentence": "Wild bison herd sighted in Yellowstone National Park.", "tokens": ["Wild", "bison", "herd", "sighted", "in", "Yellowstone", "National", "Park", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "I-location", "I-location", "O"]}
{"sentence": "Bill Gates donates $1 million to charity.", "tokens": ["Bill", "Gates", "donates", "$", "1", "million", "to", "charity", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York Stock Exchange experiences record-breaking gains.", "tokens": ["New", "York", "Stock", "Exchange", "experiences", "record", "-", "breaking", "gains", "."], "labels": ["B-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Paris Hilton launches new fashion line.", "tokens": ["Paris", "Hilton", "launches", "new", "fashion", "line", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "United States imposes new sanctions on Russia.", "tokens": ["United", "States", "imposes", "new", "sanctions", "on", "Russia", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Pop singer Taylor Swift releases new album.", "tokens": ["Pop", "singer", "Taylor", "Swift", "releases", "new", "album", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "Microsoft to acquire gaming company Bethesda.", "tokens": ["Microsoft", "to", "acquire", "gaming", "company", "Bethesda", "."], "labels": ["B-organization", "O", "O", "O", "O", "B-organization", "O"]}
{"sentence": "Former President Obama to write a memoir.", "tokens": ["Former", "President", "Obama", "to", "write", "a", "memoir", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "London Marathon raises millions for charity.", "tokens": ["London", "Marathon", "raises", "millions", "for", "charity", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "In-N-Out Burger opens new location in Arizona.", "tokens": ["In", "-", "N", "-", "Out", "Burger", "opens", "new", "location", "in", "Arizona", "."], "labels": ["B-organization", "I-organization", "I-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "High-speed rail project approved for construction in California.", "tokens": ["High", "-", "speed", "rail", "project", "approved", "for", "construction", "in", "California", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Fashion designer Karl Lagerfeld passes away at 85.", "tokens": ["Fashion", "designer", "Karl", "Lagerfeld", "passes", "away", "at", "85", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "American actress Angelina Jolie visits Syrian refugee camp.", "tokens": ["American", "actress", "Angelina", "Jolie", "visits", "Syrian", "refugee", "camp", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "SpaceX founder Elon Musk plans trip to space.", "tokens": ["SpaceX", "founder", "Elon", "Musk", "plans", "trip", "to", "space", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Apple Store robbed in downtown Chicago.", "tokens": ["Apple", "Store", "robbed", "in", "downtown", "Chicago", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "B-location", "O"]}
{"sentence": "World Bank warns of economic downturn in developing countries.", "tokens": ["World", "Bank", "warns", "of", "economic", "downturn", "in", "developing", "countries", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York Yankees defeat Boston Red Sox in extra innings.", "tokens": ["New", "York", "Yankees", "defeat", "Boston", "Red", "Sox", "in", "extra", "innings", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O"]}
{"sentence": "South Africa imposes strict lockdown measures to curb COVID-19 spread.", "tokens": ["South", "Africa", "imposes", "strict", "lockdown", "measures", "to", "curb", "COVID", "-", "19", "spread", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Australian airline Qantas to resume international flights.", "tokens": ["Australian", "airline", "Qantas", "to", "resume", "international", "flights", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Technology giant IBM announces new sustainability initiative.", "tokens": ["Technology", "giant", "IBM", "announces", "new", "sustainability", "initiative", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Instagram influencer faces backlash over controversial post.", "tokens": ["Instagram", "influencer", "faces", "backlash", "over", "controversial", "post", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations appoints new envoy for climate change.", "tokens": ["United", "Nations", "appoints", "new", "envoy", "for", "climate", "change", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New study shows the benefits of exercise for heart health.", "tokens": ["New", "study", "shows", "the", "benefits", "of", "exercise", "for", "heart", "health", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Microsoft CEO Satya Nadella speaks at technology conference.", "tokens": ["Microsoft", "CEO", "Satya", "Nadella", "speaks", "at", "technology", "conference", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Los Angeles Lakers star LeBron James sidelined with ankle injury.", "tokens": ["Los", "Angeles", "Lakers", "star", "LeBron", "James", "sidelined", "with", "ankle", "injury", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "The CEO of Apple announced a new product launch.", "tokens": ["The", "CEO", "of", "Apple", "announced", "a", "new", "product", "launch", "."], "labels": ["O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "London's mayor declares a state of emergency due to extreme weather conditions.", "tokens": ["London", "'", "s", "mayor", "declares", "a", "state", "of", "emergency", "due", "to", "extreme", "weather", "conditions", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations has issued a statement condemning the recent acts of violence in the region.", "tokens": ["The", "United", "Nations", "has", "issued", "a", "statement", "condemning", "the", "recent", "acts", "of", "violence", "in", "the", "region", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Famous actress Jennifer Lawrence will star in a new film adaptation of a classic novel.", "tokens": ["Famous", "actress", "Jennifer", "Lawrence", "will", "star", "in", "a", "new", "film", "adaptation", "of", "a", "classic", "novel", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The European Union has implemented new trade tariffs on imported goods.", "tokens": ["The", "European", "Union", "has", "implemented", "new", "trade", "tariffs", "on", "imported", "goods", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York City plans to invest millions of dollars in upgrading its public transportation system.", "tokens": ["New", "York", "City", "plans", "to", "invest", "millions", "of", "dollars", "in", "upgrading", "its", "public", "transportation", "system", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The former president of the United States will be delivering a keynote speech at the upcoming conference.", "tokens": ["The", "former", "president", "of", "the", "United", "States", "will", "be", "delivering", "a", "keynote", "speech", "at", "the", "upcoming", "conference", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook is facing scrutiny over its data privacy policies.", "tokens": ["Facebook", "is", "facing", "scrutiny", "over", "its", "data", "privacy", "policies", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Thousands of protesters gathered outside the headquarters of the multinational corporation to demand better working conditions.", "tokens": ["Thousands", "of", "protesters", "gathered", "outside", "the", "headquarters", "of", "the", "multinational", "corporation", "to", "demand", "better", "working", "conditions", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Australian Prime Minister announces a new policy to combat climate change.", "tokens": ["Australian", "Prime", "Minister", "announces", "a", "new", "policy", "to", "combat", "climate", "change", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The latest study conducted by the World Health Organization reveals alarming statistics about global obesity rates.", "tokens": ["The", "latest", "study", "conducted", "by", "the", "World", "Health", "Organization", "reveals", "alarming", "statistics", "about", "global", "obesity", "rates", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The CEO of Tesla unveils plans for a revolutionary new electric vehicle.", "tokens": ["The", "CEO", "of", "Tesla", "unveils", "plans", "for", "a", "revolutionary", "new", "electric", "vehicle", "."], "labels": ["O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tokyo Olympics organizers reveal new safety measures for the upcoming games.", "tokens": ["Tokyo", "Olympics", "organizers", "reveal", "new", "safety", "measures", "for", "the", "upcoming", "games", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The famous singer Taylor Swift announces a world tour.", "tokens": ["The", "famous", "singer", "Taylor", "Swift", "announces", "a", "world", "tour", "."], "labels": ["O", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "The World Bank has approved a loan for infrastructure development in developing countries.", "tokens": ["The", "World", "Bank", "has", "approved", "a", "loan", "for", "infrastructure", "development", "in", "developing", "countries", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "NASA launches a new satellite into orbit to study climate patterns.", "tokens": ["NASA", "launches", "a", "new", "satellite", "into", "orbit", "to", "study", "climate", "patterns", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Italian government is facing backlash over its immigration policies.", "tokens": ["The", "Italian", "government", "is", "facing", "backlash", "over", "its", "immigration", "policies", "."], "labels": ["O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Pop star Lady Gaga cancels her upcoming concert due to illness.", "tokens": ["Pop", "star", "Lady", "Gaga", "cancels", "her", "upcoming", "concert", "due", "to", "illness", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations Security Council condemns the recent terrorist attacks in the Middle East.", "tokens": ["The", "United", "Nations", "Security", "Council", "condemns", "the", "recent", "terrorist", "attacks", "in", "the", "Middle", "East", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The city of Paris is implementing new measures to reduce air pollution.", "tokens": ["The", "city", "of", "Paris", "is", "implementing", "new", "measures", "to", "reduce", "air", "pollution", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The CEO of Amazon announces plans for expansion into new markets.", "tokens": ["The", "CEO", "of", "Amazon", "announces", "plans", "for", "expansion", "into", "new", "markets", "."], "labels": ["O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Indian Prime Minister addresses the nation on Independence Day.", "tokens": ["Indian", "Prime", "Minister", "addresses", "the", "nation", "on", "Independence", "Day", "."], "labels": ["B-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The International Monetary Fund warns of a global economic slowdown.", "tokens": ["The", "International", "Monetary", "Fund", "warns", "of", "a", "global", "economic", "slowdown", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Russian government denies allegations of election interference.", "tokens": ["The", "Russian", "government", "denies", "allegations", "of", "election", "interference", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Renowned chef Gordon Ramsay opens a new restaurant in Los Angeles.", "tokens": ["Renowned", "chef", "Gordon", "Ramsay", "opens", "a", "new", "restaurant", "in", "Los", "Angeles", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Trade Organization reports a significant increase in global trade tensions.", "tokens": ["The", "World", "Trade", "Organization", "reports", "a", "significant", "increase", "in", "global", "trade", "tensions", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "London Symphony Orchestra announces a series of upcoming performances.", "tokens": ["London", "Symphony", "Orchestra", "announces", "a", "series", "of", "upcoming", "performances", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The British monarchy faces criticism over its handling of recent scandals.", "tokens": ["The", "British", "monarchy", "faces", "criticism", "over", "its", "handling", "of", "recent", "scandals", "."], "labels": ["O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Microsoft unveils a new line of products at its annual conference.", "tokens": ["Microsoft", "unveils", "a", "new", "line", "of", "products", "at", "its", "annual", "conference", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "French President Macron delivers a speech on national security.", "tokens": ["French", "President", "Macron", "delivers", "a", "speech", "on", "national", "security", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Wildlife Fund is raising awareness about endangered species in Africa.", "tokens": ["The", "World", "Wildlife", "Fund", "is", "raising", "awareness", "about", "endangered", "species", "in", "Africa", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Chinese government announces a crackdown on corruption within the Communist Party.", "tokens": ["The", "Chinese", "government", "announces", "a", "crackdown", "on", "corruption", "within", "the", "Communist", "Party", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Hollywood actor Brad Pitt to star in a new action thriller film.", "tokens": ["Hollywood", "actor", "Brad", "Pitt", "to", "star", "in", "a", "new", "action", "thriller", "film", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The International Red Cross issues a statement on the humanitarian crisis in Syria.", "tokens": ["The", "International", "Red", "Cross", "issues", "a", "statement", "on", "the", "humanitarian", "crisis", "in", "Syria", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tokyo Electric Power Company faces lawsuits over the Fukushima nuclear disaster.", "tokens": ["Tokyo", "Electric", "Power", "Company", "faces", "lawsuits", "over", "the", "Fukushima", "nuclear", "disaster", "."], "labels": ["B-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The German Chancellor meets with world leaders to discuss trade agreements.", "tokens": ["The", "German", "Chancellor", "meets", "with", "world", "leaders", "to", "discuss", "trade", "agreements", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The American Red Cross provides aid to victims of a natural disaster in the Midwest.", "tokens": ["The", "American", "Red", "Cross", "provides", "aid", "to", "victims", "of", "a", "natural", "disaster", "in", "the", "Midwest", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "South Korean pop sensation BTS breaks records with their latest album release.", "tokens": ["South", "Korean", "pop", "sensation", "BTS", "breaks", "records", "with", "their", "latest", "album", "release", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The European Central Bank announces an interest rate hike.", "tokens": ["The", "European", "Central", "Bank", "announces", "an", "interest", "rate", "hike", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New Zealand Prime Minister addresses climate change at the United Nations summit.", "tokens": ["New", "Zealand", "Prime", "Minister", "addresses", "climate", "change", "at", "the", "United", "Nations", "summit", "."], "labels": ["B-person", "I-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Economic Forum releases its annual report on global competitiveness.", "tokens": ["The", "World", "Economic", "Forum", "releases", "its", "annual", "report", "on", "global", "competitiveness", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Australian wildfires devastate wildlife and natural habitats.", "tokens": ["Australian", "wildfires", "devastate", "wildlife", "and", "natural", "habitats", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Gates Foundation pledges millions of dollars to combat infectious diseases in developing countries.", "tokens": ["The", "Gates", "Foundation", "pledges", "millions", "of", "dollars", "to", "combat", "infectious", "diseases", "in", "developing", "countries", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Filipino government faces criticism over human rights abuses.", "tokens": ["The", "Filipino", "government", "faces", "criticism", "over", "human", "rights", "abuses", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The National Aeronautics and Space Administration (NASA) confirms the presence of water on the moon.", "tokens": ["The", "National", "Aeronautics", "and", "Space", "Administration", "(", "NASA", ")", "confirms", "the", "presence", "of", "water", "on", "the", "moon", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Swedish pop group ABBA announces a reunion tour.", "tokens": ["The", "Swedish", "pop", "group", "ABBA", "announces", "a", "reunion", "tour", "."], "labels": ["O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Harvard University researchers make a breakthrough in cancer treatment.", "tokens": ["Harvard", "University", "researchers", "make", "a", "breakthrough", "in", "cancer", "treatment", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "South African President delivers a speech on economic reforms.", "tokens": ["South", "African", "President", "delivers", "a", "speech", "on", "economic", "reforms", "."], "labels": ["B-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Health Organization warns of a new strain of flu virus in Southeast Asia.", "tokens": ["The", "World", "Health", "Organization", "warns", "of", "a", "new", "strain", "of", "flu", "virus", "in", "Southeast", "Asia", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations Educational, Scientific and Cultural Organization (UNESCO) designates a new World Heritage Site.", "tokens": ["The", "United", "Nations", "Educational", ",", "Scientific", "and", "Cultural", "Organization", "(", "UNESCO", ")", "designates", "a", "new", "World", "Heritage", "Site", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "I-organization", "I-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New study shows that eating fruits and vegetables can lower the risk of heart disease.", "tokens": ["New", "study", "shows", "that", "eating", "fruits", "and", "vegetables", "can", "lower", "the", "risk", "of", "heart", "disease", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "President Biden announces new infrastructure plan to improve roads and bridges.", "tokens": ["President", "Biden", "announces", "new", "infrastructure", "plan", "to", "improve", "roads", "and", "bridges", "."], "labels": ["O", "B-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tesla CEO Elon Musk predicts that self-driving cars will be on the road by next year.", "tokens": ["Tesla", "CEO", "Elon", "Musk", "predicts", "that", "self", "-", "driving", "cars", "will", "be", "on", "the", "road", "by", "next", "year", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Japan to host 2021 Summer Olympics despite concerns over COVID-19.", "tokens": ["Japan", "to", "host", "2021", "Summer", "Olympics", "despite", "concerns", "over", "COVID", "-", "19", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple unveils new iPhone with advanced features and expanded battery life.", "tokens": ["Apple", "unveils", "new", "iPhone", "with", "advanced", "features", "and", "expanded", "battery", "life", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Rising star actress Emma Watson takes a break from acting to focus on activism.", "tokens": ["Rising", "star", "actress", "Emma", "Watson", "takes", "a", "break", "from", "acting", "to", "focus", "on", "activism", "."], "labels": ["O", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Wildfires continue to ravage California, forcing thousands to evacuate their homes.", "tokens": ["Wildfires", "continue", "to", "ravage", "California", ",", "forcing", "thousands", "to", "evacuate", "their", "homes", "."], "labels": ["O", "O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon founder Jeff Bezos announces plans for space tourism company.", "tokens": ["Amazon", "founder", "Jeff", "Bezos", "announces", "plans", "for", "space", "tourism", "company", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Study finds that regular exercise can improve mental health and cognitive function.", "tokens": ["Study", "finds", "that", "regular", "exercise", "can", "improve", "mental", "health", "and", "cognitive", "function", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "European Union leaders meet to discuss climate change and environmental policies.", "tokens": ["European", "Union", "leaders", "meet", "to", "discuss", "climate", "change", "and", "environmental", "policies", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations convened a special session to discuss climate change.", "tokens": ["The", "United", "Nations", "convened", "a", "special", "session", "to", "discuss", "climate", "change", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "B-organization", "I-organization", "O"]}
{"sentence": "President Biden announced new policies on immigration reform.", "tokens": ["President", "Biden", "announced", "new", "policies", "on", "immigration", "reform", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The city of London broke ground on a new public transportation project.", "tokens": ["The", "city", "of", "London", "broke", "ground", "on", "a", "new", "public", "transportation", "project", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon employees protest for better working conditions.", "tokens": ["Amazon", "employees", "protest", "for", "better", "working", "conditions", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Elon Musk launches new space exploration company.", "tokens": ["Elon", "Musk", "launches", "new", "space", "exploration", "company", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The New York Stock Exchange experienced a record high trading day.", "tokens": ["The", "New", "York", "Stock", "Exchange", "experienced", "a", "record", "high", "trading", "day", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Thousands of people gathered in Paris to protest vaccine mandates.", "tokens": ["Thousands", "of", "people", "gathered", "in", "Paris", "to", "protest", "vaccine", "mandates", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Apple releases the latest iPhone model.", "tokens": ["Apple", "releases", "the", "latest", "iPhone", "model", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Spanish soccer star Lionel Messi signs with Paris Saint-Germain.", "tokens": ["Spanish", "soccer", "star", "Lionel", "Messi", "signs", "with", "Paris", "Saint", "-", "Germain", "."], "labels": ["O", "O", "O", "B-person", "I-person", "O", "O", "B-organization", "I-organization", "I-organization", "I-organization", "O"]}
{"sentence": "The European Union announces new trade agreements with China.", "tokens": ["The", "European", "Union", "announces", "new", "trade", "agreements", "with", "China", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "CEO of Tesla, Elon Musk, becomes the world's richest person.", "tokens": ["CEO", "of", "Tesla", ",", "Elon", "Musk", ",", "becomes", "the", "world", "'", "s", "richest", "person", "."], "labels": ["O", "O", "B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Hurricane season devastates coastal communities in the Caribbean.", "tokens": ["Hurricane", "season", "devastates", "coastal", "communities", "in", "the", "Caribbean", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Australian government invests in renewable energy projects.", "tokens": ["Australian", "government", "invests", "in", "renewable", "energy", "projects", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Grammy award-winning artist Beyonc\u00e9 releases new album.", "tokens": ["Grammy", "award", "-", "winning", "artist", "Beyonc\u00e9", "releases", "new", "album", "."], "labels": ["O", "O", "O", "O", "O", "B-person", "O", "O", "O", "O"]}
{"sentence": "Russian President Vladimir Putin meets with German Chancellor Angela Merkel.", "tokens": ["Russian", "President", "Vladimir", "Putin", "meets", "with", "German", "Chancellor", "Angela", "Merkel", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "B-person", "I-person", "I-person", "I-person", "O"]}
{"sentence": "Tokyo to host 2021 Summer Olympics.", "tokens": ["Tokyo", "to", "host", "2021", "Summer", "Olympics", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook under fire for privacy breaches.", "tokens": ["Facebook", "under", "fire", "for", "privacy", "breaches", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Health Organization warns of a new strain of flu virus.", "tokens": ["The", "World", "Health", "Organization", "warns", "of", "a", "new", "strain", "of", "flu", "virus", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "South African activist Desmond Tutu awarded Nobel Peace Prize.", "tokens": ["South", "African", "activist", "Desmond", "Tutu", "awarded", "Nobel", "Peace", "Prize", "."], "labels": ["O", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon announces plans to open new headquarters in Atlanta.", "tokens": ["Amazon", "announces", "plans", "to", "open", "new", "headquarters", "in", "Atlanta", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Scientists discover new species of dinosaur in Patagonia.", "tokens": ["Scientists", "discover", "new", "species", "of", "dinosaur", "in", "Patagonia", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Apple releases new iPhone with advanced camera features.", "tokens": ["Apple", "releases", "new", "iPhone", "with", "advanced", "camera", "features", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New study shows correlation between social media use and mental health issues.", "tokens": ["New", "study", "shows", "correlation", "between", "social", "media", "use", "and", "mental", "health", "issues", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "European Union imposes sanctions on Russia.", "tokens": ["European", "Union", "imposes", "sanctions", "on", "Russia", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "B-location", "O"]}
{"sentence": "Actress Emma Watson speaks out against gender inequality in Hollywood.", "tokens": ["Actress", "Emma", "Watson", "speaks", "out", "against", "gender", "inequality", "in", "Hollywood", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Ford recalls over 100,000 vehicles due to safety concerns.", "tokens": ["Ford", "recalls", "over", "100", ",", "000", "vehicles", "due", "to", "safety", "concerns", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations announces humanitarian aid for refugees in Syria.", "tokens": ["United", "Nations", "announces", "humanitarian", "aid", "for", "refugees", "in", "Syria", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "CEO Elon Musk steps down from Tesla.", "tokens": ["CEO", "Elon", "Musk", "steps", "down", "from", "Tesla", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "B-organization", "O"]}
{"sentence": "New York City implements new COVID-19 restrictions.", "tokens": ["New", "York", "City", "implements", "new", "COVID", "-", "19", "restrictions", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Former President Obama to release memoir next year.", "tokens": ["Former", "President", "Obama", "to", "release", "memoir", "next", "year", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Starbucks to expand its presence in China.", "tokens": ["Starbucks", "to", "expand", "its", "presence", "in", "China", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Tennis player Serena Williams advances to the semifinals.", "tokens": ["Tennis", "player", "Serena", "Williams", "advances", "to", "the", "semifinals", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Google faces antitrust lawsuit from the Department of Justice.", "tokens": ["Google", "faces", "antitrust", "lawsuit", "from", "the", "Department", "of", "Justice", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-organization", "I-organization", "I-organization", "O"]}
{"sentence": "Hurricane season expected to be particularly active this year.", "tokens": ["Hurricane", "season", "expected", "to", "be", "particularly", "active", "this", "year", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Microsoft acquires artificial intelligence startup.", "tokens": ["Microsoft", "acquires", "artificial", "intelligence", "startup", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "French government proposes new tax reforms.", "tokens": ["French", "government", "proposes", "new", "tax", "reforms", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Pilot reported seeing unidentified flying object over Arizona.", "tokens": ["Pilot", "reported", "seeing", "unidentified", "flying", "object", "over", "Arizona", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Legendary musician Paul McCartney to perform at charity concert.", "tokens": ["Legendary", "musician", "Paul", "McCartney", "to", "perform", "at", "charity", "concert", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Walmart to launch same-day delivery service.", "tokens": ["Walmart", "to", "launch", "same", "-", "day", "delivery", "service", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "California wildfires continue to devastate communities.", "tokens": ["California", "wildfires", "continue", "to", "devastate", "communities", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO Tim Cook unveils new line of products at Apple event.", "tokens": ["CEO", "Tim", "Cook", "unveils", "new", "line", "of", "products", "at", "Apple", "event", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "B-organization", "O", "O"]}
{"sentence": "Renowned chef Gordon Ramsay opens new restaurant in Las Vegas.", "tokens": ["Renowned", "chef", "Gordon", "Ramsay", "opens", "new", "restaurant", "in", "Las", "Vegas", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "South Korea imposes strict lockdown measures amid surge in COVID-19 cases.", "tokens": ["South", "Korea", "imposes", "strict", "lockdown", "measures", "amid", "surge", "in", "COVID", "-", "19", "cases", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Bitcoin reaches new all-time high in trading value.", "tokens": ["Bitcoin", "reaches", "new", "all", "-", "time", "high", "in", "trading", "value", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon founder Jeff Bezos steps down as CEO.", "tokens": ["Amazon", "founder", "Jeff", "Bezos", "steps", "down", "as", "CEO", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Broadway musical Hamilton to return to theaters next year.", "tokens": ["Broadway", "musical", "Hamilton", "to", "return", "to", "theaters", "next", "year", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "AstraZeneca vaccine found to be highly effective in new study.", "tokens": ["AstraZeneca", "vaccine", "found", "to", "be", "highly", "effective", "in", "new", "study", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Manchester United signs new sponsorship deal with major tech company.", "tokens": ["Manchester", "United", "signs", "new", "sponsorship", "deal", "with", "major", "tech", "company", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "McDonald's introduces new plant-based burger to its menu.", "tokens": ["McDonald", "'", "s", "introduces", "new", "plant", "-", "based", "burger", "to", "its", "menu", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Swiss government announces plans to reduce carbon emissions.", "tokens": ["Swiss", "government", "announces", "plans", "to", "reduce", "carbon", "emissions", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Actress Angelina Jolie appointed as special envoy for the United Nations High Commissioner for Refugees.", "tokens": ["Actress", "Angelina", "Jolie", "appointed", "as", "special", "envoy", "for", "the", "United", "Nations", "High", "Commissioner", "for", "Refugees", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "B-organization", "I-organization", "I-organization", "I-organization", "I-organization", "I-organization", "O"]}
{"sentence": "Violent protests erupt in downtown Los Angeles.", "tokens": ["Violent", "protests", "erupt", "in", "downtown", "Los", "Angeles", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "CEO Mark Zuckerberg faces congressional hearing on privacy issues.", "tokens": ["CEO", "Mark", "Zuckerberg", "faces", "congressional", "hearing", "on", "privacy", "issues", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tokyo Olympics organizers announce new COVID-19 protocols for athletes.", "tokens": ["Tokyo", "Olympics", "organizers", "announce", "new", "COVID", "-", "19", "protocols", "for", "athletes", "."], "labels": ["B-location", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Starbucks CEO Howard Schultz to step down from his position.", "tokens": ["Starbucks", "CEO", "Howard", "Schultz", "to", "step", "down", "from", "his", "position", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New Zealand experiences 6.5 magnitude earthquake.", "tokens": ["New", "Zealand", "experiences", "6", ".", "5", "magnitude", "earthquake", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Grammy-winning artist Beyonc\u00e9 to headline music festival in Chicago.", "tokens": ["Grammy", "-", "winning", "artist", "Beyonc\u00e9", "to", "headline", "music", "festival", "in", "Chicago", "."], "labels": ["O", "O", "O", "O", "B-person", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Tesla announces plans to build new Gigafactory in Texas.", "tokens": ["Tesla", "announces", "plans", "to", "build", "new", "Gigafactory", "in", "Texas", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Germany approves new climate change legislation.", "tokens": ["Germany", "approves", "new", "climate", "change", "legislation", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Legendary director Steven Spielberg to produce new film.", "tokens": ["Legendary", "director", "Steven", "Spielberg", "to", "produce", "new", "film", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "SpaceX CEO Elon Musk announces plans for manned mission to Mars.", "tokens": ["SpaceX", "CEO", "Elon", "Musk", "announces", "plans", "for", "manned", "mission", "to", "Mars", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Australian government launches inquiry into banking industry practices.", "tokens": ["Australian", "government", "launches", "inquiry", "into", "banking", "industry", "practices", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Major snowstorm expected to hit northeastern United States.", "tokens": ["Major", "snowstorm", "expected", "to", "hit", "northeastern", "United", "States", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Instagram introduces new feature to combat cyberbullying.", "tokens": ["Instagram", "introduces", "new", "feature", "to", "combat", "cyberbullying", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York City mayor announces new infrastructure plan.", "tokens": ["New", "York", "City", "mayor", "announces", "new", "infrastructure", "plan", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Apple speaks at annual tech conference.", "tokens": ["CEO", "of", "Apple", "speaks", "at", "annual", "tech", "conference", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Protests erupt in Paris over new labor laws.", "tokens": ["Protests", "erupt", "in", "Paris", "over", "new", "labor", "laws", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Famous actor arrested for drunk driving.", "tokens": ["Famous", "actor", "arrested", "for", "drunk", "driving", "."], "labels": ["O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations report highlights climate change concerns.", "tokens": ["United", "Nations", "report", "highlights", "climate", "change", "concerns", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Prime Minister of Japan meets with President of the United States.", "tokens": ["Prime", "Minister", "of", "Japan", "meets", "with", "President", "of", "the", "United", "States", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "B-person", "I-person", "I-person", "I-person", "I-person", "O"]}
{"sentence": "Major earthquake hits southern California.", "tokens": ["Major", "earthquake", "hits", "southern", "California", "."], "labels": ["O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Olympic athlete breaks world record in 100m dash.", "tokens": ["Olympic", "athlete", "breaks", "world", "record", "in", "100m", "dash", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New study shows link between diet and heart disease.", "tokens": ["New", "study", "shows", "link", "between", "diet", "and", "heart", "disease", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Hurricane warning issued for Florida coast.", "tokens": ["Hurricane", "warning", "issued", "for", "Florida", "coast", "."], "labels": ["O", "O", "O", "O", "B-location", "O", "O"]}
{"sentence": "Apple announces new iPhone models.", "tokens": ["Apple", "announces", "new", "iPhone", "models", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "President Biden visits troops in Afghanistan.", "tokens": ["President", "Biden", "visits", "troops", "in", "Afghanistan", "."], "labels": ["B-person", "I-person", "O", "O", "O", "B-location", "O"]}
{"sentence": "Google CEO resigns amid controversy.", "tokens": ["Google", "CEO", "resigns", "amid", "controversy", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "California wildfires rage on, threatening homes.", "tokens": ["California", "wildfires", "rage", "on", ",", "threatening", "homes", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tesla stock reaches all-time high.", "tokens": ["Tesla", "stock", "reaches", "all", "-", "time", "high", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Dr. Anthony Fauci warns of potential COVID-19 surge.", "tokens": ["Dr", ".", "Anthony", "Fauci", "warns", "of", "potential", "COVID", "-", "19", "surge", "."], "labels": ["B-person", "I-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations calls for ceasefire in Syria.", "tokens": ["United", "Nations", "calls", "for", "ceasefire", "in", "Syria", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Amazon Prime Day sales exceed expectations.", "tokens": ["Amazon", "Prime", "Day", "sales", "exceed", "expectations", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York City imposes vaccine mandate for indoor dining.", "tokens": ["New", "York", "City", "imposes", "vaccine", "mandate", "for", "indoor", "dining", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook faces backlash over privacy concerns.", "tokens": ["Facebook", "faces", "backlash", "over", "privacy", "concerns", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Angela Merkel elected as President of the European Union.", "tokens": ["Angela", "Merkel", "elected", "as", "President", "of", "the", "European", "Union", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "B-organization", "I-organization", "O"]}
{"sentence": "Tokyo Olympics postponed due to pandemic.", "tokens": ["Tokyo", "Olympics", "postponed", "due", "to", "pandemic", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "International Monetary Fund projects global economic slowdown.", "tokens": ["International", "Monetary", "Fund", "projects", "global", "economic", "slowdown", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "FDA approves new cancer treatment.", "tokens": ["FDA", "approves", "new", "cancer", "treatment", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "World Health Organization declares polio eradicated in Africa.", "tokens": ["World", "Health", "Organization", "declares", "polio", "eradicated", "in", "Africa", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Uber partners with local restaurant to offer food delivery.", "tokens": ["Uber", "partners", "with", "local", "restaurant", "to", "offer", "food", "delivery", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple plans to open a new store in downtown Miami.", "tokens": ["Apple", "plans", "to", "open", "a", "new", "store", "in", "downtown", "Miami", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "The CEO of Tesla announced a new electric car model at the conference.", "tokens": ["The", "CEO", "of", "Tesla", "announced", "a", "new", "electric", "car", "model", "at", "the", "conference", "."], "labels": ["O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Mexico surpasses 100,000 COVID-19 deaths.", "tokens": ["Mexico", "surpasses", "100", ",", "000", "COVID", "-", "19", "deaths", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Japanese Prime Minister to visit South Korea next week.", "tokens": ["Japanese", "Prime", "Minister", "to", "visit", "South", "Korea", "next", "week", "."], "labels": ["B-person", "I-person", "I-person", "O", "O", "B-location", "I-location", "O", "O", "O"]}
{"sentence": "Facebook acquires a leading artificial intelligence startup.", "tokens": ["Facebook", "acquires", "a", "leading", "artificial", "intelligence", "startup", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The famous actor, Tom Hanks, to star in a new war film.", "tokens": ["The", "famous", "actor", ",", "Tom", "Hanks", ",", "to", "star", "in", "a", "new", "war", "film", "."], "labels": ["O", "O", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Germany imposes strict lockdown measures in response to rising COVID-19 cases.", "tokens": ["Germany", "imposes", "strict", "lockdown", "measures", "in", "response", "to", "rising", "COVID", "-", "19", "cases", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations peacekeeping mission in the Middle East extends for another year.", "tokens": ["The", "United", "Nations", "peacekeeping", "mission", "in", "the", "Middle", "East", "extends", "for", "another", "year", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "B-location", "I-location", "O", "O", "O", "O", "O"]}
{"sentence": "New York City to invest $1 billion in affordable housing initiatives.", "tokens": ["New", "York", "City", "to", "invest", "$", "1", "billion", "in", "affordable", "housing", "initiatives", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The renowned scientist Marie Curie awarded the Nobel Prize for her groundbreaking research.", "tokens": ["The", "renowned", "scientist", "Marie", "Curie", "awarded", "the", "Nobel", "Prize", "for", "her", "groundbreaking", "research", "."], "labels": ["O", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Samsung to launch its latest smartphone model next month.", "tokens": ["Samsung", "to", "launch", "its", "latest", "smartphone", "model", "next", "month", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The president of France addresses the nation in a televised speech.", "tokens": ["The", "president", "of", "France", "addresses", "the", "nation", "in", "a", "televised", "speech", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Harvard University announces new financial aid initiatives for low-income students.", "tokens": ["Harvard", "University", "announces", "new", "financial", "aid", "initiatives", "for", "low", "-", "income", "students", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Hurricane Maria causes widespread devastation in Puerto Rico.", "tokens": ["Hurricane", "Maria", "causes", "widespread", "devastation", "in", "Puerto", "Rico", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "The popular singer Taylor Swift releases a new album.", "tokens": ["The", "popular", "singer", "Taylor", "Swift", "releases", "a", "new", "album", "."], "labels": ["O", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Google's parent company, Alphabet, faces antitrust investigation by the European Union.", "tokens": ["Google", "'", "s", "parent", "company", ",", "Alphabet", ",", "faces", "antitrust", "investigation", "by", "the", "European", "Union", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "The mayor of London introduces new cycling infrastructure to reduce traffic congestion.", "tokens": ["The", "mayor", "of", "London", "introduces", "new", "cycling", "infrastructure", "to", "reduce", "traffic", "congestion", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Microsoft announces a partnership with a leading tech startup to develop innovative software solutions.", "tokens": ["Microsoft", "announces", "a", "partnership", "with", "a", "leading", "tech", "startup", "to", "develop", "innovative", "software", "solutions", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The renowned chef Julia Child's cookbook sells for a record price at auction.", "tokens": ["The", "renowned", "chef", "Julia", "Child", "'", "s", "cookbook", "sells", "for", "a", "record", "price", "at", "auction", "."], "labels": ["O", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Brazil's president signs an executive order to address deforestation in the Amazon rainforest.", "tokens": ["Brazil", "'", "s", "president", "signs", "an", "executive", "order", "to", "address", "deforestation", "in", "the", "Amazon", "rainforest", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "SpaceX launches a new satellite into orbit.", "tokens": ["SpaceX", "launches", "a", "new", "satellite", "into", "orbit", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The famous basketball player Michael Jordan to invest in a new sports team.", "tokens": ["The", "famous", "basketball", "player", "Michael", "Jordan", "to", "invest", "in", "a", "new", "sports", "team", "."], "labels": ["O", "O", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The European Central Bank announces new stimulus measures to support the economy.", "tokens": ["The", "European", "Central", "Bank", "announces", "new", "stimulus", "measures", "to", "support", "the", "economy", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The iconic landmark, the Eiffel Tower, reopens to tourists after a lengthy renovation.", "tokens": ["The", "iconic", "landmark", ",", "the", "Eiffel", "Tower", ",", "reopens", "to", "tourists", "after", "a", "lengthy", "renovation", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United States announces new trade tariffs on Chinese goods.", "tokens": ["The", "United", "States", "announces", "new", "trade", "tariffs", "on", "Chinese", "goods", "."], "labels": ["O", "B-location", "I-location", "O", "O", "O", "O", "O", "B-organization", "O", "O"]}
{"sentence": "Mary Smith appointed as new CEO of ABC Corporation.", "tokens": ["Mary", "Smith", "appointed", "as", "new", "CEO", "of", "ABC", "Corporation", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "B-organization", "I-organization", "O"]}
{"sentence": "Firefighters battle massive wildfire in California.", "tokens": ["Firefighters", "battle", "massive", "wildfire", "in", "California", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Researchers discover potential new treatment for Alzheimer's disease.", "tokens": ["Researchers", "discover", "potential", "new", "treatment", "for", "Alzheimer", "'", "s", "disease", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "President Johnson delivers annual State of the Union address.", "tokens": ["President", "Johnson", "delivers", "annual", "State", "of", "the", "Union", "address", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "NASA launches new Mars rover mission.", "tokens": ["NASA", "launches", "new", "Mars", "rover", "mission", "."], "labels": ["B-organization", "O", "O", "B-location", "O", "O", "O"]}
{"sentence": "Prime Minister Lee visits South Korea to discuss trade agreements.", "tokens": ["Prime", "Minister", "Lee", "visits", "South", "Korea", "to", "discuss", "trade", "agreements", "."], "labels": ["B-person", "I-person", "I-person", "O", "B-location", "I-location", "O", "O", "O", "O", "O"]}
{"sentence": "Tennis star Serena Williams wins Wimbledon championship.", "tokens": ["Tennis", "star", "Serena", "Williams", "wins", "Wimbledon", "championship", "."], "labels": ["O", "O", "B-person", "I-person", "O", "B-location", "O", "O"]}
{"sentence": "A new study reveals the impact of climate change on polar bears in the Arctic.", "tokens": ["A", "new", "study", "reveals", "the", "impact", "of", "climate", "change", "on", "polar", "bears", "in", "the", "Arctic", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "OPEC members meet to discuss oil production quotas.", "tokens": ["OPEC", "members", "meet", "to", "discuss", "oil", "production", "quotas", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Environmental activist Greta Thunberg speaks at United Nations climate summit.", "tokens": ["Environmental", "activist", "Greta", "Thunberg", "speaks", "at", "United", "Nations", "climate", "summit", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "B-organization", "I-organization", "O", "O", "O"]}
{"sentence": "Japan's economy enters recession due to pandemic-related lockdowns.", "tokens": ["Japan", "'", "s", "economy", "enters", "recession", "due", "to", "pandemic", "-", "related", "lockdowns", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "SpaceX successfully launches satellite into orbit.", "tokens": ["SpaceX", "successfully", "launches", "satellite", "into", "orbit", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Local brewery wins top prize at international beer competition.", "tokens": ["Local", "brewery", "wins", "top", "prize", "at", "international", "beer", "competition", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Famous actor Brad Pitt stars in new blockbuster film.", "tokens": ["Famous", "actor", "Brad", "Pitt", "stars", "in", "new", "blockbuster", "film", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Health Organization declares global pandemic.", "tokens": ["The", "World", "Health", "Organization", "declares", "global", "pandemic", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O"]}
{"sentence": "Nature conservation group protests logging in Amazon rainforest.", "tokens": ["Nature", "conservation", "group", "protests", "logging", "in", "Amazon", "rainforest", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "UK Parliament votes to approve new Brexit deal.", "tokens": ["UK", "Parliament", "votes", "to", "approve", "new", "Brexit", "deal", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "B-organization", "O", "O"]}
{"sentence": "Microsoft acquires AI startup for $1 billion.", "tokens": ["Microsoft", "acquires", "AI", "startup", "for", "$", "1", "billion", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Egyptian archaeologists uncover ancient tomb in Luxor.", "tokens": ["Egyptian", "archaeologists", "uncover", "ancient", "tomb", "in", "Luxor", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Famous chef Gordon Ramsay opens new restaurant in London.", "tokens": ["Famous", "chef", "Gordon", "Ramsay", "opens", "new", "restaurant", "in", "London", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "WHO warns of potential vaccine shortages in developing countries.", "tokens": ["WHO", "warns", "of", "potential", "vaccine", "shortages", "in", "developing", "countries", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Uber driver charged with assault in Los Angeles.", "tokens": ["Uber", "driver", "charged", "with", "assault", "in", "Los", "Angeles", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Facebook faces antitrust investigation by US government.", "tokens": ["Facebook", "faces", "antitrust", "investigation", "by", "US", "government", "."], "labels": ["B-organization", "O", "O", "O", "O", "B-location", "O", "O"]}
{"sentence": "Activist Malala Yousafzai awarded Nobel Peace Prize.", "tokens": ["Activist", "Malala", "Yousafzai", "awarded", "Nobel", "Peace", "Prize", "."], "labels": ["O", "B-person", "I-person", "O", "B-organization", "I-organization", "I-organization", "O"]}
{"sentence": "Severe weather warnings issued for Midwest region.", "tokens": ["Severe", "weather", "warnings", "issued", "for", "Midwest", "region", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "O", "O"]}
{"sentence": "Harvard University announces new scholarship program for low-income students.", "tokens": ["Harvard", "University", "announces", "new", "scholarship", "program", "for", "low", "-", "income", "students", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Fashion designer Stella McCartney launches sustainable clothing line.", "tokens": ["Fashion", "designer", "Stella", "McCartney", "launches", "sustainable", "clothing", "line", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Oil spill contaminates coastal waters in Nigeria.", "tokens": ["Oil", "spill", "contaminates", "coastal", "waters", "in", "Nigeria", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Former president Barack Obama releases memoir.", "tokens": ["Former", "president", "Barack", "Obama", "releases", "memoir", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O"]}
{"sentence": "Volunteers clean up litter on beach in Sydney.", "tokens": ["Volunteers", "clean", "up", "litter", "on", "beach", "in", "Sydney", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Japanese automaker Toyota recalls millions of vehicles.", "tokens": ["Japanese", "automaker", "Toyota", "recalls", "millions", "of", "vehicles", "."], "labels": ["B-organization", "O", "B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "UNICEF provides humanitarian aid to refugees in war-torn region.", "tokens": ["UNICEF", "provides", "humanitarian", "aid", "to", "refugees", "in", "war", "-", "torn", "region", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Olympic champion Usain Bolt announces retirement from track and field.", "tokens": ["Olympic", "champion", "Usain", "Bolt", "announces", "retirement", "from", "track", "and", "field", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Earthquake rocks major city in Indonesia.", "tokens": ["Earthquake", "rocks", "major", "city", "in", "Indonesia", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Pfizer and Moderna report positive results from COVID-19 vaccine trials.", "tokens": ["Pfizer", "and", "Moderna", "report", "positive", "results", "from", "COVID", "-", "19", "vaccine", "trials", "."], "labels": ["B-organization", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Climate activist groups organize protest outside government headquarters.", "tokens": ["Climate", "activist", "groups", "organize", "protest", "outside", "government", "headquarters", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Renowned author J.K. Rowling publishes new book.", "tokens": ["Renowned", "author", "J", ".", "K", ".", "Rowling", "publishes", "new", "book", "."], "labels": ["O", "O", "B-person", "I-person", "I-person", "I-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "Italian government launches investigation into corruption allegations.", "tokens": ["Italian", "government", "launches", "investigation", "into", "corruption", "allegations", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Thousands attend protest against police brutality in Chicago.", "tokens": ["Thousands", "attend", "protest", "against", "police", "brutality", "in", "Chicago", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "SpaceX founder Elon Musk announces plans for Mars colonization.", "tokens": ["SpaceX", "founder", "Elon", "Musk", "announces", "plans", "for", "Mars", "colonization", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "B-location", "O", "O"]}
{"sentence": "World Bank approves funding for infrastructure project in Africa.", "tokens": ["World", "Bank", "approves", "funding", "for", "infrastructure", "project", "in", "Africa", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Soccer star Lionel Messi signs record-breaking contract with Paris Saint-Germain.", "tokens": ["Soccer", "star", "Lionel", "Messi", "signs", "record", "-", "breaking", "contract", "with", "Paris", "Saint", "-", "Germain", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "B-organization", "I-organization", "I-organization", "I-organization", "O"]}
{"sentence": "Philadelphia Eagles quarterback suffers season-ending injury.", "tokens": ["Philadelphia", "Eagles", "quarterback", "suffers", "season", "-", "ending", "injury", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "British Prime Minister Boris Johnson declares national emergency.", "tokens": ["British", "Prime", "Minister", "Boris", "Johnson", "declares", "national", "emergency", "."], "labels": ["O", "O", "O", "B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "Australian scientists make breakthrough in cancer research.", "tokens": ["Australian", "scientists", "make", "breakthrough", "in", "cancer", "research", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations reports a surge in violence in the Middle East.", "tokens": ["The", "United", "Nations", "reports", "a", "surge", "in", "violence", "in", "the", "Middle", "East", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Apple announces new iPhone release date.", "tokens": ["Apple", "announces", "new", "iPhone", "release", "date", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "German Chancellor Angela Merkel meets with French President Emmanuel Macron.", "tokens": ["German", "Chancellor", "Angela", "Merkel", "meets", "with", "French", "President", "Emmanuel", "Macron", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "B-person", "I-person", "O"]}
{"sentence": "Tokyo Olympics to proceed without international spectators.", "tokens": ["Tokyo", "Olympics", "to", "proceed", "without", "international", "spectators", "."], "labels": ["B-location", "B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tesla founder Elon Musk unveils plans for Mars colonization.", "tokens": ["Tesla", "founder", "Elon", "Musk", "unveils", "plans", "for", "Mars", "colonization", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "B-location", "O", "O"]}
{"sentence": "NATO allies meet to discuss global security concerns.", "tokens": ["NATO", "allies", "meet", "to", "discuss", "global", "security", "concerns", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Hurricane Florence batters the U.S. East Coast.", "tokens": ["Hurricane", "Florence", "batters", "the", "U", ".", "S", ".", "East", "Coast", "."], "labels": ["B-location", "I-location", "O", "O", "B-location", "I-location", "I-location", "I-location", "I-location", "I-location", "O"]}
{"sentence": "Amazon employees plan to unionize for better working conditions.", "tokens": ["Amazon", "employees", "plan", "to", "unionize", "for", "better", "working", "conditions", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Barcelona FC appoints new head coach.", "tokens": ["Barcelona", "FC", "appoints", "new", "head", "coach", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Apple Inc. announces new iPhone release.", "tokens": ["Apple", "Inc", ".", "announces", "new", "iPhone", "release", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "President Biden visits the White House.", "tokens": ["President", "Biden", "visits", "the", "White", "House", "."], "labels": ["B-person", "I-person", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Tokyo Olympics to be held without spectators.", "tokens": ["Tokyo", "Olympics", "to", "be", "held", "without", "spectators", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon launches new delivery drone program.", "tokens": ["Amazon", "launches", "new", "delivery", "drone", "program", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO Elon Musk unveils plans for Mars colonization.", "tokens": ["CEO", "Elon", "Musk", "unveils", "plans", "for", "Mars", "colonization", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "B-location", "O", "O"]}
{"sentence": "United Nations calls for ceasefire in the Middle East.", "tokens": ["United", "Nations", "calls", "for", "ceasefire", "in", "the", "Middle", "East", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Xerox Corporation reports record profits for the quarter.", "tokens": ["Xerox", "Corporation", "reports", "record", "profits", "for", "the", "quarter", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "British Prime Minister Johnson tests positive for COVID-19.", "tokens": ["British", "Prime", "Minister", "Johnson", "tests", "positive", "for", "COVID", "-", "19", "."], "labels": ["O", "B-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Google announces expansion of data center in Texas.", "tokens": ["Google", "announces", "expansion", "of", "data", "center", "in", "Texas", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "European Union approves new trade agreement with Canada.", "tokens": ["European", "Union", "approves", "new", "trade", "agreement", "with", "Canada", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "SpaceX successfully launches communication satellite into orbit.", "tokens": ["SpaceX", "successfully", "launches", "communication", "satellite", "into", "orbit", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "London Stock Exchange experiences record trading volume.", "tokens": ["London", "Stock", "Exchange", "experiences", "record", "trading", "volume", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Microsoft acquires artificial intelligence startup for $1 billion.", "tokens": ["Microsoft", "acquires", "artificial", "intelligence", "startup", "for", "$", "1", "billion", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Florida Governor DeSantis signs controversial voting rights bill.", "tokens": ["Florida", "Governor", "DeSantis", "signs", "controversial", "voting", "rights", "bill", "."], "labels": ["B-location", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Coca-Cola announces retirement after 30 years with the company.", "tokens": ["CEO", "of", "Coca", "-", "Cola", "announces", "retirement", "after", "30", "years", "with", "the", "company", "."], "labels": ["O", "O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Australian Prime Minister Morrison addresses climate change at G7 summit.", "tokens": ["Australian", "Prime", "Minister", "Morrison", "addresses", "climate", "change", "at", "G7", "summit", "."], "labels": ["B-person", "I-person", "I-person", "I-person", "O", "O", "O", "O", "B-organization", "O", "O"]}
{"sentence": "Bank of America introduces new sustainable investing initiative.", "tokens": ["Bank", "of", "America", "introduces", "new", "sustainable", "investing", "initiative", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Los Angeles Lakers sign top draft pick to multi-year contract.", "tokens": ["Los", "Angeles", "Lakers", "sign", "top", "draft", "pick", "to", "multi", "-", "year", "contract", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "WHO declares global pandemic due to new virus strain.", "tokens": ["WHO", "declares", "global", "pandemic", "due", "to", "new", "virus", "strain", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO Mark Zuckerberg testifies before Congress on data privacy.", "tokens": ["CEO", "Mark", "Zuckerberg", "testifies", "before", "Congress", "on", "data", "privacy", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New Zealand Prime Minister Ardern announces economic stimulus package.", "tokens": ["New", "Zealand", "Prime", "Minister", "Ardern", "announces", "economic", "stimulus", "package", "."], "labels": ["B-location", "I-location", "O", "O", "B-person", "O", "O", "O", "O", "O"]}
{"sentence": "Reuters to lay off 10% of workforce in cost-cutting measure.", "tokens": ["Reuters", "to", "lay", "off", "10", "%", "of", "workforce", "in", "cost", "-", "cutting", "measure", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "NBA Finals to be held in Phoenix for the first time.", "tokens": ["NBA", "Finals", "to", "be", "held", "in", "Phoenix", "for", "the", "first", "time", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "South Korean President Moon Jae-in meets with North Korean leader Kim Jong-un.", "tokens": ["South", "Korean", "President", "Moon", "Jae", "-", "in", "meets", "with", "North", "Korean", "leader", "Kim", "Jong", "-", "un", "."], "labels": ["B-person", "I-person", "I-person", "I-person", "I-person", "I-person", "I-person", "O", "O", "B-location", "I-location", "I-location", "B-person", "I-person", "I-person", "I-person", "O"]}
{"sentence": "Goldman Sachs predicts 3% GDP growth for the next fiscal year.", "tokens": ["Goldman", "Sachs", "predicts", "3", "%", "GDP", "growth", "for", "the", "next", "fiscal", "year", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Spotify reaches 200 million paid subscribers milestone.", "tokens": ["Spotify", "reaches", "200", "million", "paid", "subscribers", "milestone", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Seattle Seahawks quarterback Russell Wilson signs record-breaking contract extension.", "tokens": ["Seattle", "Seahawks", "quarterback", "Russell", "Wilson", "signs", "record", "-", "breaking", "contract", "extension", "."], "labels": ["B-organization", "I-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Food and Drug Administration approves new cancer treatment drug.", "tokens": ["Food", "and", "Drug", "Administration", "approves", "new", "cancer", "treatment", "drug", "."], "labels": ["B-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Brazilian President Bolsonaro faces impeachment proceedings.", "tokens": ["Brazilian", "President", "Bolsonaro", "faces", "impeachment", "proceedings", "."], "labels": ["B-person", "I-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "World Health Organization issues travel advisory for Southeast Asia.", "tokens": ["World", "Health", "Organization", "issues", "travel", "advisory", "for", "Southeast", "Asia", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "JP Morgan Chase CEO Jamie Dimon undergoes emergency surgery.", "tokens": ["JP", "Morgan", "Chase", "CEO", "Jamie", "Dimon", "undergoes", "emergency", "surgery", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "G20 Summit concludes with new global economic recovery plan.", "tokens": ["G20", "Summit", "concludes", "with", "new", "global", "economic", "recovery", "plan", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Hong Kong protests escalate as China imposes new security law.", "tokens": ["Hong", "Kong", "protests", "escalate", "as", "China", "imposes", "new", "security", "law", "."], "labels": ["B-location", "I-location", "O", "O", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "IBM launches quantum computing research center in Switzerland.", "tokens": ["IBM", "launches", "quantum", "computing", "research", "center", "in", "Switzerland", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Uber to expand services to rural communities in the United States.", "tokens": ["Uber", "to", "expand", "services", "to", "rural", "communities", "in", "the", "United", "States", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Australian Open tennis tournament to allow limited fan attendance.", "tokens": ["Australian", "Open", "tennis", "tournament", "to", "allow", "limited", "fan", "attendance", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "San Francisco-based tech company raises $100 million in new funding round.", "tokens": ["San", "Francisco", "-", "based", "tech", "company", "raises", "$", "100", "million", "in", "new", "funding", "round", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Pope Francis visits Iraq in historic trip.", "tokens": ["Pope", "Francis", "visits", "Iraq", "in", "historic", "trip", "."], "labels": ["B-person", "I-person", "O", "B-location", "O", "O", "O", "O"]}
{"sentence": "Toyota recalls over 1 million vehicles for safety issue.", "tokens": ["Toyota", "recalls", "over", "1", "million", "vehicles", "for", "safety", "issue", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Denmark to build new offshore wind farm to increase renewable energy production.", "tokens": ["Denmark", "to", "build", "new", "offshore", "wind", "farm", "to", "increase", "renewable", "energy", "production", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "NASA launches new satellite to monitor climate change effects.", "tokens": ["NASA", "launches", "new", "satellite", "to", "monitor", "climate", "change", "effects", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Atlanta Braves win World Series title for the first time in 26 years.", "tokens": ["Atlanta", "Braves", "win", "World", "Series", "title", "for", "the", "first", "time", "in", "26", "years", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "British Airways cancels flights due to ongoing labor strike.", "tokens": ["British", "Airways", "cancels", "flights", "due", "to", "ongoing", "labor", "strike", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Indonesian President Widodo unveils economic reform plan.", "tokens": ["Indonesian", "President", "Widodo", "unveils", "economic", "reform", "plan", "."], "labels": ["B-person", "I-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "U.S. Supreme Court justices hear landmark case on gun rights.", "tokens": ["U", ".", "S", ".", "Supreme", "Court", "justices", "hear", "landmark", "case", "on", "gun", "rights", "."], "labels": ["B-organization", "I-organization", "I-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York Times Pulitzer Prize-winning journalist writes investigative report on government corruption.", "tokens": ["New", "York", "Times", "Pulitzer", "Prize", "-", "winning", "journalist", "writes", "investigative", "report", "on", "government", "corruption", "."], "labels": ["B-organization", "I-organization", "I-organization", "B-organization", "I-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York City announces new public transportation initiative.", "tokens": ["New", "York", "City", "announces", "new", "public", "transportation", "initiative", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon to acquire MGM Studios for $8.45 billion.", "tokens": ["Amazon", "to", "acquire", "MGM", "Studios", "for", "$", "8", ".", "45", "billion", "."], "labels": ["B-organization", "O", "O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple unveils new iPhone with advanced camera technology.", "tokens": ["Apple", "unveils", "new", "iPhone", "with", "advanced", "camera", "technology", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Elon Musk's SpaceX launches new satellite into orbit.", "tokens": ["Elon", "Musk", "'", "s", "SpaceX", "launches", "new", "satellite", "into", "orbit", "."], "labels": ["B-person", "I-person", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Brazil surpasses 500,000 COVID-19 deaths.", "tokens": ["Brazil", "surpasses", "500", ",", "000", "COVID", "-", "19", "deaths", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Microsoft introduces new cybersecurity measures.", "tokens": ["Microsoft", "introduces", "new", "cybersecurity", "measures", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Famous actor Leonardo DiCaprio involved in charity fundraiser.", "tokens": ["Famous", "actor", "Leonardo", "DiCaprio", "involved", "in", "charity", "fundraiser", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "European Union imposes sanctions on Belarus.", "tokens": ["European", "Union", "imposes", "sanctions", "on", "Belarus", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "B-location", "O"]}
{"sentence": "World Bank approves $1 billion loan for sustainable energy projects.", "tokens": ["World", "Bank", "approves", "$", "1", "billion", "loan", "for", "sustainable", "energy", "projects", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Serena Williams advances to Wimbledon final.", "tokens": ["Serena", "Williams", "advances", "to", "Wimbledon", "final", "."], "labels": ["B-person", "I-person", "O", "O", "B-location", "O", "O"]}
{"sentence": "California wildfires continue to rage, destroying thousands of acres.", "tokens": ["California", "wildfires", "continue", "to", "rage", ",", "destroying", "thousands", "of", "acres", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook reveals plan to introduce new virtual reality technology.", "tokens": ["Facebook", "reveals", "plan", "to", "introduce", "new", "virtual", "reality", "technology", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Angela Merkel elected as new head of European Central Bank.", "tokens": ["Angela", "Merkel", "elected", "as", "new", "head", "of", "European", "Central", "Bank", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "B-organization", "I-organization", "I-organization", "O"]}
{"sentence": "SpaceX founder Elon Musk becomes world's wealthiest person.", "tokens": ["SpaceX", "founder", "Elon", "Musk", "becomes", "world", "'", "s", "wealthiest", "person", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "South Korea reports surge in COVID-19 cases.", "tokens": ["South", "Korea", "reports", "surge", "in", "COVID", "-", "19", "cases", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations announces initiative to combat global poverty.", "tokens": ["United", "Nations", "announces", "initiative", "to", "combat", "global", "poverty", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Taylor Swift releases highly-anticipated album.", "tokens": ["Taylor", "Swift", "releases", "highly", "-", "anticipated", "album", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Flooding in India displaces thousands of residents.", "tokens": ["Flooding", "in", "India", "displaces", "thousands", "of", "residents", "."], "labels": ["O", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Google faces antitrust lawsuit from European Union.", "tokens": ["Google", "faces", "antitrust", "lawsuit", "from", "European", "Union", "."], "labels": ["B-organization", "O", "O", "O", "O", "B-organization", "I-organization", "O"]}
{"sentence": "Prince Harry and Meghan Markle launch new charitable foundation.", "tokens": ["Prince", "Harry", "and", "Meghan", "Markle", "launch", "new", "charitable", "foundation", "."], "labels": ["B-person", "I-person", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "London Stock Exchange experiences record gains.", "tokens": ["London", "Stock", "Exchange", "experiences", "record", "gains", "."], "labels": ["B-location", "B-organization", "I-organization", "O", "O", "O", "O"]}
{"sentence": "Australian government pledges support for renewable energy initiatives.", "tokens": ["Australian", "government", "pledges", "support", "for", "renewable", "energy", "initiatives", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "NBA superstar LeBron James signs new endorsement deal.", "tokens": ["NBA", "superstar", "LeBron", "James", "signs", "new", "endorsement", "deal", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Hurricane devastates coastal communities in the Caribbean.", "tokens": ["Hurricane", "devastates", "coastal", "communities", "in", "the", "Caribbean", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Uber partners with local charities to provide free rides for healthcare workers.", "tokens": ["Uber", "partners", "with", "local", "charities", "to", "provide", "free", "rides", "for", "healthcare", "workers", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Cardi B's new single reaches number one on the charts.", "tokens": ["Cardi", "B", "'", "s", "new", "single", "reaches", "number", "one", "on", "the", "charts", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Italy imposes new restrictions to curb rising COVID-19 infections.", "tokens": ["Italy", "imposes", "new", "restrictions", "to", "curb", "rising", "COVID", "-", "19", "infections", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Twitter bans political advertisements ahead of upcoming elections.", "tokens": ["Twitter", "bans", "political", "advertisements", "ahead", "of", "upcoming", "elections", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Anthony Fauci warns of potential surge in COVID-19 cases.", "tokens": ["Anthony", "Fauci", "warns", "of", "potential", "surge", "in", "COVID", "-", "19", "cases", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Russia sends aid to war-torn region of Ukraine.", "tokens": ["Russia", "sends", "aid", "to", "war", "-", "torn", "region", "of", "Ukraine", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "TaylorMade Golf Company to release new line of clubs.", "tokens": ["TaylorMade", "Golf", "Company", "to", "release", "new", "line", "of", "clubs", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Kenyan marathon runner sets new world record.", "tokens": ["Kenyan", "marathon", "runner", "sets", "new", "world", "record", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Walmart introduces new sustainability initiative.", "tokens": ["Walmart", "introduces", "new", "sustainability", "initiative", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Paris Fashion Week sees return of in-person runway shows.", "tokens": ["Paris", "Fashion", "Week", "sees", "return", "of", "in", "-", "person", "runway", "shows", "."], "labels": ["B-location", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "SpaceX's Starship prototype successfully completes test flight.", "tokens": ["SpaceX", "'", "s", "Starship", "prototype", "successfully", "completes", "test", "flight", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Egypt and Sudan agree to joint infrastructure development project.", "tokens": ["Egypt", "and", "Sudan", "agree", "to", "joint", "infrastructure", "development", "project", "."], "labels": ["B-location", "O", "B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Starbucks to open 500 new locations in China.", "tokens": ["Starbucks", "to", "open", "500", "new", "locations", "in", "China", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Adele's new album breaks streaming records.", "tokens": ["Adele", "'", "s", "new", "album", "breaks", "streaming", "records", "."], "labels": ["B-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "British Royal Family announces new charity initiative.", "tokens": ["British", "Royal", "Family", "announces", "new", "charity", "initiative", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Taylor Swift wins top honors at Grammy Awards.", "tokens": ["Taylor", "Swift", "wins", "top", "honors", "at", "Grammy", "Awards", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "B-organization", "I-organization", "O"]}
{"sentence": "Spain celebrates national soccer team's victory in championship.", "tokens": ["Spain", "celebrates", "national", "soccer", "team", "'", "s", "victory", "in", "championship", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Twitter CEO Jack Dorsey steps down from position.", "tokens": ["Twitter", "CEO", "Jack", "Dorsey", "steps", "down", "from", "position", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Germany experiences surge in renewable energy production.", "tokens": ["Germany", "experiences", "surge", "in", "renewable", "energy", "production", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple Music to feature exclusive concert from popular R&B artist.", "tokens": ["Apple", "Music", "to", "feature", "exclusive", "concert", "from", "popular", "R", "&", "B", "artist", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations refugee agency provides aid to displaced families in Syria.", "tokens": ["United", "Nations", "refugee", "agency", "provides", "aid", "to", "displaced", "families", "in", "Syria", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Apple releases new iPhone model.", "tokens": ["Apple", "releases", "new", "iPhone", "model", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "California experiences record heatwave.", "tokens": ["California", "experiences", "record", "heatwave", "."], "labels": ["B-location", "O", "O", "O", "O"]}
{"sentence": "Tokyo Olympics to begin next month.", "tokens": ["Tokyo", "Olympics", "to", "begin", "next", "month", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon CEO Jeff Bezos steps down.", "tokens": ["Amazon", "CEO", "Jeff", "Bezos", "steps", "down", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O"]}
{"sentence": "Hurricane Laura devastates Louisiana coast.", "tokens": ["Hurricane", "Laura", "devastates", "Louisiana", "coast", "."], "labels": ["B-location", "I-location", "O", "B-location", "O", "O"]}
{"sentence": "Pfizer announces new COVID-19 vaccine trial results.", "tokens": ["Pfizer", "announces", "new", "COVID", "-", "19", "vaccine", "trial", "results", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Greta Thunberg speaks at climate change conference.", "tokens": ["Greta", "Thunberg", "speaks", "at", "climate", "change", "conference", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York City to implement vaccine passport system.", "tokens": ["New", "York", "City", "to", "implement", "vaccine", "passport", "system", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Wildfires rage in California, forcing evacuations.", "tokens": ["Wildfires", "rage", "in", "California", ",", "forcing", "evacuations", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O"]}
{"sentence": "Former President Obama to speak at climate change conference.", "tokens": ["Former", "President", "Obama", "to", "speak", "at", "climate", "change", "conference", "."], "labels": ["O", "O", "B-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations calls for ceasefire in war-torn region.", "tokens": ["United", "Nations", "calls", "for", "ceasefire", "in", "war", "-", "torn", "region", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Pfizer vaccine approved for children ages 5-11.", "tokens": ["Pfizer", "vaccine", "approved", "for", "children", "ages", "5", "-", "11", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Hurricane devastates communities along the Gulf Coast.", "tokens": ["Hurricane", "devastates", "communities", "along", "the", "Gulf", "Coast", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Facebook faces allegations of antitrust violations.", "tokens": ["Facebook", "faces", "allegations", "of", "antitrust", "violations", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "London Stock Exchange experiences record-breaking gains.", "tokens": ["London", "Stock", "Exchange", "experiences", "record", "-", "breaking", "gains", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Germany imposes new restrictions to curb COVID-19 spread.", "tokens": ["Germany", "imposes", "new", "restrictions", "to", "curb", "COVID", "-", "19", "spread", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Actress Emma Watson announces new film project.", "tokens": ["Actress", "Emma", "Watson", "announces", "new", "film", "project", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon plans to open new fulfillment center in Texas.", "tokens": ["Amazon", "plans", "to", "open", "new", "fulfillment", "center", "in", "Texas", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "New York City mayor proposes budget cuts to address fiscal crisis.", "tokens": ["New", "York", "City", "mayor", "proposes", "budget", "cuts", "to", "address", "fiscal", "crisis", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Tesla predicts surge in electric vehicle sales.", "tokens": ["CEO", "of", "Tesla", "predicts", "surge", "in", "electric", "vehicle", "sales", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "NASA unveils plans for lunar exploration mission.", "tokens": ["NASA", "unveils", "plans", "for", "lunar", "exploration", "mission", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Argentina seeks IMF assistance to address economic challenges.", "tokens": ["Argentina", "seeks", "IMF", "assistance", "to", "address", "economic", "challenges", "."], "labels": ["B-location", "O", "B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Pop star Taylor Swift surprises fans with new album release.", "tokens": ["Pop", "star", "Taylor", "Swift", "surprises", "fans", "with", "new", "album", "release", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Hong Kong protests erupt over proposed extradition law.", "tokens": ["Hong", "Kong", "protests", "erupt", "over", "proposed", "extradition", "law", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Google faces lawsuit over alleged privacy violations.", "tokens": ["Google", "faces", "lawsuit", "over", "alleged", "privacy", "violations", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "President Macron addresses nation in televised speech.", "tokens": ["President", "Macron", "addresses", "nation", "in", "televised", "speech", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Chicago police department under investigation for misconduct.", "tokens": ["Chicago", "police", "department", "under", "investigation", "for", "misconduct", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Ferrari unveils new lineup of luxury sports cars.", "tokens": ["Ferrari", "unveils", "new", "lineup", "of", "luxury", "sports", "cars", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Bill Gates donates $1 million to humanitarian aid efforts.", "tokens": ["Bill", "Gates", "donates", "$", "1", "million", "to", "humanitarian", "aid", "efforts", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "World Health Organization issues global health alert.", "tokens": ["World", "Health", "Organization", "issues", "global", "health", "alert", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Australian Open tennis tournament postponed due to COVID-19 concerns.", "tokens": ["Australian", "Open", "tennis", "tournament", "postponed", "due", "to", "COVID", "-", "19", "concerns", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Bank of America reports record profits for the quarter.", "tokens": ["Bank", "of", "America", "reports", "record", "profits", "for", "the", "quarter", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Rihanna launches new fashion collection.", "tokens": ["Rihanna", "launches", "new", "fashion", "collection", "."], "labels": ["B-person", "O", "O", "O", "O", "O"]}
{"sentence": "Paris climate agreement gains support from major nations.", "tokens": ["Paris", "climate", "agreement", "gains", "support", "from", "major", "nations", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Starbucks unveils plans for sustainable packaging initiative.", "tokens": ["Starbucks", "unveils", "plans", "for", "sustainable", "packaging", "initiative", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "J.K. Rowling faces backlash over controversial statements.", "tokens": ["J", ".", "K", ".", "Rowling", "faces", "backlash", "over", "controversial", "statements", "."], "labels": ["B-person", "I-person", "I-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "South Africa announces new measures to combat rising crime rates.", "tokens": ["South", "Africa", "announces", "new", "measures", "to", "combat", "rising", "crime", "rates", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Beyonc\u00e9 set to headline music festival in New Orleans.", "tokens": ["Beyonc\u00e9", "set", "to", "headline", "music", "festival", "in", "New", "Orleans", "."], "labels": ["B-person", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Jeff Bezos steps down as CEO of Amazon.", "tokens": ["Jeff", "Bezos", "steps", "down", "as", "CEO", "of", "Amazon", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "B-organization", "O"]}
{"sentence": "Philippine president visits China to discuss trade agreements.", "tokens": ["Philippine", "president", "visits", "China", "to", "discuss", "trade", "agreements", "."], "labels": ["B-location", "O", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Sony announces release date for new PlayStation console.", "tokens": ["Sony", "announces", "release", "date", "for", "new", "PlayStation", "console", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Major earthquake strikes off the coast of Japan.", "tokens": ["Major", "earthquake", "strikes", "off", "the", "coast", "of", "Japan", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Warren Buffet donates $1 billion to charity.", "tokens": ["Warren", "Buffet", "donates", "$", "1", "billion", "to", "charity", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "WHO declares global pandemic due to novel coronavirus.", "tokens": ["WHO", "declares", "global", "pandemic", "due", "to", "novel", "coronavirus", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Justin Bieber's new album breaks streaming records.", "tokens": ["Justin", "Bieber", "'", "s", "new", "album", "breaks", "streaming", "records", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Golden State Warriors to face off against the Brooklyn Nets in the NBA Finals.", "tokens": ["Golden", "State", "Warriors", "to", "face", "off", "against", "the", "Brooklyn", "Nets", "in", "the", "NBA", "Finals", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "B-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Federal Reserve raises interest rates in response to inflation concerns.", "tokens": ["Federal", "Reserve", "raises", "interest", "rates", "in", "response", "to", "inflation", "concerns", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Reddit co-founder resigns from company board.", "tokens": ["Reddit", "co", "-", "founder", "resigns", "from", "company", "board", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "International Space Station conducts groundbreaking experiments in zero gravity.", "tokens": ["International", "Space", "Station", "conducts", "groundbreaking", "experiments", "in", "zero", "gravity", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New Zealand Prime Minister announces new policies to address climate change.", "tokens": ["New", "Zealand", "Prime", "Minister", "announces", "new", "policies", "to", "address", "climate", "change", "."], "labels": ["B-location", "I-location", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Newly elected president pledges to address climate change.", "tokens": ["Newly", "elected", "president", "pledges", "to", "address", "climate", "change", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Los Angeles Dodgers clinch playoff spot.", "tokens": ["Los", "Angeles", "Dodgers", "clinch", "playoff", "spot", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O"]}
{"sentence": "Paris, France experiences record high temperatures.", "tokens": ["Paris", ",", "France", "experiences", "record", "high", "temperatures", "."], "labels": ["B-location", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Apple Inc. announces new product launch.", "tokens": ["CEO", "of", "Apple", "Inc", ".", "announces", "new", "product", "launch", "."], "labels": ["O", "O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations calls for peace negotiations in the Middle East.", "tokens": ["United", "Nations", "calls", "for", "peace", "negotiations", "in", "the", "Middle", "East", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "British Prime Minister visits India for trade talks.", "tokens": ["British", "Prime", "Minister", "visits", "India", "for", "trade", "talks", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "B-location", "O", "O", "O", "O"]}
{"sentence": "Major earthquake hits Tokyo, Japan.", "tokens": ["Major", "earthquake", "hits", "Tokyo", ",", "Japan", "."], "labels": ["O", "O", "O", "B-location", "O", "B-location", "O"]}
{"sentence": "World Health Organization declares global pandemic.", "tokens": ["World", "Health", "Organization", "declares", "global", "pandemic", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O"]}
{"sentence": "President Biden visits the United Kingdom for G7 summit.", "tokens": ["President", "Biden", "visits", "the", "United", "Kingdom", "for", "G7", "summit", "."], "labels": ["B-person", "I-person", "O", "O", "B-location", "I-location", "O", "B-organization", "O", "O"]}
{"sentence": "Apple Inc. releases new iPhone model.", "tokens": ["Apple", "Inc", ".", "releases", "new", "iPhone", "model", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Famous actress Angelina Jolie adopts a child from Ethiopia.", "tokens": ["Famous", "actress", "Angelina", "Jolie", "adopts", "a", "child", "from", "Ethiopia", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "The European Union imposes sanctions on Russia.", "tokens": ["The", "European", "Union", "imposes", "sanctions", "on", "Russia", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "B-location", "O"]}
{"sentence": "New York City to host Olympic Games in 2032.", "tokens": ["New", "York", "City", "to", "host", "Olympic", "Games", "in", "2032", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Bill Gates donates $1 billion to combat climate change.", "tokens": ["Bill", "Gates", "donates", "$", "1", "billion", "to", "combat", "climate", "change", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Health Organization declares a global pandemic.", "tokens": ["The", "World", "Health", "Organization", "declares", "a", "global", "pandemic", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Tiger Woods wins the Masters Golf Tournament.", "tokens": ["Tiger", "Woods", "wins", "the", "Masters", "Golf", "Tournament", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon to open new headquarters in Tokyo.", "tokens": ["Amazon", "to", "open", "new", "headquarters", "in", "Tokyo", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "German Chancellor Angela Merkel meets with French President Macron.", "tokens": ["German", "Chancellor", "Angela", "Merkel", "meets", "with", "French", "President", "Macron", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "B-person", "I-person", "O"]}
{"sentence": "Microsoft acquires cybersecurity firm for $10 billion.", "tokens": ["Microsoft", "acquires", "cybersecurity", "firm", "for", "$", "10", "billion", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Kim Kardashian launches new beauty line.", "tokens": ["Kim", "Kardashian", "launches", "new", "beauty", "line", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Mount Everest climbers stranded after avalanche.", "tokens": ["Mount", "Everest", "climbers", "stranded", "after", "avalanche", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations warns of humanitarian crisis in Syria.", "tokens": ["The", "United", "Nations", "warns", "of", "humanitarian", "crisis", "in", "Syria", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "LeBron James signs $100 million contract with LA Lakers.", "tokens": ["LeBron", "James", "signs", "$", "100", "million", "contract", "with", "LA", "Lakers", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "B-organization", "I-organization", "O"]}
{"sentence": "Facebook to invest in renewable energy projects.", "tokens": ["Facebook", "to", "invest", "in", "renewable", "energy", "projects", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Harry Styles announces world tour dates.", "tokens": ["Harry", "Styles", "announces", "world", "tour", "dates", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "United Airlines expands routes to Latin America.", "tokens": ["United", "Airlines", "expands", "routes", "to", "Latin", "America", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "German scientists make breakthrough in cancer research.", "tokens": ["German", "scientists", "make", "breakthrough", "in", "cancer", "research", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tom Cruise to star in new Mission Impossible movie.", "tokens": ["Tom", "Cruise", "to", "star", "in", "new", "Mission", "Impossible", "movie", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "European Central Bank announces interest rate hike.", "tokens": ["European", "Central", "Bank", "announces", "interest", "rate", "hike", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Taylor Swift to receive humanitarian award at the MTV Music Awards.", "tokens": ["Taylor", "Swift", "to", "receive", "humanitarian", "award", "at", "the", "MTV", "Music", "Awards", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "B-organization", "I-organization", "I-organization", "O"]}
{"sentence": "Oil prices surge after Saudi Arabia drone attack.", "tokens": ["Oil", "prices", "surge", "after", "Saudi", "Arabia", "drone", "attack", "."], "labels": ["O", "O", "O", "O", "B-location", "I-location", "O", "O", "O"]}
{"sentence": "SpaceX founder Elon Musk plans mission to Mars.", "tokens": ["SpaceX", "founder", "Elon", "Musk", "plans", "mission", "to", "Mars", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "B-location", "O"]}
{"sentence": "Federal Reserve raises key interest rates.", "tokens": ["Federal", "Reserve", "raises", "key", "interest", "rates", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Angelina Jolie divorces Brad Pitt after 5 years of marriage.", "tokens": ["Angelina", "Jolie", "divorces", "Brad", "Pitt", "after", "5", "years", "of", "marriage", "."], "labels": ["B-person", "I-person", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "California wildfires destroy thousands of homes.", "tokens": ["California", "wildfires", "destroy", "thousands", "of", "homes", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Paris Fashion Week showcases latest designer collections.", "tokens": ["Paris", "Fashion", "Week", "showcases", "latest", "designer", "collections", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Google announces new artificial intelligence research center in Beijing.", "tokens": ["Google", "announces", "new", "artificial", "intelligence", "research", "center", "in", "Beijing", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Pope Francis visits Iraq for historic peace mission.", "tokens": ["Pope", "Francis", "visits", "Iraq", "for", "historic", "peace", "mission", "."], "labels": ["B-person", "I-person", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Australian Prime Minister announces new climate change policy.", "tokens": ["Australian", "Prime", "Minister", "announces", "new", "climate", "change", "policy", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Jay-Z and Beyonce donate $1 million to charity.", "tokens": ["Jay", "-", "Z", "and", "Beyonce", "donate", "$", "1", "million", "to", "charity", "."], "labels": ["B-person", "I-person", "I-person", "O", "B-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Italy declares state of emergency after earthquake.", "tokens": ["Italy", "declares", "state", "of", "emergency", "after", "earthquake", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Spotify surpasses 1 billion active users.", "tokens": ["Spotify", "surpasses", "1", "billion", "active", "users", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "London Stock Exchange experiences record highs.", "tokens": ["London", "Stock", "Exchange", "experiences", "record", "highs", "."], "labels": ["B-location", "B-organization", "I-organization", "O", "O", "O", "O"]}
{"sentence": "Jennifer Lawrence to star in new sci-fi thriller.", "tokens": ["Jennifer", "Lawrence", "to", "star", "in", "new", "sci", "-", "fi", "thriller", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Iranian President Rouhani addresses United Nations General Assembly.", "tokens": ["Iranian", "President", "Rouhani", "addresses", "United", "Nations", "General", "Assembly", "."], "labels": ["B-person", "I-person", "I-person", "O", "B-organization", "I-organization", "I-organization", "I-organization", "O"]}
{"sentence": "Starbucks to open 1,000 new stores in China.", "tokens": ["Starbucks", "to", "open", "1", ",", "000", "new", "stores", "in", "China", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Taylor Swift and Kanye West to headline music festival.", "tokens": ["Taylor", "Swift", "and", "Kanye", "West", "to", "headline", "music", "festival", "."], "labels": ["B-person", "I-person", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "New Zealand Prime Minister announces COVID-19 vaccination mandate.", "tokens": ["New", "Zealand", "Prime", "Minister", "announces", "COVID", "-", "19", "vaccination", "mandate", "."], "labels": ["B-location", "I-location", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The International Monetary Fund releases global economic forecast.", "tokens": ["The", "International", "Monetary", "Fund", "releases", "global", "economic", "forecast", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Disney to launch new streaming service in Europe.", "tokens": ["Disney", "to", "launch", "new", "streaming", "service", "in", "Europe", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "SpaceX unveils plans for manned mission to Mars.", "tokens": ["SpaceX", "unveils", "plans", "for", "manned", "mission", "to", "Mars", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Japan to invest $2 billion in renewable energy projects.", "tokens": ["Japan", "to", "invest", "$", "2", "billion", "in", "renewable", "energy", "projects", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "U.S. government sanctions North Korean officials.", "tokens": ["U", ".", "S", ".", "government", "sanctions", "North", "Korean", "officials", "."], "labels": ["B-location", "I-location", "I-location", "I-location", "O", "O", "B-location", "I-location", "O", "O"]}
{"sentence": "Apple's CEO Tim Cook announces new product launch.", "tokens": ["Apple", "'", "s", "CEO", "Tim", "Cook", "announces", "new", "product", "launch", "."], "labels": ["B-organization", "O", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations reports increase in global poverty rates.", "tokens": ["The", "United", "Nations", "reports", "increase", "in", "global", "poverty", "rates", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "President Biden signs new infrastructure bill into law.", "tokens": ["President", "Biden", "signs", "new", "infrastructure", "bill", "into", "law", "."], "labels": ["O", "B-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "SpaceX plans to launch new satellite into orbit next week.", "tokens": ["SpaceX", "plans", "to", "launch", "new", "satellite", "into", "orbit", "next", "week", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Thousands gather in Times Square to celebrate New Year's Eve.", "tokens": ["Thousands", "gather", "in", "Times", "Square", "to", "celebrate", "New", "Year", "'", "s", "Eve", "."], "labels": ["O", "O", "O", "B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Walmart announces partnership with local food banks to fight hunger.", "tokens": ["Walmart", "announces", "partnership", "with", "local", "food", "banks", "to", "fight", "hunger", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "London police arrest suspect in connection to recent bank robbery.", "tokens": ["London", "police", "arrest", "suspect", "in", "connection", "to", "recent", "bank", "robbery", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon's stock price reaches all-time high after strong earnings report.", "tokens": ["Amazon", "'", "s", "stock", "price", "reaches", "all", "-", "time", "high", "after", "strong", "earnings", "report", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Famous singer Taylor Swift to release new album next month.", "tokens": ["Famous", "singer", "Taylor", "Swift", "to", "release", "new", "album", "next", "month", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "European Union leaders meet to discuss climate change initiatives.", "tokens": ["European", "Union", "leaders", "meet", "to", "discuss", "climate", "change", "initiatives", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Microsoft CEO Satya Nadella speaks at tech industry conference.", "tokens": ["Microsoft", "CEO", "Satya", "Nadella", "speaks", "at", "tech", "industry", "conference", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York City announces plan to build affordable housing units.", "tokens": ["New", "York", "City", "announces", "plan", "to", "build", "affordable", "housing", "units", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "WHO issues global health warning for new strain of influenza.", "tokens": ["WHO", "issues", "global", "health", "warning", "for", "new", "strain", "of", "influenza", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tokyo Olympics committee reveals new event schedule for 2024 games.", "tokens": ["Tokyo", "Olympics", "committee", "reveals", "new", "event", "schedule", "for", "2024", "games", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tesla's Elon Musk breaks ground on new Gigafactory in Texas.", "tokens": ["Tesla", "'", "s", "Elon", "Musk", "breaks", "ground", "on", "new", "Gigafactory", "in", "Texas", "."], "labels": ["B-organization", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Pope Francis condemns violence in Middle East conflict.", "tokens": ["Pope", "Francis", "condemns", "violence", "in", "Middle", "East", "conflict", "."], "labels": ["B-person", "I-person", "O", "O", "O", "B-location", "I-location", "O", "O"]}
{"sentence": "Google announces expansion of data center in Midwest region.", "tokens": ["Google", "announces", "expansion", "of", "data", "center", "in", "Midwest", "region", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "B-location", "O", "O"]}
{"sentence": "NBA player LeBron James signs multi-million dollar endorsement deal.", "tokens": ["NBA", "player", "LeBron", "James", "signs", "multi", "-", "million", "dollar", "endorsement", "deal", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Paris fashion week kicks off with star-studded runway show.", "tokens": ["Paris", "fashion", "week", "kicks", "off", "with", "star", "-", "studded", "runway", "show", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "IMF warns of economic downturn in developing nations.", "tokens": ["IMF", "warns", "of", "economic", "downturn", "in", "developing", "nations", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook introduces new privacy features to protect user data.", "tokens": ["Facebook", "introduces", "new", "privacy", "features", "to", "protect", "user", "data", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Former president Barack Obama to release memoir next year.", "tokens": ["Former", "president", "Barack", "Obama", "to", "release", "memoir", "next", "year", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "London mayor announces plans to improve public transportation system.", "tokens": ["London", "mayor", "announces", "plans", "to", "improve", "public", "transportation", "system", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "ExxonMobil faces lawsuit over environmental pollution.", "tokens": ["ExxonMobil", "faces", "lawsuit", "over", "environmental", "pollution", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Australian Open tennis tournament attracts top players from around the world.", "tokens": ["Australian", "Open", "tennis", "tournament", "attracts", "top", "players", "from", "around", "the", "world", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Coca-Cola steps down after 10 years in the role.", "tokens": ["CEO", "of", "Coca", "-", "Cola", "steps", "down", "after", "10", "years", "in", "the", "role", "."], "labels": ["O", "O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Berlin to host international film festival next month.", "tokens": ["Berlin", "to", "host", "international", "film", "festival", "next", "month", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "National Weather Service issues tornado warnings for several states.", "tokens": ["National", "Weather", "Service", "issues", "tornado", "warnings", "for", "several", "states", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "NBA legend Michael Jordan to invest in new sports complex.", "tokens": ["NBA", "legend", "Michael", "Jordan", "to", "invest", "in", "new", "sports", "complex", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Sony introduces new line of virtual reality gaming consoles.", "tokens": ["Sony", "introduces", "new", "line", "of", "virtual", "reality", "gaming", "consoles", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "London-based charity raises funds for humanitarian aid in war-torn region.", "tokens": ["London", "-", "based", "charity", "raises", "funds", "for", "humanitarian", "aid", "in", "war", "-", "torn", "region", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Twitter CEO Jack Dorsey resigns, citing personal reasons.", "tokens": ["Twitter", "CEO", "Jack", "Dorsey", "resigns", ",", "citing", "personal", "reasons", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Italian government announces plan to invest in renewable energy.", "tokens": ["Italian", "government", "announces", "plan", "to", "invest", "in", "renewable", "energy", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Las Vegas casino workers vote to authorize strike.", "tokens": ["Las", "Vegas", "casino", "workers", "vote", "to", "authorize", "strike", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "International Monetary Fund predicts global economic recovery.", "tokens": ["International", "Monetary", "Fund", "predicts", "global", "economic", "recovery", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Grammy-winning artist Beyonc\u00e9 to headline music festival.", "tokens": ["Grammy", "-", "winning", "artist", "Beyonc\u00e9", "to", "headline", "music", "festival", "."], "labels": ["O", "O", "O", "O", "B-person", "O", "O", "O", "O", "O"]}
{"sentence": "Chicago police arrest suspect in connection to recent string of burglaries.", "tokens": ["Chicago", "police", "arrest", "suspect", "in", "connection", "to", "recent", "string", "of", "burglaries", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Pfizer and Moderna CEOs testify before Senate committee on vaccine distribution.", "tokens": ["Pfizer", "and", "Moderna", "CEOs", "testify", "before", "Senate", "committee", "on", "vaccine", "distribution", "."], "labels": ["B-organization", "O", "B-organization", "O", "O", "O", "B-organization", "I-organization", "O", "O", "O", "O"]}
{"sentence": "Paris agreement on climate change receives support from over 180 countries.", "tokens": ["Paris", "agreement", "on", "climate", "change", "receives", "support", "from", "over", "180", "countries", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Goldman Sachs reports record profits in third quarter.", "tokens": ["Goldman", "Sachs", "reports", "record", "profits", "in", "third", "quarter", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Los Angeles mayor unveils plan to tackle homelessness crisis.", "tokens": ["Los", "Angeles", "mayor", "unveils", "plan", "to", "tackle", "homelessness", "crisis", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "World Health Organization declares new strain of virus a global health emergency.", "tokens": ["World", "Health", "Organization", "declares", "new", "strain", "of", "virus", "a", "global", "health", "emergency", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Academy Award-winning actress Meryl Streep to star in new film adaptation.", "tokens": ["Academy", "Award", "-", "winning", "actress", "Meryl", "Streep", "to", "star", "in", "new", "film", "adaptation", "."], "labels": ["O", "O", "O", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tokyo prepares to host Summer Olympics amid ongoing pandemic.", "tokens": ["Tokyo", "prepares", "to", "host", "Summer", "Olympics", "amid", "ongoing", "pandemic", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Starbucks introduces new line of sustainable coffee products.", "tokens": ["Starbucks", "introduces", "new", "line", "of", "sustainable", "coffee", "products", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "University of Oxford researchers make breakthrough in cancer treatment.", "tokens": ["University", "of", "Oxford", "researchers", "make", "breakthrough", "in", "cancer", "treatment", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Riots erupt in major cities following controversial court ruling.", "tokens": ["Riots", "erupt", "in", "major", "cities", "following", "controversial", "court", "ruling", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "World Bank approves loan to fund infrastructure projects in developing countries.", "tokens": ["World", "Bank", "approves", "loan", "to", "fund", "infrastructure", "projects", "in", "developing", "countries", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Real Madrid's Cristiano Ronaldo breaks goal-scoring record.", "tokens": ["Real", "Madrid", "'", "s", "Cristiano", "Ronaldo", "breaks", "goal", "-", "scoring", "record", "."], "labels": ["B-organization", "I-organization", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "London Stock Exchange experiences sharp decline amid global market volatility.", "tokens": ["London", "Stock", "Exchange", "experiences", "sharp", "decline", "amid", "global", "market", "volatility", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tokyo Olympics officially postponed until 2021.", "tokens": ["Tokyo", "Olympics", "officially", "postponed", "until", "2021", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook unveils new privacy features.", "tokens": ["Facebook", "unveils", "new", "privacy", "features", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon workers protest working conditions.", "tokens": ["Amazon", "workers", "protest", "working", "conditions", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Angela Merkel reelected as German Chancellor.", "tokens": ["Angela", "Merkel", "reelected", "as", "German", "Chancellor", "."], "labels": ["B-person", "I-person", "O", "O", "B-location", "O", "O"]}
{"sentence": "New York City bans plastic straws.", "tokens": ["New", "York", "City", "bans", "plastic", "straws", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O"]}
{"sentence": "Microsoft acquires cybersecurity firm.", "tokens": ["Microsoft", "acquires", "cybersecurity", "firm", "."], "labels": ["B-organization", "O", "O", "O", "O"]}
{"sentence": "Elon Musk announces plans for Mars colonization.", "tokens": ["Elon", "Musk", "announces", "plans", "for", "Mars", "colonization", "."], "labels": ["B-person", "I-person", "O", "O", "O", "B-location", "O", "O"]}
{"sentence": "Florida declares state of emergency.", "tokens": ["Florida", "declares", "state", "of", "emergency", "."], "labels": ["B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Google faces antitrust investigation.", "tokens": ["Google", "faces", "antitrust", "investigation", "."], "labels": ["B-organization", "O", "O", "O", "O"]}
{"sentence": "Cristiano Ronaldo scores hat-trick in soccer match.", "tokens": ["Cristiano", "Ronaldo", "scores", "hat", "-", "trick", "in", "soccer", "match", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Paris agrees to new climate change initiatives.", "tokens": ["Paris", "agrees", "to", "new", "climate", "change", "initiatives", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "IMF projects global economic slowdown.", "tokens": ["IMF", "projects", "global", "economic", "slowdown", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Jeff Bezos steps down as Amazon CEO.", "tokens": ["Jeff", "Bezos", "steps", "down", "as", "Amazon", "CEO", "."], "labels": ["B-person", "I-person", "O", "O", "O", "B-organization", "O", "O"]}
{"sentence": "London experiences record high temperatures.", "tokens": ["London", "experiences", "record", "high", "temperatures", "."], "labels": ["B-location", "O", "O", "O", "O", "O"]}
{"sentence": "WHO issues new guidelines for pandemic response.", "tokens": ["WHO", "issues", "new", "guidelines", "for", "pandemic", "response", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Serena Williams wins Wimbledon.", "tokens": ["Serena", "Williams", "wins", "Wimbledon", "."], "labels": ["B-person", "I-person", "O", "B-location", "O"]}
{"sentence": "Australia to host international summit.", "tokens": ["Australia", "to", "host", "international", "summit", "."], "labels": ["B-location", "O", "O", "O", "O", "O"]}
{"sentence": "TikTok under fire for privacy concerns.", "tokens": ["TikTok", "under", "fire", "for", "privacy", "concerns", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Joe Rogan signs exclusive podcast deal.", "tokens": ["Joe", "Rogan", "signs", "exclusive", "podcast", "deal", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "California wildfires rage on.", "tokens": ["California", "wildfires", "rage", "on", "."], "labels": ["B-location", "O", "O", "O", "O"]}
{"sentence": "Pfizer vaccine approved for emergency use.", "tokens": ["Pfizer", "vaccine", "approved", "for", "emergency", "use", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "London Fashion Week kicks off with new designers.", "tokens": ["London", "Fashion", "Week", "kicks", "off", "with", "new", "designers", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations calls for ceasefire in conflict.", "tokens": ["United", "Nations", "calls", "for", "ceasefire", "in", "conflict", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Beyonc\u00e9 announces new album release date.", "tokens": ["Beyonc\u00e9", "announces", "new", "album", "release", "date", "."], "labels": ["B-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tokyo to host 2032 Summer Olympics.", "tokens": ["Tokyo", "to", "host", "2032", "Summer", "Olympics", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Uber introduces new driver safety measures.", "tokens": ["Uber", "introduces", "new", "driver", "safety", "measures", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Las Vegas reopens casinos amidst pandemic.", "tokens": ["Las", "Vegas", "reopens", "casinos", "amidst", "pandemic", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O"]}
{"sentence": "NOAA issues hurricane warning for East Coast.", "tokens": ["NOAA", "issues", "hurricane", "warning", "for", "East", "Coast", "."], "labels": ["B-organization", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Tom Hanks named as new UNICEF ambassador.", "tokens": ["Tom", "Hanks", "named", "as", "new", "UNICEF", "ambassador", "."], "labels": ["B-person", "I-person", "O", "O", "O", "B-organization", "O", "O"]}
{"sentence": "Paris to implement new bike sharing program.", "tokens": ["Paris", "to", "implement", "new", "bike", "sharing", "program", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "ISIS claims responsibility for latest terrorist attack.", "tokens": ["ISIS", "claims", "responsibility", "for", "latest", "terrorist", "attack", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Taylor Swift releases new single.", "tokens": ["Taylor", "Swift", "releases", "new", "single", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "Los Angeles to invest in affordable housing.", "tokens": ["Los", "Angeles", "to", "invest", "in", "affordable", "housing", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Johnson & Johnson vaccine shows promising results.", "tokens": ["Johnson", "&", "Johnson", "vaccine", "shows", "promising", "results", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Chicago sees spike in violent crime.", "tokens": ["Chicago", "sees", "spike", "in", "violent", "crime", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "NASA to launch new satellite for climate research.", "tokens": ["NASA", "to", "launch", "new", "satellite", "for", "climate", "research", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Meghan Markle and Prince Harry welcome baby girl.", "tokens": ["Meghan", "Markle", "and", "Prince", "Harry", "welcome", "baby", "girl", "."], "labels": ["B-person", "I-person", "O", "B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "Miami to implement new public transportation system.", "tokens": ["Miami", "to", "implement", "new", "public", "transportation", "system", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Twitter introduces new anti-harassment tools.", "tokens": ["Twitter", "introduces", "new", "anti", "-", "harassment", "tools", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Sydney Opera House to hold virtual concerts.", "tokens": ["Sydney", "Opera", "House", "to", "hold", "virtual", "concerts", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O"]}
{"sentence": "Al-Qaeda leader killed in drone strike.", "tokens": ["Al", "-", "Qaeda", "leader", "killed", "in", "drone", "strike", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Adele to headline music festival.", "tokens": ["Adele", "to", "headline", "music", "festival", "."], "labels": ["B-person", "O", "O", "O", "O", "O"]}
{"sentence": "San Francisco to ban single-use plastics.", "tokens": ["San", "Francisco", "to", "ban", "single", "-", "use", "plastics", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon founder Jeff Bezos goes to space.", "tokens": ["Amazon", "founder", "Jeff", "Bezos", "goes", "to", "space", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "B-location", "O"]}
{"sentence": "The United States announced a new trade deal with Mexico and Canada.", "tokens": ["The", "United", "States", "announced", "a", "new", "trade", "deal", "with", "Mexico", "and", "Canada", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "B-location", "O", "B-location", "O"]}
{"sentence": "Professor Jones published a groundbreaking study on climate change.", "tokens": ["Professor", "Jones", "published", "a", "groundbreaking", "study", "on", "climate", "change", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The European Union imposed sanctions on Russia.", "tokens": ["The", "European", "Union", "imposed", "sanctions", "on", "Russia", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "B-location", "O"]}
{"sentence": "The stock market reached record highs today.", "tokens": ["The", "stock", "market", "reached", "record", "highs", "today", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Mayor Garcia unveiled a new plan for city infrastructure improvements.", "tokens": ["Mayor", "Garcia", "unveiled", "a", "new", "plan", "for", "city", "infrastructure", "improvements", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Health Organization declared a global health emergency.", "tokens": ["The", "World", "Health", "Organization", "declared", "a", "global", "health", "emergency", "."], "labels": ["B-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "British singer Adele released a new album.", "tokens": ["British", "singer", "Adele", "released", "a", "new", "album", "."], "labels": ["B-location", "O", "B-person", "O", "O", "O", "O", "O"]}
{"sentence": "SpaceX successfully launched a new satellite into orbit.", "tokens": ["SpaceX", "successfully", "launched", "a", "new", "satellite", "into", "orbit", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Wildfires continue to spread across Australia.", "tokens": ["Wildfires", "continue", "to", "spread", "across", "Australia", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "New York City declares state of emergency due to extreme weather conditions.", "tokens": ["New", "York", "City", "declares", "state", "of", "emergency", "due", "to", "extreme", "weather", "conditions", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tesla unveils new electric pickup truck.", "tokens": ["Tesla", "unveils", "new", "electric", "pickup", "truck", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Soccer player Lionel Messi signs $500 million contract with Paris Saint-Germain.", "tokens": ["Soccer", "player", "Lionel", "Messi", "signs", "$", "500", "million", "contract", "with", "Paris", "Saint", "-", "Germain", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "B-organization", "I-organization", "I-organization", "I-organization", "O"]}
{"sentence": "India surpasses China as world's most populous country.", "tokens": ["India", "surpasses", "China", "as", "world", "'", "s", "most", "populous", "country", "."], "labels": ["B-location", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "President Biden announces new infrastructure plan to rebuild roads and bridges.", "tokens": ["President", "Biden", "announces", "new", "infrastructure", "plan", "to", "rebuild", "roads", "and", "bridges", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Google faces antitrust lawsuit in European Union.", "tokens": ["Google", "faces", "antitrust", "lawsuit", "in", "European", "Union", "."], "labels": ["B-organization", "O", "O", "O", "O", "B-organization", "I-organization", "O"]}
{"sentence": "Senator Elizabeth Warren proposes legislation to cancel student loan debt.", "tokens": ["Senator", "Elizabeth", "Warren", "proposes", "legislation", "to", "cancel", "student", "loan", "debt", "."], "labels": ["B-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "SpaceX launches new mission to International Space Station.", "tokens": ["SpaceX", "launches", "new", "mission", "to", "International", "Space", "Station", "."], "labels": ["B-organization", "O", "O", "O", "O", "B-location", "I-location", "I-location", "O"]}
{"sentence": "Amazon CEO Jeff Bezos steps down, handing over reins to Andy Jassy.", "tokens": ["Amazon", "CEO", "Jeff", "Bezos", "steps", "down", ",", "handing", "over", "reins", "to", "Andy", "Jassy", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "B-person", "I-person", "O"]}
{"sentence": "Brazil experiences record-breaking heatwave, prompting health warnings.", "tokens": ["Brazil", "experiences", "record", "-", "breaking", "heatwave", ",", "prompting", "health", "warnings", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Olympic gold medalist Simone Biles announces retirement from gymnastics.", "tokens": ["Olympic", "gold", "medalist", "Simone", "Biles", "announces", "retirement", "from", "gymnastics", "."], "labels": ["O", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "UNICEF provides aid to children affected by conflict in Syria.", "tokens": ["UNICEF", "provides", "aid", "to", "children", "affected", "by", "conflict", "in", "Syria", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Apple introduces new iPhone with advanced camera features.", "tokens": ["Apple", "introduces", "new", "iPhone", "with", "advanced", "camera", "features", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Former President Donald Trump launches new social media platform.", "tokens": ["Former", "President", "Donald", "Trump", "launches", "new", "social", "media", "platform", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Wildfires continue to ravage California, prompting evacuations.", "tokens": ["Wildfires", "continue", "to", "ravage", "California", ",", "prompting", "evacuations", "."], "labels": ["O", "O", "O", "O", "B-location", "O", "O", "O", "O"]}
{"sentence": "President Biden to meet with G7 leaders in June.", "tokens": ["President", "Biden", "to", "meet", "with", "G7", "leaders", "in", "June", "."], "labels": ["O", "B-person", "O", "O", "O", "B-organization", "O", "O", "O", "O"]}
{"sentence": "Wildfires ravage California, thousands evacuated.", "tokens": ["Wildfires", "ravage", "California", ",", "thousands", "evacuated", "."], "labels": ["O", "O", "B-location", "O", "O", "O", "O"]}
{"sentence": "Major earthquake hits Japan, tsunami warning issued.", "tokens": ["Major", "earthquake", "hits", "Japan", ",", "tsunami", "warning", "issued", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon faces backlash over labor practices.", "tokens": ["Amazon", "faces", "backlash", "over", "labor", "practices", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New study finds link between processed meat and cancer risk.", "tokens": ["New", "study", "finds", "link", "between", "processed", "meat", "and", "cancer", "risk", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Hurricane season expected to be particularly severe this year.", "tokens": ["Hurricane", "season", "expected", "to", "be", "particularly", "severe", "this", "year", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook under investigation for antitrust violations.", "tokens": ["Facebook", "under", "investigation", "for", "antitrust", "violations", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Prime Minister Trudeau visits India to discuss trade agreements.", "tokens": ["Prime", "Minister", "Trudeau", "visits", "India", "to", "discuss", "trade", "agreements", "."], "labels": ["O", "O", "B-person", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Hollywood actress arrested for DUI.", "tokens": ["Hollywood", "actress", "arrested", "for", "DUI", "."], "labels": ["O", "O", "O", "O", "O", "O"]}
{"sentence": "Google introduces new privacy features for users.", "tokens": ["Google", "introduces", "new", "privacy", "features", "for", "users", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Former President Obama to release memoir.", "tokens": ["Former", "President", "Obama", "to", "release", "memoir", "."], "labels": ["O", "O", "B-person", "O", "O", "O", "O"]}
{"sentence": "South Africa imposes new COVID-19 lockdown measures.", "tokens": ["South", "Africa", "imposes", "new", "COVID", "-", "19", "lockdown", "measures", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Pfizer vaccine found to be highly effective in children.", "tokens": ["Pfizer", "vaccine", "found", "to", "be", "highly", "effective", "in", "children", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations calls for ceasefire in Yemen.", "tokens": ["United", "Nations", "calls", "for", "ceasefire", "in", "Yemen", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Major data breach at Microsoft exposes millions of users' information.", "tokens": ["Major", "data", "breach", "at", "Microsoft", "exposes", "millions", "of", "users", "'", "information", "."], "labels": ["O", "O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Pope Francis condemns violence in the Middle East.", "tokens": ["Pope", "Francis", "condemns", "violence", "in", "the", "Middle", "East", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "New York City to invest in affordable housing initiatives.", "tokens": ["New", "York", "City", "to", "invest", "in", "affordable", "housing", "initiatives", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Netflix steps down amid controversy.", "tokens": ["CEO", "of", "Netflix", "steps", "down", "amid", "controversy", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Russia announces plans for manned mission to Mars.", "tokens": ["Russia", "announces", "plans", "for", "manned", "mission", "to", "Mars", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Prince Harry and Meghan Markle welcome baby daughter.", "tokens": ["Prince", "Harry", "and", "Meghan", "Markle", "welcome", "baby", "daughter", "."], "labels": ["O", "B-person", "O", "B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "Australian wildfires destroy acres of land.", "tokens": ["Australian", "wildfires", "destroy", "acres", "of", "land", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Disney resigns following accusations of misconduct.", "tokens": ["CEO", "of", "Disney", "resigns", "following", "accusations", "of", "misconduct", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Italy extends COVID-19 lockdown restrictions.", "tokens": ["Italy", "extends", "COVID", "-", "19", "lockdown", "restrictions", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Jennifer Lopez and Ben Affleck spotted together in public.", "tokens": ["Jennifer", "Lopez", "and", "Ben", "Affleck", "spotted", "together", "in", "public", "."], "labels": ["B-person", "I-person", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Japan's economy shows signs of recovery after pandemic.", "tokens": ["Japan", "'", "s", "economy", "shows", "signs", "of", "recovery", "after", "pandemic", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Former CEO of Amazon launches new space exploration company.", "tokens": ["Former", "CEO", "of", "Amazon", "launches", "new", "space", "exploration", "company", "."], "labels": ["O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Paris Hilton launches new clothing line.", "tokens": ["Paris", "Hilton", "launches", "new", "clothing", "line", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "NASA's Perseverance rover discovers evidence of ancient life on Mars.", "tokens": ["NASA", "'", "s", "Perseverance", "rover", "discovers", "evidence", "of", "ancient", "life", "on", "Mars", "."], "labels": ["B-organization", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Greece declares state of emergency as wildfires rage.", "tokens": ["Greece", "declares", "state", "of", "emergency", "as", "wildfires", "rage", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "LeBron James to star in new basketball documentary.", "tokens": ["LeBron", "James", "to", "star", "in", "new", "basketball", "documentary", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Chinese government cracks down on cryptocurrency trading.", "tokens": ["Chinese", "government", "cracks", "down", "on", "cryptocurrency", "trading", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Meghan Markle launches new podcast series.", "tokens": ["Meghan", "Markle", "launches", "new", "podcast", "series", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "South Korea to host international peace summit.", "tokens": ["South", "Korea", "to", "host", "international", "peace", "summit", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Taylor Swift releases new album.", "tokens": ["Taylor", "Swift", "releases", "new", "album", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "Amazon to invest billions in renewable energy projects.", "tokens": ["Amazon", "to", "invest", "billions", "in", "renewable", "energy", "projects", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Canada announces plans for nationwide infrastructure upgrades.", "tokens": ["Canada", "announces", "plans", "for", "nationwide", "infrastructure", "upgrades", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Elon Musk's SpaceX launches astronauts to International Space Station.", "tokens": ["Elon", "Musk", "'", "s", "SpaceX", "launches", "astronauts", "to", "International", "Space", "Station", "."], "labels": ["B-person", "I-person", "O", "O", "B-organization", "O", "O", "O", "B-location", "I-location", "I-location", "O"]}
{"sentence": "UK imposes new travel restrictions amid COVID-19 concerns.", "tokens": ["UK", "imposes", "new", "travel", "restrictions", "amid", "COVID", "-", "19", "concerns", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Beyonc\u00e9 releases surprise album.", "tokens": ["Beyonc\u00e9", "releases", "surprise", "album", "."], "labels": ["B-person", "O", "O", "O", "O"]}
{"sentence": "Germany to phase out coal power by 2038.", "tokens": ["Germany", "to", "phase", "out", "coal", "power", "by", "2038", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Angelina Jolie launches new charity initiative.", "tokens": ["Angelina", "Jolie", "launches", "new", "charity", "initiative", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "World Health Organization urges global cooperation in pandemic response.", "tokens": ["World", "Health", "Organization", "urges", "global", "cooperation", "in", "pandemic", "response", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tesla announces plans to build new gigafactory in Texas.", "tokens": ["Tesla", "announces", "plans", "to", "build", "new", "gigafactory", "in", "Texas", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Senator Kamala Harris to visit small businesses in Ohio.", "tokens": ["Senator", "Kamala", "Harris", "to", "visit", "small", "businesses", "in", "Ohio", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Apple releases new iPhone model with advanced camera technology.", "tokens": ["Apple", "releases", "new", "iPhone", "model", "with", "advanced", "camera", "technology", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Famous chef Gordon Ramsay launches new cooking show on Netflix.", "tokens": ["Famous", "chef", "Gordon", "Ramsay", "launches", "new", "cooking", "show", "on", "Netflix", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "B-organization", "O"]}
{"sentence": "Massive wildfire destroys thousands of acres in California.", "tokens": ["Massive", "wildfire", "destroys", "thousands", "of", "acres", "in", "California", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "World Health Organization warns of new COVID-19 variant.", "tokens": ["World", "Health", "Organization", "warns", "of", "new", "COVID", "-", "19", "variant", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Olympic gold medalist Simone Biles discusses mental health struggles.", "tokens": ["Olympic", "gold", "medalist", "Simone", "Biles", "discusses", "mental", "health", "struggles", "."], "labels": ["O", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "New York City to invest $1 billion in public transportation.", "tokens": ["New", "York", "City", "to", "invest", "$", "1", "billion", "in", "public", "transportation", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Former president Barack Obama speaks at climate change conference.", "tokens": ["Former", "president", "Barack", "Obama", "speaks", "at", "climate", "change", "conference", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Google announces partnership with leading research institution.", "tokens": ["Google", "announces", "partnership", "with", "leading", "research", "institution", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Wild weather causes power outages across the Midwest.", "tokens": ["Wild", "weather", "causes", "power", "outages", "across", "the", "Midwest", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Amazon founder Jeff Bezos sets new record for space travel.", "tokens": ["Amazon", "founder", "Jeff", "Bezos", "sets", "new", "record", "for", "space", "travel", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "European Union imposes sanctions on Russian officials.", "tokens": ["European", "Union", "imposes", "sanctions", "on", "Russian", "officials", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "B-organization", "O", "O"]}
{"sentence": "Celebrated author J.K. Rowling releases new book.", "tokens": ["Celebrated", "author", "J", ".", "K", ".", "Rowling", "releases", "new", "book", "."], "labels": ["O", "O", "B-person", "I-person", "I-person", "I-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "UNICEF delivers aid to refugee camps in the Middle East.", "tokens": ["UNICEF", "delivers", "aid", "to", "refugee", "camps", "in", "the", "Middle", "East", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Australian Open tennis tournament to allow limited spectators.", "tokens": ["Australian", "Open", "tennis", "tournament", "to", "allow", "limited", "spectators", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Los Angeles Lakers trade star player to New York Knicks.", "tokens": ["Los", "Angeles", "Lakers", "trade", "star", "player", "to", "New", "York", "Knicks", "."], "labels": ["B-location", "I-location", "B-organization", "O", "O", "O", "O", "B-organization", "I-organization", "I-organization", "O"]}
{"sentence": "Environmental activist Greta Thunberg speaks at climate rally.", "tokens": ["Environmental", "activist", "Greta", "Thunberg", "speaks", "at", "climate", "rally", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Paris Fashion Week attracts top designers from around the world.", "tokens": ["Paris", "Fashion", "Week", "attracts", "top", "designers", "from", "around", "the", "world", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Uber and Lyft reach settlement in driver classification lawsuit.", "tokens": ["Uber", "and", "Lyft", "reach", "settlement", "in", "driver", "classification", "lawsuit", "."], "labels": ["B-organization", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Surge in COVID-19 cases overwhelms hospitals in the South.", "tokens": ["Surge", "in", "COVID", "-", "19", "cases", "overwhelms", "hospitals", "in", "the", "South", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Facebook unveils new initiative to combat misinformation.", "tokens": ["Facebook", "unveils", "new", "initiative", "to", "combat", "misinformation", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Football legend Tom Brady announces retirement.", "tokens": ["Football", "legend", "Tom", "Brady", "announces", "retirement", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O"]}
{"sentence": "National Hurricane Center issues warning for Gulf Coast.", "tokens": ["National", "Hurricane", "Center", "issues", "warning", "for", "Gulf", "Coast", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Black Lives Matter protests erupt in major cities across the country.", "tokens": ["Black", "Lives", "Matter", "protests", "erupt", "in", "major", "cities", "across", "the", "country", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "SpaceX CEO Elon Musk plans mission to Mars.", "tokens": ["SpaceX", "CEO", "Elon", "Musk", "plans", "mission", "to", "Mars", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "National Park Service celebrates 150 years of preservation.", "tokens": ["National", "Park", "Service", "celebrates", "150", "years", "of", "preservation", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Golden State Warriors star Stephen Curry scores 50 points in win.", "tokens": ["Golden", "State", "Warriors", "star", "Stephen", "Curry", "scores", "50", "points", "in", "win", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations General Assembly convenes in New York City.", "tokens": ["United", "Nations", "General", "Assembly", "convenes", "in", "New", "York", "City", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "B-location", "I-location", "I-location", "O"]}
{"sentence": "World Bank approves loan for rural development in Africa.", "tokens": ["World", "Bank", "approves", "loan", "for", "rural", "development", "in", "Africa", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Major earthquake rattles residents in Japan.", "tokens": ["Major", "earthquake", "rattles", "residents", "in", "Japan", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Grammy-winning artist Taylor Swift releases surprise album.", "tokens": ["Grammy", "-", "winning", "artist", "Taylor", "Swift", "releases", "surprise", "album", "."], "labels": ["O", "O", "O", "O", "B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "European Central Bank announces stimulus package.", "tokens": ["European", "Central", "Bank", "announces", "stimulus", "package", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O"]}
{"sentence": "Pro-democracy protests continue in Hong Kong.", "tokens": ["Pro", "-", "democracy", "protests", "continue", "in", "Hong", "Kong", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Ford Motor Company announces plans for electric vehicle production.", "tokens": ["Ford", "Motor", "Company", "announces", "plans", "for", "electric", "vehicle", "production", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Major flooding displaces thousands in Southeast Asia.", "tokens": ["Major", "flooding", "displaces", "thousands", "in", "Southeast", "Asia", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Pop icon Madonna celebrates 40 years in music industry.", "tokens": ["Pop", "icon", "Madonna", "celebrates", "40", "years", "in", "music", "industry", "."], "labels": ["O", "O", "B-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "International Monetary Fund projects global economic recovery.", "tokens": ["International", "Monetary", "Fund", "projects", "global", "economic", "recovery", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Florida Governor Ron DeSantis signs controversial bill into law.", "tokens": ["Florida", "Governor", "Ron", "DeSantis", "signs", "controversial", "bill", "into", "law", "."], "labels": ["B-location", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Social media giant Twitter announces new privacy features.", "tokens": ["Social", "media", "giant", "Twitter", "announces", "new", "privacy", "features", "."], "labels": ["O", "O", "O", "B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Severe drought impacts agriculture in the Midwest.", "tokens": ["Severe", "drought", "impacts", "agriculture", "in", "the", "Midwest", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Hollywood actress Jennifer Lawrence stars in new film.", "tokens": ["Hollywood", "actress", "Jennifer", "Lawrence", "stars", "in", "new", "film", "."], "labels": ["B-location", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "New study reveals connection between coffee consumption and heart health.", "tokens": ["New", "study", "reveals", "connection", "between", "coffee", "consumption", "and", "heart", "health", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon to open new headquarters in Austin, Texas.", "tokens": ["Amazon", "to", "open", "new", "headquarters", "in", "Austin", ",", "Texas", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-location", "O", "B-location", "O"]}
{"sentence": "Researchers discover new species of dinosaur in Patagonia.", "tokens": ["Researchers", "discover", "new", "species", "of", "dinosaur", "in", "Patagonia", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Apple launches new iPhone with advanced camera technology.", "tokens": ["Apple", "launches", "new", "iPhone", "with", "advanced", "camera", "technology", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Report shows increase in air pollution levels in Los Angeles.", "tokens": ["Report", "shows", "increase", "in", "air", "pollution", "levels", "in", "Los", "Angeles", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Soccer star Lionel Messi signs with Paris Saint-Germain.", "tokens": ["Soccer", "star", "Lionel", "Messi", "signs", "with", "Paris", "Saint", "-", "Germain", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "B-organization", "I-organization", "I-organization", "I-organization", "O"]}
{"sentence": "Study finds correlation between screen time and anxiety in teenagers.", "tokens": ["Study", "finds", "correlation", "between", "screen", "time", "and", "anxiety", "in", "teenagers", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New report ranks Tokyo as the most expensive city in the world.", "tokens": ["New", "report", "ranks", "Tokyo", "as", "the", "most", "expensive", "city", "in", "the", "world", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Wildfires continue to ravage Northern California.", "tokens": ["Wildfires", "continue", "to", "ravage", "Northern", "California", "."], "labels": ["O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Study suggests link between processed meat consumption and cancer risk.", "tokens": ["Study", "suggests", "link", "between", "processed", "meat", "consumption", "and", "cancer", "risk", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook faces criticism over data privacy concerns.", "tokens": ["Facebook", "faces", "criticism", "over", "data", "privacy", "concerns", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Olympic swimmer Michael Phelps breaks world record.", "tokens": ["Olympic", "swimmer", "Michael", "Phelps", "breaks", "world", "record", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "New York City announces plan to combat rising crime rates.", "tokens": ["New", "York", "City", "announces", "plan", "to", "combat", "rising", "crime", "rates", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Scientists discover potential new treatment for Alzheimer's disease.", "tokens": ["Scientists", "discover", "potential", "new", "treatment", "for", "Alzheimer", "'", "s", "disease", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Rising sea levels pose threat to coastal communities.", "tokens": ["Rising", "sea", "levels", "pose", "threat", "to", "coastal", "communities", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Study shows correlation between exercise and mental health.", "tokens": ["Study", "shows", "correlation", "between", "exercise", "and", "mental", "health", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "NASA's Perseverance rover discovers ancient river delta on Mars.", "tokens": ["NASA", "'", "s", "Perseverance", "rover", "discovers", "ancient", "river", "delta", "on", "Mars", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Bitcoin reaches new all-time high.", "tokens": ["Bitcoin", "reaches", "new", "all", "-", "time", "high", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations issues statement on global food insecurity.", "tokens": ["United", "Nations", "issues", "statement", "on", "global", "food", "insecurity", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Researchers develop new vaccine for malaria.", "tokens": ["Researchers", "develop", "new", "vaccine", "for", "malaria", "."], "labels": ["O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Mayor Garcetti proposes new housing plan for Los Angeles.", "tokens": ["Mayor", "Garcetti", "proposes", "new", "housing", "plan", "for", "Los", "Angeles", "."], "labels": ["O", "B-person", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Study reveals link between social media use and loneliness.", "tokens": ["Study", "reveals", "link", "between", "social", "media", "use", "and", "loneliness", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon founder Jeff Bezos becomes the richest person in the world.", "tokens": ["Amazon", "founder", "Jeff", "Bezos", "becomes", "the", "richest", "person", "in", "the", "world", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "London named as the host city for 2022 World Athletics Championships.", "tokens": ["London", "named", "as", "the", "host", "city", "for", "2022", "World", "Athletics", "Championships", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Climate change activists protest outside the headquarters of ExxonMobil.", "tokens": ["Climate", "change", "activists", "protest", "outside", "the", "headquarters", "of", "ExxonMobil", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "B-organization", "O"]}
{"sentence": "New data shows increase in unemployment rates across the country.", "tokens": ["New", "data", "shows", "increase", "in", "unemployment", "rates", "across", "the", "country", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO Tim Cook announces new product lineup for Apple.", "tokens": ["CEO", "Tim", "Cook", "announces", "new", "product", "lineup", "for", "Apple", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O", "B-organization", "O"]}
{"sentence": "Study finds link between air pollution and respiratory illnesses.", "tokens": ["Study", "finds", "link", "between", "air", "pollution", "and", "respiratory", "illnesses", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook introduces new feature to combat misinformation.", "tokens": ["Facebook", "introduces", "new", "feature", "to", "combat", "misinformation", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "President Macron addresses nation on new COVID-19 restrictions.", "tokens": ["President", "Macron", "addresses", "nation", "on", "new", "COVID", "-", "19", "restrictions", "."], "labels": ["O", "B-person", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tokyo Olympics to proceed without spectators.", "tokens": ["Tokyo", "Olympics", "to", "proceed", "without", "spectators", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Researchers discover ancient Mayan city hidden in the jungle.", "tokens": ["Researchers", "discover", "ancient", "Mayan", "city", "hidden", "in", "the", "jungle", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New study highlights impact of climate change on polar bear populations.", "tokens": ["New", "study", "highlights", "impact", "of", "climate", "change", "on", "polar", "bear", "populations", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "SpaceX founder Elon Musk announces plan for mission to Mars.", "tokens": ["SpaceX", "founder", "Elon", "Musk", "announces", "plan", "for", "mission", "to", "Mars", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Global survey ranks Paris as the best city for student life.", "tokens": ["Global", "survey", "ranks", "Paris", "as", "the", "best", "city", "for", "student", "life", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Study finds link between fast food consumption and obesity rates.", "tokens": ["Study", "finds", "link", "between", "fast", "food", "consumption", "and", "obesity", "rates", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York City mayor announces plan to address homelessness crisis.", "tokens": ["New", "York", "City", "mayor", "announces", "plan", "to", "address", "homelessness", "crisis", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Researchers identify new strain of flu virus with pandemic potential.", "tokens": ["Researchers", "identify", "new", "strain", "of", "flu", "virus", "with", "pandemic", "potential", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon faces antitrust scrutiny over its e-commerce dominance.", "tokens": ["Amazon", "faces", "antitrust", "scrutiny", "over", "its", "e", "-", "commerce", "dominance", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO Mark Zuckerberg testifies before Congress on data privacy issues.", "tokens": ["CEO", "Mark", "Zuckerberg", "testifies", "before", "Congress", "on", "data", "privacy", "issues", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "World Health Organization issues warning about new Covid variant.", "tokens": ["World", "Health", "Organization", "issues", "warning", "about", "new", "Covid", "variant", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Scientists discover new species of deep-sea fish in the Pacific Ocean.", "tokens": ["Scientists", "discover", "new", "species", "of", "deep", "-", "sea", "fish", "in", "the", "Pacific", "Ocean", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Study shows impact of deforestation on biodiversity in the Amazon rainforest.", "tokens": ["Study", "shows", "impact", "of", "deforestation", "on", "biodiversity", "in", "the", "Amazon", "rainforest", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O", "O"]}
{"sentence": "Apple CEO Tim Cook announces plans for sustainable manufacturing.", "tokens": ["Apple", "CEO", "Tim", "Cook", "announces", "plans", "for", "sustainable", "manufacturing", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Researcher Elizabeth Blackwell honored for groundbreaking medical discoveries.", "tokens": ["Researcher", "Elizabeth", "Blackwell", "honored", "for", "groundbreaking", "medical", "discoveries", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple unveils new iPhone model.", "tokens": ["Apple", "unveils", "new", "iPhone", "model", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "President Biden delivers State of the Union address.", "tokens": ["President", "Biden", "delivers", "State", "of", "the", "Union", "address", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Powerful earthquake hits Japan.", "tokens": ["Powerful", "earthquake", "hits", "Japan", "."], "labels": ["O", "O", "O", "B-location", "O"]}
{"sentence": "Amazon workers protest for higher wages.", "tokens": ["Amazon", "workers", "protest", "for", "higher", "wages", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Microsoft steps down.", "tokens": ["CEO", "of", "Microsoft", "steps", "down", "."], "labels": ["O", "O", "B-organization", "O", "O", "O"]}
{"sentence": "Wildfires continue to ravage California.", "tokens": ["Wildfires", "continue", "to", "ravage", "California", "."], "labels": ["O", "O", "O", "O", "B-location", "O"]}
{"sentence": "NASA announces new space mission.", "tokens": ["NASA", "announces", "new", "space", "mission", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "New study shows link between obesity and heart disease.", "tokens": ["New", "study", "shows", "link", "between", "obesity", "and", "heart", "disease", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Famous actor opens restaurant in New York City.", "tokens": ["Famous", "actor", "opens", "restaurant", "in", "New", "York", "City", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "I-location", "I-location", "O"]}
{"sentence": "United Nations report highlights climate change impact.", "tokens": ["United", "Nations", "report", "highlights", "climate", "change", "impact", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Bill Gates donates millions to charity.", "tokens": ["Bill", "Gates", "donates", "millions", "to", "charity", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Pandemic causes economic downturn in Italy.", "tokens": ["Pandemic", "causes", "economic", "downturn", "in", "Italy", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Facebook data breach affects millions of users.", "tokens": ["Facebook", "data", "breach", "affects", "millions", "of", "users", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Olympic Games to be held in Paris.", "tokens": ["Olympic", "Games", "to", "be", "held", "in", "Paris", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "CEO of Tesla under investigation for fraud.", "tokens": ["CEO", "of", "Tesla", "under", "investigation", "for", "fraud", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Protests in Hong Kong demand democratic reforms.", "tokens": ["Protests", "in", "Hong", "Kong", "demand", "democratic", "reforms", "."], "labels": ["O", "O", "B-location", "I-location", "O", "O", "O", "O"]}
{"sentence": "Federal Reserve raises interest rates.", "tokens": ["Federal", "Reserve", "raises", "interest", "rates", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O"]}
{"sentence": "Famous singer cancels world tour.", "tokens": ["Famous", "singer", "cancels", "world", "tour", "."], "labels": ["O", "O", "O", "O", "O", "O"]}
{"sentence": "Wildfires in Australia destroy homes.", "tokens": ["Wildfires", "in", "Australia", "destroy", "homes", "."], "labels": ["O", "O", "B-location", "O", "O", "O"]}
{"sentence": "CEO of Walmart steps down.", "tokens": ["CEO", "of", "Walmart", "steps", "down", "."], "labels": ["O", "O", "B-organization", "O", "O", "O"]}
{"sentence": "New study shows benefits of exercise.", "tokens": ["New", "study", "shows", "benefits", "of", "exercise", "."], "labels": ["O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations peacekeepers sent to conflict zone.", "tokens": ["United", "Nations", "peacekeepers", "sent", "to", "conflict", "zone", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Oil spill devastates marine life in the Gulf of Mexico.", "tokens": ["Oil", "spill", "devastates", "marine", "life", "in", "the", "Gulf", "of", "Mexico", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "I-location", "O"]}
{"sentence": "Google faces antitrust lawsuit.", "tokens": ["Google", "faces", "antitrust", "lawsuit", "."], "labels": ["B-organization", "O", "O", "O", "O"]}
{"sentence": "South Korea announces new trade deal with Japan.", "tokens": ["South", "Korea", "announces", "new", "trade", "deal", "with", "Japan", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Famous author releases new book.", "tokens": ["Famous", "author", "releases", "new", "book", "."], "labels": ["O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations humanitarian aid reaches war-torn region.", "tokens": ["United", "Nations", "humanitarian", "aid", "reaches", "war", "-", "torn", "region", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "German Chancellor addresses immigration crisis.", "tokens": ["German", "Chancellor", "addresses", "immigration", "crisis", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "Protesters clash with police in London.", "tokens": ["Protesters", "clash", "with", "police", "in", "London", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Reuters wins Pulitzer Prize for investigative journalism.", "tokens": ["Reuters", "wins", "Pulitzer", "Prize", "for", "investigative", "journalism", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New study shows potential link between cell phone use and cancer.", "tokens": ["New", "study", "shows", "potential", "link", "between", "cell", "phone", "use", "and", "cancer", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "European Union leaders meet to discuss economic recovery.", "tokens": ["European", "Union", "leaders", "meet", "to", "discuss", "economic", "recovery", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Amazon under scrutiny for labor practices.", "tokens": ["CEO", "of", "Amazon", "under", "scrutiny", "for", "labor", "practices", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Heavy rainfall causes flooding in India.", "tokens": ["Heavy", "rainfall", "causes", "flooding", "in", "India", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Apple CEO announces new product lineup.", "tokens": ["Apple", "CEO", "announces", "new", "product", "lineup", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New study shows impact of air pollution on respiratory health.", "tokens": ["New", "study", "shows", "impact", "of", "air", "pollution", "on", "respiratory", "health", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "President Macron signs new environmental legislation.", "tokens": ["President", "Macron", "signs", "new", "environmental", "legislation", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Violence erupts in the Middle East.", "tokens": ["Violence", "erupts", "in", "the", "Middle", "East", "."], "labels": ["O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Walmart employees demand better working conditions.", "tokens": ["Walmart", "employees", "demand", "better", "working", "conditions", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Study finds potential breakthrough in cancer research.", "tokens": ["Study", "finds", "potential", "breakthrough", "in", "cancer", "research", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "UNICEF reports alarming rise in child poverty.", "tokens": ["UNICEF", "reports", "alarming", "rise", "in", "child", "poverty", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Twitter resigns amid controversy.", "tokens": ["CEO", "of", "Twitter", "resigns", "amid", "controversy", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O"]}
{"sentence": "Famous athlete announces retirement.", "tokens": ["Famous", "athlete", "announces", "retirement", "."], "labels": ["O", "O", "O", "O", "O"]}
{"sentence": "High-speed train derails in Germany.", "tokens": ["High", "-", "speed", "train", "derails", "in", "Germany", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "CEO of Google testifies before Congress.", "tokens": ["CEO", "of", "Google", "testifies", "before", "Congress", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O"]}
{"sentence": "New study shows positive effects of meditation on stress levels.", "tokens": ["New", "study", "shows", "positive", "effects", "of", "meditation", "on", "stress", "levels", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Taylor Swift releases new album", "tokens": ["Taylor", "Swift", "releases", "new", "album"], "labels": ["B-person", "I-person", "O", "O", "O"]}
{"sentence": "SpaceX successfully launches mission to Mars", "tokens": ["SpaceX", "successfully", "launches", "mission", "to", "Mars"], "labels": ["B-organization", "O", "O", "O", "O", "B-location"]}
{"sentence": "President Biden announces new infrastructure plan", "tokens": ["President", "Biden", "announces", "new", "infrastructure", "plan"], "labels": ["B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "Amazon opens new headquarters in New York City", "tokens": ["Amazon", "opens", "new", "headquarters", "in", "New", "York", "City"], "labels": ["B-organization", "O", "O", "O", "O", "B-location", "I-location", "I-location"]}
{"sentence": "El Salvador becomes first country to adopt Bitcoin as legal tender", "tokens": ["El", "Salvador", "becomes", "first", "country", "to", "adopt", "Bitcoin", "as", "legal", "tender"], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Serena Williams wins Wimbledon championship", "tokens": ["Serena", "Williams", "wins", "Wimbledon", "championship"], "labels": ["B-person", "I-person", "O", "O", "O"]}
{"sentence": "European Union imposes sanctions on Russia", "tokens": ["European", "Union", "imposes", "sanctions", "on", "Russia"], "labels": ["B-organization", "I-organization", "O", "O", "O", "B-location"]}
{"sentence": "Jeff Bezos steps down as CEO of Amazon", "tokens": ["Jeff", "Bezos", "steps", "down", "as", "CEO", "of", "Amazon"], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "B-organization"]}
{"sentence": "California wildfires force thousands to evacuate", "tokens": ["California", "wildfires", "force", "thousands", "to", "evacuate"], "labels": ["B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Pfizer announces new COVID-19 vaccine booster shot", "tokens": ["Pfizer", "announces", "new", "COVID", "-", "19", "vaccine", "booster", "shot"], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tokyo Olympics opening ceremony dazzles viewers", "tokens": ["Tokyo", "Olympics", "opening", "ceremony", "dazzles", "viewers"], "labels": ["B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Elon Musk's SpaceX plans mission to colonize Mars", "tokens": ["Elon", "Musk", "'", "s", "SpaceX", "plans", "mission", "to", "colonize", "Mars"], "labels": ["B-person", "I-person", "O", "O", "B-organization", "O", "O", "O", "O", "B-location"]}
{"sentence": "Google faces antitrust investigation by EU", "tokens": ["Google", "faces", "antitrust", "investigation", "by", "EU"], "labels": ["B-organization", "O", "O", "O", "O", "B-organization"]}
{"sentence": "Hurricane Irma devastates Caribbean islands", "tokens": ["Hurricane", "Irma", "devastates", "Caribbean", "islands"], "labels": ["O", "O", "O", "B-location", "O"]}
{"sentence": "Cristiano Ronaldo signs with Manchester United", "tokens": ["Cristiano", "Ronaldo", "signs", "with", "Manchester", "United"], "labels": ["B-person", "I-person", "O", "O", "B-organization", "I-organization"]}
{"sentence": "New York City mayor announces new affordable housing initiative", "tokens": ["New", "York", "City", "mayor", "announces", "new", "affordable", "housing", "initiative"], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Angela Merkel wins re-election in Germany", "tokens": ["Angela", "Merkel", "wins", "re", "-", "election", "in", "Germany"], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "B-location"]}
{"sentence": "Facebook under fire for privacy violations", "tokens": ["Facebook", "under", "fire", "for", "privacy", "violations"], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Miami Dolphins sign new quarterback", "tokens": ["Miami", "Dolphins", "sign", "new", "quarterback"], "labels": ["B-organization", "I-organization", "O", "O", "O"]}
{"sentence": "Indonesian volcano eruption leads to evacuation of nearby villages", "tokens": ["Indonesian", "volcano", "eruption", "leads", "to", "evacuation", "of", "nearby", "villages"], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Taylor Swift breaks record for most listened-to album in a day", "tokens": ["Taylor", "Swift", "breaks", "record", "for", "most", "listened", "-", "to", "album", "in", "a", "day"], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple unveils new iPhone with advanced features", "tokens": ["Apple", "unveils", "new", "iPhone", "with", "advanced", "features"], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Greece struggles with economic crisis", "tokens": ["Greece", "struggles", "with", "economic", "crisis"], "labels": ["B-location", "O", "O", "O", "O"]}
{"sentence": "Serena Williams donates $1 million to charity", "tokens": ["Serena", "Williams", "donates", "$", "1", "million", "to", "charity"], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tesla surpasses $1 trillion market value", "tokens": ["Tesla", "surpasses", "$", "1", "trillion", "market", "value"], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Paris Hilton launches new fashion line", "tokens": ["Paris", "Hilton", "launches", "new", "fashion", "line"], "labels": ["B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "United Nations issues global warning on climate change", "tokens": ["United", "Nations", "issues", "global", "warning", "on", "climate", "change"], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "California governor signs new climate change legislation", "tokens": ["California", "governor", "signs", "new", "climate", "change", "legislation"], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Beyonc\u00e9 announces new world tour dates", "tokens": ["Beyonc\u00e9", "announces", "new", "world", "tour", "dates"], "labels": ["B-person", "O", "O", "O", "O", "O"]}
{"sentence": "Boeing announces plans for new supersonic jet", "tokens": ["Boeing", "announces", "plans", "for", "new", "supersonic", "jet"], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Portugal national soccer team wins European championship", "tokens": ["Portugal", "national", "soccer", "team", "wins", "European", "championship"], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Bill Gates donates billions to combat malaria", "tokens": ["Bill", "Gates", "donates", "billions", "to", "combat", "malaria"], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Volkswagen recalls millions of vehicles for safety issues", "tokens": ["Volkswagen", "recalls", "millions", "of", "vehicles", "for", "safety", "issues"], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "London experiences record-breaking heatwave", "tokens": ["London", "experiences", "record", "-", "breaking", "heatwave"], "labels": ["B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Taylor Swift wins Grammy for Album of the Year", "tokens": ["Taylor", "Swift", "wins", "Grammy", "for", "Album", "of", "the", "Year"], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Microsoft acquires video game company for $7 billion", "tokens": ["Microsoft", "acquires", "video", "game", "company", "for", "$", "7", "billion"], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Adele's new album breaks streaming records", "tokens": ["Adele", "'", "s", "new", "album", "breaks", "streaming", "records"], "labels": ["B-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon founder Jeff Bezos launches into space", "tokens": ["Amazon", "founder", "Jeff", "Bezos", "launches", "into", "space"], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O"]}
{"sentence": "New Zealand implements strict COVID-19 restrictions", "tokens": ["New", "Zealand", "implements", "strict", "COVID", "-", "19", "restrictions"], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Lady Gaga to headline Super Bowl halftime show", "tokens": ["Lady", "Gaga", "to", "headline", "Super", "Bowl", "halftime", "show"], "labels": ["B-person", "I-person", "O", "O", "B-organization", "I-organization", "O", "O"]}
{"sentence": "International Space Station celebrates 20th anniversary", "tokens": ["International", "Space", "Station", "celebrates", "20th", "anniversary"], "labels": ["B-location", "I-location", "I-location", "O", "O", "O"]}
{"sentence": "Taylor Swift donates $1 million to tornado relief efforts", "tokens": ["Taylor", "Swift", "donates", "$", "1", "million", "to", "tornado", "relief", "efforts"], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "South Korea imposes strict lockdown measures", "tokens": ["South", "Korea", "imposes", "strict", "lockdown", "measures"], "labels": ["B-location", "I-location", "O", "O", "O", "O"]}
{"sentence": "Justin Bieber surprises fans with impromptu concert", "tokens": ["Justin", "Bieber", "surprises", "fans", "with", "impromptu", "concert"], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Apple faces lawsuit over alleged patent infringement", "tokens": ["Apple", "faces", "lawsuit", "over", "alleged", "patent", "infringement"], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York City subway system faces significant delays", "tokens": ["New", "York", "City", "subway", "system", "faces", "significant", "delays"], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O"]}
{"sentence": "Beyonc\u00e9 to receive prestigious award for humanitarian work", "tokens": ["Beyonc\u00e9", "to", "receive", "prestigious", "award", "for", "humanitarian", "work"], "labels": ["B-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Russia launches new space mission to Mars", "tokens": ["Russia", "launches", "new", "space", "mission", "to", "Mars"], "labels": ["B-location", "O", "O", "O", "O", "O", "B-location"]}
{"sentence": "Kanye West announces new album release", "tokens": ["Kanye", "West", "announces", "new", "album", "release"], "labels": ["B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "The CEO of Apple, Tim Cook, announced a new product launch.", "tokens": ["The", "CEO", "of", "Apple", ",", "Tim", "Cook", ",", "announced", "a", "new", "product", "launch", "."], "labels": ["O", "O", "O", "B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Thousands of people were evacuated from the flooded city of Houston.", "tokens": ["Thousands", "of", "people", "were", "evacuated", "from", "the", "flooded", "city", "of", "Houston", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "The United Nations issued a statement condemning the recent terrorist attacks.", "tokens": ["The", "United", "Nations", "issued", "a", "statement", "condemning", "the", "recent", "terrorist", "attacks", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The famous actor, Tom Hanks, will star in a new biopic about a World War II hero.", "tokens": ["The", "famous", "actor", ",", "Tom", "Hanks", ",", "will", "star", "in", "a", "new", "biopic", "about", "a", "World", "War", "II", "hero", "."], "labels": ["O", "O", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon reported a record-breaking quarter with a 200% increase in profits.", "tokens": ["Amazon", "reported", "a", "record", "-", "breaking", "quarter", "with", "a", "200", "%", "increase", "in", "profits", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The president of France, Emmanuel Macron, met with world leaders to discuss climate change.", "tokens": ["The", "president", "of", "France", ",", "Emmanuel", "Macron", ",", "met", "with", "world", "leaders", "to", "discuss", "climate", "change", "."], "labels": ["O", "O", "O", "B-location", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Hurricane Florence wreaks havoc in the Carolinas.", "tokens": ["Hurricane", "Florence", "wreaks", "havoc", "in", "the", "Carolinas", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "NASA to launch a new space probe to explore the outer planets.", "tokens": ["NASA", "to", "launch", "a", "new", "space", "probe", "to", "explore", "the", "outer", "planets", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The UK Prime Minister, Boris Johnson, faces criticism over his handling of the pandemic.", "tokens": ["The", "UK", "Prime", "Minister", ",", "Boris", "Johnson", ",", "faces", "criticism", "over", "his", "handling", "of", "the", "pandemic", "."], "labels": ["O", "B-location", "O", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tesla's CEO, Elon Musk, unveils plans for a new electric car model.", "tokens": ["Tesla", "'", "s", "CEO", ",", "Elon", "Musk", ",", "unveils", "plans", "for", "a", "new", "electric", "car", "model", "."], "labels": ["B-organization", "O", "O", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The city of Tokyo gears up for the upcoming Olympics.", "tokens": ["The", "city", "of", "Tokyo", "gears", "up", "for", "the", "upcoming", "Olympics", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Health Organization warns of a new virus spreading rapidly in Africa.", "tokens": ["The", "World", "Health", "Organization", "warns", "of", "a", "new", "virus", "spreading", "rapidly", "in", "Africa", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "10 killed in a terrorist attack in Berlin.", "tokens": ["10", "killed", "in", "a", "terrorist", "attack", "in", "Berlin", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "The legendary musician, Bob Dylan, releases a new album.", "tokens": ["The", "legendary", "musician", ",", "Bob", "Dylan", ",", "releases", "a", "new", "album", "."], "labels": ["O", "O", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook under fire for data privacy breach.", "tokens": ["Facebook", "under", "fire", "for", "data", "privacy", "breach", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Scientists discover a new species of lizard in the Amazon rainforest.", "tokens": ["Scientists", "discover", "a", "new", "species", "of", "lizard", "in", "the", "Amazon", "rainforest", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O", "O"]}
{"sentence": "Former President Barack Obama to write a memoir about his time in office.", "tokens": ["Former", "President", "Barack", "Obama", "to", "write", "a", "memoir", "about", "his", "time", "in", "office", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Google launches a new initiative to support small businesses.", "tokens": ["Google", "launches", "a", "new", "initiative", "to", "support", "small", "businesses", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Prime Minister of Japan, Shinzo Abe, resigns due to health reasons.", "tokens": ["The", "Prime", "Minister", "of", "Japan", ",", "Shinzo", "Abe", ",", "resigns", "due", "to", "health", "reasons", "."], "labels": ["O", "O", "O", "O", "B-location", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Wildfires rage through the state of California, destroying thousands of homes.", "tokens": ["Wildfires", "rage", "through", "the", "state", "of", "California", ",", "destroying", "thousands", "of", "homes", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple announces new iPhone release.", "tokens": ["Apple", "announces", "new", "iPhone", "release", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "London mayor approves new public transportation plan.", "tokens": ["London", "mayor", "approves", "new", "public", "transportation", "plan", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Google steps down.", "tokens": ["CEO", "of", "Google", "steps", "down", "."], "labels": ["O", "O", "B-organization", "O", "O", "O"]}
{"sentence": "United Nations report warns of climate change crisis.", "tokens": ["United", "Nations", "report", "warns", "of", "climate", "change", "crisis", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New study shows link between coffee consumption and lower risk of heart disease.", "tokens": ["New", "study", "shows", "link", "between", "coffee", "consumption", "and", "lower", "risk", "of", "heart", "disease", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "President of France visits Canada.", "tokens": ["President", "of", "France", "visits", "Canada", "."], "labels": ["O", "O", "B-location", "O", "B-location", "O"]}
{"sentence": "Supreme Court rules in favor of same-sex marriage.", "tokens": ["Supreme", "Court", "rules", "in", "favor", "of", "same", "-", "sex", "marriage", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Famous actor announces retirement from Hollywood.", "tokens": ["Famous", "actor", "announces", "retirement", "from", "Hollywood", "."], "labels": ["O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Microsoft unveils new product line.", "tokens": ["CEO", "of", "Microsoft", "unveils", "new", "product", "line", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Paris Fashion Week attracts international designers.", "tokens": ["Paris", "Fashion", "Week", "attracts", "international", "designers", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "British Prime Minister calls for national healthcare reform.", "tokens": ["British", "Prime", "Minister", "calls", "for", "national", "healthcare", "reform", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Starbucks introduces new eco-friendly coffee cup.", "tokens": ["Starbucks", "introduces", "new", "eco", "-", "friendly", "coffee", "cup", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "California wildfires leave thousands homeless.", "tokens": ["California", "wildfires", "leave", "thousands", "homeless", "."], "labels": ["B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Australian scientist makes breakthrough in cancer research.", "tokens": ["Australian", "scientist", "makes", "breakthrough", "in", "cancer", "research", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "World Health Organization declares pandemic.", "tokens": ["World", "Health", "Organization", "declares", "pandemic", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O"]}
{"sentence": "Facebook founder testifies before Congress.", "tokens": ["Facebook", "founder", "testifies", "before", "Congress", "."], "labels": ["B-organization", "O", "O", "O", "B-organization", "O"]}
{"sentence": "German chancellor announces economic stimulus plan.", "tokens": ["German", "chancellor", "announces", "economic", "stimulus", "plan", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New regulations proposed for cryptocurrency trading.", "tokens": ["New", "regulations", "proposed", "for", "cryptocurrency", "trading", "."], "labels": ["O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "NBA player breaks record for most points in a single game.", "tokens": ["NBA", "player", "breaks", "record", "for", "most", "points", "in", "a", "single", "game", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Italian government unveils plan to reduce carbon emissions.", "tokens": ["Italian", "government", "unveils", "plan", "to", "reduce", "carbon", "emissions", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "London Stock Exchange experiences record high trading volume.", "tokens": ["London", "Stock", "Exchange", "experiences", "record", "high", "trading", "volume", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Russia accuses United States of election interference.", "tokens": ["Russia", "accuses", "United", "States", "of", "election", "interference", "."], "labels": ["B-location", "O", "B-location", "I-location", "O", "O", "O", "O"]}
{"sentence": "Famous singer cancels world tour due to illness.", "tokens": ["Famous", "singer", "cancels", "world", "tour", "due", "to", "illness", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Japan launches new satellite into space.", "tokens": ["Japan", "launches", "new", "satellite", "into", "space", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "British royal family welcomes new addition.", "tokens": ["British", "royal", "family", "welcomes", "new", "addition", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Volkswagen recalls millions of vehicles over safety concerns.", "tokens": ["Volkswagen", "recalls", "millions", "of", "vehicles", "over", "safety", "concerns", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Australian Open tennis tournament kicks off.", "tokens": ["Australian", "Open", "tennis", "tournament", "kicks", "off", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "New report shows rising sea levels in the Maldives.", "tokens": ["New", "report", "shows", "rising", "sea", "levels", "in", "the", "Maldives", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Major earthquake strikes California.", "tokens": ["Major", "earthquake", "strikes", "California", "."], "labels": ["O", "O", "O", "B-location", "O"]}
{"sentence": "Spanish government announces new immigration policy.", "tokens": ["Spanish", "government", "announces", "new", "immigration", "policy", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Intel reveals new chip technology.", "tokens": ["Intel", "reveals", "new", "chip", "technology", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "South Korean president meets with North Korean leader for peace talks.", "tokens": ["South", "Korean", "president", "meets", "with", "North", "Korean", "leader", "for", "peace", "talks", "."], "labels": ["B-location", "I-location", "O", "O", "O", "B-location", "I-location", "O", "O", "O", "O", "O"]}
{"sentence": "World Bank approves loan for infrastructure development in Africa.", "tokens": ["World", "Bank", "approves", "loan", "for", "infrastructure", "development", "in", "Africa", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "British Prime Minister resigns amid political scandal.", "tokens": ["British", "Prime", "Minister", "resigns", "amid", "political", "scandal", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New study shows link between air pollution and increased respiratory illnesses.", "tokens": ["New", "study", "shows", "link", "between", "air", "pollution", "and", "increased", "respiratory", "illnesses", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "NASA announces plans for manned mission to Mars.", "tokens": ["NASA", "announces", "plans", "for", "manned", "mission", "to", "Mars", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "New York City to invest billions in public housing renovations.", "tokens": ["New", "York", "City", "to", "invest", "billions", "in", "public", "housing", "renovations", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Brazilian president faces impeachment proceedings.", "tokens": ["Brazilian", "president", "faces", "impeachment", "proceedings", "."], "labels": ["B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Tesla stocks surge after strong earnings report.", "tokens": ["Tesla", "stocks", "surge", "after", "strong", "earnings", "report", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Hong Kong protests escalate as government crackdown intensifies.", "tokens": ["Hong", "Kong", "protests", "escalate", "as", "government", "crackdown", "intensifies", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Famous author wins Nobel Prize for Literature.", "tokens": ["Famous", "author", "wins", "Nobel", "Prize", "for", "Literature", "."], "labels": ["O", "O", "O", "B-organization", "I-organization", "O", "O", "O"]}
{"sentence": "Paris to implement new measures to combat air pollution.", "tokens": ["Paris", "to", "implement", "new", "measures", "to", "combat", "air", "pollution", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "World Economic Forum to be held in Davos.", "tokens": ["World", "Economic", "Forum", "to", "be", "held", "in", "Davos", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Elon Musk's SpaceX launches 60 more Starlink satellites.", "tokens": ["Elon", "Musk", "'", "s", "SpaceX", "launches", "60", "more", "Starlink", "satellites", "."], "labels": ["B-person", "I-person", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York City announces plans to build new affordable housing.", "tokens": ["New", "York", "City", "announces", "plans", "to", "build", "new", "affordable", "housing", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Health Organization declares a global health emergency.", "tokens": ["The", "World", "Health", "Organization", "declares", "a", "global", "health", "emergency", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple unveils new iPhone with 5G capabilities.", "tokens": ["Apple", "unveils", "new", "iPhone", "with", "5G", "capabilities", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Several European countries impose new travel restrictions.", "tokens": ["Several", "European", "countries", "impose", "new", "travel", "restrictions", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon expands its warehouse network in Texas.", "tokens": ["Amazon", "expands", "its", "warehouse", "network", "in", "Texas", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Olympic swimmer Michael Phelps sets new world record.", "tokens": ["Olympic", "swimmer", "Michael", "Phelps", "sets", "new", "world", "record", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations calls for ceasefire in ongoing conflict.", "tokens": ["The", "United", "Nations", "calls", "for", "ceasefire", "in", "ongoing", "conflict", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "California wildfires continue to threaten homes and wildlife.", "tokens": ["California", "wildfires", "continue", "to", "threaten", "homes", "and", "wildlife", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook introduces new privacy features for users.", "tokens": ["Facebook", "introduces", "new", "privacy", "features", "for", "users", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Microsoft announces plans for expansion in Asia.", "tokens": ["CEO", "of", "Microsoft", "announces", "plans", "for", "expansion", "in", "Asia", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Former President Obama to release new memoir.", "tokens": ["Former", "President", "Obama", "to", "release", "new", "memoir", "."], "labels": ["O", "O", "B-person", "O", "O", "O", "O", "O"]}
{"sentence": "Paris fashion week goes virtual amid pandemic.", "tokens": ["Paris", "fashion", "week", "goes", "virtual", "amid", "pandemic", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "WHO investigates new strain of avian flu in China.", "tokens": ["WHO", "investigates", "new", "strain", "of", "avian", "flu", "in", "China", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "SpaceX founder Elon Musk launches new electric car company.", "tokens": ["SpaceX", "founder", "Elon", "Musk", "launches", "new", "electric", "car", "company", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "London mayor announces new transportation initiatives.", "tokens": ["London", "mayor", "announces", "new", "transportation", "initiatives", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Google faces antitrust investigation from European Union.", "tokens": ["Google", "faces", "antitrust", "investigation", "from", "European", "Union", "."], "labels": ["B-organization", "O", "O", "O", "O", "B-organization", "I-organization", "O"]}
{"sentence": "Pope Francis meets with leaders from various religious organizations.", "tokens": ["Pope", "Francis", "meets", "with", "leaders", "from", "various", "religious", "organizations", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Music festival in New Orleans canceled due to COVID-19 concerns.", "tokens": ["Music", "festival", "in", "New", "Orleans", "canceled", "due", "to", "COVID", "-", "19", "concerns", "."], "labels": ["O", "O", "O", "B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tesla CEO Elon Musk becomes world's richest person.", "tokens": ["Tesla", "CEO", "Elon", "Musk", "becomes", "world", "'", "s", "richest", "person", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "South Africa imposes new lockdown measures to curb COVID-19 cases.", "tokens": ["South", "Africa", "imposes", "new", "lockdown", "measures", "to", "curb", "COVID", "-", "19", "cases", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Angelina Jolie donates millions to refugee relief efforts.", "tokens": ["Angelina", "Jolie", "donates", "millions", "to", "refugee", "relief", "efforts", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "London-based pharmaceutical company announces breakthrough in cancer research.", "tokens": ["London", "-", "based", "pharmaceutical", "company", "announces", "breakthrough", "in", "cancer", "research", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Japan to host 2021 Summer Olympics despite pandemic concerns.", "tokens": ["Japan", "to", "host", "2021", "Summer", "Olympics", "despite", "pandemic", "concerns", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Amazon Jeff Bezos steps down from position.", "tokens": ["CEO", "of", "Amazon", "Jeff", "Bezos", "steps", "down", "from", "position", "."], "labels": ["O", "O", "B-organization", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Wildfires in Australia lead to unprecedented destruction.", "tokens": ["Wildfires", "in", "Australia", "lead", "to", "unprecedented", "destruction", "."], "labels": ["O", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "New York City to invest in infrastructure improvements.", "tokens": ["New", "York", "City", "to", "invest", "in", "infrastructure", "improvements", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "SpaceX successfully lands rocket on drone ship.", "tokens": ["SpaceX", "successfully", "lands", "rocket", "on", "drone", "ship", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Elon Musk's Tesla announces plans for new electric pickup truck.", "tokens": ["Elon", "Musk", "'", "s", "Tesla", "announces", "plans", "for", "new", "electric", "pickup", "truck", "."], "labels": ["B-person", "I-person", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "France to increase police presence in response to recent terrorist attacks.", "tokens": ["France", "to", "increase", "police", "presence", "in", "response", "to", "recent", "terrorist", "attacks", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Apple Tim Cook unveils new line of products.", "tokens": ["CEO", "of", "Apple", "Tim", "Cook", "unveils", "new", "line", "of", "products", "."], "labels": ["O", "O", "B-organization", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "London prepares for Brexit transition period to end.", "tokens": ["London", "prepares", "for", "Brexit", "transition", "period", "to", "end", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Google's parent company Alphabet faces legal challenges.", "tokens": ["Google", "'", "s", "parent", "company", "Alphabet", "faces", "legal", "challenges", "."], "labels": ["B-organization", "O", "O", "O", "O", "B-organization", "O", "O", "O", "O"]}
{"sentence": "Rising sea levels threaten coastal communities worldwide.", "tokens": ["Rising", "sea", "levels", "threaten", "coastal", "communities", "worldwide", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "South Korean president addresses nation in New Year's speech.", "tokens": ["South", "Korean", "president", "addresses", "nation", "in", "New", "Year", "'", "s", "speech", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Ford CEO announces plans for increased investment in electric vehicles.", "tokens": ["Ford", "CEO", "announces", "plans", "for", "increased", "investment", "in", "electric", "vehicles", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Germany reports record number of COVID-19 cases.", "tokens": ["Germany", "reports", "record", "number", "of", "COVID", "-", "19", "cases", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Microsoft co-founder Bill Gates launches new initiative to combat climate change.", "tokens": ["Microsoft", "co", "-", "founder", "Bill", "Gates", "launches", "new", "initiative", "to", "combat", "climate", "change", "."], "labels": ["B-organization", "O", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "World Bank provides financial assistance to developing countries.", "tokens": ["World", "Bank", "provides", "financial", "assistance", "to", "developing", "countries", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Australian Open tennis tournament to proceed with limited audience.", "tokens": ["Australian", "Open", "tennis", "tournament", "to", "proceed", "with", "limited", "audience", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Hong Kong police arrest pro-democracy activists.", "tokens": ["Hong", "Kong", "police", "arrest", "pro", "-", "democracy", "activists", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Uber faces legal challenge over driver classification.", "tokens": ["Uber", "faces", "legal", "challenge", "over", "driver", "classification", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple unveils new iPhone 12 models.", "tokens": ["Apple", "unveils", "new", "iPhone", "12", "models", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "President Biden delivers inaugural address.", "tokens": ["President", "Biden", "delivers", "inaugural", "address", "."], "labels": ["O", "B-person", "O", "O", "O", "O"]}
{"sentence": "England imposes new lockdown measures.", "tokens": ["England", "imposes", "new", "lockdown", "measures", "."], "labels": ["B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Tokyo Olympics postponed due to COVID-19.", "tokens": ["Tokyo", "Olympics", "postponed", "due", "to", "COVID", "-", "19", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Prime Minister Trudeau announces new climate initiative.", "tokens": ["Prime", "Minister", "Trudeau", "announces", "new", "climate", "initiative", "."], "labels": ["O", "O", "B-person", "O", "O", "O", "O", "O"]}
{"sentence": "Germany extends lockdown restrictions.", "tokens": ["Germany", "extends", "lockdown", "restrictions", "."], "labels": ["B-location", "O", "O", "O", "O"]}
{"sentence": "Amazon launches new delivery drone service.", "tokens": ["Amazon", "launches", "new", "delivery", "drone", "service", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Twitter resigns.", "tokens": ["CEO", "of", "Twitter", "resigns", "."], "labels": ["O", "O", "B-organization", "O", "O"]}
{"sentence": "Wildfires sweep through California.", "tokens": ["Wildfires", "sweep", "through", "California", "."], "labels": ["O", "O", "O", "B-location", "O"]}
{"sentence": "President Macron tests positive for COVID-19.", "tokens": ["President", "Macron", "tests", "positive", "for", "COVID", "-", "19", "."], "labels": ["O", "B-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York City to mandate COVID-19 vaccinations for indoor activities.", "tokens": ["New", "York", "City", "to", "mandate", "COVID", "-", "19", "vaccinations", "for", "indoor", "activities", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations issues statement on refugee crisis.", "tokens": ["United", "Nations", "issues", "statement", "on", "refugee", "crisis", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "California governor faces recall election.", "tokens": ["California", "governor", "faces", "recall", "election", "."], "labels": ["B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Microsoft acquires gaming company for $7.5 billion.", "tokens": ["Microsoft", "acquires", "gaming", "company", "for", "$", "7", ".", "5", "billion", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Deadly earthquake strikes Haiti.", "tokens": ["Deadly", "earthquake", "strikes", "Haiti", "."], "labels": ["O", "O", "O", "B-location", "O"]}
{"sentence": "Olympic gold medalist Simone Biles withdraws from competition.", "tokens": ["Olympic", "gold", "medalist", "Simone", "Biles", "withdraws", "from", "competition", "."], "labels": ["O", "O", "O", "B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "WHO declares COVID-19 a global pandemic.", "tokens": ["WHO", "declares", "COVID", "-", "19", "a", "global", "pandemic", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New vaccine mandates issued for federal employees.", "tokens": ["New", "vaccine", "mandates", "issued", "for", "federal", "employees", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook whistleblower reveals internal documents.", "tokens": ["Facebook", "whistleblower", "reveals", "internal", "documents", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Hurricane causes widespread damage in Louisiana.", "tokens": ["Hurricane", "causes", "widespread", "damage", "in", "Louisiana", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "CEO of Amazon announces retirement.", "tokens": ["CEO", "of", "Amazon", "announces", "retirement", "."], "labels": ["O", "O", "B-organization", "O", "O", "O"]}
{"sentence": "Voter turnout reaches record high in presidential election.", "tokens": ["Voter", "turnout", "reaches", "record", "high", "in", "presidential", "election", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Canadian province Manitoba declares state of emergency.", "tokens": ["Canadian", "province", "Manitoba", "declares", "state", "of", "emergency", "."], "labels": ["O", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Google faces antitrust lawsuit in Europe.", "tokens": ["Google", "faces", "antitrust", "lawsuit", "in", "Europe", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Severe flooding in India displaces thousands.", "tokens": ["Severe", "flooding", "in", "India", "displaces", "thousands", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O"]}
{"sentence": "Senator Warren proposes wealth tax.", "tokens": ["Senator", "Warren", "proposes", "wealth", "tax", "."], "labels": ["O", "B-person", "O", "O", "O", "O"]}
{"sentence": "Major cybersecurity breach affects government agencies.", "tokens": ["Major", "cybersecurity", "breach", "affects", "government", "agencies", "."], "labels": ["O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Wildfires force evacuations in Oregon.", "tokens": ["Wildfires", "force", "evacuations", "in", "Oregon", "."], "labels": ["O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Apple announces new privacy features for iPhone users.", "tokens": ["Apple", "announces", "new", "privacy", "features", "for", "iPhone", "users", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Pfizer seeks approval for COVID-19 vaccine booster shot.", "tokens": ["Pfizer", "seeks", "approval", "for", "COVID", "-", "19", "vaccine", "booster", "shot", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Florida governor signs controversial new voting bill into law.", "tokens": ["Florida", "governor", "signs", "controversial", "new", "voting", "bill", "into", "law", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Walmart announces ambitious sustainability goals.", "tokens": ["CEO", "of", "Walmart", "announces", "ambitious", "sustainability", "goals", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Death toll rises in European flooding disaster.", "tokens": ["Death", "toll", "rises", "in", "European", "flooding", "disaster", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Meghan Markle launches new initiative to support women in workforce.", "tokens": ["Meghan", "Markle", "launches", "new", "initiative", "to", "support", "women", "in", "workforce", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New study shows link between air pollution and increased risk of dementia.", "tokens": ["New", "study", "shows", "link", "between", "air", "pollution", "and", "increased", "risk", "of", "dementia", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Chicago mayor announces plan to tackle city's violence epidemic.", "tokens": ["Chicago", "mayor", "announces", "plan", "to", "tackle", "city", "'", "s", "violence", "epidemic", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "NASA announces new mission to study Europa's ocean.", "tokens": ["NASA", "announces", "new", "mission", "to", "study", "Europa", "'", "s", "ocean", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-location", "O", "O", "O", "O"]}
{"sentence": "Apple to release new iPhone model next month.", "tokens": ["Apple", "to", "release", "new", "iPhone", "model", "next", "month", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Former President Obama endorses candidate for Senate race.", "tokens": ["Former", "President", "Obama", "endorses", "candidate", "for", "Senate", "race", "."], "labels": ["B-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Wildfire in California destroys thousands of acres.", "tokens": ["Wildfire", "in", "California", "destroys", "thousands", "of", "acres", "."], "labels": ["O", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "WHO issues new guidelines for COVID-19 prevention.", "tokens": ["WHO", "issues", "new", "guidelines", "for", "COVID", "-", "19", "prevention", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New study finds link between air pollution and respiratory illnesses.", "tokens": ["New", "study", "finds", "link", "between", "air", "pollution", "and", "respiratory", "illnesses", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "SpaceX launches new satellites into orbit.", "tokens": ["SpaceX", "launches", "new", "satellites", "into", "orbit", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "High school student awarded scholarship for academic achievement.", "tokens": ["High", "school", "student", "awarded", "scholarship", "for", "academic", "achievement", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Supreme Court justice announces retirement.", "tokens": ["Supreme", "Court", "justice", "announces", "retirement", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O"]}
{"sentence": "Tornado causes widespread damage in Texas.", "tokens": ["Tornado", "causes", "widespread", "damage", "in", "Texas", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "International Red Cross delivers aid to refugees in Ukraine.", "tokens": ["International", "Red", "Cross", "delivers", "aid", "to", "refugees", "in", "Ukraine", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Chinese president visits European leaders.", "tokens": ["Chinese", "president", "visits", "European", "leaders", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "New research from Harvard University identifies potential treatment for Alzheimer's disease.", "tokens": ["New", "research", "from", "Harvard", "University", "identifies", "potential", "treatment", "for", "Alzheimer", "'", "s", "disease", "."], "labels": ["O", "O", "O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Police arrest suspect in connection with bank robbery.", "tokens": ["Police", "arrest", "suspect", "in", "connection", "with", "bank", "robbery", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "African Union condemns human rights abuses in South Sudan.", "tokens": ["African", "Union", "condemns", "human", "rights", "abuses", "in", "South", "Sudan", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Famous actor to star in upcoming movie.", "tokens": ["Famous", "actor", "to", "star", "in", "upcoming", "movie", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Twitter bans accounts spreading misinformation about COVID-19 vaccines.", "tokens": ["Twitter", "bans", "accounts", "spreading", "misinformation", "about", "COVID", "-", "19", "vaccines", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Canadian Prime Minister promises new initiatives for climate change.", "tokens": ["Canadian", "Prime", "Minister", "promises", "new", "initiatives", "for", "climate", "change", "."], "labels": ["B-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "US Treasury Department to provide aid to small businesses affected by pandemic.", "tokens": ["US", "Treasury", "Department", "to", "provide", "aid", "to", "small", "businesses", "affected", "by", "pandemic", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Doctor warns of potential outbreak of rare virus in Africa.", "tokens": ["Doctor", "warns", "of", "potential", "outbreak", "of", "rare", "virus", "in", "Africa", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Ford Motor Company recalls thousands of vehicles for safety issues.", "tokens": ["Ford", "Motor", "Company", "recalls", "thousands", "of", "vehicles", "for", "safety", "issues", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Japanese scientist wins Nobel Prize for groundbreaking research.", "tokens": ["Japanese", "scientist", "wins", "Nobel", "Prize", "for", "groundbreaking", "research", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "UNICEF launches campaign to provide clean water to communities in need.", "tokens": ["UNICEF", "launches", "campaign", "to", "provide", "clean", "water", "to", "communities", "in", "need", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon founder and CEO becomes world's richest person.", "tokens": ["Amazon", "founder", "and", "CEO", "becomes", "world", "'", "s", "richest", "person", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Italian government introduces new measures to combat organized crime.", "tokens": ["Italian", "government", "introduces", "new", "measures", "to", "combat", "organized", "crime", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Firefighters battle massive blaze in Australia.", "tokens": ["Firefighters", "battle", "massive", "blaze", "in", "Australia", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Olympic Games to be held in Paris in 2024.", "tokens": ["Olympic", "Games", "to", "be", "held", "in", "Paris", "in", "2024", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O", "O", "O"]}
{"sentence": "California governor signs bill to address homelessness crisis.", "tokens": ["California", "governor", "signs", "bill", "to", "address", "homelessness", "crisis", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Researchers develop promising vaccine for Malaria.", "tokens": ["Researchers", "develop", "promising", "vaccine", "for", "Malaria", "."], "labels": ["O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Bank of America to expand investment in renewable energy projects.", "tokens": ["Bank", "of", "America", "to", "expand", "investment", "in", "renewable", "energy", "projects", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Russian president meets with leaders from Middle East countries.", "tokens": ["Russian", "president", "meets", "with", "leaders", "from", "Middle", "East", "countries", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "B-location", "I-location", "O", "O"]}
{"sentence": "Royal Caribbean Cruises announces new ship for luxury travel.", "tokens": ["Royal", "Caribbean", "Cruises", "announces", "new", "ship", "for", "luxury", "travel", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "University of Oxford study shows potential link between diet and mental health.", "tokens": ["University", "of", "Oxford", "study", "shows", "potential", "link", "between", "diet", "and", "mental", "health", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple CEO unveils new product at tech conference.", "tokens": ["Apple", "CEO", "unveils", "new", "product", "at", "tech", "conference", "."], "labels": ["B-organization", "B-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New round of peace talks to begin in Middle East conflict.", "tokens": ["New", "round", "of", "peace", "talks", "to", "begin", "in", "Middle", "East", "conflict", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O", "O"]}
{"sentence": "British actress wins award for performance in new film.", "tokens": ["British", "actress", "wins", "award", "for", "performance", "in", "new", "film", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Volkswagen to invest billions in electric vehicle production.", "tokens": ["Volkswagen", "to", "invest", "billions", "in", "electric", "vehicle", "production", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Turkish government announces plans for infrastructure improvements.", "tokens": ["Turkish", "government", "announces", "plans", "for", "infrastructure", "improvements", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Former CEO of Google to testify before Congress.", "tokens": ["Former", "CEO", "of", "Google", "to", "testify", "before", "Congress", "."], "labels": ["O", "B-person", "I-person", "I-person", "O", "O", "O", "B-organization", "O"]}
{"sentence": "Solar storm could disrupt communication systems, warns scientist.", "tokens": ["Solar", "storm", "could", "disrupt", "communication", "systems", ",", "warns", "scientist", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Yelp to add new features for restaurant reviews.", "tokens": ["Yelp", "to", "add", "new", "features", "for", "restaurant", "reviews", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Philippines president signs law to combat online child exploitation.", "tokens": ["Philippines", "president", "signs", "law", "to", "combat", "online", "child", "exploitation", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New study finds correlation between social media use and depression.", "tokens": ["New", "study", "finds", "correlation", "between", "social", "media", "use", "and", "depression", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "IMF forecasts global economic growth in the upcoming year.", "tokens": ["IMF", "forecasts", "global", "economic", "growth", "in", "the", "upcoming", "year", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple unveils new iPhone 13 models.", "tokens": ["Apple", "unveils", "new", "iPhone", "13", "models", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Wildfires ravage through California.", "tokens": ["Wildfires", "ravage", "through", "California", "."], "labels": ["O", "O", "O", "B-location", "O"]}
{"sentence": "Amazon to hire 100,000 new employees.", "tokens": ["Amazon", "to", "hire", "100", ",", "000", "new", "employees", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "European Union imposes new tariffs on Chinese imports.", "tokens": ["European", "Union", "imposes", "new", "tariffs", "on", "Chinese", "imports", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "UN report highlights climate change crisis.", "tokens": ["UN", "report", "highlights", "climate", "change", "crisis", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York City to invest in affordable housing projects.", "tokens": ["New", "York", "City", "to", "invest", "in", "affordable", "housing", "projects", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Researchers discover new species in the Amazon rainforest.", "tokens": ["Researchers", "discover", "new", "species", "in", "the", "Amazon", "rainforest", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O", "O"]}
{"sentence": "Tokyo Olympics postponed due to COVID-19 outbreak.", "tokens": ["Tokyo", "Olympics", "postponed", "due", "to", "COVID", "-", "19", "outbreak", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Walmart to raise minimum wage for employees.", "tokens": ["Walmart", "to", "raise", "minimum", "wage", "for", "employees", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Angela Merkel wins reelection as German Chancellor.", "tokens": ["Angela", "Merkel", "wins", "reelection", "as", "German", "Chancellor", "."], "labels": ["B-person", "I-person", "O", "O", "O", "B-organization", "I-organization", "O"]}
{"sentence": "United Nations calls for ceasefire in Middle East conflict.", "tokens": ["United", "Nations", "calls", "for", "ceasefire", "in", "Middle", "East", "conflict", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "B-location", "I-location", "O", "O"]}
{"sentence": "California governor signs new climate change legislation.", "tokens": ["California", "governor", "signs", "new", "climate", "change", "legislation", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Twitter faces backlash over handling of misinformation.", "tokens": ["Twitter", "faces", "backlash", "over", "handling", "of", "misinformation", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "British royal family celebrates the Queen's birthday.", "tokens": ["British", "royal", "family", "celebrates", "the", "Queen", "'", "s", "birthday", "."], "labels": ["O", "O", "O", "O", "O", "B-person", "O", "O", "O", "O"]}
{"sentence": "NASA's Mars rover captures new images of the Martian landscape.", "tokens": ["NASA", "'", "s", "Mars", "rover", "captures", "new", "images", "of", "the", "Martian", "landscape", "."], "labels": ["B-organization", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Volkswagen announces plans to go all-electric by 2035.", "tokens": ["Volkswagen", "announces", "plans", "to", "go", "all", "-", "electric", "by", "2035", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Australian Prime Minister visits wildfire affected areas.", "tokens": ["Australian", "Prime", "Minister", "visits", "wildfire", "affected", "areas", "."], "labels": ["B-location", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook launches new feature to combat cyberbullying.", "tokens": ["Facebook", "launches", "new", "feature", "to", "combat", "cyberbullying", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Paris fashion week showcases latest designer collections.", "tokens": ["Paris", "fashion", "week", "showcases", "latest", "designer", "collections", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Tesla tweets controversial statement.", "tokens": ["CEO", "of", "Tesla", "tweets", "controversial", "statement", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O"]}
{"sentence": "Russian President Vladimir Putin meets with Chinese leader Xi Jinping.", "tokens": ["Russian", "President", "Vladimir", "Putin", "meets", "with", "Chinese", "leader", "Xi", "Jinping", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "B-person", "I-person", "O"]}
{"sentence": "Australian Open tennis tournament cancelled due to COVID-19.", "tokens": ["Australian", "Open", "tennis", "tournament", "cancelled", "due", "to", "COVID", "-", "19", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Bill Gates donates millions to combat malaria in Africa.", "tokens": ["Bill", "Gates", "donates", "millions", "to", "combat", "malaria", "in", "Africa", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Starbucks to introduce new environmentally friendly cup design.", "tokens": ["Starbucks", "to", "introduce", "new", "environmentally", "friendly", "cup", "design", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Harvard University announces new scholarship program.", "tokens": ["Harvard", "University", "announces", "new", "scholarship", "program", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "New Orleans prepares for hurricane season.", "tokens": ["New", "Orleans", "prepares", "for", "hurricane", "season", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O"]}
{"sentence": "South Korean K-pop group BTS breaks new record with latest album.", "tokens": ["South", "Korean", "K", "-", "pop", "group", "BTS", "breaks", "new", "record", "with", "latest", "album", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "London mayor announces plans for public transportation expansion.", "tokens": ["London", "mayor", "announces", "plans", "for", "public", "transportation", "expansion", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Canadian Prime Minister Justin Trudeau visits Indigenous communities.", "tokens": ["Canadian", "Prime", "Minister", "Justin", "Trudeau", "visits", "Indigenous", "communities", "."], "labels": ["B-location", "O", "O", "B-person", "I-person", "O", "B-organization", "O", "O"]}
{"sentence": "SpaceX to launch new satellite internet service.", "tokens": ["SpaceX", "to", "launch", "new", "satellite", "internet", "service", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "World Economic Forum held in Davos, Switzerland.", "tokens": ["World", "Economic", "Forum", "held", "in", "Davos", ",", "Switzerland", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "B-location", "O", "B-location", "O"]}
{"sentence": "Pope Francis visits refugee camp in Greece.", "tokens": ["Pope", "Francis", "visits", "refugee", "camp", "in", "Greece", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "BMW recalls thousands of vehicles for safety issues.", "tokens": ["BMW", "recalls", "thousands", "of", "vehicles", "for", "safety", "issues", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New study reveals alarming decline in bee populations.", "tokens": ["New", "study", "reveals", "alarming", "decline", "in", "bee", "populations", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Indian government announces new COVID-19 vaccine distribution plan.", "tokens": ["Indian", "government", "announces", "new", "COVID", "-", "19", "vaccine", "distribution", "plan", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Microsoft acquires gaming company for $7 billion.", "tokens": ["Microsoft", "acquires", "gaming", "company", "for", "$", "7", "billion", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Sydney Opera House reopens to the public.", "tokens": ["Sydney", "Opera", "House", "reopens", "to", "the", "public", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O"]}
{"sentence": "Indonesian president calls for increased cooperation on climate change.", "tokens": ["Indonesian", "president", "calls", "for", "increased", "cooperation", "on", "climate", "change", "."], "labels": ["O", "B-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Starbucks to close hundreds of stores in the United States.", "tokens": ["Starbucks", "to", "close", "hundreds", "of", "stores", "in", "the", "United", "States", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "Nigerian author wins Nobel Prize for literature.", "tokens": ["Nigerian", "author", "wins", "Nobel", "Prize", "for", "literature", "."], "labels": ["B-location", "O", "O", "B-organization", "I-organization", "O", "O", "O"]}
{"sentence": "New York Mets sign top free agent pitcher.", "tokens": ["New", "York", "Mets", "sign", "top", "free", "agent", "pitcher", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Google's parent company Alphabet announces quarterly earnings.", "tokens": ["Google", "'", "s", "parent", "company", "Alphabet", "announces", "quarterly", "earnings", "."], "labels": ["B-organization", "O", "O", "O", "O", "B-organization", "O", "O", "O", "O"]}
{"sentence": "The United States reported a record number of daily COVID-19 cases.", "tokens": ["The", "United", "States", "reported", "a", "record", "number", "of", "daily", "COVID", "-", "19", "cases", "."], "labels": ["O", "B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple Inc. announced a new line of products at their annual conference.", "tokens": ["Apple", "Inc", ".", "announced", "a", "new", "line", "of", "products", "at", "their", "annual", "conference", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Actress Emma Watson is rumored to be dating a tech entrepreneur.", "tokens": ["Actress", "Emma", "Watson", "is", "rumored", "to", "be", "dating", "a", "tech", "entrepreneur", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The European Union is considering new sanctions against Russia.", "tokens": ["The", "European", "Union", "is", "considering", "new", "sanctions", "against", "Russia", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Tesla CEO Elon Musk made headlines with his latest controversial tweet.", "tokens": ["Tesla", "CEO", "Elon", "Musk", "made", "headlines", "with", "his", "latest", "controversial", "tweet", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Japan will host the 2021 Olympics despite concerns over the COVID-19 pandemic.", "tokens": ["Japan", "will", "host", "the", "2021", "Olympics", "despite", "concerns", "over", "the", "COVID", "-", "19", "pandemic", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Health Organization issued a warning about a new strain of the flu virus.", "tokens": ["The", "World", "Health", "Organization", "issued", "a", "warning", "about", "a", "new", "strain", "of", "the", "flu", "virus", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "President Joe Biden signed a new executive order aimed at addressing climate change.", "tokens": ["President", "Joe", "Biden", "signed", "a", "new", "executive", "order", "aimed", "at", "addressing", "climate", "change", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Pop star Taylor Swift released a new album, causing a frenzy among fans.", "tokens": ["Pop", "star", "Taylor", "Swift", "released", "a", "new", "album", ",", "causing", "a", "frenzy", "among", "fans", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations is urging for immediate humanitarian aid to be sent to the war-torn region.", "tokens": ["The", "United", "Nations", "is", "urging", "for", "immediate", "humanitarian", "aid", "to", "be", "sent", "to", "the", "war", "-", "torn", "region", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Microsoft announced a major security breach that affected millions of users.", "tokens": ["Microsoft", "announced", "a", "major", "security", "breach", "that", "affected", "millions", "of", "users", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "South Africa is experiencing a severe drought, leading to food shortages.", "tokens": ["South", "Africa", "is", "experiencing", "a", "severe", "drought", ",", "leading", "to", "food", "shortages", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Olympic swimmer Michael Phelps broke another world record in the 100m butterfly event.", "tokens": ["Olympic", "swimmer", "Michael", "Phelps", "broke", "another", "world", "record", "in", "the", "100m", "butterfly", "event", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook is facing criticism for its handling of misinformation on the platform.", "tokens": ["Facebook", "is", "facing", "criticism", "for", "its", "handling", "of", "misinformation", "on", "the", "platform", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Amazon rainforest is at risk of widespread deforestation due to illegal logging.", "tokens": ["The", "Amazon", "rainforest", "is", "at", "risk", "of", "widespread", "deforestation", "due", "to", "illegal", "logging", "."], "labels": ["O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United States imposes new sanctions on Russia.", "tokens": ["The", "United", "States", "imposes", "new", "sanctions", "on", "Russia", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "CEO of Apple, Tim Cook, announces new product launch.", "tokens": ["CEO", "of", "Apple", ",", "Tim", "Cook", ",", "announces", "new", "product", "launch", "."], "labels": ["O", "O", "B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "China surpasses the United States in renewable energy production.", "tokens": ["China", "surpasses", "the", "United", "States", "in", "renewable", "energy", "production", "."], "labels": ["B-location", "O", "O", "B-location", "I-location", "O", "O", "O", "O", "O"]}
{"sentence": "Famous actress Angelina Jolie visits refugee camp in Syria.", "tokens": ["Famous", "actress", "Angelina", "Jolie", "visits", "refugee", "camp", "in", "Syria", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "The European Union issues a statement condemning human rights abuses in Myanmar.", "tokens": ["The", "European", "Union", "issues", "a", "statement", "condemning", "human", "rights", "abuses", "in", "Myanmar", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Scientist Stephen Hawking's research on black holes creates a breakthrough in astrophysics.", "tokens": ["Scientist", "Stephen", "Hawking", "'", "s", "research", "on", "black", "holes", "creates", "a", "breakthrough", "in", "astrophysics", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Indian Prime Minister Narendra Modi proposes new trade agreement with Japan.", "tokens": ["Indian", "Prime", "Minister", "Narendra", "Modi", "proposes", "new", "trade", "agreement", "with", "Japan", "."], "labels": ["O", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Germany experiences record-breaking heatwave.", "tokens": ["Germany", "experiences", "record", "-", "breaking", "heatwave", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "A new study by the World Health Organization reveals the impact of air pollution on public health.", "tokens": ["A", "new", "study", "by", "the", "World", "Health", "Organization", "reveals", "the", "impact", "of", "air", "pollution", "on", "public", "health", "."], "labels": ["O", "O", "O", "O", "O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations launches investigation into war crimes in Yemen.", "tokens": ["The", "United", "Nations", "launches", "investigation", "into", "war", "crimes", "in", "Yemen", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Amazon CEO Jeff Bezos becomes the world's richest person.", "tokens": ["Amazon", "CEO", "Jeff", "Bezos", "becomes", "the", "world", "'", "s", "richest", "person", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "South Africa undergoes political turmoil as corruption scandal unfolds.", "tokens": ["South", "Africa", "undergoes", "political", "turmoil", "as", "corruption", "scandal", "unfolds", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The G7 summit ends in a stalemate over trade negotiations.", "tokens": ["The", "G7", "summit", "ends", "in", "a", "stalemate", "over", "trade", "negotiations", "."], "labels": ["O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "British author J.K. Rowling announces new book release.", "tokens": ["British", "author", "J", ".", "K", ".", "Rowling", "announces", "new", "book", "release", "."], "labels": ["B-location", "O", "B-person", "I-person", "I-person", "I-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "The World Bank commits $1 billion in aid to developing countries.", "tokens": ["The", "World", "Bank", "commits", "$", "1", "billion", "in", "aid", "to", "developing", "countries", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "French President Emmanuel Macron proposes new climate change legislation.", "tokens": ["French", "President", "Emmanuel", "Macron", "proposes", "new", "climate", "change", "legislation", "."], "labels": ["B-location", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Irish rock band U2 announces farewell tour.", "tokens": ["Irish", "rock", "band", "U2", "announces", "farewell", "tour", "."], "labels": ["B-location", "O", "O", "B-organization", "O", "O", "O", "O"]}
{"sentence": "The International Monetary Fund warns of global economic slowdown.", "tokens": ["The", "International", "Monetary", "Fund", "warns", "of", "global", "economic", "slowdown", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Canadian singer Celine Dion cancels world tour due to health issues.", "tokens": ["Canadian", "singer", "Celine", "Dion", "cancels", "world", "tour", "due", "to", "health", "issues", "."], "labels": ["B-location", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Central Intelligence Agency unveils new counter-terrorism initiatives.", "tokens": ["The", "Central", "Intelligence", "Agency", "unveils", "new", "counter", "-", "terrorism", "initiatives", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Italian fashion house Gucci releases new designer collection.", "tokens": ["Italian", "fashion", "house", "Gucci", "releases", "new", "designer", "collection", "."], "labels": ["B-location", "O", "O", "B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "The International Olympic Committee announces new host city for the 2032 Summer Olympics.", "tokens": ["The", "International", "Olympic", "Committee", "announces", "new", "host", "city", "for", "the", "2032", "Summer", "Olympics", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Swiss tennis player Roger Federer wins Wimbledon championship.", "tokens": ["Swiss", "tennis", "player", "Roger", "Federer", "wins", "Wimbledon", "championship", "."], "labels": ["B-location", "O", "O", "B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "The World Trade Organization reports record-breaking trade deficit.", "tokens": ["The", "World", "Trade", "Organization", "reports", "record", "-", "breaking", "trade", "deficit", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Brazilian President Jair Bolsonaro faces criticism over environmental policies.", "tokens": ["Brazilian", "President", "Jair", "Bolsonaro", "faces", "criticism", "over", "environmental", "policies", "."], "labels": ["B-location", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations Children's Fund provides aid to refugee children in Syria.", "tokens": ["The", "United", "Nations", "Children", "'", "s", "Fund", "provides", "aid", "to", "refugee", "children", "in", "Syria", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "American tech giant Microsoft reveals new cybersecurity strategy.", "tokens": ["American", "tech", "giant", "Microsoft", "reveals", "new", "cybersecurity", "strategy", "."], "labels": ["B-location", "O", "O", "B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Japanese automaker Toyota announces recall of over 1 million vehicles.", "tokens": ["Japanese", "automaker", "Toyota", "announces", "recall", "of", "over", "1", "million", "vehicles", "."], "labels": ["B-location", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Economic Forum publishes global competitiveness report.", "tokens": ["The", "World", "Economic", "Forum", "publishes", "global", "competitiveness", "report", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "British Prime Minister Boris Johnson faces criticism over handling of COVID-19 pandemic.", "tokens": ["British", "Prime", "Minister", "Boris", "Johnson", "faces", "criticism", "over", "handling", "of", "COVID", "-", "19", "pandemic", "."], "labels": ["B-location", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The European Space Agency launches new Mars rover mission.", "tokens": ["The", "European", "Space", "Agency", "launches", "new", "Mars", "rover", "mission", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "B-location", "O", "O", "O"]}
{"sentence": "Chinese e-commerce company Alibaba reports significant profit growth.", "tokens": ["Chinese", "e", "-", "commerce", "company", "Alibaba", "reports", "significant", "profit", "growth", "."], "labels": ["B-location", "O", "O", "O", "O", "B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "The International Criminal Court issues arrest warrant for war crimes suspect in Sudan.", "tokens": ["The", "International", "Criminal", "Court", "issues", "arrest", "warrant", "for", "war", "crimes", "suspect", "in", "Sudan", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "American basketball player LeBron James signs record-breaking contract with Los Angeles Lakers.", "tokens": ["American", "basketball", "player", "LeBron", "James", "signs", "record", "-", "breaking", "contract", "with", "Los", "Angeles", "Lakers", "."], "labels": ["B-location", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "B-organization", "I-organization", "I-organization", "O"]}
{"sentence": "Indian pharmaceutical company Cipla receives approval for new drug in the US.", "tokens": ["Indian", "pharmaceutical", "company", "Cipla", "receives", "approval", "for", "new", "drug", "in", "the", "US", "."], "labels": ["B-location", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "The Economic Community of West African States imposes sanctions on Mali.", "tokens": ["The", "Economic", "Community", "of", "West", "African", "States", "imposes", "sanctions", "on", "Mali", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "B-location", "O"]}
{"sentence": "Russian President Vladimir Putin holds summit with North Korean leader Kim Jong Un.", "tokens": ["Russian", "President", "Vladimir", "Putin", "holds", "summit", "with", "North", "Korean", "leader", "Kim", "Jong", "Un", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "B-location", "I-location", "O", "B-person", "I-person", "I-person", "O"]}
{"sentence": "Microsoft co-founder Bill Gates donates $1 billion to global health initiatives.", "tokens": ["Microsoft", "co", "-", "founder", "Bill", "Gates", "donates", "$", "1", "billion", "to", "global", "health", "initiatives", "."], "labels": ["B-organization", "O", "O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The International Labor Organization advocates for workers' rights in the gig economy.", "tokens": ["The", "International", "Labor", "Organization", "advocates", "for", "workers", "'", "rights", "in", "the", "gig", "economy", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "American chef Gordon Ramsay opens new restaurant in London.", "tokens": ["American", "chef", "Gordon", "Ramsay", "opens", "new", "restaurant", "in", "London", "."], "labels": ["B-location", "O", "B-person", "I-person", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "The World Food Programme delivers aid to famine-stricken regions in Ethiopia.", "tokens": ["The", "World", "Food", "Programme", "delivers", "aid", "to", "famine", "-", "stricken", "regions", "in", "Ethiopia", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "South Korean boy band BTS breaks streaming record with new album release.", "tokens": ["South", "Korean", "boy", "band", "BTS", "breaks", "streaming", "record", "with", "new", "album", "release", "."], "labels": ["O", "O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations High Commissioner for Refugees provides emergency assistance to Rohingya refugees in Bangladesh.", "tokens": ["The", "United", "Nations", "High", "Commissioner", "for", "Refugees", "provides", "emergency", "assistance", "to", "Rohingya", "refugees", "in", "Bangladesh", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "B-organization", "O", "O", "B-location", "O"]}
{"sentence": "Amazon founder Jeff Bezos launches space exploration company Blue Origin.", "tokens": ["Amazon", "founder", "Jeff", "Bezos", "launches", "space", "exploration", "company", "Blue", "Origin", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "B-organization", "I-organization", "O"]}
{"sentence": "German Chancellor Angela Merkel meets with French President Emmanuel Macron to discuss economic policies.", "tokens": ["German", "Chancellor", "Angela", "Merkel", "meets", "with", "French", "President", "Emmanuel", "Macron", "to", "discuss", "economic", "policies", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "B-location", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "The World Health Organization declares new strain of flu virus a global health emergency.", "tokens": ["The", "World", "Health", "Organization", "declares", "new", "strain", "of", "flu", "virus", "a", "global", "health", "emergency", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "American singer Taylor Swift donates $1 million to tornado relief efforts in Tennessee.", "tokens": ["American", "singer", "Taylor", "Swift", "donates", "$", "1", "million", "to", "tornado", "relief", "efforts", "in", "Tennessee", "."], "labels": ["B-location", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "The European Union Parliament votes against proposed trade agreement with China.", "tokens": ["The", "European", "Union", "Parliament", "votes", "against", "proposed", "trade", "agreement", "with", "China", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "New Study Shows Link Between Coffee and Longevity", "tokens": ["New", "Study", "Shows", "Link", "Between", "Coffee", "and", "Longevity"], "labels": ["O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple Announces Release of New iPhone Model", "tokens": ["Apple", "Announces", "Release", "of", "New", "iPhone", "Model"], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "President Biden Signs Executive Order on Climate Change", "tokens": ["President", "Biden", "Signs", "Executive", "Order", "on", "Climate", "Change"], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Wildfires Threaten Homes in California", "tokens": ["Wildfires", "Threaten", "Homes", "in", "California"], "labels": ["O", "O", "O", "O", "B-location"]}
{"sentence": "WHO Warns of Potential COVID-19 Surge in Europe", "tokens": ["WHO", "Warns", "of", "Potential", "COVID", "-", "19", "Surge", "in", "Europe"], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "B-location"]}
{"sentence": "Elon Musk's SpaceX Launches New Satellite into Orbit", "tokens": ["Elon", "Musk", "'", "s", "SpaceX", "Launches", "New", "Satellite", "into", "Orbit"], "labels": ["B-person", "I-person", "O", "O", "B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Kim Kardashian Releases New Skincare Line", "tokens": ["Kim", "Kardashian", "Releases", "New", "Skincare", "Line"], "labels": ["B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "Florida Governor Signs Controversial Voting Bill into Law", "tokens": ["Florida", "Governor", "Signs", "Controversial", "Voting", "Bill", "into", "Law"], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tokyo Olympics to Proceed Without Spectators", "tokens": ["Tokyo", "Olympics", "to", "Proceed", "Without", "Spectators"], "labels": ["B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Microsoft Reports Record Profits in Q2", "tokens": ["Microsoft", "Reports", "Record", "Profits", "in", "Q2"], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "New York City Prepares for Influx of Tourists", "tokens": ["New", "York", "City", "Prepares", "for", "Influx", "of", "Tourists"], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O"]}
{"sentence": "Study Finds Link Between Sleep and Mental Health", "tokens": ["Study", "Finds", "Link", "Between", "Sleep", "and", "Mental", "Health"], "labels": ["O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations issues statement on humanitarian crisis in Yemen", "tokens": ["United", "Nations", "issues", "statement", "on", "humanitarian", "crisis", "in", "Yemen"], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "B-location"]}
{"sentence": "Amazon Announces Plan to Build New Headquarters", "tokens": ["Amazon", "Announces", "Plan", "to", "Build", "New", "Headquarters"], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Biden Administration Announces Infrastructure Investment Plan", "tokens": ["Biden", "Administration", "Announces", "Infrastructure", "Investment", "Plan"], "labels": ["B-person", "O", "O", "O", "O", "O"]}
{"sentence": "Hurricane Florence Causes Devastation in North Carolina", "tokens": ["Hurricane", "Florence", "Causes", "Devastation", "in", "North", "Carolina"], "labels": ["B-organization", "I-organization", "O", "O", "O", "B-location", "I-location"]}
{"sentence": "French Open Champion Advances to Quarterfinals", "tokens": ["French", "Open", "Champion", "Advances", "to", "Quarterfinals"], "labels": ["B-organization", "I-organization", "O", "O", "O", "O"]}
{"sentence": "Google Faces Antitrust Lawsuit from EU", "tokens": ["Google", "Faces", "Antitrust", "Lawsuit", "from", "EU"], "labels": ["B-organization", "O", "O", "O", "O", "B-organization"]}
{"sentence": "Germany to Host G7 Summit Next Year", "tokens": ["Germany", "to", "Host", "G7", "Summit", "Next", "Year"], "labels": ["B-location", "O", "O", "B-organization", "O", "O", "O"]}
{"sentence": "CEO of Tesla Elon Musk Denies Securities Fraud Allegations", "tokens": ["CEO", "of", "Tesla", "Elon", "Musk", "Denies", "Securities", "Fraud", "Allegations"], "labels": ["B-organization", "O", "B-organization", "B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "Taylor Swift Announces Stadium Tour Dates", "tokens": ["Taylor", "Swift", "Announces", "Stadium", "Tour", "Dates"], "labels": ["B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "Political Unrest in Venezuela Escalates", "tokens": ["Political", "Unrest", "in", "Venezuela", "Escalates"], "labels": ["O", "O", "O", "B-location", "O"]}
{"sentence": "NASA Prepares to Launch New Mars Rover", "tokens": ["NASA", "Prepares", "to", "Launch", "New", "Mars", "Rover"], "labels": ["B-organization", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Starbucks to Open 200 New Stores in China", "tokens": ["Starbucks", "to", "Open", "200", "New", "Stores", "in", "China"], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "B-location"]}
{"sentence": "Canadian Prime Minister Justin Trudeau Announces New Housing Plan", "tokens": ["Canadian", "Prime", "Minister", "Justin", "Trudeau", "Announces", "New", "Housing", "Plan"], "labels": ["B-organization", "O", "O", "B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "Typhoon Hits Philippines, Leaving Thousands Displaced", "tokens": ["Typhoon", "Hits", "Philippines", ",", "Leaving", "Thousands", "Displaced"], "labels": ["O", "O", "B-location", "O", "O", "O", "O"]}
{"sentence": "Facebook Faces Scrutiny Over Data Privacy Issues", "tokens": ["Facebook", "Faces", "Scrutiny", "Over", "Data", "Privacy", "Issues"], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Actress Emma Stone Welcomes Baby Girl", "tokens": ["Actress", "Emma", "Stone", "Welcomes", "Baby", "Girl"], "labels": ["O", "B-person", "I-person", "O", "O", "O"]}
{"sentence": "World Bank Releases Report on Global Poverty Rates", "tokens": ["World", "Bank", "Releases", "Report", "on", "Global", "Poverty", "Rates"], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "5.8 Magnitude Earthquake Strikes Japan", "tokens": ["5", ".", "8", "Magnitude", "Earthquake", "Strikes", "Japan"], "labels": ["O", "O", "O", "O", "O", "O", "B-location"]}
{"sentence": "New Research Indicates Potential Breakthrough in Cancer Treatment", "tokens": ["New", "Research", "Indicates", "Potential", "Breakthrough", "in", "Cancer", "Treatment"], "labels": ["O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "AstraZeneca Announces Plans for COVID-19 Vaccine Distribution", "tokens": ["AstraZeneca", "Announces", "Plans", "for", "COVID", "-", "19", "Vaccine", "Distribution"], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Storm Causes Flooding in Australia's East Coast", "tokens": ["Storm", "Causes", "Flooding", "in", "Australia", "'", "s", "East", "Coast"], "labels": ["O", "O", "O", "O", "B-location", "O", "O", "O", "O"]}
{"sentence": "Major League Baseball Commissioner Addresses Player Strikes", "tokens": ["Major", "League", "Baseball", "Commissioner", "Addresses", "Player", "Strikes"], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O"]}
{"sentence": "Pop Star Rihanna Launches New Fashion Line", "tokens": ["Pop", "Star", "Rihanna", "Launches", "New", "Fashion", "Line"], "labels": ["O", "O", "B-person", "O", "O", "O", "O"]}
{"sentence": "World Health Organization Issues Warning on Antibiotic Resistance", "tokens": ["World", "Health", "Organization", "Issues", "Warning", "on", "Antibiotic", "Resistance"], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Texas Governor Signs Controversial Abortion Bill into Law", "tokens": ["Texas", "Governor", "Signs", "Controversial", "Abortion", "Bill", "into", "Law"], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Kentucky Derby Winner Tests Positive for Banned Substance", "tokens": ["Kentucky", "Derby", "Winner", "Tests", "Positive", "for", "Banned", "Substance"], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Jeff Bezos Steps Down as CEO of Amazon", "tokens": ["Jeff", "Bezos", "Steps", "Down", "as", "CEO", "of", "Amazon"], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "B-organization"]}
{"sentence": "European Union Approves New Climate Change Regulations", "tokens": ["European", "Union", "Approves", "New", "Climate", "Change", "Regulations"], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "South African President Cyril Ramaphosa Addresses Economic Challenges", "tokens": ["South", "African", "President", "Cyril", "Ramaphosa", "Addresses", "Economic", "Challenges"], "labels": ["B-organization", "I-organization", "O", "B-person", "I-person", "O", "O", "O"]}
{"sentence": "New Zealand to Implement Stricter Gun Control Measures", "tokens": ["New", "Zealand", "to", "Implement", "Stricter", "Gun", "Control", "Measures"], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "McDonald's Announces Plant-Based Burger Option", "tokens": ["McDonald", "'", "s", "Announces", "Plant", "-", "Based", "Burger", "Option"], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Nigerian President Muhammadu Buhari Meets with U.S. Secretary of State", "tokens": ["Nigerian", "President", "Muhammadu", "Buhari", "Meets", "with", "U", ".", "S", ".", "Secretary", "of", "State"], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "B-location", "I-location", "I-location", "I-location", "O", "O", "O"]}
{"sentence": "Taylor Swift Wins Album of the Year at Grammys", "tokens": ["Taylor", "Swift", "Wins", "Album", "of", "the", "Year", "at", "Grammys"], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "B-organization"]}
{"sentence": "FBI Investigates Cyberattack on Major U.S. Corporation", "tokens": ["FBI", "Investigates", "Cyberattack", "on", "Major", "U", ".", "S", ".", "Corporation"], "labels": ["B-organization", "O", "O", "O", "O", "B-location", "I-location", "I-location", "I-location", "O"]}
{"sentence": "Italy Faces Political Turmoil Amidst Government Resignations", "tokens": ["Italy", "Faces", "Political", "Turmoil", "Amidst", "Government", "Resignations"], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Zara Unveils Sustainable Fashion Collection", "tokens": ["Zara", "Unveils", "Sustainable", "Fashion", "Collection"], "labels": ["B-organization", "O", "O", "O", "O"]}
{"sentence": "Microsoft CEO Satya Nadella Discusses Future of Technology at Summit", "tokens": ["Microsoft", "CEO", "Satya", "Nadella", "Discusses", "Future", "of", "Technology", "at", "Summit"], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple launches new iPhone 12.", "tokens": ["Apple", "launches", "new", "iPhone", "12", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Tokyo Olympics postponed until 2021 due to COVID-19.", "tokens": ["Tokyo", "Olympics", "postponed", "until", "2021", "due", "to", "COVID", "-", "19", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon announces plans to open new headquarters in Texas.", "tokens": ["Amazon", "announces", "plans", "to", "open", "new", "headquarters", "in", "Texas", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Elon Musk's SpaceX successfully launches new satellite into space.", "tokens": ["Elon", "Musk", "'", "s", "SpaceX", "successfully", "launches", "new", "satellite", "into", "space", "."], "labels": ["B-person", "I-person", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "California wildfires continue to threaten homes.", "tokens": ["California", "wildfires", "continue", "to", "threaten", "homes", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Pfizer and Moderna announce progress on COVID-19 vaccine.", "tokens": ["Pfizer", "and", "Moderna", "announce", "progress", "on", "COVID", "-", "19", "vaccine", "."], "labels": ["B-organization", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York City to invest billions in renewable energy projects.", "tokens": ["New", "York", "City", "to", "invest", "billions", "in", "renewable", "energy", "projects", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Simone Biles wins gold at gymnastics world championships.", "tokens": ["Simone", "Biles", "wins", "gold", "at", "gymnastics", "world", "championships", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tesla to build new gigafactory in Germany.", "tokens": ["Tesla", "to", "build", "new", "gigafactory", "in", "Germany", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "South Africa to implement new COVID-19 restrictions.", "tokens": ["South", "Africa", "to", "implement", "new", "COVID", "-", "19", "restrictions", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations has declared a humanitarian crisis in the war-torn country.", "tokens": ["The", "United", "Nations", "has", "declared", "a", "humanitarian", "crisis", "in", "the", "war", "-", "torn", "country", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The CEO of Google, Sundar Pichai, announced their plans to invest in renewable energy.", "tokens": ["The", "CEO", "of", "Google", ",", "Sundar", "Pichai", ",", "announced", "their", "plans", "to", "invest", "in", "renewable", "energy", "."], "labels": ["O", "O", "O", "B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The annual meeting of the World Health Organization will take place in Geneva next month.", "tokens": ["The", "annual", "meeting", "of", "the", "World", "Health", "Organization", "will", "take", "place", "in", "Geneva", "next", "month", "."], "labels": ["O", "O", "O", "O", "O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "B-location", "O", "O", "O"]}
{"sentence": "The Prime Minister of Canada, Justin Trudeau, unveiled a new economic stimulus package.", "tokens": ["The", "Prime", "Minister", "of", "Canada", ",", "Justin", "Trudeau", ",", "unveiled", "a", "new", "economic", "stimulus", "package", "."], "labels": ["O", "O", "O", "O", "B-location", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United States experienced a record-breaking heatwave last week, with temperatures reaching over 100 degrees in some areas.", "tokens": ["The", "United", "States", "experienced", "a", "record", "-", "breaking", "heatwave", "last", "week", ",", "with", "temperatures", "reaching", "over", "100", "degrees", "in", "some", "areas", "."], "labels": ["O", "B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The European Union is implementing new regulations to improve data privacy for its citizens.", "tokens": ["The", "European", "Union", "is", "implementing", "new", "regulations", "to", "improve", "data", "privacy", "for", "its", "citizens", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Mayor of London, Sadiq Khan, announced plans to expand the city's public transportation system.", "tokens": ["The", "Mayor", "of", "London", ",", "Sadiq", "Khan", ",", "announced", "plans", "to", "expand", "the", "city", "'", "s", "public", "transportation", "system", "."], "labels": ["O", "O", "O", "B-location", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "NASA is preparing for a historic mission to Mars, with plans to send humans to the red planet by 2030.", "tokens": ["NASA", "is", "preparing", "for", "a", "historic", "mission", "to", "Mars", ",", "with", "plans", "to", "send", "humans", "to", "the", "red", "planet", "by", "2030", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The CEO of Microsoft, Satya Nadella, revealed the company's plans for expansion into the healthcare industry.", "tokens": ["The", "CEO", "of", "Microsoft", ",", "Satya", "Nadella", ",", "revealed", "the", "company", "'", "s", "plans", "for", "expansion", "into", "the", "healthcare", "industry", "."], "labels": ["O", "O", "O", "B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations Climate Change Conference will be held in Glasgow later this year.", "tokens": ["The", "United", "Nations", "Climate", "Change", "Conference", "will", "be", "held", "in", "Glasgow", "later", "this", "year", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O", "O", "O", "O"]}
{"sentence": "President Biden signs executive order to boost cybersecurity.", "tokens": ["President", "Biden", "signs", "executive", "order", "to", "boost", "cybersecurity", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York City announces plan to reopen all schools in September.", "tokens": ["New", "York", "City", "announces", "plan", "to", "reopen", "all", "schools", "in", "September", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple unveils new iPhone with upgraded camera and battery life.", "tokens": ["Apple", "unveils", "new", "iPhone", "with", "upgraded", "camera", "and", "battery", "life", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Scientists discover new species of butterfly in the Amazon rainforest.", "tokens": ["Scientists", "discover", "new", "species", "of", "butterfly", "in", "the", "Amazon", "rainforest", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O", "O"]}
{"sentence": "Tesla CEO Elon Musk becomes the world's richest person.", "tokens": ["Tesla", "CEO", "Elon", "Musk", "becomes", "the", "world", "'", "s", "richest", "person", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations reports record-breaking temperatures in Antarctica.", "tokens": ["United", "Nations", "reports", "record", "-", "breaking", "temperatures", "in", "Antarctica", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Former President Obama to release memoir this fall.", "tokens": ["Former", "President", "Obama", "to", "release", "memoir", "this", "fall", "."], "labels": ["O", "O", "B-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Hurricane warning issued for coastal areas of Florida.", "tokens": ["Hurricane", "warning", "issued", "for", "coastal", "areas", "of", "Florida", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Google to invest $1 billion in renewable energy projects.", "tokens": ["Google", "to", "invest", "$", "1", "billion", "in", "renewable", "energy", "projects", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Legendary musician Bob Dylan announces new album.", "tokens": ["Legendary", "musician", "Bob", "Dylan", "announces", "new", "album", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "The U.S. House of Representatives passed the bill with a vote of 220-212.", "tokens": ["The", "U", ".", "S", ".", "House", "of", "Representatives", "passed", "the", "bill", "with", "a", "vote", "of", "220", "-", "212", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "I-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "President Biden issued a statement on the ongoing conflict in the Middle East.", "tokens": ["President", "Biden", "issued", "a", "statement", "on", "the", "ongoing", "conflict", "in", "the", "Middle", "East", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "The World Health Organization declared the Ebola outbreak a public health emergency.", "tokens": ["The", "World", "Health", "Organization", "declared", "the", "Ebola", "outbreak", "a", "public", "health", "emergency", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Several residents in New York City reported sightings of a large black bear in the area.", "tokens": ["Several", "residents", "in", "New", "York", "City", "reported", "sightings", "of", "a", "large", "black", "bear", "in", "the", "area", "."], "labels": ["O", "O", "O", "B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Elon Musk's SpaceX successfully launched another batch of Starlink satellites.", "tokens": ["Elon", "Musk", "'", "s", "SpaceX", "successfully", "launched", "another", "batch", "of", "Starlink", "satellites", "."], "labels": ["B-person", "I-person", "O", "O", "B-organization", "O", "O", "O", "O", "O", "B-organization", "O", "O"]}
{"sentence": "The European Union imposed sanctions on Belarus for human rights abuses.", "tokens": ["The", "European", "Union", "imposed", "sanctions", "on", "Belarus", "for", "human", "rights", "abuses", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "The singer Taylor Swift released a new album, gaining widespread acclaim from fans.", "tokens": ["The", "singer", "Taylor", "Swift", "released", "a", "new", "album", ",", "gaining", "widespread", "acclaim", "from", "fans", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "California Governor Gavin Newsom signed a bill to address climate change in the state.", "tokens": ["California", "Governor", "Gavin", "Newsom", "signed", "a", "bill", "to", "address", "climate", "change", "in", "the", "state", "."], "labels": ["B-location", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations announced a new initiative to combat global poverty and hunger.", "tokens": ["The", "United", "Nations", "announced", "a", "new", "initiative", "to", "combat", "global", "poverty", "and", "hunger", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Thousands of protesters gathered in Hong Kong to demand democratic reforms.", "tokens": ["Thousands", "of", "protesters", "gathered", "in", "Hong", "Kong", "to", "demand", "democratic", "reforms", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "I-location", "O", "O", "O", "O", "O"]}
{"sentence": "Elon Musk reveals plans for a manned mission to Mars.", "tokens": ["Elon", "Musk", "reveals", "plans", "for", "a", "manned", "mission", "to", "Mars", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Facebook faces backlash over data privacy concerns.", "tokens": ["Facebook", "faces", "backlash", "over", "data", "privacy", "concerns", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "President Biden announces new infrastructure proposal.", "tokens": ["President", "Biden", "announces", "new", "infrastructure", "proposal", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Tokyo Olympics set to begin next week.", "tokens": ["Tokyo", "Olympics", "set", "to", "begin", "next", "week", "."], "labels": ["B-location", "B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Researchers discover new species of deep-sea jellyfish.", "tokens": ["Researchers", "discover", "new", "species", "of", "deep", "-", "sea", "jellyfish", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Europe experiences record-breaking heatwave.", "tokens": ["Europe", "experiences", "record", "-", "breaking", "heatwave", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Stock market hits record high as tech companies soar.", "tokens": ["Stock", "market", "hits", "record", "high", "as", "tech", "companies", "soar", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York City subway system faces budget crisis.", "tokens": ["New", "York", "City", "subway", "system", "faces", "budget", "crisis", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations condemns human rights abuses in Myanmar.", "tokens": ["The", "United", "Nations", "condemns", "human", "rights", "abuses", "in", "Myanmar", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Apple unveils latest iPhone model.", "tokens": ["Apple", "unveils", "latest", "iPhone", "model", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "China surpasses United States in renewable energy production.", "tokens": ["China", "surpasses", "United", "States", "in", "renewable", "energy", "production", "."], "labels": ["B-location", "O", "B-location", "I-location", "O", "O", "O", "O", "O"]}
{"sentence": "Prime Minister Modi meets with Russian President Putin.", "tokens": ["Prime", "Minister", "Modi", "meets", "with", "Russian", "President", "Putin", "."], "labels": ["B-person", "I-person", "I-person", "O", "O", "B-person", "I-person", "I-person", "O"]}
{"sentence": "Pfizer announces new vaccine efficacy study results.", "tokens": ["Pfizer", "announces", "new", "vaccine", "efficacy", "study", "results", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Hurricane Florence devastates coastal communities.", "tokens": ["Hurricane", "Florence", "devastates", "coastal", "communities", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O"]}
{"sentence": "Tesla reports record profits for the quarter.", "tokens": ["Tesla", "reports", "record", "profits", "for", "the", "quarter", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Google faces antitrust probe from European Union.", "tokens": ["Google", "faces", "antitrust", "probe", "from", "European", "Union", "."], "labels": ["B-organization", "O", "O", "O", "O", "B-organization", "I-organization", "O"]}
{"sentence": "CEO of Amazon steps down from role.", "tokens": ["CEO", "of", "Amazon", "steps", "down", "from", "role", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Massive wildfire threatens California town.", "tokens": ["Massive", "wildfire", "threatens", "California", "town", "."], "labels": ["O", "O", "O", "B-location", "O", "O"]}
{"sentence": "The United States announces new sanctions on Russia.", "tokens": ["The", "United", "States", "announces", "new", "sanctions", "on", "Russia", "."], "labels": ["O", "B-location", "I-location", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "CEO of Apple Inc. steps down from his position.", "tokens": ["CEO", "of", "Apple", "Inc", ".", "steps", "down", "from", "his", "position", "."], "labels": ["O", "O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Global warming continues to be a concern for scientists.", "tokens": ["Global", "warming", "continues", "to", "be", "a", "concern", "for", "scientists", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "President of France visits Germany for bilateral talks.", "tokens": ["President", "of", "France", "visits", "Germany", "for", "bilateral", "talks", "."], "labels": ["O", "O", "B-location", "O", "B-location", "O", "O", "O", "O"]}
{"sentence": "New research shows that coffee consumption may have health benefits.", "tokens": ["New", "research", "shows", "that", "coffee", "consumption", "may", "have", "health", "benefits", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Severe flooding reported in several areas of India.", "tokens": ["Severe", "flooding", "reported", "in", "several", "areas", "of", "India", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Tesla announces plans to build new factory in Texas.", "tokens": ["Tesla", "announces", "plans", "to", "build", "new", "factory", "in", "Texas", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Hollywood actress wins Oscar for Best Actress.", "tokens": ["Hollywood", "actress", "wins", "Oscar", "for", "Best", "Actress", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations calls for immediate ceasefire in war-torn region.", "tokens": ["United", "Nations", "calls", "for", "immediate", "ceasefire", "in", "war", "-", "torn", "region", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The president of the United States issued a statement regarding the recent weather disasters.", "tokens": ["The", "president", "of", "the", "United", "States", "issued", "a", "statement", "regarding", "the", "recent", "weather", "disasters", "."], "labels": ["O", "O", "O", "O", "B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Sales of the new iPhone model exceeded expectations in the first quarter.", "tokens": ["Sales", "of", "the", "new", "iPhone", "model", "exceeded", "expectations", "in", "the", "first", "quarter", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The CEO of Apple Inc. announced a new sustainability initiative.", "tokens": ["The", "CEO", "of", "Apple", "Inc", ".", "announced", "a", "new", "sustainability", "initiative", "."], "labels": ["O", "O", "O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tensions continue to rise between North Korea and South Korea.", "tokens": ["Tensions", "continue", "to", "rise", "between", "North", "Korea", "and", "South", "Korea", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "I-location", "O", "B-location", "I-location", "O"]}
{"sentence": "Taylor Swift wins Grammy for Album of the Year.", "tokens": ["Taylor", "Swift", "wins", "Grammy", "for", "Album", "of", "the", "Year", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations declared a humanitarian crisis in the war-torn region.", "tokens": ["The", "United", "Nations", "declared", "a", "humanitarian", "crisis", "in", "the", "war", "-", "torn", "region", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Thousands gather in Times Square to celebrate the New Year.", "tokens": ["Thousands", "gather", "in", "Times", "Square", "to", "celebrate", "the", "New", "Year", "."], "labels": ["O", "O", "O", "B-location", "I-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Prime Minister of Canada addressed the nation about the new policies.", "tokens": ["The", "Prime", "Minister", "of", "Canada", "addressed", "the", "nation", "about", "the", "new", "policies", "."], "labels": ["O", "O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tesla Motors unveils plans for a new electric vehicle.", "tokens": ["Tesla", "Motors", "unveils", "plans", "for", "a", "new", "electric", "vehicle", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The famous chef opened a new restaurant in downtown Manhattan.", "tokens": ["The", "famous", "chef", "opened", "a", "new", "restaurant", "in", "downtown", "Manhattan", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "China launches new satellite into orbit.", "tokens": ["China", "launches", "new", "satellite", "into", "orbit", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The outbreak of a new virus threatens public health.", "tokens": ["The", "outbreak", "of", "a", "new", "virus", "threatens", "public", "health", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon.com announces record-breaking sales during the holiday season.", "tokens": ["Amazon", ".", "com", "announces", "record", "-", "breaking", "sales", "during", "the", "holiday", "season", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The European Union criticizes the new trade agreement.", "tokens": ["The", "European", "Union", "criticizes", "the", "new", "trade", "agreement", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Duchess of Cambridge visits a children's hospital in London.", "tokens": ["The", "Duchess", "of", "Cambridge", "visits", "a", "children", "'", "s", "hospital", "in", "London", "."], "labels": ["O", "B-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "CEO of Microsoft announces new strategic partnership.", "tokens": ["CEO", "of", "Microsoft", "announces", "new", "strategic", "partnership", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations condemns the recent human rights violations in Syria.", "tokens": ["The", "United", "Nations", "condemns", "the", "recent", "human", "rights", "violations", "in", "Syria", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Famous actor Brad Pitt is set to star in a new blockbuster film.", "tokens": ["Famous", "actor", "Brad", "Pitt", "is", "set", "to", "star", "in", "a", "new", "blockbuster", "film", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Health Organization reports an increase in cases of malaria in Africa.", "tokens": ["The", "World", "Health", "Organization", "reports", "an", "increase", "in", "cases", "of", "malaria", "in", "Africa", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "President of France announces new environmental initiatives.", "tokens": ["President", "of", "France", "announces", "new", "environmental", "initiatives", "."], "labels": ["O", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Thousands gather in protest against government corruption in Brazil.", "tokens": ["Thousands", "gather", "in", "protest", "against", "government", "corruption", "in", "Brazil", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "New study shows the impact of climate change on polar bear populations.", "tokens": ["New", "study", "shows", "the", "impact", "of", "climate", "change", "on", "polar", "bear", "populations", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The European Union imposes sanctions on Belarus for human rights abuses.", "tokens": ["The", "European", "Union", "imposes", "sanctions", "on", "Belarus", "for", "human", "rights", "abuses", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Apple Inc. reveals plans for a new line of products.", "tokens": ["Apple", "Inc", ".", "reveals", "plans", "for", "a", "new", "line", "of", "products", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple launches new iPhone model.", "tokens": ["Apple", "launches", "new", "iPhone", "model", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Tokyo experiences record-breaking heatwave.", "tokens": ["Tokyo", "experiences", "record", "-", "breaking", "heatwave", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Famous singer Taylor Swift announces new album release.", "tokens": ["Famous", "singer", "Taylor", "Swift", "announces", "new", "album", "release", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations passes resolution on climate change.", "tokens": ["United", "Nations", "passes", "resolution", "on", "climate", "change", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Los Angeles Dodgers win World Series.", "tokens": ["Los", "Angeles", "Dodgers", "win", "World", "Series", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O"]}
{"sentence": "President of the United States signs new trade agreement.", "tokens": ["President", "of", "the", "United", "States", "signs", "new", "trade", "agreement", "."], "labels": ["B-person", "I-person", "I-person", "I-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "London sees surge in COVID-19 cases.", "tokens": ["London", "sees", "surge", "in", "COVID", "-", "19", "cases", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon hires 100,000 new employees.", "tokens": ["Amazon", "hires", "100", ",", "000", "new", "employees", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Cristiano Ronaldo signs record-breaking contract with new team.", "tokens": ["Cristiano", "Ronaldo", "signs", "record", "-", "breaking", "contract", "with", "new", "team", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Elon Musk's SpaceX successfully launches 60 Starlink satellites into orbit.", "tokens": ["Elon", "Musk", "'", "s", "SpaceX", "successfully", "launches", "60", "Starlink", "satellites", "into", "orbit", "."], "labels": ["B-person", "I-person", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York City Mayor announces plan to improve public transportation system.", "tokens": ["New", "York", "City", "Mayor", "announces", "plan", "to", "improve", "public", "transportation", "system", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple Inc. unveils new iPhone with groundbreaking features.", "tokens": ["Apple", "Inc", ".", "unveils", "new", "iPhone", "with", "groundbreaking", "features", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Famous actor faces backlash for controversial social media post.", "tokens": ["Famous", "actor", "faces", "backlash", "for", "controversial", "social", "media", "post", "."], "labels": ["O", "B-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "California experiences record-breaking heatwave.", "tokens": ["California", "experiences", "record", "-", "breaking", "heatwave", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations report highlights worsening global hunger crisis.", "tokens": ["United", "Nations", "report", "highlights", "worsening", "global", "hunger", "crisis", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Miami Beach imposes new restrictions to control spring break crowds.", "tokens": ["Miami", "Beach", "imposes", "new", "restrictions", "to", "control", "spring", "break", "crowds", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "World Health Organization declares Ebola outbreak a global health emergency.", "tokens": ["World", "Health", "Organization", "declares", "Ebola", "outbreak", "a", "global", "health", "emergency", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The CEO of Apple Inc. announced a new product launch.", "tokens": ["The", "CEO", "of", "Apple", "Inc", ".", "announced", "a", "new", "product", "launch", "."], "labels": ["O", "O", "O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The mayor of New York City revealed plans for a new subway system.", "tokens": ["The", "mayor", "of", "New", "York", "City", "revealed", "plans", "for", "a", "new", "subway", "system", "."], "labels": ["O", "O", "O", "B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations released a report on climate change.", "tokens": ["The", "United", "Nations", "released", "a", "report", "on", "climate", "change", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "A local charity organization raised $1 million for a new shelter.", "tokens": ["A", "local", "charity", "organization", "raised", "$", "1", "million", "for", "a", "new", "shelter", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The prime minister of Japan visited the United States for trade talks.", "tokens": ["The", "prime", "minister", "of", "Japan", "visited", "the", "United", "States", "for", "trade", "talks", "."], "labels": ["O", "O", "O", "O", "B-location", "O", "O", "B-location", "I-location", "O", "O", "O", "O"]}
{"sentence": "Google's parent company, Alphabet Inc., reported record profits.", "tokens": ["Google", "'", "s", "parent", "company", ",", "Alphabet", "Inc", ".", ",", "reported", "record", "profits", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "The earthquake in California caused widespread damage.", "tokens": ["The", "earthquake", "in", "California", "caused", "widespread", "damage", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O"]}
{"sentence": "The European Union imposed new tariffs on imported goods.", "tokens": ["The", "European", "Union", "imposed", "new", "tariffs", "on", "imported", "goods", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The president of France signed a new trade agreement with Germany.", "tokens": ["The", "president", "of", "France", "signed", "a", "new", "trade", "agreement", "with", "Germany", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "NASA launched a new satellite into orbit.", "tokens": ["NASA", "launched", "a", "new", "satellite", "into", "orbit", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations Secretary-General visited conflict zones in Africa.", "tokens": ["The", "United", "Nations", "Secretary", "-", "General", "visited", "conflict", "zones", "in", "Africa", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "The CEO of Tesla unveiled a new electric car model.", "tokens": ["The", "CEO", "of", "Tesla", "unveiled", "a", "new", "electric", "car", "model", "."], "labels": ["O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The governor of Texas announced a state of emergency due to severe weather.", "tokens": ["The", "governor", "of", "Texas", "announced", "a", "state", "of", "emergency", "due", "to", "severe", "weather", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Health Organization released a report on global vaccination rates.", "tokens": ["The", "World", "Health", "Organization", "released", "a", "report", "on", "global", "vaccination", "rates", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Apple's new headquarters in Cupertino, California, opened to the public.", "tokens": ["Apple", "'", "s", "new", "headquarters", "in", "Cupertino", ",", "California", ",", "opened", "to", "the", "public", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-location", "O", "B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations Security Council held an emergency meeting on the crisis in the Middle East.", "tokens": ["The", "United", "Nations", "Security", "Council", "held", "an", "emergency", "meeting", "on", "the", "crisis", "in", "the", "Middle", "East", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "The prime minister of Canada addressed the nation in a televised speech.", "tokens": ["The", "prime", "minister", "of", "Canada", "addressed", "the", "nation", "in", "a", "televised", "speech", "."], "labels": ["O", "O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon Inc. faced criticism for its labor practices.", "tokens": ["Amazon", "Inc", ".", "faced", "criticism", "for", "its", "labor", "practices", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The mayor of London announced a new initiative to reduce air pollution.", "tokens": ["The", "mayor", "of", "London", "announced", "a", "new", "initiative", "to", "reduce", "air", "pollution", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The International Monetary Fund warned of a global economic downturn.", "tokens": ["The", "International", "Monetary", "Fund", "warned", "of", "a", "global", "economic", "downturn", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook's CEO testified before Congress on data privacy issues.", "tokens": ["Facebook", "'", "s", "CEO", "testified", "before", "Congress", "on", "data", "privacy", "issues", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "The president of Russia met with the leaders of China and India to discuss trade agreements.", "tokens": ["The", "president", "of", "Russia", "met", "with", "the", "leaders", "of", "China", "and", "India", "to", "discuss", "trade", "agreements", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "B-location", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations Human Rights Council condemned human rights abuses in North Korea.", "tokens": ["The", "United", "Nations", "Human", "Rights", "Council", "condemned", "human", "rights", "abuses", "in", "North", "Korea", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "The CEO of Microsoft announced a major reorganization of the company.", "tokens": ["The", "CEO", "of", "Microsoft", "announced", "a", "major", "reorganization", "of", "the", "company", "."], "labels": ["O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "California's governor signed a new law to combat climate change.", "tokens": ["California", "'", "s", "governor", "signed", "a", "new", "law", "to", "combat", "climate", "change", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations Refugee Agency provided aid to displaced families in Syria.", "tokens": ["The", "United", "Nations", "Refugee", "Agency", "provided", "aid", "to", "displaced", "families", "in", "Syria", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "The CEO of Walmart unveiled a new sustainability initiative.", "tokens": ["The", "CEO", "of", "Walmart", "unveiled", "a", "new", "sustainability", "initiative", "."], "labels": ["O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The mayor of Paris announced plans to improve public transportation.", "tokens": ["The", "mayor", "of", "Paris", "announced", "plans", "to", "improve", "public", "transportation", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Trade Organization issued a statement on global trade tensions.", "tokens": ["The", "World", "Trade", "Organization", "issued", "a", "statement", "on", "global", "trade", "tensions", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Google's CEO testified before the Senate on antitrust concerns.", "tokens": ["Google", "'", "s", "CEO", "testified", "before", "the", "Senate", "on", "antitrust", "concerns", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "B-organization", "O", "O", "O", "O"]}
{"sentence": "The governor of Florida declared a state of emergency ahead of a hurricane.", "tokens": ["The", "governor", "of", "Florida", "declared", "a", "state", "of", "emergency", "ahead", "of", "a", "hurricane", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The International Atomic Energy Agency reported on nuclear non-proliferation efforts.", "tokens": ["The", "International", "Atomic", "Energy", "Agency", "reported", "on", "nuclear", "non", "-", "proliferation", "efforts", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The CEO of Amazon Inc. announced a new initiative to combat climate change.", "tokens": ["The", "CEO", "of", "Amazon", "Inc", ".", "announced", "a", "new", "initiative", "to", "combat", "climate", "change", "."], "labels": ["O", "O", "O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The mayor of Tokyo unveiled a plan to host the Olympic Games.", "tokens": ["The", "mayor", "of", "Tokyo", "unveiled", "a", "plan", "to", "host", "the", "Olympic", "Games", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The European Central Bank announced new monetary policy measures.", "tokens": ["The", "European", "Central", "Bank", "announced", "new", "monetary", "policy", "measures", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The president of Brazil spoke at the United Nations General Assembly.", "tokens": ["The", "president", "of", "Brazil", "spoke", "at", "the", "United", "Nations", "General", "Assembly", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "B-organization", "I-organization", "I-organization", "I-organization", "O"]}
{"sentence": "NASA's Mars rover discovered evidence of ancient microbial life.", "tokens": ["NASA", "'", "s", "Mars", "rover", "discovered", "evidence", "of", "ancient", "microbial", "life", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The mayor of Berlin announced a new initiative to support local businesses.", "tokens": ["The", "mayor", "of", "Berlin", "announced", "a", "new", "initiative", "to", "support", "local", "businesses", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Health Organization declared a global pandemic.", "tokens": ["The", "World", "Health", "Organization", "declared", "a", "global", "pandemic", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Apple Inc. faced criticism for its supply chain practices.", "tokens": ["Apple", "Inc", ".", "faced", "criticism", "for", "its", "supply", "chain", "practices", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The governor of Michigan signed a new education bill into law.", "tokens": ["The", "governor", "of", "Michigan", "signed", "a", "new", "education", "bill", "into", "law", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The International Monetary Fund warned of a recession.", "tokens": ["The", "International", "Monetary", "Fund", "warned", "of", "a", "recession", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Google's parent company, Alphabet Inc., reported a data breach.", "tokens": ["Google", "'", "s", "parent", "company", ",", "Alphabet", "Inc", ".", ",", "reported", "a", "data", "breach", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The mayor of Rome announced plans to overhaul the city's infrastructure.", "tokens": ["The", "mayor", "of", "Rome", "announced", "plans", "to", "overhaul", "the", "city", "'", "s", "infrastructure", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Trade Organization issued a report on global trade trends.", "tokens": ["The", "World", "Trade", "Organization", "issued", "a", "report", "on", "global", "trade", "trends", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook's CEO faced questioning from Congress on misinformation.", "tokens": ["Facebook", "'", "s", "CEO", "faced", "questioning", "from", "Congress", "on", "misinformation", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "B-organization", "O", "O", "O"]}
{"sentence": "The president of Indonesia addressed the nation in a televised speech.", "tokens": ["The", "president", "of", "Indonesia", "addressed", "the", "nation", "in", "a", "televised", "speech", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Food Programme provided aid to famine-stricken regions in Africa.", "tokens": ["The", "World", "Food", "Programme", "provided", "aid", "to", "famine", "-", "stricken", "regions", "in", "Africa", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "The CEO of Tesla faced legal challenges over labor practices.", "tokens": ["The", "CEO", "of", "Tesla", "faced", "legal", "challenges", "over", "labor", "practices", "."], "labels": ["O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The mayor of Chicago announced a new program to address gun violence.", "tokens": ["The", "mayor", "of", "Chicago", "announced", "a", "new", "program", "to", "address", "gun", "violence", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The CEO of Apple is expected to announce a new product next week.", "tokens": ["The", "CEO", "of", "Apple", "is", "expected", "to", "announce", "a", "new", "product", "next", "week", "."], "labels": ["O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "London police arrest suspect in connection with the recent bank robbery.", "tokens": ["London", "police", "arrest", "suspect", "in", "connection", "with", "the", "recent", "bank", "robbery", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The famous actress will receive a lifetime achievement award at the upcoming film festival.", "tokens": ["The", "famous", "actress", "will", "receive", "a", "lifetime", "achievement", "award", "at", "the", "upcoming", "film", "festival", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations issued a statement condemning the recent acts of aggression in the region.", "tokens": ["The", "United", "Nations", "issued", "a", "statement", "condemning", "the", "recent", "acts", "of", "aggression", "in", "the", "region", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Russia launches new initiative to combat climate change.", "tokens": ["Russia", "launches", "new", "initiative", "to", "combat", "climate", "change", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The renowned chef plans to open a new restaurant in Paris next year.", "tokens": ["The", "renowned", "chef", "plans", "to", "open", "a", "new", "restaurant", "in", "Paris", "next", "year", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O", "O", "O"]}
{"sentence": "The Prime Minister of Japan meets with world leaders to discuss economic cooperation.", "tokens": ["The", "Prime", "Minister", "of", "Japan", "meets", "with", "world", "leaders", "to", "discuss", "economic", "cooperation", "."], "labels": ["O", "O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "NASA announces plans for a manned mission to Mars in the next decade.", "tokens": ["NASA", "announces", "plans", "for", "a", "manned", "mission", "to", "Mars", "in", "the", "next", "decade", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook CEO testifies before Congress about the company's data privacy practices.", "tokens": ["Facebook", "CEO", "testifies", "before", "Congress", "about", "the", "company", "'", "s", "data", "privacy", "practices", "."], "labels": ["B-organization", "O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The mayor of New York City unveils a new plan to improve public transportation.", "tokens": ["The", "mayor", "of", "New", "York", "City", "unveils", "a", "new", "plan", "to", "improve", "public", "transportation", "."], "labels": ["O", "O", "O", "B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations issues a statement condemning the recent terrorist attacks in Europe.", "tokens": ["The", "United", "Nations", "issues", "a", "statement", "condemning", "the", "recent", "terrorist", "attacks", "in", "Europe", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Mary Smith appointed as the new CEO of Johnson & Johnson.", "tokens": ["Mary", "Smith", "appointed", "as", "the", "new", "CEO", "of", "Johnson", "&", "Johnson", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "B-organization", "I-organization", "I-organization", "O"]}
{"sentence": "NASA announces plans for a manned mission to Mars by 2030.", "tokens": ["NASA", "announces", "plans", "for", "a", "manned", "mission", "to", "Mars", "by", "2030", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O", "O", "O"]}
{"sentence": "Italy imposes new restrictions to combat the spread of COVID-19.", "tokens": ["Italy", "imposes", "new", "restrictions", "to", "combat", "the", "spread", "of", "COVID", "-", "19", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "B-organization", "I-organization", "I-organization", "O"]}
{"sentence": "Amazon to open a new fulfillment center in Texas, creating thousands of jobs.", "tokens": ["Amazon", "to", "open", "a", "new", "fulfillment", "center", "in", "Texas", ",", "creating", "thousands", "of", "jobs", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "President Biden signs an executive order to address climate change.", "tokens": ["President", "Biden", "signs", "an", "executive", "order", "to", "address", "climate", "change", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Health Organization warns of a potential new strain of influenza.", "tokens": ["The", "World", "Health", "Organization", "warns", "of", "a", "potential", "new", "strain", "of", "influenza", "."], "labels": ["B-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Russia accuses the United States of espionage activities near its border.", "tokens": ["Russia", "accuses", "the", "United", "States", "of", "espionage", "activities", "near", "its", "border", "."], "labels": ["B-location", "O", "B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Serena Williams wins her 23rd Grand Slam title at the Australian Open.", "tokens": ["Serena", "Williams", "wins", "her", "23rd", "Grand", "Slam", "title", "at", "the", "Australian", "Open", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "B-organization", "I-organization", "O"]}
{"sentence": "Apple announces record-breaking sales for its latest iPhone model.", "tokens": ["Apple", "announces", "record", "-", "breaking", "sales", "for", "its", "latest", "iPhone", "model", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "B-organization", "O", "O"]}
{"sentence": "Elon Musk plans to send more astronauts to the International Space Station.", "tokens": ["Elon", "Musk", "plans", "to", "send", "more", "astronauts", "to", "the", "International", "Space", "Station", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "I-location", "O"]}
{"sentence": "The European Union imposes new tariffs on steel imports from China.", "tokens": ["The", "European", "Union", "imposes", "new", "tariffs", "on", "steel", "imports", "from", "China", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Meghan Markle and Prince Harry welcome their second child.", "tokens": ["Meghan", "Markle", "and", "Prince", "Harry", "welcome", "their", "second", "child", "."], "labels": ["B-person", "I-person", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Pfizer announces new vaccine efficacy data against COVID-19 variants.", "tokens": ["Pfizer", "announces", "new", "vaccine", "efficacy", "data", "against", "COVID", "-", "19", "variants", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "India reports a surge in COVID-19 cases.", "tokens": ["India", "reports", "a", "surge", "in", "COVID", "-", "19", "cases", "."], "labels": ["B-location", "O", "O", "O", "O", "B-organization", "I-organization", "I-organization", "O", "O"]}
{"sentence": "Facebook faces antitrust probe from the Federal Trade Commission.", "tokens": ["Facebook", "faces", "antitrust", "probe", "from", "the", "Federal", "Trade", "Commission", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-organization", "I-organization", "I-organization", "O"]}
{"sentence": "SpaceX launches a new batch of Starlink satellites into orbit.", "tokens": ["SpaceX", "launches", "a", "new", "batch", "of", "Starlink", "satellites", "into", "orbit", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-organization", "O", "O", "O", "O"]}
{"sentence": "Angela Merkel visits the United States for diplomatic talks.", "tokens": ["Angela", "Merkel", "visits", "the", "United", "States", "for", "diplomatic", "talks", "."], "labels": ["B-person", "I-person", "O", "O", "B-location", "I-location", "O", "O", "O", "O"]}
{"sentence": "An earthquake hits Tokyo, causing widespread damage.", "tokens": ["An", "earthquake", "hits", "Tokyo", ",", "causing", "widespread", "damage", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O"]}
{"sentence": "Apple unveils the latest iPhone model at a product launch event.", "tokens": ["Apple", "unveils", "the", "latest", "iPhone", "model", "at", "a", "product", "launch", "event", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Cristiano Ronaldo signs a new contract with Juventus.", "tokens": ["Cristiano", "Ronaldo", "signs", "a", "new", "contract", "with", "Juventus", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "B-organization", "O"]}
{"sentence": "The World Health Organization warns of a new variant of the Ebola virus.", "tokens": ["The", "World", "Health", "Organization", "warns", "of", "a", "new", "variant", "of", "the", "Ebola", "virus", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "B-organization", "O", "O"]}
{"sentence": "The United Kingdom imposes stricter travel restrictions due to COVID-19.", "tokens": ["The", "United", "Kingdom", "imposes", "stricter", "travel", "restrictions", "due", "to", "COVID", "-", "19", "."], "labels": ["O", "B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon to open new headquarters in Texas.", "tokens": ["Amazon", "to", "open", "new", "headquarters", "in", "Texas", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Hurricane leaves trail of destruction in Florida.", "tokens": ["Hurricane", "leaves", "trail", "of", "destruction", "in", "Florida", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "University of California experiences budget cuts.", "tokens": ["University", "of", "California", "experiences", "budget", "cuts", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O"]}
{"sentence": "Tesla announces record-breaking sales.", "tokens": ["Tesla", "announces", "record", "-", "breaking", "sales", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New York City imposes new COVID-19 restrictions.", "tokens": ["New", "York", "City", "imposes", "new", "COVID", "-", "19", "restrictions", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "WHO issues new guidelines for vaccine distribution.", "tokens": ["WHO", "issues", "new", "guidelines", "for", "vaccine", "distribution", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Google acquires new tech startup.", "tokens": ["Google", "acquires", "new", "tech", "startup", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Olympic athlete tests positive for banned substance.", "tokens": ["Olympic", "athlete", "tests", "positive", "for", "banned", "substance", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Paris Fashion Week kicks off.", "tokens": ["Paris", "Fashion", "Week", "kicks", "off", "."], "labels": ["B-location", "O", "O", "O", "O", "O"]}
{"sentence": "New study reveals link between diet and heart health.", "tokens": ["New", "study", "reveals", "link", "between", "diet", "and", "heart", "health", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Microsoft introduces new gaming console.", "tokens": ["Microsoft", "introduces", "new", "gaming", "console", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Delta variant spreads rapidly across Europe.", "tokens": ["Delta", "variant", "spreads", "rapidly", "across", "Europe", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "B-location", "O"]}
{"sentence": "Apple and Google announce partnership for new app development.", "tokens": ["Apple", "and", "Google", "announce", "partnership", "for", "new", "app", "development", "."], "labels": ["B-organization", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Protests erupt in Hong Kong over new extradition law.", "tokens": ["Protests", "erupt", "in", "Hong", "Kong", "over", "new", "extradition", "law", "."], "labels": ["O", "O", "O", "B-location", "I-location", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook under fire for privacy violations.", "tokens": ["Facebook", "under", "fire", "for", "privacy", "violations", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "John Doe elected as new mayor of Smalltown, USA.", "tokens": ["John", "Doe", "elected", "as", "new", "mayor", "of", "Smalltown", ",", "USA", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "B-location", "O", "B-location", "O"]}
{"sentence": "Wells Fargo to close multiple branches.", "tokens": ["Wells", "Fargo", "to", "close", "multiple", "branches", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "California governor declares state of emergency after earthquake.", "tokens": ["California", "governor", "declares", "state", "of", "emergency", "after", "earthquake", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Miami Heat signs star rookie to multi-million dollar contract.", "tokens": ["Miami", "Heat", "signs", "star", "rookie", "to", "multi", "-", "million", "dollar", "contract", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "WHO declares global health emergency due to new virus.", "tokens": ["WHO", "declares", "global", "health", "emergency", "due", "to", "new", "virus", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Hurricane hits Caribbean islands, leaving widespread destruction.", "tokens": ["Hurricane", "hits", "Caribbean", "islands", ",", "leaving", "widespread", "destruction", "."], "labels": ["O", "O", "B-location", "I-location", "O", "O", "O", "O", "O"]}
{"sentence": "Amazon workers go on strike for better working conditions.", "tokens": ["Amazon", "workers", "go", "on", "strike", "for", "better", "working", "conditions", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Washington Post publishes article on climate change.", "tokens": ["Washington", "Post", "publishes", "article", "on", "climate", "change", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "College students protest rising tuition costs.", "tokens": ["College", "students", "protest", "rising", "tuition", "costs", "."], "labels": ["O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United Nations to send aid to conflict-affected region.", "tokens": ["United", "Nations", "to", "send", "aid", "to", "conflict", "-", "affected", "region", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Tokyo Olympics faces new challenges amid pandemic.", "tokens": ["Tokyo", "Olympics", "faces", "new", "challenges", "amid", "pandemic", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Miami Beach imposes new restrictions on alcohol sales.", "tokens": ["Miami", "Beach", "imposes", "new", "restrictions", "on", "alcohol", "sales", "."], "labels": ["B-location", "I-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Secretary of State visits Middle East for peace negotiations.", "tokens": ["Secretary", "of", "State", "visits", "Middle", "East", "for", "peace", "negotiations", "."], "labels": ["B-person", "I-person", "I-person", "O", "B-location", "I-location", "O", "O", "O", "O"]}
{"sentence": "Starbucks introduces new plant-based menu options.", "tokens": ["Starbucks", "introduces", "new", "plant", "-", "based", "menu", "options", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "New study shows alarming increase in childhood obesity rates.", "tokens": ["New", "study", "shows", "alarming", "increase", "in", "childhood", "obesity", "rates", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Facebook grilled by lawmakers in Congressional hearing.", "tokens": ["CEO", "of", "Facebook", "grilled", "by", "lawmakers", "in", "Congressional", "hearing", "."], "labels": ["B-person", "I-person", "I-person", "O", "O", "O", "O", "B-organization", "O", "O"]}
{"sentence": "Wildfire season in Australia poses new challenges for firefighters.", "tokens": ["Wildfire", "season", "in", "Australia", "poses", "new", "challenges", "for", "firefighters", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Former president announces bid for reelection.", "tokens": ["Former", "president", "announces", "bid", "for", "reelection", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Election results show Johnson in the lead.", "tokens": ["Election", "results", "show", "Johnson", "in", "the", "lead", "."], "labels": ["O", "O", "O", "B-person", "O", "O", "O", "O"]}
{"sentence": "Earthquake hits northern California.", "tokens": ["Earthquake", "hits", "northern", "California", "."], "labels": ["O", "O", "O", "B-location", "O"]}
{"sentence": "President Harris announces new economic policy.", "tokens": ["President", "Harris", "announces", "new", "economic", "policy", "."], "labels": ["O", "B-person", "O", "O", "O", "O", "O"]}
{"sentence": "Wildfires continue to ravage Australia.", "tokens": ["Wildfires", "continue", "to", "ravage", "Australia", "."], "labels": ["O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Famous actress arrested for DUI.", "tokens": ["Famous", "actress", "arrested", "for", "DUI", "."], "labels": ["O", "B-person", "O", "O", "O", "O"]}
{"sentence": "Amazon plans to open new distribution center.", "tokens": ["Amazon", "plans", "to", "open", "new", "distribution", "center", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Mayor of New York City proposes new tax law.", "tokens": ["Mayor", "of", "New", "York", "City", "proposes", "new", "tax", "law", "."], "labels": ["O", "O", "B-location", "I-location", "I-location", "O", "O", "O", "O", "O"]}
{"sentence": "Cyclone causes devastation in Bangladesh.", "tokens": ["Cyclone", "causes", "devastation", "in", "Bangladesh", "."], "labels": ["O", "O", "O", "O", "B-location", "O"]}
{"sentence": "CEO of Tesla predicts strong quarter earnings.", "tokens": ["CEO", "of", "Tesla", "predicts", "strong", "quarter", "earnings", "."], "labels": ["O", "O", "B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Famous singer announces world tour.", "tokens": ["Famous", "singer", "announces", "world", "tour", "."], "labels": ["O", "B-person", "O", "O", "O", "O"]}
{"sentence": "Flooding in Japan displaces thousands.", "tokens": ["Flooding", "in", "Japan", "displaces", "thousands", "."], "labels": ["O", "O", "B-location", "O", "O", "O"]}
{"sentence": "Microsoft acquires new start-up company.", "tokens": ["Microsoft", "acquires", "new", "start", "-", "up", "company", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "A.de Silva not out 49", "tokens": ["A", ".", "de", "Silva", "not", "out", "49"], "labels": ["B-person", "I-person", "I-person", "I-person", "O", "O", "O"]}
{"sentence": "Arizona senator calls for immigration reform.", "tokens": ["Arizona", "senator", "calls", "for", "immigration", "reform", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "NBA player LeBron James signs new contract.", "tokens": ["NBA", "player", "LeBron", "James", "signs", "new", "contract", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "New COVID-19 variant discovered in South Africa.", "tokens": ["New", "COVID", "-", "19", "variant", "discovered", "in", "South", "Africa", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "SpaceX CEO Elon Musk to resign.", "tokens": ["SpaceX", "CEO", "Elon", "Musk", "to", "resign", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O"]}
{"sentence": "Prime Minister Trudeau promises infrastructure funding.", "tokens": ["Prime", "Minister", "Trudeau", "promises", "infrastructure", "funding", "."], "labels": ["O", "O", "B-person", "O", "O", "O", "O"]}
{"sentence": "Oil prices surge amid Middle East tensions.", "tokens": ["Oil", "prices", "surge", "amid", "Middle", "East", "tensions", "."], "labels": ["O", "O", "O", "O", "B-location", "I-location", "O", "O"]}
{"sentence": "Activist Greta Thunberg speaks at climate change summit.", "tokens": ["Activist", "Greta", "Thunberg", "speaks", "at", "climate", "change", "summit", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "California governor signs new gun control bill.", "tokens": ["California", "governor", "signs", "new", "gun", "control", "bill", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Boxer Muhammad Ali wins championship fight.", "tokens": ["Boxer", "Muhammad", "Ali", "wins", "championship", "fight", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "U.S. President Biden to visit Mexico next week.", "tokens": ["U", ".", "S", ".", "President", "Biden", "to", "visit", "Mexico", "next", "week", "."], "labels": ["O", "O", "O", "O", "O", "B-person", "O", "O", "B-location", "O", "O", "O"]}
{"sentence": "CEO of Amazon Jeff Bezos steps down.", "tokens": ["CEO", "of", "Amazon", "Jeff", "Bezos", "steps", "down", "."], "labels": ["O", "O", "B-organization", "B-person", "I-person", "O", "O", "O"]}
{"sentence": "Queens Park Rangers win championship title.", "tokens": ["Queens", "Park", "Rangers", "win", "championship", "title", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O"]}
{"sentence": "Yellowstone National Park experiences record number of visitors.", "tokens": ["Yellowstone", "National", "Park", "experiences", "record", "number", "of", "visitors", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Singer Taylor Swift to release new album.", "tokens": ["Singer", "Taylor", "Swift", "to", "release", "new", "album", "."], "labels": ["O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "Starbucks launches new line of sustainable products.", "tokens": ["Starbucks", "launches", "new", "line", "of", "sustainable", "products", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Angela Merkel wins re-election as Chancellor of Germany.", "tokens": ["Angela", "Merkel", "wins", "re", "-", "election", "as", "Chancellor", "of", "Germany", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Wildfires in Oregon force evacuations.", "tokens": ["Wildfires", "in", "Oregon", "force", "evacuations", "."], "labels": ["O", "O", "B-location", "O", "O", "O"]}
{"sentence": "CEO of Microsoft Satya Nadella to step down.", "tokens": ["CEO", "of", "Microsoft", "Satya", "Nadella", "to", "step", "down", "."], "labels": ["O", "O", "B-organization", "B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "Famous chef Gordon Ramsay opens new restaurant.", "tokens": ["Famous", "chef", "Gordon", "Ramsay", "opens", "new", "restaurant", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "Google CEO Sundar Pichai announces new AI initiative.", "tokens": ["Google", "CEO", "Sundar", "Pichai", "announces", "new", "AI", "initiative", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O"]}
{"sentence": "New study shows link between climate change and extreme weather events.", "tokens": ["New", "study", "shows", "link", "between", "climate", "change", "and", "extreme", "weather", "events", "."], "labels": ["O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Coinbase goes public on stock exchange.", "tokens": ["Coinbase", "goes", "public", "on", "stock", "exchange", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Serena Williams wins Wimbledon title.", "tokens": ["Serena", "Williams", "wins", "Wimbledon", "title", "."], "labels": ["B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "Notre Dame Cathedral undergoes reconstruction.", "tokens": ["Notre", "Dame", "Cathedral", "undergoes", "reconstruction", "."], "labels": ["B-location", "I-location", "I-location", "O", "O", "O"]}
{"sentence": "SpaceX launches manned mission to International Space Station.", "tokens": ["SpaceX", "launches", "manned", "mission", "to", "International", "Space", "Station", "."], "labels": ["B-organization", "O", "O", "O", "O", "B-location", "I-location", "I-location", "O"]}
{"sentence": "New York Yankees acquire top pitcher in trade.", "tokens": ["New", "York", "Yankees", "acquire", "top", "pitcher", "in", "trade", "."], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Facebook whistleblower exposes internal corruption.", "tokens": ["Facebook", "whistleblower", "exposes", "internal", "corruption", "."], "labels": ["B-organization", "O", "O", "O", "O", "O"]}
{"sentence": "Hurricane causes widespread destruction in Caribbean islands.", "tokens": ["Hurricane", "causes", "widespread", "destruction", "in", "Caribbean", "islands", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "O", "O"]}
{"sentence": "CEO of Twitter Jack Dorsey steps down.", "tokens": ["CEO", "of", "Twitter", "Jack", "Dorsey", "steps", "down", "."], "labels": ["O", "O", "B-organization", "B-person", "I-person", "O", "O", "O"]}
{"sentence": "The United Nations issued a report today on the impact of climate change.", "tokens": ["The", "United", "Nations", "issued", "a", "report", "today", "on", "the", "impact", "of", "climate", "change", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "NASA plans to launch a new satellite into orbit next week.", "tokens": ["NASA", "plans", "to", "launch", "a", "new", "satellite", "into", "orbit", "next", "week", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The CEO of Apple, Tim Cook, made a statement regarding the company's latest product release.", "tokens": ["The", "CEO", "of", "Apple", ",", "Tim", "Cook", ",", "made", "a", "statement", "regarding", "the", "company", "'", "s", "latest", "product", "release", "."], "labels": ["O", "O", "O", "B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Prime Minister of Canada visited a local school in Toronto.", "tokens": ["The", "Prime", "Minister", "of", "Canada", "visited", "a", "local", "school", "in", "Toronto", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Facebook announced a new initiative to combat misinformation on its platform.", "tokens": ["Facebook", "announced", "a", "new", "initiative", "to", "combat", "misinformation", "on", "its", "platform", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The mayor of New York City unveiled a new plan for affordable housing.", "tokens": ["The", "mayor", "of", "New", "York", "City", "unveiled", "a", "new", "plan", "for", "affordable", "housing", "."], "labels": ["O", "O", "O", "B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United States Department of Defense confirmed a successful military operation in the Middle East.", "tokens": ["The", "United", "States", "Department", "of", "Defense", "confirmed", "a", "successful", "military", "operation", "in", "the", "Middle", "East", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "The singer Taylor Swift announced a world tour starting next year.", "tokens": ["The", "singer", "Taylor", "Swift", "announced", "a", "world", "tour", "starting", "next", "year", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "South Korea and North Korea signed a historic peace agreement last week.", "tokens": ["South", "Korea", "and", "North", "Korea", "signed", "a", "historic", "peace", "agreement", "last", "week", "."], "labels": ["B-location", "I-location", "O", "B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The CEO of Tesla, Elon Musk, made headlines with a controversial tweet.", "tokens": ["The", "CEO", "of", "Tesla", ",", "Elon", "Musk", ",", "made", "headlines", "with", "a", "controversial", "tweet", "."], "labels": ["O", "O", "O", "B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations Security Council passed a resolution condemning the recent acts of aggression.", "tokens": ["The", "United", "Nations", "Security", "Council", "passed", "a", "resolution", "condemning", "the", "recent", "acts", "of", "aggression", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "United States imposes sanctions on Russia.", "tokens": ["United", "States", "imposes", "sanctions", "on", "Russia", "."], "labels": ["B-location", "I-location", "O", "O", "O", "B-location", "O"]}
{"sentence": "Tennis star Serena Williams announces retirement.", "tokens": ["Tennis", "star", "Serena", "Williams", "announces", "retirement", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O"]}
{"sentence": "European Union leaders discuss immigration policy.", "tokens": ["European", "Union", "leaders", "discuss", "immigration", "policy", "."], "labels": ["B-organization", "I-organization", "O", "O", "O", "O", "O"]}
{"sentence": "CEO of Amazon, Jeff Bezos, steps down.", "tokens": ["CEO", "of", "Amazon", ",", "Jeff", "Bezos", ",", "steps", "down", "."], "labels": ["O", "O", "B-organization", "O", "B-person", "I-person", "O", "O", "O", "O"]}
{"sentence": "Wildfire destroys thousands of acres in California.", "tokens": ["Wildfire", "destroys", "thousands", "of", "acres", "in", "California", "."], "labels": ["O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Pfizer seeks approval for Covid-19 vaccine booster shots.", "tokens": ["Pfizer", "seeks", "approval", "for", "Covid", "-", "19", "vaccine", "booster", "shots", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Former president Barack Obama to speak at climate summit.", "tokens": ["Former", "president", "Barack", "Obama", "to", "speak", "at", "climate", "summit", "."], "labels": ["O", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Teen climate activist Greta Thunberg nominated for Nobel Peace Prize.", "tokens": ["Teen", "climate", "activist", "Greta", "Thunberg", "nominated", "for", "Nobel", "Peace", "Prize", "."], "labels": ["O", "O", "O", "B-person", "I-person", "O", "O", "B-organization", "I-organization", "I-organization", "O"]}
{"sentence": "Flooding in India displaces thousands.", "tokens": ["Flooding", "in", "India", "displaces", "thousands", "."], "labels": ["O", "O", "B-location", "O", "O", "O"]}
{"sentence": "World Health Organization declares new Covid-19 variant a \"variant of concern.\"", "tokens": ["World", "Health", "Organization", "declares", "new", "Covid", "-", "19", "variant", "a", "\"", "variant", "of", "concern", ".", "\""], "labels": ["B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "NASA rover discovers evidence of ancient life on Mars.", "tokens": ["NASA", "rover", "discovers", "evidence", "of", "ancient", "life", "on", "Mars", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Facebook announces new measures to combat fake news.", "tokens": ["Facebook", "announces", "new", "measures", "to", "combat", "fake", "news", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Australian government introduces new climate change legislation.", "tokens": ["Australian", "government", "introduces", "new", "climate", "change", "legislation", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Alibaba founder Jack Ma reemerges after period of absence.", "tokens": ["Alibaba", "founder", "Jack", "Ma", "reemerges", "after", "period", "of", "absence", "."], "labels": ["B-organization", "O", "B-person", "I-person", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Oil spill contaminates coastline in Louisiana.", "tokens": ["Oil", "spill", "contaminates", "coastline", "in", "Louisiana", "."], "labels": ["O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "Juventus signs soccer prodigy from Argentina.", "tokens": ["Juventus", "signs", "soccer", "prodigy", "from", "Argentina", "."], "labels": ["B-organization", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "The United Nations has announced a new peacekeeping mission in the Middle East.", "tokens": ["The", "United", "Nations", "has", "announced", "a", "new", "peacekeeping", "mission", "in", "the", "Middle", "East", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "The CEO of Apple Inc. is set to unveil the latest iPhone model at the upcoming tech conference.", "tokens": ["The", "CEO", "of", "Apple", "Inc", ".", "is", "set", "to", "unveil", "the", "latest", "iPhone", "model", "at", "the", "upcoming", "tech", "conference", "."], "labels": ["O", "O", "O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Prime Minister of Canada is facing criticism for the handling of the recent economic crisis.", "tokens": ["The", "Prime", "Minister", "of", "Canada", "is", "facing", "criticism", "for", "the", "handling", "of", "the", "recent", "economic", "crisis", "."], "labels": ["O", "B-person", "I-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "Russia has announced plans to increase military presence along its western border.", "tokens": ["Russia", "has", "announced", "plans", "to", "increase", "military", "presence", "along", "its", "western", "border", "."], "labels": ["B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The European Union has imposed sanctions on several individuals with ties to the regime in Belarus.", "tokens": ["The", "European", "Union", "has", "imposed", "sanctions", "on", "several", "individuals", "with", "ties", "to", "the", "regime", "in", "Belarus", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "The mayor of New York City has declared a state of emergency due to the recent surge in COVID-19 cases.", "tokens": ["The", "mayor", "of", "New", "York", "City", "has", "declared", "a", "state", "of", "emergency", "due", "to", "the", "recent", "surge", "in", "COVID", "-", "19", "cases", "."], "labels": ["O", "O", "O", "B-location", "I-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "NASA plans to launch a new satellite into orbit next month.", "tokens": ["NASA", "plans", "to", "launch", "a", "new", "satellite", "into", "orbit", "next", "month", "."], "labels": ["B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The World Health Organization has issued a warning about the spread of a new variant of the coronavirus.", "tokens": ["The", "World", "Health", "Organization", "has", "issued", "a", "warning", "about", "the", "spread", "of", "a", "new", "variant", "of", "the", "coronavirus", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The President of France is scheduled to meet with leaders from neighboring countries to discuss trade agreements.", "tokens": ["The", "President", "of", "France", "is", "scheduled", "to", "meet", "with", "leaders", "from", "neighboring", "countries", "to", "discuss", "trade", "agreements", "."], "labels": ["O", "B-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations Security Council has voted to impose sanctions on North Korea.", "tokens": ["The", "United", "Nations", "Security", "Council", "has", "voted", "to", "impose", "sanctions", "on", "North", "Korea", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O"]}
{"sentence": "The CEO of Amazon has announced plans to invest in renewable energy projects.", "tokens": ["The", "CEO", "of", "Amazon", "has", "announced", "plans", "to", "invest", "in", "renewable", "energy", "projects", "."], "labels": ["O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The earthquake in Japan has caused widespread destruction and loss of life.", "tokens": ["The", "earthquake", "in", "Japan", "has", "caused", "widespread", "destruction", "and", "loss", "of", "life", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Secretary-General of the United Nations is calling for an immediate ceasefire in the region.", "tokens": ["The", "Secretary", "-", "General", "of", "the", "United", "Nations", "is", "calling", "for", "an", "immediate", "ceasefire", "in", "the", "region", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "I-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The European Central Bank has announced plans to increase interest rates in response to inflation concerns.", "tokens": ["The", "European", "Central", "Bank", "has", "announced", "plans", "to", "increase", "interest", "rates", "in", "response", "to", "inflation", "concerns", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The governor of Texas has declared a state of emergency following severe flooding in the region.", "tokens": ["The", "governor", "of", "Texas", "has", "declared", "a", "state", "of", "emergency", "following", "severe", "flooding", "in", "the", "region", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The IMF has approved a new aid package for Argentina to help stabilize its economy.", "tokens": ["The", "IMF", "has", "approved", "a", "new", "aid", "package", "for", "Argentina", "to", "help", "stabilize", "its", "economy", "."], "labels": ["O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The President of the United States has signed a new trade agreement with Mexico and Canada.", "tokens": ["The", "President", "of", "the", "United", "States", "has", "signed", "a", "new", "trade", "agreement", "with", "Mexico", "and", "Canada", "."], "labels": ["O", "B-person", "I-person", "I-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "B-location", "O", "B-location", "O"]}
{"sentence": "The WHO has declared a global health emergency in response to the rapid spread of a new virus.", "tokens": ["The", "WHO", "has", "declared", "a", "global", "health", "emergency", "in", "response", "to", "the", "rapid", "spread", "of", "a", "new", "virus", "."], "labels": ["O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The prime minister of Italy has resigned amid political turmoil.", "tokens": ["The", "prime", "minister", "of", "Italy", "has", "resigned", "amid", "political", "turmoil", "."], "labels": ["O", "B-organization", "I-organization", "I-organization", "I-organization", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The UNHCR is assisting refugees from war-torn countries in finding safe havens.", "tokens": ["The", "UNHCR", "is", "assisting", "refugees", "from", "war", "-", "torn", "countries", "in", "finding", "safe", "havens", "."], "labels": ["O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The mayor of London has announced plans to address the city's housing crisis.", "tokens": ["The", "mayor", "of", "London", "has", "announced", "plans", "to", "address", "the", "city", "'", "s", "housing", "crisis", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The South Korean government has unveiled a new plan to boost the economy through infrastructure investments.", "tokens": ["The", "South", "Korean", "government", "has", "unveiled", "a", "new", "plan", "to", "boost", "the", "economy", "through", "infrastructure", "investments", "."], "labels": ["O", "B-location", "I-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The CEO of Tesla has announced a partnership with a Chinese technology company to build a new electric vehicle factory.", "tokens": ["The", "CEO", "of", "Tesla", "has", "announced", "a", "partnership", "with", "a", "Chinese", "technology", "company", "to", "build", "a", "new", "electric", "vehicle", "factory", "."], "labels": ["O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The WHO has warned of a potential humanitarian crisis in a conflict-affected region.", "tokens": ["The", "WHO", "has", "warned", "of", "a", "potential", "humanitarian", "crisis", "in", "a", "conflict", "-", "affected", "region", "."], "labels": ["O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The governor of California has declared a state of emergency due to the ongoing wildfires.", "tokens": ["The", "governor", "of", "California", "has", "declared", "a", "state", "of", "emergency", "due", "to", "the", "ongoing", "wildfires", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The IMF has approved a new financial assistance program for Ukraine.", "tokens": ["The", "IMF", "has", "approved", "a", "new", "financial", "assistance", "program", "for", "Ukraine", "."], "labels": ["O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "The CEO of Facebook has testified before Congress about data privacy concerns.", "tokens": ["The", "CEO", "of", "Facebook", "has", "testified", "before", "Congress", "about", "data", "privacy", "concerns", "."], "labels": ["O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The President of Brazil has announced plans to address deforestation in the Amazon rainforest.", "tokens": ["The", "President", "of", "Brazil", "has", "announced", "plans", "to", "address", "deforestation", "in", "the", "Amazon", "rainforest", "."], "labels": ["O", "B-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O", "O"]}
{"sentence": "The European Union has imposed tariffs on imports from the United States in a trade dispute.", "tokens": ["The", "European", "Union", "has", "imposed", "tariffs", "on", "imports", "from", "the", "United", "States", "in", "a", "trade", "dispute", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O", "O", "O", "O", "O"]}
{"sentence": "The CEO of Microsoft has announced a major restructuring of the company's leadership team.", "tokens": ["The", "CEO", "of", "Microsoft", "has", "announced", "a", "major", "restructuring", "of", "the", "company", "'", "s", "leadership", "team", "."], "labels": ["O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Prime Minister of Japan has pledged support for developing countries in combating climate change.", "tokens": ["The", "Prime", "Minister", "of", "Japan", "has", "pledged", "support", "for", "developing", "countries", "in", "combating", "climate", "change", "."], "labels": ["O", "B-person", "I-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The UNHCR is calling for increased humanitarian aid for refugees in the region.", "tokens": ["The", "UNHCR", "is", "calling", "for", "increased", "humanitarian", "aid", "for", "refugees", "in", "the", "region", "."], "labels": ["O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The governor of Florida has issued a state of emergency in preparation for an incoming hurricane.", "tokens": ["The", "governor", "of", "Florida", "has", "issued", "a", "state", "of", "emergency", "in", "preparation", "for", "an", "incoming", "hurricane", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The IMF has revised its global economic growth forecast for the upcoming year.", "tokens": ["The", "IMF", "has", "revised", "its", "global", "economic", "growth", "forecast", "for", "the", "upcoming", "year", "."], "labels": ["O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The CEO of Google has announced plans to expand the company's presence in the Middle East market.", "tokens": ["The", "CEO", "of", "Google", "has", "announced", "plans", "to", "expand", "the", "company", "'", "s", "presence", "in", "the", "Middle", "East", "market", "."], "labels": ["O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "I-location", "O", "O"]}
{"sentence": "The President of South Africa has called for unity and reconciliation in the wake of recent political unrest.", "tokens": ["The", "President", "of", "South", "Africa", "has", "called", "for", "unity", "and", "reconciliation", "in", "the", "wake", "of", "recent", "political", "unrest", "."], "labels": ["O", "B-person", "I-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The mayor of Chicago has unveiled a new plan to combat rising crime rates in the city.", "tokens": ["The", "mayor", "of", "Chicago", "has", "unveiled", "a", "new", "plan", "to", "combat", "rising", "crime", "rates", "in", "the", "city", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The European Commission has proposed new regulations to curb greenhouse gas emissions.", "tokens": ["The", "European", "Commission", "has", "proposed", "new", "regulations", "to", "curb", "greenhouse", "gas", "emissions", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Prime Minister of Australia has announced measures to address the country's housing affordability crisis.", "tokens": ["The", "Prime", "Minister", "of", "Australia", "has", "announced", "measures", "to", "address", "the", "country", "'", "s", "housing", "affordability", "crisis", "."], "labels": ["O", "B-person", "I-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The United Nations has launched a new initiative to promote gender equality in the workplace.", "tokens": ["The", "United", "Nations", "has", "launched", "a", "new", "initiative", "to", "promote", "gender", "equality", "in", "the", "workplace", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The CEO of Netflix has announced plans to invest in original content production in India.", "tokens": ["The", "CEO", "of", "Netflix", "has", "announced", "plans", "to", "invest", "in", "original", "content", "production", "in", "India", "."], "labels": ["O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O"]}
{"sentence": "The WHO has warned of a potential public health crisis in a region with limited access to medical care.", "tokens": ["The", "WHO", "has", "warned", "of", "a", "potential", "public", "health", "crisis", "in", "a", "region", "with", "limited", "access", "to", "medical", "care", "."], "labels": ["O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The governor of Michigan has declared a state of emergency in response to a water contamination crisis.", "tokens": ["The", "governor", "of", "Michigan", "has", "declared", "a", "state", "of", "emergency", "in", "response", "to", "a", "water", "contamination", "crisis", "."], "labels": ["O", "O", "O", "B-location", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Secretary-General of the United Nations has called for urgent action on climate change.", "tokens": ["The", "Secretary", "-", "General", "of", "the", "United", "Nations", "has", "called", "for", "urgent", "action", "on", "climate", "change", "."], "labels": ["O", "B-person", "I-person", "I-person", "I-person", "I-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The IMF has approved a new loan program for a struggling African country.", "tokens": ["The", "IMF", "has", "approved", "a", "new", "loan", "program", "for", "a", "struggling", "African", "country", "."], "labels": ["O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "B-location", "O", "O"]}
{"sentence": "The CEO of Twitter has announced new measures to combat misinformation on the platform.", "tokens": ["The", "CEO", "of", "Twitter", "has", "announced", "new", "measures", "to", "combat", "misinformation", "on", "the", "platform", "."], "labels": ["O", "O", "O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The President of Argentina has pledged support for farmers affected by drought in the region.", "tokens": ["The", "President", "of", "Argentina", "has", "pledged", "support", "for", "farmers", "affected", "by", "drought", "in", "the", "region", "."], "labels": ["O", "B-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The European Union has allocated funds for the development of renewable energy projects in member countries.", "tokens": ["The", "European", "Union", "has", "allocated", "funds", "for", "the", "development", "of", "renewable", "energy", "projects", "in", "member", "countries", "."], "labels": ["O", "B-organization", "I-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The Prime Minister of India has announced plans to invest in infrastructure to boost economic growth.", "tokens": ["The", "Prime", "Minister", "of", "India", "has", "announced", "plans", "to", "invest", "in", "infrastructure", "to", "boost", "economic", "growth", "."], "labels": ["O", "B-person", "I-person", "I-person", "I-person", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
{"sentence": "The UNHCR is providing aid to refugees fleeing conflict in the region.", "tokens": ["The", "UNHCR", "is", "providing", "aid", "to", "refugees", "fleeing", "conflict", "in", "the", "region", "."], "labels": ["O", "B-organization", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O", "O"]}
