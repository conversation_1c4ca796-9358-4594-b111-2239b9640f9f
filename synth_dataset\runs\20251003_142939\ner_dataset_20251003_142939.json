{"国籍": [{"text": "美国留学生将在下个月抵达北京参加学术会议。", "label": [{"entity": "美国", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "日本游客对中国的长城表示了浓厚的兴趣。", "label": [{"entity": "日本", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "英国首相计划在下周五访问法国巴黎。", "label": [{"entity": "英国", "start_idx": 0, "end_idx": 2, "type": "国籍"}, {"entity": "法国", "start_idx": 9, "end_idx": 11, "type": "国籍"}]}, {"text": "韩国明星在东京举行了盛大的粉丝见面会。", "label": [{"entity": "韩国", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "加拿大科学家在实验室里取得了重大突破。", "label": [{"entity": "加拿大", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "澳大利亚运动员在奥运会上获得了金牌。", "label": [{"entity": "澳大利亚", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "德国工程师为上海地铁项目提供了技术支持。", "label": [{"entity": "德国", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "俄罗斯作家的新书在莫斯科引起了轰动。", "label": [{"entity": "俄罗斯", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "法国厨师在成都开了一家法式餐厅。", "label": [{"entity": "法国", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "新加坡商人决定在广州投资兴建新工厂。", "label": [{"entity": "新加坡", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "日本游客在京都的传统寺庙里拍摄了许多照片。", "label": [{"entity": "日本", "start_idx": 0, "end_idx": 1, "type": "国籍"}]}, {"text": "法国厨师在巴黎的餐厅里展示了精湛的烹饪技艺。", "label": [{"entity": "法国", "start_idx": 0, "end_idx": 1, "type": "国籍"}]}, {"text": "英国学生正在伦敦的大学里学习计算机科学。", "label": [{"entity": "英国", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "澳大利亚运动员在墨尔本赢得了游泳比赛冠军。", "label": [{"entity": "澳大利亚", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "德国工程师为慕尼黑的工厂设计了新型生产线。", "label": [{"entity": "德国", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "美国教授在纽约的大学里讲授了人工智能课程。", "label": [{"entity": "美国", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}], "年龄": [{"text": "这位76岁的老人每天都坚持锻炼身体。", "label": [{"entity": "76岁", "start_idx": 2, "end_idx": 4, "type": "年龄"}]}, {"text": "那个18岁的年轻人刚刚拿到了驾照。", "label": [{"entity": "18岁", "start_idx": 4, "end_idx": 6, "type": "年龄"}]}, {"text": "他今年42岁，已经是公司的部门经理。", "label": [{"entity": "42岁", "start_idx": 4, "end_idx": 6, "type": "年龄"}]}, {"text": "那个9岁的孩子正在学习弹钢琴。", "label": [{"entity": "9岁", "start_idx": 3, "end_idx": 4, "type": "年龄"}]}, {"text": "我的祖母今年85岁，身体依然很硬朗。", "label": [{"entity": "85岁", "start_idx": 7, "end_idx": 9, "type": "年龄"}]}, {"text": "这个7岁的男孩特别喜欢看动画片。", "label": [{"entity": "7岁", "start_idx": 3, "end_idx": 5, "type": "年龄"}]}, {"text": "她今年24岁，刚刚大学毕业参加工作。", "label": [{"entity": "24", "start_idx": 4, "end_idx": 5, "type": "年龄"}]}, {"text": "这位75岁的老人每天坚持晨练。", "label": [{"entity": "75岁", "start_idx": 2, "end_idx": 4, "type": "年龄"}]}, {"text": "我的孩子今年8岁，正在上小学。", "label": [{"entity": "8岁", "start_idx": 7, "end_idx": 9, "type": "年龄"}]}, {"text": "那位90岁的老奶奶精神依然很好。", "label": [{"entity": "90岁", "start_idx": 4, "end_idx": 6, "type": "年龄"}]}, {"text": "这家幼儿园招收2到5岁的儿童。", "label": [{"entity": "2到5岁", "start_idx": 9, "end_idx": 12, "type": "年龄"}]}, {"text": "医生建议65岁以上的老人定期体检。", "label": [{"entity": "65", "start_idx": 6, "end_idx": 7, "type": "年龄"}]}, {"text": "我的爷爷已经82岁，身体还算硬朗。", "label": [{"entity": "82岁", "start_idx": 7, "end_idx": 9, "type": "年龄"}]}, {"text": "这个11岁的男孩已经会弹钢琴了。", "label": [{"entity": "11岁", "start_idx": 2, "end_idx": 4, "type": "年龄"}]}, {"text": "小明今年5岁，是幼儿园的小班学生。", "label": [{"entity": "5岁", "start_idx": 4, "end_idx": 6, "type": "年龄"}]}, {"text": "我的祖母已经85岁了，依然精神矍铄。", "label": [{"entity": "85岁", "start_idx": 7, "end_idx": 9, "type": "年龄"}]}, {"text": "她女儿刚满18岁，昨天刚拿到驾照。", "label": [{"entity": "18岁", "start_idx": 7, "end_idx": 9, "type": "年龄"}]}, {"text": "这个3岁的孩子已经会自己穿衣服了。", "label": [{"entity": "3岁", "start_idx": 2, "end_idx": 4, "type": "年龄"}]}], "姓名": [{"text": "张伟明天要去北京参加一个重要的会议。", "label": [{"entity": "张伟", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "李娜是一位非常出色的网球运动员。", "label": [{"entity": "李娜", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "王芳在图书馆找到了她丢失的笔记本。", "label": [{"entity": "王芳", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "赵红昨天去电影院看了新上映的电影。", "label": [{"entity": "赵红", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "孙悦的公司最近搬到了新的办公楼。", "label": [{"entity": "孙悦", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "周杰在篮球比赛中投进了关键一球。", "label": [{"entity": "周杰", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "吴涛的生日聚会定在了下个周末。", "label": [{"entity": "吴涛", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "黄磊的弟弟今天开始上小学一年级。", "label": [{"entity": "黄磊", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "张伟是新来的数学老师，学生们都很喜欢他。", "label": [{"entity": "张伟", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "王芳在会议上提出了一个很有创意的建议。", "label": [{"entity": "王芳", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "刘洋最近在准备一场重要的演讲比赛。", "label": [{"entity": "刘洋", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "陈晓昨天晚上去电影院看了新上映的电影。", "label": [{"entity": "陈晓", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "赵磊在公司的年度评选中获得了优秀员工奖。", "label": [{"entity": "赵磊", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "杨华是一位非常有名的画家，他的作品很受欢迎。", "label": [{"entity": "杨华", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "周杰在篮球比赛中表现非常出色，赢得了观众的掌声。", "label": [{"entity": "周杰", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "孙悦在餐厅里遇到了一位老朋友，两人聊了很久。", "label": [{"entity": "孙悦", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "老朋友", "start_idx": 7, "end_idx": 9, "type": "姓名"}]}, {"text": "吴磊是一名医生，他在医院里工作已经有十年了。", "label": [{"entity": "吴磊", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "张伟在会议上提出了关于项目进度的详细报告。", "label": [{"entity": "张伟", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "李娜今天下午三点会来公司参加产品发布会。", "label": [{"entity": "李娜", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}], "性别": [{"text": "李明是一位男性医生，每天为病人提供专业的医疗服务。", "label": [{"entity": "男性", "start_idx": 5, "end_idx": 7, "type": "性别"}]}, {"text": "张婷是女性教师，在小学担任语文老师已有十年。", "label": [{"entity": "女性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "王强这位男性工程师，负责公司所有项目的技术支持。", "label": [{"entity": "男性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "刘芳作为女性设计师，她的作品总是充满创意和美感。", "label": [{"entity": "女性", "start_idx": 4, "end_idx": 6, "type": "性别"}]}, {"text": "陈刚这位男性运动员，在去年的全国比赛中获得了金牌。", "label": [{"entity": "男性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "赵雪是女性律师，擅长处理复杂的商业法律纠纷。", "label": [{"entity": "女性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "杨勇这位男性消防员，多次在火灾中成功救出被困群众。", "label": [{"entity": "男性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "周丽作为女性护士，她的细心和耐心让病人感到安心。", "label": [{"entity": "女性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "吴浩是男性导演，他的电影作品多次获得国际奖项。", "label": [{"entity": "男性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "孙梅是女性科学家，在生物医学领域有重要的研究成果。", "label": [{"entity": "女性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "李先生今天早上准时到达了公司会议室。", "label": [{"entity": "先生", "start_idx": 2, "end_idx": 3, "type": "性别"}]}, {"text": "王女士正在给她的孩子们准备丰盛的晚餐。", "label": [{"entity": "女士", "start_idx": 1, "end_idx": 2, "type": "性别"}]}, {"text": "张小姐的笔记本电脑在昨天下午不慎丢失了。", "label": [{"entity": "张小姐", "start_idx": 0, "end_idx": 3, "type": "性别"}]}, {"text": "陈女士是这家餐厅里最受欢迎的服务员。", "label": [{"entity": "陈女士", "start_idx": 0, "end_idx": 3, "type": "性别"}]}, {"text": "赵先生刚刚完成了一项重要的商业项目。", "label": [{"entity": "先生", "start_idx": 2, "end_idx": 3, "type": "性别"}]}, {"text": "孙小姐在图书馆找到了她一直想看的小说。", "label": [{"entity": "小姐", "start_idx": 2, "end_idx": 3, "type": "性别"}]}, {"text": "王先生刚刚完成了一项复杂的工程项目，获得了公司嘉奖。", "label": [{"entity": "先生", "start_idx": 2, "end_idx": 3, "type": "性别"}]}, {"text": "李先生今天早上准时到达了公司的会议现场。", "label": [{"entity": "先生", "start_idx": 1, "end_idx": 2, "type": "性别"}]}], "职业": [{"text": "医生正在为患者进行详细的身体检查。", "label": [{"entity": "医生", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "教师用生动的方式向学生们讲解课文内容。", "label": [{"entity": "教师", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "工程师正在设计一栋现代化的大楼。", "label": [{"entity": "工程师", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "厨师在厨房里准备着今天的特色菜。", "label": [{"entity": "厨师", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "程序员正在编写代码以修复系统漏洞。", "label": [{"entity": "程序员", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "摄影师在户外拍摄美丽的自然风光。", "label": [{"entity": "摄影师", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "记者在采访一位刚刚获奖的科学家。", "label": [{"entity": "记者", "start_idx": 0, "end_idx": 2, "type": "职业"}, {"entity": "科学家", "start_idx": 10, "end_idx": 13, "type": "职业"}]}, {"text": "医生正在为这位患者进行详细检查。", "label": [{"entity": "医生", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "教师耐心地解答了学生们的问题。", "label": [{"entity": "教师", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "厨师精心准备了一桌丰盛的晚餐。", "label": [{"entity": "厨师", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "程序员正在编写新的应用程序代码。", "label": [{"entity": "程序员", "start_idx": 0, "end_idx": 2, "type": "职业"}]}, {"text": "这位小学教师正在认真批改学生的数学作业。", "label": [{"entity": "小学教师", "start_idx": 2, "end_idx": 5, "type": "职业"}]}], "民族": [{"text": "藏族同胞在高原上跳起了传统的锅庄舞。", "label": [{"entity": "藏族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "维吾尔族姑娘穿着鲜艳的民族服装参加节日庆典。", "label": [{"entity": "维吾尔族", "start_idx": 0, "end_idx": 3, "type": "民族"}]}, {"text": "壮族人民用壮锦编织出精美的图案。", "label": [{"entity": "壮族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "蒙古族牧民在草原上放牧着成群的牛羊。", "label": [{"entity": "蒙古族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "朝鲜族农民在田间辛勤劳作，庆祝丰收。", "label": [{"entity": "朝鲜族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "彝族青年在火把节上点燃了象征希望的火把。", "label": [{"entity": "彝族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "苗族妇女在河边清洗着五彩斑斓的刺绣。", "label": [{"entity": "苗族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "满族老人在庭院里讲述着古老的故事。", "label": [{"entity": "满族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "瑶族儿童在山间追逐嬉戏，笑声不断。", "label": [{"entity": "瑶族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "傣族村民在泼水节上互相泼水，庆祝新年。", "label": [{"entity": "傣族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "藏族人民在高原上跳起了传统的锅庄舞。", "label": [{"entity": "藏族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "维吾尔族的音乐充满独特的民族风情。", "label": [{"entity": "维吾尔族", "start_idx": 0, "end_idx": 3, "type": "民族"}]}, {"text": "壮族姑娘穿着鲜艳的节日盛装参加庆典。", "label": [{"entity": "壮族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "蒙古族牧民在草原上放牧着成群的牛羊。", "label": [{"entity": "蒙古族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "朝鲜族的长鼓舞表演赢得了满堂喝彩。", "label": [{"entity": "朝鲜族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "苗族银饰工艺精湛，展现了独特的审美。", "label": [{"entity": "苗族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "满族旗袍的设计融合了传统与现代元素。", "label": [{"entity": "满族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "土家族的摆手舞反映了民族的生活习俗。", "label": [{"entity": "土家族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "傣族的泼水节是当地最具特色的传统节日。", "label": [{"entity": "傣族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}], "教育背景": [{"text": "他的教育背景是北京大学计算机科学与技术专业的学士学位。", "label": [{"entity": "北京大学计算机科学与技术专业的学士学位", "start_idx": 7, "end_idx": 23, "type": "教育背景"}]}, {"text": "她拥有清华大学经济管理学院的硕士学位。", "label": [{"entity": "清华大学经济管理学院的硕士学位", "start_idx": 6, "end_idx": 23, "type": "教育背景"}]}, {"text": "该公司要求应聘者至少具备上海交通大学电气工程专业的本科学历。", "label": [{"entity": "上海交通大学", "start_idx": 11, "end_idx": 17, "type": "教育背景"}, {"entity": "电气工程专业", "start_idx": 18, "end_idx": 23, "type": "教育背景"}, {"entity": "本科学历", "start_idx": 24, "end_idx": 27, "type": "教育背景"}]}, {"text": "这位教授的教育背景是哈佛大学历史学博士学位。", "label": [{"entity": "哈佛大学历史学博士学位", "start_idx": 10, "end_idx": 21, "type": "教育背景"}]}, {"text": "招聘启事中明确要求应聘者持有复旦大学新闻学专业的学士学位。", "label": [{"entity": "复旦大学", "start_idx": 21, "end_idx": 23, "type": "教育背景"}, {"entity": "新闻学", "start_idx": 24, "end_idx": 26, "type": "教育背景"}, {"entity": "学士学位", "start_idx": 27, "end_idx": 30, "type": "教育背景"}]}, {"text": "他的简历显示，他毕业于浙江大学机械工程专业的硕士课程。", "label": [{"entity": "浙江大学机械工程专业的硕士课程", "start_idx": 12, "end_idx": 32, "type": "教育背景"}]}, {"text": "该职位优先考虑拥有牛津大学数学专业博士学位的候选人。", "label": [{"entity": "牛津大学", "start_idx": 11, "end_idx": 13, "type": "教育背景"}, {"entity": "数学专业博士学位", "start_idx": 14, "end_idx": 21, "type": "教育背景"}]}, {"text": "她通过自己的努力，获得了剑桥大学法律专业的硕士学位。", "label": [{"entity": "剑桥大学法律专业的硕士学位", "start_idx": 10, "end_idx": 21, "type": "教育背景"}]}, {"text": "面试时，他展示了同济大学建筑学专业的本科学历证书。", "label": [{"entity": "同济大学建筑学专业的本科学历证书", "start_idx": 11, "end_idx": 30, "type": "教育背景"}]}, {"text": "这份工作要求应聘者具有麻省理工学院物理学专业的教育背景。", "label": [{"entity": "麻省理工学院物理学专业", "start_idx": 15, "end_idx": 23, "type": "教育背景"}]}, {"text": "他的教育背景是北京大学计算机科学与技术专业的博士学位。", "label": [{"entity": "北京大学计算机科学与技术专业的博士学位", "start_idx": 7, "end_idx": 23, "type": "教育背景"}]}, {"text": "她拥有清华大学电子工程专业的学士学位。", "label": [{"entity": "清华大学", "start_idx": 5, "end_idx": 7, "type": "教育背景"}, {"entity": "电子工程专业", "start_idx": 8, "end_idx": 11, "type": "教育背景"}, {"entity": "学士学位", "start_idx": 12, "end_idx": 14, "type": "教育背景"}]}, {"text": "他的教育背景是上海交通大学工商管理专业的硕士学位。", "label": [{"entity": "上海交通大学工商管理专业的硕士学位", "start_idx": 9, "end_idx": 28, "type": "教育背景"}]}, {"text": "她毕业于复旦大学新闻传播学专业的本科。", "label": [{"entity": "复旦大学", "start_idx": 6, "end_idx": 9, "type": "教育背景"}, {"entity": "新闻传播学专业", "start_idx": 10, "end_idx": 16, "type": "教育背景"}, {"entity": "本科", "start_idx": 17, "end_idx": 19, "type": "教育背景"}]}, {"text": "他的教育背景是浙江大学临床医学专业的博士学位。", "label": [{"entity": "浙江大学临床医学专业的博士学位", "start_idx": 8, "end_idx": 23, "type": "教育背景"}]}, {"text": "她拥有南京大学中国语言文学专业的硕士学位。", "label": [{"entity": "南京大学中国语言文学专业", "start_idx": 5, "end_idx": 15, "type": "教育背景"}]}, {"text": "他的教育背景是武汉大学国际经济与贸易专业的学士学位。", "label": [{"entity": "武汉大学国际经济与贸易专业的学士学位", "start_idx": 7, "end_idx": 28, "type": "教育背景"}]}], "婚姻状况": [{"text": "张先生目前是已婚状态，已经和妻子结婚五年了。", "label": [{"entity": "已婚", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "李女士的婚姻状况为离异，带着孩子独自生活。", "label": [{"entity": "离异", "start_idx": 7, "end_idx": 9, "type": "婚姻状况"}]}, {"text": "王总目前是单身，正在积极寻找合适的伴侣。", "label": [{"entity": "单身", "start_idx": 4, "end_idx": 6, "type": "婚姻状况"}]}, {"text": "赵女士的婚姻状况是未婚，专注于事业发展。", "label": [{"entity": "未婚", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "刘先生的婚姻状况为丧偶，妻子三年前因病去世。", "label": [{"entity": "丧偶", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "陈小姐目前是同居状态，和男友已经一起生活两年。", "label": [{"entity": "同居状态", "start_idx": 5, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "杨女士的婚姻状况为分居，和丈夫因工作原因两地分居。", "label": [{"entity": "分居", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "周先生的婚姻状况是已婚，育有一对双胞胎。", "label": [{"entity": "已婚", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "吴女士目前是离婚状态，正在办理财产分割手续。", "label": [{"entity": "离婚状态", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "郑先生的婚姻状况为未婚，父母催促他尽快结婚。", "label": [{"entity": "未婚", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "小张的婚姻状况是未婚，他今年刚满30岁。", "label": [{"entity": "未婚", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "李女士的婚姻状况为已婚，她与丈夫育有两个孩子。", "label": [{"entity": "已婚", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "王先生的婚姻状况是离异，他正在寻找新的伴侣。", "label": [{"entity": "离异", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "赵女士的婚姻状况为丧偶，她独自抚养着年幼的女儿。", "label": [{"entity": "丧偶", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "陈先生目前是单身，他的婚姻状况显示为未婚。", "label": [{"entity": "单身", "start_idx": 4, "end_idx": 6, "type": "婚姻状况"}, {"entity": "未婚", "start_idx": 12, "end_idx": 14, "type": "婚姻状况"}]}, {"text": "刘女士的婚姻状况是已婚，她和丈夫经营着一家小店。", "label": [{"entity": "已婚", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "张先生的婚姻状况为离异，他独自居住在城市的一角。", "label": [{"entity": "离异", "start_idx": 4, "end_idx": 6, "type": "婚姻状况"}]}, {"text": "孙女士的婚姻状况是未婚，她专注于自己的事业。", "label": [{"entity": "未婚", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "周先生的婚姻状况为已婚，他和妻子共同生活了十年。", "label": [{"entity": "已婚", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "杨女士的婚姻状况是丧偶，她正在适应新的生活状态。", "label": [{"entity": "丧偶", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}], "政治倾向": [{"text": "他明确表示支持中国共产党的领导。", "label": [{"entity": "中国共产党", "start_idx": 9, "end_idx": 14, "type": "政治倾向"}]}, {"text": "这位民主党人强烈反对共和党的移民政策。", "label": [{"entity": "民主党人", "start_idx": 3, "end_idx": 6, "type": "政治倾向"}, {"entity": "共和党", "start_idx": 10, "end_idx": 13, "type": "政治倾向"}]}, {"text": "工党政府在上次选举中获得了压倒性胜利。", "label": [{"entity": "工党", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "工党政府", "start_idx": 0, "end_idx": 4, "type": "政治倾向"}]}, {"text": "自由党在环保议题上持进步主义立场。", "label": [{"entity": "自由党", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}, {"entity": "进步主义", "start_idx": 8, "end_idx": 10, "type": "政治倾向"}]}, {"text": "保守党议员公开批评了社会主义的经济模式。", "label": [{"entity": "社会主义", "start_idx": 14, "end_idx": 18, "type": "政治倾向"}]}, {"text": "社会民主党在福利政策上表现得尤为积极。", "label": [{"entity": "社会民主党", "start_idx": 0, "end_idx": 5, "type": "政治倾向"}]}, {"text": "绿党成员坚持推动可持续发展议程。", "label": [{"entity": "绿党", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}]}, {"text": "独立候选人以无党派身份参与了市政选举。", "label": [{"entity": "无党派", "start_idx": 7, "end_idx": 9, "type": "政治倾向"}]}, {"text": "共和党支持者主张减税和放松管制。", "label": [{"entity": "共和党支持者", "start_idx": 0, "end_idx": 5, "type": "政治倾向"}, {"entity": "减税", "start_idx": 7, "end_idx": 9, "type": "政治倾向"}, {"entity": "放松管制", "start_idx": 10, "end_idx": 13, "type": "政治倾向"}]}, {"text": "社会党领导人呼吁加强劳动权益保护。", "label": [{"entity": "社会党", "start_idx": 0, "end_idx": 2, "type": "政治倾向"}]}, {"text": "这位支持中国特色社会主义的学者在会上发表了重要演讲。", "label": [{"entity": "中国特色社会主义", "start_idx": 10, "end_idx": 18, "type": "政治倾向"}]}, {"text": "他明确表示自己是一名坚定的自由主义者，反对任何形式的集权。", "label": [{"entity": "自由主义者", "start_idx": 14, "end_idx": 19, "type": "政治倾向"}]}, {"text": "作为无党派人士，她一直倡导民主社会主义的理念。", "label": [{"entity": "无党派人士", "start_idx": 6, "end_idx": 11, "type": "政治倾向"}, {"entity": "民主社会主义", "start_idx": 22, "end_idx": 29, "type": "政治倾向"}]}, {"text": "这位民族主义者强烈反对任何形式的国际干预。", "label": [{"entity": "民族主义者", "start_idx": 3, "end_idx": 7, "type": "政治倾向"}]}, {"text": "他公开表示自己是共产主义的支持者，并为此奋斗终身。", "label": [{"entity": "共产主义的支持者", "start_idx": 7, "end_idx": 17, "type": "政治倾向"}]}, {"text": "作为保守主义者，他坚决维护传统价值观和社会秩序。", "label": [{"entity": "保守主义者", "start_idx": 5, "end_idx": 8, "type": "政治倾向"}]}, {"text": "这位进步主义者主张通过改革推动社会公平和正义。", "label": [{"entity": "进步主义者", "start_idx": 3, "end_idx": 7, "type": "政治倾向"}]}, {"text": "他明确表示自己是社会民主主义者，支持福利国家政策。", "label": [{"entity": "社会民主主义者", "start_idx": 8, "end_idx": 16, "type": "政治倾向"}, {"entity": "福利国家政策", "start_idx": 20, "end_idx": 27, "type": "政治倾向"}]}, {"text": "作为绿党成员，她致力于推动环保主义和可持续发展。", "label": [{"entity": "绿党", "start_idx": 7, "end_idx": 9, "type": "政治倾向"}, {"entity": "环保主义", "start_idx": 15, "end_idx": 18, "type": "政治倾向"}, {"entity": "可持续发展", "start_idx": 19, "end_idx": 24, "type": "政治倾向"}]}, {"text": "这位社会主义者认为公有制是解决经济不平等的关键。", "label": [{"entity": "社会主义者", "start_idx": 3, "end_idx": 7, "type": "政治倾向"}]}], "工资数额": [{"text": "他的月薪是8500元，比去年涨了不少。", "label": [{"entity": "8500元", "start_idx": 6, "end_idx": 9, "type": "工资数额"}]}, {"text": "她刚入职时拿到的底薪是5000元。", "label": [{"entity": "5000元", "start_idx": 14, "end_idx": 17, "type": "工资数额"}]}, {"text": "这家公司的起薪标准是6000元。", "label": [{"entity": "6000元", "start_idx": 10, "end_idx": 12, "type": "工资数额"}]}, {"text": "小张的年终奖金拿到了15000元。", "label": [{"entity": "15000元", "start_idx": 9, "end_idx": 12, "type": "工资数额"}]}, {"text": "老李的退休工资每月有3000元。", "label": [{"entity": "3000元", "start_idx": 8, "end_idx": 10, "type": "工资数额"}]}, {"text": "这份兼职的时薪是40元，还算不错。", "label": [{"entity": "40元", "start_idx": 10, "end_idx": 12, "type": "工资数额"}]}, {"text": "他这周的加班费共计1200元。", "label": [{"entity": "1200元", "start_idx": 10, "end_idx": 12, "type": "工资数额"}]}, {"text": "她的税后工资是7200元，扣除社保后。", "label": [{"entity": "7200元", "start_idx": 7, "end_idx": 10, "type": "工资数额"}]}, {"text": "他的月薪是8500元，比上一年涨了不少。", "label": [{"entity": "8500元", "start_idx": 7, "end_idx": 10, "type": "工资数额"}]}, {"text": "这个职位的起薪是5000元，福利待遇还不错。", "label": [{"entity": "5000元", "start_idx": 8, "end_idx": 11, "type": "工资数额"}]}, {"text": "她最近拿到了15000元的年终奖金，非常开心。", "label": [{"entity": "15000元", "start_idx": 7, "end_idx": 10, "type": "工资数额"}]}, {"text": "这家公司的底薪是6000元，绩效奖金另算。", "label": [{"entity": "6000元", "start_idx": 7, "end_idx": 10, "type": "工资数额"}]}, {"text": "他的时薪是35元，每周工作40小时。", "label": [{"entity": "35元", "start_idx": 7, "end_idx": 9, "type": "工资数额"}, {"entity": "40小时", "start_idx": 14, "end_idx": 17, "type": "工资数额"}]}, {"text": "这份兼职的日薪是200元，适合大学生做。", "label": [{"entity": "200元", "start_idx": 9, "end_idx": 11, "type": "工资数额"}]}, {"text": "他的年薪达到了30万元，是公司的技术骨干。", "label": [{"entity": "30万元", "start_idx": 7, "end_idx": 10, "type": "工资数额"}]}, {"text": "这个项目的奖金是5万元，完成难度很大。", "label": [{"entity": "5万元", "start_idx": 8, "end_idx": 10, "type": "工资数额"}]}, {"text": "她的月薪加上提成，总共拿到了12000元。", "label": [{"entity": "12000元", "start_idx": 14, "end_idx": 17, "type": "工资数额"}]}, {"text": "他的工资卡里每月会打入8000元，准时到账。", "label": [{"entity": "8000元", "start_idx": 15, "end_idx": 18, "type": "工资数额"}]}, {"text": "他的月薪是8500元，比上家公司高了不少。", "label": [{"entity": "8500元", "start_idx": 7, "end_idx": 10, "type": "工资数额"}]}, {"text": "这个职位的年薪大约是12万元，福利待遇也不错。", "label": [{"entity": "12万元", "start_idx": 11, "end_idx": 14, "type": "工资数额"}]}], "家庭成员": [{"text": "妈妈李华今天去超市买了新鲜的蔬菜。", "label": [{"entity": "妈妈", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "爸爸王强周末经常带我和妹妹去公园玩。", "label": [{"entity": "爸爸", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "我", "start_idx": 7, "end_idx": 8, "type": "家庭成员"}, {"entity": "妹妹", "start_idx": 9, "end_idx": 11, "type": "家庭成员"}]}, {"text": "奶奶张秀英做的红烧肉是我最爱吃的菜。", "label": [{"entity": "奶奶", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "哥哥刘明明天要去北京参加重要的会议。", "label": [{"entity": "哥哥", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "姐姐陈琳大学毕业后就在上海找到了工作。", "label": [{"entity": "姐姐", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "叔叔赵刚每年春节都会回老家看望亲戚。", "label": [{"entity": "叔叔", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "亲戚", "start_idx": 13, "end_idx": 15, "type": "家庭成员"}]}, {"text": "姑姑孙丽上周给我寄来了一箱家乡的特产。", "label": [{"entity": "姑姑", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "我", "start_idx": 6, "end_idx": 7, "type": "家庭成员"}]}, {"text": "小姨周红今天开车送我去火车站。", "label": [{"entity": "小姨", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "大伯吴军退休后每天都去社区打太极拳。", "label": [{"entity": "大伯", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "表哥黄杰昨天晚上给我打来电话聊了很久。", "label": [{"entity": "表哥", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "我的爸爸张伟今天带我去了公园玩。", "label": [{"entity": "爸爸", "start_idx": 2, "end_idx": 3, "type": "家庭成员"}, {"entity": "爸爸", "start_idx": 2, "end_idx": 3, "type": "家庭成员"}]}, {"text": "奶奶王芳每天早上都会去跳广场舞。", "label": [{"entity": "奶奶", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "舅舅赵明昨天给我寄了生日礼物。", "label": [{"entity": "舅舅", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "妈妈今天带我和弟弟去公园玩了一整天。", "label": [{"entity": "妈妈", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "我", "start_idx": 4, "end_idx": 5, "type": "家庭成员"}, {"entity": "弟弟", "start_idx": 6, "end_idx": 8, "type": "家庭成员"}]}, {"text": "爸爸上周出差回来，给哥哥带了一件礼物。", "label": [{"entity": "爸爸", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "哥哥", "start_idx": 10, "end_idx": 12, "type": "家庭成员"}]}, {"text": "妹妹特别喜欢和奶奶一起看动画片。", "label": [{"entity": "妹妹", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "奶奶", "start_idx": 6, "end_idx": 8, "type": "家庭成员"}]}, {"text": "爷爷每天早上都会陪孙子去晨练。", "label": [{"entity": "爷爷", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "孙子", "start_idx": 9, "end_idx": 11, "type": "家庭成员"}]}, {"text": "姐姐和我的生日是同一天，我们经常一起庆祝。", "label": [{"entity": "姐姐", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "叔叔周末来家里吃饭，带来了很多水果。", "label": [{"entity": "叔叔", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}, {"text": "外婆做的红烧肉是全家人都爱吃的菜。", "label": [{"entity": "外婆", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}]}], "投资产品": [{"text": "我最近购买了一份招商银行的招商中证白酒指数基金。", "label": [{"entity": "招商银行的招商中证白酒指数基金", "start_idx": 12, "end_idx": 34, "type": "投资产品"}]}, {"text": "客户选择了华夏基金旗下的华夏恒生ETF联接基金进行投资。", "label": [{"entity": "华夏基金", "start_idx": 4, "end_idx": 7, "type": "投资产品"}, {"entity": "华夏恒生ETF联接基金", "start_idx": 9, "end_idx": 16, "type": "投资产品"}]}, {"text": "他长期持有茅台股票，作为其投资组合的核心资产。", "label": [{"entity": "茅台股票", "start_idx": 8, "end_idx": 11, "type": "投资产品"}]}, {"text": "她通过支付宝购买了余额宝，用于日常资金的灵活管理。", "label": [{"entity": "支付宝", "start_idx": 4, "end_idx": 6, "type": "投资产品"}, {"entity": "余额宝", "start_idx": 8, "end_idx": 10, "type": "投资产品"}]}, {"text": "我们公司为员工提供了平安养老险的年金保险产品。", "label": [{"entity": "平安养老险", "start_idx": 11, "end_idx": 15, "type": "投资产品"}, {"entity": "年金保险产品", "start_idx": 16, "end_idx": 22, "type": "投资产品"}]}, {"text": "投资者可以选择购买国债逆回购，以获取短期稳定收益。", "label": [{"entity": "国债逆回购", "start_idx": 10, "end_idx": 13, "type": "投资产品"}]}, {"text": "我建议他配置一些黄金ETF，以对冲市场风险。", "label": [{"entity": "黄金ETF", "start_idx": 11, "end_idx": 14, "type": "投资产品"}]}, {"text": "她通过招商证券开通了科创板股票的交易权限。", "label": [{"entity": "招商证券", "start_idx": 6, "end_idx": 10, "type": "投资产品"}, {"entity": "科创板股票", "start_idx": 14, "end_idx": 18, "type": "投资产品"}]}, {"text": "客户选择了招商银行的摩羯智投，进行智能资产配置。", "label": [{"entity": "招商银行", "start_idx": 5, "end_idx": 7, "type": "投资产品"}, {"entity": "摩羯智投", "start_idx": 8, "end_idx": 10, "type": "投资产品"}]}, {"text": "他通过东方财富购买了中证500ETF，分散投资风险。", "label": [{"entity": "东方财富", "start_idx": 5, "end_idx": 8, "type": "投资产品"}, {"entity": "中证500ETF", "start_idx": 12, "end_idx": 16, "type": "投资产品"}]}, {"text": "购买华夏基金旗下的华夏回报混合基金是我今年的首要选择。", "label": [{"entity": "华夏基金", "start_idx": 4, "end_idx": 7, "type": "投资产品"}, {"entity": "华夏回报混合基金", "start_idx": 9, "end_idx": 16, "type": "投资产品"}]}, {"text": "我决定将部分资金投入招商银行的快e理财系列。", "label": [{"entity": "招商银行", "start_idx": 9, "end_idx": 12, "type": "投资产品"}, {"entity": "快e理财系列", "start_idx": 13, "end_idx": 18, "type": "投资产品"}]}, {"text": "通过支付宝购买了蚂蚁财富的余额宝货币基金。", "label": [{"entity": "蚂蚁财富", "start_idx": 5, "end_idx": 7, "type": "投资产品"}, {"entity": "余额宝货币基金", "start_idx": 8, "end_idx": 12, "type": "投资产品"}]}, {"text": "客户经理推荐了中信证券的融通债券基金作为稳健投资。", "label": [{"entity": "融通债券基金", "start_idx": 9, "end_idx": 13, "type": "投资产品"}]}, {"text": "他的退休金主要配置在泰达宏利养老目标日期基金。", "label": [{"entity": "泰达宏利养老目标日期基金", "start_idx": 16, "end_idx": 25, "type": "投资产品"}]}, {"text": "我对比了不同平台的招商银行朝朝宝，最终选择了余额宝。", "label": [{"entity": "招商银行朝朝宝", "start_idx": 7, "end_idx": 14, "type": "投资产品"}, {"entity": "余额宝", "start_idx": 22, "end_idx": 25, "type": "投资产品"}]}, {"text": "通过招商银行APP申购了招商中证白酒指数基金。", "label": [{"entity": "招商银行APP", "start_idx": 2, "end_idx": 8, "type": "投资产品"}, {"entity": "招商中证白酒指数基金", "start_idx": 12, "end_idx": 21, "type": "投资产品"}]}, {"text": "客户咨询了广发基金的稳健收益债券型基金的投资策略。", "label": [{"entity": "广发基金", "start_idx": 5, "end_idx": 8, "type": "投资产品"}, {"entity": "稳健收益债券型基金", "start_idx": 9, "end_idx": 18, "type": "投资产品"}]}, {"text": "我们讨论了华夏基金的沪深300ETF联接基金的风险收益。", "label": [{"entity": "华夏基金", "start_idx": 5, "end_idx": 8, "type": "投资产品"}, {"entity": "沪深300ETF联接基金", "start_idx": 9, "end_idx": 19, "type": "投资产品"}]}, {"text": "她将闲置资金转入了招商银行的朝朝盈货币市场基金。", "label": [{"entity": "招商银行", "start_idx": 10, "end_idx": 12, "type": "投资产品"}, {"entity": "朝朝盈货币市场基金", "start_idx": 13, "end_idx": 19, "type": "投资产品"}]}], "税务记录": [{"text": "请提供具体的税务记录类型实体示例，例如“个人所得税纳税申报表”、“增值税专用发票”等，以便我为您生成符合要求的句子。", "label": [{"entity": "个人所得税纳税申报表", "start_idx": 49, "end_idx": 67, "type": "税务记录"}, {"entity": "增值税专用发票", "start_idx": 69, "end_idx": 83, "type": "税务记录"}]}, {"text": "我们需要提交2021年度企业所得税年度纳税申报表。", "label": [{"entity": "2021年度企业所得税年度纳税申报表", "start_idx": 6, "end_idx": 18, "type": "税务记录"}]}, {"text": "客户的个人所得税完税证明显示2022年已缴纳税款。", "label": [{"entity": "客户的个人所得税完税证明", "start_idx": 0, "end_idx": 9, "type": "税务记录"}, {"entity": "2022年", "start_idx": 10, "end_idx": 13, "type": "税务记录"}, {"entity": "已缴纳税款", "start_idx": 14, "end_idx": 19, "type": "税务记录"}]}, {"text": "请提供2020年度增值税专用发票抵扣联作为税务记录。", "label": [{"entity": "2020年度增值税专用发票抵扣联", "start_idx": 4, "end_idx": 13, "type": "税务记录"}]}, {"text": "税务部门要求我们补交2019年度印花税纳税凭证。", "label": [{"entity": "2019年度印花税纳税凭证", "start_idx": 11, "end_idx": 23, "type": "税务记录"}]}, {"text": "该公司的土地增值税清算表已经通过税务局审核。", "label": [{"entity": "土地增值税清算表", "start_idx": 7, "end_idx": 16, "type": "税务记录"}]}, {"text": "我们保存了2018年度企业所得税汇算清缴报告作为税务档案。", "label": [{"entity": "2018年度企业所得税汇算清缴报告", "start_idx": 5, "end_idx": 11, "type": "税务记录"}]}, {"text": "请核对2017年度契税缴纳凭证上的金额是否准确。", "label": [{"entity": "2017年度契税缴纳凭证", "start_idx": 3, "end_idx": 10, "type": "税务记录"}, {"entity": "金额", "start_idx": 13, "end_idx": 15, "type": "税务记录"}]}, {"text": "这份2016年度房产税纳税申报表需要重新填写。", "label": [{"entity": "2016年度房产税纳税申报表", "start_idx": 2, "end_idx": 12, "type": "税务记录"}]}, {"text": "个人所得税纳税申报表是每个纳税人每年必须提交的文件。", "label": [{"entity": "个人所得税纳税申报表", "start_idx": 0, "end_idx": 7, "type": "税务记录"}]}, {"text": "企业所得税年度汇算清缴报告需要在次年3月31日前完成。", "label": [{"entity": "企业所得税年度汇算清缴报告", "start_idx": 0, "end_idx": 9, "type": "税务记录"}]}, {"text": "增值税专用发票的抵扣联需要妥善保管以备税务检查。", "label": [{"entity": "增值税专用发票的抵扣联", "start_idx": 0, "end_idx": 8, "type": "税务记录"}]}, {"text": "印花税纳税凭证的粘贴位置应在合同左上角显眼处。", "label": [{"entity": "印花税纳税凭证", "start_idx": 0, "end_idx": 6, "type": "税务记录"}]}, {"text": "房产税纳税登记表需在购房后30日内向税务机关申报。", "label": [{"entity": "房产税纳税登记表", "start_idx": 0, "end_idx": 7, "type": "税务记录"}, {"entity": "税务机关", "start_idx": 23, "end_idx": 27, "type": "税务记录"}]}, {"text": "车辆购置税完税证明是办理车辆登记的必要文件之一。", "label": [{"entity": "车辆购置税完税证明", "start_idx": 0, "end_idx": 7, "type": "税务记录"}]}, {"text": "土地增值税清算表需由具备资质的会计师事务所出具。", "label": [{"entity": "土地增值税清算表", "start_idx": 0, "end_idx": 7, "type": "税务记录"}]}, {"text": "消费税纳税申报表适用于生产销售应税消费品的企业。", "label": [{"entity": "消费税纳税申报表", "start_idx": 0, "end_idx": 7, "type": "税务记录"}, {"entity": "生产销售应税消费品的企业", "start_idx": 17, "end_idx": 29, "type": "税务记录"}]}, {"text": "城市维护建设税的计税依据是实际缴纳的增值税额。", "label": [{"entity": "城市维护建设税", "start_idx": 0, "end_idx": 7, "type": "税务记录"}, {"entity": "增值税额", "start_idx": 17, "end_idx": 20, "type": "税务记录"}]}, {"text": "教育费附加缴款书需在每月15日前完成缴纳手续。", "label": [{"entity": "教育费附加缴款书", "start_idx": 0, "end_idx": 6, "type": "税务记录"}]}, {"text": "请提供您希望包含的具体税务记录类型实体，例如“个人所得税申报表”、“增值税专用发票”等，我将为您生成包含该实体的句子。", "label": [{"entity": "个人所得税申报表", "start_idx": 52, "end_idx": 62, "type": "税务记录"}, {"entity": "增值税专用发票", "start_idx": 64, "end_idx": 77, "type": "税务记录"}]}], "信用记录": [{"text": "他的个人信用报告显示，信用卡还款记录为“按时还款”。", "label": [{"entity": "按时还款", "start_idx": 20, "end_idx": 25, "type": "信用记录"}]}, {"text": "这份企业信用档案中，工商注册信息显示状态为“正常”。", "label": [{"entity": "企业信用档案", "start_idx": 3, "end_idx": 10, "type": "信用记录"}, {"entity": "工商注册信息", "start_idx": 14, "end_idx": 21, "type": "信用记录"}]}, {"text": "在查询征信报告时，发现贷款审批记录为“已通过”。", "label": [{"entity": "贷款审批记录为“已通过”", "start_idx": 10, "end_idx": 25, "type": "信用记录"}]}, {"text": "银行调取的信用记录里，房贷还款状态是“无逾期”。", "label": [{"entity": "信用记录", "start_idx": 5, "end_idx": 8, "type": "信用记录"}]}, {"text": "客户的信用档案显示，纳税记录为“依法纳税”。", "label": [{"entity": "依法纳税", "start_idx": 12, "end_idx": 16, "type": "信用记录"}]}, {"text": "这份信用报告里，水电费缴纳记录是“全部结清”。", "label": [{"entity": "水电费缴纳记录", "start_idx": 7, "end_idx": 14, "type": "信用记录"}, {"entity": "全部结清", "start_idx": 17, "end_idx": 22, "type": "信用记录"}]}, {"text": "查询到的信用记录中，手机话费缴费状态为“正常”。", "label": [{"entity": "信用记录", "start_idx": 2, "end_idx": 6, "type": "信用记录"}]}, {"text": "该用户的信用档案显示，社保缴纳记录是“连续缴纳”。", "label": [{"entity": "社保缴纳记录", "start_idx": 11, "end_idx": 18, "type": "信用记录"}]}, {"text": "征信系统中，查询记录类型为“本人查询”。", "label": [{"entity": "本人查询", "start_idx": 8, "end_idx": 10, "type": "信用记录"}]}, {"text": "他的信用记录中，法院判决信息为“无失信”。", "label": [{"entity": "信用记录", "start_idx": 4, "end_idx": 7, "type": "信用记录"}, {"entity": "法院判决信息", "start_idx": 11, "end_idx": 17, "type": "信用记录"}]}, {"text": "他的个人信用报告显示，最近一次查询记录来自中国人民银行征信中心。", "label": [{"entity": "中国人民银行征信中心", "start_idx": 24, "end_idx": 34, "type": "信用记录"}]}, {"text": "该企业的商业信用评级为BBB-，由穆迪投资者服务公司评定。", "label": [{"entity": "BBB-", "start_idx": 9, "end_idx": 12, "type": "信用记录"}]}, {"text": "小张的信用卡还款记录在招商银行系统中显示为正常。", "label": [{"entity": "信用卡还款记录", "start_idx": 5, "end_idx": 10, "type": "信用记录"}]}, {"text": "根据上海资信有限公司的报告，该笔贷款的五级分类为关注类。", "label": [{"entity": "该笔贷款", "start_idx": 18, "end_idx": 22, "type": "信用记录"}, {"entity": "五级分类", "start_idx": 24, "end_idx": 27, "type": "信用记录"}, {"entity": "关注类", "start_idx": 30, "end_idx": 32, "type": "信用记录"}]}, {"text": "他的芝麻信用分达到了685分，属于良好信用等级。", "label": [{"entity": "685分", "start_idx": 10, "end_idx": 12, "type": "信用记录"}, {"entity": "良好信用等级", "start_idx": 14, "end_idx": 20, "type": "信用记录"}]}, {"text": "该公司的税务信用等级被评为A级，由国家税务总局认证。", "label": [{"entity": "A级", "start_idx": 9, "end_idx": 11, "type": "信用记录"}]}, {"text": "银行调取了他的个人征信报告，其中包含房贷还款记录。", "label": [{"entity": "个人征信报告", "start_idx": 6, "end_idx": 11, "type": "信用记录"}, {"entity": "房贷还款记录", "start_idx": 19, "end_idx": 26, "type": "信用记录"}]}, {"text": "该商户的支付宝信用评分是742分，显示为信用良好。", "label": [{"entity": "742分", "start_idx": 10, "end_idx": 12, "type": "信用记录"}]}, {"text": "他的个人征信报告中，公积金贷款记录显示已结清。", "label": [{"entity": "公积金贷款记录", "start_idx": 11, "end_idx": 18, "type": "信用记录"}]}, {"text": "他的个人信用报告显示，中国人民银行征信中心的信用记录分数为720分。", "label": [{"entity": "720分", "start_idx": 28, "end_idx": 31, "type": "信用记录"}]}], "实体资产": [{"text": "他的书房里摆放着一幅梵高的《向日葵》。", "label": [{"entity": "梵高的《向日葵》", "start_idx": 11, "end_idx": 18, "type": "实体资产"}]}, {"text": "那栋位于曼哈顿的摩天大楼属于一家跨国公司。", "label": [{"entity": "那栋位于曼哈顿的摩天大楼", "start_idx": 0, "end_idx": 9, "type": "实体资产"}]}, {"text": "仓库里堆满了成箱的可口可乐饮料。", "label": [{"entity": "仓库", "start_idx": 0, "end_idx": 2, "type": "实体资产"}, {"entity": "成箱的可口可乐饮料", "start_idx": 6, "end_idx": 12, "type": "实体资产"}]}, {"text": "公司的保险库里有大量黄金储备。", "label": [{"entity": "黄金储备", "start_idx": 10, "end_idx": 13, "type": "实体资产"}]}, {"text": "展览会上展示了一艘二战时期的战舰模型。", "label": [{"entity": "战舰模型", "start_idx": 11, "end_idx": 14, "type": "实体资产"}]}, {"text": "图书馆收藏了大量的第一版《红楼梦》古籍。", "label": [{"entity": "图书馆", "start_idx": 0, "end_idx": 2, "type": "实体资产"}, {"entity": "第一版《红楼梦》古籍", "start_idx": 7, "end_idx": 13, "type": "实体资产"}]}, {"text": "这栋位于上海的别墅现在市值已经超过两千万。", "label": [{"entity": "别墅", "start_idx": 10, "end_idx": 11, "type": "实体资产"}, {"entity": "两千万", "start_idx": 20, "end_idx": 22, "type": "实体资产"}]}, {"text": "他收藏的古董花瓶是清朝时期的青花瓷。", "label": [{"entity": "古董花瓶", "start_idx": 3, "end_idx": 6, "type": "实体资产"}, {"entity": "清朝时期的青花瓷", "start_idx": 7, "end_idx": 15, "type": "实体资产"}]}, {"text": "公司最近购买了一批崭新的福特F-150卡车。", "label": [{"entity": "福特F-150卡车", "start_idx": 11, "end_idx": 15, "type": "实体资产"}]}, {"text": "那座历史悠久的石拱桥是当地的标志性建筑。", "label": [{"entity": "石拱桥", "start_idx": 6, "end_idx": 9, "type": "实体资产"}, {"entity": "标志性建筑", "start_idx": 15, "end_idx": 19, "type": "实体资产"}]}, {"text": "博物馆里展出的明代青铜鼎非常珍贵。", "label": [{"entity": "明代青铜鼎", "start_idx": 7, "end_idx": 10, "type": "实体资产"}]}, {"text": "仓库里堆满了未售出的联想ThinkPad笔记本电脑。", "label": [{"entity": "仓库", "start_idx": 0, "end_idx": 2, "type": "实体资产"}, {"entity": "联想ThinkPad笔记本电脑", "start_idx": 6, "end_idx": 12, "type": "实体资产"}]}, {"text": "这座金矿的年产量约为五百公斤纯金。", "label": [{"entity": "金矿", "start_idx": 3, "end_idx": 5, "type": "实体资产"}]}, {"text": "他继承的家族产业包括一片广袤的橡胶种植园。", "label": [{"entity": "家族产业", "start_idx": 3, "end_idx": 7, "type": "实体资产"}, {"entity": "橡胶种植园", "start_idx": 11, "end_idx": 17, "type": "实体资产"}]}, {"text": "那艘停在港口的超级游艇属于一位知名企业家。", "label": [{"entity": "超级游艇", "start_idx": 7, "end_idx": 10, "type": "实体资产"}]}, {"text": "这家公司的主要资产是一栋位于市中心的写字楼。", "label": [{"entity": "写字楼", "start_idx": 21, "end_idx": 23, "type": "实体资产"}]}, {"text": "李先生投资了一块位于海南的三亚湾海景别墅。", "label": [{"entity": "位于海南的三亚湾海景别墅", "start_idx": 6, "end_idx": 19, "type": "实体资产"}]}, {"text": "博物馆收藏了大量青铜器，其中最著名的是司母戊鼎。", "label": [{"entity": "青铜器", "start_idx": 6, "end_idx": 8, "type": "实体资产"}, {"entity": "司母戊鼎", "start_idx": 19, "end_idx": 22, "type": "实体资产"}]}, {"text": "工厂的设备清单里有一台德国进口的西门子数控机床。", "label": [{"entity": "西门子数控机床", "start_idx": 15, "end_idx": 23, "type": "实体资产"}]}, {"text": "银行冻结了那辆法拉利跑车作为抵押资产。", "label": [{"entity": "法拉利跑车", "start_idx": 8, "end_idx": 10, "type": "实体资产"}]}], "疾病": [{"text": "这位患者被诊断患有糖尿病，需要长期控制血糖。", "label": [{"entity": "糖尿病", "start_idx": 8, "end_idx": 10, "type": "疾病"}]}, {"text": "他的高血压病情已经持续多年，最近有所加重。", "label": [{"entity": "高血压", "start_idx": 3, "end_idx": 5, "type": "疾病"}]}, {"text": "医生建议她进行基因检测，以排除遗传性乳腺癌的风险。", "label": [{"entity": "遗传性乳腺癌", "start_idx": 23, "end_idx": 28, "type": "疾病"}]}, {"text": "肺炎是导致他近期发热和咳嗽的主要原因。", "label": [{"entity": "肺炎", "start_idx": 0, "end_idx": 2, "type": "疾病"}]}, {"text": "她因为慢性支气管炎，经常感到呼吸困难。", "label": [{"entity": "慢性支气管炎", "start_idx": 4, "end_idx": 8, "type": "疾病"}]}, {"text": "这位老人患有阿尔茨海默病，记忆力明显下降。", "label": [{"entity": "阿尔茨海默病", "start_idx": 8, "end_idx": 13, "type": "疾病"}]}, {"text": "他的皮肤上出现了荨麻疹，可能是过敏引起的。", "label": [{"entity": "荨麻疹", "start_idx": 7, "end_idx": 9, "type": "疾病"}]}, {"text": "经过检查，她被确诊为系统性红斑狼疮。", "label": [{"entity": "系统性红斑狼疮", "start_idx": 10, "end_idx": 17, "type": "疾病"}]}, {"text": "急性阑尾炎需要及时手术治疗，否则会有危险。", "label": [{"entity": "急性阑尾炎", "start_idx": 0, "end_idx": 4, "type": "疾病"}]}, {"text": "他的胃溃疡病情反复发作，影响了正常饮食。", "label": [{"entity": "胃溃疡", "start_idx": 3, "end_idx": 5, "type": "疾病"}]}, {"text": "糖尿病患者的血糖控制需要长期监测和合理饮食。", "label": [{"entity": "糖尿病", "start_idx": 0, "end_idx": 2, "type": "疾病"}]}, {"text": "高血压是导致心脑血管疾病的重要危险因素之一。", "label": [{"entity": "高血压", "start_idx": 0, "end_idx": 2, "type": "疾病"}, {"entity": "心脑血管疾病", "start_idx": 8, "end_idx": 12, "type": "疾病"}]}, {"text": "肺炎患者需要及时就医，以免延误最佳治疗时机。", "label": [{"entity": "肺炎", "start_idx": 0, "end_idx": 2, "type": "疾病"}]}], "药物": [{"text": "阿司匹林是一种常用的非处方解热镇痛药。", "label": [{"entity": "阿司匹林", "start_idx": 0, "end_idx": 3, "type": "药物"}]}, {"text": "二甲双胍是治疗2型糖尿病的一线口服降糖药。", "label": [{"entity": "二甲双胍", "start_idx": 0, "end_idx": 3, "type": "药物"}]}, {"text": "头孢克肟属于第三代头孢菌素类抗生素。", "label": [{"entity": "头孢克肟", "start_idx": 0, "end_idx": 3, "type": "药物"}, {"entity": "第三代头孢菌素类抗生素", "start_idx": 6, "end_idx": 11, "type": "药物"}]}, {"text": "甲硝唑可用于治疗细菌性阴道炎和滴虫病。", "label": [{"entity": "甲硝唑", "start_idx": 0, "end_idx": 2, "type": "药物"}]}, {"text": "氯雷他定是常用的抗组胺药，用于缓解过敏症状。", "label": [{"entity": "氯雷他定", "start_idx": 0, "end_idx": 3, "type": "药物"}]}, {"text": "奥美拉唑属于质子泵抑制剂，用于治疗胃溃疡。", "label": [{"entity": "奥美拉唑", "start_idx": 0, "end_idx": 3, "type": "药物"}, {"entity": "质子泵抑制剂", "start_idx": 7, "end_idx": 13, "type": "药物"}]}, {"text": "阿托伐他汀是常用的降脂药，可降低低密度脂蛋白。", "label": [{"entity": "阿托伐他汀", "start_idx": 0, "end_idx": 4, "type": "药物"}, {"entity": "降脂药", "start_idx": 8, "end_idx": 10, "type": "药物"}]}, {"text": "布洛芬是一种非甾体抗炎药，常用于缓解疼痛。", "label": [{"entity": "布洛芬", "start_idx": 0, "end_idx": 2, "type": "药物"}]}, {"text": "利巴韦林是抗病毒药物，可用于治疗呼吸道合胞病毒感染。", "label": [{"entity": "利巴韦林", "start_idx": 0, "end_idx": 3, "type": "药物"}]}, {"text": "苯妥英钠是治疗癫痫大发作的一线抗癫痫药。", "label": [{"entity": "苯妥英钠", "start_idx": 0, "end_idx": 4, "type": "药物"}]}, {"text": "医生给我开了阿莫西林胶囊，用于治疗我的咽喉炎。", "label": [{"entity": "阿莫西林胶囊", "start_idx": 6, "end_idx": 10, "type": "药物"}]}, {"text": "高血压患者需要长期服用硝苯地平缓释片来控制血压。", "label": [{"entity": "硝苯地平缓释片", "start_idx": 12, "end_idx": 17, "type": "药物"}]}], "交易信息": [{"text": "张先生通过招商银行信用卡支付了订单号20231101001的货款。", "label": [{"entity": "招商银行信用卡", "start_idx": 7, "end_idx": 15, "type": "交易信息"}, {"entity": "订单号20231101001", "start_idx": 16, "end_idx": 28, "type": "交易信息"}, {"entity": "货款", "start_idx": 29, "end_idx": 31, "type": "交易信息"}]}, {"text": "李女士在京东商城使用支付宝账号123456789支付了订单金额299.50元。", "label": [{"entity": "支付宝账号123456789", "start_idx": 9, "end_idx": 20, "type": "交易信息"}, {"entity": "299.50元", "start_idx": 28, "end_idx": 32, "type": "交易信息"}]}, {"text": "王总通过微信转账给供应商，交易流水号为W2023102500123。", "label": [{"entity": "微信转账", "start_idx": 5, "end_idx": 8, "type": "交易信息"}, {"entity": "W2023102500123", "start_idx": 17, "end_idx": 26, "type": "交易信息"}]}, {"text": "刘小姐使用中国银行储蓄卡尾号8888完成了订单编号JD20231026的支付。", "label": [{"entity": "订单编号JD20231026", "start_idx": 22, "end_idx": 37, "type": "交易信息"}]}, {"text": "赵先生在淘宝网确认收货后，系统自动退还了订单号TM20231027005的尾款25.8元。", "label": [{"entity": "订单号TM20231027005", "start_idx": 26, "end_idx": 38, "type": "交易信息"}, {"entity": "尾款25.8元", "start_idx": 40, "end_idx": 46, "type": "交易信息"}]}, {"text": "陈女士通过PayPal账户支付了国际订单金额500美元，交易ID为PP20231028001。", "label": [{"entity": "PayPal账户", "start_idx": 6, "end_idx": 11, "type": "交易信息"}, {"entity": "国际订单", "start_idx": 13, "end_idx": 18, "type": "交易信息"}, {"entity": "500美元", "start_idx": 21, "end_idx": 25, "type": "交易信息"}, {"entity": "交易ID", "start_idx": 29, "end_idx": 33, "type": "交易信息"}, {"entity": "PP20231028001", "start_idx": 34, "end_idx": 44, "type": "交易信息"}]}, {"text": "孙先生在美团外卖使用优惠券后，实际支付了订单编号ME20231029002的金额68.5元。", "label": [{"entity": "优惠券", "start_idx": 11, "end_idx": 13, "type": "交易信息"}, {"entity": "订单编号ME20231029002", "start_idx": 28, "end_idx": 43, "type": "交易信息"}, {"entity": "68.5元", "start_idx": 45, "end_idx": 48, "type": "交易信息"}]}, {"text": "吴先生在拼多多平台使用优惠券抵扣后，支付了订单号PPD20231030003的金额89.9元。", "label": [{"entity": "优惠券", "start_idx": 14, "end_idx": 17, "type": "交易信息"}, {"entity": "订单号PPD20231030003", "start_idx": 24, "end_idx": 38, "type": "交易信息"}, {"entity": "89.9元", "start_idx": 42, "end_idx": 46, "type": "交易信息"}]}, {"text": "郑女士通过银联云闪付支付了订单编号YH20231031004的货款，金额为456.75元。", "label": [{"entity": "订单编号YH20231031004", "start_idx": 18, "end_idx": 27, "type": "交易信息"}, {"entity": "货款", "start_idx": 28, "end_idx": 30, "type": "交易信息"}, {"entity": "456.75元", "start_idx": 37, "end_idx": 41, "type": "交易信息"}]}, {"text": "张先生通过工商银行ATM机完成了1000元的转账操作。", "label": [{"entity": "1000元", "start_idx": 14, "end_idx": 17, "type": "交易信息"}]}, {"text": "李女士在京东商城下单购买了一件价格为299元的连衣裙。", "label": [{"entity": "京东商城", "start_idx": 5, "end_idx": 8, "type": "交易信息"}, {"entity": "299元", "start_idx": 18, "end_idx": 21, "type": "交易信息"}, {"entity": "连衣裙", "start_idx": 22, "end_idx": 25, "type": "交易信息"}]}, {"text": "王老板向供应商支付了50000元货款，交易日期为2023年10月15日。", "label": [{"entity": "50000元", "start_idx": 6, "end_idx": 9, "type": "交易信息"}, {"entity": "货款", "start_idx": 9, "end_idx": 11, "type": "交易信息"}, {"entity": "2023年10月15日", "start_idx": 15, "end_idx": 21, "type": "交易信息"}]}, {"text": "刘小姐使用支付宝向朋友转账了200元，备注为“聚餐费用”。", "label": [{"entity": "支付宝", "start_idx": 5, "end_idx": 7, "type": "交易信息"}, {"entity": "转账", "start_idx": 8, "end_idx": 10, "type": "交易信息"}, {"entity": "200元", "start_idx": 15, "end_idx": 18, "type": "交易信息"}, {"entity": "聚餐费用", "start_idx": 27, "end_idx": 31, "type": "交易信息"}]}, {"text": "陈先生在星巴克消费了38元，支付方式为微信支付。", "label": [{"entity": "38元", "start_idx": 8, "end_idx": 10, "type": "交易信息"}, {"entity": "微信支付", "start_idx": 14, "end_idx": 17, "type": "交易信息"}]}, {"text": "孙先生在淘宝网购买了一双价值699元的运动鞋。", "label": [{"entity": "淘宝网", "start_idx": 5, "end_idx": 8, "type": "交易信息"}, {"entity": "一双", "start_idx": 10, "end_idx": 12, "type": "交易信息"}, {"entity": "价值699元", "start_idx": 15, "end_idx": 19, "type": "交易信息"}, {"entity": "运动鞋", "start_idx": 20, "end_idx": 23, "type": "交易信息"}]}, {"text": "杨女士通过银联POS机刷卡支付了458元购买家电。", "label": [{"entity": "银联POS机", "start_idx": 6, "end_idx": 10, "type": "交易信息"}, {"entity": "刷卡支付", "start_idx": 11, "end_idx": 14, "type": "交易信息"}, {"entity": "458元", "start_idx": 15, "end_idx": 18, "type": "交易信息"}, {"entity": "家电", "start_idx": 22, "end_idx": 24, "type": "交易信息"}]}, {"text": "周先生在美团外卖点餐，支付了35元的外卖费。", "label": [{"entity": "35元", "start_idx": 14, "end_idx": 16, "type": "交易信息"}]}, {"text": "吴小姐通过PayPal向海外卖家支付了150美元的订单款。", "label": [{"entity": "PayPal", "start_idx": 7, "end_idx": 13, "type": "交易信息"}, {"entity": "海外卖家", "start_idx": 14, "end_idx": 18, "type": "交易信息"}, {"entity": "150美元", "start_idx": 22, "end_idx": 26, "type": "交易信息"}, {"entity": "订单款", "start_idx": 27, "end_idx": 30, "type": "交易信息"}]}, {"text": "请支付订单号2023110100456的尾款，金额为299元。", "label": [{"entity": "2023110100456", "start_idx": 7, "end_idx": 16, "type": "交易信息"}, {"entity": "尾款", "start_idx": 17, "end_idx": 19, "type": "交易信息"}, {"entity": "299元", "start_idx": 24, "end_idx": 27, "type": "交易信息"}]}, {"text": "您已成功转账5000元至账号6222 0888 1111 2222。", "label": [{"entity": "5000元", "start_idx": 7, "end_idx": 10, "type": "交易信息"}, {"entity": "账号6222 0888 1111 2222", "start_idx": 12, "end_idx": 27, "type": "交易信息"}]}], "临床表现": [{"text": "患者出现了持续性的胸痛，伴有呼吸困难。", "label": [{"entity": "持续性的胸痛", "start_idx": 4, "end_idx": 9, "type": "临床表现"}, {"entity": "呼吸困难", "start_idx": 12, "end_idx": 16, "type": "临床表现"}]}, {"text": "患者主诉有明显的恶心和呕吐症状。", "label": [{"entity": "恶心", "start_idx": 6, "end_idx": 7, "type": "临床表现"}, {"entity": "呕吐", "start_idx": 8, "end_idx": 9, "type": "临床表现"}]}, {"text": "检查发现患者存在皮肤黄染和巩膜黄染。", "label": [{"entity": "皮肤黄染", "start_idx": 6, "end_idx": 9, "type": "临床表现"}, {"entity": "巩膜黄染", "start_idx": 10, "end_idx": 13, "type": "临床表现"}]}, {"text": "患者表现为阵发性的心悸和头晕症状。", "label": [{"entity": "阵发性的心悸和头晕症状", "start_idx": 5, "end_idx": 17, "type": "临床表现"}]}, {"text": "患者出现了明显的关节肿胀和活动受限。", "label": [{"entity": "关节肿胀", "start_idx": 5, "end_idx": 8, "type": "临床表现"}, {"entity": "活动受限", "start_idx": 9, "end_idx": 12, "type": "临床表现"}]}, {"text": "患者主诉有剧烈的腹痛和腹泻症状。", "label": [{"entity": "剧烈的腹痛", "start_idx": 5, "end_idx": 8, "type": "临床表现"}, {"entity": "腹泻", "start_idx": 9, "end_idx": 10, "type": "临床表现"}]}, {"text": "患者检查发现存在明显的肝肿大和脾肿大。", "label": [{"entity": "肝肿大", "start_idx": 7, "end_idx": 9, "type": "临床表现"}, {"entity": "脾肿大", "start_idx": 10, "end_idx": 12, "type": "临床表现"}]}, {"text": "患者表现为持续性的咳嗽和咳痰症状。", "label": [{"entity": "持续性的咳嗽和咳痰症状", "start_idx": 5, "end_idx": 13, "type": "临床表现"}]}, {"text": "患者出现了双下肢水肿和皮肤发红。", "label": [{"entity": "双下肢水肿", "start_idx": 4, "end_idx": 8, "type": "临床表现"}, {"entity": "皮肤发红", "start_idx": 9, "end_idx": 12, "type": "临床表现"}]}, {"text": "患者主诉有明显的头痛和视物模糊症状。", "label": [{"entity": "头痛", "start_idx": 7, "end_idx": 9, "type": "临床表现"}, {"entity": "视物模糊", "start_idx": 10, "end_idx": 13, "type": "临床表现"}]}, {"text": "患者出现了明显的呼吸困难，伴有咳嗽和胸痛。", "label": [{"entity": "呼吸困难", "start_idx": 4, "end_idx": 7, "type": "临床表现"}, {"entity": "咳嗽", "start_idx": 9, "end_idx": 10, "type": "临床表现"}, {"entity": "胸痛", "start_idx": 12, "end_idx": 13, "type": "临床表现"}]}, {"text": "他的皮肤出现了典型的荨麻疹，伴有瘙痒感。", "label": [{"entity": "荨麻疹", "start_idx": 8, "end_idx": 10, "type": "临床表现"}, {"entity": "瘙痒感", "start_idx": 13, "end_idx": 15, "type": "临床表现"}]}, {"text": "检查发现患者有明显的黄疸，尿色呈深黄色。", "label": [{"entity": "明显的黄疸", "start_idx": 5, "end_idx": 9, "type": "临床表现"}, {"entity": "深黄色", "start_idx": 12, "end_idx": 16, "type": "临床表现"}]}, {"text": "她感觉双腿麻木，伴有针刺样的异常感觉。", "label": [{"entity": "双腿麻木", "start_idx": 4, "end_idx": 7, "type": "临床表现"}, {"entity": "针刺样的异常感觉", "start_idx": 9, "end_idx": 16, "type": "临床表现"}]}, {"text": "患者出现了持续的高热，体温高达39.5℃。", "label": [{"entity": "持续的高热", "start_idx": 4, "end_idx": 9, "type": "临床表现"}, {"entity": "体温高达39.5℃", "start_idx": 10, "end_idx": 18, "type": "临床表现"}]}, {"text": "他的关节出现了红肿和压痛，活动受限。", "label": [{"entity": "红肿", "start_idx": 7, "end_idx": 9, "type": "临床表现"}, {"entity": "压痛", "start_idx": 10, "end_idx": 12, "type": "临床表现"}, {"entity": "活动受限", "start_idx": 13, "end_idx": 17, "type": "临床表现"}]}, {"text": "她描述有剧烈的头痛，伴有恶心和呕吐症状。", "label": [{"entity": "剧烈的头痛", "start_idx": 4, "end_idx": 8, "type": "临床表现"}, {"entity": "恶心", "start_idx": 10, "end_idx": 12, "type": "临床表现"}, {"entity": "呕吐症状", "start_idx": 14, "end_idx": 18, "type": "临床表现"}]}], "医疗程序": [{"text": "医生建议他进行冠状动脉造影术以检查血管状况。", "label": [{"entity": "冠状动脉造影术", "start_idx": 11, "end_idx": 17, "type": "医疗程序"}]}, {"text": "患者接受了胃镜检查，结果显示轻微炎症。", "label": [{"entity": "胃镜检查", "start_idx": 4, "end_idx": 7, "type": "医疗程序"}]}, {"text": "她预约了核磁共振成像来评估脑部情况。", "label": [{"entity": "核磁共振成像", "start_idx": 4, "end_idx": 8, "type": "医疗程序"}]}, {"text": "医生为她安排了肺功能测试以诊断呼吸问题。", "label": [{"entity": "肺功能测试", "start_idx": 8, "end_idx": 11, "type": "医疗程序"}]}, {"text": "他需要做一次超声波检查来观察肝脏状况。", "label": [{"entity": "超声波检查", "start_idx": 7, "end_idx": 12, "type": "医疗程序"}]}, {"text": "医院为他安排了心脏瓣膜置换手术。", "label": [{"entity": "心脏瓣膜置换手术", "start_idx": 10, "end_idx": 20, "type": "医疗程序"}]}, {"text": "医生建议进行结肠镜检查以筛查肠道疾病。", "label": [{"entity": "结肠镜检查", "start_idx": 7, "end_idx": 10, "type": "医疗程序"}]}, {"text": "他接受了经皮冠状动脉介入治疗以改善血流。", "label": [{"entity": "经皮冠状动脉介入治疗", "start_idx": 4, "end_idx": 11, "type": "医疗程序"}]}, {"text": "她预约了骨密度测试来评估骨质疏松风险。", "label": [{"entity": "骨密度测试", "start_idx": 5, "end_idx": 8, "type": "医疗程序"}]}, {"text": "医生建议他进行结肠镜检查以筛查早期病变。", "label": [{"entity": "结肠镜检查", "start_idx": 7, "end_idx": 11, "type": "医疗程序"}]}, {"text": "患者接受了胃镜检查，结果显示一切正常。", "label": [{"entity": "胃镜检查", "start_idx": 4, "end_idx": 7, "type": "医疗程序"}]}, {"text": "她预约了超声波检查来评估肝脏状况。", "label": [{"entity": "超声波检查", "start_idx": 4, "end_idx": 8, "type": "医疗程序"}]}, {"text": "医生为她安排了腰椎穿刺术以检测脑脊液。", "label": [{"entity": "腰椎穿刺术", "start_idx": 7, "end_idx": 11, "type": "医疗程序"}]}, {"text": "他需要做肺功能测试来评估呼吸系统健康。", "label": [{"entity": "肺功能测试", "start_idx": 7, "end_idx": 11, "type": "医疗程序"}]}, {"text": "患者接受了冠状动脉造影术以诊断血管狭窄。", "label": [{"entity": "冠状动脉造影术", "start_idx": 5, "end_idx": 10, "type": "医疗程序"}]}, {"text": "她进行了骨密度测试，结果显示骨质疏松风险较高。", "label": [{"entity": "骨密度测试", "start_idx": 3, "end_idx": 7, "type": "医疗程序"}]}, {"text": "医生建议进行血液透析以维持肾功能。", "label": [{"entity": "血液透析", "start_idx": 7, "end_idx": 10, "type": "医疗程序"}]}, {"text": "他预约了心脏电生理检查来评估心律失常。", "label": [{"entity": "心脏电生理检查", "start_idx": 5, "end_idx": 10, "type": "医疗程序"}]}, {"text": "患者接受了胸腔穿刺术以抽取胸腔积液。", "label": [{"entity": "胸腔穿刺术", "start_idx": 6, "end_idx": 11, "type": "医疗程序"}]}], "地理位置": [{"text": "北京故宫的太和殿是明清两代的皇家宫殿。", "label": [{"entity": "北京故宫", "start_idx": 0, "end_idx": 4, "type": "地理位置"}, {"entity": "太和殿", "start_idx": 5, "end_idx": 7, "type": "地理位置"}]}, {"text": "上海外滩的夜景吸引了无数游客前来观赏。", "label": [{"entity": "上海外滩", "start_idx": 0, "end_idx": 4, "type": "地理位置"}]}, {"text": "杭州西湖的断桥边，许多情侣在此留下合影。", "label": [{"entity": "杭州", "start_idx": 0, "end_idx": 2, "type": "地理位置"}, {"entity": "西湖", "start_idx": 3, "end_idx": 5, "type": "地理位置"}, {"entity": "断桥", "start_idx": 6, "end_idx": 8, "type": "地理位置"}]}, {"text": "成都宽窄巷子的茶馆里，人们悠闲地品着盖碗茶。", "label": [{"entity": "成都", "start_idx": 0, "end_idx": 2, "type": "地理位置"}, {"entity": "宽窄巷子", "start_idx": 3, "end_idx": 6, "type": "地理位置"}, {"entity": "茶馆", "start_idx": 7, "end_idx": 9, "type": "地理位置"}]}, {"text": "广州塔的观景台可以俯瞰整个珠江新城的繁华。", "label": [{"entity": "广州塔", "start_idx": 0, "end_idx": 2, "type": "地理位置"}, {"entity": "观景台", "start_idx": 3, "end_idx": 5, "type": "地理位置"}, {"entity": "珠江新城", "start_idx": 12, "end_idx": 15, "type": "地理位置"}]}, {"text": "西安兵马俑博物馆展示了秦朝的辉煌历史。", "label": [{"entity": "西安", "start_idx": 0, "end_idx": 1, "type": "地理位置"}, {"entity": "兵马俑博物馆", "start_idx": 2, "end_idx": 5, "type": "地理位置"}, {"entity": "秦朝", "start_idx": 8, "end_idx": 9, "type": "地理位置"}]}, {"text": "重庆洪崖洞的吊脚楼建筑风格独特，令人惊叹。", "label": [{"entity": "重庆", "start_idx": 0, "end_idx": 2, "type": "地理位置"}, {"entity": "洪崖洞", "start_idx": 3, "end_idx": 6, "type": "地理位置"}]}, {"text": "南京夫子庙的秦淮河畔，灯火辉煌，热闹非凡。", "label": [{"entity": "南京夫子庙", "start_idx": 0, "end_idx": 4, "type": "地理位置"}, {"entity": "秦淮河畔", "start_idx": 5, "end_idx": 8, "type": "地理位置"}]}, {"text": "深圳欢乐谷的过山车项目让游客尖叫不断。", "label": [{"entity": "深圳欢乐谷", "start_idx": 0, "end_idx": 4, "type": "地理位置"}]}, {"text": "苏州园林的拙政园以其精巧的设计闻名于世。", "label": [{"entity": "苏州园林", "start_idx": 0, "end_idx": 4, "type": "地理位置"}, {"entity": "拙政园", "start_idx": 5, "end_idx": 7, "type": "地理位置"}]}, {"text": "我们计划下个月去北京故宫博物院参观历史文物。", "label": [{"entity": "北京故宫博物院", "start_idx": 10, "end_idx": 19, "type": "地理位置"}]}, {"text": "上海外滩的夜景非常迷人，每年吸引大量游客。", "label": [{"entity": "上海外滩", "start_idx": 0, "end_idx": 3, "type": "地理位置"}]}, {"text": "他家住在纽约曼哈顿第五大道，交通便利。", "label": [{"entity": "纽约曼哈顿第五大道", "start_idx": 4, "end_idx": 11, "type": "地理位置"}]}, {"text": "珠穆朗玛峰是世界最高峰，位于中国和尼泊尔边境。", "label": [{"entity": "珠穆朗玛峰", "start_idx": 0, "end_idx": 5, "type": "地理位置"}, {"entity": "中国", "start_idx": 15, "end_idx": 17, "type": "地理位置"}, {"entity": "尼泊尔", "start_idx": 18, "end_idx": 21, "type": "地理位置"}]}, {"text": "东京迪士尼乐园是亚洲最受欢迎的主题公园之一。", "label": [{"entity": "东京迪士尼乐园", "start_idx": 0, "end_idx": 6, "type": "地理位置"}, {"entity": "亚洲", "start_idx": 8, "end_idx": 9, "type": "地理位置"}]}, {"text": "澳大利亚悉尼歌剧院是著名的建筑地标。", "label": [{"entity": "澳大利亚", "start_idx": 0, "end_idx": 2, "type": "地理位置"}, {"entity": "悉尼歌剧院", "start_idx": 3, "end_idx": 7, "type": "地理位置"}]}, {"text": "我大学时在西安大雁塔附近租过房子。", "label": [{"entity": "西安", "start_idx": 6, "end_idx": 7, "type": "地理位置"}, {"entity": "大雁塔", "start_idx": 8, "end_idx": 10, "type": "地理位置"}]}], "过敏信息": [{"text": "他对花生过敏，每次吃都会出现严重反应。", "label": [{"entity": "花生", "start_idx": 4, "end_idx": 6, "type": "过敏信息"}]}, {"text": "她对牛奶蛋白过敏，喝完牛奶后皮肤会发红。", "label": [{"entity": "牛奶蛋白", "start_idx": 4, "end_idx": 6, "type": "过敏信息"}, {"entity": "牛奶", "start_idx": 10, "end_idx": 12, "type": "过敏信息"}]}, {"text": "小明对芒果过敏，吃了一口就感到喉咙发痒。", "label": [{"entity": "芒果", "start_idx": 5, "end_idx": 7, "type": "过敏信息"}]}, {"text": "她对青霉素过敏，注射后出现了呼吸困难。", "label": [{"entity": "对青霉素过敏", "start_idx": 3, "end_idx": 8, "type": "过敏信息"}]}, {"text": "他对小麦麸质过敏，吃面包后肚子会疼。", "label": [{"entity": "小麦麸质", "start_idx": 4, "end_idx": 7, "type": "过敏信息"}]}, {"text": "她对海鲜过敏，尤其是虾，吃后会起荨麻疹。", "label": [{"entity": "海鲜", "start_idx": 4, "end_idx": 6, "type": "过敏信息"}, {"entity": "虾", "start_idx": 9, "end_idx": 10, "type": "过敏信息"}]}, {"text": "他对尘螨过敏，房间里有尘土就会打喷嚏。", "label": [{"entity": "尘螨", "start_idx": 3, "end_idx": 4, "type": "过敏信息"}, {"entity": "尘土", "start_idx": 9, "end_idx": 10, "type": "过敏信息"}]}, {"text": "她对鸡蛋过敏，吃蛋糕时必须确认不含鸡蛋。", "label": [{"entity": "鸡蛋", "start_idx": 3, "end_idx": 4, "type": "过敏信息"}]}, {"text": "他对大豆过敏，豆浆和豆腐都不能食用。", "label": [{"entity": "大豆", "start_idx": 3, "end_idx": 4, "type": "过敏信息"}, {"entity": "豆浆", "start_idx": 7, "end_idx": 8, "type": "过敏信息"}, {"entity": "豆腐", "start_idx": 10, "end_idx": 11, "type": "过敏信息"}]}, {"text": "她对花粉过敏，春天外出时鼻子会一直痒。", "label": [{"entity": "花粉", "start_idx": 3, "end_idx": 4, "type": "过敏信息"}]}, {"text": "她对花生过敏，每次吃都会出现严重的皮肤反应。", "label": [{"entity": "花生", "start_idx": 4, "end_idx": 6, "type": "过敏信息"}]}, {"text": "小明对牛奶蛋白过敏，医生建议他避免饮用任何乳制品。", "label": [{"entity": "牛奶蛋白", "start_idx": 5, "end_idx": 8, "type": "过敏信息"}, {"entity": "乳制品", "start_idx": 21, "end_idx": 24, "type": "过敏信息"}]}, {"text": "他对芒果过敏，皮肤接触后会出现红肿和瘙痒。", "label": [{"entity": "芒果", "start_idx": 4, "end_idx": 5, "type": "过敏信息"}]}, {"text": "我妹妹对小麦过敏，所以她的饮食中不能有面包或面条。", "label": [{"entity": "小麦", "start_idx": 5, "end_idx": 6, "type": "过敏信息"}]}, {"text": "他对尘螨过敏，家里的床单需要经常更换。", "label": [{"entity": "尘螨", "start_idx": 4, "end_idx": 6, "type": "过敏信息"}]}, {"text": "她对贝类过敏，吃海鲜时必须特别小心。", "label": [{"entity": "贝类", "start_idx": 3, "end_idx": 4, "type": "过敏信息"}, {"entity": "海鲜", "start_idx": 7, "end_idx": 8, "type": "过敏信息"}]}, {"text": "他对花粉过敏，春天外出时总是戴口罩。", "label": [{"entity": "花粉", "start_idx": 3, "end_idx": 4, "type": "过敏信息"}]}, {"text": "他对青霉素过敏，用药前必须告知医生。", "label": [{"entity": "青霉素", "start_idx": 4, "end_idx": 6, "type": "过敏信息"}]}, {"text": "她对鸡蛋过敏，烘焙食品中不能使用鸡蛋成分。", "label": [{"entity": "鸡蛋", "start_idx": 4, "end_idx": 6, "type": "过敏信息"}, {"entity": "鸡蛋成分", "start_idx": 18, "end_idx": 21, "type": "过敏信息"}]}, {"text": "他对坚果过敏，巧克力中如果有坚果成分就不能吃。", "label": [{"entity": "坚果", "start_idx": 4, "end_idx": 6, "type": "过敏信息"}]}], "生育信息": [{"text": "她的预产期是2024年6月15日，医生建议按时产检。", "label": [{"entity": "2024年6月15日", "start_idx": 6, "end_idx": 14, "type": "生育信息"}]}, {"text": "张女士的产前检查结果显示胎儿心率每分钟140次。", "label": [{"entity": "胎儿心率", "start_idx": 12, "end_idx": 15, "type": "生育信息"}, {"entity": "每分钟140次", "start_idx": 16, "end_idx": 21, "type": "生育信息"}]}, {"text": "李先生的妻子在2023年9月1日顺利生下一名3.2公斤的女婴。", "label": [{"entity": "2023年9月1日", "start_idx": 5, "end_idx": 10, "type": "生育信息"}, {"entity": "3.2公斤", "start_idx": 16, "end_idx": 19, "type": "生育信息"}, {"entity": "女婴", "start_idx": 20, "end_idx": 22, "type": "生育信息"}]}, {"text": "王医生记录显示，孕妇的宫高为32厘米，符合孕周34周的标准。", "label": [{"entity": "孕妇", "start_idx": 7, "end_idx": 9, "type": "生育信息"}, {"entity": "宫高为32厘米", "start_idx": 10, "end_idx": 16, "type": "生育信息"}, {"entity": "孕周34周", "start_idx": 23, "end_idx": 28, "type": "生育信息"}]}, {"text": "该夫妇的试管婴儿移植日期定于2024年3月20日。", "label": [{"entity": "2024年3月20日", "start_idx": 12, "end_idx": 19, "type": "生育信息"}]}, {"text": "小王的宝宝出生时身长为51厘米，体重4.1公斤。", "label": [{"entity": "51厘米", "start_idx": 7, "end_idx": 9, "type": "生育信息"}, {"entity": "4.1公斤", "start_idx": 11, "end_idx": 13, "type": "生育信息"}]}, {"text": "医院记录显示，产妇的产后出血量为200毫升，属于正常范围。", "label": [{"entity": "200毫升", "start_idx": 16, "end_idx": 19, "type": "生育信息"}, {"entity": "正常范围", "start_idx": 22, "end_idx": 26, "type": "生育信息"}]}, {"text": "孕妇的羊水量检查结果为正常范围，约800毫升。", "label": [{"entity": "孕妇", "start_idx": 0, "end_idx": 2, "type": "生育信息"}, {"entity": "羊水量", "start_idx": 3, "end_idx": 5, "type": "生育信息"}, {"entity": "800毫升", "start_idx": 13, "end_idx": 16, "type": "生育信息"}]}, {"text": "赵女士的剖腹产手术定于2023年12月5日上午10点进行。", "label": [{"entity": "2023年12月5日", "start_idx": 9, "end_idx": 15, "type": "生育信息"}, {"entity": "上午10点", "start_idx": 16, "end_idx": 19, "type": "生育信息"}]}, {"text": "新生儿的Apgar评分在出生后1分钟时为9分，状态良好。", "label": [{"entity": "新生儿的Apgar评分", "start_idx": 0, "end_idx": 7, "type": "生育信息"}, {"entity": "出生后1分钟", "start_idx": 8, "end_idx": 13, "type": "生育信息"}, {"entity": "9分", "start_idx": 14, "end_idx": 16, "type": "生育信息"}]}, {"text": "张女士的预产期是2024年5月15日，她正在准备待产包。", "label": [{"entity": "2024年5月15日", "start_idx": 6, "end_idx": 14, "type": "生育信息"}]}, {"text": "李医生建议王女士在孕周28周时进行糖耐量测试。", "label": [{"entity": "孕周28周", "start_idx": 11, "end_idx": 14, "type": "生育信息"}]}, {"text": "陈先生的精子活力检测结果显示为A级，符合生育标准。", "label": [{"entity": "A级", "start_idx": 9, "end_idx": 11, "type": "生育信息"}, {"entity": "生育标准", "start_idx": 18, "end_idx": 21, "type": "生育信息"}]}, {"text": "刘女士的卵泡监测显示，促排第12天卵泡直径达18mm。", "label": [{"entity": "第12天", "start_idx": 12, "end_idx": 15, "type": "生育信息"}, {"entity": "卵泡直径", "start_idx": 18, "end_idx": 21, "type": "生育信息"}, {"entity": "18mm", "start_idx": 22, "end_idx": 25, "type": "生育信息"}]}, {"text": "赵家庭的双胞胎妊娠风险评分为1.5，属于低风险范围。", "label": [{"entity": "双胞胎妊娠", "start_idx": 4, "end_idx": 8, "type": "生育信息"}, {"entity": "风险评分", "start_idx": 8, "end_idx": 11, "type": "生育信息"}, {"entity": "1.5", "start_idx": 12, "end_idx": 14, "type": "生育信息"}, {"entity": "低风险范围", "start_idx": 16, "end_idx": 20, "type": "生育信息"}]}, {"text": "孙女士的叶酸补充剂量为每天0.4mg，已坚持服用三个月。", "label": [{"entity": "0.4mg", "start_idx": 10, "end_idx": 13, "type": "生育信息"}, {"entity": "三个月", "start_idx": 21, "end_idx": 24, "type": "生育信息"}]}, {"text": "周先生的精液分析报告显示，精子密度为每毫升1500万。", "label": [{"entity": "精子密度", "start_idx": 7, "end_idx": 9, "type": "生育信息"}, {"entity": "每毫升1500万", "start_idx": 10, "end_idx": 13, "type": "生育信息"}]}, {"text": "吴女士的胎盘位置在子宫前壁，B超显示为正常状态。", "label": [{"entity": "胎盘位置", "start_idx": 3, "end_idx": 6, "type": "生育信息"}, {"entity": "子宫前壁", "start_idx": 7, "end_idx": 10, "type": "生育信息"}, {"entity": "正常状态", "start_idx": 15, "end_idx": 18, "type": "生育信息"}]}, {"text": "郑家庭通过第三代试管婴儿技术，成功筛查出健康胚胎。", "label": [{"entity": "健康胚胎", "start_idx": 21, "end_idx": 24, "type": "生育信息"}]}, {"text": "黄女士的催产素使用剂量为2.5单位，已进入第二产程。", "label": [{"entity": "2.5单位", "start_idx": 7, "end_idx": 10, "type": "生育信息"}, {"entity": "第二产程", "start_idx": 17, "end_idx": 20, "type": "生育信息"}]}], "行程信息": [{"text": "我将于2023年12月15日乘坐CA1234航班从北京首都国际机场出发。", "label": [{"entity": "CA1234航班", "start_idx": 14, "end_idx": 20, "type": "行程信息"}]}, {"text": "下周三早上8:30，我的火车票是G718次，从上海虹桥站到杭州东站。", "label": [{"entity": "下周三", "start_idx": 0, "end_idx": 3, "type": "行程信息"}, {"entity": "早上8:30", "start_idx": 4, "end_idx": 9, "type": "行程信息"}, {"entity": "G718次", "start_idx": 14, "end_idx": 18, "type": "行程信息"}, {"entity": "上海虹桥站", "start_idx": 23, "end_idx": 28, "type": "行程信息"}, {"entity": "杭州东站", "start_idx": 30, "end_idx": 35, "type": "行程信息"}]}, {"text": "请帮我预订一张2024年1月10日从广州白云机场飞往成都双流机场的机票。", "label": [{"entity": "机票", "start_idx": 47, "end_idx": 49, "type": "行程信息"}]}, {"text": "我们计划在2023年10月1日乘坐大巴，从南京南站出发前往苏州站。", "label": [{"entity": "2023年10月1日", "start_idx": 6, "end_idx": 13, "type": "行程信息"}, {"entity": "大巴", "start_idx": 15, "end_idx": 17, "type": "行程信息"}, {"entity": "南京南站", "start_idx": 24, "end_idx": 28, "type": "行程信息"}, {"entity": "苏州站", "start_idx": 32, "end_idx": 36, "type": "行程信息"}]}, {"text": "我需要查询2023年9月25日从深圳宝安机场到西安咸阳机场的航班信息。", "label": [{"entity": "航班信息", "start_idx": 41, "end_idx": 46, "type": "行程信息"}]}, {"text": "请确认我的2023年7月15日晚上10:05从武汉站到长沙南站的动车票。", "label": [{"entity": "动车票", "start_idx": 36, "end_idx": 39, "type": "行程信息"}]}, {"text": "我的国际航班CX878将在2023年5月5日早上9:30从香港国际机场起飞。", "label": [{"entity": "国际航班CX878", "start_idx": 5, "end_idx": 16, "type": "行程信息"}, {"entity": "2023年5月5日", "start_idx": 18, "end_idx": 26, "type": "行程信息"}, {"entity": "早上9:30", "start_idx": 27, "end_idx": 33, "type": "行程信息"}, {"entity": "香港国际机场", "start_idx": 36, "end_idx": 45, "type": "行程信息"}]}, {"text": "我计划下周三早上8:30从北京首都国际机场起飞前往上海浦东国际机场。", "label": [{"entity": "下周三", "start_idx": 3, "end_idx": 5, "type": "行程信息"}, {"entity": "早上8:30", "start_idx": 6, "end_idx": 11, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 15, "end_idx": 23, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 28, "end_idx": 36, "type": "行程信息"}]}, {"text": "请确认您的火车票信息：G101次列车，明天早上7:00从北京南站出发。", "label": [{"entity": "G101次列车", "start_idx": 6, "end_idx": 10, "type": "行程信息"}, {"entity": "明天早上7:00", "start_idx": 11, "end_idx": 16, "type": "行程信息"}, {"entity": "北京南站", "start_idx": 20, "end_idx": 24, "type": "行程信息"}]}, {"text": "您预订的酒店房间将于2024年1月10日早上9:00办理退房手续。", "label": [{"entity": "2024年1月10日", "start_idx": 12, "end_idx": 19, "type": "行程信息"}, {"entity": "早上9:00", "start_idx": 22, "end_idx": 28, "type": "行程信息"}]}, {"text": "这趟高铁的到达时间是明天下午16:30，终点站是杭州东站。", "label": [{"entity": "高铁", "start_idx": 4, "end_idx": 6, "type": "行程信息"}, {"entity": "到达时间", "start_idx": 7, "end_idx": 10, "type": "行程信息"}, {"entity": "明天下午16:30", "start_idx": 11, "end_idx": 18, "type": "行程信息"}, {"entity": "终点站", "start_idx": 19, "end_idx": 22, "type": "行程信息"}, {"entity": "杭州东站", "start_idx": 23, "end_idx": 27, "type": "行程信息"}]}, {"text": "请注意，您的航班CA1834因天气原因延误，预计起飞时间为18:45。", "label": [{"entity": "航班CA1834", "start_idx": 8, "end_idx": 14, "type": "行程信息"}, {"entity": "18:45", "start_idx": 25, "end_idx": 29, "type": "行程信息"}]}, {"text": "会议安排在下周二下午14:00于北京国贸三期酒店三楼会议室举行。", "label": [{"entity": "下周二下午14:00", "start_idx": 5, "end_idx": 13, "type": "行程信息"}, {"entity": "北京国贸三期酒店三楼会议室", "start_idx": 16, "end_idx": 38, "type": "行程信息"}]}, {"text": "我预订的火车票是G123，出发时间是2023年11月20日早上8点。", "label": [{"entity": "G123", "start_idx": 9, "end_idx": 12, "type": "行程信息"}, {"entity": "2023年11月20日早上8点", "start_idx": 16, "end_idx": 28, "type": "行程信息"}]}, {"text": "请确认您的酒店预订，入住日期为2023年12月5日，位于上海浦东香格里拉。", "label": [{"entity": "2023年12月5日", "start_idx": 10, "end_idx": 18, "type": "行程信息"}, {"entity": "上海浦东香格里拉", "start_idx": 21, "end_idx": 33, "type": "行程信息"}]}, {"text": "您的会议安排在2024年2月8日下午3点，地点是杭州国际会议中心。", "label": [{"entity": "2024年2月8日下午3点", "start_idx": 7, "end_idx": 19, "type": "行程信息"}, {"entity": "杭州国际会议中心", "start_idx": 23, "end_idx": 32, "type": "行程信息"}]}, {"text": "请携带身份证件，您的签证有效期至2024年3月25日。", "label": [{"entity": "身份证件", "start_idx": 3, "end_idx": 6, "type": "行程信息"}, {"entity": "签证", "start_idx": 9, "end_idx": 11, "type": "行程信息"}, {"entity": "2024年3月25日", "start_idx": 14, "end_idx": 21, "type": "行程信息"}]}, {"text": "预订的租车服务从2024年4月1日开始，车型为丰田卡罗拉，取车地点在成都东站。", "label": [{"entity": "2024年4月1日", "start_idx": 9, "end_idx": 15, "type": "行程信息"}, {"entity": "丰田卡罗拉", "start_idx": 19, "end_idx": 24, "type": "行程信息"}, {"entity": "成都东站", "start_idx": 31, "end_idx": 36, "type": "行程信息"}]}, {"text": "我明天上午9点从北京首都国际机场出发，飞往上海浦东国际机场。", "label": [{"entity": "明天上午9点", "start_idx": 2, "end_idx": 7, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 10, "end_idx": 20, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 24, "end_idx": 34, "type": "行程信息"}]}, {"text": "下周三下午2点30分，我将在广州白云机场乘坐CA1234航班前往成都双流机场。", "label": [{"entity": "下周三下午2点30分", "start_idx": 0, "end_idx": 7, "type": "行程信息"}, {"entity": "广州白云机场", "start_idx": 12, "end_idx": 18, "type": "行程信息"}, {"entity": "CA1234航班", "start_idx": 19, "end_idx": 24, "type": "行程信息"}, {"entity": "成都双流机场", "start_idx": 27, "end_idx": 34, "type": "行程信息"}]}]}