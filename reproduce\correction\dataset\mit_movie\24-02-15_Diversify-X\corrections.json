{"dataset-name": "mit-movie", "triples-dir-name": "24-02-05_NER-Dataset_{fmt=n-p2,#l=3,dc=T,lc=T}", "completions-dir-name": "24-02-15_15-19-30_Correction-Res_{fmt=n-p2,#cr=3,dc=T}_{t=0}", "corrections": {"Title": [{"sentence": "Can I stream the movie Arrival on any platform?", "span": "Arrival", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I can't wait to learn more about the director's cut of Blade Runner, when will it be released?", "span": "Blade Runner", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm interested in learning about the history behind the making of the sci-fi movie Blade Runner. Can you provide me with some details about the creative process involved in its production?", "span": "Blade Runner", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Tell me about the plot of the sci-fi movie Blade Runner 2049 released in 2017.", "span": "Blade Runner 2049", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Is there a movie theater near me showing the award-winning film Casablanca?", "span": "Casablanca", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who composed the music for the classic film Casablanca?", "span": "Casablanca", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the classic film \"Casablanca\"?", "span": "Casablanca", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Show me a trailer for a classic film that was both critically acclaimed and commercially successful.", "span": "classic film", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you play the trailer for the classic movie Ghostbusters?", "span": "classic movie Ghostbusters", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you provide me with the viewers' rating for the film Fargo?", "span": "Fargo", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Is the movie Finding Nemo appropriate for kids, and has it won any awards?", "span": "Finding Nemo", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me about the awards and nominations for the animated movie Frozen?", "span": "Frozen", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me the plot of the movie Frozen?", "span": "Frozen", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the movie Gone with the Wind and what's it about?", "span": "Gone with the Wind", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What is the specific subgenre of horror does the movie Hereditary fall under", "span": "Hereditary", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Are there any streaming platforms where I can watch the movie Inception?", "span": "Inception", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you give me a brief summary of the plot for the movie Inception?", "span": "Inception", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "can you give me a summary of the movie inception", "span": "inception", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you list the nominations for the movie Inception?", "span": "Inception", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you provide me with the director of the making-of documentary for the movie Inception?", "span": "Inception", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me what the heck happens in the movie Inception?", "span": "Inception", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me who directed the making of the movie 'Inception'?", "span": "Inception", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Could you provide a summary of the plot for the film Inception directed by <PERSON>?", "span": "Inception", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Could you provide me with the soundtrack of the movie Inception directed by <PERSON>?", "span": "Inception", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What is the plot of the movie Inception?", "span": "Inception", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the making-of documentary for the movie Inception?", "span": "Inception", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the movie Inception and who were the main actors?", "span": "Inception", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the sci-fi thriller film titled Interstellar and what technologies were used in its visual effects?", "span": "Interstellar", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Hey, are there any showtimes for the new James Bond movie?", "span": "<PERSON>", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I heard great things about the new James Bond movie, can you help me find tickets for it?", "span": "<PERSON>", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me about the behind-the-scenes details of the movie Jaws, directed by <PERSON>?", "span": "Jaws", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Are there any special features or bonus content available for the Blu-ray edition of the film Jurassic Park?", "span": "Jurassic Park", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm a big fan of <PERSON>'s work. Can you tell me the plot of his latest movie and if there's a trailer available?", "span": "latest movie", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": "Title", "span_index": null, "span_index_super": null}, {"sentence": "What are people saying about the new Marvel movie directed by <PERSON>?", "span": "Marvel movie", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Has the director of <PERSON><PERSON> received any nominations for the film?", "span": "Parasite", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I need to know the deal with the movie Pulp Fiction, can you give me a quick rundown?", "span": "Pulp Fiction", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the movie Shawshank Redemption?", "span": "Shawshank Redemption", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Do you have any showtimes for the new animated movie 'Sing 2'?", "span": "Sing 2", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me when and where I can see the new Spider-Man movie?", "span": "Spider-Man", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I need to see a preview of the new Spider-Man movie right now", "span": "Spider-Man", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you show me the trailer for the latest Star Wars movie?", "span": "Star Wars", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm so bored, show me some interviews with the cast of The Avengers film.", "span": "The Avengers", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can I find The Dark Knight on any streaming platforms?", "span": "The Dark Knight", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What is the viewers' rating for the film The Dark Knight released in 2008 on any streaming platform?", "span": "The Dark Knight", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What kind of movie is The Dark Knight? Like, is it action or thriller or what?", "span": "The Dark Knight", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'd like to know more about the creative mind behind the making of The Godfather, what can you tell me about the person who directed it?", "span": "The Godfather", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Which actor played the lead role in the film The Godfather?", "span": "The Godfather", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I need to watch a comedy movie right now, is 'The Hangover' available on any streaming platform?", "span": "The Hangover", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "tell me about the storyline of the matrix trilogy", "span": "the matrix", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me about the director and some interesting behind-the-scenes facts of the movie The Notebook?", "span": "The Notebook", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What awards did the movie The Shape of Water win?", "span": "The Shape of Water", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What nominations did the movie The Shape of Water receive?", "span": "The Shape of Water", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you provide me with a brief plot synopsis of the movie The Shawshank Redemption?", "span": "The Shawshank Redemption", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me the leading actor in The Shawshank Redemption?", "span": "The Shawshank Redemption", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Could you give me an overview of the storyline for the movie The Shawshank Redemption?", "span": "The Shawshank Redemption", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me the plot of the classic film The Sound of Music?", "span": "The Sound of Music", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me about the director of the movie Titanic and how the special effects were created?", "span": "Titanic", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Show me some exclusive interviews with the cast of the movie Titanic.", "span": "Titanic", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the movie that reveals the making of the Titanic film", "span": "Titanic", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the movie Titanic and who starred in it? I need to know now!", "span": "Titanic", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What is the overall rating for the movie To All the Boys I've Loved Before", "span": "To All the Boys I've Loved Before", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the western movie Tombstone?", "span": "Tombstone", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the Transformers movie that came out in 2007?", "span": "Transformers", "entity_type": "Title", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me the plot of the latest Avengers movie?", "span": "latest Avengers movie", "entity_type": "Title", "correction_label": {"label": "__wrong_boundary__"}, "correction": "Avengers", "span_index": null, "span_index_super": null}, {"sentence": "Can I buy tickets for the latest James Bond movie?", "span": "latest <PERSON>", "entity_type": "Title", "correction_label": {"label": "__wrong_boundary__"}, "correction": "<PERSON>", "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for showtimes and tickets for the latest Marvel movie", "span": "latest Marvel movie", "entity_type": "Title", "correction_label": {"label": "__wrong_boundary__"}, "correction": "Marvel", "span_index": null, "span_index_super": null}, {"sentence": "Do you know if there's a behind-the-scenes featurette for the latest Star Wars movie? I'd love to see how they created those special effects.", "span": "latest Star Wars movie", "entity_type": "Title", "correction_label": {"label": "__wrong_boundary__"}, "correction": "Star Wars", "span_index": null, "span_index_super": null}, {"sentence": "Which theater is playing the latest Marvel movie and what time does it start?", "span": "Marvel movie", "entity_type": "Title", "correction_label": {"label": "__wrong_boundary__"}, "correction": "Marvel", "span_index": null, "span_index_super": null}, {"sentence": "Can I get tickets for the new James Bond movie?", "span": "new <PERSON>", "entity_type": "Title", "correction_label": {"label": "__wrong_boundary__"}, "correction": "<PERSON>", "span_index": null, "span_index_super": null}, {"sentence": "Hey there! Can you show me a trailer for the new James Bond movie?", "span": "new <PERSON>", "entity_type": "Title", "correction_label": {"label": "__wrong_boundary__"}, "correction": "<PERSON>", "span_index": null, "span_index_super": null}, {"sentence": "Yo, where can I stream that new Spider-Man flick?", "span": "new Spider-Man flick", "entity_type": "Title", "correction_label": {"label": "__wrong_boundary__"}, "correction": "Spider-Man", "span_index": null, "span_index_super": null}, {"sentence": "Can I find tickets for the new James Bond movie?", "span": "the new James Bond movie", "entity_type": "Title", "correction_label": {"label": "__wrong_boundary__"}, "correction": "<PERSON>", "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me about any acclaimed movies in terms of recognition?", "span": "acclaimed movies", "entity_type": "Title", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "Who directed the classic film with a stellar performance by <PERSON><PERSON>?", "span": "classic film", "entity_type": "Title", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "I'm curious to learn about the creative process behind a classic movie that was quite influential. Can you provide insight into the making of this film?", "span": "classic movie", "entity_type": "Title", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "Can you provide me with the director of the new Disney movie about mermaids?", "span": "Disney", "entity_type": "Title", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "Is there a teaser for the new animated Disney movie with the song 'Let It Go'?", "span": "Disney", "entity_type": "Title", "correction_label": {"label": "__wrong_type__"}, "correction": "Production Company", "span_index": null, "span_index_super": null}, {"sentence": "Can you find any teasers for the upcoming Marvel movie", "span": "Marvel", "entity_type": "Title", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "I'm so pumped for the upcoming Marvel movie, can you hook me up with a glimpse of the trailer?", "span": "Marvel", "entity_type": "Title", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "Show me the sneak peek of the upcoming Marvel superhero movie scheduled for release next year.", "span": "Marvel", "entity_type": "Title", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "What is the name of the actor who plays <PERSON> Man in the Marvel movies?", "span": "Marvel movies", "entity_type": "Title", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "What's the plot of the new Disney princess movie that came out this year?", "span": "new Disney princess movie", "entity_type": "Title", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "Can I trust the ratings for the new horror film?", "span": "new horror film", "entity_type": "Title", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "Are there any theaters playing the new Marvel movie tonight?", "span": "new Marvel", "entity_type": "Title", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "Can you play the trailer for the newest animated movie for kids?", "span": "newest animated movie", "entity_type": "Title", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a movie with a compelling plot and strong performances, is it possible to find such a film on a streaming platform?", "span": "streaming platform", "entity_type": "Title", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "I am looking for a thriller movie directed by <PERSON>, could you suggest a title?", "span": "suggest a title", "entity_type": "Title", "correction_label": {"label": "__wrong_type__"}, "correction": "Other", "span_index": null, "span_index_super": null}, {"sentence": "Do you know if there's a trailer out for the upcoming superhero movie?", "span": "upcoming superhero movie", "entity_type": "Title", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}], "Viewers' Rating": [{"sentence": "What is the best-rated action movie of the year and when can I see it?", "span": "best-rated", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a good movie with a high viewers' rating?", "span": "good", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a romantic comedy film with a high viewers' rating?", "span": "high", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a romantic comedy movie from the 1990s with a high viewers' rating?", "span": "high", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Show me a movie with a high IMDb rating and a plot twist", "span": "high IMDb rating", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a movie with a high Viewers' Rating that came out in the last year?", "span": "high Viewers' Rating", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": "high Viewers' Rating", "span_index": null, "span_index_super": null}, {"sentence": "What's the highest rated action movie from the 90s? I really need to find something exciting to watch!", "span": "highest rated", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What's the highest rated action movie of all time?", "span": "highest rated", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What's the highest rated movie of all time?", "span": "highest rated", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "can you tell me about the highest-rated film in terms of viewers' ratings", "span": "highest-rated", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What is the highest-rated comedy film that stars <PERSON>?", "span": "highest-rated", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm in the mood for a classic film. Could you recommend a highly acclaimed black and white movie from the 1950s and share some trivia about the director?", "span": "highly acclaimed", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What are some highly rated classic musical films from the 1950s that I should watch?", "span": "highly rated", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a highly-rated action movie from the 1990s?", "span": "highly-rated", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a highly-rated action movie from the 2000s?", "span": "highly-rated", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What are some highly-rated romantic movies from the 1950s?", "span": "highly-rated", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a must-see action movie from the 90s directed by <PERSON>?", "span": "must-see", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What movie from the 1990s is a must-see action film?", "span": "must-see", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a movie with a really high viewers' rating?", "span": "really high viewers' rating", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "can you tell me about a movie with a really interesting storyline", "span": "really interesting", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm craving a horror movie that will scare the daylights out of me. What's a truly terrifying supernatural thriller worth watching?", "span": "terrifying", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What are the top-rated horror movies from the 2010s?", "span": "top-rated", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What are the top-rated sci-fi movies released in the past 5 years", "span": "top-rated", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who is the director of the top-rated science fiction film of all time?", "span": "top-rated", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who starred in the top-rated action movie of the year?", "span": "top-rated", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What is the viewer rating for the latest <PERSON> film?", "span": "viewer rating", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a romantic comedy that's not super cheesy, but still makes you feel all warm and fuzzy inside?", "span": "warm and fuzzy inside", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm not sure which movie to watch, can you suggest one with a memorable theme song that's worth listening to?", "span": "worth listening to", "entity_type": "Viewers' Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What animated films can you suggest that fall under the adventure genre?", "span": "animated films", "entity_type": "Viewers' Rating", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "Yo, did that movie with <PERSON> win any sick awards or get any nominations?", "span": "awards", "entity_type": "Viewers' Rating", "correction_label": {"label": "__wrong_type__"}, "correction": "Other", "span_index": null, "span_index_super": null}, {"sentence": "What children's movie from the 90s has a princess as the main character?", "span": "children's", "entity_type": "Viewers' Rating", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "What children's movies are in the fantasy genre?", "span": "children's movies", "entity_type": "Viewers' Rating", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "I'm interested in learning about the cinematography techniques used in the classic movie The Godfather, could you provide some insights?", "span": "classic", "entity_type": "Viewers' Rating", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "What are some classic action films from the 1980s that are considered iconic?", "span": "classic", "entity_type": "Viewers' Rating", "correction_label": {"label": "__wrong_type__"}, "correction": "Other", "span_index": null, "span_index_super": null}, {"sentence": "Who directed the classic film \"Casablanca\"?", "span": "classic", "entity_type": "Viewers' Rating", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "Could you recommend a family-friendly movie that came out this year?", "span": "family-friendly", "entity_type": "Viewers' Rating", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "What family-friendly movie with a catchy theme song was released in the past year?", "span": "family-friendly", "entity_type": "Viewers' Rating", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "Hey, I'm looking for a movie with some crazy action scenes, like, fast and furious style.", "span": "fast and furious style", "entity_type": "Viewers' Rating", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a feel-good drama film from the 80s that showcases family values", "span": "feel-good", "entity_type": "Viewers' Rating", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "Do you know if 'Forrest Gump' received any honors for its special effects?", "span": "honors", "entity_type": "Viewers' Rating", "correction_label": {"label": "__wrong_type__"}, "correction": "Other", "span_index": null, "span_index_super": null}, {"sentence": "I'm feeling nostalgic for the 80s - can you play the teaser for the film E.T. the Extra-Terrestrial?", "span": "nostalgic", "entity_type": "Viewers' Rating", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "I'm feeling nostalgic for an old western movie, can you show me a preview for the film 'The Good, the Bad and the Ugly'?", "span": "old", "entity_type": "Viewers' Rating", "correction_label": {"label": "__wrong_type__"}, "correction": "Year", "span_index": null, "span_index_super": null}, {"sentence": "Are there any streaming platforms where I can watch the movie Inception?", "span": "streaming platforms", "entity_type": "Viewers' Rating", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Is the director of the romantic comedy film known for making successful movies?", "span": "successful", "entity_type": "Viewers' Rating", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Who directed the fantasy film with the best visual effects of all time?", "span": "visual effects", "entity_type": "Viewers' Rating", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "What's the rating for the latest Marvel movie?", "span": "rating", "entity_type": "Viewers' Rating", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can I buy tickets for the new James Bond movie today?", "span": "tickets", "entity_type": "Viewers' Rating", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}], "Year": [{"sentence": "Can you recommend a movie with a great romantic song that came out in the 1980s?", "span": "1980s", "entity_type": "Year", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What are some highly acclaimed science fiction movies from the 1980s?", "span": "1980s", "entity_type": "Year", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me which movie won the most awards in the 1990s?", "span": "1990s", "entity_type": "Year", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me the plot of the movie Sleepless in Seattle from 1993?", "span": "1993", "entity_type": "Year", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the science fiction film from 1997 that received the highest rating from viewers?", "span": "1997", "entity_type": "Year", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What is the best sci-fi film from the 2010s with a strong female lead?", "span": "2010s", "entity_type": "Year", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What are the best rated movies of 2020, and can you share any interesting making-of stories about them?", "span": "2020", "entity_type": "Year", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a movie with a great soundtrack from the 80s?", "span": "80s", "entity_type": "Year", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I need to find a movie with the best original song from the 80s, can you help?", "span": "80s", "entity_type": "Year", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What are some highly-rated movies from the 80s that are suitable for family viewing?", "span": "80s", "entity_type": "Year", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What are the top-rated action films from the 90s?", "span": "90s", "entity_type": "Year", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What children's movie from the 90s has a princess as the main character?", "span": "90s", "entity_type": "Year", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Yo, who's the mastermind behind that dope action movie from last year with the insane stunts?", "span": "last year", "entity_type": "Year", "correction_label": {"label": "__correct__"}, "correction": "last year", "span_index": null, "span_index_super": null}, {"sentence": "Show me the sneak peek of the upcoming Marvel superhero movie scheduled for release next year.", "span": "next year", "entity_type": "Year", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What is the best-rated action movie of the year and when can I see it?", "span": "the year", "entity_type": "Year", "correction_label": {"label": "__correct__"}, "correction": "Year", "span_index": null, "span_index_super": null}, {"sentence": "What is the best movie of the year so far, according to the critics?", "span": "year", "entity_type": "Year", "correction_label": {"label": "__wrong_boundary__"}, "correction": "year so far", "span_index": null, "span_index_super": null}, {"sentence": "Do you have any documentaries about the making of classic Hollywood movies from the 1950s?", "span": "classic Hollywood movies from the 1950s", "entity_type": "Year", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "Could you show me a preview for a new release that leaves something to the imagination?", "span": "new release", "entity_type": "Year", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Do you have tickets available for the re-release of The Lion King?", "span": "re-release", "entity_type": "Year", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Do you know any recent movies with a score by a female composer?", "span": "recent movies", "entity_type": "Year", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "I'm interested in watching a science fiction movie with a focus on time travel. Are there any recent ones with good viewer ratings?", "span": "recent ones", "entity_type": "Year", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "What's the plot of the new Disney princess movie that came out this year?", "span": "this year", "entity_type": "Year", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "What awards did 'Parasite' win in the year it was released?", "span": "year it was released", "entity_type": "Year", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Can you show me the trailer for the latest romantic comedy film?", "span": "latest", "entity_type": "Year", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me the storyline of the latest Marvel movie?", "span": "latest", "entity_type": "Year", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Could you provide me with a review of the latest Quentin Tarantino movie?", "span": "latest", "entity_type": "Year", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What are the showtimes for the latest Tarantino film?", "span": "latest", "entity_type": "Year", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What's the latest movie that everyone's talking about?", "span": "latest", "entity_type": "Year", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who are the main actors in the latest sci-fi movie directed by <PERSON>?", "span": "latest", "entity_type": "Year", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the latest fantasy adventure film for kids?", "span": "latest", "entity_type": "Year", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Is there a new action movie out this year that's worth watching on any streaming services?", "span": "new", "entity_type": "Year", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a recent movie with a stellar cast and a compelling plot?", "span": "recent", "entity_type": "Year", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What year was the movie Inception released, and who directed it?", "span": "released", "entity_type": "Year", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Is there a romantic comedy playing this weekend?", "span": "this weekend", "entity_type": "Year", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Do you know if there are any good action movies playing this weekend?", "span": "weekend", "entity_type": "Year", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm interested in seeing a movie from the thriller genre this weekend. Can you tell me the showtimes for any recent releases?", "span": "weekend", "entity_type": "Year", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What family-friendly movies are showing this weekend?", "span": "weekend", "entity_type": "Year", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}], "Genre": [{"sentence": "Hey, what's the name of that movie with the awesome soundtrack that has a bunch of 80s hits?", "span": "80s hits", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Are there any must-see films with intense action movie trailers?", "span": "action", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can I really trust that the action movie has good reviews?", "span": "action", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you check out the trailer for that new action flick starring <PERSON><PERSON>?", "span": "action", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a good action film from the 1980s featuring <PERSON>?", "span": "action", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a good action movie from the 90s with <PERSON> in it?", "span": "action", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Give me the best action film of all time now!", "span": "action", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Hey, is there a dope action movie playing at the theaters this weekend?", "span": "action", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Hey, what's the coolest action movie of all time?", "span": "action", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Hey, what's the plot of the new action movie with <PERSON>?", "span": "action", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Hey, who directed that awesome action movie with <PERSON> and <PERSON><PERSON><PERSON>?", "span": "action", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm in a rush, so tell me who directed the highest-rated action film of last year", "span": "action", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for an action movie from the 2000s with a high viewers' rating. Any suggestions?", "span": "action", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Show me a trailer for any action movie released in the past year.", "span": "action", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What are the showtimes for the action movie with <PERSON> in it?", "span": "action", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What is the best-rated action movie of the year and when can I see it?", "span": "action", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What's an action movie with a great song in it?", "span": "action", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Yo, can you hook me up with a sneak peek of that new action flick with <PERSON><PERSON>?", "span": "action", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a recent action film with a strong female lead and a catchy soundtrack?", "span": "action film", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I heard that the latest action film starring <PERSON> got mixed reviews. What's the story about anyway?", "span": "action film", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "is there a movie theater nearby playing the new action film?", "span": "action film", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the action film with a high viewers' rating and <PERSON> as the lead actress?", "span": "action film", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What are the top-rated action films from the 90s?", "span": "action films", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a high-rated action movie from the 1990s?", "span": "action movie", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you show me some teaser for a new action movie?", "span": "action movie", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm bored, can you show me a trailer for the latest action movie?", "span": "action movie", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Is there a new action movie out this year that's worth watching on any streaming services?", "span": "action movie", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Which streaming platform has the highest rated action movie from the 1990s?", "span": "action movie", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "<PERSON> directed the action movie starring <PERSON> and <PERSON> with a PG-13 rating", "span": "action movie", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Do you know if there are any good action movies playing this weekend?", "span": "action movies", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What are some good action movies from the 1980s?", "span": "action movies", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm in the mood for something exciting! Can you recommend a movie with an action-packed teaser?", "span": "action-packed", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm interested in a movie that features an epic soundtrack, preferably in the adventure or fantasy genre. Any suggestions?", "span": "adventure", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you play the trailer for the new animated movie featuring talking animals?", "span": "animated", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a family-friendly animated movie with a catchy soundtrack?", "span": "animated", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me the highest-rated animated movies of all time?", "span": "animated", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a new animated film to watch, can you give me the plot of the movie Luca?", "span": "animated", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the animated film about a brave princess who saves her kingdom from an evil sorcerer", "span": "animated", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I heard that the director of The Dark Knight also made a behind-the-scenes documentary. Can you tell me more about it?", "span": "behind-the-scenes documentary", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm in the mood for a classic musical, preferably from the 1950s or 1960s.", "span": "classic musical", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I miss classic musicals with great songs. Can you suggest one for me?", "span": "classic musicals", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What are some classic western movies available for streaming?", "span": "classic western movies", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I need something to watch, is there a comedy movie from the 1990s available to stream?", "span": "comedy", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What is the highest-rated comedy film that stars <PERSON>?", "span": "comedy", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed that crazy action movie with all the explosions?", "span": "crazy action movie", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a crime movie directed by <PERSON> that has a neo-noir style", "span": "crime", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm lookin' for a movie that's like, action but also crime, you know what I mean?", "span": "crime", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What type of films fall under the crime genre, particularly those involving heists or capers?", "span": "crime", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "which crime thriller movies are worth watching", "span": "crime thriller", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a film about the history and production of Disney animated classics?", "span": "Disney animated classics", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I heard there's a documentary about the making of a classic horror film, can you provide more information?", "span": "documentary", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Is there a documentary about the history of animation films and the filmmakers behind them", "span": "documentary", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a coming-of-age movie in the drama genre?", "span": "drama", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm feeling really down. Can you recommend a drama movie that will make me cry?", "span": "drama", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Is there a drama film directed by <PERSON> that I can watch this weekend?", "span": "drama", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a family-friendly animated movie with a catchy soundtrack?", "span": "family-friendly", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I want to learn more about the special effects in a recent fantasy film.", "span": "fantasy", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a film where I can see how they created the magical costumes and props for a fantasy world", "span": "fantasy", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a feel-good movie with a catchy soundtrack. Any suggestions?", "span": "feel-good", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Hey, what's that super scary horror movie with the killer clown that came out in 2017?", "span": "horror", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Hey, what's the deal with that new horror flick with the haunted house?", "span": "horror", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed that awesome horror movie with a killer clown that scared the pants off everyone?", "span": "horror", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the highest-rated horror movie of the year?", "span": "horror", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm in the mood for a scary movie. Can you recommend a good horror film from the 80s or 90s?", "span": "horror film", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Hey, what's the latest comedy that's playing in theaters right now?", "span": "latest comedy", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the making-of documentary for the movie Inception?", "span": "making-of", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you provide me with the director of the making-of documentary for the movie Inception?", "span": "making-of documentary", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can I watch the latest Marvel movie at the theater tonight?", "span": "Marvel", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me what the new Marvel superhero movie is all about?", "span": "Marvel", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Do you have any teasers for the upcoming Marvel superhero film?", "span": "Marvel", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I would like to see a teaser for the upcoming Marvel superhero movie, if possible.", "span": "Marvel", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What are the showtimes for the latest Marvel movie?", "span": "Marvel movie", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'd like to see the new Marvel superhero movie, can you tell me the showtimes and ticket availability?", "span": "Marvel superhero", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What songs were featured in the musical film The Sound of Music?", "span": "musical", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a movie with a mysterious and twisty plot. Any suggestions?", "span": "mysterious", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a highly-rated mystery movie from the 1980s?", "span": "mystery", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a thriller with elements of mystery. Any suggestions?", "span": "mystery", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I miss the old romantic comedies. Can you tell me a movie with a memorable love song in its soundtrack?", "span": "old romantic comedies", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a heartwarming romance movie from the 90s with a strong female lead", "span": "romance", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm bored out of my mind, show me a classic romance movie featuring <PERSON>.", "span": "romance", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm not sure if I want to watch a scary movie trailer or a romantic one, can you help me decide?", "span": "romantic", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you show me the trailer for the new romantic comedy directed by <PERSON>?", "span": "romantic comedy", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Is the theater showing the romantic comedy film this weekend?", "span": "romantic comedy", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Is there a romantic comedy playing this weekend?", "span": "romantic comedy", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I need a recommendation for a scary movie, but nothing too intense. Can you suggest something with a supernatural thriller vibe and maybe a strong female lead?", "span": "scary movie", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you give me some behind-the-scenes insights into the making of the latest sci-fi movie about time travel?", "span": "sci-fi", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Hey, can you tell me who directed that awesome sci-fi movie with the killer soundtrack?", "span": "sci-fi", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What are some classic sci-fi films from the 80s directed by <PERSON>", "span": "sci-fi", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you hook me up with the deets on the director of that sci-fi flick with the epic special effects?", "span": "sci-fi flick", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Tell me about the sci-fi movie Interstellar directed by <PERSON>.", "span": "sci-fi movie", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the sci-fi thriller film titled Interstellar and what technologies were used in its visual effects?", "span": "sci-fi thriller", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the latest science fiction movie set in space?", "span": "science fiction", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the science fiction movie with a high viewers' rating", "span": "science fiction", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I heard there's a new superhero movie coming out soon, can you give me a sneak peek?", "span": "superhero", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I want to watch a film about a superhero who can fly - do you know any?", "span": "superhero", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Yo, where can I peep the trailer for that new superhero flick with the kickass special effects?", "span": "superhero flick", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I need a recommendation for a scary movie, but nothing too intense. Can you suggest something with a supernatural thriller vibe and maybe a strong female lead?", "span": "supernatural thriller", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I heard there's a new thriller movie out. What's the plot and who stars in it?", "span": "thriller", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm interested in seeing a movie from the thriller genre this weekend. Can you tell me the showtimes for any recent releases?", "span": "thriller", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a thriller with elements of mystery. Any suggestions?", "span": "thriller", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend some classic western films with intense gunfights and gripping storylines?", "span": "western", "entity_type": "Genre", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can I find any 80s action movies starring <PERSON> on any streaming service?", "span": "80s action movies", "entity_type": "Genre", "correction_label": {"label": "__wrong_boundary__"}, "correction": "80s action", "span_index": null, "span_index_super": null}, {"sentence": "Can you show me a trailer for the latest action movie directed by <PERSON>?", "span": "action movie", "entity_type": "Genre", "correction_label": {"label": "__wrong_boundary__"}, "correction": "action", "span_index": null, "span_index_super": null}, {"sentence": "Do you have any trailers for those blockbuster action movies coming out next year?", "span": "action movies", "entity_type": "Genre", "correction_label": {"label": "__wrong_boundary__"}, "correction": "action", "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend any action-packed spy movies from the 2000s?", "span": "action-packed", "entity_type": "Genre", "correction_label": {"label": "__wrong_boundary__"}, "correction": "action-packed spy movies", "span_index": null, "span_index_super": null}, {"sentence": "I'm dying to know the synopsis of the upcoming action-packed superhero film. Could you give me some details about the plot, please?", "span": "action-packed", "entity_type": "Genre", "correction_label": {"label": "__wrong_boundary__"}, "correction": "action-packed superhero", "span_index": null, "span_index_super": null}, {"sentence": "I'm in the mood for a classic film. Could you recommend a highly acclaimed black and white movie from the 1950s and share some trivia about the director?", "span": "black and white movie", "entity_type": "Genre", "correction_label": {"label": "__wrong_boundary__"}, "correction": "black and white", "span_index": null, "span_index_super": null}, {"sentence": "I'm so pumped to learn about a classic action movie from the 80s with a killer soundtrack. Can you show me a behind-the-scenes clip?", "span": "classic action movie", "entity_type": "Genre", "correction_label": {"label": "__wrong_boundary__"}, "correction": "classic action", "span_index": null, "span_index_super": null}, {"sentence": "I wonder if there are any famous actors who have starred in classic musical films. Can you give me some insights about the production of those movies?", "span": "classic musical films", "entity_type": "Genre", "correction_label": {"label": "__wrong_boundary__"}, "correction": "musical", "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a classic suspense movie from the 1950s directed by <PERSON>?", "span": "classic suspense movie", "entity_type": "Genre", "correction_label": {"label": "__wrong_boundary__"}, "correction": "classic suspense", "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a comedy film directed by <PERSON>, do you have any suggestions?", "span": "comedy film", "entity_type": "Genre", "correction_label": {"label": "__wrong_boundary__"}, "correction": "comedy", "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a detective movie where the main character is a retired cop hunting down a serial killer", "span": "detective", "entity_type": "Genre", "correction_label": {"label": "__wrong_boundary__"}, "correction": "detective movie", "span_index": null, "span_index_super": null}, {"sentence": "I want to watch a drama film from the 90s with a strong female lead and a compelling storyline", "span": "drama film", "entity_type": "Genre", "correction_label": {"label": "__wrong_boundary__"}, "correction": "drama", "span_index": null, "span_index_super": null}, {"sentence": "Are there any good action movies playing today?", "span": "good action movies", "entity_type": "Genre", "correction_label": {"label": "__wrong_boundary__"}, "correction": "action movies", "span_index": null, "span_index_super": null}, {"sentence": "I need to know the storyline of the highest-rated horror film of 2021 ASAP", "span": "highest-rated horror film", "entity_type": "Genre", "correction_label": {"label": "__wrong_boundary__"}, "correction": "horror film", "span_index": null, "span_index_super": null}, {"sentence": "Can you show me a movie trailer for the latest horror film directed by <PERSON>", "span": "latest horror film", "entity_type": "Genre", "correction_label": {"label": "__wrong_boundary__"}, "correction": "horror film", "span_index": null, "span_index_super": null}, {"sentence": "What's a good coming-of-age film that deals with LGBTQ+ themes?", "span": "LGBTQ+", "entity_type": "Genre", "correction_label": {"label": "__wrong_boundary__"}, "correction": "LGBTQ+ themes", "span_index": null, "span_index_super": null}, {"sentence": "What's the deal with that new horror movie? Is it really as scary as they say?", "span": "new horror movie", "entity_type": "Genre", "correction_label": {"label": "__wrong_boundary__"}, "correction": "horror", "span_index": null, "span_index_super": null}, {"sentence": "What's the buzz on the new thriller film that just came out?", "span": "new thriller film", "entity_type": "Genre", "correction_label": {"label": "__wrong_boundary__"}, "correction": "thriller film", "span_index": null, "span_index_super": null}, {"sentence": "I need to buy tickets for the top-rated comedy film that just came out", "span": "top-rated comedy film", "entity_type": "Genre", "correction_label": {"label": "__wrong_boundary__"}, "correction": "comedy", "span_index": null, "span_index_super": null}, {"sentence": "I'm really looking forward to seeing a new action movie. What are the showtimes for 'Mad Max: Fury Road' in 3D?", "span": "3D", "entity_type": "Genre", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Who directed the movie with the best 80s music soundtrack?", "span": "80s music", "entity_type": "Genre", "correction_label": {"label": "__wrong_type__"}, "correction": "Soundtrack", "span_index": null, "span_index_super": null}, {"sentence": "Is there a movie that has a really cool adventure plot?", "span": "adventure plot", "entity_type": "Genre", "correction_label": {"label": "__wrong_type__"}, "correction": "Plot", "span_index": null, "span_index_super": null}, {"sentence": "Has the movie La La Land won any awards?", "span": "awards", "entity_type": "Genre", "correction_label": {"label": "__wrong_type__"}, "correction": "Award", "span_index": null, "span_index_super": null}, {"sentence": "Hey, can you tell me if the movie 'The Shawshank Redemption' won any awards?", "span": "awards", "entity_type": "Genre", "correction_label": {"label": "__wrong_type__"}, "correction": "Other", "span_index": null, "span_index_super": null}, {"sentence": "I'd like to know about a movie that had a famous director known for their unique approach to filmmaking. Can you tell me more about this film and its behind-the-scenes dynamics?", "span": "behind-the-scenes", "entity_type": "Genre", "correction_label": {"label": "__wrong_type__"}, "correction": "Other", "span_index": null, "span_index_super": null}, {"sentence": "What are some of the best musical movies of all time?", "span": "best musical", "entity_type": "Genre", "correction_label": {"label": "__wrong_type__"}, "correction": "Viewers' Rating", "span_index": null, "span_index_super": null}, {"sentence": "I can't decide on a movie, do you have any suggestions for a classic film directed by <PERSON>?", "span": "classic film", "entity_type": "Genre", "correction_label": {"label": "__wrong_type__"}, "correction": "Title", "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a film with a soundtrack filled with classic rock hits, can you suggest one?", "span": "classic rock hits", "entity_type": "Genre", "correction_label": {"label": "__wrong_type__"}, "correction": "Song", "span_index": null, "span_index_super": null}, {"sentence": "I'm interested in a film about the history of visual effects in cinema, what options do you have?", "span": "film", "entity_type": "Genre", "correction_label": {"label": "__wrong_type__"}, "correction": "Title", "span_index": null, "span_index_super": null}, {"sentence": "I'm interested in seeing a gripping thriller. Could you recommend a movie directed by <PERSON>?", "span": "gripping", "entity_type": "Genre", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Could you recommend a family-friendly movie with a strong female character as the lead?", "span": "lead", "entity_type": "Genre", "correction_label": {"label": "__wrong_type__"}, "correction": "Character", "span_index": null, "span_index_super": null}, {"sentence": "I'm curious, did 'La La Land' get any nominations for its music?", "span": "nominations", "entity_type": "Genre", "correction_label": {"label": "__wrong_type__"}, "correction": "Title", "span_index": null, "span_index_super": null}, {"sentence": "Could you recommend a movie with a beautiful instrumental soundtrack for relaxation?", "span": "relaxation", "entity_type": "Genre", "correction_label": {"label": "__wrong_type__"}, "correction": "Viewers' Rating", "span_index": null, "span_index_super": null}, {"sentence": "Show me a movie featuring a detailed behind-the-scenes look at the special effects in the Lord of the Rings trilogy.", "span": "special effects", "entity_type": "Genre", "correction_label": {"label": "__wrong_type__"}, "correction": "Plot", "span_index": null, "span_index_super": null}, {"sentence": "\"Can I watch Titanic on any streaming platforms?", "span": "streaming platforms", "entity_type": "Genre", "correction_label": {"label": "__wrong_type__"}, "correction": "Other", "span_index": null, "span_index_super": null}, {"sentence": "Hey, can I watch The Matrix on any streaming platforms?", "span": "streaming platforms", "entity_type": "Genre", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Can I find any showtimes for a movie with a superhero character?", "span": "superhero character", "entity_type": "Genre", "correction_label": {"label": "__wrong_type__"}, "correction": "Character", "span_index": null, "span_index_super": null}, {"sentence": "I'm not sure what to watch tonight, can you give me a brief overview of a movie with a thrilling plot?", "span": "thrilling", "entity_type": "Genre", "correction_label": {"label": "__wrong_type__"}, "correction": "Plot", "span_index": null, "span_index_super": null}], "Director": [{"sentence": "I'm looking for a classic film directed by <PERSON>, with a suspenseful plot and a high viewers' rating.", "span": "<PERSON>", "entity_type": "Director", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Are there any action movies directed by <PERSON> playing this weekend?", "span": "<PERSON>", "entity_type": "Director", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you provide a brief plot summary of the movie Inception directed by <PERSON>?", "span": "<PERSON>", "entity_type": "Director", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me about the making of the film 'Inception' directed by <PERSON>?", "span": "<PERSON>", "entity_type": "Director", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me the plot of the movie Inception directed by <PERSON>?", "span": "<PERSON>", "entity_type": "Director", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Could you provide a summary of the plot for the film Inception directed by <PERSON>?", "span": "<PERSON>", "entity_type": "Director", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Could you provide information on the making-of the movie Inception directed by <PERSON>?", "span": "<PERSON>", "entity_type": "Director", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Could you provide me with the soundtrack of the movie Inception directed by <PERSON>?", "span": "<PERSON>", "entity_type": "Director", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me about the plot of the movie Blade Runner 2049 directed by <PERSON>?", "span": "<PERSON>", "entity_type": "Director", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What are people saying about the new Marvel movie directed by <PERSON>?", "span": "<PERSON>", "entity_type": "Director", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you show me a movie trailer for the latest horror film directed by <PERSON>", "span": "<PERSON>", "entity_type": "Director", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me the plot for the movie Black Panther directed by <PERSON>?", "span": "<PERSON>", "entity_type": "Director", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What behind-the-scenes information can you provide about the making of the movie 'Jurassic Park' directed by <PERSON>?", "span": "<PERSON>", "entity_type": "Director", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What's the synopsis of the <PERSON>-directed adventure movie Jurassic Park?", "span": "<PERSON>", "entity_type": "Director", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm interested in learning more about the production process for the movie The Matrix directed by the <PERSON><PERSON><PERSON><PERSON>. Can you provide any details?", "span": "<PERSON><PERSON><PERSON><PERSON>", "entity_type": "Director", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can I see a list of theaters showing the new Christopher Nolan film?", "span": "new <PERSON> film", "entity_type": "Director", "correction_label": {"label": "__wrong_boundary__"}, "correction": "<PERSON>", "span_index": null, "span_index_super": null}, {"sentence": "Is there a sci-fi film from the 90s with a bonus feature showing the actors' training for their roles?", "span": "actors' training", "entity_type": "Director", "correction_label": {"label": "__wrong_type__"}, "correction": "Other", "span_index": null, "span_index_super": null}, {"sentence": "Do you know any recent movies with a score by a female composer?", "span": "female composer", "entity_type": "Director", "correction_label": {"label": "__wrong_type__"}, "correction": "Other", "span_index": null, "span_index_super": null}, {"sentence": "I'm open to watching a movie directed by a female filmmaker that received critical acclaim. Do you have any recommendations?", "span": "female filmmaker", "entity_type": "Director", "correction_label": {"label": "__wrong_type__"}, "correction": "Other", "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a movie with a director's commentary by a non-binary filmmaker", "span": "non-binary", "entity_type": "Director", "correction_label": {"label": "__wrong_type__"}, "correction": "Other", "span_index": null, "span_index_super": null}, {"sentence": "I heard there's a new Pixar animated movie out, can you show me a sneak peek or something?", "span": "Pixar", "entity_type": "Director", "correction_label": {"label": "__wrong_type__"}, "correction": "Studio", "span_index": null, "span_index_super": null}, {"sentence": "Which action movie from the 2000s has the best special effects and stunts, and who was the stunt coordinator for that film?", "span": "stunt coordinator", "entity_type": "Director", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "What's the deal with that new Tarantino movie, is it any good?", "span": "Tarantino movie", "entity_type": "Director", "correction_label": {"label": "__wrong_type__"}, "correction": "Title", "span_index": null, "span_index_super": null}, {"sentence": "I'm in a rush, so tell me who directed the highest-rated action film of last year", "span": "directed", "entity_type": "Director", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the highest-rated horror movie of the year?", "span": "directed", "entity_type": "Director", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you provide me with the director of the documentary on the making of The Dark Knight?", "span": "director", "entity_type": "Director", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me about the director of the latest Marvel movie and any special features on the Blu-ray edition?", "span": "director", "entity_type": "Director", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm curious about the director behind the scenes of that action-packed film with the intense fight scenes. Can you tell me who directed it?", "span": "director", "entity_type": "Director", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm in the mood for a classic film. Could you recommend a highly acclaimed black and white movie from the 1950s and share some trivia about the director?", "span": "director", "entity_type": "Director", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Which director has released a new film with a thrilling plot that I can get a sneak peek of?", "span": "director", "entity_type": "Director", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Which director is known for creating movies in the psychological thriller subgenre?", "span": "director", "entity_type": "Director", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who is the director of the top-rated science fiction film of all time?", "span": "director", "entity_type": "Director", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}], "MPAA Rating": [{"sentence": "I want to know the plot of the action film directed by <PERSON> and rated PG-13", "span": "PG-13", "entity_type": "MPAA Rating", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Are there any special features or bonus content available for the Blu-ray edition of the film Jurassic Park?", "span": "Blu-ray edition", "entity_type": "MPAA Rating", "correction_label": {"label": "__wrong_type__"}, "correction": "Edition", "span_index": null, "span_index_super": null}], "Plot": [{"sentence": "Can you tell me about the director of that one movie with lots of action scenes?", "span": "action scenes", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": "action scenes", "span_index": null, "span_index_super": null}, {"sentence": "Show me a movie with the behind-the-scenes footage of a famous action sequence.", "span": "action sequence", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a movie with an epic soundtrack and action-packed scenes?", "span": "action-packed scenes", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Show me a film directed by <PERSON> that reveals the backstory of its main character.", "span": "backstory", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What are some must-see films that explore the behind-the-scenes of famous Hollywood blockbusters", "span": "behind-the-scenes", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you provide me with behind-the-scenes footage of the Avengers: Endgame movie", "span": "behind-the-scenes footage", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Show me a movie featuring a detailed behind-the-scenes look at the special effects in the Lord of the Rings trilogy.", "span": "behind-the-scenes look", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm curious to know which actor has worked on the most iconic romantic films throughout their career.", "span": "career", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I heard there's a documentary about the making of a classic horror film, can you provide more information?", "span": "classic horror film", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What's the story behind that <PERSON> movie where he gets stranded on a desert island?", "span": "desert island", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What are some documentaries about the making of classic Hitchcock films?", "span": "documentaries", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a fantasy movie for kids with dragons and magic spells", "span": "dragons and magic spells", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I am interested in learning more about the filming locations of the movie The Godfather. Can you assist me with that?", "span": "filming locations", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Show me a romantic comedy with a heartwarming storyline and great chemistry between the lead actors", "span": "heartwarming storyline", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Which movie from the 90s has the most authentic portrayal of a historical event?", "span": "historical event", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm interested in a film about the history of visual effects in cinema, what options do you have?", "span": "history of visual effects in cinema", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What is the most intense horror movie to watch with friends and what was the inspiration behind the set design and overall atmosphere?", "span": "inspiration", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a film from the 90s with a very intense plot. Do you know if it's available on any streaming platforms?", "span": "intense plot", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": "intense plot", "span_index": null, "span_index_super": null}, {"sentence": "I'm interested in finding out more about a film that has an intriguing backstory in terms of its production. Can you share some details about this?", "span": "intriguing backstory", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a classic western movie with lots of action?", "span": "lots of action", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you show me a trailer for a movie about love and loss, something that will make me cry?", "span": "love and loss", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me about the making-of the newest Star Wars movie?", "span": "making-of", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm interested in learning about the making-of process of an iconic 90s film. Do you have any recommendations?", "span": "making-of process", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you just tell me the plot of the Shawshank Redemption already?", "span": "plot", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a film with a famous actor and a plot full of suspense.", "span": "plot full of suspense", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Yo, what's the deal with that new flick with the crazy plot twist at the end?", "span": "plot twist", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What's that movie with the really cool fight scenes and is considered a classic martial arts film?", "span": "really cool fight scenes", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "show me a documentary about the special effects in the Star Wars movies", "span": "special effects", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm curious to know more about the storyline of a recent action movie, could you provide some details?", "span": "storyline", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a movie directed by <PERSON> with a complex and mind-bending storyline", "span": "storyline", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What action movies with a superhero theme have been popular lately?", "span": "superhero", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a horror movie with some supernatural elements?", "span": "supernatural elements", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you play the trailer for the new animated movie featuring talking animals?", "span": "talking animals", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "show me a documentary about the production of the lord of the rings trilogy", "span": "trilogy", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Is there a theater near me showing a drama film based on a true story?", "span": "true story", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the sci-fi thriller film titled Interstellar and what technologies were used in its visual effects?", "span": "visual effects", "entity_type": "Plot", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a film with an unforgettable song that perfectly captures the adventurous spirit of the characters.", "span": "adventurous spirit", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "Character", "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me about the director of the movie <PERSON> and some interesting behind-the-scenes stories?", "span": "behind-the-scenes", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "What new sci-fi movie has an interesting behind-the-scenes featurette?", "span": "behind-the-scenes", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me about the director of the film <PERSON> Revenant and any behind-the-scenes details?", "span": "behind-the-scenes details", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "What are some popular behind-the-scenes features for fantasy movies from the 2010s", "span": "behind-the-scenes features", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a movie with a behind-the-scenes look at the special effects used in action films", "span": "behind-the-scenes look", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Show me a classic movie directed by <PERSON> and share some behind-the-scenes stories about the making of the film.", "span": "behind-the-scenes stories", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Is there a sci-fi film from the 90s with a bonus feature showing the actors' training for their roles?", "span": "bonus feature", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "What movie with romance as the genre has a captivating and heartwarming plot?", "span": "captivating", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "I'm in the mood for a movie with an engaging storyline, can you tell me about a film that has a captivating plot?", "span": "captivating plot", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Could you recommend a movie with a strong female lead character and a compelling plot", "span": "compelling plot", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Show me a film with a compelling plot and strong performances from <PERSON><PERSON>.", "span": "compelling plot", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a movie directed by <PERSON> with a complex and mind-bending storyline", "span": "complex and mind-bending", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "I'm interested in learning about the history behind the making of the sci-fi movie Blade Runner. Can you provide me with some details about the creative process involved in its production?", "span": "creative process", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a film with a legendary actor known for their dedication to perfecting their roles, like in those behind-the-scenes documentaries. Can you recommend one?", "span": "dedication to perfecting their roles", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "Actor", "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a movie with an exciting and mysterious plot. Can you recommend a film for me?", "span": "exciting and mysterious", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a feel-good movie with an uplifting song that will brighten my day. Can you suggest one that's family-friendly?", "span": "feel-good", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "Song", "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me about the director and the filming process of the movie The Sound of Music?", "span": "filming process", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "I loved the soundtrack in 'Dirty Dancing', can you tell me the name of the song that played during the final dance scene?", "span": "final dance scene", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "I miss classic musicals with great songs. Can you suggest one for me?", "span": "great songs", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "Song", "span_index": null, "span_index_super": null}, {"sentence": "show me a movie with an iconic musical number", "span": "iconic musical number", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "I'm in the mood for a movie with a great soundtrack. Can you recommend a film with iconic songs from the 80s?", "span": "iconic songs", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "Song", "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a documentary about the production of the film Inception and its impact on the science fiction genre?", "span": "impact", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a film where I can see how they created the magical costumes and props for a fantasy world", "span": "magical costumes and props", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Who directed the making-of documentary for the Lord of the Rings trilogy?", "span": "making-of documentary", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Who directed the making-of documentary for the movie Inception", "span": "making-of documentary", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "Other", "span_index": null, "span_index_super": null}, {"sentence": "What are some mind-bending sci-fi movies that will leave me on the edge of my seat?", "span": "mind-bending", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a movie with a character who performs a memorable musical number. Can you suggest one?", "span": "musical number", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "Character", "span_index": null, "span_index_super": null}, {"sentence": "I wonder if there are any famous actors who have starred in classic musical films. Can you give me some insights about the production of those movies?", "span": "production", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Show me a film that delves into the production process of a classic western movie", "span": "production process", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "What are the showtimes for the new Marvel movie?", "span": "showtimes", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a movie with a behind-the-scenes look at the special effects used in action films", "span": "special effects", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me about the special effects in the latest Avengers movie?", "span": "special effects", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Yo, where can I peep the trailer for that new superhero flick with the kickass special effects?", "span": "special effects", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Are there any special features or bonus content available for the Blu-ray edition of the film Jurassic Park?", "span": "special features", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me the storyline of the latest Marvel movie?", "span": "storyline", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me what the new Marvel superhero movie is all about?", "span": "superhero", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "Who directed the sci-fi thriller film titled Interstellar and what technologies were used in its visual effects?", "span": "technologies", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Which director has released a new film with a thrilling plot that I can get a sneak peek of?", "span": "thrilling plot", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Do you have tickets available for the re-release of The Lion King?", "span": "tickets", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "I'd like to know about a movie that had a famous director known for their unique approach to filmmaking. Can you tell me more about this film and its behind-the-scenes dynamics?", "span": "unique approach", "entity_type": "Plot", "correction_label": {"label": "__wrong_type__"}, "correction": "Director", "span_index": null, "span_index_super": null}, {"sentence": "I'm not sure what to watch tonight, can you give me a brief overview of a movie with a thrilling plot?", "span": "brief overview", "entity_type": "Plot", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm a big fan of <PERSON>'s work. Can you tell me the plot of his latest movie and if there's a trailer available?", "span": "plot", "entity_type": "Plot", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What is the plot of the highest-rated comedy movie from the 1990s?", "span": "plot", "entity_type": "Plot", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm not sure if the trailer for the new James Bond film truly represents the storyline. Can you provide a summary of the plot?", "span": "storyline", "entity_type": "Plot", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can I stream the movie Arrival on any platform?", "span": "stream", "entity_type": "Plot", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}], "Actor": [{"sentence": "I'm looking for a classic movie with <PERSON> in the lead role, can you suggest one?", "span": "<PERSON>", "entity_type": "Actor", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I want to see a critically acclaimed romantic comedy from the 2000s with <PERSON> in it, can you help me find showtimes?", "span": "<PERSON>", "entity_type": "Actor", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Tell me about that film where <PERSON><PERSON> plays a badass assassin.", "span": "<PERSON><PERSON>", "entity_type": "Actor", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can I find the movie Inception with Leonardo DiCaprio available on any streaming platform?", "span": "<PERSON>", "entity_type": "Actor", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a movie starring <PERSON>?", "span": "<PERSON>", "entity_type": "Actor", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me what the latest <PERSON> movie is about?", "span": "<PERSON>", "entity_type": "Actor", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Show me a film with a compelling plot and strong performances from <PERSON><PERSON>.", "span": "<PERSON><PERSON>", "entity_type": "Actor", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Show me a movie with <PERSON><PERSON> as the lead character.", "span": "<PERSON><PERSON>", "entity_type": "Actor", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "<PERSON>, who's the director of that new action flick with The Rock?", "span": "The Rock", "entity_type": "Actor", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What's that movie where <PERSON> plays a character stranded on an island?", "span": "<PERSON>", "entity_type": "Actor", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Is there any Netflix original movie with <PERSON> in it?", "span": "<PERSON>", "entity_type": "Actor", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm so bored, show me some interviews with the cast of The Avengers film.", "span": "cast", "entity_type": "Actor", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Show me interviews with the director and cast members of The Dark Knight. I need something to do.", "span": "cast members", "entity_type": "Actor", "correction_label": {"label": "__wrong_type__"}, "correction": "Other", "span_index": null, "span_index_super": null}, {"sentence": "Show me a film with a diverse cast that received critical acclaim", "span": "diverse cast", "entity_type": "Actor", "correction_label": {"label": "__wrong_type__"}, "correction": "Other", "span_index": null, "span_index_super": null}, {"sentence": "What's the deal with that movie where the dude from Avengers plays a spy or something?", "span": "dude from Avengers", "entity_type": "Actor", "correction_label": {"label": "__wrong_type__"}, "correction": "Actor's name is not specified", "span_index": null, "span_index_super": null}, {"sentence": "Hey, what's the deal with that movie that has that girl from the horror flick last year, you know what I'm talking about?", "span": "girl", "entity_type": "Actor", "correction_label": {"label": "__wrong_type__"}, "correction": "Actor's name is not specified", "span_index": null, "span_index_super": null}, {"sentence": "Can you provide me with the plot of the movie Inception and the name of the actor who played the lead role?", "span": "actor", "entity_type": "Actor", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who are the main actors in the latest sci-fi movie directed by <PERSON>?", "span": "main actors", "entity_type": "Actor", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}], "Trailer": [{"sentence": "Could you show me a preview for a new release that leaves something to the imagination?", "span": "preview", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Show me a preview of the new Marvel movie directed by <PERSON><PERSON>", "span": "preview", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm really hyped for the next Star Wars film, can you show me a sneak peek of the teaser?", "span": "sneak peek", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Hello! I'm looking for a romantic comedy. Could you show me a teaser for a popular one?", "span": "teaser", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": "Trailer", "span_index": null, "span_index_super": null}, {"sentence": "I need to see a teaser for the upcoming Marvel movie right now!", "span": "teaser", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I remember a movie with a catchy song, can you show me a teaser for the film with the song 'My Heart Will Go On'?", "span": "teaser", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Show me a teaser for the upcoming Marvel movie.", "span": "teaser", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "can I see the trailer for the latest Marvel movie", "span": "trailer", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can I see the trailer for the new high school musical movie?", "span": "trailer", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can I see the trailer for the new James Bond movie?", "span": "trailer", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you actually show me a trailer for the new James Bond movie?", "span": "trailer", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you play the trailer for the latest Marvel movie for me?", "span": "trailer", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you play the trailer for the new animated movie featuring talking animals?", "span": "trailer", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you show me the trailer for the film Inception?", "span": "trailer", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you show me the trailer for the latest Marvel movie?", "span": "trailer", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you show me the trailer for the latest romantic comedy film?", "span": "trailer", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you show me the trailer for the latest Star Wars movie?", "span": "trailer", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you show me the trailer for the new James Bond movie?", "span": "trailer", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "hey, can you play the trailer for the new Fast & Furious movie?", "span": "trailer", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I heard there's a thrilling new trailer for the upcoming sci-fi film. I cannot wait to watch it!", "span": "trailer", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": "new {{trailer}}", "span_index": null, "span_index_super": null}, {"sentence": "I'm not sure if the trailer for the new James Bond film truly represents the storyline. Can you provide a summary of the plot?", "span": "trailer", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Show me a trailer for the new Spider-Man movie with <PERSON>.", "span": "trailer", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Show me the trailer for the latest Meryl <PERSON>reep movie", "span": "trailer", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Show me the trailer for the latest Quentin Tarantino movie.", "span": "trailer", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Show me the trailer for the latest <PERSON> film", "span": "trailer", "entity_type": "Trailer", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I am interested in watching a movie directed by <PERSON>. Can you recommend a film with a captivating trailer?", "span": "captivating trailer", "entity_type": "Trailer", "correction_label": {"label": "__wrong_boundary__"}, "correction": "trailer", "span_index": null, "span_index_super": null}, {"sentence": "What movie from last year has a fun and exciting preview that kids will love?", "span": "fun and exciting preview", "entity_type": "Trailer", "correction_label": {"label": "__wrong_boundary__"}, "correction": "preview", "span_index": null, "span_index_super": null}, {"sentence": "Hi! I'm in the mood for some action. Can you recommend a movie teaser with lots of explosions?", "span": "teaser", "entity_type": "Trailer", "correction_label": {"label": "__wrong_boundary__"}, "correction": "movie teaser", "span_index": null, "span_index_super": null}, {"sentence": "Do you have any trailers for those blockbuster action movies coming out next year?", "span": "trailers", "entity_type": "Trailer", "correction_label": {"label": "__wrong_boundary__"}, "correction": "trailers for those blockbuster action movies", "span_index": null, "span_index_super": null}, {"sentence": "Who directed the film Inception and can you show me some behind-the-scenes clips", "span": "behind-the-scenes clips", "entity_type": "Trailer", "correction_label": {"label": "__wrong_type__"}, "correction": "Other", "span_index": null, "span_index_super": null}, {"sentence": "Do you know if there's a behind-the-scenes featurette for the latest Star Wars movie? I'd love to see how they created those special effects.", "span": "behind-the-scenes featurette", "entity_type": "Trailer", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a movie with a really intense trailer?", "span": "intense", "entity_type": "Trailer", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Which director is known for creating amazing movie teasers?", "span": "teasers", "entity_type": "Trailer", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}], "Song": [{"sentence": "which movie has a famous theme song like 'My Heart Will Go On' by <PERSON><PERSON> from Titanic?", "span": "'My Heart Will Go On' by <PERSON><PERSON>", "entity_type": "Song", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a movie with an amazing soundtrack that features the song 'Bohemian Rhapsody'?", "span": "Bohemian Rhapsody", "entity_type": "Song", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a movie with an iconic song from the 80s?", "span": "iconic song", "entity_type": "Song", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Could you show me a movie with a great love song in it?", "span": "love song", "entity_type": "Song", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a movie with a great soundtrack. Can you recommend a film with a memorable song performed by <PERSON>?", "span": "memorable song", "entity_type": "Song", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a movie with a captivating plot and the song 'My Heart Will Go On'. Can you suggest one?", "span": "my heart will go on", "entity_type": "Song", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What's a movie from the 90s with a popular love song in its soundtrack?", "span": "popular love song", "entity_type": "Song", "correction_label": {"label": "__correct__"}, "correction": "popular love song", "span_index": null, "span_index_super": null}, {"sentence": "Who directed the classic romantic comedy with the song 'You've Got a Friend in Me'?", "span": "You've Got a Friend in Me", "entity_type": "Song", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a movie with an amazing soundtrack, particularly one with a popular pop song. Can you suggest one?", "span": "amazing soundtrack", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Hey, what's the name of that movie with the awesome soundtrack that has a bunch of 80s hits?", "span": "awesome soundtrack", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Could you recommend a movie with a beautiful instrumental soundtrack for relaxation?", "span": "beautiful instrumental soundtrack", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Could you suggest a film with a beautiful theme song that complements the storyline?", "span": "beautiful theme song", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Could you suggest a family-friendly movie with a catchy soundtrack?", "span": "catchy soundtrack", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Are there any must-see movies known for their famous musical scores or theme songs?", "span": "famous musical scores", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Is there a movie with a famous song by <PERSON> in it?", "span": "famous song by <PERSON>", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Show me a movie with a famous theme song that became a chart-topping hit", "span": "famous theme song", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Can you find me a movie with a great soundtrack playing nearby?", "span": "great soundtrack", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a feel-good movie with a strong female lead and a great soundtrack?", "span": "great soundtrack", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Tell me about a classic film with a great soundtrack.", "span": "great soundtrack", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "I'm in a rush to find a movie with a killer soundtrack, any recommendations?", "span": "killer soundtrack", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a movie with a great soundtrack featuring love songs from the 80s?", "span": "love songs from the 80s", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "I'm hosting a movie night and want to feature a film with a memorable love theme. Can you recommend a romantic movie from the 1990s?", "span": "memorable love theme", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a movie with a memorable soundtrack?", "span": "memorable soundtrack", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "I'm not sure which movie to watch, can you suggest one with a memorable theme song that's worth listening to?", "span": "memorable theme song", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Is there a movie playing with a song by <PERSON>?", "span": "<PERSON>", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "Actor", "span_index": null, "span_index_super": null}, {"sentence": "What's the latest movie with an amazing original song? I'm in the mood to discover some new music that will blow my mind!", "span": "original song", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "What movie has the best soundtrack of all time?", "span": "soundtrack", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "What film is known for its iconic soundtrack with songs by The Beatles?", "span": "The Beatles", "entity_type": "Song", "correction_label": {"label": "__wrong_type__"}, "correction": "Artist", "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a film with a really catchy theme song, any suggestions?", "span": "catchy theme song", "entity_type": "Song", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a classic movie that has an iconic song in it, any recommendations?", "span": "iconic song", "entity_type": "Song", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What is the song featured in the end credits of the latest James Bond movie?", "span": "song", "entity_type": "Song", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}], "Review": [{"sentence": "Is there a movie with an amazing soundtrack available on any streaming platform?", "span": "amazing soundtrack", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a movie with an awesome soundtrack for a girls' night in?", "span": "awesome soundtrack", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What movie has the best soundtrack of all time?", "span": "best", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What movie has the best trailer of all time?", "span": "best", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": "Review", "span_index": null, "span_index_super": null}, {"sentence": "What is the best movie of the year so far, according to the critics?", "span": "best movie", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the science fiction film that won best picture in the 90s?", "span": "best picture", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a feel-good movie with a catchy soundtrack. Any suggestions?", "span": "catchy soundtrack", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Show me a trailer for a classic film that was both critically acclaimed and commercially successful.", "span": "critically acclaimed", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Is the movie The Shawshank Redemption really as good as people say it is", "span": "good as people say", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Are there any old movies with a great love song?", "span": "great love song", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm in the mood for a movie with a great soundtrack. Can you recommend a film with iconic songs from the 80s?", "span": "great soundtrack", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a popular action movie with great special effects. Can you suggest one?", "span": "great special effects", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed the most recent superhero movie with great special effects?", "span": "great special effects", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a movie with a top-notch plot and a gripping review?", "span": "gripping", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a critically acclaimed film with the highest number of nominations for Best Picture.", "span": "highest number of nominations for Best Picture", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I need a movie that will make me cry. Can you find a title with a heartwarming plot and a highly rated director?", "span": "make me cry", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a movie with a memorable soundtrack that is worth listening to?", "span": "memorable soundtrack", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I heard that the latest action film starring <PERSON> got mixed reviews. What's the story about anyway?", "span": "mixed reviews", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Which movie has the most exciting teaser of all time?", "span": "most exciting", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Which movie from 2019 received the most nominations?", "span": "most nominations", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What's the highest rated movie of all time?", "span": "movie of all time", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a movie from the 90s that was nominated for multiple Oscars, can you help me find one?", "span": "nominated for multiple Oscars", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Yo, did that movie with <PERSON> win any sick awards or get any nominations?", "span": "nominations", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you suggest a family-friendly film with a lot of nominations for major awards?", "span": "nominations for major awards", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I need to know the deal with the movie Pulp Fiction, can you give me a quick rundown?", "span": "quick rundown", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a highly-rated movie with a gripping plot and stunning visuals?", "span": "stunning visuals", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What movie from the 2010s has won the most awards?", "span": "won the most awards", "entity_type": "Review", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Which movie, directed by <PERSON>, has the most nominations for the Academy Awards?", "span": "Academy Awards", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Award", "span_index": null, "span_index_super": null}, {"sentence": "I heard there's a popular movie with a great soundtrack, is it available to stream anywhere?", "span": "available to stream", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Availability", "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me if the movie Mean Girls won any awards?", "span": "awards", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Viewers' Rating", "span_index": null, "span_index_super": null}, {"sentence": "Has the movie The Shawshank Redemption won any awards?", "span": "awards", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend any classic movies with a catchy song in the teaser?", "span": "catchy", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Song", "span_index": null, "span_index_super": null}, {"sentence": "Hey, what's the coolest action movie of all time?", "span": "coolest", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Viewers' Rating", "span_index": null, "span_index_super": null}, {"sentence": "What is the best movie of the year so far, according to the critics?", "span": "critics", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Viewers' Rating", "span_index": null, "span_index_super": null}, {"sentence": "Show me a preview of a sci-fi movie from the 90s that has a cult following", "span": "cult following", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "Hey, what's the deal with that movie everyone's gossiping about? Is it worth checking out?", "span": "everyone's gossiping about", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Viewers' Rating", "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me which film has received the highest viewers' rating recently?", "span": "highest viewers' rating", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Viewers' Rating", "span_index": null, "span_index_super": null}, {"sentence": "What are some highly-rated action movies from the 1990s?", "span": "highly-rated", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Viewers' Rating", "span_index": null, "span_index_super": null}, {"sentence": "What are some highly-rated movies from the 80s that are suitable for family viewing?", "span": "highly-rated", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Viewers' Rating", "span_index": null, "span_index_super": null}, {"sentence": "can you recommend a film with an iconic soundtrack", "span": "iconic", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Song", "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a film with an iconic soundtrack from the 80s?", "span": "iconic", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Viewers' Rating", "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a crime movie directed by <PERSON> that has a neo-noir style", "span": "neo-noir", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "What are some popular movies with amazing teasers?", "span": "popular", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Viewers' Rating", "span_index": null, "span_index_super": null}, {"sentence": "What are some iconic historical films directed by <PERSON> that I should watch?", "span": "should watch", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Viewers' Rating", "span_index": null, "span_index_super": null}, {"sentence": "What are the top-rated action films from the 90s?", "span": "top-rated", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Viewers' Rating", "span_index": null, "span_index_super": null}, {"sentence": "Who directed the top-rated romantic comedy of the past decade?", "span": "top-rated", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Viewers' Rating", "span_index": null, "span_index_super": null}, {"sentence": "Which movie directed by <PERSON> has the highest viewers' rating?", "span": "viewers' rating", "entity_type": "Review", "correction_label": {"label": "__wrong_type__"}, "correction": "Viewers' Rating", "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a movie starring <PERSON>?", "span": "recommend", "entity_type": "Review", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}], "Character": [{"sentence": "Show me some exclusive interviews with the cast of the movie Titanic.", "span": "cast", "entity_type": "Character", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can I buy tickets for the new James Bond film?", "span": "<PERSON>", "entity_type": "Character", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can I buy tickets for the new James Bond movie today?", "span": "<PERSON>", "entity_type": "Character", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can I watch the trailer for the new James Bond movie No Time to Die?", "span": "<PERSON>", "entity_type": "Character", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you hurry up and show me a trailer for the next James Bond movie already?", "span": "<PERSON>", "entity_type": "Character", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you play the trailer for the latest James Bond film?", "span": "<PERSON>", "entity_type": "Character", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me the plot of the latest James Bond film and who's playing the villain? I can't wait to find out!", "span": "<PERSON>", "entity_type": "Character", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Can you tell me the plot of the latest James Bond movie?", "span": "<PERSON>", "entity_type": "Character", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Hey, can you show me a trailer for the upcoming James Bond movie?", "span": "<PERSON>", "entity_type": "Character", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "I need a quick rundown of the plot for the latest James Bond film", "span": "<PERSON>", "entity_type": "Character", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Is there a teaser for the upcoming James Bond movie?", "span": "<PERSON>", "entity_type": "Character", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Hey, what's that super scary horror movie with the killer clown that came out in 2017?", "span": "killer clown", "entity_type": "Character", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who directed that awesome horror movie with a killer clown that scared the pants off everyone?", "span": "killer clown", "entity_type": "Character", "correction_label": {"label": "__correct__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "What's the deal with that movie where the dude from Avengers plays a spy or something?", "span": "spy", "entity_type": "Character", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "Could you recommend a family-friendly movie with a strong female character as the lead?", "span": "strong female character", "entity_type": "Character", "correction_label": {"label": "__wrong_type__"}, "correction": "other", "span_index": null, "span_index_super": null}, {"sentence": "Can you recommend a movie with high viewers' rating and a thrilling plot for young adults?", "span": "young adults", "entity_type": "Character", "correction_label": {"label": "__wrong_type__"}, "correction": "Viewer's Rating", "span_index": null, "span_index_super": null}, {"sentence": "Who directed the highest rated movie for young adults in the past 5 years?", "span": "young adults", "entity_type": "Character", "correction_label": {"label": "__wrong_type__"}, "correction": "Genre", "span_index": null, "span_index_super": null}, {"sentence": "I'm looking for a movie with a character who performs a memorable musical number. Can you suggest one?", "span": "a character", "entity_type": "Character", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}, {"sentence": "Who played the lead in that classic romance film?", "span": "lead", "entity_type": "Character", "correction_label": {"label": "__not_named_entity__"}, "correction": null, "span_index": null, "span_index_super": null}]}}