from src.generate.step_wise.entity_type2info_dict.util import CORRECT, WRONG_SPAN, WRONG_TYPE, NA


__all__ = ['entity_type2info_dict']


entity_type2info_dict = {
    '25-04-20_NER-Dataset_{fmt=n-p2,#l=50,lc=T}_privacy_bench': {
        '姓名': dict(
            defn=(
                '姓名实体是指个人的全名或部分名称，包括姓氏、名字或全名。'
                '姓名实体应该只包含人名本身，不应包含职位、称谓等额外信息。'
            ),
            name='姓名',
            name_full='姓名实体',
            demos=[
                dict(sentence="张三是一名来自北京的软件工程师。", entity_span="张三", label=CORRECT),
                dict(sentence="李教授将在下周三进行学术讲座。", entity_span="李教授", label=WRONG_SPAN, correct_span="李"),
                dict(sentence="这份文件需要王总签字确认。", entity_span="王", label=CORRECT),
            ]
        ),
        '年龄': dict(
            defn=(
                '年龄实体是指个人的年龄信息，包括具体数字或年龄段描述。'
                '年龄实体应包含完整的年龄表达，包括数字和单位（如"岁"、"年"等）。'
            ),
            demos=[
                dict(sentence="患者今年35岁，有高血压病史。", entity_span="35岁", label=CORRECT),
                dict(sentence="这位30多岁的男性需要进行体检。", entity_span="30多岁", label=CORRECT),
                dict(sentence="该研究针对18至25岁的年轻人群。", entity_span="18", label=WRONG_SPAN, correct_span="18至25岁"),
            ]
        ),
        '性别': dict(
            defn=(
                '性别实体是指个人的性别信息，如"男"、"女"、"男性"、"女性"等。'
                '性别实体应只包含性别本身，不应包含其他描述性词语。'
            ),
            demos=[
                dict(sentence="该患者是一名男性，现年45岁。", entity_span="男性", label=CORRECT),
                dict(sentence="招聘岗位不限男女，欢迎应聘。", entity_span="男女", label=CORRECT),
                dict(sentence="年轻女士请到3号窗口办理。", entity_span="年轻女士", label=WRONG_SPAN, correct_span="女士"),
            ]
        ),
        '国籍': dict(
            defn=(
                '国籍实体是指个人所属的国家或地区身份。'
                '国籍实体应包含完整的国家或地区名称。'
            ),
            demos=[
                dict(sentence="王先生是中国公民，持有效护照。", entity_span="中国", label=CORRECT),
                dict(sentence="该留学生来自法国巴黎。", entity_span="法国", label=CORRECT),
                dict(sentence="他是一名美籍华人企业家。", entity_span="美籍华人", label=WRONG_SPAN, correct_span="美籍"),
            ]
        ),
        '职业': dict(
            defn=(
                '职业实体是指个人的工作、职位或专业身份。'
                '职业实体应包含完整的职业描述，不应包含人名或组织名称。'
            ),
            demos=[
                dict(sentence="李明是一名软件工程师，在科技公司工作。", entity_span="软件工程师", label=CORRECT),
                dict(sentence="该医生有十年临床经验。", entity_span="医生", label=CORRECT),
                dict(sentence="张教授将主持本次会议。", entity_span="张教授", label=WRONG_SPAN, correct_span="教授"),
            ]
        ),
        '种族': dict(
            defn=(
                '种族实体是指人类学或社会学意义上的种族分类。'
                '种族实体应包含完整的种族描述，不应与民族混淆。'
            ),
            demos=[
                dict(sentence="研究对象包括亚洲人、非洲人和欧洲人。", entity_span="亚洲人", label=CORRECT),
                dict(sentence="该调查针对黑人社区的健康状况。", entity_span="黑人", label=CORRECT),
                dict(sentence="他是一名白人美国公民。", entity_span="白人美国", label=WRONG_SPAN, correct_span="白人"),
            ]
        ),
        '民族': dict(
            defn=(
                '民族实体是指具有共同文化、语言或历史的人群分类。'
                '民族实体应包含完整的民族名称，在中国语境下通常指56个民族。'
            ),
            demos=[
                dict(sentence="李娜是汉族，来自湖北省。", entity_span="汉族", label=CORRECT),
                dict(sentence="该地区主要居住着维吾尔族和哈萨克族。", entity_span="维吾尔族", label=CORRECT),
                dict(sentence="少数民族学生可以享受加分政策。", entity_span="少数民族", label=CORRECT),
            ]
        ),
        '教育背景': dict(
            defn=(
                '教育背景实体是指个人的学历、教育经历、专业背景等教育相关信息。'
                '教育背景实体应包含完整的教育描述，可以包括学校名称、学位、专业等。'
            ),
            demos=[
                dict(sentence="他拥有北京大学计算机科学学士学位。", entity_span="北京大学计算机科学学士学位", label=CORRECT),
                dict(sentence="她是哈佛大学商学院毕业的MBA。", entity_span="哈佛大学商学院MBA", label=CORRECT),
                dict(sentence="该职位要求应聘者具有硕士及以上学历。", entity_span="硕士及以上学历", label=CORRECT),
            ]
        ),
        '婚姻状况': dict(
            defn=(
                '婚姻状况实体是指个人的婚姻相关信息，如已婚、未婚、离异等。'
                '婚姻状况实体应只包含婚姻状态描述，不应包含其他个人信息。'
            ),
            demos=[
                dict(sentence="调查表显示他的婚姻状况为已婚。", entity_span="已婚", label=CORRECT),
                dict(sentence="该申请人是一名离异单身母亲。", entity_span="离异", label=CORRECT),
                dict(sentence="未婚人士可以申请单身公寓。", entity_span="未婚人士", label=WRONG_SPAN, correct_span="未婚"),
            ]
        ),
        '政治倾向': dict(
            defn=(
                '政治倾向实体是指个人的政治立场、党派归属或政治观点等信息。'
                '政治倾向实体应包含完整的政治描述，可以是具体的政党名称或政治立场。'
            ),
            demos=[
                dict(sentence="他是一名共产党员，有20年党龄。", entity_span="共产党员", label=CORRECT),
                dict(sentence="调查显示该地区选民多为保守派。", entity_span="保守派", label=CORRECT),
                dict(sentence="她持有进步主义观点，支持社会改革。", entity_span="进步主义", label=CORRECT),
            ]
        ),
        '家庭成员': dict(
            defn=(
                '家庭成员实体是指与个人有亲属关系的家庭成员信息，包括配偶、子女、父母等。'
                '家庭成员实体应包含家庭关系描述，可以包括具体的关系名称。'
            ),
            demos=[
                dict(sentence="他的妻子是一名小学教师。", entity_span="妻子", label=CORRECT),
                dict(sentence="李先生有两个孩子，一男一女。", entity_span="两个孩子", label=CORRECT),
                dict(sentence="她的父母都是医生，在同一家医院工作。", entity_span="父母", label=CORRECT),
            ]
        ),
        '工资数额': dict(
            defn=(
                '工资数额实体是指个人的薪资或收入金额，包括具体数字和单位。'
                '工资数额实体应包含完整的金额表达，包括数字和货币单位。'
            ),
            demos=[
                dict(sentence="他的月薪是15000元。", entity_span="15000元", label=CORRECT),
                dict(sentence="该职位提供年薪20万至30万不等。", entity_span="年薪20万至30万", label=CORRECT),
                dict(sentence="她的时薪是200元每小时。", entity_span="200元每小时", label=CORRECT),
            ]
        ),
        '投资产品': dict(
            defn=(
                '投资产品实体是指个人购买或持有的金融投资产品，包括股票、基金、债券等。'
                '投资产品实体应包含完整的产品名称或类型。'
            ),
            demos=[
                dict(sentence="他购买了100股阿里巴巴股票。", entity_span="阿里巴巴股票", label=CORRECT),
                dict(sentence="该客户持有易方达蓝筹基金。", entity_span="易方达蓝筹基金", label=CORRECT),
                dict(sentence="投资组合中包含国债和企业债券。", entity_span="国债", label=CORRECT),
            ]
        ),
        '税务记录': dict(
            defn=(
                '税务记录实体是指个人的纳税信息、税务申报记录或税收相关数据。'
                '税务记录实体应包含完整的税务信息描述。'
            ),
            demos=[
                dict(sentence="他的个人所得税申报表显示年收入30万元。", entity_span="个人所得税申报表", label=CORRECT),
                dict(sentence="该企业享受小微企业税收优惠政策。", entity_span="小微企业税收优惠", label=CORRECT),
                dict(sentence="她去年缴纳了5万元的个税。", entity_span="5万元的个税", label=CORRECT),
            ]
        ),
        '信用记录': dict(
            defn=(
                '信用记录实体是指个人的信用评级、信用历史或信用相关信息。'
                '信用记录实体应包含完整的信用信息描述。'
            ),
            demos=[
                dict(sentence="他的信用评分是820分，属于优秀级别。", entity_span="信用评分是820分", label=CORRECT),
                dict(sentence="该客户有一次信用卡逾期记录。", entity_span="信用卡逾期记录", label=CORRECT),
                dict(sentence="银行将查询申请人的个人征信报告。", entity_span="个人征信报告", label=CORRECT),
            ]
        ),
        '实体资产': dict(
            defn=(
                '实体资产实体是指个人拥有的实物资产，如房产、车辆、贵重物品等。'
                '实体资产实体应包含完整的资产描述。'
            ),
            demos=[
                dict(sentence="他在北京海淀区拥有一套120平米的住房。", entity_span="一套120平米的住房", label=CORRECT),
                dict(sentence="该客户名下有一辆奔驰C级轿车。", entity_span="奔驰C级轿车", label=CORRECT),
                dict(sentence="她收藏了价值50万的钻石首饰。", entity_span="价值50万的钻石首饰", label=CORRECT),
            ]
        ),
        '交易信息': dict(
            defn=(
                '交易信息实体是指个人的消费记录、转账记录或其他金融交易信息。'
                '交易信息实体应包含完整的交易描述。'
            ),
            demos=[
                dict(sentence="他昨天在京东消费了2000元。", entity_span="在京东消费了2000元", label=CORRECT),
                dict(sentence="该账户每月向父母转账5000元。", entity_span="每月向父母转账5000元", label=CORRECT),
                dict(sentence="她的信用卡月消费额度为3万元。", entity_span="信用卡月消费额度为3万元", label=CORRECT),
            ]
        ),
        '疾病': dict(
            defn=(
                '疾病实体是指医学诊断的疾病名称或健康问题。'
                '疾病实体应包含完整的疾病名称，可以包括具体的疾病类型。'
            ),
            demos=[
                dict(sentence="患者被诊断为2型糖尿病。", entity_span="2型糖尿病", label=CORRECT),
                dict(sentence="他有高血压和冠心病病史。", entity_span="高血压", label=CORRECT),
                dict(sentence="该研究针对肺炎患者的治疗效果。", entity_span="肺炎", label=CORRECT),
            ]
        ),
        '药物': dict(
            defn=(
                '药物实体是指医疗用途的药品名称，包括处方药、非处方药等。'
                '药物实体应包含完整的药品名称，可以包括具体的剂量信息。'
            ),
            demos=[
                dict(sentence="医生开了阿司匹林和降压药。", entity_span="阿司匹林", label=CORRECT),
                dict(sentence="患者每日服用20mg立普妥。", entity_span="20mg立普妥", label=CORRECT),
                dict(sentence="该药店有售布洛芬止痛片。", entity_span="布洛芬止痛片", label=CORRECT),
            ]
        ),
        '临床表现': dict(
            defn=(
                '临床表现实体是指疾病或健康问题的症状、体征或临床特征。'
                '临床表现实体应包含完整的症状描述。'
            ),
            demos=[
                dict(sentence="患者出现持续高烧三天的症状。", entity_span="持续高烧三天", label=CORRECT),
                dict(sentence="检查显示血压165/95，属于高血压范围。", entity_span="血压165/95", label=CORRECT),
                dict(sentence="他报告有剧烈头痛和恶心症状。", entity_span="剧烈头痛和恶心", label=CORRECT),
            ]
        ),
        '医疗程序': dict(
            defn=(
                '医疗程序实体是指医疗检查、治疗方案、手术或其他医疗干预措施。'
                '医疗程序实体应包含完整的医疗程序描述。'
            ),
            demos=[
                dict(sentence="患者需要进行核磁共振检查。", entity_span="核磁共振检查", label=CORRECT),
                dict(sentence="医生建议进行冠状动脉搭桥手术。", entity_span="冠状动脉搭桥手术", label=CORRECT),
                dict(sentence="治疗方案包括物理治疗和药物治疗。", entity_span="物理治疗", label=CORRECT),
            ]
        ),
        '过敏信息': dict(
            defn=(
                '过敏信息实体是指个人对特定物质、药物或环境因素的过敏反应。'
                '过敏信息实体应包含完整的过敏描述。'
            ),
            demos=[
                dict(sentence="患者对青霉素过敏，表现为皮疹。", entity_span="对青霉素过敏", label=CORRECT),
                dict(sentence="她有花粉过敏史，春季症状加重。", entity_span="花粉过敏史", label=CORRECT),
                dict(sentence="病历显示该患者对海鲜过敏。", entity_span="对海鲜过敏", label=CORRECT),
            ]
        ),
        '生育信息': dict(
            defn=(
                '生育信息实体是指与生育、怀孕、生殖健康相关的个人信息。'
                '生育信息实体应包含完整的生育相关描述。'
            ),
            demos=[
                dict(sentence="患者目前已怀孕28周。", entity_span="已怀孕28周", label=CORRECT),
                dict(sentence="她于2018年进行过剖腹产手术。", entity_span="2018年进行过剖腹产", label=CORRECT),
                dict(sentence="病历显示患者有三次流产经历。", entity_span="三次流产经历", label=CORRECT),
            ]
        ),
        '地理位置': dict(
            defn=(
                '地理位置实体是指个人所在或曾到访的地理位置，包括具体地址、区域等。'
                '地理位置实体应包含完整的位置描述。'
            ),
            demos=[
                dict(sentence="他住在北京市海淀区中关村南大街5号。", entity_span="北京市海淀区中关村南大街5号", label=CORRECT),
                dict(sentence="公司位于上海浦东新区张江高科技园区。", entity_span="上海浦东新区张江高科技园区", label=CORRECT),
                dict(sentence="她经常在北京三里屯购物。", entity_span="北京三里屯", label=CORRECT),
            ]
        ),
        '行程信息': dict(
            defn=(
                '行程信息实体是指个人的出行计划、旅行记录或行动轨迹。'
                '行程信息实体应包含完整的行程描述。'
            ),
            demos=[
                dict(sentence="他计划下周前往上海出差三天。", entity_span="下周前往上海出差三天", label=CORRECT),
                dict(sentence="她每天7点从家出发到公司。", entity_span="每天7点从家出发到公司", label=CORRECT),
                dict(sentence="行程显示他昨日乘坐G105次高铁。", entity_span="昨日乘坐G105次高铁", label=CORRECT),
            ]
        )
    }
}
