# NER数据集质量评估模块

本模块提供全面的NER数据集质量评估功能，包括均衡性验证和多样性验证。

## 功能特性

### 1. 均衡性验证
- **实体分布统计**：计算各类预定义敏感实体的实例数量和相对比例
- **目标分布比较**：将实际分布与生成策略中设定的目标分布进行比较
- **覆盖率检查**：确保所有目标实体类型都有足够的样本
- **分布差异分析**：识别缺失和多余的实体类型

### 2. 多样性验证
- **词汇多样性**：基于jieba分词计算词汇丰富度
- **句法多样性**：分析句子结构和语法模式
- **语义多样性**：基于TF-IDF向量相似度评估语义差异
- **上下文多样性**：评估句子上下文模式的丰富程度
- **实体多样性**：分析实体使用的多样性和唯一性

### 3. 可视化报告
- **分布对比图**：实际分布与目标分布的对比
- **多样性雷达图**：多维度多样性指标的可视化
- **实体多样性热力图**：各实体类型的多样性得分
- **评估总结图**：整体评估结果的图表展示
- **HTML报告**：交互式的评估报告

## 文件结构

```
src/synth_eval/
├── quality_evaluation.py      # 核心评估逻辑
├── visualization.py           # 可视化功能
├── main_evaluation.py         # 主评估脚本
├── evaluation_config.json     # 评估配置
└── README.md                  # 说明文档
```

## 安装依赖

```bash
pip install jieba numpy matplotlib seaborn scikit-learn
```

## 使用方法

### 1. 命令行使用

```bash
# 基本评估
python src/synth_eval/main_evaluation.py path/to/dataset.json

# 指定输出目录
python src/synth_eval/main_evaluation.py path/to/dataset.json --output-dir results/

# 不生成图表
python src/synth_eval/main_evaluation.py path/to/dataset.json --no-plots

# 不生成HTML报告
python src/synth_eval/main_evaluation.py path/to/dataset.json --no-html
```

### 2. Python API使用

```python
from src.synth_eval.quality_evaluation import evaluate_dataset_quality
from src.synth_eval.visualization import create_comprehensive_report

# 执行评估
evaluation_result = evaluate_dataset_quality("path/to/dataset.json")

# 生成可视化报告
create_comprehensive_report(evaluation_result, "output_dir")
```

### 3. 单独使用评估模块

```python
from src.synth_eval.quality_evaluation import evaluate_balance, evaluate_diversity

# 只执行均衡性验证
balance_result = evaluate_balance(dataset, target_counts)

# 只执行多样性验证
diversity_result = evaluate_diversity(dataset)
```

## 配置参数

在 `evaluation_config.json` 中可以调整以下参数：

### 均衡性验证阈值
- `distribution_tolerance`: 分布差异容忍度 (默认: 0.15)
- `min_coverage_ratio`: 最小覆盖率 (默认: 0.8)
- `max_missing_entities`: 最大缺失实体数 (默认: 2)
- `max_excess_entities`: 最大多余实体数 (默认: 3)

### 多样性验证阈值
- `vocabulary_diversity`: 词汇多样性阈值 (默认: 0.6)
- `syntactic_diversity`: 句法多样性阈值 (默认: 0.5)
- `semantic_diversity`: 语义多样性阈值 (默认: 0.4)
- `context_diversity`: 上下文多样性阈值 (默认: 0.5)
- `entity_diversity`: 实体多样性阈值 (默认: 0.7)

## 输出文件

评估完成后会生成以下文件：

```
evaluation_results_YYYYMMDD_HHMMSS/
├── evaluation_report.json          # JSON格式详细报告
├── evaluation_report.html          # HTML格式可视化报告
├── evaluation_summary.txt          # 文本格式总结
└── plots/                          # 可视化图表目录
    ├── distribution_comparison.png # 分布对比图
    ├── diversity_radar.png         # 多样性雷达图
    ├── entity_diversity_heatmap.png # 实体多样性热力图
    └── evaluation_summary.png      # 评估总结图
```

## 评估指标说明

### 均衡性指标
- **整体得分**: 0-1之间的分数，越接近1表示分布越均衡
- **覆盖率**: 实际包含的实体类型占目标类型的比例
- **分布差异**: 每个实体类型的实际比例与目标比例的差异

### 多样性指标
- **词汇多样性**: 唯一词汇数占总词汇数的比例
- **句法多样性**: 使用的句法模式占所有可能模式的比例
- **语义多样性**: 基于TF-IDF相似度的语义差异度
- **上下文多样性**: 句子上下文模式的丰富程度
- **实体多样性**: 唯一实体数占总实体数的比例

## 评估结果解读

### 通过标准
- **均衡性验证通过**: 整体得分 ≥ 0.85 且 覆盖率 ≥ 0.8
- **多样性验证通过**: 所有多样性指标都达到预设阈值
- **整体通过**: 均衡性和多样性验证都通过

### 改进建议
- **均衡性不足**: 调整生成策略，确保各实体类型分布更均衡
- **多样性不足**: 增加句子模板和实体池的多样性
- **覆盖率不足**: 检查实体生成逻辑，确保所有目标类型都有样本

## 注意事项

1. **数据格式**: 输入数据集必须是JSON格式，包含 `text` 和 `label` 字段
2. **中文支持**: 模块使用jieba进行中文分词，确保中文文本正确处理
3. **内存使用**: 大型数据集可能需要较多内存，特别是语义多样性计算
4. **依赖库**: 确保安装了所有必要的Python库

## 故障排除

### 常见问题

#### 1. 模块导入错误
**错误信息**：`ModuleNotFoundError: No module named 'src'`

**解决方案**：
```bash
# 方法1：在synth_eval目录下运行
cd src/synth_eval
python main_evaluation.py path/to/dataset.json

# 方法2：使用测试脚本验证
cd src/synth_eval
python test_evaluation.py

# 方法3：设置PYTHONPATH
export PYTHONPATH=$PYTHONPATH:$(pwd)/src
python src/synth_eval/main_evaluation.py path/to/dataset.json
```

#### 2. 字体显示问题
如果图表中文显示异常，请安装中文字体：
```bash
# Ubuntu/Debian
sudo apt-get install fonts-wqy-microhei

# CentOS/RHEL
sudo yum install wqy-microhei-fonts

# Windows
# 确保系统安装了中文字体，如SimHei
```

#### 3. 内存不足
对于大型数据集，可以关闭语义多样性计算：
```python
# 在quality_evaluation.py中注释掉语义多样性计算
# semantic_diversity = calculate_semantic_diversity(texts)
semantic_diversity = 0.0  # 临时禁用
```

#### 4. 依赖库缺失
确保安装了所有必要的Python库：
```bash
pip install jieba numpy matplotlib seaborn scikit-learn
```

### 调试模式
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 扩展功能

模块设计为可扩展的，可以轻松添加新的评估指标：

1. 在 `quality_evaluation.py` 中添加新的评估函数
2. 在 `evaluation_config.json` 中添加相应的阈值配置
3. 在 `visualization.py` 中添加对应的可视化功能

## 贡献指南

欢迎提交问题和改进建议！请确保：
1. 代码符合PEP 8规范
2. 添加适当的文档和注释
3. 包含测试用例
4. 更新相关文档 