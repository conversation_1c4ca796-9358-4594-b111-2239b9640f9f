[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成10个多样化的、可以归类为性别的命名实体，每行一个。这些实体将用于命名实体识别任务中的性别类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 56}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Male\n2. Female\n3. Non-binary\n4. Genderqueer\n5. Transgender\n6. Intersex\n7. Agender\n8. Bigender\n9. Cisgender\n10. Two-Spirit", "role": "assistant"}}], "created": 1745476999, "id": "202504241443175ea7ca7489fd4435", "model": "glm-4-air-250414", "request_id": "202504241443175ea7ca7489fd4435", "usage": {"completion_tokens": 52, "prompt_tokens": 47, "total_tokens": 99}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成15个多样化的、可以归类为种族的命名实体，每行一个。这些实体将用于命名实体识别任务中的种族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 77}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\nAfrican American  \nAsian American  \nCaucasian  \nHispanic  \nNative American  \nPacific Islander  \nBlack  \nWhite  \nLatino  \nIndigenous  \nMiddle Eastern  \nSouth Asian  \nEast Asian  \nCaribbean  \nAfro-Caribbean", "role": "assistant"}}], "created": 1745476999, "id": "202504241443177c6f161b55b547b7", "model": "glm-4-air-250414", "request_id": "202504241443177c6f161b55b547b7", "usage": {"completion_tokens": 51, "prompt_tokens": 47, "total_tokens": 98}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成15个多样化的、可以归类为姓名的命名实体，每行一个。这些实体将用于命名实体识别任务中的姓名类型。\n请生成多样化的中文姓名，包括不同性别、年龄段和地区特色的姓名。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 42}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n张伟  \n李娜  \n王芳  \n刘洋  \n陈晨  \n赵磊  \n孙悦  \n周涛  \n吴敏  \n郑宇  \n马丽  \n朱峰  \n胡静  \n高翔  \n林雪", "role": "assistant"}}], "created": 1745476999, "id": "20250424144317bd9755fd5b224ce6", "model": "glm-4-air-250414", "request_id": "20250424144317bd9755fd5b224ce6", "usage": {"completion_tokens": 46, "prompt_tokens": 63, "total_tokens": 109}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成20个多样化的、可以归类为职业的命名实体，每行一个。这些实体将用于命名实体识别任务中的职业类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 420}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n数据科学家  \n软件工程师  \n市场分析师  \n财务顾问  \n人力资源经理  \n建筑师  \n记者  \n医生  \n律师  \n教师  \n项目经理  \n厨师  \n设计师  \n销售代表  \n机械师  \n演员  \n音乐家  \n护士  \n程序员", "role": "assistant"}}], "created": 1745476999, "id": "20250424144317f453913112404287", "model": "glm-4-air-250414", "request_id": "20250424144317f453913112404287", "usage": {"completion_tokens": 47, "prompt_tokens": 47, "total_tokens": 94}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成15个多样化的、可以归类为国籍的命名实体，每行一个。这些实体将用于命名实体识别任务中的国籍类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 63}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. American  \n2. Chinese  \n3. British  \n4. German  \n5. Japanese  \n6. Indian  \n7. Brazilian  \n8. French  \n9. Canadian  \n10. Australian  \n11. Russian  \n12. Mexican  \n13. Italian  \n14. Spanish  \n15. South Korean", "role": "assistant"}}], "created": 1745476999, "id": "202504241443173e6c523c9a304968", "model": "glm-4-air-250414", "request_id": "202504241443173e6c523c9a304968", "usage": {"completion_tokens": 62, "prompt_tokens": 47, "total_tokens": 109}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成15个多样化的、可以归类为疾病的命名实体，每行一个。这些实体将用于命名实体识别任务中的疾病类型。\n请生成多样化的疾病名称，包括常见病、慢性病、罕见病等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 161}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n高血压  \n糖尿病  \n冠心病  \n哮喘  \n骨质疏松  \n抑郁症  \n阿尔茨海默病  \n系统性红斑狼疮  \n帕金森病  \n多发性硬化  \n淋巴瘤  \n囊性纤维化  \n亨廷顿舞蹈症  \n莱姆病  \n渐冻症（肌萎缩侧索硬化症）", "role": "assistant"}}], "created": 1745476999, "id": "20250424144317611bff52d9274c94", "model": "glm-4-air-250414", "request_id": "20250424144317611bff52d9274c94", "usage": {"completion_tokens": 63, "prompt_tokens": 63, "total_tokens": 126}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成10个多样化的、可以归类为政治倾向的命名实体，每行一个。这些实体将用于命名实体识别任务中的政治倾向类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 105}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 民主党支持者  \n2. 共和党选民  \n3. 左翼激进派  \n4. 右翼保守派  \n5. 自由主义联盟  \n6. 传统主义组织  \n7. 绿色和平行动  \n8. 蓝色阵营代表  \n9. 工党成员  \n10. 保守党顾问", "role": "assistant"}}], "created": 1745476999, "id": "2025042414431799efdd508fd74154", "model": "glm-4-air-250414", "request_id": "2025042414431799efdd508fd74154", "usage": {"completion_tokens": 76, "prompt_tokens": 49, "total_tokens": 125}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成10个多样化的、可以归类为性别的命名实体，每行一个。这些实体将用于命名实体识别任务中的性别类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 581}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 张伟  \n2. 李娜  \n3. 王芳  \n4. 刘强  \n5. 陈静  \n6. 杨阳  \n7. 赵雪  \n8. 周杰  \n9. 吴敏  \n10. 郑宇", "role": "assistant"}}], "created": 1745476999, "id": "2025042414431761bd9476152d4e35", "model": "glm-4-air-250414", "request_id": "2025042414431761bd9476152d4e35", "usage": {"completion_tokens": 59, "prompt_tokens": 46, "total_tokens": 105}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成10个多样化的、可以归类为性别的命名实体，每行一个。这些实体将用于命名实体识别任务中的性别类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 231}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 男士  \n2. 女性  \n3. 男性  \n4. 女性患者  \n5. 男性医生  \n6. 女性护理员  \n7. 男童  \n8. 女婴  \n9. 非二元性别  \n10. 跨性别男性", "role": "assistant"}}], "created": 1745476999, "id": "2025042414431795a5f0a8496b4d48", "model": "glm-4-air-250414", "request_id": "2025042414431795a5f0a8496b4d48", "usage": {"completion_tokens": 65, "prompt_tokens": 47, "total_tokens": 112}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成15个多样化的、可以归类为国籍的命名实体，每行一个。这些实体将用于命名实体识别任务中的国籍类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 238}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 中国\n2. 美国\n3. 英国\n4. 加拿大\n5. 澳大利亚\n6. 日本\n7. 韩国\n8. 法国\n9. 德国\n10. 意大利\n11. 印度\n12. 巴西\n13. 俄罗斯\n14. 墨西哥\n15. 南非", "role": "assistant"}}], "created": 1745476999, "id": "20250424144317d8b05b1de98841b9", "model": "glm-4-air-250414", "request_id": "20250424144317d8b05b1de98841b9", "usage": {"completion_tokens": 75, "prompt_tokens": 47, "total_tokens": 122}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成10个多样化的、可以归类为婚姻状况的命名实体，每行一个。这些实体将用于命名实体识别任务中的婚姻状况类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 98}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 已婚（配偶在世）  \n2. 丧偶  \n3. 离异  \n4. 未婚  \n5. 分居中  \n6. 同居（非法定婚姻）  \n7. 单身（从未结婚）  \n8. 民事结合  \n9. 已婚（配偶长期失踪）  \n10. 暂时婚姻（如某些宗教或文化下的短暂结合形式）", "role": "assistant"}}], "created": 1745477000, "id": "202504241443177e68f9f9c93d4a9c", "model": "glm-4-air-250414", "request_id": "202504241443177e68f9f9c93d4a9c", "usage": {"completion_tokens": 91, "prompt_tokens": 49, "total_tokens": 140}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成15个多样化的、可以归类为种族的命名实体，每行一个。这些实体将用于命名实体识别任务中的种族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 252}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n以下是15个多样化的种族命名实体，每行一个，适用于命名实体识别任务中的种族类型分类：\n\n1. African American  \n2. Hispanic  \n3. Asian  \n4. Caucasian  \n5. Native American  \n6. Pacific Islander  \n7. Middle Eastern  \n8. South Asian  \n9. East Asian  \n10. Latino  \n11. Indigenous  \n12. Mixed Race  \n13. Black  \n14. White  \n15. Southeast Asian", "role": "assistant"}}], "created": 1745477000, "id": "20250424144317ae2c753c03fd4750", "model": "glm-4-air-250414", "request_id": "20250424144317ae2c753c03fd4750", "usage": {"completion_tokens": 92, "prompt_tokens": 47, "total_tokens": 139}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成15个多样化的、可以归类为民族的命名实体，每行一个。这些实体将用于命名实体识别任务中的民族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 434}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 汉族  \n2. 藏族  \n3. 壮族  \n4. 蒙古族  \n5. 回族  \n6. 维吾尔族  \n7. 苗族  \n8. 彝族  \n9. 土家族  \n10. 朝鲜族  \n11. 满族  \n12. 布依族  \n13. 侗族  \n14. 瑶族  \n15. 白族", "role": "assistant"}}], "created": 1745477000, "id": "202504241443178a162f0658a443eb", "model": "glm-4-air-250414", "request_id": "202504241443178a162f0658a443eb", "usage": {"completion_tokens": 96, "prompt_tokens": 46, "total_tokens": 142}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成15个多样化的、可以归类为临床表现的命名实体，每行一个。这些实体将用于命名实体识别任务中的临床表现类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 525}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 胸痛  \n2. 发热  \n3. 呼吸困难  \n4. 头痛  \n5. 腹泻  \n6. 咳嗽  \n7. 疲劳  \n8. 肌肉疼痛  \n9. 恶心  \n10. 意识模糊  \n11. 关节肿胀  \n12. 视力模糊  \n13. 瘙痒  \n14. 尿频  \n15. 失眠", "role": "assistant"}}], "created": 1745477000, "id": "20250424144317c580db8387ac4588", "model": "glm-4-air-250414", "request_id": "20250424144317c580db8387ac4588", "usage": {"completion_tokens": 103, "prompt_tokens": 47, "total_tokens": 150}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成15个多样化的、可以归类为生育信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的生育信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 546}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 妊娠周数  \n2. 胎儿心率  \n3. 孕期血糖水平  \n4. 妊娠并发症记录  \n5. 分娩方式偏好  \n6. 新生儿出生体重  \n7. 产后恢复计划  \n8. 人工授精日期  \n9. 辅助生殖技术类型  \n10. 避孕方法选择  \n11. 月经周期记录  \n12. 卵巢储备功能测试  \n13. 子宫内膜厚度  \n14. 胎儿性别预测  \n15. 生育保险覆盖范围", "role": "assistant"}}], "created": 1745477000, "id": "202504241443177a7e77929de747ee", "model": "glm-4-air-250414", "request_id": "202504241443177a7e77929de747ee", "usage": {"completion_tokens": 123, "prompt_tokens": 48, "total_tokens": 171}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成15个多样化的、可以归类为过敏信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的过敏信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 189}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 花生过敏史\n2. 鱼类过敏记录\n3. 青霉素过敏信息\n4. 哮喘发作记录\n5. 乳制品不耐受档案\n6. 芒果过敏病例\n7. 橡胶接触过敏文档\n8. 尘螨过敏详情\n9. 磺胺类药物过敏\n10. 哮喘治疗史\n11. 蛋白质过敏信息\n12. 蜜蜂蜇伤过敏记录\n13. 小麦过敏数据\n14. 某种食物过敏源标识\n15. 特定药物过敏警告", "role": "assistant"}}], "created": 1745477000, "id": "20250424144317adbf1d5b62de4008", "model": "glm-4-air-250414", "request_id": "20250424144317adbf1d5b62de4008", "usage": {"completion_tokens": 139, "prompt_tokens": 48, "total_tokens": 187}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成15个多样化的、可以归类为临床表现的命名实体，每行一个。这些实体将用于命名实体识别任务中的临床表现类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 175}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 头痛  \n2. 发热  \n3. 呼吸困难  \n4. 胸痛  \n5. 恶心呕吐  \n6. 腹泻  \n7. 关节疼痛  \n8. 意识模糊  \n9. 皮疹  \n10. 心悸  \n11. 眩晕  \n12. 疲劳  \n13. 尿频  \n14. 咳嗽  \n15. 肌肉痉挛", "role": "assistant"}}], "created": 1745477000, "id": "202504241443174c29eb7b4d2e4c09", "model": "glm-4-air-250414", "request_id": "202504241443174c29eb7b4d2e4c09", "usage": {"completion_tokens": 101, "prompt_tokens": 47, "total_tokens": 148}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成20个多样化的、可以归类为年龄的命名实体，每行一个。这些实体将用于命名实体识别任务中的年龄类型。\n请生成多样化的年龄表达，包括具体数字和年龄段描述。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 224}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 25岁  \n2. 三十多岁  \n3. 18岁  \n4. 中年  \n5. 七旬  \n6. 40岁  \n7. 少年  \n8. 五十上下  \n9. 12岁  \n10. 老年  \n11. 60岁  \n12. 青少年  \n13. 30岁出头  \n14. 90岁以上  \n15. 50岁  \n16. 幼年  \n17. 不满18岁  \n18. 80岁  \n19. 二十岁左右  \n20. 婴儿", "role": "assistant"}}], "created": 1745477000, "id": "2025042414431782505b20c7eb47ca", "model": "glm-4-air-250414", "request_id": "2025042414431782505b20c7eb47ca", "usage": {"completion_tokens": 125, "prompt_tokens": 60, "total_tokens": 185}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成15个多样化的、可以归类为生育信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的生育信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 196}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 孕周数  \n2. 流产记录  \n3. 产前检查  \n4. 妊娠并发症  \n5. 剖腹产史  \n6. 辅助生殖技术  \n7. 人工授精记录  \n8. 子宫切除术  \n9. 生育年龄  \n10. 产后抑郁  \n11. 卵巢早衰  \n12. 子宫肌瘤  \n13. 宫颈筛查  \n14. 胎儿性别  \n15. 不孕不育诊断", "role": "assistant"}}], "created": 1745477000, "id": "2025042414431793affabe06904923", "model": "glm-4-air-250414", "request_id": "2025042414431793affabe06904923", "usage": {"completion_tokens": 108, "prompt_tokens": 48, "total_tokens": 156}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成15个多样化的、可以归类为药物的命名实体，每行一个。这些实体将用于命名实体识别任务中的药物类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 168}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 降压灵  \n2. 心得安  \n3. 安乃近  \n4. 头孢克洛  \n5. 阿司匹林  \n6. 布洛芬  \n7. 氯雷他定  \n8. 地西泮  \n9. 甲硝唑  \n10. 红霉素  \n11. 青霉素G  \n12. 胰岛素  \n13. 阿托伐他汀  \n14. 呋塞米  \n15. 多潘立酮", "role": "assistant"}}], "created": 1745477000, "id": "20250424144317e09e3331823b4ad2", "model": "glm-4-air-250414", "request_id": "20250424144317e09e3331823b4ad2", "usage": {"completion_tokens": 114, "prompt_tokens": 46, "total_tokens": 160}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成15个多样化的、可以归类为药物的命名实体，每行一个。这些实体将用于命名实体识别任务中的药物类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 518}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 布洛芬  \n2. 阿司匹林  \n3. 吗啡  \n4. 氯雷他定  \n5. 头孢克肟  \n6. 硝酸甘油  \n7. 甲状腺素  \n8. 地西泮  \n9. 阿托伐他汀  \n10. 甲氨蝶呤  \n11. 伏立康唑  \n12. 红霉素  \n13. 丙戊酸钠  \n14. 硫酸锌  \n15. 胰岛素", "role": "assistant"}}], "created": 1745477000, "id": "20250424144317fe93cd2246d84f29", "model": "glm-4-air-250414", "request_id": "20250424144317fe93cd2246d84f29", "usage": {"completion_tokens": 119, "prompt_tokens": 46, "total_tokens": 165}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成15个多样化的、可以归类为医疗程序的命名实体，每行一个。这些实体将用于命名实体识别任务中的医疗程序类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 357}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 磁共振成像 (MRI)  \n2. 心脏搭桥手术  \n3. PET-CT扫描  \n4. 腹腔镜手术  \n5. 放射治疗  \n6. 腰椎穿刺  \n7. 体外受精  \n8. 针灸疗法  \n9. 肾脏移植  \n10. 内窥镜检查  \n11. 骨髓穿刺  \n12. 透析治疗  \n13. 超声波检查  \n14. 脑电图监测  \n15. 麻醉手术", "role": "assistant"}}], "created": **********, "id": "20250424144317181b4d087fb64be4", "model": "glm-4-air-250414", "request_id": "20250424144317181b4d087fb64be4", "usage": {"completion_tokens": 120, "prompt_tokens": 48, "total_tokens": 168}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成20个多样化的、可以归类为职业的命名实体，每行一个。这些实体将用于命名实体识别任务中的职业类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 245}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 医生  \n2. 护士  \n3. 医疗顾问  \n4. 数据分析师  \n5. 医疗研究员  \n6. 诊所经理  \n7. 药剂师  \n8. 放射科医生  \n9. 外科医生  \n10. 健康顾问  \n11. 医疗信息官  \n12. 医疗统计师  \n13. 急救人员  \n14. 健康档案管理员  \n15. 呼吸治疗师  \n16. 医疗软件开发师  \n17. 物理治疗师  \n18. 医疗伦理师  \n19. 职业治疗师  \n20. 公共卫生专家", "role": "assistant"}}], "created": **********, "id": "2025042414431731806757d217404e", "model": "glm-4-air-250414", "request_id": "2025042414431731806757d217404e", "usage": {"completion_tokens": 149, "prompt_tokens": 47, "total_tokens": 196}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成20个多样化的、可以归类为信用记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的信用记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 140}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 个人信用报告  \n2. 信用评分  \n3. 信用历史  \n4. 信用档案  \n5. 负债记录  \n6. 逾期记录  \n7. 公共记录  \n8. 查询记录  \n9. 信用账户  \n10. 信用卡记录  \n11. 贷款历史  \n12. 还款记录  \n13. 破产记录  \n14. 法院判决记录  \n15. 税务留置记录  \n16. 信用额度使用率  \n17. 债务收入比  \n18. 信用查询历史  \n19. 信用违约记录  \n20. 信用恢复记录", "role": "assistant"}}], "created": **********, "id": "20250424144317219ca69caaed40dd", "model": "glm-4-air-250414", "request_id": "20250424144317219ca69caaed40dd", "usage": {"completion_tokens": 141, "prompt_tokens": 49, "total_tokens": 190}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成25个多样化的、可以归类为行程信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的行程信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 560}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Flight Ticket\n2. Boarding Pass\n3. Itinerary Details\n4. Hotel Reservation\n5. Car Rental Confirmation\n6. Train Schedule\n7. Flight Number\n8. Departure Time\n9. Arrival Location\n10. Travel Insurance Policy\n11. Cruise Booking\n12. Bus Pass\n13. Airport Code\n14. Luggage Tag\n15. Accommodation Details\n16. Visa Application\n17. Route Map\n18. Hotel Check-out Time\n19. Travel Itinerary\n20. Ferry Ticket\n21. Layover Information\n22. Airport Shuttle Booking\n23. Rental Car License Plate\n24. Flight Delay Notice\n25. Trip Confirmation Number", "role": "assistant"}}], "created": **********, "id": "202504241443179f34b5079b0a4c78", "model": "glm-4-air-250414", "request_id": "202504241443179f34b5079b0a4c78", "usage": {"completion_tokens": 142, "prompt_tokens": 48, "total_tokens": 190}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成25个多样化的、可以归类为交易信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的交易信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 154}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 转账金额  \n2. 支付日期  \n3. 交易类型  \n4. 收款人姓名  \n5. 发卡行名称  \n6. 卡号后四位  \n7. 交易时间戳  \n8. 汇率信息  \n9. 交易手续费  \n10. 扣款账户  \n11. 交易流水号  \n12. 交易状态  \n13. 货币类型  \n14. 原始金额  \n15. 到账时间  \n16. 退款金额  \n17. 交易地点  \n18. 商户代码  \n19. 交易描述  \n20. 支付方式  \n21. 交易限额  \n22. 审核人姓名  \n23. 交易备注  \n24. 验证码  \n25. 银行代码", "role": "assistant"}}], "created": **********, "id": "20250424144317dbc8e0a2dc974363", "model": "glm-4-air-250414", "request_id": "20250424144317dbc8e0a2dc974363", "usage": {"completion_tokens": 172, "prompt_tokens": 48, "total_tokens": 220}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成20个多样化的、可以归类为职业的命名实体，每行一个。这些实体将用于命名实体识别任务中的职业类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 70}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 首席财务官\n2. 财务分析师\n3. 税务顾问\n4. 会计师\n5. 审计师\n6. 投资银行家\n7. 风险管理师\n8. 金融工程师\n9. 资产管理经理\n10. 财务规划师\n11. 证券分析师\n12. 保险精算师\n13. 银行行长\n14. 投资经理\n15. 预算分析师\n16. 财务总监\n17. 合规专员\n18. 金融顾问\n19. 资本市场分析师\n20. 风险评估师", "role": "assistant"}}], "created": **********, "id": "20250424144317a2245b0160e940fe", "model": "glm-4-air-250414", "request_id": "20250424144317a2245b0160e940fe", "usage": {"completion_tokens": 152, "prompt_tokens": 47, "total_tokens": 199}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成20个多样化的、可以归类为教育背景的命名实体，每行一个。这些实体将用于命名实体识别任务中的教育背景类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 91}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Bachelor of Economics  \n2. Master of Business Administration  \n3. Doctor of Philosophy in Finance  \n4. Chartered Financial Analyst Certification  \n5. Certified Public Accountant Degree  \n6. Bachelor of Science in Accounting  \n7. Master of Science in Financial Engineering  \n8. Doctor of Business Administration  \n9. Certified Financial Planner Qualification  \n10. Bachelor of Arts in Economics  \n11. Master of International Business  \n12. Doctor of Commerce in Finance  \n13. Certified Management Accountant License  \n14. Bachelor of Business Administration  \n15. Master of Taxation  \n16. Doctor of Economic Sciences  \n17. Professional Accounting Diploma  \n18. Master of Financial Management  \n19. Bachelor of Financial Analysis  \n20. Certified Internal Auditor Credential", "role": "assistant"}}], "created": **********, "id": "202504241443176035d272f70b4032", "model": "glm-4-air-250414", "request_id": "202504241443176035d272f70b4032", "usage": {"completion_tokens": 149, "prompt_tokens": 49, "total_tokens": 198}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成20个多样化的、可以归类为税务记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的税务记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 133}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 个人所得税申报表  \n2. 企业所得税纳税记录  \n3. 增值税专用发票  \n4. 个人所得税缴纳凭证  \n5. 企业所得税汇算清缴报告  \n6. 增值税普通发票  \n7. 个人税务身份识别号  \n8. 企业税务登记证  \n9. 增值税抵扣明细表  \n10. 个人所得税减免申请  \n11. 企业所得税年度审计报告  \n12. 增值税专用发票抵扣联  \n13. 个人税务申报历史记录  \n14. 企业税务稽查报告  \n15. 增值税应纳税额计算表  \n16. 个人所得税预缴记录  \n17. 企业税务风险自查表  \n18. 增值税发票查验记录  \n19. 个人税务合规声明  \n20. 企业税务优惠政策申请", "role": "assistant"}}], "created": **********, "id": "2025042414431782a660a36a344e26", "model": "glm-4-air-250414", "request_id": "2025042414431782a660a36a344e26", "usage": {"completion_tokens": 177, "prompt_tokens": 49, "total_tokens": 226}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成15个多样化的、可以归类为医疗程序的命名实体，每行一个。这些实体将用于命名实体识别任务中的医疗程序类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 182}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 心脏瓣膜置换术  \n2. 腰椎间盘突出微创手术  \n3. 肺癌根治性切除术  \n4. 骨髓移植治疗  \n5. 人工关节置换手术  \n6. 胆囊切除术  \n7. 甲状腺功能亢进射频消融术  \n8. 胃癌根治术  \n9. 糖尿病足清创缝合术  \n10. 脑胶质瘤切除术  \n11. 白内障超声乳化术  \n12. 宫颈癌根治性放疗  \n13. 颈动脉内膜剥脱术  \n14. 前列腺增生激光剜除术  \n15. 肾结石体外冲击波碎石术", "role": "assistant"}}], "created": **********, "id": "202504241443177c04103e8ae04098", "model": "glm-4-air-250414", "request_id": "202504241443177c04103e8ae04098", "usage": {"completion_tokens": 155, "prompt_tokens": 48, "total_tokens": 203}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成20个多样化的、可以归类为税务记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的税务记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 308}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 税务申报表  \n2. 个人所得税记录  \n3. 企业所得税申报单  \n4. 增值税专用发票  \n5. 税务登记证  \n6. 税务缴纳凭证  \n7. 税务审计报告  \n8. 税务减免申请表  \n9. 税务滞纳金通知  \n10. 税务罚款记录  \n11. 税务完税证明  \n12. 税务抵扣凭证  \n13. 税务稽查记录  \n14. 税务行政复议书  \n15. 税务行政复议决定书  \n16. 税务行政复议申请书  \n17. 税务行政复议受理通知书  \n18. 税务行政复议答辩书  \n19. 税务行政复议决定书  \n20. 税务行政复议申请书", "role": "assistant"}}], "created": **********, "id": "202504241443174d4ab784c377470b", "model": "glm-4-air-250414", "request_id": "202504241443174d4ab784c377470b", "usage": {"completion_tokens": 187, "prompt_tokens": 49, "total_tokens": 236}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成20个多样化的、可以归类为投资产品的命名实体，每行一个。这些实体将用于命名实体识别任务中的投资产品类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 651}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 增利宝  \n2. 稳盈基金  \n3. 财富通  \n4. 盈利宝  \n5. 稳健成长计划  \n6. 高收益债券  \n7. 优选股票组合  \n8. 稳健理财计划  \n9. 资产增值基金  \n10. 保险连结投资  \n11. 定期存款产品  \n12. 互联网理财通  \n13. 智能投顾组合  \n14. 退休金计划  \n15. 私募股权基金  \n16. 黄金ETF  \n17. 股票指数基金  \n18. 房地产信托投资  \n19. 债券指数基金  \n20. 货币市场基金", "role": "assistant"}}], "created": **********, "id": "20250424144317a04ae45593c8470f", "model": "glm-4-air-250414", "request_id": "20250424144317a04ae45593c8470f", "usage": {"completion_tokens": 160, "prompt_tokens": 47, "total_tokens": 207}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成20个多样化的、可以归类为实体资产的命名实体，每行一个。这些实体将用于命名实体识别任务中的实体资产类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 322}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 电子健康记录系统  \n2. 医院信息系统数据库  \n3. 患者主索引表  \n4. 电子病历文档  \n5. 医疗影像存储库  \n6. 生物识别数据集  \n7. 遗传信息档案  \n8. 疫苗接种记录表  \n9. 药品处方数据库  \n10. 保险索赔文件  \n11. 医疗账单记录  \n12. 手术日志系统  \n13. 病人随访数据集  \n14. 临床试验结果库  \n15. 医疗设备使用日志  \n16. 病历分析报告  \n17. 医疗人员资质档案  \n18. 诊断编码数据库  \n19. 慢性病管理档案  \n20. 儿童健康监测数据", "role": "assistant"}}], "created": **********, "id": "202504241443173f35fea9ef9744ad", "model": "glm-4-air-250414", "request_id": "202504241443173f35fea9ef9744ad", "usage": {"completion_tokens": 170, "prompt_tokens": 48, "total_tokens": 218}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成20个多样化的、可以归类为投资产品的命名实体，每行一个。这些实体将用于命名实体识别任务中的投资产品类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 301}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 安盛健康投资信托基金  \n2. 蓝筹医疗股票指数基金  \n3. 稳健医疗债券组合计划  \n4. 生命科学成长型投资产品  \n5. 健康产业优先收益证券  \n6. 生物医药创新投资基金  \n7. 医疗设备专项理财产品  \n8. 养老医疗稳健收益计划  \n9. 互联网医疗概念ETF  \n10. 医疗保险费率挂钩票据  \n11. 跨境医疗股权投资基金  \n12. 慢病管理服务收益凭证  \n13. 基因检测技术成长基金  \n14. 专科医院建设债券计划  \n15. 医疗AI技术应用信托  \n16. 中药产业指数增强产品  \n17. 医疗耗材供应链基金  \n18. 老龄化服务收益债券  \n19. 疫苗研发主题投资产品  \n20. 医疗健康ESG策略基金", "role": "assistant"}}], "created": **********, "id": "2025042414431744b1473783be4f1e", "model": "glm-4-air-250414", "request_id": "2025042414431744b1473783be4f1e", "usage": {"completion_tokens": 198, "prompt_tokens": 48, "total_tokens": 246}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成15个多样化的、可以归类为疾病的命名实体，每行一个。这些实体将用于命名实体识别任务中的疾病类型。\n请生成多样化的疾病名称，包括常见病、慢性病、罕见病等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 336}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 糖尿病  \n2. 高血压  \n3. 肺结核  \n4. 风湿性关节炎  \n5. 帕金森病  \n6. 系统性红斑狼疮  \n7. 阿尔茨海默病  \n8. 先天性心脏病  \n9. 甲状腺功能减退症  \n10. 多发性硬化  \n11. 克罗恩病  \n12. 艾滋病  \n13. 白血病  \n14. 银屑病  \n15. 贝赫切特综合征", "role": "assistant"}}], "created": 1745477002, "id": "20250424144319b9d1fa49fe45484a", "model": "glm-4-air-250414", "request_id": "20250424144319b9d1fa49fe45484a", "usage": {"completion_tokens": 117, "prompt_tokens": 63, "total_tokens": 180}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成25个多样化的、可以归类为交易信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的交易信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 504}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 信用卡号\n2. 购物车ID\n3. 交易金额\n4. 订单编号\n5. 支付方式\n6. 发票号码\n7. 物流单号\n8. 商品价格\n9. 退款记录\n10. 交易时间戳\n11. 签名文件\n12. 验证码\n13. 银行账号\n14. 结账确认码\n15. 账单地址\n16. 购物积分\n17. 优惠券代码\n18. 税务识别号\n19. 支付期限\n20. 电子钱包余额\n21. 换货凭证\n22. 预授权码\n23. 账单周期\n24. 运费明细\n25. 交易备注", "role": "assistant"}}], "created": 1745477002, "id": "20250424144317a0573aa862bf4f9b", "model": "glm-4-air-250414", "request_id": "20250424144317a0573aa862bf4f9b", "usage": {"completion_tokens": 178, "prompt_tokens": 48, "total_tokens": 226}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成15个多样化的、可以归类为家庭成员的命名实体，每行一个。这些实体将用于命名实体识别任务中的家庭成员类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 112}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n张伟的父亲  \n李娜的母亲  \n王强的妻子  \n刘洋的兄弟  \n陈红的女儿  \n赵峰的儿子  \n黄静的姐姐  \n周杰的弟弟  \n吴芳的丈夫  \n孙亮的侄子  \n杨雪的姑姑  \n郑阳的叔叔  \n马丽的阿姨  \n钱进的侄女  \n朱婷的堂兄", "role": "assistant"}}], "created": 1745477002, "id": "202504241443202ea0d810070e496b", "model": "glm-4-air-250414", "request_id": "202504241443202ea0d810070e496b", "usage": {"completion_tokens": 71, "prompt_tokens": 47, "total_tokens": 118}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成15个多样化的、可以归类为过敏信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的过敏信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 364}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 青霉素过敏\n2. 花粉不耐受\n3. 奶制品过敏反应\n4. 螨虫过敏史\n5. 海鲜过敏症状\n6. 鸡蛋过敏记录\n7. 面粉过敏检测\n8. 猫毛过敏测试\n9. 青霉素类抗生素过敏\n10. 树木花粉过敏源\n11. 鱼类食物过敏史\n12. 小麦制品过敏反应\n13. 蟑螂过敏记录\n14. 水果类过敏症状\n15. 花生过敏检测", "role": "assistant"}}], "created": 1745477002, "id": "2025042414431811ee952fc9b344ba", "model": "glm-4-air-250414", "request_id": "2025042414431811ee952fc9b344ba", "usage": {"completion_tokens": 135, "prompt_tokens": 48, "total_tokens": 183}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成20个多样化的、可以归类为信用记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的信用记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 490}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 个人信用报告  \n2. 信用评分记录  \n3. 信用卡使用历史  \n4. 贷款申请记录  \n5. 公共记录信息  \n6. 逾期还款记录  \n7. 查询历史记录  \n8. 银行账户信用信息  \n9. 负面信用事件  \n10. 信用额度使用情况  \n11. 债务清算记录  \n12. 信用修复历史  \n13. 破产记录信息  \n14. 信用额度调整记录  \n15. 信用报告更新日志  \n16. 信用卡还款详情  \n17. 贷款还款状态  \n18. 信用授权查询记录  \n19. 信用异议处理记录  \n20. 信用风险评估报告", "role": "assistant"}}], "created": 1745477002, "id": "2025042414431716cbec77a4bd454e", "model": "glm-4-air-250414", "request_id": "2025042414431716cbec77a4bd454e", "usage": {"completion_tokens": 159, "prompt_tokens": 49, "total_tokens": 208}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成20个多样化的、可以归类为税务记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的税务记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 483}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 个人所得税申报表\n2. 企业所得税汇算清缴报告\n3. 增值税专用发票\n4. 印花税完税证明\n5. 车船税缴纳凭证\n6. 土地增值税清算表\n7. 契税缴纳记录\n8. 房产税纳税申报单\n9. 资源税计算表\n10. 环境保护税申报表\n11. 城市维护建设税凭证\n12. 教育费附加缴纳记录\n13. 地方教育附加证明\n14. 储备金缴款书\n15. 境外所得税收抵免表\n16. 预提所得税扣缴凭证\n17. 股权转让个人所得税申报表\n18. 税收减免审批文件\n19. 税务稽查报告\n20. 税务登记证", "role": "assistant"}}], "created": 1745477002, "id": "202504241443177dbef6883e6c4d06", "model": "glm-4-air-250414", "request_id": "202504241443177dbef6883e6c4d06", "usage": {"completion_tokens": 198, "prompt_tokens": 49, "total_tokens": 247}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成25个多样化的、可以归类为行程信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的行程信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 735}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 出发地坐标\n2. 目的地经纬度\n3. 行车路线编号\n4. 公交线路站点\n5. 铁路时刻表\n6. 旅行时间戳\n7. 途经城市列表\n8. 飞机航班号\n9. 停留地点标记\n10. 地铁换乘节点\n11. 驾驶方向指南\n12. 徒步路径坐标\n13. 水路航线信息\n14. 自行车租赁点\n15. 旅行日历记录\n16. 行程总里程\n17. 燃油消耗数据\n18. 导航语音提示\n19. 停车场位置\n20. 高速公路出口\n21. 国际旅行签证\n22. 旅游景点打卡\n23. 行李托运记录\n24. 车辆追踪日志\n25. 地理围栏区域", "role": "assistant"}}], "created": 1745477002, "id": "20250424144317cb214ea8b13c4c97", "model": "glm-4-air-250414", "request_id": "20250424144317cb214ea8b13c4c97", "usage": {"completion_tokens": 195, "prompt_tokens": 47, "total_tokens": 242}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成25个多样化的、可以归类为交易信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的交易信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 329}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 医疗账单编号  \n2. 保险理赔记录  \n3. 药品采购订单  \n4. 就诊费用明细  \n5. 医疗服务合同号  \n6. 住院结算单  \n7. 检查项目收费单  \n8. 医疗设备租赁协议  \n9. 手术费用结算表  \n10. 医保报销凭证  \n11. 药品销售发票  \n12. 医疗卡支付记录  \n13. 咨询服务交易ID  \n14. 化验单费用条目  \n15. 电子处方配药凭证  \n16. 康复治疗费用清单  \n17. 体检套餐订单号  \n18. 远程医疗会诊收费  \n19. 医疗保险理赔编号  \n20. 物理治疗支付记录  \n21. 医疗保险索赔文件  \n22. 专科门诊缴费单  \n23. 医疗服务采购合同  \n24. 病历复印费用凭证  \n25. 医疗服务折扣记录", "role": "assistant"}}], "created": 1745477002, "id": "202504241443179b7b41467102422e", "model": "glm-4-air-250414", "request_id": "202504241443179b7b41467102422e", "usage": {"completion_tokens": 218, "prompt_tokens": 48, "total_tokens": 266}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成20个多样化的、可以归类为信用记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的信用记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 315}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 信用卡账户\n2. 贷款还款记录\n3. 信用评分\n4. 账单支付历史\n5. 信用额度\n6. 负债总额\n7. 逾期记录\n8. 公共记录\n9. 信用查询记录\n10. 信用卡逾期天数\n11. 贷款拖欠情况\n12. 债务重组历史\n13. 信用报告条目\n14. 征信报告信息\n15. 银行账户信用\n16. 信用历史长度\n17. 贷款违约记录\n18. 信用恢复计划\n19. 个人信用档案\n20. 信用风险评估", "role": "assistant"}}], "created": 1745477002, "id": "20250424144319a7ce520ac2334906", "model": "glm-4-air-250414", "request_id": "20250424144319a7ce520ac2334906", "usage": {"completion_tokens": 149, "prompt_tokens": 49, "total_tokens": 198}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成20个多样化的、可以归类为信用记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的信用记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 665}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 信用评分记录  \n2. 信用报告摘要  \n3. 账单支付历史  \n4. 信用卡账户详情  \n5. 贷款申请记录  \n6. 债务违约信息  \n7. 公共记录查询  \n8. 逾期账单列表  \n9. 信用额度使用情况  \n10. 贷款还款跟踪  \n11. 信用查询日志  \n12. 财务债务概况  \n13. 信用修复档案  \n14. 预审批贷款记录  \n15. 信用评分变化趋势  \n16. 银行账户关联信用  \n17. 破产记录信息  \n18. 信用监控报告  \n19. 负面信用标记  \n20. 信用验证文档", "role": "assistant"}}], "created": 1745477003, "id": "202504241443174213c91a20ce4224", "model": "glm-4-air-250414", "request_id": "202504241443174213c91a20ce4224", "usage": {"completion_tokens": 163, "prompt_tokens": 48, "total_tokens": 211}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成25个多样化的、可以归类为工资数额的命名实体，每行一个。这些实体将用于命名实体识别任务中的工资数额类型。\n请生成多样化的工资金额表达，包括月薪、年薪、时薪等不同计量方式。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 119}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 月薪8千  \n2. 年薪30万  \n3. 时薪50元  \n4. 月入1.5万  \n5. 年薪45万人民币  \n6. 每小时80美元  \n7. 月薪9千5百  \n8. 年薪50万+  \n9. 时薪60英镑  \n10. 月薪12万  \n11. 年薪40万美金  \n12. 时薪100人民币  \n13. 月薪1万2千  \n14. 年薪35万  \n15. 时薪75欧元  \n16. 月薪8千5百  \n17. 年薪25万  \n18. 时薪90人民币  \n19. 月薪15万  \n20. 年薪60万  \n21. 时薪100加元  \n22. 月薪1万  \n23. 年薪20万  \n24. 时薪85美元  \n25. 月薪10万", "role": "assistant"}}], "created": 1745477004, "id": "202504241443200ee9bd35b2f24ad2", "model": "glm-4-air-250414", "request_id": "202504241443200ee9bd35b2f24ad2", "usage": {"completion_tokens": 190, "prompt_tokens": 69, "total_tokens": 259}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成20个多样化的、可以归类为实体资产的命名实体，每行一个。这些实体将用于命名实体识别任务中的实体资产类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 147}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 固定资产  \n2. 存货  \n3. 现金  \n4. 有价证券  \n5. 机器设备  \n6. 土地使用权  \n7. 车辆  \n8. 房屋建筑  \n9. 应收账款  \n10. 长期投资  \n11. 库存现金  \n12. 电子设备  \n13. 专利权  \n14. 无形资产  \n15. 货币资金  \n16. 工具器具  \n17. 原材料  \n18. 短期投资  \n19. 存货跌价准备  \n20. 产成品", "role": "assistant"}}], "created": 1745477004, "id": "2025042414432135d6589704d648ce", "model": "glm-4-air-250414", "request_id": "2025042414432135d6589704d648ce", "usage": {"completion_tokens": 136, "prompt_tokens": 48, "total_tokens": 184}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成15个多样化的、可以归类为药物的命名实体，每行一个。这些实体将用于命名实体识别任务中的药物类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 343}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 布洛芬  \n2. 阿莫西林  \n3. 辛伐他汀  \n4. 降糖灵  \n5. 甲硝唑  \n6. 复方甘草片  \n7. 青霉素  \n8. 阿司匹林  \n9. 红霉素  \n10. 氯雷他定  \n11. 速效救心丸  \n12. 降压灵  \n13. 诺氟沙星  \n14. 骨刺宁胶囊  \n15. 维生素C", "role": "assistant"}}], "created": 1745477004, "id": "202504241443211a2ea35284df486a", "model": "glm-4-air-250414", "request_id": "202504241443211a2ea35284df486a", "usage": {"completion_tokens": 117, "prompt_tokens": 46, "total_tokens": 163}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成20个多样化的、可以归类为地理位置的命名实体，每行一个。这些实体将用于命名实体识别任务中的地理位置类型。\n请生成多样化的地理位置，包括省市区县、街道地址、建筑物名称等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 203}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n上海市  \n北京市朝阳区  \n广东省深圳市南山区  \n江苏省南京市建邺区  \n浙江省杭州市西湖区曙光路  \n英国伦敦  \n法国巴黎香榭丽舍大街  \n美国纽约曼哈顿  \n加拿大温哥华  \n澳大利亚悉尼  \n日本东京  \n俄罗斯莫斯科克里姆林宫  \n印度新德里  \n巴西里约热内卢  \n德国柏林  \n西班牙巴塞罗那  \n新加坡  \n中国香港特别行政区  \n中国澳门特别行政区  \n陕西省西安市碑林区和平门里仁街", "role": "assistant"}}], "created": 1745477005, "id": "202504241443222cde9da854924518", "model": "glm-4-air-250414", "request_id": "202504241443222cde9da854924518", "usage": {"completion_tokens": 102, "prompt_tokens": 64, "total_tokens": 166}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成20个多样化的、可以归类为教育背景的命名实体，每行一个。这些实体将用于命名实体识别任务中的教育背景类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 266}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 本科医学学位  \n2. 医学博士学位  \n3. 医学院毕业证书  \n4. 硕士公共卫生学位  \n5. 临床医学专业毕业  \n6. 博士后研究经历  \n7. 护理学学士学位  \n8. 健康信息管理学位  \n9. 药学博士学位  \n10. 口腔医学专业毕业  \n11. 生物医学工程硕士  \n12. 公共卫生学院进修  \n13. 医学影像学培训证书  \n14. 营养学博士学位  \n15. 健康管理学士学位  \n16. 临床心理学研究生  \n17. 医学检验技术文凭  \n18. 老年医学专业认证  \n19. 麻醉学住院医师培训  \n20. 健康政策与法律硕士", "role": "assistant"}}], "created": 1745477005, "id": "2025042414432112fbe695c4c84162", "model": "glm-4-air-250414", "request_id": "2025042414432112fbe695c4c84162", "usage": {"completion_tokens": 171, "prompt_tokens": 49, "total_tokens": 220}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成20个多样化的、可以归类为地理位置的命名实体，每行一个。这些实体将用于命名实体识别任务中的地理位置类型。\n请生成多样化的地理位置，包括省市区县、街道地址、建筑物名称等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 728}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 北京市  \n2. 上海市长宁区  \n3. 广州市天河区  \n4. 深圳市南山区  \n5. 杭州市西湖区  \n6. 成都市锦江区  \n7. 南京市玄武区  \n8. 武汉市江汉区  \n9. 西安市雁塔区  \n10. 重庆市渝中区  \n11. 天津市和平区  \n12. 苏州市姑苏区  \n13. 宁波市鄞州区  \n14. 合肥市蜀山区  \n15. 富春街道  \n16. 中山路  \n17. 香格里拉大酒店  \n18. 上海环球金融中心  \n19. 北京故宫  \n20. 深圳平安金融中心", "role": "assistant"}}], "created": 1745477006, "id": "2025042414432388956df016a64744", "model": "glm-4-air-250414", "request_id": "2025042414432388956df016a64744", "usage": {"completion_tokens": 146, "prompt_tokens": 63, "total_tokens": 209}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成15个多样化的、可以归类为民族的命名实体，每行一个。这些实体将用于命名实体识别任务中的民族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 84}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 汉族\n2. 蒙古族\n3. 回族\n4. 藏族\n5. 维吾尔族\n6. 苗族\n7. 彝族\n8. 壮族\n9. 布依族\n10. 朝鲜族\n11. 满族\n12. 侗族\n13. 瑶族\n14. 白族\n15. 土家族", "role": "assistant"}}], "created": 1745477006, "id": "2025042414432395bda1e920c84377", "model": "glm-4-air-250414", "request_id": "2025042414432395bda1e920c84377", "usage": {"completion_tokens": 96, "prompt_tokens": 46, "total_tokens": 142}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成15个多样化的、可以归类为姓名的命名实体，每行一个。这些实体将用于命名实体识别任务中的姓名类型。\n请生成多样化的中文姓名，包括不同性别、年龄段和地区特色的姓名。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 217}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n张伟  \n李娜  \n王芳  \n刘洋  \n陈明  \n赵静  \n杨帆  \n周丽  \n吴强  \n孙雪  \n郑宇  \n朱琳  \n马超  \n胡敏  \n郭峰", "role": "assistant"}}], "created": 1745477007, "id": "20250424144326f78795e57ea94608", "model": "glm-4-air-250414", "request_id": "20250424144326f78795e57ea94608", "usage": {"completion_tokens": 46, "prompt_tokens": 63, "total_tokens": 109}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成15个多样化的、可以归类为疾病的命名实体，每行一个。这些实体将用于命名实体识别任务中的疾病类型。\n请生成多样化的疾病名称，包括常见病、慢性病、罕见病等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 686}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n高血压  \n糖尿病  \n哮喘  \n阿尔茨海默病  \n帕金森病  \n多发性硬化症  \n系统性红斑狼疮  \n镰状细胞病  \n亨廷顿舞蹈症  \n结节病  \n重症肌无力  \n克罗恩病  \n肌萎缩侧索硬化症  \n囊性纤维化  \n血友病", "role": "assistant"}}], "created": 1745477007, "id": "20250424144325502534109d094115", "model": "glm-4-air-250414", "request_id": "20250424144325502534109d094115", "usage": {"completion_tokens": 66, "prompt_tokens": 62, "total_tokens": 128}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成10个多样化的、可以归类为婚姻状况的命名实体，每行一个。这些实体将用于命名实体识别任务中的婚姻状况类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 273}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 未婚  \n2. 已婚  \n3. 离异  \n4. 丧偶  \n5. 同居  \n6. 单身  \n7. 已订婚  \n8. 分居  \n9. 再婚  \n10. 民政局登记", "role": "assistant"}}], "created": 1745477007, "id": "20250424144325bc72019a1989466b", "model": "glm-4-air-250414", "request_id": "20250424144325bc72019a1989466b", "usage": {"completion_tokens": 58, "prompt_tokens": 49, "total_tokens": 107}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成10个多样化的、可以归类为政治倾向的命名实体，每行一个。这些实体将用于命名实体识别任务中的政治倾向类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 280}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 进步联盟  \n2. 保守派协会  \n3. 民主先锋  \n4. 自由之声  \n5. 传统价值联盟  \n6. 激进改革党  \n7. 中立论坛  \n8. 环保激进组织  \n9. 工人阶级联盟  \n10. 宪法捍卫者", "role": "assistant"}}], "created": 1745477007, "id": "20250424144322b18eec04723441c4", "model": "glm-4-air-250414", "request_id": "20250424144322b18eec04723441c4", "usage": {"completion_tokens": 74, "prompt_tokens": 49, "total_tokens": 123}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成20个多样化的、可以归类为投资产品的命名实体，每行一个。这些实体将用于命名实体识别任务中的投资产品类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 126}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 股票型基金  \n2. 债券指数基金  \n3. 黄金ETF  \n4. 混合型理财产品  \n5. 定期存款计划  \n6. 房地产信托基金  \n7. 股权投资基金  \n8. 优先股投资组合  \n9. 外汇交易账户  \n10. 资产支持证券  \n11. 期货合约产品  \n12. 环保主题基金  \n13. 大宗商品指数基金  \n14. 银行结构性存款  \n15. 消费金融资产包  \n16. 知识产权投资信托  \n17. 可转换债券基金  \n18. 数字货币投资计划  \n19. 海外房产基金  \n20. 健康医疗主题ETF", "role": "assistant"}}], "created": 1745477007, "id": "20250424144324d516589b20cd4327", "model": "glm-4-air-250414", "request_id": "20250424144324d516589b20cd4327", "usage": {"completion_tokens": 163, "prompt_tokens": 48, "total_tokens": 211}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成15个多样化的、可以归类为家庭成员的命名实体，每行一个。这些实体将用于命名实体识别任务中的家庭成员类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 287}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 父亲\n2. 母亲\n3. 兄弟\n4. 姐妹\n5. 儿子\n6. 女儿\n7. 祖父\n8. 祖母\n9. 外祖父\n10. 外祖母\n11. 丈夫\n12. 妻子\n13. 侄子\n14. 侄女\n15. 孙子", "role": "assistant"}}], "created": 1745477008, "id": "202504241443269812e6c2d7d9405c", "model": "glm-4-air-250414", "request_id": "202504241443269812e6c2d7d9405c", "usage": {"completion_tokens": 95, "prompt_tokens": 47, "total_tokens": 142}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成15个多样化的、可以归类为临床表现的命名实体，每行一个。这些实体将用于命名实体识别任务中的临床表现类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 350}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 胸闷  \n2. 头痛  \n3. 发热  \n4. 呼吸困难  \n5. 腹痛  \n6. 恶心  \n7. 咳嗽  \n8. 眩晕  \n9. 肌肉酸痛  \n10. 疲劳  \n11. 皮疹  \n12. 关节肿胀  \n13. 视物模糊  \n14. 声音嘶哑  \n15. 排尿困难", "role": "assistant"}}], "created": 1745477008, "id": "202504241443233a63c66740014abd", "model": "glm-4-air-250414", "request_id": "202504241443233a63c66740014abd", "usage": {"completion_tokens": 104, "prompt_tokens": 47, "total_tokens": 151}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成15个多样化的、可以归类为民族的命名实体，每行一个。这些实体将用于命名实体识别任务中的民族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 259}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 汉族  \n2. 蒙古族  \n3. 回族  \n4. 藏族  \n5. 维吾尔族  \n6. 苗族  \n7. 彝族  \n8. 壮族  \n9. 布依族  \n10. 朝鲜族  \n11. 满族  \n12. 侗族  \n13. 瑶族  \n14. 白族  \n15. 土家族", "role": "assistant"}}], "created": 1745477008, "id": "202504241443261b3231347b4f4c38", "model": "glm-4-air-250414", "request_id": "202504241443261b3231347b4f4c38", "usage": {"completion_tokens": 96, "prompt_tokens": 46, "total_tokens": 142}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成25个多样化的、可以归类为行程信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的行程信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 210}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 航班号  \n2. 机场代码  \n3. 航班日期  \n4. 出发城市  \n5. 到达城市  \n6. 旅客姓名  \n7. 航班时间  \n8. 座位号  \n9. 机票预订号  \n10. 退改签记录  \n11. 行李信息  \n12. 机上Wi-Fi密码  \n13. 登机口号码  \n14. 航班延误通知  \n15. 飞行路线  \n16. 航空公司名称  \n17. 旅行保险编号  \n18. 登机牌条形码  \n19. 里程积分  \n20. 机上餐食选项  \n21. 航班动态跟踪  \n22. 旅客护照号  \n23. 飞行时间  \n24. 机场安检记录  \n25. 候机室位置", "role": "assistant"}}], "created": 1745477009, "id": "2025042414432446a06af85dca49e4", "model": "glm-4-air-250414", "request_id": "2025042414432446a06af85dca49e4", "usage": {"completion_tokens": 190, "prompt_tokens": 48, "total_tokens": 238}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成10个多样化的、可以归类为性别的命名实体，每行一个。这些实体将用于命名实体识别任务中的性别类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 406}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Mr. Johnson\n2. Queen Elizabeth II\n3. Transgender Activist\n4. Non-binary individual\n5. She/her pronouns\n6. He/him identity\n7. Genderfluid person\n8. Agender symbol\n9. Genderqueer advocate\n10. cisgender male", "role": "assistant"}}], "created": 1745477010, "id": "20250424144328a8bfc6d78994412d", "model": "glm-4-air-250414", "request_id": "20250424144328a8bfc6d78994412d", "usage": {"completion_tokens": 65, "prompt_tokens": 47, "total_tokens": 112}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成15个多样化的、可以归类为生育信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的生育信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 371}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 子宫内膜厚度  \n2. 精液分析报告  \n3. 孕酮水平  \n4. 输卵管造影结果  \n5. 卵泡发育监测数据  \n6. 人工授精记录  \n7. 辅助生殖技术同意书  \n8. 流产史记录  \n9. 宫颈筛查结果  \n10. 胎儿生长曲线  \n11. 母体血样检测结果  \n12. 胚胎移植日志  \n13. 睾酮浓度测量  \n14. 妊娠周数确认  \n15. 生育能力评估报告", "role": "assistant"}}], "created": 1745477010, "id": "2025042414432874a0f06446e947fb", "model": "glm-4-air-250414", "request_id": "2025042414432874a0f06446e947fb", "usage": {"completion_tokens": 130, "prompt_tokens": 48, "total_tokens": 178}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成15个多样化的、可以归类为种族的命名实体，每行一个。这些实体将用于命名实体识别任务中的种族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 427}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. AfricanAmerican\n2. Hispanic\n3. Asian\n4. Caucasian\n5. NativeAmerican\n6. PacificIslander\n7. MiddleEastern\n8. SouthAsian\n9. EastAsian\n10. Indigenous\n11. Black\n12. White\n13. Latino\n14. Arab\n15. Multiracial", "role": "assistant"}}], "created": 1745477010, "id": "20250424144329df4ebb21638e4954", "model": "glm-4-air-250414", "request_id": "20250424144329df4ebb21638e4954", "usage": {"completion_tokens": 70, "prompt_tokens": 47, "total_tokens": 117}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成15个多样化的、可以归类为家庭成员的命名实体，每行一个。这些实体将用于命名实体识别任务中的家庭成员类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 462}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 父亲  \n2. 母亲  \n3. 哥哥  \n4. 弟弟  \n5. 姐姐  \n6. 妹妹  \n7. 祖父  \n8. 祖母  \n9. 外祖父  \n10. 外祖母  \n11. 伯父  \n12. 叔父  \n13. 舅舅  \n14. 姨母  \n15. 侄子", "role": "assistant"}}], "created": 1745477011, "id": "20250424144329c749723f897c4dad", "model": "glm-4-air-250414", "request_id": "20250424144329c749723f897c4dad", "usage": {"completion_tokens": 98, "prompt_tokens": 47, "total_tokens": 145}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成10个多样化的、可以归类为婚姻状况的命名实体，每行一个。这些实体将用于命名实体识别任务中的婚姻状况类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 448}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n离婚人士  \n单身贵族  \n已婚人士  \n离异状态  \n丧偶人士  \n同居伴侣  \n事实婚姻  \n未婚状态  \n再婚人士  \n未登记婚姻", "role": "assistant"}}], "created": 1745477011, "id": "2025042414433043cd9c7cb22e46be", "model": "glm-4-air-250414", "request_id": "2025042414433043cd9c7cb22e46be", "usage": {"completion_tokens": 35, "prompt_tokens": 49, "total_tokens": 84}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成10个多样化的、可以归类为政治倾向的命名实体，每行一个。这些实体将用于命名实体识别任务中的政治倾向类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 455}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 民主先锋组织  \n2. 保守主义联盟  \n3. 自由主义论坛  \n4. 中立派智库  \n5. 社会主义研究协会  \n6. 进步主义联盟  \n7. 保守派政策研究中心  \n8. 激进民主联盟  \n9. 传统价值观联盟  \n10. 激进左派行动组织", "role": "assistant"}}], "created": 1745477011, "id": "20250424144329c136a21c771a442f", "model": "glm-4-air-250414", "request_id": "20250424144329c136a21c771a442f", "usage": {"completion_tokens": 81, "prompt_tokens": 49, "total_tokens": 130}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成25个多样化的、可以归类为工资数额的命名实体，每行一个。这些实体将用于命名实体识别任务中的工资数额类型。\n请生成多样化的工资金额表达，包括月薪、年薪、时薪等不同计量方式。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 294}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 月薪8000元  \n2. 年薪50万元  \n3. 时薪50美元  \n4. 月收入12000人民币  \n5. 年收入80万人民币  \n6. 每小时150元  \n7. 月薪10K  \n8. 年薪12万美元  \n9. 每月2000欧元  \n10. 年薪1500000日元  \n11. 月薪9000人民币  \n12. 年薪100万人民币  \n13. 时薪30元  \n14. 月收入30000元  \n15. 年收入200万人民币  \n16. 每小时100美元  \n17. 月薪1.5万  \n18. 年薪60万人民币  \n19. 时薪40欧元  \n20. 月收入5000美元  \n21. 年收入50000英镑  \n22. 每小时60元  \n23. 月薪2万元  \n24. 年薪90万人民币  \n25. 时薪75美元", "role": "assistant"}}], "created": 1745477012, "id": "2025042414432729aa7429fbdf420e", "model": "glm-4-air-250414", "request_id": "2025042414432729aa7429fbdf420e", "usage": {"completion_tokens": 200, "prompt_tokens": 69, "total_tokens": 269}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成15个多样化的、可以归类为国籍的命名实体，每行一个。这些实体将用于命名实体识别任务中的国籍类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 413}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. American  \n2. Chinese  \n3. British  \n4. French  \n5. German  \n6. Japanese  \n7. Indian  \n8. Brazilian  \n9. Russian  \n10. Canadian  \n11. Australian  \n12. Italian  \n13. Spanish  \n14. Nigerian  \n15. Mexican", "role": "assistant"}}], "created": 1745477012, "id": "2025042414433171500130f8fd4af9", "model": "glm-4-air-250414", "request_id": "2025042414433171500130f8fd4af9", "usage": {"completion_tokens": 61, "prompt_tokens": 47, "total_tokens": 108}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成20个多样化的、可以归类为教育背景的命名实体，每行一个。这些实体将用于命名实体识别任务中的教育背景类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 441}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 高中毕业证书  \n2. 本科学位  \n3. 硕士学位  \n4. 博士学位  \n5. 职业资格证书  \n6. 学士学位  \n7. 研究生文凭  \n8. 中专学历  \n9. 技校毕业证  \n10. 硕士文凭  \n11. 博士后研究  \n12. 专科毕业证  \n13. 文凭认证  \n14. 继续教育证书  \n15. 在职研究生  \n16. 学位证书  \n17. 教育培训证明  \n18. 高等教育经历  \n19. 学历证明  \n20. 学术成就认证", "role": "assistant"}}], "created": 1745477013, "id": "20250424144327aa729c2c0be04a4e", "model": "glm-4-air-250414", "request_id": "20250424144327aa729c2c0be04a4e", "usage": {"completion_tokens": 140, "prompt_tokens": 49, "total_tokens": 189}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成20个多样化的、可以归类为实体资产的命名实体，每行一个。这些实体将用于命名实体识别任务中的实体资产类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 497}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 个人住宅  \n2. 私人汽车  \n3. 银行账户  \n4. 投资组合  \n5. 退休金计划  \n6. 保险保单  \n7. 存款证明  \n8. 房产证  \n9. 股票证书  \n10. 专利权  \n11. 知识产权  \n12. 商业地产  \n13. 航空里程  \n14. 储蓄账户  \n15. 退休账户  \n16. 公司股份  \n17. 信托基金  \n18. 贵金属储备  \n19. 税收抵免  \n20. 虚拟货币钱包", "role": "assistant"}}], "created": 1745477013, "id": "202504241443302e2278e619404ba7", "model": "glm-4-air-250414", "request_id": "202504241443302e2278e619404ba7", "usage": {"completion_tokens": 141, "prompt_tokens": 48, "total_tokens": 189}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成15个多样化的、可以归类为疾病的命名实体，每行一个。这些实体将用于命名实体识别任务中的疾病类型。\n请生成多样化的疾病名称，包括常见病、慢性病、罕见病等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 511}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 糖尿病  \n2. 高血压  \n3. 风湿性关节炎  \n4. 肺炎  \n5. 银屑病  \n6. 帕金森病  \n7. 多发性硬化症  \n8. 白血病  \n9. 先天性心脏病  \n10. 甲状腺功能亢进症  \n11. 阿尔茨海默病  \n12. 艾滋病  \n13. 遗传性血管性水肿  \n14. 慢性阻塞性肺疾病  \n15. 系统性红斑狼疮", "role": "assistant"}}], "created": 1745477014, "id": "20250424144331ae7bc984a5d54c42", "model": "glm-4-air-250414", "request_id": "20250424144331ae7bc984a5d54c42", "usage": {"completion_tokens": 127, "prompt_tokens": 63, "total_tokens": 190}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成20个多样化的、可以归类为年龄的命名实体，每行一个。这些实体将用于命名实体识别任务中的年龄类型。\n请生成多样化的年龄表达，包括具体数字和年龄段描述。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 399}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n25岁  \n30岁  \n45岁  \n60岁  \n18岁  \n35岁  \n50岁  \n22岁  \n40岁  \n55岁  \n\"二十多岁\"  \n\"三十多岁\"  \n\"四十多岁\"  \n\"五十多岁\"  \n\"六十岁以上\"  \n\"青少年\"  \n\"成年人\"  \n\"老年人\"  \n\"中年人\"  \n\"少年\"", "role": "assistant"}}], "created": 1745477015, "id": "20250424144332098e4ac03dc94b11", "model": "glm-4-air-250414", "request_id": "20250424144332098e4ac03dc94b11", "usage": {"completion_tokens": 77, "prompt_tokens": 60, "total_tokens": 137}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成15个多样化的、可以归类为姓名的命名实体，每行一个。这些实体将用于命名实体识别任务中的姓名类型。\n请生成多样化的中文姓名，包括不同性别、年龄段和地区特色的姓名。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 567}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n张伟  \n李娜  \n王芳  \n刘强  \n陈静  \n杨洋  \n赵雪  \n黄磊  \n周明  \n吴婷  \n郑华  \n朱晓  \n孙悦  \n马超  \n徐静", "role": "assistant"}}], "created": 1745477015, "id": "20250424144334a5fcc1de78994ea8", "model": "glm-4-air-250414", "request_id": "20250424144334a5fcc1de78994ea8", "usage": {"completion_tokens": 46, "prompt_tokens": 62, "total_tokens": 108}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成20个多样化的、可以归类为地理位置的命名实体，每行一个。这些实体将用于命名实体识别任务中的地理位置类型。\n请生成多样化的地理位置，包括省市区县、街道地址、建筑物名称等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 553}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 北京市朝阳区  \n2. 上海市浦东新区  \n3. 广州市天河区  \n4. 深圳市南山区  \n5. 成都市武侯区  \n6. 西安市雁塔区  \n7. 杭州市西湖区  \n8. 南京市鼓楼区  \n9. 重庆市渝中区  \n10. 武汉市江汉区  \n11. 长沙市岳麓区  \n12. 苏州市工业园区  \n13. 天津市和平区  \n14. 哈尔滨市南岗区  \n15. 福州市鼓楼区  \n16. 云南省昆明市  \n17. 湖南省长沙市  \n18. 海南省三亚市  \n19. 河南省郑州市  \n20. 山东省青岛市", "role": "assistant"}}], "created": 1745477016, "id": "2025042414433392104442f9684c18", "model": "glm-4-air-250414", "request_id": "2025042414433392104442f9684c18", "usage": {"completion_tokens": 155, "prompt_tokens": 64, "total_tokens": 219}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成15个多样化的、可以归类为姓名的命名实体，每行一个。这些实体将用于命名实体识别任务中的姓名类型。\n请生成多样化的中文姓名，包括不同性别、年龄段和地区特色的姓名。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 392}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 王小明  \n2. 李静怡  \n3. 张伟  \n4. 陈晓燕  \n5. 刘志强  \n6. 周美玲  \n7. 吴国栋  \n8. 郑雨晴  \n9. 赵芳  \n10. 孙浩然  \n11. 钱思琪  \n12. 郭鹏飞  \n13. 林玉婷  \n14. 朱宏亮  \n15. 何雅欣", "role": "assistant"}}], "created": 1745477016, "id": "202504241443341968feb373924afa", "model": "glm-4-air-250414", "request_id": "202504241443341968feb373924afa", "usage": {"completion_tokens": 100, "prompt_tokens": 63, "total_tokens": 163}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成20个多样化的、可以归类为年龄的命名实体，每行一个。这些实体将用于命名实体识别任务中的年龄类型。\n请生成多样化的年龄表达，包括具体数字和年龄段描述。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 574}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n25岁  \n30岁  \n45岁  \n60岁  \n75岁  \n儿童  \n青少年  \n青年  \n中年  \n老年  \n婴儿  \n幼儿  \n小学生  \n中学生  \n大学生  \n成年人  \n青少年时期  \n中年阶段  \n老年阶段  \n耄耋之年", "role": "assistant"}}], "created": 1745477017, "id": "202504241443358d4a9c483d8b4819", "model": "glm-4-air-250414", "request_id": "202504241443358d4a9c483d8b4819", "usage": {"completion_tokens": 53, "prompt_tokens": 59, "total_tokens": 112}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成20个多样化的、可以归类为投资产品的命名实体，每行一个。这些实体将用于命名实体识别任务中的投资产品类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 476}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 退休金计划  \n2. 共同基金  \n3. 股票期权  \n4. 债券投资  \n5. 不动产信托  \n6. 期货合约  \n7. 交易所交易基金  \n8. 保险储蓄账户  \n9. 银行定期存款  \n10. 投资组合管理  \n11. 现金管理账户  \n12. 股票指数基金  \n13. 资产管理计划  \n14. 增长型基金  \n15. 对冲基金  \n16. 零息债券  \n17. 证券化产品  \n18. 私募股权基金  \n19. 货币市场基金  \n20. 生命周期基金", "role": "assistant"}}], "created": 1745477017, "id": "2025042414433368d8590ca06a4d1e", "model": "glm-4-air-250414", "request_id": "2025042414433368d8590ca06a4d1e", "usage": {"completion_tokens": 154, "prompt_tokens": 48, "total_tokens": 202}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成25个多样化的、可以归类为工资数额的命名实体，每行一个。这些实体将用于命名实体识别任务中的工资数额类型。\n请生成多样化的工资金额表达，包括月薪、年薪、时薪等不同计量方式。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 469}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 月薪8000元  \n2. 年薪50万人民币  \n3. 时薪25美元  \n4. 月收入6000元  \n5. 年收入12万  \n6. 时薪35欧元  \n7. 月薪1.2万  \n8. 年薪100万日元  \n9. 时薪30人民币  \n10. 月收入7500元  \n11. 年收入60万  \n12. 时薪45美元  \n13. 月薪9千  \n14. 年薪80万人民币  \n15. 时薪20欧元  \n16. 月收入5千  \n17. 年收入30万  \n18. 时薪28人民币  \n19. 月薪1万5  \n20. 年薪200万日元  \n21. 时薪50美元  \n22. 月收入8500元  \n23. 年收入90万  \n24. 时薪40欧元  \n25. 月薪1.1万", "role": "assistant"}}], "created": 1745477018, "id": "202504241443313c97fb08c9344753", "model": "glm-4-air-250414", "request_id": "202504241443313c97fb08c9344753", "usage": {"completion_tokens": 189, "prompt_tokens": 69, "total_tokens": 258}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成15个多样化的、可以归类为姓名的命名实体，每行一个。这些实体将用于命名实体识别任务中的姓名类型。\n请生成多样化的中文姓名，包括不同性别、年龄段和地区特色的姓名。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 742}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n张伟  \n李娜  \n王芳  \n刘洋  \n陈明  \n杨静  \n赵磊  \n周慧  \n吴强  \n郑秀英  \n黄志强  \n朱丽丽  \n孙国栋  \n马秀兰  \n胡杰", "role": "assistant"}}], "created": 1745477018, "id": "20250424144337bfa1d6b0a82a483c", "model": "glm-4-air-250414", "request_id": "20250424144337bfa1d6b0a82a483c", "usage": {"completion_tokens": 51, "prompt_tokens": 63, "total_tokens": 114}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成15个多样化的、可以归类为国籍的命名实体，每行一个。这些实体将用于命名实体识别任务中的国籍类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 588}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 中国籍  \n2. 美国籍  \n3. 英国籍  \n4. 加拿大籍  \n5. 澳大利亚籍  \n6. 日本籍  \n7. 韩国籍  \n8. 德国籍  \n9. 法国籍  \n10. 巴西籍  \n11. 印度籍  \n12. 俄罗斯籍  \n13. 南非籍  \n14. 意大利籍  \n15. 西班牙籍", "role": "assistant"}}], "created": 1745477018, "id": "20250424144336b92c16baf99f444c", "model": "glm-4-air-250414", "request_id": "20250424144336b92c16baf99f444c", "usage": {"completion_tokens": 95, "prompt_tokens": 46, "total_tokens": 141}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成25个多样化的、可以归类为行程信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的行程信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 385}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 住院记录  \n2. 手术日程  \n3. 复诊预约  \n4. 康复计划  \n5. 门诊时间表  \n6. 透析安排  \n7. 化疗疗程  \n8. 输血记录  \n9. 输液计划  \n10. 病人转运记录  \n11. 隔离措施表  \n12. 检查时间安排  \n13. 会诊预约  \n14. 预防接种记录  \n15. 急诊就诊时间  \n16. 随访计划  \n17. 远程医疗预约  \n18. 康复治疗安排  \n19. 药物配送记录  \n20. 呼吸机使用日志  \n21. 辅助设备借用记录  \n22. 健康管理日程  \n23. 家庭护理计划  \n24. 术后护理安排  \n25. 特殊检查时间表", "role": "assistant"}}], "created": 1745477018, "id": "202504241443322ae8da3f494f444a", "model": "glm-4-air-250414", "request_id": "202504241443322ae8da3f494f444a", "usage": {"completion_tokens": 190, "prompt_tokens": 48, "total_tokens": 238}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成15个多样化的、可以归类为医疗程序的命名实体，每行一个。这些实体将用于命名实体识别任务中的医疗程序类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 532}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 腹腔镜胆囊切除术  \n2. 核磁共振成像（MRI）扫描  \n3. 放射性碘治疗  \n4. 心脏导管插入术  \n5. 白内障摘除术  \n6. 腰椎穿刺（脑脊液检查）  \n7. 骨髓移植  \n8. 乳房X光摄影检查  \n9. 体外受精（IVF）  \n10. 关节镜检查  \n11. 胰岛素注射疗法  \n12. 颈动脉内膜切除术  \n13. 胃镜检查  \n14. 血液透析  \n15. PET-CT联合扫描", "role": "assistant"}}], "created": 1745477019, "id": "202504241443348874c75729fd42f0", "model": "glm-4-air-250414", "request_id": "202504241443348874c75729fd42f0", "usage": {"completion_tokens": 139, "prompt_tokens": 48, "total_tokens": 187}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名个人身份领域的数据隐私专家。请生成15个多样化的、可以归类为过敏信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的过敏信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 539}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 青霉素过敏史  \n2. 花粉过敏记录  \n3. 牛奶蛋白不耐受  \n4. 花生过敏标记  \n5. 布洛芬过敏提示  \n6. 海鲜过敏诊断  \n7. 螨虫过敏档案  \n8. 磺胺类药物敏感  \n9. 芒果过敏备注  \n10. 橡胶过敏警示  \n11. 小麦麸质不耐受  \n12. 碘过敏测试结果  \n13. 花露水过敏病例  \n14. 蜜蜂蜇伤过敏史  \n15. 氨基比林过敏记录", "role": "assistant"}}], "created": 1745477019, "id": "20250424144336c618d7adecae4633", "model": "glm-4-air-250414", "request_id": "20250424144336c618d7adecae4633", "usage": {"completion_tokens": 141, "prompt_tokens": 48, "total_tokens": 189}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成15个多样化的、可以归类为家庭成员的命名实体，每行一个。这些实体将用于命名实体识别任务中的家庭成员类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 637}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 父亲\n2. 母亲\n3. 儿子\n4. 女儿\n5. 哥哥\n6. 姐姐\n7. 弟弟\n8. 妹妹\n9. 爷爷\n10. 奶奶\n11. 外公\n12. 外婆\n13. 丈夫\n14. 妻子\n15. 配偶", "role": "assistant"}}], "created": 1745477020, "id": "202504241443385b028e748962497c", "model": "glm-4-air-250414", "request_id": "202504241443385b028e748962497c", "usage": {"completion_tokens": 97, "prompt_tokens": 46, "total_tokens": 143}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成20个多样化的、可以归类为教育背景的命名实体，每行一个。这些实体将用于命名实体识别任务中的教育背景类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 616}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Bachelor of Science in Computer Science  \n2. Master of Business Administration  \n3. Doctor of Philosophy in Geography  \n4. Associate Degree in Environmental Science  \n5. High School Diploma from Lincoln High  \n6. Certificate in Geographic Information Systems  \n7. Ph.D. in Urban Planning  \n8. Bachelor of Arts in History  \n9. Master of Public Administration  \n10. Associate of Applied Science in Surveying  \n11. Secondary School Certificate  \n12. Diploma in Cartography  \n13. Master of Science in Remote Sensing  \n14. Bachelor of Engineering in Civil Engineering  \n15. Graduate Certificate in Spatial Analysis  \n16. Doctor of Education in Educational Technology  \n17. Associate Degree in Geospatial Technology  \n18. High School Leaving Certificate  \n19. Master of Arts in International Relations  \n20. Certificate III in Spatial Information Services", "role": "assistant"}}], "created": 1745477020, "id": "20250424144336c2de52cd53c041f0", "model": "glm-4-air-250414", "request_id": "20250424144336c2de52cd53c041f0", "usage": {"completion_tokens": 168, "prompt_tokens": 48, "total_tokens": 216}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名医疗健康领域的数据隐私专家。请生成20个多样化的、可以归类为地理位置的命名实体，每行一个。这些实体将用于命名实体识别任务中的地理位置类型。\n请生成多样化的地理位置，包括省市区县、街道地址、建筑物名称等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 378}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 北京市朝阳区\n2. 上海市浦东新区张江高科技园区\n3. 广东省深圳市南山区科技园\n4. 江苏省苏州市工业园区\n5. 西安市雁塔区大雁塔\n6. 成都市锦江区春熙路\n7. 重庆市江北区观音桥步行街\n8. 浙江省杭州市西湖区\n9. 天津市和平区南京路\n10. 武汉市洪山区光谷广场\n11. 南京市鼓楼区中山北路\n12. 长沙市岳麓区岳麓山\n13. 福建省厦门市思明区\n14. 山东省济南市历下区千佛山\n15. 海南省三亚市亚龙湾\n16. 哈尔滨市南岗区红军街\n17. 沈阳市沈河区中街\n18. 云南省昆明市五华区翠湖\n19. 青岛市市南区栈桥\n20. 石家庄市长安区正定古城", "role": "assistant"}}], "created": 1745477020, "id": "202504241443354038eb2dd86041bf", "model": "glm-4-air-250414", "request_id": "202504241443354038eb2dd86041bf", "usage": {"completion_tokens": 210, "prompt_tokens": 64, "total_tokens": 274}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成15个多样化的、可以归类为种族的命名实体，每行一个。这些实体将用于命名实体识别任务中的种族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 602}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 非洲裔\n2. 亚裔\n3. 欧洲裔\n4. 拉丁裔\n5. 印第安裔\n6. 美洲原住民\n7. 混血儿\n8. 阿拉伯裔\n9. 高加索人\n10. 太平洋岛民\n11. 南亚裔\n12. 东亚裔\n13. 中东裔\n14. 西亚裔\n15. 北美原住民", "role": "assistant"}}], "created": 1745477020, "id": "20250424144337975f879b1d42408f", "model": "glm-4-air-250414", "request_id": "20250424144337975f879b1d42408f", "usage": {"completion_tokens": 112, "prompt_tokens": 46, "total_tokens": 158}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成15个多样化的、可以归类为种族的命名实体，每行一个。这些实体将用于命名实体识别任务中的种族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 777}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. African\n2. Caucasian\n3. Asian\n4. Hispanic\n5. Latino\n6. Native American\n7. Middle Eastern\n8. Indian\n9. Pacific Islander\n10. Black\n11. White\n12. Asian American\n13. Latino American\n14. Middle Eastern American\n15. Pacific Islander American", "role": "assistant"}}], "created": 1745477021, "id": "20250424144339aa15c457d9e64db9", "model": "glm-4-air-250414", "request_id": "20250424144339aa15c457d9e64db9", "usage": {"completion_tokens": 72, "prompt_tokens": 47, "total_tokens": 119}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成15个多样化的、可以归类为医疗程序的命名实体，每行一个。这些实体将用于命名实体识别任务中的医疗程序类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 707}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 外科手术\n2. 核磁共振成像\n3. 内窥镜检查\n4. 牙科清洁\n5. 皮肤活检\n6. 放射治疗\n7. 超声波扫描\n8. 眼科激光手术\n9. 骨科矫形\n10. 胸腔穿刺术\n11. 心脏导管插入术\n12. 脑电图监测\n13. 肾透析\n14. 妇科超声波\n15. 胃镜检查", "role": "assistant"}}], "created": 1745477021, "id": "20250424144339d12fdefc2f61459c", "model": "glm-4-air-250414", "request_id": "20250424144339d12fdefc2f61459c", "usage": {"completion_tokens": 117, "prompt_tokens": 47, "total_tokens": 164}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成10个多样化的、可以归类为性别的命名实体，每行一个。这些实体将用于命名实体识别任务中的性别类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 756}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. FemaleUser_123  \n2. MaleAccount_XYZ  \n3. TransgenderProfile_456  \n4. NonBinaryHandle_A1B2  \n5. GenderFluidID_789  \n6. AgenderUser_C3D4  \n7. TwoSpiritAccount_E5F6  \n8. BigenderProfile_G7H8  \n9. GenderqueerHandle_I9J0  \n10. AndrogyneID_K1L2", "role": "assistant"}}], "created": **********, "id": "202504241443403196282473264073", "model": "glm-4-air-250414", "request_id": "202504241443403196282473264073", "usage": {"completion_tokens": 96, "prompt_tokens": 47, "total_tokens": 143}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成20个多样化的、可以归类为年龄的命名实体，每行一个。这些实体将用于命名实体识别任务中的年龄类型。\n请生成多样化的年龄表达，包括具体数字和年龄段描述。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 749}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n18岁  \n30岁  \n5岁  \n60岁以上  \n青少年  \n中年  \n婴幼儿  \n70-80岁  \n20多岁  \n儿童  \n40岁  \n90岁  \n幼儿  \n65岁  \n50岁  \n成年  \n3岁  \n80-90岁  \n少年  \n100岁", "role": "assistant"}}], "created": **********, "id": "202504241443416a39d7958d234e21", "model": "glm-4-air-250414", "request_id": "202504241443416a39d7958d234e21", "usage": {"completion_tokens": 58, "prompt_tokens": 60, "total_tokens": 118}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成10个多样化的、可以归类为政治倾向的命名实体，每行一个。这些实体将用于命名实体识别任务中的政治倾向类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 630}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n自由民主党  \n保守主义联盟  \n绿党环保主义  \n国家社会主义党  \n独立工会联盟  \n共产主义先锋队  \n极右翼联盟  \n社会民主党  \n自由意志党  \n民粹主义运动", "role": "assistant"}}], "created": 1745477023, "id": "20250424144342b7a4cd1a81da48f0", "model": "glm-4-air-250414", "request_id": "20250424144342b7a4cd1a81da48f0", "usage": {"completion_tokens": 42, "prompt_tokens": 48, "total_tokens": 90}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成20个多样化的、可以归类为税务记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的税务记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 658}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 个人所得税申报表  \n2. 企业所得税纳税证明  \n3. 增值税发票记录  \n4. 财产税缴纳证明  \n5. 土地使用税申报单  \n6. 车船税缴纳记录  \n7. 印花税登记表  \n8. 契税缴纳凭证  \n9. 房产税纳税清单  \n10. 环境保护税申报文件  \n11. 城市维护建设税记录  \n12. 教育费附加缴纳表  \n13. 地方教育附加申报单  \n14. 社会保险费缴纳证明  \n15. 个人所得税汇算清缴单  \n16. 企业所得税预缴凭证  \n17. 消费税纳税申报表  \n18. 关税缴纳记录  \n19. 资源税申报文件  \n20. 综合所得纳税明细", "role": "assistant"}}], "created": 1745477023, "id": "20250424144339dc686e79f6b54e7a", "model": "glm-4-air-250414", "request_id": "20250424144339dc686e79f6b54e7a", "usage": {"completion_tokens": 186, "prompt_tokens": 48, "total_tokens": 234}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成15个多样化的、可以归类为生育信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的生育信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 721}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 产前检查记录  \n2. 妊娠周数  \n3. 人工授精时间  \n4. 流产手术日期  \n5. 生育证明文件  \n6. 辅助生殖治疗史  \n7. 婴儿出生证明  \n8. 子宫肌瘤手术记录  \n9. 母乳喂养计划  \n10. 子宫切除手术  \n11. 生育能力评估  \n12. 孕期并发症报告  \n13. 试管婴儿周期  \n14. 胎儿发育监测  \n15. 避孕措施历史", "role": "assistant"}}], "created": 1745477024, "id": "2025042414434095eee71597494145", "model": "glm-4-air-250414", "request_id": "2025042414434095eee71597494145", "usage": {"completion_tokens": 122, "prompt_tokens": 47, "total_tokens": 169}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成25个多样化的、可以归类为工资数额的命名实体，每行一个。这些实体将用于命名实体识别任务中的工资数额类型。\n请生成多样化的工资金额表达，包括月薪、年薪、时薪等不同计量方式。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 644}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 月薪12,000元  \n2. 年薪60万人民币  \n3. 时薪50美元  \n4. 月收入9,500元  \n5. 年收入80万人民币  \n6. 每小时35欧元  \n7. 月薪8,800元  \n8. 年薪100万人民币  \n9. 时薪28英镑  \n10. 月收入15,000元  \n11. 年收入50万人民币  \n12. 每小时20美元  \n13. 月薪10,500元  \n14. 年薪90万人民币  \n15. 时薪45欧元  \n16. 月收入7,200元  \n17. 年收入70万人民币  \n18. 每小时32美元  \n19. 月薪13,000元  \n20. 年薪85万人民币  \n21. 时薪25英镑  \n22. 月收入18,000元  \n23. 年收入95万人民币  \n24. 每小时40美元  \n25. 月薪9,800元", "role": "assistant"}}], "created": 1745477024, "id": "202504241443383005881d5edb4f47", "model": "glm-4-air-250414", "request_id": "202504241443383005881d5edb4f47", "usage": {"completion_tokens": 210, "prompt_tokens": 68, "total_tokens": 278}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成20个多样化的、可以归类为实体资产的命名实体，每行一个。这些实体将用于命名实体识别任务中的实体资产类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 672}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 上海证券交易所服务器机房  \n2. 北京地铁信号控制系统  \n3. 深圳华为研发中心大楼  \n4. 广州白云机场雷达导航设备  \n5. 成都英特尔芯片制造工厂  \n6. 重庆两江新区数据中心  \n7. 杭州阿里巴巴云计算基地  \n8. 武汉长江大桥钢结构件  \n9. 南京紫金山天文台观测设备  \n10. 青岛海尔全球研发中心  \n11. 西安大唐不夜城景区照明系统  \n12. 沈阳沈飞航空制造厂房  \n13. 长春一汽解放卡车生产线  \n14. 昆明滇池污染监测传感器  \n15. 贵阳大数据交易所服务器集群  \n16. 宁波舟山港自动化码头设备  \n17. 南昌洪都航空试飞基地  \n18. 福州长乐国际机场航站楼  \n19. 哈尔滨冰雕艺术展览馆  \n20. 乌鲁木齐国际机场卫星导航系统", "role": "assistant"}}], "created": 1745477025, "id": "202504241443416c66efc2ff124d49", "model": "glm-4-air-250414", "request_id": "202504241443416c66efc2ff124d49", "usage": {"completion_tokens": 203, "prompt_tokens": 47, "total_tokens": 250}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成10个多样化的、可以归类为婚姻状况的命名实体，每行一个。这些实体将用于命名实体识别任务中的婚姻状况类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 623}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 已婚人士  \n2. 单身汉  \n3. 离异女性  \n4. 未婚情侣  \n5. 鳏夫  \n6. 赛居状态  \n7. 恋爱中  \n8. 同居关系  \n9. 复婚人员  \n10. 遗孀", "role": "assistant"}}], "created": 1745477025, "id": "20250424144344e49c0389c74e4125", "model": "glm-4-air-250414", "request_id": "20250424144344e49c0389c74e4125", "usage": {"completion_tokens": 71, "prompt_tokens": 48, "total_tokens": 119}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成15个多样化的、可以归类为国籍的命名实体，每行一个。这些实体将用于命名实体识别任务中的国籍类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 763}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n美国  \n中国  \n英国  \n德国  \n法国  \n日本  \n印度  \n巴西  \n加拿大  \n澳大利亚  \n俄罗斯  \n意大利  \n西班牙  \n韩国  \n墨西哥", "role": "assistant"}}], "created": 1745477025, "id": "20250424144344ecfa2961fa6d4a46", "model": "glm-4-air-250414", "request_id": "20250424144344ecfa2961fa6d4a46", "usage": {"completion_tokens": 31, "prompt_tokens": 47, "total_tokens": 78}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成15个多样化的、可以归类为民族的命名实体，每行一个。这些实体将用于命名实体识别任务中的民族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 784}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1.汉族\n2.藏族\n3.维吾尔族\n4.苗族\n5.彝族\n6.壮族\n7.蒙古族\n8.回族\n9.满族\n10.土家族\n11.傣族\n12.白族\n13.朝鲜族\n14.哈尼族\n15.哈萨克族", "role": "assistant"}}], "created": 1745477026, "id": "20250424144344b9384bcb4e244369", "model": "glm-4-air-250414", "request_id": "20250424144344b9384bcb4e244369", "usage": {"completion_tokens": 72, "prompt_tokens": 46, "total_tokens": 118}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成20个多样化的、可以归类为职业的命名实体，每行一个。这些实体将用于命名实体识别任务中的职业类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 595}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 软件工程师  \n2. 医生  \n3. 教师  \n4. 律师  \n5. 建筑师  \n6. 会计师  \n7. 厨师  \n8. 警察  \n9. 护士  \n10. 记者  \n11. 画家  \n12. 消防员  \n13. 机械师  \n14. 心理学家  \n15. 电气工程师  \n16. 航空工程师  \n17. 生物学家  \n18. 图书管理员  \n19. 销售代表  \n20. 人力资源专员", "role": "assistant"}}], "created": 1745477026, "id": "202504241443439ded1ed8294c41d6", "model": "glm-4-air-250414", "request_id": "202504241443439ded1ed8294c41d6", "usage": {"completion_tokens": 122, "prompt_tokens": 46, "total_tokens": 168}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成10个多样化的、可以归类为政治倾向的命名实体，每行一个。这些实体将用于命名实体识别任务中的政治倾向类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 805}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n保守派联盟  \n自由民主阵线  \n社会正义先锋  \n民族复兴力量  \n绿色和平联盟  \n劳工权益协会  \n科技未来派  \n文化保守主义者  \n全球南方联盟  \n民粹主义运动", "role": "assistant"}}], "created": 1745477026, "id": "2025042414434590772d098fb24448", "model": "glm-4-air-250414", "request_id": "2025042414434590772d098fb24448", "usage": {"completion_tokens": 43, "prompt_tokens": 49, "total_tokens": 92}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成25个多样化的、可以归类为交易信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的交易信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 679}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 购物收据编号\n2. 信用卡交易记录\n3. 网上支付订单号\n4. 零售店消费小票\n5. 现金交易凭证\n6. 电子钱包交易ID\n7. 银行转账流水号\n8. 移动支付凭证\n9. POS机交易数据\n10. 扫码支付记录\n11. 账单支付详情\n12. 订阅服务费用\n13. 商品退货凭证\n14. 会员积分交易\n15. 代金券使用记录\n16. 购物卡充值记录\n17. 车票购买凭证\n18. 餐饮消费清单\n19. 在线预订确认单\n20. 物流配送费用\n21. 税务交易编号\n22. 股票交易记录\n23. 房产交易合同号\n24. 车辆购买发票\n25. 旅游服务费用明细", "role": "assistant"}}], "created": 1745477027, "id": "20250424144342661677835f504dfb", "model": "glm-4-air-250414", "request_id": "20250424144342661677835f504dfb", "usage": {"completion_tokens": 212, "prompt_tokens": 47, "total_tokens": 259}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成15个多样化的、可以归类为药物的命名实体，每行一个。这些实体将用于命名实体识别任务中的药物类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 693}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 阿司匹林  \n2. 布洛芬  \n3. 吗啡  \n4. 地高辛  \n5. 氯化钠注射液  \n6. 青霉素V钾  \n7. 硫酸阿托品  \n8. 帕罗西汀  \n9. 依那普利  \n10. 头孢曲松钠  \n11. 异烟肼  \n12. 硝酸甘油  \n13. 阿莫西林克拉维酸钾  \n14. 氟西汀  \n15. 甲硝唑", "role": "assistant"}}], "created": 1745477027, "id": "202504241443422814e61a9f6940cf", "model": "glm-4-air-250414", "request_id": "202504241443422814e61a9f6940cf", "usage": {"completion_tokens": 126, "prompt_tokens": 45, "total_tokens": 171}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成10个多样化的、可以归类为婚姻状况的命名实体，每行一个。这些实体将用于命名实体识别任务中的婚姻状况类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 798}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 未婚  \n2. 已婚  \n3. 离异  \n4. 分居  \n5. 丧偶  \n6. 同居  \n7. 单身  \n8. 恋爱中  \n9. 订婚  \n10. 复婚", "role": "assistant"}}], "created": 1745477028, "id": "202504241443469ca6c9dcda3e417a", "model": "glm-4-air-250414", "request_id": "202504241443469ca6c9dcda3e417a", "usage": {"completion_tokens": 59, "prompt_tokens": 49, "total_tokens": 108}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成15个多样化的、可以归类为民族的命名实体，每行一个。这些实体将用于命名实体识别任务中的民族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 609}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n藏族  \n维吾尔族  \n回族  \n壮族  \n满族  \n苗族  \n彝族  \n土家族  \n蒙古族  \n藏族  \n傣族  \n畲族  \n高山族  \n朝鲜族  \n赫哲族", "role": "assistant"}}], "created": 1745477028, "id": "20250424144347b7af6a49f8bb4482", "model": "glm-4-air-250414", "request_id": "20250424144347b7af6a49f8bb4482", "usage": {"completion_tokens": 43, "prompt_tokens": 45, "total_tokens": 88}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成20个多样化的、可以归类为职业的命名实体，每行一个。这些实体将用于命名实体识别任务中的职业类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 770}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 软件工程师  \n2. 数据分析师  \n3. 产品经理  \n4. 市场营销专员  \n5. 人力资源总监  \n6. 财务顾问  \n7. 建筑师  \n8. 医生  \n9. 教师  \n10. 律师  \n11. 会计师  \n12. 记者  \n13. 广告设计师  \n14. 机械工程师  \n15. 电子商务运营  \n16. 行政助理  \n17. 销售代表  \n18. 营养师  \n19. 物流经理  \n20. 网络安全专家", "role": "assistant"}}], "created": 1745477028, "id": "20250424144345318bbf5953aa4221", "model": "glm-4-air-250414", "request_id": "20250424144345318bbf5953aa4221", "usage": {"completion_tokens": 128, "prompt_tokens": 47, "total_tokens": 175}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成15个多样化的、可以归类为过敏信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的过敏信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 714}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n以下是为地理位置领域数据隐私专家生成的15个多样化的过敏信息命名实体，每行一个：\n\n1. PeanutAllergy\n2. ShellfishIntolerance\n3. LactoseHypersensitivity\n4. GlutenSensitivity\n5. PenicillinReaction\n6. LatexAllergic\n7. SulfaMedicationAllergy\n8. EggProteinIntolerance\n9. SesameSeedHypersensitivity\n10. NickelContactAllergy\n11. DustMiteSensitivity\n12. BirchPollenAllergy\n13. AspirinInducedReaction\n14. SoyProteinHypersensitivity\n15. SulfiteSensitivity", "role": "assistant"}}], "created": 1745477029, "id": "20250424144343a655a1d981d842cc", "model": "glm-4-air-250414", "request_id": "20250424144343a655a1d981d842cc", "usage": {"completion_tokens": 141, "prompt_tokens": 47, "total_tokens": 188}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成15个多样化的、可以归类为药物的命名实体，每行一个。这些实体将用于命名实体识别任务中的药物类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 868}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n阿莫西林  \n布洛芬  \n二甲双胍  \n氟西汀  \n吗啡  \n氯雷他定  \n利培酮  \n美托洛尔  \n奥美拉唑  \n帕罗西汀  \n青霉素  \n曲马多  \n硝苯地平  \n盐酸左氧氟沙星  \n盐酸氨溴索", "role": "assistant"}}], "created": 1745477029, "id": "20250424144347d6e89832dc734869", "model": "glm-4-air-250414", "request_id": "20250424144347d6e89832dc734869", "usage": {"completion_tokens": 70, "prompt_tokens": 46, "total_tokens": 116}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成15个多样化的、可以归类为临床表现的命名实体，每行一个。这些实体将用于命名实体识别任务中的临床表现类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 875}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n发热  \n咳嗽  \n头痛  \n腹泻  \n恶心  \n呕吐  \n皮疹  \n呼吸困难  \n胸痛  \n腹痛  \n头晕  \n疲劳  \n关节痛  \n肌肉酸痛  \n失眠", "role": "assistant"}}], "created": 1745477029, "id": "20250424144348f1dfe9ceabc04367", "model": "glm-4-air-250414", "request_id": "20250424144348f1dfe9ceabc04367", "usage": {"completion_tokens": 35, "prompt_tokens": 47, "total_tokens": 82}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成20个多样化的、可以归类为实体资产的命名实体，每行一个。这些实体将用于命名实体识别任务中的实体资产类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 847}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 个人银行账户信息  \n2. 信用卡交易记录  \n3. 社会安全号码  \n4. 医疗保险数据  \n5. 教育背景文件  \n6. 驾驶执照信息  \n7. 税务申报记录  \n8. 房产所有权证明  \n9. 投资组合明细  \n10. 社交媒体个人资料  \n11. 电子邮件通信日志  \n12. 生物识别数据  \n13. 个人购物历史  \n14. 雇佣合同文件  \n15. 股票交易记录  \n16. 网络浏览历史  \n17. 个人通讯录  \n18. 储蓄账户详情  \n19. 支付方式偏好  \n20. 个人健康记录", "role": "assistant"}}], "created": 1745477031, "id": "202504241443475371411f06544d1c", "model": "glm-4-air-250414", "request_id": "202504241443475371411f06544d1c", "usage": {"completion_tokens": 156, "prompt_tokens": 48, "total_tokens": 204}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成25个多样化的、可以归类为工资数额的命名实体，每行一个。这些实体将用于命名实体识别任务中的工资数额类型。\n请生成多样化的工资金额表达，包括月薪、年薪、时薪等不同计量方式。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 819}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 月薪¥5,000  \n2. 年薪$80,000  \n3. 时薪€20  \n4. 月薪₩3,000,000  \n5. 年薪£50,000  \n6. 时薪$15  \n7. 月薪¥12,000  \n8. 年薪€60,000  \n9. 时薪₩15,000  \n10. 月薪$3,500  \n11. 年薪¥1,000,000  \n12. 时薪£10  \n13. 月薪₩4,500,000  \n14. 年薪$120,000  \n15. 时薪¥300  \n16. 月薪€4,000  \n17. 年薪₩50,000,000  \n18. 时薪$25  \n19. 月薪£3,000  \n20. 年薪¥800,000  \n21. 时薪₩20,000  \n22. 月薪$6,000  \n23. 年薪€80,000  \n24. 时薪¥500  \n25. 月薪₩5,000,000", "role": "assistant"}}], "created": 1745477031, "id": "20250424144346d9ad75cafded4317", "model": "glm-4-air-250414", "request_id": "20250424144346d9ad75cafded4317", "usage": {"completion_tokens": 230, "prompt_tokens": 69, "total_tokens": 299}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名地理位置领域的数据隐私专家。请生成15个多样化的、可以归类为临床表现的命名实体，每行一个。这些实体将用于命名实体识别任务中的临床表现类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 700}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 胸痛  \n2. 发热  \n3. 呼吸困难  \n4. 头痛  \n5. 恶心呕吐  \n6. 疲劳乏力  \n7. 关节疼痛  \n8. 意识模糊  \n9. 腹泻  \n10. 心悸  \n11. 咳嗽  \n12. 视物模糊  \n13. 皮疹  \n14. 肌肉痉挛  \n15. 失眠", "role": "assistant"}}], "created": 1745477032, "id": "2025042414435080bbf346485f482b", "model": "glm-4-air-250414", "request_id": "2025042414435080bbf346485f482b", "usage": {"completion_tokens": 102, "prompt_tokens": 46, "total_tokens": 148}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成15个多样化的、可以归类为疾病的命名实体，每行一个。这些实体将用于命名实体识别任务中的疾病类型。\n请生成多样化的疾病名称，包括常见病、慢性病、罕见病等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 861}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n高血压  \n糖尿病  \n哮喘  \n阿尔茨海默病  \n类风湿性关节炎  \n地中海贫血  \n系统性红斑狼疮  \n多发性硬化  \n帕金森病  \n克罗恩病  \n囊性纤维化  \n亨廷顿舞蹈症  \n孤独症谱系障碍  \n肌萎缩侧索硬化症  \n肺结核", "role": "assistant"}}], "created": 1745477033, "id": "202504241443521525206dfd8e4b4a", "model": "glm-4-air-250414", "request_id": "202504241443521525206dfd8e4b4a", "usage": {"completion_tokens": 66, "prompt_tokens": 63, "total_tokens": 129}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成25个多样化的、可以归类为行程信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的行程信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 910}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 机场航班号  \n2. 酒店预订确认码  \n3. 火车车次信息  \n4. 飞机登机口号码  \n5. 出租车行程记录  \n6. 公交车乘车卡信息  \n7. 高铁座位号  \n8. 轮渡船票编号  \n9. 停车场取车凭证  \n10. 自驾路线导航记录  \n11. 地铁行程单  \n12. 旅行保险单号  \n13. 徒步路线地图  \n14. 航班延误通知  \n15. 旅店入住时间  \n16. 包车合同编号  \n17. 长途巴士车票  \n18. 骑行轨迹数据  \n19. 航班行李托运单  \n20. 景区门票订单  \n21. 国际驾照编号  \n22. 航班退改签记录  \n23. 旅行计划时间表  \n24. 航班行李领取凭证  \n25. 旅行保险索赔单", "role": "assistant"}}], "created": 1745477033, "id": "2025042414434922e083e8b83844aa", "model": "glm-4-air-250414", "request_id": "2025042414434922e083e8b83844aa", "usage": {"completion_tokens": 220, "prompt_tokens": 48, "total_tokens": 268}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成15个多样化的、可以归类为家庭成员的命名实体，每行一个。这些实体将用于命名实体识别任务中的家庭成员类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 812}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 父亲  \n2. 母亲  \n3. 哥哥  \n4. 弟弟  \n5. 姐姐  \n6. 妹妹  \n7. 爷爷  \n8. 奶奶  \n9. 外公  \n10. 外婆  \n11. 丈夫  \n12. 妻子  \n13. 儿子  \n14. 女儿  \n15. 孙子", "role": "assistant"}}], "created": **********, "id": "202504241443517e014d90b8af4aa2", "model": "glm-4-air-250414", "request_id": "202504241443517e014d90b8af4aa2", "usage": {"completion_tokens": 97, "prompt_tokens": 47, "total_tokens": 144}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成25个多样化的、可以归类为交易信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的交易信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 854}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. TransactionID_123456\n2. PurchaseAmount_789.50\n3. CreditCardNumber_4561-2345-6789-0123\n4. BillingAddress_789 Oak St\n5. ShippingZipCode_90210\n6. OrderDate_2023-11-15\n7. ExpirationDate_12/25\n8. CVVCode_123\n9. MerchantName_QuickMart\n10. IP_Address_192.168.1.1\n11. SessionID_SES789012\n12. UserID_U7890123\n13. ProductSKU_SKU987654\n14. QuantityPurchased_3\n15. DiscountCode_SUMMER25\n16. PaymentMethod_CreditCard\n17. BankRoutingNumber_121000358\n18. AccountNumber_987654321\n19. TransactionTimestamp_2023-11-15T14:30:00Z\n20. CurrencyCode_USD\n21. AuthorizationCode_AUTH9876\n22. RefundAmount_50.25\n23. ShippingCost_12.99\n24. TaxAmount_15.75\n25. LoyaltyPointsEarned_250", "role": "assistant"}}], "created": **********, "id": "2025042414434837c487b33bf242af", "model": "glm-4-air-250414", "request_id": "2025042414434837c487b33bf242af", "usage": {"completion_tokens": 265, "prompt_tokens": 48, "total_tokens": 313}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成20个多样化的、可以归类为税务记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的税务记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 833}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 个人所得税申报表  \n2. 企业所得税纳税证明  \n3. 增值税发票记录  \n4. 所得税减免申请单  \n5. 税务登记证号码  \n6. 资产增值税申报表  \n7. 关税缴纳凭证  \n8. 土地使用税清册  \n9. 车船税缴纳记录  \n10. 印花税缴纳通知单  \n11. 房产税申报明细  \n12. 个人税务居民身份证明  \n13. 企业税务审计报告  \n14. 跨境交易税额计算表  \n15. 税收滞纳金明细单  \n16. 税务优惠资格认定书  \n17. 股权转让税额确认书  \n18. 境外收入税务备案表  \n19. 税务行政复议申请书  \n20. 企业所得税汇算清缴表", "role": "assistant"}}], "created": **********, "id": "2025042414434901751b52726d4476", "model": "glm-4-air-250414", "request_id": "2025042414434901751b52726d4476", "usage": {"completion_tokens": 190, "prompt_tokens": 49, "total_tokens": 239}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成20个多样化的、可以归类为教育背景的命名实体，每行一个。这些实体将用于命名实体识别任务中的教育背景类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 791}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Bachelor of Science in Computer Science  \n2. Master of Business Administration  \n3. PhD in Data Science  \n4. Associate Degree in Web Development  \n5. Doctor of Philosophy in Sociology  \n6. Bachelor of Arts in Communications  \n7. Master of Education in Curriculum Design  \n8. Graduate Certificate in Digital Marketing  \n9. Associate of Applied Science in Nursing  \n10. Bachelor of Engineering in Electrical Engineering  \n11. Master of Public Health  \n12. Doctorate in Artificial Intelligence  \n13. Postgraduate Diploma in International Relations  \n14. Bachelor of Fine Arts in Graphic Design  \n15. Master of Science in Environmental Science  \n16. Certificate III in Information Technology  \n17. Juris Doctor  \n18. Master of Fine Arts in Creative Writing  \n19. Bachelor of Laws  \n20. PhD in Psychology", "role": "assistant"}}], "created": **********, "id": "20250424144349c8fff9d3c14f483a", "model": "glm-4-air-250414", "request_id": "20250424144349c8fff9d3c14f483a", "usage": {"completion_tokens": 162, "prompt_tokens": 49, "total_tokens": 211}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成20个多样化的、可以归类为投资产品的命名实体，每行一个。这些实体将用于命名实体识别任务中的投资产品类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 826}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 普通股  \n2. 优先股  \n3. 公司债券  \n4. 国债  \n5. 市政债券  \n6. 货币市场基金  \n7. 指数基金  \n8. 对冲基金  \n9. 期权合约  \n10. 期货合约  \n11. 投资信托  \n12. 交易所交易基金  \n13. 房地产投资信托  \n14. 互惠基金  \n15. 分红保险  \n16. 股息优先证券  \n17. 可转换债券  \n18. 商品期货  \n19. 零息债券  \n20. 结构化产品", "role": "assistant"}}], "created": **********, "id": "20250424144350add054bdfc124ead", "model": "glm-4-air-250414", "request_id": "20250424144350add054bdfc124ead", "usage": {"completion_tokens": 137, "prompt_tokens": 48, "total_tokens": 185}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成10个多样化的、可以归类为性别的命名实体，每行一个。这些实体将用于命名实体识别任务中的性别类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 931}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. John\n2. Maria\n3. Zhang Wei\n4. Fatima\n5. Ramesh\n6. Svetlana\n7. Omar\n8. Priya\n9. Chloe\n10. Kenji", "role": "assistant"}}], "created": **********, "id": "20250424144353ff2c47b63a5f41f6", "model": "glm-4-air-250414", "request_id": "20250424144353ff2c47b63a5f41f6", "usage": {"completion_tokens": 50, "prompt_tokens": 47, "total_tokens": 97}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成20个多样化的、可以归类为信用记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的信用记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 840}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 信用评分\n2. 信用报告\n3. 信用历史\n4. 信用额度\n5. 信用查询\n6. 信用账单\n7. 信用风险\n8. 信用评级\n9. 信用账户\n10. 信用记录\n11. 信用违约\n12. 信用损失\n13. 信用价值\n14. 信用分析\n15. 信用状况\n16. 信用评估\n17. 信用违约率\n18. 信用监控\n19. 信用文件\n20. 信用管理", "role": "assistant"}}], "created": 1745477036, "id": "2025042414435238ab0f26a152455d", "model": "glm-4-air-250414", "request_id": "2025042414435238ab0f26a152455d", "usage": {"completion_tokens": 123, "prompt_tokens": 49, "total_tokens": 172}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成15个多样化的、可以归类为生育信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的生育信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 896}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n以下是为命名实体识别任务设计的15个多样化且可归类为生育信息的命名实体，每行一个：\n\n1. 怀孕周期  \n2. 产前检查  \n3. 婴儿性别  \n4. 流产记录  \n5. 辅助生殖  \n6. 剖腹产史  \n7. 生育年龄  \n8. 孕期反应  \n9. 胎儿发育  \n10. 人工授精  \n11. 避孕措施  \n12. 多胎妊娠  \n13. 产后抑郁  \n14. 哺乳期  \n15. 精子捐赠", "role": "assistant"}}], "created": 1745477036, "id": "2025042414435382bb0e6e532d412f", "model": "glm-4-air-250414", "request_id": "2025042414435382bb0e6e532d412f", "usage": {"completion_tokens": 132, "prompt_tokens": 48, "total_tokens": 180}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成15个多样化的、可以归类为医疗程序的命名实体，每行一个。这些实体将用于命名实体识别任务中的医疗程序类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 882}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 腹腔镜胆囊切除术  \n2. 磁共振成像（MRI）扫描  \n3. 经皮冠状动脉介入治疗（PCI）  \n4. 体外冲击波碎石术（ESWL）  \n5. 白内障超声乳化术  \n6. 胸腔镜肺叶切除术  \n7. 骨髓移植术  \n8. 人工关节置换术  \n9. 脑电图（EEG）监测  \n10. 鼻内镜下鼻窦手术  \n11. 宫腔镜下宫腔粘连分离术  \n12. 心脏起搏器植入术  \n13. 肾穿刺活检术  \n14. 腰椎间盘突出微创手术  \n15. 静脉溶栓治疗", "role": "assistant"}}], "created": 1745477036, "id": "20250424144351e662a4ab7e464049", "model": "glm-4-air-250414", "request_id": "20250424144351e662a4ab7e464049", "usage": {"completion_tokens": 160, "prompt_tokens": 48, "total_tokens": 208}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成15个多样化的、可以归类为过敏信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的过敏信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 889}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n花生过敏  \n乳制品不耐受  \n花粉症  \n海鲜过敏  \n麸质过敏  \n坚果类过敏  \n药物过敏（青霉素）  \n宠物毛发过敏  \n昆虫叮咬过敏  \n酒精不耐受  \n染料过敏  \n香精过敏  \n磺胺类药物过敏  \n霉菌过敏  \n鸡蛋过敏", "role": "assistant"}}], "created": 1745477036, "id": "20250424144354b2b852502d5f458c", "model": "glm-4-air-250414", "request_id": "20250424144354b2b852502d5f458c", "usage": {"completion_tokens": 63, "prompt_tokens": 48, "total_tokens": 111}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成15个多样化的、可以归类为姓名的命名实体，每行一个。这些实体将用于命名实体识别任务中的姓名类型。\n请生成多样化的中文姓名，包括不同性别、年龄段和地区特色的姓名。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 917}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n张伟  \n李娜  \n王芳  \n刘强  \n陈明  \n黄晓燕  \n赵鑫  \n周涛  \n孙丽  \n吴佳  \n郑阳  \n朱海燕  \n马超  \n徐静  \n胡军", "role": "assistant"}}], "created": **********, "id": "202504241443551d745b4400cf4300", "model": "glm-4-air-250414", "request_id": "202504241443551d745b4400cf4300", "usage": {"completion_tokens": 48, "prompt_tokens": 63, "total_tokens": 111}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成15个多样化的、可以归类为民族的命名实体，每行一个。这些实体将用于命名实体识别任务中的民族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 959}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n汉族  \n回族  \n藏族  \n维吾尔族  \n蒙古族  \n壮族  \n彝族  \n苗族  \n满族  \n侗族  \n瑶族  \n白族  \n土家族  \n哈萨克族  \n傣族", "role": "assistant"}}], "created": **********, "id": "20250424144356e13c58cbbca84744", "model": "glm-4-air-250414", "request_id": "20250424144356e13c58cbbca84744", "usage": {"completion_tokens": 41, "prompt_tokens": 46, "total_tokens": 87}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成15个多样化的、可以归类为种族的命名实体，每行一个。这些实体将用于命名实体识别任务中的种族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 952}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n以下是为命名实体识别任务中的种族类型生成的15个多样化的命名实体，每行一个：\n\n1. 汉族\n2. 印度斯坦族\n3. 尼日利亚人\n4. 阿拉伯人\n5. 印度尼西亚人\n6. 巴西人\n7. 墨西哥人\n8. 韩国人\n9. 日本人\n10. 菲律宾人\n11. 孟加拉人\n12. 巴基斯坦人\n13. 埃及人\n14. 土耳其人\n15. 尼泊尔人", "role": "assistant"}}], "created": **********, "id": "2025042414435323cca337a0784cf5", "model": "glm-4-air-250414", "request_id": "2025042414435323cca337a0784cf5", "usage": {"completion_tokens": 130, "prompt_tokens": 47, "total_tokens": 177}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成20个多样化的、可以归类为职业的命名实体，每行一个。这些实体将用于命名实体识别任务中的职业类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 945}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Software Engineer  \n2. Data Scientist  \n3. Marketing Manager  \n4. Financial Analyst  \n5. Registered Nurse  \n6. Elementary School Teacher  \n7. Mechanical Engineer  \n8. Graphic Designer  \n9. Human Resources Specialist  \n10. Electrician  \n11. Veterinarian  \n12. Accountant  \n13. Project Manager  \n14. Chef  \n15. Software Architect  \n16. Insurance Underwriter  \n17. Construction Worker  \n18. Public Relations Specialist  \n19. Environmental Scientist  \n20. Retail Sales Associate", "role": "assistant"}}], "created": **********, "id": "20250424144355aabe6b758e4b4576", "model": "glm-4-air-250414", "request_id": "20250424144355aabe6b758e4b4576", "usage": {"completion_tokens": 106, "prompt_tokens": 47, "total_tokens": 153}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成20个多样化的、可以归类为年龄的命名实体，每行一个。这些实体将用于命名实体识别任务中的年龄类型。\n请生成多样化的年龄表达，包括具体数字和年龄段描述。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 924}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 25岁  \n2. 中年  \n3. 30岁左右  \n4. 老年人  \n5. 少年  \n6. 18-25岁  \n7. 青年  \n8. 50多岁  \n9. 儿童  \n10. 40岁出头  \n11. 幼年  \n12. 青少年  \n13. 60岁以上  \n14. 青壮年  \n15. 35岁上下  \n16. 中老年  \n17. 20来岁  \n18. 中年人  \n19. 40岁上下  \n20. 70岁左右", "role": "assistant"}}], "created": **********, "id": "202504241443548e61344c038d4ec4", "model": "glm-4-air-250414", "request_id": "202504241443548e61344c038d4ec4", "usage": {"completion_tokens": 131, "prompt_tokens": 60, "total_tokens": 191}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成15个多样化的、可以归类为国籍的命名实体，每行一个。这些实体将用于命名实体识别任务中的国籍类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 938}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. American\n2. Chinese\n3. British\n4. Indian\n5. French\n6. German\n7. Japanese\n8. Brazilian\n9. Canadian\n10. Australian\n11. Nigerian\n12. Russian\n13. Mexican\n14. Spanish\n15. Italian", "role": "assistant"}}], "created": 1745477038, "id": "20250424144356a20b09e2705c4ef5", "model": "glm-4-air-250414", "request_id": "20250424144356a20b09e2705c4ef5", "usage": {"completion_tokens": 61, "prompt_tokens": 47, "total_tokens": 108}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名社交网络领域的数据隐私专家。请生成20个多样化的、可以归类为地理位置的命名实体，每行一个。这些实体将用于命名实体识别任务中的地理位置类型。\n请生成多样化的地理位置，包括省市区县、街道地址、建筑物名称等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 903}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 北京市朝阳区\n2. 上海市浦东新区\n3. 广东省深圳市南山区\n4. 江苏省南京市玄武区\n5. 湖北省武汉市江汉区\n6. 西安市碑林区\n7. 哈尔滨市道里区\n8. 重庆市渝中区\n9. 杭州市西湖区\n10. 青岛市市南区\n11. 北京路\n12. 长江路\n13. 黄河路\n14. 中山大街\n15. 解放碑\n16. 东方明珠塔\n17. 天安门广场\n18. 国家大剧院\n19. 上海环球金融中心\n20. 广州塔", "role": "assistant"}}], "created": 1745477038, "id": "202504241443551455a6fb5b1c4a3f", "model": "glm-4-air-250414", "request_id": "202504241443551455a6fb5b1c4a3f", "usage": {"completion_tokens": 146, "prompt_tokens": 64, "total_tokens": 210}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成10个多样化的、可以归类为政治倾向的命名实体，每行一个。这些实体将用于命名实体识别任务中的政治倾向类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 980}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Progressive Alliance  \n2. Conservative Union  \n3. Libertarian Front  \n4. Socialist Movement  \n5. Green Party  \n6. Nationalist Coalition  \n7. Labor Congress  \n8. Republican Caucus  \n9. Democratic Association  \n10. Independent Network", "role": "assistant"}}], "created": 1745477038, "id": "20250424144357e27050d8f9bf4e79", "model": "glm-4-air-250414", "request_id": "20250424144357e27050d8f9bf4e79", "usage": {"completion_tokens": 52, "prompt_tokens": 49, "total_tokens": 101}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成15个多样化的、可以归类为药物的命名实体，每行一个。这些实体将用于命名实体识别任务中的药物类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1043}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n阿司匹林  \n布洛芬  \n吗啡  \n青霉素  \n甲硝唑  \n氯雷他定  \n左氧氟沙星  \n奥美拉唑  \n二甲双胍  \n地高辛  \n利福平  \n苯妥英钠  \n华法林  \n多潘立酮  \n氨氯地平", "role": "assistant"}}], "created": 1745477039, "id": "202504241443588ec3e70307bb49f1", "model": "glm-4-air-250414", "request_id": "202504241443588ec3e70307bb49f1", "usage": {"completion_tokens": 68, "prompt_tokens": 46, "total_tokens": 114}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成10个多样化的、可以归类为婚姻状况的命名实体，每行一个。这些实体将用于命名实体识别任务中的婚姻状况类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 973}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 已婚  \n2. 未婚  \n3. 离异  \n4. 丧偶  \n5. 同居  \n6. 分居  \n7. 注册伴侣  \n8. 单身  \n9. 民政结合  \n10. 未说明", "role": "assistant"}}], "created": 1745477040, "id": "20250424144359003a3014f82f42ae", "model": "glm-4-air-250414", "request_id": "20250424144359003a3014f82f42ae", "usage": {"completion_tokens": 57, "prompt_tokens": 49, "total_tokens": 106}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成20个多样化的、可以归类为信用记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的信用记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1015}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 信用评分\n2. 信用报告\n3. 借款历史\n4. 逾期记录\n5. 公共记录\n6. 信用卡记录\n7. 按揭贷款记录\n8. 学生贷款记录\n9. 汽车贷款记录\n10. 个人贷款记录\n11. 信用查询记录\n12. 信用额度\n13. 信用利用率\n14. 信用账户状态\n15. 信用支付历史\n16. 信用债务比例\n17. 信用违约记录\n18. 信用欺诈警报\n19. 信用修复记录\n20. 信用监测服务", "role": "assistant"}}], "created": 1745477040, "id": "202504241443570d5b62e7e7ad44f3", "model": "glm-4-air-250414", "request_id": "202504241443570d5b62e7e7ad44f3", "usage": {"completion_tokens": 137, "prompt_tokens": 49, "total_tokens": 186}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成15个多样化的、可以归类为疾病的命名实体，每行一个。这些实体将用于命名实体识别任务中的疾病类型。\n请生成多样化的疾病名称，包括常见病、慢性病、罕见病等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1036}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n高血压  \n糖尿病  \n哮喘  \n心脏病  \n类风湿关节炎  \n多发性硬化  \n帕金森病  \n癫痫  \n艾滋病  \n系统性红斑狼疮  \n地中海贫血  \n囊性纤维化  \n亨廷顿舞蹈症  \n肺结核  \n精神分裂症", "role": "assistant"}}], "created": 1745477041, "id": "2025042414440028a0712fd15f457b", "model": "glm-4-air-250414", "request_id": "2025042414440028a0712fd15f457b", "usage": {"completion_tokens": 52, "prompt_tokens": 63, "total_tokens": 115}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成25个多样化的、可以归类为工资数额的命名实体，每行一个。这些实体将用于命名实体识别任务中的工资数额类型。\n请生成多样化的工资金额表达，包括月薪、年薪、时薪等不同计量方式。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 994}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 月薪8000元  \n2. 年薪15万  \n3. 时薪50元  \n4. 月收入2.5万  \n5. 年薪25万元人民币  \n6. 每小时薪资120元  \n7. 月薪6k  \n8. 年薪30万+  \n9. 时薪35美元  \n10. 月工资10000元  \n11. 年薪45万元  \n12. 每月5000元  \n13. 时薪60元人民币  \n14. 年收入20万  \n15. 月薪1.2万  \n16. 年薪18万元  \n17. 时薪45美元  \n18. 月工资7500元  \n19. 年薪12万  \n20. 每小时75元  \n21. 月薪9000  \n22. 年薪50万元  \n23. 时薪40元  \n24. 月收入3万  \n25. 年薪8万元", "role": "assistant"}}], "created": 1745477041, "id": "2025042414435792e3a3b11c624fba", "model": "glm-4-air-250414", "request_id": "2025042414435792e3a3b11c624fba", "usage": {"completion_tokens": 194, "prompt_tokens": 69, "total_tokens": 263}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成15个多样化的、可以归类为家庭成员的命名实体，每行一个。这些实体将用于命名实体识别任务中的家庭成员类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 987}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 母亲  \n2. 父亲  \n3. 配偶  \n4. 兄弟  \n5. 姐妹  \n6. 儿子  \n7. 女儿  \n8. 祖母  \n9. 祖父  \n10. 丈夫  \n11. 妻子  \n12. 侄子  \n13. 侄女  \n14. 伯父  \n15. 舅舅", "role": "assistant"}}], "created": 1745477042, "id": "2025042414440027884cab93ba48bd", "model": "glm-4-air-250414", "request_id": "2025042414440027884cab93ba48bd", "usage": {"completion_tokens": 98, "prompt_tokens": 47, "total_tokens": 145}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成20个多样化的、可以归类为投资产品的命名实体，每行一个。这些实体将用于命名实体识别任务中的投资产品类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1001}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 股票基金  \n2. 债券ETF  \n3. 贵金属期货  \n4. 房地产信托  \n5. 创业板指数  \n6. 蓝筹股组合  \n7. 高收益债券  \n8. 货币市场基金  \n9. 量化对冲产品  \n10. 大宗商品期权  \n11. 可转换债券  \n12. REITs投资信托  \n13. 消费者信贷ABS  \n14. 普通股优先权  \n15. 互联网科技ETF  \n16. 地方政府债券  \n17. 油气勘探基金  \n18. 环保产业债券  \n19. 医疗健康指数  \n20. 人工智能产业基金", "role": "assistant"}}], "created": 1745477042, "id": "20250424144358c6ad415626ea426b", "model": "glm-4-air-250414", "request_id": "20250424144358c6ad415626ea426b", "usage": {"completion_tokens": 159, "prompt_tokens": 48, "total_tokens": 207}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成20个多样化的、可以归类为教育背景的命名实体，每行一个。这些实体将用于命名实体识别任务中的教育背景类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 966}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 博士学位\n2. 本科教育\n3. 高中毕业证书\n4. 硕士学位\n5. 职业培训课程\n6. 学士学位\n7. 中专学历\n8. 大专文凭\n9. 工程师认证\n10. 艺术学士\n11. 法学硕士\n12. 医学博士\n13. 教育学学士\n14. 技术学校证书\n15. 研究生学历\n16. 商业管理硕士\n17. 计算机科学博士\n18. 外语培训课程\n19. 理学学士\n20. 职业学院毕业证书", "role": "assistant"}}], "created": 1745477042, "id": "20250424144359430f85152c3a4b26", "model": "glm-4-air-250414", "request_id": "20250424144359430f85152c3a4b26", "usage": {"completion_tokens": 150, "prompt_tokens": 49, "total_tokens": 199}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名金融财务领域的数据隐私专家。请生成20个多样化的、可以归类为年龄的命名实体，每行一个。这些实体将用于命名实体识别任务中的年龄类型。\n请生成多样化的年龄表达，包括具体数字和年龄段描述。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 49}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 25岁  \n2. 40岁  \n3. 16岁  \n4. 六十多岁  \n5. 30岁左右  \n6. 5岁  \n7. 三十岁出头  \n8. 中年  \n9. 近70岁  \n10. 少年时期  \n11. 55-65岁  \n12. 20多岁  \n13. 古稀之年  \n14. 婴儿期  \n15. 35-40岁  \n16. 青年  \n17. 50余岁  \n18. 老年  \n19. 十几岁  \n20. 幼年", "role": "assistant"}}], "created": 1745477044, "id": "202504241444022369876a03a44749", "model": "glm-4-air-250414", "request_id": "202504241444022369876a03a44749", "usage": {"completion_tokens": 132, "prompt_tokens": 60, "total_tokens": 192}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成15个多样化的、可以归类为临床表现的命名实体，每行一个。这些实体将用于命名实体识别任务中的临床表现类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1050}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 胸痛  \n2. 发热  \n3. 呼吸困难  \n4. 恶心呕吐  \n5. 腹泻  \n6. 头痛  \n7. 关节疼痛  \n8. 皮疹  \n9. 意识模糊  \n10. 肌肉无力  \n11. 感觉异常  \n12. 咳嗽  \n13. 尿频  \n14. 视力模糊  \n15. 失眠", "role": "assistant"}}], "created": 1745477045, "id": "2025042414440296c7a069076d441e", "model": "glm-4-air-250414", "request_id": "2025042414440296c7a069076d441e", "usage": {"completion_tokens": 103, "prompt_tokens": 47, "total_tokens": 150}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成20个多样化的、可以归类为实体资产的命名实体，每行一个。这些实体将用于命名实体识别任务中的实体资产类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1022}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 学生档案系统\n2. 教师人事记录库\n3. 课程评估数据集\n4. 在线学习平台账户\n5. 毕业生就业数据库\n6. 校园安全监控录像\n7. 员工薪资明细表\n8. 学生成绩管理系统\n9. 研究项目资助文件\n10. 校园活动报名信息\n11. 教师培训记录文档\n12. 学生健康档案数据\n13. 校园网络登录日志\n14. 培训机构学员名单\n15. 职业技能认证证书\n16. 招聘会企业联系方式\n17. 教师专业发展记录\n18. 学生成才档案材料\n19. 教育研究实验数据\n20. 校园设施维护记录", "role": "assistant"}}], "created": 1745477045, "id": "202504241444015d58c841a93d4ea6", "model": "glm-4-air-250414", "request_id": "202504241444015d58c841a93d4ea6", "usage": {"completion_tokens": 171, "prompt_tokens": 48, "total_tokens": 219}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成20个多样化的、可以归类为税务记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的税务记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1008}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 薪资所得税申报表\n2. 企业增值税明细\n3. 个人所得税完税证明\n4. 税务登记信息变更记录\n5. 零申报纳税报告\n6. 跨境贸易关税清单\n7. 增值税专用发票存根\n8. 土地使用税缴纳凭证\n9. 企业年度汇算清缴表\n10. 个人所得税专项附加扣除记录\n11. 税务审计调查报告\n12. 房产税计税依据表\n13. 企业所得税季度预缴申报单\n14. 印花税缴纳登记簿\n15. 契税减免申请审批表\n16. 个人社保缴费税务记录\n17. 税务违法处罚决定书\n18. 车船税缴纳收据\n19. 企业资产折旧税务备案\n20. 个体工商户简易申报表", "role": "assistant"}}], "created": 1745477045, "id": "202504241444007d32463c988042bd", "model": "glm-4-air-250414", "request_id": "202504241444007d32463c988042bd", "usage": {"completion_tokens": 195, "prompt_tokens": 49, "total_tokens": 244}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成20个多样化的、可以归类为地理位置的命名实体，每行一个。这些实体将用于命名实体识别任务中的地理位置类型。\n请生成多样化的地理位置，包括省市区县、街道地址、建筑物名称等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1078}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n北京市  \n上海市  \n广东省  \n深圳市南山区  \n西湖区文三路  \n东方明珠塔  \n国家会议中心  \n长安街  \n纽约市曼哈顿  \n旧金山市金门大桥  \n东京都新宿区  \n悉尼市中心  \n伦敦市白金汉宫  \n巴黎市埃菲尔铁塔  \n莫斯科市红场  \n柏林市勃兰登堡门  \n多伦多市中心  \n新加坡市乌节路  \n华盛顿特区白宫  \n里约热内卢科帕卡巴纳海滩", "role": "assistant"}}], "created": 1745477046, "id": "202504241444041a40250fbc1a4178", "model": "glm-4-air-250414", "request_id": "202504241444041a40250fbc1a4178", "usage": {"completion_tokens": 105, "prompt_tokens": 64, "total_tokens": 169}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成15个多样化的、可以归类为医疗程序的命名实体，每行一个。这些实体将用于命名实体识别任务中的医疗程序类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1057}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 腔镜胆囊切除术  \n2. 人工关节置换术  \n3. 经皮冠状动脉介入治疗  \n4. 核磁共振成像检查  \n5. 肺癌根治性切除术  \n6. 白内障超声乳化吸除术  \n7. 颅内动脉瘤夹闭术  \n8. 宫颈癌根治性放疗  \n9. 肾脏移植手术  \n10. 腰椎间盘突出症微创手术  \n11. 乳腺癌保乳手术  \n12. 骨髓穿刺活检术  \n13. 肝脏射频消融治疗  \n14. 脑出血钻孔引流术  \n15. 心脏起搏器植入术", "role": "assistant"}}], "created": 1745477046, "id": "202504241444020a64ee90e2aa4080", "model": "glm-4-air-250414", "request_id": "202504241444020a64ee90e2aa4080", "usage": {"completion_tokens": 147, "prompt_tokens": 48, "total_tokens": 195}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成15个多样化的、可以归类为过敏信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的过敏信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1064}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 花粉过敏史  \n2. 青霉素过敏记录  \n3. 哮喘发作史  \n4. 螨虫过敏反应  \n5. 蛋白质过敏源  \n6. 鱼类过敏档案  \n7. 鸡蛋不耐受记录  \n8. 布洛芬过敏标记  \n9. 青菜过敏信息  \n10. 尘螨过敏详情  \n11. 牛奶蛋白敏感史  \n12. 碘过敏测试结果  \n13. 花生过敏警示  \n14. 咖啡因不耐受记录  \n15. 橡胶制品过敏文档", "role": "assistant"}}], "created": 1745477046, "id": "202504241444034bc48af346534620", "model": "glm-4-air-250414", "request_id": "202504241444034bc48af346534620", "usage": {"completion_tokens": 140, "prompt_tokens": 48, "total_tokens": 188}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成15个多样化的、可以归类为姓名的命名实体，每行一个。这些实体将用于命名实体识别任务中的姓名类型。\n请生成多样化的中文姓名，包括不同性别、年龄段和地区特色的姓名。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1092}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n李明  \n张雨薇  \n王大山  \n陈思思  \n刘建国  \n赵秀英  \n孙小梅  \n周强  \n吴丽华  \n郑天成  \n钱芳芳  \n冯云飞  \n朱永强  \n许晓燕  \n何俊杰", "role": "assistant"}}], "created": 1745477046, "id": "20250424144405a2dadbc49c2740ee", "model": "glm-4-air-250414", "request_id": "20250424144405a2dadbc49c2740ee", "usage": {"completion_tokens": 56, "prompt_tokens": 63, "total_tokens": 119}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成15个多样化的、可以归类为生育信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的生育信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1071}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 孕周记录  \n2. 分娩日期  \n3. 流产史  \n4. 子宫肌瘤信息  \n5. 乳腺检查结果  \n6. 卵巢功能评估  \n7. 人工授精记录  \n8. 输卵管堵塞报告  \n9. 胎儿发育监测  \n10. 哺乳期时间  \n11. 辅助生殖治疗史  \n12. 避孕措施记录  \n13. 产前筛查结果  \n14. 不孕不育诊断  \n15. 男性生育力检测", "role": "assistant"}}], "created": 1745477046, "id": "202504241444037262ed6aef5e430d", "model": "glm-4-air-250414", "request_id": "202504241444037262ed6aef5e430d", "usage": {"completion_tokens": 118, "prompt_tokens": 48, "total_tokens": 166}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成10个多样化的、可以归类为性别的命名实体，每行一个。这些实体将用于命名实体识别任务中的性别类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1106}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n性别 (Gender)  \n女性 (Female)  \n男性 (Male)  \n跨性别者 (Transgender)  \n双性人 (Intersex)  \n非二元性别者 (Non-binary)  \n性别酷儿 (Genderqueer)  \n性别流动者 (Genderfluid)  \n性别中立者 (Gender-neutral)  \n无性别者 (Agender)", "role": "assistant"}}], "created": 1745477047, "id": "20250424144405ae86f1ec8b844e1e", "model": "glm-4-air-250414", "request_id": "20250424144405ae86f1ec8b844e1e", "usage": {"completion_tokens": 74, "prompt_tokens": 47, "total_tokens": 121}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成15个多样化的、可以归类为国籍的命名实体，每行一个。这些实体将用于命名实体识别任务中的国籍类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1113}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 中国籍  \n2. 美国籍  \n3. 日本籍  \n4. 英国籍  \n5. 法国籍  \n6. 德国籍  \n7. 俄罗斯籍  \n8. 巴西籍  \n9. 印度籍  \n10. 加拿大籍  \n11. 澳大利亚籍  \n12. 韩国籍  \n13. 意大利籍  \n14. 墨西哥籍  \n15. 南非籍", "role": "assistant"}}], "created": 1745477048, "id": "20250424144406eb95e34f6e0d46ba", "model": "glm-4-air-250414", "request_id": "20250424144406eb95e34f6e0d46ba", "usage": {"completion_tokens": 97, "prompt_tokens": 47, "total_tokens": 144}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成15个多样化的、可以归类为民族的命名实体，每行一个。这些实体将用于命名实体识别任务中的民族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1134}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n汉族  \n回族  \n藏族  \n维吾尔族  \n苗族  \n彝族  \n壮族  \n满族  \n侗族  \n瑶族  \n白族  \n土家族  \n哈尼族  \n哈萨克族  \n傣族", "role": "assistant"}}], "created": 1745477048, "id": "2025042414440743019225aa7c4800", "model": "glm-4-air-250414", "request_id": "2025042414440743019225aa7c4800", "usage": {"completion_tokens": 42, "prompt_tokens": 46, "total_tokens": 88}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成20个多样化的、可以归类为年龄的命名实体，每行一个。这些实体将用于命名实体识别任务中的年龄类型。\n请生成多样化的年龄表达，包括具体数字和年龄段描述。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1099}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 25岁  \n2. 三十岁  \n3. 40多岁  \n4. 年轻人  \n5. 中年人  \n6. 60岁  \n7. 七旬老人  \n8. 30岁左右  \n9. 小孩  \n10. 50年代生人  \n11. 28周岁  \n12. 青年人  \n13. 70岁高龄  \n14. 40-50岁之间  \n15. 老年人  \n16. 20岁出头  \n17. 80岁  \n18. 幼儿  \n19. 花甲之年  \n20. 90后", "role": "assistant"}}], "created": 1745477048, "id": "20250424144405aa0e8c4168804224", "model": "glm-4-air-250414", "request_id": "20250424144405aa0e8c4168804224", "usage": {"completion_tokens": 134, "prompt_tokens": 60, "total_tokens": 194}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成25个多样化的、可以归类为交易信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的交易信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1029}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 薪资流水记录  \n2. 招聘费用单据  \n3. 合同签署时间戳  \n4. 佣金支付明细  \n5. 税务缴款凭证  \n6. 培训费用发票  \n7. 社保缴纳记录  \n8. 劳务派遣合同  \n9. 股票期权协议  \n10. 差旅报销单  \n11. 年终奖金发放表  \n12. 退休金转账记录  \n13. 供应商付款通知  \n14. 职业保险理赔单  \n15. 外包服务合同  \n16. 工资单电子副本  \n17. 股权激励计划  \n18. 离职结算清单  \n19. 采购订单确认  \n20. 薪酬调整通知  \n21. 临时工结算单  \n22. 业绩奖金明细  \n23. 退休福利计算表  \n24. 员工借款记录  \n25. 股东分红通知", "role": "assistant"}}], "created": 1745477048, "id": "20250424144401c9ec035e962441de", "model": "glm-4-air-250414", "request_id": "20250424144401c9ec035e962441de", "usage": {"completion_tokens": 211, "prompt_tokens": 48, "total_tokens": 259}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名教育就业领域的数据隐私专家。请生成25个多样化的、可以归类为行程信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的行程信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1085}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 出发日期\n2. 到达时间\n3. 航班号\n4. 车次信息\n5. 住宿地点\n6. 会议地点\n7. 出发城市\n8. 目的地国家\n9. 行程天数\n10. 交通方式\n11. 预定单号\n12. 会议室编号\n13. 机票价格\n14. 火车票余票\n15. 旅馆名称\n16. 行程安排表\n17. 停留时长\n18. 出发机场\n19. 到达港口\n20. 活动日程\n21. 行李托运号\n22. 车票座位号\n23. 会议时间\n24. 往返路线\n25. 旅行保险单号", "role": "assistant"}}], "created": 1745477049, "id": "20250424144404ce59ebcd45a844b2", "model": "glm-4-air-250414", "request_id": "20250424144404ce59ebcd45a844b2", "usage": {"completion_tokens": 169, "prompt_tokens": 48, "total_tokens": 217}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成10个多样化的、可以归类为婚姻状况的命名实体，每行一个。这些实体将用于命名实体识别任务中的婚姻状况类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1148}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 未婚  \n2. 已婚  \n3. 离异  \n4. 丧偶  \n5. 民事结合  \n6. 分居  \n7. 事实婚姻  \n8. 单身  \n9. 同居关系  \n10. 复婚", "role": "assistant"}}], "created": 1745477050, "id": "20250424144408534ff6fdff7f4490", "model": "glm-4-air-250414", "request_id": "20250424144408534ff6fdff7f4490", "usage": {"completion_tokens": 60, "prompt_tokens": 49, "total_tokens": 109}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成20个多样化的、可以归类为职业的命名实体，每行一个。这些实体将用于命名实体识别任务中的职业类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1120}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 公务员\n2. 医生\n3. 教师\n4. 警察\n5. 消防员\n6. 社会工作者\n7. 税务专员\n8. 法官\n9. 建筑工程师\n10. 环境保护官员\n11. 交通指挥员\n12. 档案管理员\n13. 公共卫生专家\n14. 社区调解员\n15. 水利工程师\n16. 统计分析师\n17. 文化遗产保护员\n18. 公共安全顾问\n19. 农业技术推广员\n20. 城市规划师", "role": "assistant"}}], "created": 1745477050, "id": "2025042414440691874c28544a4358", "model": "glm-4-air-250414", "request_id": "2025042414440691874c28544a4358", "usage": {"completion_tokens": 143, "prompt_tokens": 47, "total_tokens": 190}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成15个多样化的、可以归类为种族的命名实体，每行一个。这些实体将用于命名实体识别任务中的种族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1127}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n以下是一些多样化的种族命名实体，每行一个，适用于命名实体识别任务中的种族类型分类：\n\n1. 汉族\n2. 藏族\n3. 维吾尔族\n4. 壮族\n5. 苗族\n6. 彝族\n7. 土家族\n8. 蒙古族\n9. 朝鲜族\n10. 满族\n11. 回族\n12. 白族\n13. 哈尼族\n14. 傣族\n15. 纳西族", "role": "assistant"}}], "created": 1745477050, "id": "2025042414440758fba133e87e452f", "model": "glm-4-air-250414", "request_id": "2025042414440758fba133e87e452f", "usage": {"completion_tokens": 119, "prompt_tokens": 47, "total_tokens": 166}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成10个多样化的、可以归类为政治倾向的命名实体，每行一个。这些实体将用于命名实体识别任务中的政治倾向类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1155}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 民主党派支持者  \n2. 共和党核心成员  \n3. 绿色党环保倡导者  \n4. 工党劳工权益代表  \n5. 保守主义联盟成员  \n6. 自由主义先锋组织  \n7. 社会主义青年团  \n8. 无党派中立选民  \n9. 极右翼民族主义者  \n10. 左翼进步运动领袖", "role": "assistant"}}], "created": 1745477050, "id": "2025042414440825fcc76cebbf4aff", "model": "glm-4-air-250414", "request_id": "2025042414440825fcc76cebbf4aff", "usage": {"completion_tokens": 88, "prompt_tokens": 49, "total_tokens": 137}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成20个多样化的、可以归类为教育背景的命名实体，每行一个。这些实体将用于命名实体识别任务中的教育背景类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1141}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 高中毕业生  \n2. 大学本科在读  \n3. 硕士学位持有者  \n4. 博士后研究员  \n5. 职业技术学院学生  \n6. 研究生学历  \n7. 学士学位获得者  \n8. 专科毕业生  \n9. 中等教育证书持有者  \n10. 博士学历  \n11. 在职研究生  \n12. 学士学位在读  \n13. 硕士在读  \n14. 高等教育文凭  \n15. 本科学历  \n16. 研究生毕业生  \n17. 硕士毕业生  \n18. 博士毕业生  \n19. 技工学校毕业生  \n20. 成人高等教育学历", "role": "assistant"}}], "created": 1745477051, "id": "20250424144408fc6b668f62b746aa", "model": "glm-4-air-250414", "request_id": "20250424144408fc6b668f62b746aa", "usage": {"completion_tokens": 148, "prompt_tokens": 49, "total_tokens": 197}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成15个多样化的、可以归类为家庭成员的命名实体，每行一个。这些实体将用于命名实体识别任务中的家庭成员类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1162}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 父亲  \n2. 母亲  \n3. 儿子  \n4. 女儿  \n5. 爷爷  \n6. 奶奶  \n7. 叔叔  \n8. 阿姨  \n9. 兄弟  \n10. 姐妹  \n11. 丈夫  \n12. 妻子  \n13. 儿媳  \n14. 孙子  \n15. 孙女", "role": "assistant"}}], "created": 1745477051, "id": "2025042414440920441f9f5b2d4453", "model": "glm-4-air-250414", "request_id": "2025042414440920441f9f5b2d4453", "usage": {"completion_tokens": 98, "prompt_tokens": 47, "total_tokens": 145}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成25个多样化的、可以归类为工资数额的命名实体，每行一个。这些实体将用于命名实体识别任务中的工资数额类型。\n请生成多样化的工资金额表达，包括月薪、年薪、时薪等不同计量方式。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1169}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n月薪8500元  \n年薪30万元  \n时薪45元  \n月薪12,000元  \n年薪50万人民币  \n时薪60元整  \n月薪9,500  \n年薪35万元整  \n时薪50.5元  \n月薪8千  \n年薪28万  \n时薪55  \n月薪10,500元  \n年薪40万元  \n时薪65元  \n月薪7,800  \n年薪25万  \n时薪48元  \n月薪11,000人民币  \n年薪38万元整  \n时薪58  \n月薪9,200元  \n年薪32万  \n时薪52元整  \n月薪15,000  \n年薪45万元  \n时薪70元", "role": "assistant"}}], "created": 1745477052, "id": "2025042414440943aaddfca2964113", "model": "glm-4-air-250414", "request_id": "2025042414440943aaddfca2964113", "usage": {"completion_tokens": 135, "prompt_tokens": 69, "total_tokens": 204}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成20个多样化的、可以归类为投资产品的命名实体，每行一个。这些实体将用于命名实体识别任务中的投资产品类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1176}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 国债基金\n2. 股票型ETF\n3. 保险连结储蓄计划\n4. 货币市场基金\n5. 高收益债券\n6. 房地产投资信托\n7. 黄金现货合约\n8. 指数增强型产品\n9. 混合型共同基金\n10. 股息再投资计划\n11. 贵金属交易账户\n12. 退休年金产品\n13. 结构性票据\n14. 稳健收益债\n15. 私募股权基金\n16. 对冲基金产品\n17. 科技主题基金\n18. 银行理财产品\n19. 可转换债券\n20. 量化交易策略", "role": "assistant"}}], "created": 1745477053, "id": "20250424144410740e9cd932fa47ed", "model": "glm-4-air-250414", "request_id": "20250424144410740e9cd932fa47ed", "usage": {"completion_tokens": 158, "prompt_tokens": 48, "total_tokens": 206}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成20个多样化的、可以归类为信用记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的信用记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1190}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 信用卡欠款记录  \n2. 贷款还款历史  \n3. 公共事业缴费记录  \n4. 法院判决记录  \n5. 破产申请记录  \n6. 逾期账单明细  \n7. 信用卡额度使用情况  \n8. 担保贷款信息  \n9. 房屋抵押贷款记录  \n10. 个人税务缴纳记录  \n11. 小额信贷评分数据  \n12. 车辆贷款还款状态  \n13. 租赁合同违约记录  \n14. 保险理赔历史  \n15. 学生贷款逾期情况  \n16. 电信服务欠费记录  \n17. 信用查询频率统计  \n18. 企业关联信用报告  \n19. 个人破产清偿记录  \n20. 社会保险金缴纳历史", "role": "assistant"}}], "created": 1745477054, "id": "20250424144410fdbde99520064fba", "model": "glm-4-air-250414", "request_id": "20250424144410fdbde99520064fba", "usage": {"completion_tokens": 166, "prompt_tokens": 49, "total_tokens": 215}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成15个多样化的、可以归类为临床表现的命名实体，每行一个。这些实体将用于命名实体识别任务中的临床表现类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1225}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 胸闷  \n2. 头痛  \n3. 发热  \n4. 咳嗽  \n5. 呼吸困难  \n6. 腹痛  \n7. 恶心  \n8. 呕吐  \n9. 肌肉酸痛  \n10. 皮疹  \n11. 脱水  \n12. 贫血  \n13. 失眠  \n14. 意识模糊  \n15. 乏力", "role": "assistant"}}], "created": 1745477055, "id": "20250424144413e3ca1bf4c52f4036", "model": "glm-4-air-250414", "request_id": "20250424144413e3ca1bf4c52f4036", "usage": {"completion_tokens": 101, "prompt_tokens": 47, "total_tokens": 148}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成15个多样化的、可以归类为疾病的命名实体，每行一个。这些实体将用于命名实体识别任务中的疾病类型。\n请生成多样化的疾病名称，包括常见病、慢性病、罕见病等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1211}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 糖尿病  \n2. 高血压  \n3. 风湿性关节炎  \n4. 阿尔茨海默病  \n5. 白血病  \n6. 肺结核  \n7. 帕金森病  \n8. 艾滋病  \n9. 系统性红斑狼疮  \n10. 神经纤维瘤病  \n11. 心肌梗死  \n12. 癫痫  \n13. 强直性脊柱炎  \n14. 肝硬化  \n15. 甲状腺功能亢进症", "role": "assistant"}}], "created": 1745477055, "id": "202504241444122c013a483054499d", "model": "glm-4-air-250414", "request_id": "202504241444122c013a483054499d", "usage": {"completion_tokens": 120, "prompt_tokens": 63, "total_tokens": 183}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成15个多样化的、可以归类为过敏信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的过敏信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1239}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n花生过敏  \n牛奶过敏  \n鸡蛋过敏  \n鱼过敏  \n贝类过敏  \n小麦过敏  \n坚果过敏  \n大豆过敏  \n磺胺类药物过敏  \n青霉素过敏  \n花粉过敏  \n尘螨过敏  \n猫毛过敏  \n狗毛过敏  \n芒果过敏", "role": "assistant"}}], "created": 1745477055, "id": "202504241444149165e031a4fd4611", "model": "glm-4-air-250414", "request_id": "202504241444149165e031a4fd4611", "usage": {"completion_tokens": 53, "prompt_tokens": 48, "total_tokens": 101}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成15个多样化的、可以归类为医疗程序的命名实体，每行一个。这些实体将用于命名实体识别任务中的医疗程序类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1232}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 眼科激光角膜手术  \n2. 心脏搭桥手术  \n3. 核磁共振成像检查  \n4. 乳腺癌筛查  \n5. 牙齿矫正治疗  \n6. 甲状腺切除术  \n7. 腹腔镜胆囊摘除  \n8. 骨髓移植  \n9. 针灸理疗  \n10. 肾透析治疗  \n11. 肺功能测试  \n12. 胃镜检查  \n13. 前列腺激光治疗  \n14. 脊柱微创手术  \n15. 糖尿病足部护理", "role": "assistant"}}], "created": 1745477056, "id": "20250424144413cd38af7cce1f4593", "model": "glm-4-air-250414", "request_id": "20250424144413cd38af7cce1f4593", "usage": {"completion_tokens": 130, "prompt_tokens": 48, "total_tokens": 178}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成15个多样化的、可以归类为药物的命名实体，每行一个。这些实体将用于命名实体识别任务中的药物类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1218}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 头孢克肟  \n2. 阿莫西林  \n3. 氢氯噻嗪  \n4. 硝酸甘油  \n5. 阿司匹林  \n6. 布洛芬  \n7. 甲硝唑  \n8. 依那普利  \n9. 氟康唑  \n10. 卡马西平  \n11. 磺胺嘧啶  \n12. 吲哚美辛  \n13. 多潘立酮  \n14. 磷酸铝  \n15. 氯雷他定", "role": "assistant"}}], "created": 1745477057, "id": "202504241444122735d0f3294846f2", "model": "glm-4-air-250414", "request_id": "202504241444122735d0f3294846f2", "usage": {"completion_tokens": 127, "prompt_tokens": 46, "total_tokens": 173}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成15个多样化的、可以归类为姓名的命名实体，每行一个。这些实体将用于命名实体识别任务中的姓名类型。\n请生成多样化的中文姓名，包括不同性别、年龄段和地区特色的姓名。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1267}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n张伟  \n李娜  \n王芳  \n刘洋  \n陈静  \n赵强  \n周梅  \n吴磊  \n郑秀英  \n孙涛  \n朱小玲  \n徐亮  \n胡雪梅  \n高天宇  \n林晓华", "role": "assistant"}}], "created": 1745477057, "id": "20250424144415ea25f8a046414c63", "model": "glm-4-air-250414", "request_id": "20250424144415ea25f8a046414c63", "usage": {"completion_tokens": 51, "prompt_tokens": 64, "total_tokens": 115}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成15个多样化的、可以归类为生育信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的生育信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1246}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 生育计划时间表  \n2. 胎儿发育记录  \n3. 孕期检查结果  \n4. 产前筛查报告  \n5. 婴儿出生证明  \n6. 产假申请文件  \n7. 流产手术记录  \n8. 辅助生殖病历  \n9. 计划生育证明  \n10. 子女抚养权文件  \n11. 人工授精报告  \n12. 胎儿性别信息  \n13. 终止妊娠申请  \n14. 生育保险凭证  \n15. 婴儿健康档案", "role": "assistant"}}], "created": 1745477057, "id": "20250424144414e44f699b89a54ced", "model": "glm-4-air-250414", "request_id": "20250424144414e44f699b89a54ced", "usage": {"completion_tokens": 123, "prompt_tokens": 48, "total_tokens": 171}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成10个多样化的、可以归类为性别的命名实体，每行一个。这些实体将用于命名实体识别任务中的性别类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1281}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n男性  \n女性  \n跨性别  \n性别酷儿  \n性别流动  \n非二元性别  \n双性人  \n无性别  \n性别中立  \n顺性别", "role": "assistant"}}], "created": 1745477057, "id": "202504241444165c2d9b8a378e41b8", "model": "glm-4-air-250414", "request_id": "202504241444165c2d9b8a378e41b8", "usage": {"completion_tokens": 32, "prompt_tokens": 48, "total_tokens": 80}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成25个多样化的、可以归类为交易信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的交易信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1204}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 支付凭证号码  \n2. 银行转账记录  \n3. 交易流水号  \n4. 消费金额明细  \n5. 网上支付订单号  \n6. 账单支付日期  \n7. 电子发票编号  \n8. 缴费确认码  \n9. 公共服务费用收据  \n10. 线上缴费凭证  \n11. 交易时间戳  \n12. 支付方式代码  \n13. 订单确认通知  \n14. 账单支付状态  \n15. 转账备注信息  \n16. 交易对方账户名  \n17. 电子支付签名  \n18. 退款处理编号  \n19. 账单分割记录  \n20. 线下交易小票  \n21. 支付渠道标识  \n22. 交易手续费明细  \n23. 公共服务订阅码  \n24. 支付安全令牌  \n25. 账单结算周期", "role": "assistant"}}], "created": 1745477058, "id": "2025042414441137e4721c053b41c0", "model": "glm-4-air-250414", "request_id": "2025042414441137e4721c053b41c0", "usage": {"completion_tokens": 205, "prompt_tokens": 48, "total_tokens": 253}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成20个多样化的、可以归类为实体资产的命名实体，每行一个。这些实体将用于命名实体识别任务中的实体资产类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1197}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n好的，作为一名政府公共服务领域的数据隐私专家，以下是为命名实体识别任务生成的20个多样化的实体资产名称，这些名称代表了政府运营中可能涉及的物理或逻辑实体资产：\n\n1.  公共图书馆分馆一号\n2.  城市数据中心主服务器A\n3.  社区养老服务中心\n4.  民航旅客信息查询终端\n5.  电子健康档案服务器集群\n6.  公共交通一卡通发卡机\n7.  环境监测传感器网络节点\n8.  政府办公大楼门禁系统\n9.  税务自助申报终端\n10. 应急广播控制中心\n11. 社区事务受理窗口\n12. 城市交通信号灯控制器\n13. 公共卫生信息采集设备\n14. 政府文件档案密集架\n15. 社区网格化管理终端\n16. 水利工程闸门控制系统\n17. 网上政务服务平台\n18. 社会保险卡读写器\n19. 城市安全监控摄像头\n20. 退役军人优待证办理柜台", "role": "assistant"}}], "created": 1745477058, "id": "20250424144411f82bb5946bbd4d1c", "model": "glm-4-air-250414", "request_id": "20250424144411f82bb5946bbd4d1c", "usage": {"completion_tokens": 244, "prompt_tokens": 48, "total_tokens": 292}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成20个多样化的、可以归类为年龄的命名实体，每行一个。这些实体将用于命名实体识别任务中的年龄类型。\n请生成多样化的年龄表达，包括具体数字和年龄段描述。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1274}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 25岁  \n2. 三十岁  \n3. 40周岁  \n4. 中年  \n5. 少年  \n6. 18岁  \n7. 老年人  \n8. 20出头  \n9. 青年  \n10. 50岁以上  \n11. 儿童期  \n12. 60多岁  \n13. 青少年  \n14. 七十年代生  \n15. 青壮年  \n16. 30岁左右  \n17. 花甲之年  \n18. 未成年人  \n19. 八十年代生  \n20. 暮年", "role": "assistant"}}], "created": 1745477058, "id": "202504241444152a020192571f47e0", "model": "glm-4-air-250414", "request_id": "202504241444152a020192571f47e0", "usage": {"completion_tokens": 133, "prompt_tokens": 61, "total_tokens": 194}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成20个多样化的、可以归类为税务记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的税务记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1183}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 个人所得税申报表  \n2. 企业所得税年度纳税申报表  \n3. 增值税专用发票记录  \n4. 消费税缴纳凭证  \n5. 土地增值税清算表  \n6. 车船税缴纳记录  \n7. 房产税缴纳证明  \n8. 印花税缴纳单  \n9. 资源税纳税申报表  \n10. 环境保护税缴纳凭证  \n11. 契税缴纳记录  \n12. 综合所得年度汇算清缴表  \n13. 非居民个人税务居民证明  \n14. 企业税收优惠申请表  \n15. 税收违法案件处理记录  \n16. 税收代扣代缴明细表  \n17. 跨境交易税务备案表  \n18. 个人财产转让所得纳税申报表  \n19. 税收减免审批文件  \n20. 税务稽查案卷材料", "role": "assistant"}}], "created": 1745477058, "id": "20250424144410c4426f0999a04281", "model": "glm-4-air-250414", "request_id": "20250424144410c4426f0999a04281", "usage": {"completion_tokens": 202, "prompt_tokens": 49, "total_tokens": 251}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成25个多样化的、可以归类为行程信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的行程信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1260}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 出发日期\n2. 到达时间\n3. 途经城市\n4. 交通方式\n5. 乘客姓名\n6. 车票编号\n7. 旅行路线\n8. 停留地点\n9. 住宿信息\n10. 行李清单\n11. 预定时间\n12. 出发机场\n13. 到达车站\n14. 旅行目的\n15. 乘车时间\n16. 返程日期\n17. 航班号\n18. 车厢座位\n19. 中转站信息\n20. 旅行证件\n21. 行程变更记录\n22. 紧急联系人\n23. 交通费用\n24. 行程保险信息\n25. 预订平台记录", "role": "assistant"}}], "created": 1745477058, "id": "202504241444158f16000209f04370", "model": "glm-4-air-250414", "request_id": "202504241444158f16000209f04370", "usage": {"completion_tokens": 168, "prompt_tokens": 48, "total_tokens": 216}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名政府公共服务领域的数据隐私专家。请生成20个多样化的、可以归类为地理位置的命名实体，每行一个。这些实体将用于命名实体识别任务中的地理位置类型。\n请生成多样化的地理位置，包括省市区县、街道地址、建筑物名称等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1253}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 北京市朝阳区  \n2. 上海市浦东新区  \n3. 广东省深圳市南山区  \n4. 河北省石家庄市长安区  \n5. 江苏省苏州市姑苏区  \n6. 四川省成都市武侯区  \n7. 浙江省杭州市西湖区  \n8. 福建省厦门市思明区  \n9. 山东省济南市历下区  \n10. 湖北省武汉市洪山区  \n11. 长沙市芙蓉区东屯渡街道  \n12. 广州市天河区珠江新城  \n13. 南京市鼓楼区中山北路  \n14. 重庆市渝中区解放碑  \n15. 西安市长安区郭杜街道  \n16. 沈阳市沈河区中街路  \n17. 深圳市福田区市民中心  \n18. 天津市和平区南京路  \n19. 青岛市市南区香港中路  \n20. 哈尔滨市南岗区红军街", "role": "assistant"}}], "created": 1745477059, "id": "202504241444144dd936eb41de46c3", "model": "glm-4-air-250414", "request_id": "202504241444144dd936eb41de46c3", "usage": {"completion_tokens": 199, "prompt_tokens": 64, "total_tokens": 263}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成15个多样化的、可以归类为国籍的命名实体，每行一个。这些实体将用于命名实体识别任务中的国籍类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1288}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 中国籍  \n2. 美国籍  \n3. 英国籍  \n4. 日本籍  \n5. 法国籍  \n6. 德国籍  \n7. 印度籍  \n8. 巴西籍  \n9. 加拿大籍  \n10. 澳大利亚籍  \n11. 俄罗斯籍  \n12. 韩国籍  \n13. 意大利籍  \n14. 西班牙籍  \n15. 南非籍", "role": "assistant"}}], "created": 1745477059, "id": "2025042414441696827f1daedf45f2", "model": "glm-4-air-250414", "request_id": "2025042414441696827f1daedf45f2", "usage": {"completion_tokens": 95, "prompt_tokens": 48, "total_tokens": 143}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成15个多样化的、可以归类为民族的命名实体，每行一个。这些实体将用于命名实体识别任务中的民族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1309}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n汉族  \n藏族  \n维吾尔族  \n蒙古族  \n回族  \n壮族  \n满族  \n苗族  \n彝族  \n土家族  \n侗族  \n瑶族  \n朝鲜族  \n白族  \n傣族", "role": "assistant"}}], "created": 1745477059, "id": "202504241444183259b889da2441cd", "model": "glm-4-air-250414", "request_id": "202504241444183259b889da2441cd", "usage": {"completion_tokens": 41, "prompt_tokens": 47, "total_tokens": 88}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成15个多样化的、可以归类为种族的命名实体，每行一个。这些实体将用于命名实体识别任务中的种族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1302}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. African_American\n2. Asian_Pacific_Islander\n3. Hispanic_Latino\n4. Native_American\n5. White_Caucasian\n6. Middle_Eastern\n7. South_Asian\n8. East_Asian\n9. Black_African\n10. Indigenous_Australian\n11. Latino_Hispanic\n12. Pacific_Islander\n13. North_African\n14. South_East_Asian\n15. Multi_Racial", "role": "assistant"}}], "created": 1745477059, "id": "20250424144417d4b6f0900f3441a0", "model": "glm-4-air-250414", "request_id": "20250424144417d4b6f0900f3441a0", "usage": {"completion_tokens": 100, "prompt_tokens": 48, "total_tokens": 148}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成20个多样化的、可以归类为职业的命名实体，每行一个。这些实体将用于命名实体识别任务中的职业类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1295}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 数据分析师  \n2. 市场研究员  \n3. 用户体验设计师  \n4. 品牌经理  \n5. 消费者行为学家  \n6. 数字营销专员  \n7. 零售店长  \n8. 采购经理  \n9. 供应链专家  \n10. 家庭经济学家  \n11. 食品科学家  \n12. 时尚顾问  \n13. 房地产经纪人  \n14. 家居设计师  \n15. 个人理财顾问  \n16. 生活方式博主  \n17. 可持续发展经理  \n18. 电商运营专员  \n19. 社交媒体经理  \n20. 消费者权益律师", "role": "assistant"}}], "created": 1745477060, "id": "202504241444176bee3dd75d214e30", "model": "glm-4-air-250414", "request_id": "202504241444176bee3dd75d214e30", "usage": {"completion_tokens": 139, "prompt_tokens": 48, "total_tokens": 187}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成10个多样化的、可以归类为婚姻状况的命名实体，每行一个。这些实体将用于命名实体识别任务中的婚姻状况类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1323}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 单身人士  \n2. 已婚有孩家庭  \n3. 离异未再婚  \n4. 同居伴侣  \n5. 丧偶独居  \n6. 再婚家庭  \n7. 恋爱中未婚  \n8. 分居状态  \n9. 注册伴侣关系  \n10. 终身未婚", "role": "assistant"}}], "created": 1745477060, "id": "202504241444183c12680026d545fe", "model": "glm-4-air-250414", "request_id": "202504241444183c12680026d545fe", "usage": {"completion_tokens": 73, "prompt_tokens": 50, "total_tokens": 123}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成10个多样化的、可以归类为政治倾向的命名实体，每行一个。这些实体将用于命名实体识别任务中的政治倾向类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1330}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 民主联盟  \n2. 共和派阵营  \n3. 进步主义协会  \n4. 保守派基金会  \n5. 自由主义联盟  \n6. 社会主义先锋  \n7. 资本主义论坛  \n8. 绿色政治运动  \n9. 中间派智库  \n10. 极右翼组织", "role": "assistant"}}], "created": 1745477061, "id": "202504241444193e68337a273949c0", "model": "glm-4-air-250414", "request_id": "202504241444193e68337a273949c0", "usage": {"completion_tokens": 76, "prompt_tokens": 50, "total_tokens": 126}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成15个多样化的、可以归类为家庭成员的命名实体，每行一个。这些实体将用于命名实体识别任务中的家庭成员类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1337}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 爸爸  \n2. 妈妈  \n3. 儿子  \n4. 女儿  \n5. 爷爷  \n6. 奶奶  \n7. 孙子  \n8. 孙女  \n9. 哥哥  \n10. 姐姐  \n11. 弟弟  \n12. 妹妹  \n13. 叔叔  \n14. 阿姨  \n15. 外公", "role": "assistant"}}], "created": 1745477062, "id": "202504241444194775f99d89e9417d", "model": "glm-4-air-250414", "request_id": "202504241444194775f99d89e9417d", "usage": {"completion_tokens": 95, "prompt_tokens": 48, "total_tokens": 143}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成20个多样化的、可以归类为教育背景的命名实体，每行一个。这些实体将用于命名实体识别任务中的教育背景类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1316}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 高中教育\n2. 本科教育\n3. 硕士研究生\n4. 博士研究生\n5. 学士学位\n6. 硕士学位\n7. 博士学位\n8. 中专教育\n9. 大专教育\n10. 职业教育\n11. 工程学教育\n12. 商业管理教育\n13. 医学教育\n14. 法律教育\n15. 艺术教育\n16. 教育学教育\n17. 技术学院教育\n18. 研究生院教育\n19. 继续教育\n20. 在线教育", "role": "assistant"}}], "created": 1745477062, "id": "202504241444187cb48281f02545f0", "model": "glm-4-air-250414", "request_id": "202504241444187cb48281f02545f0", "usage": {"completion_tokens": 141, "prompt_tokens": 50, "total_tokens": 191}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成20个多样化的、可以归类为投资产品的命名实体，每行一个。这些实体将用于命名实体识别任务中的投资产品类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1351}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 股票基金  \n2. 债券组合  \n3. 共同基金  \n4. 退休金计划  \n5. 指数基金  \n6. 信托基金  \n7. 期权合约  \n8. 期货交易  \n9. ETF产品  \n10. 保险理财  \n11. 股权投资  \n12. 不动产信托  \n13. 优先股  \n14. 对冲基金  \n15. 货币市场基金  \n16. 生命周期基金  \n17. 结构性产品  \n18. 创业投资  \n19. 定期存款  \n20. 资产支持证券", "role": "assistant"}}], "created": 1745477063, "id": "2025042414442025c039334a2a4434", "model": "glm-4-air-250414", "request_id": "2025042414442025c039334a2a4434", "usage": {"completion_tokens": 137, "prompt_tokens": 49, "total_tokens": 186}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成20个多样化的、可以归类为税务记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的税务记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1358}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 个人所得税申报表  \n2. 企业所得税纳税证明  \n3. 增值税专用发票记录  \n4. 个人所得汇算清缴单  \n5. 企业年度税务审计报告  \n6. 印花税缴纳凭证  \n7. 房产税计税依据表  \n8. 土地使用税缴纳记录  \n9. 车辆购置税完税证明  \n10. 消费税纳税申报表  \n11. 营业税纳税记录  \n12. 企业税务登记信息  \n13. 个人税务身份验证文件  \n14. 增值税进项税额明细表  \n15. 企业所得税减免申请表  \n16. 个人所得税代扣代缴凭证  \n17. 契税缴纳确认单  \n18. 企业增值税留抵退税申请  \n19. 个人年度纳税总额明细  \n20. 税务机关备案凭证", "role": "assistant"}}], "created": 1745477065, "id": "2025042414442106f00fd44ca240a6", "model": "glm-4-air-250414", "request_id": "2025042414442106f00fd44ca240a6", "usage": {"completion_tokens": 189, "prompt_tokens": 50, "total_tokens": 239}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成20个多样化的、可以归类为实体资产的命名实体，每行一个。这些实体将用于命名实体识别任务中的实体资产类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1372}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 智能手机  \n2. 笔记本电脑  \n3. 电视  \n4. 洗衣机  \n5. 冰箱  \n6. 汽车钥匙  \n7. 房产证  \n8. 银行卡  \n9. 个人电脑  \n10. 数码相机  \n11. 电动牙刷  \n12. 空调  \n13. 咖啡机  \n14. 智能手表  \n15. 保险箱  \n16. 摄像头  \n17. 游戏机  \n18. 太阳能电池板  \n19. 家具  \n20. 电子钱包", "role": "assistant"}}], "created": 1745477065, "id": "20250424144421f3e14bd5fd76423c", "model": "glm-4-air-250414", "request_id": "20250424144421f3e14bd5fd76423c", "usage": {"completion_tokens": 137, "prompt_tokens": 49, "total_tokens": 186}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成25个多样化的、可以归类为工资数额的命名实体，每行一个。这些实体将用于命名实体识别任务中的工资数额类型。\n请生成多样化的工资金额表达，包括月薪、年薪、时薪等不同计量方式。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1344}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 月薪8千  \n2. 年薪15万  \n3. 时薪50元  \n4. 月薪5千5  \n5. 年薪25万2  \n6. 时薪30元整  \n7. 月薪1万2  \n8. 年薪30万  \n9. 时薪80元  \n10. 月薪7千8  \n11. 年薪20万5  \n12. 时薪45元  \n13. 月薪9千5  \n14. 年薪18万  \n15. 时薪60元整  \n16. 月薪6千  \n17. 年薪22万8  \n18. 时薪40元  \n19. 月薪1万  \n20. 年薪12万  \n21. 时薪75元  \n22. 月薪8千5  \n23. 年薪28万3  \n24. 时薪55元  \n25. 月薪1万5", "role": "assistant"}}], "created": 1745477065, "id": "202504241444208a6e051a09134330", "model": "glm-4-air-250414", "request_id": "202504241444208a6e051a09134330", "usage": {"completion_tokens": 188, "prompt_tokens": 70, "total_tokens": 258}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成20个多样化的、可以归类为信用记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的信用记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1365}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 个人信用评分\n2. 信用卡账单\n3. 贷款还款记录\n4. 按揭贷款历史\n5. 公共记录查询\n6. 逾期还款信息\n7. 信用报告摘要\n8. 征信查询记录\n9. 负面信用事件\n10. 信用额度使用\n11. 信用查询授权\n12. 银行账户关联\n13. 债务催收通知\n14. 信用修复活动\n15. 财务负债明细\n16. 信用风险评分\n17. 信用卡申请记录\n18. 破产记录信息\n19. 信用争议状态\n20. 信用历史档案", "role": "assistant"}}], "created": 1745477066, "id": "20250424144421f246342ad57a4718", "model": "glm-4-air-250414", "request_id": "20250424144421f246342ad57a4718", "usage": {"completion_tokens": 156, "prompt_tokens": 50, "total_tokens": 206}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成25个多样化的、可以归类为交易信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的交易信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1379}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 交易金额  \n2. 支付方式  \n3. 交易时间  \n4. 交易地点  \n5. 购买商品  \n6. 发票号码  \n7. 退款记录  \n8. 信用卡号  \n9. 收款账户  \n10. 虚拟货币  \n11. 交易流水号  \n12. 电子支付凭证  \n13. 线下交易签名  \n14. 扣款授权书  \n15. 虚拟卡信息  \n16. 二维码支付码  \n17. 交易手续费  \n18. 购物积分  \n19. 赠品记录  \n20. 代金券使用记录  \n21. 会员卡号  \n22. 电子钱包余额  \n23. 跨境交易税单  \n24. 现金收据编号  \n25. 微信/支付宝交易单号", "role": "assistant"}}], "created": 1745477066, "id": "2025042414442224da9b6ba9274e95", "model": "glm-4-air-250414", "request_id": "2025042414442224da9b6ba9274e95", "usage": {"completion_tokens": 185, "prompt_tokens": 49, "total_tokens": 234}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成15个多样化的、可以归类为医疗程序的命名实体，每行一个。这些实体将用于命名实体识别任务中的医疗程序类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1407}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 白内障手术  \n2. 核磁共振检查  \n3. 胆囊切除术  \n4. 心脏搭桥手术  \n5. 牙齿种植术  \n6. 腰椎间盘突出治疗  \n7. 乳腺活检  \n8. 胃镜检查  \n9. 鼻窦炎手术  \n10. 人工关节置换  \n11. 剖腹产手术  \n12. 肾结石碎石术  \n13. 颈椎病治疗  \n14. 皮肤激光治疗  \n15. 糖尿病并发症筛查", "role": "assistant"}}], "created": 1745477066, "id": "20250424144423b46075c918f0484e", "model": "glm-4-air-250414", "request_id": "20250424144423b46075c918f0484e", "usage": {"completion_tokens": 124, "prompt_tokens": 49, "total_tokens": 173}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成15个多样化的、可以归类为临床表现的命名实体，每行一个。这些实体将用于命名实体识别任务中的临床表现类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1400}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 头痛  \n2. 发热  \n3. 咳嗽  \n4. 胸痛  \n5. 呼吸困难  \n6. 恶心  \n7. 腹泻  \n8. 皮疹  \n9. 关节痛  \n10. 眩晕  \n11. 意识模糊  \n12. 肌肉无力  \n13. 视物模糊  \n14. 瘙痒  \n15. 水肿", "role": "assistant"}}], "created": 1745477066, "id": "202504241444232ebdb49a015c4b75", "model": "glm-4-air-250414", "request_id": "202504241444232ebdb49a015c4b75", "usage": {"completion_tokens": 103, "prompt_tokens": 48, "total_tokens": 151}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成15个多样化的、可以归类为药物的命名实体，每行一个。这些实体将用于命名实体识别任务中的药物类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1393}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 诺氟沙星胶囊  \n2. 布洛芬缓释片  \n3. 阿莫西林颗粒  \n4. 头孢克肟分散片  \n5. 氧氟沙星滴眼液  \n6. 氯雷他定片  \n7. 青霉素V钾胶囊  \n8. 地塞米松磷酸钠注射液  \n9. 复方感冒灵颗粒  \n10. 甲硝唑片  \n11. 硝苯地平控释片  \n12. 葛根素注射液  \n13. 蒙脱石散剂  \n14. 醋酸泼尼松片  \n15. 硫酸锌口服溶液", "role": "assistant"}}], "created": 1745477066, "id": "20250424144423039ce26da3d246ca", "model": "glm-4-air-250414", "request_id": "20250424144423039ce26da3d246ca", "usage": {"completion_tokens": 152, "prompt_tokens": 47, "total_tokens": 199}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成15个多样化的、可以归类为过敏信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的过敏信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1414}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 常见过敏原列表\n2. 食物过敏档案\n3. 药物过敏记录\n4. 接触性过敏史\n5. 过敏原检测报告\n6. 呼吸道过敏信息\n7. 食品过敏标签\n8. 医疗过敏警告\n9. 过敏原数据库\n10. 个人过敏详情\n11. 过敏反应记录\n12. 过敏原清单\n13. 过敏史摘要\n14. 过敏触发物信息\n15. 过敏风险评估", "role": "assistant"}}], "created": 1745477066, "id": "202504241444244a7e58c59c7a4d31", "model": "glm-4-air-250414", "request_id": "202504241444244a7e58c59c7a4d31", "usage": {"completion_tokens": 121, "prompt_tokens": 49, "total_tokens": 170}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成15个多样化的、可以归类为疾病的命名实体，每行一个。这些实体将用于命名实体识别任务中的疾病类型。\n请生成多样化的疾病名称，包括常见病、慢性病、罕见病等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1386}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 糖尿病  \n2. 高血压  \n3. 风湿性关节炎  \n4. 哮喘  \n5. 肺炎  \n6. 癫痫  \n7. 多发性硬化症  \n8. 肝硬化  \n9. 肾结石  \n10. 甲状腺功能亢进  \n11. 系统性红斑狼疮  \n12. 克罗恩病  \n13. 帕金森病  \n14. 先天性心脏病  \n15. 丙型肝炎", "role": "assistant"}}], "created": 1745477067, "id": "20250424144422579433612d6c49e4", "model": "glm-4-air-250414", "request_id": "20250424144422579433612d6c49e4", "usage": {"completion_tokens": 111, "prompt_tokens": 64, "total_tokens": 175}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成15个多样化的、可以归类为生育信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的生育信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1421}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 孕期记录  \n2. 产前检查  \n3. 婴儿出生证明  \n4. 避孕措施详情  \n5. 人工授精记录  \n6. 流产手术报告  \n7. 子宫肌瘤诊断  \n8. 卵巢功能检查  \n9. 男性生育能力评估  \n10. 辅助生殖技术方案  \n11. 产后恢复计划  \n12. 胎儿发育监测  \n13. 生育保险申请  \n14. 子宫切除手术记录  \n15. 精子库捐赠信息", "role": "assistant"}}], "created": 1745477067, "id": "2025042414442413da05c8de0b43be", "model": "glm-4-air-250414", "request_id": "2025042414442413da05c8de0b43be", "usage": {"completion_tokens": 121, "prompt_tokens": 49, "total_tokens": 170}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成10个多样化的、可以归类为性别的命名实体，每行一个。这些实体将用于命名实体识别任务中的性别类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1456}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Mr. Smith  \n2. Dr. Li  \n3. Ms. Garcia  \n4. Prof. Chen  \n5. Queen Elizabeth  \n6. King James  \n7. Sir Isaac  \n8. Madame Curie  \n9. Emperor Hirohito  \n10. Lady Gaga", "role": "assistant"}}], "created": 1745477068, "id": "202504241444265500d367e47f48fb", "model": "glm-4-air-250414", "request_id": "202504241444265500d367e47f48fb", "usage": {"completion_tokens": 58, "prompt_tokens": 47, "total_tokens": 105}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成15个多样化的、可以归类为国籍的命名实体，每行一个。这些实体将用于命名实体识别任务中的国籍类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1463}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. American  \n2. Chinese  \n3. British  \n4. Japanese  \n5. German  \n6. French  \n7. Brazilian  \n8. Indian  \n9. Russian  \n10. Canadian  \n11. Australian  \n12. Italian  \n13. Spanish  \n14. Mexican  \n15. South Korean", "role": "assistant"}}], "created": 1745477068, "id": "202504241444279d4d325e708e4773", "model": "glm-4-air-250414", "request_id": "202504241444279d4d325e708e4773", "usage": {"completion_tokens": 62, "prompt_tokens": 47, "total_tokens": 109}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成15个多样化的、可以归类为姓名的命名实体，每行一个。这些实体将用于命名实体识别任务中的姓名类型。\n请生成多样化的中文姓名，包括不同性别、年龄段和地区特色的姓名。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1442}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n张伟  \n李娜  \n王芳  \n刘洋  \n陈明  \n赵秀英  \n杨帆  \n周杰  \n吴秀兰  \n郑晓东  \n孙丽华  \n马洪波  \n朱晓梅  \n胡志强  \n高慧敏", "role": "assistant"}}], "created": 1745477068, "id": "202504241444265598a818db404b97", "model": "glm-4-air-250414", "request_id": "202504241444265598a818db404b97", "usage": {"completion_tokens": 54, "prompt_tokens": 63, "total_tokens": 117}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成20个多样化的、可以归类为职业的命名实体，每行一个。这些实体将用于命名实体识别任务中的职业类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1470}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Software Engineer  \n2. Data Scientist  \n3. Medical Doctor  \n4. Financial Analyst  \n5. Project Manager  \n6. Graphic Designer  \n7. Elementary Teacher  \n8. Marketing Manager  \n9. Electrician  \n10. Chef  \n11. Lawyer  \n12. Accountant  \n13. Veterinarian  \n14. Architect  \n15. Human Resources Specialist  \n16. Pharmacist  \n17. Journalist  \n18. Civil Engineer  \n19. Retail Manager  \n20. Web Developer", "role": "assistant"}}], "created": **********, "id": "20250424144427da41771740324b2e", "model": "glm-4-air-250414", "request_id": "20250424144427da41771740324b2e", "usage": {"completion_tokens": 100, "prompt_tokens": 47, "total_tokens": 147}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成15个多样化的、可以归类为种族的命名实体，每行一个。这些实体将用于命名实体识别任务中的种族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1477}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 非洲裔美国人  \n2. 拉美裔  \n3. 亚裔  \n4. 美洲原住民  \n5. 白人  \n6. 混血  \n7. 阿拉伯裔  \n8. 南亚裔  \n9. 中东裔  \n10. 加勒比裔  \n11. 波利尼西亚裔  \n12. 因纽特人  \n13. 欧洲裔  \n14. 亚洲裔  \n15. 非洲裔", "role": "assistant"}}], "created": **********, "id": "20250424144428c3e8790a73914276", "model": "glm-4-air-250414", "request_id": "20250424144428c3e8790a73914276", "usage": {"completion_tokens": 109, "prompt_tokens": 47, "total_tokens": 156}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成15个多样化的、可以归类为民族的命名实体，每行一个。这些实体将用于命名实体识别任务中的民族类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1484}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n以下是为命名实体识别任务设计的15个多样化的民族命名实体，每行一个：\n\n1. 汉族  \n2. 藏族  \n3. 蒙古族  \n4. 回族  \n5. 维吾尔族  \n6. 苗族  \n7. 彝族  \n8. 壮族  \n9. 朝鲜族  \n10. 满族  \n11. 土家族  \n12. 哈尼族  \n13. 傣族  \n14. 羌族  \n15. 俄罗斯族", "role": "assistant"}}], "created": **********, "id": "20250424144428e757bfd01e024b4b", "model": "glm-4-air-250414", "request_id": "20250424144428e757bfd01e024b4b", "usage": {"completion_tokens": 115, "prompt_tokens": 46, "total_tokens": 161}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成20个多样化的、可以归类为地理位置的命名实体，每行一个。这些实体将用于命名实体识别任务中的地理位置类型。\n请生成多样化的地理位置，包括省市区县、街道地址、建筑物名称等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1428}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 北京市朝阳区建国门外大街1号  \n2. 上海市浦东新区陆家嘴环路1000号  \n3. 广州市天河区天河路385号  \n4. 深圳市南山区科技园南区  \n5. 成都市武侯区人民南路四段11号  \n6. 杭州市西湖区文三路478号  \n7. 南京市鼓楼区中山北路123号  \n8. 武汉市江汉区解放大道688号  \n9. 重庆市渝中区解放碑步行街  \n10. 西安市雁塔区高新路25号  \n11. 苏州市姑苏区平江路128号  \n12. 青岛市市南区香港中路10号  \n13. 天津市和平区南京路189号  \n14. 长沙市芙蓉区五一大道389号  \n15. 大连市中山区中山广场2号  \n16. 宁波市鄞州区钱湖南路8号  \n17. 福州市鼓楼区杨桥路118号  \n18. 哈尔滨市南岗区红军街1号  \n19. 郑州市金水区农业路16号  \n20. 昆明市五华区翠湖南路14号", "role": "assistant"}}], "created": 1745477071, "id": "20250424144425c1c9e7084c9e4b7f", "model": "glm-4-air-250414", "request_id": "20250424144425c1c9e7084c9e4b7f", "usage": {"completion_tokens": 259, "prompt_tokens": 65, "total_tokens": 324}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成10个多样化的、可以归类为婚姻状况的命名实体，每行一个。这些实体将用于命名实体识别任务中的婚姻状况类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1498}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 已婚人士  \n2. 单身贵族  \n3. 离异状态  \n4. 丧偶人士  \n5. 未婚青年  \n6. 恋爱中  \n7. 离婚状态  \n8. 已订婚  \n9. 永久单身  \n10. 复婚状态", "role": "assistant"}}], "created": 1745477071, "id": "2025042414442956c713d89f054108", "model": "glm-4-air-250414", "request_id": "2025042414442956c713d89f054108", "usage": {"completion_tokens": 71, "prompt_tokens": 49, "total_tokens": 120}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成20个多样化的、可以归类为年龄的命名实体，每行一个。这些实体将用于命名实体识别任务中的年龄类型。\n请生成多样化的年龄表达，包括具体数字和年龄段描述。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1449}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 23岁  \n2. 早期20年代  \n3. 七十高龄  \n4. 青少年时期  \n5. 30岁出头  \n6. 中年阶段  \n7. 50多岁  \n8. 壮年时期  \n9. 少年时代  \n10. 80岁左右  \n11. 花甲之年  \n12. 青春期  \n13. 90岁以上  \n14. 年轻成人  \n15. 晚年期  \n16. 40岁上下  \n17. 青年阶段  \n18. 耄耋之年  \n19. 25-30岁之间  \n20. 老年阶段", "role": "assistant"}}], "created": 1745477071, "id": "202504241444261b8bdb73660a4677", "model": "glm-4-air-250414", "request_id": "202504241444261b8bdb73660a4677", "usage": {"completion_tokens": 145, "prompt_tokens": 60, "total_tokens": 205}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成10个多样化的、可以归类为政治倾向的命名实体，每行一个。这些实体将用于命名实体识别任务中的政治倾向类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1505}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 民主党支持者  \n2. 共和党选民  \n3. 绿色和平组织  \n4. 极右翼联盟  \n5. 自由主义者协会  \n6. 社会主义先锋队  \n7. 中间派论坛  \n8. 工党成员  \n9. 极左翼运动  \n10. 保守派联盟", "role": "assistant"}}], "created": 1745477071, "id": "202504241444295bc45be9f5d64b42", "model": "glm-4-air-250414", "request_id": "202504241444295bc45be9f5d64b42", "usage": {"completion_tokens": 78, "prompt_tokens": 49, "total_tokens": 127}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名日常生活与消费领域的数据隐私专家。请生成25个多样化的、可以归类为行程信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的行程信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1435}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 飞机票号\n2. 火车票日期\n3. 出发机场\n4. 到达城市\n5. 酒店预订号\n6. 汽车租赁公司\n7. 船票座位号\n8. 旅行套餐名称\n9. 签证申请日期\n10. 旅行保险单号\n11. 地铁通行证ID\n12. 自驾路线规划\n13. 旅游景点门票\n14. 国际航班转机时间\n15. 预订酒店名称\n16. 出租车订单号\n17. 公交卡余额\n18. 轮渡班次号\n19. 旅行行程单\n20. 停车场发票\n21. 徒步路线地图\n22. 航班延误通知\n23. 预订座位等级\n24. 旅行目的地邮编\n25. 交通卡充值记录", "role": "assistant"}}], "created": 1745477072, "id": "202504241444252b3f8663f83f4769", "model": "glm-4-air-250414", "request_id": "202504241444252b3f8663f83f4769", "usage": {"completion_tokens": 200, "prompt_tokens": 49, "total_tokens": 249}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成15个多样化的、可以归类为家庭成员的命名实体，每行一个。这些实体将用于命名实体识别任务中的家庭成员类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1512}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 父亲  \n2. 母亲  \n3. 儿子  \n4. 女儿  \n5. 爷爷  \n6. 奶奶  \n7. 姥爷  \n8. 姥姥  \n9. 哥哥  \n10. 弟弟  \n11. 姐姐  \n12. 妹妹  \n13. 堂兄  \n14. 表妹  \n15. 孙子", "role": "assistant"}}], "created": 1745477072, "id": "20250424144430229b10b5d8484455", "model": "glm-4-air-250414", "request_id": "20250424144430229b10b5d8484455", "usage": {"completion_tokens": 101, "prompt_tokens": 47, "total_tokens": 148}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成20个多样化的、可以归类为教育背景的命名实体，每行一个。这些实体将用于命名实体识别任务中的教育背景类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1491}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 本科教育  \n2. 硕士学位  \n3. 博士后研究  \n4. 高中毕业证书  \n5. 学士学位  \n6. 研究生学历  \n7. 职业技术学院  \n8. 荣誉学位  \n9. 学士后文凭  \n10. 专科学历  \n11. 研究生证书  \n12. 职业资格证书  \n13. 博士学位  \n14. 技工学校教育  \n15. 中等教育文凭  \n16. 专业技术培训  \n17. 本科肄业  \n18. 研究生课程  \n19. 高等职业教育  \n20. 学历认证证书", "role": "assistant"}}], "created": 1745477072, "id": "20250424144429787e89defba2472e", "model": "glm-4-air-250414", "request_id": "20250424144429787e89defba2472e", "usage": {"completion_tokens": 148, "prompt_tokens": 49, "total_tokens": 197}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成15个多样化的、可以归类为疾病的命名实体，每行一个。这些实体将用于命名实体识别任务中的疾病类型。\n请生成多样化的疾病名称，包括常见病、慢性病、罕见病等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1561}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n高血压  \n糖尿病  \n哮喘  \n关节炎  \n抑郁症  \n心脏病  \n肺炎  \n脑卒中  \n骨质疏松  \n系统性红斑狼疮  \n阿尔茨海默病  \n克氏综合征  \n多发性硬化  \n血友病  \n肌萎缩侧索硬化症", "role": "assistant"}}], "created": 1745477074, "id": "202504241444331f27cc384aba4009", "model": "glm-4-air-250414", "request_id": "202504241444331f27cc384aba4009", "usage": {"completion_tokens": 50, "prompt_tokens": 63, "total_tokens": 113}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成20个多样化的、可以归类为信用记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的信用记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1540}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 信用评分\n2. 信用卡账单\n3. 贷款历史\n4. 按揭记录\n5. 公共记录\n6. 逾期信息\n7. 负面标记\n8. 债务清算\n9. 信用查询\n10. 信用报告\n11. 账户状态\n12. 负债总额\n13. 信用额度\n14. 还款记录\n15. 破产记录\n16. 信用利用率\n17. 信用账户\n18. 征信报告\n19. 信用档案\n20. 信用分值", "role": "assistant"}}], "created": 1745477074, "id": "202504241444318b9b5b63f1e6437d", "model": "glm-4-air-250414", "request_id": "202504241444318b9b5b63f1e6437d", "usage": {"completion_tokens": 138, "prompt_tokens": 49, "total_tokens": 187}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成20个多样化的、可以归类为投资产品的命名实体，每行一个。这些实体将用于命名实体识别任务中的投资产品类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1526}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 优先股  \n2. 共同基金  \n3. 交易所交易基金  \n4. 股票期权  \n5. 退休金计划  \n6. 债券  \n7. 信托基金  \n8. 保险投资产品  \n9. 创业投资  \n10. 不动产投资信托  \n11. 期货合约  \n12. 股票指数基金  \n13. 股票市场指数  \n14. 股票衍生品  \n15. 股票交易账户  \n16. 股票投资组合  \n17. 股票分析工具  \n18. 股票市场数据  \n19. 股票市场预测  \n20. 股票市场趋势", "role": "assistant"}}], "created": 1745477075, "id": "20250424144431843d0286558c4855", "model": "glm-4-air-250414", "request_id": "20250424144431843d0286558c4855", "usage": {"completion_tokens": 157, "prompt_tokens": 48, "total_tokens": 205}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成20个多样化的、可以归类为税务记录的命名实体，每行一个。这些实体将用于命名实体识别任务中的税务记录类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1533}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 年度所得税申报表  \n2. 个人所得税完税证明  \n3. 企业增值税发票  \n4. 税务登记证副本  \n5. 工资薪金所得税单  \n6. 土地增值税清算表  \n7. 个人独资企业所得税报表  \n8. 房产税缴纳凭证  \n9. 契税缴纳通知书  \n10. 企业所得税汇算清缴报告  \n11. 个人股权转让所得税记录  \n12. 小规模纳税人增值税申报表  \n13. 城市维护建设税缴纳单  \n14. 资源税计算表  \n15. 税收减免审批文件  \n16. 个人经营所得纳税证明  \n17. 车船税缴纳凭证  \n18. 外商投资企业所得税审计报告  \n19. 印花税缴纳记录  \n20. 税收滞纳金计算表", "role": "assistant"}}], "created": 1745477075, "id": "2025042414443101ca95d8068b4218", "model": "glm-4-air-250414", "request_id": "2025042414443101ca95d8068b4218", "usage": {"completion_tokens": 182, "prompt_tokens": 49, "total_tokens": 231}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成20个多样化的、可以归类为实体资产的命名实体，每行一个。这些实体将用于命名实体识别任务中的实体资产类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1547}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 高级服务器阵列  \n2. 光纤通信干线  \n3. 数据中心基础设施  \n4. 移动网络基站  \n5. 企业级加密通信设备  \n6. 卫星通信终端  \n7. 云存储服务系统  \n8. 物联网传感器网络  \n9. 无线接入点集群  \n10. 光传输设备  \n11. 安全防火墙系统  \n12. 网络路由器设备  \n13. 链路冗余备份系统  \n14. 量子通信加密模块  \n15. 分布式计算集群  \n16. 5G核心网设备  \n17. 光缆传输网络  \n18. 智能合约执行节点  \n19. 网络负载均衡器  \n20. 数字信号处理器", "role": "assistant"}}], "created": 1745477075, "id": "20250424144432f7ba63305ebc4e5c", "model": "glm-4-air-250414", "request_id": "20250424144432f7ba63305ebc4e5c", "usage": {"completion_tokens": 159, "prompt_tokens": 48, "total_tokens": 207}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成15个多样化的、可以归类为临床表现的命名实体，每行一个。这些实体将用于命名实体识别任务中的临床表现类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1575}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 发热  \n2. 头痛  \n3. 咳嗽  \n4. 胸痛  \n5. 呼吸困难  \n6. 恶心  \n7. 腹泻  \n8. 皮疹  \n9. 肌肉酸痛  \n10. 失眠  \n11. 疲劳  \n12. 眩晕  \n13. 喉咙痛  \n14. 关节肿胀  \n15. 意识模糊", "role": "assistant"}}], "created": 1745477076, "id": "2025042414443478171dfcb01343ba", "model": "glm-4-air-250414", "request_id": "2025042414443478171dfcb01343ba", "usage": {"completion_tokens": 102, "prompt_tokens": 47, "total_tokens": 149}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成25个多样化的、可以归类为工资数额的命名实体，每行一个。这些实体将用于命名实体识别任务中的工资数额类型。\n请生成多样化的工资金额表达，包括月薪、年薪、时薪等不同计量方式。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1519}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 月薪8千\n2. 年薪50万\n3. 时薪25元\n4. 月收入1.2万\n5. 年薪120K\n6. 时薪$15\n7. 月薪9千5百\n8. 年收入60万\n9. 时薪30美元\n10. 月薪1万2\n11. 年薪80万\n12. 时薪人民币20\n13. 月收入9千\n14. 年薪100K\n15. 时薪€20\n16. 月薪1万5千\n17. 年收入70万\n18. 时薪$20\n19. 月薪2万\n20. 年薪90万\n21. 时薪25美元\n22. 月收入1万8\n23. 年薪110K\n24. 时薪人民币30\n25. 月薪1万", "role": "assistant"}}], "created": 1745477076, "id": "2025042414443009cc40d813b74594", "model": "glm-4-air-250414", "request_id": "2025042414443009cc40d813b74594", "usage": {"completion_tokens": 184, "prompt_tokens": 69, "total_tokens": 253}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成25个多样化的、可以归类为交易信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的交易信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1554}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 交易编号\n2. 账单金额\n3. 支付日期\n4. 订单号码\n5. 购买时间\n6. 发票号码\n7. 客户ID\n8. 银行卡号\n9. 交易时间戳\n10. 付款方式\n11. 订单总价\n12. 结账信息\n13. 交易流水号\n14. 收款账号\n15. 支付确认码\n16. 发票日期\n17. 交易状态\n18. 订单详情\n19. 账单周期\n20. 退款记录\n21. 支付网关ID\n22. 订单跟踪号\n23. 账户余额\n24. 交易分类\n25. 结算周期", "role": "assistant"}}], "created": 1745477076, "id": "2025042414443245fa7316f6d546a5", "model": "glm-4-air-250414", "request_id": "2025042414443245fa7316f6d546a5", "usage": {"completion_tokens": 172, "prompt_tokens": 48, "total_tokens": 220}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成15个多样化的、可以归类为药物的命名实体，每行一个。这些实体将用于命名实体识别任务中的药物类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1568}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n以下是为命名实体识别任务生成的15个多样化的药物命名实体，每行一个：\n\n1. 布洛芬  \n2. 头孢克肟  \n3. 阿莫西林  \n4. 阿司匹林  \n5. 辛伐他汀  \n6. 硝苯地平  \n7. 甲硝唑  \n8. 氟康唑  \n9. 青霉素V钾  \n10. 美托洛尔  \n11. 多潘立酮  \n12. 氯雷他定  \n13. 环丙沙星  \n14. 氨氯地平  \n15. 氟哌酸", "role": "assistant"}}], "created": 1745477077, "id": "2025042414443395063cd2122c4790", "model": "glm-4-air-250414", "request_id": "2025042414443395063cd2122c4790", "usage": {"completion_tokens": 146, "prompt_tokens": 46, "total_tokens": 192}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成15个多样化的、可以归类为生育信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的生育信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1596}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 胎儿心率监测数据  \n2. 孕周时间戳记录  \n3. 产前检查预约信息  \n4. 新生儿血型鉴定结果  \n5. 妊娠糖尿病筛查记录  \n6. 胎位检查影像报告  \n7. 孕妇BMI历史数据  \n8. 催产素使用剂量记录  \n9. 胎儿发育超声测量值  \n10. 产后恢复随访时间表  \n11. 人工授精周期日志  \n12. 辅助生殖技术方案  \n13. 婴儿疫苗接种记录  \n14. 产妇分娩方式选择  \n15. 婴儿睡眠质量监测", "role": "assistant"}}], "created": 1745477077, "id": "202504241444358c780cd2961346b9", "model": "glm-4-air-250414", "request_id": "202504241444358c780cd2961346b9", "usage": {"completion_tokens": 143, "prompt_tokens": 48, "total_tokens": 191}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成15个多样化的、可以归类为医疗程序的命名实体，每行一个。这些实体将用于命名实体识别任务中的医疗程序类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1582}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 腹腔镜胆囊切除术  \n2. 核磁共振成像扫描  \n3. 腰椎穿刺术  \n4. 冠状动脉搭桥手术  \n5. 白内障超声乳化术  \n6. 支气管镜检查  \n7. 骨髓移植  \n8. 肾脏移植手术  \n9. 颅内压监测  \n10. 关节镜手术  \n11. 放射治疗  \n12. 胃镜检查  \n13. 人工髋关节置换  \n14. 超声波引导下穿刺活检  \n15. 脑电图检查", "role": "assistant"}}], "created": 1745477078, "id": "2025042414443470260eea0ab04cbb", "model": "glm-4-air-250414", "request_id": "2025042414443470260eea0ab04cbb", "usage": {"completion_tokens": 131, "prompt_tokens": 48, "total_tokens": 179}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成15个多样化的、可以归类为过敏信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的过敏信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1589}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n以下是为命名实体识别任务设计的15个多样化的过敏信息命名实体，每行一个：\n\n1. 牛奶过敏\n2. 花生过敏症\n3. 青霉素过敏\n4. 海鲜不耐受\n5. 橡胶蛋白过敏\n6. 鸡蛋过敏反应\n7. 芒果接触性皮炎\n8. 麸质敏感症\n9. 蜜蜂毒液过敏\n10. 水杨酸酯类过敏\n11. 鱼类过敏史\n12. 大豆蛋白不耐受\n13. 螨虫过敏原\n14. 鸡蛋白过敏\n15. 坚果类过敏群组", "role": "assistant"}}], "created": 1745477078, "id": "20250424144434cdf20e7ef1a74ac5", "model": "glm-4-air-250414", "request_id": "20250424144434cdf20e7ef1a74ac5", "usage": {"completion_tokens": 153, "prompt_tokens": 48, "total_tokens": 201}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成20个多样化的、可以归类为地理位置的命名实体，每行一个。这些实体将用于命名实体识别任务中的地理位置类型。\n请生成多样化的地理位置，包括省市区县、街道地址、建筑物名称等。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1603}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 北京市\n2. 上海市\n3. 广州市天河区\n4. 深圳市南山区\n5. 杭州市西湖区\n6. 成都市武侯区\n7. 重庆市渝中区\n8. 南京市鼓楼区\n9. 武汉市江汉区\n10. 西安市雁塔区\n11. 朝阳区建国路88号\n12. 长安街1号\n13. 黄埔区科学城\n14. 沙河口区和平广场\n15. 静安区南京西路\n16. 前海深港合作区\n17. 王府井大街\n18. 东方明珠广播电视塔\n19. 中关村科技园区\n20. 黄鹤楼景区", "role": "assistant"}}], "created": 1745477080, "id": "202504241444357822ab77ed1d44ea", "model": "glm-4-air-250414", "request_id": "202504241444357822ab77ed1d44ea", "usage": {"completion_tokens": 157, "prompt_tokens": 64, "total_tokens": 221}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名通信内容领域的数据隐私专家。请生成25个多样化的、可以归类为行程信息的命名实体，每行一个。这些实体将用于命名实体识别任务中的行程信息类型。"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 1610}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 航班号  \n2. 机票预订号  \n3. 登机口号码  \n4. 机场代码  \n5. 机位号码  \n6. 航班日期  \n7. 出发时间  \n8. 到达时间  \n9. 行李提取转盘号  \n10. 座位号  \n11. 酒店预订号  \n12. 酒店房间号  \n13. 入住日期  \n14. 离店日期  \n15. 汽车租赁订单号  \n16. 租车人身份证号  \n17. 还车地点  \n18. 租车日期  \n19. 返还日期  \n20. 火车票订单号  \n21. 车厢号  \n22. 站台号  \n23. 火车票验证码  \n24. 旅行保险单号  \n25. 旅行顾问联系方式", "role": "assistant"}}], "created": 1745477082, "id": "202504241444366da8cec85345497b", "model": "glm-4-air-250414", "request_id": "202504241444366da8cec85345497b", "usage": {"completion_tokens": 194, "prompt_tokens": 48, "total_tokens": 242}}]
