[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成10个真实的、多样化的、可以归类为性别的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的性别类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 56}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\nAlex Johnson  \nM<PERSON> Rodriguez  \nJamal Washington  \nS<PERSON><PERSON> Garcia  \n<PERSON><PERSON>  \n<PERSON><PERSON><PERSON>  \n<PERSON><PERSON>  \n<PERSON><PERSON>  \n<PERSON><PERSON><PERSON>  \n<PERSON><PERSON>", "role": "assistant"}}], "created": 1745481565, "id": "20250424155923dcd906613a9545d1", "model": "glm-4-air-250414", "request_id": "20250424155923dcd906613a9545d1", "usage": {"completion_tokens": 43, "prompt_tokens": 121, "total_tokens": 164}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成30个真实的、多样化的、可以归类为国籍的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的国籍类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 63}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n美国  \n中国  \n英国  \n法国  \n德国  \n日本  \n韩国  \n加拿大  \n澳大利亚  \n意大利  \n西班牙  \n巴西  \n俄罗斯  \n印度  \n墨西哥  \n荷兰  \n瑞典  \n挪威  \n丹麦  \n瑞士  \n芬兰  \n希腊  \n葡萄牙  \n波兰  \n捷克  \n匈牙利  \n南非  \n新西兰  \n阿根廷  \n泰国", "role": "assistant"}}], "created": 1745481565, "id": "20250424155923a8dc69b1e01d4d64", "model": "glm-4-air-250414", "request_id": "20250424155923a8dc69b1e01d4d64", "usage": {"completion_tokens": 61, "prompt_tokens": 120, "total_tokens": 181}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成20个真实的、多样化的、可以归类为种族的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的种族类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 77}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. AfricanAmerican\n2. HispanicLatino\n3. AsianAmerican\n4. NativeAmerican\n5. Caucasian\n6. MiddleEastern\n7. SouthAsian\n8. EastAsian\n9. SoutheastAsian\n10. PacificIslander\n11. BlackCanadian\n12. WhiteBritish\n13. IndianSubcontinent\n14. KoreanDescent\n15. ChineseHeritage\n16. FilipinoOrigin\n17. JapaneseAncestry\n18. VietnameseBackground\n19. IranianLineage\n20. MultiracialIdentity", "role": "assistant"}}], "created": 1745481565, "id": "2025042415592313cc14411cdb4cc9", "model": "glm-4-air-250414", "request_id": "2025042415592313cc14411cdb4cc9", "usage": {"completion_tokens": 110, "prompt_tokens": 120, "total_tokens": 230}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成30个真实的、多样化的、可以归类为政治倾向的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的政治倾向类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 105}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. TeaPartyExpress  \n2. BlueDogCoalition  \n3. FreedomWorks  \n4. ProgressiveDemocrats  \n5. ConservativeActionProject  \n6. SocialistWorkerParty  \n7. LibertarianInstitute  \n8. GreenPartyUSA  \n9. RepublicanNationalCommittee  \n10. DemocraticSocialistsAmerica  \n11. ConstitutionalParty  \n12. AmericanIndependentParty  \n13. CentristUnion  \n14. WorkersWorldParty  \n15. AllianceForUnity  \n16. PatriotsActionNetwork  \n17. ReformPartyUSA  \n18. SolidarityCoalition  \n19. ConstitutionParty  \n20. NewDemocracyMovement  \n21. ChristianConservativeCoalition  \n22. ProgressiveAlliance  \n23. LibertyUnionParty  \n24. AmericanSolidarityParty  \n25. ProgressiveRepublicans  \n26. ProgressiveCaucus  \n27. ConservativeVoice  \n28. SocialistAlternative  \n29. DemocraticProgressive Caucus  \n30. RightToLifeLeague", "role": "assistant"}}], "created": 1745481566, "id": "20250424155923a0c4f1b48b554489", "model": "glm-4-air-250414", "request_id": "20250424155923a0c4f1b48b554489", "usage": {"completion_tokens": 185, "prompt_tokens": 122, "total_tokens": 307}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成30个真实的、多样化的、可以归类为婚姻状况的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的婚姻状况类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 98}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 25岁单身\n2. 30岁已婚\n3. 35岁离异\n4. 40岁丧偶\n5. 22岁未婚\n6. 28岁已婚无子女\n7. 45岁再婚\n8. 18岁未满法定婚龄\n9. 50岁离异未再婚\n10. 33岁同居\n11. 29岁订婚\n12. 38岁分居\n13. 42岁丧偶再婚\n14. 26岁离婚\n15. 31岁单身母亲\n16. 37岁单身父亲\n17. 44岁已婚有子女\n18. 27岁事实婚姻\n19. 21岁恋爱中\n20. 36岁单身未育\n21. 48岁未婚同居\n22. 39岁婚姻存续中\n23. 20岁未到法定婚龄\n24. 41岁婚姻登记中\n25. 32岁分居待判\n26. 23岁订婚后结婚\n27. 49岁丧偶未再婚\n28. 24岁同居未登记\n29. 34岁单身养子\n30. 47岁已婚无配偶", "role": "assistant"}}], "created": 1745481567, "id": "20250424155923f5eaa45e99c64c38", "model": "glm-4-air-250414", "request_id": "20250424155923f5eaa45e99c64c38", "usage": {"completion_tokens": 262, "prompt_tokens": 122, "total_tokens": 384}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成50个真实的、多样化的、可以归类为年龄的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的年龄类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 49}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 25岁  \n2. 42岁  \n3. 19岁  \n4. 61岁  \n5. 33岁  \n6. 50岁  \n7. 16岁  \n8. 78岁  \n9. 29岁  \n10. 55岁  \n11. 37岁  \n12. 22岁  \n13. 64岁  \n14. 11岁  \n15. 47岁  \n16. 39岁  \n17. 70岁  \n18. 8岁  \n19. 51岁  \n20. 27岁  \n21. 74岁  \n22. 15岁  \n23. 44岁  \n24. 31岁  \n25. 60岁  \n26. 20岁  \n27. 63岁  \n28. 9岁  \n29. 49岁  \n30. 41岁  \n31. 5岁  \n32. 58岁  \n33. 35岁  \n34. 68岁  \n35. 14岁  \n36. 53岁  \n37. 26岁  \n38. 73岁  \n39. 3岁  \n40. 48岁  \n41. 36岁  \n42. 67岁  \n43. 17岁  \n44. 59岁  \n45. 4岁  \n46. 62岁  \n47. 21岁  \n48. 77岁  \n49. 10岁  \n50. 45岁", "role": "assistant"}}], "created": 1745481568, "id": "20250424155923552c8dbca4e4441a", "model": "glm-4-air-250414", "request_id": "20250424155923552c8dbca4e4441a", "usage": {"completion_tokens": 301, "prompt_tokens": 120, "total_tokens": 421}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成56个真实的、多样化的、可以归类为民族的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的民族类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 84}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n汉族  \n藏族  \n维吾尔族  \n蒙古族  \n回族  \n彝族  \n壮族  \n满族  \n苗族  \n土家族  \n侗族  \n瑶族  \n朝鲜族  \n白族  \n哈尼族  \n哈萨克族  \n傣族  \n黎族  \n傈僳族  \n佤族  \n畲族  \n高山族  \n拉祜族  \n水族  \n东乡族  \n纳西族  \n景颇族  \n柯尔克孜族  \n土族  \n达斡尔族  \n仫佬族  \n羌族  \n布朗族  \n撒拉族  \n毛南族  \n仡佬族  \n锡伯族  \n阿昌族  \n普米族  \n塔吉克族  \n怒族  \n乌孜别克族  \n俄罗斯族  \n鄂温克族  \n德昂族  \n保安族  \n裕固族  \n京族  \n塔塔尔族  \n独龙族  \n鄂伦春族  \n赫哲族  \n门巴族  \n珞巴族  \n基诺族  \n哈萨克斯坦族  \n乌兹别克斯坦族  \n塔吉克斯坦族  \n吉尔吉斯斯坦族  \n土库曼斯坦族  \n哈萨克族  \n乌兹别克族  \n塔吉克族  \n吉尔吉斯族  \n土库曼族  \n哈萨克斯坦族  \n乌兹别克斯坦族  \n塔吉克斯坦族  \n吉尔吉斯斯坦族  \n土库曼斯坦族  \n哈萨克族  \n乌兹别克族  \n塔吉克族  \n吉尔吉斯族  \n土库曼族", "role": "assistant"}}], "created": 1745481568, "id": "20250424155923078bfd8b055b460f", "model": "glm-4-air-250414", "request_id": "20250424155923078bfd8b055b460f", "usage": {"completion_tokens": 306, "prompt_tokens": 120, "total_tokens": 426}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成60个真实的、多样化的、可以归类为地理位置的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的地理位置类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 203}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 北京  \n2. 上海  \n3. 广州  \n4. 深圳  \n5. 成都  \n6. 杭州  \n7. 南京  \n8. 武汉  \n9. 西安  \n10. 重庆  \n11. 天津  \n12. 苏州  \n13. 长沙  \n14. 青岛  \n15. 大连  \n16. 厦门  \n17. 沈阳  \n18. 济南  \n19. 哈尔滨  \n20. 郑州  \n21. 昆明  \n22. 乌鲁木齐  \n23. 拉萨  \n24. 海口  \n25. 三亚  \n26. 福州  \n27. 宁波  \n28. 合肥  \n29. 太原  \n30. 石家庄  \n31. 兰州  \n32. 银川  \n33. 西宁  \n34. 贵阳  \n35. 南昌  \n36. 长春  \n37. 呼和浩特  \n38. 香港  \n39. 澳门  \n40. 台湾  \n41. 纽约  \n42. 伦敦  \n43. 东京  \n44. 巴黎  \n45. 悉尼  \n46. 多伦多  \n47. 柏林  \n48. 罗马  \n49. 新加坡  \n50. 首尔  \n51. 马德里  \n52. 莫斯科  \n53. 开罗  \n54. 曼谷  \n55. 雅加达  \n56. 里约热内卢  \n57. 墨西哥城  \n58. 开普敦  \n59. 曼彻斯特  \n60. 芝加哥", "role": "assistant"}}], "created": 1745481568, "id": "2025042415592320c39a5118b44620", "model": "glm-4-air-250414", "request_id": "2025042415592320c39a5118b44620", "usage": {"completion_tokens": 330, "prompt_tokens": 120, "total_tokens": 450}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成40个真实的、多样化的、可以归类为家庭成员的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的家庭成员类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 112}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 父亲张伟  \n2. 母亲李娜  \n3. 儿子刘洋  \n4. 女儿王芳  \n5. 爷爷赵建国  \n6. 奶奶钱秀英  \n7. 哥哥陈明  \n8. 弟弟吴强  \n9. 姐姐孙丽  \n10. 妹妹周婷  \n11. 舅舅郑军  \n12. 舅妈冯敏  \n13. 叔叔徐刚  \n14. 阿姨马秀兰  \n15. 侄子杨帆  \n16. 侄女林小红  \n17. 侄子朱磊  \n18. 侄女何静  \n19. 外甥孙浩  \n20. 外甥女胡晓  \n21. 堂兄马超  \n22. 堂妹赵雪  \n23. 表哥钱磊  \n24. 表妹孙悦  \n25. 岳父高建国  \n26. 岳母陈秀华  \n27. 公公王强  \n28. 婆婆李红  \n29. 小叔子周杰  \n30. 小姑子吴佳  \n31. 大伯子刘勇  \n32. 大姑子张敏  \n33. 女婿李明  \n34. 女儿媳王丽  \n35. 儿子媳赵芳  \n36. 女婿郑凯  \n37. 亲家公孙建  \n38. 亲家母周梅  \n39. 继父杨建国  \n40. 继母林秀琴", "role": "assistant"}}], "created": 1745481568, "id": "2025042415592360a7f7477e02419a", "model": "glm-4-air-250414", "request_id": "2025042415592360a7f7477e02419a", "usage": {"completion_tokens": 348, "prompt_tokens": 120, "total_tokens": 468}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成40个真实的、多样化的、可以归类为临床表现的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的临床表现类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 175}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 心悸  \n2. 胸痛  \n3. 头痛  \n4. 呼吸困难  \n5. 腹痛  \n6. 恶心  \n7. 呕吐  \n8. 发热  \n9. 皮疹  \n10. 关节痛  \n11. 肌肉痉挛  \n12. 眩晕  \n13. 失眠  \n14. 疲劳  \n15. 咳嗽  \n16. 咽喉痛  \n17. 鼻塞  \n18. 腹泻  \n19. 便秘  \n20. 尿频  \n21. 水肿  \n22. 瘫痪  \n23. 意识模糊  \n24. 抽搐  \n25. 耳鸣  \n26. 视力模糊  \n27. 手抖  \n28. 皮疹瘙痒  \n29. 食欲不振  \n30. 腰痛  \n31. 月经不调  \n32. 尿急  \n33. 痛风发作  \n34. 脱水症状  \n35. 糖尿病酮症酸中毒  \n36. 脑卒中后遗症  \n37. 骨折疼痛  \n38. 肺炎症状  \n39. 肝功能异常  \n40. 消化不良", "role": "assistant"}}], "created": 1745481569, "id": "2025042415592335eb9e6df1454534", "model": "glm-4-air-250414", "request_id": "2025042415592335eb9e6df1454534", "usage": {"completion_tokens": 281, "prompt_tokens": 120, "total_tokens": 401}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成60个真实的、多样化的、可以归类为职业的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的职业类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 70}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 医生\n2. 软件工程师\n3. 法官\n4. 教师\n5. 护士\n6. 会计师\n7. 律师\n8. 建筑师\n9. 记者编辑\n10. 数据分析师\n11. 厨师\n12. 营销经理\n13. 飞行员\n14. 心理咨询师\n15. 物理治疗师\n16. 人力资源专员\n17. 摄影师\n18. 公关顾问\n19. 电气工程师\n20. 翻译\n21. 消防员\n22. 销售代表\n23. 生物学家\n24. 金融分析师\n25. 网页设计师\n26. 项目经理\n27. 游戏开发者\n28. 珠宝设计师\n29. 人力资源总监\n30. 船长\n31. 职业运动员\n32. 环境科学家\n33. 大学教授\n34. 服装设计师\n35. 音乐制作人\n36. 电视主持人\n37. 保险代理人\n38. 机械工程师\n39. 社交媒体经理\n40. 侦探\n41. 舞蹈教师\n42. 图书管理员\n43. 客服代表\n44. 矫齿医生\n45. 供应链经理\n46. 美容师\n47. 银行家\n48. 天文学家\n49. 犯罪学家\n50. 神经外科医生\n51. 程序员\n52. 顾问\n53. 机器人工程师\n54. 时尚编辑\n55. 咖啡师\n56. 广告创意\n57. 风险分析师\n58. 游戏测试员\n59. 园艺师\n60. 翻译员", "role": "assistant"}}], "created": 1745481569, "id": "20250424155923315715a077594bb5", "model": "glm-4-air-250414", "request_id": "20250424155923315715a077594bb5", "usage": {"completion_tokens": 400, "prompt_tokens": 120, "total_tokens": 520}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成50个真实的、多样化的、可以归类为药物的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的药物类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 168}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 布洛芬  \n2. 阿司匹林  \n3. 对乙酰氨基酚  \n4. 氢氯噻嗪  \n5. 卡托普利  \n6. 依那普利  \n7. 氨氯地平  \n8. 厄贝沙坦  \n9. 阿托伐他汀  \n10. 瑞舒伐他汀  \n11. 非那雄胺  \n12. 甲基多巴  \n13. 甲硝唑  \n14. 克拉霉素  \n15. 阿莫西林  \n16. 头孢呋辛  \n17. 氧氟沙星  \n18. 左氧氟沙星  \n19. 磺胺甲恶唑  \n20. 甲氧苄啶  \n21. 异烟肼  \n22. 利福平  \n23. 吡嗪酰胺  \n24. 阿昔洛韦  \n25. 伐昔洛韦  \n26. 奥司他韦  \n27. 利巴韦林  \n28. 齐多夫定  \n29. 拉米夫定  \n30. 依非韦伦  \n31. 阿德福韦酯  \n32. 替诺福韦  \n33. 乙胺嘧啶  \n34. 奎宁  \n35. 青蒿素  \n36. 磷霉素  \n37. 万古霉素  \n38. 替考拉宁  \n39. 达托霉素  \n40. 环丙沙星  \n41. 氟康唑  \n42. 伊曲康唑  \n43. 酮康唑  \n44. 两性霉素B  \n45. 阿苯达唑  \n46. 哌嗪  \n47. 乙胺嗪  \n48. 吡喹酮  \n49. 非奈利酮  \n50. 依维莫司", "role": "assistant"}}], "created": 1745481570, "id": "20250424155923f3c04a0e75444aac", "model": "glm-4-air-250414", "request_id": "20250424155923f3c04a0e75444aac", "usage": {"completion_tokens": 409, "prompt_tokens": 120, "total_tokens": 529}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成80个真实的、多样化的、可以归类为工资数额的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的工资数额类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 119}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n5000元  \n8000元  \n12000元  \n15000元  \n20000元  \n25000元  \n30000元  \n35000元  \n40000元  \n45000元  \n50000元  \n55000元  \n60000元  \n65000元  \n70000元  \n75000元  \n80000元  \n85000元  \n90000元  \n95000元  \n100000元  \n105000元  \n110000元  \n115000元  \n120000元  \n125000元  \n130000元  \n135000元  \n140000元  \n145000元  \n150000元  \n155000元  \n160000元  \n165000元  \n170000元  \n175000元  \n180000元  \n185000元  \n190000元  \n195000元  \n200000元  \n210000元  \n220000元  \n230000元  \n240000元  \n250000元  \n260000元  \n270000元  \n280000元  \n290000元  \n300000元  \n320000元  \n340000元  \n360000元  \n380000元  \n400000元  \n420000元  \n440000元  \n460000元  \n480000元  \n500000元  \n550000元  \n600000元  \n650000元  \n700000元  \n750000元  \n800000元  \n850000元  \n900000元  \n950000元  \n1000000元", "role": "assistant"}}], "created": 1745481570, "id": "20250424155923b497fbad7d914d68", "model": "glm-4-air-250414", "request_id": "20250424155923b497fbad7d914d68", "usage": {"completion_tokens": 290, "prompt_tokens": 122, "total_tokens": 412}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成50个真实的、多样化的、可以归类为生育信息的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的生育信息类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 196}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 孕期检查记录  \n2. 分娩手术日期  \n3. 孕周数36周  \n4. 产前筛查结果  \n5. 人工授精手术  \n6. 胎儿超声波报告  \n7. 剖腹产手术编号  \n8. 胎心监测数据  \n9. 孕期血糖水平  \n10. 产后恢复计划  \n11. 辅助生殖治疗  \n12. 孕期体重增长表  \n13. 避孕措施记录  \n14. 胎位矫正手术  \n15. 产后抑郁筛查  \n16. 宫颈扩张度记录  \n17. 多胎妊娠诊断  \n18. 卵泡监测数据  \n19. 输卵管疏通手术  \n20. 孕期高血压记录  \n21. 自然流产时间  \n22. 宫外孕手术记录  \n23. 试管婴儿周期编号  \n24. 孕期钙片用量  \n25. 产后出血量记录  \n26. 羊水穿刺报告  \n27. 孕酮水平检测  \n28. 早产儿出生证明  \n29. 妊娠糖尿病诊断  \n30. 子宫肌瘤手术  \n31. 孕期叶酸用量  \n32. 双胞胎出生体重  \n33. 胎膜早破时间  \n34. 产后母乳喂养记录  \n35. 宫颈环扎手术  \n36. 孕期B超检查  \n37. 胎动计数表  \n38. 输卵管结扎手术  \n39. 孕期铁剂用量  \n40. 前置胎盘诊断  \n41. 产后盆底康复计划  \n42. 胎死腹中时间  \n43. 孕期维生素D水平  \n44. 人工流产手术编号  \n45. 孕期血压监测数据  \n46. 胎盘早剥记录  \n47. 孕期肝功能检测  \n48. 产后避孕措施选择  \n49. 胎龄评估报告  \n50. 孕期甲状腺功能检测", "role": "assistant"}}], "created": 1745481570, "id": "2025042415592317af1059264c4df1", "model": "glm-4-air-250414", "request_id": "2025042415592317af1059264c4df1", "usage": {"completion_tokens": 427, "prompt_tokens": 122, "total_tokens": 549}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成50个真实的、多样化的、可以归类为医疗程序的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的医疗程序类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 182}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 胆囊切除术  \n2. 骨髓移植  \n3. 心脏搭桥手术  \n4. 白内障摘除术  \n5. 腰椎间盘突出微创手术  \n6. 肾脏移植  \n7. 胰腺切除术  \n8. 肺叶切除术  \n9. 前列腺摘除术  \n10. 脑部伽马刀治疗  \n11. 关节置换手术  \n12. 胃癌根治术  \n13. 宫颈癌放疗  \n14. 肝脏穿刺活检  \n15. 鼻窦炎内镜手术  \n16. 人工耳蜗植入  \n17. 腹腔镜阑尾切除术  \n18. 乳腺癌保乳手术  \n19. 颅内动脉瘤夹闭术  \n20. 肠道息肉切除术  \n21. 羊膜腔穿刺术  \n22. 前列腺癌根治术  \n23. 膝关节镜检查  \n24. 胸腔镜肺大疱切除术  \n25. 脊柱侧弯矫正术  \n26. 肾上腺切除术  \n27. 食管扩张术  \n28. 甲状腺次全切除术  \n29. 脑积水分流术  \n30. 胃肠减压术  \n31. 肝癌射频消融术  \n32. 膀胱镜检查  \n33. 腹股沟疝修补术  \n34. 喉癌激光治疗  \n35. 骨折内固定术  \n36. 鼻息肉切除术  \n37. 胸腔闭式引流术  \n38. 肝门部胆管癌手术  \n39. 眼底激光治疗  \n40. 腹腔镜脾切除术  \n41. 胸腔镜交感神经切断术  \n42. 子宫肌瘤剔除术  \n43. 脑室腹腔分流术  \n44. 肺功能检测  \n45. 阑尾炎切除术  \n46. 肾穿刺造瘘术  \n47. 腰椎穿刺术  \n48. 膀胱肿瘤电切术  \n49. 胃肠道支架植入术  \n50. 肝移植手术", "role": "assistant"}}], "created": 1745481570, "id": "20250424155923bd351b4830a14c9b", "model": "glm-4-air-250414", "request_id": "20250424155923bd351b4830a14c9b", "usage": {"completion_tokens": 473, "prompt_tokens": 122, "total_tokens": 595}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成40个真实的、多样化的、可以归类为疾病的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的疾病类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 161}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 心肌梗死  \n2. 糖尿病  \n3. 高血压  \n4. 肺炎  \n5. 脑卒中  \n6. 风湿性关节炎  \n7. 癫痫  \n8. 结肠癌  \n9. 乳腺癌  \n10. 肝炎  \n11. 肾衰竭  \n12. 青光眼  \n13. 艾滋病  \n14. 帕金森病  \n15. 骨折  \n16. 感冒  \n17. 胃溃疡  \n18. 贫血  \n19. 支气管炎  \n20. 胆囊炎  \n21. 白血病  \n22. 脂肪肝  \n23. 肺结核  \n24. 银屑病  \n25. 耳鸣  \n26. 脊髓损伤  \n27. 脑积水  \n28. 尿路感染  \n29. 营养不良  \n30. 过敏性鼻炎  \n31. 甲状腺功能亢进  \n32. 多囊卵巢综合征  \n33. 膝关节炎  \n34. 蛛网膜下腔出血  \n35. 主动脉夹层  \n36. 慢性阻塞性肺疾病  \n37. 血友病  \n38. 系统性红斑狼疮  \n39. 先天性心脏病  \n40. 胰腺炎", "role": "assistant"}}], "created": 1745481572, "id": "20250424155923754d309f8dbb436f", "model": "glm-4-air-250414", "request_id": "20250424155923754d309f8dbb436f", "usage": {"completion_tokens": 296, "prompt_tokens": 120, "total_tokens": 416}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成70个真实的、多样化的、可以归类为投资产品的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的投资产品类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 126}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 阿里巴巴股票  \n2. 腾讯控股  \n3. 中国平安保险  \n4. 招商银行定期存款  \n5. 货币市场基金A  \n6. 易方达蓝筹精选  \n7. 华夏回报混合型基金  \n8. 南方全球精选QDII  \n9. 博时主题行业股票  \n10. 嘉实沪深300指数  \n11. 工银瑞信核心债券  \n12. 中信证券股票质押  \n13. 恒生指数期货  \n14. 大宗商品ETF  \n15. 上海黄金交易所T+D  \n16. 银行承兑汇票  \n17. 融资融券账户  \n18. 可转换债券  \n19. 银华日利货币市场  \n20. 广发聚丰混合  \n21. 国泰金鹰增长混合  \n22. 兴全全球视野股票  \n23. 华安创新基金  \n24. 银河稳健混合  \n25. 景顺长城内需增长  \n26. 泰达宏利领先成长  \n27. 诺安成长混合  \n28. 长信金利趋势混合  \n29. 中欧价值发现混合  \n30. 天弘余额宝货币  \n31. 华夏现金增利货币  \n32. 广发中证500指数  \n33. 南方中证500ETF  \n34. 华夏上证50ETF  \n35. 易方达上证50ETF  \n36. 招商中证白酒指数  \n37. 国泰国证食品饮料指数  \n38. 华宝中证消费龙头  \n39. 富国中证医药卫生  \n40. 景顺长城新能源产业  \n41. 易方达创业板ETF  \n42. 华宝油气ETF  \n43. 博时黄金ETF  \n44. 华安黄金ETF  \n45. 易方达MSCI中国A  \n46. 南方全球小盘指数  \n47. 华夏纳斯达克100  \n48. 招商标普石油天然气  \n49. 广发德国30ETF  \n50. 中信标普中国全指  \n51. 银华-道琼斯88精选  \n52. 国泰纳斯达克100  \n53. 华夏全球精选股票  \n54. 博时标普500ETF  \n55. 易方达标普500ETF  \n56. 华夏恒生ETF  \n57. 招商标普中国指数  \n58. 广发道琼斯中国  \n59. 华安法国CAC40  \n60. 景顺长城新兴市场  \n61. 易方达新兴市场股票  \n62. 南方富时中国A50  \n63. 华夏MSCI中国A股  \n64. 博时标普中国指数  \n65. 中信标普中国大盘  \n66. 银华MSCI中国A股  \n67. 国泰纳斯达克ETF  \n68. 华夏德国30ETF  \n69. 易方达道琼斯中国  \n70. 南方富时中国A50ETF", "role": "assistant"}}], "created": 1745481572, "id": "2025042415592333f5e0a0a902454e", "model": "glm-4-air-250414", "request_id": "2025042415592333f5e0a0a902454e", "usage": {"completion_tokens": 635, "prompt_tokens": 122, "total_tokens": 757}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成80个真实的、多样化的、可以归类为姓名的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的姓名类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 42}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 张伟\n2. 王芳\n3. 李娜\n4. 刘洋\n5. 陈明\n6. 杨丽\n7. 赵强\n8. 周红\n9. 吴磊\n10. 郑宇\n11. 孙悦\n12. 马超\n13. 朱琳\n14. 胡杰\n15. 林峰\n16. 高翔\n17. 徐婷\n18. 何军\n19. 郭静\n20. 罗涛\n21. 宋宇\n22. 钱芳\n23. 唐亮\n24. 冯雪\n25. 谢波\n26. 陆婷\n27. 韩梅\n28. 方勇\n29. 江涛\n30. 白雪\n31. 欧阳明\n32. 夏雨\n33. 诸葛峰\n34. 上官雪\n35. 司马亮\n36. 南宫婷\n37. 东方宇\n38. 夏侯涛\n39. 令狐雪\n40. 司徒勇\n41. 独孤明\n42. 百里涛\n43. 尉迟雪\n44. 公孙宇\n45. 皇甫婷\n46. 宇文亮\n47. 淳于涛\n48. 梁山雪\n49. 长孙勇\n50. 尉迟明\n51. 赵一鸣\n52. 钱二宝\n53. 孙三多\n54. 李四光\n55. 周五洲\n56. 吴六六\n57. 郑七七\n58. 王八八\n59. 冯九九\n60. 陈十分\n61. 赵子龙\n62. 钱仁杰\n63. 孙悟空\n64. 李世民\n65. 周邦彦\n66. 吴承恩\n67. 郑和\n68. 王阳明\n69. 冯小刚\n70. 陈道明\n71. 朱元璋\n72. 秦始皇\n73. 唐玄奘\n74. 宋江\n75. 元好问\n76. 明太祖\n77. 清高宗\n78. 隋炀帝\n79. 汉武帝\n80. 曹操", "role": "assistant"}}], "created": 1745481572, "id": "20250424155923e14068fd8f2348a3", "model": "glm-4-air-250414", "request_id": "20250424155923e14068fd8f2348a3", "usage": {"completion_tokens": 510, "prompt_tokens": 120, "total_tokens": 630}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成60个真实的、多样化的、可以归类为信用记录的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的信用记录类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 140}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 信用评分845  \n2. 还款历史记录  \n3. 公共记录查询  \n4. 负债总额45678  \n5. 信用卡逾期标记  \n6. 贷款申请日期2022-03-15  \n7. 账户状态活跃  \n8. 破产记录2018  \n9. 信用查询次数12  \n10. 贷款余额78900  \n11. 信用卡额度50000  \n12. 逾期天数30  \n13. 欠款金额2345  \n14. 贷款还款记录2021-11  \n15. 信用报告生成时间2023-01-10  \n16. 公共记录清除日期2020-07  \n17. 信用卡未结清金额34567  \n18. 贷款逾期历史2019-02  \n19. 账户关闭日期2017-05  \n20. 信用评分趋势下降  \n21. 负债比率0.45  \n22. 信用查询来源银行A  \n23. 贷款批准日期2022-09-20  \n24. 公共记录类型税务留置  \n25. 信用卡使用率0.3  \n26. 贷款拒绝原因信用不足  \n27. 账户激活日期2016-08  \n28. 信用评分变化+15  \n29. 负债总额更新2023-02  \n30. 贷款还款延迟记录2022-04  \n31. 公共记录状态已解决  \n32. 信用卡账户余额56789  \n33. 贷款利率4.5%  \n34. 信用查询类型硬查询  \n35. 账户类型抵押贷款  \n36. 破产类型第7章  \n37. 信用评分范围良好  \n38. 负债明细医疗账单  \n39. 贷款类型汽车贷款  \n40. 公共记录关联法院判决  \n41. 信用卡还款历史2020-12  \n42. 贷款总额100000  \n43. 信用查询时间戳2023-03-05  \n44. 负债类型学生贷款  \n45. 贷款状态已结清  \n46. 公共记录编号PR78901  \n47. 信用卡发卡行银行B  \n48. 贷款还款方式月付  \n49. 信用评分变化-8  \n50. 负债总额分类消费贷  \n51. 贷款申请编号LN45678  \n52. 公共记录日期2021-06  \n53. 信用卡最低还款额2000  \n54. 贷款拖欠状态当前  \n55. 信用查询原因贷款审批  \n56. 负债总额分类信用卡  \n57. 贷款期限36个月  \n58. 公共记录更新时间2022-11  \n59. 信用卡账户IDCC98765  \n60. 贷款还款记录2022-10", "role": "assistant"}}], "created": 1745481573, "id": "20250424155923377b47cb23cf48cb", "model": "glm-4-air-250414", "request_id": "20250424155923377b47cb23cf48cb", "usage": {"completion_tokens": 636, "prompt_tokens": 122, "total_tokens": 758}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成70个真实的、多样化的、可以归类为行程信息的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的行程信息类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 210}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Flight AA1234  \n2. Hotel California Suite  \n3. Car Rental XYZ123  \n4. Train Ticket E789  \n5. Cruise Ship Majesty  \n6. Airport JFK Terminal 4  \n7. Bus Pass Metro ID567  \n8. Vacation Package SunIsland  \n9. Travel Visa UK2023  \n10. Itinerary Ref ABC123  \n11. Boarding Pass XYZ789  \n12. Hotel Reservation ID456  \n13. Rental Car Agreement 789  \n14. Train Schedule Ref 234  \n15. Flight Itinerary AA987  \n16. Cruise Booking Conf 123  \n17. Airport Check-in Counter 5  \n18. Bus Route 42 Pass  \n19. Tour Guide License 123  \n20. Hotel Key Card GH456  \n21. Car Rental Drop-off Slip 78  \n22. Flight Gate 7 Pass  \n23. Cruise Cabin 802  \n24. Airport Lounge Access 34  \n25. Train Platform 3 Ticket  \n26. Hotel Breakfast Voucher 56  \n27. Rental Car Damage Report 89  \n28. Flight Baggage Claim 12  \n29. Cruise Dining Reservation 90  \n30. Airport Security Token 45  \n31. Bus Stop Pass 67  \n32. Vacation Home Key 1234  \n33. Travel Insurance Policy 5678  \n34. Hotel Spa Booking 9012  \n35. Rental Car Insurance 3456  \n36. Flight Delay Compensation 7890  \n37. Cruise Photo Pass 2345  \n38. Airport Parking Ticket 6789  \n39. Bus Transfer Slip 0123  \n40. Tour Package Code 4567  \n41. Hotel Laundry Voucher 8901  \n42. Car Rental Extension Form 234  \n43. Flight Change Fee Receipt 567  \n44. Cruise Shore Excursion 890  \n45. Airport Tax ID 12345  \n46. Train Seat Assignment 67890  \n47. Hotel Parking Permit 23456  \n48. Rental Car Mileage Log 78901  \n49. Flight Boarding Time 34567  \n50. Cruise Souvenir Card 89012  \n51. Airport Departure Gate 45678  \n52. Bus Day Pass 901234  \n53. Vacation Itinerary PDF 567890  \n54. Travel Document Checklist 234567  \n55. Hotel Room Key 789012  \n56. Car Rental Agreement 345678  \n57. Flight Meal Request 901234  \n58. Cruise Fitness Pass 567890  \n59. Airport Arrival Hall 234567  \n60. Train Conductor Stamp 789012  \n61. Hotel Concierge Log 345678  \n62. Rental Car GPS Device 901234  \n63. Flight Layover Duration 567890  \n64. Cruise Entertainment Pass 234567  \n65. Airport Baggage Tag 789012  \n66. Bus Route Map ID 345678  \n67. Vacation Rental Code 901234  \n68. Travel Passport Sticker 567890  \n69. Hotel Breakfast Time 234567  \n70. Car Rental Fuel Receipt 789012", "role": "assistant"}}], "created": 1745481573, "id": "202504241559232e69ba14e57946c2", "model": "glm-4-air-250414", "request_id": "202504241559232e69ba14e57946c2", "usage": {"completion_tokens": 677, "prompt_tokens": 122, "total_tokens": 799}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成60个真实的、多样化的、可以归类为教育背景的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的教育背景类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 91}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 高等教育学历证书  \n2. 本科毕业证  \n3. 硕士学位证书  \n4. 博士研究生学历  \n5. 中专毕业证书  \n6. 职业技术学院文凭  \n7. 高等教育自学考试文凭  \n8. 网络教育毕业证  \n9. 函授本科学位  \n10. 成人高等教育学历  \n11. 专科毕业证书  \n12. 研究生毕业证  \n13. 工商管理硕士学位  \n14. 计算机科学学士学位  \n15. 法律硕士专业学位  \n16. 教育学博士学位  \n17. 医学研究生学历  \n18. 工程技术文凭  \n19. 艺术设计本科学历  \n20. 会计学硕士证书  \n21. 外语专业毕业证  \n22. 国际贸易学士学位  \n23. 生物技术研究生学历  \n24. 环境科学博士学位  \n25. 公共管理硕士  \n26. 农业技术推广文凭  \n27. 旅游管理学士学位  \n28. 建筑学研究生学历  \n29. 财务管理专业证书  \n30. 新闻传播学硕士  \n31. 物理学博士学位  \n32. 经济学本科毕业证  \n33. 社会工作研究生学历  \n34. 信息工程学士学位  \n35. 食品科学与工程文凭  \n36. 人力资源管理硕士  \n37. 交通运输研究生学历  \n38. 材料科学博士学位  \n39. 化学工程学士学位  \n40. 人力资源管理专业证书  \n41. 统计学研究生学历  \n42. 物流管理学士学位  \n43. 软件工程硕士  \n44. 药学博士学位  \n45. 市场营销专业证书  \n46. 电子信息工程研究生学历  \n47. 金融学学士学位  \n48. 哲学硕士  \n49. 文学博士学位  \n50. 行政管理专业证书  \n51. 航空航天工程研究生学历  \n52. 精算学学士学位  \n53. 社会学硕士  \n54. 生态学博士学位  \n55. 广播电视编导专业证书  \n56. 机械工程研究生学历  \n57. 国际金融学士学位  \n58. 人类学硕士  \n59. 矿物学博士学位  \n60. 数字媒体艺术专业证书", "role": "assistant"}}], "created": 1745481573, "id": "202504241559231c672190d241496b", "model": "glm-4-air-250414", "request_id": "202504241559231c672190d241496b", "usage": {"completion_tokens": 481, "prompt_tokens": 122, "total_tokens": 603}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成60个真实的、多样化的、可以归类为税务记录的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的税务记录类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 133}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 1040表申报日期  \n2. 2022年应纳税所得额  \n3. 社会安全号SSN123456789  \n4. 工资单编号W-2-2023  \n5. 投资收益明细表1099-DIV  \n6. 自雇收入总额$45,000  \n7. 退税金额$1,200.50  \n8. 1099-G表失业补偿金  \n9. 股票交易收益$3,450  \n10. 1040EZ表提交日期  \n11. 零售业增值税发票VAT2023001  \n12. 个人所得税减免额$2,300  \n13. 1099-INT表利息收入  \n14. 房产税缴纳记录2023Q2  \n15. 自行车销售增值税$750  \n16. 企业所得税纳税申报表C型  \n17. 退休金账户贡献额$12,000  \n18. 小型企业亏损结转额$8,500  \n19. 1099-R表养老金分配  \n20. 土地增值税发票LVT2023-456  \n21. 增值税进项税额$5,600  \n22. 个人所得税预缴税额$1,800  \n23. 1099-SA表健康储蓄账户  \n24. 2023年季度纳税申报表Q1  \n25. 企业所得税减免凭证EIC2023  \n26. 1099-B表经纪交易报告  \n27. 土地增值税申报表LVT-DEC2023  \n28. 增值税销项税额$7,200  \n29. 个人所得税抵扣额$950  \n30. 1099-MISC表杂项收入  \n31. 税务审计报告编号TA-2023-789  \n32. 企业所得税滞纳金$450  \n33. 1099-H表高等教育学费  \n34. 2023年企业所得税预缴单  \n35. 增值税专用发票VAT2023-789  \n36. 个人所得税补缴通知2023-456  \n37. 1099-PATR表合作社分配  \n38. 房产税滞纳金$120  \n39. 企业所得税汇算清缴表2023  \n40. 1099-K表支付卡处理收入  \n41. 土地增值税评估报告LVT-VAL2023  \n42. 增值税发票认证号VAT-CERT-2023  \n43. 个人所得税年度结余$500  \n44. 1099-LTC表长期护理支出  \n45. 税务登记证号码TIN987654321  \n46. 企业所得税减免额$15,000  \n47. 1099-C表取消债务收入  \n48. 2023年房产税缴纳证明  \n49. 增值税留抵税额$3,200  \n50. 个人所得税抵免额$1,100  \n51. 1099-A表放弃资产报告  \n52. 企业所得税年度纳税申报表C-2023  \n53. 1099-S表房地产销售报告  \n54. 2023年增值税减免申请表  \n55. 个人所得税抵扣限额$2,000  \n56. 1099-OID表原定利息收入  \n57. 企业所得税纳税评估报告2023  \n58. 1099-GCT表全球投资收益  \n59. 增值税发票号码VAT-INV-2023-123  \n60. 个人所得税汇算清缴表2023", "role": "assistant"}}], "created": **********, "id": "202504241559233230d2a97ff248bd", "model": "glm-4-air-250414", "request_id": "202504241559233230d2a97ff248bd", "usage": {"completion_tokens": 750, "prompt_tokens": 122, "total_tokens": 872}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成60个真实的、多样化的、可以归类为实体资产的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的实体资产类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 147}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. Apple MacBook Pro  \n2. Tesla Model 3  \n3. iPhone 13 Pro  \n4. Samsung Galaxy S22  \n5. Dell XPS 15  \n6. Canon EOS R5  \n7. Honda Civic  \n8. Sony PlayStation 5  \n9. Microsoft Surface Laptop  \n10. Dyson V11 Vacuum  \n11. Fitbit Charge 5  \n12. Nest Thermostat  \n13. Bosch Dishwasher  \n14. Apple Watch Series 7  \n15. LG OLED TV  \n16. Subaru Outback  \n17. Nikon Z6 II  \n18. Google Pixel 6  \n19. Lenovo ThinkPad X1  \n20. Bosch Refrigerator  \n21. Cadillac Escalade  \n22. Xiaomi Redmi Note 11  \n23. HP Spectre x360  \n24. KitchenAid Mixer  \n25. Audi Q5  \n26. ASUS ROG Strix Laptop  \n27. BlackRock iShares ETF  \n28. Vanguard S&P 500 Index Fund  \n29. Berkshire Hathaway Class B Shares  \n30. JPMorgan Chase Savings Account  \n31. Fidelity Mutual Fund  \n32. Bank of America Checking Account  \n33. PayPal Business Account  \n34. T. Rowe Price Retirement Fund  \n35. Charles Schwab Brokerage Account  \n36. BlackRock Global Allocation Fund  \n37. Goldman Sachs Treasury Bond ETF  \n38. Morgan Stanley Wealth Account  \n39. State Street SPDR Gold Trust  \n40. UBS Swiss Bank Account  \n41. Wells Fargo Certificate of Deposit  \n42. Invesco QQQ Trust  \n43. Northern Trust Private Wealth Account  \n44. Credit Suisse Private Banking Portfolio  \n45. Royal Bank of Canada Investment Account  \n46. ING Direct Savings Certificate  \n47. Santander Business Loan  \n48. Commonwealth Bank Mortgage  \n49. Barclays Global Equity Fund  \n50. NatWest Business Deposit  \n51. HSBC Premier Checking  \n52. Commonwealth Bank Term Deposit  \n53. NAB Superannuation Fund  \n54. ANZ Personal Loan  \n55. Westpac Business Credit Card  \n56. Bank of Queensland Home Loan  \n57. Macquarie Infrastructure Trust  \n58. St.George Business Savings  \n59. CBAshares Trust Investment Account  \n60. UBank Fixed Term Investment", "role": "assistant"}}], "created": **********, "id": "20250424155923bcaf393b059d4125", "model": "glm-4-air-250414", "request_id": "20250424155923bcaf393b059d4125", "usage": {"completion_tokens": 454, "prompt_tokens": 122, "total_tokens": 576}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成50个真实的、多样化的、可以归类为过敏信息的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的过敏信息类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 189}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 花生过敏史  \n2. 青霉素过敏记录  \n3. 牛奶不耐受标记  \n4. 芒果过敏信息  \n5. 尼美舒利不良反应  \n6. 氨基比林过敏源  \n7. 鱼虾类过敏档案  \n8. 头孢类药物敏感症  \n9. 螨虫过敏检测结果  \n10. 花粉症严重程度  \n11. 碘造影剂不良反应  \n12. 鸡蛋过敏医疗记录  \n13. 青霉素G皮试阳性  \n14. 蜂蜜不耐受症状  \n15. 阿司匹林哮喘关联  \n16. 螨虫过敏史详细描述  \n17. 氯雷他定药物过敏  \n18. 海鲜类过敏检测单  \n19. 橡胶手套接触性皮炎  \n20. 蛋白质过敏临床报告  \n21. 青霉素类药物禁忌  \n22. 牛奶蛋白过敏确诊  \n23. 花粉过敏季节性记录  \n24. 磺胺类药物不良反应  \n25. 蜂毒过敏紧急处理  \n26. 鱼类过敏严重级别  \n27. 青霉素皮试阳性报告  \n28. 鸡蛋过敏儿童档案  \n29. 芒果接触性皮炎描述  \n30. 头孢菌素类药物过敏  \n31. 螨虫过敏治疗计划  \n32. 碘过敏影像检查限制  \n33. 花生过敏严重等级  \n34. 青霉素类药物过敏史  \n35. 牛奶蛋白不耐受记录  \n36. 芒果过敏皮肤测试  \n37. 鱼虾过敏紧急预案  \n38. 头孢曲松钠不良反应  \n39. 螨虫过敏药物清单  \n40. 碘过敏医疗警示标记  \n41. 花生过敏食物清单  \n42. 青霉素G药物禁忌症  \n43. 牛奶不耐受儿童记录  \n44. 芒果过敏临床症状  \n45. 鱼类过敏检测报告  \n46. 头孢类药物过敏档案  \n47. 螨虫过敏治疗方案  \n48. 碘造影剂过敏史  \n49. 花生过敏紧急处理  \n50. 青霉素类药物过敏反应", "role": "assistant"}}], "created": **********, "id": "20250424155923d5819ae00199497a", "model": "glm-4-air-250414", "request_id": "20250424155923d5819ae00199497a", "usage": {"completion_tokens": 494, "prompt_tokens": 122, "total_tokens": 616}}]
[{"model": "glm-4-air-250414", "messages": [{"role": "user", "content": "假设你是一名数据隐私专家。请生成70个真实的、多样化的、可以归类为交易信息的具体命名实体，每行一个。这些实体将用于命名实体识别任务中的交易信息类型。\n请注意以下要求：\n1. 请不要输出类别名称或泛指词语（例如“交易ID”、“少年期”）；应提供**具体的实例**（例如“TXN84573290”、“34岁”）。\n2. 所有实体必须具有清晰的边界，能够被直接用于模型标注。\n3. 避免使用模糊或抽象概念。\n"}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 154}, {"choices": [{"finish_reason": "stop", "index": 0, "message": {"content": "\n1. 信用卡号：4111111111111111  \n2. 交易金额：129.50美元  \n3. 商户ID：MERCHANT12345  \n4. 订单号：ORD78901234  \n5. 支付日期：2023-11-15  \n6. 账单周期：2023年10月1日至2023年10月31日  \n7. 银行路由号：122000247  \n8. 支票号码：1029384756  \n9. 交易时间戳：2023-11-15T14:30:22Z  \n10. 签名哈希：a1b2c3d4e5f6g7h8i9j0  \n11. 加密密钥：AES256-ABCDEF123456  \n12. 客户交易ID：CUSTTXN987654321  \n13. 发票编号：INV20231115001  \n14. 回扣金额：25.00美元  \n15. 折扣码：SAVE10NOW  \n16. 货币代码：USD  \n17. 转账参考号：TRF789012345678  \n18. 批准代码：APR567890123  \n19. 账户余额：1,250.75美元  \n20. 费用代码：FEE1001  \n21. 税额：7.65美元  \n22. 退款ID：REF20231115002  \n23. 分期付款次数：12  \n24. 最低还款额：50.00美元  \n25. 滞纳金：15.00美元  \n26. 利率：18.5%  \n27. 信用卡过期日期：12/25  \n28. 安全码：CSC123  \n29. 微支付标识符：MPID456789012  \n30. 二维码数据：QR456789012345678  \n31. 支付网关响应码：PGW200OK  \n32. 验证码：VER123456  \n33. 储值卡余额：500.00元  \n34. 代金券代码：VCH78901234  \n35. 赠品兑换码：GIFT56789012  \n36. 分账比例：70/30  \n37. 跨境交易标识：CBT20231115  \n38. 风险评分：85.2  \n39. 反欺诈令牌：ANTIFRAUD7890  \n40. 交易类型代码：TXN_TYPE_001  \n41. 自动转账频率：每月  \n42. 定期存款期限：36个月  \n43. 借记卡PIN码：DPIN1234  \n44. 电子钱包交易ID：EWALLET987654321  \n45. 积分兑换比率：1:0.01美元  \n46. 优惠券有效期：2023-12-31  \n47. 账单支付截止日期：2023-11-28  \n48. 账户关联手机号：+1-555-123-4567  \n49. 交易备注：礼品购买  \n50. 清算日期：2023-11-20  \n51. 费用减免金额：5.00美元  \n52. 信用卡授权码：AUTH78901234  \n53. 虚拟账户号：VAC456789012345  \n54. 支付方式代码：PAY_METHOD_002  \n55. 批准限额：5000.00美元  \n56. 交易分类码：CAT_COMMERCE  \n57. 税号：TAX_NUM123456789  \n58. 银行账号后四位：5678  \n59. 交易状态码：TXN_STATUS_003  \n60. 争议ID：DISPUTE123456789  \n61. 退款原因代码：REFUNDR_001  \n62. 储值卡序列号：SN9876543210  \n63. 签名设备ID：SIG_DEV_123456  \n64. 交易追踪号：TTN789012345678  \n65. 银行参考号：BANK_REF_456789  \n66. 账单地址邮政编码：90210  \n67. 货到付款金额：199.99美元  \n68. 信用卡类型代码：CARD_TYPE_VISA  \n69. 虚拟商户名称：VMN_ABC_STORE  \n70. 交易确认哈希：CONF_HASH_xyz7890", "role": "assistant"}}], "created": **********, "id": "20250424155923d87914315d574935", "model": "glm-4-air-250414", "request_id": "20250424155923d87914315d574935", "usage": {"completion_tokens": 1005, "prompt_tokens": 122, "total_tokens": 1127}}]
