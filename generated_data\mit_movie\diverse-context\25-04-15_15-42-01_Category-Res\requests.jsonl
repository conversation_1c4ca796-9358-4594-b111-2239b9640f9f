{"model": "gpt-4", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 10 diverse movie query categories for movie queries to the dialog system."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 42}
{"model": "gpt-4", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 10 diverse movie query categories for movie queries to the dialog system."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 49}
{"model": "gpt-4", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 10 diverse movie query categories for movie queries to the dialog system."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 56}
{"model": "gpt-4", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 6 different user demographics, i.e. different age groups and genders, for movie queries to the dialog system. Some examples are [older, age-friendly, young, family-friendly]."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 63}
{"model": "gpt-4", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 6 different user demographics, i.e. different age groups and genders, for movie queries to the dialog system. Some examples are [older, young, age-friendly, family-friendly]."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 70}
{"model": "gpt-4", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 6 different user demographics, i.e. different age groups and genders, for movie queries to the dialog system. Some examples are [older, age-friendly, family-friendly, young]."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 77}
{"model": "gpt-4", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 10 different user moods for movie queries to the dialog system. Some examples are [curious, frustrated]."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 84}
{"model": "gpt-4", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 10 different user moods for movie queries to the dialog system. Some examples are [curious, frustrated]."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 91}
{"model": "gpt-4", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 10 different user moods for movie queries to the dialog system. Some examples are [curious, frustrated]."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 98}
{"model": "gpt-4", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 8 different language variations for movie queries to the dialog system. Some examples are [slang, formal, technical, straightforward, indirect, colloquial, vague, informal, casual]."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 105}
{"model": "gpt-4", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 8 different language variations for movie queries to the dialog system. Some examples are [technical, indirect, slang, vague, straightforward, casual, informal, colloquial, formal]."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 112}
{"model": "gpt-4", "messages": [{"role": "user", "content": "Suppose you are a user of a dialog system or conversational agent. List 8 different language variations for movie queries to the dialog system. Some examples are [formal, informal, colloquial, indirect, slang, casual, technical, straightforward, vague]."}], "temperature": 1, "max_tokens": 1024, "logprobs": false, "seed": 119}
