{"prompts": ["Suppose you are a user of a dialog system or conversational agent. List 10 diverse movie query categories for movie queries to the dialog system.", "Suppose you are a user of a dialog system or conversational agent. List 10 diverse movie query categories for movie queries to the dialog system.", "Suppose you are a user of a dialog system or conversational agent. List 10 diverse movie query categories for movie queries to the dialog system.", "Suppose you are a user of a dialog system or conversational agent. List 6 different user demographics, i.e. different age groups and genders, for movie queries to the dialog system. Some examples are [older, age-friendly, young, family-friendly].", "Suppose you are a user of a dialog system or conversational agent. List 6 different user demographics, i.e. different age groups and genders, for movie queries to the dialog system. Some examples are [older, young, age-friendly, family-friendly].", "Suppose you are a user of a dialog system or conversational agent. List 6 different user demographics, i.e. different age groups and genders, for movie queries to the dialog system. Some examples are [older, age-friendly, family-friendly, young].", "Suppose you are a user of a dialog system or conversational agent. List 10 different user moods for movie queries to the dialog system. Some examples are [curious, frustrated].", "Suppose you are a user of a dialog system or conversational agent. List 10 different user moods for movie queries to the dialog system. Some examples are [curious, frustrated].", "Suppose you are a user of a dialog system or conversational agent. List 10 different user moods for movie queries to the dialog system. Some examples are [curious, frustrated].", "Suppose you are a user of a dialog system or conversational agent. List 8 different language variations for movie queries to the dialog system. Some examples are [slang, formal, technical, straightforward, indirect, colloquial, vague, informal, casual].", "Suppose you are a user of a dialog system or conversational agent. List 8 different language variations for movie queries to the dialog system. Some examples are [technical, indirect, slang, vague, straightforward, casual, informal, colloquial, formal].", "Suppose you are a user of a dialog system or conversational agent. List 8 different language variations for movie queries to the dialog system. Some examples are [formal, informal, colloquial, indirect, slang, casual, technical, straightforward, vague]."]}