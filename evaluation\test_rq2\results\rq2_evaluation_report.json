{"evaluation_type": "RQ2: 生成数据质量评估", "evaluation_time": "2025-07-05T16:27:02.299652", "config": {"rq2_quality_assessment": {"description": "生成数据质量评估配置", "metrics": {"naturalness": {"enabled": true, "model": "zhipu-api", "scoring_scale": [1, 10], "sample_size": 100, "threshold": 6.0}, "annotation_accuracy": {"enabled": true, "check_entity_boundaries": true, "check_entity_types": true, "manual_validation_sample": 50}, "semantic_consistency": {"enabled": true, "context_window": 5, "similarity_threshold": 0.7}, "diversity_metrics": {"vocabulary_diversity": 0.6, "syntactic_diversity": 0.5, "semantic_diversity": 0.4, "context_diversity": 0.5, "entity_diversity": 0.7}, "balance_metrics": {"distribution_tolerance": 0.15, "min_coverage_ratio": 0.8}}, "evaluation_methods": {"automatic_scoring": true, "manual_evaluation": true, "cross_validation": true}}}, "quality_results": {"annotation_accuracy": {"boundary_accuracy": 0.6818181818181818, "type_accuracy": 0.8636363636363636, "total_entities": 440, "valid_entities": 300, "invalid_boundaries": 140, "unknown_types": 60}, "semantic_consistency": {"avg_consistency": 0.76, "std_consistency": 0.10198039027185568, "min_consistency": 0.6000000000000001, "max_consistency": 0.9, "scores": [0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001, 0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001, 0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001, 0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001, 0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001, 0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001, 0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001, 0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001, 0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001, 0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001, 0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001, 0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001, 0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001, 0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001, 0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001, 0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001, 0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001, 0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001, 0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001, 0.8, 0.8, 0.7000000000000001, 0.9, 0.6000000000000001]}, "diversity_metrics": {"vocabulary_diversity": 0.03934426229508197, "sentence_diversity": 0.05, "context_diversity": 0.05, "syntactic_diversity": 0.02, "semantic_diversity": 0.04736842105263158, "entity_diversity": 0.05}, "balance_metrics": {"actual_distribution": {"人名": 100, "职业": 60, "地名": 80, "时间": 40, "组织名": 100, "金额": 20, "专业": 20, "学历": 20}, "distribution_entropy": 2.7333330526810653, "balance_score": 0.0, "coverage_ratio": 0.041666666666666664}, "entity_type_consistency": {"overall_consistency": 0.8636363636363636, "entity_type_consistency": {"人名": {"consistency_rate": 0.4, "total_examples": 100, "consistent_examples": 40, "inconsistent_examples": 60}, "职业": {"consistency_rate": 1.0, "total_examples": 60, "consistent_examples": 60, "inconsistent_examples": 0}, "地名": {"consistency_rate": 1.0, "total_examples": 80, "consistent_examples": 80, "inconsistent_examples": 0}, "时间": {"consistency_rate": 1.0, "total_examples": 40, "consistent_examples": 40, "inconsistent_examples": 0}, "组织名": {"consistency_rate": 1.0, "total_examples": 100, "consistent_examples": 100, "inconsistent_examples": 0}, "金额": {"consistency_rate": 1.0, "total_examples": 20, "consistent_examples": 20, "inconsistent_examples": 0}, "专业": {"consistency_rate": 1.0, "total_examples": 20, "consistent_examples": 20, "inconsistent_examples": 0}, "学历": {"consistency_rate": 1.0, "total_examples": 20, "consistent_examples": 20, "inconsistent_examples": 0}}, "inconsistencies": [{"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 0}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 1}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 3}, {"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 5}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 6}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 8}, {"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 10}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 11}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 13}, {"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 15}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 16}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 18}, {"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 20}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 21}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 23}, {"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 25}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 26}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 28}, {"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 30}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 31}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 33}, {"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 35}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 36}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 38}, {"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 40}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 41}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 43}, {"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 45}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 46}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 48}, {"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 50}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 51}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 53}, {"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 55}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 56}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 58}, {"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 60}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 61}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 63}, {"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 65}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 66}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 68}, {"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 70}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 71}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 73}, {"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 75}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 76}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 78}, {"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 80}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 81}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 83}, {"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 85}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 86}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 88}, {"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 90}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 91}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 93}, {"entity_text": "张三", "entity_type": "人名", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 95}, {"entity_text": "李四", "entity_type": "人名", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 96}, {"entity_text": "赵六", "entity_type": "人名", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 98}], "total_entities": 440, "total_inconsistencies": 60}, "context_diversity": {"overall_context_diversity": 0.05, "entity_type_diversity": {"人名": {"diversity_ratio": 0.05, "unique_contexts": 5, "total_contexts": 100, "most_common_contexts": [{"context": "[ENTITY]是一名优秀", "count": 20}, {"context": "[ENTITY]在上海的华", "count": 20}, {"context": "[ENTITY]毕业于清华", "count": 20}]}, "职业": {"diversity_ratio": 0.05, "unique_contexts": 3, "total_contexts": 60, "most_common_contexts": [{"context": "是一名优秀[ENTITY]师，他在北", "count": 20}, {"context": "公司担任产[ENTITY]年薪50万", "count": 20}, {"context": "司，担任算[ENTITY]", "count": 20}]}, "地名": {"diversity_ratio": 0.05, "unique_contexts": 4, "total_contexts": 80, "most_common_contexts": [{"context": "程师，他在[ENTITY]工作了五年", "count": 20}, {"context": "李四在[ENTITY]的华为公司", "count": 20}, {"context": "系，现在在[ENTITY]创业。", "count": 20}]}, "时间": {"diversity_ratio": 0.05, "unique_contexts": 2, "total_contexts": 40, "most_common_contexts": [{"context": "北京工作了[ENTITY]。", "count": 20}, {"context": "赵六于[ENTITY]月加入腾讯", "count": 20}]}, "组织名": {"diversity_ratio": 0.05, "unique_contexts": 5, "total_contexts": 100, "most_common_contexts": [{"context": "四在上海的[ENTITY]担任产品经", "count": 20}, {"context": "王五毕业于[ENTITY]计算机系，", "count": 20}, {"context": "于清华大学[ENTITY]，现在在深", "count": 20}]}, "金额": {"diversity_ratio": 0.05, "unique_contexts": 1, "total_contexts": 20, "most_common_contexts": [{"context": "理，年薪5[ENTITY]", "count": 20}]}, "专业": {"diversity_ratio": 0.05, "unique_contexts": 1, "total_contexts": 20, "most_common_contexts": [{"context": "大学攻读人[ENTITY]业博士学位", "count": 20}]}, "学历": {"diversity_ratio": 0.05, "unique_contexts": 1, "total_contexts": 20, "most_common_contexts": [{"context": "智能专业博[ENTITY]", "count": 20}]}}, "total_entity_types": 8}, "linguistic_quality": {"grammar_score": 1.0, "fluency_score": 1.0, "coherence_score": 0.9200000000000003, "readability_score": 1.0, "detailed_analysis": {"grammar_distribution": {"mean": 1.0, "std": 0.0, "min": 1.0, "max": 1.0, "percentiles": {"25th": 1.0, "50th": 1.0, "75th": 1.0, "95th": 1.0}}, "fluency_distribution": {"mean": 1.0, "std": 0.0, "min": 1.0, "max": 1.0, "percentiles": {"25th": 1.0, "50th": 1.0, "75th": 1.0, "95th": 1.0}}, "coherence_distribution": {"mean": 0.9200000000000003, "std": 0.03999999999999999, "min": 0.9, "max": 1.0, "percentiles": {"25th": 0.9, "50th": 0.9, "75th": 0.9, "95th": 1.0}}, "readability_distribution": {"mean": 1.0, "std": 0.0, "min": 1.0, "max": 1.0, "percentiles": {"25th": 1.0, "50th": 1.0, "75th": 1.0, "95th": 1.0}}}}, "entity_quality": {"entity_length_analysis": {"人名": {"avg_length": 2.0, "min_length": 2, "max_length": 2, "length_variance": 0.0}, "职业": {"avg_length": 4.666666666666667, "min_length": 4, "max_length": 5, "length_variance": 0.22222222222222224}, "地名": {"avg_length": 2.0, "min_length": 2, "max_length": 2, "length_variance": 0.0}, "时间": {"avg_length": 4.5, "min_length": 2, "max_length": 7, "length_variance": 6.25}, "组织名": {"avg_length": 4.8, "min_length": 4, "max_length": 8, "length_variance": 2.5600000000000005}, "金额": {"avg_length": 4.0, "min_length": 4, "max_length": 4, "length_variance": 0.0}, "专业": {"avg_length": 4.0, "min_length": 4, "max_length": 4, "length_variance": 0.0}, "学历": {"avg_length": 4.0, "min_length": 4, "max_length": 4, "length_variance": 0.0}}, "entity_position_analysis": {"人名": {"avg_position": 0.0, "position_variance": 0.0, "position_distribution": {"beginning": 1.0, "middle": 0.0, "end": 0.0}}, "职业": {"avg_position": 0.553639846743295, "position_variance": 0.04793997078727557, "position_distribution": {"beginning": 0.3333333333333333, "middle": 0.3333333333333333, "end": 0.3333333333333333}}, "地名": {"avg_position": 0.4237071805006587, "position_variance": 0.08901975393596993, "position_distribution": {"beginning": 0.5, "middle": 0.25, "end": 0.25}}, "时间": {"avg_position": 0.48922413793103453, "position_variance": 0.1488230157550535, "position_distribution": {"beginning": 0.5, "middle": 0.0, "end": 0.5}}, "组织名": {"avg_position": 0.31220526100586066, "position_variance": 0.0066850603964376375, "position_distribution": {"beginning": 0.6, "middle": 0.4, "end": 0.0}}, "金额": {"avg_position": 0.8333333333333334, "position_variance": 0.0, "position_distribution": {"beginning": 0.0, "middle": 0.0, "end": 1.0}}, "专业": {"avg_position": 0.5652173913043478, "position_variance": 0.0, "position_distribution": {"beginning": 0.0, "middle": 1.0, "end": 0.0}}, "学历": {"avg_position": 0.826086956521739, "position_variance": 1.232595164407831e-32, "position_distribution": {"beginning": 0.0, "middle": 0.0, "end": 1.0}}}, "entity_context_analysis": {"人名": {"total_contexts": 100, "unique_contexts": 5, "context_diversity": 0.05, "most_common_contexts": [["张三是一名优秀", 20], ["李四在上海的华", 20], ["王五毕业于清华", 20]]}, "职业": {"total_contexts": 60, "unique_contexts": 3, "context_diversity": 0.05, "most_common_contexts": [["是一名优秀的软件工程师，他在北", 20], ["公司担任产品经理，年薪50万", 20], ["司，担任算法工程师。", 20]]}, "地名": {"total_contexts": 80, "unique_contexts": 4, "context_diversity": 0.05, "most_common_contexts": [["程师，他在北京工作了五年", 20], ["李四在上海的华为公司", 20], ["系，现在在深圳创业。", 20]]}, "时间": {"total_contexts": 40, "unique_contexts": 2, "context_diversity": 0.05, "most_common_contexts": [["北京工作了五年。", 20], ["赵六于2023年3月加入腾讯", 20]]}, "组织名": {"total_contexts": 100, "unique_contexts": 5, "context_diversity": 0.05, "most_common_contexts": [["四在上海的华为公司担任产品经", 20], ["王五毕业于清华大学计算机系，", 20], ["于清华大学计算机系，现在在深", 20]]}, "金额": {"total_contexts": 20, "unique_contexts": 1, "context_diversity": 0.05, "most_common_contexts": [["理，年薪50万元。", 20]]}, "专业": {"total_contexts": 20, "unique_contexts": 1, "context_diversity": 0.05, "most_common_contexts": [["大学攻读人工智能专业博士学位", 20]]}, "学历": {"total_contexts": 20, "unique_contexts": 1, "context_diversity": 0.05, "most_common_contexts": [["智能专业博士学位。", 20]]}}, "entity_type_distribution": {"人名": 100, "职业": 60, "地名": 80, "时间": 40, "组织名": 100, "金额": 20, "专业": 20, "学历": 20}, "problematic_entities": [{"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 0, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 1, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 1, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 3, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 3, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 4, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 4, "issue": "文本不匹配"}, {"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 5, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 6, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 6, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 8, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 8, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 9, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 9, "issue": "文本不匹配"}, {"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 10, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 11, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 11, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 13, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 13, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 14, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 14, "issue": "文本不匹配"}, {"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 15, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 16, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 16, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 18, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 18, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 19, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 19, "issue": "文本不匹配"}, {"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 20, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 21, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 21, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 23, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 23, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 24, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 24, "issue": "文本不匹配"}, {"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 25, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 26, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 26, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 28, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 28, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 29, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 29, "issue": "文本不匹配"}, {"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 30, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 31, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 31, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 33, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 33, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 34, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 34, "issue": "文本不匹配"}, {"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 35, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 36, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 36, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 38, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 38, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 39, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 39, "issue": "文本不匹配"}, {"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 40, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 41, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 41, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 43, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 43, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 44, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 44, "issue": "文本不匹配"}, {"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 45, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 46, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 46, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 48, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 48, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 49, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 49, "issue": "文本不匹配"}, {"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 50, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 51, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 51, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 53, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 53, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 54, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 54, "issue": "文本不匹配"}, {"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 55, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 56, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 56, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 58, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 58, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 59, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 59, "issue": "文本不匹配"}, {"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 60, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 61, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 61, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 63, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 63, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 64, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 64, "issue": "文本不匹配"}, {"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 65, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 66, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 66, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 68, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 68, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 69, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 69, "issue": "文本不匹配"}, {"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 70, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 71, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 71, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 73, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 73, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 74, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 74, "issue": "文本不匹配"}, {"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 75, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 76, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 76, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 78, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 78, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 79, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 79, "issue": "文本不匹配"}, {"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 80, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 81, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 81, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 83, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 83, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 84, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 84, "issue": "文本不匹配"}, {"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 85, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 86, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 86, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 88, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 88, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 89, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 89, "issue": "文本不匹配"}, {"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 90, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 91, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 91, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 93, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 93, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 94, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 94, "issue": "文本不匹配"}, {"entity_text": "软件工程师", "entity_type": "职业", "context": "张三是一名优秀的软件工程师，他在北京工作了五年。", "item_index": 95, "issue": "文本不匹配"}, {"entity_text": "产品经理", "entity_type": "职业", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 96, "issue": "文本不匹配"}, {"entity_text": "50万元", "entity_type": "金额", "context": "李四在上海的华为公司担任产品经理，年薪50万元。", "item_index": 96, "issue": "边界错误"}, {"entity_text": "2023年3月", "entity_type": "时间", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 98, "issue": "文本不匹配"}, {"entity_text": "算法工程师", "entity_type": "职业", "context": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。", "item_index": 98, "issue": "文本不匹配"}, {"entity_text": "人工智能", "entity_type": "专业", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 99, "issue": "文本不匹配"}, {"entity_text": "博士学位", "entity_type": "学历", "context": "孙七在广州的中山大学攻读人工智能专业博士学位。", "item_index": 99, "issue": "文本不匹配"}]}, "entity_coverage": {"type_coverage": 0.041666666666666664, "quantity_coverage": 0.4731182795698925, "distribution_similarity": 0.014677433691431934, "missing_types": ["药物", "家庭成员", "生育信息", "工资数额", "政治倾向", "国籍", "地理位置", "税务记录", "教育背景", "过敏信息", "信用记录", "性别", "实体资产", "民族", "交易信息", "投资产品", "年龄", "临床表现", "姓名", "医疗程序", "婚姻状况", "疾病", "行程信息"], "excess_types": ["地名", "专业", "金额", "组织名", "人名", "学历", "时间"], "detailed_coverage": {"药物": {"target_count": 38, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "家庭成员": {"target_count": 50, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "生育信息": {"target_count": 50, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "工资数额": {"target_count": 50, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "政治倾向": {"target_count": 50, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "国籍": {"target_count": 28, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "地理位置": {"target_count": 5, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "税务记录": {"target_count": 50, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "教育背景": {"target_count": 24, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "职业": {"target_count": 9, "actual_count": 60, "coverage_ratio": 1.0, "excess_ratio": 5.666666666666667}, "过敏信息": {"target_count": 50, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "信用记录": {"target_count": 50, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "性别": {"target_count": 50, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "实体资产": {"target_count": 50, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "民族": {"target_count": 24, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "交易信息": {"target_count": 50, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "投资产品": {"target_count": 50, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "年龄": {"target_count": 50, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "临床表现": {"target_count": 29, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "姓名": {"target_count": 34, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "医疗程序": {"target_count": 23, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "婚姻状况": {"target_count": 50, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "疾病": {"target_count": 16, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}, "行程信息": {"target_count": 50, "actual_count": 0, "coverage_ratio": 0.0, "excess_ratio": 0.0}}}, "cross_validation": {"fold_results": [{"fold_index": 0, "data_size": 20, "naturalness_score": 10.0, "boundary_accuracy": 0.75, "type_accuracy": 0.8636363636363636, "vocabulary_diversity": 0.20425531914893616, "entity_diversity": 0.25}, {"fold_index": 1, "data_size": 20, "naturalness_score": 10.0, "boundary_accuracy": 0.6444444444444445, "type_accuracy": 0.8555555555555555, "vocabulary_diversity": 0.1927710843373494, "entity_diversity": 0.24444444444444444}, {"fold_index": 2, "data_size": 20, "naturalness_score": 10.0, "boundary_accuracy": 0.6395348837209303, "type_accuracy": 0.872093023255814, "vocabulary_diversity": 0.1935483870967742, "entity_diversity": 0.2558139534883721}, {"fold_index": 3, "data_size": 20, "naturalness_score": 10.0, "boundary_accuracy": 0.6931818181818182, "type_accuracy": 0.8636363636363636, "vocabulary_diversity": 0.19672131147540983, "entity_diversity": 0.25}, {"fold_index": 4, "data_size": 20, "naturalness_score": 10.0, "boundary_accuracy": 0.6818181818181818, "type_accuracy": 0.8636363636363636, "vocabulary_diversity": 0.19672131147540983, "entity_diversity": 0.25}], "average_metrics": {"naturalness_score": {"mean": 10.0, "std": 0.0, "min": 10.0, "max": 10.0}, "boundary_accuracy": {"mean": 0.6817958656330749, "std": 0.03990629929017476, "min": 0.6395348837209303, "max": 0.75}, "type_accuracy": {"mean": 0.8637115339440922, "std": 0.005230416776553766, "min": 0.8555555555555555, "max": 0.872093023255814}, "vocabulary_diversity": {"mean": 0.1968034827067759, "std": 0.0040595382649567914, "min": 0.1927710843373494, "max": 0.20425531914893616}, "entity_diversity": {"mean": 0.2500516795865633, "std": 0.0035959115338807116, "min": 0.24444444444444444, "max": 0.2558139534883721}}, "consistency_analysis": {"overall_consistency": 0.980081011377354, "metric_consistency": {"naturalness_score": {"consistency_score": 1.0, "coefficient_of_variation": 0.0, "range": 0.0}, "boundary_accuracy": {"consistency_score": 0.9414688452926887, "coefficient_of_variation": 0.05853115470731133, "range": 0.11046511627906974}, "type_accuracy": {"consistency_score": 0.9939442550307631, "coefficient_of_variation": 0.006055744969236835, "range": 0.016537467700258435}, "vocabulary_diversity": {"consistency_score": 0.9793726299498203, "coefficient_of_variation": 0.02062737005017962, "range": 0.011484234811586752}, "entity_diversity": {"consistency_score": 0.9856193266134976, "coefficient_of_variation": 0.01438067338650238, "range": 0.011369509043927667}}, "stability_score": 0.9794523493216648}}}, "summary": {"overall_quality_score": 0.5257998012916046, "quality_dimensions": {"accuracy": {"score": 0.7727272727272727, "boundary_accuracy": 0.6818181818181818, "type_accuracy": 0.8636363636363636, "status": "良好"}, "consistency": {"score": 0.76, "status": "良好"}, "diversity": {"score": 0.04467213114754098, "vocabulary_diversity": 0.03934426229508197, "entity_diversity": 0.05, "status": "需改进"}}, "key_metrics": {"total_dimensions_evaluated": 3, "excellent_dimensions": 0, "needs_improvement_dimensions": 1}, "quality_level": "一般"}, "recommendations": ["实体边界准确性需要改进，检查实体识别和标注逻辑", "实体类型准确性需要改进，优化实体分类规则", "词汇多样性不足，建议扩大词汇库和同义词替换", "实体多样性不足，建议增加更多实体变体", "人名类型的一致性较低，需要优化该类型的生成规则"]}