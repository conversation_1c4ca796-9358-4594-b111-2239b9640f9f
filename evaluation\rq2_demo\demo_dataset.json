[{"text": "张伟博士在清华大学计算机科学与技术系担任教授，专注于人工智能研究。", "label": [{"text": "张伟", "type": "人名", "start": 0, "end": 2}, {"text": "博士", "type": "学历", "start": 2, "end": 4}, {"text": "清华大学", "type": "组织名", "start": 5, "end": 9}, {"text": "计算机科学与技术系", "type": "组织名", "start": 9, "end": 18}, {"text": "教授", "type": "职业", "start": 21, "end": 23}, {"text": "人工智能", "type": "专业", "start": 26, "end": 30}]}, {"text": "李明在北京市海淀区中关村软件园的百度公司工作，担任高级算法工程师。", "label": [{"text": "<PERSON>", "type": "人名", "start": 0, "end": 2}, {"text": "北京市", "type": "地名", "start": 3, "end": 6}, {"text": "海淀区", "type": "地名", "start": 6, "end": 9}, {"text": "中关村软件园", "type": "地名", "start": 9, "end": 15}, {"text": "百度公司", "type": "组织名", "start": 16, "end": 20}, {"text": "高级算法工程师", "type": "职业", "start": 24, "end": 31}]}, {"text": "张伟博士在清华大学计算机科学与技术系担任教授，专注于人工智能研究。", "label": [{"text": "张伟", "type": "人名", "start": 0, "end": 2}, {"text": "博士", "type": "学历", "start": 2, "end": 4}, {"text": "清华大学", "type": "组织名", "start": 5, "end": 9}, {"text": "计算机科学与技术系", "type": "组织名", "start": 9, "end": 18}, {"text": "教授", "type": "职业", "start": 21, "end": 23}, {"text": "人工智能", "type": "专业", "start": 26, "end": 30}]}, {"text": "李明在北京市海淀区中关村软件园的百度公司工作，担任高级算法工程师。", "label": [{"text": "<PERSON>", "type": "人名", "start": 0, "end": 2}, {"text": "北京市", "type": "地名", "start": 3, "end": 6}, {"text": "海淀区", "type": "地名", "start": 6, "end": 9}, {"text": "中关村软件园", "type": "地名", "start": 9, "end": 15}, {"text": "百度公司", "type": "组织名", "start": 16, "end": 20}, {"text": "高级算法工程师", "type": "职业", "start": 24, "end": 31}]}, {"text": "张伟博士在清华大学计算机科学与技术系担任教授，专注于人工智能研究。", "label": [{"text": "张伟", "type": "人名", "start": 0, "end": 2}, {"text": "博士", "type": "学历", "start": 2, "end": 4}, {"text": "清华大学", "type": "组织名", "start": 5, "end": 9}, {"text": "计算机科学与技术系", "type": "组织名", "start": 9, "end": 18}, {"text": "教授", "type": "职业", "start": 21, "end": 23}, {"text": "人工智能", "type": "专业", "start": 26, "end": 30}]}, {"text": "李明在北京市海淀区中关村软件园的百度公司工作，担任高级算法工程师。", "label": [{"text": "<PERSON>", "type": "人名", "start": 0, "end": 2}, {"text": "北京市", "type": "地名", "start": 3, "end": 6}, {"text": "海淀区", "type": "地名", "start": 6, "end": 9}, {"text": "中关村软件园", "type": "地名", "start": 9, "end": 15}, {"text": "百度公司", "type": "组织名", "start": 16, "end": 20}, {"text": "高级算法工程师", "type": "职业", "start": 24, "end": 31}]}, {"text": "张伟博士在清华大学计算机科学与技术系担任教授，专注于人工智能研究。", "label": [{"text": "张伟", "type": "人名", "start": 0, "end": 2}, {"text": "博士", "type": "学历", "start": 2, "end": 4}, {"text": "清华大学", "type": "组织名", "start": 5, "end": 9}, {"text": "计算机科学与技术系", "type": "组织名", "start": 9, "end": 18}, {"text": "教授", "type": "职业", "start": 21, "end": 23}, {"text": "人工智能", "type": "专业", "start": 26, "end": 30}]}, {"text": "李明在北京市海淀区中关村软件园的百度公司工作，担任高级算法工程师。", "label": [{"text": "<PERSON>", "type": "人名", "start": 0, "end": 2}, {"text": "北京市", "type": "地名", "start": 3, "end": 6}, {"text": "海淀区", "type": "地名", "start": 6, "end": 9}, {"text": "中关村软件园", "type": "地名", "start": 9, "end": 15}, {"text": "百度公司", "type": "组织名", "start": 16, "end": 20}, {"text": "高级算法工程师", "type": "职业", "start": 24, "end": 31}]}, {"text": "张伟博士在清华大学计算机科学与技术系担任教授，专注于人工智能研究。", "label": [{"text": "张伟", "type": "人名", "start": 0, "end": 2}, {"text": "博士", "type": "学历", "start": 2, "end": 4}, {"text": "清华大学", "type": "组织名", "start": 5, "end": 9}, {"text": "计算机科学与技术系", "type": "组织名", "start": 9, "end": 18}, {"text": "教授", "type": "职业", "start": 21, "end": 23}, {"text": "人工智能", "type": "专业", "start": 26, "end": 30}]}, {"text": "李明在北京市海淀区中关村软件园的百度公司工作，担任高级算法工程师。", "label": [{"text": "<PERSON>", "type": "人名", "start": 0, "end": 2}, {"text": "北京市", "type": "地名", "start": 3, "end": 6}, {"text": "海淀区", "type": "地名", "start": 6, "end": 9}, {"text": "中关村软件园", "type": "地名", "start": 9, "end": 15}, {"text": "百度公司", "type": "组织名", "start": 16, "end": 20}, {"text": "高级算法工程师", "type": "职业", "start": 24, "end": 31}]}, {"text": "张伟博士在清华大学计算机科学与技术系担任教授，专注于人工智能研究。", "label": [{"text": "张伟", "type": "人名", "start": 0, "end": 2}, {"text": "博士", "type": "学历", "start": 2, "end": 4}, {"text": "清华大学", "type": "组织名", "start": 5, "end": 9}, {"text": "计算机科学与技术系", "type": "组织名", "start": 9, "end": 18}, {"text": "教授", "type": "职业", "start": 21, "end": 23}, {"text": "人工智能", "type": "专业", "start": 26, "end": 30}]}, {"text": "李明在北京市海淀区中关村软件园的百度公司工作，担任高级算法工程师。", "label": [{"text": "<PERSON>", "type": "人名", "start": 0, "end": 2}, {"text": "北京市", "type": "地名", "start": 3, "end": 6}, {"text": "海淀区", "type": "地名", "start": 6, "end": 9}, {"text": "中关村软件园", "type": "地名", "start": 9, "end": 15}, {"text": "百度公司", "type": "组织名", "start": 16, "end": 20}, {"text": "高级算法工程师", "type": "职业", "start": 24, "end": 31}]}, {"text": "张伟博士在清华大学计算机科学与技术系担任教授，专注于人工智能研究。", "label": [{"text": "张伟", "type": "人名", "start": 0, "end": 2}, {"text": "博士", "type": "学历", "start": 2, "end": 4}, {"text": "清华大学", "type": "组织名", "start": 5, "end": 9}, {"text": "计算机科学与技术系", "type": "组织名", "start": 9, "end": 18}, {"text": "教授", "type": "职业", "start": 21, "end": 23}, {"text": "人工智能", "type": "专业", "start": 26, "end": 30}]}, {"text": "李明在北京市海淀区中关村软件园的百度公司工作，担任高级算法工程师。", "label": [{"text": "<PERSON>", "type": "人名", "start": 0, "end": 2}, {"text": "北京市", "type": "地名", "start": 3, "end": 6}, {"text": "海淀区", "type": "地名", "start": 6, "end": 9}, {"text": "中关村软件园", "type": "地名", "start": 9, "end": 15}, {"text": "百度公司", "type": "组织名", "start": 16, "end": 20}, {"text": "高级算法工程师", "type": "职业", "start": 24, "end": 31}]}, {"text": "张伟博士在清华大学计算机科学与技术系担任教授，专注于人工智能研究。", "label": [{"text": "张伟", "type": "人名", "start": 0, "end": 2}, {"text": "博士", "type": "学历", "start": 2, "end": 4}, {"text": "清华大学", "type": "组织名", "start": 5, "end": 9}, {"text": "计算机科学与技术系", "type": "组织名", "start": 9, "end": 18}, {"text": "教授", "type": "职业", "start": 21, "end": 23}, {"text": "人工智能", "type": "专业", "start": 26, "end": 30}]}, {"text": "李明在北京市海淀区中关村软件园的百度公司工作，担任高级算法工程师。", "label": [{"text": "<PERSON>", "type": "人名", "start": 0, "end": 2}, {"text": "北京市", "type": "地名", "start": 3, "end": 6}, {"text": "海淀区", "type": "地名", "start": 6, "end": 9}, {"text": "中关村软件园", "type": "地名", "start": 9, "end": 15}, {"text": "百度公司", "type": "组织名", "start": 16, "end": 20}, {"text": "高级算法工程师", "type": "职业", "start": 24, "end": 31}]}, {"text": "张伟博士在清华大学计算机科学与技术系担任教授，专注于人工智能研究。", "label": [{"text": "张伟", "type": "人名", "start": 0, "end": 2}, {"text": "博士", "type": "学历", "start": 2, "end": 4}, {"text": "清华大学", "type": "组织名", "start": 5, "end": 9}, {"text": "计算机科学与技术系", "type": "组织名", "start": 9, "end": 18}, {"text": "教授", "type": "职业", "start": 21, "end": 23}, {"text": "人工智能", "type": "专业", "start": 26, "end": 30}]}, {"text": "李明在北京市海淀区中关村软件园的百度公司工作，担任高级算法工程师。", "label": [{"text": "<PERSON>", "type": "人名", "start": 0, "end": 2}, {"text": "北京市", "type": "地名", "start": 3, "end": 6}, {"text": "海淀区", "type": "地名", "start": 6, "end": 9}, {"text": "中关村软件园", "type": "地名", "start": 9, "end": 15}, {"text": "百度公司", "type": "组织名", "start": 16, "end": 20}, {"text": "高级算法工程师", "type": "职业", "start": 24, "end": 31}]}, {"text": "张伟博士在清华大学计算机科学与技术系担任教授，专注于人工智能研究。", "label": [{"text": "张伟", "type": "人名", "start": 0, "end": 2}, {"text": "博士", "type": "学历", "start": 2, "end": 4}, {"text": "清华大学", "type": "组织名", "start": 5, "end": 9}, {"text": "计算机科学与技术系", "type": "组织名", "start": 9, "end": 18}, {"text": "教授", "type": "职业", "start": 21, "end": 23}, {"text": "人工智能", "type": "专业", "start": 26, "end": 30}]}, {"text": "李明在北京市海淀区中关村软件园的百度公司工作，担任高级算法工程师。", "label": [{"text": "<PERSON>", "type": "人名", "start": 0, "end": 2}, {"text": "北京市", "type": "地名", "start": 3, "end": 6}, {"text": "海淀区", "type": "地名", "start": 6, "end": 9}, {"text": "中关村软件园", "type": "地名", "start": 9, "end": 15}, {"text": "百度公司", "type": "组织名", "start": 16, "end": 20}, {"text": "高级算法工程师", "type": "职业", "start": 24, "end": 31}]}, {"text": "张伟博士在清华大学计算机科学与技术系担任教授，专注于人工智能研究。", "label": [{"text": "张伟", "type": "人名", "start": 0, "end": 2}, {"text": "博士", "type": "学历", "start": 2, "end": 4}, {"text": "清华大学", "type": "组织名", "start": 5, "end": 9}, {"text": "计算机科学与技术系", "type": "组织名", "start": 9, "end": 18}, {"text": "教授", "type": "职业", "start": 21, "end": 23}, {"text": "人工智能", "type": "专业", "start": 26, "end": 30}]}, {"text": "李明在北京市海淀区中关村软件园的百度公司工作，担任高级算法工程师。", "label": [{"text": "<PERSON>", "type": "人名", "start": 0, "end": 2}, {"text": "北京市", "type": "地名", "start": 3, "end": 6}, {"text": "海淀区", "type": "地名", "start": 6, "end": 9}, {"text": "中关村软件园", "type": "地名", "start": 9, "end": 15}, {"text": "百度公司", "type": "组织名", "start": 16, "end": 20}, {"text": "高级算法工程师", "type": "职业", "start": 24, "end": 31}]}, {"text": "张伟博士在清华大学计算机科学与技术系担任教授，专注于人工智能研究。", "label": [{"text": "张伟", "type": "人名", "start": 0, "end": 2}, {"text": "博士", "type": "学历", "start": 2, "end": 4}, {"text": "清华大学", "type": "组织名", "start": 5, "end": 9}, {"text": "计算机科学与技术系", "type": "组织名", "start": 9, "end": 18}, {"text": "教授", "type": "职业", "start": 21, "end": 23}, {"text": "人工智能", "type": "专业", "start": 26, "end": 30}]}, {"text": "李明在北京市海淀区中关村软件园的百度公司工作，担任高级算法工程师。", "label": [{"text": "<PERSON>", "type": "人名", "start": 0, "end": 2}, {"text": "北京市", "type": "地名", "start": 3, "end": 6}, {"text": "海淀区", "type": "地名", "start": 6, "end": 9}, {"text": "中关村软件园", "type": "地名", "start": 9, "end": 15}, {"text": "百度公司", "type": "组织名", "start": 16, "end": 20}, {"text": "高级算法工程师", "type": "职业", "start": 24, "end": 31}]}, {"text": "张伟博士在清华大学计算机科学与技术系担任教授，专注于人工智能研究。", "label": [{"text": "张伟", "type": "人名", "start": 0, "end": 2}, {"text": "博士", "type": "学历", "start": 2, "end": 4}, {"text": "清华大学", "type": "组织名", "start": 5, "end": 9}, {"text": "计算机科学与技术系", "type": "组织名", "start": 9, "end": 18}, {"text": "教授", "type": "职业", "start": 21, "end": 23}, {"text": "人工智能", "type": "专业", "start": 26, "end": 30}]}, {"text": "李明在北京市海淀区中关村软件园的百度公司工作，担任高级算法工程师。", "label": [{"text": "<PERSON>", "type": "人名", "start": 0, "end": 2}, {"text": "北京市", "type": "地名", "start": 3, "end": 6}, {"text": "海淀区", "type": "地名", "start": 6, "end": 9}, {"text": "中关村软件园", "type": "地名", "start": 9, "end": 15}, {"text": "百度公司", "type": "组织名", "start": 16, "end": 20}, {"text": "高级算法工程师", "type": "职业", "start": 24, "end": 31}]}, {"text": "张伟博士在清华大学计算机科学与技术系担任教授，专注于人工智能研究。", "label": [{"text": "张伟", "type": "人名", "start": 0, "end": 2}, {"text": "博士", "type": "学历", "start": 2, "end": 4}, {"text": "清华大学", "type": "组织名", "start": 5, "end": 9}, {"text": "计算机科学与技术系", "type": "组织名", "start": 9, "end": 18}, {"text": "教授", "type": "职业", "start": 21, "end": 23}, {"text": "人工智能", "type": "专业", "start": 26, "end": 30}]}, {"text": "李明在北京市海淀区中关村软件园的百度公司工作，担任高级算法工程师。", "label": [{"text": "<PERSON>", "type": "人名", "start": 0, "end": 2}, {"text": "北京市", "type": "地名", "start": 3, "end": 6}, {"text": "海淀区", "type": "地名", "start": 6, "end": 9}, {"text": "中关村软件园", "type": "地名", "start": 9, "end": 15}, {"text": "百度公司", "type": "组织名", "start": 16, "end": 20}, {"text": "高级算法工程师", "type": "职业", "start": 24, "end": 31}]}, {"text": "张伟博士在清华大学计算机科学与技术系担任教授，专注于人工智能研究。", "label": [{"text": "张伟", "type": "人名", "start": 0, "end": 2}, {"text": "博士", "type": "学历", "start": 2, "end": 4}, {"text": "清华大学", "type": "组织名", "start": 5, "end": 9}, {"text": "计算机科学与技术系", "type": "组织名", "start": 9, "end": 18}, {"text": "教授", "type": "职业", "start": 21, "end": 23}, {"text": "人工智能", "type": "专业", "start": 26, "end": 30}]}, {"text": "李明在北京市海淀区中关村软件园的百度公司工作，担任高级算法工程师。", "label": [{"text": "<PERSON>", "type": "人名", "start": 0, "end": 2}, {"text": "北京市", "type": "地名", "start": 3, "end": 6}, {"text": "海淀区", "type": "地名", "start": 6, "end": 9}, {"text": "中关村软件园", "type": "地名", "start": 9, "end": 15}, {"text": "百度公司", "type": "组织名", "start": 16, "end": 20}, {"text": "高级算法工程师", "type": "职业", "start": 24, "end": 31}]}, {"text": "王芳是上海交通大学的学生，学习机器学习。", "label": [{"text": "王芳", "type": "人名", "start": 0, "end": 2}, {"text": "上海交通大学", "type": "组织名", "start": 3, "end": 9}, {"text": "学生", "type": "职业", "start": 11, "end": 13}, {"text": "机器学习", "type": "专业", "start": 16, "end": 20}]}, {"text": "陈华在深圳腾讯工作，年薪80万。", "label": [{"text": "陈华", "type": "人名", "start": 0, "end": 2}, {"text": "深圳", "type": "地名", "start": 3, "end": 5}, {"text": "腾讯", "type": "组织名", "start": 5, "end": 7}, {"text": "80万", "type": "金额", "start": 12, "end": 15}]}, {"text": "王芳是上海交通大学的学生，学习机器学习。", "label": [{"text": "王芳", "type": "人名", "start": 0, "end": 2}, {"text": "上海交通大学", "type": "组织名", "start": 3, "end": 9}, {"text": "学生", "type": "职业", "start": 11, "end": 13}, {"text": "机器学习", "type": "专业", "start": 16, "end": 20}]}, {"text": "陈华在深圳腾讯工作，年薪80万。", "label": [{"text": "陈华", "type": "人名", "start": 0, "end": 2}, {"text": "深圳", "type": "地名", "start": 3, "end": 5}, {"text": "腾讯", "type": "组织名", "start": 5, "end": 7}, {"text": "80万", "type": "金额", "start": 12, "end": 15}]}, {"text": "王芳是上海交通大学的学生，学习机器学习。", "label": [{"text": "王芳", "type": "人名", "start": 0, "end": 2}, {"text": "上海交通大学", "type": "组织名", "start": 3, "end": 9}, {"text": "学生", "type": "职业", "start": 11, "end": 13}, {"text": "机器学习", "type": "专业", "start": 16, "end": 20}]}, {"text": "陈华在深圳腾讯工作，年薪80万。", "label": [{"text": "陈华", "type": "人名", "start": 0, "end": 2}, {"text": "深圳", "type": "地名", "start": 3, "end": 5}, {"text": "腾讯", "type": "组织名", "start": 5, "end": 7}, {"text": "80万", "type": "金额", "start": 12, "end": 15}]}, {"text": "王芳是上海交通大学的学生，学习机器学习。", "label": [{"text": "王芳", "type": "人名", "start": 0, "end": 2}, {"text": "上海交通大学", "type": "组织名", "start": 3, "end": 9}, {"text": "学生", "type": "职业", "start": 11, "end": 13}, {"text": "机器学习", "type": "专业", "start": 16, "end": 20}]}, {"text": "陈华在深圳腾讯工作，年薪80万。", "label": [{"text": "陈华", "type": "人名", "start": 0, "end": 2}, {"text": "深圳", "type": "地名", "start": 3, "end": 5}, {"text": "腾讯", "type": "组织名", "start": 5, "end": 7}, {"text": "80万", "type": "金额", "start": 12, "end": 15}]}, {"text": "王芳是上海交通大学的学生，学习机器学习。", "label": [{"text": "王芳", "type": "人名", "start": 0, "end": 2}, {"text": "上海交通大学", "type": "组织名", "start": 3, "end": 9}, {"text": "学生", "type": "职业", "start": 11, "end": 13}, {"text": "机器学习", "type": "专业", "start": 16, "end": 20}]}, {"text": "陈华在深圳腾讯工作，年薪80万。", "label": [{"text": "陈华", "type": "人名", "start": 0, "end": 2}, {"text": "深圳", "type": "地名", "start": 3, "end": 5}, {"text": "腾讯", "type": "组织名", "start": 5, "end": 7}, {"text": "80万", "type": "金额", "start": 12, "end": 15}]}, {"text": "王芳是上海交通大学的学生，学习机器学习。", "label": [{"text": "王芳", "type": "人名", "start": 0, "end": 2}, {"text": "上海交通大学", "type": "组织名", "start": 3, "end": 9}, {"text": "学生", "type": "职业", "start": 11, "end": 13}, {"text": "机器学习", "type": "专业", "start": 16, "end": 20}]}, {"text": "陈华在深圳腾讯工作，年薪80万。", "label": [{"text": "陈华", "type": "人名", "start": 0, "end": 2}, {"text": "深圳", "type": "地名", "start": 3, "end": 5}, {"text": "腾讯", "type": "组织名", "start": 5, "end": 7}, {"text": "80万", "type": "金额", "start": 12, "end": 15}]}, {"text": "王芳是上海交通大学的学生，学习机器学习。", "label": [{"text": "王芳", "type": "人名", "start": 0, "end": 2}, {"text": "上海交通大学", "type": "组织名", "start": 3, "end": 9}, {"text": "学生", "type": "职业", "start": 11, "end": 13}, {"text": "机器学习", "type": "专业", "start": 16, "end": 20}]}, {"text": "陈华在深圳腾讯工作，年薪80万。", "label": [{"text": "陈华", "type": "人名", "start": 0, "end": 2}, {"text": "深圳", "type": "地名", "start": 3, "end": 5}, {"text": "腾讯", "type": "组织名", "start": 5, "end": 7}, {"text": "80万", "type": "金额", "start": 12, "end": 15}]}, {"text": "王芳是上海交通大学的学生，学习机器学习。", "label": [{"text": "王芳", "type": "人名", "start": 0, "end": 2}, {"text": "上海交通大学", "type": "组织名", "start": 3, "end": 9}, {"text": "学生", "type": "职业", "start": 11, "end": 13}, {"text": "机器学习", "type": "专业", "start": 16, "end": 20}]}, {"text": "陈华在深圳腾讯工作，年薪80万。", "label": [{"text": "陈华", "type": "人名", "start": 0, "end": 2}, {"text": "深圳", "type": "地名", "start": 3, "end": 5}, {"text": "腾讯", "type": "组织名", "start": 5, "end": 7}, {"text": "80万", "type": "金额", "start": 12, "end": 15}]}, {"text": "王芳是上海交通大学的学生，学习机器学习。", "label": [{"text": "王芳", "type": "人名", "start": 0, "end": 2}, {"text": "上海交通大学", "type": "组织名", "start": 3, "end": 9}, {"text": "学生", "type": "职业", "start": 11, "end": 13}, {"text": "机器学习", "type": "专业", "start": 16, "end": 20}]}, {"text": "陈华在深圳腾讯工作，年薪80万。", "label": [{"text": "陈华", "type": "人名", "start": 0, "end": 2}, {"text": "深圳", "type": "地名", "start": 3, "end": 5}, {"text": "腾讯", "type": "组织名", "start": 5, "end": 7}, {"text": "80万", "type": "金额", "start": 12, "end": 15}]}, {"text": "王芳是上海交通大学的学生，学习机器学习。", "label": [{"text": "王芳", "type": "人名", "start": 0, "end": 2}, {"text": "上海交通大学", "type": "组织名", "start": 3, "end": 9}, {"text": "学生", "type": "职业", "start": 11, "end": 13}, {"text": "机器学习", "type": "专业", "start": 16, "end": 20}]}, {"text": "陈华在深圳腾讯工作，年薪80万。", "label": [{"text": "陈华", "type": "人名", "start": 0, "end": 2}, {"text": "深圳", "type": "地名", "start": 3, "end": 5}, {"text": "腾讯", "type": "组织名", "start": 5, "end": 7}, {"text": "80万", "type": "金额", "start": 12, "end": 15}]}, {"text": "小明在公司上班。", "label": [{"text": "小明", "type": "人名", "start": 0, "end": 2}, {"text": "公司", "type": "组织名", "start": 3, "end": 5}]}, {"text": "他去了那里工作很久了。", "label": [{"text": "他", "type": "人名", "start": 0, "end": 1}, {"text": "那里", "type": "地名", "start": 3, "end": 5}]}, {"text": "小明在公司上班。", "label": [{"text": "小明", "type": "人名", "start": 0, "end": 2}, {"text": "公司", "type": "组织名", "start": 3, "end": 5}]}, {"text": "他去了那里工作很久了。", "label": [{"text": "他", "type": "人名", "start": 0, "end": 1}, {"text": "那里", "type": "地名", "start": 3, "end": 5}]}, {"text": "小明在公司上班。", "label": [{"text": "小明", "type": "人名", "start": 0, "end": 2}, {"text": "公司", "type": "组织名", "start": 3, "end": 5}]}, {"text": "他去了那里工作很久了。", "label": [{"text": "他", "type": "人名", "start": 0, "end": 1}, {"text": "那里", "type": "地名", "start": 3, "end": 5}]}, {"text": "小明在公司上班。", "label": [{"text": "小明", "type": "人名", "start": 0, "end": 2}, {"text": "公司", "type": "组织名", "start": 3, "end": 5}]}, {"text": "他去了那里工作很久了。", "label": [{"text": "他", "type": "人名", "start": 0, "end": 1}, {"text": "那里", "type": "地名", "start": 3, "end": 5}]}, {"text": "小明在公司上班。", "label": [{"text": "小明", "type": "人名", "start": 0, "end": 2}, {"text": "公司", "type": "组织名", "start": 3, "end": 5}]}, {"text": "他去了那里工作很久了。", "label": [{"text": "他", "type": "人名", "start": 0, "end": 1}, {"text": "那里", "type": "地名", "start": 3, "end": 5}]}]