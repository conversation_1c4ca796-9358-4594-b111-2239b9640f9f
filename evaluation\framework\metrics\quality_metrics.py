﻿# -*- coding: utf-8 -*-
"""
质量指标计算模块
用于RQ2: 生成数据质量评估
"""

import json
import numpy as np
from typing import Dict, List, Any, Tuple
from collections import Counter
import jieba

def evaluate_naturalness(dataset: List[Dict], sample_size: int = 100) -> Dict[str, Any]:
    """评估数据的自然度"""
    # 这是一个简化的自然度评估，实际应该使用语言模型
    naturalness_scores = []
    
    # 随机采样
    import random
    sampled_data = random.sample(dataset, min(sample_size, len(dataset)))
    
    for item in sampled_data:
        text = item.get("text", "")
        
        # 简单的自然度评估规则
        score = 10.0  # 基础分数
        
        # 长度惩罚（过短或过长）
        if len(text) < 5:
            score -= 2.0
        elif len(text) > 200:
            score -= 1.0
        
        # 重复字符惩罚
        if has_repeated_chars(text):
            score -= 1.0
        
        # 标点符号合理性
        if not has_reasonable_punctuation(text):
            score -= 0.5
        
        # 词汇合理性
        words = list(jieba.cut(text))
        if len(words) < 2:
            score -= 1.0
        
        # 确保分数在合理范围内
        score = max(1.0, min(10.0, score))
        naturalness_scores.append(score)
    
    return {
        "avg_score": np.mean(naturalness_scores),
        "std_score": np.std(naturalness_scores),
        "min_score": np.min(naturalness_scores),
        "max_score": np.max(naturalness_scores),
        "scores": naturalness_scores,
        "sample_size": len(sampled_data)
    }

def has_repeated_chars(text: str, threshold: int = 3) -> bool:
    """检查是否有重复字符"""
    for i in range(len(text) - threshold + 1):
        if len(set(text[i:i+threshold])) == 1:
            return True
    return False

def has_reasonable_punctuation(text: str) -> bool:
    """检查标点符号是否合理"""
    punctuation = "，。！？；：""''（）【】"
    punct_count = sum(1 for char in text if char in punctuation)
    
    # 标点符号应该占文本的合理比例
    if len(text) > 0:
        punct_ratio = punct_count / len(text)
        return 0.0 <= punct_ratio <= 0.3
    return True

def evaluate_annotation_accuracy(dataset: List[Dict]) -> Dict[str, Any]:
    """评估标注准确性"""
    accuracy_metrics = {
        "boundary_accuracy": 0.0,
        "type_accuracy": 0.0,
        "total_entities": 0,
        "valid_entities": 0,
        "invalid_boundaries": 0,
        "unknown_types": 0
    }
    
    known_entity_types = {
        "人名", "地名", "组织名", "时间", "数字", "职业", "产品", "事件"
    }
    
    for item in dataset:
        text = item.get("text", "")
        labels = item.get("label", [])
        
        for label in labels:
            accuracy_metrics["total_entities"] += 1
            
            # 检查边界准确性
            start = label.get("start", 0)
            end = label.get("end", 0)
            entity_text = label.get("text", "")
            
            if 0 <= start < end <= len(text):
                actual_text = text[start:end]
                if actual_text == entity_text:
                    accuracy_metrics["valid_entities"] += 1
                else:
                    accuracy_metrics["invalid_boundaries"] += 1
            else:
                accuracy_metrics["invalid_boundaries"] += 1
            
            # 检查类型准确性
            entity_type = label.get("type", "")
            if entity_type not in known_entity_types:
                accuracy_metrics["unknown_types"] += 1
    
    # 计算准确率
    if accuracy_metrics["total_entities"] > 0:
        accuracy_metrics["boundary_accuracy"] = accuracy_metrics["valid_entities"] / accuracy_metrics["total_entities"]
        accuracy_metrics["type_accuracy"] = (accuracy_metrics["total_entities"] - accuracy_metrics["unknown_types"]) / accuracy_metrics["total_entities"]
    
    return accuracy_metrics

def evaluate_semantic_consistency(dataset: List[Dict], context_window: int = 5) -> Dict[str, Any]:
    """评估语义一致性"""
    consistency_scores = []
    
    for item in dataset:
        text = item.get("text", "")
        labels = item.get("label", [])
        
        # 简化的语义一致性检查
        score = 1.0
        
        for label in labels:
            start = label.get("start", 0)
            end = label.get("end", 0)
            entity_type = label.get("type", "")
            entity_text = label.get("text", "")
            
            # 提取上下文
            context_start = max(0, start - context_window)
            context_end = min(len(text), end + context_window)
            context = text[context_start:context_end]
            
            # 简单的一致性检查
            if not is_semantically_consistent(entity_text, entity_type, context):
                score -= 0.1
        
        consistency_scores.append(max(0.0, score))
    
    return {
        "avg_consistency": np.mean(consistency_scores),
        "std_consistency": np.std(consistency_scores),
        "min_consistency": np.min(consistency_scores),
        "max_consistency": np.max(consistency_scores),
        "scores": consistency_scores
    }

def is_semantically_consistent(entity_text: str, entity_type: str, context: str) -> bool:
    """检查实体与上下文的语义一致性"""
    # 简化的语义一致性检查
    type_keywords = {
        "人名": ["先生", "女士", "老师", "医生", "经理", "同学"],
        "地名": ["市", "省", "区", "县", "路", "街", "在", "到", "从"],
        "组织名": ["公司", "学校", "医院", "银行", "政府", "部门"],
        "时间": ["年", "月", "日", "时", "分", "秒", "今天", "明天", "昨天"],
        "职业": ["工作", "职位", "岗位", "从事", "担任"]
    }
    
    keywords = type_keywords.get(entity_type, [])
    return any(keyword in context for keyword in keywords)

def calculate_diversity_metrics(dataset: List[Dict]) -> Dict[str, float]:
    """计算多样性指标"""
    # 词汇多样性
    all_words = []
    all_sentences = []
    all_entity_contexts = []
    
    for item in dataset:
        text = item.get("text", "")
        labels = item.get("label", [])
        
        # 词汇收集
        words = list(jieba.cut(text))
        all_words.extend(words)
        
        # 句子收集
        all_sentences.append(text)
        
        # 实体上下文收集
        for label in labels:
            start = label.get("start", 0)
            end = label.get("end", 0)
            
            context_start = max(0, start - 3)
            context_end = min(len(text), end + 3)
            context = text[context_start:context_end]
            all_entity_contexts.append(context)
    
    # 计算多样性
    word_counts = Counter(all_words)
    sentence_counts = Counter(all_sentences)
    context_counts = Counter(all_entity_contexts)
    
    diversity_metrics = {
        "vocabulary_diversity": len(word_counts) / len(all_words) if all_words else 0,
        "sentence_diversity": len(sentence_counts) / len(all_sentences) if all_sentences else 0,
        "context_diversity": len(context_counts) / len(all_entity_contexts) if all_entity_contexts else 0,
        "syntactic_diversity": calculate_syntactic_diversity(all_sentences),
        "semantic_diversity": calculate_semantic_diversity(all_sentences),
        "entity_diversity": calculate_entity_diversity(dataset)
    }
    
    return diversity_metrics

def calculate_syntactic_diversity(sentences: List[str]) -> float:
    """计算句法多样性"""
    # 简化的句法模式分析
    patterns = []
    
    for sentence in sentences:
        # 基于长度和标点的简单模式
        length_pattern = len(sentence) // 10  # 长度模式
        punct_pattern = sum(1 for char in sentence if char in "，。！？；：")  # 标点模式
        pattern = f"len_{length_pattern}_punct_{punct_pattern}"
        patterns.append(pattern)
    
    pattern_counts = Counter(patterns)
    return len(pattern_counts) / len(patterns) if patterns else 0

def calculate_semantic_diversity(sentences: List[str]) -> float:
    """计算语义多样性"""
    # 简化的语义分析（基于关键词）
    semantic_features = []
    
    for sentence in sentences:
        words = list(jieba.cut(sentence))
        # 提取名词作为语义特征
        nouns = [word for word in words if len(word) > 1 and word.isalpha()]
        semantic_features.extend(nouns)
    
    if not semantic_features:
        return 0
    
    feature_counts = Counter(semantic_features)
    return len(feature_counts) / len(semantic_features)

def calculate_entity_diversity(dataset: List[Dict]) -> float:
    """计算实体多样性"""
    all_entities = []
    
    for item in dataset:
        labels = item.get("label", [])
        for label in labels:
            entity_text = label.get("text", "")
            entity_type = label.get("type", "")
            entity_key = f"{entity_type}:{entity_text}"
            all_entities.append(entity_key)
    
    if not all_entities:
        return 0
    
    entity_counts = Counter(all_entities)
    return len(entity_counts) / len(all_entities)

def evaluate_balance_metrics(dataset: List[Dict], target_distribution: Dict[str, int] = None) -> Dict[str, Any]:
    """评估平衡性指标"""
    # 计算实际分布
    actual_distribution = Counter()
    
    for item in dataset:
        labels = item.get("label", [])
        for label in labels:
            entity_type = label.get("type", "")
            if entity_type:
                actual_distribution[entity_type] += 1
    
    balance_metrics = {
        "actual_distribution": dict(actual_distribution),
        "distribution_entropy": calculate_entropy(actual_distribution),
        "balance_score": 0.0,
        "coverage_ratio": 0.0
    }
    
    if target_distribution:
        # 计算平衡得分
        total_target = sum(target_distribution.values())
        total_actual = sum(actual_distribution.values())
        
        if total_target > 0 and total_actual > 0:
            balance_score = 0.0
            covered_types = 0
            
            for entity_type, target_count in target_distribution.items():
                actual_count = actual_distribution.get(entity_type, 0)
                if actual_count > 0:
                    covered_types += 1
                
                # 计算相对误差
                if target_count > 0:
                    relative_error = abs(actual_count - target_count) / target_count
                    balance_score += 1.0 - min(1.0, relative_error)
            
            balance_metrics["balance_score"] = balance_score / len(target_distribution)
            balance_metrics["coverage_ratio"] = covered_types / len(target_distribution)
    
    return balance_metrics

def calculate_entropy(counter: Counter) -> float:
    """计算信息熵"""
    import math
    
    total = sum(counter.values())
    if total == 0:
        return 0
    
    entropy = 0
    for count in counter.values():
        p = count / total
        if p > 0:
            entropy -= p * math.log2(p)
    
    return entropy
