{"timestamp": "20251003_155501", "start_time": "2025-10-03T15:55:01.056056", "status": "failed", "config": {"dataset_path": "format-dataset\\privacy_bench_small_10.json", "target_count": 20, "max_iterations": 10, "batch_size": 10, "distribution_threshold": 0.05, "generation_features": {"use_sentence_diversity": true, "use_entity_diversity": true, "use_example_sentences": true}}, "directories": {"root": "synth_dataset\\runs\\20251003_155501", "config": "synth_dataset\\runs\\20251003_155501\\config", "source": "synth_dataset\\runs\\20251003_155501\\source", "intermediate": "synth_dataset\\runs\\20251003_155501\\intermediate", "intermediate_raw": "synth_dataset\\runs\\20251003_155501\\intermediate\\raw", "intermediate_processed": "synth_dataset\\runs\\20251003_155501\\intermediate\\processed", "intermediate_by_entity": "synth_dataset\\runs\\20251003_155501\\intermediate\\by_entity", "iterations": "synth_dataset\\runs\\20251003_155501\\iterations", "strategies": "synth_dataset\\runs\\20251003_155501\\strategies", "output": "synth_dataset\\runs\\20251003_155501\\output", "evaluation": "synth_dataset\\runs\\20251003_155501\\evaluation", "logs": "synth_dataset\\runs\\20251003_155501\\logs"}, "last_updated": "2025-10-03T16:09:42.078533", "stage": "iteration", "current_iteration": 1, "max_iterations": 10, "error": "can only concatenate list (not \"dict\") to list"}