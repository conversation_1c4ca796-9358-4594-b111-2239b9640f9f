﻿#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试RQ2质量评估功能
"""

import json
import numpy as np
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def create_test_dataset():
    """创建测试数据集"""
    test_data = []
    
    # 创建多样化的测试数据
    samples = [
        {
            "text": "张三是一名优秀的软件工程师，他在北京工作了五年。",
            "label": [
                {"text": "张三", "type": "人名", "start": 0, "end": 2},
                {"text": "软件工程师", "type": "职业", "start": 7, "end": 12},
                {"text": "北京", "type": "地名", "start": 16, "end": 18},
                {"text": "五年", "type": "时间", "start": 21, "end": 23}
            ]
        },
        {
            "text": "李四在上海的华为公司担任产品经理，年薪50万元。",
            "label": [
                {"text": "李四", "type": "人名", "start": 0, "end": 2},
                {"text": "上海", "type": "地名", "start": 3, "end": 5},
                {"text": "华为公司", "type": "组织名", "start": 6, "end": 10},
                {"text": "产品经理", "type": "职业", "start": 13, "end": 17},
                {"text": "50万元", "type": "金额", "start": 20, "end": 25}
            ]
        },
        {
            "text": "王五毕业于清华大学计算机系，现在在深圳创业。",
            "label": [
                {"text": "王五", "type": "人名", "start": 0, "end": 2},
                {"text": "清华大学", "type": "组织名", "start": 5, "end": 9},
                {"text": "计算机系", "type": "组织名", "start": 9, "end": 13},
                {"text": "深圳", "type": "地名", "start": 17, "end": 19}
            ]
        },
        {
            "text": "赵六于2023年3月加入腾讯科技有限公司，担任算法工程师。",
            "label": [
                {"text": "赵六", "type": "人名", "start": 0, "end": 2},
                {"text": "2023年3月", "type": "时间", "start": 3, "end": 9},
                {"text": "腾讯科技有限公司", "type": "组织名", "start": 12, "end": 20},
                {"text": "算法工程师", "type": "职业", "start": 24, "end": 29}
            ]
        },
        {
            "text": "孙七在广州的中山大学攻读人工智能专业博士学位。",
            "label": [
                {"text": "孙七", "type": "人名", "start": 0, "end": 2},
                {"text": "广州", "type": "地名", "start": 3, "end": 5},
                {"text": "中山大学", "type": "组织名", "start": 6, "end": 10},
                {"text": "人工智能", "type": "专业", "start": 13, "end": 17},
                {"text": "博士学位", "type": "学历", "start": 19, "end": 23}
            ]
        }
    ]
    
    # 扩展数据集
    for i in range(20):
        for sample in samples:
            new_sample = {
                "text": sample["text"],
                "label": sample["label"].copy()
            }
            test_data.append(new_sample)
    
    return test_data

def create_target_distribution():
    """创建目标分布"""
    return {
        "人名": 25,
        "地名": 20,
        "组织名": 30,
        "职业": 15,
        "时间": 10,
        "金额": 5,
        "专业": 8,
        "学历": 7
    }

def test_rq2_evaluation():
    """测试RQ2评估功能"""
    print("=" * 60)
    print("测试RQ2质量评估功能")
    print("=" * 60)
    
    # 创建测试目录
    test_dir = Path("evaluation/test_rq2")
    test_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建测试数据
    print("创建测试数据...")
    test_dataset = create_test_dataset()
    target_distribution = create_target_distribution()
    
    # 保存测试数据
    test_dataset_path = test_dir / "test_dataset.json"
    target_dist_path = test_dir / "target_distribution.json"
    
    with open(test_dataset_path, 'w', encoding='utf-8') as f:
        json.dump(test_dataset, f, ensure_ascii=False, indent=2)
    
    with open(target_dist_path, 'w', encoding='utf-8') as f:
        json.dump(target_distribution, f, ensure_ascii=False, indent=2)
    
    print(f" 测试数据已保存到: {test_dataset_path}")
    print(f" 目标分布已保存到: {target_dist_path}")
    
    # 测试各个评估模块
    print("\n测试评估模块...")
    
    try:
        # 测试基础质量指标
        from evaluation.framework.metrics.quality_metrics import (
            evaluate_naturalness,
            evaluate_annotation_accuracy,
            calculate_diversity_metrics
        )
        
        print(" 基础质量指标模块导入成功")
        
        # 测试高级质量指标
        from evaluation.framework.metrics.advanced_quality_metrics import (
            evaluate_linguistic_quality,
            evaluate_entity_quality,
            evaluate_entity_coverage
        )
        
        print(" 高级质量指标模块导入成功")
        
        # 测试可视化模块
        from evaluation.framework.utils.quality_visualization import (
            create_quality_dashboard,
            create_detailed_quality_report
        )
        
        print(" 可视化模块导入成功")
        
        # 运行基础评估
        print("\n运行基础评估...")
        
        naturalness_result = evaluate_naturalness(test_dataset[:10])
        print(f" 自然度评估: {naturalness_result['avg_score']:.2f}")
        
        accuracy_result = evaluate_annotation_accuracy(test_dataset)
        print(f" 标注准确性: {accuracy_result['boundary_accuracy']:.2%}")
        
        diversity_result = calculate_diversity_metrics(test_dataset)
        print(f" 多样性评估: {diversity_result.get('vocabulary_diversity', 0):.3f}")
        
        # 运行高级评估
        print("\n运行高级评估...")
        
        linguistic_result = evaluate_linguistic_quality(test_dataset)
        print(f" 语言学质量: {linguistic_result['grammar_score']:.3f}")
        
        entity_quality_result = evaluate_entity_quality(test_dataset)
        problem_count = len(entity_quality_result.get("problematic_entities", []))
        print(f" 实体质量: 发现 {problem_count} 个问题实体")
        
        coverage_result = evaluate_entity_coverage(test_dataset, target_distribution)
        print(f" 实体覆盖率: {coverage_result['type_coverage']:.2%}")
        
        print("\n 所有评估模块测试通过！")
        
    except Exception as e:
        print(f" 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_rq2_script():
    """测试RQ2评估脚本"""
    print("\n" + "=" * 60)
    print("测试RQ2评估脚本")
    print("=" * 60)
    
    test_dir = Path("evaluation/test_rq2")
    test_dataset_path = test_dir / "test_dataset.json"
    output_dir = test_dir / "results"
    
    # 检查测试数据是否存在
    if not test_dataset_path.exists():
        print(" 测试数据不存在，请先运行 test_rq2_evaluation()")
        return False
    
    # 运行RQ2评估脚本
    import subprocess
    
    cmd = [
        sys.executable,
        "evaluation/framework/rq2_quality_assessment.py",
        "--dataset", str(test_dataset_path),
        "--output", str(output_dir),
        "--sample-size", "20",
        "--skip-naturalness"  # 跳过自然度评估以加快测试
    ]
    
    try:
        print("运行RQ2评估脚本...")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print(" RQ2评估脚本运行成功！")
            print(f" 输出目录: {output_dir}")
            
            # 检查输出文件
            report_file = output_dir / "rq2_evaluation_report.json"
            summary_file = output_dir / "rq2_summary.txt"
            
            if report_file.exists():
                print(f" 评估报告已生成: {report_file}")
                
                # 读取并显示部分结果
                with open(report_file, 'r', encoding='utf-8') as f:
                    report = json.load(f)
                
                summary = report.get("summary", {})
                print(f" 质量等级: {summary.get('quality_level', '未知')}")
                print(f" 综合得分: {summary.get('overall_quality_score', 0):.3f}")
            
            if summary_file.exists():
                print(f" 文本摘要已生成: {summary_file}")
            
            return True
        else:
            print(f" RQ2评估脚本运行失败:")
            print(f"错误输出: {result.stderr}")
            return False
            
    except Exception as e:
        print(f" 运行RQ2评估脚本时发生异常: {e}")
        return False

def main():
    """主函数"""
    print("开始测试RQ2质量评估功能...")
    
    # 测试评估模块
    if not test_rq2_evaluation():
        print(" 评估模块测试失败")
        return
    
    # 测试评估脚本
    if not test_rq2_script():
        print(" 评估脚本测试失败")
        return
    
    print("\n" + "=" * 60)
    print(" RQ2质量评估功能测试完成！")
    print("=" * 60)
    print("所有功能正常工作，可以开始使用RQ2质量评估。")
    print("\n使用方法:")
    print("python evaluation/framework/rq2_quality_assessment.py --dataset your_dataset.json --output results/rq2/")

if __name__ == "__main__":
    main()
