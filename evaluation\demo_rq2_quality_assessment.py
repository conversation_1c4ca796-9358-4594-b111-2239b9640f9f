﻿#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RQ2质量评估演示脚本
展示RQ2的完整功能和使用方法
"""

import json
import numpy as np
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def create_demo_dataset():
    """创建演示数据集"""
    demo_data = []
    
    # 高质量样本
    high_quality_samples = [
        {
            "text": "张伟博士在清华大学计算机科学与技术系担任教授，专注于人工智能研究。",
            "label": [
                {"text": "张伟", "type": "人名", "start": 0, "end": 2},
                {"text": "博士", "type": "学历", "start": 2, "end": 4},
                {"text": "清华大学", "type": "组织名", "start": 5, "end": 9},
                {"text": "计算机科学与技术系", "type": "组织名", "start": 9, "end": 18},
                {"text": "教授", "type": "职业", "start": 21, "end": 23},
                {"text": "人工智能", "type": "专业", "start": 26, "end": 30}
            ]
        },
        {
            "text": "李明在北京市海淀区中关村软件园的百度公司工作，担任高级算法工程师。",
            "label": [
                {"text": "李明", "type": "人名", "start": 0, "end": 2},
                {"text": "北京市", "type": "地名", "start": 3, "end": 6},
                {"text": "海淀区", "type": "地名", "start": 6, "end": 9},
                {"text": "中关村软件园", "type": "地名", "start": 9, "end": 15},
                {"text": "百度公司", "type": "组织名", "start": 16, "end": 20},
                {"text": "高级算法工程师", "type": "职业", "start": 24, "end": 31}
            ]
        }
    ]
    
    # 中等质量样本
    medium_quality_samples = [
        {
            "text": "王芳是上海交通大学的学生，学习机器学习。",
            "label": [
                {"text": "王芳", "type": "人名", "start": 0, "end": 2},
                {"text": "上海交通大学", "type": "组织名", "start": 3, "end": 9},
                {"text": "学生", "type": "职业", "start": 11, "end": 13},
                {"text": "机器学习", "type": "专业", "start": 16, "end": 20}
            ]
        },
        {
            "text": "陈华在深圳腾讯工作，年薪80万。",
            "label": [
                {"text": "陈华", "type": "人名", "start": 0, "end": 2},
                {"text": "深圳", "type": "地名", "start": 3, "end": 5},
                {"text": "腾讯", "type": "组织名", "start": 5, "end": 7},
                {"text": "80万", "type": "金额", "start": 12, "end": 15}
            ]
        }
    ]
    
    # 低质量样本（存在问题）
    low_quality_samples = [
        {
            "text": "小明在公司上班。",
            "label": [
                {"text": "小明", "type": "人名", "start": 0, "end": 2},
                {"text": "公司", "type": "组织名", "start": 3, "end": 5}
            ]
        },
        {
            "text": "他去了那里工作很久了。",
            "label": [
                {"text": "他", "type": "人名", "start": 0, "end": 1},
                {"text": "那里", "type": "地名", "start": 3, "end": 5}
            ]
        }
    ]
    
    # 构建完整数据集
    for i in range(15):
        demo_data.extend(high_quality_samples)
    
    for i in range(10):
        demo_data.extend(medium_quality_samples)
    
    for i in range(5):
        demo_data.extend(low_quality_samples)
    
    return demo_data

def run_rq2_demo():
    """运行RQ2演示"""
    print("=" * 80)
    print("RQ2: 生成数据质量评估 - 功能演示")
    print("=" * 80)
    
    # 创建演示目录
    demo_dir = Path("evaluation/rq2_demo")
    demo_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建演示数据集
    print("1. 创建演示数据集...")
    demo_dataset = create_demo_dataset()
    
    dataset_path = demo_dir / "demo_dataset.json"
    with open(dataset_path, 'w', encoding='utf-8') as f:
        json.dump(demo_dataset, f, ensure_ascii=False, indent=2)
    
    print(f"    演示数据集已创建: {dataset_path}")
    print(f"    数据集包含 {len(demo_dataset)} 条记录")
    print(f"    包含高质量样本、中等质量样本和低质量样本")
    
    # 运行质量评估
    print("\n2. 运行质量评估...")
    
    import subprocess
    
    output_dir = demo_dir / "evaluation_results"
    
    cmd = [
        sys.executable,
        "evaluation/framework/rq2_quality_assessment.py",
        "--dataset", str(dataset_path),
        "--output", str(output_dir),
        "--sample-size", "30",
        "--skip-naturalness"  # 跳过自然度评估以加快演示
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("    质量评估完成")
            
            # 显示评估结果
            print("\n3. 评估结果摘要:")
            
            summary_file = output_dir / "rq2_summary.txt"
            if summary_file.exists():
                with open(summary_file, 'r', encoding='utf-8') as f:
                    summary_content = f.read()
                
                # 提取关键信息
                lines = summary_content.split('\n')
                for line in lines:
                    if "质量等级:" in line or "综合得分:" in line:
                        print(f"   {line.strip()}")
                    elif line.strip().startswith("accuracy:") or line.strip().startswith("diversity:") or line.strip().startswith("consistency:"):
                        print(f"   {line.strip()}")
            
            # 显示生成的文件
            print("\n4. 生成的文件:")
            
            if output_dir.exists():
                for file_path in output_dir.glob("*"):
                    if file_path.is_file():
                        file_size = file_path.stat().st_size
                        if file_size > 1024:
                            size_str = f"{file_size // 1024}KB"
                        else:
                            size_str = f"{file_size}B"
                        print(f"    {file_path.name} ({size_str})")
            
            # 展示主要功能
            print("\n5. RQ2主要功能:")
            print("    自然度评估 - 使用规则或LLM评估文本自然度")
            print("    标注准确性 - 检查实体边界和类型准确性")
            print("    语义一致性 - 评估实体标注的语义合理性")
            print("    多样性分析 - 词汇、句子、实体等多维度多样性")
            print("    平衡性评估 - 与目标分布的匹配程度")
            print("    实体质量 - 深入分析实体标注质量")
            print("    语言学质量 - 语法、流畅性、连贯性、可读性")
            print("    交叉验证 - 评估质量指标的稳定性")
            print("    可视化报告 - 生成丰富的图表和仪表板")
            
            # 使用建议
            print("\n6. 使用建议:")
            print("    对于大规模数据集，建议使用采样评估以提高效率")
            print("    自然度评估可以配置LLM API以获得更准确的结果")
            print("    可以通过配置文件自定义评估指标和阈值")
            print("    生成的可视化图表有助于直观理解数据质量")
            print("    建议结合RQ1统计分析和RQ3迭代分析获得全面视角")
            
            print(f"\n RQ2演示完成！详细结果请查看: {output_dir}")
            
        else:
            print(f"    质量评估失败: {result.stderr}")
            
    except Exception as e:
        print(f"    运行质量评估时发生异常: {e}")

def show_rq2_capabilities():
    """展示RQ2的核心能力"""
    print("\n" + "=" * 80)
    print("RQ2: 生成数据质量评估 - 核心能力")
    print("=" * 80)
    
    capabilities = {
        "1. 多维度质量评估": [
            "自然度评估 - 文本的自然程度和可读性",
            "准确性评估 - 实体标注的边界和类型准确性",
            "一致性评估 - 语义和类型标注的一致性",
            "多样性评估 - 词汇、句子、实体等多样性",
            "平衡性评估 - 与目标分布的匹配程度"
        ],
        "2. 深度分析功能": [
            "语言学质量 - 语法、流畅性、连贯性、可读性",
            "实体质量分析 - 实体长度、位置、上下文分析",
            "覆盖率分析 - 实体类型和数量覆盖率",
            "交叉验证 - 质量指标的稳定性和一致性",
            "问题诊断 - 识别和分类数据质量问题"
        ],
        "3. 可视化报告": [
            "质量仪表板 - 综合质量指标概览",
            "雷达图 - 多维度质量对比",
            "分布图 - 质量指标分布分析",
            "趋势图 - 质量改进趋势",
            "问题分析图 - 问题类型和分布"
        ],
        "4. 配置和扩展": [
            "灵活配置 - 通过JSON配置评估参数",
            "模块化设计 - 可选择性运行特定评估",
            "API集成 - 支持LLM API进行高级评估",
            "输出格式 - JSON和文本多种格式输出",
            "批量处理 - 支持大规模数据集评估"
        ]
    }
    
    for category, items in capabilities.items():
        print(f"\n{category}:")
        for item in items:
            print(f"    {item}")
    
    print(f"\n RQ2为数据质量评估提供了全面、深入、可视化的解决方案")

def main():
    """主函数"""
    print(" 开始RQ2质量评估演示...")
    
    # 运行演示
    run_rq2_demo()
    
    # 展示能力
    show_rq2_capabilities()
    
    print("\n" + "=" * 80)
    print(" RQ2质量评估演示完成！")
    print("=" * 80)
    print("\n 更多使用方法:")
    print("   python evaluation/framework/rq2_quality_assessment.py --help")
    print("\n 演示文件位置:")
    print("   evaluation/rq2_demo/")

if __name__ == "__main__":
    main()
