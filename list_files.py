import os

def list_files(directory):
    print(f"\n--- Files in {directory} ---")
    for item in os.listdir(directory):
        item_path = os.path.join(directory, item)
        if os.path.isfile(item_path):
            print(f"File: {item}")
        elif os.path.isdir(item_path):
            print(f"Dir: {item}")

# List files in src/generate
list_files("src/generate")

# List files in src/generate/step_wise
list_files("src/generate/step_wise")
