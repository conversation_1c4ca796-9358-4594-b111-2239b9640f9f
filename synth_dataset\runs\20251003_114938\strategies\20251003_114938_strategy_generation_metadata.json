{"start_time": "2025-10-03T11:49:38.759520", "output_directory": "synth_dataset\\runs\\20251003_114938\\strategies", "target_entity_types": ["姓名", "年龄", "性别", "国籍", "职业", "民族", "教育背景", "婚姻状况", "政治倾向", "家庭成员", "工资数额", "投资产品", "税务记录", "信用记录", "实体资产", "交易信息", "疾病", "药物", "临床表现", "医疗程序", "过敏信息", "生育信息", "地理位置", "行程信息"], "total_entity_types": 24, "generation_config": {"dataset_path": "format-dataset\\privacy_bench_small.json", "target_count": 100, "max_iterations": 10, "batch_size": 10, "distribution_threshold": 0.05, "generation_features": {"use_sentence_diversity": true, "use_entity_diversity": true, "use_example_sentences": true}}, "strategy_files": {"target_distribution": "synth_dataset\\runs\\20251003_114938\\strategies\\entity_target\\privacy_bench_target.json", "sentence_diversity": "synth_dataset\\runs\\20251003_114938\\strategies\\sen_diversity\\sen_diversify_value.json", "entity_diversity": "synth_dataset\\runs\\20251003_114938\\strategies\\entity_diversity\\entity_diversity_20251003_114938\\entity_diversity.json"}, "generation_statistics": {"sentence_diversity_attributes": 0, "entity_diversity_pools": 0, "target_distribution_size": 0}, "strategy_statistics": {"strategy_file_sizes": {"target_distribution": 521, "sentence_diversity": 572, "entity_diversity": 27987}, "sentence_diversity_stats": {"total_attributes": 1, "attributes": ["sen_diversify_value"], "total_values": 1, "attribute_value_counts": {"sen_diversify_value": 1}}, "entity_diversity_stats": {"total_entity_types": 24, "entity_type_counts": {"姓名": 25, "年龄": 25, "性别": 25, "国籍": 25, "职业": 25, "民族": 25, "教育背景": 25, "婚姻状况": 25, "政治倾向": 25, "家庭成员": 25, "工资数额": 25, "投资产品": 25, "税务记录": 25, "信用记录": 25, "实体资产": 25, "交易信息": 25, "疾病": 25, "药物": 25, "临床表现": 25, "医疗程序": 25, "过敏信息": 25, "生育信息": 25, "地理位置": 25, "行程信息": 25}, "vanilla_entity_counts": {"姓名": 10, "年龄": 10, "性别": 10, "国籍": 10, "职业": 10, "民族": 10, "教育背景": 10, "婚姻状况": 10, "政治倾向": 10, "家庭成员": 10, "工资数额": 10, "投资产品": 10, "税务记录": 10, "信用记录": 10, "实体资产": 10, "交易信息": 10, "疾病": 10, "药物": 10, "临床表现": 10, "医疗程序": 10, "过敏信息": 10, "生育信息": 10, "地理位置": 10, "行程信息": 10}, "latent_scenario_counts": {"姓名": 3, "年龄": 3, "性别": 3, "国籍": 3, "职业": 3, "民族": 3, "教育背景": 3, "婚姻状况": 3, "政治倾向": 3, "家庭成员": 3, "工资数额": 3, "投资产品": 3, "税务记录": 3, "信用记录": 3, "实体资产": 3, "交易信息": 3, "疾病": 3, "药物": 3, "临床表现": 3, "医疗程序": 3, "过敏信息": 3, "生育信息": 3, "地理位置": 3, "行程信息": 3}, "total_entities": 600}, "target_distribution_stats": {"total_entity_types": 24, "entity_types": ["姓名", "年龄", "性别", "国籍", "职业", "民族", "教育背景", "婚姻状况", "政治倾向", "家庭成员", "工资数额", "投资产品", "税务记录", "信用记录", "实体资产", "交易信息", "疾病", "药物", "临床表现", "医疗程序", "过敏信息", "生育信息", "地理位置", "行程信息"], "target_counts": {"姓名": 84, "年龄": 100, "性别": 100, "国籍": 78, "职业": 59, "民族": 74, "教育背景": 74, "婚姻状况": 100, "政治倾向": 100, "家庭成员": 100, "工资数额": 100, "投资产品": 100, "税务记录": 100, "信用记录": 100, "实体资产": 100, "交易信息": 100, "疾病": 66, "药物": 88, "临床表现": 79, "医疗程序": 73, "过敏信息": 100, "生育信息": 100, "地理位置": 55, "行程信息": 100}, "total_target_count": 2130, "average_target_count": 88.75, "target_count_distribution": {"84": 1, "100": 14, "78": 1, "59": 1, "74": 2, "66": 1, "88": 1, "79": 1, "73": 1, "55": 1}}}}