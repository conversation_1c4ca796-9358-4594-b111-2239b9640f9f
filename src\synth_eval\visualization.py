# visualization.py
# 评估结果可视化模块

import json
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from typing import Dict, List, Any
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_distribution_comparison_plot(
    actual_dist: Dict[str, Dict[str, Any]], 
    target_dist: Dict[str, float],
    original_dist: Dict[str, Dict[str, Any]] = None,
    output_path: str = "distribution_comparison.png"
):
    """创建分布对比图 - 支持原数据集、最终数据集和目标分布的三重对比，动态调整尺寸"""
    
    # 获取所有实体类型
    all_entity_types = set(actual_dist.keys())
    if original_dist:
        all_entity_types.update(original_dist.keys())
    all_entity_types.update(target_dist.keys())
    all_entity_types = sorted(list(all_entity_types))
    
    num_entities = len(all_entity_types)
    
    # 动态调整图表尺寸
    # 基础宽度16，每个实体增加0.6英寸，最小16英寸，最大30英寸
    width = max(16, min(30, 16 + (num_entities - 10) * 0.6))
    # 增加高度以容纳标注
    height = max(10, min(14, 10 + (num_entities - 10) * 0.2))
    
    fig, ax = plt.subplots(1, 1, figsize=(width, height))
    
    # 准备数据
    x = np.arange(len(all_entity_types))
    bar_width = 0.8
    
    # 获取数据
    original_counts = [original_dist.get(et, {}).get("count", 0) if original_dist else 0 for et in all_entity_types]
    final_counts = [actual_dist.get(et, {}).get("count", 0) for et in all_entity_types]
    target_counts = [target_dist.get(et, 0) for et in all_entity_types]
    
    # 使用堆叠柱状图
    if original_dist:
        # 底部：原数据集
        bars1 = ax.bar(x, original_counts, bar_width, 
                      label='原数据集', alpha=0.7, color='lightblue', edgecolor='navy', linewidth=0.8)
        
        # 计算增量
        incremental_counts = [final - orig for final, orig in zip(final_counts, original_counts)]
        positive_increments = [max(0, inc) for inc in incremental_counts]
        negative_increments = [min(0, inc) for inc in incremental_counts]
        
        # 正增量（绿色）
        bars2 = ax.bar(x, positive_increments, bar_width, 
                      bottom=original_counts, label='生成增量', 
                      alpha=0.8, color='lightgreen', edgecolor='darkgreen', linewidth=0.8)
        
        # 负增量（红色）
        negative_bottoms = [orig + neg for orig, neg in zip(original_counts, negative_increments)]
        bars3 = ax.bar(x, [-neg for neg in negative_increments], bar_width,
                      bottom=negative_bottoms, label='生成不足',
                      alpha=0.8, color='lightcoral', edgecolor='darkred', linewidth=0.8)
    else:
        # 只显示最终数据集
        bars2 = ax.bar(x, final_counts, bar_width, 
                      label='最终数据集', alpha=0.8, color='lightcoral', edgecolor='darkred', linewidth=0.8)
    
    # 目标分布（虚线）
    ax.plot(x, target_counts, 'k--', linewidth=2, marker='o', markersize=6, 
           markerfacecolor='gold', markeredgecolor='black', markeredgewidth=1.5, 
           label='目标分布', zorder=10)
    
    # 智能添加数值标签
    max_count = max(max(final_counts), max(target_counts), max(original_counts))
    y_max = max_count * 1.25  # 增加顶部空间
    
    # 计算标签位置
    label_positions = {}  # 记录每个x位置的已使用y坐标
    
    def find_label_position(x_pos, y_val, min_gap=max_count*0.08):
        """为标签找到合适的位置，避免重叠"""
        if x_pos not in label_positions:
            label_positions[x_pos] = []
        
        y_pos = y_val + max_count * 0.03
        while any(abs(y - y_pos) < min_gap for y in label_positions[x_pos]):
            y_pos += min_gap
        
        label_positions[x_pos].append(y_pos)
        return y_pos
    
    # 添加标签
    for i, (final_count, target_count) in enumerate(zip(final_counts, target_counts)):
        # 目标值标签
        if target_count > 0:
            y_pos = find_label_position(i, target_count)
            ax.text(i, y_pos, f'{int(target_count)}', 
                   ha='center', va='bottom', fontsize=9, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.2', facecolor='gold', alpha=0.7))
        
        # 实际值标签（如果与目标值有显著差异）
        if target_count > 0:
            deviation_ratio = abs(final_count - target_count) / target_count
            if deviation_ratio > 0.1:  # 偏差超过10%
                color = 'red' if final_count < target_count else 'green'
                y_pos = find_label_position(i, final_count)
                ax.text(i, y_pos, f'{int(final_count)}', 
                       ha='center', va='bottom', fontsize=8, fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))
    
    # 设置图表属性
    ax.set_xlabel('实体类型', fontsize=12, fontweight='bold')
    ax.set_ylabel('实体数量', fontsize=12, fontweight='bold')
    ax.set_title('实体分布对比', fontsize=14, fontweight='bold')
    
    # 动态调整标签显示
    ax.set_xticks(x)
    if num_entities <= 15:
        ax.set_xticklabels(all_entity_types, rotation=45, ha='right', fontsize=10)
    else:
        ax.set_xticklabels(all_entity_types, rotation=90, ha='center', fontsize=9)
    
    # 设置图例
    ax.legend(loc='upper right', bbox_to_anchor=(1, 1), fontsize=10)
    
    # 网格设置
    ax.grid(True, alpha=0.3, axis='y', linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)
    
    # Y轴范围调整
    ax.set_ylim(0, y_max)
    
    # 添加统计信息文本框
    if original_dist:
        total_original = sum(original_counts)
        total_final = sum(final_counts) 
        total_target = sum(target_counts)
        
        stats_text = (f'总计:\n'
                     f'原始: {total_original}\n'
                     f'目标: {total_target}\n'
                     f'最终: {total_final}')
        # 将统计信息移到右下角
        ax.text(0.98, 0.02, stats_text, transform=ax.transAxes, fontsize=9,
               ha='right', va='bottom', 
               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # 调整布局
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.2)  # 为旋转的标签留出更多空间
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"实体分布对比图已保存到：{output_path}")
    print(f"图表尺寸: {width:.1f} x {height:.1f} 英寸，适配 {num_entities} 个实体类型")

def create_diversity_comparison_plot(
    generated_diversity_scores: Dict[str, float],
    original_diversity_scores: Dict[str, float] = None,
    output_path: str = "diversity_comparison.png"
):
    """创建多样性对比图 - 显示原数据集vs生成数据集的多样性对比"""
    # 增加图表高度以容纳标注
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 获取所有多样性维度
    all_dimensions = set(generated_diversity_scores.keys())
    if original_diversity_scores:
        all_dimensions.update(original_diversity_scores.keys())
    all_dimensions = sorted(list(all_dimensions))
    
    # 准备数据
    x = np.arange(len(all_dimensions))
    width = 0.35
    
    # 生成数据集多样性
    generated_values = [generated_diversity_scores.get(dim, 0) for dim in all_dimensions]
    bars1 = ax.bar(x + width/2, generated_values, width, label='生成数据集', alpha=0.8, color='lightcoral')
    
    # 原数据集多样性（如果提供）
    if original_diversity_scores:
        original_values = [original_diversity_scores.get(dim, 0) for dim in all_dimensions]
        bars2 = ax.bar(x - width/2, original_values, width, label='原数据集', alpha=0.8, color='lightblue')
    
    # 计算标签位置
    label_positions = {}  # 记录每个x位置的已使用y坐标
    
    def find_label_position(x_pos, y_val, min_gap=0.05):
        """为标签找到合适的位置，避免重叠"""
        if x_pos not in label_positions:
            label_positions[x_pos] = []
        
        y_pos = y_val + 0.02
        while any(abs(y - y_pos) < min_gap for y in label_positions[x_pos]):
            y_pos += min_gap
        
        label_positions[x_pos].append(y_pos)
        return y_pos
    
    # 添加数值标签和改进指示
    for i, (gen_val, dim) in enumerate(zip(generated_values, all_dimensions)):
        if gen_val > 0:
            # 生成数据集标签
            y_pos = find_label_position(i, gen_val)
            ax.text(i + width/2, y_pos, f'{gen_val:.3f}', 
                   ha='center', va='bottom', fontsize=10)
        
        if original_diversity_scores:
            orig_val = original_values[i]
            if orig_val > 0:
                # 原数据集标签
                y_pos = find_label_position(i, orig_val)
                ax.text(i - width/2, y_pos, f'{orig_val:.3f}', 
                       ha='center', va='bottom', fontsize=10)
            
            # 改进指示
            if orig_val > 0 and gen_val > 0:
                improvement = gen_val - orig_val
                color = 'green' if improvement > 0 else 'red' if improvement < 0 else 'gray'
                symbol = '↑' if improvement > 0 else '↓' if improvement < 0 else '='
                y_pos = find_label_position(i, max(orig_val, gen_val))
                ax.text(i, y_pos, f'{symbol}{abs(improvement):.3f}', 
                       ha='center', va='bottom', color=color, fontweight='bold', fontsize=10)
    
    # 设置图表属性
    ax.set_ylabel('多样性得分', fontsize=12, fontweight='bold')
    ax.set_title('多样性评估对比', fontsize=14, fontweight='bold')
    
    # 设置x轴
    ax.set_xticks(x)
    ax.set_xticklabels(all_dimensions, rotation=45, ha='right', fontsize=10)
    
    # 设置y轴范围（留出足够空间给标签）
    y_max = max(max(generated_values), max(original_values) if original_diversity_scores else 0)
    ax.set_ylim(0, y_max * 1.25)
    
    # 添加网格线
    ax.grid(True, axis='y', alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)
    
    # 设置图例
    ax.legend(loc='upper right', bbox_to_anchor=(1, 1))
    
    # 调整布局
    plt.tight_layout()
    plt.subplots_adjust(bottom=0.15)  # 为旋转的标签留出空间
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"多样性对比图已保存到：{output_path}")

# 保留原函数名作为兼容性别名
def create_diversity_radar_plot(diversity_scores: Dict[str, float], output_path: str = "diversity_radar.png"):
    """兼容性函数 - 调用新的对比图函数"""
    create_diversity_comparison_plot(diversity_scores, None, output_path)

def create_entity_diversity_heatmap(
    entity_diversity: Dict[str, float],
    output_path: str = "entity_diversity_heatmap.png"
):
    """创建实体多样性热力图"""
    entity_types = list(entity_diversity.keys())
    diversity_values = list(entity_diversity.values())
    
    # 创建数据矩阵
    data_matrix = np.array(diversity_values).reshape(1, -1)
    
    fig, ax = plt.subplots(figsize=(12, 4))
    
    sns.heatmap(
        data_matrix, 
        annot=True, 
        fmt='.3f',
        xticklabels=entity_types,
        yticklabels=['多样性得分'],
        cmap='YlOrRd',
        cbar_kws={'label': '多样性得分'}
    )
    
    ax.set_title('各实体类型多样性得分')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"实体多样性热力图已保存到：{output_path}")

def create_balance_comparison_plot(
    generated_balance_metrics: Dict[str, float],
    original_balance_metrics: Dict[str, float] = None,
    output_path: str = "balance_comparison.png"
):
    """创建均衡性对比图 - 只展示整体得分"""
    fig, ax = plt.subplots(figsize=(8, 6))
    
    # 获取整体得分
    generated_score = generated_balance_metrics.get("overall_score", 0)
    original_score = original_balance_metrics.get("overall_score", 0) if original_balance_metrics else 0
    
    # 设置柱状图位置
    x = np.array([0])  # 只有一个位置
    width = 0.35
    
    # 绘制柱状图
    if original_balance_metrics:
        ax.bar(x - width/2, [original_score], width, label='原数据集', color='lightblue', alpha=0.8)
        ax.bar(x + width/2, [generated_score], width, label='生成数据集', color='lightcoral', alpha=0.8)
        
        # 添加改进指示
        improvement = generated_score - original_score
        color = 'green' if improvement > 0 else 'red' if improvement < 0 else 'gray'
        symbol = '↑' if improvement > 0 else '↓' if improvement < 0 else '='
        ax.text(x[0], max(original_score, generated_score) + 0.05, 
                f'{symbol}{abs(improvement):.3f}', 
                ha='center', va='bottom', color=color, fontweight='bold', fontsize=12)
    else:
        ax.bar(x, [generated_score], width, label='生成数据集', color='lightcoral', alpha=0.8)
    
    # 添加数值标签
    def add_value_labels(x, values, offset=0):
        for i, v in enumerate(values):
            ax.text(x[i], v + 0.01, f'{v:.3f}', ha='center', va='bottom', fontsize=10)
    
    if original_balance_metrics:
        add_value_labels(x - width/2, [original_score])
        add_value_labels(x + width/2, [generated_score])
    else:
        add_value_labels(x, [generated_score])
    
    # 设置图表属性
    ax.set_ylabel('均衡性得分', fontsize=12, fontweight='bold')
    ax.set_title('数据集均衡性评估', fontsize=14, fontweight='bold')
    
    # 设置x轴
    ax.set_xticks(x)
    ax.set_xticklabels(['整体得分'])
    
    # 设置y轴范围
    ax.set_ylim(0, 1.1)
    
    # 添加网格线
    ax.grid(True, axis='y', alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)
    
    # 设置图例
    if original_balance_metrics:
        ax.legend(loc='upper right')
    
    # 调整布局
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"均衡性对比图已保存到：{output_path}")

def create_text_length_distribution_plot(
    texts: List[str],
    output_path: str = "text_length_distribution.png"
):
    """创建文本长度分布图"""
    lengths = [len(text) for text in texts]
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 直方图
    ax1.hist(lengths, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.set_title('文本长度分布')
    ax1.set_xlabel('文本长度（字符数）')
    ax1.set_ylabel('频次')
    ax1.axvline(np.mean(lengths), color='red', linestyle='--', label=f'平均值: {np.mean(lengths):.1f}')
    ax1.legend()
    
    # 箱线图
    ax2.boxplot(lengths, patch_artist=True, boxprops=dict(facecolor='lightgreen'))
    ax2.set_title('文本长度箱线图')
    ax2.set_ylabel('文本长度（字符数）')
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"文本长度分布图已保存到：{output_path}")

def create_comprehensive_report(
    evaluation_result: Dict[str, Any],
    output_dir: str = "evaluation_plots",
    original_evaluation_result: Dict[str, Any] = None
):
    """创建综合评估报告，包含所有可视化图表"""
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. 创建分布对比图
    if "balance_evaluation" in evaluation_result:
        balance_eval = evaluation_result["balance_evaluation"]
        original_balance = original_evaluation_result["balance_evaluation"] if original_evaluation_result else None
    
        # 分布对比图
        create_distribution_comparison_plot(
        balance_eval["actual_distribution"],
        balance_eval["target_distribution"],
            original_balance["actual_distribution"] if original_balance else None,
        os.path.join(output_dir, "distribution_comparison.png")
    )
    
        # 均衡性对比图
        create_balance_comparison_plot(
            balance_eval,
            original_balance,
            os.path.join(output_dir, "balance_comparison.png")
        )
    
    # 2. 创建多样性相关图表
    if "diversity_metrics" in evaluation_result:
        diversity_metrics = evaluation_result["diversity_metrics"]
        original_diversity = original_evaluation_result["diversity_metrics"] if original_evaluation_result else None
        
        # 多样性对比图
        create_diversity_comparison_plot(
            diversity_metrics,
            original_diversity,
            os.path.join(output_dir, "diversity_comparison.png")
        )
        
        # 多样性雷达图
        create_diversity_radar_plot(
                diversity_metrics,
            os.path.join(output_dir, "diversity_radar.png")
        )
    
    # 实体多样性热力图
    if "entity_diversity" in evaluation_result:
        create_entity_diversity_heatmap(
            evaluation_result["entity_diversity"],
            os.path.join(output_dir, "entity_diversity_heatmap.png")
        )
    
    # 3. 生成HTML报告
    generate_html_report(evaluation_result, os.path.join(output_dir, "evaluation_report.html"))
    
    print(f"[✓] 评估报告已生成到目录：{output_dir}")
    print(f"  - 实体分布对比图: distribution_comparison.png")
    print(f"  - 均衡性对比图: balance_comparison.png")
    print(f"  - 多样性对比图: diversity_comparison.png")
    print(f"  - 多样性雷达图: diversity_radar.png")
    if "entity_diversity" in evaluation_result:
        print(f"  - 实体多样性热力图: entity_diversity_heatmap.png")
    print(f"  - HTML报告: evaluation_report.html")

def generate_html_report(
    evaluation_result: Dict[str, Any],
    output_path: str = "evaluation_report.html"
):
    """生成HTML格式的评估报告"""
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>NER数据集质量评估报告</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
            .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
            .metric {{ display: inline-block; margin: 10px; padding: 10px; background-color: #f9f9f9; border-radius: 3px; }}
            .passed {{ color: green; font-weight: bold; }}
            .failed {{ color: red; font-weight: bold; }}
            table {{ border-collapse: collapse; width: 100%; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>NER数据集质量评估报告</h1>
            <p>评估时间：{evaluation_result['evaluation_time']}</p>
            <p>整体评估结果：<span class="{'passed' if evaluation_result['overall_passed'] else 'failed'}">
                {'通过' if evaluation_result['overall_passed'] else '未通过'}
            </span></p>
        </div>
        
        <div class="section">
            <h2>数据集信息</h2>
            <div class="metric">总记录数：{evaluation_result['dataset_info']['total_records']}</div>
            <div class="metric">总实体数：{evaluation_result['dataset_info']['total_entities']}</div>
            <div class="metric">实体类型数：{len(evaluation_result['dataset_info']['entity_types'])}</div>
        </div>
        
        <div class="section">
            <h2>均衡性验证</h2>
            <p>验证结果：<span class="{'passed' if evaluation_result['balance_evaluation']['passed'] else 'failed'}">
                {'通过' if evaluation_result['balance_evaluation']['passed'] else '未通过'}
            </span></p>
            <div class="metric">整体得分：{evaluation_result['balance_evaluation']['overall_score']:.3f}</div>
            <div class="metric">覆盖率：{evaluation_result['balance_evaluation']['coverage_ratio']:.3f}</div>
        </div>
        
        <div class="section">
            <h2>多样性验证</h2>
            <p>验证结果：<span class="{'passed' if evaluation_result['diversity_evaluation']['passed'] else 'failed'}">
                {'通过' if evaluation_result['diversity_evaluation']['passed'] else '未通过'}
            </span></p>
            <div class="metric">词汇多样性：{evaluation_result['diversity_evaluation']['vocabulary_diversity']:.3f}</div>
            <div class="metric">句法多样性：{evaluation_result['diversity_evaluation']['syntactic_diversity']:.3f}</div>
            <div class="metric">语义多样性：{evaluation_result['diversity_evaluation']['semantic_diversity']:.3f}</div>
            <div class="metric">上下文多样性：{evaluation_result['diversity_evaluation']['context_diversity']:.3f}</div>
            <div class="metric">实体多样性：{evaluation_result['diversity_evaluation']['entity_diversity']['overall']:.3f}</div>
        </div>
    </body>
    </html>
    """
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"HTML评估报告已保存到：{output_path}")

if __name__ == "__main__":
    # 测试可视化功能
    print("可视化模块测试完成") 