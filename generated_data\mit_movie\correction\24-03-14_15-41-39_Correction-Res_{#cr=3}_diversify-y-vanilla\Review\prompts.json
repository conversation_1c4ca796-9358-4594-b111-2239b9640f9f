{"prompts": ["Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a {{superior}} suspenseful movie for all ages\"\nText Span: \"superior\"\n\n2. Query: \"What movie released in 2028 has the {{highest viewers' rating}}?\"\nText Span: \"highest viewers' rating\"\n\n3. Query: \"can you recommend a film with a {{great plot twist}}\"\nText Span: \"great plot twist\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the {{best}} environmental disaster movie of all time?\"\nText Span: \"best\"\n\n2. Query: \"What is a {{phenomenal}} movie with Jennifer Lawrence as the main character\"\nText Span: \"phenomenal\"\n\n3. Query: \"Is the movie Grease considered a {{classic}}?\"\nText Span: \"classic\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is considered the {{greatest}} movie featuring Natalie Dormer?\"\nText Span: \"greatest\"\n\n2. Query: \"Can you recommend a movie with {{astonishing visuals}} and a strong mystery plot?\"\nText Span: \"astonishing visuals\"\n\n3. Query: \"Can you recommend a {{gripping}} thriller film from the 1990s?\"\nText Span: \"gripping\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n2. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"I'm looking for a film directed by Alfred Hitchcock and {{worth watching}}\"\nText Span: \"worth watching\"\n\n2. Query: \"I'm looking for the {{highest rated drama film}} of all time\"\nText Span: \"highest rated drama film\"\n\n3. Query: \"could you tell me about any {{decent}} movies from the 90s\"\nText Span: \"decent\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"I'm looking for a movie with {{Dynamic performances}} and a viewers' rating of at least 7.5/10 stars.\"\nText Span: \"Dynamic performances\"\n\n2. Query: \"What is the {{best action movie}} of the 1990s with a killer soundtrack?\"\nText Span: \"best action movie\"\n\n3. Query: \"What {{crowd-pleaser}} movies from the 90s are worth watching?\"\nText Span: \"crowd-pleaser\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n2. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What year did Stanley Donen direct the {{unforgettable}} movie with Cary Grant and Audrey Hepburn?\"\nText Span: \"unforgettable\"\n\n2. Query: \"Can you recommend a drama film from 1998 with a {{satisfying conclusion}}?\"\nText Span: \"satisfying conclusion\"\n\n3. Query: \"what are some survival movies with {{high viewers' ratings}}\"\nText Span: \"high viewers' ratings\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is a {{deeply moving}} film directed by Jordan Peele?\"\nText Span: \"deeply moving\"\n\n2. Query: \"What are some {{captivating}} movies released between 2008-2010\"\nText Span: \"captivating\"\n\n3. Query: \"Is the movie The Shawshank Redemption considered {{stale}} by viewers?\"\nText Span: \"stale\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Show me a movie with {{Heartwarming moments}} starring Finn and a Viewers' Rating of 90% or higher\"\nText Span: \"Heartwarming moments\"\n\n2. Query: \"Please recommend a movie with a TV-Y7 rating that has an {{amazing plot}}\"\nText Span: \"amazing plot\"\n\n3. Query: \"Could you recommend an {{awe-inspiring}} gangster film directed by Orson Welles?\"\nText Span: \"awe-inspiring\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n2. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What are some of the most powerful and impactful movies of the 21st century according to {{AA}}?\"\nText Span: \"AA\"\n\n2. Query: \"What did the critics say about the {{mediocre}} movie last week?\"\nText Span: \"mediocre\"\n\n3. Query: \"What movie did Martin Scorsese direct that is known for its {{stellar}} special effects?\"\nText Span: \"stellar\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a movie with {{Compelling performances}} and a TV-14 rating?\"\nText Span: \"Compelling performances\"\n\n2. Query: \"I heard that a movie called American Son with Kerry Washington has {{phenomenal writing}}. Can you tell me more about it?\"\nText Span: \"phenomenal writing\"\n\n3. Query: \"Is the film Alfredo Rodriguez directed {{unwatchable or worth seeing}}?\"\nText Span: \"unwatchable or worth seeing\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"I'm looking for the final trailer of the most {{displeasing}} horror movie\"\nText Span: \"displeasing\"\n\n2. Query: \"Which movie directed by Luca Guadagnino is known for its {{razor-sharp dialogue}}?\"\nText Span: \"razor-sharp dialogue\"\n\n3. Query: \"Are there any movies starring Kevin Spacey that are considered {{a standout}} in the drama genre?\"\nText Span: \"a standout\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n2. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Which film, directed by Christopher Nolan, is rated R and received {{high viewers' rating}}?\"\nText Span: \"high viewers' rating\"\n\n2. Query: \"Show me an extended preview of the new action movie with a {{disappointing}} viewers' rating\"\nText Span: \"disappointing\"\n\n3. Query: \"What is the {{highest-rated}} comedy film featuring Ron Weasley as a character?\"\nText Span: \"highest-rated\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"I'm looking for {{A gem}} of a movie with perfect pacing, can you suggest one?\"\nText Span: \"A gem\"\n\n2. Query: \"Which movie is considered the most {{awesome}} of all time?\"\nText Span: \"awesome\"\n\n3. Query: \"Can I have a {{first look}} at the teaser for a wilderness survival film from 1972?\"\nText Span: \"first look\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n2. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What movie directed by Christopher Nolan has the {{best viewers' rating}}?\"\nText Span: \"best viewers' rating\"\n\n2. Query: \"Can you recommend a movie with {{gripping suspense}}?\"\nText Span: \"gripping suspense\"\n\n3. Query: \"can you recommend a movie with a {{powerful message}}\"\nText Span: \"powerful message\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n2. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What film in The Twilight Saga has the {{best}} viewers' rating?\"\nText Span: \"best\"\n\n2. Query: \"What movie directed by Kerry Washington was {{Critically acclaimed}}?\"\nText Span: \"Critically acclaimed\"\n\n3. Query: \"Can you recommend a movie with an {{Outstanding ensemble cast}}?\"\nText Span: \"Outstanding ensemble cast\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you recommend a movie featuring Al Pacino that showcases his {{acting highlights}}?\"\nText Span: \"acting highlights\"\n\n2. Query: \"Can you recommend any movies with {{Astonishing visuals}}?\"\nText Span: \"Astonishing visuals\"\n\n3. Query: \"What is the {{best movie}} with Captain Kirk as a character?\"\nText Span: \"best movie\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the {{1/10 rating}} movie that I should avoid at all costs?\"\nText Span: \"1/10 rating\"\n\n2. Query: \"Can you recommend a TV-PG movie with an {{Awe-inducing}} plot and a bitter rivalry?\"\nText Span: \"Awe-inducing\"\n\n3. Query: \"What movie with a mind-bending plot has the {{best viewers' rating}}?\"\nText Span: \"best viewers' rating\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"I'm looking for a movie with an {{Affecting}} storyline and a princess rescue plot.\"\nText Span: \"Affecting\"\n\n2. Query: \"can you show me the teaser trailer for the movie with {{heartfelt performances}}\"\nText Span: \"heartfelt performances\"\n\n3. Query: \"Show me a {{Remarkable}} movie featuring the song 'Belle'.\"\nText Span: \"Remarkable\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Is Raging Bull considered a {{classic}} movie? What's it about?\"\nText Span: \"classic\"\n\n2. Query: \"What are some {{raw and authentic}} films directed by Clint Eastwood?\"\nText Span: \"raw and authentic\"\n\n3. Query: \"Can you recommend a movie with {{rich and layered storytelling}}?\"\nText Span: \"rich and layered storytelling\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"{{Not recommended}} movie with a high viewers' rating from the 1990s\"\nText Span: \"Not recommended\"\n\n2. Query: \"can you show me a {{multi-layered}} movie promo with jennifer garner and will turner\"\nText Span: \"multi-layered\"\n\n3. Query: \"Can you recommend a movie with {{stunning costume design}}?\"\nText Span: \"stunning costume design\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Is there a specific year that the {{best science fiction movie}} was released?\"\nText Span: \"best science fiction movie\"\n\n2. Query: \"Can you recommend any movies with {{Remarkable acting}} by Tilda Swinton?\"\nText Span: \"Remarkable acting\"\n\n3. Query: \"What is the plot of the {{greatest}} dreams-related movie of all time?\"\nText Span: \"greatest\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n2. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What is the {{most extraordinary}} film ever made?\"\nText Span: \"most extraordinary\"\n\n2. Query: \"Who directed the {{top action movie}} of the 1990s?\"\nText Span: \"top action movie\"\n\n3. Query: \"can you recommend a {{captivating}} movie directed by Céline Sciamma\"\nText Span: \"captivating\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n2. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What {{outstanding}} movies has Martin Riggs acted in?\"\nText Span: \"outstanding\"\n\n2. Query: \"What is considered the {{greatest}} movie directed by Rob Reiner\"\nText Span: \"greatest\"\n\n3. Query: \"What year did the director of the action-packed movie 'Die Hard' win an award for {{best director}}?\"\nText Span: \"best director\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n2. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"Can you suggest a classic movie from the 1950s with a {{captivating}} plot and strong character development?\"\nText Span: \"captivating\"\n\n2. Query: \"Which movie featuring a family feud has the {{highest viewers' rating}}\"\nText Span: \"highest viewers' rating\"\n\n3. Query: \"Can you recommend a {{resonant}} movie with a powerful message and a Western theme?\"\nText Span: \"resonant\"", "Here are 3 spoken queries to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for each query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n2. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n\n---\n\n\nPlease classify the following 3 spans of text:\n\n1. Query: \"What are some {{unforgettable}} psychological thriller movies?\"\nText Span: \"unforgettable\"\n\n2. Query: \"recommend a movie with a {{breathtaking}} plot\"\nText Span: \"breathtaking\"\n\n3. Query: \"What year was the movie The Breakfast Club released and what is its {{viewers' rating}}?\"\nText Span: \"viewers' rating\"", "Here is a spoken query to a dialogue system about movies. Your task is to classify whether the span of text is a named entity of type Review.\nIn particular, for the given query with a span of text enclosed in double braces, please classify the enclosed span of text in context of the query into one of the following categories:\n- (A). The span is a named entity of type review\n- (B). The span contains a named review entity but the span boundary is not precise\n- (C). The span is a named entity but the category is not review\n- (D). The span is not a named entity\nIf the label is (B), you should provide the correct span boundary.\nIf the label is (C), you should provide the correct entity type. Pick an entity type from the following list:\n[Title, Viewers' Rating, Year, Genre, Director, MPAA Rating, Plot, Actor, Trailer, Song, Character, other].\n\nA named review entity must be a recognition or a detailed, qualitative assessment of a movie, such as \"funniest of all time\" and \"incredible visual effects\". Named review entities must describe specific aspects of a movie, such as \"most awards\".\nEnsure to distinguish named review entities from named viewers' rating entities. Short evaluative descriptors such as \"good\", \"highest rated\" and \"top-rated\" are named viewers' rating entities. Performance metrics such as \"highest-grossing\" and \"best-rated\" are also named viewers' rating entities.\nAmbiguous identifiers such as \"review\" are not named entities.\n\nHere are some example span classifications for your reference. Please follow this format.\n\nExamples:\n\n1. Query: \"Which movie has the {{best special effects}} of all time\"\nText Span: \"best special effects\"\nLabel: (A). Correct Entity Annotation.\n\n2. Query: \"Can you recommend a {{good}} movie from the 1980s\"\nText Span: \"good\"\nLabel: (C). Wrong Type. The correct entity type is Viewers' Rating.\n\n\n---\n\n\nPlease classify the following span of text:\n\nQuery: \"Who directed the movie that stars Frankie Dunn and is considered {{breathtaking}} by viewers?\"\nText Span: \"breathtaking\""]}