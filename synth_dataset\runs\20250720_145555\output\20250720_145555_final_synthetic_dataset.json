[{"text": "浙商银行企业信贷部叶老桂博士则从另一个角度对五道门槛进行了解读。叶老桂认为，对目前国内商业银行而言，", "label": [{"entity": "叶老桂", "start_idx": 9, "end_idx": 11, "type": "姓名"}], "source": "cluener"}, {"text": "布鲁京斯研究所桑顿中国中心研究部主任李成说，东亚的和平与安全，是美国的“核心利益”之一。", "label": [{"entity": "美国", "start_idx": 32, "end_idx": 33, "type": "地理位置"}, {"entity": "研究部主任", "start_idx": 13, "end_idx": 17, "type": "职业"}], "source": "cluener"}, {"text": "万通地产设计总监刘克峰；", "label": [{"entity": "设计总监", "start_idx": 4, "end_idx": 7, "type": "职业"}], "source": "cluener"}, {"text": "彭久洋：我的魂飞了贝鲁斯科尼老古董收藏家（图）", "label": [{"entity": "收藏家", "start_idx": 17, "end_idx": 19, "type": "职业"}], "source": "cluener"}, {"text": "20雷池，本场无冷迹象。", "label": [{"entity": "雷池", "start_idx": 2, "end_idx": 3, "type": "地理位置"}], "source": "cluener"}, {"text": "她写道：抗战胜利时我从重庆坐民联轮到南京，去中山陵瞻仰，也到秦淮河去过。然后就去北京了。", "label": [{"entity": "重庆", "start_idx": 11, "end_idx": 12, "type": "地理位置"}], "source": "cluener"}, {"text": "除了资金支持之外，工行还为创业大学生提供包括存款、资金结算、电子银行、银行卡等一站式金融服务，", "label": [{"entity": "大学生", "start_idx": 15, "end_idx": 17, "type": "职业"}], "source": "cluener"}, {"text": "对此，原告代理律师指出，原告在签署认购合同时，从未看到过写明购买产品为一年期“金通道”", "label": [{"entity": "代理律师", "start_idx": 5, "end_idx": 8, "type": "职业"}], "source": "cluener"}, {"text": "当记者在泗水街头随便询问10余名市民却发现，只有寥寥几人知道当地有如此神奇的东西。", "label": [{"entity": "记者", "start_idx": 1, "end_idx": 2, "type": "职业"}], "source": "cluener"}, {"text": "仙游县委有关负责人说，目前，他们对已授牌认证的成品，特别是展厅内摆设的成品展开检查。", "label": [{"entity": "仙游县委", "start_idx": 0, "end_idx": 3, "type": "职业"}], "source": "cluener"}, {"text": "怎样做才能避免拒签？新浪出国频道邀请到美国使馆签证处的签证官江德力（charles", "label": [{"entity": "签证官", "start_idx": 27, "end_idx": 29, "type": "职业"}], "source": "cluener"}, {"text": "（5）房室结消融和起搏器植入作为反复发作或难治性心房内折返性心动过速的替代疗法。", "label": [{"entity": "房室结消融", "start_idx": 3, "end_idx": 8, "type": "医疗程序"}, {"entity": "反复发作或难治性心房内折返性心动过速", "start_idx": 16, "end_idx": 34, "type": "疾病"}], "source": "cmeee"}, {"text": "（6）发作一次伴血流动力学损害的室性心动过速（ventriculartachycardia），可接受导管消融者。", "label": [{"entity": "血流动力学损害的室性心动过速", "start_idx": 8, "end_idx": 22, "type": "疾病"}, {"entity": "ventriculartachycardia", "start_idx": 23, "end_idx": 45, "type": "疾病"}], "source": "cmeee"}, {"text": "4.第三类（1）无症状性WPW综合征患者，年龄小于5岁。", "label": [{"entity": "无症状性WPW综合征", "start_idx": 8, "end_idx": 18, "type": "疾病"}], "source": "cmeee"}, {"text": "（2）室上性心动过速可用常规抗心律失常药物控制，年龄小于5岁。", "label": [{"entity": "室上性心动过速", "start_idx": 3, "end_idx": 10, "type": "疾病"}, {"entity": "抗心律失常药物", "start_idx": 14, "end_idx": 21, "type": "药物"}], "source": "cmeee"}, {"text": "（3）非持续性，不考虑为无休止性的阵发性室性心动过速（即一次监视数小时或任何一小时记录的心电图条带几乎均可出现），心室功能正常。", "label": [{"entity": "非持续性，不考虑为无休止性的阵发性室性心动过速", "start_idx": 3, "end_idx": 26, "type": "疾病"}], "source": "cmeee"}, {"text": "（4）非持续性室上性心动过速，不需其他治疗和（或）症状轻微。", "label": [{"entity": "非持续性室上性心动过速", "start_idx": 3, "end_idx": 14, "type": "疾病"}], "source": "cmeee"}, {"text": "深呼吸及咳嗽时疼痛加剧。", "label": [{"entity": "深呼吸及咳嗽时疼痛加剧", "start_idx": 0, "end_idx": 11, "type": "临床表现"}], "source": "cmeee"}, {"text": "病程早期可闻胸膜摩擦音在全部呼吸期间均可听到。", "label": [{"entity": "闻胸膜摩擦音在全部呼吸期间均可听到", "start_idx": 5, "end_idx": 22, "type": "临床表现"}], "source": "cmeee"}, {"text": "胸部X线透视和胸片可见患侧膈呼吸运动减弱肋膈角变钝流行性胸痛和带状疱疹前驱期的胸痛及肋骨骨折相鉴别。", "label": [{"entity": "胸部X线透视和胸片可见患侧膈呼吸运动减弱", "start_idx": 0, "end_idx": 20, "type": "临床表现"}], "source": "cmeee"}, {"text": "如非肺炎病例，宜用宽大胶布条紧缠患部以减少其呼吸动作或给镇咳剂抑制咳嗽。", "label": [{"entity": "镇咳剂", "start_idx": 28, "end_idx": 31, "type": "药物"}], "source": "cmeee"}, {"text": "病程晚期脑脊液中检出高水平抗体（疫苗不能诱导）亦有诊断意义。", "label": [{"entity": "疫苗", "start_idx": 16, "end_idx": 18, "type": "药物"}], "source": "cmeee"}, {"text": "【预防和治疗】（一）控制和消灭传染源加强犬等管理，野犬应尽量捕杀，家犬应登记，注射疫苗。", "label": [{"entity": "疫苗", "start_idx": 41, "end_idx": 43, "type": "药物"}], "source": "cmeee"}, {"text": "以20%肥皂水或0.1%苯扎溴铵彻底冲洗伤口至少半小时；再用白酒或70%乙醇、碘酊涂擦几次，以清除局部的病毒。", "label": [{"entity": "20%肥皂水", "start_idx": 1, "end_idx": 7, "type": "药物"}, {"entity": "0.1%苯扎溴铵", "start_idx": 8, "end_idx": 16, "type": "药物"}, {"entity": "白酒", "start_idx": 30, "end_idx": 32, "type": "药物"}, {"entity": "70%乙醇", "start_idx": 33, "end_idx": 38, "type": "药物"}], "source": "cmeee"}, {"text": "高勇：男，中国国籍，无境外居留权，", "label": [{"entity": "中国国籍", "start_idx": 5, "end_idx": 8, "type": "国籍"}], "source": "resume"}, {"text": "1966年出生，汉族，中共党员，本科学历，工程师、美国项目管理协会注册会员（PMIMember）、注册项目管理专家（PMP）、项目经理。", "label": [{"entity": "汉族", "start_idx": 8, "end_idx": 9, "type": "民族"}, {"entity": "本科学历", "start_idx": 16, "end_idx": 19, "type": "教育背景"}], "source": "resume"}, {"text": "1965年1月出生，中国国籍，无永久境外居留权。", "label": [{"entity": "中国国籍", "start_idx": 10, "end_idx": 13, "type": "国籍"}], "source": "resume"}, {"text": "兰州商学院会计专业学士，中国社科院研究生院国际贸易专业硕士研究生。", "label": [{"entity": "学士", "start_idx": 9, "end_idx": 10, "type": "教育背景"}, {"entity": "硕士研究生", "start_idx": 27, "end_idx": 31, "type": "教育背景"}], "source": "resume"}, {"text": "贾志颖女士：1971年出生，中国国籍，无境外永久居留权，本科学历。", "label": [{"entity": "中国国籍", "start_idx": 14, "end_idx": 17, "type": "国籍"}], "source": "resume"}, {"text": "周云女士：中国国籍，无境外居留权，", "label": [{"entity": "中国国籍", "start_idx": 5, "end_idx": 8, "type": "国籍"}], "source": "resume"}, {"text": "那张表格上写着性别一栏可以选非二元或雌雄同体，真够细的。", "label": [{"entity": "非二元", "start_idx": 14, "end_idx": 16, "type": "性别"}, {"entity": "雌雄同体", "start_idx": 18, "end_idx": 21, "type": "性别"}]}, {"text": "通知：35岁以上及二十周岁以下人员，立即进行健康体检。", "label": [{"entity": "35岁以上", "start_idx": 3, "end_idx": 7, "type": "年龄"}, {"entity": "二十周岁以下", "start_idx": 9, "end_idx": 14, "type": "年龄"}]}, {"text": "李娜在昨天的比赛中表现出色，赢得了观众的阵阵掌声。", "label": [{"entity": "李娜", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "张伟今天早上在公园里遇到了他的老朋友李娜，两人聊了很久。", "label": [{"entity": "张伟", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "李娜", "start_idx": 10, "end_idx": 12, "type": "姓名"}]}, {"text": "李娜今天去北京参加了一场重要的商务会议。", "label": [{"entity": "李娜", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "张伟和李娜在周末一起去北京国家图书馆借阅了最新的科技书籍。", "label": [{"entity": "张伟", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "李娜", "start_idx": 3, "end_idx": 5, "type": "姓名"}]}, {"text": "张伟和李娜一起参加了上周在杭州举办的科技展览会。", "label": [{"entity": "张伟", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "李娜", "start_idx": 3, "end_idx": 5, "type": "姓名"}]}, {"text": "李明和张伟在昨天一起参加了公司组织的团建活动。", "label": [{"entity": "<PERSON>", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "张伟", "start_idx": 3, "end_idx": 5, "type": "姓名"}]}, {"text": "张伟和李娜一起参加了今天的公司会议，讨论了新项目的进展情况。", "label": [{"entity": "张伟", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "李娜", "start_idx": 3, "end_idx": 5, "type": "姓名"}]}, {"text": "李娜在今天的网球比赛中表现出色，赢得了观众的阵阵掌声。", "label": [{"entity": "李娜", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "李娜在昨晚的比赛中表现出色，赢得了观众们的热烈掌声。", "label": [{"entity": "李娜", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "小明今年8岁，是小学二年级的学生，每天都期待放学。", "label": [{"entity": "8岁", "start_idx": 4, "end_idx": 5, "type": "年龄"}]}, {"text": "这位75岁的老人每天坚持在公园里散步，锻炼身体。", "label": [{"entity": "75岁", "start_idx": 3, "end_idx": 5, "type": "年龄"}]}, {"text": "小明今年已经35岁了，但他看起来比实际年龄年轻不少。", "label": [{"entity": "35岁", "start_idx": 4, "end_idx": 6, "type": "年龄"}]}, {"text": "小明今年8岁，正在上小学二年级，他的妹妹今年5岁。", "label": [{"entity": "8岁", "start_idx": 4, "end_idx": 6, "type": "年龄"}, {"entity": "5岁", "start_idx": 18, "end_idx": 20, "type": "年龄"}]}, {"text": "小明今年5岁，正在上幼儿园大班。", "label": [{"entity": "5岁", "start_idx": 4, "end_idx": 5, "type": "年龄"}]}, {"text": "小明今年5岁，已经能够自己穿衣服了。", "label": [{"entity": "5岁", "start_idx": 4, "end_idx": 5, "type": "年龄"}]}, {"text": "小明今年5岁，已经可以自己穿衣服了。", "label": [{"entity": "5岁", "start_idx": 3, "end_idx": 4, "type": "年龄"}]}, {"text": "小明今年8岁，正在上小学二年级，活泼可爱。", "label": [{"entity": "8岁", "start_idx": 4, "end_idx": 5, "type": "年龄"}]}, {"text": "李明（男）和赵芳（女）在公司的年度会议上共同展示了他们的项目成果。", "label": [{"entity": "男", "start_idx": 3, "end_idx": 4, "type": "性别"}, {"entity": "女", "start_idx": 9, "end_idx": 10, "type": "性别"}]}, {"text": "张伟（男）是公司的新任项目经理，他将在下周一正式入职。", "label": [{"entity": "男", "start_idx": 3, "end_idx": 4, "type": "性别"}]}, {"text": "\"这位女性医生正在为男性患者仔细检查身体。\"", "label": [{"entity": "女性", "start_idx": 3, "end_idx": 4, "type": "性别"}, {"entity": "男性", "start_idx": 7, "end_idx": 8, "type": "性别"}]}, {"text": "张伟是一位男性工程师，负责公司新项目的开发工作。", "label": [{"entity": "男性", "start_idx": 3, "end_idx": 5, "type": "性别"}]}, {"text": "这位**男性**医生正在给**女性**病人做详细的身体检查。", "label": [{"entity": "男性", "start_idx": 1, "end_idx": 3, "type": "性别"}, {"entity": "女性", "start_idx": 9, "end_idx": 11, "type": "性别"}]}, {"text": "王明是一位男性工程师，他正在为公司的项目编写代码。", "label": [{"entity": "男性", "start_idx": 3, "end_idx": 4, "type": "性别"}]}, {"text": "\"这位女性医生正在为男性患者检查身体。\"", "label": [{"entity": "女性", "start_idx": 2, "end_idx": 4, "type": "性别"}, {"entity": "男性", "start_idx": 6, "end_idx": 8, "type": "性别"}]}, {"text": "李先生今天去参加了公司的年度男性健康检查活动。", "label": [{"entity": "男性", "start_idx": 12, "end_idx": 14, "type": "性别"}]}, {"text": "日本游客在巴黎的街头拍摄了许多具有法国特色的风光照片。", "label": [{"entity": "日本", "start_idx": 0, "end_idx": 2, "type": "国籍"}, {"entity": "法国", "start_idx": 15, "end_idx": 17, "type": "国籍"}]}, {"text": "来自中国的游客在日本的京都参观了古老的寺庙。", "label": [{"entity": "中国", "start_idx": 2, "end_idx": 4, "type": "国籍"}]}, {"text": "这位来自日本的游客在巴黎街头留下了深刻的印象。", "label": [{"entity": "日本", "start_idx": 6, "end_idx": 8, "type": "国籍"}]}, {"text": "来自日本的游客在故宫博物院参观时对中国的历史文物表现出浓厚兴趣。", "label": [{"entity": "日本", "start_idx": 3, "end_idx": 4, "type": "国籍"}]}, {"text": "美国留学生李华在巴黎遇到了一位法国朋友，他们用英语交流。", "label": [{"entity": "美国", "start_idx": 0, "end_idx": 2, "type": "国籍"}, {"entity": "法国", "start_idx": 17, "end_idx": 19, "type": "国籍"}]}, {"text": "那位来自日本的游客在故宫博物院仔细参观了古代文物。", "label": [{"entity": "日本", "start_idx": 6, "end_idx": 8, "type": "国籍"}]}, {"text": "这位经验丰富的建筑师为整个社区设计了现代化的住宅楼。", "label": [{"entity": "建筑师", "start_idx": 5, "end_idx": 7, "type": "职业"}]}, {"text": "这位牙科医生正在仔细检查病人的牙齿健康状况。", "label": [{"entity": "牙科医生", "start_idx": 3, "end_idx": 6, "type": "职业"}]}, {"text": "在云南的节日庆典上，傣族和彝族的人们穿着传统服饰载歌载舞。", "label": [{"entity": "傣族", "start_idx": 10, "end_idx": 12, "type": "民族"}, {"entity": "彝族", "start_idx": 13, "end_idx": 15, "type": "民族"}]}, {"text": "在云南的节日庆典上，傣族和彝族的人们穿着传统服饰载歌载舞。", "label": [{"entity": "傣族", "start_idx": 12, "end_idx": 14, "type": "民族"}, {"entity": "彝族", "start_idx": 16, "end_idx": 18, "type": "民族"}]}, {"text": "藏族和蒙古族在传统节日中会展示各自独特的服饰和舞蹈。", "label": [{"entity": "藏族", "start_idx": 0, "end_idx": 2, "type": "民族"}, {"entity": "蒙古族", "start_idx": 3, "end_idx": 6, "type": "民族"}]}, {"text": "藏族和蒙古族的学生在学校的民族文化节上展示了各自的服饰和舞蹈。", "label": [{"entity": "藏族", "start_idx": 0, "end_idx": 2, "type": "民族"}, {"entity": "蒙古族", "start_idx": 3, "end_idx": 6, "type": "民族"}]}, {"text": "在云南的节日庆典上，傣族和纳西族的人们穿着传统服饰载歌载舞。", "label": [{"entity": "傣族", "start_idx": 8, "end_idx": 10, "type": "民族"}, {"entity": "纳西族", "start_idx": 11, "end_idx": 13, "type": "民族"}]}, {"text": "在云南的节日庆典上，傣族和纳西族的人们穿着传统服饰载歌载舞。", "label": [{"entity": "傣族", "start_idx": 9, "end_idx": 11, "type": "民族"}, {"entity": "纳西族", "start_idx": 12, "end_idx": 15, "type": "民族"}]}, {"text": "在云南的傣族村寨里，村民们正忙着准备传统的泼水节活动。", "label": [{"entity": "傣族", "start_idx": 5, "end_idx": 7, "type": "民族"}]}, {"text": "在云南的节日庆典上，彝族和傣族的歌舞表演吸引了众多游客。", "label": [{"entity": "彝族", "start_idx": 10, "end_idx": 12, "type": "民族"}, {"entity": "傣族", "start_idx": 13, "end_idx": 15, "type": "民族"}]}, {"text": "藏族同胞在高原上热情地跳起了传统的锅庄舞。", "label": [{"entity": "藏族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "李明毕业于北京大学计算机科学与技术专业，获得了工学硕士学位。", "label": [{"entity": "北京大学计算机科学与技术专业", "start_idx": 3, "end_idx": 10, "type": "教育背景"}, {"entity": "工学硕士学位", "start_idx": 19, "end_idx": 24, "type": "教育背景"}]}, {"text": "张华拥有北京大学计算机科学与技术专业的学士学位。", "label": [{"entity": "北京大学计算机科学与技术专业的学士学位", "start_idx": 4, "end_idx": 23, "type": "教育背景"}]}, {"text": "李华拥有北京大学计算机科学与技术专业的博士学位。", "label": [{"entity": "北京大学", "start_idx": 3, "end_idx": 5, "type": "教育背景"}, {"entity": "计算机科学与技术专业", "start_idx": 6, "end_idx": 10, "type": "教育背景"}, {"entity": "博士学位", "start_idx": 11, "end_idx": 13, "type": "教育背景"}]}, {"text": "张华拥有北京大学计算机科学与技术专业的博士学位，目前正在申请教授职位。", "label": [{"entity": "北京大学计算机科学与技术专业的博士学位", "start_idx": 3, "end_idx": 14, "type": "教育背景"}]}, {"text": "李华拥有北京大学计算机科学与技术专业的博士学位，目前在该公司担任首席技术官。", "label": [{"entity": "北京大学计算机科学与技术专业的博士学位", "start_idx": 3, "end_idx": 19, "type": "教育背景"}]}, {"text": "张华拥有北京大学计算机科学与技术专业的硕士学位。", "label": [{"entity": "北京大学计算机科学与技术专业的硕士学位", "start_idx": 3, "end_idx": 16, "type": "教育背景"}]}, {"text": "李华拥有北京大学计算机科学专业的硕士学位，这为他的职业发展奠定了坚实基础。", "label": [{"entity": "北京大学计算机科学专业的硕士学位", "start_idx": 3, "end_idx": 17, "type": "教育背景"}]}, {"text": "李明目前处于离异状态，他计划下周去民政局办理复婚手续。", "label": [{"entity": "离异", "start_idx": 3, "end_idx": 5, "type": "婚姻状况"}]}, {"text": "张女士的婚姻状况是已婚，并且已经育有两个孩子。", "label": [{"entity": "已婚", "start_idx": 7, "end_idx": 9, "type": "婚姻状况"}]}, {"text": "王女士的婚姻状况是已婚，她与丈夫已经共同生活了十年。", "label": [{"entity": "已婚", "start_idx": 6, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "张三的婚姻状况是已婚，他和李四已经共同生活了十年。", "label": [{"entity": "已婚", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "张三的婚姻状况是已婚，他和妻子已经共同生活了十年。", "label": [{"entity": "已婚", "start_idx": 7, "end_idx": 9, "type": "婚姻状况"}]}, {"text": "张三的婚姻状况是已婚，他与妻子李四已经共同生活了十年。", "label": [{"entity": "已婚", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "张先生的婚姻状况是已婚，他和妻子已经共同生活了十年。", "label": [{"entity": "已婚", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "张先生的婚姻状况是已婚，他和妻子已经共同生活了十年。", "label": [{"entity": "已婚", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "李女士的婚姻状况是离异，她现在独自抚养两个孩子。", "label": [{"entity": "离异", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "李明目前处于已婚状态，他和妻子已经共同生活了十年。", "label": [{"entity": "已婚", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "这位支持自由主义政治理念的代表在会议上发表了重要讲话。", "label": [{"entity": "自由主义", "start_idx": 7, "end_idx": 10, "type": "政治倾向"}]}, {"text": "这位选民坚定支持自由主义，认为政府应该减少对经济的干预。", "label": [{"entity": "自由主义", "start_idx": 10, "end_idx": 13, "type": "政治倾向"}]}, {"text": "这位支持者明确表示自己属于美国民主党的自由派阵营。", "label": [{"entity": "美国民主党", "start_idx": 14, "end_idx": 18, "type": "政治倾向"}, {"entity": "自由派", "start_idx": 20, "end_idx": 22, "type": "政治倾向"}]}, {"text": "这位自由主义者认为，政府应该减少对经济的干预。", "label": [{"entity": "自由主义者", "start_idx": 2, "end_idx": 6, "type": "政治倾向"}]}, {"text": "这位自由主义者支持渐进式改革，反对激进的社会变革。", "label": [{"entity": "自由主义者", "start_idx": 1, "end_idx": 5, "type": "政治倾向"}]}, {"text": "作为一名坚定的共产主义信徒，他始终拥护中国共产党领导。", "label": [{"entity": "共产主义", "start_idx": 7, "end_idx": 12, "type": "政治倾向"}, {"entity": "中国共产党", "start_idx": 24, "end_idx": 29, "type": "政治倾向"}]}, {"text": "作为一名坚定的社会主义者，他在选举中始终支持中国共产党。", "label": [{"entity": "社会主义者", "start_idx": 5, "end_idx": 8, "type": "政治倾向"}, {"entity": "中国共产党", "start_idx": 24, "end_idx": 28, "type": "政治倾向"}]}, {"text": "\"这位学者长期关注自由主义思潮在当代社会的影响。\"", "label": [{"entity": "自由主义", "start_idx": 7, "end_idx": 9, "type": "政治倾向"}]}, {"text": "\"作为一名坚定的社会主义者，他始终支持中国共产党的领导。\"", "label": [{"entity": "社会主义者", "start_idx": 5, "end_idx": 8, "type": "政治倾向"}, {"entity": "中国共产党", "start_idx": 19, "end_idx": 22, "type": "政治倾向"}]}, {"text": "\"作为一名坚定的共产主义党员，他始终坚守无产阶级革命立场。\"", "label": [{"entity": "共产主义党员", "start_idx": 7, "end_idx": 14, "type": "政治倾向"}, {"entity": "无产阶级革命立场", "start_idx": 22, "end_idx": 31, "type": "政治倾向"}]}, {"text": "张妈妈带着李爸爸和他们的儿子小明一起去公园散步。", "label": [{"entity": "张妈妈", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "李爸爸", "start_idx": 4, "end_idx": 6, "type": "家庭成员"}, {"entity": "小明", "start_idx": 9, "end_idx": 11, "type": "家庭成员"}]}, {"text": "我的爸爸张伟和妈妈李娜周末会带我去外婆王芳家吃饭。", "label": [{"entity": "爸爸", "start_idx": 2, "end_idx": 4, "type": "家庭成员"}, {"entity": "张伟", "start_idx": 4, "end_idx": 7, "type": "家庭成员"}, {"entity": "妈妈", "start_idx": 8, "end_idx": 10, "type": "家庭成员"}, {"entity": "李娜", "start_idx": 10, "end_idx": 13, "type": "家庭成员"}, {"entity": "我", "start_idx": 14, "end_idx": 15, "type": "家庭成员"}, {"entity": "外婆", "start_idx": 18, "end_idx": 20, "type": "家庭成员"}, {"entity": "王芳", "start_idx": 20, "end_idx": 23, "type": "家庭成员"}]}, {"text": "我妈妈张丽昨天给我打电话，让我周末去姥姥李芳家吃饭。", "label": [{"entity": "张丽", "start_idx": 2, "end_idx": 4, "type": "家庭成员"}, {"entity": "姥姥", "start_idx": 10, "end_idx": 12, "type": "家庭成员"}, {"entity": "李芳", "start_idx": 12, "end_idx": 14, "type": "家庭成员"}]}, {"text": "妈妈和爸爸带着我、哥哥还有弟弟一起去奶奶家过周末。", "label": [{"entity": "妈妈", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "爸爸", "start_idx": 3, "end_idx": 5, "type": "家庭成员"}, {"entity": "我", "start_idx": 7, "end_idx": 8, "type": "家庭成员"}, {"entity": "哥哥", "start_idx": 10, "end_idx": 12, "type": "家庭成员"}, {"entity": "弟弟", "start_idx": 16, "end_idx": 18, "type": "家庭成员"}, {"entity": "奶奶", "start_idx": 22, "end_idx": 24, "type": "家庭成员"}]}, {"text": "我爸爸张伟和妈妈李娜明天要带我妹妹小芳一起去公园玩。", "label": [{"entity": "张伟", "start_idx": 3, "end_idx": 5, "type": "家庭成员"}, {"entity": "李娜", "start_idx": 7, "end_idx": 9, "type": "家庭成员"}, {"entity": "小芳", "start_idx": 13, "end_idx": 15, "type": "家庭成员"}]}, {"text": "我妈妈张丽昨天带着我的儿子小明去公园玩了整整一下午。", "label": [{"entity": "张丽", "start_idx": 2, "end_idx": 4, "type": "家庭成员"}, {"entity": "小明", "start_idx": 9, "end_idx": 11, "type": "家庭成员"}]}, {"text": "我的爸爸张伟和妈妈李娜明天要带妹妹小芳一起去公园玩。", "label": [{"entity": "张伟", "start_idx": 4, "end_idx": 6, "type": "家庭成员"}, {"entity": "李娜", "start_idx": 9, "end_idx": 11, "type": "家庭成员"}, {"entity": "小芳", "start_idx": 17, "end_idx": 19, "type": "家庭成员"}]}, {"text": "我的哥哥张伟明天要带他的女儿小雅去游乐园玩。", "label": [{"entity": "张伟", "start_idx": 3, "end_idx": 5, "type": "家庭成员"}, {"entity": "女儿", "start_idx": 9, "end_idx": 11, "type": "家庭成员"}]}, {"text": "我弟弟张明今天下午要去接妹妹李婷放学回家。", "label": [{"entity": "张明", "start_idx": 3, "end_idx": 5, "type": "家庭成员"}, {"entity": "李婷", "start_idx": 10, "end_idx": 12, "type": "家庭成员"}]}, {"text": "我的妈妈李女士和爸爸王先生一起去参加了奶奶张女士的七十岁生日宴会。", "label": [{"entity": "妈妈", "start_idx": 2, "end_idx": 4, "type": "家庭成员"}, {"entity": "李女士", "start_idx": 4, "end_idx": 7, "type": "家庭成员"}, {"entity": "爸爸", "start_idx": 8, "end_idx": 10, "type": "家庭成员"}, {"entity": "王先生", "start_idx": 10, "end_idx": 13, "type": "家庭成员"}, {"entity": "奶奶", "start_idx": 16, "end_idx": 18, "type": "家庭成员"}, {"entity": "张女士", "start_idx": 18, "end_idx": 21, "type": "家庭成员"}]}, {"text": "这家公司的月薪标准是8000元，比同行业平均水平要高一些。", "label": [{"entity": "8000元", "start_idx": 8, "end_idx": 10, "type": "工资数额"}]}, {"text": "小王的月薪是8000元，比他预期的7000元高出不少。", "label": [{"entity": "8000元", "start_idx": 5, "end_idx": 7, "type": "工资数额"}, {"entity": "7000元", "start_idx": 10, "end_idx": 12, "type": "工资数额"}]}, {"text": "这家公司的月薪是12,500元，比同行业平均水平要高一些。", "label": [{"entity": "12,500元", "start_idx": 9, "end_idx": 14, "type": "工资数额"}]}, {"text": "这家公司的月工资标准是8000元，比同行业平均水平高了不少。", "label": [{"entity": "8000元", "start_idx": 10, "end_idx": 13, "type": "工资数额"}]}, {"text": "这家公司的月工资是8,500元，比同行业平均水平要高一些。", "label": [{"entity": "8,500元", "start_idx": 8, "end_idx": 12, "type": "工资数额"}]}, {"text": "这家公司的月薪标准是8000元，比同行业高出不少。", "label": [{"entity": "8000元", "start_idx": 8, "end_idx": 10, "type": "工资数额"}]}, {"text": "小王的月工资是8500元，比上个月多了500元。", "label": [{"entity": "8500元", "start_idx": 6, "end_idx": 9, "type": "工资数额"}, {"entity": "500元", "start_idx": 14, "end_idx": 17, "type": "工资数额"}]}, {"text": "张先生上个月的工资是8,500元，比预期的要高一些。", "label": [{"entity": "8,500元", "start_idx": 7, "end_idx": 11, "type": "工资数额"}]}, {"text": "这家公司的月工资标准是8500元，比同行业平均水平要高一些。", "label": [{"entity": "8500元", "start_idx": 9, "end_idx": 12, "type": "工资数额"}]}, {"text": "这家公司的月薪标准是8000元，比同行业平均水平高出不少。", "label": [{"entity": "8000元", "start_idx": 10, "end_idx": 13, "type": "工资数额"}]}, {"text": "我决定将一部分资金投资于华夏成长混合型证券投资基金。", "label": [{"entity": "华夏成长混合型证券投资基金", "start_idx": 11, "end_idx": 20, "type": "投资产品"}]}, {"text": "我最近购买了招商银行发行的招商中证白酒指数基金。", "label": [{"entity": "招商中证白酒指数基金", "start_idx": 11, "end_idx": 21, "type": "投资产品"}]}, {"text": "我决定将部分资金投资于华夏基金旗下的华夏成长混合型证券投资基金。", "label": [{"entity": "华夏成长混合型证券投资基金", "start_idx": 17, "end_idx": 28, "type": "投资产品"}]}, {"text": "\"我决定将部分资金投资于华夏成长混合型证券投资基金。\"", "label": [{"entity": "华夏成长混合型证券投资基金", "start_idx": 10, "end_idx": 27, "type": "投资产品"}]}, {"text": "\"客户选择将部分资金配置到华夏成长混合型基金和招商中证白酒指数基金。\"", "label": [{"entity": "华夏成长混合型基金", "start_idx": 10, "end_idx": 19, "type": "投资产品"}, {"entity": "招商中证白酒指数基金", "start_idx": 20, "end_idx": 30, "type": "投资产品"}]}, {"text": "我选择将部分资金投资于华夏成长混合型证券投资基金。", "label": [{"entity": "华夏成长混合型证券投资基金", "start_idx": 9, "end_idx": 25, "type": "投资产品"}]}, {"text": "\"我决定将一部分资金投资于华夏成长混合型证券投资基金。\"", "label": [{"entity": "华夏成长混合型证券投资基金", "start_idx": 10, "end_idx": 21, "type": "投资产品"}]}, {"text": "\"我决定将部分资金投资于华夏基金旗下的华夏回报混合型基金。\"", "label": [{"entity": "华夏回报混合型基金", "start_idx": 13, "end_idx": 22, "type": "投资产品"}]}, {"text": "我决定将部分资金投资于华夏基金旗下的华夏回报混合型证券投资基金。", "label": [{"entity": "华夏回报混合型证券投资基金", "start_idx": 15, "end_idx": 24, "type": "投资产品"}]}, {"text": "“我决定将部分资金投资于招商银行发行的招商中证白酒指数基金。”", "label": [{"entity": "招商中证白酒指数基金", "start_idx": 15, "end_idx": 25, "type": "投资产品"}]}, {"text": "我在2023年度个人所得税纳税申报表中记录了工资收入总额为12万元。", "label": [{"entity": "2023年度个人所得税纳税申报表", "start_idx": 3, "end_idx": 11, "type": "税务记录"}, {"entity": "工资收入总额为12万元", "start_idx": 15, "end_idx": 24, "type": "税务记录"}]}, {"text": "我在2023年个人所得税年度汇算清缴中提交了工资薪金所得记录。", "label": [{"entity": "个人所得税年度汇算清缴", "start_idx": 3, "end_idx": 10, "type": "税务记录"}, {"entity": "工资薪金所得记录", "start_idx": 16, "end_idx": 22, "type": "税务记录"}]}, {"text": "我在2022年个人所得税年度汇算清缴时提交了工资薪金所得记录。", "label": [{"entity": "个人所得税年度汇算清缴", "start_idx": 3, "end_idx": 11, "type": "税务记录"}, {"entity": "工资薪金所得记录", "start_idx": 20, "end_idx": 28, "type": "税务记录"}]}, {"text": "张先生提交了2023年度的个人所得税年度汇算清缴申报表，并附上了工资收入明细表。", "label": [{"entity": "个人所得税年度汇算清缴申报表", "start_idx": 5, "end_idx": 11, "type": "税务记录"}, {"entity": "工资收入明细表", "start_idx": 17, "end_idx": 21, "type": "税务记录"}]}, {"text": "请查看您的个人税务记录，包括个人所得税缴纳证明（2023年）和增值税发票（20231115）。", "label": [{"entity": "个人所得税缴纳证明（2023年）", "start_idx": 10, "end_idx": 27, "type": "税务记录"}, {"entity": "增值税发票（20231115）", "start_idx": 29, "end_idx": 46, "type": "税务记录"}]}, {"text": "我需要查看2021年度个人所得税纳税申报表和增值税专用发票存根联。", "label": [{"entity": "2021年度个人所得税纳税申报表", "start_idx": 6, "end_idx": 24, "type": "税务记录"}, {"entity": "增值税专用发票存根联", "start_idx": 27, "end_idx": 45, "type": "税务记录"}]}, {"text": "公司的增值税专用发票编号为NO.123456789，请查收。", "label": [{"entity": "NO.123456789", "start_idx": 12, "end_idx": 22, "type": "税务记录"}]}, {"text": "公司提交了2023年度企业所得税年度汇算清缴纳税申报表。", "label": [{"entity": "企业所得税年度汇算清缴纳税申报表", "start_idx": 6, "end_idx": 24, "type": "税务记录"}]}, {"text": "请提供我司2023年第三季度的增值税专用发票记录，谢谢。", "label": [{"entity": "增值税专用发票记录", "start_idx": 7, "end_idx": 17, "type": "税务记录"}]}, {"text": "公司提交了2023年度企业所得税纳税申报表，并附带了增值税专用发票。", "label": [{"entity": "2023年度企业所得税纳税申报表", "start_idx": 4, "end_idx": 17, "type": "税务记录"}, {"entity": "增值税专用发票", "start_idx": 25, "end_idx": 31, "type": "税务记录"}]}, {"text": "张先生查询了自己的中国人民银行征信中心个人信用报告，发现信用评分达到了750分。", "label": [{"entity": "个人信用报告", "start_idx": 9, "end_idx": 12, "type": "信用记录"}]}, {"text": "张先生查询了自己的个人征信报告，发现信用评分达到了760分。", "label": [{"entity": "个人征信报告", "start_idx": 5, "end_idx": 9, "type": "信用记录"}, {"entity": "信用评分", "start_idx": 15, "end_idx": 18, "type": "信用记录"}]}, {"text": "王先生的个人征信报告显示，他的信用卡还款记录为\"良好\"，贷款逾期次数为\"0次\"。", "label": [{"entity": "良好", "start_idx": 12, "end_idx": 14, "type": "信用记录"}, {"entity": "0次", "start_idx": 27, "end_idx": 29, "type": "信用记录"}]}, {"text": "小张的信用卡还款记录显示，他的招商银行信用卡账户在2023年11月已全额还款。", "label": [{"entity": "信用卡还款记录", "start_idx": 3, "end_idx": 9, "type": "信用记录"}, {"entity": "招商银行信用卡账户", "start_idx": 14, "end_idx": 24, "type": "信用记录"}]}, {"text": "张先生在央行征信系统中查询了自己的个人信用报告，发现其中包含一份名为“房贷还款记录”的信用记录。", "label": [{"entity": "房贷还款记录", "start_idx": 28, "end_idx": 32, "type": "信用记录"}]}, {"text": "张先生在查询个人信用报告时，发现他的信用卡逾期记录显示为“已结清”。", "label": [{"entity": "个人信用报告", "start_idx": 6, "end_idx": 13, "type": "信用记录"}, {"entity": "信用卡逾期记录", "start_idx": 21, "end_idx": 30, "type": "信用记录"}]}, {"text": "张先生的个人征信报告显示，他的信用卡还款记录为“正常”，贷款逾期记录为“无”。", "label": [{"entity": "正常", "start_idx": 17, "end_idx": 18, "type": "信用记录"}, {"entity": "无", "start_idx": 26, "end_idx": 26, "type": "信用记录"}]}, {"text": "根据中国人民银行征信中心的报告，他的个人信用报告显示当前无逾期记录。", "label": [{"entity": "个人信用报告", "start_idx": 15, "end_idx": 23, "type": "信用记录"}]}, {"text": "根据您的个人信用记录类型——\"按时还款记录\"，银行批准了您的贷款申请。", "label": [{"entity": "按时还款记录", "start_idx": 10, "end_idx": 17, "type": "信用记录"}]}, {"text": "小明查看了自己的中国人民银行征信报告，发现信用评分达到了750分。", "label": [{"entity": "中国人民银行征信报告", "start_idx": 5, "end_idx": 11, "type": "信用记录"}, {"entity": "信用评分", "start_idx": 16, "end_idx": 19, "type": "信用记录"}]}, {"text": "这家公司的主要资产包括一栋位于北京朝阳区国贸CBD的写字楼和一辆奔驰S级轿车。", "label": [{"entity": "写字楼", "start_idx": 14, "end_idx": 16, "type": "实体资产"}, {"entity": "奔驰S级轿车", "start_idx": 21, "end_idx": 25, "type": "实体资产"}]}, {"text": "这台工厂里的卡特彼勒D6R推土机需要定期进行维护保养。", "label": [{"entity": "卡特彼勒D6R推土机", "start_idx": 7, "end_idx": 15, "type": "实体资产"}]}, {"text": "这家公司的仓库里存放着大量的黄金、原油和房地产。", "label": [{"entity": "黄金", "start_idx": 13, "end_idx": 15, "type": "实体资产"}, {"entity": "原油", "start_idx": 16, "end_idx": 18, "type": "实体资产"}, {"entity": "房地产", "start_idx": 19, "end_idx": 22, "type": "实体资产"}]}, {"text": "这家公司拥有两栋位于北京市朝阳区CBD的甲级写字楼和一辆奔驰S级轿车作为实体资产。", "label": [{"entity": "两栋位于北京市朝阳区CBD的甲级写字楼", "start_idx": 6, "end_idx": 25, "type": "实体资产"}, {"entity": "一辆奔驰S级轿车", "start_idx": 28, "end_idx": 40, "type": "实体资产"}]}, {"text": "这家公司的主要实体资产包括一栋位于北京市朝阳区建国路88号的写字楼和一架波音737-800飞机。", "label": [{"entity": "一栋位于北京市朝阳区建国路88号的写字楼", "start_idx": 14, "end_idx": 43, "type": "实体资产"}, {"entity": "一架波音737-800飞机", "start_idx": 44, "end_idx": 60, "type": "实体资产"}]}, {"text": "这家公司的主要资产包括位于上海市浦东新区的两栋写字楼和一架波音737飞机。", "label": [{"entity": "两栋写字楼", "start_idx": 12, "end_idx": 15, "type": "实体资产"}, {"entity": "一架波音737飞机", "start_idx": 17, "end_idx": 23, "type": "实体资产"}]}, {"text": "这家公司的仓库里存放着大量的集装箱、挖掘机和叉车等实体资产。", "label": [{"entity": "集装箱", "start_idx": 11, "end_idx": 13, "type": "实体资产"}, {"entity": "挖掘机", "start_idx": 14, "end_idx": 16, "type": "实体资产"}, {"entity": "叉车", "start_idx": 17, "end_idx": 19, "type": "实体资产"}]}, {"text": "这台设备是一台苹果MacBook Pro笔记本电脑，目前状态良好。", "label": [{"entity": "苹果MacBook Pro笔记本电脑", "start_idx": 5, "end_idx": 12, "type": "实体资产"}]}, {"text": "这家公司拥有多台价值超过5000万元的挖掘机作为实体资产。", "label": [{"entity": "挖掘机", "start_idx": 14, "end_idx": 16, "type": "实体资产"}]}, {"text": "这家公司的主要实体资产包括两栋位于北京市朝阳区建国路的写字楼和一辆奔驰S级轿车。", "label": [{"entity": "两栋位于北京市朝阳区建国路的写字楼", "start_idx": 10, "end_idx": 38, "type": "实体资产"}, {"entity": "一辆奔驰S级轿车", "start_idx": 40, "end_idx": 53, "type": "实体资产"}]}, {"text": "张先生于2023年10月15日通过招商银行向李女士转账了5000元人民币。", "label": [{"entity": "张先生", "start_idx": 0, "end_idx": 3, "type": "交易信息"}, {"entity": "2023年10月15日", "start_idx": 5, "end_idx": 13, "type": "交易信息"}, {"entity": "招商银行", "start_idx": 17, "end_idx": 21, "type": "交易信息"}, {"entity": "李女士", "start_idx": 25, "end_idx": 28, "type": "交易信息"}, {"entity": "5000元人民币", "start_idx": 34, "end_idx": 40, "type": "交易信息"}]}, {"text": "张先生通过招商银行转账给李女士，交易金额为￥3,500.00，交易时间为2023-11-15 14:30。", "label": [{"entity": "招商银行", "start_idx": 5, "end_idx": 9, "type": "交易信息"}, {"entity": "￥3,500.00", "start_idx": 16, "end_idx": 22, "type": "交易信息"}, {"entity": "2023-11-15 14:30", "start_idx": 27, "end_idx": 38, "type": "交易信息"}]}, {"text": "张先生通过支付宝向李女士转账了500元，交易时间为2023年10月15日15:30。", "label": [{"entity": "支付宝", "start_idx": 6, "end_idx": 9, "type": "交易信息"}, {"entity": "500元", "start_idx": 13, "end_idx": 16, "type": "交易信息"}, {"entity": "2023年10月15日15:30", "start_idx": 22, "end_idx": 34, "type": "交易信息"}]}, {"text": "张先生通过支付宝向李女士转账了500元，交易时间为2023年10月15日15:30。", "label": [{"entity": "支付宝", "start_idx": 5, "end_idx": 7, "type": "交易信息"}, {"entity": "李女士", "start_idx": 9, "end_idx": 11, "type": "交易信息"}, {"entity": "500元", "start_idx": 13, "end_idx": 15, "type": "交易信息"}, {"entity": "2023年10月15日15:30", "start_idx": 18, "end_idx": 27, "type": "交易信息"}]}, {"text": "张先生通过支付宝转账给李女士1000元，交易时间为2023年10月15日15:30。", "label": [{"entity": "支付宝", "start_idx": 4, "end_idx": 6, "type": "交易信息"}, {"entity": "1000元", "start_idx": 10, "end_idx": 13, "type": "交易信息"}, {"entity": "2023年10月15日15:30", "start_idx": 18, "end_idx": 29, "type": "交易信息"}]}, {"text": "张先生通过招商银行信用卡支付了订单编号20230001的款项，金额为2999元。", "label": [{"entity": "招商银行信用卡", "start_idx": 6, "end_idx": 13, "type": "交易信息"}, {"entity": "订单编号20230001", "start_idx": 16, "end_idx": 26, "type": "交易信息"}, {"entity": "2999元", "start_idx": 33, "end_idx": 36, "type": "交易信息"}]}, {"text": "张先生通过工商银行转账支付了2023年12月5日的订单，金额为￥1,280.50。", "label": [{"entity": "工商银行", "start_idx": 6, "end_idx": 9, "type": "交易信息"}, {"entity": "2023年12月5日", "start_idx": 12, "end_idx": 19, "type": "交易信息"}, {"entity": "￥1,280.50", "start_idx": 29, "end_idx": 34, "type": "交易信息"}]}, {"text": "张先生通过工商银行向李女士转账了5000元，交易时间为2023年10月15日。", "label": [{"entity": "工商银行", "start_idx": 6, "end_idx": 9, "type": "交易信息"}, {"entity": "李女士", "start_idx": 11, "end_idx": 13, "type": "交易信息"}, {"entity": "5000元", "start_idx": 18, "end_idx": 20, "type": "交易信息"}, {"entity": "2023年10月15日", "start_idx": 25, "end_idx": 30, "type": "交易信息"}]}, {"text": "张先生通过支付宝完成了从招商银行向建设银行的转账，金额为5000元。", "label": [{"entity": "支付宝", "start_idx": 5, "end_idx": 7, "type": "交易信息"}, {"entity": "招商银行", "start_idx": 11, "end_idx": 13, "type": "交易信息"}, {"entity": "建设银行", "start_idx": 15, "end_idx": 17, "type": "交易信息"}, {"entity": "5000元", "start_idx": 22, "end_idx": 24, "type": "交易信息"}]}, {"text": "张先生通过招商银行信用卡支付了订单号为20230515的购物款项。", "label": [{"entity": "招商银行信用卡", "start_idx": 5, "end_idx": 11, "type": "交易信息"}, {"entity": "20230515", "start_idx": 17, "end_idx": 23, "type": "交易信息"}]}, {"text": "这位患者被诊断出患有糖尿病，需要长期控制血糖水平。", "label": [{"entity": "糖尿病", "start_idx": 10, "end_idx": 12, "type": "疾病"}]}, {"text": "这位患者被诊断出患有糖尿病，需要定期监测血糖水平。", "label": [{"entity": "糖尿病", "start_idx": 10, "end_idx": 12, "type": "疾病"}]}, {"text": "这位患者被诊断患有糖尿病，需要定期监测血糖水平。", "label": [{"entity": "糖尿病", "start_idx": 8, "end_idx": 10, "type": "疾病"}]}, {"text": "阿司匹林是一种常见的解热镇痛药，常用于缓解轻至中度疼痛。", "label": [{"entity": "阿司匹林", "start_idx": 0, "end_idx": 3, "type": "药物"}]}, {"text": "这位高血压患者每天按时服用硝苯地平缓释片来控制血压。", "label": [{"entity": "硝苯地平缓释片", "start_idx": 14, "end_idx": 20, "type": "药物"}]}, {"text": "患者出现了持续性咳嗽、胸闷和呼吸困难等症状，需要进一步检查。", "label": [{"entity": "持续性咳嗽", "start_idx": 4, "end_idx": 8, "type": "临床表现"}, {"entity": "胸闷", "start_idx": 9, "end_idx": 11, "type": "临床表现"}, {"entity": "呼吸困难", "start_idx": 12, "end_idx": 15, "type": "临床表现"}]}, {"text": "这位患者出现了明显的呼吸困难、胸痛和持续性咳嗽的症状。", "label": [{"entity": "呼吸困难", "start_idx": 6, "end_idx": 9, "type": "临床表现"}, {"entity": "胸痛", "start_idx": 10, "end_idx": 12, "type": "临床表现"}, {"entity": "持续性咳嗽", "start_idx": 13, "end_idx": 18, "type": "临床表现"}]}, {"text": "患者出现了明显的胸痛、呼吸困难以及持续性的咳嗽症状。", "label": [{"entity": "胸痛", "start_idx": 4, "end_idx": 6, "type": "临床表现"}, {"entity": "呼吸困难", "start_idx": 7, "end_idx": 10, "type": "临床表现"}, {"entity": "持续性的咳嗽", "start_idx": 11, "end_idx": 16, "type": "临床表现"}]}, {"text": "这位患者出现了明显的发热、咳嗽和呼吸困难等症状。", "label": [{"entity": "发热", "start_idx": 5, "end_idx": 7, "type": "临床表现"}, {"entity": "咳嗽", "start_idx": 8, "end_idx": 10, "type": "临床表现"}, {"entity": "呼吸困难", "start_idx": 11, "end_idx": 14, "type": "临床表现"}]}, {"text": "这位患者的临床表现包括发热、咳嗽和呼吸困难。", "label": [{"entity": "发热", "start_idx": 9, "end_idx": 10, "type": "临床表现"}, {"entity": "咳嗽", "start_idx": 11, "end_idx": 12, "type": "临床表现"}, {"entity": "呼吸困难", "start_idx": 13, "end_idx": 15, "type": "临床表现"}]}, {"text": "患者出现了明显的皮疹、发热和关节疼痛等临床表现。", "label": [{"entity": "皮疹", "start_idx": 5, "end_idx": 7, "type": "临床表现"}, {"entity": "发热", "start_idx": 8, "end_idx": 10, "type": "临床表现"}, {"entity": "关节疼痛", "start_idx": 11, "end_idx": 15, "type": "临床表现"}]}, {"text": "患者出现了明显的呼吸困难、咳嗽和发热症状，需要进一步检查。", "label": [{"entity": "呼吸困难", "start_idx": 4, "end_idx": 7, "type": "临床表现"}, {"entity": "咳嗽", "start_idx": 8, "end_idx": 10, "type": "临床表现"}, {"entity": "发热", "start_idx": 11, "end_idx": 13, "type": "临床表现"}]}, {"text": "医生建议患者进行结肠镜检查，以排除早期病变的可能性。", "label": [{"entity": "结肠镜检查", "start_idx": 6, "end_idx": 10, "type": "医疗程序"}]}, {"text": "医生建议他进行冠状动脉造影检查，以明确诊断。", "label": [{"entity": "冠状动脉造影检查", "start_idx": 6, "end_idx": 12, "type": "医疗程序"}]}, {"text": "医生建议他进行胃镜检查，以明确消化道出血的原因。", "label": [{"entity": "胃镜检查", "start_idx": 6, "end_idx": 9, "type": "医疗程序"}]}, {"text": "医生建议他进行胃镜检查，以明确消化性溃疡的诊断。", "label": [{"entity": "胃镜检查", "start_idx": 6, "end_idx": 9, "type": "医疗程序"}]}, {"text": "患者需要接受冠状动脉造影检查以明确诊断。", "label": [{"entity": "冠状动脉造影检查", "start_idx": 8, "end_idx": 16, "type": "医疗程序"}]}, {"text": "医生建议患者进行冠状动脉造影检查以明确诊断。", "label": [{"entity": "冠状动脉造影检查", "start_idx": 10, "end_idx": 18, "type": "医疗程序"}]}, {"text": "医生建议患者进行结肠镜检查以进一步明确诊断。", "label": [{"entity": "结肠镜检查", "start_idx": 11, "end_idx": 17, "type": "医疗程序"}]}, {"text": "医生建议患者进行冠状动脉造影检查，以明确诊断。", "label": [{"entity": "冠状动脉造影检查", "start_idx": 8, "end_idx": 15, "type": "医疗程序"}]}, {"text": "医生建议他进行胃镜检查，以明确消化道出血的原因。", "label": [{"entity": "胃镜检查", "start_idx": 6, "end_idx": 9, "type": "医疗程序"}]}, {"text": "小明对花生酱过敏，每次吃后都会出现严重的皮肤瘙痒症状。", "label": [{"entity": "花生酱", "start_idx": 5, "end_idx": 7, "type": "过敏信息"}, {"entity": "皮肤瘙痒", "start_idx": 15, "end_idx": 18, "type": "过敏信息"}]}, {"text": "小明对花生酱和牛奶过敏，每次吃后都会出现皮肤红肿的症状。", "label": [{"entity": "花生酱", "start_idx": 5, "end_idx": 7, "type": "过敏信息"}, {"entity": "牛奶", "start_idx": 8, "end_idx": 10, "type": "过敏信息"}]}, {"text": "小明对花生过敏，每次吃花生酱都会引发严重的皮肤瘙痒。", "label": [{"entity": "花生", "start_idx": 4, "end_idx": 6, "type": "过敏信息"}, {"entity": "花生酱", "start_idx": 12, "end_idx": 15, "type": "过敏信息"}]}, {"text": "小明对花生过敏，每次吃到含有花生酱的食物都会出现皮肤瘙痒的症状。", "label": [{"entity": "花生", "start_idx": 3, "end_idx": 4, "type": "过敏信息"}, {"entity": "花生酱", "start_idx": 17, "end_idx": 19, "type": "过敏信息"}]}, {"text": "小明对花生过敏，每次吃含花生酱的食物都会出现严重反应。", "label": [{"entity": "花生", "start_idx": 4, "end_idx": 5, "type": "过敏信息"}, {"entity": "花生酱", "start_idx": 12, "end_idx": 14, "type": "过敏信息"}]}, {"text": "小明对花生酱过敏，每次吃到都会引起严重的皮肤瘙痒。", "label": [{"entity": "花生酱", "start_idx": 4, "end_idx": 6, "type": "过敏信息"}]}, {"text": "小明对花生过敏，每次吃花生酱都会出现严重的皮肤瘙痒症状。", "label": [{"entity": "花生", "start_idx": 4, "end_idx": 6, "type": "过敏信息"}, {"entity": "花生酱", "start_idx": 10, "end_idx": 13, "type": "过敏信息"}]}, {"text": "小明对花生酱过敏，每次吃都会出现严重的皮肤瘙痒症状。", "label": [{"entity": "花生酱", "start_idx": 4, "end_idx": 6, "type": "过敏信息"}, {"entity": "严重的皮肤瘙痒症状", "start_idx": 12, "end_idx": 19, "type": "过敏信息"}]}, {"text": "小明对花生过敏，每次吃花生酱都会引发严重的皮肤反应。", "label": [{"entity": "花生", "start_idx": 4, "end_idx": 5, "type": "过敏信息"}, {"entity": "花生酱", "start_idx": 12, "end_idx": 14, "type": "过敏信息"}]}, {"text": "小明对花生过敏，每次吃坚果类食物前都会仔细检查成分表。", "label": [{"entity": "花生", "start_idx": 4, "end_idx": 5, "type": "过敏信息"}]}, {"text": "张女士在2022年3月15日生产，新生儿体重为3.5千克，Apgar评分10分。", "label": [{"entity": "张女士", "start_idx": 0, "end_idx": 3, "type": "生育信息"}, {"entity": "2022年3月15日", "start_idx": 5, "end_idx": 12, "type": "生育信息"}, {"entity": "新生儿体重为3.5千克", "start_idx": 13, "end_idx": 23, "type": "生育信息"}, {"entity": "Apgar评分10分", "start_idx": 24, "end_idx": 31, "type": "生育信息"}]}, {"text": "张女士在2023年3月15日成功分娩，产下一名体重3.5公斤的女婴。", "label": [{"entity": "张女士", "start_idx": 0, "end_idx": 3, "type": "生育信息"}, {"entity": "2023年3月15日", "start_idx": 4, "end_idx": 10, "type": "生育信息"}, {"entity": "3.5公斤", "start_idx": 20, "end_idx": 24, "type": "生育信息"}]}, {"text": "李女士的预产期是2024年6月15日，她计划在市妇幼保健院进行产前检查。", "label": [{"entity": "2024年6月15日", "start_idx": 6, "end_idx": 15, "type": "生育信息"}, {"entity": "市妇幼保健院", "start_idx": 27, "end_idx": 34, "type": "生育信息"}]}, {"text": "张女士在2023年4月15日进行了产前检查，血压值为120/80mmHg。", "label": [{"entity": "产前检查", "start_idx": 7, "end_idx": 10, "type": "生育信息"}]}, {"text": "张女士的预产期是2024年5月15日，她的血型为O型。", "label": [{"entity": "2024年5月15日", "start_idx": 6, "end_idx": 14, "type": "生育信息"}, {"entity": "O型", "start_idx": 21, "end_idx": 23, "type": "生育信息"}]}, {"text": "这位孕妇的预产期是2024年5月15日，胎位为头位，胎心率为145次/分钟。", "label": [{"entity": "2024年5月15日", "start_idx": 6, "end_idx": 15, "type": "生育信息"}, {"entity": "头位", "start_idx": 17, "end_idx": 19, "type": "生育信息"}, {"entity": "145次/分钟", "start_idx": 21, "end_idx": 27, "type": "生育信息"}]}, {"text": "张女士的预产期是2024年6月15日，她怀的是双胞胎，已孕周数为32周。", "label": [{"entity": "2024年6月15日", "start_idx": 7, "end_idx": 16, "type": "生育信息"}, {"entity": "双胞胎", "start_idx": 18, "end_idx": 20, "type": "生育信息"}, {"entity": "32周", "start_idx": 27, "end_idx": 29, "type": "生育信息"}]}, {"text": "张女士在2022年4月15日进行了剖腹产手术，产下一名体重3.5千克的男婴。", "label": [{"entity": "张女士", "start_idx": 0, "end_idx": 3, "type": "生育信息"}, {"entity": "2022年4月15日", "start_idx": 4, "end_idx": 11, "type": "生育信息"}, {"entity": "剖腹产手术", "start_idx": 12, "end_idx": 18, "type": "生育信息"}, {"entity": "男婴", "start_idx": 22, "end_idx": 24, "type": "生育信息"}, {"entity": "3.5千克", "start_idx": 20, "end_idx": 23, "type": "生育信息"}]}, {"text": "张女士的预产期是2024年5月15日，她目前怀孕32周，血型为O型RH阳性。", "label": [{"entity": "2024年5月15日", "start_idx": 6, "end_idx": 14, "type": "生育信息"}, {"entity": "32周", "start_idx": 20, "end_idx": 23, "type": "生育信息"}]}, {"text": "张女士的预产期是2023年10月15日，她已怀孕34周。", "label": [{"entity": "2023年10月15日", "start_idx": 6, "end_idx": 14, "type": "生育信息"}, {"entity": "34周", "start_idx": 19, "end_idx": 21, "type": "生育信息"}]}, {"text": "上海外滩的夜景吸引了众多游客前来观赏。", "label": [{"entity": "上海外滩", "start_idx": 0, "end_idx": 4, "type": "地理位置"}]}, {"text": "昨天我去了北京市海淀区中关村大街上的那家咖啡店，环境很不错。", "label": [{"entity": "北京市", "start_idx": 3, "end_idx": 5, "type": "地理位置"}, {"entity": "海淀区", "start_idx": 6, "end_idx": 8, "type": "地理位置"}, {"entity": "中关村大街", "start_idx": 9, "end_idx": 13, "type": "地理位置"}]}, {"text": "上海外滩的夜景吸引了众多游客前来观赏。", "label": [{"entity": "上海", "start_idx": 0, "end_idx": 1, "type": "地理位置"}, {"entity": "外滩", "start_idx": 2, "end_idx": 3, "type": "地理位置"}]}, {"text": "我计划这个周末去北京故宫博物院参观，顺便在附近的王府井大街逛逛。", "label": [{"entity": "北京故宫博物院", "start_idx": 8, "end_idx": 14, "type": "地理位置"}, {"entity": "王府井大街", "start_idx": 26, "end_idx": 30, "type": "地理位置"}]}, {"text": "北京故宫的游客们沿着中轴线参观，惊叹于太和殿的宏伟建筑。", "label": [{"entity": "北京故宫", "start_idx": 0, "end_idx": 4, "type": "地理位置"}, {"entity": "中轴线", "start_idx": 7, "end_idx": 10, "type": "地理位置"}, {"entity": "太和殿", "start_idx": 16, "end_idx": 19, "type": "地理位置"}]}, {"text": "昨天我去北京市朝阳区三里屯SOHO参加了一个科技展览。", "label": [{"entity": "北京市", "start_idx": 3, "end_idx": 5, "type": "地理位置"}, {"entity": "朝阳区", "start_idx": 6, "end_idx": 8, "type": "地理位置"}, {"entity": "三里屯SOHO", "start_idx": 9, "end_idx": 13, "type": "地理位置"}]}, {"text": "北京市的故宫博物院是中国最大的古代文化艺术博物馆。", "label": [{"entity": "北京市", "start_idx": 0, "end_idx": 2, "type": "地理位置"}, {"entity": "故宫博物院", "start_idx": 3, "end_idx": 7, "type": "地理位置"}]}, {"text": "我的航班号是CA1234，从北京首都国际机场起飞，下午3点到达上海浦东国际机场。", "label": [{"entity": "CA1234", "start_idx": 5, "end_idx": 9, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 13, "end_idx": 22, "type": "行程信息"}, {"entity": "下午3点", "start_idx": 28, "end_idx": 32, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 35, "end_idx": 45, "type": "行程信息"}]}, {"text": "我计划下周三上午9点从北京首都国际机场出发，前往上海浦东国际机场。", "label": [{"entity": "下周三上午9点", "start_idx": 3, "end_idx": 10, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 12, "end_idx": 23, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 28, "end_idx": 39, "type": "行程信息"}]}, {"text": "我计划下周三上午9点从北京首都国际机场出发，前往上海浦东国际机场。", "label": [{"entity": "下周三上午9点", "start_idx": 3, "end_idx": 10, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 11, "end_idx": 23, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 29, "end_idx": 41, "type": "行程信息"}]}, {"text": "我计划下周三从北京首都国际机场搭乘CA1234航班前往上海浦东国际机场。", "label": [{"entity": "下周三", "start_idx": 4, "end_idx": 7, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 10, "end_idx": 19, "type": "行程信息"}, {"entity": "CA1234", "start_idx": 23, "end_idx": 27, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 31, "end_idx": 40, "type": "行程信息"}]}, {"text": "我计划明天上午9点从北京首都国际机场出发，前往上海浦东国际机场。", "label": [{"entity": "明天上午9点", "start_idx": 3, "end_idx": 9, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 12, "end_idx": 24, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 29, "end_idx": 41, "type": "行程信息"}]}, {"text": "我计划下周三早上8:30从北京首都国际机场出发，前往上海浦东国际机场。", "label": [{"entity": "下周三早上8:30", "start_idx": 3, "end_idx": 11, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 16, "end_idx": 24, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 29, "end_idx": 37, "type": "行程信息"}]}, {"text": "我的航班将于2023年12月15日早上8:30从北京首都国际机场起飞。", "label": [{"entity": "2023年12月15日", "start_idx": 6, "end_idx": 14, "type": "行程信息"}, {"entity": "早上8:30", "start_idx": 15, "end_idx": 20, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 24, "end_idx": 34, "type": "行程信息"}]}, {"text": "我计划下周三上午9点从北京首都国际机场出发，飞往上海浦东国际机场。", "label": [{"entity": "下周三上午9点", "start_idx": 4, "end_idx": 10, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 12, "end_idx": 21, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 24, "end_idx": 33, "type": "行程信息"}]}, {"text": "我计划下周三上午9:30从北京首都国际机场搭乘CA1234航班前往上海浦东国际机场。", "label": [{"entity": "下周三上午9:30", "start_idx": 3, "end_idx": 11, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 14, "end_idx": 22, "type": "行程信息"}, {"entity": "CA1234", "start_idx": 26, "end_idx": 30, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 33, "end_idx": 41, "type": "行程信息"}]}, {"text": "我的航班号是CA1234，明天早上8:30从北京首都国际机场起飞。", "label": [{"entity": "CA1234", "start_idx": 5, "end_idx": 9, "type": "行程信息"}, {"entity": "明天早上8:30", "start_idx": 10, "end_idx": 16, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 19, "end_idx": 27, "type": "行程信息"}]}]