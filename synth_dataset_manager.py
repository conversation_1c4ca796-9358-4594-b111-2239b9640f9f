import os
import json
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List

class SynthDatasetManager:
    """合成数据集管理器 - 管理每次运行的目录结构和文件"""
    
    def __init__(self, base_dir: str = "synth_dataset", timestamp: str = None):
        self.base_dir = Path(base_dir)
        if timestamp:
            self.timestamp = timestamp
            self.run_dir = self.base_dir / "runs" / self.timestamp
        else:
            self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.run_dir = self.base_dir / "runs" / self.timestamp
        
        # 定义目录结构
        self.dirs = {
            "root": self.run_dir,
            "config": self.run_dir / "config",
            "source": self.run_dir / "source",
            "intermediate": self.run_dir / "intermediate",
            "intermediate_raw": self.run_dir / "intermediate" / "raw",
            "intermediate_processed": self.run_dir / "intermediate" / "processed", 
            "intermediate_by_entity": self.run_dir / "intermediate" / "by_entity",
            "iterations": self.run_dir / "iterations",
            "strategies": self.run_dir / "strategies",
            "output": self.run_dir / "output",
            "evaluation": self.run_dir / "evaluation",
            "logs": self.run_dir / "logs"
        }
        
    def initialize_run(self, config: Dict[str, Any] = None) -> Dict[str, Path]:
        """初始化运行目录结构"""
        # 创建所有目录
        for dir_path in self.dirs.values():
            dir_path.mkdir(parents=True, exist_ok=True)
            
        # 保存运行元信息
        run_metadata = {
            "timestamp": self.timestamp,
            "start_time": datetime.now().isoformat(),
            "status": "initialized",
            "config": config or {},
            "directories": {k: str(v) for k, v in self.dirs.items()}
        }
        
        self.save_metadata(run_metadata)
        self._update_latest_link()
        
        print(f"[✓] 已初始化运行目录：{self.run_dir}")
        return self.dirs
    
    def get_timestamp(self) -> str:
        """获取当前运行的时间戳"""
        return self.timestamp
    
    def get_run_dir(self) -> Path:
        """获取当前运行目录路径"""
        return self.run_dir
        
    def save_file(self, data: Any, filename: str, category: str, 
                 format: str = 'json', subdirectory: str = None) -> Path:
        """保存文件到指定类别目录"""
        # 确定保存目录
        if subdirectory:
            save_dir = self.dirs[category] / subdirectory
        else:
            save_dir = self.dirs[category]
            
        save_dir.mkdir(parents=True, exist_ok=True)
        
        # 确保文件名包含时间戳
        if not filename.startswith(self.timestamp):
            filename = f"{self.timestamp}_{filename}"
            
        if not filename.endswith(f".{format}"):
            filename = f"{filename}.{format}"
            
        file_path = save_dir / filename
        
        # 保存文件
        if format == 'json':
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        elif format == 'txt':
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(str(data))
        else:
            # 其他格式直接写入
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(str(data))
                
        return file_path
        
    def copy_source_file(self, source_path: str, target_name: str = None) -> Path:
        """复制源文件到source目录"""
        source_path = Path(source_path)
        if not source_path.exists():
            raise FileNotFoundError(f"源文件不存在：{source_path}")
            
        target_name = target_name or source_path.name
        target_path = self.dirs["source"] / target_name
        
        shutil.copy2(source_path, target_path)
        return target_path
        
    def create_iteration_dir(self, iteration_num: int) -> Path:
        """创建迭代目录"""
        iter_dir = self.dirs["iterations"] / f"iteration_{iteration_num:03d}"
        iter_dir.mkdir(parents=True, exist_ok=True)
        return iter_dir
        
    def save_strategy_files(self, strategy_files: Dict[str, str]) -> Dict[str, Path]:
        """保存策略文件到strategies目录"""
        saved_files = {}
        
        for strategy_type, file_path in strategy_files.items():
            if os.path.exists(file_path):
                target_name = f"{self.timestamp}_{strategy_type}.json"
                target_path = self.dirs["strategies"] / target_name
                shutil.copy2(file_path, target_path)
                saved_files[strategy_type] = target_path
                
        return saved_files
        
    def update_status(self, status: str, details: Dict[str, Any] = None):
        """更新运行状态"""
        metadata = self.load_metadata()
        metadata["status"] = status
        metadata["last_updated"] = datetime.now().isoformat()
        
        if details:
            metadata.update(details)
            
        self.save_metadata(metadata)
        
    def save_metadata(self, metadata: Dict[str, Any]):
        """保存运行元信息"""
        metadata_file = self.run_dir / "run_metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
            
    def load_metadata(self) -> Dict[str, Any]:
        """加载运行元信息"""
        metadata_file = self.run_dir / "run_metadata.json"
        if metadata_file.exists():
            with open(metadata_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
        
    def get_file_path(self, category: str, filename: str, subdirectory: str = None) -> Path:
        """获取文件路径"""
        if subdirectory:
            return self.dirs[category] / subdirectory / filename
        else:
            return self.dirs[category] / filename
            
    def get_latest_file(self, category: str, pattern: str = "*.json") -> Optional[Path]:
        """获取指定类别下的最新文件"""
        search_dir = self.dirs[category]
        if not search_dir.exists():
            return None
            
        files = list(search_dir.glob(pattern))
        if not files:
            return None
            
        return max(files, key=lambda f: f.stat().st_mtime)
        
    def _update_latest_link(self):
        """更新latest软链接"""
        latest_dir = self.base_dir / "latest"
        
        try:
            if latest_dir.exists():
                if latest_dir.is_symlink():
                    latest_dir.unlink()
                else:
                    shutil.rmtree(latest_dir)
                    
            # 在Windows上创建目录链接，其他系统创建软链接
            if os.name == 'nt':
                os.system(f'mklink /D "{latest_dir}" "{self.run_dir}"')
            else:
                latest_dir.symlink_to(self.run_dir)
                
        except Exception as e:
            print(f"[警告] 无法创建latest链接：{e}")
            # 如果创建链接失败，复制关键目录
            try:
                latest_dir.mkdir(exist_ok=True)
                key_dirs = ["output", "evaluation", "logs"]
                for key_dir in key_dirs:
                    src_dir = self.run_dir / key_dir
                    dst_dir = latest_dir / key_dir
                    if src_dir.exists():
                        if dst_dir.exists():
                            shutil.rmtree(dst_dir)
                        shutil.copytree(src_dir, dst_dir)
            except Exception as e2:
                print(f"[警告] 无法复制到latest目录：{e2}")
                
    def cleanup_old_runs(self, keep_days: int = 30, keep_count: int = 10):
        """清理旧的运行记录"""
        runs_dir = self.base_dir / "runs"
        if not runs_dir.exists():
            return
            
        # 获取所有运行目录
        run_dirs = [d for d in runs_dir.iterdir() if d.is_dir()]
        run_dirs.sort(key=lambda x: x.name, reverse=True)
        
        # 保留最新的keep_count个运行
        if len(run_dirs) <= keep_count:
            return
            
        now = datetime.now()
        for run_dir in run_dirs[keep_count:]:
            try:
                # 解析时间戳
                timestamp = datetime.strptime(run_dir.name, "%Y%m%d_%H%M%S")
                days_old = (now - timestamp).days
                
                if days_old > keep_days:
                    shutil.rmtree(run_dir)
                    print(f"[清理] 已删除旧运行：{run_dir.name}")
                    
            except ValueError:
                continue
                
    def get_run_summary(self) -> Dict[str, Any]:
        """获取运行摘要"""
        metadata = self.load_metadata()
        
        # 统计文件数量
        file_counts = {}
        for category, dir_path in self.dirs.items():
            if dir_path.exists():
                file_counts[category] = len(list(dir_path.rglob("*.json")))
            else:
                file_counts[category] = 0
                
        # 计算磁盘使用量
        total_size = sum(f.stat().st_size for f in self.run_dir.rglob('*') if f.is_file())
        disk_usage = f"{total_size / (1024*1024):.2f} MB"
        
        return {
            "timestamp": self.timestamp,
            "metadata": metadata,
            "file_counts": file_counts,
            "disk_usage": disk_usage,
            "run_dir": str(self.run_dir)
        }
        
    @classmethod
    def list_runs(cls, base_dir: str = "synth_dataset") -> List[Dict[str, Any]]:
        """列出所有运行"""
        runs_dir = Path(base_dir) / "runs"
        if not runs_dir.exists():
            return []
            
        runs = []
        for run_dir in runs_dir.iterdir():
            if run_dir.is_dir():
                metadata_file = run_dir / "run_metadata.json"
                if metadata_file.exists():
                    try:
                        with open(metadata_file, 'r', encoding='utf-8') as f:
                            metadata = json.load(f)
                        runs.append({
                            "timestamp": run_dir.name,
                            "status": metadata.get("status", "unknown"),
                            "start_time": metadata.get("start_time", ""),
                            "config": metadata.get("config", {})
                        })
                    except Exception:
                        continue
                        
        return sorted(runs, key=lambda x: x["timestamp"], reverse=True)
        
    def archive_run(self, archive_name: str, description: str = ""):
        """归档当前运行"""
        archive_dir = self.base_dir / "archive" / archive_name
        archive_dir.parent.mkdir(parents=True, exist_ok=True)
        
        # 复制运行目录
        shutil.copytree(self.run_dir, archive_dir)
        
        # 添加归档信息
        archive_info = {
            "original_timestamp": self.timestamp,
            "archive_time": datetime.now().isoformat(),
            "archive_name": archive_name,
            "description": description
        }
        
        with open(archive_dir / "archive_info.json", 'w', encoding='utf-8') as f:
            json.dump(archive_info, f, ensure_ascii=False, indent=2)
            
        print(f"[✓] 运行已归档到：{archive_dir}")

# 兼容性函数，用于从旧路径迁移到新结构
def migrate_legacy_structure():
    """将旧的synth_dataset结构迁移到新结构"""
    legacy_base = Path("synth_dataset")
    if not legacy_base.exists():
        return
        
    # 创建新的runs目录
    runs_dir = legacy_base / "runs"
    runs_dir.mkdir(exist_ok=True)
    
    # 查找带时间戳的文件，将它们迁移到对应的运行目录
    timestamp_files = {}
    
    # 扫描各个目录中的时间戳文件
    for subdir in ["origin", "final", "logs", "iterations"]:
        subdir_path = legacy_base / subdir
        if subdir_path.exists():
            for file_path in subdir_path.rglob("*"):
                if file_path.is_file():
                    # 尝试从文件名中提取时间戳
                    for part in file_path.name.split("_"):
                        if len(part) == 15 and part.isdigit():  # YYYYMMDD_HHMMSS格式
                            timestamp = part
                            if timestamp not in timestamp_files:
                                timestamp_files[timestamp] = []
                            timestamp_files[timestamp].append(file_path)
                            break
    
    # 为每个时间戳创建运行目录并迁移文件
    for timestamp, files in timestamp_files.items():
        run_dir = runs_dir / timestamp
        run_dir.mkdir(exist_ok=True)
        
        # 创建基本目录结构
        for subdir in ["source", "intermediate", "output", "evaluation", "logs"]:
            (run_dir / subdir).mkdir(exist_ok=True)
            
        # 迁移文件到对应目录
        for file_path in files:
            if "final" in str(file_path) or "synthetic_dataset" in file_path.name:
                target_dir = run_dir / "output"
            elif "log" in str(file_path) or "summary" in file_path.name:
                target_dir = run_dir / "logs"
            elif "iteration" in str(file_path):
                target_dir = run_dir / "iterations"
            elif "origin" in str(file_path) or "intermediate" in str(file_path):
                target_dir = run_dir / "intermediate"
            else:
                target_dir = run_dir / "source"
                
            target_path = target_dir / file_path.name
            try:
                shutil.copy2(file_path, target_path)
                print(f"[迁移] {file_path} -> {target_path}")
            except Exception as e:
                print(f"[警告] 迁移失败 {file_path}: {e}")
    
    print(f"[✓] 迁移完成，共处理 {len(timestamp_files)} 个时间戳的文件") 