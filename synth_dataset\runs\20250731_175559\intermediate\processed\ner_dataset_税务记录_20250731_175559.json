[{"text": "他的个人所得税完税证明编号为20230001，已成功提交至税务局。", "label": [{"entity": "个人所得税完税证明编号", "start_idx": 5, "end_idx": 12, "type": "税务记录"}, {"entity": "20230001", "start_idx": 13, "end_idx": 19, "type": "税务记录"}]}, {"text": "公司提交的增值税专用发票编号NO.12345678，用于抵扣进项税。", "label": [{"entity": "增值税专用发票编号NO.12345678", "start_idx": 6, "end_idx": 24, "type": "税务记录"}]}, {"text": "她在2022年度企业所得税汇算清缴报告中，申报了利润总额500万元。", "label": [{"entity": "2022年度企业所得税汇算清缴报告", "start_idx": 3, "end_idx": 13, "type": "税务记录"}, {"entity": "利润总额500万元", "start_idx": 20, "end_idx": 26, "type": "税务记录"}]}, {"text": "这份印花税票销账单编号ST-2023-001，记录了合同印花税缴纳情况。", "label": [{"entity": "印花税票销账单编号ST-2023-001", "start_idx": 5, "end_idx": 25, "type": "税务记录"}, {"entity": "合同印花税", "start_idx": 31, "end_idx": 37, "type": "税务记录"}]}, {"text": "他提交的契税完税凭证编号CT20230001，用于办理房产过户手续。", "label": [{"entity": "契税完税凭证编号CT20230001", "start_idx": 7, "end_idx": 23, "type": "税务记录"}]}, {"text": "这份土地增值税清算表编号LDSC-2023-001，已通过税务部门审核。", "label": [{"entity": "土地增值税清算表编号LDSC-2023-001", "start_idx": 3, "end_idx": 24, "type": "税务记录"}]}, {"text": "她的消费税纳税申报表编号XFNS20230001，显示应纳税额为120万元。", "label": [{"entity": "消费税纳税申报表编号XFNS20230001", "start_idx": 3, "end_idx": 26, "type": "税务记录"}, {"entity": "应纳税额为120万元", "start_idx": 28, "end_idx": 38, "type": "税务记录"}]}, {"text": "公司提交的房产税缴纳凭证编号FCSJ20230001，已确认入库。", "label": [{"entity": "FCSJ20230001", "start_idx": 14, "end_idx": 25, "type": "税务记录"}]}, {"text": "这份车辆购置税完税证明编号CQCST20230001，用于办理车辆登记。", "label": [{"entity": "车辆购置税完税证明编号CQCST20230001", "start_idx": 5, "end_idx": 25, "type": "税务记录"}]}, {"text": "他提交的城镇土地使用税申报表编号CSST20230001，已按时完成申报。", "label": [{"entity": "城镇土地使用税申报表编号CSST20230001", "start_idx": 5, "end_idx": 27, "type": "税务记录"}]}]