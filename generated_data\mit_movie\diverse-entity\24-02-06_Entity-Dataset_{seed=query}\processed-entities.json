{"entity-type2entity-names": {"Movie showtimes and ticket availability": {"Title": ["Titanic", "The Godfather", "Jurassic Park", "Inception", "The Shawshank Redemption", "The Avengers", "The Dark Knight", "The Lion King", "Star Wars", "<PERSON>", "Spider-Man", "Avatar", "Jaws", "Pulp Fiction", "The Matrix", "Iron Man", "Frozen", "<PERSON>", "The Incredibles", "Toy Story", "The Lord of the Rings", "Finding Nemo", "Black Panther", "The Wizard of Oz", "E.T. the Extra-Terrestrial", "The Silence of the Lambs", "Casablanca", "<PERSON> and the Beast", "The Graduate", "A Clockwork Orange", "The Exorcist", "The Shining", "Back to the Future", "Raiders of the Lost Ark", "A Nightmare on Elm Street", "The Terminator", "Ghostbusters", "Die Hard", "The Sixth Sense", "Men in Black", "The Green Mile", "Terminator 2", "The Departed", "Gladiator", "Goodfellas", "<PERSON><PERSON><PERSON>'s List", "Braveheart", "Star Wars: Episode IV", "La La Land", "The Social Network", "The Revenant", "The Queen's Gambit", "The Sound of Music", "The Grand Budapest Hotel", "The Irishman", "The Princess Bride", "The Great Gatsby", "The Breakfast Club", "The Pursuit of Happyness", "The Shape of Water", "The Truman Show", "The Blair Witch Project", "The Rocky Horror Picture Show", "The Usual Suspects", "Get Out", "Argo", "The Hunger Games", "Interstellar", "A Beautiful Mind", "The Big Lebowski", "American Beauty", "The Little Mermaid", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> and the Sorcerer's Stone", "Indiana Jones", "Fight Club", "Gone with the Wind", "A Star is Born", "Dunkirk", "It", "Mad <PERSON>", "Psycho", "<PERSON>", "<PERSON>", "Grease", "<PERSON>'s Day Off", "Top Gun", "Home Alone", "Jurassic World", "The Greatest Showman"], "Viewers' Rating": ["Five-star rating", "Four-star rating", "Three-star rating", "Two-star rating", "One-star rating", "Rave reviews", "Glowing reviews", "Positive feedback", "High ratings", "Top-rated", "Best-reviewed", "Outstanding reviews", "Excellent ratings", "Impressive score", "Critically acclaimed", "Popular choice", "Highly recommended", "10/10", "9/10", "8/10", "7/10", "6/10", "Highly praised", "Overwhelmingly positive", "Phenomenal ratings", "Stellar reviews", "Praise from viewers", "Great audience response", "Positive viewer comments", "Must-see", "Enthusiastic feedback", "Thumbs up", "Great word of mouth", "High satisfaction", "Impressive audience score", "Classic", "Overrated", "Underrated", "Timeless", "Mediocre", "Average", "Breathtaking", "Thrilling", "Captivating", "Disappointing", "Unforgettable", "Mind-blowing", "Riveting", "Intense", "Hilarious", "Touching", "Terrifying", "<PERSON><PERSON>", "Spectacular", "Masterpiece", "Unmissable", "Solid", "Exciting", "Uninspired", "Unbelievable", "Impressive", "Worth watching", "Forgettable", "Disgusting", "Thought-provoking", "Entertaining", "Unique", "Gripping", "Refreshing", "Must see", "Excellent", "Top choice", "Loved it", "A favorite", "Great film", "Amazing", "Phenomenal", "Outstanding", "Must watch", "Superb", "A winner", "Worth it", "A gem", "Terrific", "Awesome", "Fantastic", "Brilliant", "Top-notch", "Five-star", "Epic", "Incredible", "Fabulous", "A must-see", "Remarkable", "Exceptional", "Iconic", "Flawless", "Great", "Compelling", "Engrossing", "Enthralling", "Worthwhile", "Satisfying", "Enjoyable", "Decent", "5-star rating", "4-star rating", "3-star rating", "2-star rating", "1-star rating", "A+ rating", "A rating", "B+ rating", "B rating", "C+ rating", "C rating", "D+ rating", "D rating", "F rating", "Excellent rating", "Good rating", "Fair rating", "Average rating", "Poor rating", "Terrible rating", "Amazing rating", "Outstanding rating", "Exceptional rating", "Mediocre rating", "Subpar rating", "Spectacular rating", "Phenomenal rating", "Superb rating", "Below average rating", "Above average rating", "Decent rating", "Notable rating", "Impressive rating", "Lackluster rating", "Unimpressive rating"], "Year": ["2021", "2000s", "1990s", "1987", "1975", "1960s", "1950s", "1941", "1930s", "1925", "1910", "1900s", "1899", "1888", "1873", "1866", "1855", "1843", "1830s", "1822", "1811", "1800s", "1799", "1780s", "1776", "1765", "1754", "1742", "1730s", "1727", "1715", "1706", "1699", "1688", "1677", "2022", "1995", "1980s", "1977", "1942", "1910s", "1903", "2020-2023", "2010-2015", "2005-2010", "1990-1995", "1985-1990", "1975-1980", "1965-1970", "1955-1960", "1945-1950", "1935-1940", "1925-1930", "1915-1920", "1905-1910", "2024", "2016", "2015", "2007", "1998", "1989", "1979", "1969", "1959", "1949", "1999", "2005", "2010", "1968", "2003", "1984", "1992", "1956", "2009", "2020", "1982", "2017", "1965", "2000", "2012", "1972", "1990", "2006", "1985", "2019", "1993", "2002", "1980", "2018", "1970s", "1976", "1962", "2023", "1974", "2008", "1988", "1978", "2025", "2013", "1961.", "1983", "2001", "1990-2000", "1970", "1994", "2011", "1997", "1980-1990", "2014", "1998-2003"], "Genre": ["Comedy", "Action", "Drama", "Science fiction", "Horror", "Thriller", "Adventure", "Romance", "Fantasy", "Animation", "Crime", "Mystery", "Musical", "War", "Western", "Supernatural", "Historical", "Biographical", "Martial arts", "Spy", "Disaster", "Sports", "Superhero", "Teen", "Satire", "Noir", "<PERSON>lasher", "Mockumentary", "Cyberpunk", "Zombie", "Erotic", "Gothic", "Surreal", "Apocalyptic", "Family", "History", "Science Fiction", "Documentary", "Experimental", "Fairy Tale", "Film Noir", "Gangster", "Heist", "Martial Arts", "Political", "Psychological", "Road Movie", "Techno-thriller", "Urban", "Sci-Fi", "Biopic", "Coming of Age", "Dracula", "Samurai", "Alien", "Post-apocalyptic", "Time travel", "Vampire", "Werewolf", "Ghost", "Dystopian", "Anthology", "Biography", "Music", "Silent", "Found Footage", "<PERSON><PERSON><PERSON>", "Monster"], "Director": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Coen Brothers", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Sofia Coppola", "Guillermo <PERSON> Toro", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>ho", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "MPAA Rating": ["PG-13", "R", "G", "NC-17", "PG", "MA", "TV-MA", "NR", "X", "Unrated", "M", "U", "A", "TV-G"], "Plot": ["Time travel", "Space exploration", "Treasure hunt", "Undercover agent", "Cop vs. criminal", "Love triangle", "Revenge plot", "Zombie apocalypse", "Alien invasion", "Identity theft", "Wilderness survival", "Mystery crime", "Political scandal", "Robot rebellion", "Historical war", "Family drama", "International espionage", "Cyber hacking", "Superhero origin", "Courtroom drama", "Post-apocalyptic world", "Secret mission", "Sports rivalry", "Animal adventure", "Mythical quest", "Star Wars: Episode IX", "<PERSON> and the Sorcerer's Stone", "The Godfather", "The Lion King", "The Shawshank Redemption", "Inception", "The Matrix", "Titanic", "The Avengers", "<PERSON>", "The Dark Knight", "Pulp Fiction", "Jurassic Park", "The Lord of the Rings", "Frozen", "The Terminator", "Toy Story", "The Silence of the Lambs", "Jaws", "Black Panther", "The Wizard of Oz", "E.T. the Extra-Terrestrial", "The Sixth Sense", "Back to the Future", "Finding Nemo", "Redemption arc", "Undercover mission", "Quest for treasure", "Political conspiracy", "Survival story", "Battle between good and evil", "Intergalactic war", "Heist", "Coming-of-age story", "Romantic comedy", "Mysterious disappearance", "Historical drama", "Technological revolution", "Supernatural powers", "Environmental disaster", "Forbidden love affair", "Gang warfare", "Corporate espionage", "Artificial intelligence uprising", "Superhero origin story", "Assassination plot", "Undercover operation", "Amnesia", "Post-apocalyptic survival", "Con artist scheme", "Supernatural haunting", "War drama", "Medical mystery", "Family inheritance dispute", "Cybercrime", "Sports underdog story", "World War II resistance fighter", "Undercover cop", "Zombie apocalypse survivor", "Heist mastermind", "Cyborg assassin", "Time-traveling scientist", "High school misfit", "Sword-wielding samurai", "Alien invasion survivor", "Post-apocalyptic scavenger", "Treasure hunter", "Superhero sidekick", "Dystopian society rebel", "Wilderness survival expert", "Mafia boss", "Archaeologist adventurer", "Vampire hunter", "Cult escapee", "Cyberpunk hacker", "Sorcerer's apprentice", "Robot uprising leader", "Race car driver", "Martial arts master", "Space pirate", "Conspiracy theorist"], "Actor": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Vin Diesel", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> Jr.", "Gal Gadot", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "Trailer": ["Teaser", "Preview", "Sneak peek", "Film clip", "Movie snippet", "Trailer", "Screenings", "Showings", "Cinema listings", "Box office availability", "Clip", "Promo", "Commercial", "Teaser trailer", "TV spot", "Extended preview", "First look", "Teaser clip", "Featurette", "Extended trailer", "Exclusive footage", "Theatrical trailer", "Official trailer", "Exclusive look", "Trailer release"], "Song": ["Bohemian Rhapsody", "My Heart Will Go On", "Grease Lightning", "I Will Always Love You", "<PERSON><PERSON><PERSON>", "Let It Go", "Staying Alive", "I Want to Hold Your Hand", "Girls Just Want to Have Fun", "Purple Rain", "Uptown Funk", "\"I Believe", "Don't Stop Believin'", "Can't Stop the Feeling", "I Will Survive", "Twist and Shout", "Shake It Off", "I'm Every Woman", "\"What", "Boogie Wonderland", "The Power of Love", "\"You're the One That", "Don't You", "Eye of the Tiger", "Unchained Melody", "Stayin' Alive", "The Sound of Music", "A Whole New World", "<PERSON><PERSON>", "Moon River", "Under the Sea", "Take My Breath Away", "Man in the Mirror", "\"I Don't Want to Miss", "My Girl", "Everything Is Awesome", "You'll Be in My Heart", "My Way", "I Got You Babe", "Can't Stop the Feeling!", "Lose Yourself", "Shallow", "<PERSON> and the Beast", "I Don't Want to Miss a Thing", "Happy", "Jai <PERSON>", "Let's Get Loud", "<PERSON><PERSON>", "Old Time Rock and Roll", "Don't You (Forget About Me)", "Skyfall", "The Lion Sleeps Tonight", "Thriller", "Wind Beneath My Wings", "A Star is Born", "La La Land", "My <PERSON> Lady", "Singin' in the Rain", "The Lion King", "Grease", "The Greatest Showman", "<PERSON><PERSON>", "Frozen", "The Little Mermaid", "<PERSON><PERSON><PERSON>", "Mamma Mia!", "Les Misérables", "Pitch Perfect", "Chicago", "Rock of Ages", "Once", "Hairspray", "Dreamgirls", "<PERSON>", "High School Musical", "Greased Lightning", "<PERSON><PERSON><PERSON>", "The Circle of Life", "The Bare Necessities", "Part of Your World", "How Far I'll Go", "\"When You Wish Upon", "Yellow Submarine", "\"Livin' on", "White Christmas", "The Sound of Silence", "Somewhere Over the Rainbow", "All That Jazz"], "Review": ["Gripping", "Captivating", "Entertaining", "Thought-provoking", "Compelling", "Riveting", "Hilarious", "Touching", "Thrilling", "Breathtaking", "Heartwarming", "Exhilarating", "Electrifying", "Mesmerizing", "Brilliant", "Masterful", "Spectacular", "Impactful", "Poignant", "Terrifying", "Astonishing", "Emotional", "Mind-blowing", "Outstanding", "Awe-inspiring", "Phenomenal", "Flawless", "Unforgettable", "Dazzling", "Superb", "Impressive", "Intense", "Enthralling", "Tear-jerking", "Inspiring", "Unpredictable", "Suspenseful", "Emotionally charged", "Absorbing", "Remarkable", "Memorable", "Sensational", "Unmissable", "Must-see", "Gripping storyline", "Stellar performances", "Mind-blowing special effects", "Intriguing plot twists", "Masterful direction", "Compelling character development", "Heartwarming moments", "Jaw-dropping action sequences", "Beautiful cinematography", "Thought-provoking themes", "Engaging dialogue", "Spectacular visual effects", "Memorable soundtrack", "Riveting suspense", "Emotionally impactful", "Hilarious comedy", "Intense drama", "Authentic period details", "Clever storytelling", "Outstanding ensemble cast", "Unique and original", "Impressive production design", "Chilling horror elements", "Breathtaking set pieces", "Absorbing screenplay", "Rich and immersive world-building", "Hauntingly beautiful", "Powerful message", "Spellbinding entertainment", "Well-paced and tightly edited", "Captivating performances", "Unforgettable moments", "Exhilarating and thrilling", "Relevant social commentary", "Satisfying conclusion", "Epic", "Masterpiece", "Stellar", "Amazing", "Extraordinary", "<PERSON><PERSON><PERSON>", "Incredible", "Fantastic", "Terrific", "Unbelievable", "Sublime", "Infatuating", "Jaw-dropping visuals", "Thought-provoking storyline", "Unforgettable performances", "Captivating cinematography", "Edge-of-your-seat suspense", "Genuinely hilarious", "Touching and emotional", "Thrilling action sequences", "Impressive special effects", "Iconic quotes", "Epic scale and ambition", "Intense and gripping", "Powerful and moving", "Explosive and adrenaline-pumping", "Spectacular set design", "Impeccable sound design", "Soaring musical score", "Authentic and realistic", "Poignant and profound", "Visually stunning", "Chilling and eerie", "Lively and entertaining", "Intimate and heartfelt", "Immersive world-building", "Dynamic and energetic", "Provocative and thought-provoking", "Show-stopping performances", "Unforgettable characters", "Mesmerizing and enchanting", "Nostalgic and charming", "Gripping and suspenseful", "Awe-inspiring and breathtaking", "Exhilarating and action-packed."], "Character": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Captain <PERSON>", "Wonder Woman", "Iron Man", "<PERSON>", "<PERSON>", "Spider-Man", "<PERSON><PERSON><PERSON>", "<PERSON>", "Black Widow", "<PERSON>", "<PERSON>", "<PERSON>", "Deadpool", "Rey", "The Joker", "Black Panther", "Princess <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Han Solo", "<PERSON><PERSON>", "The Terminator", "Neo", "<PERSON>", "<PERSON>", "<PERSON>", "Captain <PERSON>", "The Hulk", "Wolverine", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Frankenstein's Monster", "The Invisible Man", "<PERSON>", "Mr. <PERSON>", "Indiana Jones", "<PERSON><PERSON>", "<PERSON>", "Maleficent", "<PERSON><PERSON><PERSON>", "Jasmine", "Simba", "Scar", "Pocahontas", "Hercules", "Ariel", "<PERSON>", "Flik", "Legolas", "Aragorn", "<PERSON><PERSON> Vader", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Lightning McQueen", "Mr. Incredible", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Nemo", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>uss in Boots", "<PERSON>", "<PERSON>", "Elastigirl", "Violet", "Dash", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Jaws", "Terminator", "Rocky <PERSON>", "<PERSON>", "<PERSON><PERSON>", "Dracula", "The Grinch", "The Little Mermaid", "Cinderella", "<PERSON><PERSON><PERSON>", "Tiana", "<PERSON>", "Captain <PERSON>", "Trinity", "Morpheus", "Professor <PERSON>", "Joker"]}, "Plot summary and synopsis": {"Title": ["The Lord of the Rings", "The Shawshank Redemption", "The Matrix", "Titanic", "The Godfather", "Pulp Fiction", "Inception", "Jurassic Park", "The Dark Knight", "<PERSON>", "The Silence of the Lambs", "The Lion King", "The Avengers", "The Terminator", "Toy Story", "ET", "Gladiator", "<PERSON> and the Sorcerer's Stone", "Jaws", "Back to the Future", "Raiders of the Lost Ark", "<PERSON><PERSON><PERSON>'s List", "The Wizard of Oz", "The Sound of Music", "Gone with the Wind", "Casablanca", "Star Wars: Episode IV", "The Exorcist", "A Clockwork Orange", "The Shining", "Braveheart", "Alien", "<PERSON>ar<PERSON>", "The Sixth Sense", "The Social Network", "Goodfellas", "The Princess Bride", "<PERSON><PERSON><PERSON>", "The Empire Strikes Back", "The Breakfast Club", "The Big Lebowski", "The Graduate", "Some Like It Hot", "<PERSON>", "Blade Runner", "Star Wars", "Saving Private <PERSON>", "E.T. the Extra-Terrestrial", "<PERSON>", "Ghostbusters", "A Few Good Men", "The Goonies", "American Beauty", "Black Swan", "Avatar", "The Hunger Games", "Interstellar", "La La Land", "The Shape of Water", "The Grand Budapest Hotel", "Get Out", "<PERSON><PERSON>", "Fight Club", "The Godfather Part II", "The Departed", "Twelve Monkeys", "Psycho", "The Green Mile", "Taxi Driver", "From Here to Eternity", "A Streetcar Named Desire", "<PERSON>'s Day Off", "Pretty Woman", "Dirty Dancing", "Home Alone", "When <PERSON> Met <PERSON>", "Ghost", "Mrs. <PERSON>", "The Pursuit of Happyness", "The Great Gatsby", "The Incredibles", "<PERSON><PERSON><PERSON>", "Die Hard", "Inglourious Basterds", "A Beautiful Mind", "Black Panther", "The Revenant", "The Truman Show", "The King's Speech", "The Hurt Locker", "The Usual Suspects", "The Prestige", "Moulin Rouge!"], "Viewers' Rating": ["Gripping", "Must see", "Riveting", "Compelling", "Engaging", "Entertaining", "Captivating", "Spellbinding", "Absorbing", "Addictive", "Intriguing", "Fascinating", "Enthralling", "Suspenseful", "Dramatic", "Emotional", "Touching", "Inspiring", "Heartfelt", "Uplifting", "Thought-provoking", "Stirring", "Haunting", "Electrifying", "Thrilling", "Action-packed", "Intense", "Dark", "Twisted", "Mind-bending", "Unpredictable", "Chilling", "Unsettling", "5-star rating", "4-star rating", "3-star rating", "2-star rating", "1-star rating", "10/10 rating", "9/10 rating", "8/10 rating", "7/10 rating", "6/10 rating", "5/10 rating", "4/10 rating", "3/10 rating", "2/10 rating", "1/10 rating", "Thumbs up", "Thumbs down", "Highly recommended", "Recommended", "Must-see", "Pass", "A+ rating", "A rating", "B rating", "C rating", "D rating", "F rating", "Five-star review", "Four-star review", "Three-star review", "Two-star review", "One-star review", "Top rating", "Outstanding", "Mediocre", "Must watch", "Perfect 10", "Solid plot", "Gripping storyline", "5 stars", "Epic", "Edge-of-your-seat", "Mind-blowing", "Masterpiece", "Phenomenal", "Breathtaking", "Top-notch", "Unforgettable", "Brilliantly written", "Awe-inspiring", "Emotionally moving", "Well-crafted", "Surprisingly good", "Solid 9", "Exhilarating", "Terrific", "Impressive", "Powerful", "Exceptional", "Highly entertaining", "Exciting", "Compulsive", "Unmissable", "Mesmerizing", "Astonishing", "Masterful", "Jaw-dropping", "Hilarious", "Heartwarming", "Poignant", "Insightful", "Out of this world", "Impactful", "Remarkable", "Spectacular", "Worthwhile"], "Year": ["1980", "1995", "2001", "1950s", "1960s", "1970s", "1980s", "1990s", "2000s", "2010s", "2020s", "1930-1940", "1950-1960", "1970-1980", "1990-2000", "2010-2020", "1940-1950", "1960-1970", "1980-1990", "2000-2010", "2020-2030", "1930-1950", "1960-1980", "1990-2010", "1930-1960", "1970-2000", "1950-1970", "1980-2000", "1960-1990", "1970-2010", "1980-2010", "1990-2020", "2000-2030", "1950-1990", "1960-2020", "1940s", "1930s", "1920s", "1910s", "1975-1985", "1995-2005", "1910-1920", "1945-1955", "1965-1975", "2015-2025", "1988", "1999", "2004", "1977", "1955", "1939", "1969", "1985", "1991", "2008", "1985-1990", "1995-2000", "2005-2010", "2020-2022", "2015", "1987", "1972", "1965", "1954", "1943", "1938", "2023", "1993", "2003", "2012", "1989", "1978", "1958", "1947", "1935", "2025", "1997", "1990", "2005", "1984", "1976", "1998", "2019", "1967", "1973", "2018", "1963", "2011", "1979", "2016", "1968", "2007", "2014", "1971", "2009", "1983", "1986", "2017", "1970", "1975", "2006", "1994", "1956", "1962", "1952", "1992", "1974", "2010", "1959"], "Genre": ["Action", "Adventure", "Animation", "Comedy", "Crime", "Documentary", "Drama", "Family", "Fantasy", "Film Noir", "Horror", "Musical", "Mystery", "Romance", "Sci-Fi", "Sports", "Superhero", "Thriller", "War", "Western", "Biographical", "Disaster", "Historical", "Martial Arts", "Psychological", "Satire", "Silent", "Spy", "Supernatural", "Teen", "Travel", "True Crime", "Urban", "Women's", "Apocalyptic", "Science fiction", "Biography", "Space opera", "Martial arts", "Courtroom", "Crime drama", "Mockumentary", "Noir", "Psychological thriller", "Zombie", "War documentary", "History", "Music", "Sport", "Coming of age", "Alien invasion", "Found footage", "Post-apocalyptic", "Cyberpunk", "<PERSON>lasher", "Musical comedy", "Experimental", "Road", "Cult", "Independent", "Dance", "Black comedy", "Period", "Christmas-themed"], "Director": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Sofia Coppola", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Guillermo <PERSON> Toro", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>ho", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Coen Brothers", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>wook", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "The Coen Brothers", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "MPAA Rating": ["PG-13", "R", "G", "PG", "NC-17", "Unrated", "NR", "M", "E", "GP", "Not Rated", "PG-14", "X"], "Plot": ["Love triangle", "Murder mystery", "Time travel", "Undercover operation", "Heist", "Family secret", "Revenge", "Political conspiracy", "Apocalyptic event", "Identity theft", "Coming of age", "Band of outlaws", "Alien invasion", "Quest for power", "Courtroom drama", "Supernatural phenomenon", "Survival in the wilderness", "Betrayal", "Forbidden love", "Artificial intelligence uprising", "Drug cartel", "Corporate espionage", "Historical revolution", "Epic battle", "Identity crisis", "Family inheritance", "Revenge plot", "Supernatural powers", "Revolutionary war", "Historical figure assassination", "Amnesia", "Quest for", "Pandemic outbreak", "Immigrant experience", "Space exploration", "Prison break", "Cyber warfare", "Serial killer on the loose", "Betrayal and double-cross", "Environmental disaster", "Art theft", "Undercover mission", "Zombie apocalypse", "Deep sea adventure", "Bank robbery", "Organized crime", "Post-apocalyptic world", "Western frontier", "Human cloning", "Treasure hunt", "Witness protection", "Espionage", "Artificial intelligence", "Exorcism", "Cyber terrorism", "Rags to riches", "Space colonization", "Small town mystery", "Superhero origin story", "Haunted house", "Historical war drama", "Robot uprising", "Family secrets", "Cyber espionage", "Medical thriller", "Virtual reality world", "Technological dystopia", "Undercover cop", "Assassination attempt", "Art heist", "Zombie outbreak", "Survival story", "Race against time", "Historical war", "Drug smuggling", "Missing person", "Cybercrime", "Wilderness adventure", "Family inheritance feud"], "Actor": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> Jr.", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Gal Gadot", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Al Pacino", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "Trailer": ["Teaser", "Sneak peek", "Preview", "Promo", "Highlight reel", "Glimpse", "Sizzle reel", "Clip", "Featurette", "Trailer teaser", "Teaser trailer", "Extended trailer", "First look", "Official trailer", "Footage", "Excerpt", "Teaser clip", "Trailer snippet", "Preview clip", "TV spot", "Trailer release", "Highlight", "Trailer", "Promotional video"], "Song": ["Purple Rain", "Bohemian Rhapsody", "Don't Want to Miss a Thing", "My Heart Will Go On", "Eye of the Tiger", "I Will Always Love You", "Let It Go", "Shallow", "<PERSON><PERSON><PERSON>", "Crazy in Love", "Unchained Melody", "Ghostbusters", "I Believe I Can Fly", "The Sound of Silence", "I'm Gonna Be (500 Miles)", "You've Got a Friend in Me", "Jai <PERSON>", "Girls Just Want to Have Fun", "<PERSON> <PERSON><PERSON>", "Twist and Shout", "You Never Can Tell", "Boogie Wonderland", "Under the Boardwalk", "Hooked on a Feeling", "<PERSON> and the Beast", "Can't Stop the Feeling", "Circle of Life", "Don't You", "Can't Help Falling in Love", "Livin' on a Prayer", "Take My Breath Away", "Everything Is Awesome", "Don't Stop Believin'", "Flashdance... What a Feeling", "I Will Follow Him", "<PERSON><PERSON><PERSON>", "Stayin' Alive", "I Am a Man of Constant <PERSON>rrow", "The Sound of Music", "Time of My Life", "The Circle of Life", "Pure Imagination", "Greased Lightnin'", "I Wanna Dance with <PERSON>", "Happy", "We Will Rock You", "Can You Feel the Love Tonight", "You're the One That I Want", "Moon River", "This Is Me", "In the Air Tonight", "Old Time Rock and Roll", "I've Had The Time of My Life", "\"Kiss from", "Fight the Power", "Endless Love", "Memory", "Skyfall", "Chariots of Fire", "Gangsta's Paradise", "Can't Stop the Feeling!", "I Want to Hold Your Hand", "My Girl", "A Whole New World", "I Just Can't Wait to be King", "Danger Zone", "My Generation", "I Love Rock 'n' Roll", "You Can't Hurry Love"], "Review": ["Gripping storyline", "Intriguing plot", "Compelling narrative", "Engaging storyline", "Twisty plot", "Complex narrative", "Captivating storyline", "Multi-layered plot", "Thought-provoking plot", "Suspenseful narrative", "Exciting storyline", "Fast-paced plot", "Well-developed narrative", "Emotionally charged storyline", "Twisted plot", "Intricate narrative", "Rich storyline", "Deep plot", "Detailed narrative", "Interesting storyline", "Episodic plot", "Dynamic narrative", "Vivid storyline", "Heart-wrenching plot", "Masterful narrative", "Slow-burn storyline", "Elegant plot", "Surreal narrative", "Dark plot", "Singular narrative", "Emotionally resonant storyline", "Convincing plot", "Enigmatic narrative", "Poignant storyline", "Gripping", "Compelling", "Riveting", "Captivating", "Engaging", "Intriguing", "Absorbing", "Fascinating", "Exciting", "Thrilling", "Action-packed", "Intense", "Suspenseful", "Enthralling", "Mesmerizing", "Thought-provoking", "Mind-bending", "Complex", "Intricate", "Multilayered", "Twists and turns", "Well-paced", "Well-structured", "Well-developed", "Well-crafted", "Complicated", "Layered", "Subtle", "Nuanced", "Deep", "Emotional", "Powerful", "Impactful", "Poignant", "Heart-wrenching", "Entertaining", "Touching", "Empowering", "Inspiring", "Uplifting", "Heartfelt", "Heartwarming", "<PERSON><PERSON>", "Magical", "Enchanting", "Quirky", "Witty", "Humorous", "Hilarious", "<PERSON><PERSON><PERSON>", "Inventive", "Original", "Unique", "Unconventional", "Imaginative", "Engrossing", "Unpredictable", "Emotionally charged", "Impressive", "Innovative", "Astonishing", "Profound", "Satisfying", "<PERSON><PERSON><PERSON> written", "Masterful", "Illustrious", "Remarkable", "Compelling plot", "Engaging narrative", "Intricate storyline", "Well-developed plot", "Fast-paced", "Slow-burn", "Multilayered plot", "Riveting storytelling", "Surprising plot twists", "Well-crafted plot", "Detailed synopsis", "Captivating plot", "<PERSON><PERSON> plot", "Well-executed story", "Interesting plot developments", "Multi-faceted plot", "Engrossing plot", "Satisfying resolution", "Dynamic storytelling", "Complicated plot", "Layered narrative", "Suspenseful plot", "Intertwining storylines", "Captivating narrative", "Solid storytelling", "Dense narrative", "Elaborate plot", "Intriguing plot points", "Well-constructed storyline"], "Character": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON> Vader", "<PERSON>", "Princess <PERSON><PERSON>", "<PERSON>", "Indiana Jones", "Spider-Man", "Captain <PERSON>", "Iron Man", "Black Widow", "Wonder Woman", "<PERSON>", "<PERSON>", "The Joker", "<PERSON>", "<PERSON>", "<PERSON>", "Simba", "<PERSON><PERSON><PERSON>", "Scar", "Ariel", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Beast", "<PERSON>", "Pinocchio", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Captain <PERSON>", "Tinker Bell", "Cinderella", "<PERSON>", "Maleficent", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Mushu", "<PERSON><PERSON>", "Pocahontas", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Neo", "Captain <PERSON>", "Rey", "Rocky <PERSON>", "The Terminator", "Black Panther", "Aliens", "Predator", "The Hulk", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Professor <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Legolas", "Aragorn", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "The T-800", "Captain <PERSON>", "<PERSON>", "Trinity", "<PERSON>", "Han Solo", "<PERSON>", "Wolverine", "Dr. <PERSON>", "Dr. <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Voldemort", "Professor <PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Sauron", "<PERSON>", "R2-D2", "C-3PO", "<PERSON>", "<PERSON>", "M", "Q", "Jaws", "Dr. <PERSON>", "Professor <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Catwoman", "The Flash", "The Green Lantern", "The Penguin", "Storm", "Professor <PERSON>", "<PERSON><PERSON><PERSON>", "Mystique", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Elastigirl", "Mr. Incredible", "Morpheus", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Albus Dumbledore", "<PERSON><PERSON><PERSON>", "<PERSON>pock", "<PERSON>"]}, "Cast and crew information": {"Title": ["The Godfather", "Pulp Fiction", "Titanic", "Parasite", "The Shawshank Redemption", "Inception", "The Dark Knight", "<PERSON>", "<PERSON><PERSON><PERSON>'s List", "The Matrix", "<PERSON><PERSON><PERSON> Unchained", "Goodfellas", "The Silence of the Lambs", "Fight Club", "The Lord of the Rings", "The Departed", "The Social Network", "The Avengers", "Toy Story", "Terminator", "The Lion King", "Gladiator", "The Godfather Part II", "Black Panther", "The Grand Budapest Hotel", "Back to the Future", "The Wizard of Oz", "Jurassic Park", "The Sound of Music", "The Exorcist", "A Clockwork Orange", "The Shining", "La La Land", "Jaws", "A Beautiful Mind", "The Sixth Sense", "A Streetcar Named Desire", "Training Day", "The Fugitive", "Philadelphia", "The Green Mile", "The Usual Suspects", "Reservoir Dogs", "12 Angry Men", "Casablanca", "The Incredibles", "Finding Nemo", "Star Wars: Episode IV", "Avatar", "The Shape of Water", "Moonlight", "Interstellar", "Jurassic World", "The Revenant", "The Martian", "Avengers", "<PERSON> and the Sorcerer's Stone", "12 Years", "The Princess Bride", "The Big Lebowski", "Fargo", "The Truman Show", "The Breakfast Club", "The Prestige", "<PERSON><PERSON>", "Joker", "A Star is Born", "Les Misérables", "Frozen", "Coco", "Whiplash", "Blade Runner 2049", "Arrival", "Dunkirk", "Mad <PERSON>", "Gravity", "American Hustle", "Her", "Silver Linings Playbook", "Gone with the Wind", "<PERSON>", "Psycho", "The Graduate", "Apocalypse Now", "Black Swan", "Boyhood", "Argo", "Life of Pi", "Slumdog Millionaire", "Juno", "Lost in Translation", "School of Rock", "The Artist", "The King's Speech", "The Hurt Locker", "Star Wars", "Saving Private <PERSON>", "The Hateful Eight", "Kill Bill", "The Great Gatsby", "The Imitation Game"], "Viewers' Rating": ["Must-see", "Excellent", "Outstanding", "Brilliant", "Amazing", "Fantastic", "Masterpiece", "Phenomenal", "Captivating", "Riveting", "Compelling", "Gripping", "Engaging", "Memorable", "Awe-inspiring", "Impressive", "Exceptional", "Superb", "Terrific", "Well-acted", "Emotionally engaging", "Truly special", "Unforgettable", "Mind-blowing", "Worthwhile", "Remarkable", "Thought-provoking", "Thoughtful", "Impactful", "Inspirational", "Favourite", "Thrilling", "<PERSON><PERSON>", "5-star rating", "Top-rated", "Highly recommended", "10/10", "9 out of 10", "Perfect", "4-star rating", "8 out of 10", "3.5 stars", "7 out of 10", "Worth watching", "3-star rating", "6 out of 10", "Solid performance", "2-star rating", "5 out of 10", "Average", "1-star rating", "Below average", "Disappointing", "Mixed reviews", "4 out of 10", "Overrated", "3 out of", "Mediocre", "2 out of", "Unimpressive", "1 out of", "Poorly received", "Not worth watching", "Terrible", "0 out of", "Avoid at all costs", "Rave reviews", "Positive feedback", "Negative feedback", "Well-received", "<PERSON>lop", "Underrated", "Critically acclaimed", "Audience favorite", "Polarizing", "Love it or hate it", "Brings", "Groundbreaking", "Top-notch", "A hit with audiences", "Divisive", "Lackluster", "Unremarkable", "Stands out", "<PERSON>k of chemistry", "Uninspired", "Five-star rating", "Four-star rating", "Three-star rating", "Two-star rating", "One-star rating", "Ten-star rating", "Highly rated", "Low-rated", "Outstanding performances", "Lackluster performances", "Skip it", "Crowd-pleaser", "Stellar cast", "Memorable roles", "Unconvincing acting", "Riveting storytelling", "Predictable plot", "Fan favorite", "Unpopular opinion", "Groundbreaking cinematography", "Mediocre direction", "Compelling performances", "Amateurish production", "Unbelievable special effects", "Great performance", "5 stars", "Oscar-worthy", "Talented actors", "10 out of 10", "Amazing chemistry", "Outstanding acting", "Top-notch performances", "Impressive ensemble", "Superb casting", "Perfect casting", "Brilliant performances", "Standout performances", "Flawless acting", "A-list talent", "Phenomenal cast", "Must-see performances", "Solid casting", "4 out of", "Excellent chemistry", "Incredible performances", "Well-cast", "Exceptional acting", "Memorable performances", "Best casting", "5 out of", "Strong performances", "Believable performances", "Captivating performances", "Remarkable casting"], "Year": ["1995", "2000s", "1987", "2010-2015", "1973", "1960s", "2018", "1985", "2005", "1992", "2008", "1979", "1999", "2012", "1982", "1970s", "2003", "1990", "2016", "1988", "2002", "1997", "1980", "2014", "1975", "2007", "1998", "1983", "2011", "1989", "2004", "2017", "1981", "1993", "2009", "2005-2010", "2021", "1999-2003", "2015", "1969", "1972", "2010", "1950s", "2002-2005", "1965", "2019", "1977", "1962", "1986", "1991", "1968", "2006", "1974", "1996", "1950", "1965-1970", "2000", "2020", "1980-1985", "1960", "1978", "2022", "2001", "2013", "1971", "1984", "1964", "1970", "1956", "1975-1980", "1964-1968", "1976"], "Genre": ["Action", "Adventure", "Animation", "Comedy", "Crime", "Drama", "Family", "Fantasy", "Horror", "Musical", "Mystery", "Romance", "Sci-Fi", "Thriller", "War", "Western", "Biographical", "Documentary", "Historical", "Martial arts", "Science fiction", "Superhero", "Suspense", "Teen", "Action-comedy", "Crime drama", "Fantasy adventure", "Period drama", "Romantic comedy", "Musical drama", "Sci-fi thriller", "War romance", "Comedy-drama", "Horror-thriller", "Action-adventure", "Biography", "Film Noir", "History", "Music", "Short", "Sport", "Independent", "Noir", "Experimental", "Erotic", "Arthouse", "Cult", "Mockumentary", "LGBT", "Adult", "Disaster", "<PERSON><PERSON><PERSON>", "Science Fiction", "Spy", "Martial Arts", "Sports", "Psychological", "Supernatural", "Political", "Religious", "Dance", "Environmental", "Surreal", "Fairy Tale", "Satirical", "Courtroom Drama", "Epic", "Gangster", "Heist", "Monster", "Period", "Satire", "Zombie", "Medical", "<PERSON>lasher"], "Director": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Sofia Coppola", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Guillermo <PERSON> Toro", "<PERSON>", "<PERSON>", "<PERSON><PERSON>ho", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>wook", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Howard <PERSON>", "<PERSON><PERSON>", "<PERSON>", "Coen Brothers", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>a", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "MPAA Rating": ["PG-13", "R", "G", "NC-17", "PG", "M", "GP", "NR", "U", "18A", "X", "Unrated"], "Plot": ["Time travel", "Artificial intelligence", "Bank heist", "Love triangle", "American Civil War", "Cyberpunk", "Serial killer", "Alien invasion", "Superhero origin", "Haunted house", "Undercover agent", "Zombie apocalypse", "Identity theft", "High school reunion", "Treasure hunt", "Assassin's creed", "Space exploration", "Revenge plot", "Political conspiracy", "Post-apocalyptic world", "Amnesia", "Robbery gone wrong", "Drug cartel", "Pirate treasure", "Dangerous obsession", "War strategy", "Murder investigation", "Undercover operation", "Robbery scheme", "Kidnapping plot", "Survival mission", "Quest for treasure", "Family drama", "High school rivalry", "Environmental activism", "Art heist", "Cyberwarfare", "Cult initiation", "Robot uprising", "Psychological thriller", "Superhero origin story", "A haunted house", "Disappearance mystery.", "Revenge", "Undercover cop", "Coming of age", "Murder mystery", "Midlife crisis", "Hero's journey", "High school prom", "Family road trip", "Historical war", "Artificial intelligence uprising", "Immigrant experience", "Supernatural powers", "Cybercrime", "Zombie outbreak", "Life after death", "Mafia betrayal", "Virtual reality adventure", "Heist", "Betrayal", "Forbidden love", "Quest for", "Technology gone wrong", "Cult ritual", "Family feud", "Survival in the wilderness", "Drug trafficking", "Corporate espionage", "Historical reenactment", "Art forgery", "Government cover-up", "Witness protection", "Environmental disaster", "<PERSON><PERSON>", "Family secret", "Family inheritance", "Secret mission", "Survival story", "Historical event", "Kidnapping", "War", "Struggle for power", "High school drama", "Apocalypse", "Mafia", "Corruption"], "Actor": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> Jr.", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Gal Gadot", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "Trailer": ["Teaser", "Sneak peek", "Preview", "Clip", "In-Cinema", "TV spot", "Featurette", "Behind the scenes", "First look", "Official trailer", "Promo", "Teaser trailer", "Teaser clip", "Trailer release", "Advance look", "Footage", "Sizzle reel", "Actor/actress names", "Director", "Producer", "Cinematographer", "Screenwriter", "Costume designer", "Composer", "Set designer", "Makeup artist", "Stunt coordinator", "Spotlight", "Exclusive look", "Glimpse"], "Song": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Queen", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Prince", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Eminem", "<PERSON> <PERSON>", "<PERSON>", "The Beatles", "<PERSON>her", "The Rolling Stones", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Pink", "Journey", "Purple Rain", "La La Land", "A Star Is Born", "The Sound of Music", "The Blues Brothers", "Dreamgirls", "Moulin Rouge!", "The Greatest Showman", "Singin' in the Rain", "Chicago", "The Lion King", "Bohemian Rhapsody", "Dirty Dancing", "Grease", "The Bodyguard", "8 Mile", "Sister Act", "<PERSON><PERSON><PERSON>", "Into the Woods", "Across the Universe", "<PERSON>", "High School Musical", "Mamma Mia!", "West Side Story", "Aashiqui 2", "<PERSON><PERSON>", "Frozen", "<PERSON> and the Beast", "<PERSON><PERSON><PERSON>", "The Jungle Book", "The Lego Movie", "Pitch Perfect", "Rock of Ages", "School of Rock", "Phantom of the Opera", "Hairspray", "The Rocky Horror Picture Show"], "Review": ["Stellar performances", "Chemistry between the actors", "Leading actor's strong performance", "Supportive cast", "Casting choices", "Ensemble cast", "Cameo appearances", "Main cast", "Cast chemistry", "Memorable characters", "Iconic roles", "Underwhelming performances", "Lackluster acting", "Miscasting", "Strong supporting roles", "Lead actress's performance", "Scene-stealing performances", "Talented ensemble", "Cast diversity", "Cast dynamics", "Director's cameo", "Director's vision for the cast", "Casting depth", "Cast cohesion", "Role diversity", "Chemistry between director and actors", "Director's choices for the cast", "Supporting actress's performance", "Cast unity", "Leading actress's strong performance", "Director's previous collaborations with the cast", "Cast development", "Director's influence on the cast", "Director's rapport with the actors", "Outstanding performances", "Stellar cast", "Spectacular acting", "Impressive chemistry", "Multi-talented cast", "Phenomenal ensemble", "Electrifying performances", "Exceptional cast selection", "Superb casting", "A-list actors", "Powerhouse cast", "Strong supporting cast", "Versatile actors", "Captivating actors", "Convincing portrayals", "Excellent character development", "Top-notch acting", "Memorable performances", "Compelling cast chemistry", "Amazing on-screen presence", "Talented group of actors", "Convincing performances", "Solid ensemble", "Skillful depiction of emotions", "Outstanding directing", "Masterful storytelling", "Well-executed cinematography", "Brilliant screenwriting", "Seamless editing", "Remarkable production design", "Resourceful direction", "Innovative camera work", "Expert use of sound", "Powerful music score", "Incredible costume design", "A standout cast", "Brilliant direction", "Superb acting", "Impressive ensemble", "Talented cast", "Strong chemistry", "Well-crafted characters", "Perfect casting", "Stellar lineup", "Great screen presence", "Resonating performances", "Dynamic ensemble", "Compelling characters", "A-list talent", "Stellar acting", "Incredible chemistry", "Top-notch performances", "Masterful performances", "Engaging characters", "Impressive lineup", "Phenomenal acting", "Solid performances", "Captivating chemistry", "Outstanding chemistry", "Seamless ensemble", "Impeccable casting", "Genuine chemistry", "Impressive character development", "Powerhouse performances", "Effortless chemistry", "Unforgettable performances", "Solid acting", "Phenomenal cast", "Outstanding ensemble", "Great chemistry", "Impressive line-up", "Well-casted", "Standout performances", "Masterful portrayal", "Exceptional performances", "Believable characters", "Cinematic powerhouse", "Veteran actors", "Fresh faces", "Dynamic duo", "Exceptional chemistry", "Dynamic performances", "Unforgettable characters", "Engaging performances", "Stellar lead", "Electrifying cast", "Perfectly matched cast", "Award-worthy performances", "Strong on-screen presence", "Cohesive cast", "Engaging chemistry", "Stellar casting choices", "Great on-screen dynamics", "The chemistry between the lead actors was palpable", "Exceptional casting", "Stellar ensemble cast", "Strong and believable performances", "Standout lead actors", "Brilliant acting", "Impeccable casting choices", "Breathtaking performances", "Top-notch cast", "Perfectly cast", "Well-matched cast and crew", "Engaging and charismatic characters", "Solid performances all around", "Convincing and authentic acting", "Captivating performances", "Outstanding supporting cast", "Mismatched casting choices", "Unconvincing performances", "Poor casting decisions", "Weak supporting cast", "<PERSON><PERSON> of chemistry among the cast", "Distracting cameo appearances", "Underutilized talent", "Misfit cast members", "Uninspired performances", "Lacked star power", "Unconvincing character portrayals", "Flawless acting", "Inept casting", "Over-the-top performances", "Incomparable performances", "Unforgettable cast and crew dynamics"], "Character": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Vader", "<PERSON>", "<PERSON>", "Neo", "<PERSON>", "Indiana Jones", "<PERSON>", "<PERSON>", "<PERSON>", "Spider-Man", "Black Widow", "Wonder Woman", "<PERSON>", "The Joker", "<PERSON>", "<PERSON>", "Iron Man", "Captain <PERSON>", "Black Panther", "<PERSON>", "Han Solo", "Princess <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Professor <PERSON>", "Wolverine", "Deadpool", "<PERSON>", "The Terminator", "<PERSON> \"<PERSON><PERSON>\" <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Simba", "Cruella de Vil", "<PERSON><PERSON>", "The Genie", "<PERSON><PERSON><PERSON>", "Pocahontas", "Captain <PERSON>", "Aragorn", "<PERSON>", "<PERSON>", "<PERSON>", "Dracula", "Rey", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Elastigirl", "Dr. <PERSON>", "Count <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Legolas", "The Bride", "Maleficent", "Vin Diesel", "<PERSON>", "Professor <PERSON><PERSON><PERSON>", "Joker", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Mr. Incredible", "<PERSON><PERSON><PERSON>", "Mater", "<PERSON>", "Tiana", "Lightning McQueen", "Cinderella", "Nemo", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Captain <PERSON><PERSON><PERSON>", "<PERSON>", "Morpheus", "Trinity", "<PERSON>", "<PERSON>", "<PERSON>", "The Wicked Witch of the West", "<PERSON>pock", "Captain <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Mr. <PERSON><PERSON><PERSON>", "<PERSON> \"<PERSON>\" <PERSON>", "<PERSON><PERSON>", "Domino", "Cable", "Coloss<PERSON>", "Albus Dumbledore", "<PERSON><PERSON><PERSON>", "Voldemort", "<PERSON>", "Ariel", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Chewbacca"]}, "Movie reviews and ratings": {"Title": ["The Godfather", "Avatar", "The Shawshank Redemption", "Titanic", "Jurassic Park", "Casablanca", "The Lion King", "Star Wars", "The Dark Knight", "The Matrix", "<PERSON>", "Inception", "Pulp Fiction", "The Avengers", "The Wizard of Oz", "E.T. the Extra-Terrestrial", "Jaws", "Gone with the Wind", "Gladiator", "The Sound of Music", "The Lord of the Rings", "The Silence of the Lambs", "The Exorcist", "Braveheart", "Good<PERSON><PERSON>s", "Blade Runner", "The Green Mile", "Black Panther", "12 Years", "A Beautiful Mind", "The Departed", "The Social Network", "The King's Speech", "Moonlight", "The Shape of Water", "<PERSON><PERSON>", "La La Land", "Roma", "Parasite", "Little Women", "Arrival", "Gravity", "Dunkirk", "Mad <PERSON>", "<PERSON><PERSON><PERSON>'s List", "Goodfellas", "Fight Club", "The Revenant", "Inside Out", "Get Out", "Interstellar", "The Grand Budapest Hotel", "The Princess Bride", "The Truman Show", "The Big Lebowski", "Reservoir Dogs", "The Usual Suspects", "The Breakfast Club", "<PERSON>'s Day Off", "The Princess Diaries", "The Devil Wears Prada", "Crazy Rich Asians", "The Dark Knight Rises", "Avengers", "Star Wars:", "Psycho", "Raiders of the Lost Ark", "The Sixth Sense", "The Graduate", "The Terminator", "Back to the Future", "Ghostbusters", "The Shining", "A Clockwork Orange", "Apocalypse Now", "Bohemian Rhapsody", "Joker", "Boyhood", "Eternal Sunshine of the Spotless Mind", "The Prestige", "Fargo", "The Good, the Bad and the Ugly", "American Beauty", "<PERSON> and the Sorcerer's Stone", "Dallas Buyers Club", "A Star is Born", "The Great Gatsby", "Toy Story", "The Incredibles", "Finding Nemo", "Toy Story 3", "<PERSON><PERSON><PERSON>", "Frozen", "<PERSON><PERSON>"], "Viewers' Rating": ["Must-see", "Highly recommended", "Excellent", "Top-notch", "A+", "Outstanding", "Brilliant", "Phenomenal", "Five stars", "Exceptional", "A masterpiece", "Perfect 10", "Breathtaking", "Must watch", "Riveting", "Stellar", "Terrific", "Amazing", "Flawless", "Remarkable", "Unforgettable", "A classic", "Spectacular", "10/10", "Mind-blowing", "Impressive", "Superb", "Awe-inspiring", "Five out of five", "Exemplary", "Mesmerizing", "Unbeatable", "A must-see", "Must see", "Worth watching", "Top pick", "Five-star", "Fantastic", "Perfect", "Captivating", "Gripping", "Compelling", "Engaging", "Exciting", "Thrilling", "Great", "Good", "Decent", "Okay", "Mediocre", "Disappointing", "Binge-worthy", "Incredible", "Solid", "Good watch", "Enjoyable", "Worthwhile", "Entertaining", "Fun", "Suspenseful", "Intense", "Thought-provoking", "Insightful", "Memorable", "Touching", "Heartwarming", "Hilarious", "<PERSON><PERSON>", "Well-executed", "A bit disappointing", "Must-watch", "Loved it", "Spellbinding", "Engrossing", "Worth the watch", "Masterpiece", "A gem", "Magnificent", "Mixed feelings", "Overrated", "Unimpressive", "Avoid this.", "5-star rating", "4-star rating", "3-star rating", "2-star rating", "1-star rating", "10/10 rating", "9/10 rating", "8/10 rating", "7/10 rating", "6/10 rating", "5 out of", "4.5 out of", "4 out of", "3.5 out of", "3 out of", "2.5 out of", "2 out of", "1.5 out of", "1 out of", "Excellent rating", "Great rating", "Good rating", "Average rating", "Poor rating", "Terrible rating", "Recommended", "Not recommended", "Passable", "Mixed reviews", "Underrated", "Critically acclaimed"], "Year": ["2020", "1995", "1982", "2005", "1970s", "2012", "1989", "2000", "1960s", "1998", "1975", "2018", "1990", "1985", "2007", "1965", "1999", "1980", "2015", "1978", "2003", "1968", "1997", "1987", "2010s", "1972", "2016", "2008", "1963", "1996", "1984", "2004", "1973", "2014", "1993", "2002", "1985-1990", "1998-2004", "2010", "1967", "2008-2012", "1950", "1992-1998", "2019", "1977", "2001", "2014-2018", "1955", "2009", "1988-1992", "1959", "2013", "1969", "1979", "2015-2020", "1980s", "2022", "1990s", "2000-2005", "1950s", "2006", "2020s", "1940s", "2030", "1945", "2012-2015", "1920s", "1930s", "2017", "1983", "2021", "1949", "1972-1976", "1988", "1970-1980", "Early 2000s", "Late 1990s", "1986", "1994", "1992", "1958", "2002-2007", "2000s", "2010-2015", "1976", "1991"], "Genre": ["Action", "Adventure", "Comedy", "Romance", "Horror", "Sci-fi", "Drama", "Thriller", "Fantasy", "Musical", "Mystery", "Crime", "Western", "War", "Animation", "Family", "Superhero", "Historical", "Biographical", "Documentary", "Sports", "Martial arts", "Spy", "Disaster", "Supernatural", "Apocalyptic", "Political", "Epic", "Satire", "Surreal", "Cyberpunk", "Monster", "Time travel", "Zombie", "Science fiction", "Biography", "Noir", "Music", "Road", "Teen", "Mockumentary", "<PERSON>lasher", "Dystopian", "Film Noir", "Science Fiction", "Sport", "Martial Arts", "Crime Drama", "Space Opera", "<PERSON><PERSON><PERSON>", "Film noir", "History", "Biopic", "Experimental", "Period", "Psychological", "Silent", "Slice of life", "Survival", "Tragedy", "Urban", "Coming-of-age", "Independent"], "Director": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Sofia Coppola", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Guillermo <PERSON> Toro", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>ho", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Coen Brothers", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "MPAA Rating": ["G", "PG", "PG-13", "R", "NC-17", "NR", "M", "X", "TV-MA", "TV-G", "Unrated", "GP"], "Plot": ["Serial killer", "Time travel", "Alien invasion", "Undercover cop", "Bank heist", "Murder mystery", "Parallel universe", "Revenge plot", "Love triangle", "Haunted house", "Artificial intelligence", "Zombie apocalypse", "Superhero origin story", "Quest for treasure", "Political conspiracy", "Identity theft", "Family drama", "Hard-boiled detective", "Coming of age", "Space exploration", "War epic", "Cyberpunk dystopia", "Time loop", "Sports underdog", "Survival adventure", "Undercover agent", "<PERSON><PERSON>", "Revenge", "Rags to riches", "Forbidden love", "Supernatural powers", "Survival", "Civil war", "Mystery inheritance", "Wartime romance", "Betrayal", "Amnesia", "Road trip", "Fight for justice", "Mind control", "Animal adventure", "Secret agent", "Heist", "War veteran", "Mafia boss", "Superhero origin", "Government conspiracy", "Treasure hunt", "Post-apocalyptic world", "Royalty intrigue", "Political corruption", "Technology gone wrong", "Family feud", "Environmental disaster", "Supernatural curse", "Undercover operation", "Survival story", "Historical fiction", "Psychological thriller", "Race against time", "Crime spree", "Betrayal and deception", "Hero's journey", "Supernatural forces", "Fish out of water", "Forbidden romance", "Epic battle", "Cyber warfare", "Journey to the underworld", "Moral dilemma", "High school romance", "Coming of age story", "World war", "Magical quest", "Cyberpunk universe", "<PERSON><PERSON><PERSON>'s mission", "Forbidden love affair"], "Actor": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> Jr.", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Al Pacino", "<PERSON>", "Oct<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "Trailer": ["Teaser", "Preview", "Sneak peek", "First look", "Clip", "Footage", "Promo", "Teaser trailer", "Extended look", "Special feature", "Trailer", "Featurette", "Movie clip", "Film preview", "Preview clip", "Movie trailer", "Film clip", "Promotional video", "Movie footage", "Promotional clip", "Excerpt", "Highlights", "Glimpse", "Montage", "Compilation", "Official trailer", "Extended trailer"], "Song": ["Moon River", "My Heart Will Go On", "Let It Go", "Eye of the Tiger", "<PERSON><PERSON><PERSON>", "I Will Always Love You", "Can't Stop the Feeling", "Jai <PERSON>", "Lose Yourself", "Ghostbusters", "Purple Rain", "Grease Lightning", "Who Let the Dogs Out", "I Believe", "Nine to Five", "The Power of Love", "I Got You Babe", "<PERSON><PERSON><PERSON>", "Shallow", "The Circle of Life", "Stayin' Alive", "The Sound of Silence", "Bohemian Rhapsody", "I'm Gonna Be", "I Don't Want to Miss a Thing", "Rainbow Connection", "Unchained Melody", "Raindrops Keep Falling on My Head", "Maniac", "Don't You", "Take My Breath Away", "Over the Rainbow", "Diamonds Are a Girl's Best Friend", "Circle of Life", "My Favorite Things", "A Star is Born", "Singin' in the Rain", "The Sound of Music", "La La Land", "Moulin Rouge", "Grease", "The Greatest Showman", "Dreamgirls", "The Lion King", "<PERSON> and the Beast", "The Bodyguard", "8 Mile", "<PERSON>", "The Blues Brothers", "Fame", "Hairspray", "High School Musical", "Into the Woods", "Les Misérables", "Mamma Mia!", "Pitch Perfect", "Rock of Ages", "Another Day of Sun", "City of Stars", "Can't Stop the Feeling!", "Skyfall", "Live and Let Die", "I Believe I Can Fly", "My Girl", "You've Got a Friend in Me", "Can't Help Falling in Love", "I Say a Little Prayer", "Hallelujah", "Street Fighting Man", "Boogie Nights", "Crazy in Love", "Girls Just Want to Have Fun", "Don't <PERSON><PERSON><PERSON>, <PERSON> <PERSON>", "Purple Haze", "<PERSON><PERSON>", "I Heard It Through the Grapevine"], "Review": ["Gripping", "Captivating", "<PERSON><PERSON><PERSON>", "Mesmerizing", "Phenomenal", "Engrossing", "Riveting", "Compelling", "Masterful", "Terrific", "Spectacular", "Exhilarating", "Spellbinding", "Impressive", "Breathtaking", "Electrifying", "Unforgettable", "Awe-inspiring", "Mind-blowing", "Outstanding", "Superb", "Excellent", "Remarkable", "Exceptional", "Unbelievable", "Fantastic", "Brilliant", "Incredible", "Dazzling", "Flawless", "Thrilling", "Unpredictable", "Hilarious", "Heartwarming", "Intense", "Enthralling", "<PERSON><PERSON>", "Poignant", "Touching", "Impactful", "Unique", "Sensational", "Sublime", "Magnificent", "Engaging", "Chilling", "Emotional", "Moving", "Uplifting", "Thought-provoking", "Powerful", "Visually stunning", "Impeccable", "Exquisite", "Must-see", "Intriguing", "Jaw-dropping", "<PERSON><PERSON><PERSON>", "Gorgeous", "Grotesque", "Abhorrent", "Appalling", "Repellant", "Disgusting", "Revolting", "Sickening", "Repulsive", "Vomitory", "Terrible", "Horrible", "Unpleasant", "Disagreeable", "Entertaining", "Action-packed", "Suspenseful", "Inspiring", "Lighthearted", "Dramatic", "Complicated", "Groundbreaking", "Addictive", "Perfectly paced", "Unmissable"], "Character": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Iron Man", "Wonder Woman", "Black Panther", "<PERSON>", "<PERSON>", "Simba", "<PERSON>", "Neo", "<PERSON><PERSON><PERSON>", "Django", "The Joker", "<PERSON>", "Spider-Man", "Captain <PERSON>", "<PERSON><PERSON><PERSON>", "Deadpool", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>pock", "<PERSON>", "Fu<PERSON>sa", "<PERSON><PERSON>", "Dracula", "Indiana Jones", "Rocky <PERSON>", "<PERSON>", "Dr. <PERSON>", "Killmonger", "<PERSON><PERSON>", "<PERSON>", "Dr. <PERSON>", "Legolas", "<PERSON><PERSON> Vader", "Aragorn", "Captain <PERSON>", "<PERSON>", "<PERSON>", "Black Widow", "Joker", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Nemo", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Jasmine", "<PERSON>", "Ariel", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Rambo", "<PERSON>", "Trinity", "<PERSON>", "Princess <PERSON><PERSON>", "Morpheus", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "The Terminator", "<PERSON>", "Agent <PERSON>", "<PERSON>", "The Predator", "<PERSON><PERSON><PERSON><PERSON>", "Han Solo", "Princess <PERSON>", "tony stark", "Rey", "<PERSON><PERSON>", "<PERSON>ar Jar <PERSON>", "Wolverine", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Albus Dumbledore", "<PERSON><PERSON><PERSON>", "Sirius Black", "Draco <PERSON>", "Professor <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Captain <PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Scar", "<PERSON>", "<PERSON>", "<PERSON>", "Captain <PERSON>", "Tinker Bell", "Cruella de Vil", "<PERSON><PERSON><PERSON>"]}, "Streaming platform availability": {"Title": ["The Shawshank Redemption", "Pulp Fiction", "The Godfather", "Jurassic Park", "Inception", "The Matrix", "Titanic", "<PERSON>", "The Dark Knight", "The Lion King", "<PERSON><PERSON><PERSON>'s List", "12 Angry Men", "The Silence of the Lambs", "Goodfellas", "The Departed", "The Avengers", "Gladiator", "Braveheart", "Back to the Future", "Die Hard", "The Social Network", "The Shining", "The Graduate", "The Terminator", "A Clockwork Orange", "American Beauty", "The Green Mile", "The Truman Show", "The Usual Suspects", "Fight Club", "The Big Lebowski", "Lost in Translation", "The Princess Bride", "Reservoir Dogs", "The Sixth Sense", "Memento", "American History X", "Eternal Sunshine of the Spotless Mind", "There Will Be Blood", "No Country for Old Men", "The Hurt Locker", "Slumdog Millionaire", "The King's Speech", "Moonlight", "La La Land", "Silence of the Lambs", "Saving Private <PERSON>", "Jaws", "E.T. the Extra-Terrestrial", "Toy Story", "The Lord of the Rings", "<PERSON> and the Sorcerer's Stone", "Star Wars:", "<PERSON> Jones and the Raiders of the Lost Ark", "Avatar", "Black Panther", "Spider-Man", "Captain <PERSON>", "Wonder Woman", "Deadpool", "The Incredibles", "Frozen", "Finding Nemo", "The Little Mermaid", "<PERSON><PERSON>", "Toy Story 3", "Up", "<PERSON><PERSON><PERSON>", "<PERSON> and the Beast", "The Jungle Book", "Cinderella", "The Prestige", "The Grand Budapest Hotel", "Se7en", "The Hangover", "The Notebook", "The Exorcist", "The Breakfast Club", "The Sound of Music", "The Great Gatsby", "The Pursuit of Happyness", "The Hunger Games", "The Girl with the Dragon Tattoo", "The Dark Crystal", "The Birdcage", "The Place Beyond the Pines", "The Kid", "The Fall", "Crazy Rich Asians", "Get Out", "The Shape of Water", "Her", "Mad <PERSON>", "The Babadook", "12 Years", "The Revenant", "The Wizard of Oz", "Casablanca", "Gone with the Wind", "Vertigo", "<PERSON>", "Silver Linings Playbook", "A Beautiful Mind", "Interstellar", "Arrival", "Dunkirk", "<PERSON>", "Call Me by Your Name", "Parasite", "Joker", "<PERSON>", "The Irishman", "Marriage Story", "Roma", "Hereditary", "Midsommar", "Knives Out", "The Farewell", "Little Women", "<PERSON><PERSON>", "Once Upon", "1917"], "Viewers' Rating": ["5-star rating", "4.5-star rating", "Highly rated", "Best reviewed", "9 out of 10", "8.5 out of 10", "Top-rated", "Well-received", "93% approval", "8 out of 10", "4.8-star rating", "Exceptional", "85% positive reviews", "4.5 out of", "Impressive", "95% audience score", "9.5 out of 10", "Top-notch", "4.7-star rating", "Critically acclaimed", "Highly recommended", "90% approval", "8.9 out of 10", "Well-loved", "4 out of", "Must-see", "87% positive reviews", "4.2-star rating", "Stellar", "96% audience score", "9.8 out of 10", "Rave reviews", "4.6-star rating", "Highly praised", "91% approval", "4-star rating", "3-star rating", "2-star rating", "1-star rating", "Recommended", "Fan-favorite", "Popular", "Good reviews", "Positive feedback", "Praiseworthy", "Hit", "All-time favorite", "Award-winning", "Cult classic", "Underrated gem", "Overlooked treasure", "Solid", "Decent", "Mixed reviews", "Average", "Lackluster", "Disappointing", "Poorly rated", "Trash", "Terrible", "Awful", "Abysmal", "Unwatchable", "3.5-star rating", "2.5-star rating", "1.5-star rating", "Excellent feedback", "Mixed opinions", "Polarizing reactions", "Average rating", "Below-average rating", "Outstanding reviews", "Overwhelmingly positive", "Mediocre feedback", "Disappointing reactions", "Solid rating", "Decent reviews", "Top-notch feedback", "Highly acclaimed", "90% approval rating", "80% approval rating", "70% approval rating", "60% approval rating", "50% approval rating", "40% approval rating", "30% approval rating", "20% approval rating", "10% approval rating", "Low rating", "High rating", "Five-star", "Family favorite", "Binge-worthy", "Addictive", "Captivating", "Gripping", "Compelling", "Entertaining", "Hilarious", "Mind-blowing", "Heartwarming", "Engrossing", "Unforgettable", "Riveting", "Amazing", "Infuriating", "Chilling", "Terrifying", "Spellbinding", "Outstanding", "Exhilarating", "Six out of five stars", "Love it or hate it", "Classic", "Nostalgic", "Controversial", "Mind-bending", "Goosebump-inducing", "Thought-provoking", "Highly emotional", "Essential viewing", "Worth watching", "A must watch", "Instant classic", "Top pick", "Incredible", "Enthralling", "Unmissable", "Phenomenal", "Epic", "Awe-inspiring", "Remarkable", "Breathtaking", "Superior", "Masterpiece", "Superb", "Excellent", "Splendid", "Terrific"], "Year": ["2000", "2010", "1995", "1987", "2021", "1990s", "1975", "1985", "1960", "2020", "1980s", "1997", "1950s", "1971", "1965", "2015", "2007", "1983", "1999", "1940s", "1969", "1978", "1992", "1988", "2013", "2005", "1970s", "2025", "1998", "1982", "2001", "1962", "1955", "1973", "2022", "2000s", "2010s", "2020s", "1980-1990", "1990-2000", "2000-2010", "2010-2020", "2020-2025", "1970-1980", "1980-1990s", "1990-2000s", "2000-2010s", "2010-2020s", "1960s", "Early 2000s", "Late 2000s", "Early 2010s", "Late 2010s", "Early 1980s", "Late 1980s", "Early 1990s", "Late 1990s", "Early 1960s", "Late 1960s", "Early 1970s", "Late 1970s", "Early 1950s", "2012", "2018", "1993-1996", "2008-2012", "2003", "2006", "2009", "2011", "2014", "2016", "2019", "2002", "2017", "1990-1995", "1990", "1982-1990", "1980", "1987-1995", "1993", "1962-1970", "2008", "1967", "1989", "1968-1975", "1979", "1963", "1995-2000", "1985-1990", "2005-2010", "1975-1980", "1965-1970", "1968", "1964", "2004", "1957", "1972"], "Genre": ["Action", "Comedy", "Drama", "Horror", "Science Fiction", "Romance", "Thriller", "Fantasy", "Mystery", "Adventure", "Crime", "Animation", "Family", "Documentary", "Musical", "Western", "War", "Historical", "Biopic", "Superhero", "Spy", "Supernatural", "Martial Arts", "Disaster", "Political", "Sports", "Psychological", "Music", "Teen", "Coming of Age", "Period", "Christmas", "Silent", "Experimental", "Science fiction", "documentary", "Martial arts", "Satire", "Mockumentary", "Cyberpunk", "Found footage", "<PERSON>lasher", "Zombie", "Natural disaster", "Biography", "Film-noir", "Game-Show", "History", "News", "Reality-TV", "Sci-Fi", "Short", "Sport", "Talk-Show", "Indie", "Cult", "Classic", "Foreign", "LGBTQ+", "Animated", "Suspense", "Animated Comedy", "Biographical", "Ensemble Cast", "Found Footage", "Noir", "<PERSON><PERSON><PERSON>", "Road", "True Crime", "Anime", "Social", "Gothic", "Neo-noir"], "Director": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Guillermo <PERSON> Toro", "<PERSON>", "Sofia Coppola", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>ho", "<PERSON><PERSON>", "Coen Brothers", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "MPAA Rating": ["G", "PG", "PG-13", "R", "NC-17", "NR", "Unrated", "X", "TV-G", "TV-MA", "TV-PG", "GR", "GP", "Not Rated"], "Plot": ["Love triangle", "Undercover cop", "Time travel", "Revenge plot", "Heist", "Apocalyptic future", "Wild west", "Superhero origin story", "Forbidden love", "Amnesia", "Small town murder mystery", "Alien invasion", "Prison break", "Coming-of-age story", "Cyberpunk dystopia", "Mafia revenge", "Supernatural powers", "Post-apocalyptic survival", "Artificial intelligence uprising", "Diamond heist", "Political conspiracy", "Zombie outbreak", "Space exploration", "Time loop", "Underwater adventure", "Revenge", "Apocalyptic world", "Undercover operation", "Coming-of-age", "Family drama", "Artificial intelligence", "Supernatural forces", "Historical fiction", "Mystery thriller", "Survival adventure", "Superhero origin", "Forbidden romance", "Psychological thriller", "War drama", "Vigilante justice", "Cybercrime", "Romantic comedy", "Identity theft", "Disaster survival", "Undercover agent", "Murder mystery", "Coming of age", "Fight for survival", "Post-apocalyptic world", "High school drama", "Road trip", "Mafia family", "Haunted house", "Cyber espionage", "Treasure hunt", "Historical drama", "Robot uprising", "Rescue mission", "Secret society", "Interstellar war", "World War II", "Undercover mission", "Zombie apocalypse", "Quest for treasure", "Coming of age story", "Rags to riches", "Battle for survival", "High school romance", "Ocean adventure"], "Actor": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> Jr.", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Gal Gadot", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Oct<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Sir <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "Trailer": ["Sneak peek", "Teaser", "Preview", "Footage", "Clip", "Teaser trailer", "Extended look", "First look", "Trailer breakdown", "Theatrical trailer", "Promo", "Official trailer", "Extended trailer", "TV spot", "Featurette", "Promotional clip", "Film snippet", "Trailer", "Exclusive look", "Film montage", "Movie highlight", "Trailer showcase", "Excerpt", "Extended preview"], "Song": ["Shallow", "Let It Go", "I Will Always Love You", "My Heart Will Go On", "Can't Stop the Feeling", "Circle of Life", "Remember Me", "Over the Rainbow", "<PERSON><PERSON><PERSON>", "I Want to Hold Your Hand", "I'm Still Standing", "Bohemian Rhapsody", "Eye of the Tiger", "I Don't Want to Miss a Thing", "The Sound of Silence", "Stayin' Alive", "Eight Days a Week", "Old Time Rock and Roll", "<PERSON><PERSON>", "We Will Rock You", "Don't You Forget About Me", "<PERSON> <PERSON><PERSON>", "The Winner Takes It All", "Dancing Queen", "Man in the Mirror", "Purple Rain", "Into the Woods", "La La Land", "The Sound of Music", "A Star is Born", "Moulin Rouge!", "Sister Act", "The Greatest Showman", "Singin' in the Rain", "The Rocky Horror Picture Show", "Dreamgirls", "Grease", "Fame", "<PERSON>", "<PERSON><PERSON><PERSON>", "The Lion King", "Flashdance", "<PERSON>", "The Phantom of the Opera", "Hairspray", "Chicago", "West Side Story", "The Blues Brothers", "Yellow Submarine", "<PERSON><PERSON><PERSON>", "Jai <PERSON>", "Rocket Man", "Old Town Road", "A Whole New World", "\"Livin' on", "Sweet Child o' Mine", "Take Me Home, Country Roads", "Thriller", "Girls Just Want to Have Fun", "Stairway to Heaven", "\"I Don't Want to Miss", "<PERSON> and the Beast", "\"I Believe", "Summer Nights", "Yesterday", "City of Stars", "<PERSON> the Girl", "The Lion Sleeps Tonight", "Oh Happy Day", "Pure Imagination", "Rainbow Connection", "Somewhere Over the Rainbow", "Uptown Funk", "Viva Las Vegas", "\"When You Wish Upon", "\"You've Got", "Frozen", "<PERSON><PERSON>", "The Bodyguard", "The Wizard of Oz", "The Jungle Book", "Pitch Perfect", "The Little Mermaid", "<PERSON><PERSON>", "Coco", "<PERSON><PERSON><PERSON>", "A Hard Day's Night"], "Review": ["Streamable", "Available on streaming platform", "Watchable", "Accessible on streaming service", "On-demand", "Ready to watch online", "Stream-ready", "Available for viewing", "Stream on [streaming platform]", "Available to stream", "Watch on [streaming platform]", "Stream-friendly", "Stream anywhere", "Stream on any device", "Stream at home", "Easy to stream", "Stream with subscription", "Stream with membership", "Stream with premium account", "Stream without ads", "Stream in HD", "Stream in 4K", "Stream with subtitles", "Stream with audio description", "Stream with closed captioning", "Stream with parental controls", "Stream from mobile", "Stream from tablet", "Stream from laptop", "Stream from smart TV", "Stream from gaming console", "Stream from streaming stick", "Stream from web browser", "Stream on the go", "Stream on multiple devices", "Must-watch", "Classic", "Highly recommended", "Gripping", "Riveting", "Breathtaking", "Must-see", "Phenomenal", "Groundbreaking", "Masterpiece", "Engrossing", "Captivating", "Compelling", "Impressive", "Jaw-dropping", "Unforgettable", "Incredible", "Thrilling", "Entertaining", "Mind-blowing", "Outstanding", "Flawless", "Superb", "Exhilarating", "Electrifying", "Spellbinding", "Awe-inspiring", "Touching", "Remarkable", "Unbelievable", "Unmissable", "Spectacular", "Enthralling", "Heartwarming", "Gripping storyline", "Captivating performances", "Visually stunning", "Compelling narrative", "Thought-provoking", "Engaging characters", "Emotionally impactful", "Memorable scenes", "Riveting plot", "Hilarious moments", "Intense action", "Mind-bending", "Refreshing concept", "Well-crafted dialogue", "Unforgettable soundtrack", "Astonishing special effects", "Ambitious storytelling", "Stellar ensemble cast", "Beautifully shot", "Awe-inspiring cinematography", "Groundbreaking animation", "Gripping suspense", "Jaw-dropping visuals", "Haunting atmosphere", "Powerful message", "Breathtaking set design", "<PERSON> writing", "Inventive direction", "Captivating soundtrack", "Explosive thrills", "Charming characters", "Breathtaking cinematography", "Unpredictable twists", "Compelling performances", "Netflix availability", "Prime Video availability", "Disney+ availability", "HBO Max availability", "Hulu availability", "Amazon availability", "Availability on streaming platforms", "Streamable on Netflix", "Streamable on Prime Video", "Streamable on Disney+", "Streamable on HBO Max", "Streamable on Hulu", "Can be streamed on Netflix", "Can be streamed on Prime Video", "Can be streamed on Disney+", "Can be streamed on HBO Max", "Can be streamed on Hulu", "Not available for streaming", "Exclusive to Netflix", "Exclusive to Prime Video", "Exclusive to Disney+", "Exclusive to HBO Max", "Exclusive to <PERSON><PERSON>", "Streamable on multiple platforms", "Wide streaming availability", "Limited streaming availability", "Best streaming platform for this movie", "Worst streaming platform for this movie", "Easy to find on streaming platforms", "Hard to find on streaming platforms", "Top-rated on streaming services", "Low-rated on streaming services", "Popular streaming platform choice", "Unpopular streaming platform choice", "Available for streaming", "Streaming platform options", "Watch on streaming services", "Streamable on various platforms", "Streaming availability", "Availability on streaming websites", "Accessible through streaming", "Streaming platform compatibility", "Stream on popular services", "Watch on subscription platforms", "Available on streaming apps", "Streamable content", "Movie on streaming platforms", "Stream without cable", "Streaming access", "Streaming service availability", "On-demand streaming options", "Movie streaming availability", "Watch on streaming devices", "Streamable on mobile", "Streaming platform variety", "Streaming platform selection", "Available for online streaming", "Streaming platform availability", "Movie on streaming channels", "Stream on smart TV", "Movie access via streaming", "Streamable on game consoles", "Streaming service compatibility", "Watch on streaming sticks", "Available for digital streaming", "Streaming platform diversity", "Movie streaming access", "Streamable through internet services."], "Character": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Vader", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Black Widow", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Princess <PERSON><PERSON>", "Captain <PERSON>", "<PERSON><PERSON><PERSON>", "Aragorn", "Black Panther", "<PERSON><PERSON>", "Legolas", "Han Solo", "Spider-Man", "Wonder Woman", "<PERSON><PERSON>", "Iron Man", "<PERSON><PERSON><PERSON>", "The Joker", "Neo", "<PERSON>", "Indiana Jones", "The Terminator", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Simba", "<PERSON><PERSON><PERSON>", "Scar", "Captain <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Rey", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Aquaman", "The Little Mermaid", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "The Hulk", "Captain <PERSON>", "<PERSON><PERSON><PERSON>", "Wolverine", "Professor", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Mystique", "Deadpool", "<PERSON>", "Ant-<PERSON>", "<PERSON><PERSON>", "Black Canary", "The Flash", "Poison Ivy", "The Riddler", "Catwoman", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Maleficent", "<PERSON>", "Storm", "Trinity", "Terminator", "<PERSON>", "<PERSON>", "<PERSON>", "Ma<PERSON><PERSON>", "<PERSON>", "Mr. <PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Professor <PERSON>", "Cruella de Vil", "Captain <PERSON>", "Scarlet Witch", "<PERSON>", "Groot", "Princess <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, "Soundtrack and music information": {"Title": ["The Lion King", "La La Land", "A Star is Born", "The Sound of Music", "Bohemian Rhapsody", "Purple Rain", "Walk the Line", "Singin' in the Rain", "Saturday Night Fever", "The Greatest Showman", "Dirty Dancing", "Grease", "The Wizard of Oz", "Frozen", "<PERSON><PERSON>", "The Little Mermaid", "<PERSON><PERSON><PERSON>", "<PERSON> and the Beast", "The Jungle Book", "Coco", "The Nightmare Before Christmas", "The Phantom of the Opera", "Chicago", "Moulin Rouge!", "Les Misérables", "A Hard Day's Night", "Once", "Whiplash", "<PERSON><PERSON><PERSON>", "The Piano", "August Rush", "Trolls", "Guardians of the Galaxy", "Baby Driver", "<PERSON><PERSON><PERSON>", "The Blues Brothers", "Pitch Perfect", "High School Musical", "Rock of Ages", "Across the Universe", "8 Mile", "Purple Haze", "Strawberry Fields Forever", "Hound Dog", "A Star Is Born", "Mamma Mia!", "West Side Story", "Pulp Fiction", "The Bodyguard", "Saving Private <PERSON>", "Star Wars", "Jurassic Park", "Titanic", "The Lord of the Rings", "<PERSON>", "The Avengers", "<PERSON>", "<PERSON>", "Spider-Man", "Wonder Woman", "Black Panther", "Cinderella", "<PERSON>", "The Hunchback of Notre Dame", "Trainspotting", "The Great Gatsby", "Top Gun", "The Graduate", "Almost Famous", "High Fidelity", "500 Days of Summer", "<PERSON>", "This is Spinal Tap", "School of Rock", "Sing Street", "The Muppet Movie", "Finding Nemo", "Fantasia", "The Shawshank Redemption", "Into the Wild", "Breakfast at Tiffany's", "The Umbrellas of Cherbourg", "Romeo + Juliet", "Beyond the Lights", "<PERSON><PERSON><PERSON>", "Easy Rider", "The Harder They Come", "The Big Chill", "<PERSON><PERSON><PERSON>", "Flashdance", "The Commitments", "Buena Vista Social Club", "The Rocky Horror Picture Show", "Lost in Translation", "Juno", "Begin Again", "The Wiz", "American Graffiti", "Empire Records", "Wayne's World", "O Brother, Where Art Thou?", "<PERSON>", "Dazed and Confused", "The Perks of Being", "Garden State", "The Breakfast Club"], "Viewers' Rating": ["Catchy tunes", "Beautiful music", "Iconic soundtrack", "Fits the film perfectly", "Adds depth to the story", "Memorable songs", "Masterful score", "Elevates the movie", "Perfectly chosen music", "Mood-setting music", "Emotional soundtrack", "Instant classic", "Unforgettable songs", "Haunting melodies", "Perfect blend of music and visuals", "Adds to the overall experience", "Atmospheric music", "Tugs at the heartstrings", "Incredible sound design", "Adds excitement to the scenes", "Feel-good tunes", "Unbelievably good", "Fantastic musical choices", "Immersive sound experience", "Raises the emotional stakes", "Sets the tone for the movie", "Reinforces the storytelling", "Creates", "Brilliantly composed", "Uplifting songs", "Pleasant surprise", "Adds", "Emotionally resonant", "Keeps the audience engaged", "Enhances the overall enjoyment", "5-star soundtrack", "Catchy music", "Melodic score", "Energetic music", "Haunting score", "Memorable soundtrack", "Iconic score", "Nostalgic soundtrack", "Powerful score", "Lyrical soundtrack", "Cinematic music", "Uplifting score", "Innovative soundtrack", "Rockin' music", "Dramatic score", "Heartfelt soundtrack", "Enchanting music", "Epic score", "Soothing soundtrack", "Timeless music", "Exciting score", "Captivating soundtrack", "Instrumental music", "Hypnotic score", "Authentic soundtrack", "Folk-inspired music", "Riveting score", "Harmonious soundtrack", "Jazzy music", "Dynamic score", "Eclectic soundtrack", "Original music", "5-star rating", "4-star rating", "3-star rating", "2-star rating", "1-star rating", "10/10", "9/10", "8/10", "7/10", "6/10", "Solid soundtrack", "Memorable music", "Mediocre score", "Excellent music", "Lackluster soundtrack", "Outstanding score", "Weak music", "Mediocre soundtrack", "Unforgettable music", "Engaging score", "Dull soundtrack", "Captivating music", "Ordinary score", "Stunning soundtrack", "Iconic music", "Forgettable score", "Average soundtrack", "Uninspired score", "Impressive soundtrack", "Harmonious music", "Unimpressive score", "Epic soundtrack", "Melodic music", "Underwhelming score", "4-star music", "3.5-star score", "4.5 out of 5", "Great music", "Average score", "Excellent soundtrack", "Mediocre music", "3-star soundtrack", "Subpar soundtrack", "Phenomenal music", "Lackluster score", "4-star soundtrack", "3-star music", "4.5-star score", "5 out of 5", "Weak soundtrack", "Spectacular music", "Decent score", "3.5-star soundtrack", "Good music", "2-star score", "Exceptional music", "Disappointing score", "4.5-star soundtrack", "2.5-star music", "3.5 out of 5", "10/10 soundtrack", "A+", "B", "Perfect soundtrack", "Must-see for music lovers", "Subpar score", "2-star soundtrack", "7/10 music", "3-star score", "6.5/10 soundtrack", "D", "Disappointing music", "Top-notch music", "8-star score", "9/10 soundtrack", "C-", "Stellar score", "1-star soundtrack", "5/10 music", "4-star score", "7.5/10 soundtrack", "F", "Abysmal music", "Remarkable score", "Phenomenal soundtrack", "Masterful music"], "Year": ["1980s", "1990s", "2000s", "2010s", "2020s", "1970s", "1960s", "1950s", "1940s", "1930s", "1920s", "1910s", "1900s", "1890s", "1880s", "1870s", "1860s", "1850s", "1840s", "1830s", "1820s", "1810s", "1800s", "1770s", "1760s", "1750s", "1740s", "1730s", "1720s", "1710s", "1700s", "1690s", "1680s", "1670s", "1660s", "2020", "1985", "Early 2000s", "1967", "1973", "Late 1990s", "2015", "1988", "2005", "1978", "1994", "2012", "1982", "Early 1990s", "1965", "2009", "1998", "1971", "1963", "2018", "1989", "1976", "2003", "Late 1980s", "1958", "2014", "1992", "1969", "2007", "1979", "1996", "1970-1980", "1980-1990", "1990-2000", "2000-2010", "2010-2020", "1960-1970", "1950-1960", "1940-1950", "early 2000s", "late 2000s", "20th century", "21st century", "mid 2000s", "mid 2010s", "1930-1940", "1920-1930", "1790s", "1780s", "1995", "1987", "2021", "1981", "1972", "2008", "1983", "2017", "1999", "2001", "1955", "1993", "2013"], "Genre": ["Action", "Adventure", "Animation", "Comedy", "Crime", "Documentary", "Drama", "Family", "Fantasy", "History", "Horror", "Music", "Musical", "Mystery", "Romance", "Science Fiction", "Sports", "Superhero", "Thriller", "War", "Western", "Biographical", "Disaster", "Epic", "Gangster", "Heist", "Martial Arts", "Neo-Noir", "Road", "Spy", "Surreal", "Teen", "Women", "<PERSON><PERSON>", "Urban", "Rock", "Pop", "Jazz", "Blues", "Hip hop", "R&B", "Country", "Classical", "Electronic", "Reggae", "Folk", "Metal", "Punk", "Ska", "<PERSON>", "Soul", "Gospel", "World", "Latin", "Dance", "Ambient", "Reggaeton", "Rap", "Alternative", "Indie", "Disco", "Techno", "House", "K-pop", "J-pop", "Bollywood", "New Age", "Bluegrass", "Salsa", "Hip-hop", "Opera", "EDM", "Dubstep", "Grunge", "Celtic", "African", "Caribbean", "Flamenco", "Biopic"], "Director": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Sofia Coppola", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Guillermo <PERSON> Toro", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>ho", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>wook", "<PERSON>", "<PERSON>"], "MPAA Rating": ["G", "PG", "PG-13", "R", "NC-17", "NR", "Unrated", "M", "X", "Approved", "Not Rated", "E", "GP"], "Plot": ["Underdog musician", "Battle of the bands", "Musical prodigy", "Music festival", "Rock and roll legend", "Jazz club", "Hip-hop rivalry", "Classical composer", "Punk rock rebellion", "Country music star", "Blues guitarist", "Pop diva", "Folk music revival", "Opera singer", "EDM DJ", "Heavy metal band", "Reggae musician", "Rap battle", "Gospel choir", "Mariachi band", "Bollywood musical", "Salsa dance competition", "Flamenco guitarist", "Barbershop quartet", "A cappella group", "Love triangle", "Revenge", "Rags-to-riches", "Coming of age", "Family dynamics", "Political intrigue", "Heist", "Time travel", "Survival", "Betrayal", "Quest for redemption", "Undercover mission", "Forbidden love", "Mystery solving", "Environmental activism", "Legal drama", "Battle for justice", "Oppression and resistance", "Alien invasion", "Post-apocalyptic world", "Spiritual journey", "Dance competition", "Body swapping", "Gang rivalry", "Undercover cop", "Power struggle", "Redemption", "Mob boss", "Struggling artist", "Identity theft", "Political corruption", "Mental illness", "Family drama", "Historical fiction", "Dark secrets", "Quest for vengeance", "Hacker", "Supernatural forces", "Robot uprising", "Civil war", "Environmental disaster", "Rock star", "Pop idol", "Music producer", "Record label", "Jazz musician", "Hip hop artist", "Folk singer", "Rhythm and blues", "Country music festival", "Garage band", "Music video director", "Orchestra conductor", "Album release party", "Songwriting competition", "Music therapy", "Music festival headliner", "DJ residency", "Concert pianist", "Soundtrack composer", "Singing competition winner", "Love story", "Underdog triumph", "Rags to riches", "War drama", "Musical competition", "Technological breakthrough", "Historical event", "Drug trade", "Life in the future", "High school romance", "Midlife crisis", "Journey to self-discovery", "Music as therapy"], "Actor": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON> Jr.", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Gal Gadot", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Cardi B", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Sia", "<PERSON>", "<PERSON>", "Lana <PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "Trailer": ["Theme song", "Musical score", "Original soundtrack", "Soundtrack album", "Background music", "Orchestral arrangement", "Song compilation", "Musical compositions", "Singing performances", "Instrumental music", "Score", "Soundtrack", "Musical number", "Original song", "Cinematic music", "Instrumental track", "Title track", "Original score", "Musical montage", "Soundtrack preview", "Musical moments", "Score excerpts", "Song snippets", "Musically enhanced scenes", "Soundtrack highlights", "Musical theme", "Movie theme", "Instrumental soundtrack", "Original music", "Movie music", "Opening credits music", "Closing credits song", "Song featured in trailer"], "Song": ["My Heart Will Go On", "Bohemian Rhapsody", "Stayin' Alive", "Eye of the Tiger", "I Will Always Love You", "Mrs. <PERSON>", "Let It Go", "I Don't Want to Miss a Thing", "You've Got a Friend in Me", "Can't Stop the Feeling!", "Circle of Life", "The Way We Were", "<PERSON><PERSON><PERSON>", "Unchained Melody", "Everything Is Awesome", "Don't You", "Take My Breath Away", "Ghostbusters", "Let's Get It On", "Time of My Life", "In the Air Tonight", "I Will Follow Him", "Unforgettable", "I've Had the Time of My Life", "Can't Help Falling in Love", "Purple Rain", "\"Don't You", "Jai <PERSON>", "Happy", "<PERSON> <PERSON><PERSON>", "Pretty Woman", "Fame", "Old Time Rock and Roll", "Danger Zone", "Wind Beneath My Wings", "I Got You Babe", "Shallow", "City of Stars", "<PERSON><PERSON><PERSON>", "I Believe I Can Fly", "You've Got a Friend In Me", "Kiss from a Rose", "Over the Rainbow", "The Power of Love", "Stay", "We Belong Together", "I'm Gonna Love Me Again", "See You Again", "The Lion King", "A Star is Born", "Titanic", "<PERSON><PERSON>", "The Greatest Showman", "Pulp Fiction", "Dirty Dancing", "Grease", "The Sound of Music", "La La Land", "Guardians of the Galaxy", "The Bodyguard", "Frozen", "Chicago", "The Blues Brothers", "Saturday Night Fever", "The Rocky Horror Picture Show", "Mamma Mia!", "Pitch Perfect", "<PERSON><PERSON><PERSON>", "Singin' in the Rain", "High School Musical", "Staying Alive", "We Will Rock You", "I Wanna Dance with <PERSON>", "Lose Yourself", "My Girl", "The Circle of Life", "Stand by Me", "Heart of Glass", "I Will Survive", "Rocky Theme Song"], "Review": ["Enchanting music", "Lyrical soundtrack", "Melodic score", "Catchy tunes", "Emotive music", "Harmonious compositions", "Rousing soundtracks", "Memorable songs", "Euphoric rhythms", "Stirring melodies", "Heart-wrenching ballads", "Soul-stirring music", "Iconic theme songs", "Uplifting soundscapes", "Atmospheric scores", "Evocative sound design", "Dynamic musical arrangements", "Haunting melodies", "Nostalgic music", "Jazzy tunes", "Retro soundtracks", "Symphonic orchestration", "Vibrant musical accompaniment", "Eclectic music selection", "Enthralling vocals", "Poignant musical moments", "Electrifying music", "Captivating theme music", "Folk-infused melodies", "Epic musical compositions", "Metaphorical music", "Experimental soundscapes", "Provocative musical choices", "Timeless soundtracks", "Diverse musical influences", "Catchy soundtrack", "Engaging musical score", "Memorable music", "Evocative soundtrack", "Riveting musical arrangements", "Intriguing sound design", "Atmospheric musical score", "Compelling soundtrack choices", "Captivating theme song", "Harmonious soundtrack", "Soul-stirring musical arrangements", "Energetic music", "Powerful musical score", "Melancholic soundtrack", "Lively music", "Nostalgic soundtrack", "Expressive musical arrangements", "Upbeat soundtrack", "Emotional score", "Sensational soundtrack", "Cinematic musical arrangements", "Gripping soundtrack", "Magical musical score", "Whimsical soundtrack", "Ethereal music", "Captivating soundtrack choices", "Rich musical score", "Soothing soundtrack", "Thrilling musical arrangements", "Uplifting music", "Enthralling soundtrack", "Moving musical arrangements", "Dynamic soundtrack"], "Character": ["<PERSON>", "<PERSON>", "<PERSON><PERSON> Vader", "Simba", "<PERSON>", "<PERSON><PERSON><PERSON>", "Indiana Jones", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Captain <PERSON>", "<PERSON>", "<PERSON>", "Black Widow", "Neo", "<PERSON><PERSON><PERSON>", "Wonder Woman", "Iron Man", "<PERSON>", "<PERSON>", "Spider-Man", "<PERSON>", "The Joker", "<PERSON><PERSON>", "Cinderella", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Pocahontas", "Ariel", "<PERSON><PERSON><PERSON>", "Tiana", "Nemo", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Lightning McQueen", "Mater", "<PERSON>", "<PERSON><PERSON>", "Frozone", "<PERSON>", "Baymax", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Lieutenant <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Legolas", "Aragorn", "<PERSON><PERSON><PERSON>", "Rocky <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "M", "Han Solo", "Princess <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "King <PERSON>", "Queen <PERSON>rgo", "<PERSON><PERSON>", "<PERSON>", "Beast", "<PERSON>", "<PERSON>", "Rey", "<PERSON><PERSON>", "Black Panther", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Deadpool", "<PERSON>", "Dumbledore", "<PERSON>", "<PERSON>", "Elastigirl", "<PERSON>", "Agent <PERSON>", "Dracula", "Scooby-Doo", "<PERSON>", "The Terminator", "<PERSON>", "Captain <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Scar", "<PERSON>", "<PERSON>", "Trinity", "Morpheus", "<PERSON><PERSON>", "<PERSON>", "Lord <PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Jasmine", "Wolverine", "<PERSON><PERSON><PERSON>", "Cruella de Vil", "Maleficent", "<PERSON><PERSON>", "Frankenstein"]}, "Behind-the-scenes and making-of details": {"Title": ["The Dark Knight", "Inception", "Jurassic Park", "Avatar", "The Godfather", "Star Wars", "Titanic", "Gladiator", "Lord of the Rings", "The Shawshank Redemption", "Pulp Fiction", "Fight Club", "The Matrix", "<PERSON><PERSON><PERSON>'s List", "<PERSON>", "The Silence of the Lambs", "The Shining", "Indiana Jones", "Back to the Future", "E.T. the Extra-Terrestrial", "Jaws", "The Wizard of Oz", "The Sound of Music", "The Exorcist", "<PERSON>", "The Terminator", "Alien", "The Breakfast Club", "Top Gun", "Dirty Dancing", "Ghostbusters", "<PERSON>'s Day Off", "The Goonies", "Beetlejuice", "The Princess Bride", "The Karate Kid", "The Lost Boys", "Stand by Me", "When <PERSON> Met <PERSON>", "Pretty Woman", "Home Alone", "Mrs. <PERSON>", "Clueless", "The Truman Show", "American Beauty", "Apocalypse Now", "Raiders of the Lost Ark", "The Lord of the Rings", "Gone with the Wind", "<PERSON>", "Casablanca", "Psycho", "Blade Runner", "Goodfellas", "2001:", "Jurassic World", "The Big Lebowski", "The Graduate", "A Clockwork Orange", "The French Connection", "The Sting", "Taxi Driver", "The Great Gatsby", "The Birds", "The Bridge on the River Kwai", "Blazing Saddles", "Singin' in the Rain", "Saving Private <PERSON>", "The Departed", "The Green Mile", "The Social Network", "The Godfather Part II", "A Beautiful Mind", "Braveheart", "12 Years", "The Revenant", "The Hurt Locker", "Million Dollar Baby", "No Country for Old Men", "Slumdog Millionaire", "The Artist", "Argo", "<PERSON><PERSON>", "The Shape of Water", "Moonlight", "La La Land", "Whiplash", "1917", "Parasite", "The King's Speech", "Gravity", "Black Swan", "The Grand Budapest Hotel", "The Avengers", "Empire of Dreams", "<PERSON>", "Pacific Rim", "Interstellar", "<PERSON> Gump", "Casino", "The Imitation Game", "12 Years a Slave", "Spotlight", "There Will Be Blood", "The Fighter", "The Lion King", "The Sixth Sense", "The Usual Suspects", "The Bourne Identity", "It's a Wonderful Life", "North by Northwest", "Sunset Boulevard", "Some Like It Hot", "An American in Paris", "<PERSON><PERSON><PERSON><PERSON>", "Lawrence of Arabia", "West Side Story"], "Viewers' Rating": ["5-star rating", "10/10 rating", "2 thumbs up", "3.5 star rating", "A+", "Highly recommended", "Top-notch", "Must-see", "Impressive", "A perfect 5", "Brilliantly made", "Superbly executed", "Astonishing", "Excellent", "9/10 rating", "4.5 stars out of 5", "8.5/10 rating", "A solid", "7/10 rating", "Breath-taking", "Flawless", "Outstanding", "A masterpiece", "Incredible", "4.9 stars out of 5", "9.5/10 rating", "A well-deserved", "Nailed it", "Phenomenal", "One of", "4.8 stars out of 5", "Exceeded expectations", "Outstanding performance", "Riveting", "4-star rating", "3-star rating", "2-star rating", "1-star rating", "Perfect score", "Great", "Good", "Average", "Decent", "Fair", "Mediocre", "Poor", "Disappointing", "Terrible", "Exceptional", "Amazing", "Spectacular", "Masterpiece", "Captivating", "Engaging", "Entertaining", "Insightful", "Informative", "Educational", "Enlightening", "Inspirational", "Revealing", "Fascinating", "Compelling", "Gripping", "10/10", "5 stars", "9/10", "4 out of 5", "B-", "8/10", "C+", "3.5/5", "7/10", "4.5 stars", "A-", "6/10", "3 out of 5", "B+", "8.5/10", "4 stars", "A", "9.5/10", "B", "7.5/10", "3.5 stars", "C", "6.5/10", "3 stars", "8 out of 10", "5 out of 5", "4.5 out of 5", "8/10 rating", "6/10 rating", "4/10 rating", "2/10 rating", "Abysmal", "A+ rating", "B+ rating", "C+ rating", "D+ rating", "F rating", "Worth watching", "Unimpressive", "Overrated", "Underrated", "Solid", "Passable", "Forgettable", "Avoid at all costs"], "Year": ["1950s", "1970s", "1980s", "1990s", "2000s", "2010s", "2020s", "1960-1970", "1970-1980", "1980-1990", "1990-2000", "2000-2010", "2010-2020", "1960-1980", "1970-1990", "1980-2000", "1990-2010", "2000-2020", "1960-1990", "1970-2000", "1980-2010", "1990-2020", "1960-2000", "1970-2010", "1980-2020", "1960-2020", "1970-1985", "1985-2000", "2000-2015", "2015-2025", "1995-2005", "2005-2015", "1975-1985", "1990-2003", "1960s", "1950-1960", "Early 2000s", "Late 2000s", "Mid-1990s", "Late 1990s", "Early 1990s", "Early 1980s", "Late 1980s", "Mid-1970s", "Early 1970s", "Late 1960s", "Mid-1960s", "Early 1960s", "Late 1950s", "1950-1955", "1955-1960", "1960-1965", "1965-1970", "1970-1975", "1975-1980", "1980-1985", "1985-1990", "1990", "2005", "1968", "2020", "1950", "1995", "1982", "2003", "2014", "1973", "1987", "1954", "2008", "1999", "1957", "2001", "1969", "1992", "1988", "2017", "1979", "1996", "1959", "1971", "2019", "1963", "2006", "1983", "early 2000s", "late 1990s", "mid-2000s", "1995-2000", "1987-1992", "1968-1973", "1977-1982", "1989-1994", "2003-2008", "2012-2017", "1963-1968", "1983-1988", "1998-2003", "1979-1984", "1958-1963", "1969-1974", "1984-1989", "2005-2010", "2018-2023", "1992-1997", "1971-1976", "1962-1967", "2008-2013"], "Genre": ["Drama", "Comedy", "Thriller", "Action", "Horror", "Science fiction", "Fantasy", "Adventure", "Romance", "Animation", "Musical", "Mystery", "War", "Crime", "Historical", "Documentary", "Biographical", "Sports", "Western", "Spy", "Disaster", "Superhero", "Martial arts", "Supernatural", "Zombie", "Post-apocalyptic", "Space opera", "Satire", "Psychological", "Cyberpunk", "Found footage", "Mockumentary", "Noir", "<PERSON>lasher", "<PERSON><PERSON><PERSON>", "Science Fiction", "Martial Arts", "Indie", "Reality", "Music", "Dance", "Erotica", "Cult", "Silent", "Experimental", "Sci-Fi", "Biopic", "Exploitation", "Erotic", "Musical comedy", "<PERSON><PERSON><PERSON>", "Romantic comedy", "Action thriller", "Family", "Teen", "Political", "Fairy tale", "Coming-of-age", "Psychological thriller", "Silent film", "Biography", "Christmas", "Holiday", "Legal", "Environmental"], "Director": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Sofia Coppola", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Guillermo <PERSON> Toro", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>ho", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Chang-dong Lee", "<PERSON>", "<PERSON>", "<PERSON>", "Coen Brothers", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON> and <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "MPAA Rating": ["PG-13", "R", "G", "NC-17", "PG", "X", "Unrated", "Not rated", "NR", "U", "MA15+", "G/PG", "PG-13/R", "R/NC-17", "TV-MA", "TV-G", "M"], "Plot": ["Film director", "Screenplay", "Scriptwriter", "Film producer", "Cinematographer", "Film editor", "Set designer", "Special effects", "Stunt coordinator", "Costume designer", "Soundtrack composer", "Location scout", "Production assistant", "Casting director", "Storyboard artist", "Film crew", "Behind-the-scenes footage", "Movie set", "Film studio", "Film budget", "Shooting schedule", "Casting process", "Film distribution", "Film promotion", "Film marketing campaign", "Love triangle", "Murder mystery", "Time travel", "Heist", "Revenge", "Alien invasion", "Undercover operation", "Political conspiracy", "Quest for redemption", "Coming of age", "War strategy", "Survival in the wilderness", "Space exploration", "<PERSON><PERSON>", "Battle for justice", "Corporate espionage", "Treasure hunt", "Kidnapping", "Identity theft", "Historical revolution", "Betrayal", "Supernatural forces", "Drug trafficking", "Art theft", "Cybercrime", "Hollywood", "Filming", "Director", "Producer", "Studio", "Set", "<PERSON><PERSON><PERSON>", "Casting", "Soundtrack", "Location", "Cinematography", "Costumes", "Stunts", "Props", "Art direction", "Editing", "Post-production", "Visual effects", "Rehearsals", "Marketing campaign", "Hollywood studio", "On-set drama", "Filming location", "Movie producer", "Casting decision", "Screenplay rewrites", "Director's vision", "Set design", "Special effects team", "Stunt double", "Soundstage", "CGI technology", "Production budget", "Studio backlot", "Script revisions", "Camera crew", "Actor's preparation", "Film editing process", "Prop master", "Art department", "Production delays", "Undercover police officer", "Drug cartel", "Political scandal", "Rebellion", "Archaeological artifact", "Assassination plot", "Prohibition era", "Historic heist", "Underdog sports team", "Hollywood studio system", "Cold War espionage", "Art forgery", "Cyberterrorism", "Serial killer", "Family betrayal", "Secret society", "Dystopian future", "Computer hacking", "Supernatural curse", "Wizarding world"], "Actor": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON> Jr.", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Al Pacino", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "Trailer": ["Bloopers", "Behind-the-scenes footage", "Making-of featurette", "Cast interviews", "Director's commentary", "Set tour", "Production diaries", "Costume and makeup design", "Special effects showcase", "Cinematography highlights", "Sneak peek", "Teaser", "Preview", "Exclusive footage", "Behind-the-scenes look", "On-set interviews", "Production diary", "Filmmaker commentary", "Director's cut preview", "B-roll footage", "Behind-the-scenes clips", "Production featurettes", "Set visits", "Cast and crew interviews", "On-location shooting", "Filmmaking process", "Behind-the-camera moments", "Making-of glimpses", "Set design", "Special effects", "Costume design", "Stunt work", "Production design", "Cinematography", "Featurette", "Behind-the-scenes", "Making-of", "B-roll", "Production video", "On-set footage"], "Song": ["Hallelujah", "My Heart Will Go On", "Eye of the Tiger", "I Will Always Love You", "Moon River", "Shallow", "<PERSON><PERSON><PERSON>", "Don't You (Forget About Me)", "Take My Breath Away", "I Don't Want to Miss a Thing", "I've Had the Time of My Life", "Stayin' Alive", "The Sound of Silence", "Bohemian Rhapsody", "The Power of Love", "Jai <PERSON>", "You've Got a Friend in Me", "Circle of Life", "Let It Go", "Raindrops Keep Fallin' on My Head", "A Whole New World", "<PERSON> and the Beast", "Somewhere Over the Rainbow", "Unchained Melody", "Can't Take My Eyes Off You", "Purple Rain", "I Feel Pretty", "That's <PERSON><PERSON>", "Happy", "My Girl", "Respect", "The Time of My Life", "I Want to Hold Your Hand", "The Way We Were", "You're the One That I Want", "Under the Sea", "<PERSON><PERSON>", "I Got You Babe", "Time of My Life", "<PERSON><PERSON><PERSON>", "Can't Stop the Feeling", "Ghostbusters", "Stand by Me", "Hound Dog", "Piece of My Heart", "Smooth", "I Will Survive", "Born to Be <PERSON>", "Staying Alive", "For Once in My Life", "Time After Time", "Jailhouse Rock", "I Just Can't Wait to Be King", "We Are the Champions", "Space Jam", "A Star is Born", "The Sound of Music", "Mamma Mia!", "The Blues Brothers", "La La Land", "<PERSON><PERSON>", "A Hard Day's Night", "Singin' in the Rain", "The Lion King", "The Greatest Showman", "Pitch Perfect", "<PERSON>", "Dreamgirls", "8 Mile", "Almost Famous", "\"Eurovision Song Contest", "The Commitments", "Crazy Heart", "Coal Miner's Daughter", "<PERSON>", "The Bodyguard"], "Review": ["Behind-the-scenes insights", "Making-of details", "Production process analysis", "Cinematography critique", "Set design evaluation", "Costume design assessment", "Lighting and sound effects evaluation", "Director's vision critique", "Actor/actress performance review", "Script development discussion", "Special effects analysis", "Editing and post-production critique", "Art direction evaluation", "Makeup and hairstyling assessment", "Stunt coordination review", "Location scouting analysis", "Casting choices critique", "Film score and soundtrack evaluation", "Choreography assessment", "Visual effects critique", "Action sequences analysis", "CGI and animation evaluation", "Filming techniques critique", "Background and props assessment", "Dialogue writing review", "Rehearsal process analysis", "Camera work evaluation", "3D and IMAX effects critique", "Motion capture assessment", "Concept art review", "Key grip and gaffer evaluation", "Key makeup artist critique", "Set decorators assessment", "Sound and music editing review", "Visual effects supervisor critique", "Riveting", "Informative", "Insightful", "Fascinating", "Engaging", "Revealing", "Compelling", "Eye-opening", "Entertaining", "Educational", "Behind-the-scenes", "Intriguing", "In-depth", "Candid", "Authentic", "Enthralling", "Captivating", "Unveiling", "Genuine", "Provocative", "Thought-provoking", "Immersive", "Unscripted", "Intimate", "Raw", "Exposing", "Gripping", "Edifying", "Unvarnished", "Unrehearsed", "Unconventional", "Exclusive", "Detailed", "Insider", "Up-close", "Unfiltered", "Comprehensive", "Gritty", "Insider’s perspective", "Exclusive access", "Hands-on", "Real-life", "Unbiased", "Cutting-edge", "Unprecedented", "Groundbreaking", "Making-of", "Discovery", "Uncovering", "Complementary", "Lost footage", "Revealed secrets", "Director's commentary", "Production details", "Never-before-seen footage", "Unseen aspects", "Behind-the-camera", "Process insights", "Unheard stories", "Filmmaking tidbits", "Inside look", "Filming anecdotes", "Production trivia", "Backstage details", "Hidden features", "Set secrets", "Rare glimpses", "The untold story", "Engrossing", "Enlightening", "Lively", "Enticing", "Absorbing", "Passionate", "Unforgettable", "Monumental", "Exquisite", "Astounding", "Mind-blowing", "Mind-bending", "Breathtaking", "Unbelievable", "Remarkable", "Extraordinary", "Unparalleled"], "Character": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Vader", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Neo", "Morpheus", "<PERSON>", "<PERSON>", "Captain <PERSON>", "<PERSON>", "<PERSON><PERSON>", "Rey", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Vizzini", "Buttercup", "Count <PERSON>", "<PERSON><PERSON>", "RoboCop", "Terminator", "<PERSON>", "<PERSON>", "<PERSON>", "The Bride", "<PERSON><PERSON><PERSON>", "The Joker", "<PERSON>", "<PERSON>", "Albus Dumbledore", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Aragorn", "Legolas", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Al Pacino", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Guillermo <PERSON> Toro", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Sofia Coppola", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Princess <PERSON><PERSON>", "<PERSON>", "<PERSON>", "Wonder Woman", "<PERSON>", "Indiana Jones", "Spider-Man", "Black Widow", "<PERSON>", "Iron Man", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Black Panther", "<PERSON>", "Captain <PERSON>", "Doctor Who", "The Flash", "Aquaman", "The Punisher", "Daredevil", "Black Canary", "Green Arrow", "Poison Ivy", "<PERSON>", "The Riddler", "Two-Face", "Catwoman", "Penguin", "The Hulk", "Wolverine", "Deadpool", "<PERSON><PERSON>", "Han Solo", "Trinity", "<PERSON>", "Joker", "Captain <PERSON>", "Professor <PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Rocky <PERSON>", "<PERSON><PERSON>", "<PERSON>", "The Dude", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Dr. <PERSON>", "Django", "<PERSON>", "<PERSON>"]}, "Genre and subgenre classification": {"Title": ["The Godfather", "Pulp Fiction", "<PERSON>", "The Shawshank Redemption", "The Matrix", "Titanic", "The Lord of the Rings", "The Dark Knight", "Jurassic Park", "The Silence of the Lambs", "Fight Club", "The Lion King", "The Wizard of Oz", "Goodfellas", "<PERSON><PERSON><PERSON>'s List", "Braveheart", "The Green Mile", "The Departed", "The Shining", "The Exorcist", "Jaws", "Alien", "Psycho", "Blade Runner", "The Terminator", "Die Hard", "The Bourne Identity", "Casino Royale", "Black Swan", "Gone Girl", "The Avengers", "Guardians of the Galaxy", "The Hunger Games", "The Incredibles", "The Hangover", "Bridesmaids", "Mean Girls", "The Graduate", "500 Days of Summer", "Breakfast at Tiffany's", "Grease", "La La Land", "A Star is Born", "The Shape of Water", "Get Out", "Inception", "Star Wars", "The Social Network", "The Grand Budapest Hotel", "Black Panther", "Whiplash", "The Sixth Sense", "The Princess Bride", "The Big Lebowski", "12 Angry Men", "The Breakfast Club", "The Sandlot", "The Notebook", "The Fault in Our Stars", "The Little Mermaid", "The Sound of Music", "The Rocky Horror Picture Show", "The Karate Kid", "The Pursuit of Happyness", "The Martian", "The Revenant", "Gladiator", "<PERSON> and the Sorcerer's Stone", "Star Wars:", "Saving Private <PERSON>", "Interstellar", "A Clockwork Orange", "The Blair Witch Project", "The Conjuring", "The Babadook", "The Ring", "2001:", "The Prestige", "A Beautiful Mind", "The Usual Suspects", "<PERSON>", "<PERSON>", "The Great Gatsby", "Eternal Sunshine of the Spotless Mind", "Snatch", "No Country for Old Men", "Memento", "The Truman Show", "The Green Lantern"], "Viewers' Rating": ["5-star rating", "4-star rating", "3-star rating", "2-star rating", "1-star rating", "Highly recommended", "Must-see", "Disappointing", "Average", "Overrated", "Underrated", "Cult classic", "Blockbuster", "Masterpiece", "Trash", "Guilty pleasure", "Hidden gem", "Worth the watch", "Timeless", "Unforgettable", "<PERSON><PERSON>", "Instant classic", "Mixed reviews", "All-time favorite", "Cinema gold", "<PERSON><PERSON>", "Riveting", "Thought-provoking", "Classic", "Epic", "Terrific", "Poorly received", "Surprisingly good", "Disgusting", "Exhilarating", "Rated R", "PG-13", "Parental guidance suggested", "Suitable for all ages", "Mature audience only", "Family-friendly", "Not suitable for children", "Adult content", "Viewer discretion advised", "Average rating", "Top-rated", "Low-rated", "Box office flop", "Critical acclaim", "Audience favorite", "Divisive", "Thrilling", "Hilarious", "Heartwarming", "Mind-bending", "Action-packed", "Emotional rollercoaster", "Rave reviews", "Positive feedback", "Negative feedback", "High viewers' ratings", "Low viewers' ratings", "Highly acclaimed", "Critically acclaimed", "Popular choice", "Superb ratings", "Mediocre ratings", "Exceptional reviews", "Outstanding ratings", "Dismal ratings", "Excellent viewer feedback", "Poor viewer feedback", "Top-notch ratings", "Disappointing ratings", "Above-average ratings", "Below-average ratings", "Stellar reviews", "Lackluster ratings", "Remarkable ratings", "Disparaging reviews", "Exceptional viewer ratings", "Unsatisfactory ratings", "High viewers' approval", "Thumbs up", "10/10", "A+", "Top-notch", "Perfect score", "Phenomenal", "Outstanding", "Exemplary", "Superior", "Awe-inspiring", "Solid", "Impressive", "Delightful", "Exceptional", "Splendid", "Remarkable", "Excellent", "Good", "Satisfactory", "Decent", "Enjoyable", "Mediocre", "Fair", "Lackluster", "Subpar", "Unimpressive", "Dismal", "3.5-star rating", "10/10 rating", "8/10 rating", "6/10 rating", "5/10 rating", "3/10 rating", "Thumbs down", "Not recommended", "Poor", "Fantastic", "Passable", "Superb", "Dreadful", "Don't waste your time", "Five stars", "Four stars", "Three stars", "Two stars", "One star"], "Year": ["1980s", "1990s", "2000s", "2010s", "2020s", "1950s", "1960s", "1970s", "1930s", "1940s", "1920s", "1910s", "1900s", "1890s", "1880s", "1870s", "1860s", "1850s", "1840s", "1830s", "1820s", "1810s", "1800s", "1790s", "1780s", "1770s", "1760s", "1750s", "1740s", "1730s", "1720s", "1710s", "1700s", "1690s", "1680s", "1975-1985", "1986-1996", "1997-2007", "2008-2018", "2019-2029", "Early 2000s", "Late 2000s", "Classic", "Vintage", "Contemporary", "Modern", "Golden age", "Old-school", "Mid-century", "New millennium", "Turn of the century", "Roaring twenties", "Post-war era", "Pre-Depression era", "Technicolor era", "Black and white era", "Silent film era", "Blockbuster era", "Independent film era", "Film noir era", "Western era", "Teen film era", "Sci-fi era", "Romantic comedy era", "1970-1980", "1990-2000", "2000-2010", "1980-1990", "2010-2020", "1900-1920", "1920-1940", "1940-1960", "1960-1980", "1980-2000", "2000-2020", "1950-1970", "1970-1990", "1990-2010", "Golden Age", "Silent Era", "New Wave", "Retro", "Nostalgic", "Upcoming", "Revival", "2020", "1930-1940", "1940-1950", "1950-1960", "1960-1970", "1950-1980", "1973", "1939", "2004", "1968", "1975", "2014", "1999", "1985", "1957", "1962", "2006", "1994", "1978", "1964", "1989", "2001", "1960-1969", "1977", "1983", "1955", "1965", "1987", "2020-2022", "2005", "1991", "1979", "1982", "2015", "1945", "1972", "2003", "1958", "1997", "1980", "2018", "1942", "1969", "1988", "2008-2010"], "Genre": ["Action", "Adventure", "Comedy", "Drama", "Fantasy", "Horror", "Mystery", "Romance", "Science Fiction", "Thriller", "Animation", "Crime", "Family", "Historical", "Musical", "War", "Western", "Documentary", "Superhero", "Spy", "Disaster", "Martial Arts", "Noir", "Psychodrama", "Romantic Comedy", "Satire", "Surrealism", "Biopic", "Epic", "Found Footage", "Mockumentary", "Road Movie", "Space Opera", "Sports", "Supernatural", "Science fiction", "Biography", "Martial arts", "Teen", "Black comedy", "Dark fantasy", "Psychological thriller", "<PERSON>lasher", "Space opera", "Zombie", "<PERSON>", "Cyberpunk", "Monster", "Political", "Sci-fi", "Biographical", "Dance", "Psychological", "Legal", "Period", "Coming-of-age", "Found footage", "Animated", "Film Noir", "Silent", "Surreal", "Time Travel", "Music", "Holiday", "Experimental"], "Director": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Sofia Coppola", "<PERSON>", "Guillermo <PERSON> Toro", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>ho", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Timoth<PERSON> Chalamet", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Gaspar <PERSON>", "Sergio <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Coen Brothers", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON> and <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "MPAA Rating": ["G", "PG", "PG-13", "R", "NC-17", "Not Rated", "M", "Unrated", "GP", "NR", "X", "General Audience", "Parental Guidance Suggested", "Restricted"], "Plot": ["Time travel", "Zombie apocalypse", "Space exploration", "Supernatural creatures", "Underwater adventure", "Post-apocalyptic world", "Detective investigation", "Revenge plot", "Forbidden love", "Historical war", "Time loop", "Survival thriller", "Superhuman powers", "Heist scheme", "Alien invasion", "Superhero origin story", "Cyberpunk dystopia", "Martial arts tournament", "Political conspiracy", "Reality-bending technology", "Treasure hunt", "Robot uprising", "Coming-of-age journey", "Identity theft", "Environmental disaster", "Love Triangle", "Revenge", "Undercover Investigation", "Time Travel", "Survival", "Family Drama", "Adventure", "Coming of Age", "Heist", "War", "Mystery", "Superhero", "Western", "Fantasy", "Science Fiction", "Horror", "Political Thriller", "Historical Drama", "Romantic Comedy", "Biographical", "Psychological Thriller", "Disaster", "Action", "Crime", "Musical", "Artificial intelligence uprising", "Undercover cop", "Love triangle", "Coming of age", "Rags to riches", "Murder mystery", "Road trip", "Cold war espionage", "World war II resistance", "Historical epic", "Sports underdog story", "Folklore fantasy", "Survival adventure", "Supernatural horror", "Light-hearted comedy", "Political corruption", "Werewolf", "Vampire romance", "Zombie outbreak", "High school drama", "Disaster movie", "Superhero team-up", "Spy thriller", "Assassination plot", "Mobster underworld", "Underdog story", "Apocalypse", "Teen romance", "Family drama", "Historical fiction", "Science fiction", "Comedy", "Thriller", "Documentary"], "Actor": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> Jr.", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Gal Gadot", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Oct<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Lupita <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "Trailer": ["Action-packed", "Heart-wrenching", "Suspenseful", "Sci-fi thriller", "Romantic comedy", "Horror", "Adventure", "Fantasy", "Animated", "Historical drama", "Thrilling", "Romantic", "Sci-fi", "Comedy", "Drama-filled", "Fantastical", "Comedic", "Science fiction", "Thriller", "Mystery", "Drama", "Fantasy adventure", "Musical", "Family-friendly", "Independent film"], "Song": ["Purple Rain", "Bohemian Rhapsody", "My Heart Will Go On", "Born to Be <PERSON>", "Don't Stop Believin'", "Eye of the Tiger", "Crazy in Love", "Let It Go", "I Will Always Love You", "Can't Help Falling in Love", "You've Got a Friend in Me", "My Girl", "La La Land", "Shallow", "Hallelujah", "I Want to Hold Your Hand", "Happy", "<PERSON><PERSON><PERSON>", "Unchained Melody", "I Don't Want to Miss a Thing", "Under the Sea", "<PERSON><PERSON><PERSON>", "Another Day of Sun", "Somewhere Over the Rainbow", "You're the One That I Want", "Jai <PERSON>", "Time of My Life", "Stayin' Alive", "\"I Don't Want to Miss", "Ghostbusters", "Take My Breath Away", "Let's Go Crazy", "Maniac", "Danger Zone", "St. Elmo's Fire", "The Power of Love", "Nothing's Gonna Stop Us Now", "I Don't Want to Live Without You", "Who You Are", "Friends Will Be Friends", "A Star is Born", "The Sound of Music", "Mamma Mia!", "The Lion King", "Singin' in the Rain", "Grease", "Pitch Perfect", "Coal Miner's Daughter", "Fame", "The Bodyguard", "Dreamgirls", "Sister Act", "8 Mile", "High School Musical", "Walk the Line", "Empire Records", "<PERSON>", "Crazy Heart", "The Blues Brothers", "Almost Famous", "A Whole New World", "Moon River", "Oh, <PERSON> Woman", "Greased Lightnin'", "Circle of Life", "Don't You", "He's a Pirate", "I See Fire", "I Believe I Can Fly", "Can You Feel the Love Tonight", "<PERSON> and the Beast", "Can't Stop the Feeling!", "The Sound of Silence", "Livin' on a Prayer", "Yellow Submarine", "Hotel California", "The Way We Were", "Boogie Woogie Bugle Boy", "I Will Survive", "It's My Party", "Blue Suede Shoes"], "Review": ["Action-packed", "Gripping", "Dramatic", "Suspenseful", "Thrilling", "Heart-pounding", "Intense", "Fast-paced", "Explosive", "Spectacular", "Jaw-dropping", "Mind-blowing", "Breathtaking", "Exciting", "Captivating", "Riveting", "Adrenaline-pumping", "Compelling", "Exhilarating", "Electrifying", "Entertaining", "Engaging", "Immersive", "Enthralling", "Spellbinding", "Fascinating", "Intriguing", "Unique", "Original", "Groundbreaking", "Inventive", "Innovative", "Refreshing", "Thought-provoking", "Unforgettable", "Heartwarming", "Hilarious", "Mind-bending", "Emotional", "Touching", "Uplifting", "Horrifying", "Chilling", "Creepy", "<PERSON><PERSON>", "Frightening", "Adrenaline-fueled", "Mesmerizing", "Imaginative", "Fresh", "Offbeat", "Quirky", "Eccentric", "Unconventional", "Avant-garde", "Absorbing", "Complicated", "Unpredictable", "Cinematic", "Masterful", "Artistic", "Impactful", "Provocative", "Raw", "Gritty", "Humorous", "Spine-tingling", "Moving", "Empowering", "Magical", "Lighthearted", "Terrifying", "Culturally significant", "Emotionally resonant", "Darkly comedic", "Visually stunning", "Disappointing", "Underwhelming", "One-dimensional", "Suspenseful thriller", "Romantic comedy", "Intense drama", "Family-friendly", "Mind-bending science fiction", "Classic adventure", "Gritty crime", "Historical epic", "Feel-good musical", "Horror", "Coming-of-age", "Independent", "Foreign", "Animated", "Superhero", "War", "Musical", "Mystery", "Adventure", "Crime", "Comedy", "Fantasy", "Science fiction", "Thriller", "Western", "Drama", "Romance", "Documentary", "Biographical", "Sports", "Teen", "Satire", "Action"], "Character": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Vader", "<PERSON>", "Spider-Man", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Wolverine", "Black Widow", "<PERSON>", "<PERSON>", "<PERSON>", "Wonder Woman", "<PERSON>", "Neo", "Terminator", "<PERSON>", "Indiana Jones", "The Joker", "<PERSON><PERSON>", "Rey", "Captain <PERSON>", "<PERSON>", "Iron Man", "<PERSON>", "Dracula", "The Hulk", "<PERSON>", "<PERSON><PERSON><PERSON>", "Aragorn", "<PERSON>", "<PERSON>", "The Bride", "Trinity", "<PERSON>", "King <PERSON>", "<PERSON>", "<PERSON><PERSON>", "Corporal <PERSON>", "Princess <PERSON><PERSON>", "Han Solo", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>walker", "Emperor <PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chewbacca", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Count <PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Jabba the Hutt", "C-3PO", "R2-D2", "Supreme Leader <PERSON><PERSON><PERSON>", "General Grievous", "General <PERSON>", "Captain <PERSON><PERSON><PERSON>", "Princess <PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Aunt <PERSON><PERSON>", "Uncle <PERSON>", "Wedge <PERSON>", "Admiral <PERSON><PERSON><PERSON>", "<PERSON> <PERSON><PERSON>", "Wick<PERSON> <PERSON><PERSON>", "Doctor <PERSON><PERSON><PERSON>", "Grand Admiral <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "K-2SO", "<PERSON><PERSON> Rook", "Director <PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Baze Malbus", "<PERSON>", "<PERSON><PERSON><PERSON>", "Albus Dumbledore", "<PERSON><PERSON><PERSON>", "Captain <PERSON><PERSON><PERSON>", "<PERSON>", "Legolas", "Morpheus", "<PERSON>", "Dr. <PERSON>", "<PERSON>", "Princess <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Simba", "Scar", "<PERSON><PERSON>", "<PERSON><PERSON>", "Mushu", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Captain <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Rocky <PERSON>", "<PERSON>", "<PERSON><PERSON>", "Black Panther", "Maleficent", "<PERSON>", "The Terminator", "Deadpool", "Optimus Prime", "Jigsaw", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Mad <PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Legally <PERSON><PERSON>", "The Iron Lady", "Miracle on 34th Street", "Little Miss Sunshine"]}, "Movie trailers and teasers": {"Title": ["The Shawshank Redemption", "The Godfather", "Pulp Fiction", "Inception", "The Dark Knight", "<PERSON>", "The Matrix", "Titanic", "Gladiator", "The Lion King", "Saving Private <PERSON>", "Jurassic Park", "The Lord of the Rings", "Star Wars: Episode IV", "Raiders of the Lost Ark", "Back to the Future", "Jaws", "E.T. the Extra-Terrestrial", "The Terminator", "Aliens", "The Sixth Sense", "The Exorcist", "The Shining", "Blade Runner", "The Princess Bride", "Fight Club", "Goodfellas", "<PERSON><PERSON><PERSON>'s List", "Toy Story", "The Sound of Music", "<PERSON>", "The Karate Kid", "The Wizard of Oz", "Casablanca", "Avatar", "Gone with the Wind", "<PERSON>", "It's", "2001:", "A Clockwork Orange", "The Breakfast Club", "The Silence of the Lambs", "The Graduate", "Rebel Without", "Psycho", "The Avengers", "The Social Network", "Star Wars", "<PERSON> and the Sorcerer's Stone", "The Great Gatsby", "Mad <PERSON>", "The Revenant", "Black Panther", "Joker", "Spider-Man", "The Wolf of Wall Street", "La La Land", "The Shape of Water", "Get Out", "Dunkirk", "The Grand Budapest Hotel", "The Florida Project", "Moonlight", "Boyhood", "Whiplash", "The Martian", "Interstellar", "Gravity", "The Hurt Locker", "American Sniper", "Her", "<PERSON><PERSON>", "Avengers", "<PERSON> and the Philosopher's Stone", "Frozen", "The Big Lebowski", "Die Hard", "Taxi Driver", "Apocalypse Now", "The Matrix Reloaded", "Jurassic World", "Finding Nemo", "The Incredibles", "Guardians of the Galaxy", "The Hunger Games", "Wonder Woman", "<PERSON> and the Beast", "12 Years", "A Few Good Men", "Good Will Hunting", "500 Days of Summer", "Mean Girls", "Braveheart", "Alien", "Reservoir Dogs", "The Good, the Bad and the Ugly", "Raging Bull"], "Viewers' Rating": ["Must-see", "Amazing", "Brilliant", "Captivating", "Exhilarating", "Fantastic", "Gripping", "Thrilling", "Mesmerizing", "<PERSON><PERSON><PERSON>", "Phenomenal", "Mind-blowing", "Outstanding", "Unforgettable", "Impressive", "Riveting", "Exciting", "Terrific", "Spectacular", "Intriguing", "Compelling", "Engrossing", "Spellbinding", "Awe-inspiring", "Superb", "Breathtaking", "Unmissable", "Electrifying", "Enthralling", "Masterful", "Strongly Recommended", "Highly Entertaining", "A Must-Watch", "<PERSON>y", "Top-notch", "Five-star", "Four-star", "Three-star", "Two-star", "One-star", "Highly rated", "Poorly rated", "Mixed reviews", "Critically acclaimed", "Overrated", "Underrated", "Rave reviews", "Disappointing", "Not worth watching", "Top-rated", "Average rating", "Highly recommended", "Audience favorite", "Mediocre", "Terrible", "Excellent", "Good but not great", "Solid performance", "Subpar", "Promising", "Unimpressive", "Worthy of praise", "Lackluster", "Astonishing", "Dismal", "Decent", "Five-star rating", "Four-star rating", "Three-star rating", "Two-star rating", "One-star rating", "High ratings", "Low ratings", "Positive reviews", "Negative reviews", "Highly anticipated", "Best movie of the year", "Highly acclaimed", "A solid 5/5", "A decent 4/5", "A mediocre 3/5", "A disappointing 2/5", "A terrible 1/5", "Excellent feedback", "Lackluster response", "Disappointing reception", "Terrible ratings", "Exceptional ratings", "Mediocre ratings", "Mixed ratings", "Must watch", "5-star", "A must-see", "Exceptional", "Addictive", "Entertaining", "Unique", "Compulsory", "Remarkable", "Unputdownable", "Wondrous", "Indispensable", "Incredible", "Game-changing", "Epic", "High-octane", "Jaw-dropping", "Flawless", "Solid", "Good", "Average", "Underwhelming", "Bland", "Avoid"], "Year": ["1990", "2005", "2012", "1987", "2020", "1975", "1999", "2015", "1981", "2002", "2018", "1970s", "1992", "2008", "2019", "1985", "2025", "1968", "2000s", "2016", "1983", "1978", "2007", "2021", "1995", "2013", "1989", "2003", "2017", "1960s", "1998", "2009", "2014", "1984", "2022", "1975-1985", "2010", "1988", "1950s", "1977", "1980-1990", "1969", "1965", "1973", "1962", "1971", "1953", "2006", "1979", "2001", "1986", "1967", "1974", "2010-2020", "1990-1999", "1980s", "1966", "1997", "2000", "1982", "1963-1970", "1964", "1972", "1993", "1981-1990", "1990s", "Early 2000s", "2010-2015", "Late 1990s", "2000-2010", "1940s", "Late 2000s", "Early 1990s", "1976", "1963", "1957", "2018-2020", "1991", "1955", "1958"], "Genre": ["Action", "Adventure", "Animation", "Biography", "Comedy", "Crime", "Documentary", "Drama", "Family", "Fantasy", "Film noir", "History", "Horror", "Musical", "Mystery", "Romance", "Science fiction", "Sport", "Superhero", "Thriller", "War", "Western", "Disaster", "Mockumentary", "Musical comedy", "Psychological thriller", "Romantic comedy", "Sci-fi thriller", "<PERSON>lasher", "Spy", "Supernatural horror", "Teen", "Tragedy", "Urban fantasy", "Western comedy", "Science Fiction", "Historical", "Biographical", "Sports", "Noir", "Martial Arts", "Zombie", "Psychological", "Independent", "Erotic", "Neo-Noir", "Road", "Crime Caper", "Cyberpunk", "Adventure comedy", "Epic", "Gangster", "Heist", "Martial arts", "Paranormal", "Political", "Satire", "Space opera", "Sci-fi", "Dance", "Time travel", "Alien invasion", "Post-apocalyptic", "Found footage", "Coming of age", "Crime thriller", "Biopic", "Supernatural", "Space", "Time Travel", "Alien", "Vampire"], "Director": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Sofia Coppola", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Guillermo <PERSON> Toro", "<PERSON><PERSON>ho", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Gaspar <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Coen Brothers", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "MPAA Rating": ["G", "PG", "PG-13", "R", "NC-17", "Unrated", "Rated X", "NR", "TV-MA", "TV-PG", "GP", "M", "X"], "Plot": ["Love triangle", "Revenge", "Time travel", "Alien invasion", "Undercover cop", "Amnesia", "Identity theft", "Quest for treasure", "Betrayal", "Zombie apocalypse", "Haunted house", "Political conspiracy", "Coming of age", "Survival in the wilderness", "Forbidden love", "Family inheritance", "World domination", "Kidnapping", "Artificial intelligence takeover", "Genetic engineering", "Nuclear war", "Space exploration", "Historical assassination", "Cyber terrorism", "Superhero origin story", "Love Triangle", "Time Travel", "Undercover Operation", "Space Exploration", "Treasure Hunt", "Identity Theft", "Survival", "Political Conspiracy", "Forbidden Romance", "Haunted House", "Heist", "Manhunt", "War", "Road Trip", "Superhero Origin Story", "Rags to <PERSON><PERSON>", "Family Feud", "Sisterhood", "Corporate Espionage", "Animal Migration", "Quest for Redemption", "Cyber Warfare", "Disaster Escape", "Undercover agent", "Supernatural powers", "Treasure hunt", "Disappearance", "Assassination plot", "Revolutionary war", "Artificial intelligence uprising", "Revenge plot", "Cyber warfare", "Global pandemic", "Parenthood struggles", "Mercenary mission", "Detectives and crime solving", "Teenage romance", "Climate change disaster", "Time loop predicament", "Undercover operation", "Mystery murder", "Superhero origin", "Post-apocalyptic world", "High school prom", "World war", "Rags to riches story", "Mafia family drama", "Natural disaster survival", "Historical romance", "Cybercrime", "Bank heist", "Zombie outbreak", "Animal adventure", "Murder mystery", "Dystopian future", "War story", "High school drama", "Family drama", "Forbidden romance", "Apocalyptic event", "Escape from prison", "Technology gone wrong", "Historical drama", "Environmental disaster", "Amnesia storyline"], "Actor": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON> Jr.", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Gal Gadot", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "Trailer": ["Teaser trailer", "Official trailer", "Red band trailer", "International trailer", "Teaser", "Extended trailer", "First look", "Sneak peek", "Promo", "Preview", "Footage", "Teaser video", "Clip", "Preview trailer", "Trailer", "Film preview", "Motion picture teaser", "Pre-release footage", "Extended cut", "Teaser clip", "Trailer release", "Promotional clip", "Exclusive look", "Movie preview"], "Song": ["I Will Always Love You", "Let It Go", "My Heart Will Go On", "Bohemian Rhapsody", "Eye of the Tiger", "Unchained Melody", "Lose Yourself", "Girls Just Want to Have Fun", "<PERSON><PERSON><PERSON>", "Can't Stop the Feeling", "I Wanna Dance with <PERSON>", "We Will Rock You", "\"You're the One That", "\"I Don't Want to Miss", "Stayin' Alive", "<PERSON> <PERSON><PERSON>", "Born to Be <PERSON>", "Footprints on the Moon", "All the Stars", "I Don't Want to Wait", "St. Elm<PERSON>'s Fire (Man in Motion)", "Pretty Woman", "\"If", "Magic", "I Will Survive", "Purple Rain", "Happy", "Hello", "I Don't Want to Miss a Thing", "Ghostbusters", "I Believe I Can Fly", "Jai <PERSON>", "Skyfall", "My Girl", "Mrs. <PERSON>", "The Sound of Silence", "Flashdance... What a Feeling", "Circle of Life", "The Time of My Life", "You've Got a Friend in Me", "La La Land", "Can't Stop the Feeling!", "Don't You (Forget About Me)", "Shallow", "I Want to Hold Your Hand", "Fly Me to the Moon", "Moon River", "<PERSON><PERSON>", "Hello, <PERSON>!", "Sweet Caroline", "Bad Romance", "Boogie Wonderland", "<PERSON><PERSON><PERSON>", "The Power of Love", "My Way", "Under the Sea", "Take My Breath Away", "Kiss from a Rose", "Three Little Birds", "Stay", "Eye to Eye", "The Sound of Music", "Gangnam Style", "Hallelujah", "Don't You", "Let's Get It On", "Somewhere Over the Rainbow", "Uptown Funk", "Livin' on a Prayer", "Old Time Rock and Roll"], "Review": ["Gripping", "<PERSON><PERSON><PERSON>", "Breathtaking", "Epic", "Captivating", "Riveting", "Thrilling", "Heart-pounding", "Intense", "Exhilarating", "Spectacular", "Electrifying", "Mind-blowing", "Awe-inspiring", "Compelling", "Jaw-dropping", "Impressive", "Unforgettable", "Masterful", "<PERSON><PERSON>", "Complete masterpiece", "Perfectly crafted", "Flawless", "Outstanding", "Brilliant", "Stellar", "Remarkable", "Game-changing", "Phenomenal", "Top-notch", "Must-see", "Full of surprises", "Unique and original", "Unbelievable", "A rollercoaster of emotions", "Exciting", "Intriguing", "Suspenseful", "Enthralling", "Mesmerizing", "Powerful", "Dynamic", "Spellbinding", "Unpredictable", "Unsettling", "Shocking", "Extraordinary", "Unparalleled", "Unmissable", "Gripping storyline", "Visually stunning", "Compelling characters", "Intense action", "Riveting plot", "Mind-blowing visuals", "Memorable performances", "Edge-of-your-seat", "Incredible special effects", "Breath-taking cinematography", "Dynamic direction", "Thrilling suspense", "Outstanding sound design", "Masterful storytelling", "Electrifying performances", "Unforgettable moments", "Impressive production design", "Unpredictable twists", "Captivating visuals", "Compelling narrative", "Immersive experience", "Exhilarating action sequences", "Jaw-dropping visuals", "Unforgettable characters", "Mesmerizing storytelling", "Gripping drama", "Spellbinding visuals", "Spectacular scope", "Unbelievable spectacle", "Awe-inspiring visuals", "Heart-pounding excitement", "Unforgettable dialogue", "Stirring emotions", "Phenomenal storytelling", "Unforgettable scenes", "Thought-provoking", "Evocative", "Moving", "Touching", "Heart-wrenching", "Emotionally-charged", "Heart-stopping", "Mind-bending", "Eye-opening", "Exquisite", "World-class", "Oscar-worthy", "Seamless special effects", "Phenomenal acting", "Terrifyingly intense", "Strikingly beautiful", "Masterpiece", "Hauntingly beautiful", "Emotionally gripping", "Powerfully moving", "Brilliantly directed", "Edge-of-your-seat suspense", "Genuinely terrifying", "Unapologetically bold", "Honestly haunting", "Absolutely breathtaking"], "Character": ["<PERSON>", "<PERSON><PERSON> Vader", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Black Widow", "Captain <PERSON>", "The Joker", "Wonder Woman", "Spider-Man", "<PERSON>", "<PERSON>", "<PERSON>", "Rey", "Iron Man", "Princess <PERSON><PERSON>", "The Hulk", "Black Panther", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Legolas", "Aragorn", "<PERSON><PERSON><PERSON>", "Professor <PERSON><PERSON><PERSON><PERSON>", "Albus Dumbledore", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "R2-D2", "C-3PO", "Chewbacca", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Groot", "Gamora", "<PERSON>", "Drax", "<PERSON><PERSON>", "Neb<PERSON>", "Aquaman", "Captain <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Simba", "Nemo", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Indiana Jones", "<PERSON>", "Neo", "Trinity", "Dumbledore", "Voldemort", "The Terminator", "<PERSON>", "<PERSON><PERSON><PERSON>", "Rocky <PERSON>", "Joker", "Mad <PERSON>", "<PERSON><PERSON>", "ET", "<PERSON>", "Captain <PERSON>", "<PERSON>", "<PERSON><PERSON>", "Blade", "Wolverine", "The Flash", "Green Lantern", "<PERSON><PERSON>", "Deadpool", "<PERSON>", "Ant-<PERSON>", "Star-Lord", "Black Canary", "Green Arrow", "<PERSON>girl", "<PERSON>", "Mystique", "<PERSON>", "<PERSON>", "King <PERSON>", "<PERSON>", "<PERSON>", "Rambo", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Pocahontas", "Ariel", "<PERSON>", "Tiana", "Hercules", "<PERSON><PERSON>", "Maleficent", "Cinderella", "<PERSON>", "<PERSON>", "<PERSON>uss in Boots", "Tinker Bell", "King Kong", "Godzilla"]}, "Awards and nominations received": {"Title": ["The Godfather", "<PERSON><PERSON><PERSON>'s List", "Titanic", "The Shawshank Redemption", "The Lord of the Rings", "<PERSON>", "La La Land", "The Dark Knight", "The Silence of the Lambs", "Gladiator", "A Beautiful Mind", "The Departed", "The Shape of Water", "12 Years", "<PERSON><PERSON>", "Moonlight", "The Artist", "Argo", "Slumdog Millionaire", "No Country for Old Men", "Million Dollar Baby", "The Hurt Locker", "The King's Speech", "Atonement", "American Beauty", "Braveheart", "The English Patient", "Shakespeare in Love", "Pulp Fiction", "The Wolf of Wall Street", "The Social Network", "Inception", "Gravity", "Inglourious Basterds", "American Hustle", "<PERSON><PERSON><PERSON> Unchained", "The Revenant", "Arrival", "Black Swan", "The Fighter", "The Descendants", "Parasite", "Boyhood", "Black Panther", "Dallas Buyers Club", "Good Will Hunting", "Girl, Interrupted", "Chicago", "The Pianist", "Precious", "Lincoln", "Silver Linings Playbook", "The Curious Case of <PERSON>", "12 Angry Men", "The Shining", "The Graduate", "ET", "Manchester by the Sea", "Gone with the Wind", "Casablanca", "<PERSON>", "The Sound of Music", "Spotlight", "Joker", "The Wizard of Oz", "<PERSON> and the Seven Dwarfs", "The Bridge on the River Kwai", "<PERSON><PERSON><PERSON><PERSON>", "My <PERSON> Lady", "<PERSON>", "Jaws", "E.T. the Extra-Terrestrial", "Fargo", "Brokeback Mountain", "Avatar", "Star Wars: Episode IV", "It's", "West Side Story", "<PERSON><PERSON><PERSON>", "One Flew Over the Cuckoo's Nest", "<PERSON>", "<PERSON>", "The French Connection", "The Apartment", "Midnight Cowboy", "<PERSON> vs. <PERSON>", "<PERSON>", "<PERSON>", "Terms of Endearment", "Platoon", "Dances with Wolves", "The Piano", "A Star is Born", "The Aviator", "Driving Miss Daisy", "Room", "The Green Mile", "The Fugitive", "Philadelphia"], "Viewers' Rating": ["A must-see", "Highly recommended", "Worth watching", "Amazing", "Outstanding", "Unforgettable", "Must watch", "Masterpiece", "Incredible", "Impressive", "Astounding", "A classic", "Breathtaking", "Touching", "Spectacular", "Remarkable", "Fantastic", "Phenomenal", "Awe-inspiring", "Terrific", "Compelling", "Captivating", "Superb", "A gem", "Riveting", "Excellent", "Glorious", "Brilliant", "Exceptional", "Tantalizing", "Spellbinding", "Groundbreaking", "5-star rating", "10/10 rating", "9 out of 10", "A+ rating", "Must-see movie", "Top-notch", "Flawless", "Marvelous", "<PERSON><PERSON><PERSON>", "Perfect score", "High praise", "Praiseworthy", "Honorable mention", "Award-worthy", "Standout performance", "Top-rated", "Rave reviews", "Gold standard", "Worthy of accolades", "Commendable", "First-rate", "Unparalleled", "Exemplary", "Five-star rating", "10/10", "A+", "Must-see", "Perfect", "Deserving", "Engrossing", "Gripping", "Stirring", "Inspirational", "Heartwarming", "Thought-provoking", "Moving", "5 stars", "4.5 stars", "9/10", "B-", "Solid 8/10", "3 out of", "Must-See", "Decent", "Golden Globe winner", "Academy Award nominee", "Oscar-worthy", "Critics' Choice", "Rotten Tomatoes Certified Fresh", "7.5 out of 10", "87% approval", "Worth the hype", "Nominated for multiple awards", "Five-star performance", "Deserves recognition", "8.5 out of 10", "Golden Palm winner", "Cannes Film Festival selection", "Box office hit", "95% on Rotten Tomatoes", "A perfect 10", "A masterpiece", "Hugely popular", "4.5 out of", "9/10 rating", "3.5 stars", "8.5/10 rating", "4-star rating", "7/10 rating", "6/10 rating", "2.5 stars", "5 out of 10 rating", "2-star rating", "4/10 rating", "1.5 out of", "3/10 rating", "1-star rating", "2 out of 10 rating", "0.5 out of", "1/10 rating", "Highly rated", "Moderately rated", "Poorly rated", "Mixed reviews", "Critically acclaimed", "Fan favorite", "Average viewer rating", "Mostly positive feedback", "Mostly negative feedback", "Low-rated", "Above average rating", "Below average rating", "Recommended by viewers", "Not recommended by viewers"], "Year": ["2020", "1995", "2007", "1983", "2015", "1979", "2002", "2019", "2005", "1998", "1987", "2012", "1976", "2009", "1992", "1985", "2017", "2000", "1996", "1989", "2014", "1978", "2004", "2011", "2018", "2003", "1999", "1994", "1981", "2013", "2006", "2016", "1997", "1990", "1988", "2005-2009", "1965", "2010s", "1980", "1990-1995", "1973", "1956", "1977-1981", "1947", "1969-1972", "2001", "1954", "1943", "1966", "1959", "1975", "1950-1953", "Early 2000s", "1970-1975", "1987-1990", "1953", "2022", "Late 1990s", "1940s", "1965-1970", "2008", "1982", "1977-1982", "2000s", "1950", "1980s", "2013-2015", "1962", "1960s", "1957", "1968", "1956-1957", "1988-1992", "2010", "1963", "1980-1985", "2001-2003", "1967-1969", "1983-1986", "1960-1962", "1977", "1966-1968", "1972", "1964-1966", "2000-2005", "1976-1980", "1967", "1971", "1960", "1947-1950", "1974", "1964", "1969", "1955", "1996-2000"], "Genre": ["Drama", "Comedy", "Action", "Horror", "Sci-Fi", "Thriller", "Fantasy", "Animation", "Musical", "Romance", "Adventure", "Western", "Documentary", "Mystery", "War", "Crime", "Biopic", "Historical", "Superhero", "Sports", "Music", "Family", "Independent", "Foreign", "Cult", "Noir", "Satire", "Disaster", "Supernatural", "Political", "LGBTQ+", "Teen", "Coming-of-age", "Road trip", "Mockumentary", "Film-Noir", "History", "Sport", "Biographical", "Children", "Courtroom", "Epic", "Gangster", "Heist", "Martial Arts", "<PERSON><PERSON><PERSON>", "Period", "Psychological", "Suspense", "Biography", "Sci-fi", "Crime-thriller", "Animated musical", "Science fiction", "Romantic comedy", "Adventure-drama", "Biographical drama", "War film", "Historical drama", "Fantasy-comedy", "Action-thriller", "Horror-comedy", "Crime-drama", "Romantic fantasy", "Music biopic", "Family animation", "Romantic", "Animated", "Spy", "Reality", "Fiction", "Paranormal", "Surreal", "Slapstick", "Neo-noir", "Environmental", "Road", "Erotic"], "Director": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Guillermo <PERSON> Toro", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Sofia Coppola", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>ho", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "Regina King", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "MPAA Rating": ["PG-13", "R", "G", "PG", "NC-17", "Unrated", "M", "X", "Approved", "NR", "TV-MA"], "Plot": ["Time travel", "Revenge plot", "Love triangle", "Undercover operation", "Espionage mission", "Heist", "Alien invasion", "Survival story", "War drama", "Political conspiracy", "Treasure hunt", "Assassination plot", "Bank robbery", "Historical biopic", "Zombie apocalypse", "Coming-of-age story", "Space exploration", "Family feud", "Corporate corruption", "Superhero origin", "Sports underdog", "Cybersecurity threat", "Environmental activism", "Medical breakthrough", "Courtroom drama", "Mystery solving", "Coming of age", "Family drama", "Sports competition", "War", "Survival", "Political intrigue", "Road trip", "Quest for immortality", "Virtual reality", "Disaster survival", "Buddy cop", "Secret mission", "Betrayal", "High school romance", "<PERSON>ult escape", "Rags to riches", "Historical drama", "Con artist scheme", "Mystery thriller", "Underdog story", "Historical fiction", "Art heist", "Wilderness survival", "War epic", "Cyberpunk", "Disaster movie", "Post-apocalyptic world", "Romantic comedy", "High school prom", "Lighthearted adventure", "Crime spree", "Coming of age story", "World War II", "Undercover agent", "Medieval kingdom", "Mafia boss", "Murder investigation", "Bank heist", "Prohibition era", "Political corruption", "Cyborg assassin", "Superhero origin story", "Hollywood scandal", "Robot uprising", "Drug cartel", "Western showdown", "The Godfather", "<PERSON><PERSON><PERSON>'s List", "<PERSON>", "Titanic", "The Shawshank Redemption", "The Lord of the Rings", "Gladiator", "The Departed", "No Country for Old Men", "Million Dollar Baby", "The Artist", "Slumdog Millionaire", "The Social Network", "Argo", "12 Years", "<PERSON><PERSON>", "La La Land", "Moonlight", "The Shape of Water", "Black Panther", "Parasite", "A Star is Born", "Joker", "Once Upon", "Nomadland"], "Actor": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>-<PERSON>", "<PERSON>", "Al Pacino", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Oct<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON> Jr.", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Jude Law", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Penélope Cruz", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "Trailer": ["Academy Awards", "Golden Globes", "Critics Choice Awards", "Screen Actors Guild Awards", "Cannes Film Festival", "Tribeca Film Festival", "Sundance Film Festival", "Independent Spirit Awards", "BAFTA Awards", "Toronto International Film Festival", "Oscar-winning", "Golden Globe-nominated", "Award-winning", "Nominated for Best Picture", "Critically acclaimed", "Festival winner", "Recognition for acting", "Honored at Cannes", "Awarded for best screenplay", "Winner of multiple accolades", "Teaser trailer", "Official trailer", "Extended trailer", "Award-winning trailer", "Nominated trailer", "Best picture trailer", "Critics' choice trailer", "Golden Globe nominated trailer", "Oscar-nominated trailer", "Sundance award-winning trailer", "Nominated for Golden Globe", "Cannes Film Festival selection", "Academy Award contender", "Critics Choice Award nominee", "Sundance Film Festival winner", "Independent Spirit Award recipient", "<PERSON><PERSON> d'Or contender", "BAFTA nominated", "Emmy Award-winning", "Teaser", "Trailer", "Official preview", "Sneak peek", "Film clip", "Featurette", "Promo", "Preview", "Opening scene"], "Song": ["My Heart Will Go On", "Skyfall", "The Lion King", "Let It Go", "The Sound of Music", "<PERSON><PERSON><PERSON>", "Shallow", "Jai <PERSON>", "<PERSON> and the Beast", "The Power of Love", "Can't Stop the Feeling!", "City of Stars", "Take My Breath Away", "Glory", "The Way We Were", "I Don't Want to Miss a Thing", "Purple Rain", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "My Girl", "Flashdance... What a Feeling", "A Whole New World", "Against All Odds", "The Time of My Life", "You Light Up My Life", "Eye of the Tiger", "Lose Yourself", "I Will Always Love You", "Something About the Way You Look Tonight", "Raindrops Keep Fallin' on My Head", "Over the Rainbow", "You'll Be in My Heart", "Under the Sea", "You've Got a Friend in Me", "I Just Can't Wait to Be King", "Into the Unknown", "Can You Feel the Love Tonight", "Colors of the Wind", "When You Wish Upon a Star", "Moon River", "Zip-a-<PERSON>-<PERSON>-<PERSON>h", "Can’t Stop the Feeling", "<PERSON><PERSON><PERSON>", "Circle of Life", "I Just Called to Say I Love You", "(I've Had) The Time of My Life", "Fame", "Sooner or Later", "Ev'ry Time We Say Goodbye", "Wind Beneath My Wings", "Remember Me", "Happy", "Let's Hear It for the Boy", "The Man in the Mirror", "<PERSON>", "You’ll Be in My Heart", "Streets of Philadelphia", "I Believe I Can Fly", "Ghetto Supastar", "<PERSON>", "<PERSON>", "<PERSON> <PERSON>", "<PERSON>", "<PERSON>", "Sia", "<PERSON>", "<PERSON>", "Eminem", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>e", "<PERSON>", "<PERSON>", "U2", "<PERSON><PERSON><PERSON><PERSON>", "H.E.R.", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "Review": ["Oscar-winning performance", "Golden Globe nominee", "Academy Award recognition", "Critically acclaimed", "Nominated for multiple awards", "Award-winning film", "Award-worthy performances", "Accolades from film festivals", "Recognition from industry peers", "Praised by critics", "Well-deserved nominations", "Outstanding achievements in cinema", "Highly decorated movie", "Honored with prestigious awards", "Garnered widespread acclaim", "Notable award nominations", "Recognized for excellence", "Standout performances", "Highly lauded by award bodies", "Deserving of accolades", "Esteemed recognition", "Celebrated by award committees", "Commendable performances", "Impressive track record in awards", "Distinguished accolades", "Respected by award voters", "Noteworthy achievements", "Glowing reviews from award voters", "Remarkable film industry recognition", "Notable achievements in awards", "Recognition from top film organizations", "Impressive award nominations", "Celebrated by the film community", "Lauded with prestigious honors", "Solid track record in awards recognition", "Oscar-winning", "Nominated for", "Award-winning", "Golden Globe winner", "Highly rewarded", "Received numerous accolades", "Swept the awards", "Proudly honored", "Garnered praise", "Highly celebrated", "Praised for its achievements", "Voted best", "Rave reviews", "Exemplary performance", "Impressive recognition", "Highly lauded", "Deserving of awards", "Honored with multiple nominations", "Stellar in its awards", "Notable achievements", "Collected accolades", "Standout in the awards season", "Recognized by industry peers", "Award magnet", "Bestowed with honors", "Outstanding in awards category", "Noteworthy acclaim", "Showered with awards", "Acclaimed in awards circuit", "Esteemed for its awards", "Recognized with prestigious accolades", "Achieved success in awards", "Highly decorated film", "Oscar nominated", "Academy Award recipient", "Nominated for Best Picture", "Highly praised", "Award-nominated", "Received multiple nominations", "Stellar performances", "Award-worthy", "Recognized for its excellence", "Garnered accolades", "Highly decorated", "Received widespread recognition", "Total domination at the awards", "Trophy magnet", "Recognized for its cinematic achievements", "Awarded for its outstanding performances", "Included in the list of award winners", "Recognized with numerous accolades", "No shortage of awards", "Successful in the awards circuit", "Deserving of all the praise", "Recognized by industry insiders", "Standing ovation-worthy", "The recipient of numerous nominations", "Commended by critics and industry professionals", "Award-winning performances", "Clearly", "Oscar-nominated", "Academy Award-winning", "Critics' Choice nominee", "Emmy-winning", "Cannes Film Festival honoree", "BAFTA-nominated", "<PERSON><PERSON> d'Or winner", "Best Picture contender", "Tony Award-winning", "SAG-nominated", "Independent Spirit Award recipient", "Sundance Film Festival favorite", "Berlin International Film Festival winner", "People's Choice Award nominee", "National Board of Review honoree", "Gotham Awards recipient", "AFI Award-winning", "Cannes Grand Prix winner", "Venice Film Festival nominee", "New York Film Critics Circle honoree", "SXSW Film Festival favorite", "Australian Academy of Cinema and Television Arts winner", "Berlinale Golden Bear winner", "Toronto International Film Festival standout", "César Award nominee", "Goya Award winner", "Hong Kong Film Awards recipient", "Satellite Award-winning", "Screen Actors Guild Award nominee", "National Society of Film Critics honoree", "European Film Award winner", "Golden Horse Film Festival contender", "British Independent Film Award nominee", "Youth in Film Awards recipient", "Outstanding performance", "Well-deserved accolades", "Unforgettable movie", "Remarkable achievement", "Deserves all the awards", "Oscar-worthy", "Decades of excellence", "Monumental win", "Unparalleled success", "Outstanding cinematography", "Top honors", "Critical acclaim", "Groundbreaking film", "A triumph", "Academy Award nominee", "Unrivaled talent", "Award-winning script", "Breakthrough performance", "Industry recognition", "Award season favorite", "Stellar cast", "Phenomenal achievement", "Unprecedented success", "Highlight of the year", "Outstanding direction", "Award-winning soundtrack", "Impressive accolades", "Winning formula", "International recognition", "Award-winning special effects", "Incomparable talent"], "Character": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON> Vader", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Princess <PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Rocky <PERSON>", "<PERSON>", "<PERSON>", "Indiana Jones", "<PERSON>", "The Terminator", "<PERSON>", "<PERSON>pock", "Captain <PERSON>", "<PERSON> the Great", "Black Panther", "<PERSON>", "Wonder Woman", "Neo", "Trinity", "Wolverine", "The Joker", "Albus Dumbledore", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Aragorn", "Legolas", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Captain <PERSON>", "Black Widow", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "King <PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Rambo", "Beetlejuice", "<PERSON><PERSON>", "The Bride", "<PERSON><PERSON><PERSON>", "Deadpool", "V", "Mrs. <PERSON>", "<PERSON><PERSON>", "Iron Man", "<PERSON>"]}}, "meta": {"dataset-name": "mit-movie", "entity-types": ["Title", "Viewers' Rating", "Year", "Genre", "Director", "MPAA Rating", "Plot", "Actor", "Trailer", "Song", "Review", "Character"], "seeded": true, "seed-attribute-name": "query-category", "seed-options": ["Movie showtimes and ticket availability", "Plot summary and synopsis", "Cast and crew information", "Movie reviews and ratings", "Streaming platform availability", "Soundtrack and music information", "Behind-the-scenes and making-of details", "Genre and subgenre classification", "Movie trailers and teasers", "Awards and nominations received"], "entity-sizes": {"Movie showtimes and ticket availability": {"Title": 91, "Viewers' Rating": 140, "Year": 110, "Genre": 68, "Director": 90, "MPAA Rating": 14, "Plot": 106, "Actor": 77, "Trailer": 25, "Song": 88, "Review": 124, "Character": 99, "__average__": 86.0, "__total__": 1032}, "Plot summary and synopsis": {"Title": 93, "Viewers' Rating": 109, "Year": 109, "Genre": 64, "Director": 91, "MPAA Rating": 13, "Plot": 79, "Actor": 85, "Trailer": 24, "Song": 69, "Review": 130, "Character": 120, "__average__": 82.2, "__total__": 986}, "Cast and crew information": {"Title": 101, "Viewers' Rating": 140, "Year": 72, "Genre": 75, "Director": 102, "MPAA Rating": 12, "Plot": 88, "Actor": 78, "Trailer": 30, "Song": 74, "Review": 159, "Character": 111, "__average__": 86.8, "__total__": 1042}, "Movie reviews and ratings": {"Title": 94, "Viewers' Rating": 113, "Year": 86, "Genre": 63, "Director": 87, "MPAA Rating": 12, "Plot": 77, "Actor": 77, "Trailer": 27, "Song": 76, "Review": 83, "Character": 106, "__average__": 75.1, "__total__": 901}, "Streaming platform availability": {"Title": 122, "Viewers' Rating": 139, "Year": 98, "Genre": 73, "Director": 87, "MPAA Rating": 14, "Plot": 69, "Actor": 88, "Trailer": 24, "Song": 87, "Review": 171, "Character": 114, "__average__": 90.5, "__total__": 1086}, "Soundtrack and music information": {"Title": 106, "Viewers' Rating": 153, "Year": 97, "Genre": 79, "Director": 99, "MPAA Rating": 13, "Plot": 99, "Actor": 98, "Trailer": 33, "Song": 80, "Review": 68, "Character": 122, "__average__": 87.2, "__total__": 1047}, "Behind-the-scenes and making-of details": {"Title": 117, "Viewers' Rating": 109, "Year": 108, "Genre": 65, "Director": 95, "MPAA Rating": 17, "Plot": 111, "Actor": 78, "Trailer": 40, "Song": 76, "Review": 120, "Character": 170, "__average__": 92.2, "__total__": 1106}, "Genre and subgenre classification": {"Title": 89, "Viewers' Rating": 129, "Year": 130, "Genre": 65, "Director": 92, "MPAA Rating": 14, "Plot": 84, "Actor": 86, "Trailer": 25, "Song": 81, "Review": 112, "Character": 141, "__average__": 87.3, "__total__": 1048}, "Movie trailers and teasers": {"Title": 97, "Viewers' Rating": 114, "Year": 80, "Genre": 72, "Director": 88, "MPAA Rating": 13, "Plot": 90, "Actor": 82, "Trailer": 24, "Song": 70, "Review": 110, "Character": 109, "__average__": 79.1, "__total__": 949}, "Awards and nominations received": {"Title": 99, "Viewers' Rating": 126, "Year": 95, "Genre": 78, "Director": 90, "MPAA Rating": 11, "Plot": 97, "Actor": 94, "Trailer": 48, "Song": 83, "Review": 160, "Character": 149, "__average__": 94.2, "__total__": 1130}}}}