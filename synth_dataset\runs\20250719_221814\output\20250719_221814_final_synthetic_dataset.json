[{"text": "浙商银行企业信贷部叶老桂博士则从另一个角度对五道门槛进行了解读。叶老桂认为，对目前国内商业银行而言，", "label": [{"entity": "叶老桂", "start_idx": 9, "end_idx": 11, "type": "姓名"}], "source": "cluener"}, {"text": "布鲁京斯研究所桑顿中国中心研究部主任李成说，东亚的和平与安全，是美国的“核心利益”之一。", "label": [{"entity": "美国", "start_idx": 32, "end_idx": 33, "type": "地理位置"}, {"entity": "研究部主任", "start_idx": 13, "end_idx": 17, "type": "职业"}], "source": "cluener"}, {"text": "万通地产设计总监刘克峰；", "label": [{"entity": "设计总监", "start_idx": 4, "end_idx": 7, "type": "职业"}], "source": "cluener"}, {"text": "彭久洋：我的魂飞了贝鲁斯科尼老古董收藏家（图）", "label": [{"entity": "收藏家", "start_idx": 17, "end_idx": 19, "type": "职业"}], "source": "cluener"}, {"text": "20雷池，本场无冷迹象。", "label": [{"entity": "雷池", "start_idx": 2, "end_idx": 3, "type": "地理位置"}], "source": "cluener"}, {"text": "她写道：抗战胜利时我从重庆坐民联轮到南京，去中山陵瞻仰，也到秦淮河去过。然后就去北京了。", "label": [{"entity": "重庆", "start_idx": 11, "end_idx": 12, "type": "地理位置"}], "source": "cluener"}, {"text": "除了资金支持之外，工行还为创业大学生提供包括存款、资金结算、电子银行、银行卡等一站式金融服务，", "label": [{"entity": "大学生", "start_idx": 15, "end_idx": 17, "type": "职业"}], "source": "cluener"}, {"text": "对此，原告代理律师指出，原告在签署认购合同时，从未看到过写明购买产品为一年期“金通道”", "label": [{"entity": "代理律师", "start_idx": 5, "end_idx": 8, "type": "职业"}], "source": "cluener"}, {"text": "当记者在泗水街头随便询问10余名市民却发现，只有寥寥几人知道当地有如此神奇的东西。", "label": [{"entity": "记者", "start_idx": 1, "end_idx": 2, "type": "职业"}], "source": "cluener"}, {"text": "仙游县委有关负责人说，目前，他们对已授牌认证的成品，特别是展厅内摆设的成品展开检查。", "label": [{"entity": "仙游县委", "start_idx": 0, "end_idx": 3, "type": "职业"}], "source": "cluener"}, {"text": "怎样做才能避免拒签？新浪出国频道邀请到美国使馆签证处的签证官江德力（charles", "label": [{"entity": "签证官", "start_idx": 27, "end_idx": 29, "type": "职业"}], "source": "cluener"}, {"text": "（5）房室结消融和起搏器植入作为反复发作或难治性心房内折返性心动过速的替代疗法。", "label": [{"entity": "房室结消融", "start_idx": 3, "end_idx": 8, "type": "医疗程序"}, {"entity": "反复发作或难治性心房内折返性心动过速", "start_idx": 16, "end_idx": 34, "type": "疾病"}], "source": "cmeee"}, {"text": "（6）发作一次伴血流动力学损害的室性心动过速（ventriculartachycardia），可接受导管消融者。", "label": [{"entity": "血流动力学损害的室性心动过速", "start_idx": 8, "end_idx": 22, "type": "疾病"}, {"entity": "ventriculartachycardia", "start_idx": 23, "end_idx": 45, "type": "疾病"}], "source": "cmeee"}, {"text": "4.第三类（1）无症状性WPW综合征患者，年龄小于5岁。", "label": [{"entity": "无症状性WPW综合征", "start_idx": 8, "end_idx": 18, "type": "疾病"}], "source": "cmeee"}, {"text": "（2）室上性心动过速可用常规抗心律失常药物控制，年龄小于5岁。", "label": [{"entity": "室上性心动过速", "start_idx": 3, "end_idx": 10, "type": "疾病"}, {"entity": "抗心律失常药物", "start_idx": 14, "end_idx": 21, "type": "药物"}], "source": "cmeee"}, {"text": "（3）非持续性，不考虑为无休止性的阵发性室性心动过速（即一次监视数小时或任何一小时记录的心电图条带几乎均可出现），心室功能正常。", "label": [{"entity": "非持续性，不考虑为无休止性的阵发性室性心动过速", "start_idx": 3, "end_idx": 26, "type": "疾病"}], "source": "cmeee"}, {"text": "（4）非持续性室上性心动过速，不需其他治疗和（或）症状轻微。", "label": [{"entity": "非持续性室上性心动过速", "start_idx": 3, "end_idx": 14, "type": "疾病"}], "source": "cmeee"}, {"text": "深呼吸及咳嗽时疼痛加剧。", "label": [{"entity": "深呼吸及咳嗽时疼痛加剧", "start_idx": 0, "end_idx": 11, "type": "临床表现"}], "source": "cmeee"}, {"text": "病程早期可闻胸膜摩擦音在全部呼吸期间均可听到。", "label": [{"entity": "闻胸膜摩擦音在全部呼吸期间均可听到", "start_idx": 5, "end_idx": 22, "type": "临床表现"}], "source": "cmeee"}, {"text": "胸部X线透视和胸片可见患侧膈呼吸运动减弱肋膈角变钝流行性胸痛和带状疱疹前驱期的胸痛及肋骨骨折相鉴别。", "label": [{"entity": "胸部X线透视和胸片可见患侧膈呼吸运动减弱", "start_idx": 0, "end_idx": 20, "type": "临床表现"}], "source": "cmeee"}, {"text": "如非肺炎病例，宜用宽大胶布条紧缠患部以减少其呼吸动作或给镇咳剂抑制咳嗽。", "label": [{"entity": "镇咳剂", "start_idx": 28, "end_idx": 31, "type": "药物"}], "source": "cmeee"}, {"text": "病程晚期脑脊液中检出高水平抗体（疫苗不能诱导）亦有诊断意义。", "label": [{"entity": "疫苗", "start_idx": 16, "end_idx": 18, "type": "药物"}], "source": "cmeee"}, {"text": "【预防和治疗】（一）控制和消灭传染源加强犬等管理，野犬应尽量捕杀，家犬应登记，注射疫苗。", "label": [{"entity": "疫苗", "start_idx": 41, "end_idx": 43, "type": "药物"}], "source": "cmeee"}, {"text": "以20%肥皂水或0.1%苯扎溴铵彻底冲洗伤口至少半小时；再用白酒或70%乙醇、碘酊涂擦几次，以清除局部的病毒。", "label": [{"entity": "20%肥皂水", "start_idx": 1, "end_idx": 7, "type": "药物"}, {"entity": "0.1%苯扎溴铵", "start_idx": 8, "end_idx": 16, "type": "药物"}, {"entity": "白酒", "start_idx": 30, "end_idx": 32, "type": "药物"}, {"entity": "70%乙醇", "start_idx": 33, "end_idx": 38, "type": "药物"}], "source": "cmeee"}, {"text": "高勇：男，中国国籍，无境外居留权，", "label": [{"entity": "中国国籍", "start_idx": 5, "end_idx": 8, "type": "国籍"}], "source": "resume"}, {"text": "1966年出生，汉族，中共党员，本科学历，工程师、美国项目管理协会注册会员（PMIMember）、注册项目管理专家（PMP）、项目经理。", "label": [{"entity": "汉族", "start_idx": 8, "end_idx": 9, "type": "民族"}, {"entity": "本科学历", "start_idx": 16, "end_idx": 19, "type": "教育背景"}], "source": "resume"}, {"text": "1965年1月出生，中国国籍，无永久境外居留权。", "label": [{"entity": "中国国籍", "start_idx": 10, "end_idx": 13, "type": "国籍"}], "source": "resume"}, {"text": "兰州商学院会计专业学士，中国社科院研究生院国际贸易专业硕士研究生。", "label": [{"entity": "学士", "start_idx": 9, "end_idx": 10, "type": "教育背景"}, {"entity": "硕士研究生", "start_idx": 27, "end_idx": 31, "type": "教育背景"}], "source": "resume"}, {"text": "贾志颖女士：1971年出生，中国国籍，无境外永久居留权，本科学历。", "label": [{"entity": "中国国籍", "start_idx": 14, "end_idx": 17, "type": "国籍"}], "source": "resume"}, {"text": "周云女士：中国国籍，无境外居留权，", "label": [{"entity": "中国国籍", "start_idx": 5, "end_idx": 8, "type": "国籍"}], "source": "resume"}, {"text": "那张表格上写着性别一栏可以选非二元或雌雄同体，真够细的。", "label": [{"entity": "非二元", "start_idx": 14, "end_idx": 16, "type": "性别"}, {"entity": "雌雄同体", "start_idx": 18, "end_idx": 21, "type": "性别"}]}, {"text": "通知：35岁以上及二十周岁以下人员，立即进行健康体检。", "label": [{"entity": "35岁以上", "start_idx": 3, "end_idx": 7, "type": "年龄"}, {"entity": "二十周岁以下", "start_idx": 9, "end_idx": 14, "type": "年龄"}]}, {"text": "张伟和李娜在公司的年度会议上进行了精彩的演讲。", "label": [{"entity": "张伟", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "李娜", "start_idx": 3, "end_idx": 5, "type": "姓名"}]}, {"text": "张伟和李娜一起去参加了王芳组织的周末郊游活动。", "label": [{"entity": "张伟", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "李娜", "start_idx": 3, "end_idx": 5, "type": "姓名"}, {"entity": "王芳", "start_idx": 9, "end_idx": 11, "type": "姓名"}]}, {"text": "李明和张伟在周末一起去参观了故宫博物院。", "label": [{"entity": "<PERSON>", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "张伟", "start_idx": 3, "end_idx": 5, "type": "姓名"}]}, {"text": "李华今天早上在公园里遇到了他的老朋友张伟，两人聊了很久。", "label": [{"entity": "李华", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "张伟", "start_idx": 11, "end_idx": 13, "type": "姓名"}]}, {"text": "李娜昨天在巴黎网球公开赛中再次展现了她的强大实力。", "label": [{"entity": "李娜", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "李娜在昨天的比赛中表现优异，赢得了观众们的热烈掌声。", "label": [{"entity": "李娜", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "李娜昨天在网球比赛中再次展现了她的卓越实力，赢得了观众们的热烈掌声。", "label": [{"entity": "李娜", "start_idx": 0, "end_idx": 2, "type": "姓名"}]}, {"text": "张伟和李娜一起去参加了王芳组织的周末郊游活动。", "label": [{"entity": "张伟", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "李娜", "start_idx": 3, "end_idx": 5, "type": "姓名"}, {"entity": "王芳", "start_idx": 9, "end_idx": 11, "type": "姓名"}]}, {"text": "张伟和李娜在周末一起去北京故宫参观，度过了一个愉快的下午。", "label": [{"entity": "张伟", "start_idx": 0, "end_idx": 2, "type": "姓名"}, {"entity": "李娜", "start_idx": 3, "end_idx": 5, "type": "姓名"}]}, {"text": "小明今年12岁，刚刚升入了初中一年级。", "label": [{"entity": "12岁", "start_idx": 3, "end_idx": 5, "type": "年龄"}]}, {"text": "今天我在公园里遇到了一个5岁的孩子，他正在学骑自行车。", "label": [{"entity": "5岁", "start_idx": 12, "end_idx": 14, "type": "年龄"}]}, {"text": "小明今年8岁，已经可以自己独立完成作业了。", "label": [{"entity": "8岁", "start_idx": 3, "end_idx": 4, "type": "年龄"}]}, {"text": "这位7岁的男孩刚刚完成了他的第一幅水彩画。", "label": [{"entity": "7岁", "start_idx": 3, "end_idx": 4, "type": "年龄"}]}, {"text": "今天我在公园里遇到了一个5岁的孩子，他正在学骑自行车。", "label": [{"entity": "5岁", "start_idx": 12, "end_idx": 14, "type": "年龄"}]}, {"text": "小明今年8岁，正在上小学二年级，喜欢和同学们一起玩游戏。", "label": [{"entity": "8岁", "start_idx": 4, "end_idx": 5, "type": "年龄"}]}, {"text": "小明的儿子今年8岁，正在上小学二年级。", "label": [{"entity": "8岁", "start_idx": 7, "end_idx": 9, "type": "年龄"}]}, {"text": "这位7岁的男孩正在幼儿园里学习如何写字。", "label": [{"entity": "7岁", "start_idx": 2, "end_idx": 3, "type": "年龄"}]}, {"text": "李明是一位男性工程师，他负责公司新项目的开发工作。", "label": [{"entity": "男性", "start_idx": 3, "end_idx": 4, "type": "性别"}]}, {"text": "李小姐今天穿着一件粉色连衣裙，看起来非常优雅。", "label": [{"entity": "李小姐", "start_idx": 0, "end_idx": 3, "type": "性别"}]}, {"text": "这位女性医生正在为男性患者进行详细的健康检查。", "label": [{"entity": "女性", "start_idx": 2, "end_idx": 4, "type": "性别"}, {"entity": "男性", "start_idx": 8, "end_idx": 10, "type": "性别"}]}, {"text": "李明是一位男性工程师，他正在为女性客户设计一款新型智能家居系统。", "label": [{"entity": "男性", "start_idx": 1, "end_idx": 3, "type": "性别"}, {"entity": "女性", "start_idx": 7, "end_idx": 9, "type": "性别"}]}, {"text": "这位女性医生正在为男患者进行详细的身体检查。", "label": [{"entity": "女性", "start_idx": 2, "end_idx": 4, "type": "性别"}, {"entity": "男", "start_idx": 7, "end_idx": 8, "type": "性别"}]}, {"text": "这位男性医生正在为一位女性患者进行详细的健康检查。", "label": [{"entity": "男性", "start_idx": 2, "end_idx": 4, "type": "性别"}, {"entity": "女性", "start_idx": 11, "end_idx": 13, "type": "性别"}]}, {"text": "这位**男性**医生正在为**女性**患者进行详细的身体检查。", "label": [{"entity": "男性", "start_idx": 1, "end_idx": 3, "type": "性别"}, {"entity": "女性", "start_idx": 10, "end_idx": 12, "type": "性别"}]}, {"text": "李明是一位男性医生，今天他接诊了一位女性患者。", "label": [{"entity": "男性", "start_idx": 3, "end_idx": 4, "type": "性别"}, {"entity": "女性", "start_idx": 18, "end_idx": 19, "type": "性别"}]}, {"text": "美国游客在巴黎埃菲尔铁塔前兴奋地拍照留念。", "label": [{"entity": "美国", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "这位加拿大籍的工程师正在为国际项目提供技术支持。", "label": [{"entity": "加拿大", "start_idx": 3, "end_idx": 5, "type": "国籍"}]}, {"text": "这位美国游客在巴黎的埃菲尔铁塔前兴奋地拍照留念。", "label": [{"entity": "美国", "start_idx": 3, "end_idx": 4, "type": "国籍"}]}, {"text": "美国游客在法国巴黎的埃菲尔铁塔前留下了深刻的印象。", "label": [{"entity": "美国", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "日本游客在巴黎埃菲尔铁塔前兴奋地合影留念。", "label": [{"entity": "日本", "start_idx": 0, "end_idx": 2, "type": "国籍"}]}, {"text": "李明是一名中国籍的工程师，他在德国工作多年，深受当地同事的尊重。", "label": [{"entity": "中国", "start_idx": 4, "end_idx": 6, "type": "国籍"}, {"entity": "德国", "start_idx": 10, "end_idx": 12, "type": "国籍"}]}, {"text": "这位牙医每周三下午都会在社区诊所为居民提供免费检查。", "label": [{"entity": "牙医", "start_idx": 1, "end_idx": 2, "type": "职业"}]}, {"text": "这位牙医为患者提供了详细的口腔健康检查和建议。", "label": [{"entity": "牙医", "start_idx": 1, "end_idx": 3, "type": "职业"}]}, {"text": "在云南的少数民族村落里，傣族和哈尼族的人们正在庆祝传统的泼水节。", "label": [{"entity": "傣族", "start_idx": 12, "end_idx": 14, "type": "民族"}, {"entity": "哈尼族", "start_idx": 16, "end_idx": 19, "type": "民族"}]}, {"text": "在云南的节日庆典上，傣族和哈尼族的舞蹈表演吸引了众多游客。", "label": [{"entity": "傣族", "start_idx": 10, "end_idx": 12, "type": "民族"}, {"entity": "哈尼族", "start_idx": 13, "end_idx": 16, "type": "民族"}]}, {"text": "藏族和维吾尔族在传统节日里会举办丰富多彩的文化活动。", "label": [{"entity": "藏族", "start_idx": 0, "end_idx": 2, "type": "民族"}, {"entity": "维吾尔族", "start_idx": 3, "end_idx": 6, "type": "民族"}]}, {"text": "在云南的节日庆典上，白族和傣族的传统文化表演吸引了众多游客。", "label": [{"entity": "白族", "start_idx": 8, "end_idx": 10, "type": "民族"}, {"entity": "傣族", "start_idx": 11, "end_idx": 13, "type": "民族"}]}, {"text": "在云南的傣族村寨里，村民们穿着传统的服饰庆祝泼水节。", "label": [{"entity": "傣族", "start_idx": 6, "end_idx": 8, "type": "民族"}]}, {"text": "在云南的节日庆典上，傣族和彝族的人们穿着传统服饰载歌载舞。", "label": [{"entity": "傣族", "start_idx": 9, "end_idx": 11, "type": "民族"}, {"entity": "彝族", "start_idx": 12, "end_idx": 14, "type": "民族"}]}, {"text": "在云南的傣族村寨里，村民们正在庆祝传统的泼水节。", "label": [{"entity": "傣族", "start_idx": 5, "end_idx": 7, "type": "民族"}]}, {"text": "朝鲜族的传统舞蹈在延边地区有着悠久的历史和独特的艺术魅力。", "label": [{"entity": "朝鲜族", "start_idx": 0, "end_idx": 2, "type": "民族"}]}, {"text": "在云南的傣族村寨里，人们身着传统服饰，欢快地庆祝泼水节。", "label": [{"entity": "傣族", "start_idx": 6, "end_idx": 8, "type": "民族"}]}, {"text": "张伟拥有北京大学计算机科学与技术专业的学士学位，目前在清华大学攻读硕士学位。", "label": [{"entity": "北京大学计算机科学与技术专业的学士学位", "start_idx": 4, "end_idx": 18, "type": "教育背景"}, {"entity": "清华大学", "start_idx": 24, "end_idx": 27, "type": "教育背景"}, {"entity": "硕士学位", "start_idx": 28, "end_idx": 30, "type": "教育背景"}]}, {"text": "张华拥有北京大学计算机科学与技术专业的硕士学位。", "label": [{"entity": "北京大学计算机科学与技术专业的硕士学位", "start_idx": 4, "end_idx": 17, "type": "教育背景"}]}, {"text": "李明拥有北京大学计算机科学与技术专业的学士学位，现正在清华大学攻读硕士学位。", "label": [{"entity": "北京大学计算机科学与技术专业的学士学位", "start_idx": 3, "end_idx": 16, "type": "教育背景"}, {"entity": "清华大学", "start_idx": 20, "end_idx": 23, "type": "教育背景"}]}, {"text": "张华拥有北京大学计算机科学与技术专业的博士学位。", "label": [{"entity": "北京大学计算机科学与技术专业的博士学位", "start_idx": 4, "end_idx": 20, "type": "教育背景"}]}, {"text": "李华拥有北京大学计算机科学与技术专业的学士学位。", "label": [{"entity": "北京大学计算机科学与技术专业的学士学位", "start_idx": 2, "end_idx": 14, "type": "教育背景"}]}, {"text": "李明拥有北京大学计算机科学与技术专业的学士学位。", "label": [{"entity": "北京大学计算机科学与技术专业", "start_idx": 3, "end_idx": 13, "type": "教育背景"}, {"entity": "学士学位", "start_idx": 14, "end_idx": 16, "type": "教育背景"}]}, {"text": "张伟毕业于北京大学计算机科学与技术专业，获得了硕士学位。", "label": [{"entity": "北京大学", "start_idx": 4, "end_idx": 7, "type": "教育背景"}, {"entity": "计算机科学与技术专业", "start_idx": 8, "end_idx": 14, "type": "教育背景"}, {"entity": "硕士学位", "start_idx": 20, "end_idx": 23, "type": "教育背景"}]}, {"text": "李明的婚姻状况是已婚，他和妻子已经共同生活了五年。", "label": [{"entity": "已婚", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "张先生的婚姻状况是已婚，他和妻子已经共同生活了十五年。", "label": [{"entity": "已婚", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "张先生在个人资料中明确标注了自己的婚姻状况为“已婚”。", "label": [{"entity": "已婚", "start_idx": 22, "end_idx": 24, "type": "婚姻状况"}]}, {"text": "王女士的婚姻状况是离异，她现在独自抚养两个孩子。", "label": [{"entity": "离异", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "王女士的婚姻状况是已婚，她与丈夫已经共同生活了十年。", "label": [{"entity": "已婚", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "张先生的婚姻状况是已婚，他和妻子已经共同生活了十年。", "label": [{"entity": "已婚", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "张先生在个人资料中填写了自己的婚姻状况为已婚，并注明有一名配偶。", "label": [{"entity": "已婚", "start_idx": 15, "end_idx": 17, "type": "婚姻状况"}]}, {"text": "王女士的婚姻状况是离异，她独自抚养着两个孩子。", "label": [{"entity": "离异", "start_idx": 6, "end_idx": 8, "type": "婚姻状况"}]}, {"text": "王先生的婚姻状况是离异，他独自抚养着两个年幼的孩子。", "label": [{"entity": "离异", "start_idx": 5, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "张伟的婚姻状况是已婚，他和妻子已经共同生活了十年。", "label": [{"entity": "已婚", "start_idx": 6, "end_idx": 7, "type": "婚姻状况"}]}, {"text": "作为民主党支持者，他在社区活动中积极推广环境保护政策。", "label": [{"entity": "民主党", "start_idx": 5, "end_idx": 7, "type": "政治倾向"}]}, {"text": "这位自由主义者在选举中支持民主党，并倡导社会平等。", "label": [{"entity": "自由主义者", "start_idx": 1, "end_idx": 5, "type": "政治倾向"}, {"entity": "民主党", "start_idx": 13, "end_idx": 16, "type": "政治倾向"}]}, {"text": "这位民主党支持者认为，共和党的税收政策对中产阶级不公平。", "label": [{"entity": "民主党", "start_idx": 5, "end_idx": 7, "type": "政治倾向"}, {"entity": "共和党", "start_idx": 11, "end_idx": 13, "type": "政治倾向"}]}, {"text": "这位选民长期支持民主社会主义政党，并在每次选举中都投给他们。", "label": [{"entity": "民主社会主义政党", "start_idx": 8, "end_idx": 17, "type": "政治倾向"}]}, {"text": "这位自由主义者认为政府应该减少对市场的干预。", "label": [{"entity": "自由主义者", "start_idx": 1, "end_idx": 5, "type": "政治倾向"}]}, {"text": "这位学者在论文中明确表达了对自由主义思想的坚定支持。", "label": [{"entity": "自由主义", "start_idx": 16, "end_idx": 19, "type": "政治倾向"}]}, {"text": "作为民主党支持者，他始终关注社会公平与经济平等议题。", "label": [{"entity": "民主党", "start_idx": 6, "end_idx": 8, "type": "政治倾向"}]}, {"text": "自由主义者在议会中提出了关于环境保护的激进法案。", "label": [{"entity": "自由主义者", "start_idx": 0, "end_idx": 4, "type": "政治倾向"}]}, {"text": "自由主义者在议会中提出了关于扩大公民权利的议案。", "label": [{"entity": "自由主义者", "start_idx": 0, "end_idx": 5, "type": "政治倾向"}]}, {"text": "作为自由主义者，他坚决反对任何形式的政府干预市场经济。", "label": [{"entity": "自由主义者", "start_idx": 5, "end_idx": 10, "type": "政治倾向"}]}, {"text": "我妈妈张丽华昨天带爸爸李建国去看了奶奶王秀英。", "label": [{"entity": "张丽华", "start_idx": 2, "end_idx": 5, "type": "家庭成员"}, {"entity": "李建国", "start_idx": 8, "end_idx": 11, "type": "家庭成员"}, {"entity": "王秀英", "start_idx": 15, "end_idx": 18, "type": "家庭成员"}]}, {"text": "我妈妈张丽明天要带我和我弟弟李明一起去外婆家吃晚饭。", "label": [{"entity": "张丽", "start_idx": 2, "end_idx": 4, "type": "家庭成员"}, {"entity": "我", "start_idx": 5, "end_idx": 6, "type": "家庭成员"}, {"entity": "<PERSON>", "start_idx": 8, "end_idx": 10, "type": "家庭成员"}]}, {"text": "我爸爸张明今天去接我妹妹李婷放学了。", "label": [{"entity": "张明", "start_idx": 2, "end_idx": 4, "type": "家庭成员"}, {"entity": "李婷", "start_idx": 8, "end_idx": 10, "type": "家庭成员"}]}, {"text": "李华的弟弟张伟今天去参加了妈妈的生日派对。", "label": [{"entity": "李华", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "张伟", "start_idx": 3, "end_idx": 5, "type": "家庭成员"}, {"entity": "妈妈", "start_idx": 10, "end_idx": 12, "type": "家庭成员"}]}, {"text": "妈妈今天带我和妹妹去外婆家吃晚饭。", "label": [{"entity": "妈妈", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "我", "start_idx": 3, "end_idx": 4, "type": "家庭成员"}, {"entity": "妹妹", "start_idx": 5, "end_idx": 7, "type": "家庭成员"}, {"entity": "外婆", "start_idx": 9, "end_idx": 11, "type": "家庭成员"}]}, {"text": "妈妈昨天给爸爸和小明买了一盒他们喜欢的巧克力。", "label": [{"entity": "妈妈", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "爸爸", "start_idx": 5, "end_idx": 7, "type": "家庭成员"}, {"entity": "小明", "start_idx": 8, "end_idx": 10, "type": "家庭成员"}]}, {"text": "李明的儿子张伟今天要去参加他父亲的婚礼。", "label": [{"entity": "<PERSON>", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "张伟", "start_idx": 3, "end_idx": 5, "type": "家庭成员"}]}, {"text": "妈妈今天带弟弟去公园玩，爸爸在家照顾奶奶。", "label": [{"entity": "妈妈", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "弟弟", "start_idx": 5, "end_idx": 7, "type": "家庭成员"}, {"entity": "爸爸", "start_idx": 9, "end_idx": 11, "type": "家庭成员"}, {"entity": "奶奶", "start_idx": 16, "end_idx": 18, "type": "家庭成员"}]}, {"text": "李明和他妻子王芳决定带他们的儿子张浩一起去公园散步。", "label": [{"entity": "<PERSON>", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "王芳", "start_idx": 5, "end_idx": 7, "type": "家庭成员"}, {"entity": "张浩", "start_idx": 12, "end_idx": 14, "type": "家庭成员"}]}, {"text": "李明和妻子王芳带着女儿小红一起去爷爷奶奶家吃晚饭。", "label": [{"entity": "<PERSON>", "start_idx": 0, "end_idx": 2, "type": "家庭成员"}, {"entity": "王芳", "start_idx": 4, "end_idx": 6, "type": "家庭成员"}, {"entity": "小红", "start_idx": 8, "end_idx": 10, "type": "家庭成员"}, {"entity": "爷爷奶奶", "start_idx": 14, "end_idx": 16, "type": "家庭成员"}]}, {"text": "这家公司的月薪是8500元，比同行业的平均水平要高一些。", "label": [{"entity": "8500元", "start_idx": 6, "end_idx": 9, "type": "工资数额"}]}, {"text": "这家公司的月薪标准是8000元，比同行业平均水平要高一些。", "label": [{"entity": "8000元", "start_idx": 8, "end_idx": 10, "type": "工资数额"}]}, {"text": "这家公司的月工资标准是8500元，比同行业平均水平高出不少。", "label": [{"entity": "8500元", "start_idx": 8, "end_idx": 11, "type": "工资数额"}]}, {"text": "这家公司的月工资是8500元，比上家高了不少。", "label": [{"entity": "8500元", "start_idx": 7, "end_idx": 10, "type": "工资数额"}]}, {"text": "这个月的工资数额是8500元，比上个月多了500元。", "label": [{"entity": "8500元", "start_idx": 7, "end_idx": 10, "type": "工资数额"}, {"entity": "500元", "start_idx": 15, "end_idx": 18, "type": "工资数额"}]}, {"text": "小王的月薪是8000元，比上个月多了2000元，他感到非常满意。", "label": [{"entity": "8000元", "start_idx": 6, "end_idx": 8, "type": "工资数额"}, {"entity": "2000元", "start_idx": 13, "end_idx": 15, "type": "工资数额"}]}, {"text": "小明上个月税后工资是12500元，比预期高了不少。", "label": [{"entity": "12500元", "start_idx": 8, "end_idx": 11, "type": "工资数额"}]}, {"text": "小李的月薪是8500元，比上个月多了500元。", "label": [{"entity": "8500元", "start_idx": 6, "end_idx": 9, "type": "工资数额"}, {"entity": "500元", "start_idx": 12, "end_idx": 15, "type": "工资数额"}]}, {"text": "张伟上个月的工资是8,500元，比预想的要高一些。", "label": [{"entity": "8,500元", "start_idx": 7, "end_idx": 11, "type": "工资数额"}]}, {"text": "这家公司的月薪标准是8500元，比同行业平均水平高出不少。", "label": [{"entity": "8500元", "start_idx": 10, "end_idx": 13, "type": "工资数额"}]}, {"text": "\"我决定将部分资金投资于华夏成长混合型证券投资基金。\"", "label": [{"entity": "华夏成长混合型证券投资基金", "start_idx": 13, "end_idx": 30, "type": "投资产品"}]}, {"text": "我决定将部分资金投入招商银行(600036)的股票，以期获得长期稳定的回报。", "label": [{"entity": "招商银行(600036)", "start_idx": 9, "end_idx": 19, "type": "投资产品"}]}, {"text": "\"我决定将部分资金投资于华夏沪深300ETF，以期获得长期稳定的收益。\"", "label": [{"entity": "华夏沪深300ETF", "start_idx": 9, "end_idx": 17, "type": "投资产品"}]}, {"text": "客户购买了100万元的招商银行招商安泰平衡混合型证券投资基金。", "label": [{"entity": "招商安泰平衡混合型证券投资基金", "start_idx": 11, "end_idx": 36, "type": "投资产品"}]}, {"text": "我决定将一部分资金投入到华夏成长混合型证券投资基金中。", "label": [{"entity": "华夏成长混合型证券投资基金", "start_idx": 11, "end_idx": 21, "type": "投资产品"}]}, {"text": "我决定将部分资金投资于华夏上证50ETF，以期获得长期稳定的收益。", "label": [{"entity": "华夏上证50ETF", "start_idx": 11, "end_idx": 18, "type": "投资产品"}]}, {"text": "\"我最近购买了一份华夏回报二号混合型基金，收益表现还不错。\"", "label": [{"entity": "华夏回报二号混合型基金", "start_idx": 7, "end_idx": 19, "type": "投资产品"}]}, {"text": "\"李先生决定将部分资金投资于华夏回报混合型证券投资基金。\"", "label": [{"entity": "华夏回报混合型证券投资基金", "start_idx": 10, "end_idx": 19, "type": "投资产品"}]}, {"text": "客户将一部分资金配置到了招商银行摩羯智投的股票型基金产品中。", "label": [{"entity": "股票型基金产品", "start_idx": 16, "end_idx": 20, "type": "投资产品"}]}, {"text": "我最近购买了一份招商银行稳健收益理财产品，收益很稳定。", "label": [{"entity": "招商银行稳健收益理财产品", "start_idx": 6, "end_idx": 18, "type": "投资产品"}]}, {"text": "公司需要提交2023年度企业所得税年度汇算清缴申报表。", "label": [{"entity": "企业所得税年度汇算清缴申报表", "start_idx": 12, "end_idx": 38, "type": "税务记录"}]}, {"text": "请提供具体的税务记录类型实体，例如“增值税专用发票”或“个人所得税申报表”，我将为您生成符合要求的句子。", "label": [{"entity": "增值税专用发票", "start_idx": 31, "end_idx": 39, "type": "税务记录"}, {"entity": "个人所得税申报表", "start_idx": 41, "end_idx": 52, "type": "税务记录"}]}, {"text": "个人所得税纳税记录显示，张先生2022年度的应纳税所得额为85000元。", "label": [{"entity": "个人所得税纳税记录", "start_idx": 0, "end_idx": 7, "type": "税务记录"}]}, {"text": "请提供具体的税务记录类型实体，例如“增值税专用发票”、“个人所得税申报表”等，我将为您生成包含该实体的自然中文句子。", "label": [{"entity": "增值税专用发票", "start_idx": 27, "end_idx": 36, "type": "税务记录"}, {"entity": "个人所得税申报表", "start_idx": 39, "end_idx": 51, "type": "税务记录"}]}, {"text": "请提供您的2019年度个人所得税纳税申报表，以便我们核对相关信息。", "label": [{"entity": "2019年度个人所得税纳税申报表", "start_idx": 7, "end_idx": 25, "type": "税务记录"}]}, {"text": "我在2023年个人所得税年度汇算清缴中提交了工资薪金所得证明。", "label": [{"entity": "个人所得税年度汇算清缴", "start_idx": 3, "end_idx": 13, "type": "税务记录"}, {"entity": "工资薪金所得证明", "start_idx": 24, "end_idx": 32, "type": "税务记录"}]}, {"text": "\"请提供您的个人所得税年度申报表2022年的详细记录。\"", "label": [{"entity": "个人所得税年度申报表2022年", "start_idx": 7, "end_idx": 24, "type": "税务记录"}]}, {"text": "公司提交了2023年度企业所得税纳税申报表，并附上了增值税专用发票。", "label": [{"entity": "2023年度企业所得税纳税申报表", "start_idx": 6, "end_idx": 20, "type": "税务记录"}, {"entity": "增值税专用发票", "start_idx": 33, "end_idx": 39, "type": "税务记录"}]}, {"text": "请提供具体的税务记录类型实体，我将为您生成包含该实体的自然中文句子。例如，如果您提供“增值税专用发票”，我可以生成：“他在2019年12月31日收到了一张增值税专用发票。”", "label": [{"entity": "增值税专用发票", "start_idx": 50, "end_idx": 59, "type": "税务记录"}]}, {"text": "公司提交了2023年度企业所得税汇算清缴纳税申报表。", "label": [{"entity": "企业所得税汇算清缴纳税申报表", "start_idx": 7, "end_idx": 22, "type": "税务记录"}]}, {"text": "我在中国人民银行征信中心查询了自己的个人信用报告，发现信用评分达到了760分。", "label": [{"entity": "个人信用报告", "start_idx": 13, "end_idx": 18, "type": "信用记录"}, {"entity": "信用评分", "start_idx": 22, "end_idx": 26, "type": "信用记录"}]}, {"text": "张先生查看了自己的个人征信报告，发现其中包含中国人民银行征信中心的信用记录。", "label": [{"entity": "信用记录", "start_idx": 16, "end_idx": 18, "type": "信用记录"}]}, {"text": "小明在查询个人信用报告时，发现他的央行征信报告显示为“良好”。", "label": [{"entity": "个人信用报告", "start_idx": 6, "end_idx": 10, "type": "信用记录"}, {"entity": "央行征信报告", "start_idx": 18, "end_idx": 22, "type": "信用记录"}]}, {"text": "张先生查询了自己的中国人民银行征信报告，发现信用记录良好。", "label": [{"entity": "信用记录", "start_idx": 10, "end_idx": 12, "type": "信用记录"}]}, {"text": "小明在查询个人信用报告时，发现他的中国人民银行征信中心信用评分达到了750分。", "label": [{"entity": "750分", "start_idx": 38, "end_idx": 40, "type": "信用记录"}]}, {"text": "小明查看了自己的中国人民银行征信报告，发现个人信用评分达到了750分。", "label": [{"entity": "中国人民银行征信报告", "start_idx": 4, "end_idx": 11, "type": "信用记录"}]}, {"text": "王先生的个人信用报告显示，他的信用卡逾期记录为356分，贷款还款记录为优。", "label": [{"entity": "356分", "start_idx": 10, "end_idx": 12, "type": "信用记录"}, {"entity": "优", "start_idx": 18, "end_idx": 19, "type": "信用记录"}]}, {"text": "他的个人信用报告显示，近两年的还款记录为“按时还款”，无逾期情况。", "label": [{"entity": "按时还款", "start_idx": 11, "end_idx": 14, "type": "信用记录"}]}, {"text": "\"根据中国人民银行征信中心报告，他的个人信用记录显示为'良好'。\"", "label": [{"entity": "良好", "start_idx": 19, "end_idx": 21, "type": "信用记录"}]}, {"text": "小李的芝麻信用分达到了750分，这在申请贷款时非常有帮助。", "label": [{"entity": "750分", "start_idx": 7, "end_idx": 9, "type": "信用记录"}]}, {"text": "这家公司的主要资产包括三栋位于上海浦东的写字楼和一辆奔驰S级轿车。", "label": [{"entity": "三栋位于上海浦东的写字楼", "start_idx": 10, "end_idx": 26, "type": "实体资产"}, {"entity": "一辆奔驰S级轿车", "start_idx": 29, "end_idx": 41, "type": "实体资产"}]}, {"text": "这家公司的主要资产包括两栋位于北京国贸的写字楼和一架波音737飞机。", "label": [{"entity": "两栋位于北京国贸的写字楼", "start_idx": 11, "end_idx": 24, "type": "实体资产"}, {"entity": "一架波音737飞机", "start_idx": 26, "end_idx": 35, "type": "实体资产"}]}, {"text": "这家公司的主要资产包括三栋位于北京市朝阳区的高层写字楼。", "label": [{"entity": "三栋位于北京市朝阳区的高层写字楼", "start_idx": 10, "end_idx": 30, "type": "实体资产"}]}, {"text": "这家公司拥有两栋位于上海市浦东新区的办公楼和一辆奔驰S级轿车作为实体资产。", "label": [{"entity": "办公楼", "start_idx": 11, "end_idx": 13, "type": "实体资产"}, {"entity": "轿车", "start_idx": 23, "end_idx": 25, "type": "实体资产"}]}, {"text": "这家公司的主要实体资产包括位于北京市朝阳区的一栋写字楼和一辆奔驰S级轿车。", "label": [{"entity": "写字楼", "start_idx": 19, "end_idx": 21, "type": "实体资产"}, {"entity": "奔驰S级轿车", "start_idx": 26, "end_idx": 30, "type": "实体资产"}]}, {"text": "这家公司的主要资产包括两栋位于北京市朝阳区的商业写字楼和一辆奔驰S级轿车。", "label": [{"entity": "两栋位于北京市朝阳区的商业写字楼", "start_idx": 11, "end_idx": 38, "type": "实体资产"}, {"entity": "一辆奔驰S级轿车", "start_idx": 39, "end_idx": 55, "type": "实体资产"}]}, {"text": "这家公司拥有多栋位于上海市浦东新区的甲级写字楼和一批奔驰S级轿车作为资产。", "label": [{"entity": "甲级写字楼", "start_idx": 9, "end_idx": 12, "type": "实体资产"}, {"entity": "奔驰S级轿车", "start_idx": 22, "end_idx": 27, "type": "实体资产"}]}, {"text": "这家公司的主要资产包括三栋位于上海市浦东新区的办公楼和一辆奔驰S级轿车。", "label": [{"entity": "三栋位于上海市浦东新区的办公楼", "start_idx": 11, "end_idx": 29, "type": "实体资产"}, {"entity": "一辆奔驰S级轿车", "start_idx": 31, "end_idx": 41, "type": "实体资产"}]}, {"text": "这家公司的主要资产包括位于北京市朝阳区的一栋写字楼和一辆奔驰S级轿车。", "label": [{"entity": "写字楼", "start_idx": 21, "end_idx": 23, "type": "实体资产"}, {"entity": "奔驰S级轿车", "start_idx": 28, "end_idx": 32, "type": "实体资产"}]}, {"text": "这家公司拥有两栋位于上海市浦东新区的办公楼和一辆奔驰S级轿车。", "label": [{"entity": "办公楼", "start_idx": 7, "end_idx": 9, "type": "实体资产"}, {"entity": "奔驰S级轿车", "start_idx": 20, "end_idx": 25, "type": "实体资产"}]}, {"text": "张先生通过招商银行向李女士转账了1000元，交易时间为2023年10月15日。", "label": [{"entity": "招商银行", "start_idx": 6, "end_idx": 10, "type": "交易信息"}, {"entity": "李女士", "start_idx": 11, "end_idx": 13, "type": "交易信息"}, {"entity": "1000元", "start_idx": 18, "end_idx": 21, "type": "交易信息"}, {"entity": "2023年10月15日", "start_idx": 24, "end_idx": 32, "type": "交易信息"}]}, {"text": "张先生通过支付宝完成了10086元的转账交易，交易时间为2023年12月15日。", "label": [{"entity": "支付宝", "start_idx": 5, "end_idx": 7, "type": "交易信息"}, {"entity": "10086元", "start_idx": 8, "end_idx": 11, "type": "交易信息"}, {"entity": "2023年12月15日", "start_idx": 14, "end_idx": 20, "type": "交易信息"}]}, {"text": "张先生通过招商银行APP完成了一笔金额为3999元的电子产品购买交易。", "label": [{"entity": "招商银行APP", "start_idx": 6, "end_idx": 12, "type": "交易信息"}, {"entity": "3999元", "start_idx": 17, "end_idx": 20, "type": "交易信息"}, {"entity": "电子产品", "start_idx": 22, "end_idx": 25, "type": "交易信息"}]}, {"text": "张先生通过工商银行转账支付了人民币3,500元用于购买商品编号A10025的设备。", "label": [{"entity": "工商银行", "start_idx": 5, "end_idx": 8, "type": "交易信息"}, {"entity": "人民币3,500元", "start_idx": 10, "end_idx": 15, "type": "交易信息"}, {"entity": "商品编号A10025", "start_idx": 26, "end_idx": 32, "type": "交易信息"}]}, {"text": "用户通过工商银行的借记卡6222 0168 0000 1234完成了10000元的转账交易。", "label": [{"entity": "工商银行的借记卡6222 0168 0000 1234", "start_idx": 6, "end_idx": 29, "type": "交易信息"}, {"entity": "10000元", "start_idx": 35, "end_idx": 39, "type": "交易信息"}]}, {"text": "张先生在2023年10月15日通过招商银行APP完成了金额为5280元的信用卡还款。", "label": [{"entity": "2023年10月15日", "start_idx": 5, "end_idx": 14, "type": "交易信息"}, {"entity": "招商银行APP", "start_idx": 15, "end_idx": 23, "type": "交易信息"}, {"entity": "5280元", "start_idx": 32, "end_idx": 35, "type": "交易信息"}]}, {"text": "张先生于2023年10月15日通过招商银行转账支付了金额为¥5,280的账单。", "label": [{"entity": "2023年10月15日", "start_idx": 6, "end_idx": 15, "type": "交易信息"}, {"entity": "招商银行", "start_idx": 16, "end_idx": 20, "type": "交易信息"}, {"entity": "¥5,280", "start_idx": 28, "end_idx": 32, "type": "交易信息"}]}, {"text": "张先生在2023年10月15日通过支付宝向李女士转账了500元用于购买书籍。", "label": [{"entity": "2023年10月15日", "start_idx": 4, "end_idx": 13, "type": "交易信息"}, {"entity": "支付宝", "start_idx": 16, "end_idx": 19, "type": "交易信息"}, {"entity": "500元", "start_idx": 24, "end_idx": 27, "type": "交易信息"}]}, {"text": "客户通过招商银行向支付宝账户123456789转账1000元用于支付订单。", "label": [{"entity": "招商银行", "start_idx": 4, "end_idx": 7, "type": "交易信息"}, {"entity": "支付宝账户123456789", "start_idx": 8, "end_idx": 18, "type": "交易信息"}, {"entity": "1000元", "start_idx": 21, "end_idx": 24, "type": "交易信息"}]}, {"text": "张先生于2023年10月15日在北京朝阳区的星巴克用信用卡尾号5678支付了128元。", "label": [{"entity": "2023年10月15日", "start_idx": 4, "end_idx": 13, "type": "交易信息"}, {"entity": "北京朝阳区", "start_idx": 15, "end_idx": 21, "type": "交易信息"}, {"entity": "星巴克", "start_idx": 22, "end_idx": 25, "type": "交易信息"}, {"entity": "信用卡尾号5678", "start_idx": 28, "end_idx": 37, "type": "交易信息"}, {"entity": "128元", "start_idx": 41, "end_idx": 44, "type": "交易信息"}]}, {"text": "这位患者被诊断为糖尿病，需要长期控制血糖水平。", "label": [{"entity": "糖尿病", "start_idx": 8, "end_idx": 10, "type": "疾病"}]}, {"text": "这位患者被诊断为糖尿病，需要长期监测血糖水平。", "label": [{"entity": "糖尿病", "start_idx": 7, "end_idx": 9, "type": "疾病"}]}, {"text": "这位患者被诊断患有糖尿病，需要定期监测血糖水平。", "label": [{"entity": "糖尿病", "start_idx": 9, "end_idx": 11, "type": "疾病"}]}, {"text": "医生建议糖尿病患者每天服用二甲双胍来控制血糖水平。", "label": [{"entity": "二甲双胍", "start_idx": 16, "end_idx": 18, "type": "药物"}]}, {"text": "医生建议他服用阿莫西林胶囊来治疗他的呼吸道感染。", "label": [{"entity": "阿莫西林胶囊", "start_idx": 9, "end_idx": 14, "type": "药物"}]}, {"text": "患者出现了明显的胸痛、呼吸困难以及持续性的咳嗽症状。", "label": [{"entity": "胸痛", "start_idx": 4, "end_idx": 6, "type": "临床表现"}, {"entity": "呼吸困难", "start_idx": 7, "end_idx": 10, "type": "临床表现"}, {"entity": "持续性的咳嗽", "start_idx": 11, "end_idx": 17, "type": "临床表现"}]}, {"text": "这位患者出现了持续高热、剧烈头痛和喷射性呕吐的症状。", "label": [{"entity": "持续高热", "start_idx": 6, "end_idx": 9, "type": "临床表现"}, {"entity": "剧烈头痛", "start_idx": 10, "end_idx": 13, "type": "临床表现"}, {"entity": "喷射性呕吐", "start_idx": 14, "end_idx": 18, "type": "临床表现"}]}, {"text": "患者出现了明显的呼吸困难、胸痛和持续性咳嗽等症状。", "label": [{"entity": "呼吸困难", "start_idx": 4, "end_idx": 7, "type": "临床表现"}, {"entity": "胸痛", "start_idx": 8, "end_idx": 10, "type": "临床表现"}, {"entity": "持续性咳嗽", "start_idx": 11, "end_idx": 15, "type": "临床表现"}]}, {"text": "这位患者出现了明显的呼吸困难、胸痛和持续低烧的临床表现。", "label": [{"entity": "呼吸困难", "start_idx": 7, "end_idx": 10, "type": "临床表现"}, {"entity": "胸痛", "start_idx": 11, "end_idx": 13, "type": "临床表现"}, {"entity": "持续低烧", "start_idx": 14, "end_idx": 18, "type": "临床表现"}]}, {"text": "这位患者出现了明显的胸痛、呼吸困难以及持续低热等症状。", "label": [{"entity": "胸痛", "start_idx": 6, "end_idx": 8, "type": "临床表现"}, {"entity": "呼吸困难", "start_idx": 9, "end_idx": 12, "type": "临床表现"}, {"entity": "持续低热", "start_idx": 13, "end_idx": 16, "type": "临床表现"}]}, {"text": "这位患者出现了明显的皮疹、发热和关节疼痛等症状。", "label": [{"entity": "皮疹", "start_idx": 6, "end_idx": 8, "type": "临床表现"}, {"entity": "发热", "start_idx": 9, "end_idx": 11, "type": "临床表现"}, {"entity": "关节疼痛", "start_idx": 12, "end_idx": 16, "type": "临床表现"}]}, {"text": "患者出现持续性的胸痛、呼吸困难以及咯血症状，需要立即进行进一步检查。", "label": [{"entity": "胸痛", "start_idx": 4, "end_idx": 6, "type": "临床表现"}, {"entity": "呼吸困难", "start_idx": 7, "end_idx": 10, "type": "临床表现"}, {"entity": "咯血", "start_idx": 11, "end_idx": 13, "type": "临床表现"}]}, {"text": "医生建议他进行冠状动脉搭桥手术来改善心脏供血问题。", "label": [{"entity": "冠状动脉搭桥手术", "start_idx": 7, "end_idx": 15, "type": "医疗程序"}]}, {"text": "医生建议他进行胃镜检查，以明确上腹疼痛的原因。", "label": [{"entity": "胃镜检查", "start_idx": 6, "end_idx": 9, "type": "医疗程序"}]}, {"text": "医生建议他进行冠状动脉造影检查，以明确诊断。", "label": [{"entity": "冠状动脉造影检查", "start_idx": 8, "end_idx": 17, "type": "医疗程序"}]}, {"text": "医生建议他进行冠状动脉造影检查，以明确诊断。", "label": [{"entity": "冠状动脉造影检查", "start_idx": 8, "end_idx": 16, "type": "医疗程序"}]}, {"text": "医生建议他进行胃镜检查，以明确上腹部不适的原因。", "label": [{"entity": "胃镜检查", "start_idx": 6, "end_idx": 9, "type": "医疗程序"}]}, {"text": "医生建议患者进行心脏导管插入术以进一步检查血管状况。", "label": [{"entity": "心脏导管插入术", "start_idx": 7, "end_idx": 14, "type": "医疗程序"}]}, {"text": "医生建议他进行胃镜检查以明确胃部不适的原因。", "label": [{"entity": "胃镜检查", "start_idx": 6, "end_idx": 9, "type": "医疗程序"}]}, {"text": "医生建议患者进行冠状动脉造影检查，以明确诊断。", "label": [{"entity": "冠状动脉造影检查", "start_idx": 8, "end_idx": 15, "type": "医疗程序"}]}, {"text": "医生建议患者进行冠状动脉造影检查以明确诊断。", "label": [{"entity": "冠状动脉造影检查", "start_idx": 12, "end_idx": 21, "type": "医疗程序"}]}, {"text": "小明对花生酱过敏，每次吃都会引起严重的皮肤瘙痒反应。", "label": [{"entity": "花生酱", "start_idx": 4, "end_idx": 7, "type": "过敏信息"}, {"entity": "皮肤瘙痒", "start_idx": 12, "end_idx": 16, "type": "过敏信息"}]}, {"text": "小明对花生酱和牛奶过敏，每次吃这些食物都会引发严重的皮肤反应。", "label": [{"entity": "花生酱", "start_idx": 6, "end_idx": 8, "type": "过敏信息"}, {"entity": "牛奶", "start_idx": 9, "end_idx": 11, "type": "过敏信息"}]}, {"text": "小明对花生过敏，每次吃坚果类食物前都会仔细检查成分表。", "label": [{"entity": "花生", "start_idx": 5, "end_idx": 6, "type": "过敏信息"}, {"entity": "坚果类食物", "start_idx": 14, "end_idx": 17, "type": "过敏信息"}]}, {"text": "小明对花生过敏，每次吃饼干时都会仔细查看配料表是否含有花生成分。", "label": [{"entity": "花生", "start_idx": 5, "end_idx": 7, "type": "过敏信息"}]}, {"text": "小明对花生过敏，每次吃到含有花生酱的食品都会出现严重反应。", "label": [{"entity": "花生", "start_idx": 4, "end_idx": 6, "type": "过敏信息"}, {"entity": "花生酱", "start_idx": 15, "end_idx": 18, "type": "过敏信息"}]}, {"text": "小明因为对花生过敏，每次点餐时都会特别提醒服务员不要放花生酱。", "label": [{"entity": "花生", "start_idx": 6, "end_idx": 7, "type": "过敏信息"}, {"entity": "花生酱", "start_idx": 25, "end_idx": 27, "type": "过敏信息"}]}, {"text": "小明对花生过敏，每次点餐时都会特别提醒服务员不要放花生酱。", "label": [{"entity": "花生", "start_idx": 3, "end_idx": 4, "type": "过敏信息"}, {"entity": "花生酱", "start_idx": 21, "end_idx": 23, "type": "过敏信息"}]}, {"text": "小明对花生过敏，每次吃花生酱都会引发严重的皮肤反应。", "label": [{"entity": "花生", "start_idx": 4, "end_idx": 5, "type": "过敏信息"}, {"entity": "花生酱", "start_idx": 10, "end_idx": 12, "type": "过敏信息"}]}, {"text": "小明对花生过敏，每次点餐时都会特别提醒服务员不要放花生酱。", "label": [{"entity": "花生", "start_idx": 5, "end_idx": 7, "type": "过敏信息"}, {"entity": "花生酱", "start_idx": 26, "end_idx": 29, "type": "过敏信息"}]}, {"text": "小明对花生过敏，每次吃坚果类食物前都会仔细检查成分表。", "label": [{"entity": "花生", "start_idx": 4, "end_idx": 5, "type": "过敏信息"}]}, {"text": "张女士的最后一次月经日期是2023年5月15日，预产期为2023年12月22日。", "label": [{"entity": "2023年5月15日", "start_idx": 6, "end_idx": 14, "type": "生育信息"}, {"entity": "2023年12月22日", "start_idx": 20, "end_idx": 28, "type": "生育信息"}]}, {"text": "张女士的预产期是2024年5月15日，她计划在医院进行剖腹产手术。", "label": [{"entity": "预产期", "start_idx": 4, "end_idx": 6, "type": "生育信息"}, {"entity": "2024年5月15日", "start_idx": 7, "end_idx": 15, "type": "生育信息"}, {"entity": "剖腹产手术", "start_idx": 29, "end_idx": 35, "type": "生育信息"}]}, {"text": "张女士在2023年5月12日进行了剖腹产手术，新生儿体重为3.5公斤。", "label": [{"entity": "张女士", "start_idx": 0, "end_idx": 3, "type": "生育信息"}, {"entity": "2023年5月12日", "start_idx": 4, "end_idx": 10, "type": "生育信息"}, {"entity": "剖腹产手术", "start_idx": 11, "end_idx": 17, "type": "生育信息"}, {"entity": "新生儿体重为3.5公斤", "start_idx": 18, "end_idx": 28, "type": "生育信息"}]}, {"text": "张女士的预产期是2024年5月15日，她的血型为O型Rh阳性。", "label": [{"entity": "2024年5月15日", "start_idx": 6, "end_idx": 14, "type": "生育信息"}]}, {"text": "张女士的预产期是2024年6月15日，医生建议她提前两周开始准备待产包。", "label": [{"entity": "2024年6月15日", "start_idx": 7, "end_idx": 16, "type": "生育信息"}, {"entity": "提前两周", "start_idx": 26, "end_idx": 31, "type": "生育信息"}]}, {"text": "医生记录了孕妇的预产期为2024年6月15日，并建议定期产检。", "label": [{"entity": "孕妇", "start_idx": 4, "end_idx": 6, "type": "生育信息"}, {"entity": "预产期", "start_idx": 7, "end_idx": 9, "type": "生育信息"}, {"entity": "2024年6月15日", "start_idx": 10, "end_idx": 18, "type": "生育信息"}]}, {"text": "张女士的预产期是2024年5月15日，她的血型是A型RH阳性。", "label": [{"entity": "2024年5月15日", "start_idx": 5, "end_idx": 13, "type": "生育信息"}]}, {"text": "张女士在2023年5月12日进行了产前检查，结果显示胎儿胎心为140次/分钟。", "label": [{"entity": "张女士", "start_idx": 0, "end_idx": 3, "type": "生育信息"}, {"entity": "2023年5月12日", "start_idx": 4, "end_idx": 11, "type": "生育信息"}, {"entity": "产前检查", "start_idx": 12, "end_idx": 17, "type": "生育信息"}, {"entity": "140次/分钟", "start_idx": 31, "end_idx": 37, "type": "生育信息"}]}, {"text": "张女士的生育记录显示，她于2018年3月15日自然分娩，新生儿体重为3.5公斤。", "label": [{"entity": "2018年3月15日", "start_idx": 7, "end_idx": 15, "type": "生育信息"}, {"entity": "自然分娩", "start_idx": 16, "end_idx": 20, "type": "生育信息"}, {"entity": "3.5公斤", "start_idx": 30, "end_idx": 34, "type": "生育信息"}]}, {"text": "张女士的生育信息显示，她于2015年7月1日生下了第一个孩子，是一名男孩。", "label": [{"entity": "2015年7月1日", "start_idx": 8, "end_idx": 15, "type": "生育信息"}, {"entity": "第一个孩子", "start_idx": 17, "end_idx": 21, "type": "生育信息"}, {"entity": "男孩", "start_idx": 24, "end_idx": 26, "type": "生育信息"}]}, {"text": "上海外滩的夜景吸引了无数游客前来观赏。", "label": [{"entity": "上海外滩", "start_idx": 0, "end_idx": 4, "type": "地理位置"}]}, {"text": "北京故宫的游客们正在天安门广场附近欣赏古老的建筑。", "label": [{"entity": "北京", "start_idx": 0, "end_idx": 1, "type": "地理位置"}, {"entity": "故宫", "start_idx": 2, "end_idx": 3, "type": "地理位置"}, {"entity": "天安门广场", "start_idx": 7, "end_idx": 9, "type": "地理位置"}]}, {"text": "北京故宫是中国明清两代的皇家宫殿，位于北京市中心。", "label": [{"entity": "北京故宫", "start_idx": 0, "end_idx": 3, "type": "地理位置"}, {"entity": "北京市中心", "start_idx": 20, "end_idx": 25, "type": "地理位置"}]}, {"text": "北京故宫的门票在旺季时需要提前在网上预约购买。", "label": [{"entity": "北京", "start_idx": 0, "end_idx": 1, "type": "地理位置"}, {"entity": "故宫", "start_idx": 2, "end_idx": 3, "type": "地理位置"}]}, {"text": "上海外滩的夜景以其璀璨的灯光和壮观的建筑群吸引着众多游客。", "label": [{"entity": "上海外滩", "start_idx": 0, "end_idx": 4, "type": "地理位置"}]}, {"text": "北京故宫是中国最著名的古代宫殿建筑群之一。", "label": [{"entity": "北京故宫", "start_idx": 0, "end_idx": 3, "type": "地理位置"}]}, {"text": "北京故宫是中国最大的古代宫殿建筑群，位于北京市中心。", "label": [{"entity": "北京故宫", "start_idx": 0, "end_idx": 3, "type": "地理位置"}, {"entity": "北京市中心", "start_idx": 18, "end_idx": 23, "type": "地理位置"}]}, {"text": "今天下午3点，我需要从北京首都国际机场T3航站楼出发，前往上海浦东国际机场。", "label": [{"entity": "今天下午3点", "start_idx": 0, "end_idx": 6, "type": "行程信息"}, {"entity": "北京首都国际机场T3航站楼", "start_idx": 8, "end_idx": 23, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 28, "end_idx": 40, "type": "行程信息"}]}, {"text": "我计划明天早上8点从北京首都国际机场出发，前往上海浦东国际机场。", "label": [{"entity": "明天早上8点", "start_idx": 3, "end_idx": 8, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 9, "end_idx": 20, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 22, "end_idx": 35, "type": "行程信息"}]}, {"text": "我计划下周二上午9:30从北京首都国际机场出发，前往上海浦东国际机场。", "label": [{"entity": "下周二上午9:30", "start_idx": 3, "end_idx": 10, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 14, "end_idx": 24, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 27, "end_idx": 37, "type": "行程信息"}]}, {"text": "我计划明天上午10点从北京首都国际机场出发，前往上海浦东国际机场。", "label": [{"entity": "明天上午10点", "start_idx": 3, "end_idx": 9, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 14, "end_idx": 24, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 29, "end_idx": 39, "type": "行程信息"}]}, {"text": "我计划明天上午9点从北京首都国际机场出发，前往上海浦东国际机场。", "label": [{"entity": "明天上午9点", "start_idx": 3, "end_idx": 8, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 10, "end_idx": 20, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 24, "end_idx": 34, "type": "行程信息"}]}, {"text": "我计划下周三从北京首都国际机场出发，搭乘CA1234航班前往上海浦东国际机场。", "label": [{"entity": "下周三", "start_idx": 3, "end_idx": 6, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 10, "end_idx": 22, "type": "行程信息"}, {"entity": "CA1234", "start_idx": 27, "end_idx": 31, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 36, "end_idx": 48, "type": "行程信息"}]}, {"text": "我计划明天上午9:30从北京首都国际机场搭乘CA1234航班前往上海浦东国际机场。", "label": [{"entity": "明天上午9:30", "start_idx": 3, "end_idx": 10, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 13, "end_idx": 24, "type": "行程信息"}, {"entity": "CA1234", "start_idx": 27, "end_idx": 31, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 36, "end_idx": 47, "type": "行程信息"}]}, {"text": "我计划下周三上午9:30从北京首都国际机场出发，飞往上海浦东国际机场。", "label": [{"entity": "下周三上午9:30", "start_idx": 3, "end_idx": 10, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 12, "end_idx": 21, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 24, "end_idx": 33, "type": "行程信息"}]}, {"text": "我计划下周三从北京首都国际机场搭乘CA1234航班前往上海浦东国际机场。", "label": [{"entity": "下周三", "start_idx": 3, "end_idx": 6, "type": "行程信息"}, {"entity": "北京首都国际机场", "start_idx": 9, "end_idx": 16, "type": "行程信息"}, {"entity": "CA1234航班", "start_idx": 18, "end_idx": 22, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 25, "end_idx": 32, "type": "行程信息"}]}, {"text": "今天下午3点，我需要从北京首都国际机场T3航站楼搭乘CA1234航班前往上海浦东国际机场。", "label": [{"entity": "北京首都国际机场T3航站楼", "start_idx": 11, "end_idx": 24, "type": "行程信息"}, {"entity": "CA1234航班", "start_idx": 27, "end_idx": 32, "type": "行程信息"}, {"entity": "上海浦东国际机场", "start_idx": 36, "end_idx": 45, "type": "行程信息"}]}]