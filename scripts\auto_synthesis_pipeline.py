#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动合成数据生成流水线
实现迭代优化策略，自动补齐实体分布并提升多样性

使用方法：
python scripts/auto_synthesis_pipeline.py --dataset path/to/original_dataset.json --target-count 100 --max-iterations 10
"""

import os
import sys
import json
import copy
import argparse
import shutil
import random
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Any
from collections import Counter, defaultdict
import subprocess

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入数据集管理器
from synth_dataset_manager import SynthDatasetManager

# 导入增强的数据收集器
from evaluation.framework.utils.evaluation_collector import (
    collect_strategy_generation_metadata,
    collect_strategy_statistics,
    collect_detailed_evaluation_metrics,
    save_enhanced_iteration_data,
    collect_ablation_experiment_data
)

# 配置参数
DEFAULT_CONFIG = {
    "distribution_threshold": 0.05,  # 分布偏差阈值 5%
    "diversity_thresholds": {
        "vocabulary_diversity": 0.7,
        "syntactic_diversity": 0.6,
        "semantic_diversity": 0.5,
        "context_diversity": 0.6,
        "entity_diversity": 0.8
    },
    "batch_size": 10,  # 每次生成的批次大小
    "max_retries": 3,  # 最大重试次数
    "intermediate_results": {
        "enabled": True,
        "max_iterations_to_keep": 3,  # 保留最近N轮的结果
        "compression_enabled": True,  # 是否启用压缩
        "compression_format": "gzip",  # gzip, bz2, xz
        "cleanup_on_success": True,  # 成功时是否清理旧的中间结果
        "storage_quotas": {  # 存储配额（单位：MB）
            "raw": 500,
            "by_entity": 1000,
            "processed": 500
        }
    }
}

def setup_environment():
    """设置环境变量和路径"""
    print("[✓] 环境设置完成")

def copy_config_to_run_dir(src_path: str, run_dir: Path, config_type: str) -> Path:
    """复制配置文件到运行目录
    
    Args:
        src_path: 源配置文件路径
        run_dir: 运行目录
        config_type: 配置类型(用于目录命名)
    
    Returns:
        Path: 运行目录中的配置文件路径
    """
    # 确保配置目录存在
    config_dir = run_dir / "config"
    config_dir.mkdir(exist_ok=True)
    
    # 构建目标路径
    dst_path = config_dir / f"{config_type}_config.json"
    
    # 复制配置文件
    shutil.copy2(src_path, dst_path)
    print(f"[✓] 已复制{config_type}配置到运行目录：{dst_path}")
    
    return dst_path

def write_back_config(src_path: Path, dst_path: str, config_type: str):
    """选择性回写配置文件到全局目录
    
    Args:
        src_path: 运行目录中的配置文件路径
        dst_path: 全局目录中的配置文件路径
        config_type: 配置类型(用于日志)
    """
    try:
        # 读取运行目录的配置
        with src_path.open('r', encoding='utf-8') as f:
            run_config = json.load(f)
        
        # 读取全局目录的配置
        with open(dst_path, 'r', encoding='utf-8') as f:
            global_config = json.load(f)
        
        # 检查是否有实质性变化
        if run_config != global_config:
            # 备份全局配置
            backup_path = f"{dst_path}.bak"
            shutil.copy2(dst_path, backup_path)
            print(f"[✓] 已备份全局{config_type}配置：{backup_path}")
            
            # 回写配置
            with open(dst_path, 'w', encoding='utf-8') as f:
                json.dump(run_config, f, ensure_ascii=False, indent=2)
            print(f"[✓] 已回写{config_type}配置到全局目录")
        else:
            print(f"[信息] {config_type}配置无变化，跳过回写")
    except Exception as e:
        print(f"[错误] 回写配置失败：{e}")
        raise

def load_original_dataset(dataset_path: str) -> List[Dict]:
    """加载原始数据集"""
    print(f"加载原始数据集：{dataset_path}")
    
    with open(dataset_path, 'r', encoding='utf-8') as f:
        dataset = json.load(f)
    
    print(f"原始数据集包含 {len(dataset)} 条记录")
    return dataset

def load_entity_schema() -> List[str]:
    """从entity_schema.json加载所有实体类型"""
    try:
        with open('src/gen_strat/entity_schema.json', 'r', encoding='utf-8') as f:
            schema = json.load(f)
        
        # 返回扁平化的实体类型列表
        entity_types = schema.get('flat_list', [])
        print(f"[✓] 从entity_schema.json加载了 {len(entity_types)} 种实体类型")
        return entity_types
        
    except Exception as e:
        print(f"[错误] 加载entity_schema.json失败：{e}")
        raise

def calculate_entity_distribution(dataset: List[Dict]) -> Dict[str, int]:
    """计算当前实体分布"""
    entity_counts = Counter()
    
    for item in dataset:
        for span in item.get("label", []):
            entity_type = span.get("type")
            if entity_type:
                entity_counts[entity_type] += 1
    
    return dict(entity_counts)

def calculate_distribution_gap(current_dist: Dict[str, int], target_dist: Dict[str, int]) -> Dict[str, int]:
    """计算当前分布与评估目标的差距
    
    Args:
        current_dist: 当前数据集的实体分布
        target_dist: 评估目标分布（来自balance_config.json）
    
    Returns:
        Dict[str, int]: 每个实体类型的差距（正值表示需要补充，负值表示需要修剪）
    """
    gap = {}
    
    for entity_type, target_count in target_dist.items():
        current_count = current_dist.get(entity_type, 0)
        # 计算实际差距（可以是正值或负值）
        gap[entity_type] = target_count - current_count
    
    return gap

def find_priority_entity(gap: Dict[str, int]) -> Tuple[str, int]:
    """找到需要优先补齐的实体类型（差距最大者）"""
    if not gap:
        return None, 0
    
    # 找到差距最大的实体类型
    priority_entity = max(gap.items(), key=lambda x: x[1])
    return priority_entity

def trim_dataset_by_entity_count(
    dataset: List[Dict], 
    target_distribution: Dict[str, int], 
    tolerance: float = 0.1,
    random_seed: int = None,
    replacement_mode: bool = True
):
    """根据评估目标修剪数据集，支持替换式修剪和随机种子控制
    
    使用balance_config.json中的评估目标作为参考，对数据集进行修剪：
    1. 如果某实体类型数量超过目标值+容差，进行随机删减或替换
    2. 如果某实体类型数量低于目标值-容差，在后续迭代中补充
    
    Args:
        dataset: 要修剪的数据集
        target_distribution: 评估目标分布（来自balance_config.json）
        tolerance: 容差比例（默认0.1，即±10%）
        random_seed: 随机种子，用于保证修剪结果可复现
        replacement_mode: 是否使用替换式修剪（保留句子但替换过量实体）
    
    Returns:
        Tuple[List[Dict], Dict[str, Dict]]: 修剪后的数据集和修剪统计信息
    
    注意：这里使用的是评估目标，不是生成补充目标
    """
    print("[说明] 使用评估目标（来自balance_config.json）进行数据修剪")
    print(f"    - 容差范围：±{tolerance*100}%")
    print(f"    - 修剪模式：{'替换式' if replacement_mode else '删除式'}")
    if random_seed is not None:
        print(f"    - 随机种子：{random_seed}")
        random.seed(random_seed)
    
    # 计算当前分布
    current_distribution = {}
    entity_positions = defaultdict(list)  # 记录每个实体类型的所有出现位置
    
    for idx, item in enumerate(dataset):
        for span_idx, entity in enumerate(item.get("label", [])):
            entity_type = entity.get("type")
            if entity_type:
                current_distribution[entity_type] = current_distribution.get(entity_type, 0) + 1
                entity_positions[entity_type].append((idx, span_idx))
    
    # 初始化统计信息
    trim_stats = {}
    for entity_type in target_distribution:
        current_count = current_distribution.get(entity_type, 0)
        trim_stats[entity_type] = {
            "original_count": current_count,
            "target_count": target_distribution[entity_type],
            "trimmed_count": current_count,
            "removed_count": 0,
            "replaced_count": 0
        }
    
    # 深拷贝数据集以进行修改
    trimmed_dataset = copy.deepcopy(dataset)
    
    # 对每个实体类型进行修剪
    for entity_type, target_count in target_distribution.items():
        current_count = current_distribution.get(entity_type, 0)
        max_allowed = int(target_count * (1 + tolerance))
        
        if current_count > max_allowed:
            # 需要修剪的数量
            to_remove = current_count - max_allowed
            positions = entity_positions[entity_type]
            
            if replacement_mode:
                # 替换式修剪：随机选择要替换的实体位置
                positions_to_replace = random.sample(positions, to_remove)
                
                # 替换选中的实体（用通用占位符或从其他样本学习）
                for doc_idx, span_idx in positions_to_replace:
                    original_span = trimmed_dataset[doc_idx]["label"][span_idx]
                    # 替换为通用占位符，支持不同的键名格式
                    span_data = {
                        "type": entity_type,
                        "entity": f"[{entity_type}]",  # 占位符
                        "is_placeholder": True  # 标记为占位符
                    }
                    
                    # 根据实际数据格式添加起始和结束索引
                    if "start_idx" in original_span and "end_idx" in original_span:
                        span_data.update({
                            "start_idx": original_span["start_idx"],
                            "end_idx": original_span["end_idx"]
                        })
                    elif "start" in original_span and "end" in original_span:
                        span_data.update({
                            "start": original_span["start"],
                            "end": original_span["end"]
                        })
                    else:
                        # 如果没有找到索引信息，使用占位符值
                        span_data.update({
                            "start_idx": 0,
                            "end_idx": 1
                        })
                    
                    trimmed_dataset[doc_idx]["label"][span_idx] = span_data
                
                # 更新统计信息
                trim_stats[entity_type]["replaced_count"] = to_remove
                trim_stats[entity_type]["trimmed_count"] = max_allowed
                
            else:
                # 删除式修剪：随机选择要保留的样本
                items_with_entity = [
                    item for item in dataset 
                    if any(e.get("type") == entity_type for e in item.get("label", []))
                ]
                
                # 确保不会删除太多
                to_keep = max_allowed
                if len(items_with_entity) > to_keep:
                    kept_items = set(random.sample(items_with_entity, to_keep))
                    
                    # 更新统计信息
                    removed_count = len(items_with_entity) - len(kept_items)
                    trim_stats[entity_type]["removed_count"] = removed_count
                    trim_stats[entity_type]["trimmed_count"] = len(kept_items)
                    
                    # 只保留未被删除的样本
                    trimmed_dataset = [
                        item for item in trimmed_dataset
                        if not any(e.get("type") == entity_type for e in item.get("label", []))
                        or item in kept_items
                    ]
    
    # 修剪后重新计算分布
    final_distribution = {}
    coverage_gaps = {}
    
    for item in trimmed_dataset:
        for entity in item.get("label", []):
            entity_type = entity.get("type")
            if entity_type:
                final_distribution[entity_type] = final_distribution.get(entity_type, 0) + 1
    
    # 检查覆盖率和补齐需求
    for entity_type, target_count in target_distribution.items():
        final_count = final_distribution.get(entity_type, 0)
        min_required = int(target_count * (1 - tolerance))
        
        if final_count < min_required:
            coverage_gaps[entity_type] = min_required - final_count
    
    # 输出修剪统计
    print("\n=== 修剪统计 ===")
    for entity_type, stats in trim_stats.items():
        if stats["removed_count"] > 0 or stats["replaced_count"] > 0:
            print(f"实体类型：{entity_type}")
            print(f"  - 原始数量：{stats['original_count']}")
            print(f"  - 目标数量：{stats['target_count']} (±{tolerance*100}%)")
            print(f"  - 修剪后数量：{stats['trimmed_count']}")
            if stats["removed_count"] > 0:
                print(f"  - 删除数量：{stats['removed_count']}")
            if stats["replaced_count"] > 0:
                print(f"  - 替换数量：{stats['replaced_count']}")
    
    # 输出覆盖率检查结果
    if coverage_gaps:
        print("\n=== 覆盖率检查 ===")
        print("以下实体类型需要补齐：")
        for entity_type, gap in coverage_gaps.items():
            print(f"  - {entity_type}: 还需 {gap} 个")
    
    # 返回结果
    return trimmed_dataset, {
        "trim_stats": trim_stats,
        "final_distribution": final_distribution,
        "coverage_gaps": coverage_gaps
    }

def update_balance_config(
    target_count: int, 
    entity_types: List[str],
    dataset_manager: SynthDatasetManager,
    write_back: bool = False
):
    """更新balance_config.json中的评估目标数量
    
    这个目标数量用于：
    1. 评估数据集是否达标
    2. 剪枝时判断上下限
    3. 作为最终目标参考
    
    注意：这不是生成补充数据时的目标数量，生成目标由run_strategy_generation动态计算
    
    Args:
        target_count: 每个实体类型的目标数量
        entity_types: 所有实体类型列表
        dataset_manager: 数据集管理器实例
        write_back: 是否回写到全局配置
    
    Returns:
        Path: 运行目录中的配置文件路径
    """
    # 全局配置文件路径
    global_config_path = Path("src") / "gen_strat" / "balance_config.json"
    
    try:
        # 1. 复制配置到运行目录
        run_config_path = copy_config_to_run_dir(
            str(global_config_path),
            dataset_manager.run_dir,
            "balance"
        )
        
        # 2. 读取并更新配置
        with run_config_path.open('r', encoding='utf-8') as f:
            config = json.load(f)
        
        config["balance_target_per_type"] = target_count
        
        # 更新所有实体类型的目标数量
        entity_targets = {}
        for entity_type in entity_types:
            entity_targets[entity_type] = target_count
        
        config["entity_type_targets"] = entity_targets
        
        # 3. 保存到运行目录
        with run_config_path.open('w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"[✓] 已更新评估目标配置")
        print(f"    - 配置文件：{run_config_path}")
        print(f"    - 每个实体类型目标数量：{target_count}")
        print(f"    - 实体类型总数：{len(entity_types)} 种")
        print(f"    - 用途：评估达标情况、数据剪枝")
        
        # 4. 选择性回写到全局配置
        if write_back:
            write_back_config(run_config_path, str(global_config_path), "balance")
        
        return run_config_path
        
    except Exception as e:
        print(f"[错误] 更新balance_config.json失败：{e}")
        raise

def update_dataset_path(dataset_path: str):
    """更新1-gen_distribution_target.py中的数据集路径"""
    script_path = "src/gen_strat/1-gen_distribution_target.py"
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新数据集路径
        old_path = 'format-dataset/privacy_bench.json'
        content = content.replace(old_path, dataset_path)
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"[✓] 已更新数据集路径：{dataset_path}")
        
    except Exception as e:
        print(f"[错误] 更新数据集路径失败：{e}")
        raise

def run_strategy_generation(
    dataset_manager: SynthDatasetManager,
    force_regenerate: bool = False,
    diversity_metrics: Dict[str, float] = None
):
    """运行策略生成，支持策略复用
    
    这个函数会：
    1. 分析当前数据集与评估目标的差距
    2. 生成一个新的目标分布文件，指示本次需要补充的数量
    3. 这个补充目标不同于评估目标，它是动态计算的增量
    
    Args:
        dataset_manager: 数据集管理器实例
        force_regenerate: 是否强制重新生成策略
        diversity_metrics: 当前的多样性指标，用于判断是否需要重新生成
    
    Returns:
        Tuple[Dict[str, str], bool]: (策略文件路径字典, 是否生成了新策略)
    
    策略复用规则：
    1. 默认复用上一轮的策略文件
    2. 在以下情况下重新生成：
       - force_regenerate=True
       - 首次运行或找不到策略文件
       - 多样性指标低于阈值
       - 连续多轮无改善
    """
    print("=== 检查策略状态 ===")
    
    try:
        # 使用dataset_manager的strategies目录作为输出目录
        output_dir = str(dataset_manager.dirs["strategies"])
        print(f"[信息] 策略目录：{output_dir}")
        
        # 获取数据集路径和配置
        metadata = dataset_manager.load_metadata()
        dataset_path = metadata.get("config", {}).get("dataset_path", "format-dataset/privacy_bench_small.json")
        
        # 检查是否需要重新生成策略
        need_regenerate = force_regenerate
        strategy_files = get_latest_strategy_files(output_dir)
        
        # 如果没有找到任何策略文件，强制重新生成
        if not strategy_files:
            print("[信息] 未找到任何策略文件，将生成新策略")
            need_regenerate = True
        elif not all(strategy_files.values()):
            print("[信息] 策略文件不完整，将生成新策略")
            need_regenerate = True
        
        if not need_regenerate and all(strategy_files.values()):
            # 只有当不需要重新生成且所有策略文件都存在时，才复用
            print("[✓] 复用上一轮策略文件")
            print(f"    - 目标分布：{strategy_files.get('target_distribution', '未找到')}")
            print(f"    - 句子多样化：{strategy_files.get('sentence_diversity', '未找到')}")
            print(f"    - 实体多样化：{strategy_files.get('entity_diversity', '未找到')}")
            return strategy_files, False
        elif diversity_metrics:
            # 检查多样性指标是否过低
            thresholds = {
                "vocabulary_diversity": 0.4,
                "syntactic_diversity": 0.3,
                "semantic_diversity": 0.3,
                "context_diversity": 0.3,
                "entity_diversity": 0.5
            }
            low_metrics = [
                metric for metric, value in diversity_metrics.items()
                if metric in thresholds and value < thresholds[metric]
            ]
            if low_metrics:
                print(f"[信息] 以下多样性指标过低，需要重新生成策略：{low_metrics}")
                need_regenerate = True
        
        print("\n=== 生成新策略 ===")
        print("[说明] 即将生成补充目标:")
        print("    - 基于当前数据集与评估目标的差距")
        print("    - 用于指导本次迭代生成多少新数据")
        print("    - 这不是最终评估标准，评估标准在balance_config.json中")
        
        # 加载实体类型和评估目标
        all_entity_types = load_entity_schema()
        balance_config_path = Path("src") / "gen_strat" / "balance_config.json"
        
        # 读取评估目标
        with balance_config_path.open('r', encoding='utf-8') as f:
            balance_config = json.load(f)
            target_distribution = balance_config.get("entity_type_targets", {})
            tolerance = balance_config.get("distribution_tolerance", 0.1)
        
        # 收集策略生成元数据
        strategy_metadata = collect_strategy_generation_metadata(
            output_dir=output_dir,
            entity_types=all_entity_types,
            generation_config=metadata.get("config", {})
        )
        
        # 运行目标分布生成
        try:
            import importlib.util
            print(f"\n=== 目标分布生成 ===")
            print(f"[1/4] 检查环境...")
            print(f"[信息] 模块路径：src/gen_strat/1-gen_distribution_target.py")
            print(f"[信息] 数据集路径：{dataset_path}")
            print(f"[信息] 输出目录：{output_dir}")
            
            # 检查数据集文件是否存在
            if not os.path.exists(dataset_path):
                raise FileNotFoundError(f"数据集文件不存在：{dataset_path}")
            print(f"[✓] 数据集文件检查通过")
            
            # 检查输出目录权限
            target_dir = Path(output_dir) / "entity_target"
            try:
                target_dir.mkdir(parents=True, exist_ok=True)
                test_file = target_dir / "write_test.tmp"
                test_file.touch()
                test_file.unlink()
                print(f"[✓] 输出目录权限检查通过")
            except Exception as e:
                raise PermissionError(f"输出目录权限检查失败：{e}")
            
            print(f"[2/4] 导入模块...")
            # 检查模块文件是否存在
            module_path = Path("src/gen_strat/1-gen_distribution_target.py")
            if not module_path.exists():
                raise FileNotFoundError(f"模块文件不存在：{module_path}")
                
            spec = importlib.util.spec_from_file_location("gen_distribution_target", str(module_path))
            if spec is None:
                raise ImportError("无法加载模块规范")
                
            gen_distribution_target = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(gen_distribution_target)
            print(f"[✓] 模块导入成功")
            
        except Exception as e:
            print(f"[错误] 目标分布生成环境检查失败：{e}")
            import traceback
            traceback.print_exc()
            raise
        
        try:
            print(f"[3/4] 生成目标分布...")
            # 调用目标分布生成，传递输出目录和数据集路径
            target_file = gen_distribution_target.main(output_dir, dataset_path)
            
            print(f"[4/4] 验证生成结果...")
            if not target_file:
                raise ValueError("目标分布生成返回了空路径")
                
            if not os.path.exists(target_file):
                raise FileNotFoundError(f"目标分布文件未生成：{target_file}")
                
            # 验证文件内容
            try:
                with open(target_file, 'r', encoding='utf-8') as f:
                    target_data = json.load(f)
                if not isinstance(target_data, dict):
                    raise ValueError(f"目标分布文件格式错误：应为字典，实际为{type(target_data)}")
                print(f"[✓] 文件内容验证通过")
            except json.JSONDecodeError as e:
                raise ValueError(f"目标分布文件不是有效的JSON：{e}")
                
            print(f"[✓] 补充目标分布文件已生成：{target_file}")
            print(f"    - 该文件指示本次迭代需要新生成的数据量")
            print(f"    - 基于评估目标（来自balance_config.json）计算得出")
            strategy_metadata["strategy_files"]["target_distribution"] = target_file
            
        except Exception as e:
            print(f"[错误] 目标分布生成失败：{e}")
            import traceback
            traceback.print_exc()
            raise
        
        # 运行多样化策略生成
        try:
            print(f"\n=== 多样化策略生成 ===")
            print(f"[1/4] 检查环境...")
            
            # 检查模块文件是否存在
            module_path = Path("src/gen_strat/2-gen_diversity.py")
            if not module_path.exists():
                raise FileNotFoundError(f"模块文件不存在：{module_path}")
            print(f"[✓] 模块文件检查通过")
            
            # 检查输出目录权限
            diversity_dirs = [
                Path(output_dir) / "sen_diversity",
                Path(output_dir) / "entity_diversity"
            ]
            for dir_path in diversity_dirs:
                try:
                    dir_path.mkdir(parents=True, exist_ok=True)
                    test_file = dir_path / "write_test.tmp"
                    test_file.touch()
                    test_file.unlink()
                except Exception as e:
                    raise PermissionError(f"目录权限检查失败 {dir_path}: {e}")
            print(f"[✓] 输出目录权限检查通过")
            
            print(f"[2/4] 导入模块...")
            spec = importlib.util.spec_from_file_location("gen_diversity", str(module_path))
            if spec is None:
                raise ImportError("无法加载模块规范")
            
            gen_diversity = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(gen_diversity)
            print(f"[✓] 模块导入成功")
            
            print(f"[3/4] 生成多样化策略...")
            # 调用多样化策略生成，传递输出目录和时间戳并启用全局缓存
            gen_diversity.main(output_dir, use_global_cache=True, timestamp=dataset_manager.get_timestamp())
            
            print(f"[4/4] 验证生成结果...")
            # 获取生成的策略文件
            strategy_files = get_latest_strategy_files(output_dir)
            
            # 验证必需的策略文件
            required_files = {
                'sentence_diversity': Path(output_dir) / "sen_diversity" / "sen_diversify_value.json",
                'entity_diversity': Path(output_dir) / "entity_diversity"
            }
            
            for key, path in required_files.items():
                if key not in strategy_files:
                    raise FileNotFoundError(f"缺少必需的策略文件：{key} ({path})")
                elif key == 'entity_diversity':
                    if not path.exists() or not any(path.glob("**/entity_diversity.json")):
                        raise FileNotFoundError(f"实体多样化目录不存在或为空：{path}")
                elif not path.exists():
                    raise FileNotFoundError(f"策略文件不存在：{path}")
                    
            # 验证文件内容
            for key, file_path in strategy_files.items():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    if not isinstance(data, (dict, list)):
                        raise ValueError(f"策略文件格式错误 {key}：应为字典或列表，实际为{type(data)}")
                except json.JSONDecodeError as e:
                    raise ValueError(f"策略文件不是有效的JSON {key}：{e}")
            
            print(f"[✓] 多样化策略已生成到：{output_dir}")
            print(f"[✓] 文件内容验证通过")
            
            strategy_metadata["strategy_files"].update(strategy_files)
            
            # 收集策略文件统计信息
            strategy_statistics = collect_strategy_statistics(strategy_files)
            strategy_metadata["strategy_statistics"] = strategy_statistics
            
        except Exception as e:
            print(f"[错误] 多样化策略生成失败：{e}")
            import traceback
            traceback.print_exc()
            raise
        
        # 保存策略元数据
        metadata_file = dataset_manager.save_file(
            strategy_metadata,
            "strategy_generation_metadata",
            "strategies"
        )
        print(f"[✓] 策略元数据已保存到：{metadata_file}")
        
        print("[✓] 策略生成完成")
        print(f"[✓] 策略文件已保存到：{dataset_manager.dirs['strategies']}")
        print(f"[✓] 全局缓存已更新，下次运行将复用句子多样化和实体多样化数据")
        
        return strategy_files, True
        
    except Exception as e:
        print(f"[错误] 策略生成失败：{e}")
        raise

def get_latest_strategy_files(base_dir: str = "reproduce") -> Dict[str, str]:
    """获取策略文件路径"""
    files = {}
    
    # 使用 Path 对象处理路径
    base_path = Path(base_dir)
    
    # 获取目标分布文件（固定文件名）
    target_file = base_path / "entity_target" / "privacy_bench_target.json"
    print(f"[信息] 目标分布文件：{target_file}")
    if target_file.exists():
        files['target_distribution'] = str(target_file)
    
    # 获取句子多样化文件（固定文件名）
    sen_diversity_file = base_path / "sen_diversity" / "sen_diversify_value.json"
    print(f"[信息] 句子多样化文件：{sen_diversity_file}")
    if sen_diversity_file.exists():
        files['sentence_diversity'] = str(sen_diversity_file)
    
    # 获取最新的实体多样化目录
    entity_diversity_dir = base_path / "entity_diversity"
    if entity_diversity_dir.exists():
        entity_dirs = [d for d in entity_diversity_dir.iterdir() if d.is_dir()]
        if entity_dirs:
            latest_entity = sorted(entity_dirs)[-1]
            entity_json_file = latest_entity / 'entity_diversity.json'
            if entity_json_file.exists():
                files['entity_diversity'] = str(entity_json_file)
            else:
                print(f"[警告] 未找到实体多样化文件: {entity_json_file}")
    
    print(f"[✓] 找到策略文件：{len(files)} 个")
    for strategy_type, path in files.items():
        print(f"  - {strategy_type}: {path}")
    
    return files

def update_ner_config(
    strategy_files: Dict[str, str], 
    examples_path: str, 
    dataset_manager: SynthDatasetManager,
    generation_features: Dict = None,
    write_back: bool = False
):
    """更新ner_config.json中的文件路径和生成特征配置
    
    Args:
        strategy_files: 策略文件路径字典
        examples_path: 示例句子文件路径
        dataset_manager: 数据集管理器实例
        generation_features: 生成特征配置
        write_back: 是否回写到全局配置
    
    Returns:
        Path: 运行目录中的配置文件路径
    """
    # 全局配置文件路径
    global_config_path = "src/synth_data/ner_config.json"
    
    try:
        # 1. 复制配置到运行目录
        run_config_path = copy_config_to_run_dir(
            global_config_path,
            dataset_manager.run_dir,
            "ner"
        )
        
        # 2. 读取并更新配置
        with run_config_path.open('r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 更新文件路径（修正键名匹配）
        config.update({
            "target_file": strategy_files.get("target_distribution", ""),
            "sentence_diversity_file": strategy_files.get("sentence_diversity", ""),
            "entity_diversity_dir": strategy_files.get("entity_diversity", ""),
            "example_sentences_file": examples_path
        })

        # 更新生成特征配置
        if generation_features is None:
            generation_features = {
                "use_sentence_diversity": True,
                "use_entity_diversity": True,
                "use_example_sentences": True
            }
        config["generation_features"] = generation_features
        
        # 3. 保存到运行目录
        with run_config_path.open('w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        # 输出配置更新信息
        print(f"[✓] 已更新NER配置")
        print(f"    - 配置文件：{run_config_path}")
        print(f"    - 目标分布文件: {config.get('target_file', '未设置')}")
        print(f"    - 句子多样化文件: {config.get('sentence_diversity_file', '未设置')}")
        print(f"    - 实体多样化文件: {config.get('entity_diversity_dir', '未设置')}")
        print(f"    - 示例句子文件: {config.get('example_sentences_file', '未设置')}")
        print(f"    - 生成特征: {generation_features}")
        
        # 4. 选择性回写到全局配置
        if write_back:
            write_back_config(run_config_path, global_config_path, "ner")
        
        return run_config_path
        
    except Exception as e:
        print(f"[错误] 更新ner_config.json失败：{e}")
        raise

def run_data_generation(dataset_manager: SynthDatasetManager):
    """运行数据生成

    Returns:
        str: 生成的合成数据文件路径
    """
    print("=== 生成合成数据 ===")

    try:
        # 导入并运行NER数据生成模块
        sys.path.insert(0, str(project_root / "src" / "synth_data"))
        from ner_data_generation import main as ner_main

        # 获取当前运行的配置
        metadata = dataset_manager.load_metadata()
        config = metadata.get("config", {})
        
        # 更新中间结果配置
        intermediate_config = DEFAULT_CONFIG.get("intermediate_results", {}).copy()
        if config.get("intermediate_results"):
            intermediate_config.update(config["intermediate_results"])
        
        # 运行数据生成，传递策略目录、中间结果配置和时间戳
        ner_main(
            strategy_dir=str(dataset_manager.dirs["strategies"]),
            intermediate_results_config=intermediate_config,
            timestamp=dataset_manager.get_timestamp()
        )

        print("[✓] 合成数据生成完成")

        # 获取生成的数据文件路径（优先当前运行目录，回退到origin）
        synthetic_data_path = get_latest_synthetic_data(dataset_manager.run_dir)
        if not synthetic_data_path:
            raise FileNotFoundError("未找到生成的合成数据文件")

        print(f"[✓] 生成的数据文件：{synthetic_data_path}")
        return synthetic_data_path

    except Exception as e:
        print(f"[错误] 合成数据生成失败：{e}")
        raise

def get_latest_synthetic_data(run_dir: Path = None) -> str:
    """获取最新的合成数据文件路径
    
    优先在本次运行目录查找：
    - 运行根目录下匹配 ner_dataset_*.json
    - 运行目录的 output 子目录下匹配 *.json（若存在）
    找不到则回退到 synth_dataset/origin 下查找最新 .json
    """
    candidates = []

    try:
        if run_dir:
            run_dir = Path(run_dir)
            if run_dir.exists():
                # 运行根目录：ner_dataset_*.json
                candidates.extend(run_dir.glob("ner_dataset_*.json"))
                # 运行目录 output 子目录
                output_dir = run_dir / "output"
                if output_dir.exists():
                    candidates.extend([p for p in output_dir.glob("*.json")])
    except Exception:
        pass

    # 若在运行目录发现候选，返回最新
    if candidates:
        latest = max(candidates, key=lambda f: f.stat().st_ctime)
        return str(latest)

    # 回退到 origin 目录
    origin_dir = Path("synth_dataset") / "origin"
    if origin_dir.exists():
        files = [f for f in origin_dir.iterdir() if f.is_file() and f.suffix == '.json']
        if files:
            latest_file = max(files, key=lambda f: f.stat().st_ctime)
            return str(latest_file)

    return None

def load_synthetic_data(data_path: str) -> List[Dict]:
    """加载合成数据"""
    with open(data_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def merge_datasets(original_data: List[Dict], synthetic_data: List[Dict]) -> List[Dict]:
    """合并原始数据和合成数据"""
    return original_data + synthetic_data

def calculate_diversity_metrics(dataset: List[Dict]) -> Dict[str, float]:
    """计算多样性指标，包括 vanilla/latent 比例"""
    # 这里应该调用实际的多样性计算函数
    # 暂时返回模拟数据
    
    texts = [item["text"] for item in dataset]
    
    # 词汇多样性（简化计算）
    all_words = []
    for text in texts:
        words = text.split()
        all_words.extend(words)
    
    vocab_diversity = len(set(all_words)) / len(all_words) if all_words else 0
    
    # 计算 vanilla 比例（从标记中获取）
    total_entities = 0
    vanilla_entities = 0
    for item in dataset:
        for span in item.get("label", []):
            total_entities += 1
            # 假设生成时在 span 中标记了 is_vanilla
            if span.get("is_vanilla", False):
                vanilla_entities += 1
    
    vanilla_ratio = vanilla_entities / total_entities if total_entities > 0 else 0
    
    # 其他多样性指标需要实际实现
    return {
        "vocabulary_diversity": vocab_diversity,
        "syntactic_diversity": 0.8,  # 模拟值
        "semantic_diversity": 0.7,   # 模拟值
        "context_diversity": 0.6,    # 模拟值
        "entity_diversity": 0.75,    # 模拟值
        "vanilla_ratio": vanilla_ratio  # 新增：vanilla 实体比例
    }

def check_termination_conditions(
    gap: Dict[str, int], 
    diversity_metrics: Dict[str, float],
    config: Dict
) -> Dict[str, Any]:
    """修正后的终止条件检查 - 使用更严格的标准，将vanilla比例改为警告指标
    
    Returns:
        Dict[str, Any]: {
            "converged": bool,  # 是否收敛
            "warnings": List[str],  # 警告信息
            "distribution_satisfied": bool,  # 分布是否达标
            "diversity_satisfied": bool,  # 多样性是否达标
            "min_coverage_satisfied": bool  # 最小覆盖率是否达标
        }
    """
    warnings = []
    
    # 1. 修正分布检查：使用容差而非精确匹配
    try:
        with open('src/gen_strat/balance_config.json', 'r', encoding='utf-8') as f:
            balance_config = json.load(f)
    except FileNotFoundError:
        print("[警告] 未找到balance_config.json，使用默认容差")
        balance_config = {"distribution_tolerance": 0.1, "entity_type_targets": {}}
    
    tolerance = balance_config.get("distribution_tolerance", 0.1)
    target_counts = balance_config.get("entity_type_targets", {})
    
    # 检查每个实体类型是否在容差范围内
    within_tolerance_count = 0
    total_entity_types = len(target_counts)
    out_of_tolerance_types = []
    min_coverage_ratio = 0.5  # 每个类型至少达到目标的50%
    coverage_failed_types = []
    
    for entity_type, target_count in target_counts.items():
        current_gap = gap.get(entity_type, 0)
        tolerance_range = target_count * tolerance
        current_count = target_count - current_gap
        
        # 检查最小覆盖率
        if current_count < target_count * min_coverage_ratio:
            coverage_failed_types.append({
                "type": entity_type,
                "current": current_count,
                "target": target_count
            })
        
        # 如果gap在容差范围内，认为满足要求
        if abs(current_gap) <= tolerance_range:
            within_tolerance_count += 1
        else:
            out_of_tolerance_types.append({
                "type": entity_type,
                "gap": current_gap,
                "tolerance": tolerance_range
            })
    
    # 分布要求：
    # 1. 至少95%的实体类型在容差范围内
    # 2. 超出容差的实体类型的gap不能超过目标数量的20%
    tolerance_ratio = within_tolerance_count / total_entity_types if total_entity_types > 0 else 0
    severe_deviation = any(abs(item["gap"]) > target_counts[item["type"]] * 0.2 
                         for item in out_of_tolerance_types)
    
    distribution_satisfied = tolerance_ratio >= 0.95 and not severe_deviation
    
    # 最小覆盖率要求：所有类型都达到目标的50%
    min_coverage_satisfied = len(coverage_failed_types) == 0
    if not min_coverage_satisfied:
        warnings.append("部分实体类型未达到最小覆盖率要求(50%)")
        for item in coverage_failed_types:
            warnings.append(f"  - {item['type']}: {item['current']}/{item['target']} "
                          f"({item['current']/item['target']:.1%})")
    
    # 2. 修正多样性检查：根据当前配置调整阈值
    generation_features = config.get("generation_features", {})
    
    # 如果全功能禁用，降低多样性要求
    all_features_disabled = not any(generation_features.values())
    if all_features_disabled:
        adjusted_thresholds = {
            "vocabulary_diversity": 0.3,    # 从0.7降到0.3
            "syntactic_diversity": 0.5,     # 保持不变
            "semantic_diversity": 0.4,      # 保持不变
            "context_diversity": 0.5,       # 保持不变
            "entity_diversity": 0.5,        # 从0.8降到0.5
        }
        print("[信息] 检测到全功能禁用，使用调整后的多样性阈值")
    else:
        adjusted_thresholds = config["diversity_thresholds"]
    
    diversity_satisfied = True
    failed_metrics = []
    
    for metric, threshold in adjusted_thresholds.items():
        current_value = diversity_metrics.get(metric, 0)
        if current_value < threshold:
            diversity_satisfied = False
            failed_metrics.append(f"{metric}: {current_value:.3f} < {threshold}")
    
    # 3. 检查 vanilla 比例（改为警告指标）
    MIN_VANILLA_RATIO = 0.3  # vanilla最低比例
    current_vanilla_ratio = diversity_metrics.get("vanilla_ratio", 0)
    if current_vanilla_ratio < MIN_VANILLA_RATIO:
        warnings.append(f"Vanilla实体比例过低: {current_vanilla_ratio:.1%} < {MIN_VANILLA_RATIO:.1%}")
    
    # 4. 输出详细信息
    print(f"\n分布检查：{'通过' if distribution_satisfied else '未通过'}")
    print(f"  - 容差范围内实体类型：{within_tolerance_count}/{total_entity_types} ({tolerance_ratio:.1%})")
    print(f"  - 使用容差：±{tolerance:.1%}")
    if out_of_tolerance_types:
        print("  - 超出容差的实体类型:")
        for item in out_of_tolerance_types:
            print(f"    * {item['type']}: gap={item['gap']} (容差范围：±{item['tolerance']:.1f})")
    
    print(f"\n多样性检查：{'通过' if diversity_satisfied else '未通过'}")
    if failed_metrics:
        print(f"  - 未达标指标：{', '.join(failed_metrics)}")
    
    if warnings:
        print("\n⚠️ 警告：")
        for warning in warnings:
            print(f"  {warning}")
    
    # 收敛条件：分布达标 且 多样性达标 且 最小覆盖率达标
    converged = distribution_satisfied and diversity_satisfied and min_coverage_satisfied
    
    return {
        "converged": converged,
        "warnings": warnings,
        "distribution_satisfied": distribution_satisfied,
        "diversity_satisfied": diversity_satisfied,
        "min_coverage_satisfied": min_coverage_satisfied
    }

def adjust_generation_strategy(gap: Dict[str, int], diversity_metrics: Dict[str, float], 
                              iteration: int, generation_features: Dict[str, bool]) -> Dict[str, bool]:
    """根据当前状态智能调整生成策略"""
    
    original_features = generation_features.copy()
    
    # 如果词汇多样性过低，启用句子多样化
    vocab_diversity = diversity_metrics.get("vocabulary_diversity", 0)
    if vocab_diversity < 0.4:
        if not generation_features.get("use_sentence_diversity", False):
            print(f"[策略调整] 词汇多样性过低({vocab_diversity:.3f})，启用句子多样化")
            generation_features["use_sentence_diversity"] = True
    
    # 如果实体多样性过低，启用实体多样化
    entity_diversity = diversity_metrics.get("entity_diversity", 0)
    if entity_diversity < 0.6:
        if not generation_features.get("use_entity_diversity", False):
            print(f"[策略调整] 实体多样性过低({entity_diversity:.3f})，启用实体多样化")
            generation_features["use_entity_diversity"] = True
    
    # 如果分布差距过大且迭代超过3次，启用示例句子
    total_gap = sum(abs(g) for g in gap.values())
    if total_gap > 50 and iteration > 3:
        if not generation_features.get("use_example_sentences", False):
            print(f"[策略调整] 分布差距过大({total_gap})且迭代>3次，启用示例句子")
            generation_features["use_example_sentences"] = True
    
    # 如果连续多次迭代无改善，逐步启用所有功能
    if iteration > 5:
        all_features = ["use_sentence_diversity", "use_entity_diversity", "use_example_sentences"]
        disabled_features = [f for f in all_features if not generation_features.get(f, False)]
        
        if disabled_features:
            # 每次启用一个功能
            feature_to_enable = disabled_features[0]
            print(f"[策略调整] 第{iteration}次迭代，启用{feature_to_enable}以提升效果")
            generation_features[feature_to_enable] = True
    
    # 检查是否有变化
    if generation_features != original_features:
        changed_features = []
        for key, value in generation_features.items():
            if value != original_features.get(key, False):
                status = "启用" if value else "禁用"
                changed_features.append(f"{key}:{status}")
        print(f"[策略更新] 已调整: {', '.join(changed_features)}")
    else:
        print("[策略保持] 当前策略无需调整")
    
    return generation_features

def diagnose_iteration_issues(gap: Dict[str, int], diversity_metrics: Dict[str, float], 
                             generation_features: Dict[str, bool]) -> Tuple[List[str], List[str]]:
    """诊断迭代过程中的问题"""
    
    issues = []
    recommendations = []
    
    # 诊断分布问题
    total_gap = sum(abs(g) for g in gap.values())
    if total_gap > 100:
        issues.append("实体分布差距过大")
        recommendations.append("考虑增加目标数量或调整生成策略")
        
        # 找出差距最大的实体类型
        max_gap_entity = max(gap.items(), key=lambda x: abs(x[1]))
        recommendations.append(f"重点关注'{max_gap_entity[0]}'类型（差距：{max_gap_entity[1]}）")
    
    # 诊断多样性问题
    vocab_diversity = diversity_metrics.get("vocabulary_diversity", 0)
    if vocab_diversity < 0.3:
        issues.append("词汇多样性严重不足")
        if not generation_features.get("use_sentence_diversity", False):
            recommendations.append("启用句子多样化功能")
        else:
            recommendations.append("检查句子多样化配置是否生效")
    
    entity_diversity = diversity_metrics.get("entity_diversity", 0)
    if entity_diversity < 0.5:
        issues.append("实体多样性不足")
        if not generation_features.get("use_entity_diversity", False):
            recommendations.append("启用实体多样化功能")
        else:
            recommendations.append("增加实体候选池的大小")
    
    # 诊断语义多样性
    semantic_diversity = diversity_metrics.get("semantic_diversity", 0)
    if semantic_diversity < 0.4:
        issues.append("语义多样性不足")
        recommendations.append("检查生成模型的温度参数设置")
        recommendations.append("增加prompt模板的多样性")
    
    # 诊断句法多样性
    syntactic_diversity = diversity_metrics.get("syntactic_diversity", 0)
    if syntactic_diversity < 0.5:
        issues.append("句法多样性不足")
        recommendations.append("增加句子模式模板")
        recommendations.append("使用不同的句法结构")
    
    # 综合诊断
    all_features_disabled = not any(generation_features.values())
    if all_features_disabled and len(issues) > 2:
        issues.append("生成功能全部禁用导致质量下降")
        recommendations.append("建议至少启用一项生成功能以提升质量")
    
    # 输出诊断结果
    if issues:
        print(f"\n[诊断报告] 发现 {len(issues)} 个问题：")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
        
        print(f"\n[改进建议] 推荐 {len(recommendations)} 项措施：")
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")
    else:
        print("\n[诊断报告] ✅ 未发现明显问题，当前状态良好")
    
    return issues, recommendations

def save_iteration_data(dataset: List[Dict], iteration: int, gap: Dict[str, int], 
                       diversity_metrics: Dict[str, float], dataset_manager: SynthDatasetManager):
    """保存迭代数据"""
    # 使用增强的数据收集器保存详细的迭代数据
    enhanced_iteration_info = save_enhanced_iteration_data(
        dataset=dataset,
        iteration=iteration,
        gap=gap,
        diversity_metrics=diversity_metrics,
        dataset_manager=dataset_manager,
        strategy_metadata=None  # 可以在这里添加策略元数据
    )
    
    print(f"[✓] 迭代 {iteration} 数据已保存到：{dataset_manager.create_iteration_dir(iteration)}")
    return enhanced_iteration_info

def save_final_dataset(
    dataset: List[Dict], 
    final_metrics: Dict[str, Any], 
    dataset_manager: SynthDatasetManager,
    run_metadata: Dict[str, Any]
):
    """保存最终数据集和评估报告，包含完整的运行元数据
    
    Args:
        dataset: 最终数据集
        final_metrics: 评估指标
        dataset_manager: 数据集管理器实例
        run_metadata: 运行元数据
    
    Returns:
        Tuple[Path, Path]: (数据集路径, 报告路径)
    """
    # 1. 为数据集添加元数据
    dataset_with_meta = {
        "metadata": {
            "generation_time": datetime.now().isoformat(),
            "dataset_size": len(dataset),
            "run_info": run_metadata["run_info"],
            "system_info": run_metadata["system_info"],
            "random_state": run_metadata["random_state"],
            "command_line_args": run_metadata["command_line_args"],
            "config_snapshot": run_metadata["config_snapshot"]
        },
        "data": dataset
    }
    
    # 保存最终数据集
    final_dataset_path = dataset_manager.save_file(
        dataset_with_meta,
        "final_synthetic_dataset",
        "output"
    )
    
    # 2. 创建详细的评估报告
    final_report = {
        "metadata": {
            "generation_time": datetime.now().isoformat(),
            "dataset_size": len(dataset),
            "dataset_path": str(final_dataset_path),
            "run_info": run_metadata["run_info"],
            "system_info": run_metadata["system_info"],
            "random_state": run_metadata["random_state"],
            "command_line_args": run_metadata["command_line_args"]
        },
        "config": {
            "run_config": run_metadata["config_snapshot"],
            "config_files": run_metadata["config_files"]
        },
        "evaluation": {
            "final_metrics": final_metrics,
            "evaluation_time": datetime.now().isoformat()
        }
    }
    
    # 保存评估报告
    report_path = dataset_manager.save_file(
        final_report,
        "synthesis_report",
        "output"
    )
    
    # 3. 创建可读性更好的HTML报告
    html_report = [
        "<!DOCTYPE html>",
        "<html>",
        "<head>",
        "<title>合成数据生成报告</title>",
        "<style>",
        "body { font-family: Arial, sans-serif; margin: 20px; }",
        "h1, h2 { color: #333; }",
        "pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }",
        ".metric { margin: 10px 0; }",
        ".warning { color: #e74c3c; }",
        "</style>",
        "</head>",
        "<body>",
        f"<h1>合成数据生成报告</h1>",
        f"<p>生成时间：{final_report['metadata']['generation_time']}</p>",
        f"<p>运行ID：{final_report['metadata']['run_info']['run_id']}</p>",
        "<h2>数据集信息</h2>",
        f"<p>数据集大小：{final_report['metadata']['dataset_size']} 条记录</p>",
        f"<p>数据集路径：{final_report['metadata']['dataset_path']}</p>",
        "<h2>运行环境</h2>",
        f"<pre>{json.dumps(final_report['metadata']['system_info'], indent=2, ensure_ascii=False)}</pre>",
        "<h2>运行参数</h2>",
        f"<pre>{json.dumps(final_report['metadata']['command_line_args'], indent=2, ensure_ascii=False)}</pre>",
        "<h2>评估指标</h2>",
        f"<pre>{json.dumps(final_report['evaluation']['final_metrics'], indent=2, ensure_ascii=False)}</pre>",
        "</body>",
        "</html>"
    ]
    
    # 保存HTML报告
    html_path = dataset_manager.save_file(
        "\n".join(html_report),
        "synthesis_report",
        "output",
        format="html"
    )
    
    print(f"[✓] 最终数据集已保存到：{final_dataset_path}")
    print(f"[✓] 评估报告已保存到：{report_path}")
    print(f"[✓] HTML报告已保存到：{html_path}")
    
    return final_dataset_path, report_path

def create_summary_report(original_dataset: List[Dict], final_dataset: List[Dict], 
                         iterations: int, final_metrics: Dict[str, Any],
                         dataset_manager: SynthDatasetManager):
    """创建总结报告"""
    
    # 计算统计信息
    original_entity_dist = calculate_entity_distribution(original_dataset)
    final_entity_dist = calculate_entity_distribution(final_dataset)
    
    # 计算目标分布
    target_counts = {}
    target_file = "reproduce/entity_target/privacy_bench_target.json"
    if os.path.exists(target_file):
        with open(target_file, 'r', encoding='utf-8') as f:
            target_counts = json.load(f)
    
    # 生成报告内容
    summary_lines = []
    summary_lines.append("自动合成数据生成流水线总结报告")
    summary_lines.append("=" * 50)
    summary_lines.append("")
    summary_lines.append(f"生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    summary_lines.append(f"运行ID：{dataset_manager.timestamp}")
    summary_lines.append(f"总迭代次数：{iterations}")
    summary_lines.append("")
    
    summary_lines.append("数据集统计：")
    summary_lines.append(f"- 原始数据集：{len(original_dataset)} 条记录")
    summary_lines.append(f"- 最终数据集：{len(final_dataset)} 条记录")
    summary_lines.append(f"- 新增数据：{len(final_dataset) - len(original_dataset)} 条记录")
    summary_lines.append(f"- 目标实体类型总数：{len(target_counts)} 种")
    summary_lines.append("")
    
    if target_counts:
        summary_lines.append("目标实体类型列表：")
        entity_types = list(target_counts.keys())
        summary_lines.append(", ".join(entity_types))
        summary_lines.append("")
        
        summary_lines.append("实体分布对比：")
        for entity_type in entity_types:
            original_count = original_entity_dist.get(entity_type, 0)
            final_count = final_entity_dist.get(entity_type, 0)
            target_count = target_counts.get(entity_type, 0)
            increase = final_count - original_count
            
            status = "✓" if final_count >= target_count else "✗"
            summary_lines.append(f"  {status} {entity_type}: {original_count} -> {final_count} (+{increase}) / {target_count}")
        summary_lines.append("")
    
    # 添加多样性指标
    summary_lines.append("多样性指标：")
    for metric, value in final_metrics.get("diversity_metrics", {}).items():
        summary_lines.append(f"- {metric}：{value:.3f}")
    summary_lines.append("")
    
    # 添加终止条件
    summary_lines.append("终止条件：")
    gap = final_metrics.get("final_gap", {})
    total_gap = sum(gap.values())
    summary_lines.append(f"- 分布偏差检查：{'通过' if total_gap == 0 else '未通过'}")
    
    diversity_passed = final_metrics.get("diversity_passed", False)
    summary_lines.append(f"- 多样性检查：{'通过' if diversity_passed else '未通过'}")
    summary_lines.append("")
    
    # 保存总结报告
    summary_content = "\n".join(summary_lines)
    summary_path = dataset_manager.save_file(
        summary_content,
        "synthesis_summary",
        "logs",
        format="txt"
    )
    
    print(f"[✓] 总结报告已保存到：{summary_path}")

def collect_run_metadata(
    args: argparse.Namespace,
    config: Dict[str, Any],
    dataset_manager: SynthDatasetManager
) -> Dict[str, Any]:
    """收集运行元数据
    
    Args:
        args: 命令行参数
        config: 运行配置
        dataset_manager: 数据集管理器实例
    
    Returns:
        Dict[str, Any]: 运行元数据
    """
    import platform
    import sys
    import random
    import numpy as np
    import torch
    import time
    from datetime import datetime
    
    metadata = {
        "run_info": {
            "timestamp": datetime.now().isoformat(),
            "run_id": dataset_manager.timestamp,
            "workspace": str(dataset_manager.run_dir)
        },
        "system_info": {
            "platform": platform.platform(),
            "python_version": sys.version,
            "numpy_version": np.__version__,
            "torch_version": torch.__version__ if torch else None,
            "cpu_count": os.cpu_count()
        },
        "random_state": {
            "python_seed": random.getstate(),
            "numpy_seed": np.random.get_state()[1][0],
            "torch_seed": torch.initial_seed() if torch else None,
            "timestamp_seed": int(time.time())
        },
        "command_line_args": vars(args),
        "config_snapshot": {
            "default_config": DEFAULT_CONFIG,
            "run_config": config,
            "generation_features": config.get("generation_features", {}),
            "intermediate_results": config.get("intermediate_results", {})
        }
    }
    
    # 加载并保存配置文件快照
    config_files = {
        "balance_config": "src/gen_strat/balance_config.json",
        "ner_config": "src/synth_data/ner_config.json",
        "diversity_config": "src/gen_strat/diversity_config.json",
        "entity_schema": "src/gen_strat/entity_schema.json"
    }
    
    config_snapshots = {}
    for name, path in config_files.items():
        try:
            with open(path, 'r', encoding='utf-8') as f:
                config_snapshots[name] = json.load(f)
        except Exception as e:
            print(f"[警告] 无法加载配置文件 {path}: {e}")
            config_snapshots[name] = None
    
    metadata["config_files"] = config_snapshots
    
    return metadata

def manage_intermediate_results(
    dataset_manager: SynthDatasetManager,
    iteration: int,
    config: Dict[str, Any]
) -> None:
    """管理中间结果存储
    
    Args:
        dataset_manager: 数据集管理器实例
        iteration: 当前迭代次数
        config: 中间结果配置
    """
    if not config.get("enabled", True):
        return
    
    print("\n=== 管理中间结果 ===")
    
    # 1. 获取中间结果目录
    intermediate_dirs = {
        "raw": dataset_manager.dirs["intermediate"] / "raw",
        "by_entity": dataset_manager.dirs["intermediate"] / "by_entity",
        "processed": dataset_manager.dirs["intermediate"] / "processed"
    }
    
    # 2. 检查存储配额
    storage_quotas = config.get("storage_quotas", {})
    for dir_name, dir_path in intermediate_dirs.items():
        if not dir_path.exists():
            continue
            
        # 计算目录大小(MB)
        dir_size = sum(f.stat().st_size for f in dir_path.rglob("*") if f.is_file()) / (1024 * 1024)
        quota = storage_quotas.get(dir_name, float("inf"))
        
        if dir_size > quota:
            print(f"[警告] {dir_name}目录超出配额: {dir_size:.1f}MB > {quota}MB")
            
            # 按时间排序，保留最新的文件
            files = sorted(
                [f for f in dir_path.rglob("*") if f.is_file()],
                key=lambda x: x.stat().st_mtime,
                reverse=True
            )
            
            # 删除旧文件直到满足配额
            current_size = dir_size
            for f in files:
                if current_size <= quota:
                    break
                file_size = f.stat().st_size / (1024 * 1024)
                try:
                    f.unlink()
                    current_size -= file_size
                    print(f"  - 删除旧文件: {f.name} ({file_size:.1f}MB)")
                except Exception as e:
                    print(f"  - 删除失败: {f.name} - {e}")
    
    # 3. 清理旧迭代结果
    max_iterations = config.get("max_iterations_to_keep", 3)
    if max_iterations > 0:
        for dir_name, dir_path in intermediate_dirs.items():
            if not dir_path.exists():
                continue
                
            # 获取迭代目录
            iteration_dirs = [
                d for d in dir_path.iterdir()
                if d.is_dir() and d.name.startswith("iteration_")
            ]
            
            # 按迭代号排序
            iteration_dirs.sort(key=lambda x: int(x.name.split("_")[1]))
            
            # 保留最新的N个迭代
            if len(iteration_dirs) > max_iterations:
                for old_dir in iteration_dirs[:-max_iterations]:
                    try:
                        shutil.rmtree(old_dir)
                        print(f"[清理] 删除旧迭代目录: {old_dir.name}")
                    except Exception as e:
                        print(f"[警告] 删除失败: {old_dir.name} - {e}")
    
    # 4. 压缩处理
    if config.get("compression_enabled", True):
        import tarfile
        compression_format = config.get("compression_format", "gzip")
        
        # 压缩已完成的迭代目录
        for dir_name, dir_path in intermediate_dirs.items():
            if not dir_path.exists():
                continue
                
            for iter_dir in dir_path.iterdir():
                if not iter_dir.is_dir() or not iter_dir.name.startswith("iteration_"):
                    continue
                    
                iter_num = int(iter_dir.name.split("_")[1])
                if iter_num < iteration:  # 只压缩已完成的迭代
                    archive_name = f"{iter_dir.name}.tar.{compression_format}"
                    archive_path = dir_path / archive_name
                    
                    if not archive_path.exists():
                        try:
                            with tarfile.open(archive_path, f"w:{compression_format}") as tar:
                                tar.add(iter_dir, arcname=iter_dir.name)
                            print(f"[压缩] {iter_dir.name} -> {archive_name}")
                            
                            # 压缩成功后删除原目录
                            shutil.rmtree(iter_dir)
                        except Exception as e:
                            print(f"[警告] 压缩失败: {iter_dir.name} - {e}")
    
    print("[✓] 中间结果管理完成")

def find_example_sentences_file() -> str:
    """查找示例句子文件"""
    possible_paths = [
        Path("format-dataset") / "example_sentences.json",
        Path("src") / "synth_data" / "example_sentences.json",
        Path("example_sentences.json")
    ]
    
    for path in possible_paths:
        if path.exists():
            return str(path)
    
    # 如果找不到，返回默认路径
    print("[警告] 未找到示例句子文件，使用默认路径")
    return str(Path("format-dataset") / "example_sentences.json")

def update_evaluation_dataset_path(new_path, config_path="src/synth_eval/evaluation_config.json"):
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    config["dataset_path"] = new_path
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)

def run_quality_evaluation(
    dataset_path: str, 
    output_dir: str, 
    mode: str = "final",  # "iter" | "final" | "compare"
    ref_dataset_path: str = None,
    skip_plots: bool = False,
    skip_naturalness: bool = False,
    naturalness_sample_ratio: float = None  # 自然度评估采样比例
):
    """调用评估脚本，对指定数据集做质量评估
    
    Args:
        dataset_path: 要评估的数据集路径
        output_dir: 输出目录
        mode: 评估模式
            - "iter": 迭代评估(快速质量检查，默认采样10%做自然度评估)
            - "final": 最终评估(完整报告+可视化)
            - "compare": 对比评估(需要参考数据集)
        ref_dataset_path: 参考数据集路径(compare模式必需)
        skip_plots: 是否跳过图表生成
        skip_naturalness: 是否跳过自然度评估(用于加速迭代评估)
        naturalness_sample_ratio: 自然度评估采样比例(0-1)，默认iter模式0.1，其他模式1.0
    """
    # 设置默认采样比例
    if naturalness_sample_ratio is None:
        naturalness_sample_ratio = 0.1 if mode == "iter" else 1.0
    
    cmd = [
        sys.executable, "scripts/3_evaluate_quality.py",
        "--dataset", dataset_path,
        "--output-dir", output_dir,
        "--mode", mode
    ]
    
    # 根据模式添加参数
    if mode == "compare" and ref_dataset_path:
        cmd.extend(["--ref-dataset", ref_dataset_path])
    
    # 可选参数
    if skip_plots:
        cmd.append("--no-plots")
    
    # 自然度评估参数
    if not skip_naturalness:
        cmd.extend([
            "--naturalness-sample-ratio", str(naturalness_sample_ratio),
            "--naturalness-max-samples", "100"  # 限制最大样本数以控制评估成本
        ])
    
    print(f"[评估] 运行评估脚本：{' '.join(cmd)}")
    print(f"[信息] 自然度评估采样比例：{naturalness_sample_ratio:.1%}")
    subprocess.run(cmd, check=True)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="自动合成数据生成流水线")
    parser.add_argument("--dataset", required=True, help="原始数据集路径")
    parser.add_argument("--target-count", type=int, required=True, help="目标数量（每个实体类型）")
    parser.add_argument("--max-iterations", type=int, default=10, help="最大迭代次数")
    parser.add_argument("--batch-size", type=int, default=10, help="每次生成的批次大小")
    parser.add_argument("--distribution-threshold", type=float, default=0.05, help="分布偏差阈值")
    parser.add_argument("--examples", help="示例句子文件路径（可选，会自动检测）")
    
    # 新增：生成特征控制参数
    parser.add_argument("--disable-sentence-diversity", action="store_true", help="禁用句子多样化")
    parser.add_argument("--disable-entity-diversity", action="store_true", help="禁用实体多样化")
    parser.add_argument("--disable-examples", action="store_true", help="禁用示例句子")
    
    # 新增：中间结果管理参数
    parser.add_argument("--disable-intermediate-results", action="store_true", help="禁用中间结果保存")
    parser.add_argument("--keep-iterations", type=int, default=3, help="保留最近N轮的中间结果")
    parser.add_argument("--disable-compression", action="store_true", help="禁用中间结果压缩")
    parser.add_argument("--compression-format", choices=["gzip", "bz2", "xz"], default="gzip", help="压缩格式")
    
    # 新增：配置管理参数
    parser.add_argument("--write-back-configs", action="store_true", help="运行结束时回写配置到全局目录")
    
    args = parser.parse_args()
    
    # 检查数据集文件是否存在
    if not os.path.exists(args.dataset):
        print(f"[错误] 数据集文件不存在：{args.dataset}")
        sys.exit(1)
    
    # 构建生成特征配置
    generation_features = {
        "use_sentence_diversity": not args.disable_sentence_diversity,
        "use_entity_diversity": not args.disable_entity_diversity,
        "use_example_sentences": not args.disable_examples
    }
    
    # 构建中间结果配置
    intermediate_results = DEFAULT_CONFIG["intermediate_results"].copy()
    intermediate_results.update({
        "enabled": not args.disable_intermediate_results,
        "max_iterations_to_keep": args.keep_iterations,
        "compression_enabled": not args.disable_compression,
        "compression_format": args.compression_format
    })
    
    print("=== 开始自动合成数据生成流水线 ===")
    print(f"原始数据集：{args.dataset}")
    print("\n=== 目标配置说明 ===")
    print("1. 评估目标（来自balance_config.json）:")
    print(f"   - 每个实体类型目标数量: {args.target_count}")
    print(f"   - 用途: 评估达标情况、数据剪枝")
    print("2. 生成补充目标（动态计算）:")
    print("   - 每轮迭代时计算与评估目标的差距")
    print("   - 用途: 指导生成新数据的数量")
    print("\n=== 其他配置 ===")
    print(f"最大迭代次数：{args.max_iterations}")
    print(f"批次大小：{args.batch_size}")
    print(f"分布偏差阈值：{args.distribution_threshold}")
    print(f"生成特征配置：")
    print(f"  - 句子多样化：{'启用' if generation_features['use_sentence_diversity'] else '禁用'}")
    print(f"  - 实体多样化：{'启用' if generation_features['use_entity_diversity'] else '禁用'}")
    print(f"  - 示例句子：{'启用' if generation_features['use_example_sentences'] else '禁用'}")
    print(f"中间结果配置：")
    print(f"  - 中间结果保存：{'启用' if intermediate_results['enabled'] else '禁用'}")
    print(f"  - 保留轮数：{intermediate_results['max_iterations_to_keep']}轮")
    print(f"  - 结果压缩：{'启用' if intermediate_results['compression_enabled'] else '禁用'}")
    if intermediate_results['compression_enabled']:
        print(f"  - 压缩格式：{intermediate_results['compression_format']}")
    print(f"  - 存储配额：raw={intermediate_results['storage_quotas']['raw']}MB, by_entity={intermediate_results['storage_quotas']['by_entity']}MB, processed={intermediate_results['storage_quotas']['processed']}MB")
    
    # 更新配置
    config = DEFAULT_CONFIG.copy()
    config["distribution_threshold"] = args.distribution_threshold
    config["batch_size"] = args.batch_size
    config["intermediate_results"] = intermediate_results
    
    try:
        # 1. 初始化数据集管理器
        dataset_manager = SynthDatasetManager()
        run_config = {
            "dataset_path": args.dataset,
            "target_count": args.target_count,
            "max_iterations": args.max_iterations,
            "batch_size": args.batch_size,
            "distribution_threshold": args.distribution_threshold,
            "generation_features": generation_features
        }
        dataset_manager.initialize_run(run_config)
        dataset_manager.update_status("running", {"stage": "initialization"})
        
        # 2. 设置环境
        setup_environment()
        
        # 3. 加载原始数据集并复制到source目录
        original_dataset = load_original_dataset(args.dataset)
        dataset_manager.copy_source_file(args.dataset, "original_dataset.json")
        
        # 4. 加载所有实体类型并计算目标分布
        all_entity_types = load_entity_schema()
        original_distribution = calculate_entity_distribution(original_dataset)
        
        # 更新评估目标配置（仅写入运行目录）
        run_balance_config = update_balance_config(
            args.target_count, 
            all_entity_types,
            dataset_manager,
            write_back=False  # 不回写到全局配置
        )
        
        # 读取运行目录的评估目标作为目标分布
        with run_balance_config.open('r', encoding='utf-8') as f:
            balance_config = json.load(f)
            target_distribution = balance_config.get("entity_type_targets", {})
            tolerance = balance_config.get("distribution_tolerance", 0.1)
        
        # 保存配置信息
        dataset_manager.save_file({
            "entity_types": all_entity_types,
            "target_distribution": target_distribution,
            "original_distribution": original_distribution,
            "tolerance": tolerance
        }, "generation_config", "config")
        
        print(f"\n=== 分布信息 ===")
        print(f"原始实体分布：")
        for entity_type, count in sorted(original_distribution.items()):
            target = target_distribution.get(entity_type, 0)
            print(f"  - {entity_type}: {count} / {target}")
        print(f"\n评估目标（来自balance_config.json）：")
        print(f"  - 实体类型数量: {len(target_distribution)} 种")
        print(f"  - 每种类型目标: {args.target_count}")
        print(f"  - 容差范围: ±{tolerance*100}%")
        
        # 5. 初始化当前数据集
        current_dataset = original_dataset.copy()
        
        # 5.1 添加数据修剪逻辑
        print("=== 执行数据修剪 ===")
        # 读取容差配置
        balance_config_path = Path("src") / "gen_strat" / "balance_config.json"
        tolerance = 0.1  # 默认容差
        if balance_config_path.exists():
            with open(balance_config_path, 'r', encoding='utf-8') as f:
                balance_config = json.load(f)
                tolerance = balance_config.get("distribution_tolerance", 0.1)
        
        trimmed_dataset, trim_stats = trim_dataset_by_entity_count(
            current_dataset, 
            target_distribution, 
            tolerance=tolerance
        )

        if len(trimmed_dataset) < len(current_dataset):
            print(f"数据修剪完成：{len(current_dataset)} -> {len(trimmed_dataset)} 条记录")
            print("修剪统计：")
            for entity_type, stats in trim_stats.items():
                if stats["removed_count"] > 0:
                    print(f"  {entity_type}: {stats['original_count']} -> {stats['trimmed_count']} (移除 {stats['removed_count']} 个)")
            current_dataset = trimmed_dataset
        else:
            print("无需修剪数据")
        
        # 6. 迭代优化
        for iteration in range(1, args.max_iterations + 1):
            print(f"\n=== 迭代 {iteration}/{args.max_iterations} ===")
            dataset_manager.update_status("running", {
                "stage": "iteration",
                "current_iteration": iteration,
                "max_iterations": args.max_iterations
            })
            
            # 6.1 计算当前分布和差距
            current_distribution = calculate_entity_distribution(current_dataset)
            gap = calculate_distribution_gap(current_distribution, target_distribution)
            
            print(f"当前实体分布：{current_distribution}")
            print(f"分布差距：{gap}")
            
            # 6.2 计算多样性指标和诊断问题
            diversity_metrics = calculate_diversity_metrics(current_dataset)
            diagnose_iteration_issues(gap, diversity_metrics, generation_features)

            # 6.3 智能调整生成策略
            generation_features = adjust_generation_strategy(
                gap, diversity_metrics, iteration, generation_features
            )

            # 6.4 更新数据集管理器中的生成特征配置
            metadata = dataset_manager.load_metadata()
            metadata["config"]["generation_features"] = generation_features
            dataset_manager.save_metadata(metadata)
            
            # 6.5 检查是否满足终止条件
            termination_check = check_termination_conditions(gap, diversity_metrics, config)
            
            if termination_check["converged"]:
                print(f"[✓] 满足终止条件，停止迭代")
                if termination_check["warnings"]:
                    print("\n⚠️ 注意：虽然达到收敛条件，但存在以下警告：")
                    for warning in termination_check["warnings"]:
                        print(f"  {warning}")
                break
            
            # 6.6 找到优先补齐的实体类型
            priority_entity, priority_gap = find_priority_entity(gap)
            if not priority_entity or priority_gap == 0:
                print("[✓] 所有实体类型分布已满足要求")
                break

            print(f"优先补齐实体类型：{priority_entity}，差距：{priority_gap}")

            # 6.7 更新配置并生成策略
            # 根据检查结果决定是否重新生成策略
            force_regenerate = (
                not termination_check["distribution_satisfied"] or
                not termination_check["diversity_satisfied"]
            )
            run_balance_config = update_balance_config(
                args.target_count, 
                all_entity_types,
                dataset_manager,
                write_back=False
            )
            update_dataset_path(args.dataset)
            run_strategy_generation(dataset_manager)

            # 6.8 获取策略文件
            strategy_files = get_latest_strategy_files(str(dataset_manager.dirs["strategies"]))
            if not all(strategy_files.values()):
                print("[错误] 策略文件生成失败")
                break

            # 6.9 更新NER配置并生成数据
            examples_path = args.examples if args.examples else find_example_sentences_file()
            print(f"使用示例句子文件：{examples_path}")
            run_ner_config = update_ner_config(
                strategy_files, 
                examples_path, 
                dataset_manager,
                generation_features,
                write_back=False
            )

            # 运行数据生成并获取生成的数据路径
            synthetic_data_path = run_data_generation(dataset_manager)

            # 6.10 加载生成的数据
            synthetic_data = load_synthetic_data(synthetic_data_path)
            print(f"生成了 {len(synthetic_data)} 条合成数据")
            
            # 6.11 合并数据
            current_dataset = merge_datasets(current_dataset, synthetic_data)
            print(f"合并后数据集大小：{len(current_dataset)} 条记录")

            # 6.12 合并后重新剪枝，确保不超过容差范围
            print("\n=== 合并后重新剪枝 ===")
            print("确保每个实体类型不超过评估目标的容差范围")
            
            # 读取最新的评估目标和容差
            with balance_config_path.open('r', encoding='utf-8') as f:
                balance_config = json.load(f)
                target_distribution = balance_config.get("entity_type_targets", {})
                tolerance = balance_config.get("distribution_tolerance", 0.1)
            
            # 使用替换式修剪和随机种子
            random_seed = int(time.time())  # 使用时间戳作为种子
            trimmed_dataset, trim_results = trim_dataset_by_entity_count(
                current_dataset, 
                target_distribution, 
                tolerance=tolerance,
                random_seed=random_seed,
                replacement_mode=True  # 启用替换式修剪
            )

            if len(trimmed_dataset) < len(current_dataset):
                print(f"合并后剪枝完成：{len(current_dataset)} -> {len(trimmed_dataset)} 条记录")
                print("剪枝统计：")
                for entity_type, stats in trim_results["trim_stats"].items():
                    if stats["removed_count"] > 0 or stats["replaced_count"] > 0:
                        print(f"  {entity_type}:")
                        print(f"    - 原始数量：{stats['original_count']}")
                        print(f"    - 目标数量：{stats['target_count']} (±{tolerance*100}%)")
                        print(f"    - 修剪后数量：{stats['trimmed_count']}")
                        if stats["removed_count"] > 0:
                            print(f"    - 删除数量：{stats['removed_count']}")
                        if stats["replaced_count"] > 0:
                            print(f"    - 替换数量：{stats['replaced_count']}")
                current_dataset = trimmed_dataset
            else:
                print("合并后无需剪枝，所有实体类型都在容差范围内")
            
            # 检查是否需要补齐
            coverage_gaps = trim_results.get("coverage_gaps", {})
            if coverage_gaps:
                print("\n[注意] 检测到以下实体类型需要补齐：")
                for entity_type, gap in coverage_gaps.items():
                    print(f"  - {entity_type}: 还需 {gap} 个")
                print("将在下一轮迭代中优先补充这些实体类型")

            # 6.13 保存迭代数据
            save_iteration_data(current_dataset, iteration, gap, diversity_metrics, dataset_manager)
            
            # 6.14 管理中间结果
            manage_intermediate_results(
                dataset_manager=dataset_manager,
                iteration=iteration,
                config=config["intermediate_results"]
            )

            # 6.15 每轮数据生成后自动评估
            eval_output_dir = dataset_manager.dirs["evaluation"] / f"iteration_{iteration:03d}"
            eval_output_dir.mkdir(exist_ok=True)
            print("\n=== 评估数据集 ===")
            print("使用balance_config.json中的评估目标进行评估:")
            print(f"- 目标数量: 每类{args.target_count}个")
            print(f"- 容差范围: ±{tolerance*100}%")
            try:
                # 迭代评估：快速模式，采样10%做自然度评估，跳过图表生成
                run_quality_evaluation(
                    dataset_path=synthetic_data_path,
                    output_dir=str(eval_output_dir),
                    mode="iter",
                    skip_naturalness=False,  # 启用自然度评估
                    naturalness_sample_ratio=0.1,  # 采样10%
                    skip_plots=True,
                    iteration_data=dataset_manager.load_iteration_data()  # 加载迭代数据
                )
            except Exception as e:
                print(f"[警告] 迭代{iteration}评估失败：{e}")
        
        # 7. 计算最终指标
        final_distribution = calculate_entity_distribution(current_dataset)
        final_gap = calculate_distribution_gap(final_distribution, target_distribution)
        final_diversity_metrics = calculate_diversity_metrics(current_dataset)
        
        diversity_passed = all(
            final_diversity_metrics.get(metric, 0) >= threshold
            for metric, threshold in config["diversity_thresholds"].items()
        )
        
        final_metrics = {
            "original_dataset_size": len(original_dataset),
            "final_dataset_size": len(current_dataset),
            "total_iterations": iteration,
            "all_entity_types": all_entity_types,
            "original_distribution": original_distribution,
            "target_distribution": target_distribution,
            "final_distribution": final_distribution,
            "final_gap": final_gap,
            "diversity_metrics": final_diversity_metrics,
            "distribution_passed": sum(final_gap.values()) == 0,
            "diversity_passed": diversity_passed
        }
        
        # 8. 收集运行元数据
        run_metadata = collect_run_metadata(args, config, dataset_manager)
        
        # 9. 保存最终结果
        final_dataset_path, report_path = save_final_dataset(
            dataset=current_dataset, 
            final_metrics=final_metrics, 
            dataset_manager=dataset_manager,
            run_metadata=run_metadata
        )
        
        # 10. 创建总结报告
        create_summary_report(original_dataset, current_dataset, iteration, final_metrics, dataset_manager)
        
        # 10. 最终数据集做一次最终评估
        final_eval_dir = dataset_manager.dirs["evaluation"] / "final"
        final_eval_dir.mkdir(exist_ok=True)
        try:
            # 最终评估：完整模式，包含自然度评估和可视化
            run_quality_evaluation(
                dataset_path=str(final_dataset_path),
                output_dir=str(final_eval_dir),
                mode="final",
                ref_dataset_path=args.dataset,  # 使用原始数据集作为参考
                iteration_data=dataset_manager.load_iteration_data()  # 加载所有迭代数据
            )
        except Exception as e:
            print(f"[警告] 最终评估失败：{e}")
            
        # 10.1 额外进行对比评估
        compare_eval_dir = dataset_manager.dirs["evaluation"] / "comparison"
        compare_eval_dir.mkdir(exist_ok=True)
        try:
            # 对比评估：与原始数据集进行详细对比
            run_quality_evaluation(
                dataset_path=str(final_dataset_path),
                output_dir=str(compare_eval_dir),
                mode="compare",
                ref_dataset_path=args.dataset,
                iteration_data=dataset_manager.load_iteration_data()  # 加载所有迭代数据
            )
        except Exception as e:
            print(f"[警告] 对比评估失败：{e}")
        
        # 11. 更新最终状态
        dataset_manager.update_status("completed", {
            "final_dataset_path": str(final_dataset_path),
            "final_report_path": str(report_path),
            "total_iterations": iteration,
            "final_metrics": final_metrics
        })
        
        # 12. 选择性回写配置
        if args.write_back_configs:
            print("\n=== 回写配置到全局目录 ===")
            # 回写balance_config.json
            write_back_config(
                run_balance_config,
                "src/gen_strat/balance_config.json",
                "balance"
            )
            # 回写ner_config.json
            write_back_config(
                run_ner_config,
                "src/synth_data/ner_config.json",
                "ner"
            )
        
        # 13. 清理旧运行记录
        dataset_manager.cleanup_old_runs(keep_days=30, keep_count=10)
        
        print(f"\n=== 自动合成数据生成流水线完成 ===")
        print(f"运行ID：{dataset_manager.timestamp}")
        print(f"运行目录：{dataset_manager.run_dir}")
        print(f"最终数据集：{final_dataset_path}")
        print(f"最终报告：{report_path}")
        print(f"总共迭代：{iteration} 次")
        
        # 13. 显示运行摘要
        summary = dataset_manager.get_run_summary()
        print(f"磁盘使用量：{summary['disk_usage']}")
        
    except Exception as e:
        # 更新错误状态
        if 'dataset_manager' in locals():
            dataset_manager.update_status("failed", {"error": str(e)})
        
        print(f"\n[错误] 自动合成数据生成流水线失败：{e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 