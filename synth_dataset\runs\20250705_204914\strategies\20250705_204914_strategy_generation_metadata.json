{"start_time": "2025-07-05T20:49:14.296994", "output_directory": "synth_dataset\\runs\\20250705_204914\\strategies", "target_entity_types": ["姓名", "年龄", "性别", "国籍", "职业", "民族", "教育背景", "婚姻状况", "政治倾向", "家庭成员", "工资数额", "投资产品", "税务记录", "信用记录", "实体资产", "交易信息", "疾病", "药物", "临床表现", "医疗程序", "过敏信息", "生育信息", "地理位置", "行程信息"], "total_entity_types": 24, "generation_config": {"dataset_path": "format-dataset/privacy_bench_small.json", "target_count": 50, "max_iterations": 3, "batch_size": 10, "distribution_threshold": 0.05}, "strategy_files": {"target_distribution": "synth_dataset\\runs\\20250705_204914\\strategies\\entity_target\\privacy_bench_target.json", "sentence_diversity": "synth_dataset\\runs\\20250705_204914\\strategies\\sen_diversity\\sen_diversify_value.json", "entity_diversity": "synth_dataset\\runs\\20250705_204914\\strategies\\entity_diversity\\entity_diversity_20250705_204914\\entity_diversity.json"}, "generation_statistics": {"sentence_diversity_attributes": 0, "entity_diversity_pools": 0, "target_distribution_size": 0}, "strategy_statistics": {"strategy_file_sizes": {"target_distribution": 505, "sentence_diversity": 572, "entity_diversity": 309330}, "sentence_diversity_stats": {"total_attributes": 1, "attributes": ["sen_diversify_value"], "total_values": 1, "attribute_value_counts": {"sen_diversify_value": 1}}, "entity_diversity_stats": {"total_entity_types": 24, "entity_type_counts": {"姓名": 431, "年龄": 67, "性别": 65, "国籍": 918, "职业": 421, "民族": 504, "教育背景": 81, "婚姻状况": 74, "政治倾向": 107, "家庭成员": 387, "工资数额": 447, "投资产品": 537, "税务记录": 394, "信用记录": 422, "实体资产": 412, "交易信息": 382, "疾病": 422, "药物": 423, "临床表现": 426, "医疗程序": 424, "过敏信息": 392, "生育信息": 395, "地理位置": 440, "行程信息": 365}, "vanilla_entity_counts": {"姓名": 62, "年龄": 6, "性别": 9, "国籍": 102, "职业": 47, "民族": 56, "教育背景": 18, "婚姻状况": 14, "政治倾向": 20, "家庭成员": 43, "工资数额": 48, "投资产品": 49, "税务记录": 42, "信用记录": 44, "实体资产": 36, "交易信息": 44, "疾病": 49, "药物": 47, "临床表现": 49, "医疗程序": 49, "过敏信息": 49, "生育信息": 36, "地理位置": 47, "行程信息": 47}, "latent_scenario_counts": {"姓名": 8, "年龄": 8, "性别": 8, "国籍": 8, "职业": 8, "民族": 8, "教育背景": 8, "婚姻状况": 8, "政治倾向": 8, "家庭成员": 8, "工资数额": 8, "投资产品": 8, "税务记录": 8, "信用记录": 8, "实体资产": 8, "交易信息": 8, "疾病": 8, "药物": 8, "临床表现": 8, "医疗程序": 8, "过敏信息": 8, "生育信息": 8, "地理位置": 8, "行程信息": 8}, "total_entities": 8936}, "target_distribution_stats": {"total_entity_types": 24, "entity_types": ["姓名", "年龄", "性别", "国籍", "职业", "民族", "教育背景", "婚姻状况", "政治倾向", "家庭成员", "工资数额", "投资产品", "税务记录", "信用记录", "实体资产", "交易信息", "疾病", "药物", "临床表现", "医疗程序", "过敏信息", "生育信息", "地理位置", "行程信息"], "target_counts": {"姓名": 34, "年龄": 50, "性别": 50, "国籍": 28, "职业": 9, "民族": 24, "教育背景": 24, "婚姻状况": 50, "政治倾向": 50, "家庭成员": 50, "工资数额": 50, "投资产品": 50, "税务记录": 50, "信用记录": 50, "实体资产": 50, "交易信息": 50, "疾病": 16, "药物": 38, "临床表现": 29, "医疗程序": 23, "过敏信息": 50, "生育信息": 50, "地理位置": 5, "行程信息": 50}, "total_target_count": 930, "average_target_count": 38.75, "target_count_distribution": {"34": 1, "50": 14, "28": 1, "9": 1, "24": 2, "16": 1, "38": 1, "29": 1, "23": 1, "5": 1}}}}