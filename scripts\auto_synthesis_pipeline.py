#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动合成数据生成流水线
实现迭代优化策略，自动补齐实体分布并提升多样性

使用方法：
python scripts/auto_synthesis_pipeline.py --dataset path/to/original_dataset.json --target-count 100 --max-iterations 10
"""

import os
import sys
import json
import argparse
import shutil
import random
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Any
from collections import Counter, defaultdict
import subprocess

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入数据集管理器
from synth_dataset_manager import SynthDatasetManager

# 导入增强的数据收集器
from evaluation.framework.utils.evaluation_collector import (
    collect_strategy_generation_metadata,
    collect_strategy_statistics,
    collect_detailed_evaluation_metrics,
    save_enhanced_iteration_data,
    collect_ablation_experiment_data
)

# 配置参数
DEFAULT_CONFIG = {
    "distribution_threshold": 0.05,  # 分布偏差阈值 5%
    "diversity_thresholds": {
        "vocabulary_diversity": 0.7,
        "syntactic_diversity": 0.6,
        "semantic_diversity": 0.5,
        "context_diversity": 0.6,
        "entity_diversity": 0.8
    },
    "batch_size": 10,  # 每次生成的批次大小
    "max_retries": 3,  # 最大重试次数
}

def setup_environment():
    """设置环境变量和路径"""
    print("[✓] 环境设置完成")

def load_original_dataset(dataset_path: str) -> List[Dict]:
    """加载原始数据集"""
    print(f"加载原始数据集：{dataset_path}")
    
    with open(dataset_path, 'r', encoding='utf-8') as f:
        dataset = json.load(f)
    
    print(f"原始数据集包含 {len(dataset)} 条记录")
    return dataset

def load_entity_schema() -> List[str]:
    """从entity_schema.json加载所有实体类型"""
    try:
        with open('src/gen_strat/entity_schema.json', 'r', encoding='utf-8') as f:
            schema = json.load(f)
        
        # 返回扁平化的实体类型列表
        entity_types = schema.get('flat_list', [])
        print(f"[✓] 从entity_schema.json加载了 {len(entity_types)} 种实体类型")
        return entity_types
        
    except Exception as e:
        print(f"[错误] 加载entity_schema.json失败：{e}")
        raise

def calculate_entity_distribution(dataset: List[Dict]) -> Dict[str, int]:
    """计算当前实体分布"""
    entity_counts = Counter()
    
    for item in dataset:
        for span in item.get("label", []):
            entity_type = span.get("type")
            if entity_type:
                entity_counts[entity_type] += 1
    
    return dict(entity_counts)

def calculate_distribution_gap(current_dist: Dict[str, int], target_dist: Dict[str, int]) -> Dict[str, int]:
    """计算当前分布与评估目标的差距
    
    Args:
        current_dist: 当前数据集的实体分布
        target_dist: 评估目标分布（来自balance_config.json）
    
    Returns:
        Dict[str, int]: 每个实体类型的差距（正值表示需要补充，负值表示需要修剪）
    """
    gap = {}
    
    for entity_type, target_count in target_dist.items():
        current_count = current_dist.get(entity_type, 0)
        # 计算实际差距（可以是正值或负值）
        gap[entity_type] = target_count - current_count
    
    return gap

def find_priority_entity(gap: Dict[str, int]) -> Tuple[str, int]:
    """找到需要优先补齐的实体类型（差距最大者）"""
    if not gap:
        return None, 0
    
    # 找到差距最大的实体类型
    priority_entity = max(gap.items(), key=lambda x: x[1])
    return priority_entity

def trim_dataset_by_entity_count(dataset: List[Dict], target_distribution: Dict[str, int], tolerance: float = 0.1):
    """根据评估目标修剪数据集，允许一定容差
    
    使用balance_config.json中的评估目标作为参考，对数据集进行修剪：
    1. 如果某实体类型数量超过目标值+容差，进行随机删减
    2. 如果某实体类型数量低于目标值-容差，在后续迭代中补充
    
    Args:
        dataset: 要修剪的数据集
        target_distribution: 评估目标分布（来自balance_config.json）
        tolerance: 容差比例（默认0.1，即±10%）
    
    Returns:
        Tuple[List[Dict], Dict[str, Dict]]: 修剪后的数据集和修剪统计信息
    
    注意：这里使用的是评估目标，不是生成补充目标
    """
    print("[说明] 使用评估目标（来自balance_config.json）进行数据修剪")
    print(f"    - 容差范围：±{tolerance*100}%")
    
    trimmed_dataset = []
    trim_stats = {}
    
    # 计算当前分布
    current_distribution = {}
    for item in dataset:
        for entity in item.get("label", []):
            entity_type = entity.get("type")
            if entity_type:
                current_distribution[entity_type] = current_distribution.get(entity_type, 0) + 1
    
    # 初始化统计信息
    for entity_type in target_distribution:
        current_count = current_distribution.get(entity_type, 0)
        trim_stats[entity_type] = {
            "original_count": current_count,
            "target_count": target_distribution[entity_type],
            "trimmed_count": current_count,
            "removed_count": 0
        }
    
    # 对每个实体类型进行修剪
    for entity_type, target_count in target_distribution.items():
        current_count = current_distribution.get(entity_type, 0)
        max_allowed = int(target_count * (1 + tolerance))
        
        if current_count > max_allowed:
            # 需要修剪的数量
            to_remove = current_count - max_allowed
            removed_count = 0
            
            # 随机选择要保留的样本
            items_with_entity = [
                item for item in dataset 
                if any(e.get("type") == entity_type for e in item.get("label", []))
            ]
            
            # 确保不会删除太多
            to_keep = max_allowed
            if len(items_with_entity) > to_keep:
                import random
                kept_items = set(random.sample(items_with_entity, to_keep))
                
                # 更新统计信息
                removed_count = len(items_with_entity) - len(kept_items)
                trim_stats[entity_type]["trimmed_count"] = len(kept_items)
                trim_stats[entity_type]["removed_count"] = removed_count
                
                # 只保留未被删除的样本
                dataset = [
                    item for item in dataset
                    if not any(e.get("type") == entity_type for e in item.get("label", []))
                    or item in kept_items
                ]
    
    # 输出修剪统计
    print("\n=== 修剪统计 ===")
    for entity_type, stats in trim_stats.items():
        if stats["removed_count"] > 0:
            print(f"实体类型：{entity_type}")
            print(f"  - 原始数量：{stats['original_count']}")
            print(f"  - 目标数量：{stats['target_count']} (±{tolerance*100}%)")
            print(f"  - 修剪后数量：{stats['trimmed_count']}")
            print(f"  - 删除数量：{stats['removed_count']}")
    
    return dataset, trim_stats

def update_balance_config(target_count: int, entity_types: List[str]):
    """更新balance_config.json中的评估目标数量
    
    这个目标数量用于：
    1. 评估数据集是否达标
    2. 剪枝时判断上下限
    3. 作为最终目标参考
    
    注意：这不是生成补充数据时的目标数量，生成目标由run_strategy_generation动态计算
    
    Args:
        target_count: 每个实体类型的目标数量
        entity_types: 所有实体类型列表
    """
    balance_config_path = Path("src") / "gen_strat" / "balance_config.json"
    
    try:
        with balance_config_path.open('r', encoding='utf-8') as f:
            config = json.load(f)
        
        config["balance_target_per_type"] = target_count
        
        # 更新所有实体类型的目标数量
        entity_targets = {}
        for entity_type in entity_types:
            entity_targets[entity_type] = target_count
        
        config["entity_type_targets"] = entity_targets
        
        with balance_config_path.open('w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print(f"[✓] 已更新评估目标配置 balance_config.json")
        print(f"    - 每个实体类型目标数量：{target_count}")
        print(f"    - 实体类型总数：{len(entity_types)} 种")
        print(f"    - 用途：评估达标情况、数据剪枝")
        
    except Exception as e:
        print(f"[错误] 更新balance_config.json失败：{e}")
        raise

def update_dataset_path(dataset_path: str):
    """更新1-gen_distribution_target.py中的数据集路径"""
    script_path = "src/gen_strat/1-gen_distribution_target.py"
    
    try:
        with open(script_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新数据集路径
        old_path = 'format-dataset/privacy_bench.json'
        content = content.replace(old_path, dataset_path)
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"[✓] 已更新数据集路径：{dataset_path}")
        
    except Exception as e:
        print(f"[错误] 更新数据集路径失败：{e}")
        raise

def run_strategy_generation(dataset_manager: SynthDatasetManager):
    """运行策略生成，计算本次迭代需要补充的实体数量
    
    这个函数会：
    1. 分析当前数据集与评估目标的差距
    2. 生成一个新的目标分布文件，指示本次需要补充的数量
    3. 这个补充目标不同于评估目标，它是动态计算的增量
    
    Args:
        dataset_manager: 数据集管理器实例
    
    Returns:
        Tuple[Dict[str, str], Dict]: 策略文件路径字典和策略元数据
    """
    print("=== 生成合成策略 ===")
    print("[说明] 即将生成补充目标:")
    print("    - 基于当前数据集与评估目标的差距")
    print("    - 用于指导本次迭代生成多少新数据")
    print("    - 这不是最终评估标准，评估标准在balance_config.json中")
    
    try:
        # 使用dataset_manager的strategies目录作为输出目录
        output_dir = str(dataset_manager.dirs["strategies"])
        print(f"[信息] 策略输出目录：{output_dir}")
        
        # 获取数据集路径（从dataset_manager的配置中获取）
        metadata = dataset_manager.load_metadata()
        dataset_path = metadata.get("config", {}).get("dataset_path", "format-dataset/privacy_bench_small.json")
        
        # 加载实体类型和评估目标
        all_entity_types = load_entity_schema()
        balance_config_path = Path("src") / "gen_strat" / "balance_config.json"
        
        # 读取评估目标
        with balance_config_path.open('r', encoding='utf-8') as f:
            balance_config = json.load(f)
            target_distribution = balance_config.get("entity_type_targets", {})
            tolerance = balance_config.get("distribution_tolerance", 0.1)
        
        # 收集策略生成元数据
        strategy_metadata = collect_strategy_generation_metadata(
            output_dir=output_dir,
            entity_types=all_entity_types,
            generation_config=metadata.get("config", {})
        )
        
        # 运行目标分布生成
        import importlib.util
        spec = importlib.util.spec_from_file_location("gen_distribution_target", "src/gen_strat/1-gen_distribution_target.py")
        gen_distribution_target = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(gen_distribution_target)
        
        # 调用目标分布生成，传递输出目录和数据集路径
        target_file = gen_distribution_target.main(output_dir, dataset_path)
        print(f"[✓] 补充目标分布文件已生成：{target_file}")
        print(f"    - 该文件指示本次迭代需要新生成的数据量")
        print(f"    - 基于评估目标（来自balance_config.json）计算得出")
        strategy_metadata["strategy_files"]["target_distribution"] = target_file
        
        # 运行多样化策略生成
        spec = importlib.util.spec_from_file_location("gen_diversity", "src/gen_strat/2-gen_diversity.py")
        gen_diversity = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(gen_diversity)
        
        # 调用多样化策略生成，传递输出目录并启用全局缓存
        gen_diversity.main(output_dir, use_global_cache=True)
        print(f"[✓] 多样化策略已生成到：{output_dir}")
        
        # 获取生成的策略文件
        strategy_files = get_latest_strategy_files(output_dir)
        strategy_metadata["strategy_files"].update(strategy_files)
        
        # 收集策略文件统计信息
        strategy_statistics = collect_strategy_statistics(strategy_files)
        strategy_metadata["strategy_statistics"] = strategy_statistics
        
        # 保存策略元数据
        metadata_file = dataset_manager.save_file(
            strategy_metadata,
            "strategy_generation_metadata",
            "strategies"
        )
        print(f"[✓] 策略元数据已保存到：{metadata_file}")
        
        print("[✓] 策略生成完成")
        print(f"[✓] 策略文件已保存到：{dataset_manager.dirs['strategies']}")
        print(f"[✓] 全局缓存已更新，下次运行将复用句子多样化和实体多样化数据")
        
        return strategy_files, strategy_metadata
        
    except Exception as e:
        print(f"[错误] 策略生成失败：{e}")
        raise

def get_latest_strategy_files(base_dir: str = "reproduce") -> Dict[str, str]:
    """获取策略文件路径"""
    files = {}
    
    # 使用 Path 对象处理路径
    base_path = Path(base_dir)
    
    # 获取目标分布文件（固定文件名）
    target_file = base_path / "entity_target" / "privacy_bench_target.json"
    print(f"[信息] 目标分布文件：{target_file}")
    if target_file.exists():
        files['target_distribution'] = str(target_file)
    
    # 获取句子多样化文件（固定文件名）
    sen_diversity_file = base_path / "sen_diversity" / "sen_diversify_value.json"
    print(f"[信息] 句子多样化文件：{sen_diversity_file}")
    if sen_diversity_file.exists():
        files['sentence_diversity'] = str(sen_diversity_file)
    
    # 获取最新的实体多样化目录
    entity_diversity_dir = base_path / "entity_diversity"
    if entity_diversity_dir.exists():
        entity_dirs = [d for d in entity_diversity_dir.iterdir() if d.is_dir()]
        if entity_dirs:
            latest_entity = sorted(entity_dirs)[-1]
            entity_json_file = latest_entity / 'entity_diversity.json'
            if entity_json_file.exists():
                files['entity_diversity'] = str(entity_json_file)
            else:
                print(f"[警告] 未找到实体多样化文件: {entity_json_file}")
    
    print(f"[✓] 找到策略文件：{len(files)} 个")
    for strategy_type, path in files.items():
        print(f"  - {strategy_type}: {path}")
    
    return files

def update_ner_config(strategy_files: Dict[str, str], examples_path: str, generation_features: Dict = None):
    """更新ner_config.json中的文件路径和生成特征配置"""
    ner_config_path = "src/synth_data/ner_config.json"
    
    try:
        # 读取当前配置
        with open(ner_config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 更新文件路径
        config.update({
            "target_file": strategy_files.get("target", ""),
            "sentence_diversity_file": strategy_files.get("sentence_diversity", ""),
            "entity_diversity_dir": strategy_files.get("entity_diversity", ""),
            "example_sentences_file": examples_path
        })
        
        # 更新生成特征配置
        if generation_features is None:
            generation_features = {
                "use_sentence_diversity": True,
                "use_entity_diversity": True,
                "use_example_sentences": True
            }
        config["generation_features"] = generation_features
        
        # 保存更新后的配置
        with open(ner_config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
            
        print("[✓] 已更新ner_config.json")
        
    except Exception as e:
        print(f"[错误] 更新ner_config.json失败：{e}")
        raise

def run_data_generation(dataset_manager: SynthDatasetManager):
    """运行数据生成"""
    print("=== 生成合成数据 ===")
    
    try:
        # 获取最新的策略文件
        strategy_files = get_latest_strategy_files(str(dataset_manager.dirs["strategies"]))
        
        # 获取示例句子文件
        examples_path = find_example_sentences_file()
        
        # 从配置中获取生成特征设置
        generation_features = dataset_manager.load_metadata().get("config", {}).get("generation_features", {
            "use_sentence_diversity": True,
            "use_entity_diversity": True,
            "use_example_sentences": True
        })
        
        # 更新NER配置
        update_ner_config(strategy_files, examples_path, generation_features)
        
        # 导入并运行NER数据生成模块
        sys.path.insert(0, str(project_root / "src" / "synth_data"))
        from ner_data_generation import main as ner_main
        
        # 运行数据生成
        ner_main(strategy_dir=str(dataset_manager.dirs["strategies"]))
        
        print("[✓] 合成数据生成完成")
        
    except Exception as e:
        print(f"[错误] 合成数据生成失败：{e}")
        raise

def get_latest_synthetic_data() -> str:
    """获取最新的合成数据文件路径"""
    # 查找原始输出目录中的最新文件
    origin_dir = Path("synth_dataset") / "origin"
    if origin_dir.exists():
        files = [f for f in origin_dir.iterdir() if f.suffix == '.json']
        if files:
            latest_file = max(files, key=lambda f: f.stat().st_ctime)
            return str(latest_file)
    return None

def load_synthetic_data(data_path: str) -> List[Dict]:
    """加载合成数据"""
    with open(data_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def merge_datasets(original_data: List[Dict], synthetic_data: List[Dict]) -> List[Dict]:
    """合并原始数据和合成数据"""
    return original_data + synthetic_data

def calculate_diversity_metrics(dataset: List[Dict]) -> Dict[str, float]:
    """计算多样性指标"""
    # 这里应该调用实际的多样性计算函数
    # 暂时返回模拟数据
    
    texts = [item["text"] for item in dataset]
    
    # 词汇多样性（简化计算）
    all_words = []
    for text in texts:
        words = text.split()
        all_words.extend(words)
    
    vocab_diversity = len(set(all_words)) / len(all_words) if all_words else 0
    
    # 其他多样性指标需要实际实现
    return {
        "vocabulary_diversity": vocab_diversity,
        "syntactic_diversity": 0.8,  # 模拟值
        "semantic_diversity": 0.7,   # 模拟值
        "context_diversity": 0.6,    # 模拟值
        "entity_diversity": 0.75     # 模拟值
    }

def check_termination_conditions(
    gap: Dict[str, int], 
    diversity_metrics: Dict[str, float],
    config: Dict
) -> bool:
    """修正后的终止条件检查 - 使用更严格的标准"""
    
    # 1. 修正分布检查：使用容差而非精确匹配
    try:
        with open('src/gen_strat/balance_config.json', 'r', encoding='utf-8') as f:
            balance_config = json.load(f)
    except FileNotFoundError:
        print("[警告] 未找到balance_config.json，使用默认容差")
        balance_config = {"distribution_tolerance": 0.1, "entity_type_targets": {}}
    
    tolerance = balance_config.get("distribution_tolerance", 0.1)
    target_counts = balance_config.get("entity_type_targets", {})
    
    # 检查每个实体类型是否在容差范围内
    within_tolerance_count = 0
    total_entity_types = len(target_counts)
    out_of_tolerance_types = []
    
    for entity_type, target_count in target_counts.items():
        current_gap = gap.get(entity_type, 0)
        tolerance_range = target_count * tolerance
        
        # 如果gap在容差范围内，认为满足要求
        if abs(current_gap) <= tolerance_range:
            within_tolerance_count += 1
        else:
            out_of_tolerance_types.append({
                "type": entity_type,
                "gap": current_gap,
                "tolerance": tolerance_range
            })
    
    # 更严格的分布要求：
    # 1. 至少95%的实体类型在容差范围内
    # 2. 超出容差的实体类型的gap不能超过目标数量的20%
    tolerance_ratio = within_tolerance_count / total_entity_types if total_entity_types > 0 else 0
    severe_deviation = any(abs(item["gap"]) > target_counts[item["type"]] * 0.2 
                         for item in out_of_tolerance_types)
    
    distribution_satisfied = tolerance_ratio >= 0.95 and not severe_deviation
    
    # 2. 修正多样性检查：根据当前配置调整阈值
    generation_features = config.get("generation_features", {})
    
    # 如果全功能禁用，降低多样性要求
    all_features_disabled = not any(generation_features.values())
    if all_features_disabled:
        adjusted_thresholds = {
            "vocabulary_diversity": 0.3,    # 从0.7降到0.3
            "syntactic_diversity": 0.5,     # 保持不变
            "semantic_diversity": 0.4,      # 保持不变
            "context_diversity": 0.5,       # 保持不变
            "entity_diversity": 0.5,        # 从0.8降到0.5
        }
        print("[信息] 检测到全功能禁用，使用调整后的多样性阈值")
    else:
        adjusted_thresholds = config["diversity_thresholds"]
    
    diversity_satisfied = True
    failed_metrics = []
    
    for metric, threshold in adjusted_thresholds.items():
        current_value = diversity_metrics.get(metric, 0)
        if current_value < threshold:
            diversity_satisfied = False
            failed_metrics.append(f"{metric}: {current_value:.3f} < {threshold}")
    
    # 3. 输出详细信息
    print(f"\n分布检查：{'通过' if distribution_satisfied else '未通过'}")
    print(f"  - 容差范围内实体类型：{within_tolerance_count}/{total_entity_types} ({tolerance_ratio:.1%})")
    print(f"  - 使用容差：±{tolerance:.1%}")
    if out_of_tolerance_types:
        print("  - 超出容差的实体类型:")
        for item in out_of_tolerance_types:
            print(f"    * {item['type']}: gap={item['gap']} (容差范围：±{item['tolerance']:.1f})")
    
    print(f"\n多样性检查：{'通过' if diversity_satisfied else '未通过'}")
    if failed_metrics:
        print(f"  - 未达标指标：{', '.join(failed_metrics)}")
    
    return distribution_satisfied and diversity_satisfied

def adjust_generation_strategy(gap: Dict[str, int], diversity_metrics: Dict[str, float], 
                              iteration: int, generation_features: Dict[str, bool]) -> Dict[str, bool]:
    """根据当前状态智能调整生成策略"""
    
    original_features = generation_features.copy()
    
    # 如果词汇多样性过低，启用句子多样化
    vocab_diversity = diversity_metrics.get("vocabulary_diversity", 0)
    if vocab_diversity < 0.4:
        if not generation_features.get("use_sentence_diversity", False):
            print(f"[策略调整] 词汇多样性过低({vocab_diversity:.3f})，启用句子多样化")
            generation_features["use_sentence_diversity"] = True
    
    # 如果实体多样性过低，启用实体多样化
    entity_diversity = diversity_metrics.get("entity_diversity", 0)
    if entity_diversity < 0.6:
        if not generation_features.get("use_entity_diversity", False):
            print(f"[策略调整] 实体多样性过低({entity_diversity:.3f})，启用实体多样化")
            generation_features["use_entity_diversity"] = True
    
    # 如果分布差距过大且迭代超过3次，启用示例句子
    total_gap = sum(abs(g) for g in gap.values())
    if total_gap > 50 and iteration > 3:
        if not generation_features.get("use_example_sentences", False):
            print(f"[策略调整] 分布差距过大({total_gap})且迭代>3次，启用示例句子")
            generation_features["use_example_sentences"] = True
    
    # 如果连续多次迭代无改善，逐步启用所有功能
    if iteration > 5:
        all_features = ["use_sentence_diversity", "use_entity_diversity", "use_example_sentences"]
        disabled_features = [f for f in all_features if not generation_features.get(f, False)]
        
        if disabled_features:
            # 每次启用一个功能
            feature_to_enable = disabled_features[0]
            print(f"[策略调整] 第{iteration}次迭代，启用{feature_to_enable}以提升效果")
            generation_features[feature_to_enable] = True
    
    # 检查是否有变化
    if generation_features != original_features:
        changed_features = []
        for key, value in generation_features.items():
            if value != original_features.get(key, False):
                status = "启用" if value else "禁用"
                changed_features.append(f"{key}:{status}")
        print(f"[策略更新] 已调整: {', '.join(changed_features)}")
    else:
        print("[策略保持] 当前策略无需调整")
    
    return generation_features

def diagnose_iteration_issues(gap: Dict[str, int], diversity_metrics: Dict[str, float], 
                             generation_features: Dict[str, bool]) -> Tuple[List[str], List[str]]:
    """诊断迭代过程中的问题"""
    
    issues = []
    recommendations = []
    
    # 诊断分布问题
    total_gap = sum(abs(g) for g in gap.values())
    if total_gap > 100:
        issues.append("实体分布差距过大")
        recommendations.append("考虑增加目标数量或调整生成策略")
        
        # 找出差距最大的实体类型
        max_gap_entity = max(gap.items(), key=lambda x: abs(x[1]))
        recommendations.append(f"重点关注'{max_gap_entity[0]}'类型（差距：{max_gap_entity[1]}）")
    
    # 诊断多样性问题
    vocab_diversity = diversity_metrics.get("vocabulary_diversity", 0)
    if vocab_diversity < 0.3:
        issues.append("词汇多样性严重不足")
        if not generation_features.get("use_sentence_diversity", False):
            recommendations.append("启用句子多样化功能")
        else:
            recommendations.append("检查句子多样化配置是否生效")
    
    entity_diversity = diversity_metrics.get("entity_diversity", 0)
    if entity_diversity < 0.5:
        issues.append("实体多样性不足")
        if not generation_features.get("use_entity_diversity", False):
            recommendations.append("启用实体多样化功能")
        else:
            recommendations.append("增加实体候选池的大小")
    
    # 诊断语义多样性
    semantic_diversity = diversity_metrics.get("semantic_diversity", 0)
    if semantic_diversity < 0.4:
        issues.append("语义多样性不足")
        recommendations.append("检查生成模型的温度参数设置")
        recommendations.append("增加prompt模板的多样性")
    
    # 诊断句法多样性
    syntactic_diversity = diversity_metrics.get("syntactic_diversity", 0)
    if syntactic_diversity < 0.5:
        issues.append("句法多样性不足")
        recommendations.append("增加句子模式模板")
        recommendations.append("使用不同的句法结构")
    
    # 综合诊断
    all_features_disabled = not any(generation_features.values())
    if all_features_disabled and len(issues) > 2:
        issues.append("生成功能全部禁用导致质量下降")
        recommendations.append("建议至少启用一项生成功能以提升质量")
    
    # 输出诊断结果
    if issues:
        print(f"\n[诊断报告] 发现 {len(issues)} 个问题：")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
        
        print(f"\n[改进建议] 推荐 {len(recommendations)} 项措施：")
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")
    else:
        print("\n[诊断报告] ✅ 未发现明显问题，当前状态良好")
    
    return issues, recommendations

def save_iteration_data(dataset: List[Dict], iteration: int, gap: Dict[str, int], 
                       diversity_metrics: Dict[str, float], dataset_manager: SynthDatasetManager):
    """保存迭代数据"""
    # 使用增强的数据收集器保存详细的迭代数据
    enhanced_iteration_info = save_enhanced_iteration_data(
        dataset=dataset,
        iteration=iteration,
        gap=gap,
        diversity_metrics=diversity_metrics,
        dataset_manager=dataset_manager,
        strategy_metadata=None  # 可以在这里添加策略元数据
    )
    
    print(f"[✓] 迭代 {iteration} 数据已保存到：{dataset_manager.create_iteration_dir(iteration)}")
    return enhanced_iteration_info

def save_final_dataset(dataset: List[Dict], final_metrics: Dict[str, Any], 
                      dataset_manager: SynthDatasetManager):
    """保存最终数据集"""
    # 保存最终数据集
    final_dataset_path = dataset_manager.save_file(
        dataset,
        "final_synthetic_dataset",
        "output"
    )
    
    # 保存最终报告
    final_report = {
        "generation_time": datetime.now().isoformat(),
        "dataset_size": len(dataset),
        "final_metrics": final_metrics,
        "dataset_path": str(final_dataset_path)
    }
    
    report_path = dataset_manager.save_file(
        final_report,
        "synthesis_report",
        "output"
    )
    
    print(f"[✓] 最终数据集已保存到：{final_dataset_path}")
    print(f"[✓] 最终报告已保存到：{report_path}")
    
    return final_dataset_path, report_path

def create_summary_report(original_dataset: List[Dict], final_dataset: List[Dict], 
                         iterations: int, final_metrics: Dict[str, Any],
                         dataset_manager: SynthDatasetManager):
    """创建总结报告"""
    
    # 计算统计信息
    original_entity_dist = calculate_entity_distribution(original_dataset)
    final_entity_dist = calculate_entity_distribution(final_dataset)
    
    # 计算目标分布
    target_counts = {}
    target_file = "reproduce/entity_target/privacy_bench_target.json"
    if os.path.exists(target_file):
        with open(target_file, 'r', encoding='utf-8') as f:
            target_counts = json.load(f)
    
    # 生成报告内容
    summary_lines = []
    summary_lines.append("自动合成数据生成流水线总结报告")
    summary_lines.append("=" * 50)
    summary_lines.append("")
    summary_lines.append(f"生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    summary_lines.append(f"运行ID：{dataset_manager.timestamp}")
    summary_lines.append(f"总迭代次数：{iterations}")
    summary_lines.append("")
    
    summary_lines.append("数据集统计：")
    summary_lines.append(f"- 原始数据集：{len(original_dataset)} 条记录")
    summary_lines.append(f"- 最终数据集：{len(final_dataset)} 条记录")
    summary_lines.append(f"- 新增数据：{len(final_dataset) - len(original_dataset)} 条记录")
    summary_lines.append(f"- 目标实体类型总数：{len(target_counts)} 种")
    summary_lines.append("")
    
    if target_counts:
        summary_lines.append("目标实体类型列表：")
        entity_types = list(target_counts.keys())
        summary_lines.append(", ".join(entity_types))
        summary_lines.append("")
        
        summary_lines.append("实体分布对比：")
        for entity_type in entity_types:
            original_count = original_entity_dist.get(entity_type, 0)
            final_count = final_entity_dist.get(entity_type, 0)
            target_count = target_counts.get(entity_type, 0)
            increase = final_count - original_count
            
            status = "✓" if final_count >= target_count else "✗"
            summary_lines.append(f"  {status} {entity_type}: {original_count} -> {final_count} (+{increase}) / {target_count}")
        summary_lines.append("")
    
    # 添加多样性指标
    summary_lines.append("多样性指标：")
    for metric, value in final_metrics.get("diversity_metrics", {}).items():
        summary_lines.append(f"- {metric}：{value:.3f}")
    summary_lines.append("")
    
    # 添加终止条件
    summary_lines.append("终止条件：")
    gap = final_metrics.get("final_gap", {})
    total_gap = sum(gap.values())
    summary_lines.append(f"- 分布偏差检查：{'通过' if total_gap == 0 else '未通过'}")
    
    diversity_passed = final_metrics.get("diversity_passed", False)
    summary_lines.append(f"- 多样性检查：{'通过' if diversity_passed else '未通过'}")
    summary_lines.append("")
    
    # 保存总结报告
    summary_content = "\n".join(summary_lines)
    summary_path = dataset_manager.save_file(
        summary_content,
        "synthesis_summary",
        "logs",
        format="txt"
    )
    
    print(f"[✓] 总结报告已保存到：{summary_path}")

def find_example_sentences_file() -> str:
    """查找示例句子文件"""
    possible_paths = [
        Path("format-dataset") / "example_sentences.json",
        Path("src") / "synth_data" / "example_sentences.json",
        Path("example_sentences.json")
    ]
    
    for path in possible_paths:
        if path.exists():
            return str(path)
    
    # 如果找不到，返回默认路径
    print("[警告] 未找到示例句子文件，使用默认路径")
    return str(Path("format-dataset") / "example_sentences.json")

def update_evaluation_dataset_path(new_path, config_path="src/synth_eval/evaluation_config.json"):
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    config["dataset_path"] = new_path
    with open(config_path, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)

def run_quality_evaluation(dataset_path, output_dir, is_final=True, original_dataset_path=None):
    """调用评估脚本，对指定数据集做质量评估（含自然度）"""
    cmd = [
        sys.executable, "scripts/3_evaluate_quality.py",
        "--dataset", dataset_path,
        "--output-dir", output_dir
    ]
    if is_final:
        cmd.append("--is-final")
    if original_dataset_path:
        cmd.extend(["--original-dataset", original_dataset_path])
    print(f"[评估] 运行评估脚本：{' '.join(cmd)}")
    subprocess.run(cmd, check=True)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="自动合成数据生成流水线")
    parser.add_argument("--dataset", required=True, help="原始数据集路径")
    parser.add_argument("--target-count", type=int, required=True, help="目标数量（每个实体类型）")
    parser.add_argument("--max-iterations", type=int, default=10, help="最大迭代次数")
    parser.add_argument("--batch-size", type=int, default=10, help="每次生成的批次大小")
    parser.add_argument("--distribution-threshold", type=float, default=0.05, help="分布偏差阈值")
    parser.add_argument("--examples", help="示例句子文件路径（可选，会自动检测）")
    
    # 新增：生成特征控制参数
    parser.add_argument("--disable-sentence-diversity", action="store_true", help="禁用句子多样化")
    parser.add_argument("--disable-entity-diversity", action="store_true", help="禁用实体多样化")
    parser.add_argument("--disable-examples", action="store_true", help="禁用示例句子")
    
    args = parser.parse_args()
    
    # 检查数据集文件是否存在
    if not os.path.exists(args.dataset):
        print(f"[错误] 数据集文件不存在：{args.dataset}")
        sys.exit(1)
    
    # 构建生成特征配置
    generation_features = {
        "use_sentence_diversity": not args.disable_sentence_diversity,
        "use_entity_diversity": not args.disable_entity_diversity,
        "use_example_sentences": not args.disable_examples
    }
    
    print("=== 开始自动合成数据生成流水线 ===")
    print(f"原始数据集：{args.dataset}")
    print("\n=== 目标配置说明 ===")
    print("1. 评估目标（来自balance_config.json）:")
    print(f"   - 每个实体类型目标数量: {args.target_count}")
    print(f"   - 用途: 评估达标情况、数据剪枝")
    print("2. 生成补充目标（动态计算）:")
    print("   - 每轮迭代时计算与评估目标的差距")
    print("   - 用途: 指导生成新数据的数量")
    print("\n=== 其他配置 ===")
    print(f"最大迭代次数：{args.max_iterations}")
    print(f"批次大小：{args.batch_size}")
    print(f"分布偏差阈值：{args.distribution_threshold}")
    print(f"生成特征配置：")
    print(f"  - 句子多样化：{'启用' if generation_features['use_sentence_diversity'] else '禁用'}")
    print(f"  - 实体多样化：{'启用' if generation_features['use_entity_diversity'] else '禁用'}")
    print(f"  - 示例句子：{'启用' if generation_features['use_example_sentences'] else '禁用'}")
    
    # 更新配置
    config = DEFAULT_CONFIG.copy()
    config["distribution_threshold"] = args.distribution_threshold
    config["batch_size"] = args.batch_size
    
    try:
        # 1. 初始化数据集管理器
        dataset_manager = SynthDatasetManager()
        run_config = {
            "dataset_path": args.dataset,
            "target_count": args.target_count,
            "max_iterations": args.max_iterations,
            "batch_size": args.batch_size,
            "distribution_threshold": args.distribution_threshold,
            "generation_features": generation_features
        }
        dataset_manager.initialize_run(run_config)
        dataset_manager.update_status("running", {"stage": "initialization"})
        
        # 2. 设置环境
        setup_environment()
        
        # 3. 加载原始数据集并复制到source目录
        original_dataset = load_original_dataset(args.dataset)
        dataset_manager.copy_source_file(args.dataset, "original_dataset.json")
        
        # 4. 加载所有实体类型并计算目标分布
        all_entity_types = load_entity_schema()
        original_distribution = calculate_entity_distribution(original_dataset)
        
        # 更新评估目标配置
        update_balance_config(args.target_count, all_entity_types)
        
        # 读取评估目标作为目标分布
        balance_config_path = Path("src") / "gen_strat" / "balance_config.json"
        with balance_config_path.open('r', encoding='utf-8') as f:
            balance_config = json.load(f)
            target_distribution = balance_config.get("entity_type_targets", {})
            tolerance = balance_config.get("distribution_tolerance", 0.1)
        
        # 保存配置信息
        dataset_manager.save_file({
            "entity_types": all_entity_types,
            "target_distribution": target_distribution,
            "original_distribution": original_distribution,
            "tolerance": tolerance
        }, "generation_config", "config")
        
        print(f"\n=== 分布信息 ===")
        print(f"原始实体分布：")
        for entity_type, count in sorted(original_distribution.items()):
            target = target_distribution.get(entity_type, 0)
            print(f"  - {entity_type}: {count} / {target}")
        print(f"\n评估目标（来自balance_config.json）：")
        print(f"  - 实体类型数量: {len(target_distribution)} 种")
        print(f"  - 每种类型目标: {args.target_count}")
        print(f"  - 容差范围: ±{tolerance*100}%")
        
        # 5. 初始化当前数据集
        current_dataset = original_dataset.copy()
        
        # 5.1 添加数据修剪逻辑
        print("=== 执行数据修剪 ===")
        # 读取容差配置
        balance_config_path = Path("src") / "gen_strat" / "balance_config.json"
        tolerance = 0.1  # 默认容差
        if balance_config_path.exists():
            with open(balance_config_path, 'r', encoding='utf-8') as f:
                balance_config = json.load(f)
                tolerance = balance_config.get("distribution_tolerance", 0.1)
        
        trimmed_dataset, trim_stats = trim_dataset_by_entity_count(
            current_dataset, 
            target_distribution, 
            tolerance=tolerance
        )

        if len(trimmed_dataset) < len(current_dataset):
            print(f"数据修剪完成：{len(current_dataset)} -> {len(trimmed_dataset)} 条记录")
            print("修剪统计：")
            for entity_type, stats in trim_stats.items():
                if stats["removed_count"] > 0:
                    print(f"  {entity_type}: {stats['original_count']} -> {stats['trimmed_count']} (移除 {stats['removed_count']} 个)")
            current_dataset = trimmed_dataset
        else:
            print("无需修剪数据")
        
        # 6. 迭代优化
        for iteration in range(1, args.max_iterations + 1):
            print(f"\n=== 迭代 {iteration}/{args.max_iterations} ===")
            dataset_manager.update_status("running", {
                "stage": "iteration",
                "current_iteration": iteration,
                "max_iterations": args.max_iterations
            })
            
            # 6.1 计算当前分布和差距
            current_distribution = calculate_entity_distribution(current_dataset)
            gap = calculate_distribution_gap(current_distribution, target_distribution)
            
            print(f"当前实体分布：{current_distribution}")
            print(f"分布差距：{gap}")
            
            # 6.2 计算多样性指标和诊断问题
            diversity_metrics = calculate_diversity_metrics(current_dataset)
            issues, recommendations = diagnose_iteration_issues(gap, diversity_metrics, generation_features)
            
            # 6.3 智能调整生成策略
            generation_features = adjust_generation_strategy(
                gap, diversity_metrics, iteration, generation_features
            )
            
            # 6.4 检查是否满足终止条件
            if check_termination_conditions(gap, diversity_metrics, config):
                print(f"[✓] 满足终止条件，停止迭代")
                break
            
            # 6.5 找到优先补齐的实体类型
            priority_entity, priority_gap = find_priority_entity(gap)
            if not priority_entity or priority_gap == 0:
                print("[✓] 所有实体类型分布已满足要求")
                break
            
            print(f"优先补齐实体类型：{priority_entity}，差距：{priority_gap}")
            
            # 6.6 更新配置并生成策略
            update_balance_config(args.target_count, all_entity_types)
            update_dataset_path(args.dataset)
            strategy_files, strategy_metadata = run_strategy_generation(dataset_manager)
            
            # 6.7 获取策略文件
            strategy_files = get_latest_strategy_files(str(dataset_manager.dirs["strategies"]))
            if not all(strategy_files.values()):
                print("[错误] 策略文件生成失败")
                break
            
            # 6.8 更新NER配置并生成数据
            examples_path = args.examples if args.examples else find_example_sentences_file()
            print(f"使用示例句子文件：{examples_path}")
            update_ner_config(strategy_files, examples_path, generation_features)
            generated_data_path = run_data_generation(dataset_manager)
            
            # 6.9 获取生成的数据
            synthetic_data_path = get_latest_synthetic_data()
            if not synthetic_data_path:
                print("[错误] 未找到生成的合成数据")
                break
            
            synthetic_data = load_synthetic_data(synthetic_data_path)
            print(f"生成了 {len(synthetic_data)} 条合成数据")
            
            # 6.10 合并数据
            current_dataset = merge_datasets(current_dataset, synthetic_data)
            print(f"合并后数据集大小：{len(current_dataset)} 条记录")
            
            # 6.10.1 合并后重新剪枝，确保不超过容差范围
            print("\n=== 合并后重新剪枝 ===")
            print("确保每个实体类型不超过评估目标的容差范围")
            
            # 读取最新的评估目标和容差
            with balance_config_path.open('r', encoding='utf-8') as f:
                balance_config = json.load(f)
                target_distribution = balance_config.get("entity_type_targets", {})
                tolerance = balance_config.get("distribution_tolerance", 0.1)
            
            trimmed_dataset, trim_stats = trim_dataset_by_entity_count(
                current_dataset, 
                target_distribution, 
                tolerance=tolerance
            )

            if len(trimmed_dataset) < len(current_dataset):
                print(f"合并后剪枝完成：{len(current_dataset)} -> {len(trimmed_dataset)} 条记录")
                print("剪枝统计：")
                for entity_type, stats in trim_stats.items():
                    if stats["removed_count"] > 0:
                        print(f"  {entity_type}:")
                        print(f"    - 原始数量：{stats['original_count']}")
                        print(f"    - 目标数量：{stats['target_count']} (±{tolerance*100}%)")
                        print(f"    - 修剪后数量：{stats['trimmed_count']}")
                        print(f"    - 删除数量：{stats['removed_count']}")
                current_dataset = trimmed_dataset
            else:
                print("合并后无需剪枝，所有实体类型都在容差范围内")
            
            # 6.11 保存迭代数据
            save_iteration_data(current_dataset, iteration, gap, diversity_metrics, dataset_manager)
            
            # 6.12 每轮数据生成后自动评估
            eval_output_dir = dataset_manager.dirs["evaluation"] / f"iteration_{iteration:03d}"
            eval_output_dir.mkdir(exist_ok=True)
            print("\n=== 评估数据集 ===")
            print("使用balance_config.json中的评估目标进行评估:")
            print(f"- 目标数量: 每类{args.target_count}个")
            print(f"- 容差范围: ±{tolerance*100}%")
            try:
                run_quality_evaluation(synthetic_data_path, str(eval_output_dir), is_final=False, original_dataset_path=args.dataset)
            except Exception as e:
                print(f"[警告] 迭代{iteration}评估失败：{e}")
        
        # 7. 计算最终指标
        final_distribution = calculate_entity_distribution(current_dataset)
        final_gap = calculate_distribution_gap(final_distribution, target_distribution)
        final_diversity_metrics = calculate_diversity_metrics(current_dataset)
        
        diversity_passed = all(
            final_diversity_metrics.get(metric, 0) >= threshold
            for metric, threshold in config["diversity_thresholds"].items()
        )
        
        final_metrics = {
            "original_dataset_size": len(original_dataset),
            "final_dataset_size": len(current_dataset),
            "total_iterations": iteration,
            "all_entity_types": all_entity_types,
            "original_distribution": original_distribution,
            "target_distribution": target_distribution,
            "final_distribution": final_distribution,
            "final_gap": final_gap,
            "diversity_metrics": final_diversity_metrics,
            "distribution_passed": sum(final_gap.values()) == 0,
            "diversity_passed": diversity_passed
        }
        
        # 8. 保存最终结果
        final_dataset_path, report_path = save_final_dataset(current_dataset, final_metrics, dataset_manager)
        
        # 9. 创建总结报告
        create_summary_report(original_dataset, current_dataset, iteration, final_metrics, dataset_manager)
        
        # 10. 最终数据集做一次最终评估
        final_eval_dir = dataset_manager.dirs["evaluation"] / "final"
        final_eval_dir.mkdir(exist_ok=True)
        try:
            run_quality_evaluation(str(final_dataset_path), str(final_eval_dir), is_final=True, original_dataset_path=args.dataset)
        except Exception as e:
            print(f"[警告] 最终评估失败：{e}")
        
        # 11. 更新最终状态
        dataset_manager.update_status("completed", {
            "final_dataset_path": str(final_dataset_path),
            "final_report_path": str(report_path),
            "total_iterations": iteration,
            "final_metrics": final_metrics
        })
        
        # 12. 清理旧运行记录
        dataset_manager.cleanup_old_runs(keep_days=30, keep_count=10)
        
        print(f"\n=== 自动合成数据生成流水线完成 ===")
        print(f"运行ID：{dataset_manager.timestamp}")
        print(f"运行目录：{dataset_manager.run_dir}")
        print(f"最终数据集：{final_dataset_path}")
        print(f"最终报告：{report_path}")
        print(f"总共迭代：{iteration} 次")
        
        # 13. 显示运行摘要
        summary = dataset_manager.get_run_summary()
        print(f"磁盘使用量：{summary['disk_usage']}")
        
    except Exception as e:
        # 更新错误状态
        if 'dataset_manager' in locals():
            dataset_manager.update_status("failed", {"error": str(e)})
        
        print(f"\n[错误] 自动合成数据生成流水线失败：{e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 