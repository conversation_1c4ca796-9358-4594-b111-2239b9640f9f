# quality_evaluation.py
# 生成数据质量检测模块：均衡性验证和多样性验证

import json
import os
import re
import math
import numpy as np
from collections import Counter, defaultdict
from typing import Dict, List, Tuple, Any
from datetime import datetime
import jieba
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import time
import requests
from pathlib import Path

# =====================
# 配置和常量
# =====================

# 评估阈值配置
EVALUATION_THRESHOLDS = {
    "balance": {
        "distribution_tolerance": 0.15,  # 分布差异容忍度
        "min_coverage_ratio": 0.8,       # 最小覆盖率
    },
    "diversity": {
        "vocabulary_diversity": 0.6,     # 词汇多样性阈值
        "syntactic_diversity": 0.5,      # 句法多样性阈值
        "semantic_diversity": 0.4,       # 语义多样性阈值
        "context_diversity": 0.5,        # 上下文多样性阈值
        "entity_diversity": 0.7,         # 实体多样性阈值
        "min_unique_entities": 0.3,      # 最小唯一实体比例
    }
}

# 句法模式模板
SYNTACTIC_PATTERNS = [
    r'[^。！？]*[。！？]',  # 基本句子模式
    r'[^，；]*[，；][^，；]*[，；]',  # 包含逗号、分号的句子
    r'[^（）]*（[^）]*）[^（）]*',  # 包含括号的句子
    r'[^""]*"[^"]*"[^""]*',  # 包含引号的句子
    r'[^：]*：[^：]*',  # 包含冒号的句子
]

NATURALNESS_SCORE_THRESHOLD = 6.0  # 低于此分数视为不自然
NATURALNESS_LOW_RATE_THRESHOLD = 0.1  # 低分句子比例超过此阈值则警告

# =====================
# 数据加载函数
# =====================

def load_generated_dataset(dataset_path: str) -> List[Dict]:
    """加载生成的NER数据集"""
    with open(dataset_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def load_target_distribution(strategy_dir="reproduce") -> Dict[str, int]:
    """加载目标分布策略 - 从balance_config.json中读取"""
    # 优先从balance_config.json读取目标分布
    balance_config_file = Path("src/gen_strat/balance_config.json")
    if balance_config_file.exists():
        try:
            with open(balance_config_file, 'r', encoding='utf-8') as f:
                balance_config = json.load(f)
                target_distribution = balance_config.get("entity_type_targets", {})
                if target_distribution:
                    print(f"[✓] 从balance_config.json加载目标分布：{len(target_distribution)}个实体类型")
                    return target_distribution
        except Exception as e:
            print(f"[警告] 从balance_config.json加载目标分布失败：{e}")
    
    # 备用方案：从strategy_dir读取
    target_file = Path(strategy_dir) / "entity_target" / "privacy_bench_target.json"
    if target_file.exists():
        try:
            with open(target_file, 'r', encoding='utf-8') as f:
                result = json.load(f)
                print(f"[✓] 从{target_file}加载目标分布：{len(result)}个实体类型")
                return result
        except Exception as e:
            print(f"[警告] 从{target_file}加载目标分布失败：{e}")
    
    raise FileNotFoundError(f"未找到目标分布文件。尝试的路径：\n1. {balance_config_file}\n2. {target_file}")

def load_entity_schema() -> Dict[str, List[str]]:
    """加载实体模式定义"""
    with open('src/gen_strat/entity_schema.json', 'r', encoding='utf-8') as f:
        schema = json.load(f)
        return schema.get('categories', {})

def load_evaluation_config(config_path="src/synth_eval/evaluation_config.json"):
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

# =====================
# 均衡性验证函数
# =====================

def calculate_entity_distribution(dataset: List[Dict]) -> Dict[str, Dict[str, Any]]:
    """计算实体分布统计"""
    entity_counts = Counter()
    entity_texts = defaultdict(list)
    
    for item in dataset:
        text = item["text"]
        for span in item["label"]:
            entity_type = span["type"]
            entity_text = span["entity"]
            entity_counts[entity_type] += 1
            entity_texts[entity_type].append(entity_text)
    
    total_entities = sum(entity_counts.values())
    distribution = {}
    
    for entity_type, count in entity_counts.items():
        distribution[entity_type] = {
            "count": count,
            "percentage": count / total_entities if total_entities > 0 else 0,
            "unique_entities": len(set(entity_texts[entity_type])),
            "total_occurrences": count
        }
    
    return distribution

def calculate_target_distribution(target_counts: Dict[str, int]) -> Dict[str, float]:
    """计算目标分布比例"""
    total_target = sum(target_counts.values())
    target_distribution = {}
    
    for entity_type, count in target_counts.items():
        target_distribution[entity_type] = count / total_target if total_target > 0 else 0
    
    return target_distribution

def compare_distributions(
    actual_dist: Dict[str, Dict[str, Any]], 
    target_dist: Dict[str, float]
) -> Dict[str, Any]:
    """修正后的分布比较函数 - 基于绝对数量和容差评估
    
    优先使用balance_config.json中的绝对数量目标，只有在没有配置文件时才使用比例转换。
    
    Args:
        actual_dist: 实际分布，包含每个实体类型的统计信息
        target_dist: 目标分布（可能是比例或绝对数量）
    """
    
    # 优先从balance_config.json读取目标分布
    try:
        with open('src/gen_strat/balance_config.json', 'r', encoding='utf-8') as f:
            balance_config = json.load(f)
            target_counts = balance_config.get("entity_type_targets", {})
            tolerance = balance_config.get("distribution_tolerance", 0.1)
            
            # 如果balance_config.json中有目标数量，直接使用
            if target_counts:
                print(f"[✓] 使用balance_config.json中的绝对数量目标")
                print(f"    - 每个实体类型目标数量：{next(iter(target_counts.values()))}个")
                print(f"    - 容差范围：±{tolerance*100}%")
                # 不再进行比例转换
                is_proportion = False
            else:
                raise FileNotFoundError("balance_config.json中没有目标数量配置")
                
    except FileNotFoundError:
        print("[警告] 未找到balance_config.json或配置不完整，尝试使用比例转换")
        balance_config = {"distribution_tolerance": 0.1, "entity_type_targets": {}}
        tolerance = 0.1
        target_counts = {}
        # 检查target_dist是否为比例格式
        is_proportion = all(v <= 1.0 for v in target_dist.values() if v > 0)
    
    # 只有在没有balance_config.json的情况下才进行比例转换
    if not target_counts and is_proportion:
        print("[信息] 检测到目标分布为比例格式，进行转换")
        # 计算总实体数量，将比例转换为绝对数量
        total_actual = sum(actual_dist.get(et, {}).get("count", 0) for et in target_dist.keys())
        target_counts = {et: int(prop * total_actual) for et, prop in target_dist.items()}
        print(f"[信息] 转换后的目标数量：")
        for et, count in target_counts.items():
            print(f"    - {et}: {count}个")
    elif not target_counts:
        # 如果既没有配置文件也不是比例，直接使用target_dist
        target_counts = target_dist
    
    comparison = {
        "overall_score": 0.0,
        "entity_scores": {},
        "coverage_ratio": 0.0,
        "distribution_differences": {},
        "missing_entities": [],
        "excess_entities": [],
        "within_tolerance_count": 0,  # 在容差范围内的实体类型数量
        "tolerance_ratio": 0.0,       # 容差范围内的比例
        "tolerance_used": tolerance   # 实际使用的容差值
    }
    
    # 计算覆盖率
    covered_entities = set(actual_dist.keys())
    target_entities = set(target_dist.keys())
    comparison["coverage_ratio"] = len(covered_entities & target_entities) / len(target_entities)
    
    # 修正评分逻辑：基于绝对数量而非比例
    total_score = 0.0
    valid_comparisons = 0
    within_tolerance_count = 0
    
    for entity_type in target_entities:
        actual_count = actual_dist.get(entity_type, {}).get("count", 0)
        target_count = target_counts.get(entity_type, 0)
        
        # 计算容差范围
        tolerance_range = target_count * tolerance
        min_acceptable = max(0, target_count - tolerance_range)
        max_acceptable = target_count + tolerance_range
        
        # 判断是否在容差范围内
        within_tolerance = min_acceptable <= actual_count <= max_acceptable
        if within_tolerance:
            within_tolerance_count += 1
        
        # 计算得分：在容差范围内得满分，超出范围按距离扣分
        if within_tolerance:
            score = 1.0
        else:
            # 计算超出容差的程度
            if actual_count < min_acceptable:
                deviation = min_acceptable - actual_count
            else:  # actual_count > max_acceptable
                deviation = actual_count - max_acceptable
            
            # 根据偏差程度计算得分（偏差越大得分越低）
            max_penalty = target_count  # 最大惩罚
            score = max(0, 1 - deviation / max_penalty)
        
        comparison["entity_scores"][entity_type] = score
        comparison["distribution_differences"][entity_type] = {
            "actual": actual_count,
            "target": target_count,
            "min_acceptable": min_acceptable,
            "max_acceptable": max_acceptable,
            "within_tolerance": within_tolerance,
            "deviation": abs(actual_count - target_count),
            "deviation_ratio": abs(actual_count - target_count) / target_count if target_count > 0 else 0
        }
        
        total_score += score
        valid_comparisons += 1
    
    # 计算整体得分
    if valid_comparisons > 0:
        comparison["overall_score"] = total_score / valid_comparisons
    
    comparison["within_tolerance_count"] = within_tolerance_count
    comparison["tolerance_ratio"] = within_tolerance_count / len(target_entities) if target_entities else 0
    
    # 识别缺失和多余的实体类型
    comparison["missing_entities"] = list(target_entities - covered_entities)
    comparison["excess_entities"] = list(covered_entities - target_entities)
    
    return comparison

def evaluate_balance(dataset: List[Dict], target_counts: Dict[str, int]) -> Dict[str, Any]:
    """执行均衡性验证"""
    print("=== 执行均衡性验证 ===")
    
    # 计算实际分布
    actual_distribution = calculate_entity_distribution(dataset)
    print(f"实际实体分布：{len(actual_distribution)} 种实体类型")
    
    # 计算目标分布
    target_distribution = calculate_target_distribution(target_counts)
    print(f"目标实体分布：{len(target_distribution)} 种实体类型")
    
    # 比较分布
    comparison = compare_distributions(actual_distribution, target_distribution)
    
    # 评估结果 - 使用修正后的评估标准
    thresholds = EVALUATION_THRESHOLDS["balance"]
    
    # 修正通过标准：基于容差范围内的实体类型比例
    tolerance_threshold = 0.8  # 80%的实体类型在容差范围内即可通过
    balance_passed = (
        comparison["tolerance_ratio"] >= tolerance_threshold and
        comparison["coverage_ratio"] >= thresholds["min_coverage_ratio"]
    )
    
    evaluation_result = {
        "passed": balance_passed,
        "overall_score": comparison["overall_score"],
        "coverage_ratio": comparison["coverage_ratio"],
        "tolerance_ratio": comparison["tolerance_ratio"],
        "within_tolerance_count": comparison["within_tolerance_count"],
        "tolerance_used": comparison["tolerance_used"],
        "actual_distribution": actual_distribution,
        "target_distribution": target_distribution,
        "comparison": comparison,
        "thresholds": thresholds,
        "tolerance_threshold": tolerance_threshold
    }
    
    # 输出详细结果
    print(f"均衡性验证结果：{'通过' if balance_passed else '未通过'}")
    print(f"整体得分：{comparison['overall_score']:.3f}")
    print(f"覆盖率：{comparison['coverage_ratio']:.3f}")
    print(f"容差范围内实体类型：{comparison['within_tolerance_count']}/{len(target_distribution)}个 ({comparison['tolerance_ratio']:.1%})")
    print(f"使用容差：±{comparison['tolerance_used']:.1%}")
    
    # 输出未达标的实体类型详情
    failed_entities = []
    for entity_type, diff_info in comparison["distribution_differences"].items():
        if not diff_info["within_tolerance"]:
            failed_entities.append(f"{entity_type}: {diff_info['actual']}/{diff_info['target']} (偏差{diff_info['deviation']})")
    
    if failed_entities:
        print(f"未达标实体类型：{failed_entities}")
    
    if comparison["missing_entities"]:
        print(f"缺失实体类型：{comparison['missing_entities']}")
    if comparison["excess_entities"]:
        print(f"多余实体类型：{comparison['excess_entities']}")
    
    return evaluation_result

# =====================
# 多样性验证函数
# =====================

def calculate_vocabulary_diversity(texts: List[str]) -> float:
    """计算词汇多样性"""
    all_words = []
    for text in texts:
        words = jieba.lcut(text)
        all_words.extend(words)
    
    if not all_words:
        return 0.0
    
    unique_words = set(all_words)
    return len(unique_words) / len(all_words)

def calculate_syntactic_diversity(texts: List[str]) -> float:
    """计算句法多样性"""
    pattern_matches = defaultdict(int)
    total_sentences = len(texts)
    
    if total_sentences == 0:
        return 0.0
    
    for text in texts:
        for i, pattern in enumerate(SYNTACTIC_PATTERNS):
            if re.search(pattern, text):
                pattern_matches[i] += 1
    
    # 计算模式使用的多样性
    used_patterns = len([count for count in pattern_matches.values() if count > 0])
    return used_patterns / len(SYNTACTIC_PATTERNS)

def calculate_semantic_diversity(texts: List[str]) -> float:
    """计算语义多样性（基于TF-IDF向量相似度）"""
    if len(texts) < 2:
        return 0.0
    
    try:
        # 使用TF-IDF向量化
        vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words=None,
            ngram_range=(1, 2)
        )
        
        tfidf_matrix = vectorizer.fit_transform(texts)
        
        # 计算余弦相似度矩阵
        similarity_matrix = cosine_similarity(tfidf_matrix)
        
        # 计算平均相似度（排除对角线）
        n = len(similarity_matrix)
        total_similarity = 0
        count = 0
        
        for i in range(n):
            for j in range(i + 1, n):
                total_similarity += similarity_matrix[i][j]
                count += 1
        
        if count == 0:
            return 0.0
        
        avg_similarity = total_similarity / count
        # 相似度越低，多样性越高
        return 1 - avg_similarity
        
    except Exception as e:
        print(f"[警告] 语义多样性计算失败：{e}")
        return 0.0

def calculate_context_diversity(dataset: List[Dict]) -> float:
    """计算上下文多样性"""
    context_patterns = defaultdict(int)
    total_items = len(dataset)
    
    if total_items == 0:
        return 0.0
    
    for item in dataset:
        text = item["text"]
        
        # 分析上下文模式
        if len(item["label"]) > 1:
            context_patterns["multi_entity"] += 1
        
        if re.search(r'[，；]', text):
            context_patterns["with_punctuation"] += 1
        
        if re.search(r'[（）]', text):
            context_patterns["with_parentheses"] += 1
        
        if re.search(r'[""]', text):
            context_patterns["with_quotes"] += 1
        
        if re.search(r'[：]', text):
            context_patterns["with_colon"] += 1
    
    # 计算模式多样性
    used_patterns = len([count for count in context_patterns.values() if count > 0])
    return used_patterns / len(context_patterns)

def calculate_entity_diversity(dataset: List[Dict]) -> Dict[str, float]:
    """计算实体多样性"""
    entity_stats = defaultdict(lambda: {"total": 0, "unique": set()})
    
    for item in dataset:
        for span in item["label"]:
            entity_type = span["type"]
            entity_text = span["entity"]
            entity_stats[entity_type]["total"] += 1
            entity_stats[entity_type]["unique"].add(entity_text)
    
    diversity_scores = {}
    for entity_type, stats in entity_stats.items():
        if stats["total"] > 0:
            diversity_scores[entity_type] = len(stats["unique"]) / stats["total"]
        else:
            diversity_scores[entity_type] = 0.0
    
    # 计算整体实体多样性
    if diversity_scores:
        overall_diversity = sum(diversity_scores.values()) / len(diversity_scores)
    else:
        overall_diversity = 0.0
    
    return {
        "overall": overall_diversity,
        "by_type": diversity_scores
    }

def evaluate_diversity(dataset: List[Dict]) -> Dict[str, Any]:
    """执行多样性验证"""
    print("=== 执行多样性验证 ===")
    
    texts = [item["text"] for item in dataset]
    
    # 计算各项多样性指标
    vocab_diversity = calculate_vocabulary_diversity(texts)
    syntactic_diversity = calculate_syntactic_diversity(texts)
    semantic_diversity = calculate_semantic_diversity(texts)
    context_diversity = calculate_context_diversity(dataset)
    entity_diversity = calculate_entity_diversity(dataset)
    
    print(f"词汇多样性：{vocab_diversity:.3f}")
    print(f"句法多样性：{syntactic_diversity:.3f}")
    print(f"语义多样性：{semantic_diversity:.3f}")
    print(f"上下文多样性：{context_diversity:.3f}")
    print(f"实体多样性：{entity_diversity['overall']:.3f}")
    
    # 评估结果
    thresholds = EVALUATION_THRESHOLDS["diversity"]
    diversity_passed = (
        vocab_diversity >= thresholds["vocabulary_diversity"] and
        syntactic_diversity >= thresholds["syntactic_diversity"] and
        semantic_diversity >= thresholds["semantic_diversity"] and
        context_diversity >= thresholds["context_diversity"] and
        entity_diversity["overall"] >= thresholds["entity_diversity"]
    )
    
    evaluation_result = {
        "passed": diversity_passed,
        "vocabulary_diversity": vocab_diversity,
        "syntactic_diversity": syntactic_diversity,
        "semantic_diversity": semantic_diversity,
        "context_diversity": context_diversity,
        "entity_diversity": entity_diversity,
        "thresholds": thresholds
    }
    
    print(f"多样性验证结果：{'通过' if diversity_passed else '未通过'}")
    
    return evaluation_result

# =====================
# 句子自然度检测函数
# =====================

def evaluate_sentence_naturalness(sentences, config=None, dataset_path=None, output_dir=None):
    """直接HTTP调用智谱glm-4.1v-thinking-flashx API对每个句子进行自然度打分，并保存详细评分结果"""
    if config is None:
        config = load_evaluation_config()
    nat_cfg = config.get("naturalness", {})
    api_url = nat_cfg.get("api_url")
    api_key_env = nat_cfg.get("api_key_env", "ZHIPUAI_API_KEY")
    api_key = os.getenv(api_key_env)
    max_retry = nat_cfg.get("max_retry", 3)
    sleep_time = nat_cfg.get("sleep_time", 1.5)
    score_threshold = nat_cfg.get("score_threshold", 6.0)
    
    if not api_url or not api_key:
        print("[警告] 未配置智谱API url或API key，跳过句子自然度检测。")
        return [10.0]*len(sentences), []
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 新增：创建详细评分结果列表
    detailed_scores = []
    scores = []
    low_score_examples = []
    
    for idx, sent in enumerate(sentences):
        score_detail = {
            "sentence_id": idx + 1,
            "sentence": sent,
            "score": 0.0,
            "evaluation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "status": "success",
            "metadata": {
                "length": len(sent),
                "has_punctuation": bool(re.search(r'[，。！？；：]', sent))
            }
        }
        
        prompt = f"请对下面这句话的自然度进行打分，0分表示极其生硬、完全不符合真实语言，10分表示非常自然、完全符合真实世界表达。只返回一个数字分数，不要解释。\n句子：{sent}"
        data = {
            "model": "glm-4.1v-thinking-flashx",
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.2
        }
        
        for attempt in range(max_retry):
            try:
                resp = requests.post(api_url, headers=headers, json=data, timeout=20)
                resp.raise_for_status()
                result = resp.json()
                content = result["choices"][0]["message"]["content"].strip()
                score = float(re.findall(r"\d+(?:\.\d+)?", content)[0])
                
                # 更新评分详情
                score_detail["score"] = score
                score_detail["attempt"] = attempt + 1
                
                scores.append(score)
                if score < score_threshold and len(low_score_examples) < 10:
                    low_score_examples.append({"sentence": sent, "score": score})
                break
                
            except Exception as e:
                score_detail["status"] = "failed"
                score_detail["error"] = str(e)
                if attempt == max_retry - 1:
                    print(f"[警告] 句子自然度打分失败：{sent}，错误：{e}")
                    scores.append(0.0)
                else:
                    time.sleep(sleep_time)
        
        detailed_scores.append(score_detail)
        
        # 每100个句子保存一次进度
        if (idx + 1) % 100 == 0:
            print(f"已完成 {idx + 1}/{len(sentences)} 个句子的评分")
    
    # 保存详细评分结果
    save_detailed_scores(detailed_scores, dataset_path, output_dir)
    
    return scores, low_score_examples

def save_detailed_scores(detailed_scores: List[Dict], dataset_path: str = None, output_dir: str = None):
    """保存详细的句子评分结果"""
    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 构建输出目录
    if output_dir:
        # 如果指定了输出目录，使用指定目录
        base_output_dir = Path(output_dir)
    else:
        # 否则使用默认的synth_dataset/evaluation_results结构
        base_output_dir = Path("synth_dataset/evaluation_results") / timestamp
    
    base_output_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取数据集名称（如果提供了数据集路径）
    dataset_name = "unknown_dataset"
    if dataset_path:
        dataset_name = os.path.splitext(os.path.basename(dataset_path))[0]
    
    # 构建完整的评分报告
    score_report = {
        "evaluation_time": timestamp,
        "dataset_name": dataset_name,
        "summary": {
            "total_sentences": len(detailed_scores),
            "average_score": sum(item["score"] for item in detailed_scores) / len(detailed_scores),
            "success_rate": sum(1 for item in detailed_scores if item["status"] == "success") / len(detailed_scores),
            "score_distribution": {
                "excellent": sum(1 for item in detailed_scores if item["score"] >= 8.0),
                "good": sum(1 for item in detailed_scores if 6.0 <= item["score"] < 8.0),
                "fair": sum(1 for item in detailed_scores if 4.0 <= item["score"] < 6.0),
                "poor": sum(1 for item in detailed_scores if item["score"] < 4.0)
            }
        },
        "detailed_scores": detailed_scores
    }
    
    # 保存详细报告
    detailed_report_path = base_output_dir / f"sentence_scores_detailed_{timestamp}.json"
    with open(detailed_report_path, 'w', encoding='utf-8') as f:
        json.dump(score_report, f, ensure_ascii=False, indent=2)
    
    # 保存简要统计报告（txt格式）
    summary_path = base_output_dir / f"evaluation_summary_{timestamp}.txt"
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write(f"句子自然度评估报告\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"评估时间：{timestamp}\n")
        f.write(f"数据集：{dataset_name}\n\n")
        f.write(f"总句子数：{score_report['summary']['total_sentences']}\n")
        f.write(f"平均分数：{score_report['summary']['average_score']:.2f}\n")
        f.write(f"评分成功率：{score_report['summary']['success_rate']:.2%}\n\n")
        f.write("分数分布：\n")
        f.write(f"- 优秀 (>=8.0)：{score_report['summary']['score_distribution']['excellent']} 句\n")
        f.write(f"- 良好 (6.0-7.9)：{score_report['summary']['score_distribution']['good']} 句\n")
        f.write(f"- 一般 (4.0-5.9)：{score_report['summary']['score_distribution']['fair']} 句\n")
        f.write(f"- 较差 (<4.0)：{score_report['summary']['score_distribution']['poor']} 句\n")
    
    print(f"评估报告已保存到目录：{base_output_dir}")
    print(f"- 详细报告：{detailed_report_path.name}")
    print(f"- 统计摘要：{summary_path.name}")

# =====================
# 主评估函数
# =====================

def evaluate_dataset_quality(dataset_path: str, output_dir: str = None) -> Dict[str, Any]:
    """执行完整的数据集质量评估"""
    config = load_evaluation_config()
    nat_cfg = config.get("naturalness", {})
    score_threshold = nat_cfg.get("score_threshold", 6.0)
    low_rate_threshold = nat_cfg.get("low_rate_threshold", 0.1)
    print("=== 开始数据集质量评估 ===")
    dataset = load_generated_dataset(dataset_path)
    target_counts = load_target_distribution()
    print(f"数据集大小：{len(dataset)} 条记录")
    print(f"目标实体类型：{len(target_counts)} 种")
    # 执行均衡性验证
    balance_result = evaluate_balance(dataset, target_counts)
    # 执行多样性验证
    diversity_result = evaluate_diversity(dataset)
    # 句子自然度检测
    texts = [item["text"] for item in dataset]
    print("=== 检查句子自然度（大模型打分） ===")
    naturalness_scores, low_score_examples = evaluate_sentence_naturalness(
        texts, 
        config,
        dataset_path,  # 传入数据集路径
        output_dir     # 传入输出目录
    )
    avg_naturalness = sum(naturalness_scores) / len(naturalness_scores) if naturalness_scores else 10.0
    low_rate = sum(1 for s in naturalness_scores if s < score_threshold) / len(naturalness_scores) if naturalness_scores else 0.0
    print(f"句子自然度平均分：{avg_naturalness:.2f}，低分句子比例：{low_rate:.2%}")
    if low_rate > low_rate_threshold:
        print(f"[警告] 句子自然度低于{score_threshold}分的比例较高，请重点检查！")
    # 汇总评估结果
    evaluation_result = {
        "evaluation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "dataset_info": {
            "total_records": len(dataset),
            "total_entities": sum(len(item["label"]) for item in dataset),
            "entity_types": list(target_counts.keys()),
        },
        "balance_evaluation": balance_result,
        "diversity_evaluation": diversity_result,
        "naturalness_evaluation": {
            "avg_score": avg_naturalness,
            "low_score_rate": low_rate,
            "low_score_examples": low_score_examples,
            "threshold": score_threshold
        },
        "overall_passed": balance_result["passed"] and diversity_result["passed"] and (low_rate <= low_rate_threshold)
    }
    return evaluation_result

def save_evaluation_report(evaluation_result: Dict[str, Any], output_path: str):
    """保存评估报告"""
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(evaluation_result, f, ensure_ascii=False, indent=2)
    print(f"评估报告已保存到：{output_path}")

# =====================
# 命令行接口
# =====================

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) < 2:
        print("用法：python quality_evaluation.py <dataset_path> [output_path]")
        sys.exit(1)
    
    dataset_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else "evaluation_report.json"
    
    try:
        # 执行评估
        evaluation_result = evaluate_dataset_quality(dataset_path)
        
        # 保存报告
        save_evaluation_report(evaluation_result, output_path)
        
    except Exception as e:
        print(f"评估失败：{e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 