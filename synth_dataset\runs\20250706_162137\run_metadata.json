{"timestamp": "20250706_162137", "start_time": "2025-07-06T16:21:37.457646", "status": "completed", "config": {"dataset_path": "format-dataset/privacy_bench_small.json", "target_count": 10, "max_iterations": 2, "batch_size": 10, "distribution_threshold": 0.05}, "directories": {"root": "synth_dataset\\runs\\20250706_162137", "config": "synth_dataset\\runs\\20250706_162137\\config", "source": "synth_dataset\\runs\\20250706_162137\\source", "intermediate": "synth_dataset\\runs\\20250706_162137\\intermediate", "intermediate_raw": "synth_dataset\\runs\\20250706_162137\\intermediate\\raw", "intermediate_processed": "synth_dataset\\runs\\20250706_162137\\intermediate\\processed", "intermediate_by_entity": "synth_dataset\\runs\\20250706_162137\\intermediate\\by_entity", "iterations": "synth_dataset\\runs\\20250706_162137\\iterations", "strategies": "synth_dataset\\runs\\20250706_162137\\strategies", "output": "synth_dataset\\runs\\20250706_162137\\output", "evaluation": "synth_dataset\\runs\\20250706_162137\\evaluation", "logs": "synth_dataset\\runs\\20250706_162137\\logs"}, "last_updated": "2025-07-06T16:34:45.329791", "stage": "iteration", "current_iteration": 2, "max_iterations": 2, "final_dataset_path": "synth_dataset\\runs\\20250706_162137\\output\\20250706_162137_final_synthetic_dataset.json", "final_report_path": "synth_dataset\\runs\\20250706_162137\\output\\20250706_162137_synthesis_report.json", "total_iterations": 2, "final_metrics": {"original_dataset_size": 167, "final_dataset_size": 307, "total_iterations": 2, "all_entity_types": ["姓名", "年龄", "性别", "国籍", "职业", "民族", "教育背景", "婚姻状况", "政治倾向", "家庭成员", "工资数额", "投资产品", "税务记录", "信用记录", "实体资产", "交易信息", "疾病", "药物", "临床表现", "医疗程序", "过敏信息", "生育信息", "地理位置", "行程信息"], "original_distribution": {"姓名": 16, "地理位置": 45, "职业": 41, "医疗程序": 27, "疾病": 34, "药物": 12, "临床表现": 21, "国籍": 22, "民族": 26, "教育背景": 26}, "target_distribution": {"姓名": 10, "年龄": 10, "性别": 10, "国籍": 10, "职业": 10, "民族": 10, "教育背景": 10, "婚姻状况": 10, "政治倾向": 10, "家庭成员": 10, "工资数额": 10, "投资产品": 10, "税务记录": 10, "信用记录": 10, "实体资产": 10, "交易信息": 10, "疾病": 10, "药物": 10, "临床表现": 10, "医疗程序": 10, "过敏信息": 10, "生育信息": 10, "地理位置": 10, "行程信息": 10}, "final_distribution": {"姓名": 16, "地理位置": 45, "职业": 41, "医疗程序": 27, "疾病": 34, "药物": 12, "临床表现": 21, "国籍": 22, "民族": 26, "教育背景": 26, "年龄": 18, "性别": 20, "婚姻状况": 17, "政治倾向": 18, "家庭成员": 20, "工资数额": 20, "投资产品": 17, "税务记录": 22, "信用记录": 24, "实体资产": 17, "交易信息": 18, "过敏信息": 19, "生育信息": 12, "行程信息": 19}, "final_gap": {"姓名": 0, "年龄": 0, "性别": 0, "国籍": 0, "职业": 0, "民族": 0, "教育背景": 0, "婚姻状况": 0, "政治倾向": 0, "家庭成员": 0, "工资数额": 0, "投资产品": 0, "税务记录": 0, "信用记录": 0, "实体资产": 0, "交易信息": 0, "疾病": 0, "药物": 0, "临床表现": 0, "医疗程序": 0, "过敏信息": 0, "生育信息": 0, "地理位置": 0, "行程信息": 0}, "diversity_metrics": {"vocabulary_diversity": 1.0, "syntactic_diversity": 0.8, "semantic_diversity": 0.7, "context_diversity": 0.6, "entity_diversity": 0.75}, "distribution_passed": true, "diversity_passed": false}}