[{"text": "公司提交了2022年度企业所得税纳税申报表，并附上了增值税专用发票。", "label": [{"entity": "2022年度企业所得税纳税申报表", "start_idx": 6, "end_idx": 19, "type": "税务记录"}, {"entity": "增值税专用发票", "start_idx": 30, "end_idx": 36, "type": "税务记录"}]}, {"text": "个人在2023年度的个人所得税申报表中提交了工资薪金所得记录。", "label": [{"entity": "个人所得税申报表", "start_idx": 8, "end_idx": 18, "type": "税务记录"}, {"entity": "工资薪金所得记录", "start_idx": 25, "end_idx": 36, "type": "税务记录"}]}, {"text": "公司的增值税专用发票编号为VAT20231128001，请核对后提交。", "label": [{"entity": "VAT20231128001", "start_idx": 10, "end_idx": 23, "type": "税务记录"}]}, {"text": "公司提交了2023年度企业所得税纳税申报表，并附带了增值税专用发票。", "label": [{"entity": "企业所得税纳税申报表", "start_idx": 6, "end_idx": 14, "type": "税务记录"}, {"entity": "增值税专用发票", "start_idx": 25, "end_idx": 31, "type": "税务记录"}]}, {"text": "王先生在2023年个人所得税年度汇算清缴中提交了工资薪金所得记录。", "label": [{"entity": "个人所得税年度汇算清缴", "start_idx": 5, "end_idx": 10, "type": "税务记录"}, {"entity": "工资薪金所得记录", "start_idx": 17, "end_idx": 22, "type": "税务记录"}]}, {"text": "张先生提交了2022年度个人所得税纳税申报表，并附上了所有必要的财务凭证。", "label": [{"entity": "个人所得税纳税申报表", "start_idx": 5, "end_idx": 11, "type": "税务记录"}]}, {"text": "请提供具体的税务记录类型实体，例如“增值税专用发票”或“个人所得税纳税申报表”，以便我为您生成符合要求的句子。", "label": [{"entity": "增值税专用发票", "start_idx": 25, "end_idx": 31, "type": "税务记录"}, {"entity": "个人所得税纳税申报表", "start_idx": 35, "end_idx": 51, "type": "税务记录"}]}, {"text": "\"请查看您的个人所得税年度汇算清缴申报表2022年第1期。\"", "label": [{"entity": "个人所得税年度汇算清缴申报表2022年第1期", "start_idx": 7, "end_idx": 35, "type": "税务记录"}]}, {"text": "公司提交了2023年度企业所得税纳税申报表，并附上了增值税专用发票。", "label": [{"entity": "企业所得税纳税申报表", "start_idx": 5, "end_idx": 11, "type": "税务记录"}, {"entity": "增值税专用发票", "start_idx": 21, "end_idx": 27, "type": "税务记录"}]}, {"text": "公司的企业所得税纳税申报表已于2023年12月31日提交至国家税务总局。", "label": [{"entity": "企业所得税纳税申报表", "start_idx": 5, "end_idx": 10, "type": "税务记录"}]}]