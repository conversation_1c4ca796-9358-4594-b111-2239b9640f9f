"""
Step 1: 编写原始数据集样本 - privacy_bench 数据集
"""

import os
import json
import re
from collections import Counter
from typing import Dict, List, Any
import matplotlib.pyplot as plt

# 敏感数据类型定义
ENTITY_TYPES = {
    "个人PII类": ["姓名", "年龄", "性别", "国籍", "职业", "种族", "民族", "教育背景", "婚姻状况", "政治倾向", "家庭成员"],
    "金融财务类": ["工资数额", "投资产品", "税务记录", "信用记录", "实体资产", "交易信息"],
    "医疗健康类": ["疾病", "药物", "临床表现", "医疗程序", "过敏信息", "生育信息"],
    "个人位置类": ["地理位置", "行程信息"]
}

# 所有实体类型的平铺列表
ALL_ENTITY_TYPES = []
for category, types in ENTITY_TYPES.items():
    ALL_ENTITY_TYPES.extend(types)

def ensure_directory(directory):
    """确保目录存在，如果不存在则创建"""
    if not os.path.exists(directory):
        os.makedirs(directory)

def read_privacy_bench_data(file_path):
    """读取privacy_bench.json文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def create_ner_samples(data):
    """从数据创建NER样本"""
    samples = []
    entity_counter = Counter()

    # 初始化计数器，确保所有实体类型都被计数
    for entity_type in ALL_ENTITY_TYPES:
        entity_counter[entity_type] = 0

    for item in data:
        # 获取文本和标签
        text = item.get('text', '')
        labels = item.get('label', [])

        if not text or not labels:
            continue

        # 创建tokens（简单按字符分割）
        tokens = list(text)

        # 创建NER样本格式的entities
        ner_entities = []
        for label in labels:
            entity_type = label.get('type')
            start = label.get('start_idx')
            end = label.get('end_idx') + 1  # 结束索引需要+1，因为原始数据是闭区间
            entity_text = label.get('entity', text[start:end])

            if entity_type and start is not None and end is not None:
                # 只处理我们定义的敏感数据类型
                if entity_type in ALL_ENTITY_TYPES:
                    ner_entities.append({
                        "start": start,
                        "end": end,
                        "type": entity_type,
                        "text": entity_text
                    })
                    entity_counter[entity_type] += 1

        # 创建样本
        sample = {
            "tokens": tokens,
            "entities": ner_entities
        }
        samples.append(sample)

    return samples, entity_counter

def save_samples(samples, output_dir):
    """保存样本到指定目录"""
    ensure_directory(output_dir)

    # 分割样本为训练集、验证集和测试集
    n_samples = len(samples)
    train_size = int(n_samples * 0.7)
    dev_size = int(n_samples * 0.15)

    train_samples = samples[:train_size]
    dev_samples = samples[train_size:train_size+dev_size]
    test_samples = samples[train_size+dev_size:]

    # 保存为JSONL格式
    with open(os.path.join(output_dir, 'train.jsonl'), 'w', encoding='utf-8') as f:
        for sample in train_samples:
            f.write(json.dumps(sample, ensure_ascii=False) + '\n')

    with open(os.path.join(output_dir, 'dev.jsonl'), 'w', encoding='utf-8') as f:
        for sample in dev_samples:
            f.write(json.dumps(sample, ensure_ascii=False) + '\n')

    with open(os.path.join(output_dir, 'test.jsonl'), 'w', encoding='utf-8') as f:
        for sample in test_samples:
            f.write(json.dumps(sample, ensure_ascii=False) + '\n')

    print(f"保存了 {len(train_samples)} 个训练样本, {len(dev_samples)} 个验证样本, {len(test_samples)} 个测试样本")

def generate_entity_report(entity_counter, output_dir):
    """生成实体分析报告"""
    report = {
        "dataset": "privacy_bench",
        "total_entities": sum(entity_counter.values()),
        "entity_counts": dict(entity_counter),
        "entity_categories": {}
    }

    # 按类别组织实体计数
    for category, types in ENTITY_TYPES.items():
        category_count = sum(entity_counter[t] for t in types)
        report["entity_categories"][category] = {
            "total": category_count,
            "types": {t: entity_counter[t] for t in types}
        }

    # 保存报告为JSON
    with open(os.path.join(output_dir, 'privacy_bench_entity_report.json'), 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)

    print(f"实体分析报告已保存到 {os.path.join(output_dir, 'privacy_bench_entity_report.json')}")

    # 生成可视化图表
    # create_entity_visualization(entity_counter, output_dir)

def create_entity_visualization(entity_counter, output_dir):
    """创建实体分布可视化图表"""
    # 按类别组织实体
    category_data = {}
    for category, types in ENTITY_TYPES.items():
        category_data[category] = {t: entity_counter[t] for t in types}

    # 创建每个类别的柱状图
    for category, type_counts in category_data.items():
        plt.figure(figsize=(12, 6))
        types = list(type_counts.keys())
        counts = list(type_counts.values())

        # 创建柱状图
        bars = plt.bar(types, counts)

        # 添加数据标签
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{height}', ha='center', va='bottom')

        plt.title(f'{category} 实体分布')
        plt.xlabel('实体类型')
        plt.ylabel('数量')
        plt.xticks(rotation=45, ha='right')
        plt.tight_layout()

        # 保存图表
        plt.savefig(os.path.join(output_dir, f'privacy_bench_{category}_distribution.png'))
        plt.close()

    # 创建总体分布饼图
    category_totals = {category: sum(entity_counter[t] for t in types)
                      for category, types in ENTITY_TYPES.items()}

    plt.figure(figsize=(10, 8))
    plt.pie(category_totals.values(), labels=category_totals.keys(), autopct='%1.1f%%')
    plt.title('敏感数据类型分布')
    plt.axis('equal')
    plt.savefig(os.path.join(output_dir, 'privacy_bench_category_distribution.png'))
    plt.close()

def main():
    # 文件路径
    input_file = 'format-dataset/privacy_bench.json'
    output_dir = 'original-dataset/privacy_bench'
    report_dir = 'format-dataset'

    # 读取数据
    data = read_privacy_bench_data(input_file)
    if not data:
        print("无法读取数据，退出程序")
        return

    # 创建NER样本
    samples, entity_counter = create_ner_samples(data)

    # 保存样本
    save_samples(samples, output_dir)

    # 生成实体分析报告
    generate_entity_report(entity_counter, report_dir)

    print("Step 1 完成: 原始数据集样本已创建")

if __name__ == "__main__":
    main()
