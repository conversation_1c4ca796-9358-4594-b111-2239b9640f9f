 # 批量生成和句子筛选器使用指南

## 概述

为了提升数据生成效率和质量，我们实现了**批量生成**和**句子筛选器**功能。

## 1. 批量生成功能

### 1.1 功能特点

- **效率提升**：批量生成句子，减少API调用次数
- **智能批量大小**：根据目标数量和实体类型动态调整
- **错误恢复**：批量失败时自动回退到单句生成
- **进度监控**：实时显示生成进度和统计信息

### 1.2 批量策略

#### **自适应策略 (adaptive)**
```python
if target_count <= 20:
    return min(10, target_count)  # 小批量
elif target_count <= 100:
    return min(20, target_count // 2)  # 中批量
else:
    return min(max_batch_size, target_count // 3)  # 大批量
```

#### **固定策略 (fixed)**
使用配置的默认批量大小

#### **动态策略 (dynamic)**
根据实体类型调整批量大小：
- 行程信息、地理位置：较小批量（适合长句子）
- 其他类型：标准批量

### 1.3 配置选项

在 `src/synth_data/ner_config.json` 中配置：

```json
{
  "batch_generation": {
    "enabled": true,
    "batch_size_strategy": "adaptive",
    "max_batch_size": 50,
    "min_batch_size": 5,
    "default_batch_size": 20
  }
}
```

## 2. 句子筛选器功能

### 2.1 筛选器类型

#### **长度筛选器 (LengthFilter)**
- 检查句子长度是否符合要求
- 支持实体类型特定的长度限制
- 可配置最小和最大字符数

#### **自然度筛选器 (NaturalnessFilter)**
- 使用API对句子进行自然度评分
- 批量评分以提高效率
- 可配置评分阈值和重试次数

### 2.2 筛选器链

```python
class FilterChain:
    """筛选器链管理器"""
    
    def apply_filters(self, sentences: List[str], entity_type: str = None):
        # 依次应用所有筛选器
        # 返回筛选后的句子和详细统计
```

### 2.3 配置选项

```json
{
  "sentence_filtering": {
    "enabled": true,
    "filters": ["length", "naturalness"],
    "quality_thresholds": {
      "min_pass_rate": 0.6,
      "max_rejection_rate": 0.4
    },
    "length_filter": {
      "enabled": true,
      "min_length": 15,
      "max_length": 80,
      "entity_type_specific": {
        "行程信息": {"min_length": 20, "max_length": 100},
        "地理位置": {"min_length": 10, "max_length": 50}
      }
    },
    "naturalness_filter": {
      "enabled": true,
      "threshold": 6.0,
      "batch_size": 10,
      "max_retries": 3
    }
  }
}
```

## 3. 新的生成流程

### 3.1 批量模式流程

```
1. 计算批量大小
2. 批量生成句子
3. 应用句子筛选器
4. 对筛选后的句子进行实体标注
5. 如果数量不足，重复步骤2-4
6. 返回最终结果
```

### 3.2 单句模式流程（原有）

```
1. 逐个生成句子
2. 直接进行实体标注
3. 返回结果
```

## 4. 性能对比

| 指标 | 原有模式 | 批量模式 |
|------|----------|----------|
| API调用次数 | 2N (生成+标注) | N/批量大小 + N |
| 生成速度 | 慢 | 快（3-5倍提升） |
| 质量控制 | 无 | 主动筛选 |
| 错误恢复 | 单个跳过 | 批量重试 |
| 成本 | 高 | 低（60-80%减少） |

## 5. 使用示例

### 5.1 启用批量生成

```python
# 在 ner_config.json 中设置
{
  "batch_generation": {
    "enabled": true,
    "batch_size_strategy": "adaptive"
  }
}
```

### 5.2 启用句子筛选

```python
# 在 ner_config.json 中设置
{
  "sentence_filtering": {
    "enabled": true,
    "filters": ["length", "naturalness"]
  }
}
```

### 5.3 运行生成

```bash
python src/synth_data/ner_data_generation.py
```

输出示例：
```
[*] 使用批量生成模式
[*] 生成第 1-20 个句子
[✓] 批量生成完成：20个句子，剩余目标：80
[✓] 句子筛选完成：
  - 原始句子：20个
  - 筛选后：15个
  - 筛选率：25.00%
[*] 当前进度：已生成 15/100 个有效数据
```

## 6. 监控和调试

### 6.1 筛选统计

```python
filter_stats = {
    "total_initial": 20,        # 原始句子数
    "total_final": 15,          # 最终句子数
    "overall_rejection_rate": 0.25,  # 整体拒绝率
    "filter_results": {
        "filter_0": {
            "filter_type": "length",
            "passed": 18,
            "rejected": 2,
            "rejection_rate": 0.10
        },
        "filter_1": {
            "filter_type": "naturalness",
            "passed": 15,
            "rejected": 3,
            "rejection_rate": 0.17
        }
    }
}
```

### 6.2 质量阈值

```python
quality_thresholds = {
    "min_pass_rate": 0.6,      # 至少60%的句子通过筛选
    "max_rejection_rate": 0.4  # 最多40%的句子被拒绝
}
```

## 7. 测试功能

运行测试脚本验证功能：

```bash
python test_batch_generation.py
```

测试内容包括：
- 批量大小计算
- 批量prompt构建
- 长度筛选器
- 自然度筛选器
- 筛选器链
- 批量生成集成

## 8. 注意事项

### 8.1 API限制
- 批量生成可能受到API的token限制
- 建议根据API能力调整批量大小

### 8.2 筛选器配置
- 筛选器过于严格可能导致数据不足
- 建议根据实际需求调整筛选阈值

### 8.3 错误处理
- 批量生成失败时会自动回退到单句生成
- 筛选器失败时会跳过该筛选器

### 8.4 性能优化
- 大批量生成适合大量数据
- 小批量生成适合精确控制
- 根据实体类型选择合适的批量策略

## 9. 未来扩展

1. **更多筛选器**：语法正确性、语义一致性等
2. **智能批量调整**：根据API响应时间动态调整
3. **并行处理**：支持多线程批量生成
4. **质量预测**：预测句子质量，提前筛选
5. **自适应阈值**：根据数据分布自动调整筛选阈值